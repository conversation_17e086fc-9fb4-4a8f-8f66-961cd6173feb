
#All configuration items related to the core gameplay
[gameplay]
	#Amount of initial citizens. [Default: 4, min: 1, max: 10]
	#Range: 1 ~ 10
	initialcitizenamount = 4
	#Should players be able to place an infinite amount of supply camps/ships? [Default: false]
	allowinfinitesupplychests = false
	#Should players be allowed to abandon their colony to create a new one easily? Note: This is buggy! [Default: false]
	allowinfinitecolonies = false
	#Should colonies in other dimensions be allowed? [Default: true]
	allowotherdimcolonies = true
	#Max citizens in one colony. [Default: 250, min: 30, max: 500]
	#Range: 30 ~ 500
	maxcitizenpercolony = 50
	#Should development features be enabled (might be buggy)? [Default: false]
	enableindevelopmentfeatures = false
	#Should citizen name tags be rendered? [Default: true]
	alwaysrendernametag = true
	#Should workers work during the rain? [Default: false]
	workersalwaysworkinrain = false
	#Chance for the Miner to get an ore when mining cobblestone or stone (by default, can be expanded with datapacks to other materials). This is a percentage. (To change which ores the Miner can find, see "List of Lucky Ores".) [Default: 1, min: 0, max: 100]
	#Range: 0 ~ 100
	luckyblockchance = 1
	#The minimum level a Town Hall has to be to allow teleportation to allied colonies. [Default: 3, min: 0, max: 5]
	#Range: 0 ~ 5
	minthleveltoteleport = 3
	#Food consumption modifier. [Default: 1.000000, min: 0.100000, max: 100.000000]
	#Range: 0.1 ~ 100.0
	foodmodifier = 1.0
	#How common diseases are. 1 = Very common, 100 = extremely rare. [Default: 5, min: 1, max: 100]
	#Range: 1 ~ 100
	diseasemodifier = 5
	#If part of the colony is loaded by an owner/officer, should the colony be kept loaded? (Set how many chunks are loaded with the "Colony Chunk Loading Strictness" option.) [Default: true]
	forceloadcolony = false
	#Set how long chunks stay loaded after player leaves, does not persist through restarts. Default: 10min [Default: 10, min: 1, max: 1440]
	#Range: 1 ~ 1440
	loadtime = 10
	#This controls how many chunks are loaded with the "Chunk Load Colony" option. The higher this value, the fewer chunks will be loaded. (The innermost chunks will be loaded first.) 1 = load all claimed chunks. [Default: 3, min: 1, max: 15]
	#Range: 1 ~ 15
	colonyloadstrictness = 3
	#Max log count in one tree for the Forester to check during their tree search. [Default: 400, min: 1, max: 1000]
	#Range: 1 ~ 1000
	maxtreesize = 400
	#Disables supply camp placing restrictions, intended for skyworlds and similar [Default: false]
	nosupplyplacementrestrictions = false
	#Raiders will spawn in the sky if this is enabled [Default: false]
	skyraiders = true

#All configurations related to the research system
[research]
	#Allows automatic and (near) instant completion of research for players in creative mode. If false, creative players will still be able to begin researches, but will have normal progress rates. [Default: true]
	researchcreativecompletion = true
	#Significantly increases the amount of information related to research datapacks that is logged during the world load. [Default: false]
	researchdebuglog = false
	#A list of items to charge players when undoing an already-completed research. 
	researchresetcost = ["minecolonies:ancienttome:1"]

#All configurations related to the MineColonies commands
[commands]
	#Should players be allowed to use the /mc rtp command? [Default: false]
	canplayerusertpcommand = false
	#Should players be allowed to use the /mc colony teleport command? [Default: false]
	canplayerusecolonytpcommand = false
	#Can players teleport to allied colonies? [Default: true]
	canplayeruseallytownhallteleport = true
	#Should players be allowed to use the /mc home command? Note: Only owners of the colony can use this command. [Default: false]
	canplayerusehometpcommand = false
	#Should players be allowed to use the /mc colony info command? [Default: true]
	canplayeruseshowcolonyinfocommand = true
	#Should players be allowed to use the /mc citizens kill command? [Default: false]
	canplayerusekillcitizenscommand = false
	#Should players be allowed to use the /mc colony addOfficer command? [Default: true]
	canplayeruseaddofficercommand = true
	#Should players be allowed to use the /mc colony delete command? [Default: false]
	canplayerusedeletecolonycommand = true
	#Should players be allowed to use the /mc colony requestsystem-reset command? [Default: false]
	canplayeruseresetcommand = false

#All configuration related to colony claims
[claims]
	#Maximum claim range for a colony. This is the radius, measured in chunks. [Default: 20, min: 1, max: 250]
	#Range: 1 ~ 250
	maxColonySize = 20
	#The minimum distance (in chunks) between colonies. [Default: 8, min: 1, max: 200]
	#Range: 1 ~ 200
	minColonyDistance = 8
	#Initial claim size for a colony. This is the radius, measured in chunks. [Default: 4, min: 1, max: 15]
	#Range: 1 ~ 15
	initialColonySize = 4
	#Max distance (in blocks) from world spawn for a colony. [Default: 30000, min: 1000, max: 2147483647]
	#Range: > 1000
	maxdistancefromworldspawn = 8000
	#Min distance (in blocks) from world spawn for a colony. [Default: 0, min: 0, max: 1000]
	#Range: 0 ~ 1000
	mindistancefromworldspawn = 512

#All configuration items related to the combat elements of MineColonies
[combat]
	#minecolonies.config.dobarbariansspawn.comment [Default: true]
	dobarbariansspawn = true
	#minecolonies.config.barbarianhordedifficulty.comment [Default: 5, min: 0, max: 10]
	#Range: 0 ~ 10
	barbarianhordedifficulty = 10
	#minecolonies.config.maxbarbariansize.comment [Default: 80, min: 6, max: 400]
	#Range: 6 ~ 400
	maxBarbarianSize = 160
	#minecolonies.config.dobarbariansbreakthroughwalls.comment [Default: true]
	dobarbariansbreakthroughwalls = true
	#The average number of nights between raids. [Default: 14, min: 1, max: 50]
	#Range: 1 ~ 50
	averagenumberofnightsbetweenraids = 7
	#The minimum number of nights between raids. [Default: 10, min: 1, max: 30]
	#Range: 1 ~ 30
	minimumnumberofnightsbetweenraids = 5
	#Should mobs attack citizens? [Default: true]
	mobattackcitizens = true
	#minecolonies.config.shouldraiderbreakdoors.comment [Default: true]
	shouldraiderbreakdoors = true
	#Health multiplier for all Guards. [Default: 1.000000, min: 0.100000, max: 5.000000]
	#Range: 0.1 ~ 5.0
	guardhealthmult = 1.0
	#Turn on MineColonies PVP mode (colonies can be destroyed and griefed under certain conditions). [Default: false]
	pvp_mode = false
	#minecolonies.config.guarddamagemultiplier.comment [Default: 1.000000, min: 0.100000, max: 15.000000]
	#Range: 0.1 ~ 15.0
	guardDamageMultiplier = 1.0

#All permission configuration options
[permissions]
	#Should colony protection be enabled? [Default: true]
	enablecolonyprotection = true
	#Independent from the colony protection, should explosions be turned off inside colonies? DAMAGE_NOTHING prevents explosions completely. DAMAGE_PLAYERS, allows explosions to damage players and hostile mobs, but not blocks or neutral or friendly mobs. DAMAGE_ENTITIES allows damage to all entities. DAMAGE_EVERYTHING allows explosions to damage entities and blocks. 
	#Allowed Values: DAMAGE_NOTHING, DAMAGE_PLAYERS, DAMAGE_ENTITIES, DAMAGE_EVERYTHING
	turnoffexplosionsincolonies = "DAMAGE_ENTITIES"

#All configuration related to mod compatibility
[compatibility]
	#When loading recipes, generate audit CSV files to help debug datapacks or extra mods. [Default: false]
	auditcraftingtags = false
	#Enable inventory debugging. [Default: false]
	debuginventories = false
	#Turn this on if you're using this world for blueprint building and scanning. [Default: false]
	blueprintbuildmode = false

#All configurations related to pathfinding
[pathfinding]
	#Verbosity of pathfinding debug messages. [Default: 0, min: 0, max: 10]
	#Range: 0 ~ 10
	pathfindingdebugverbosity = 0
	#Minimum number of consecutive rails for citizens to use them. [Default: 8, min: 5, max: 100]
	#Range: 5 ~ 100
	minimumrailstopath = 8
	#Amount of additional threads to be used for pathfinding. [Default: 1, min: 1, max: 10]
	#Range: 1 ~ 10
	pathfindingmaxthreadcount = 2

#All configurations related to the request system
[requestSystem]
	#Should the request system creatively resolve (if possible) when the player is required to resolve a request? This is a debugging tool and can take a very long time to resolve a request. [Default: false]
	creativeresolve = false

