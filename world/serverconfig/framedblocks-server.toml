
[general]
	#Whether blocks with block entities can be placed in framed blocks
	allowBlockEntities = false
	#Enables the intangbility feature. Disabling this also prevents moving through blocks that are already marked as intangible
	enableIntangibleFeature = false
	#The item to use for making Framed Blocks intangible. The value must be a valid item registry name
	intangibleMarkerItem = "minecraft:phantom_membrane"
	#If true, only the player who placed the Framed One-Way Window can modify the window direction
	oneWayWindowOwnable = true
	#If true, applying a camo will consume the item and removing the camo will drop it again
	consumeCamoItem = true
	#The light level to emit when glowstone dust is applied to a framed block
	#Range: 0 ~ 15
	glowstoneLightLevel = 15

