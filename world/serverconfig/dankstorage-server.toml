
[server]
	#Stack limit of first dank storage
	#Range: > 1
	stacklimit1 = 256
	#Stack limit of second dank storage
	#Range: > 1
	stacklimit2 = 1024
	#Stack limit of third dank storage
	#Range: > 1
	stacklimit3 = 4096
	#Stack limit of fourth dank storage
	#Range: > 1
	stacklimit4 = 16384
	#Stack limit of fifth dank storage
	#Range: > 1
	stacklimit5 = 65536
	#Stack limit of sixth dank storage
	#Range: > 1
	stacklimit6 = 262144
	#Stack limit of seventh dank storage
	#Range: > 1
	stacklimit7 = 2147483647
	#Tags that are eligible for conversion, input as a list of resourcelocation, eg 'forge:ingots/iron'
	"convertible tags" = ["forge:ingots/iron", "forge:ingots/gold", "forge:ores/coal", "forge:ores/diamond", "forge:ores/emerald", "forge:ores/gold", "forge:ores/iron", "forge:ores/lapis", "forge:ores/redstone", "forge:gems/amethyst", "forge:gems/peridot", "forge:gems/ruby", "forge:ingots/copper", "forge:ingots/lead", "forge:ingots/nickel", "forge:ingots/silver", "forge:ingots/tin", "forge:ores/copper", "forge:ores/lead", "forge:ores/ruby", "forge:ores/silver", "forge:ores/tin"]

