=== FTB Ranks ===

Last README file update: Wed Jul 09 16:56:29 CST 2025
Wiki: https://www.notion.so/feedthebeast/FTB-Mod-Documentation-da2e359bad2449459d58d787edda3168
To refresh this file, run /ftbranks refresh_readme

= All available command nodes =
command
command.adastra
command.adastra.planets
command.addmahou
command.addmahou.player
command.addmahou.player.mana
command.addmahou.player.mana.overDrainHurts
command.addmahou.player.mana.overDrainHurts.damagetype
command.advancedperipherals
command.advancedperipherals.forceload
command.advancedperipherals.forceload.dump
command.advancedperipherals.forceload.help
command.advancedperipherals.getHashItem
command.advancedperipherals.safe-exec
command.advancedperipherals.safe-exec.command
command.advancement
command.advancement.grant
command.advancement.grant.targets
command.advancement.grant.targets.everything
command.advancement.grant.targets.from
command.advancement.grant.targets.from.advancement
command.advancement.grant.targets.only
command.advancement.grant.targets.only.advancement
command.advancement.grant.targets.only.advancement.criterion
command.advancement.grant.targets.through
command.advancement.grant.targets.through.advancement
command.advancement.grant.targets.until
command.advancement.grant.targets.until.advancement
command.advancement.revoke
command.advancement.revoke.targets
command.advancement.revoke.targets.everything
command.advancement.revoke.targets.from
command.advancement.revoke.targets.from.advancement
command.advancement.revoke.targets.only
command.advancement.revoke.targets.only.advancement
command.advancement.revoke.targets.only.advancement.criterion
command.advancement.revoke.targets.through
command.advancement.revoke.targets.through.advancement
command.advancement.revoke.targets.until
command.advancement.revoke.targets.until.advancement
command.ae2
command.ae2.channelmode
command.ae2.channelmode.default
command.ae2.channelmode.infinite
command.ae2.channelmode.x2
command.ae2.channelmode.x3
command.ae2.channelmode.x4
command.ae2.chunklogger
command.ae2.compass
command.ae2.reloadconfig
command.ae2.setuptestworld
command.ae2.setuptestworld.ae2:all_terminals
command.ae2.setuptestworld.ae2:annihilation_plane_seed_farm
command.ae2.setuptestworld.ae2:autocrafting_testplot
command.ae2.setuptestworld.ae2:blockingmode_subnetwork_chesttest
command.ae2.setuptestworld.ae2:canceling_jobs_from_interfacecrash
command.ae2.setuptestworld.ae2:channel_assignment_test
command.ae2.setuptestworld.ae2:controller_inside_scs
command.ae2.setuptestworld.ae2:crafting_cpu_inside_scs
command.ae2.setuptestworld.ae2:energy_overlay
command.ae2.setuptestworld.ae2:export_from_storagebus
command.ae2.setuptestworld.ae2:fluid_chest
command.ae2.setuptestworld.ae2:guidebook_structure_workarea
command.ae2.setuptestworld.ae2:import_and_export_in_one_tick
command.ae2.setuptestworld.ae2:import_exportbus
command.ae2.setuptestworld.ae2:import_from_cauldron
command.ae2.setuptestworld.ae2:import_into_storagebus
command.ae2.setuptestworld.ae2:import_on_pulse
command.ae2.setuptestworld.ae2:import_on_pulse_transactioncrash
command.ae2.setuptestworld.ae2:inscriber
command.ae2.setuptestworld.ae2:inscriber_recipe_inscriber_certus_quartz_crystal_print
command.ae2.setuptestworld.ae2:inscriber_recipe_inscriber_diamond_print
command.ae2.setuptestworld.ae2:inscriber_recipe_inscriber_gold_ingot_print
command.ae2.setuptestworld.ae2:inscriber_recipe_inscriber_silicon_print
command.ae2.setuptestworld.ae2:inscriber_recipe_nameplate
command.ae2.setuptestworld.ae2:insert_fluid_into_mechest
command.ae2.setuptestworld.ae2:insert_item_into_mechest
command.ae2.setuptestworld.ae2:interface_to_interface_different_networks
command.ae2.setuptestworld.ae2:item_chest
command.ae2.setuptestworld.ae2:mattercannon_range
command.ae2.setuptestworld.ae2:maxchannels_adhoctest
command.ae2.setuptestworld.ae2:memcard_export_bus
command.ae2.setuptestworld.ae2:memcard_interface
command.ae2.setuptestworld.ae2:memcard_pattern_provider
command.ae2.setuptestworld.ae2:p2p_channel_reconnect_behavior
command.ae2.setuptestworld.ae2:p2p_energy
command.ae2.setuptestworld.ae2:p2p_fluids
command.ae2.setuptestworld.ae2:p2p_items
command.ae2.setuptestworld.ae2:p2p_light
command.ae2.setuptestworld.ae2:p2p_me
command.ae2.setuptestworld.ae2:p2p_recursive_item
command.ae2.setuptestworld.ae2:pattern_provider_faces_round_robin
command.ae2.setuptestworld.ae2:pattern_provider_loop
command.ae2.setuptestworld.ae2:pp_block_lockmode_high
command.ae2.setuptestworld.ae2:pp_block_lockmode_low
command.ae2.setuptestworld.ae2:pp_block_lockmode_pulse
command.ae2.setuptestworld.ae2:pp_block_lockmode_pulse_starting_high
command.ae2.setuptestworld.ae2:pp_block_lockmode_result
command.ae2.setuptestworld.ae2:pp_block_wait_for_pulse_saved
command.ae2.setuptestworld.ae2:pp_block_wait_for_result_saved
command.ae2.setuptestworld.ae2:pp_part_lockmode_high
command.ae2.setuptestworld.ae2:pp_part_lockmode_low
command.ae2.setuptestworld.ae2:pp_part_lockmode_pulse
command.ae2.setuptestworld.ae2:pp_part_lockmode_result
command.ae2.setuptestworld.ae2:pp_part_wait_for_pulse_saved
command.ae2.setuptestworld.ae2:pp_part_wait_for_result_saved
command.ae2.setuptestworld.ae2:processing_pattern_inputs_unstacking
command.ae2.setuptestworld.ae2:regression_7288
command.ae2.setuptestworld.ae2:simple_qnb_link
command.ae2.setuptestworld.ae2:spatial_entity_storage
command.ae2.setuptestworld.ae2:subnet
command.ae2.setuptestworld.ae2:terminal_fullof_enchanteditems
command.ae2.setuptestworld.ae2:tool_repair_recipe
command.ae2.setuptestworld.ae2:universal_terminal
command.ae2.setuptestworld.ae2:wireless_terminal
command.ae2.spatial
command.ae2.spatial.givecell
command.ae2.spatial.givecell.plotId
command.ae2.spatial.info
command.ae2.spatial.info.plotId
command.ae2.spatial.tp
command.ae2.spatial.tp.plotId
command.ae2.spatial.tpback
command.ae2.spatial.tpback.plotId
command.ae2.testmeteorites
command.ae2.testmeteorites.force
command.ae2.tickmonitor
command.ae2.tickmonitor.enable
command.ae2things
command.ae2things.getuuid
command.ae2things.recover
command.ae2things.recover.uuid
command.aether
command.aether.eternal_day
command.aether.eternal_day.query
command.aether.eternal_day.set
command.aether.eternal_day.set.option
command.aether.player
command.aether.player.life_shards
command.aether.player.life_shards.set
command.aether.player.life_shards.set.targets
command.aether.player.life_shards.set.targets.value
command.aether.sun_altar_whitelist
command.aether.sun_altar_whitelist.add
command.aether.sun_altar_whitelist.add.targets
command.aether.sun_altar_whitelist.list
command.aether.sun_altar_whitelist.off
command.aether.sun_altar_whitelist.on
command.aether.sun_altar_whitelist.reload
command.aether.sun_altar_whitelist.remove
command.aether.sun_altar_whitelist.remove.targets
command.aether.time
command.aether.time.add
command.aether.time.add.time
command.aether.time.query
command.aether.time.query.day
command.aether.time.query.daytime
command.aether.time.set
command.aether.time.set.day
command.aether.time.set.midnight
command.aether.time.set.night
command.aether.time.set.noon
command.aether.time.set.time
command.aether.world_preview
command.aether.world_preview.fix
command.anvil
command.apoth
command.apoth.affix
command.apoth.affix.apply
command.apoth.affix.apply.affix
command.apoth.affix.apply.affix.level
command.apoth.affix.list
command.apoth.affix.list_alternatives
command.apoth.affix.list_alternatives.affix
command.apoth.gem
command.apoth.gem.fromPreset
command.apoth.gem.fromPreset.gem
command.apoth.gem.random
command.apoth.loot_category
command.apoth.lootify
command.apoth.lootify.rarity
command.apoth.modifier
command.apoth.modifier.attribute
command.apoth.modifier.attribute.op
command.apoth.modifier.attribute.op.value
command.apoth.modifier.attribute.op.value.slot
command.apoth.set_rarity
command.apoth.set_rarity.rarity
command.apoth.set_sockets
command.apoth.set_sockets.sockets
command.apoth.spawn_boss
command.apoth.spawn_boss.entity
command.apoth.spawn_boss.entity.boss
command.apoth.spawn_boss.entity.boss.rarity
command.apoth.spawn_boss.pos
command.apoth.spawn_boss.pos.boss
command.apoth.spawn_boss.pos.boss.rarity
command.ars-data
command.ars-data.dump
command.ars-data.dump.augment-compatibility-csv
command.ars-glyph
command.ars-glyph.targets
command.ars-glyph.targets.glyph
command.ars-light
command.ars-light.off
command.ars-light.on
command.ars-pathing
command.ars-reset
command.ars-reset.targets
command.ars-skull
command.ars-skull.player_name
command.ars-skull.player_name.duration
command.ars-skull.player_name.duration.nbt
command.ars-skull.player_name.duration.nbt.dropBlock
command.ars-tome
command.ars-tome.tome
command.attribute
command.attribute.target
command.attribute.target.attribute
command.attribute.target.attribute.base
command.attribute.target.attribute.base.get
command.attribute.target.attribute.base.get.scale
command.attribute.target.attribute.base.set
command.attribute.target.attribute.base.set.value
command.attribute.target.attribute.get
command.attribute.target.attribute.get.scale
command.attribute.target.attribute.modifier
command.attribute.target.attribute.modifier.add
command.attribute.target.attribute.modifier.add.uuid
command.attribute.target.attribute.modifier.add.uuid.name
command.attribute.target.attribute.modifier.add.uuid.name.value
command.attribute.target.attribute.modifier.add.uuid.name.value.add
command.attribute.target.attribute.modifier.add.uuid.name.value.multiply
command.attribute.target.attribute.modifier.add.uuid.name.value.multiply_base
command.attribute.target.attribute.modifier.remove
command.attribute.target.attribute.modifier.remove.uuid
command.attribute.target.attribute.modifier.value
command.attribute.target.attribute.modifier.value.get
command.attribute.target.attribute.modifier.value.get.uuid
command.attribute.target.attribute.modifier.value.get.uuid.scale
command.back
command.balm
command.balm.dev
command.balm.export
command.balm.export.config
command.balm.export.config.class
command.balm.export.icons
command.balm.export.icons.filter
command.ban
command.ban-ip
command.ban-ip.target
command.ban-ip.target.reason
command.ban.targets
command.ban.targets.reason
command.banlist
command.banlist.ips
command.banlist.players
command.bcore_util
command.bcore_util.debug_tile
command.bcore_util.debug_tile.pos
command.bcore_util.debug_tile.pos.multiblock
command.bcore_util.debug_tile.pos.multiblock.rotation
command.bcore_util.dump_event_listeners
command.bcore_util.eggify
command.bcore_util.eggify.target
command.bcore_util.nbt
command.bcore_util.noclip
command.bcore_util.place_multiblock
command.bcore_util.place_multiblock.pos
command.bcore_util.place_multiblock.pos.multiblock
command.bcore_util.place_multiblock.pos.multiblock.rotation
command.bcore_util.reset_contrib_handler
command.bcore_util.stack_string
command.bcore_util.stack_string.from_string
command.bcore_util.stack_string.from_string.give-to
command.bcore_util.stack_string.from_string.give-to.stack-string
command.bcore_util.stack_string.to_string
command.bcore_util.stack_string.to_string.id_count
command.bcore_util.stack_string.to_string.id_nbt
command.bcore_util.stack_string.to_string.id_nbt_capabilities
command.bcore_util.stack_string.to_string.id_nbt_count
command.bcore_util.stack_string.to_string.id_only
command.bcore_util.uuid
command.bcore_util.uuid.target
command.blue_skies
command.blue_skies.get_mob_caps
command.blue_skies.loot_bag
command.blue_skies.loot_bag.runs
command.blue_skies.refresh_supporters
command.blue_skies.set_lore_used
command.blue_skies.set_lore_used.used
command.blue_skies.set_progression
command.blue_skies.set_progression.everbright
command.blue_skies.set_progression.everbright.level
command.blue_skies.set_progression.everdawn
command.blue_skies.set_progression.everdawn.level
command.blue_skies.weather
command.blue_skies.weather.clear
command.blue_skies.weather.rain
command.blue_skies.weather.thunder
command.bookshelf
command.bookshelf.font
command.bookshelf.font.block
command.bookshelf.font.block.font
command.bookshelf.font.block.font.pos
command.bookshelf.font.book
command.bookshelf.font.book.font
command.bookshelf.font.item
command.bookshelf.font.item.font
command.bookshelf.font.say
command.bookshelf.font.say.font
command.bookshelf.font.say.font.message
command.bookshelf.hand
command.bookshelf.hand.output_type
command.bossbar
command.bossbar.add
command.bossbar.add.id
command.bossbar.add.id.name
command.bossbar.get
command.bossbar.get.id
command.bossbar.get.id.max
command.bossbar.get.id.players
command.bossbar.get.id.value
command.bossbar.get.id.visible
command.bossbar.list
command.bossbar.remove
command.bossbar.remove.id
command.bossbar.set
command.bossbar.set.id
command.bossbar.set.id.color
command.bossbar.set.id.color.blue
command.bossbar.set.id.color.green
command.bossbar.set.id.color.pink
command.bossbar.set.id.color.purple
command.bossbar.set.id.color.red
command.bossbar.set.id.color.white
command.bossbar.set.id.color.yellow
command.bossbar.set.id.max
command.bossbar.set.id.max.max
command.bossbar.set.id.name
command.bossbar.set.id.name.name
command.bossbar.set.id.players
command.bossbar.set.id.players.targets
command.bossbar.set.id.style
command.bossbar.set.id.style.notched_10
command.bossbar.set.id.style.notched_12
command.bossbar.set.id.style.notched_20
command.bossbar.set.id.style.notched_6
command.bossbar.set.id.style.progress
command.bossbar.set.id.value
command.bossbar.set.id.value.value
command.bossbar.set.id.visible
command.bossbar.set.id.visible.visible
command.botanypots
command.botanypots.dump
command.botanypots.dump.missing_crops
command.buildinggadgets2
command.buildinggadgets2.redprints
command.buildinggadgets2.redprints.give
command.buildinggadgets2.redprints.give.name
command.buildinggadgets2.redprints.give.name.targets
command.buildinggadgets2.redprints.list
command.buildinggadgets2.redprints.remove
command.buildinggadgets2.redprints.remove.name
command.c
command.c.clone
command.c.clone.begin
command.c.clone.begin.end
command.c.clone.begin.end.destination
command.c.clone.begin.end.destination.skipBlocks
command.c.config
command.c.config.path
command.c.config.path.set
command.c.config.path.set.value
command.c.coupling
command.c.coupling.add
command.c.coupling.add.cart1
command.c.coupling.add.cart1.cart2
command.c.coupling.add.carts
command.c.coupling.remove
command.c.coupling.remove.cart1
command.c.coupling.remove.cart1.cart2
command.c.coupling.removeAll
command.c.coupling.removeAll.cart
command.c.debuginfo
command.c.dismissFabulousWarning
command.c.fixLighting
command.c.glue
command.c.glue.from
command.c.glue.from.to
command.c.highlight
command.c.highlight.pos
command.c.highlight.pos.players
command.c.killTrain
command.c.killTrain.train
command.c.overlay
command.c.overlay.reset
command.c.passenger
command.c.passenger.rider
command.c.passenger.rider.vehicle
command.c.passenger.rider.vehicle.seatIndex
command.c.ponder
command.c.ponder.scene
command.c.ponder.scene.targets
command.c.rainbowDebug
command.c.rainbowDebug.off
command.c.rainbowDebug.on
command.c.trains
command.c.u
command.c.u.angle
command.c.u.angle.players
command.c.u.angle.players.mode
command.c.u.angle.players.mode.exponential
command.c.u.angle.players.mode.exponential.speed
command.c.u.angle.players.mode.linear
command.c.u.angle.players.mode.linear.speed
command.c.u.angle.players.pitch
command.c.u.angle.players.pitch.degrees
command.c.u.angle.players.yaw
command.c.u.angle.players.yaw.degrees
command.c.u.camera
command.c.u.camera.multiplier
command.c.u.camera.reset
command.c.u.clearRenderBuffers
command.c.u.flySpeed
command.c.u.flySpeed.reset
command.c.u.flySpeed.reset.target
command.c.u.flySpeed.speed
command.c.u.flySpeed.speed.target
command.c.u.replaceInCommandBlocks
command.c.u.replaceInCommandBlocks.begin
command.c.u.replaceInCommandBlocks.begin.end
command.c.u.replaceInCommandBlocks.begin.end.toReplace
command.c.u.replaceInCommandBlocks.begin.end.toReplace.replaceWith
command.c.util
command.c.util.angle
command.c.util.angle.players
command.c.util.angle.players.mode
command.c.util.angle.players.mode.exponential
command.c.util.angle.players.mode.exponential.speed
command.c.util.angle.players.mode.linear
command.c.util.angle.players.mode.linear.speed
command.c.util.angle.players.pitch
command.c.util.angle.players.pitch.degrees
command.c.util.angle.players.yaw
command.c.util.angle.players.yaw.degrees
command.c.util.camera
command.c.util.camera.multiplier
command.c.util.camera.reset
command.c.util.clearRenderBuffers
command.c.util.flySpeed
command.c.util.flySpeed.reset
command.c.util.flySpeed.reset.target
command.c.util.flySpeed.speed
command.c.util.flySpeed.speed.target
command.c.util.replaceInCommandBlocks
command.c.util.replaceInCommandBlocks.begin
command.c.util.replaceInCommandBlocks.begin.end
command.c.util.replaceInCommandBlocks.begin.end.toReplace
command.c.util.replaceInCommandBlocks.begin.end.toReplace.replaceWith
command.cast
command.cast.casters
command.cast.casters.spell
command.cast.casters.spell.function value
command.cast.casters.spell.level
command.cca_api
command.ccl
command.ccl.count
command.ccl.count.entity
command.ccl.gc
command.ccl.killall
command.ccl.killall.entity
command.ccl.killall.gracefully
command.ccl.killall.gracefully.entity
command.ccl.meminfo
command.ccl.setup_dev_world
command.chemlibhelper
command.clear
command.clear.targets
command.clear.targets.item
command.clear.targets.item.maxCount
command.clearCooldowns
command.clearCooldowns.all
command.clearCooldowns.player
command.clearCooldowns.player.targets
command.clearRecasts
command.clearRecasts.all
command.clearRecasts.player
command.clearRecasts.player.targets
command.clearcosarmor
command.clearcosarmor.targets
command.clone
command.clone.begin
command.clone.begin.end
command.clone.begin.end.destination
command.clone.begin.end.destination.filtered
command.clone.begin.end.destination.filtered.filter
command.clone.begin.end.destination.filtered.filter.force
command.clone.begin.end.destination.filtered.filter.move
command.clone.begin.end.destination.filtered.filter.normal
command.clone.begin.end.destination.masked
command.clone.begin.end.destination.masked.force
command.clone.begin.end.destination.masked.move
command.clone.begin.end.destination.masked.normal
command.clone.begin.end.destination.replace
command.clone.begin.end.destination.replace.force
command.clone.begin.end.destination.replace.move
command.clone.begin.end.destination.replace.normal
command.clone.begin.end.to
command.clone.begin.end.to.targetDimension
command.clone.begin.end.to.targetDimension.destination
command.clone.begin.end.to.targetDimension.destination.filtered
command.clone.begin.end.to.targetDimension.destination.filtered.filter
command.clone.begin.end.to.targetDimension.destination.filtered.filter.force
command.clone.begin.end.to.targetDimension.destination.filtered.filter.move
command.clone.begin.end.to.targetDimension.destination.filtered.filter.normal
command.clone.begin.end.to.targetDimension.destination.masked
command.clone.begin.end.to.targetDimension.destination.masked.force
command.clone.begin.end.to.targetDimension.destination.masked.move
command.clone.begin.end.to.targetDimension.destination.masked.normal
command.clone.begin.end.to.targetDimension.destination.replace
command.clone.begin.end.to.targetDimension.destination.replace.force
command.clone.begin.end.to.targetDimension.destination.replace.move
command.clone.begin.end.to.targetDimension.destination.replace.normal
command.clone.from
command.clone.from.sourceDimension
command.clone.from.sourceDimension.begin
command.clone.from.sourceDimension.begin.end
command.clone.from.sourceDimension.begin.end.destination
command.clone.from.sourceDimension.begin.end.destination.filtered
command.clone.from.sourceDimension.begin.end.destination.filtered.filter
command.clone.from.sourceDimension.begin.end.destination.filtered.filter.force
command.clone.from.sourceDimension.begin.end.destination.filtered.filter.move
command.clone.from.sourceDimension.begin.end.destination.filtered.filter.normal
command.clone.from.sourceDimension.begin.end.destination.masked
command.clone.from.sourceDimension.begin.end.destination.masked.force
command.clone.from.sourceDimension.begin.end.destination.masked.move
command.clone.from.sourceDimension.begin.end.destination.masked.normal
command.clone.from.sourceDimension.begin.end.destination.replace
command.clone.from.sourceDimension.begin.end.destination.replace.force
command.clone.from.sourceDimension.begin.end.destination.replace.move
command.clone.from.sourceDimension.begin.end.destination.replace.normal
command.clone.from.sourceDimension.begin.end.to
command.clone.from.sourceDimension.begin.end.to.targetDimension
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.filtered
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.filtered.filter
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.filtered.filter.force
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.filtered.filter.move
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.filtered.filter.normal
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.masked
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.masked.force
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.masked.move
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.masked.normal
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.replace
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.replace.force
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.replace.move
command.clone.from.sourceDimension.begin.end.to.targetDimension.destination.replace.normal
command.coe
command.coe.locate
command.coe.locate.recipe
command.coe.removevein
command.coe.removevein.pos
command.coe.setvein
command.coe.setvein.pos
command.coe.setvein.pos.recipe
command.coe.setvein.pos.recipe.multiplier
command.cofh
command.cofh.crafting
command.cofh.enderchest
command.cofh.enderchest.player
command.cofh.friend
command.cofh.friend.add
command.cofh.friend.add.players
command.cofh.friend.clear
command.cofh.friend.remove
command.cofh.friend.remove.players
command.cofh.heal
command.cofh.heal.targets
command.cofh.ignite
command.cofh.ignite.duration
command.cofh.ignite.targets
command.cofh.ignite.targets.duration
command.cofh.invis
command.cofh.invis.flag
command.cofh.invis.targets
command.cofh.invis.targets.flag
command.cofh.invuln
command.cofh.invuln.flag
command.cofh.invuln.targets
command.cofh.invuln.targets.flag
command.cofh.recharge
command.cofh.recharge.targets
command.cofh.repair
command.cofh.repair.targets
command.cofh.workbench
command.cofh.zap
command.cofh.zap.targets
command.compass_server
command.compass_server.build_scene
command.compass_server.build_scene.start
command.compass_server.build_scene.start.end
command.compass_server.build_scene.start.end.saveNbt
command.compass_server.build_scene.start.end.saveNbt.offset
command.computercraft
command.computercraft.dump
command.computercraft.dump.computer
command.computercraft.help
command.computercraft.help.dump
command.computercraft.help.queue
command.computercraft.help.shutdown
command.computercraft.help.tp
command.computercraft.help.turn-on
command.computercraft.help.view
command.computercraft.queue
command.computercraft.queue.computer
command.computercraft.queue.computer.args
command.computercraft.shutdown
command.computercraft.shutdown.computers
command.computercraft.tp
command.computercraft.tp.computer
command.computercraft.track
command.computercraft.track.dump
command.computercraft.track.dump.fields
command.computercraft.track.help
command.computercraft.track.help.dump
command.computercraft.track.help.start
command.computercraft.track.help.stop
command.computercraft.track.start
command.computercraft.track.stop
command.computercraft.turn-on
command.computercraft.turn-on.computers
command.computercraft.view
command.computercraft.view.computer
command.config
command.config.showfile
command.config.showfile.mod
command.config.showfile.mod.type
command.configuration
command.configuration.save
command.configuration.save.configId
command.configuration.saveAll
command.corgilib
command.corgilib.place_all
command.corgilib.place_all.features
command.corgilib.place_all.features.mod_id
command.corgilib.place_all.features.mod_id.block
command.corgilib.place_all.features.mod_id.block.depth
command.corgilib.place_all.structures
command.corgilib.place_all.structures.mod_id
command.corgilib.place_all.structures.mod_id.block
command.corgilib.place_all.structures.mod_id.block.depth
command.corgilib.place_all.templates
command.corgilib.place_all.templates.mod_id
command.corgilib.place_all.templates.mod_id.block
command.corgilib.place_all.templates.mod_id.block.depth
command.coshat
command.crafting
command.craftingtweaks
command.craftingtweaks.debug
command.create
command.create.clone
command.create.clone.begin
command.create.clone.begin.end
command.create.clone.begin.end.destination
command.create.clone.begin.end.destination.skipBlocks
command.create.config
command.create.config.path
command.create.config.path.set
command.create.config.path.set.value
command.create.coupling
command.create.coupling.add
command.create.coupling.add.cart1
command.create.coupling.add.cart1.cart2
command.create.coupling.add.carts
command.create.coupling.remove
command.create.coupling.remove.cart1
command.create.coupling.remove.cart1.cart2
command.create.coupling.removeAll
command.create.coupling.removeAll.cart
command.create.debuginfo
command.create.dismissFabulousWarning
command.create.fixLighting
command.create.glue
command.create.glue.from
command.create.glue.from.to
command.create.highlight
command.create.highlight.pos
command.create.highlight.pos.players
command.create.killTrain
command.create.killTrain.train
command.create.overlay
command.create.overlay.reset
command.create.passenger
command.create.passenger.rider
command.create.passenger.rider.vehicle
command.create.passenger.rider.vehicle.seatIndex
command.create.ponder
command.create.ponder.scene
command.create.ponder.scene.targets
command.create.rainbowDebug
command.create.rainbowDebug.off
command.create.rainbowDebug.on
command.create.trains
command.create.u
command.create.u.angle
command.create.u.angle.players
command.create.u.angle.players.mode
command.create.u.angle.players.mode.exponential
command.create.u.angle.players.mode.exponential.speed
command.create.u.angle.players.mode.linear
command.create.u.angle.players.mode.linear.speed
command.create.u.angle.players.pitch
command.create.u.angle.players.pitch.degrees
command.create.u.angle.players.yaw
command.create.u.angle.players.yaw.degrees
command.create.u.camera
command.create.u.camera.multiplier
command.create.u.camera.reset
command.create.u.clearRenderBuffers
command.create.u.flySpeed
command.create.u.flySpeed.reset
command.create.u.flySpeed.reset.target
command.create.u.flySpeed.speed
command.create.u.flySpeed.speed.target
command.create.u.replaceInCommandBlocks
command.create.u.replaceInCommandBlocks.begin
command.create.u.replaceInCommandBlocks.begin.end
command.create.u.replaceInCommandBlocks.begin.end.toReplace
command.create.u.replaceInCommandBlocks.begin.end.toReplace.replaceWith
command.create.util
command.create.util.angle
command.create.util.angle.players
command.create.util.angle.players.mode
command.create.util.angle.players.mode.exponential
command.create.util.angle.players.mode.exponential.speed
command.create.util.angle.players.mode.linear
command.create.util.angle.players.mode.linear.speed
command.create.util.angle.players.pitch
command.create.util.angle.players.pitch.degrees
command.create.util.angle.players.yaw
command.create.util.angle.players.yaw.degrees
command.create.util.camera
command.create.util.camera.multiplier
command.create.util.camera.reset
command.create.util.clearRenderBuffers
command.create.util.flySpeed
command.create.util.flySpeed.reset
command.create.util.flySpeed.reset.target
command.create.util.flySpeed.speed
command.create.util.flySpeed.speed.target
command.create.util.replaceInCommandBlocks
command.create.util.replaceInCommandBlocks.begin
command.create.util.replaceInCommandBlocks.begin.end
command.create.util.replaceInCommandBlocks.begin.end.toReplace
command.create.util.replaceInCommandBlocks.begin.end.toReplace.replaceWith
command.createDebugWizard
command.createDebugWizard.spell
command.createDebugWizard.spell.spellLevel
command.createDebugWizard.spell.spellLevel.targetsPlayer
command.createDebugWizard.spell.spellLevel.targetsPlayer.cancelAfterTicks
command.createImbuedSword
command.createImbuedSword.item
command.createImbuedSword.item.spell
command.createImbuedSword.item.spell.level
command.createScroll
command.createScroll.spell
command.createScroll.spell.level
command.createSpellBook
command.createSpellBook.slots
command.createSpellBook.slots.randomize
command.cucumber
command.cucumber.fillenergy
command.cucumber.fillenergy.block
command.cucumber.fillenergy.hand
command.curios
command.curios.add
command.curios.add.slot
command.curios.add.slot.player
command.curios.add.slot.player.amount
command.curios.clear
command.curios.clear.player
command.curios.clear.player.slot
command.curios.drop
command.curios.drop.player
command.curios.drop.player.slot
command.curios.list
command.curios.remove
command.curios.remove.slot
command.curios.remove.slot.player
command.curios.remove.slot.player.amount
command.curios.replace
command.curios.replace.slot
command.curios.replace.slot.index
command.curios.replace.slot.index.player
command.curios.replace.slot.index.player.from
command.curios.replace.slot.index.player.from.block
command.curios.replace.slot.index.player.from.block.source
command.curios.replace.slot.index.player.from.block.source.sourceSlot
command.curios.replace.slot.index.player.from.block.source.sourceSlot.modifier
command.curios.replace.slot.index.player.from.entity-curios
command.curios.replace.slot.index.player.from.entity-curios.sourceSlot
command.curios.replace.slot.index.player.from.entity-curios.sourceSlot.sourceIndex
command.curios.replace.slot.index.player.from.entity-curios.sourceSlot.sourceIndex.source
command.curios.replace.slot.index.player.from.entity-curios.sourceSlot.sourceIndex.source.modifier
command.curios.replace.slot.index.player.from.entity-vanilla
command.curios.replace.slot.index.player.from.entity-vanilla.source
command.curios.replace.slot.index.player.from.entity-vanilla.source.sourceSlot
command.curios.replace.slot.index.player.from.entity-vanilla.source.sourceSlot.modifier
command.curios.replace.slot.index.player.with
command.curios.replace.slot.index.player.with.item
command.curios.replace.slot.index.player.with.item.count
command.curios.reset
command.curios.reset.player
command.curios.set
command.curios.set.slot
command.curios.set.slot.player
command.curios.set.slot.player.amount
command.damage
command.damage.target
command.damage.target.amount
command.damage.target.amount.damageType
command.damage.target.amount.damageType.at
command.damage.target.amount.damageType.at.location
command.damage.target.amount.damageType.by
command.damage.target.amount.damageType.by.entity
command.damage.target.amount.damageType.by.entity.from
command.damage.target.amount.damageType.by.entity.from.cause
command.dankstorage
command.dankstorage.clear
command.dankstorage.clear.all
command.dankstorage.clear.dankstorage:frequency
command.dankstorage.lock
command.dankstorage.lock.dankstorage:frequency
command.dankstorage.reset_frequency
command.dankstorage.set_tier
command.dankstorage.set_tier.dankstorage:frequency
command.dankstorage.set_tier.dankstorage:frequency.tier
command.dankstorage.unlock
command.dankstorage.unlock.dankstorage:frequency
command.data
command.data.get
command.data.get.block
command.data.get.block.targetPos
command.data.get.block.targetPos.path
command.data.get.block.targetPos.path.scale
command.data.get.entity
command.data.get.entity.target
command.data.get.entity.target.path
command.data.get.entity.target.path.scale
command.data.get.storage
command.data.get.storage.target
command.data.get.storage.target.path
command.data.get.storage.target.path.scale
command.data.merge
command.data.merge.block
command.data.merge.block.targetPos
command.data.merge.block.targetPos.nbt
command.data.merge.entity
command.data.merge.entity.target
command.data.merge.entity.target.nbt
command.data.merge.storage
command.data.merge.storage.target
command.data.merge.storage.target.nbt
command.data.modify
command.data.modify.block
command.data.modify.block.targetPos
command.data.modify.block.targetPos.targetPath
command.data.modify.block.targetPos.targetPath.append
command.data.modify.block.targetPos.targetPath.append.from
command.data.modify.block.targetPos.targetPath.append.from.block
command.data.modify.block.targetPos.targetPath.append.from.block.sourcePos
command.data.modify.block.targetPos.targetPath.append.from.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.append.from.entity
command.data.modify.block.targetPos.targetPath.append.from.entity.source
command.data.modify.block.targetPos.targetPath.append.from.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.append.from.storage
command.data.modify.block.targetPos.targetPath.append.from.storage.source
command.data.modify.block.targetPos.targetPath.append.from.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.append.string
command.data.modify.block.targetPos.targetPath.append.string.block
command.data.modify.block.targetPos.targetPath.append.string.block.sourcePos
command.data.modify.block.targetPos.targetPath.append.string.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.append.string.block.sourcePos.sourcePath.start
command.data.modify.block.targetPos.targetPath.append.string.block.sourcePos.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.append.string.entity
command.data.modify.block.targetPos.targetPath.append.string.entity.source
command.data.modify.block.targetPos.targetPath.append.string.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.append.string.entity.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.append.string.entity.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.append.string.storage
command.data.modify.block.targetPos.targetPath.append.string.storage.source
command.data.modify.block.targetPos.targetPath.append.string.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.append.string.storage.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.append.string.storage.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.append.value
command.data.modify.block.targetPos.targetPath.append.value.value
command.data.modify.block.targetPos.targetPath.insert
command.data.modify.block.targetPos.targetPath.insert.index
command.data.modify.block.targetPos.targetPath.insert.index.from
command.data.modify.block.targetPos.targetPath.insert.index.from.block
command.data.modify.block.targetPos.targetPath.insert.index.from.block.sourcePos
command.data.modify.block.targetPos.targetPath.insert.index.from.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.insert.index.from.entity
command.data.modify.block.targetPos.targetPath.insert.index.from.entity.source
command.data.modify.block.targetPos.targetPath.insert.index.from.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.insert.index.from.storage
command.data.modify.block.targetPos.targetPath.insert.index.from.storage.source
command.data.modify.block.targetPos.targetPath.insert.index.from.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.insert.index.string
command.data.modify.block.targetPos.targetPath.insert.index.string.block
command.data.modify.block.targetPos.targetPath.insert.index.string.block.sourcePos
command.data.modify.block.targetPos.targetPath.insert.index.string.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.insert.index.string.block.sourcePos.sourcePath.start
command.data.modify.block.targetPos.targetPath.insert.index.string.block.sourcePos.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.insert.index.string.entity
command.data.modify.block.targetPos.targetPath.insert.index.string.entity.source
command.data.modify.block.targetPos.targetPath.insert.index.string.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.insert.index.string.entity.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.insert.index.string.entity.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.insert.index.string.storage
command.data.modify.block.targetPos.targetPath.insert.index.string.storage.source
command.data.modify.block.targetPos.targetPath.insert.index.string.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.insert.index.string.storage.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.insert.index.string.storage.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.insert.index.value
command.data.modify.block.targetPos.targetPath.insert.index.value.value
command.data.modify.block.targetPos.targetPath.merge
command.data.modify.block.targetPos.targetPath.merge.from
command.data.modify.block.targetPos.targetPath.merge.from.block
command.data.modify.block.targetPos.targetPath.merge.from.block.sourcePos
command.data.modify.block.targetPos.targetPath.merge.from.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.merge.from.entity
command.data.modify.block.targetPos.targetPath.merge.from.entity.source
command.data.modify.block.targetPos.targetPath.merge.from.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.merge.from.storage
command.data.modify.block.targetPos.targetPath.merge.from.storage.source
command.data.modify.block.targetPos.targetPath.merge.from.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.merge.string
command.data.modify.block.targetPos.targetPath.merge.string.block
command.data.modify.block.targetPos.targetPath.merge.string.block.sourcePos
command.data.modify.block.targetPos.targetPath.merge.string.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.merge.string.block.sourcePos.sourcePath.start
command.data.modify.block.targetPos.targetPath.merge.string.block.sourcePos.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.merge.string.entity
command.data.modify.block.targetPos.targetPath.merge.string.entity.source
command.data.modify.block.targetPos.targetPath.merge.string.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.merge.string.entity.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.merge.string.entity.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.merge.string.storage
command.data.modify.block.targetPos.targetPath.merge.string.storage.source
command.data.modify.block.targetPos.targetPath.merge.string.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.merge.string.storage.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.merge.string.storage.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.merge.value
command.data.modify.block.targetPos.targetPath.merge.value.value
command.data.modify.block.targetPos.targetPath.prepend
command.data.modify.block.targetPos.targetPath.prepend.from
command.data.modify.block.targetPos.targetPath.prepend.from.block
command.data.modify.block.targetPos.targetPath.prepend.from.block.sourcePos
command.data.modify.block.targetPos.targetPath.prepend.from.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.prepend.from.entity
command.data.modify.block.targetPos.targetPath.prepend.from.entity.source
command.data.modify.block.targetPos.targetPath.prepend.from.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.prepend.from.storage
command.data.modify.block.targetPos.targetPath.prepend.from.storage.source
command.data.modify.block.targetPos.targetPath.prepend.from.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.prepend.string
command.data.modify.block.targetPos.targetPath.prepend.string.block
command.data.modify.block.targetPos.targetPath.prepend.string.block.sourcePos
command.data.modify.block.targetPos.targetPath.prepend.string.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.prepend.string.block.sourcePos.sourcePath.start
command.data.modify.block.targetPos.targetPath.prepend.string.block.sourcePos.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.prepend.string.entity
command.data.modify.block.targetPos.targetPath.prepend.string.entity.source
command.data.modify.block.targetPos.targetPath.prepend.string.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.prepend.string.entity.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.prepend.string.entity.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.prepend.string.storage
command.data.modify.block.targetPos.targetPath.prepend.string.storage.source
command.data.modify.block.targetPos.targetPath.prepend.string.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.prepend.string.storage.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.prepend.string.storage.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.prepend.value
command.data.modify.block.targetPos.targetPath.prepend.value.value
command.data.modify.block.targetPos.targetPath.set
command.data.modify.block.targetPos.targetPath.set.from
command.data.modify.block.targetPos.targetPath.set.from.block
command.data.modify.block.targetPos.targetPath.set.from.block.sourcePos
command.data.modify.block.targetPos.targetPath.set.from.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.set.from.entity
command.data.modify.block.targetPos.targetPath.set.from.entity.source
command.data.modify.block.targetPos.targetPath.set.from.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.set.from.storage
command.data.modify.block.targetPos.targetPath.set.from.storage.source
command.data.modify.block.targetPos.targetPath.set.from.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.set.string
command.data.modify.block.targetPos.targetPath.set.string.block
command.data.modify.block.targetPos.targetPath.set.string.block.sourcePos
command.data.modify.block.targetPos.targetPath.set.string.block.sourcePos.sourcePath
command.data.modify.block.targetPos.targetPath.set.string.block.sourcePos.sourcePath.start
command.data.modify.block.targetPos.targetPath.set.string.block.sourcePos.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.set.string.entity
command.data.modify.block.targetPos.targetPath.set.string.entity.source
command.data.modify.block.targetPos.targetPath.set.string.entity.source.sourcePath
command.data.modify.block.targetPos.targetPath.set.string.entity.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.set.string.entity.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.set.string.storage
command.data.modify.block.targetPos.targetPath.set.string.storage.source
command.data.modify.block.targetPos.targetPath.set.string.storage.source.sourcePath
command.data.modify.block.targetPos.targetPath.set.string.storage.source.sourcePath.start
command.data.modify.block.targetPos.targetPath.set.string.storage.source.sourcePath.start.end
command.data.modify.block.targetPos.targetPath.set.value
command.data.modify.block.targetPos.targetPath.set.value.value
command.data.modify.entity
command.data.modify.entity.target
command.data.modify.entity.target.targetPath
command.data.modify.entity.target.targetPath.append
command.data.modify.entity.target.targetPath.append.from
command.data.modify.entity.target.targetPath.append.from.block
command.data.modify.entity.target.targetPath.append.from.block.sourcePos
command.data.modify.entity.target.targetPath.append.from.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.append.from.entity
command.data.modify.entity.target.targetPath.append.from.entity.source
command.data.modify.entity.target.targetPath.append.from.entity.source.sourcePath
command.data.modify.entity.target.targetPath.append.from.storage
command.data.modify.entity.target.targetPath.append.from.storage.source
command.data.modify.entity.target.targetPath.append.from.storage.source.sourcePath
command.data.modify.entity.target.targetPath.append.string
command.data.modify.entity.target.targetPath.append.string.block
command.data.modify.entity.target.targetPath.append.string.block.sourcePos
command.data.modify.entity.target.targetPath.append.string.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.append.string.block.sourcePos.sourcePath.start
command.data.modify.entity.target.targetPath.append.string.block.sourcePos.sourcePath.start.end
command.data.modify.entity.target.targetPath.append.string.entity
command.data.modify.entity.target.targetPath.append.string.entity.source
command.data.modify.entity.target.targetPath.append.string.entity.source.sourcePath
command.data.modify.entity.target.targetPath.append.string.entity.source.sourcePath.start
command.data.modify.entity.target.targetPath.append.string.entity.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.append.string.storage
command.data.modify.entity.target.targetPath.append.string.storage.source
command.data.modify.entity.target.targetPath.append.string.storage.source.sourcePath
command.data.modify.entity.target.targetPath.append.string.storage.source.sourcePath.start
command.data.modify.entity.target.targetPath.append.string.storage.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.append.value
command.data.modify.entity.target.targetPath.append.value.value
command.data.modify.entity.target.targetPath.insert
command.data.modify.entity.target.targetPath.insert.index
command.data.modify.entity.target.targetPath.insert.index.from
command.data.modify.entity.target.targetPath.insert.index.from.block
command.data.modify.entity.target.targetPath.insert.index.from.block.sourcePos
command.data.modify.entity.target.targetPath.insert.index.from.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.insert.index.from.entity
command.data.modify.entity.target.targetPath.insert.index.from.entity.source
command.data.modify.entity.target.targetPath.insert.index.from.entity.source.sourcePath
command.data.modify.entity.target.targetPath.insert.index.from.storage
command.data.modify.entity.target.targetPath.insert.index.from.storage.source
command.data.modify.entity.target.targetPath.insert.index.from.storage.source.sourcePath
command.data.modify.entity.target.targetPath.insert.index.string
command.data.modify.entity.target.targetPath.insert.index.string.block
command.data.modify.entity.target.targetPath.insert.index.string.block.sourcePos
command.data.modify.entity.target.targetPath.insert.index.string.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.insert.index.string.block.sourcePos.sourcePath.start
command.data.modify.entity.target.targetPath.insert.index.string.block.sourcePos.sourcePath.start.end
command.data.modify.entity.target.targetPath.insert.index.string.entity
command.data.modify.entity.target.targetPath.insert.index.string.entity.source
command.data.modify.entity.target.targetPath.insert.index.string.entity.source.sourcePath
command.data.modify.entity.target.targetPath.insert.index.string.entity.source.sourcePath.start
command.data.modify.entity.target.targetPath.insert.index.string.entity.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.insert.index.string.storage
command.data.modify.entity.target.targetPath.insert.index.string.storage.source
command.data.modify.entity.target.targetPath.insert.index.string.storage.source.sourcePath
command.data.modify.entity.target.targetPath.insert.index.string.storage.source.sourcePath.start
command.data.modify.entity.target.targetPath.insert.index.string.storage.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.insert.index.value
command.data.modify.entity.target.targetPath.insert.index.value.value
command.data.modify.entity.target.targetPath.merge
command.data.modify.entity.target.targetPath.merge.from
command.data.modify.entity.target.targetPath.merge.from.block
command.data.modify.entity.target.targetPath.merge.from.block.sourcePos
command.data.modify.entity.target.targetPath.merge.from.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.merge.from.entity
command.data.modify.entity.target.targetPath.merge.from.entity.source
command.data.modify.entity.target.targetPath.merge.from.entity.source.sourcePath
command.data.modify.entity.target.targetPath.merge.from.storage
command.data.modify.entity.target.targetPath.merge.from.storage.source
command.data.modify.entity.target.targetPath.merge.from.storage.source.sourcePath
command.data.modify.entity.target.targetPath.merge.string
command.data.modify.entity.target.targetPath.merge.string.block
command.data.modify.entity.target.targetPath.merge.string.block.sourcePos
command.data.modify.entity.target.targetPath.merge.string.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.merge.string.block.sourcePos.sourcePath.start
command.data.modify.entity.target.targetPath.merge.string.block.sourcePos.sourcePath.start.end
command.data.modify.entity.target.targetPath.merge.string.entity
command.data.modify.entity.target.targetPath.merge.string.entity.source
command.data.modify.entity.target.targetPath.merge.string.entity.source.sourcePath
command.data.modify.entity.target.targetPath.merge.string.entity.source.sourcePath.start
command.data.modify.entity.target.targetPath.merge.string.entity.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.merge.string.storage
command.data.modify.entity.target.targetPath.merge.string.storage.source
command.data.modify.entity.target.targetPath.merge.string.storage.source.sourcePath
command.data.modify.entity.target.targetPath.merge.string.storage.source.sourcePath.start
command.data.modify.entity.target.targetPath.merge.string.storage.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.merge.value
command.data.modify.entity.target.targetPath.merge.value.value
command.data.modify.entity.target.targetPath.prepend
command.data.modify.entity.target.targetPath.prepend.from
command.data.modify.entity.target.targetPath.prepend.from.block
command.data.modify.entity.target.targetPath.prepend.from.block.sourcePos
command.data.modify.entity.target.targetPath.prepend.from.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.prepend.from.entity
command.data.modify.entity.target.targetPath.prepend.from.entity.source
command.data.modify.entity.target.targetPath.prepend.from.entity.source.sourcePath
command.data.modify.entity.target.targetPath.prepend.from.storage
command.data.modify.entity.target.targetPath.prepend.from.storage.source
command.data.modify.entity.target.targetPath.prepend.from.storage.source.sourcePath
command.data.modify.entity.target.targetPath.prepend.string
command.data.modify.entity.target.targetPath.prepend.string.block
command.data.modify.entity.target.targetPath.prepend.string.block.sourcePos
command.data.modify.entity.target.targetPath.prepend.string.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.prepend.string.block.sourcePos.sourcePath.start
command.data.modify.entity.target.targetPath.prepend.string.block.sourcePos.sourcePath.start.end
command.data.modify.entity.target.targetPath.prepend.string.entity
command.data.modify.entity.target.targetPath.prepend.string.entity.source
command.data.modify.entity.target.targetPath.prepend.string.entity.source.sourcePath
command.data.modify.entity.target.targetPath.prepend.string.entity.source.sourcePath.start
command.data.modify.entity.target.targetPath.prepend.string.entity.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.prepend.string.storage
command.data.modify.entity.target.targetPath.prepend.string.storage.source
command.data.modify.entity.target.targetPath.prepend.string.storage.source.sourcePath
command.data.modify.entity.target.targetPath.prepend.string.storage.source.sourcePath.start
command.data.modify.entity.target.targetPath.prepend.string.storage.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.prepend.value
command.data.modify.entity.target.targetPath.prepend.value.value
command.data.modify.entity.target.targetPath.set
command.data.modify.entity.target.targetPath.set.from
command.data.modify.entity.target.targetPath.set.from.block
command.data.modify.entity.target.targetPath.set.from.block.sourcePos
command.data.modify.entity.target.targetPath.set.from.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.set.from.entity
command.data.modify.entity.target.targetPath.set.from.entity.source
command.data.modify.entity.target.targetPath.set.from.entity.source.sourcePath
command.data.modify.entity.target.targetPath.set.from.storage
command.data.modify.entity.target.targetPath.set.from.storage.source
command.data.modify.entity.target.targetPath.set.from.storage.source.sourcePath
command.data.modify.entity.target.targetPath.set.string
command.data.modify.entity.target.targetPath.set.string.block
command.data.modify.entity.target.targetPath.set.string.block.sourcePos
command.data.modify.entity.target.targetPath.set.string.block.sourcePos.sourcePath
command.data.modify.entity.target.targetPath.set.string.block.sourcePos.sourcePath.start
command.data.modify.entity.target.targetPath.set.string.block.sourcePos.sourcePath.start.end
command.data.modify.entity.target.targetPath.set.string.entity
command.data.modify.entity.target.targetPath.set.string.entity.source
command.data.modify.entity.target.targetPath.set.string.entity.source.sourcePath
command.data.modify.entity.target.targetPath.set.string.entity.source.sourcePath.start
command.data.modify.entity.target.targetPath.set.string.entity.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.set.string.storage
command.data.modify.entity.target.targetPath.set.string.storage.source
command.data.modify.entity.target.targetPath.set.string.storage.source.sourcePath
command.data.modify.entity.target.targetPath.set.string.storage.source.sourcePath.start
command.data.modify.entity.target.targetPath.set.string.storage.source.sourcePath.start.end
command.data.modify.entity.target.targetPath.set.value
command.data.modify.entity.target.targetPath.set.value.value
command.data.modify.storage
command.data.modify.storage.target
command.data.modify.storage.target.targetPath
command.data.modify.storage.target.targetPath.append
command.data.modify.storage.target.targetPath.append.from
command.data.modify.storage.target.targetPath.append.from.block
command.data.modify.storage.target.targetPath.append.from.block.sourcePos
command.data.modify.storage.target.targetPath.append.from.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.append.from.entity
command.data.modify.storage.target.targetPath.append.from.entity.source
command.data.modify.storage.target.targetPath.append.from.entity.source.sourcePath
command.data.modify.storage.target.targetPath.append.from.storage
command.data.modify.storage.target.targetPath.append.from.storage.source
command.data.modify.storage.target.targetPath.append.from.storage.source.sourcePath
command.data.modify.storage.target.targetPath.append.string
command.data.modify.storage.target.targetPath.append.string.block
command.data.modify.storage.target.targetPath.append.string.block.sourcePos
command.data.modify.storage.target.targetPath.append.string.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.append.string.block.sourcePos.sourcePath.start
command.data.modify.storage.target.targetPath.append.string.block.sourcePos.sourcePath.start.end
command.data.modify.storage.target.targetPath.append.string.entity
command.data.modify.storage.target.targetPath.append.string.entity.source
command.data.modify.storage.target.targetPath.append.string.entity.source.sourcePath
command.data.modify.storage.target.targetPath.append.string.entity.source.sourcePath.start
command.data.modify.storage.target.targetPath.append.string.entity.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.append.string.storage
command.data.modify.storage.target.targetPath.append.string.storage.source
command.data.modify.storage.target.targetPath.append.string.storage.source.sourcePath
command.data.modify.storage.target.targetPath.append.string.storage.source.sourcePath.start
command.data.modify.storage.target.targetPath.append.string.storage.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.append.value
command.data.modify.storage.target.targetPath.append.value.value
command.data.modify.storage.target.targetPath.insert
command.data.modify.storage.target.targetPath.insert.index
command.data.modify.storage.target.targetPath.insert.index.from
command.data.modify.storage.target.targetPath.insert.index.from.block
command.data.modify.storage.target.targetPath.insert.index.from.block.sourcePos
command.data.modify.storage.target.targetPath.insert.index.from.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.insert.index.from.entity
command.data.modify.storage.target.targetPath.insert.index.from.entity.source
command.data.modify.storage.target.targetPath.insert.index.from.entity.source.sourcePath
command.data.modify.storage.target.targetPath.insert.index.from.storage
command.data.modify.storage.target.targetPath.insert.index.from.storage.source
command.data.modify.storage.target.targetPath.insert.index.from.storage.source.sourcePath
command.data.modify.storage.target.targetPath.insert.index.string
command.data.modify.storage.target.targetPath.insert.index.string.block
command.data.modify.storage.target.targetPath.insert.index.string.block.sourcePos
command.data.modify.storage.target.targetPath.insert.index.string.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.insert.index.string.block.sourcePos.sourcePath.start
command.data.modify.storage.target.targetPath.insert.index.string.block.sourcePos.sourcePath.start.end
command.data.modify.storage.target.targetPath.insert.index.string.entity
command.data.modify.storage.target.targetPath.insert.index.string.entity.source
command.data.modify.storage.target.targetPath.insert.index.string.entity.source.sourcePath
command.data.modify.storage.target.targetPath.insert.index.string.entity.source.sourcePath.start
command.data.modify.storage.target.targetPath.insert.index.string.entity.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.insert.index.string.storage
command.data.modify.storage.target.targetPath.insert.index.string.storage.source
command.data.modify.storage.target.targetPath.insert.index.string.storage.source.sourcePath
command.data.modify.storage.target.targetPath.insert.index.string.storage.source.sourcePath.start
command.data.modify.storage.target.targetPath.insert.index.string.storage.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.insert.index.value
command.data.modify.storage.target.targetPath.insert.index.value.value
command.data.modify.storage.target.targetPath.merge
command.data.modify.storage.target.targetPath.merge.from
command.data.modify.storage.target.targetPath.merge.from.block
command.data.modify.storage.target.targetPath.merge.from.block.sourcePos
command.data.modify.storage.target.targetPath.merge.from.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.merge.from.entity
command.data.modify.storage.target.targetPath.merge.from.entity.source
command.data.modify.storage.target.targetPath.merge.from.entity.source.sourcePath
command.data.modify.storage.target.targetPath.merge.from.storage
command.data.modify.storage.target.targetPath.merge.from.storage.source
command.data.modify.storage.target.targetPath.merge.from.storage.source.sourcePath
command.data.modify.storage.target.targetPath.merge.string
command.data.modify.storage.target.targetPath.merge.string.block
command.data.modify.storage.target.targetPath.merge.string.block.sourcePos
command.data.modify.storage.target.targetPath.merge.string.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.merge.string.block.sourcePos.sourcePath.start
command.data.modify.storage.target.targetPath.merge.string.block.sourcePos.sourcePath.start.end
command.data.modify.storage.target.targetPath.merge.string.entity
command.data.modify.storage.target.targetPath.merge.string.entity.source
command.data.modify.storage.target.targetPath.merge.string.entity.source.sourcePath
command.data.modify.storage.target.targetPath.merge.string.entity.source.sourcePath.start
command.data.modify.storage.target.targetPath.merge.string.entity.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.merge.string.storage
command.data.modify.storage.target.targetPath.merge.string.storage.source
command.data.modify.storage.target.targetPath.merge.string.storage.source.sourcePath
command.data.modify.storage.target.targetPath.merge.string.storage.source.sourcePath.start
command.data.modify.storage.target.targetPath.merge.string.storage.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.merge.value
command.data.modify.storage.target.targetPath.merge.value.value
command.data.modify.storage.target.targetPath.prepend
command.data.modify.storage.target.targetPath.prepend.from
command.data.modify.storage.target.targetPath.prepend.from.block
command.data.modify.storage.target.targetPath.prepend.from.block.sourcePos
command.data.modify.storage.target.targetPath.prepend.from.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.prepend.from.entity
command.data.modify.storage.target.targetPath.prepend.from.entity.source
command.data.modify.storage.target.targetPath.prepend.from.entity.source.sourcePath
command.data.modify.storage.target.targetPath.prepend.from.storage
command.data.modify.storage.target.targetPath.prepend.from.storage.source
command.data.modify.storage.target.targetPath.prepend.from.storage.source.sourcePath
command.data.modify.storage.target.targetPath.prepend.string
command.data.modify.storage.target.targetPath.prepend.string.block
command.data.modify.storage.target.targetPath.prepend.string.block.sourcePos
command.data.modify.storage.target.targetPath.prepend.string.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.prepend.string.block.sourcePos.sourcePath.start
command.data.modify.storage.target.targetPath.prepend.string.block.sourcePos.sourcePath.start.end
command.data.modify.storage.target.targetPath.prepend.string.entity
command.data.modify.storage.target.targetPath.prepend.string.entity.source
command.data.modify.storage.target.targetPath.prepend.string.entity.source.sourcePath
command.data.modify.storage.target.targetPath.prepend.string.entity.source.sourcePath.start
command.data.modify.storage.target.targetPath.prepend.string.entity.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.prepend.string.storage
command.data.modify.storage.target.targetPath.prepend.string.storage.source
command.data.modify.storage.target.targetPath.prepend.string.storage.source.sourcePath
command.data.modify.storage.target.targetPath.prepend.string.storage.source.sourcePath.start
command.data.modify.storage.target.targetPath.prepend.string.storage.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.prepend.value
command.data.modify.storage.target.targetPath.prepend.value.value
command.data.modify.storage.target.targetPath.set
command.data.modify.storage.target.targetPath.set.from
command.data.modify.storage.target.targetPath.set.from.block
command.data.modify.storage.target.targetPath.set.from.block.sourcePos
command.data.modify.storage.target.targetPath.set.from.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.set.from.entity
command.data.modify.storage.target.targetPath.set.from.entity.source
command.data.modify.storage.target.targetPath.set.from.entity.source.sourcePath
command.data.modify.storage.target.targetPath.set.from.storage
command.data.modify.storage.target.targetPath.set.from.storage.source
command.data.modify.storage.target.targetPath.set.from.storage.source.sourcePath
command.data.modify.storage.target.targetPath.set.string
command.data.modify.storage.target.targetPath.set.string.block
command.data.modify.storage.target.targetPath.set.string.block.sourcePos
command.data.modify.storage.target.targetPath.set.string.block.sourcePos.sourcePath
command.data.modify.storage.target.targetPath.set.string.block.sourcePos.sourcePath.start
command.data.modify.storage.target.targetPath.set.string.block.sourcePos.sourcePath.start.end
command.data.modify.storage.target.targetPath.set.string.entity
command.data.modify.storage.target.targetPath.set.string.entity.source
command.data.modify.storage.target.targetPath.set.string.entity.source.sourcePath
command.data.modify.storage.target.targetPath.set.string.entity.source.sourcePath.start
command.data.modify.storage.target.targetPath.set.string.entity.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.set.string.storage
command.data.modify.storage.target.targetPath.set.string.storage.source
command.data.modify.storage.target.targetPath.set.string.storage.source.sourcePath
command.data.modify.storage.target.targetPath.set.string.storage.source.sourcePath.start
command.data.modify.storage.target.targetPath.set.string.storage.source.sourcePath.start.end
command.data.modify.storage.target.targetPath.set.value
command.data.modify.storage.target.targetPath.set.value.value
command.data.remove
command.data.remove.block
command.data.remove.block.targetPos
command.data.remove.block.targetPos.path
command.data.remove.entity
command.data.remove.entity.target
command.data.remove.entity.target.path
command.data.remove.storage
command.data.remove.storage.target
command.data.remove.storage.target.path
command.datapack
command.datapack.disable
command.datapack.disable.name
command.datapack.enable
command.datapack.enable.name
command.datapack.enable.name.after
command.datapack.enable.name.after.existing
command.datapack.enable.name.before
command.datapack.enable.name.before.existing
command.datapack.enable.name.first
command.datapack.enable.name.last
command.datapack.list
command.datapack.list.available
command.datapack.list.enabled
command.de_kaboom
command.de_kaboom.abort
command.de_kaboom.detonate
command.de_kaboom.radius
command.de_kaboom.radius.position
command.de_kaboom.radius.position.effect_only
command.de_kaboom.radius.position.effect_only.flash
command.de_kaboom.radius.position.no_effect
command.de_kaboom.radius.position.prime
command.de_kaboom.radius.position.prime.no_effect
command.debug
command.debug.ai
command.debug.function
command.debug.function.name
command.debug.start
command.debug.stop
command.deepresonance
command.deepresonance.create
command.deepresonance.create.special
command.defaultgamemode
command.defaultgamemode.gamemode
command.delhome
command.delhome.name
command.delwarp
command.delwarp.name
command.deop
command.deop.targets
command.devotion
command.devotion.targets
command.devotion.targets.get
command.devotion.targets.get.deity
command.devotion.targets.set
command.devotion.targets.set.deity
command.devotion.targets.set.deity.qt
command.devotion.targets.tryfix
command.difficulty
command.difficulty.easy
command.difficulty.hard
command.difficulty.normal
command.difficulty.peaceful
command.dimtablet
command.dimtablet.add
command.dimtablet.list
command.dimtablet.remove
command.dimtablet.removeAll
command.draconic_hud_config
command.dumpregistry
command.effect
command.effect.clear
command.effect.clear.targets
command.effect.clear.targets.effect
command.effect.give
command.effect.give.targets
command.effect.give.targets.effect
command.effect.give.targets.effect.infinite
command.effect.give.targets.effect.infinite.amplifier
command.effect.give.targets.effect.infinite.amplifier.hideParticles
command.effect.give.targets.effect.seconds
command.effect.give.targets.effect.seconds.amplifier
command.effect.give.targets.effect.seconds.amplifier.hideParticles
command.enchant
command.enchant.targets
command.enchant.targets.enchantment
command.enchant.targets.enchantment.level
command.end_island
command.end_island.reset
command.end_island.reset.forceNewPortalPos
command.enderchest
command.enderchest.player
command.environmental_hazard
command.environmental_hazard.clear
command.environmental_hazard.clear.source
command.environmental_hazard.clear.source.condition
command.environmental_hazard.condition
command.environmental_hazard.condition.can_spread
command.environmental_hazard.condition.can_spread.source
command.environmental_hazard.condition.can_spread.source.chunk
command.environmental_hazard.condition.can_spread.source.chunk.strength
command.environmental_hazard.condition.can_spread.source.local
command.environmental_hazard.condition.can_spread.source.local.from
command.environmental_hazard.condition.can_spread.source.local.from.to
command.er
command.er.coils
command.er.coils.get
command.er.coils.get.tag
command.er.coils.list
command.er.coils.set
command.er.coils.set.tag
command.er.coils.set.tag.bonus
command.er.coils.set.tag.bonus.value
command.er.coils.set.tag.efficiency
command.er.coils.set.tag.efficiency.value
command.er.coils.set.tag.energyExtractionRate
command.er.coils.set.tag.energyExtractionRate.value
command.er.moderators
command.er.moderators.get
command.er.moderators.get.name
command.er.moderators.list
command.er.moderators.set
command.er.moderators.set.block
command.er.moderators.set.block.absorption
command.er.moderators.set.block.absorption.value
command.er.moderators.set.block.heatConductivity
command.er.moderators.set.block.heatConductivity.value
command.er.moderators.set.block.heatEfficiency
command.er.moderators.set.block.heatEfficiency.value
command.er.moderators.set.block.moderation
command.er.moderators.set.block.moderation.value
command.er.reactants
command.er.reactants.get
command.er.reactants.get.name
command.er.reactants.list
command.er.reactants.set
command.er.reactants.set.name
command.er.reactants.set.name.absorption
command.er.reactants.set.name.absorption.value
command.er.reactants.set.name.colour
command.er.reactants.set.name.colour.value
command.er.reactants.set.name.fissionevents
command.er.reactants.set.name.fissionevents.value
command.er.reactants.set.name.fuelunits
command.er.reactants.set.name.fuelunits.value
command.er.reactants.set.name.hardness
command.er.reactants.set.name.hardness.value
command.er.reactants.set.name.moderation
command.er.reactants.set.name.moderation.value
command.er.reaction
command.er.reaction.get
command.er.reaction.get.name
command.er.reaction.list
command.er.reaction.set
command.er.reaction.set.name
command.er.reaction.set.name.fissionRate
command.er.reaction.set.name.fissionRate.value
command.er.reaction.set.name.reactivity
command.er.reaction.set.name.reactivity.value
command.execute
command.execute.align
command.execute.anchored
command.execute.as
command.execute.at
command.execute.facing
command.execute.facing.entity
command.execute.facing.entity.targets
command.execute.if
command.execute.if.biome
command.execute.if.biome.pos
command.execute.if.block
command.execute.if.block.pos
command.execute.if.blocks
command.execute.if.blocks.start
command.execute.if.blocks.start.end
command.execute.if.blocks.start.end.destination
command.execute.if.data
command.execute.if.data.block
command.execute.if.data.block.sourcePos
command.execute.if.data.entity
command.execute.if.data.entity.source
command.execute.if.data.storage
command.execute.if.data.storage.source
command.execute.if.dimension
command.execute.if.entity
command.execute.if.loaded
command.execute.if.predicate
command.execute.if.score
command.execute.if.score.target
command.execute.if.score.target.targetObjective
command.execute.if.score.target.targetObjective.<
command.execute.if.score.target.targetObjective.<.source
command.execute.if.score.target.targetObjective.<=
command.execute.if.score.target.targetObjective.<=.source
command.execute.if.score.target.targetObjective.=
command.execute.if.score.target.targetObjective.=.source
command.execute.if.score.target.targetObjective.>
command.execute.if.score.target.targetObjective.>.source
command.execute.if.score.target.targetObjective.>=
command.execute.if.score.target.targetObjective.>=.source
command.execute.if.score.target.targetObjective.matches
command.execute.in
command.execute.on
command.execute.positioned
command.execute.positioned.as
command.execute.positioned.over
command.execute.rotated
command.execute.rotated.as
command.execute.run
command.execute.store
command.execute.store.result
command.execute.store.result.block
command.execute.store.result.block.targetPos
command.execute.store.result.block.targetPos.path
command.execute.store.result.block.targetPos.path.byte
command.execute.store.result.block.targetPos.path.double
command.execute.store.result.block.targetPos.path.float
command.execute.store.result.block.targetPos.path.int
command.execute.store.result.block.targetPos.path.long
command.execute.store.result.block.targetPos.path.short
command.execute.store.result.bossbar
command.execute.store.result.bossbar.id
command.execute.store.result.entity
command.execute.store.result.entity.target
command.execute.store.result.entity.target.path
command.execute.store.result.entity.target.path.byte
command.execute.store.result.entity.target.path.double
command.execute.store.result.entity.target.path.float
command.execute.store.result.entity.target.path.int
command.execute.store.result.entity.target.path.long
command.execute.store.result.entity.target.path.short
command.execute.store.result.score
command.execute.store.result.score.targets
command.execute.store.result.storage
command.execute.store.result.storage.target
command.execute.store.result.storage.target.path
command.execute.store.result.storage.target.path.byte
command.execute.store.result.storage.target.path.double
command.execute.store.result.storage.target.path.float
command.execute.store.result.storage.target.path.int
command.execute.store.result.storage.target.path.long
command.execute.store.result.storage.target.path.short
command.execute.store.success
command.execute.store.success.block
command.execute.store.success.block.targetPos
command.execute.store.success.block.targetPos.path
command.execute.store.success.block.targetPos.path.byte
command.execute.store.success.block.targetPos.path.double
command.execute.store.success.block.targetPos.path.float
command.execute.store.success.block.targetPos.path.int
command.execute.store.success.block.targetPos.path.long
command.execute.store.success.block.targetPos.path.short
command.execute.store.success.bossbar
command.execute.store.success.bossbar.id
command.execute.store.success.entity
command.execute.store.success.entity.target
command.execute.store.success.entity.target.path
command.execute.store.success.entity.target.path.byte
command.execute.store.success.entity.target.path.double
command.execute.store.success.entity.target.path.float
command.execute.store.success.entity.target.path.int
command.execute.store.success.entity.target.path.long
command.execute.store.success.entity.target.path.short
command.execute.store.success.score
command.execute.store.success.score.targets
command.execute.store.success.storage
command.execute.store.success.storage.target
command.execute.store.success.storage.target.path
command.execute.store.success.storage.target.path.byte
command.execute.store.success.storage.target.path.double
command.execute.store.success.storage.target.path.float
command.execute.store.success.storage.target.path.int
command.execute.store.success.storage.target.path.long
command.execute.store.success.storage.target.path.short
command.execute.summon
command.execute.unless
command.execute.unless.biome
command.execute.unless.biome.pos
command.execute.unless.block
command.execute.unless.block.pos
command.execute.unless.blocks
command.execute.unless.blocks.start
command.execute.unless.blocks.start.end
command.execute.unless.blocks.start.end.destination
command.execute.unless.data
command.execute.unless.data.block
command.execute.unless.data.block.sourcePos
command.execute.unless.data.entity
command.execute.unless.data.entity.source
command.execute.unless.data.storage
command.execute.unless.data.storage.source
command.execute.unless.dimension
command.execute.unless.entity
command.execute.unless.loaded
command.execute.unless.predicate
command.execute.unless.score
command.execute.unless.score.target
command.execute.unless.score.target.targetObjective
command.execute.unless.score.target.targetObjective.<
command.execute.unless.score.target.targetObjective.<.source
command.execute.unless.score.target.targetObjective.<=
command.execute.unless.score.target.targetObjective.<=.source
command.execute.unless.score.target.targetObjective.=
command.execute.unless.score.target.targetObjective.=.source
command.execute.unless.score.target.targetObjective.>
command.execute.unless.score.target.targetObjective.>.source
command.execute.unless.score.target.targetObjective.>=
command.execute.unless.score.target.targetObjective.>=.source
command.execute.unless.score.target.targetObjective.matches
command.experience
command.experience.add
command.experience.add.targets
command.experience.add.targets.amount
command.experience.add.targets.amount.levels
command.experience.add.targets.amount.points
command.experience.query
command.experience.query.targets
command.experience.query.targets.levels
command.experience.query.targets.points
command.experience.set
command.experience.set.targets
command.experience.set.targets.amount
command.experience.set.targets.amount.levels
command.experience.set.targets.amount.points
command.extinguish
command.extinguish.player
command.feed
command.feed.player
command.fill
command.fill.from
command.fill.from.to
command.fill.from.to.block
command.fill.from.to.block.destroy
command.fill.from.to.block.hollow
command.fill.from.to.block.keep
command.fill.from.to.block.outline
command.fill.from.to.block.replace
command.fill.from.to.block.replace.filter
command.fillbiome
command.fillbiome.from
command.fillbiome.from.to
command.fillbiome.from.to.biome
command.fillbiome.from.to.biome.replace
command.fillbiome.from.to.biome.replace.filter
command.flib
command.flib.attribute
command.flib.attribute.attribute
command.flib.attribute.attribute.add
command.flib.attribute.attribute.add.player
command.flib.attribute.attribute.add.player.value
command.flib.attribute.attribute.factor
command.flib.attribute.attribute.factor.player
command.flib.attribute.attribute.factor.player.value
command.flib.attribute.attribute.random
command.flib.attribute.attribute.random.player
command.flib.attribute.attribute.random.player.min
command.flib.attribute.attribute.random.player.min.max
command.flib.attribute.attribute.reset
command.flib.attribute.attribute.reset.player
command.flib.debug
command.flib.debug.itemheld
command.flib.debug.itemheld.nbt
command.flib.debug.itemheld.tags
command.flib.debug.player
command.flib.debug.player.p
command.flib.debug.player.p.info
command.flib.gamemode
command.flib.gamemode.player
command.flib.gamemode.player.value
command.flib.health
command.flib.health.add
command.flib.health.add.player
command.flib.health.add.player.value
command.flib.health.factor
command.flib.health.factor.player
command.flib.health.factor.player.value
command.flib.health.random
command.flib.health.random.player
command.flib.health.random.player.min
command.flib.health.random.player.min.max
command.flib.health.set
command.flib.health.set.player
command.flib.health.set.player.value
command.flib.hearts
command.flib.hearts.add
command.flib.hearts.add.player
command.flib.hearts.add.player.value
command.flib.hearts.factor
command.flib.hearts.factor.player
command.flib.hearts.factor.player.value
command.flib.hearts.random
command.flib.hearts.random.player
command.flib.hearts.random.player.min
command.flib.hearts.random.player.min.max
command.flib.hearts.set
command.flib.hearts.set.player
command.flib.hearts.set.player.value
command.flib.help
command.flib.hunger
command.flib.hunger.add
command.flib.hunger.add.player
command.flib.hunger.add.player.value
command.flib.hunger.factor
command.flib.hunger.factor.player
command.flib.hunger.factor.player.value
command.flib.hunger.random
command.flib.hunger.random.player
command.flib.hunger.random.player.min
command.flib.hunger.random.player.min.max
command.flib.hunger.set
command.flib.hunger.set.player
command.flib.hunger.set.player.value
command.flib.scoreboard
command.flib.scoreboard.add
command.flib.scoreboard.add.targets
command.flib.scoreboard.add.targets.value
command.flib.scoreboard.add.targets.value.objective
command.flib.scoreboard.random
command.flib.scoreboard.random.targets
command.flib.scoreboard.random.targets.min
command.flib.scoreboard.random.targets.min.max
command.flib.scoreboard.random.targets.min.max.objective
command.flib.scoreboard.test
command.flib.scoreboard.test.targets
command.flib.scoreboard.test.targets.objective
command.flib.tpx
command.flib.tpx.dim
command.flib.tpx.dim.x
command.flib.tpx.dim.x.y
command.flib.tpx.dim.x.y.z
command.flib.tpx.dim.x.y.z.p
command.fluxnetworks
command.fluxnetworks.superadmin
command.fluxnetworks.superadmin.targets
command.fluxnetworks.superadmin.targets.enable
command.fly
command.fly.player
command.foodlist
command.foodlist.clear
command.foodlist.clear.target
command.foodlist.size
command.foodlist.size.target
command.foodlist.sync
command.foodlist.sync.target
command.forceload
command.forceload.add
command.forceload.add.from
command.forceload.add.from.to
command.forceload.query
command.forceload.query.pos
command.forceload.remove
command.forceload.remove.all
command.forceload.remove.from
command.forceload.remove.from.to
command.forge
command.forge.dimensions
command.forge.entity
command.forge.entity.list
command.forge.entity.list.filter
command.forge.entity.list.filter.dim
command.forge.generate
command.forge.generate.pos
command.forge.generate.pos.count
command.forge.generate.pos.count.dim
command.forge.generate.pos.count.dim.interval
command.forge.mods
command.forge.tags
command.forge.tags.registry
command.forge.tags.registry.get
command.forge.tags.registry.get.tag
command.forge.tags.registry.get.tag.page
command.forge.tags.registry.list
command.forge.tags.registry.list.page
command.forge.tags.registry.query
command.forge.tags.registry.query.element
command.forge.tags.registry.query.element.page
command.forge.tps
command.forge.tps.dim
command.forge.track
command.forge.track.entity
command.forge.track.reset
command.forge.track.reset.entity
command.forge.track.reset.te
command.forge.track.start
command.forge.track.start.entity
command.forge.track.start.entity.duration
command.forge.track.start.te
command.forge.track.start.te.duration
command.forge.track.te
command.ftbchunks
command.ftbchunks.admin
command.ftbchunks.admin.bypass_protection
command.ftbchunks.admin.claim_as
command.ftbchunks.admin.claim_as.team
command.ftbchunks.admin.claim_as.team.radius_in_blocks
command.ftbchunks.admin.claim_as.team.radius_in_blocks.anchor
command.ftbchunks.admin.claim_as.team.radius_in_blocks.anchor.dimension
command.ftbchunks.admin.extra_claim_chunks
command.ftbchunks.admin.extra_claim_chunks.player
command.ftbchunks.admin.extra_claim_chunks.player.add
command.ftbchunks.admin.extra_claim_chunks.player.add.number
command.ftbchunks.admin.extra_claim_chunks.player.get
command.ftbchunks.admin.extra_claim_chunks.player.set
command.ftbchunks.admin.extra_claim_chunks.player.set.number
command.ftbchunks.admin.extra_force_load_chunks
command.ftbchunks.admin.extra_force_load_chunks.player
command.ftbchunks.admin.extra_force_load_chunks.player.add
command.ftbchunks.admin.extra_force_load_chunks.player.add.number
command.ftbchunks.admin.extra_force_load_chunks.player.get
command.ftbchunks.admin.extra_force_load_chunks.player.set
command.ftbchunks.admin.extra_force_load_chunks.player.set.number
command.ftbchunks.admin.unclaim_as
command.ftbchunks.admin.unclaim_as.team
command.ftbchunks.admin.unclaim_as.team.radius_in_blocks
command.ftbchunks.admin.unclaim_as.team.radius_in_blocks.anchor
command.ftbchunks.admin.unclaim_as.team.radius_in_blocks.anchor.dimension
command.ftbchunks.admin.unclaim_everything
command.ftbchunks.admin.unload_everything
command.ftbchunks.admin.view_loaded_chunks
command.ftbchunks.admin.view_loaded_chunks.dimension
command.ftbchunks.admin.view_loaded_chunks.reset
command.ftbchunks.admin.view_loaded_chunks.reset.dimension
command.ftbchunks.block_color
command.ftbchunks.claim
command.ftbchunks.claim.radius_in_blocks
command.ftbchunks.info
command.ftbchunks.info.x
command.ftbchunks.info.x.z
command.ftbchunks.info.x.z.dimension
command.ftbchunks.load
command.ftbchunks.load.radius_in_blocks
command.ftbchunks.unclaim
command.ftbchunks.unclaim.radius_in_blocks
command.ftbchunks.unclaim_all
command.ftbchunks.unclaim_all.team
command.ftbchunks.unload
command.ftbchunks.unload.radius_in_blocks
command.ftbchunks.unload_all
command.ftbchunks.unload_all.team
command.ftbchunks.waypoint
command.ftbchunks.waypoint.add
command.ftbchunks.waypoint.add-dim
command.ftbchunks.waypoint.add-dim.name
command.ftbchunks.waypoint.add-dim.name.position
command.ftbchunks.waypoint.add-dim.name.position.dimension
command.ftbchunks.waypoint.add-dim.name.position.dimension.color
command.ftbchunks.waypoint.add-dim.name.position.dimension.color.gui
command.ftbchunks.waypoint.add.name
command.ftbchunks.waypoint.add.name.position
command.ftbchunks.waypoint.add.name.position.color
command.ftblibrary
command.ftblibrary.clientconfig
command.ftblibrary.day
command.ftblibrary.gamemode
command.ftblibrary.generate_loot_tables
command.ftblibrary.nbtedit
command.ftblibrary.nbtedit.block
command.ftblibrary.nbtedit.block.pos
command.ftblibrary.nbtedit.entity
command.ftblibrary.nbtedit.entity.entity
command.ftblibrary.nbtedit.item
command.ftblibrary.nbtedit.player
command.ftblibrary.nbtedit.player.player
command.ftblibrary.night
command.ftblibrary.rain
command.ftbquests
command.ftbquests.block_rewards
command.ftbquests.block_rewards.enabled
command.ftbquests.block_rewards.enabled.player
command.ftbquests.change_progress
command.ftbquests.change_progress.players
command.ftbquests.change_progress.players.complete
command.ftbquests.change_progress.players.complete.quest_object
command.ftbquests.change_progress.players.reset
command.ftbquests.change_progress.players.reset.quest_object
command.ftbquests.clear_item_display_cache
command.ftbquests.delete_empty_reward_tables
command.ftbquests.editing_mode
command.ftbquests.editing_mode.enabled
command.ftbquests.editing_mode.enabled.player
command.ftbquests.export_reward_table_to_chest
command.ftbquests.export_reward_table_to_chest.reward_table
command.ftbquests.export_reward_table_to_chest.reward_table.pos
command.ftbquests.generate_chapter_with_all_items_in_game
command.ftbquests.import_reward_table_from_chest
command.ftbquests.import_reward_table_from_chest.name
command.ftbquests.import_reward_table_from_chest.name.pos
command.ftbquests.locked
command.ftbquests.locked.enabled
command.ftbquests.locked.enabled.player
command.ftbquests.open_book
command.ftbquests.open_book.quest_object
command.ftbquests.reload
command.ftbranks
command.ftbranks.add
command.ftbranks.add.players
command.ftbranks.add.players.rank
command.ftbranks.condition
command.ftbranks.condition.rank
command.ftbranks.condition.rank.value
command.ftbranks.create
command.ftbranks.create.name
command.ftbranks.create.name.power
command.ftbranks.delete
command.ftbranks.delete.rank
command.ftbranks.list_all_ranks
command.ftbranks.list_players_with
command.ftbranks.list_players_with.rank
command.ftbranks.list_ranks_of
command.ftbranks.list_ranks_of.player
command.ftbranks.node
command.ftbranks.node.add
command.ftbranks.node.add.rank
command.ftbranks.node.add.rank.node
command.ftbranks.node.add.rank.node.value
command.ftbranks.node.list
command.ftbranks.node.list.rank
command.ftbranks.node.remove
command.ftbranks.node.remove.rank
command.ftbranks.node.remove.rank.node
command.ftbranks.refresh_readme
command.ftbranks.reload
command.ftbranks.remove
command.ftbranks.remove.players
command.ftbranks.remove.players.rank
command.ftbranks.show_rank
command.ftbranks.show_rank.rank
command.ftbteams
command.ftbteams.force-disband
command.ftbteams.force-disband.team
command.ftbteams.info
command.ftbteams.info.server_id
command.ftbteams.info.team
command.ftbteams.list
command.ftbteams.list.parties
command.ftbteams.list.players
command.ftbteams.list.server_teams
command.ftbteams.msg
command.ftbteams.msg.text
command.ftbteams.party
command.ftbteams.party.allies
command.ftbteams.party.allies.add
command.ftbteams.party.allies.add.player
command.ftbteams.party.allies.list
command.ftbteams.party.allies.remove
command.ftbteams.party.allies.remove.player
command.ftbteams.party.create
command.ftbteams.party.create.name
command.ftbteams.party.decline
command.ftbteams.party.decline.team
command.ftbteams.party.invite
command.ftbteams.party.invite.players
command.ftbteams.party.join
command.ftbteams.party.join.team
command.ftbteams.party.kick
command.ftbteams.party.kick.players
command.ftbteams.party.leave
command.ftbteams.party.settings
command.ftbteams.party.settings.key
command.ftbteams.party.settings.key.value
command.ftbteams.party.settings_for
command.ftbteams.party.settings_for.team
command.ftbteams.party.settings_for.team.key
command.ftbteams.party.settings_for.team.key.value
command.ftbteams.party.transfer_ownership
command.ftbteams.party.transfer_ownership.player_id
command.ftbteams.party.transfer_ownership_for
command.ftbteams.party.transfer_ownership_for.team
command.ftbteams.party.transfer_ownership_for.team.player_id
command.ftbteams.server
command.ftbteams.server.create
command.ftbteams.server.create.name
command.ftbteams.server.delete
command.ftbteams.server.delete.team
command.ftbteams.server.settings
command.ftbteams.server.settings.team
command.ftbteams.server.settings.team.key
command.ftbteams.server.settings.team.key.value
command.ftbultimine
command.ftbultimine.clientconfig
command.ftbultimine.serverconfig
command.function
command.function.name
command.gamemode
command.gamemode.gamemode
command.gamemode.gamemode.target
command.gamerule
command.gamerule.announceAdvancements
command.gamerule.announceAdvancements.value
command.gamerule.artifacts.anglersHat.luckOfTheSeaLevelBonus
command.gamerule.artifacts.anglersHat.luckOfTheSeaLevelBonus.value
command.gamerule.artifacts.anglersHat.lureLevelBonus
command.gamerule.artifacts.anglersHat.lureLevelBonus.value
command.gamerule.artifacts.antidoteVessel.enabled
command.gamerule.artifacts.antidoteVessel.enabled.value
command.gamerule.artifacts.antidoteVessel.maxEffectDuration
command.gamerule.artifacts.antidoteVessel.maxEffectDuration.value
command.gamerule.artifacts.aquaDashers.enabled
command.gamerule.artifacts.aquaDashers.enabled.value
command.gamerule.artifacts.bunnyHoppers.doCancelFallDamage
command.gamerule.artifacts.bunnyHoppers.doCancelFallDamage.value
command.gamerule.artifacts.bunnyHoppers.jumpBoostLevel
command.gamerule.artifacts.bunnyHoppers.jumpBoostLevel.value
command.gamerule.artifacts.charmOfSinking.enabled
command.gamerule.artifacts.charmOfSinking.enabled.value
command.gamerule.artifacts.chorusTotem.cooldown
command.gamerule.artifacts.chorusTotem.cooldown.value
command.gamerule.artifacts.chorusTotem.doConsumeOnUse
command.gamerule.artifacts.chorusTotem.doConsumeOnUse.value
command.gamerule.artifacts.chorusTotem.healthRestored
command.gamerule.artifacts.chorusTotem.healthRestored.value
command.gamerule.artifacts.chorusTotem.teleportationChance
command.gamerule.artifacts.chorusTotem.teleportationChance.value
command.gamerule.artifacts.cloudInABottle.enabled
command.gamerule.artifacts.cloudInABottle.enabled.value
command.gamerule.artifacts.cloudInABottle.sprintJumpHorizontalVelocity
command.gamerule.artifacts.cloudInABottle.sprintJumpHorizontalVelocity.value
command.gamerule.artifacts.cloudInABottle.sprintJumpVerticalVelocity
command.gamerule.artifacts.cloudInABottle.sprintJumpVerticalVelocity.value
command.gamerule.artifacts.cowboyHat.speedLevel
command.gamerule.artifacts.cowboyHat.speedLevel.value
command.gamerule.artifacts.crossNecklace.bonusInvincibilityTicks
command.gamerule.artifacts.crossNecklace.bonusInvincibilityTicks.value
command.gamerule.artifacts.crossNecklace.cooldown
command.gamerule.artifacts.crossNecklace.cooldown.value
command.gamerule.artifacts.crystalHeart.healthBonus
command.gamerule.artifacts.crystalHeart.healthBonus.value
command.gamerule.artifacts.diggingClaws.digSpeedBonus
command.gamerule.artifacts.diggingClaws.digSpeedBonus.value
command.gamerule.artifacts.diggingClaws.toolTier
command.gamerule.artifacts.diggingClaws.toolTier.value
command.gamerule.artifacts.eternalSteak.cooldown
command.gamerule.artifacts.eternalSteak.cooldown.value
command.gamerule.artifacts.eternalSteak.enabled
command.gamerule.artifacts.eternalSteak.enabled.value
command.gamerule.artifacts.everlastingBeef.cooldown
command.gamerule.artifacts.everlastingBeef.cooldown.value
command.gamerule.artifacts.everlastingBeef.enabled
command.gamerule.artifacts.everlastingBeef.enabled.value
command.gamerule.artifacts.feralClaws.attackSpeedBonus
command.gamerule.artifacts.feralClaws.attackSpeedBonus.value
command.gamerule.artifacts.fireGauntlet.fireDuration
command.gamerule.artifacts.fireGauntlet.fireDuration.value
command.gamerule.artifacts.flamePendant.cooldown
command.gamerule.artifacts.flamePendant.cooldown.value
command.gamerule.artifacts.flamePendant.doGrantFireResistance
command.gamerule.artifacts.flamePendant.doGrantFireResistance.value
command.gamerule.artifacts.flamePendant.fireDuration
command.gamerule.artifacts.flamePendant.fireDuration.value
command.gamerule.artifacts.flamePendant.strikeChance
command.gamerule.artifacts.flamePendant.strikeChance.value
command.gamerule.artifacts.flippers.swimSpeedBonus
command.gamerule.artifacts.flippers.swimSpeedBonus.value
command.gamerule.artifacts.goldenHook.experienceBonus
command.gamerule.artifacts.goldenHook.experienceBonus.value
command.gamerule.artifacts.heliumFlamingo.flightDuration
command.gamerule.artifacts.heliumFlamingo.flightDuration.value
command.gamerule.artifacts.heliumFlamingo.rechargeDuration
command.gamerule.artifacts.heliumFlamingo.rechargeDuration.value
command.gamerule.artifacts.kittySlippers.enabled
command.gamerule.artifacts.kittySlippers.enabled.value
command.gamerule.artifacts.luckyScarf.fortuneBonus
command.gamerule.artifacts.luckyScarf.fortuneBonus.value
command.gamerule.artifacts.nightVisionGoggles.enabled
command.gamerule.artifacts.nightVisionGoggles.enabled.value
command.gamerule.artifacts.nightVisionGoggles.strength
command.gamerule.artifacts.nightVisionGoggles.strength.value
command.gamerule.artifacts.noveltyDrinkingHat.drinkingDurationMultiplier
command.gamerule.artifacts.noveltyDrinkingHat.drinkingDurationMultiplier.value
command.gamerule.artifacts.noveltyDrinkingHat.eatingDurationMultiplier
command.gamerule.artifacts.noveltyDrinkingHat.eatingDurationMultiplier.value
command.gamerule.artifacts.obsidianSkull.fireResistanceCooldown
command.gamerule.artifacts.obsidianSkull.fireResistanceCooldown.value
command.gamerule.artifacts.obsidianSkull.fireResistanceDuration
command.gamerule.artifacts.obsidianSkull.fireResistanceDuration.value
command.gamerule.artifacts.onionRing.hasteDurationPerFoodPoint
command.gamerule.artifacts.onionRing.hasteDurationPerFoodPoint.value
command.gamerule.artifacts.onionRing.hasteLevel
command.gamerule.artifacts.onionRing.hasteLevel.value
command.gamerule.artifacts.panicNecklace.cooldown
command.gamerule.artifacts.panicNecklace.cooldown.value
command.gamerule.artifacts.panicNecklace.speedDuration
command.gamerule.artifacts.panicNecklace.speedDuration.value
command.gamerule.artifacts.panicNecklace.speedLevel
command.gamerule.artifacts.panicNecklace.speedLevel.value
command.gamerule.artifacts.pickaxeHeater.enabled
command.gamerule.artifacts.pickaxeHeater.enabled.value
command.gamerule.artifacts.plasticDrinkingHat.drinkingDurationMultiplier
command.gamerule.artifacts.plasticDrinkingHat.drinkingDurationMultiplier.value
command.gamerule.artifacts.plasticDrinkingHat.eatingDurationMultiplier
command.gamerule.artifacts.plasticDrinkingHat.eatingDurationMultiplier.value
command.gamerule.artifacts.pocketPiston.knockbackStrength
command.gamerule.artifacts.pocketPiston.knockbackStrength.value
command.gamerule.artifacts.powerGlove.attackDamageBonus
command.gamerule.artifacts.powerGlove.attackDamageBonus.value
command.gamerule.artifacts.rootedBoots.doGrowPlantsAfterEating
command.gamerule.artifacts.rootedBoots.doGrowPlantsAfterEating.value
command.gamerule.artifacts.rootedBoots.enabled
command.gamerule.artifacts.rootedBoots.enabled.value
command.gamerule.artifacts.rootedBoots.hungerReplenishingDuration
command.gamerule.artifacts.rootedBoots.hungerReplenishingDuration.value
command.gamerule.artifacts.runningShoes.doIncreaseStepHeight
command.gamerule.artifacts.runningShoes.doIncreaseStepHeight.value
command.gamerule.artifacts.runningShoes.speedBonus
command.gamerule.artifacts.runningShoes.speedBonus.value
command.gamerule.artifacts.scarfOfInvisibility.enabled
command.gamerule.artifacts.scarfOfInvisibility.enabled.value
command.gamerule.artifacts.shockPendant.cooldown
command.gamerule.artifacts.shockPendant.cooldown.value
command.gamerule.artifacts.shockPendant.doCancelLightningDamage
command.gamerule.artifacts.shockPendant.doCancelLightningDamage.value
command.gamerule.artifacts.shockPendant.strikeChance
command.gamerule.artifacts.shockPendant.strikeChance.value
command.gamerule.artifacts.snorkel.enabled
command.gamerule.artifacts.snorkel.enabled.value
command.gamerule.artifacts.snorkel.isInfinite
command.gamerule.artifacts.snorkel.isInfinite.value
command.gamerule.artifacts.snorkel.waterBreathingDuration
command.gamerule.artifacts.snorkel.waterBreathingDuration.value
command.gamerule.artifacts.snowshoes.allowWalkingOnPowderSnow
command.gamerule.artifacts.snowshoes.allowWalkingOnPowderSnow.value
command.gamerule.artifacts.snowshoes.slipperinessReduction
command.gamerule.artifacts.snowshoes.slipperinessReduction.value
command.gamerule.artifacts.steadfastSpikes.enabled
command.gamerule.artifacts.steadfastSpikes.enabled.value
command.gamerule.artifacts.steadfastSpikes.knockbackResistance
command.gamerule.artifacts.steadfastSpikes.knockbackResistance.value
command.gamerule.artifacts.superstitiousHat.lootingLevelBonus
command.gamerule.artifacts.superstitiousHat.lootingLevelBonus.value
command.gamerule.artifacts.thornPendant.cooldown
command.gamerule.artifacts.thornPendant.cooldown.value
command.gamerule.artifacts.thornPendant.maxDamage
command.gamerule.artifacts.thornPendant.maxDamage.value
command.gamerule.artifacts.thornPendant.minDamage
command.gamerule.artifacts.thornPendant.minDamage.value
command.gamerule.artifacts.thornPendant.strikeChance
command.gamerule.artifacts.thornPendant.strikeChance.value
command.gamerule.artifacts.umbrella.isGlider
command.gamerule.artifacts.umbrella.isGlider.value
command.gamerule.artifacts.umbrella.isShield
command.gamerule.artifacts.umbrella.isShield.value
command.gamerule.artifacts.universalAttractor.enabled
command.gamerule.artifacts.universalAttractor.enabled.value
command.gamerule.artifacts.vampiricGlove.absorptionChance
command.gamerule.artifacts.vampiricGlove.absorptionChance.value
command.gamerule.artifacts.vampiricGlove.absorptionRatio
command.gamerule.artifacts.vampiricGlove.absorptionRatio.value
command.gamerule.artifacts.vampiricGlove.maxHealingPerHit
command.gamerule.artifacts.vampiricGlove.maxHealingPerHit.value
command.gamerule.artifacts.villagerHat.reputationBonus
command.gamerule.artifacts.villagerHat.reputationBonus.value
command.gamerule.artifacts.whoopeeCushion.fartChance
command.gamerule.artifacts.whoopeeCushion.fartChance.value
command.gamerule.blockExplosionDropDecay
command.gamerule.blockExplosionDropDecay.value
command.gamerule.brandonscore:allowSignEditing
command.gamerule.brandonscore:allowSignEditing.value
command.gamerule.commandBlockOutput
command.gamerule.commandBlockOutput.value
command.gamerule.commandModificationBlockLimit
command.gamerule.commandModificationBlockLimit.value
command.gamerule.disableElytraMovementCheck
command.gamerule.disableElytraMovementCheck.value
command.gamerule.disableRaids
command.gamerule.disableRaids.value
command.gamerule.doDaylightCycle
command.gamerule.doDaylightCycle.value
command.gamerule.doEntityDrops
command.gamerule.doEntityDrops.value
command.gamerule.doFireTick
command.gamerule.doFireTick.value
command.gamerule.doImmediateRespawn
command.gamerule.doImmediateRespawn.value
command.gamerule.doInsomnia
command.gamerule.doInsomnia.value
command.gamerule.doLimitedCrafting
command.gamerule.doLimitedCrafting.value
command.gamerule.doMobLoot
command.gamerule.doMobLoot.value
command.gamerule.doMobSpawning
command.gamerule.doMobSpawning.value
command.gamerule.doPatrolSpawning
command.gamerule.doPatrolSpawning.value
command.gamerule.doTileDrops
command.gamerule.doTileDrops.value
command.gamerule.doTraderSpawning
command.gamerule.doTraderSpawning.value
command.gamerule.doVinesSpread
command.gamerule.doVinesSpread.value
command.gamerule.doWardenSpawning
command.gamerule.doWardenSpawning.value
command.gamerule.doWeatherCycle
command.gamerule.doWeatherCycle.value
command.gamerule.drowningDamage
command.gamerule.drowningDamage.value
command.gamerule.fakeLavaSourceConversion
command.gamerule.fakeLavaSourceConversion.value
command.gamerule.fakeWaterSourceConversion
command.gamerule.fakeWaterSourceConversion.value
command.gamerule.fallDamage
command.gamerule.fallDamage.value
command.gamerule.fireDamage
command.gamerule.fireDamage.value
command.gamerule.forgiveDeadPlayers
command.gamerule.forgiveDeadPlayers.value
command.gamerule.freezeDamage
command.gamerule.freezeDamage.value
command.gamerule.globalSoundEvents
command.gamerule.globalSoundEvents.value
command.gamerule.keepInventory
command.gamerule.keepInventory.value
command.gamerule.lavaSourceConversion
command.gamerule.lavaSourceConversion.value
command.gamerule.logAdminCommands
command.gamerule.logAdminCommands.value
command.gamerule.maxCommandChainLength
command.gamerule.maxCommandChainLength.value
command.gamerule.maxEntityCramming
command.gamerule.maxEntityCramming.value
command.gamerule.mobExplosionDropDecay
command.gamerule.mobExplosionDropDecay.value
command.gamerule.mobGriefing
command.gamerule.mobGriefing.value
command.gamerule.naturalRegeneration
command.gamerule.naturalRegeneration.value
command.gamerule.playersSleepingPercentage
command.gamerule.playersSleepingPercentage.value
command.gamerule.randomTickSpeed
command.gamerule.randomTickSpeed.value
command.gamerule.reducedDebugInfo
command.gamerule.reducedDebugInfo.value
command.gamerule.sendCommandFeedback
command.gamerule.sendCommandFeedback.value
command.gamerule.showDeathMessages
command.gamerule.showDeathMessages.value
command.gamerule.snowAccumulationHeight
command.gamerule.snowAccumulationHeight.value
command.gamerule.spawnRadius
command.gamerule.spawnRadius.value
command.gamerule.spectatorsGenerateChunks
command.gamerule.spectatorsGenerateChunks.value
command.gamerule.tfEnforcedProgression
command.gamerule.tfEnforcedProgression.value
command.gamerule.tntExplosionDropDecay
command.gamerule.tntExplosionDropDecay.value
command.gamerule.universalAnger
command.gamerule.universalAnger.value
command.gamerule.waterSourceConversion
command.gamerule.waterSourceConversion.value
command.giselle_addon
command.giselle_addon.equip
command.giselle_addon.equip.allthemodium
command.giselle_addon.equip.diamond
command.giselle_addon.equip.diving_helmet
command.giselle_addon.equip.flux_armor
command.giselle_addon.equip.jet_suit
command.giselle_addon.equip.mekasuit
command.giselle_addon.equip.netherite_diving_helmet
command.giselle_addon.equip.netherite_space_suit
command.giselle_addon.equip.pneumatic_armor
command.giselle_addon.equip.space_suit
command.give
command.give.targets
command.give.targets.item
command.give.targets.item.count
command.god
command.god.player
command.gtceu
command.gtceu.check_recipes_valid
command.gtceu.dump_data
command.gtceu.dump_data.bedrock_fluid_veins
command.gtceu.dump_data.bedrock_ore_veins
command.gtceu.dump_data.ore_veins
command.gtceu.place_vein
command.gtceu.place_vein.vein
command.gtceu.place_vein.vein.position
command.gtceu.ui_editor
command.hat
command.heal
command.heal.player
command.help
command.help.command
command.home
command.home.name
command.hostilenetworks
command.hostilenetworks.datafix_all
command.hostilenetworks.generate_all
command.hostilenetworks.generate_all.max_stack_size
command.hostilenetworks.generate_model_json
command.hostilenetworks.generate_model_json.entity_type
command.hostilenetworks.generate_model_json.entity_type.max_stack_size
command.hostilenetworks.give_model
command.hostilenetworks.give_model.targets
command.hostilenetworks.give_model.targets.model
command.hostilenetworks.give_model.targets.model.tier
command.hostilenetworks.give_model.targets.model.tier.data
command.hostilenetworks.update_model_json
command.hostilenetworks.update_model_json.data_model
command.hostilenetworks.update_model_json.data_model.max_stack_size
command.ie
command.ie.clearshaders
command.ie.clearshaders.player
command.ie.mineral
command.ie.mineral.get
command.ie.mineral.get.location
command.ie.mineral.list
command.ie.mineral.put
command.ie.mineral.put.mineral
command.ie.mineral.put.mineral.radius
command.ie.mineral.put.mineral.radius.pos
command.ie.mineral.setDepletion
command.ie.mineral.setDepletion.depletion
command.ie.mineral.setDepletion.depletion.pos
command.invsee
command.invsee.player
command.item
command.item.modify
command.item.modify.block
command.item.modify.block.pos
command.item.modify.block.pos.slot
command.item.modify.block.pos.slot.modifier
command.item.modify.entity
command.item.modify.entity.targets
command.item.modify.entity.targets.slot
command.item.modify.entity.targets.slot.modifier
command.item.replace
command.item.replace.block
command.item.replace.block.pos
command.item.replace.block.pos.slot
command.item.replace.block.pos.slot.from
command.item.replace.block.pos.slot.from.block
command.item.replace.block.pos.slot.from.block.source
command.item.replace.block.pos.slot.from.block.source.sourceSlot
command.item.replace.block.pos.slot.from.block.source.sourceSlot.modifier
command.item.replace.block.pos.slot.from.entity
command.item.replace.block.pos.slot.from.entity.source
command.item.replace.block.pos.slot.from.entity.source.sourceSlot
command.item.replace.block.pos.slot.from.entity.source.sourceSlot.modifier
command.item.replace.block.pos.slot.with
command.item.replace.block.pos.slot.with.item
command.item.replace.block.pos.slot.with.item.count
command.item.replace.entity
command.item.replace.entity.targets
command.item.replace.entity.targets.slot
command.item.replace.entity.targets.slot.from
command.item.replace.entity.targets.slot.from.block
command.item.replace.entity.targets.slot.from.block.source
command.item.replace.entity.targets.slot.from.block.source.sourceSlot
command.item.replace.entity.targets.slot.from.block.source.sourceSlot.modifier
command.item.replace.entity.targets.slot.from.entity
command.item.replace.entity.targets.slot.from.entity.source
command.item.replace.entity.targets.slot.from.entity.source.sourceSlot
command.item.replace.entity.targets.slot.from.entity.source.sourceSlot.modifier
command.item.replace.entity.targets.slot.with
command.item.replace.entity.targets.slot.with.item
command.item.replace.entity.targets.slot.with.item.count
command.jade
command.jade.hide
command.jade.hide.targets
command.jade.show
command.jade.show.targets
command.jfr
command.jfr.start
command.jfr.stop
command.jump
command.kick
command.kick.targets
command.kick.targets.reason
command.kickme
command.kill
command.kill.targets
command.kit
command.kit.cooldown
command.kit.cooldown.name
command.kit.cooldown.name.cooldown
command.kit.create_from_block_inv
command.kit.create_from_block_inv.name
command.kit.create_from_block_inv.name.cooldown
command.kit.create_from_player_hotbar
command.kit.create_from_player_hotbar.name
command.kit.create_from_player_hotbar.name.cooldown
command.kit.create_from_player_inv
command.kit.create_from_player_inv.name
command.kit.create_from_player_inv.name.cooldown
command.kit.delete
command.kit.delete.name
command.kit.give
command.kit.give.players
command.kit.give.players.name
command.kit.list
command.kit.put_in_block_inv
command.kit.put_in_block_inv.name
command.kit.reset_cooldown
command.kit.reset_cooldown.name
command.kit.reset_cooldown.name.id
command.kit.reset_cooldown.name.player
command.kit.set_autogrant
command.kit.set_autogrant.name
command.kit.set_autogrant.name.grant
command.kit.show
command.kit.show.name
command.knowledge
command.knowledge.targets
command.knowledge.targets.grant
command.knowledge.targets.grant.fact
command.knowledge.targets.grant.fact.fact
command.knowledge.targets.grant.research
command.knowledge.targets.grant.research.research
command.knowledge.targets.grant.rune
command.knowledge.targets.grant.rune.rune
command.knowledge.targets.grant.sign
command.knowledge.targets.grant.sign.sign
command.knowledge.targets.remove
command.knowledge.targets.remove.fact
command.knowledge.targets.remove.fact.fact
command.knowledge.targets.remove.research
command.knowledge.targets.remove.research.research
command.knowledge.targets.remove.rune
command.knowledge.targets.remove.rune.rune
command.knowledge.targets.remove.sign
command.knowledge.targets.remove.sign.sign
command.knowledge.targets.reset
command.knowledge.targets.reset.facts
command.knowledge.targets.reset.research
command.knowledge.targets.reset.runes
command.knowledge.targets.reset.signs
command.kubejs
command.kubejs.custom_command
command.kubejs.custom_command.id
command.kubejs.dump_internals
command.kubejs.dump_internals.events
command.kubejs.dump_registry
command.kubejs.dump_registry.registry
command.kubejs.errors
command.kubejs.errors.client
command.kubejs.errors.server
command.kubejs.errors.startup
command.kubejs.export
command.kubejs.export.pack_folders
command.kubejs.export.pack_zips
command.kubejs.generate_typings
command.kubejs.hand
command.kubejs.help
command.kubejs.hotbar
command.kubejs.inventory
command.kubejs.list_tag
command.kubejs.list_tag.registry
command.kubejs.list_tag.registry.tag
command.kubejs.offhand
command.kubejs.packmode
command.kubejs.packmode.name
command.kubejs.painter
command.kubejs.painter.player
command.kubejs.painter.player.object
command.kubejs.persistent_data
command.kubejs.persistent_data.dimension
command.kubejs.persistent_data.dimension.all
command.kubejs.persistent_data.dimension.all.get
command.kubejs.persistent_data.dimension.all.get.all
command.kubejs.persistent_data.dimension.all.get.key
command.kubejs.persistent_data.dimension.all.merge
command.kubejs.persistent_data.dimension.all.merge.nbt
command.kubejs.persistent_data.dimension.all.remove
command.kubejs.persistent_data.dimension.all.remove.all
command.kubejs.persistent_data.dimension.all.remove.key
command.kubejs.persistent_data.dimension.all.scoreboard
command.kubejs.persistent_data.dimension.all.scoreboard.export
command.kubejs.persistent_data.dimension.all.scoreboard.export.key
command.kubejs.persistent_data.dimension.all.scoreboard.export.key.targets
command.kubejs.persistent_data.dimension.all.scoreboard.export.key.targets.objective
command.kubejs.persistent_data.dimension.all.scoreboard.import
command.kubejs.persistent_data.dimension.all.scoreboard.import.key
command.kubejs.persistent_data.dimension.all.scoreboard.import.key.target
command.kubejs.persistent_data.dimension.all.scoreboard.import.key.target.objective
command.kubejs.persistent_data.dimension.dimension
command.kubejs.persistent_data.dimension.dimension.get
command.kubejs.persistent_data.dimension.dimension.get.all
command.kubejs.persistent_data.dimension.dimension.get.key
command.kubejs.persistent_data.dimension.dimension.merge
command.kubejs.persistent_data.dimension.dimension.merge.nbt
command.kubejs.persistent_data.dimension.dimension.remove
command.kubejs.persistent_data.dimension.dimension.remove.all
command.kubejs.persistent_data.dimension.dimension.remove.key
command.kubejs.persistent_data.dimension.dimension.scoreboard
command.kubejs.persistent_data.dimension.dimension.scoreboard.export
command.kubejs.persistent_data.dimension.dimension.scoreboard.export.key
command.kubejs.persistent_data.dimension.dimension.scoreboard.export.key.targets
command.kubejs.persistent_data.dimension.dimension.scoreboard.export.key.targets.objective
command.kubejs.persistent_data.dimension.dimension.scoreboard.import
command.kubejs.persistent_data.dimension.dimension.scoreboard.import.key
command.kubejs.persistent_data.dimension.dimension.scoreboard.import.key.target
command.kubejs.persistent_data.dimension.dimension.scoreboard.import.key.target.objective
command.kubejs.persistent_data.entity
command.kubejs.persistent_data.entity.entity
command.kubejs.persistent_data.entity.entity.get
command.kubejs.persistent_data.entity.entity.get.all
command.kubejs.persistent_data.entity.entity.get.key
command.kubejs.persistent_data.entity.entity.merge
command.kubejs.persistent_data.entity.entity.merge.nbt
command.kubejs.persistent_data.entity.entity.remove
command.kubejs.persistent_data.entity.entity.remove.all
command.kubejs.persistent_data.entity.entity.remove.key
command.kubejs.persistent_data.entity.entity.scoreboard
command.kubejs.persistent_data.entity.entity.scoreboard.export
command.kubejs.persistent_data.entity.entity.scoreboard.export.key
command.kubejs.persistent_data.entity.entity.scoreboard.export.key.targets
command.kubejs.persistent_data.entity.entity.scoreboard.export.key.targets.objective
command.kubejs.persistent_data.entity.entity.scoreboard.import
command.kubejs.persistent_data.entity.entity.scoreboard.import.key
command.kubejs.persistent_data.entity.entity.scoreboard.import.key.target
command.kubejs.persistent_data.entity.entity.scoreboard.import.key.target.objective
command.kubejs.persistent_data.server
command.kubejs.persistent_data.server.get
command.kubejs.persistent_data.server.get.all
command.kubejs.persistent_data.server.get.key
command.kubejs.persistent_data.server.merge
command.kubejs.persistent_data.server.merge.nbt
command.kubejs.persistent_data.server.remove
command.kubejs.persistent_data.server.remove.all
command.kubejs.persistent_data.server.remove.key
command.kubejs.persistent_data.server.scoreboard
command.kubejs.persistent_data.server.scoreboard.export
command.kubejs.persistent_data.server.scoreboard.export.key
command.kubejs.persistent_data.server.scoreboard.export.key.targets
command.kubejs.persistent_data.server.scoreboard.export.key.targets.objective
command.kubejs.persistent_data.server.scoreboard.import
command.kubejs.persistent_data.server.scoreboard.import.key
command.kubejs.persistent_data.server.scoreboard.import.key.target
command.kubejs.persistent_data.server.scoreboard.import.key.target.objective
command.kubejs.reload
command.kubejs.reload.client_scripts
command.kubejs.reload.config
command.kubejs.reload.lang
command.kubejs.reload.server_scripts
command.kubejs.reload.startup_scripts
command.kubejs.reload.textures
command.kubejs.stages
command.kubejs.stages.add
command.kubejs.stages.add.player
command.kubejs.stages.add.player.stage
command.kubejs.stages.clear
command.kubejs.stages.clear.player
command.kubejs.stages.list
command.kubejs.stages.list.player
command.kubejs.stages.remove
command.kubejs.stages.remove.player
command.kubejs.stages.remove.player.stage
command.ldlib
command.ldlib.copy_block_tag
command.ldlib.copy_block_tag.pos
command.ldlib.copy_entity_tag
command.ldlib.copy_entity_tag.entity
command.ldlib.ui_editor
command.leaderboard
command.leaderboard.damage_dealt
command.leaderboard.deaths
command.leaderboard.deaths_per_hour
command.leaderboard.distance_walked
command.leaderboard.jumps
command.leaderboard.mob_kills
command.leaderboard.player_kills
command.leaderboard.time_played
command.leaderboard.time_since_death
command.learnSpell
command.learnSpell.forget
command.learnSpell.learn
command.learnSpell.learn.spell
command.libx
command.libx.entitydata
command.libx.entitydata.entities
command.libx.entitydata.entities.nbt
command.libx.hand
command.libx.hand.nbt_path
command.libx.reload
command.libx.reload.common
command.list
command.list.uuids
command.listhomes
command.listhomes.player
command.listwarps
command.locate
command.locate.biome
command.locate.biome.biome
command.locate.poi
command.locate.poi.poi
command.locate.structure
command.locate.structure.structure
command.loot
command.loot.give
command.loot.give.players
command.loot.give.players.fish
command.loot.give.players.fish.loot_table
command.loot.give.players.fish.loot_table.pos
command.loot.give.players.fish.loot_table.pos.mainhand
command.loot.give.players.fish.loot_table.pos.offhand
command.loot.give.players.fish.loot_table.pos.tool
command.loot.give.players.kill
command.loot.give.players.kill.target
command.loot.give.players.loot
command.loot.give.players.loot.loot_table
command.loot.give.players.mine
command.loot.give.players.mine.pos
command.loot.give.players.mine.pos.mainhand
command.loot.give.players.mine.pos.offhand
command.loot.give.players.mine.pos.tool
command.loot.insert
command.loot.insert.targetPos
command.loot.insert.targetPos.fish
command.loot.insert.targetPos.fish.loot_table
command.loot.insert.targetPos.fish.loot_table.pos
command.loot.insert.targetPos.fish.loot_table.pos.mainhand
command.loot.insert.targetPos.fish.loot_table.pos.offhand
command.loot.insert.targetPos.fish.loot_table.pos.tool
command.loot.insert.targetPos.kill
command.loot.insert.targetPos.kill.target
command.loot.insert.targetPos.loot
command.loot.insert.targetPos.loot.loot_table
command.loot.insert.targetPos.mine
command.loot.insert.targetPos.mine.pos
command.loot.insert.targetPos.mine.pos.mainhand
command.loot.insert.targetPos.mine.pos.offhand
command.loot.insert.targetPos.mine.pos.tool
command.loot.replace
command.loot.replace.block
command.loot.replace.block.targetPos
command.loot.replace.block.targetPos.slot
command.loot.replace.block.targetPos.slot.count
command.loot.replace.block.targetPos.slot.count.fish
command.loot.replace.block.targetPos.slot.count.fish.loot_table
command.loot.replace.block.targetPos.slot.count.fish.loot_table.pos
command.loot.replace.block.targetPos.slot.count.fish.loot_table.pos.mainhand
command.loot.replace.block.targetPos.slot.count.fish.loot_table.pos.offhand
command.loot.replace.block.targetPos.slot.count.fish.loot_table.pos.tool
command.loot.replace.block.targetPos.slot.count.kill
command.loot.replace.block.targetPos.slot.count.kill.target
command.loot.replace.block.targetPos.slot.count.loot
command.loot.replace.block.targetPos.slot.count.loot.loot_table
command.loot.replace.block.targetPos.slot.count.mine
command.loot.replace.block.targetPos.slot.count.mine.pos
command.loot.replace.block.targetPos.slot.count.mine.pos.mainhand
command.loot.replace.block.targetPos.slot.count.mine.pos.offhand
command.loot.replace.block.targetPos.slot.count.mine.pos.tool
command.loot.replace.block.targetPos.slot.fish
command.loot.replace.block.targetPos.slot.fish.loot_table
command.loot.replace.block.targetPos.slot.fish.loot_table.pos
command.loot.replace.block.targetPos.slot.fish.loot_table.pos.mainhand
command.loot.replace.block.targetPos.slot.fish.loot_table.pos.offhand
command.loot.replace.block.targetPos.slot.fish.loot_table.pos.tool
command.loot.replace.block.targetPos.slot.kill
command.loot.replace.block.targetPos.slot.kill.target
command.loot.replace.block.targetPos.slot.loot
command.loot.replace.block.targetPos.slot.loot.loot_table
command.loot.replace.block.targetPos.slot.mine
command.loot.replace.block.targetPos.slot.mine.pos
command.loot.replace.block.targetPos.slot.mine.pos.mainhand
command.loot.replace.block.targetPos.slot.mine.pos.offhand
command.loot.replace.block.targetPos.slot.mine.pos.tool
command.loot.replace.entity
command.loot.replace.entity.entities
command.loot.replace.entity.entities.slot
command.loot.replace.entity.entities.slot.count
command.loot.replace.entity.entities.slot.count.fish
command.loot.replace.entity.entities.slot.count.fish.loot_table
command.loot.replace.entity.entities.slot.count.fish.loot_table.pos
command.loot.replace.entity.entities.slot.count.fish.loot_table.pos.mainhand
command.loot.replace.entity.entities.slot.count.fish.loot_table.pos.offhand
command.loot.replace.entity.entities.slot.count.fish.loot_table.pos.tool
command.loot.replace.entity.entities.slot.count.kill
command.loot.replace.entity.entities.slot.count.kill.target
command.loot.replace.entity.entities.slot.count.loot
command.loot.replace.entity.entities.slot.count.loot.loot_table
command.loot.replace.entity.entities.slot.count.mine
command.loot.replace.entity.entities.slot.count.mine.pos
command.loot.replace.entity.entities.slot.count.mine.pos.mainhand
command.loot.replace.entity.entities.slot.count.mine.pos.offhand
command.loot.replace.entity.entities.slot.count.mine.pos.tool
command.loot.replace.entity.entities.slot.fish
command.loot.replace.entity.entities.slot.fish.loot_table
command.loot.replace.entity.entities.slot.fish.loot_table.pos
command.loot.replace.entity.entities.slot.fish.loot_table.pos.mainhand
command.loot.replace.entity.entities.slot.fish.loot_table.pos.offhand
command.loot.replace.entity.entities.slot.fish.loot_table.pos.tool
command.loot.replace.entity.entities.slot.kill
command.loot.replace.entity.entities.slot.kill.target
command.loot.replace.entity.entities.slot.loot
command.loot.replace.entity.entities.slot.loot.loot_table
command.loot.replace.entity.entities.slot.mine
command.loot.replace.entity.entities.slot.mine.pos
command.loot.replace.entity.entities.slot.mine.pos.mainhand
command.loot.replace.entity.entities.slot.mine.pos.offhand
command.loot.replace.entity.entities.slot.mine.pos.tool
command.loot.spawn
command.loot.spawn.targetPos
command.loot.spawn.targetPos.fish
command.loot.spawn.targetPos.fish.loot_table
command.loot.spawn.targetPos.fish.loot_table.pos
command.loot.spawn.targetPos.fish.loot_table.pos.mainhand
command.loot.spawn.targetPos.fish.loot_table.pos.offhand
command.loot.spawn.targetPos.fish.loot_table.pos.tool
command.loot.spawn.targetPos.kill
command.loot.spawn.targetPos.kill.target
command.loot.spawn.targetPos.loot
command.loot.spawn.targetPos.loot.loot_table
command.loot.spawn.targetPos.mine
command.loot.spawn.targetPos.mine.pos
command.loot.spawn.targetPos.mine.pos.mainhand
command.loot.spawn.targetPos.mine.pos.offhand
command.loot.spawn.targetPos.mine.pos.tool
command.lootr
command.lootr.barrel
command.lootr.barrel.table
command.lootr.cart
command.lootr.cart.table
command.lootr.cclear
command.lootr.cclear.entities
command.lootr.chest
command.lootr.chest.table
command.lootr.clear
command.lootr.clear.profile
command.lootr.convert
command.lootr.convert.from
command.lootr.convert.from.to
command.lootr.custom
command.lootr.decay
command.lootr.id
command.lootr.open_as
command.lootr.open_as.profile
command.lootr.open_as_uuid
command.lootr.open_as_uuid.uuid
command.lootr.openers
command.lootr.openers.location
command.lootr.refresh
command.lootr.shulker
command.lootr.shulker.table
command.lootr.trapped_chest
command.lootr.trapped_chest.table
command.lostcities
command.lostcities.createbuilding
command.lostcities.createbuilding.name
command.lostcities.createbuilding.name.floors
command.lostcities.createbuilding.name.floors.cellars
command.lostcities.createbuilding.name.floors.cellars.pos
command.lostcities.createpart
command.lostcities.createpart.name
command.lostcities.createpart.name.pos
command.lostcities.debug
command.lostcities.editpart
command.lostcities.exportpart
command.lostcities.exportpart.name
command.lostcities.listparts
command.lostcities.locate
command.lostcities.locate.name
command.lostcities.locatepart
command.lostcities.locatepart.name
command.lostcities.map
command.lostcities.resumeedit
command.lostcities.saveprofile
command.lostcities.saveprofile.profile
command.lostcities.stats
command.lostcities.testfill
command.mahoucancel
command.mahoukodoku
command.mahoukodoku.player
command.mahoukodoku.player.kodoku
command.mahouset
command.mahouset.caliburn
command.mahouset.caliburn.player
command.mahouset.caliburn.player.damage
command.mahouset.caliburncap
command.mahouset.caliburncap.player
command.mahouset.caliburncap.player.damage_limit
command.mahouset.clarent
command.mahouset.clarent.player
command.mahouset.clarent.player.damage
command.mahouset.morgan
command.mahouset.morgan.player
command.mahouset.morgan.player.damage
command.mahouset.morgancap
command.mahouset.morgancap.player
command.mahouset.morgancap.player.damage_limit
command.mahouset.rhongomyniad
command.mahouset.rhongomyniad.player
command.mahouset.rhongomyniad.player.damage
command.mahouset.rhongomyniadweight
command.mahouset.rhongomyniadweight.player
command.mahouset.rhongomyniadweight.player.weight
command.mahouset.rulebreakerd
command.mahouset.rulebreakerd.player
command.mahouset.rulebreakerd.player.d
command.mahouset.rulebreakerm
command.mahouset.rulebreakerm.player
command.mahouset.rulebreakerm.player.m
command.mahouset.souls
command.mahouset.souls.player
command.mahouset.souls.player.count
command.mahousummon
command.mahousummon.morganball
command.mahousummon.morganball.pos
command.mahousummon.morganball.pos.aim
command.mahousummon.morganball.pos.aim.speed
command.mahousummon.morganball.pos.aim.speed.damage
command.mahousummon.morganball.pos.aim.speed.damage.r
command.mahousummon.morganball.pos.aim.speed.damage.r.g
command.mahousummon.morganball.pos.aim.speed.damage.r.g.b
command.mahousummon.morganball.pos.aim.speed.damage.r.g.b.r2
command.mahousummon.morganball.pos.aim.speed.damage.r.g.b.r2.g2
command.mahousummon.morganball.pos.aim.speed.damage.r.g.b.r2.g2.b2
command.mahousummon.rhongomyniad
command.mahousummon.rhongomyniad.pos
command.mahousummon.rhongomyniad.pos.damage
command.mahousummon.smite
command.mahousummon.smite.target
command.mahousummon.smite.target.r
command.mahousummon.smite.target.r.g
command.mahousummon.smite.target.r.g.b
command.mahousummon.smite.target.r.g.b.size
command.mahousummon.smite.target.r.g.b.size.damage
command.mahousummon.treasury
command.mahousummon.treasury.stack
command.mahousummon.treasury.stack.pos
command.mahousummon.treasury.stack.pos.anim_speed
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed.acc
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed.acc.r
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed.acc.r.g
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed.acc.r.g.b
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed.acc.r.g.b.aim
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed.acc.r.g.b.aim.entity
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed.acc.r.g.b.target
command.mahousummon.treasury.stack.pos.anim_speed.proj_speed.acc.r.g.b.target.entity
command.mahousummon.wepproj
command.mahousummon.wepproj.stack
command.mahousummon.wepproj.stack.pos
command.mahousummon.wepproj.stack.pos.proj_speed
command.mahousummon.wepproj.stack.pos.proj_speed.acc
command.mahousummon.wepproj.stack.pos.proj_speed.acc.aim
command.mahousummon.wepproj.stack.pos.proj_speed.acc.aim.entity
command.mahousummon.wepproj.stack.pos.proj_speed.acc.target
command.mahousummon.wepproj.stack.pos.proj_speed.acc.target.entity
command.mana
command.mana.add
command.mana.add.targets
command.mana.add.targets.amount
command.mana.get
command.mana.get.targets
command.mana.set
command.mana.set.targets
command.mana.set.targets.amount
command.markethelper
command.maxmahou
command.maxmahou.player
command.maxmahou.player.mana
command.mc
command.mc.backup
command.mc.citizens
command.mc.citizens.info
command.mc.citizens.info.colonyID
command.mc.citizens.info.colonyID.citizenID
command.mc.citizens.kill
command.mc.citizens.kill.colonyID
command.mc.citizens.kill.colonyID.citizenID
command.mc.citizens.list
command.mc.citizens.list.colonyID
command.mc.citizens.list.colonyID.startpage
command.mc.citizens.reload
command.mc.citizens.reload.colonyID
command.mc.citizens.reload.colonyID.citizenID
command.mc.citizens.spawnNew
command.mc.citizens.spawnNew.colonyID
command.mc.citizens.teleport
command.mc.citizens.teleport.colonyID
command.mc.citizens.teleport.colonyID.citizenID
command.mc.citizens.teleport.colonyID.citizenID.location
command.mc.citizens.trackPath
command.mc.citizens.trackPath.colonyID
command.mc.citizens.trackPath.colonyID.citizenID
command.mc.citizens.trackPathType
command.mc.citizens.trackPathType.pathjobname
command.mc.citizens.walk
command.mc.citizens.walk.colonyID
command.mc.citizens.walk.colonyID.citizenID
command.mc.citizens.walk.colonyID.citizenID.location
command.mc.colony
command.mc.colony.addOfficer
command.mc.colony.addOfficer.colonyID
command.mc.colony.addOfficer.colonyID.playername
command.mc.colony.canSpawnRaiders
command.mc.colony.canSpawnRaiders.colonyID
command.mc.colony.canSpawnRaiders.colonyID.canSpawn
command.mc.colony.chunkstatus
command.mc.colony.chunkstatus.colonyID
command.mc.colony.claim
command.mc.colony.claim.colonyID
command.mc.colony.claim.colonyID.range
command.mc.colony.claim.colonyID.range.add
command.mc.colony.claiminfo
command.mc.colony.claiminfo.location
command.mc.colony.delete
command.mc.colony.delete.colonyID
command.mc.colony.delete.colonyID.keep / delete buildings
command.mc.colony.delete.colonyID.keep / delete buildings.confirm
command.mc.colony.export
command.mc.colony.export.colonyID
command.mc.colony.home
command.mc.colony.info
command.mc.colony.info.colonyID
command.mc.colony.list
command.mc.colony.list.startpage
command.mc.colony.loadAllColoniesFromBackup
command.mc.colony.loadBackup
command.mc.colony.loadBackup.colonyID
command.mc.colony.printStats
command.mc.colony.printStats.colonyID
command.mc.colony.raid
command.mc.colony.raid.raidtime
command.mc.colony.raid.raidtime.colonyID
command.mc.colony.raid.raidtime.colonyID.raidtype
command.mc.colony.raid.raidtime.colonyID.raidtype.allowships
command.mc.colony.raidhistory
command.mc.colony.raidhistory.colonyID
command.mc.colony.reclaimchunks
command.mc.colony.reclaimchunks.colonyID
command.mc.colony.requestsystem-reset
command.mc.colony.requestsystem-reset-all
command.mc.colony.requestsystem-reset.colonyID
command.mc.colony.setAbandoned
command.mc.colony.setAbandoned.colonyID
command.mc.colony.setDeleteable
command.mc.colony.setDeleteable.colonyID
command.mc.colony.setDeleteable.colonyID.deletable
command.mc.colony.setRank
command.mc.colony.setRank.colonyID
command.mc.colony.setRank.colonyID.playername
command.mc.colony.setRank.colonyID.playername.rank
command.mc.colony.setowner
command.mc.colony.setowner.colonyID
command.mc.colony.setowner.colonyID.playername
command.mc.colony.teleport
command.mc.colony.teleport.colonyID
command.mc.forceunloadchunks
command.mc.help
command.mc.home
command.mc.kill
command.mc.kill.animals
command.mc.kill.chicken
command.mc.kill.cow
command.mc.kill.monster
command.mc.kill.pig
command.mc.kill.raider
command.mc.kill.sheep
command.mc.prune-world-now
command.mc.prune-world-now.stage
command.mc.prune-world-now.stage.additional block protection radius
command.mc.raid-All
command.mc.raid-All.raidtime
command.mc.raid-All.raidtime.raidtype
command.mc.ranks
command.mc.ranks.playername
command.mc.ranks.playername.startpage
command.mc.resetsupplies
command.mc.resetsupplies.playername
command.mc.trackPath
command.mc.trackPath.entity
command.mc.whereami
command.mc.whoami
command.me
command.me.action
command.medical_condition
command.medical_condition.apply
command.medical_condition.apply.targets
command.medical_condition.apply.targets.condition
command.medical_condition.apply.targets.condition.progression_multiplier
command.medical_condition.clear
command.medical_condition.clear.targets
command.medical_condition.clear.targets.condition
command.medical_condition.query
command.medical_condition.query.target
command.mek
command.mek.build
command.mek.build.boiler
command.mek.build.boiler.empty
command.mek.build.boiler.empty.start
command.mek.build.boiler.start
command.mek.build.evaporation
command.mek.build.evaporation.empty
command.mek.build.evaporation.empty.start
command.mek.build.evaporation.start
command.mek.build.fission
command.mek.build.fission.empty
command.mek.build.fission.empty.start
command.mek.build.fission.start
command.mek.build.fusion
command.mek.build.fusion.empty
command.mek.build.fusion.empty.start
command.mek.build.fusion.start
command.mek.build.matrix
command.mek.build.matrix.empty
command.mek.build.matrix.empty.start
command.mek.build.matrix.start
command.mek.build.remove
command.mek.build.sps
command.mek.build.sps.empty
command.mek.build.sps.empty.start
command.mek.build.sps.start
command.mek.build.tank
command.mek.build.tank.empty
command.mek.build.tank.empty.start
command.mek.build.tank.start
command.mek.build.turbine
command.mek.build.turbine.empty
command.mek.build.turbine.empty.start
command.mek.build.turbine.start
command.mek.chunk
command.mek.chunk.clear
command.mek.chunk.flush
command.mek.chunk.unwatch
command.mek.chunk.unwatch.pos
command.mek.chunk.watch
command.mek.chunk.watch.pos
command.mek.debug
command.mek.radiation
command.mek.radiation.add
command.mek.radiation.add.magnitude
command.mek.radiation.add.magnitude.location
command.mek.radiation.add.magnitude.location.dimension
command.mek.radiation.addEntity
command.mek.radiation.addEntity.magnitude
command.mek.radiation.addEntity.targets
command.mek.radiation.addEntity.targets.magnitude
command.mek.radiation.get
command.mek.radiation.get.location
command.mek.radiation.get.location.dimension
command.mek.radiation.heal
command.mek.radiation.heal.targets
command.mek.radiation.reduce
command.mek.radiation.reduce.magnitude
command.mek.radiation.reduce.targets
command.mek.radiation.reduce.targets.magnitude
command.mek.radiation.removeAll
command.mek.retrogen
command.mek.retrogen.from
command.mek.retrogen.from.to
command.mek.testrules
command.mek.tp
command.mek.tp.location
command.mek.tpop
command.mfsrc
command.minecolonies
command.minecolonies.backup
command.minecolonies.citizens
command.minecolonies.citizens.info
command.minecolonies.citizens.info.colonyID
command.minecolonies.citizens.info.colonyID.citizenID
command.minecolonies.citizens.kill
command.minecolonies.citizens.kill.colonyID
command.minecolonies.citizens.kill.colonyID.citizenID
command.minecolonies.citizens.list
command.minecolonies.citizens.list.colonyID
command.minecolonies.citizens.list.colonyID.startpage
command.minecolonies.citizens.reload
command.minecolonies.citizens.reload.colonyID
command.minecolonies.citizens.reload.colonyID.citizenID
command.minecolonies.citizens.spawnNew
command.minecolonies.citizens.spawnNew.colonyID
command.minecolonies.citizens.teleport
command.minecolonies.citizens.teleport.colonyID
command.minecolonies.citizens.teleport.colonyID.citizenID
command.minecolonies.citizens.teleport.colonyID.citizenID.location
command.minecolonies.citizens.trackPath
command.minecolonies.citizens.trackPath.colonyID
command.minecolonies.citizens.trackPath.colonyID.citizenID
command.minecolonies.citizens.trackPathType
command.minecolonies.citizens.trackPathType.pathjobname
command.minecolonies.citizens.walk
command.minecolonies.citizens.walk.colonyID
command.minecolonies.citizens.walk.colonyID.citizenID
command.minecolonies.citizens.walk.colonyID.citizenID.location
command.minecolonies.colony
command.minecolonies.colony.addOfficer
command.minecolonies.colony.addOfficer.colonyID
command.minecolonies.colony.addOfficer.colonyID.playername
command.minecolonies.colony.canSpawnRaiders
command.minecolonies.colony.canSpawnRaiders.colonyID
command.minecolonies.colony.canSpawnRaiders.colonyID.canSpawn
command.minecolonies.colony.chunkstatus
command.minecolonies.colony.chunkstatus.colonyID
command.minecolonies.colony.claim
command.minecolonies.colony.claim.colonyID
command.minecolonies.colony.claim.colonyID.range
command.minecolonies.colony.claim.colonyID.range.add
command.minecolonies.colony.claiminfo
command.minecolonies.colony.claiminfo.location
command.minecolonies.colony.delete
command.minecolonies.colony.delete.colonyID
command.minecolonies.colony.delete.colonyID.keep / delete buildings
command.minecolonies.colony.delete.colonyID.keep / delete buildings.confirm
command.minecolonies.colony.export
command.minecolonies.colony.export.colonyID
command.minecolonies.colony.home
command.minecolonies.colony.info
command.minecolonies.colony.info.colonyID
command.minecolonies.colony.list
command.minecolonies.colony.list.startpage
command.minecolonies.colony.loadAllColoniesFromBackup
command.minecolonies.colony.loadBackup
command.minecolonies.colony.loadBackup.colonyID
command.minecolonies.colony.printStats
command.minecolonies.colony.printStats.colonyID
command.minecolonies.colony.raid
command.minecolonies.colony.raid.raidtime
command.minecolonies.colony.raid.raidtime.colonyID
command.minecolonies.colony.raid.raidtime.colonyID.raidtype
command.minecolonies.colony.raid.raidtime.colonyID.raidtype.allowships
command.minecolonies.colony.raidhistory
command.minecolonies.colony.raidhistory.colonyID
command.minecolonies.colony.reclaimchunks
command.minecolonies.colony.reclaimchunks.colonyID
command.minecolonies.colony.requestsystem-reset
command.minecolonies.colony.requestsystem-reset-all
command.minecolonies.colony.requestsystem-reset.colonyID
command.minecolonies.colony.setAbandoned
command.minecolonies.colony.setAbandoned.colonyID
command.minecolonies.colony.setDeleteable
command.minecolonies.colony.setDeleteable.colonyID
command.minecolonies.colony.setDeleteable.colonyID.deletable
command.minecolonies.colony.setRank
command.minecolonies.colony.setRank.colonyID
command.minecolonies.colony.setRank.colonyID.playername
command.minecolonies.colony.setRank.colonyID.playername.rank
command.minecolonies.colony.setowner
command.minecolonies.colony.setowner.colonyID
command.minecolonies.colony.setowner.colonyID.playername
command.minecolonies.colony.teleport
command.minecolonies.colony.teleport.colonyID
command.minecolonies.forceunloadchunks
command.minecolonies.help
command.minecolonies.home
command.minecolonies.kill
command.minecolonies.kill.animals
command.minecolonies.kill.chicken
command.minecolonies.kill.cow
command.minecolonies.kill.monster
command.minecolonies.kill.pig
command.minecolonies.kill.raider
command.minecolonies.kill.sheep
command.minecolonies.prune-world-now
command.minecolonies.prune-world-now.stage
command.minecolonies.prune-world-now.stage.additional block protection radius
command.minecolonies.raid-All
command.minecolonies.raid-All.raidtime
command.minecolonies.raid-All.raidtime.raidtype
command.minecolonies.ranks
command.minecolonies.ranks.playername
command.minecolonies.ranks.playername.startpage
command.minecolonies.resetsupplies
command.minecolonies.resetsupplies.playername
command.minecolonies.scan
command.minecolonies.scan.pos1
command.minecolonies.scan.pos1.pos2
command.minecolonies.scan.pos1.pos2.anchor_pos
command.minecolonies.scan.pos1.pos2.player
command.minecolonies.scan.pos1.pos2.player.filename
command.minecolonies.scan.pos1.pos2.player.filename.anchor_pos
command.minecolonies.whereami
command.minecolonies.whoami
command.modernfix
command.modernfix.upgradeStructures
command.modlist
command.modonomicon
command.modonomicon.reload
command.modonomicon.reset
command.modonomicon.reset.book
command.modonomicon.save_progress
command.modonomicon.save_progress.book
command.msg
command.msg.targets
command.msg.targets.message
command.msg_team
command.msg_team.message
command.mute
command.mute.player
command.mute.player.until
command.mysticalcustomization
command.mysticalcustomization.tiers
command.mysticalcustomization.types
command.naaura
command.naaura.add
command.naaura.add.amount
command.naaura.remove
command.naaura.remove.amount
command.naaura.reset
command.naaura.reset.range
command.near
command.near.player
command.near.player.radius
command.near.radius
command.nickname
command.nickname.nickname
command.nicknamefor
command.nicknamefor.player
command.nicknamefor.player.nickname
command.observable
command.observable.allow
command.observable.allow.player
command.observable.config
command.observable.deny
command.observable.deny.player
command.observable.reset
command.observable.run
command.observable.run.duration
command.observable.set
command.observable.set.allPlayersAllowed
command.observable.set.allPlayersAllowed.newVal
command.observable.set.deviation
command.observable.set.deviation.newVal
command.observable.set.includeJvmArgs
command.observable.set.includeJvmArgs.newVal
command.observable.set.notifyInterval
command.observable.set.notifyInterval.newVal
command.observable.set.traceInterval
command.observable.set.traceInterval.newVal
command.observable.set.uploadURL
command.observable.set.uploadURL.newVal
command.observable.tp
command.observable.tp.dim
command.observable.tp.dim.entity
command.observable.tp.dim.entity.id
command.observable.tp.dim.position
command.observable.tp.dim.position.pos
command.occultism
command.occultism.debug
command.occultism.debug.ai
command.occultism.nbt
command.occultism.sharenbt
command.op
command.op.targets
command.open-patchouli-book
command.open-patchouli-book.targets
command.open-patchouli-book.targets.book
command.open-patchouli-book.targets.book.entry
command.open-patchouli-book.targets.book.entry.page
command.open_gateway
command.open_gateway.entity
command.open_gateway.entity.type
command.open_gateway.pos
command.open_gateway.pos.type
command.oredistribution
command.oredistribution.radius
command.oredistribution.radius.--file
command.oredistribution.radius.minHeight
command.oredistribution.radius.minHeight.maxHeight
command.oredistribution.radius.minHeight.maxHeight.--file
command.paraglider
command.paraglider.bargain
command.paraglider.bargain.end
command.paraglider.bargain.end.player
command.paraglider.bargain.start
command.paraglider.bargain.start.player
command.paraglider.bargain.start.player.bargainType
command.paraglider.bargain.start.player.bargainType.pos
command.paraglider.bargain.start.player.bargainType.pos.advancement
command.paraglider.bargain.start.player.bargainType.pos.advancement.lookAt
command.paraglider.give
command.paraglider.give.essence
command.paraglider.give.essence.player
command.paraglider.give.essence.player.amount
command.paraglider.give.heart_container
command.paraglider.give.heart_container.player
command.paraglider.give.heart_container.player.amount
command.paraglider.give.stamina_vessel
command.paraglider.give.stamina_vessel.player
command.paraglider.give.stamina_vessel.player.amount
command.paraglider.query
command.paraglider.query.essence
command.paraglider.query.essence.player
command.paraglider.query.heart_container
command.paraglider.query.heart_container.player
command.paraglider.query.stamina_vessel
command.paraglider.query.stamina_vessel.player
command.paraglider.reloadPlayerStates
command.paraglider.set
command.paraglider.set.essence
command.paraglider.set.essence.player
command.paraglider.set.essence.player.amount
command.paraglider.set.heart_container
command.paraglider.set.heart_container.player
command.paraglider.set.heart_container.player.amount
command.paraglider.set.stamina_vessel
command.paraglider.set.stamina_vessel.player
command.paraglider.set.stamina_vessel.player.amount
command.paraglider.take
command.paraglider.take.essence
command.paraglider.take.essence.player
command.paraglider.take.essence.player.amount
command.paraglider.take.heart_container
command.paraglider.take.heart_container.player
command.paraglider.take.heart_container.player.amount
command.paraglider.take.stamina_vessel
command.paraglider.take.stamina_vessel.player
command.paraglider.take.stamina_vessel.player.amount
command.pardon
command.pardon-ip
command.pardon-ip.target
command.pardon.targets
command.particle
command.particle.name
command.particle.name.pos
command.particle.name.pos.delta
command.particle.name.pos.delta.speed
command.particle.name.pos.delta.speed.count
command.particle.name.pos.delta.speed.count.force
command.particle.name.pos.delta.speed.count.force.viewers
command.particle.name.pos.delta.speed.count.normal
command.particle.name.pos.delta.speed.count.normal.viewers
command.perf
command.perf.start
command.perf.stop
command.place
command.place.feature
command.place.feature.feature
command.place.feature.feature.pos
command.place.jigsaw
command.place.jigsaw.pool
command.place.jigsaw.pool.target
command.place.jigsaw.pool.target.max_depth
command.place.jigsaw.pool.target.max_depth.position
command.place.structure
command.place.structure.structure
command.place.structure.structure.pos
command.place.template
command.place.template.template
command.place.template.template.pos
command.place.template.template.pos.rotation
command.place.template.template.pos.rotation.mirror
command.place.template.template.pos.rotation.mirror.integrity
command.place.template.template.pos.rotation.mirror.integrity.seed
command.placebo
command.placebo.hand
command.placebo.serialize_loot_table
command.placebo.serialize_loot_table.loot_table
command.placebo.string_to_obj
command.placebo.string_to_obj.nbt_item
command.playsound
command.playsound.sound
command.playsound.sound.ambient
command.playsound.sound.ambient.targets
command.playsound.sound.ambient.targets.pos
command.playsound.sound.ambient.targets.pos.volume
command.playsound.sound.ambient.targets.pos.volume.pitch
command.playsound.sound.ambient.targets.pos.volume.pitch.minVolume
command.playsound.sound.block
command.playsound.sound.block.targets
command.playsound.sound.block.targets.pos
command.playsound.sound.block.targets.pos.volume
command.playsound.sound.block.targets.pos.volume.pitch
command.playsound.sound.block.targets.pos.volume.pitch.minVolume
command.playsound.sound.hostile
command.playsound.sound.hostile.targets
command.playsound.sound.hostile.targets.pos
command.playsound.sound.hostile.targets.pos.volume
command.playsound.sound.hostile.targets.pos.volume.pitch
command.playsound.sound.hostile.targets.pos.volume.pitch.minVolume
command.playsound.sound.master
command.playsound.sound.master.targets
command.playsound.sound.master.targets.pos
command.playsound.sound.master.targets.pos.volume
command.playsound.sound.master.targets.pos.volume.pitch
command.playsound.sound.master.targets.pos.volume.pitch.minVolume
command.playsound.sound.music
command.playsound.sound.music.targets
command.playsound.sound.music.targets.pos
command.playsound.sound.music.targets.pos.volume
command.playsound.sound.music.targets.pos.volume.pitch
command.playsound.sound.music.targets.pos.volume.pitch.minVolume
command.playsound.sound.neutral
command.playsound.sound.neutral.targets
command.playsound.sound.neutral.targets.pos
command.playsound.sound.neutral.targets.pos.volume
command.playsound.sound.neutral.targets.pos.volume.pitch
command.playsound.sound.neutral.targets.pos.volume.pitch.minVolume
command.playsound.sound.player
command.playsound.sound.player.targets
command.playsound.sound.player.targets.pos
command.playsound.sound.player.targets.pos.volume
command.playsound.sound.player.targets.pos.volume.pitch
command.playsound.sound.player.targets.pos.volume.pitch.minVolume
command.playsound.sound.record
command.playsound.sound.record.targets
command.playsound.sound.record.targets.pos
command.playsound.sound.record.targets.pos.volume
command.playsound.sound.record.targets.pos.volume.pitch
command.playsound.sound.record.targets.pos.volume.pitch.minVolume
command.playsound.sound.voice
command.playsound.sound.voice.targets
command.playsound.sound.voice.targets.pos
command.playsound.sound.voice.targets.pos.volume
command.playsound.sound.voice.targets.pos.volume.pitch
command.playsound.sound.voice.targets.pos.volume.pitch.minVolume
command.playsound.sound.weather
command.playsound.sound.weather.targets
command.playsound.sound.weather.targets.pos
command.playsound.sound.weather.targets.pos.volume
command.playsound.sound.weather.targets.pos.volume.pitch
command.playsound.sound.weather.targets.pos.volume.pitch.minVolume
command.pncr
command.pncr.amadrone_deliver
command.pncr.amadrone_deliver.player
command.pncr.amadrone_deliver.player.fromPos
command.pncr.amadrone_deliver.toPos
command.pncr.amadrone_deliver.toPos.fromPos
command.pncr.armor_upgrade
command.pncr.armor_upgrade.upgrade
command.pncr.armor_upgrade.upgrade.enabled
command.pncr.dump_nbt
command.pncr.global_var
command.pncr.global_var.delete
command.pncr.global_var.delete.varname
command.pncr.global_var.get
command.pncr.global_var.get.varname
command.pncr.global_var.list
command.pncr.global_var.set
command.pncr.global_var.set.varname
command.pncr.global_var.set.varname.item
command.pncr.global_var.set.varname.pos
command.polymorph
command.polymorph.conflicts
command.ponderjs
command.ponderjs.generate_lang_template
command.ponderjs.generate_lang_template.lang
command.quarryplus
command.quarryplus.checkTicket
command.quarryplus.checkTicket.pos
command.quarryplus.config
command.quarryplus.config.name
command.quarryplus.config.name.value
command.railways
command.railways.split_train
command.railways.split_train.train_id
command.railways.split_train.train_id.number
command.railways.split_train.train_name
command.railways.split_train.train_name.number
command.railways.train_uuid
command.railways.train_uuid.name
command.recipe
command.recipe.give
command.recipe.give.targets
command.recipe.give.targets.all
command.recipe.give.targets.recipe
command.recipe.take
command.recipe.take.targets
command.recipe.take.targets.all
command.recipe.take.targets.recipe
command.recording
command.refinedstorage
command.refinedstorage.disk
command.refinedstorage.disk.create
command.refinedstorage.disk.create.player
command.refinedstorage.disk.create.player.id
command.refinedstorage.disk.list
command.refinedstorage.disk.list.player
command.refinedstorage.network
command.refinedstorage.network.get
command.refinedstorage.network.get.dimension
command.refinedstorage.network.get.dimension.pos
command.refinedstorage.network.get.dimension.pos.autocrafting
command.refinedstorage.network.get.dimension.pos.autocrafting.cancel
command.refinedstorage.network.get.dimension.pos.autocrafting.cancel.id
command.refinedstorage.network.get.dimension.pos.autocrafting.get
command.refinedstorage.network.get.dimension.pos.autocrafting.get.id
command.refinedstorage.network.get.dimension.pos.autocrafting.list
command.refinedstorage.network.list
command.refinedstorage.network.list.dimension
command.refinedstorage.pattern
command.refinedstorage.pattern.dump
command.reload
command.resetchunks
command.resetchunks.range
command.resetchunks.range.skipOldChunks
command.respawn_draconic_guardian
command.return
command.return.value
command.rftoolsstorage
command.rftoolsstorage.list
command.rftoolsstorage.restore
command.rftoolsstorage.restore.uuid
command.rftoolsutility
command.rftoolsutility.cleanupreceivers
command.rftoolsutility.setbuffs
command.rftoolsutility.setbuffs.style
command.rftoolsutility.setbuffs.style.x
command.rftoolsutility.setbuffs.style.x.y
command.ride
command.ride.target
command.ride.target.dismount
command.ride.target.mount
command.ride.target.mount.vehicle
command.rtp
command.save-all
command.save-all.flush
command.save-off
command.save-on
command.say
command.say.message
command.sbp
command.sbp.give
command.sbp.give.targets
command.sbp.give.targets.backpackUuid
command.sbp.list
command.sbp.list.playerName
command.sbp.removeNonPlayer
command.sbp.removeNonPlayer.onlyWithEmptyInventory
command.sc
command.sc.bug
command.sc.connect
command.sc.convert
command.sc.convert.mode
command.sc.convert.mode.fill
command.sc.convert.mode.fill.from
command.sc.convert.mode.fill.from.to
command.sc.convert.mode.set
command.sc.convert.mode.set.pos
command.sc.dump
command.sc.dump.registry
command.sc.help
command.sc.owner
command.sc.owner.fill
command.sc.owner.fill.from
command.sc.owner.fill.from.to
command.sc.owner.fill.from.to.player
command.sc.owner.fill.from.to.player.owner
command.sc.owner.fill.from.to.random
command.sc.owner.fill.from.to.reset
command.sc.owner.set
command.sc.owner.set.pos
command.sc.owner.set.pos.player
command.sc.owner.set.pos.player.owner
command.sc.owner.set.pos.random
command.sc.owner.set.pos.reset
command.schedule
command.schedule.clear
command.schedule.clear.function
command.schedule.function
command.schedule.function.function
command.schedule.function.function.time
command.schedule.function.function.time.append
command.schedule.function.function.time.replace
command.scoreboard
command.scoreboard.objectives
command.scoreboard.objectives.add
command.scoreboard.objectives.add.objective
command.scoreboard.objectives.add.objective.criteria
command.scoreboard.objectives.add.objective.criteria.displayName
command.scoreboard.objectives.list
command.scoreboard.objectives.modify
command.scoreboard.objectives.modify.objective
command.scoreboard.objectives.modify.objective.displayname
command.scoreboard.objectives.modify.objective.displayname.displayName
command.scoreboard.objectives.modify.objective.rendertype
command.scoreboard.objectives.modify.objective.rendertype.hearts
command.scoreboard.objectives.modify.objective.rendertype.integer
command.scoreboard.objectives.remove
command.scoreboard.objectives.remove.objective
command.scoreboard.objectives.setdisplay
command.scoreboard.objectives.setdisplay.slot
command.scoreboard.objectives.setdisplay.slot.objective
command.scoreboard.players
command.scoreboard.players.add
command.scoreboard.players.add.targets
command.scoreboard.players.add.targets.objective
command.scoreboard.players.add.targets.objective.score
command.scoreboard.players.enable
command.scoreboard.players.enable.targets
command.scoreboard.players.enable.targets.objective
command.scoreboard.players.get
command.scoreboard.players.get.target
command.scoreboard.players.get.target.objective
command.scoreboard.players.list
command.scoreboard.players.list.target
command.scoreboard.players.operation
command.scoreboard.players.operation.targets
command.scoreboard.players.operation.targets.targetObjective
command.scoreboard.players.operation.targets.targetObjective.operation
command.scoreboard.players.operation.targets.targetObjective.operation.source
command.scoreboard.players.operation.targets.targetObjective.operation.source.sourceObjective
command.scoreboard.players.remove
command.scoreboard.players.remove.targets
command.scoreboard.players.remove.targets.objective
command.scoreboard.players.remove.targets.objective.score
command.scoreboard.players.reset
command.scoreboard.players.reset.targets
command.scoreboard.players.reset.targets.objective
command.scoreboard.players.set
command.scoreboard.players.set.targets
command.scoreboard.players.set.targets.objective
command.scoreboard.players.set.targets.objective.score
command.securitycraft
command.securitycraft.bug
command.securitycraft.connect
command.securitycraft.convert
command.securitycraft.convert.mode
command.securitycraft.convert.mode.fill
command.securitycraft.convert.mode.fill.from
command.securitycraft.convert.mode.fill.from.to
command.securitycraft.convert.mode.set
command.securitycraft.convert.mode.set.pos
command.securitycraft.dump
command.securitycraft.dump.registry
command.securitycraft.help
command.securitycraft.owner
command.securitycraft.owner.fill
command.securitycraft.owner.fill.from
command.securitycraft.owner.fill.from.to
command.securitycraft.owner.fill.from.to.player
command.securitycraft.owner.fill.from.to.player.owner
command.securitycraft.owner.fill.from.to.random
command.securitycraft.owner.fill.from.to.reset
command.securitycraft.owner.set
command.securitycraft.owner.set.pos
command.securitycraft.owner.set.pos.player
command.securitycraft.owner.set.pos.player.owner
command.securitycraft.owner.set.pos.random
command.securitycraft.owner.set.pos.reset
command.seed
command.set_damage
command.set_damage.amount
command.set_damage.max
command.setblock
command.setblock.pos
command.setblock.pos.block
command.setblock.pos.block.destroy
command.setblock.pos.block.keep
command.setblock.pos.block.replace
command.sethome
command.sethome.name
command.setidletimeout
command.setidletimeout.minutes
command.setwarp
command.setwarp.name
command.setworldspawn
command.setworldspawn.pos
command.setworldspawn.pos.angle
command.sfm
command.sfm.bust_cable_network_cache
command.sfm.bust_water_network_cache
command.sfm.changelog
command.sfm.config
command.sfm.config.edit
command.sfm.config.edit.CLIENT
command.sfm.config.edit.SERVER
command.sfm.config.show
command.sfm.config.show.variant
command.sfm.show_bad_cable_cache_entries
command.sfm.show_bad_cable_cache_entries.block
command.sgear_grade
command.sgear_grade.set
command.sgear_grade.set.grade
command.sgear_mats
command.sgear_mats.dump
command.sgear_mats.dump.includeChildren
command.sgear_mats.list
command.sgear_parts
command.sgear_parts.dump
command.sgear_parts.list
command.sgear_random_gear
command.sgear_random_gear.players
command.sgear_random_gear.players.item
command.sgear_random_gear.players.item.tier
command.sgear_stats
command.sgear_stats.info
command.sgear_stats.info.player
command.sgear_stats.lock
command.sgear_stats.recalculate
command.sgear_stats.recalculate.players
command.sgear_traits
command.sgear_traits.describe
command.sgear_traits.describe.traitID
command.sgear_traits.dump_md
command.sgear_traits.list
command.sgjourney
command.sgjourney.debugInfo
command.sgjourney.gene
command.sgjourney.gene.target
command.sgjourney.gene.target.add
command.sgjourney.gene.target.add.ancient
command.sgjourney.gene.target.add.artificial
command.sgjourney.gene.target.add.inherited
command.sgjourney.gene.target.remove
command.sgjourney.stargateNetwork
command.sgjourney.stargateNetwork.address
command.sgjourney.stargateNetwork.address.dimension
command.sgjourney.stargateNetwork.extragalacticAddress
command.sgjourney.stargateNetwork.extragalacticAddress.dimension
command.sgjourney.stargateNetwork.forceStellarUpdate
command.sgjourney.stargateNetwork.getAllStargates
command.sgjourney.stargateNetwork.getAllStargates.dimension
command.sgjourney.stargateNetwork.settings
command.sgjourney.stargateNetwork.settings.get
command.sgjourney.stargateNetwork.settings.set
command.sgjourney.stargateNetwork.settings.set.generateRandomSolarSystems
command.sgjourney.stargateNetwork.settings.set.generateRandomSolarSystems.generateRandomSolarSystems
command.sgjourney.stargateNetwork.settings.set.randomAddressFromSeed
command.sgjourney.stargateNetwork.settings.set.randomAddressFromSeed.randomAddressFromSeed
command.sgjourney.stargateNetwork.settings.set.useDatapackAddresses
command.sgjourney.stargateNetwork.settings.set.useDatapackAddresses.useDatapackAddresses
command.sgjourney.stargateNetwork.version
command.sgjourney.transporterNetwork
command.sgjourney.transporterNetwork.getAllTransporters
command.sgjourney.transporterNetwork.getAllTransporters.dimension
command.sgjourney.transporterNetwork.reload
command.showmahou
command.shrink
command.shrink.player
command.shrink.player.size
command.simplebackups
command.simplebackups.backup
command.simplebackups.backup.pause
command.simplebackups.backup.start
command.simplebackups.backup.start.quiet
command.simplebackups.backup.unpause
command.simplebackups.mergeBackups
command.sl_nbt
command.sl_nbt.block
command.sl_nbt.block.pos
command.sl_nbt.entity
command.sl_nbt.entity.target
command.sl_nbt.item
command.sl_tp
command.sl_tp.entity
command.sl_tp.entity.dimension
command.sl_tp.entity.dimension.pos
command.smithing
command.snr
command.snr.split_train
command.snr.split_train.train_id
command.snr.split_train.train_id.number
command.snr.split_train.train_name
command.snr.split_train.train_name.number
command.snr.train_uuid
command.snr.train_uuid.name
command.spawn
command.spawnpoint
command.spawnpoint.targets
command.spawnpoint.targets.pos
command.spawnpoint.targets.pos.angle
command.spc_teams
command.spc_teams.accept
command.spc_teams.create
command.spc_teams.create.teamId
command.spc_teams.create.teamId.displayName
command.spc_teams.demote
command.spc_teams.demote.player
command.spc_teams.disband
command.spc_teams.info
command.spc_teams.info.teamId
command.spc_teams.invite
command.spc_teams.invite.player
command.spc_teams.kick
command.spc_teams.kick.player
command.spc_teams.leave
command.spc_teams.promote
command.spc_teams.promote.player
command.spc_teams.rename
command.spc_teams.rename.displayName
command.spc_teams.teams
command.spc_teams.transfer
command.spc_teams.transfer.player
command.spc_teams_sysadmin
command.spc_teams_sysadmin.add
command.spc_teams_sysadmin.add.teamId
command.spc_teams_sysadmin.add.teamId.players
command.spc_teams_sysadmin.add.teamId.players.force
command.spc_teams_sysadmin.create
command.spc_teams_sysadmin.create.teamId
command.spc_teams_sysadmin.create.teamId.displayName
command.spc_teams_sysadmin.disband
command.spc_teams_sysadmin.disband.teamId
command.spc_teams_sysadmin.kick
command.spc_teams_sysadmin.kick.players
command.spc_teams_sysadmin.kick.players.inform
command.spc_teams_sysadmin.rename
command.spc_teams_sysadmin.rename.teamId
command.spc_teams_sysadmin.rename.teamId.displayName
command.spc_teams_sysadmin.transfer
command.spc_teams_sysadmin.transfer.teamId
command.spc_teams_sysadmin.transfer.teamId.player
command.spc_teams_sysadmin.transfer.teamId.player.force
command.spectate
command.spectate.target
command.spectate.target.player
command.speed
command.speed.boost_percent
command.speed.boost_percent.player
command.spreadplayers
command.spreadplayers.center
command.spreadplayers.center.spreadDistance
command.spreadplayers.center.spreadDistance.maxRange
command.spreadplayers.center.spreadDistance.maxRange.respectTeams
command.spreadplayers.center.spreadDistance.maxRange.respectTeams.targets
command.spreadplayers.center.spreadDistance.maxRange.under
command.spreadplayers.center.spreadDistance.maxRange.under.maxHeight
command.spreadplayers.center.spreadDistance.maxRange.under.maxHeight.respectTeams
command.spreadplayers.center.spreadDistance.maxRange.under.maxHeight.respectTeams.targets
command.stonecutter
command.stop
command.stopsound
command.stopsound.targets
command.stopsound.targets.all
command.stopsound.targets.all.sound
command.stopsound.targets.ambient
command.stopsound.targets.ambient.sound
command.stopsound.targets.block
command.stopsound.targets.block.sound
command.stopsound.targets.hostile
command.stopsound.targets.hostile.sound
command.stopsound.targets.master
command.stopsound.targets.master.sound
command.stopsound.targets.music
command.stopsound.targets.music.sound
command.stopsound.targets.neutral
command.stopsound.targets.neutral.sound
command.stopsound.targets.player
command.stopsound.targets.player.sound
command.stopsound.targets.record
command.stopsound.targets.record.sound
command.stopsound.targets.voice
command.stopsound.targets.voice.sound
command.stopsound.targets.weather
command.stopsound.targets.weather.sound
command.streaming
command.structure_gel
command.structure_gel.building_tool
command.structure_gel.building_tool.clear_clipboard
command.structure_gel.building_tool.clear_clipboard.player
command.structure_gel.building_tool.clear_clipboard.player.dimension
command.structure_gel.building_tool.clear_history
command.structure_gel.building_tool.clear_history.player
command.structure_gel.building_tool.clear_history.player.dimension
command.structure_gel.building_tool.redo
command.structure_gel.building_tool.redo.player
command.structure_gel.building_tool.redo.player.amount
command.structure_gel.building_tool.undo
command.structure_gel.building_tool.undo.player
command.structure_gel.building_tool.undo.player.amount
command.structure_gel.distance
command.structure_gel.distance.biome
command.structure_gel.distance.biome.biome
command.structure_gel.distance.biome.biome.sample_size
command.structure_gel.distance.structure
command.structure_gel.distance.structure.structure
command.structure_gel.distance.structure.structure.sample_size
command.structure_gel.distance.vanilla_stats
command.structure_gel.getbiomes
command.structure_gel.getbiomes.structure
command.structure_gel.getfeatures
command.structure_gel.getfeatures.biome
command.structure_gel.getfeatures.biome.biome
command.structure_gel.getfeatures.here
command.structure_gel.getfeatures.here.pos
command.structure_gel.getspawns
command.structure_gel.getspawns.aether_aerwhale
command.structure_gel.getspawns.aether_darkness_monster
command.structure_gel.getspawns.aether_sky_monster
command.structure_gel.getspawns.aether_surface_monster
command.structure_gel.getspawns.ambient
command.structure_gel.getspawns.axolotls
command.structure_gel.getspawns.creature
command.structure_gel.getspawns.misc
command.structure_gel.getspawns.monster
command.structure_gel.getspawns.underground_water_creature
command.structure_gel.getspawns.water_ambient
command.structure_gel.getspawns.water_creature
command.structure_gel.getstructures
command.structure_gel.getstructures.biome
command.structure_gel.getstructures.biome.biome
command.structure_gel.getstructures.here
command.structure_gel.getstructures.here.pos
command.structure_gel.savestructures
command.structure_gel.savestructures.from
command.structure_gel.savestructures.from.to
command.structure_gel.savestructures.search
command.structure_gel.savestructures.search.radius
command.structure_gel.viewbounds
command.structure_gel.viewbounds.off
command.structure_gel.viewbounds.on
command.structure_gel.viewbounds.refresh
command.structurize
command.structurize.DO
command.structurize.DO.start
command.structurize.DO.start.end
command.structurize.DO.start.end.world
command.structurize.paste
command.structurize.paste.pos
command.structurize.paste.pos.pack
command.structurize.paste.pos.pack.path
command.structurize.paste.pos.pack.path.rotation
command.structurize.paste.pos.pack.path.rotation.mirror
command.structurize.paste.pos.pack.path.rotation.mirror.pretty
command.structurize.paste.pos.pack.path.rotation.mirror.pretty.player
command.structurize.pasteFolder
command.structurize.pasteFolder.pos
command.structurize.pasteFolder.pos.pack
command.structurize.pasteFolder.pos.pack.path
command.structurize.pasteFolder.pos.pack.path.rotation
command.structurize.pasteFolder.pos.pack.path.rotation.player
command.structurize.pasteFolder.pos.pack.path.rotation.player.mirror
command.structurize.pasteFolder.pos.pack.path.rotation.player.mirror.pretty
command.structurize.pasteFolder.pos.pack.path.rotation.player.mirror.pretty.plotSize
command.structurize.scan
command.structurize.scan.pos1
command.structurize.scan.pos1.pos2
command.structurize.scan.pos1.pos2.anchor_pos
command.structurize.scan.pos1.pos2.player
command.structurize.scan.pos1.pos2.player.filename
command.structurize.scan.pos1.pos2.player.filename.anchor_pos
command.summon
command.summon.entity
command.summon.entity.pos
command.summon.entity.pos.nbt
command.supplementaries
command.supplementaries.cage
command.supplementaries.cage.entity
command.supplementaries.configs
command.supplementaries.dimension
command.supplementaries.dimension.dimension
command.supplementaries.dimension.dimension.location
command.supplementaries.dimension.dimension.targets
command.supplementaries.dimension.dimension.targets.location
command.supplementaries.globe
command.supplementaries.globe.newseed
command.supplementaries.globe.resetseed
command.supplementaries.map
command.supplementaries.map.add_marker
command.supplementaries.map.add_marker.marker
command.supplementaries.map.structure_map
command.supplementaries.map.structure_map.structure
command.supplementaries.map.structure_map.structure.zoom
command.supplementaries.record
command.supplementaries.record.start
command.supplementaries.record.start.source
command.supplementaries.record.start.source.instrument_0
command.supplementaries.record.start.source.instrument_0.instrument_1
command.supplementaries.record.start.source.instrument_0.instrument_1.instrument_2
command.supplementaries.record.start.source.instrument_0.instrument_1.instrument_2.instrument_3
command.supplementaries.record.stop
command.supplementaries.record.stop.name
command.supplementaries.record.stop.name.speed_up_by
command.supplementaries.registry
command.supplementaries.registry.registry
command.supplementaries.registry.registry.list
command.supplementaries.registry.registry.list.page
command.supplementaries.registry.registry.search
command.supplementaries.registry.registry.search.keyword
command.supplementaries.registry.registry.search.keyword.page
command.supplementaries.reload
command.supplementaries.roll
command.supplementaries.roll.dice
command.tag
command.tag.targets
command.tag.targets.add
command.tag.targets.add.name
command.tag.targets.list
command.tag.targets.remove
command.tag.targets.remove.name
command.tb
command.tb.access
command.tb.access.pos
command.tb.access.target
command.tb.clear
command.tb.clear.player
command.tb.remove
command.tb.remove.player
command.tb.restore
command.tb.restore.target
command.tb.restore.target.backpack_id
command.tb.unpack
command.tb.unpack.pos
command.tb.unpack.target
command.tbacceptteleport
command.tbacceptteleport.target
command.tbalignment
command.tbalignment.player
command.tbalignment.player.decrease
command.tbalignment.player.decrease.amount
command.tbalignment.player.increase
command.tbalignment.player.increase.amount
command.tbalignment.player.set
command.tbalignment.player.set.amount
command.tbalignment.player.show
command.tbgui
command.tbknowledge
command.tbknowledge.player
command.tbknowledge.player.decrease
command.tbknowledge.player.decrease.amount
command.tbknowledge.player.increase
command.tbknowledge.player.increase.amount
command.tbknowledge.player.reset_ankh
command.tbknowledge.player.reset_perks
command.tbknowledge.player.set
command.tbknowledge.player.set.amount
command.tbknowledge.player.show
command.tbrecovery
command.tbrecovery.load_player
command.tbrecovery.load_player.player
command.tbrecovery.load_player.player.file_string
command.tbrecovery.save_all_players
command.tbrecovery.save_player
command.tbrecovery.save_player.player
command.tbrequestteleport
command.tbrequestteleport.target
command.tbrestoreinventory
command.tbrestoreinventory.player
command.tbrevivefamiliar
command.tbrevivefamiliar.player
command.tbshowlastgrave
command.tbshowlastgrave.player
command.tbsiege
command.tbsiege.start
command.tbsiege.start.dim
command.tbsiege.stop
command.tbsiege.stop.dim
command.tbteleport
command.tbteleport.biome
command.tbteleport.biome.target
command.tbteleport.biome.target.biome
command.tbteleport.biome.target.biome.dim
command.tbteleport.death
command.tbteleport.discovery
command.tbteleport.discovery.target
command.tbteleport.discovery.target.structure
command.tbteleport.discovery.target.structure.dim
command.tbteleport.grave
command.tbteleport.grave.player
command.tbteleport.grave.player.target
command.tbteleport.home
command.tbteleport.home.player
command.tbteleport.home.player.target
command.tbteleport.player
command.tbteleport.player.source
command.tbteleport.player.source.target
command.tbteleport.pos
command.tbteleport.pos.source
command.tbteleport.pos.source.pos
command.tbteleport.pos.source.pos.dim
command.team
command.team.add
command.team.add.team
command.team.add.team.displayName
command.team.empty
command.team.empty.team
command.team.join
command.team.join.team
command.team.join.team.members
command.team.leave
command.team.leave.members
command.team.list
command.team.list.team
command.team.modify
command.team.modify.team
command.team.modify.team.collisionRule
command.team.modify.team.collisionRule.always
command.team.modify.team.collisionRule.never
command.team.modify.team.collisionRule.pushOtherTeams
command.team.modify.team.collisionRule.pushOwnTeam
command.team.modify.team.color
command.team.modify.team.color.value
command.team.modify.team.deathMessageVisibility
command.team.modify.team.deathMessageVisibility.always
command.team.modify.team.deathMessageVisibility.hideForOtherTeams
command.team.modify.team.deathMessageVisibility.hideForOwnTeam
command.team.modify.team.deathMessageVisibility.never
command.team.modify.team.displayName
command.team.modify.team.displayName.displayName
command.team.modify.team.friendlyFire
command.team.modify.team.friendlyFire.allowed
command.team.modify.team.nametagVisibility
command.team.modify.team.nametagVisibility.always
command.team.modify.team.nametagVisibility.hideForOtherTeams
command.team.modify.team.nametagVisibility.hideForOwnTeam
command.team.modify.team.nametagVisibility.never
command.team.modify.team.prefix
command.team.modify.team.prefix.prefix
command.team.modify.team.seeFriendlyInvisibles
command.team.modify.team.seeFriendlyInvisibles.allowed
command.team.modify.team.suffix
command.team.modify.team.suffix.suffix
command.team.remove
command.team.remove.team
command.teammsg
command.teammsg.message
command.teleport
command.teleport.destination
command.teleport.location
command.teleport.targets
command.teleport.targets.destination
command.teleport.targets.location
command.teleport.targets.location.facing
command.teleport.targets.location.facing.entity
command.teleport.targets.location.facing.entity.facingEntity
command.teleport.targets.location.facing.entity.facingEntity.facingAnchor
command.teleport.targets.location.facing.facingLocation
command.teleport.targets.location.rotation
command.teleport_last
command.teleport_last.player
command.tellraw
command.tellraw.targets
command.tellraw.targets.message
command.time
command.time.add
command.time.add.time
command.time.query
command.time.query.day
command.time.query.daytime
command.time.query.gametime
command.time.set
command.time.set.day
command.time.set.midnight
command.time.set.night
command.time.set.noon
command.time.set.time
command.title
command.title.targets
command.title.targets.actionbar
command.title.targets.actionbar.title
command.title.targets.clear
command.title.targets.reset
command.title.targets.subtitle
command.title.targets.subtitle.title
command.title.targets.times
command.title.targets.times.fadeIn
command.title.targets.times.fadeIn.stay
command.title.targets.times.fadeIn.stay.fadeOut
command.title.targets.title
command.title.targets.title.title
command.torchmaster
command.torchmaster.entitydump
command.torchmaster.torchdump
command.torchmaster.try_setup_siege
command.tp_offline
command.tp_offline.id
command.tp_offline.id.player_id
command.tp_offline.id.player_id.pos
command.tp_offline.name
command.tp_offline.name.player
command.tp_offline.name.player.pos
command.tpa
command.tpa.target
command.tpaccept
command.tpaccept.id
command.tpahere
command.tpahere.target
command.tpdeny
command.tpdeny.id
command.tpx
command.tpx.destination
command.tpx.dimension
command.tpx.dimension.location
command.tpx.targets
command.tpx.targets.destination
command.tpx.targets.dimension
command.tpx.targets.dimension.location
command.trashcan
command.trigger
command.trigger.objective
command.trigger.objective.add
command.trigger.objective.add.value
command.trigger.objective.set
command.trigger.objective.set.value
command.twilightforest
command.twilightforest.biomepng
command.twilightforest.biomepng.width
command.twilightforest.biomepng.width.height
command.twilightforest.biomepng.width.height.showBiomePercents
command.twilightforest.center
command.twilightforest.conquer
command.twilightforest.conquer.reactivate
command.twilightforest.genbook
command.twilightforest.genbook.structure
command.twilightforest.info
command.twilightforest.shield
command.twilightforest.shield.target
command.twilightforest.shield.target.add
command.twilightforest.shield.target.add.amount
command.twilightforest.shield.target.add.amount.temp
command.twilightforest.shield.target.set
command.twilightforest.shield.target.set.amount
command.twilightforest.shield.target.set.amount.temp
command.unmute
command.unmute.player
command.unshrink
command.unshrink.player
command.warp
command.warp.name
command.waypoint
command.waypoint.create
command.waypoint.create.name
command.waypoint.create.name.dimension
command.waypoint.create.name.dimension.location
command.waypoint.create.name.dimension.location.color
command.waypoint.create.name.dimension.location.color.players
command.waypoint.create.name.dimension.location.color.players.announce
command.waypoint.delete
command.waypoint.delete.name
command.waypoint.delete.name.players
command.waypoint.delete.name.players.announce
command.weather
command.weather.clear
command.weather.clear.duration
command.weather.rain
command.weather.rain.duration
command.weather.thunder
command.weather.thunder.duration
command.whitelist
command.whitelist.add
command.whitelist.add.targets
command.whitelist.list
command.whitelist.off
command.whitelist.on
command.whitelist.reload
command.whitelist.remove
command.whitelist.remove.targets
command.woodcutter
command.woodcutter.datapack
command.woodcutter.datapack.apply
command.woodcutter.datapack.apply.modid
command.woodcutter.datapack.generate
command.woodcutter.datapack.generate.modid
command.woodcutter.datapack.remove
command.woodcutter.datapack.remove.modid
command.woodcutter.info
command.woodcutter.test
command.woodcutter.test.recipe
command.worldborder
command.worldborder.add
command.worldborder.add.distance
command.worldborder.add.distance.time
command.worldborder.center
command.worldborder.center.pos
command.worldborder.damage
command.worldborder.damage.amount
command.worldborder.damage.amount.damagePerBlock
command.worldborder.damage.buffer
command.worldborder.damage.buffer.distance
command.worldborder.get
command.worldborder.set
command.worldborder.set.distance
command.worldborder.set.distance.time
command.worldborder.warning
command.worldborder.warning.distance
command.worldborder.warning.distance.distance
command.worldborder.warning.time
command.worldborder.warning.time.time
command.zerocore
command.zerocore.debug
command.zerocore.debug.gui
command.zerocore.debug.gui.hoverFrame
command.zerocore.debug.gui.hoverFrame.disable
command.zerocore.debug.gui.hoverFrame.enable
command.zerocore.recipe
command.zerocore.recipe.clearCache
