#Fire will float you upward.
ascendingWinds = true
#You can customize which block produces wind.
#Write each blockstate to one of this format:
#  [block ID]   (Matches all state of the block)
#  [block ID]#[property1=value],[property2=value],[property3=value]   (Matches state of the block that has specified properties)
#  #[Tag ID]   (Matches all blocks with the tag)
#Same property cannot be specified multiple times. Wind sources with any invalid part will be excluded.
windSources = ["fire", "soul_fire", "campfire#lit=true", "soul_campfire#lit=true"]
#Multiplier to horizontal movement speed while paragliding.
#Value of 0.5 means 50% of the speed, 2.0 means two times the speed and so forth.
#Range: 0.2 ~ 10.0
paraglidingSpeed = 1.0
#Durability of Paragliders. Set to zero to disable durability.
#Range: > 0
paragliderDurability = 0
#Configurable option for Towers of the Wild compat feature. Can be ignored if Towers of the Wild is not installed.
#DEFAULT: Default option, spawn Deku Leaf in ocean tower chests and Paraglider in normal tower chests
#DISABLE: Don't spawn anything
#PARAGLIDER_ONLY: Spawn paraglider in both ocean and normal tower chests
#DEKU_LEAF_ONLY: Spawn deku leaf in both ocean and normal tower chests, like a boss
#Allowed Values: DEFAULT, DISABLE, PARAGLIDER_ONLY, DEKU_LEAF_ONLY
paragliderInTowersOfTheWild = "DEFAULT"

[spiritOrbs]
	#If true, Wither will drop heart container(stamina vessel if heart container is disabled) upon death.
	enderDragonDropsVessel = true
	#If true, Raids will give heart container(stamina vessel if heart container is disabled) upon victory.
	raidGivesVessel = true
	#Amount of Spirit Orbs dropped from spawners.
	#Range: 0 ~ 64
	spawnerSpiritOrbDrops = 2
	#If true, various types of chest will have chances of having Spirit Orbs inside.
	#Does not change contents of already generated chests.
	spiritOrbLoots = true

[vessels]
	#Starting health points measured in number of hearts.
	#Range: 1 ~ 512
	startingHearts = 10
	#Maximum amount of Heart Containers one player can consume.
	#Do note that the maximum health point is capped at value of 1024 (or 512 hearts) by Minecraft's default
	#attribute system; without modifying these limits, Heart Containers won't give you extra hearts beyond that.
	#Range: 0 ~ 512
	maxHeartContainers = 20
	#Maximum amount of stamina Player can get. Do note that one third of this value is equal to one stamina wheel.
	#Range: > 0
	maxStamina = 3000
	#Amount of stamina Player starts with. Values higher than maxStamina doesn't work.
	#If you want to make starting stamina displayed as one full stamina wheel, this value should be one third of maxStamina.
	#Range: > 0
	startingStamina = 1000
	#Stamina Vessels players need to obtain max out stamina. More vessels means lesser stamina increase per vessel.
	#Range: > 0
	maxStaminaVessels = 10

[stamina]
	#Paragliding will consume stamina.
	paraglidingConsumesStamina = true
	#Certain non-paragliding actions, such as running and swimming, will consume stamina.
	runningAndSwimmingConsumesStamina = false

