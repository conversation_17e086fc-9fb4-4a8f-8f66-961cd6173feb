
[General]
	#How often a hive should attempt special events like spawning undead bees. Default 500.
	#Range: > 20
	hiveTickRate = 1500
	#How long time a bee should stay in the hive when having delivered honey. Default 4800.
	#Range: > 20
	timeInHive = 4800
	#How many ticks it takes for process a recipe in the centrifuge. Default 300.
	#Range: > 20
	centrifugeProcessingTime = 300
	#How many ticks it takes for process a recipe in the powered centrifuge. Default 100.
	#Range: > 20
	centrifugePoweredProcessingTime = 100
	#How much FE to use per tick for a powered centrifuge when processing an item. Default 10.
	#Range: > 1
	centrifugePowerUse = 10
	#How many ticks it takes for process a recipe in the incubator. Default 3600.
	#Range: > 20
	incubatorProcessingTime = 3600
	#How much FE to use per tick for an incubator when processing an item. Default 10.
	#Range: > 1
	incubatorPowerUse = 10
	#How many treats to use when incubating a bee. Default 20.
	#Range: 1 ~ 64
	incubatorTreatUse = 20
	#How many ticks it takes for process a recipe in the breeding chamber. Default 6000.
	#Range: > 20
	breedingChamberProcessingTime = 6000
	#How much FE to use per tick for an incubator when processing an item. Default 10.
	#Range: > 1
	breedingChamberPowerUse = 50
	#How much FE to generate per tick. Default 60.
	#Range: > 1
	generatorPowerGen = 60
	#How much honey to consume per tick. Default 5.
	#Range: > 1
	generatorHoneyUse = 2
	#A priority list of Mod IDs that results of comb output should stem from, aka which mod you want the copper to come from.
	preferredTagSource = ["minecraft", "productivebees", "alltheores", "ato", "thermal", "tconstruct", "create", "immersiveengineering", "mekanism", "enderio", "silents_mechanisms"]
	#How many bees can fit in a bee bomb. Default is 10
	#Range: 1 ~ 50
	numberOfBeesPerBomb = 10
	#The distance a nest locator can search for nests.
	#Range: 0 ~ 1000
	nestLocatorDistance = 100
	#Initial tick cooldown when repopulating a nest.
	#Range: > 0
	nestSpawnCooldown = 24000
	#Centrifuges will pick up items thrown on it
	centrifugeHopperMode = true
	#Having a lot of bees (or bee cages in an inventory) in a single chunk can overload the chunk with data. A lot of data is already stripped from the bees as they are saved, but this will also remove all Forge capabilities, which is data added to the bees by other mods. Turn off to keep the data.
	stripForgeCaps = false
	#Enable this if you have a right click harvest handler but none of the following mods: right_click_get_crops, croptopia, quark, harvest, simplefarming, reap
	forceEnableFarmerBeeRightClickHarvest = false

[Bees]
	#Allow for bee simulation in hives. This will stop bees from exiting the hive and instead simulate a trip to flower blocks saving on performance.
	allowBeeSimulation = true
	#Range: 0.0 ~ 1.0
	spawnUndeadBeesChance = 0.05
	#Range: 0.0 ~ 1.0
	deadBeeConvertChance = 0.03
	#Range: 0.0 ~ 1.0
	sugarbagBeeChance = 0.02
	#How many animals a CuBee can breed per pollination
	#Range: > 0
	cupidBeeAnimalsPerPollination = 5
	#How densely populated should an areas need to be for the CuBee to stop breeding. The value approximates how many animals can be in a 10x10 area around the bee.
	#Range: > 0
	cupidBeeAnimalDensity = 20
	#How many cuckoo bees can spawn from a nest before it shuts off
	#Range: > 0
	cuckooSpawnCount = 2
	#Chance to spawn a KamikazBee when hit while wearing bee nest armor
	#Range: 0.0 ~ 1.0
	kamikazBeeChance = 0.3
	#Disable the wander goal in bees to increase performance
	disableWanderGoal = false
	#Allow resin bees to encase mobs in amber. With this disabled it's only possible with an amber bee and it's also not as fun.
	enableResinBeeEncasing = true

["Bee attributes"]
	#Number of ticks between effects on nearby entities
	#Range: > 20
	ticks = 2337
	#Chance that a bee will take damage while leashed in a hostile environment
	#Range: 0.0 ~ 1.0
	damageChance = 0.1
	#Chance to increase tolerance (rain or thunder tolerance trait) while leashed in a hostile environment.
	#Range: 0.0 ~ 1.0
	toleranceChance = 0.1
	#Chance to increase behavior (nocturnal trait) while leashed in a hostile environment.
	#Range: 0.0 ~ 1.0
	behaviorChance = 0.1
	#Chance to extract genes from a bottle of bee material.
	#Range: 0.0 ~ 1.0
	geneExtractChance = 1.0
	#Average purity of type genes (does not apply to attribute genes)
	#Range: 1 ~ 100
	typeGenePurity = 33

[Worldgen]
	#Probability for a nest to generate in the world given it's conditions. Nest will still be craftable and attract bees when placed in the world.
	#Range: 0.0 ~ 1.0
	stone_nest = 0.1
	#Range: 0.0 ~ 1.0
	coarse_dirt_nest = 0.6
	#Range: 0.0 ~ 1.0
	sand_nest = 0.1
	#Range: 0.0 ~ 1.0
	snow_nest = 0.1
	#Range: 0.0 ~ 1.0
	gravel_nest = 0.15
	#Range: 0.0 ~ 1.0
	sugar_cane_nest = 0.4
	#Range: 0.0 ~ 1.0
	slimy_nest = 0.1
	#Range: 0.0 ~ 1.0
	glowstone_nest = 0.9
	#Range: 0.0 ~ 1.0
	soul_sand_nest = 0.1
	#Range: 0.0 ~ 1.0
	nether_quartz_nest = 0.2
	#Range: 0.0 ~ 1.0
	nether_brick_nest = 0.9
	#Range: 0.0 ~ 1.0
	end_stone_nest = 0.15
	#Range: 0.0 ~ 1.0
	obsidian_nest = 1.0
	#Range: 0.0 ~ 1.0
	bumble_bee_nest = 0.02
	#Range: 0.0 ~ 1.0
	oak_wood_nest = 0.15
	#Range: 0.0 ~ 1.0
	spruce_wood_nest = 0.2
	#Range: 0.0 ~ 1.0
	dark_oak_wood_nest = 0.2
	#Range: 0.0 ~ 1.0
	birch_wood_nest = 0.2
	#Range: 0.0 ~ 1.0
	jungle_wood_nest = 0.1
	#Range: 0.0 ~ 1.0
	acacia_wood_nest = 0.2
	#Range: 0.0 ~ 1.0
	nether_bee_nest = 0.02
	#Range: 0.0 ~ 1.0
	sugarbag_nest = 0.02
	#Chance for a nest to spawn when growing a tree or fungus.
	#Range: 0.0 ~ 1.0
	treeGrowNestChance = 0.02

["Hive Upgrades"]
	#Time bonus gained from time upgrade. 0.2 means 20% reduction of a bee's time inside the hive or centrifuge processing time.
	#Range: 0.0 ~ 1.0
	timeBonus = 0.2
	#Multiplier per productivity alpha upgrade installed in the hive.
	#Range: 1.0 ~ 2.147483647E9
	productivityMultiplier = 1.2
	#Multiplier per productivity beta upgrade installed in the hive.
	#Range: 1.0 ~ 2.147483647E9
	productivityMultiplier2 = 1.5
	#Multiplier per productivity gamma upgrade installed in the hive.
	#Range: 1.0 ~ 2.147483647E9
	productivityMultiplier3 = 2.0
	#Multiplier per productivity omega upgrade installed in the hive.
	#Range: 1.0 ~ 2.147483647E9
	productivityMultiplier4 = 2.6
	#Chance for a bee to produce an offspring after a hive visit.
	#Range: 0.0 ~ 1.0
	breedingChance = 0.05
	#How many bees can be around a hive before a babee upgrade stops working.
	#Range: > 0
	breedingMaxNearbyEntities = 10
	#Chance for a gene sample to be taken from a bee after a hive visit.
	#Range: 0.0 ~ 1.0
	samplerChance = 0.05

