#Maximum horizontal distance to look for crystals
#Range: 1 ~ 16
maxHorizontalCrystalDistance = 10
#Maximum vertical distance to look for crystals
#Range: 1 ~ 16
maxVerticalCrystalDistance = 1
#Startup time before the generator is active
#Range: 20 ~ 2000
startupTime = 70
#Shutdown time
#Range: 20 ~ 2000
shutdownTime = 70
#Maximum amount of power per generator block
#Range: 1000 ~ 100000
powerStoragePerBlock = 50000
#Output power per tick
#Range: 10 ~ 100000
powerPerTickOut = 20000
#Maximum number of crystals supported per block
#Range: 1 ~ 8
maxCrystalsPerBlock = 2
#Maximum power input per block
#Range: 100 ~ 50000
maxPowerInputPerBlock = 10000

[crystals]
	#The maximum kilo-RF (per 1000, so 1000 = 1milion RF) that a crystal with 100% power can hold
	#Range: 1 ~ 2000000000
	maximumStoredPower = 1000000
	#The maximum RF/tick that a crystal with 100% efficiency can give
	#Range: 1 ~ 20000
	maximumPowerTick = 20000

[plate]
	#Strength of radiation that a plate block gives when it has a redstone signal. 0 to disable
	#Range: 0 ~ 100000
	radiationStrength = 20000
	#Radius of radiation that a plate block gives when it has a redstone signal
	#Range: 8 ~ 128
	radiationRadius = 10
	#Amount of ticks that the radiation from a plate block lasts
	#Range: 20 ~ 72000
	radiationTicks = 100

[crystallizer]
	#How much power/t this machine can input from a generator/capacitor
	#Range: 0 ~ 1000
	powerPerTickIn = 200
	#How much power this machine consumes per tick while crystalizing
	#Range: 0 ~ 1000
	powerPerTick = 20
	#Maximum power that can be stored in this machine
	#Range: 0 ~ 100000
	powerMaximum = 10000
	#The amount of RCL that is needed for one crystal
	#Range: 100 ~ 80000
	rclPerCrystal = 6000
	#The amount of RCL/t that is consumed during crystalizing
	#Range: 1 ~ 100000
	rclPerTick = 1

[laser]
	#How much power/t this machine can input from a generator/capacitor
	#Range: > 0
	powerPerTickIn = 2000
	#Maximum power that can be stored in this machine
	#Range: > 0
	powerMaximum = 100000
	#The maximum amount of liquified crystal this machine can hold (this is not RCL!)
	#Range: > 100
	crystalLiquidMaximum = 20000
	#The minimum amount of liquified crystal one crystal will yield (this is not RCL!). This value is for a 0% strength crystal
	#Range: > 1
	minCrystalLiquidPerCrystal = 2000
	#The maximum amount of liquified crystal one crystal will yield (this is not RCL!). This value is for a 100% strength crystal
	#Range: > 1
	maxCrystalLiquidPerCrystal = 10000
	#How much RF this machine consumes for infusing one catalyst item
	#Range: > 0
	rfUsePerCatalyst = 4000
	#How many multiples of 10 ticks are needed to infuse one catalyst item
	#Range: 0 ~ 100000
	ticks10PerCatalyst = 4
	#The amount of crystal liquid we consume per catalyst item
	#Range: > 1
	crystalLiquidPerCatalyst = 25
	#The amount of crystal liquid we consume per catalyst item
	#Range: > 1
	rclPerCatalyst = 500

[purifier]
	#Amount of ticks needed to purify one unit of RCL
	#Range: 1 ~ 10000
	ticksPerPurify = 100
	#The amount of RCL we purify as one unit
	#Range: 1 ~ 10000
	rclPerPurify = 200
	#How much the purifier adds to the purity of a liquid (in %)
	#Range: 1 ~ 100
	addedPurity = 25
	#Maximum purity that the purifier can handle (in %)
	#Range: 1 ~ 100
	maxPurity = 85

[smelter]
	#How much power/t this machine can input from a generator/capacitor
	#Range: 0 ~ 1000
	powerPerTickIn = 200
	#How much power/t this machine consumes during smelting ores
	#Range: 0 ~ 1000
	powerPerOreTick = 10
	#Maximum power that can be stored in this machine
	#Range: 0 ~ 100000
	powerMaximum = 5000
	#The number of ticks to smelt one ore
	#Range: 10 ~ 1000
	processTime = 200
	#The amount of lava to smelt one ore
	#Range: 100 ~ 10000
	lavaCost = 200
	#The amount of RCL to produce with one ore
	#Range: 50 ~ 1000
	rclPerOre = 200

[valve]
	#The amount of ticks between a transfer operation
	#Range: 1 ~ 300
	ticksPerOperation = 5
	#The amount of RCL to transfer in one operation
	#Range: 100 ~ 10000
	rclPerOperation = 100

[explosion]
	#This factor increases the radius of radiation on explosion and decreases the strength
	#Range: 0.0 ~ 1000.0
	radiationExplosionFactor = 1.3
	#The minimum explosion multiplier
	#Range: 0.1 ~ 200.0
	minimumExplosionMultiplier = 6.0
	#The maximum explosion multiplier for a 100%/100% power/strength crystal
	#Range: 0.1 ~ 200.0
	maximumExplosionMultiplier = 17.0
	#The maximum explosion multiplier that is possible. Set to 0 to disable all explosions
	#Range: 0.1 ~ 200.0
	absoluteMaximumExplosionMultiplier = 20.0

[radiation]
	#The minimum radiation radius
	#Range: 3.0 ~ 16.0
	minRadiationRadius = 7.0
	#The maximum radiation radius for a 100/100/100 crystal
	#Range: 16.0 ~ 128.0
	maxRadiationRadius = 50.0
	#The minimum radiation strength
	#Range: 500.0 ~ 250000.0
	minRadiationStrength = 3000.0
	#The maximum radiation strength for a 100/100/100 crystal
	#Range: 100000.0 ~ 1000000.0
	maxRadiationStrength = 600000.0
	#Percentage of the maximum strength the radiation increases every tick
	#Range: 1.0E-4 ~ 0.1
	strengthGrowthFactor = 0.002
	#How much the radiation strength decreases every tick
	#Range: 0.1 ~ 50.0
	strengthDecreasePerTick = 3.0
	#The radiation strength at which point destruction events can happen
	#Range: 1000.0 ~ 1000000.0
	radiationDestructionEventLevel = 300000.0
	#Every 10 ticks (half a second) this chance is evaluated to see if there should be a destruction event. 1.0 means it will always occur
	#Range: 0.0025 ~ 0.1
	radiationDestructionEventChance = 0.02
	#Below this level no effects occur
	#Range: 100.0 ~ 10000.0
	radiationEffectLevelNone = 2000.0
	#Radiation strength level 0
	#Range: 1000.0 ~ 100000.0
	radiationEffectLevel0 = 20000.0
	#Radiation strength level 1
	#Range: 2500.0 ~ 250000.0
	radiationEffectLevel1 = 50000.0
	#Radiation strength level 2
	#Range: 5000.0 ~ 500000.0
	radiationEffectLevel2 = 100000.0
	#Radiation strength level 3
	#Range: 10000.0 ~ 1000000.0
	radiationEffectLevel3 = 200000.0
	#Radiation strength level 4
	#Range: 25000.0 ~ 2500000.0
	radiationEffectLevel4 = 500000.0
	#Radiation strength level 5
	#Range: 50000.0 ~ 5000000.0
	radiationEffectLevel5 = 1000000.0
	#The maximum that a radiation meter can measure
	#Range: 0.0 ~ 1.7976931348623157E308
	maxRadiationMeter = 200000.0
	#How much obsidian blocks radiation (0.0 is total block, 1.0 is not block at all)
	#Range: 0.0 ~ 3.4028234663852886E38
	radiationShieldObsidianFactor = 0.20000000298023224
	#How much dense obsidian blocks radiation (0.0 is total block, 1.0 is not block at all)
	#Range: 0.0 ~ 3.4028234663852886E38
	radiationShieldDenseObsidianFactor = 0.05000000074505806
	#How much dense glass blocks radiation (0.0 is total block, 1.0 is not block at all)
	#Range: 0.0 ~ 3.4028234663852886E38
	radiationShieldDenseGlassFactor = 0.10000000149011612
	#How much dense lead blocks radiation (0.0 is total block, 1.0 is not block at all)
	#Range: 0.0 ~ 3.4028234663852886E38
	radiationShieldLeadFactor = 0.10000000149011612
	#How much protection you get from radiation with 1 radiation suit piece equipped
	#Range: 0.0 ~ 3.4028234663852886E38
	suitProtection1 = 0.25
	#How much protection you get from radiation with 2 radiation suit piece equipped
	#Range: 0.0 ~ 3.4028234663852886E38
	suitProtection2 = 0.5
	#How much protection you get from radiation with 3 radiation suit piece equipped
	#Range: 0.0 ~ 3.4028234663852886E38
	suitProtection3 = 0.75
	#How much protection you get from radiation with 4 radiation suit piece equipped
	#Range: 0.0 ~ 3.4028234663852886E38
	suitProtection4 = 0.949999988079071

