
#General settings
[profiles]
	#Block to put underneath a bed so that it qualifies as a teleporter bed
	specialBedBlock = "minecraft:diamond_block"
	selectedProfile = ""
	selectedCustomJson = ""
	#The size of the todo queues for the lost city generator
	#Range: 1 ~ 100000
	todoQueueSize = 20
	#If this is true then saplings will grow into trees during generation. This is more expensive
	forceSaplingGrowth = true
	#List of structures to avoid when generating cities (for example to avoid generating a city in a woodland mansion)
	avoidStructures = ["minecraft:mansion", "minecraft:jungle_pyramid", "minecraft:desert_pyramid", "minecraft:igloo", "minecraft:swamp_huts", "minecraft:pillager_outpost"]
	#If true then also avoid generating the structures mentioned in 'avoidStructures' in chunks adjacent to the chunk with the structure
	avoidStructuresAdjacent = false
	#If true then also avoid generating cities in chunks adjacent to the chunks with villages
	avoidVillagesAdjacent = false
	#If true then avoid generating cities in chunks with villages
	avoidVillages = true
	#If true then avoid flattening the terrain around the city in case there was a structure that was avoided
	avoidFlattening = true

