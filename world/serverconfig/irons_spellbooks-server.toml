
#Other Configuration
[Misc]
	#rarityConfig array values must sum to 1: [COMMON, UNCOMMON, RARE, EPIC, LEGENDARY]. Default: [.3d, .25d, .2d, .15d, .1d]
	rarityConfig = [0.3, 0.25, 0.2, 0.15, 0.1]
	#Whether or not imbued weapons require mana to be casted. Default: true
	swordsConsumeMana = true
	#The multiplier on the cooldown of imbued weapons. Default: 0.5 (50% of default cooldown)
	swordsCooldownMultiplier = 0.5
	#Whether or not players can harm their own magic summons. Default: false
	canAttackOwnSummons = false
	#The maximum amount of times an applicable piece of equipment can be upgraded in the arcane anvil. Default: 3
	maxUpgrades = 3
	#From 0-1, the percent of max mana a player respawns with. Default: 0.0
	manaSpawnPercent = 0.0
	#From 0-1, the percent chance for scrolls to be successfully recycled. Default: 0.5 (50%)
	scrollRecycleChance = 0.5
	#Whether or not potions should be allowed to be brewed in the alchemist cauldron)
	allowCauldronBrewing = true
	#Whether or not Furled Map items should skip chunks when searching for structures (only find new structures). Can impact performance while searching. Default: true
	furledMapSkipsExistingChunks = true
	#Whether or not casting items should apply all attributes while in the offhand, or just magic related ones. Default: true
	applyAllMultihandAttributes = true
	#Whether or not creepers should be healed and become fire immune when struck by lightning. Default: true
	betterCreeperThunderHit = true
	#Whether or not spells such as Fireball or Fire Breath should destroy terrain or create fire. Default: false
	spellGriefing = false
	#Whether or not the wandering trader can have magic related trades, such as ink or scrolls. Default: true
	additionalWanderingTraderTrades = true
	#Whether casting spells should be disabled in adventure mode. Default: false
	disableAdventureModeCasting = false

["Upgrade Overrides"]
	#Use these lists to change what items can interact with the Arcane Anvil's upgrade system. This can also be done via datapack.
	#Upgrade Whitelist. Use an item's id to allow it to be upgraded, ex: "minecraft:iron_sword"
	upgradeWhitelist = []
	#Upgrade Blacklist. Use an item's id to prevent it from being upgraded, ex: "minecraft:iron_sword"
	upgradeBlacklist = []
	#Whether merging scrolls with ink to upgrade them in the Arcane Anvil is enabled.
	scrollMerging = true

["Imbue Overrides"]
	#Use these lists to change what items can interact with the Arcane Anvil's imbue system.
	#/!\ Unsupported item types are not guaranteed to work out of the box.
	#Imbue Whitelist. Use an item's id to allow it to be imbued, ex: "minecraft:iron_sword"
	imbueWhitelist = []
	#Imbue Blacklist. Use an item's id to prevent it from being imbued, ex: "minecraft:iron_sword"
	imbueBlacklist = []

[Worldgen]
	#The weight of the priest house spawning in a village. Default: 4
	priestHouseWeight = 4

#Individual Spell Configuration
[Spells]

	#irons_spellbooks:fire
	[Spells."irons_spellbooks:fireball"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 3
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "EPIC"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 25.0
		AllowCrafting = true

	[Spells."irons_spellbooks:wall_of_fire"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	[Spells."irons_spellbooks:fire_breath"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 12.0
		AllowCrafting = true

	[Spells."irons_spellbooks:magma_bomb"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 12.0
		AllowCrafting = true

	[Spells."irons_spellbooks:burning_dash"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 10.0
		AllowCrafting = true

	[Spells."irons_spellbooks:blaze_storm"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	[Spells."irons_spellbooks:firebolt"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 1.0
		AllowCrafting = true

	#irons_spellbooks:nature
	[Spells."irons_spellbooks:poison_arrow"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:earthquake"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 16.0
		AllowCrafting = true

	[Spells."irons_spellbooks:poison_splash"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	[Spells."irons_spellbooks:oakskin"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 35.0
		AllowCrafting = true

	[Spells."irons_spellbooks:spider_aspect"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 90.0
		AllowCrafting = true

	[Spells."irons_spellbooks:firefly_swarm"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	[Spells."irons_spellbooks:root"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 35.0
		AllowCrafting = true

	[Spells."irons_spellbooks:blight"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 35.0
		AllowCrafting = true

	[Spells."irons_spellbooks:acid_orb"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:poison_breath"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 12.0
		AllowCrafting = true

	[Spells."irons_spellbooks:fang_ward"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:fang_strike"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 5.0
		AllowCrafting = true

	[Spells."irons_spellbooks:chain_creeper"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 6
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:gust"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 12.0
		AllowCrafting = true

	[Spells."irons_spellbooks:invisibility"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 6
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 45.0
		AllowCrafting = true

	[Spells."irons_spellbooks:summon_vex"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 150.0
		AllowCrafting = true

	[Spells."irons_spellbooks:lob_creeper"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 2.0
		AllowCrafting = true

	[Spells."irons_spellbooks:shield"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 8.0
		AllowCrafting = true

	[Spells."irons_spellbooks:summon_horse"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	[Spells."irons_spellbooks:spectral_hammer"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 10.0
		AllowCrafting = true

	[Spells."irons_spellbooks:firecracker"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 1.5
		AllowCrafting = true

	#irons_spellbooks:ice
	[Spells."irons_spellbooks:frostbite"]
		Enabled = false
		School = "irons_spellbooks:ice"
		MaxLevel = 0
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 0.0
		AllowCrafting = true

	[Spells."irons_spellbooks:ice_block"]
		Enabled = true
		School = "irons_spellbooks:ice"
		MaxLevel = 6
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:ray_of_frost"]
		Enabled = true
		School = "irons_spellbooks:ice"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:cone_of_cold"]
		Enabled = true
		School = "irons_spellbooks:ice"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 12.0
		AllowCrafting = true

	[Spells."irons_spellbooks:frost_step"]
		Enabled = true
		School = "irons_spellbooks:ice"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 10.0
		AllowCrafting = true

	[Spells."irons_spellbooks:summon_polar_bear"]
		Enabled = true
		School = "irons_spellbooks:ice"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 180.0
		AllowCrafting = true

	[Spells."irons_spellbooks:icicle"]
		Enabled = true
		School = "irons_spellbooks:ice"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 1.0
		AllowCrafting = true

	[Spells."irons_spellbooks:abyssal_shroud"]
		Enabled = false
		School = "irons_spellbooks:ender"
		MaxLevel = 3
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "LEGENDARY"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 300.0
		AllowCrafting = true

	[Spells."irons_spellbooks:evasion"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "EPIC"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 180.0
		AllowCrafting = true

	[Spells."irons_spellbooks:magic_missile"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 1.0
		AllowCrafting = true

	[Spells."irons_spellbooks:summon_ender_chest"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 1
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 5.0
		AllowCrafting = true

	[Spells."irons_spellbooks:magic_arrow"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 8.0
		AllowCrafting = true

	[Spells."irons_spellbooks:teleport"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 3.0
		AllowCrafting = true

	[Spells."irons_spellbooks:dragon_breath"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 12.0
		AllowCrafting = true

	[Spells."irons_spellbooks:black_hole"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 6
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "LEGENDARY"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 120.0
		AllowCrafting = true

	[Spells."irons_spellbooks:counterspell"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 1
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:starfall"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 16.0
		AllowCrafting = true

	#irons_spellbooks:holy
	[Spells."irons_spellbooks:healing_circle"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 25.0
		AllowCrafting = true

	[Spells."irons_spellbooks:blessing_of_life"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 10.0
		AllowCrafting = true

	[Spells."irons_spellbooks:angel_wing"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "EPIC"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 120.0
		AllowCrafting = true

	[Spells."irons_spellbooks:sunbeam"]
		Enabled = false
		School = "irons_spellbooks:holy"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	[Spells."irons_spellbooks:fortify"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 35.0
		AllowCrafting = true

	[Spells."irons_spellbooks:guiding_bolt"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 8.0
		AllowCrafting = true

	[Spells."irons_spellbooks:heal"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 25.0
		AllowCrafting = true

	[Spells."irons_spellbooks:wisp"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 3.0
		AllowCrafting = true

	[Spells."irons_spellbooks:greater_heal"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 1
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 45.0
		AllowCrafting = true

	[Spells."irons_spellbooks:cloud_of_regeneration"]
		Enabled = false
		School = "irons_spellbooks:holy"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 35.0
		AllowCrafting = true

	#irons_spellbooks:blood
	[Spells."irons_spellbooks:raise_dead"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 6
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 150.0
		AllowCrafting = true

	[Spells."irons_spellbooks:blood_slash"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 10.0
		AllowCrafting = true

	[Spells."irons_spellbooks:blood_step"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 5.0
		AllowCrafting = true

	[Spells."irons_spellbooks:acupuncture"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	[Spells."irons_spellbooks:ray_of_siphoning"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:blood_needles"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 10.0
		AllowCrafting = true

	[Spells."irons_spellbooks:heartstop"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 120.0
		AllowCrafting = true

	[Spells."irons_spellbooks:devour"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	[Spells."irons_spellbooks:wither_skull"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 1.0
		AllowCrafting = true

	[Spells."irons_spellbooks:ascension"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:lightning_lance"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 8.0
		AllowCrafting = true

	[Spells."irons_spellbooks:electrocute"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 12.0
		AllowCrafting = true

	[Spells."irons_spellbooks:lightning_bolt"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "EPIC"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 25.0
		AllowCrafting = true

	[Spells."irons_spellbooks:charge"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 3
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 40.0
		AllowCrafting = true

	[Spells."irons_spellbooks:chain_lightning"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	#irons_spellbooks:eldritch
	[Spells."irons_spellbooks:sculk_tentacles"]
		Enabled = true
		School = "irons_spellbooks:eldritch"
		MaxLevel = 4
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "LEGENDARY"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 30.0
		AllowCrafting = true

	[Spells."irons_spellbooks:telekinesis"]
		Enabled = true
		School = "irons_spellbooks:eldritch"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "LEGENDARY"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 35.0
		AllowCrafting = true

	[Spells."irons_spellbooks:planar_sight"]
		Enabled = true
		School = "irons_spellbooks:eldritch"
		MaxLevel = 3
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "LEGENDARY"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 200.0
		AllowCrafting = true

	[Spells."irons_spellbooks:eldritch_blast"]
		Enabled = true
		School = "irons_spellbooks:eldritch"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "LEGENDARY"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:sonic_boom"]
		Enabled = true
		School = "irons_spellbooks:eldritch"
		MaxLevel = 3
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "LEGENDARY"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 25.0
		AllowCrafting = true

	[Spells."irons_spellbooks:divine_smite"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:sacrifice"]
		Enabled = true
		School = "irons_spellbooks:blood"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 20.0
		AllowCrafting = true

	#irons_spellbooks:lightning
	[Spells."irons_spellbooks:thunder_step"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 8.0
		AllowCrafting = true

	[Spells."irons_spellbooks:heat_surge"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 45.0
		AllowCrafting = true

	[Spells."irons_spellbooks:scorch"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 12.0
		AllowCrafting = true

	[Spells."irons_spellbooks:flaming_barrage"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:flaming_strike"]
		Enabled = true
		School = "irons_spellbooks:fire"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:stomp"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 16.0
		AllowCrafting = true

	[Spells."irons_spellbooks:gluttony"]
		Enabled = true
		School = "irons_spellbooks:nature"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 90.0
		AllowCrafting = true

	[Spells."irons_spellbooks:frostwave"]
		Enabled = true
		School = "irons_spellbooks:ice"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 45.0
		AllowCrafting = true

	#irons_spellbooks:evocation
	[Spells."irons_spellbooks:arrow_volley"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 6
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 15.0
		AllowCrafting = true

	[Spells."irons_spellbooks:wololo"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 1
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "LEGENDARY"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 10.0
		AllowCrafting = true

	[Spells."irons_spellbooks:slow"]
		Enabled = true
		School = "irons_spellbooks:evocation"
		MaxLevel = 4
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "EPIC"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 80.0
		AllowCrafting = true

	#irons_spellbooks:ender
	[Spells."irons_spellbooks:portal"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 3
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 180.0
		AllowCrafting = true

	[Spells."irons_spellbooks:recall"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 1
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "UNCOMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 300.0
		AllowCrafting = true

	[Spells."irons_spellbooks:echoing_strikes"]
		Enabled = true
		School = "irons_spellbooks:ender"
		MaxLevel = 5
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 60.0
		AllowCrafting = true

	[Spells."irons_spellbooks:cleanse"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 1
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "EPIC"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 60.0
		AllowCrafting = true

	[Spells."irons_spellbooks:haste"]
		Enabled = true
		School = "irons_spellbooks:holy"
		MaxLevel = 4
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "EPIC"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 80.0
		AllowCrafting = true

	[Spells."irons_spellbooks:thunderstorm"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "RARE"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 120.0
		AllowCrafting = true

	[Spells."irons_spellbooks:shockwave"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 8
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 30.0
		AllowCrafting = true

	[Spells."irons_spellbooks:ball_lightning"]
		Enabled = true
		School = "irons_spellbooks:lightning"
		MaxLevel = 10
		#Allowed Values: COMMON, UNCOMMON, RARE, EPIC, LEGENDARY
		MinRarity = "COMMON"
		ManaCostMultiplier = 1.0
		SpellPowerMultiplier = 1.0
		CooldownInSeconds = 1.0
		AllowCrafting = true

