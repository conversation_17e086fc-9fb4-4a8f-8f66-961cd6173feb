#No-fly zone checks happen every x player ticks, which can be taxing on the system when there's many players. Increase this number for better performance.
#Range: > 1
checkInterval = 10
#Whether to allow flight using an elytra in a no flight zone
allowElytraFlight = false
#Whether to allow flight using a flying device like a jetpack in a no flight zone
allowFlyingDevices = false
#Allow player teleportation in a no flight zone
allowTeleporting = true
#For performance reasons biome checks are off by default. Set it to true to disallow biomes listed in the noflyzone:worldgen/biome/blacklist tag.
enableBiomeCheck = false
#For performance reasons structure checks are off by default. Set it to true to disallow structures listed in the noflyzone:worldgen/structure/blacklist tag.
enableStructureCheck = false
#A list of blacklisted dimensions.
dimensions = ["allthemodium:the_other", "blue_skies:everbright", "blue_skies:everdawn", "twilightforest:twilight_forest"]
#Give the player slow fall when stopping flight.
enableSlowFall = true
#Punish repeat offenders with lightning.
enablePunishOffenders = false
#This number represents how many failed flight attempts a player can make within 1000 ticks (50 seconds) before getting zapped.
#Range: > 1
zapInterval = 30
#A list of game stage configurations unlocking flight in a dimension. The format is "checkType,check,game_stage" ex: "dimension,minecraft:the_nether,nether_unlocked" or "biome,minecraft:plains,stage_name"
dimension_unlocks = []
#Enable iron jetpacks compatibility.
enableIronJetpacksCompat = true
#Enable create jetpack compatibility.
enableCreateJetpackCompat = true
#Enable mekanism compatibility.
enableMekanismCompat = true
#Enable ad astra compatibility.
enableAdAstraCompat = true
#Enable pneumaticraft compatibility.
enablePneumaticraftCompat = true

