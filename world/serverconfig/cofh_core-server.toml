
[Commands]
	#The required permission level for the '/cofh crafting' command.
	#Range: 0 ~ 4
	"Crafting Permission Level" = 2
	#The required permission level for the '/cofh enderchest' command.
	#Range: 0 ~ 4
	"EnderChest Permission Level" = 2
	#The required permission level for the '/cofh heal' command.
	#Range: 0 ~ 4
	"Heal Permission Level" = 2
	#The required permission level for the '/cofh ignite' command.
	#Range: 0 ~ 4
	"Ignite Permission Level" = 2
	#The required permission level for the '/cofh repair' command.
	#Range: 0 ~ 4
	"Repair Permission Level" = 2

[Enchantments]
	#If TRUE, Feather Falling will prevent Farmland from being trampled. This option will work with alternative versions (overrides) of Feather Falling.
	"Improved Feather Falling" = true
	#If TRUE, Mending behavior is altered so that Experience Orbs always repair items if possible, and the most damaged item is prioritized. This option may not work with alternative versions (overrides) of Mending.
	"Improved Mending" = true

	[Enchantments.Holding]
		#If TRUE, the Holding Enchantment is available for various Storage Items and Blocks.
		Enable = true
		#This sets whether or not the Enchantment is considered a 'treasure' enchantment.
		Treasure = false
		#This option adjusts the maximum allowable level for the Enchantment.
		#Range: 1 ~ 10
		"Max Level" = 4

