
#Server config settings
[server]

	[server.backpackSettings]
		#Enables equipping the backpack on right-click from the ground
		rightClickEquip = true
		#Enables unequipping the backpack on right-click on the ground with empty hand
		rightClickUnequip = false
		#Allows to use only equipped backpack
		allowOnlyEquippedBackpack = false
		#Allows opening the backpack by pressing a keybind while hovering over the slot with backpack in the player's inventory
		allowOpeningFromSlot = false
		#Backpack immune to any damage source (lava, fire), can't be destroyed, never disappears as floating item
		invulnerableBackpack = true
		#Tool slots accept any item
		toolSlotsAcceptEverything = false
		#List of items that can be put in tool slots (Use registry names, for example: "minecraft:apple", "minecraft:flint")
		toolSlotsAcceptableItems = []
		#List of items that can't be put in backpack inventory (Use registry names, for example: "minecraft:apple", "minecraft:flint")
		blacklistedItems = []
		#Allows putting shulker boxes and other items with inventory in backpack
		allowShulkerBoxes = false
		#Prevents backpack disappearing in void, spawns floating backpack above minimum Y when player dies in void
		voidProtection = true
		#Places backpack at place where player died
		backpackDeathPlace = true
		#Places backpack at place where player died, replacing all blocks that are breakable and do not have inventory (backpackDeathPlace must be true in order to work)
		backpackForceDeathPlace = false
		#Allows sleeping in a sleeping bag without the need to unequip and place the backpack on the ground
		quickSleepingBag = true
		enableSleepingBagSpawnPoint = false
		#Backpacks can only be equipped in the Curios/Accessories 'Back' slot, provided those mods are installed. If set to false, backpacks can only be equipped by clicking the button in the Backpack GUI. This setting can be changed without unequipping the backpack. An already equipped backpack will not disappear and can be retrieved from the player's inventory.
		backSlotIntegration = true

		#Leather Tier Backpack Settings
		[server.backpackSettings.leatherTierBackpack]
			#Number of inventory slots for the tier
			#Range: 1 ~ 154
			inventorySlotCount = 27
			#Number of upgrade slots for the tier
			#Range: 0 ~ 10
			upgradeSlotCount = 2
			#Number of tool slots for the tier
			#Range: 0 ~ 8
			toolSlotCount = 2
			#Tank capacity per row of backpack storage, 1000 equals 1 Bucket (Leather backpack 3 rows of 9 slots = 3 * 1000
			#Range: 1 ~ 100000
			tankCapacity = 1000

		#Iron Tier Backpack Settings
		[server.backpackSettings.ironTierBackpack]
			#Number of inventory slots for the tier
			#Range: 1 ~ 154
			inventorySlotCount = 45
			#Number of upgrade slots for the tier
			#Range: 0 ~ 10
			upgradeSlotCount = 3
			#Number of tool slots for the tier
			#Range: 0 ~ 8
			toolSlotCount = 3
			#Tank capacity per row of backpack storage, 1000 equals 1 Bucket (Leather backpack 3 rows of 9 slots = 3 * 1000
			#Range: 1 ~ 100000
			tankCapacity = 1000

		#Gold Tier Backpack Settings
		[server.backpackSettings.goldTierBackpack]
			#Number of inventory slots for the tier
			#Range: 1 ~ 154
			inventorySlotCount = 63
			#Number of upgrade slots for the tier
			#Range: 0 ~ 10
			upgradeSlotCount = 4
			#Number of tool slots for the tier
			#Range: 0 ~ 8
			toolSlotCount = 4
			#Tank capacity per row of backpack storage, 1000 equals 1 Bucket (Leather backpack 3 rows of 9 slots = 3 * 1000
			#Range: 1 ~ 100000
			tankCapacity = 1000

		#Diamond Tier Backpack Settings
		[server.backpackSettings.diamondTierBackpack]
			#Number of inventory slots for the tier
			#Range: 1 ~ 154
			inventorySlotCount = 81
			#Number of upgrade slots for the tier
			#Range: 0 ~ 10
			upgradeSlotCount = 5
			#Number of tool slots for the tier
			#Range: 0 ~ 8
			toolSlotCount = 5
			#Tank capacity per row of backpack storage, 1000 equals 1 Bucket (Leather backpack 3 rows of 9 slots = 3 * 1000
			#Range: 1 ~ 100000
			tankCapacity = 1000

		#Netherite Tier Backpack Settings
		[server.backpackSettings.netheriteTierBackpack]
			#Number of inventory slots for the tier
			#Range: 1 ~ 154
			inventorySlotCount = 99
			#Number of upgrade slots for the tier
			#Range: 0 ~ 10
			upgradeSlotCount = 6
			#Number of tool slots for the tier
			#Range: 0 ~ 8
			toolSlotCount = 6
			#Tank capacity per row of backpack storage, 1000 equals 1 Bucket (Leather backpack 3 rows of 9 slots = 3 * 1000
			#Range: 1 ~ 100000
			tankCapacity = 1000

	[server.backpackUpgrades]
		enableTanksUpgrade = true
		enableCraftingUpgrade = true
		enableFurnaceUpgrade = true
		enableSmokerUpgrade = true
		enableBlastFurnaceUpgrade = true
		enableJukeboxUpgrade = true

		[server.backpackUpgrades.pickupUpgradeSettings]
			enablePickupUpgrade = true
			#Range: 1 ~ 9
			filterSlotCount = 9

		[server.backpackUpgrades.magnetUpgradeSettings]
			enableMagnetUpgrade = true
			#Range: 1 ~ 9
			filterSlotCount = 9
			#Range: 1 ~ 20
			pullRange = 5
			#Range: 1 ~ 1000
			tickRate = 10

		[server.backpackUpgrades.feedingUpgradeSettings]
			enableFeedingUpgrade = true
			#Range: 1 ~ 9
			filterSlotCount = 9
			#Range: 1 ~ 1000
			tickRate = 100

		[server.backpackUpgrades.voidUpgradeSettings]
			enableVoidUpgrade = true
			#Range: 1 ~ 9
			filterSlotCount = 9

	[server.world]
		#Enables chance to spawn Zombie, Skeleton, Wither Skeleton, Piglin or Enderman with random backpack equipped
		spawnEntitiesWithBackpack = true
		#Defines spawn chance of entity with a backpack
		#Range: 0.0 ~ 1.0
		chance = 0.005
		#List of overworld entity types that can spawn with equipped backpack. DO NOT ADD anything to this list, because the game will crash, remove entries if mob should not spawn with backpack
		possibleOverworldEntityTypes = ["minecraft:zombie", "minecraft:skeleton", "minecraft:enderman"]
		#List of nether entity types that can spawn with equipped backpack. DO NOT ADD anything to this list, because the game will crash, remove entries if mob should not spawn with backpack
		possibleNetherEntityTypes = ["minecraft:wither_skeleton", "minecraft:piglin"]
		#List of backpacks that can spawn on overworld mobs
		overworldBackpacks = ["travelersbackpack:standard", "travelersbackpack:diamond", "travelersbackpack:gold", "travelersbackpack:emerald", "travelersbackpack:iron", "travelersbackpack:lapis", "travelersbackpack:redstone", "travelersbackpack:coal", "travelersbackpack:bookshelf", "travelersbackpack:sandstone", "travelersbackpack:snow", "travelersbackpack:sponge", "travelersbackpack:cake", "travelersbackpack:cactus", "travelersbackpack:hay", "travelersbackpack:melon", "travelersbackpack:pumpkin", "travelersbackpack:creeper", "travelersbackpack:enderman", "travelersbackpack:skeleton", "travelersbackpack:spider", "travelersbackpack:bee", "travelersbackpack:wolf", "travelersbackpack:fox", "travelersbackpack:ocelot", "travelersbackpack:horse", "travelersbackpack:cow", "travelersbackpack:pig", "travelersbackpack:sheep", "travelersbackpack:chicken", "travelersbackpack:squid"]
		#List of backpacks that can spawn on nether mobs
		netherBackpacks = ["travelersbackpack:quartz", "travelersbackpack:nether", "travelersbackpack:blaze", "travelersbackpack:ghast", "travelersbackpack:magma_cube", "travelersbackpack:wither"]

	[server.backpackAbilities]
		enableBackpackAbilities = true
		#Newly crafted backpacks will have ability enabled by default
		forceAbilityEnabled = true
		#List of backpacks that are allowed to have an ability. DO NOT ADD anything to this list, because the game will crash, remove entries if backpack should not have ability
		allowedAbilities = ["travelersbackpack:netherite", "travelersbackpack:diamond", "travelersbackpack:gold", "travelersbackpack:emerald", "travelersbackpack:iron", "travelersbackpack:lapis", "travelersbackpack:redstone", "travelersbackpack:bookshelf", "travelersbackpack:sponge", "travelersbackpack:cake", "travelersbackpack:cactus", "travelersbackpack:melon", "travelersbackpack:pumpkin", "travelersbackpack:creeper", "travelersbackpack:dragon", "travelersbackpack:enderman", "travelersbackpack:blaze", "travelersbackpack:ghast", "travelersbackpack:magma_cube", "travelersbackpack:spider", "travelersbackpack:wither", "travelersbackpack:warden", "travelersbackpack:bat", "travelersbackpack:bee", "travelersbackpack:ocelot", "travelersbackpack:cow", "travelersbackpack:chicken", "travelersbackpack:squid", "travelersbackpack:hay", "travelersbackpack:fox"]
		#List of effect abilities associated with backpacks, you can modify this list as you wish. Different effects can be added to different backpacks. 
		# Formatting: "<backpack_registry_name>, <status_effect_registry_name>, <min_duration_ticks>, <max_duration_ticks>, <amplifier>"
		backpackEffects = ["travelersbackpack:bat, minecraft:night_vision, 260, 300, 0", "travelersbackpack:magma_cube, minecraft:fire_resistance, 260, 300, 0", "travelersbackpack:squid, minecraft:water_breathing, 260, 300, 0", "travelersbackpack:dragon, minecraft:regeneration, 260, 300, 0", "travelersbackpack:dragon, minecraft:strength, 250, 290, 0", "travelersbackpack:quartz, minecraft:haste, 260, 300, 0", "travelersbackpack:fox, minecraft:jump_boost, 260, 300, 0"]
		#List of cooldowns that are being applied after ability usage, the backpacks on the list are all that currently have cooldowns, adding additional backpack will not give it cooldown. 
		# Formatting: "<backpack_registry_name>, <min_possible_cooldown_seconds>, <max_possible_cooldown_seconds>"
		cooldowns = ["travelersbackpack:creeper, 1200, 1800", "travelersbackpack:cow, 480, 540", "travelersbackpack:chicken, 360, 600", "travelersbackpack:cake, 360, 480", "travelersbackpack:melon, 120, 480"]

	[server.slownessDebuff]
		#Player gets slowness effect, if carries too many backpacks in inventory
		tooManyBackpacksSlowness = false
		#Maximum number of backpacks, which can be carried in inventory, without slowness effect
		#Range: 1 ~ 37
		maxNumberOfBackpacks = 3
		#Range: 0.1 ~ 5.0
		slownessPerExcessedBackpack = 1.0

