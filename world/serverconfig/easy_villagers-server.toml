
[breeder]
	#The time in ticks the breeder takes to create a new villager
	#Range: > 20
	breeding_time = 1200

[converter]
	#The time in ticks the converter takes to convert a villager
	#Range: > 20
	converting_time = 6000

[farmer]
	#The chance that a crop grows a stage in a farmer
	#Lower values mean faster growth
	#Range: > 1
	farm_speed = 10
	#The crops that the farmer will not use
	#Entries starting with '#' are treated as tags
	crop_blacklist = ["#easy_villagers:invalid_farmer_crop"]

[iron_farm]
	#The time in ticks the iron farm takes to spawn a golem
	#Range: > 201
	spawn_time = 4800

[trader]
	#The minimum amount of time in ticks the trader takes to restock
	#Range: > 1
	min_restock_time = 1200
	#The maximum amount of time in ticks the trader takes to restock
	#Range: > 2
	max_restock_time = 3600

[auto_trader]
	#The minimum amount of time in ticks the auto trader takes to restock
	#Range: > 1
	min_restock_time = 1200
	#The maximum amount of time in ticks the auto trader takes to restock
	#Range: > 2
	max_restock_time = 3600
	#The cooldown in ticks for the auto trader to do a trade
	#Range: > 1
	trade_cooldown = 20

[villager]
	#If villagers should make sounds while in the players inventory
	inventory_sounds = true
	#How frequent a villager block should make a villager sound
	#Lower values mean more frequent sounds
	#Range: > 1
	sound_amount = 20
	#If the trade cycling button should be enabled
	trade_cycling = true
	#If the villager reputation should be the same for every player
	#This affects the prices of cured/converted villagers and the prices of the auto trader
	universal_reputation = true

[incubator]
	#The speed at which the incubator ages the villagers
	#Range: 1 ~ 1024
	speed = 2

