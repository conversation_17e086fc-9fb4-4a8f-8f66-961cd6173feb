{"undergarden:undergarden/root": {"criteria": {"tick": "2025-07-23 10:41:30 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-23 10:41:30 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-23 10:41:30 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-23 10:41:30 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-23 10:41:30 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-23 10:41:30 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-23 10:41:30 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-23 10:41:30 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-23 10:41:30 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-23 10:41:30 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-23 10:41:30 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-23 10:41:30 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-23 10:41:30 +0800"}, "done": false}, "lootr:root": {"criteria": {"always_true": "2025-07-23 10:41:30 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-23 10:41:30 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-23 10:41:34 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:bamboo_jungle": "2025-07-23 10:44:09 +0800", "minecraft:beach": "2025-07-23 10:41:34 +0800", "minecraft:desert": "2025-07-23 16:17:32 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/all_biomes": {"criteria": {"floodplain": "2025-07-23 10:44:13 +0800", "lush_savanna": "2025-07-23 16:17:36 +0800", "rocky_rainforest": "2025-07-23 10:43:40 +0800", "glowing_grotto": "2025-07-23 16:21:09 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/root": {"criteria": {"rocky_rainforest": "2025-07-23 10:43:40 +0800"}, "done": true}, "maidensmerrymaking:easter/root": {"criteria": {"requirement": "2025-07-23 10:43:41 +0800"}, "done": true}, "twilightforest:root": {"criteria": {"in_tf": "2025-07-23 10:51:46 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-23 11:21:48 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-23 11:21:48 +0800", "has_carrot": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-23 11:21:48 +0800", "has_axe": "2025-07-23 11:21:48 +0800", "has_helmet": "2025-07-23 11:21:48 +0800", "has_fishing_rod": "2025-07-23 11:21:48 +0800", "has_bow": "2025-07-23 11:21:48 +0800", "has_chestplate": "2025-07-23 11:21:48 +0800", "has_sword": "2025-07-23 11:21:48 +0800", "has_fillet_knife": "2025-07-23 11:21:48 +0800", "has_shovel": "2025-07-23 11:21:48 +0800", "has_hoe": "2025-07-23 11:21:48 +0800", "has_boots": "2025-07-23 11:21:48 +0800", "has_pickaxe": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-23 11:21:48 +0800", "has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-23 11:21:48 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-23 11:21:48 +0800"}, "done": true}, "createoreexcavation:recipes/misc/vein_atlas": {"criteria": {"map": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "supplementaries:recipes/slice_map": {"criteria": {"has_map": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-23 11:21:48 +0800", "has_fillet_knife": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-23 11:21:48 +0800", "has_wpt": "2025-07-23 11:21:48 +0800", "has_wct": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-23 11:21:48 +0800", "has_fillet_knife": "2025-07-23 11:21:48 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-23 11:21:48 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "botania:recipes/tools/speed_up_belt": {"criteria": {"has_item": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-23 11:21:48 +0800", "has_fillet_knife": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-23 11:21:48 +0800", "has_wct": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-23 11:21:48 +0800", "has_wct": "2025-07-23 11:21:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-23 11:21:48 +0800", "has_wpt": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-23 11:21:48 +0800", "has_fillet_knife": "2025-07-23 11:21:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-23 11:21:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-23 11:21:48 +0800"}, "done": true}, "supplementaries:recipes/soap/map": {"criteria": {"has_filled_map": "2025-07-23 11:25:32 +0800"}, "done": true}, "pylons:recipes/misc/interdiction_pylon": {"criteria": {"has_netherite_block": "2025-07-23 13:29:53 +0800"}, "done": true}, "minecraft:recipes/misc/netherite_ingot_from_netherite_block": {"criteria": {"has_netherite_block": "2025-07-23 13:29:53 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/netherite_block_1x": {"criteria": {"has_netherite_block": "2025-07-23 13:29:53 +0800"}, "done": true}, "minecraft:nether/create_full_beacon": {"criteria": {"beacon": "2025-07-23 13:39:15 +0800"}, "done": true}, "minecraft:nether/create_beacon": {"criteria": {"beacon": "2025-07-23 13:39:15 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-07-23 13:40:34 +0800"}, "done": true}, "securitycraft:recipes/misc/sc_manual": {"criteria": {"has_wood": "2025-07-23 13:40:34 +0800"}, "done": true}, "minecraft:recipes/building_blocks/spruce_planks": {"criteria": {"has_logs": "2025-07-23 13:40:34 +0800"}, "done": true}, "naturescompass:natures_compass": {"criteria": {"has_recipe": "2025-07-23 13:40:34 +0800"}, "done": true}, "naturescompass:natures_compass_log": {"criteria": {"has_log": "2025-07-23 13:40:34 +0800"}, "done": true}, "utilitarian:recipes/food/utility/charcoal_from_campfire": {"criteria": {"has_logs": "2025-07-23 13:40:34 +0800"}, "done": true}, "railcraft:recipes/coke_oven/charcoal": {"criteria": {"has_logs": "2025-07-23 13:40:34 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/architectscutter": {"criteria": {"has_log": "2025-07-23 13:40:34 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/logs_to_bowls": {"criteria": {"has_log": "2025-07-23 13:40:34 +0800"}, "done": true}, "enderio:recipes/misc/stick": {"criteria": {"has_ingredient": "2025-07-23 13:40:34 +0800"}, "done": true}, "naturescompass:natures_compass_sapling": {"criteria": {"has_recipe": "2025-07-23 13:40:34 +0800"}, "done": true}, "alchemistry:recipes/dissolver/charcoal": {"criteria": {"has_the_recipe": "2025-07-23 13:40:34 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_boat": {"criteria": {"in_water": "2025-07-23 13:41:32 +0800"}, "done": true}, "minecraft:recipes/decorations/blast_furnace": {"criteria": {"has_smooth_stone": "2025-07-23 13:46:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/smooth_stone_chain": {"criteria": {"recipe_condition": "2025-07-23 13:46:53 +0800"}, "done": true}, "alchemistry:recipes/dissolver/smooth_stone_slab": {"criteria": {"has_the_recipe": "2025-07-23 13:46:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/smooth_stone_slab": {"criteria": {"has_smooth_stone": "2025-07-23 13:46:53 +0800"}, "done": true}, "eidolon:recipes/building_blocks/smooth_stone_masonry_stonecutter_0": {"criteria": {"has_journal": "2025-07-23 13:46:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/smooth_stone_slab_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-23 13:46:53 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_bricks_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-23 13:46:53 +0800"}, "done": true}, "utilitix:recipes/misc/advanced_brewery": {"criteria": {"criterion1": "2025-07-23 13:46:53 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_bricks": {"criteria": {"has_smooth_stone": "2025-07-23 13:46:53 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_brick_stairs_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-23 13:46:53 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_brick_slab_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-23 13:46:53 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_brick_wall_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-23 13:46:53 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/grass_block_1x": {"criteria": {"has_grass_block": "2025-07-23 13:48:26 +0800"}, "done": true}, "tombstone:adventure/cancel_ghostly_shape": {"criteria": {"coded_trigger": "2025-07-23 13:48:26 +0800"}, "done": true}, "ae2:recipes/misc/decorative/quartz_fixture": {"criteria": {"has_charged_certus_quartz_crystal": "2025-07-23 17:26:05 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:progress_merge": {"criteria": {"snow_queen": "2025-07-23 16:19:39 +0800"}, "done": false}, "ae2:recipes/misc/tools/fluix_sword": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "ae2:recipes/misc/network/upgrade_wireless_crafting_terminal": {"criteria": {"has_terminal": "2025-07-23 17:25:01 +0800"}, "done": true}, "ae2:recipes/misc/misc/fluixpearl": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-23 16:53:28 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/wood/mangrove_slab": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/mangrove_sign": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "handcrafted:recipes/misc/bear_trophy": {"criteria": {"has_bear_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "botania:recipes/tools/flower_bag": {"criteria": {"has_item": "2025-07-23 16:47:36 +0800"}, "done": true}, "twilightforest:recipes/misc/lich_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:37 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_spade": {"criteria": {"has_certus_quartz": "2025-07-23 17:26:03 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "lootr:1barrel": {"criteria": {"opened_barrel": "2025-07-23 16:18:25 +0800"}, "done": true}, "handcrafted:recipes/misc/goat_trophy": {"criteria": {"has_goat_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/misc/minoshroom_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:37 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/energy_energy_acceptor": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/twilight_oak_door": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/yellow_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "twilightforest:recipes/redstone/reappearing_block": {"criteria": {"has_item": "2025-07-23 16:19:35 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/wood/canopy_slab": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_onside": {"criteria": {"has_planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/decorative/quartz_block": {"criteria": {"has_certus_quartz_crystal": "2025-07-23 17:26:03 +0800"}, "done": true}, "supplementaries:recipes/bamboo_spikes": {"criteria": {"minecraft:wooden_slabs": "2025-07-23 16:19:32 +0800"}, "done": true}, "cfm:recipes/decorations/white_picket_gate": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/twilight_oak_button": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/parts/monitors_conversion": {"criteria": {"has_storage_monitor": "2025-07-23 17:20:16 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/view_cell_storage": {"criteria": {"has_terminal": "2025-07-23 17:17:01 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "mcwpaths:recipes/soil": {"criteria": {"has_wood": "2025-07-23 16:38:43 +0800"}, "done": true}, "supplementaries:recipes/notice_board": {"criteria": {"minecraft:planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/view_cell": {"criteria": {"has_terminal": "2025-07-23 17:17:01 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_picket_fence": {"criteria": {"has_concrete": "2025-07-23 16:34:32 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/black_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_standing": {"criteria": {"has_planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/orange_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "handcrafted:recipes/misc/skeleton_horse_trophy": {"criteria": {"has_skeleton_horse_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_pickaxe": {"criteria": {"has_certus_quartz": "2025-07-23 17:26:03 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/canopy_sign": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:progress_glacier": {"criteria": {"previous_progression": "2025-07-23 16:19:39 +0800", "trophy": "2025-07-23 16:19:39 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/mangrove_button": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "ae2:recipes/misc/network/parts/terminals_crafting": {"criteria": {"has_terminal": "2025-07-23 17:17:01 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/amber_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/decorative/fluix_block": {"criteria": {"has_fluix_crystal": "2025-07-23 17:27:49 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/green_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/storage_chest": {"criteria": {"has_terminal": "2025-07-23 17:17:01 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/glass_fluix": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "botania:recipes/decorations/pink_shiny_flower": {"criteria": {"has_item": "2025-07-23 17:12:32 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/peach_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "lootr:1chest": {"criteria": {"opened_chest": "2025-07-23 16:18:20 +0800"}, "done": true}, "twilightforest:progress_trophy_pedestal": {"criteria": {"kill_lich": "2025-07-23 16:19:37 +0800"}, "done": false}, "supplementaries:recipes/flower_box": {"criteria": {"minecraft:wooden_slabs": "2025-07-23 16:19:32 +0800"}, "done": true}, "supplementaries:recipes/speaker_block": {"criteria": {"minecraft:planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "handcrafted:recipes/misc/fox_trophy": {"criteria": {"has_fox_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/twilight_oak_trapdoor": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:progress_ur_ghast": {"criteria": {"trophy": "2025-07-23 16:19:38 +0800"}, "done": false}, "cfm:recipes/decorations/pink_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/canopy_bookshelf": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_cutting_knife": {"criteria": {"has_certus_quartz": "2025-07-23 17:26:03 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "handcrafted:recipes/misc/pufferfish_trophy": {"criteria": {"has_pufferfish_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/misc/quest_ram_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:39 +0800"}, "done": true}, "supplementaries:recipes/jar": {"criteria": {"minecraft:wooden_slabs": "2025-07-23 16:19:32 +0800"}, "done": true}, "supplementaries:recipes/pulley": {"criteria": {"minecraft:planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_bowl": {"criteria": {"has_wood_bowl": "2025-07-23 16:19:32 +0800"}, "done": true}, "botania:main/flower_pickup": {"criteria": {"flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_plate": {"criteria": {"has_wood_plate": "2025-07-23 16:19:32 +0800"}, "done": true}, "mcwroofs:recipes/grass": {"criteria": {"has_planks": "2025-07-23 16:38:43 +0800"}, "done": true}, "twilightforest:recipes/redstone/vanishing_block": {"criteria": {"has_item": "2025-07-23 16:19:35 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/magenta_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "twilightforest:recipes/misc/ur_ghast_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:38 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/bubblegum_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "botania:recipes/misc/petal_pink": {"criteria": {"has_item": "2025-07-23 17:12:32 +0800"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/brown_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "supplementaries:recipes/decorations/twilightforest/sign_post_mangrove": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "twilightforest:progress_lich": {"criteria": {"trophy": "2025-07-23 16:19:37 +0800", "kill_naga": "2025-07-23 16:19:37 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:main/network1": {"criteria": {"cable": "2025-07-23 17:30:59 +0800"}, "done": true}, "delightful:recipes/knives/certus_quartz_knife": {"criteria": {"has_gems/certus_quartz": "2025-07-23 17:26:03 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/conifer_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/wood/twilight_oak_stairs": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/energy_energy_acceptor_alt": {"criteria": {"has_cable_energy_acceptor": "2025-07-23 17:20:30 +0800"}, "done": true}, "expatternprovider:recipes/misc/crystal_fixer": {"criteria": {"has_item": "2025-07-23 17:27:49 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/tools/fluix_axe": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "twilightdelight:recipes/decorations/mining_cabinet": {"criteria": {"has_mining_slab": "2025-07-23 16:19:33 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/persimmon_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/certus_quartz_dust_from_gem": {"criteria": {"has_certus_quartz_gem": "2025-07-23 17:26:03 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_barrel": {"criteria": {"has _plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/twilight_oak_sign": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/canopy_door": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/wood/canopy_planks": {"criteria": {"has_item": "2025-07-23 16:19:34 +0800"}, "done": true}, "megacells:recipes/misc/network/mega_interface": {"criteria": {"has_interface": "2025-07-23 17:20:26 +0800"}, "done": true}, "ae2:main/controller": {"criteria": {"certus": "2025-07-23 17:25:20 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/ultramarine_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "botania:recipes/decorations/black_shiny_flower": {"criteria": {"has_item": "2025-07-23 16:47:36 +0800"}, "done": true}, "ae2:recipes/misc/misc/tiny_tnt": {"criteria": {"has_dusts/quartz": "2025-07-23 17:26:04 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_picket_gate": {"criteria": {"has_concrete": "2025-07-23 16:34:32 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_sword": {"criteria": {"has_certus_quartz": "2025-07-23 17:26:03 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:main/charged_quartz": {"criteria": {"certus": "2025-07-23 17:26:05 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/mint_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "lootr:social": {"criteria": {"opened_barrel": "2025-07-23 16:18:25 +0800", "opened_chest": "2025-07-23 16:18:20 +0800"}, "done": false}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/mangrove_door": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/fluorescent_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stick": {"criteria": {"has_the_recipe": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/parts/terminals_pattern_access": {"criteria": {"has_pattern_provider": "2025-07-23 17:20:24 +0800"}, "done": true}, "allthetweaks:recipes/misc/atm_star_from_atmstar_block": {"criteria": {"has_atm_star_block": "2025-07-23 16:30:59 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "botania:recipes/misc/petal_purple": {"criteria": {"has_item": "2025-07-23 16:51:17 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "supplementaries:recipes/item_shelf": {"criteria": {"minecraft:wooden_slabs": "2025-07-23 16:19:32 +0800"}, "done": true}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/energy_vibration_chamber": {"criteria": {"has_energy_acceptor": "2025-07-23 17:27:38 +0800"}, "done": true}, "botania:recipes/misc/petal_black": {"criteria": {"has_item": "2025-07-23 16:47:36 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/cherenkov_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:progress_labyrinth": {"criteria": {"kill_lich": "2025-07-23 16:19:37 +0800"}, "done": false}, "twilightforest:recipes/building_blocks/wood/twilight_oak_slab": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:main/fluix": {"criteria": {"certus": "2025-07-23 17:27:49 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/tools/fluix_hoe": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/empty_canopy_bookshelf": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/crystal_processing_charger": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/canopy_gate": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/aquamarine_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "supplementaries:recipes/bellows": {"criteria": {"minecraft:wooden_slabs": "2025-07-23 16:19:32 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/wood/mangrove_stairs": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "ae2:main/pattern_encoding_terminal": {"criteria": {"certus": "2025-07-23 17:17:00 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_chest": {"criteria": {"has_lots_of_items": "2025-07-23 16:53:28 +0800"}, "done": true}, "twilightforest:progress_yeti": {"criteria": {"previous_progression": "2025-07-23 16:19:37 +0800", "trophy": "2025-07-23 16:19:39 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/inscribers": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/twilight_oak_plate": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "supplementaries:recipes/decorations/twilightforest/sign_post_twilight_oak": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "botania:challenge/root": {"criteria": {"flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/mangrove_gate": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "ae2:recipes/misc/tools/fluix_upgrade_smithing_template": {"criteria": {"has_fluix_crystal": "2025-07-23 17:27:49 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "framedblocks:recipes/building_blocks/framed_cube": {"criteria": {"hasPlanks": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/parts/energy_acceptor": {"criteria": {"has_energy_acceptor": "2025-07-23 17:27:38 +0800"}, "done": true}, "botania:recipes/decorations/purple_shiny_flower": {"criteria": {"has_item": "2025-07-23 16:51:17 +0800"}, "done": true}, "supplementaries:recipes/turn_table": {"criteria": {"minecraft:planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:progress_naga": {"criteria": {"trophy": "2025-07-23 16:19:37 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/lime_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/canopy_plate": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/misc/naga_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:37 +0800"}, "done": true}, "handcrafted:recipes/misc/blaze_trophy": {"criteria": {"has_blaze_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "handcrafted:recipes/misc/wolf_trophy": {"criteria": {"has_wolf_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:main/charger": {"criteria": {"certus": "2025-07-23 17:26:19 +0800"}, "done": true}, "botania:main/root": {"criteria": {"flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "ae2:recipes/misc/tools/misctools_charged_staff": {"criteria": {"has_charged_certus_quartz_crystal": "2025-07-23 17:26:05 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/purple_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/wood/time_planks": {"criteria": {"has_item": "2025-07-23 16:19:34 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "supplementaries:recipes/blackboard": {"criteria": {"minecraft:wooden_slabs": "2025-07-23 16:19:32 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/red_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/smart_fluix_clean": {"criteria": {"has_smart_cable": "2025-07-23 17:30:02 +0800"}, "done": true}, "utilitarian:recipes/misc/snad/drit": {"criteria": {"has_dirt": "2025-07-23 16:38:43 +0800"}, "done": true}, "aether:enter_aether": {"criteria": {"enter_aether": "2025-07-23 16:21:24 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/canopy_trapdoor": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/blasting/silicon_from_certus_quartz_dust": {"criteria": {"has_certus_quartz_dust": "2025-07-23 17:26:04 +0800"}, "done": true}, "ae2:recipes/misc/network/wireless_terminal": {"criteria": {"has_terminal": "2025-07-23 17:17:01 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/mangrove_plate": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/misc/hydra_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:38 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "handcrafted:recipes/misc/silverfish_trophy": {"criteria": {"has_silverfish_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_chest": {"criteria": {"has _plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "botania:main/flower_pickup_lexicon": {"criteria": {"flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/icy_blue_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "supplementaries:recipes/clock_block": {"criteria": {"minecraft:planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/spatial_io_pylon": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/canopy_button": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/parts/terminals_pattern_encoding": {"criteria": {"has_crafting_terminal": "2025-07-23 17:17:01 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/navy_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "silentgear:recipes/misc/upgrade_base": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:progress_hydra": {"criteria": {"trophy": "2025-07-23 16:19:38 +0800"}, "done": false}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/pink_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_wrench": {"criteria": {"has_certus_quartz": "2025-07-23 17:26:03 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_2": {"criteria": {"has _plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_3": {"criteria": {"has _plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_1": {"criteria": {"has _plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_4": {"criteria": {"has _plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/white_picket_fence": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "handcrafted:recipes/misc/tropical_fish_trophy": {"criteria": {"has_tropical_fish_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "aether:recipes/tools/wooden_axe_repairing": {"criteria": {"has_wooden_axe": "2025-07-23 16:38:55 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/atm_star_block_1x": {"criteria": {"has_atm_star_block": "2025-07-23 16:30:59 +0800"}, "done": true}, "twilightforest:recipes/misc/alpha_yeti_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:39 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/mining_banister": {"criteria": {"has_item": "2025-07-23 16:19:33 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_barrel": {"criteria": {"has_slabs": "2025-07-23 16:19:32 +0800"}, "done": true}, "twilightforest:arborist": {"criteria": {"thorn_leaves": "2025-07-23 16:19:34 +0800", "twilight_oak_planks": "2025-07-23 16:19:31 +0800", "mangrove_planks": "2025-07-23 16:19:32 +0800", "dark_hanging_sign": "2025-07-23 16:19:33 +0800", "empty_canopy_bookshelf": "2025-07-23 16:19:35 +0800", "mining_slab": "2025-07-23 16:19:33 +0800", "canopy_planks": "2025-07-23 16:19:31 +0800", "time_log": "2025-07-23 16:19:34 +0800", "hollow_transformation_log": "2025-07-23 16:19:34 +0800", "sorting_fence": "2025-07-23 16:19:32 +0800", "canopy_wood": "2025-07-23 16:19:34 +0800", "mangrove_fence": "2025-07-23 16:19:32 +0800", "transformation_banister": "2025-07-23 16:19:32 +0800", "canopy_slab": "2025-07-23 16:19:32 +0800"}, "done": false}, "twilightforest:recipes/redstone/wood/mangrove_trapdoor": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/canopy_fence": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/network/wireless_crafting_terminal": {"criteria": {"has_terminal": "2025-07-23 17:17:01 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/misc/snow_queen_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:39 +0800"}, "done": true}, "ae2:recipes/misc/network/parts/quartz_fiber_part": {"criteria": {"has_dusts/quartz": "2025-07-23 17:26:04 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/cyan_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:progress_knights": {"criteria": {"trophy": "2025-07-23 16:19:38 +0800"}, "done": false}, "cfm:recipes/decorations/mangrove_kitchen_sink_light": {"criteria": {"has_top": "2025-07-23 16:45:48 +0800"}, "done": true}, "megacells:recipes/misc/network/mega_pattern_provider": {"criteria": {"has_pattern_provider": "2025-07-23 17:20:24 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/white_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "supplementaries:recipes/decorations/twilightforest/sign_post_canopy": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/lavender_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/canopy_chest": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/maroon_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_cup": {"criteria": {"has_wood_cup": "2025-07-23 16:19:32 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_axe": {"criteria": {"has_certus_quartz": "2025-07-23 17:26:03 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/honey_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:main/crafting_terminal": {"criteria": {"certus": "2025-07-23 17:17:01 +0800"}, "done": true}, "minecraft:recipes/decorations/composter": {"criteria": {"has_wood_slab": "2025-07-23 16:19:32 +0800"}, "done": true}, "supplementaries:recipes/bed_from_feather_block": {"criteria": {"minecraft:planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/network/crafting/patterns_blank": {"criteria": {"has_pattern_encoding_terminal": "2025-07-23 17:17:00 +0800"}, "done": true}, "handcrafted:recipes/misc/spider_trophy": {"criteria": {"has_spider_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "ae2:recipes/misc/decorative/quartz_glass": {"criteria": {"has_quartz_dust": "2025-07-23 17:26:04 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/controller": {"criteria": {"has_purified_fluix_crystal": "2025-07-23 17:27:49 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/tools/fluix_pickaxe": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/wine_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "botania:recipes/misc/petal_light_blue_double": {"criteria": {"has_item": "2025-07-23 16:59:13 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/wood/canopy_stairs": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_drawer": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/rose_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/spring_green_bed": {"criteria": {"has_plank": "2025-07-23 16:19:31 +0800"}, "done": true}, "expatternprovider:recipes/misc/ex_drive": {"criteria": {"has_item": "2025-07-23 17:22:07 +0800"}, "done": true}, "handcrafted:recipes/misc/wither_skeleton_trophy": {"criteria": {"has_wither_skeleton_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/building_blocks/wood/time_wood": {"criteria": {"has_item": "2025-07-23 16:19:34 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/roan_bricks": {"criteria": {"has_item1_domum_ornamentum_roan_bricks": "2025-07-23 16:38:08 +0800", "has_item2_domum_ornamentum_roan_bricks": "2025-07-23 16:38:08 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "supplementaries:recipes/lock_block": {"criteria": {"minecraft:planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/twilight_oak_chest": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "supplementaries:recipes/cage": {"criteria": {"minecraft:wooden_slabs": "2025-07-23 16:19:32 +0800"}, "done": true}, "productivebees:husbandry/bee_cage/overworld_nest": {"criteria": {"get_item": "2025-07-23 16:21:01 +0800"}, "done": true}, "handcrafted:recipes/misc/salmon_trophy": {"criteria": {"has_salmon_trophy": "2025-07-23 16:19:31 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/dirt_1x": {"criteria": {"has_dirt": "2025-07-23 16:38:43 +0800"}, "done": true}, "twilightforest:recipes/redstone/wood/twilight_oak_gate": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "aether:the_aether": {"criteria": {"the_aether": "2025-07-23 16:21:24 +0800"}, "done": true}, "ae2:recipes/misc/tools/fluix_shovel": {"criteria": {"has_crystals/fluix": "2025-07-23 17:27:49 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/energy_energy_cell": {"criteria": {"has_crystals/certus": "2025-07-23 17:26:03 +0800"}, "done": true}, "advanced_ae:recipes/misc/throughput_monitor": {"criteria": {"hasItem": "2025-07-23 17:20:16 +0800"}, "done": true}, "twilightforest:recipes/misc/knight_phantom_banner_pattern": {"criteria": {"has_trophy": "2025-07-23 16:19:38 +0800"}, "done": true}, "sliceanddice:recipes/misc/slicer": {"criteria": {"has_tool": "2025-07-23 16:38:55 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/interfaces_interface_alt": {"criteria": {"has_cable_interface": "2025-07-23 17:20:26 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/pattern_providers_interface_alt": {"criteria": {"has_cable_pattern_provider": "2025-07-23 17:20:24 +0800"}, "done": true}, "twilightdelight:recipes/decorations/canopy_cabinet": {"criteria": {"has_canopy_slab": "2025-07-23 16:19:32 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_hoe": {"criteria": {"has_certus_quartz": "2025-07-23 17:26:03 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_sink": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "ae2:recipes/misc/network/parts/storage_bus": {"criteria": {"has_interface": "2025-07-23 17:20:26 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/mangrove_fence": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "ae2:recipes/misc/smelting/silicon_from_certus_quartz_dust": {"criteria": {"has_certus_quartz_dust": "2025-07-23 17:26:04 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/canopy_banister": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "ae2:recipes/misc/decorative/quartz_fixture_from_anchors": {"criteria": {"has_charged_certus_quartz_crystal": "2025-07-23 17:26:05 +0800"}, "done": true}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2025-07-23 16:18:00 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/wooden_blacksmith_gavel": {"criteria": {"has_planks": "2025-07-23 16:19:31 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-23 16:47:36 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/twilight_oak_fence": {"criteria": {"has_item": "2025-07-23 16:19:31 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/trough": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_counter": {"criteria": {"has_concrete": "2025-07-23 16:45:48 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/mangrove_chest": {"criteria": {"has_item": "2025-07-23 16:19:32 +0800"}, "done": true}, "DataVersion": 3465}