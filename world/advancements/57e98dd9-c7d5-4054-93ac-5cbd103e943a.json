{"mcwbridges:recipes/rope_jungle_bridge": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_jungle_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "dyenamics:recipes/misc/wine_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_magenta": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "constructionwand:recipes/tools/stone_wand": {"criteria": {"has_item": "2025-07-09 23:23:32 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_swamp_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "undergarden:undergarden/root": {"criteria": {"tick": "2025-07-09 23:15:10 +0800"}, "done": true}, "alchemistry:recipes/dissolver/pink_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "farmersdelight:recipes/food/fried_egg_from_campfire_cooking": {"criteria": {"fried_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-09 23:18:28 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-09 23:15:10 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_ranch_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/misc/red_dye_from_poppy": {"criteria": {"has_poppy": "2025-07-09 23:20:39 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-09 23:18:28 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-09 23:18:28 +0800", "has_carrot": "2025-07-09 23:18:27 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite_with_vanilla_cobblestone": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_trapdoors": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_counter": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_stairs": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "handcrafted:recipes/misc/bear_trophy": {"criteria": {"has_bear_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "utilitix:recipes/misc/armed_stand": {"criteria": {"criterion1": "2025-07-09 23:21:35 +0800"}, "done": true}, "utilitix:recipes/misc/crude_furnace": {"criteria": {"criterion1": "2025-07-09 23:23:32 +0800"}, "done": true}, "alchemistry:recipes/dissolver/gray_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "supplementaries:recipes/sign_post_jungle": {"criteria": {"has_the_recipe": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_trapdoor": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_mossy_cobblestone_from_vanilla_moss": {"criteria": {"has_moss": "2025-07-09 23:23:17 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/goat_trophy": {"criteria": {"has_goat_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/persimmon_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_crate": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dark_oak_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_boats": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-09 23:15:10 +0800"}, "done": true}, "alchemistry:recipes/dissolver/magenta_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/sand_stone_bricks": {"criteria": {"has_item1_domum_ornamentum_sand_stone_bricks": "2025-07-09 23:21:46 +0800", "has_item2_domum_ornamentum_sand_stone_bricks": "2025-07-09 23:21:46 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_dark_oak_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_oak_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_stairs": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_bamboo_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwfurnitures:recipes/jungle": {"criteria": {"has_wood": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-09 23:18:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/yellow_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "supplementaries:recipes/timber_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-09 23:18:28 +0800"}, "done": true}, "twigs:recipes/tables/jungle_table": {"criteria": {"has_jungle": "2025-07-09 23:21:13 +0800"}, "done": true}, "utilitix:recipes/misc/reinforced_rail": {"criteria": {"criterion2": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/combat/stone_sword": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "minecraft:recipes/decorations/cobblestone_wall": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "minecraft:recipes/tools/stone_axe": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_pressure_plates": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_table": {"criteria": {"has_jungle_table": "2025-07-09 23:21:13 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-09 23:18:27 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_onside": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear": {"criteria": {"has_ingredient": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "silentgear:root": {"criteria": {"get_item": "2025-07-09 23:21:35 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-09 23:18:28 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "aether:recipes/food/skyroot_milk_bucket_cake": {"criteria": {"has_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-09 23:15:10 +0800"}, "done": true}, "supplementaries:recipes/notice_board": {"criteria": {"minecraft:planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_cyan": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_whispering_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/stone_blacksmith_gavel": {"criteria": {"has_stone_tool_materials": "2025-07-09 23:23:32 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/black_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_standing": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "alchemistry:recipes/dissolver/orange_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/orange_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/skeleton_horse_trophy": {"criteria": {"has_skeleton_horse_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_counter": {"criteria": {"has_jungle_counter": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_stone_bricks_from_moss_block": {"criteria": {"has_moss_block": "2025-07-09 23:23:17 +0800"}, "done": true}, "alchemistry:recipes/dissolver/green_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwlights:recipes/torches": {"criteria": {"has_the_recipe": "2025-07-09 23:23:44 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:27 +0800"}, "done": true}, "securitycraft:recipes/misc/motion_activated_light": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_orange": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:story/mine_stone": {"criteria": {"get_stone": "2025-07-09 23:23:32 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_spruce_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "biomesoplenty:recipes/misc/orange_dye_from_burning_blossom": {"criteria": {"has_burning_blossom": "2025-07-09 23:18:28 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/sand_1x": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-09 23:18:27 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/amber_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwroofs:recipes/jungle": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "caupona:recipes/decorations/mud_kitchen_stove": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/green_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/maroon_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/tools/wooden_fillet_knife": {"criteria": {"has_items": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-09 23:18:28 +0800", "has_axe": "2025-07-09 23:18:28 +0800", "has_helmet": "2025-07-09 23:18:28 +0800", "has_fishing_rod": "2025-07-09 23:18:28 +0800", "has_bow": "2025-07-09 23:18:28 +0800", "has_chestplate": "2025-07-09 23:18:28 +0800", "has_sword": "2025-07-09 23:18:28 +0800", "has_fillet_knife": "2025-07-09 23:18:28 +0800", "has_shovel": "2025-07-09 23:18:28 +0800", "has_hoe": "2025-07-09 23:18:28 +0800", "has_boots": "2025-07-09 23:18:28 +0800", "has_pickaxe": "2025-07-09 23:18:28 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/peach_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_door": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_shovel": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-09 23:18:28 +0800", "has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_birch_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwlights:recipes/tiki_torches": {"criteria": {"has_the_recipe": "2025-07-09 23:23:44 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_cupboard": {"criteria": {"has_jungle_cupboard": "2025-07-09 23:21:13 +0800"}, "done": true}, "securitycraft:recipes/misc/sc_manual": {"criteria": {"has_wood": "2025-07-09 23:21:06 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-09 23:15:11 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/peach_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_slab": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/conifer_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_drawer": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "undergarden:recipes/decorations/torch_ditchbulb_paste": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/building_blocks/brown_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/redstone/laser_block": {"criteria": {"has_stone": "2025-07-09 23:23:32 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/zinc_dust_from_raw": {"criteria": {"has_raw_zinc": "2025-07-09 23:23:58 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/edelwood_ladder": {"criteria": {"has_rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "biomesoplenty:biomesoplenty/all_biomes": {"criteria": {"floodplain": "2025-07-09 23:19:29 +0800", "lush_savanna": "2025-07-09 23:20:38 +0800", "rocky_rainforest": "2025-07-09 23:17:13 +0800", "undergrowth": "2025-07-09 23:18:24 +0800"}, "done": false}, "supplementaries:recipes/speaker_block": {"criteria": {"minecraft:planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_counter": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/spring_green_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "handcrafted:recipes/misc/fox_trophy": {"criteria": {"has_fox_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "alchemistry:recipes/dissolver/light_blue_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_acacia_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/ultramarine_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_park_bench": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_table": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwbridges:recipes/jungle_bridge_pier": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "handcrafted:recipes/misc/pufferfish_trophy": {"criteria": {"has_pufferfish_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-09 23:18:28 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_gray": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "minecraft:recipes/decorations/moss_carpet": {"criteria": {"has_moss_block": "2025-07-09 23:23:17 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:bamboo_jungle": "2025-07-09 23:18:16 +0800", "minecraft:beach": "2025-07-09 23:15:11 +0800", "minecraft:desert": "2025-07-09 23:20:46 +0800"}, "done": false}, "supplementaries:recipes/pulley": {"criteria": {"minecraft:planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_stairs_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_desk": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/tools/stone_hoe": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "minecraft:story/root": {"criteria": {"crafting_table": "2025-07-09 23:21:35 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_lime": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "farmersdelight:recipes/food/fried_egg": {"criteria": {"fried_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "minecraft:recipes/decorations/ladder": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/magenta_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-09 23:18:27 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_axe": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/bubblegum_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_side_table": {"criteria": {"has_jungle_side_table": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "create:recipes/misc/smelting/zinc_ingot_from_raw_ore": {"criteria": {"has_item": "2025-07-09 23:23:58 +0800"}, "done": true}, "mcwwindows:recipes/curtain_rods": {"criteria": {"has_wood": "2025-07-09 23:21:35 +0800"}, "done": true}, "farmersdelight:recipes/cooking/baked_cod_stew": {"criteria": {"has_any_ingredient": "2025-07-09 23:18:42 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/brown_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lime_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "alchemistry:recipes/dissolver/brown_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_bedside_cabinet": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cyan_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "alchemistry:recipes/dissolver/white_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "minecraft:recipes/decorations/campfire": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/no_soliciting_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "biomesoplenty:biomesoplenty/root": {"criteria": {"rocky_rainforest": "2025-07-09 23:17:13 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/conifer_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/building_blocks/black_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_corner_trim": {"criteria": {"has_jungle_corner_trim": "2025-07-09 23:21:13 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_oak_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_purple": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/redstone/lever": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_gray": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwroofs:recipes/jungle_planks": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_blinds": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "supplementaries:recipes/checker": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "supplementaries:recipes/decorations/sign_post_jungle": {"criteria": {"has_item": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "supplementaries:recipes/pancake_fd": {"criteria": {"forge:eggs": "2025-07-09 23:18:42 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_bricks": {"criteria": {"has_item": "2025-07-09 23:23:32 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "supplementaries:recipes/daub_brace": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "croptopia:recipes/egg_roll": {"criteria": {"has_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/persimmon_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_barrel": {"criteria": {"has _plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "minecraft:recipes/building_blocks/yellow_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_3": {"criteria": {"has_jungle_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_2": {"criteria": {"has_jungle_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_1": {"criteria": {"has_jungle_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/grit_sand": {"criteria": {"has_gravel": "2025-07-09 23:21:46 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_4": {"criteria": {"has_jungle_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/sandstone": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "biomesoplenty:recipes/building_blocks/mossy_black_sand": {"criteria": {"has_moss_block": "2025-07-09 23:23:17 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_crate": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "aquaculture:recipes/tools/stone_fillet_knife": {"criteria": {"has_items": "2025-07-09 23:23:32 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/ultramarine_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwbridges:recipes/jungle_log_bridge_middle": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "supplementaries:recipes/crank": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_pillar_trim": {"criteria": {"has_jungle_pillar_trim": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-09 23:15:10 +0800"}, "done": true}, "naturescompass:natures_compass": {"criteria": {"has_recipe": "2025-07-09 23:21:06 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-09 23:18:28 +0800", "has_fillet_knife": "2025-07-09 23:18:28 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/jungle_planks_1x": {"criteria": {"has_jungle_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/mint_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_pink": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/tools/stone_pickaxe": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-09 23:15:10 +0800"}, "done": true}, "alchemistry:recipes/dissolver/lime_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "supplementaries:recipes/timber_frame": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_hoe": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "securitycraft:recipes/misc/sonic_security_system": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "naturescompass:natures_compass_log": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear_corner": {"criteria": {"has_ingredient": "2025-07-09 23:21:35 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/fluorescent_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stick": {"criteria": {"has_the_recipe": "2025-07-09 23:21:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_bedside_cabinet": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/lavender_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purple_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-09 23:18:28 +0800", "has_wpt": "2025-07-09 23:18:28 +0800", "has_wct": "2025-07-09 23:18:28 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/crank": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "farmersdelight:recipes/food/fried_egg_from_smoking": {"criteria": {"fried_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "minecraft:recipes/food/cake": {"criteria": {"has_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/cherenkov_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_coffee_table": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecolonies:recipes/misc/shapetool": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "undergarden:recipes/decorations/undergarden_scaffolding": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "utilitarian:recipes/food/utility/charcoal_from_campfire": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_jungle_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_desk_cabinet": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_chair": {"criteria": {"has_jungle_chair": "2025-07-09 23:21:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_light": {"criteria": {"has_bottom": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/aquamarine_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_button": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobblestone_chain": {"criteria": {"recipe_condition": "2025-07-09 23:23:32 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-09 23:18:28 +0800", "has_fillet_knife": "2025-07-09 23:18:28 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_stairs": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/green_cobblestone_extra": {"criteria": {"has_material": "2025-07-09 23:23:32 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-09 23:18:28 +0800"}, "done": true}, "alchemistry:recipes/dissolver/moss_carpet": {"criteria": {"has_the_recipe": "2025-07-09 23:23:17 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-09 23:15:10 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_mystic_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_fancy_bed": {"criteria": {"has_jungle_fancy_bed": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_paper_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gray_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "alltheores:recipes/misc/zinc_ingot_from_raw": {"criteria": {"has_item": "2025-07-09 23:23:58 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_slabs": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwroofs:recipes/concrete": {"criteria": {"has_planks": "2025-07-09 23:21:46 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-09 23:15:10 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/purple_cobblestone_extra": {"criteria": {"has_material": "2025-07-09 23:23:32 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/navy_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stone": {"criteria": {"has_the_recipe": "2025-07-09 23:23:32 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-09 23:15:10 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/bubblegum_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_stairs_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-09 23:23:32 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-09 23:15:10 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_table": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "utilitix:recipes/misc/directional_highspeed_rail": {"criteria": {"criterion1": "2025-07-09 23:21:35 +0800"}, "done": true}, "framedblocks:recipes/building_blocks/framed_cube": {"criteria": {"hasPlanks": "2025-07-09 23:21:13 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/mossy_cobblestone_bricks_from_moss": {"criteria": {"has_item": "2025-07-09 23:23:17 +0800"}, "done": true}, "enderio:recipes/misc/stone_gear": {"criteria": {"has_ingredient": "2025-07-09 23:23:32 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_dining_bench": {"criteria": {"has_jungle_dining_bench": "2025-07-09 23:21:13 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/decorations/jungle_sign": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "railcraft:recipes/coke_oven/charcoal": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "supplementaries:recipes/turn_table": {"criteria": {"minecraft:planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_slab": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "minecraft:recipes/combat/wooden_sword": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_green": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/lime_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwroofs:recipes/cobblestone": {"criteria": {"has_planks": "2025-07-09 23:23:32 +0800"}, "done": true}, "handcrafted:recipes/misc/blaze_trophy": {"criteria": {"has_blaze_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_white": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "handcrafted:recipes/misc/wolf_trophy": {"criteria": {"has_wolf_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_cabinet": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/building_blocks/pink_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "utilitix:recipes/misc/directional_rail": {"criteria": {"criterion1": "2025-07-09 23:21:35 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_chair": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/purple_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "undergarden:recipes/decorations/shard_torch": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_bricks_stonecutting": {"criteria": {"has_item": "2025-07-09 23:23:32 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_cherry_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/red_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_four_panel_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "alltheores:recipes/misc/zinc_ingot_from_raw_blasting": {"criteria": {"has_item": "2025-07-09 23:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_upgraded_fence": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/honey_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge_pier": {"criteria": {"has_planks": "2025-07-09 23:23:32 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_pickaxe": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "croptopia:recipes/misc/meringue": {"criteria": {"has_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "minecraft:story/upgrade_tools": {"criteria": {"stone_pickaxe": "2025-07-09 23:23:44 +0800"}, "done": true}, "maidensmerrymaking:easter/root": {"criteria": {"requirement": "2025-07-09 23:19:28 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_mangrove_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_crimson_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwtrpdoors:recipes/acacia/jungle_bark_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/building_blocks/red_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_wall_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-09 23:23:32 +0800"}, "done": true}, "minecraft:recipes/decorations/furnace": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/torch": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/green_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/cobblestone_1x": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "aether:recipes/tools/stone_pickaxe_repairing": {"criteria": {"has_stone_pickaxe": "2025-07-09 23:23:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite_with_vanilla_diorite": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/architectscutter": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_acacia_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/silverfish_trophy": {"criteria": {"has_silverfish_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/logs_to_bowls": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_chest": {"criteria": {"has _plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_nightstand": {"criteria": {"has_jungle_nightstand": "2025-07-09 23:21:13 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/cobblestone_extra": {"criteria": {"has_material": "2025-07-09 23:23:32 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/cherenkov_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/icy_blue_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/jungle_log_1x": {"criteria": {"has_jungle_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "supplementaries:recipes/clock_block": {"criteria": {"minecraft:planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "utilitix:recipes/misc/hand_bell": {"criteria": {"criterion0": "2025-07-09 23:21:35 +0800"}, "done": true}, "utilitix:recipes/misc/comparator_redirector_up": {"criteria": {"criterion1": "2025-07-09 23:23:32 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/navy_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/tools/stone_shovel": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_doors": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwwindows:recipes/jungle": {"criteria": {"has_wood": "2025-07-09 23:21:06 +0800"}, "done": true}, "silentgear:recipes/misc/upgrade_base": {"criteria": {"has_item": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/fluorescent_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_gray_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-09 23:15:10 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_blue_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/pink_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "enderio:recipes/misc/stick": {"criteria": {"has_ingredient": "2025-07-09 23:21:06 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_2": {"criteria": {"has _plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_3": {"criteria": {"has _plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_1": {"criteria": {"has _plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_4": {"criteria": {"has _plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_cobblestone_from_moss_block": {"criteria": {"has_moss_block": "2025-07-09 23:23:17 +0800"}, "done": true}, "handcrafted:recipes/misc/tropical_fish_trophy": {"criteria": {"has_tropical_fish_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "farmersdelight:recipes/cooking/fried_rice": {"criteria": {"has_any_ingredient": "2025-07-09 23:18:42 +0800"}, "done": true}, "dyenamics:recipes/misc/lavender_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_fence_gate": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/print_beach": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_blue": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge": {"criteria": {"has_planks": "2025-07-09 23:23:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_desk": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-09 23:18:28 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_barrel": {"criteria": {"has_jungle_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_warped_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_bench": {"criteria": {"has_jungle_bench": "2025-07-09 23:21:13 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-09 23:18:27 +0800"}, "done": true}, "minecraft:nether/root": {"criteria": {"entered_nether": "2025-07-09 23:18:23 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_mangrove_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "silentgear:nether": {"criteria": {"entered_nether": "2025-07-09 23:18:23 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_diorite": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "farmersdelight:recipes/decorations/cutting_board": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_blossom_trapdoor": {"criteria": {"has_plankss": "2025-07-09 23:21:13 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_spruce_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwlights:recipes/lantern": {"criteria": {"has_the_recipe": "2025-07-09 23:23:44 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_birch_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cobblestone_slab": {"criteria": {"has_the_recipe": "2025-07-09 23:23:32 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/amber_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "paraglider:root": {"criteria": {"crafting_table": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_wood": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-09 23:18:28 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-09 23:18:28 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_cabinet": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-09 23:18:28 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/moss_block_1x": {"criteria": {"has_moss_block": "2025-07-09 23:23:17 +0800"}, "done": true}, "utilitix:recipes/misc/weak_redstone_torch": {"criteria": {"criterion1": "2025-07-09 23:21:35 +0800"}, "done": true}, "supplementaries:recipes/daub_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/cyan_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_bamboo_fence_gate": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_tropical_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_glass_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/building_blocks/magenta_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/white_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwpaths:recipes/jungle": {"criteria": {"has_wood": "2025-07-09 23:21:13 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/crafting_card": {"criteria": {"crafter": "2025-07-09 23:21:35 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_drawer": {"criteria": {"has_jungle_drawer": "2025-07-09 23:21:13 +0800"}, "done": true}, "supplementaries:recipes/timber_brace": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/lavender_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "alchemistry:recipes/dissolver/sandstone": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "naturescompass:natures_compass_sapling": {"criteria": {"has_recipe": "2025-07-09 23:21:06 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/aquamarine_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "paraglider:recipes/misc/paraglider": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_desk_cabinet": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/decorations/jungle_fence": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_black": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barred_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/decorations/cobblestone_wall_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_shelf": {"criteria": {"has_jungle_shelf": "2025-07-09 23:21:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_park_bench": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cyan_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/maroon_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "travelersbackpack:recipes/misc/cake": {"criteria": {"has_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_crimson_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "twigs:recipes/bricks/mossy_bricks_from_moss_block": {"criteria": {"has_item": "2025-07-09 23:23:17 +0800"}, "done": true}, "create:recipes/building_blocks/jungle_window": {"criteria": {"has_ingredient": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/wine_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "alchemistry:recipes/dissolver/light_gray_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:27 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "undergarden:recipes/combat/slingshot": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "create:recipes/misc/blasting/zinc_ingot_from_raw_ore": {"criteria": {"has_item": "2025-07-09 23:23:58 +0800"}, "done": true}, "mcwdoors:recipes/jungle": {"criteria": {"has_wood": "2025-07-09 23:21:06 +0800"}, "done": true}, "supplementaries:recipes/faucet": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/honey_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:story/enter_the_nether": {"criteria": {"entered_nether": "2025-07-09 23:18:23 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:27 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-09 23:15:10 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "supplementaries:recipes/bed_from_feather_block": {"criteria": {"minecraft:planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-09 23:18:28 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_slab_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-09 23:23:32 +0800"}, "done": true}, "utilitix:recipes/misc/highspeed_rail": {"criteria": {"criterion1": "2025-07-09 23:21:35 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_warped_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_blue": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "handcrafted:recipes/misc/spider_trophy": {"criteria": {"has_spider_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "utilitarian:recipes/misc/snad/snad": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwbridges:recipes/jungle_rail_bridge": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/rose_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwlights:recipes/paper_lamp": {"criteria": {"has_the_recipe": "2025-07-09 23:23:44 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "alchemistry:recipes/dissolver/yellow_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "utilitix:recipes/misc/comparator_redirector_down": {"criteria": {"criterion1": "2025-07-09 23:23:32 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-09 23:18:28 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/wine_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "alchemistry:recipes/dissolver/blue_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barrel_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_cobblestone_bridge": {"criteria": {"has_planks": "2025-07-09 23:23:32 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_pressure_plate": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/icy_blue_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "enderio:recipes/misc/conduit_binder_composite": {"criteria": {"has_ingredient_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_red": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "mcwpaths:recipes/cobblestone": {"criteria": {"has_wood": "2025-07-09 23:23:32 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/rose_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/spring_green_bed": {"criteria": {"has_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/wither_skeleton_trophy": {"criteria": {"has_wither_skeleton_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "alchemistry:recipes/dissolver/black_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_dark": {"criteria": {"has_bottom": "2025-07-09 23:21:13 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-09 23:18:28 +0800"}, "done": true}, "supplementaries:recipes/lock_block": {"criteria": {"minecraft:planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blue_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "create:recipes/misc/crafting/appliances/crafting_blueprint": {"criteria": {"has_item": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-09 23:18:28 +0800", "has_fillet_knife": "2025-07-09 23:18:28 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_slab_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "alltheores:recipes/misc/raw_zinc_block": {"criteria": {"has_raw_TagKey[minecraft:item / forge:raw_materials/zinc]": "2025-07-09 23:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stone": {"criteria": {"has_cobblestone": "2025-07-09 23:23:32 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_upgraded_gate": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_chest": {"criteria": {"has_jungle_plank": "2025-07-09 23:21:13 +0800"}, "done": true}, "tombstone:adventure/cancel_ghostly_shape": {"criteria": {"coded_trigger": "2025-07-09 23:19:39 +0800"}, "done": true}, "croptopia:recipes/chilli_relleno": {"criteria": {"has_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "aether:recipes/tools/wooden_pickaxe_repairing": {"criteria": {"has_wooden_pickaxe": "2025-07-09 23:21:42 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-09 23:18:28 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "handcrafted:recipes/misc/salmon_trophy": {"criteria": {"has_salmon_trophy": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "alchemistry:recipes/dissolver/purple_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barn_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-09 23:15:10 +0800"}, "done": false}, "silentgear:recipes/misc/stone_rod": {"criteria": {"has_item": "2025-07-09 23:23:32 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-09 23:18:28 +0800"}, "done": true}, "minecraft:recipes/building_blocks/orange_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "twigs:recipes/silt/silt_from_sand": {"criteria": {"has_item": "2025-07-09 23:21:46 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-09 23:18:28 +0800", "has_wct": "2025-07-09 23:18:28 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-09 23:18:28 +0800", "has_wct": "2025-07-09 23:18:28 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-09 23:18:28 +0800", "has_wpt": "2025-07-09 23:18:28 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_drawer": {"criteria": {"has_log": "2025-07-09 23:21:06 +0800"}, "done": true}, "croptopia:recipes/scrambled_eggs": {"criteria": {"has_egg": "2025-07-09 23:18:42 +0800"}, "done": true}, "supplementaries:recipes/slingshot": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_mossy_stone_bricks_from_vanilla_moss": {"criteria": {"has_moss": "2025-07-09 23:23:17 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_chair": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-09 23:18:27 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/blue_cobblestone_extra": {"criteria": {"has_material": "2025-07-09 23:23:32 +0800"}, "done": true}, "silentgear:recipes/misc/rough_rod": {"criteria": {"has_item": "2025-07-09 23:21:35 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_coffee_table": {"criteria": {"has_planks": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_cottage_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_brown": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "blue_skies:recipes/food/cake_compat": {"criteria": {"has_item": "2025-07-09 23:18:42 +0800"}, "done": true}, "farmersdelight:recipes/combat/flint_knife": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/glass": {"criteria": {"has_smelts_to_glass": "2025-07-09 23:21:46 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "lootr:root": {"criteria": {"always_true": "2025-07-09 23:15:10 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-09 23:18:28 +0800"}, "done": true}, "alchemistry:recipes/dissolver/red_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-09 23:21:46 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_desk": {"criteria": {"has_jungle_desk": "2025-07-09 23:21:13 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-09 23:15:10 +0800"}, "done": true}, "minecraft:recipes/decorations/torch": {"criteria": {"has_stone_pickaxe": "2025-07-09 23:23:44 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/wooden_blacksmith_gavel": {"criteria": {"has_planks": "2025-07-09 23:21:13 +0800"}, "done": true}, "alchemistry:recipes/dissolver/charcoal": {"criteria": {"has_the_recipe": "2025-07-09 23:21:06 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 23:18:28 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_cherry_fence": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "supplementaries:recipes/daub_frame": {"criteria": {"forge:rods/wooden": "2025-07-09 23:21:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_planks": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_classic_trapdoor": {"criteria": {"has_logs": "2025-07-09 23:21:06 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_boat": {"criteria": {"in_water": "2025-07-09 23:19:36 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/mint_banner": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-09 23:18:28 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_yellow": {"criteria": {"has_stick": "2025-07-09 23:21:35 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_couch": {"criteria": {"has_jungle_couch": "2025-07-09 23:21:13 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge_stair": {"criteria": {"has_planks": "2025-07-09 23:23:32 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-09 23:18:28 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/sand_bricks": {"criteria": {"has_item2_domum_ornamentum_sand_bricks": "2025-07-09 23:21:46 +0800", "has_item1_domum_ornamentum_sand_bricks": "2025-07-09 23:21:46 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-09 23:18:28 +0800", "has_fillet_knife": "2025-07-09 23:18:28 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-09 23:18:28 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_concrete_powder": {"criteria": {"has_sand": "2025-07-09 23:21:46 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-09 23:18:28 +0800"}, "done": true}, "DataVersion": 3465}