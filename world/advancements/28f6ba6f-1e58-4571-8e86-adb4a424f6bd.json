{"dyenamics:recipes/misc/wine_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_swamp_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "undergarden:undergarden/root": {"criteria": {"tick": "2025-07-21 22:18:59 +0800"}, "done": true}, "alchemistry:recipes/dissolver/pink_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwwindows:recipes/dark_prismarine": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-21 22:18:59 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_ranch_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-21 22:21:38 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-21 22:21:38 +0800", "has_carrot": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_trapdoors": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_counter": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "alchemistry:recipes/dissolver/gray_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_beach_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_mossy_cobblestone_from_vanilla_moss": {"criteria": {"has_moss": "2025-07-21 22:26:58 +0800"}, "done": true}, "additionallanterns:recipes/misc/end_stone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_crate": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_boats": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-21 22:18:59 +0800"}, "done": true}, "alchemistry:recipes/dissolver/magenta_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/sand_stone_bricks": {"criteria": {"has_item1_domum_ornamentum_sand_stone_bricks": "2025-07-21 22:24:11 +0800", "has_item2_domum_ornamentum_sand_stone_bricks": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barred_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwfurnitures:recipes/jungle": {"criteria": {"has_wood": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-21 22:21:38 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_pressure_plates": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-21 22:21:38 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-21 22:18:59 +0800"}, "done": true}, "additionallanterns:recipes/misc/netherite_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/acacia/oak_bark_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barrel_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_whispering_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-21 22:21:38 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "additionallanterns:recipes/misc/bricks_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "additionallanterns:recipes/misc/smooth_stone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwwindows:recipes/bricks": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_trapdoors": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/orange_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_drawer": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_stone_bricks_from_moss_block": {"criteria": {"has_moss_block": "2025-07-21 22:26:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/green_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-21 22:21:38 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/sand_1x": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwroofs:recipes/jungle": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-21 22:21:38 +0800", "has_axe": "2025-07-21 22:21:38 +0800", "has_helmet": "2025-07-21 22:21:38 +0800", "has_fishing_rod": "2025-07-21 22:21:38 +0800", "has_bow": "2025-07-21 22:21:38 +0800", "has_chestplate": "2025-07-21 22:21:38 +0800", "has_sword": "2025-07-21 22:21:38 +0800", "has_fillet_knife": "2025-07-21 22:21:38 +0800", "has_shovel": "2025-07-21 22:21:38 +0800", "has_hoe": "2025-07-21 22:21:38 +0800", "has_boots": "2025-07-21 22:21:38 +0800", "has_pickaxe": "2025-07-21 22:21:38 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_pressure_plates": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/diamond_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-21 22:21:38 +0800", "has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barn_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwlights:recipes/tiki_torches": {"criteria": {"has_wood": "2025-07-21 22:28:00 +0800"}, "done": true}, "securitycraft:recipes/misc/sc_manual": {"criteria": {"has_wood": "2025-07-21 22:23:01 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-21 22:19:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_drawer": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "forbidden_arcanus:recipes/decorations/deorum_lantern": {"criteria": {"has_torch": "2025-07-21 22:28:00 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/brown_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_tropical_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/gold_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "biomesoplenty:biomesoplenty/all_biomes": {"criteria": {"lush_savanna": "2025-07-21 22:23:53 +0800", "rocky_rainforest": "2025-07-21 22:21:31 +0800"}, "done": false}, "additionallanterns:recipes/misc/bone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwwindows:recipes/sandstone": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_counter": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "alchemistry:recipes/dissolver/light_blue_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_park_bench": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_table": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwbridges:recipes/jungle_bridge_pier": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-21 22:21:38 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "twigs:recipes/stick_from_twig": {"criteria": {"has_item": "2025-07-21 22:27:48 +0800"}, "done": true}, "minecraft:recipes/decorations/moss_carpet": {"criteria": {"has_moss_block": "2025-07-21 22:26:58 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:bamboo_jungle": "2025-07-21 22:21:20 +0800", "minecraft:beach": "2025-07-21 22:19:03 +0800", "minecraft:desert": "2025-07-21 22:24:08 +0800"}, "done": false}, "cfm:recipes/decorations/jungle_desk": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwwindows:recipes/diorite": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/dark_prismarine_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-21 22:21:38 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-21 22:21:38 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "cfm:recipes/decorations/oak_desk_cabinet": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwwindows:recipes/curtain_rods": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "undergarden:recipes/decorations/gloom_o_lantern": {"criteria": {"has_torch": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_mystic_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lime_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "alchemistry:recipes/dissolver/brown_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "additionallanterns:recipes/misc/basalt_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_bedside_cabinet": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "cfm:recipes/decorations/oak_upgraded_fence": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cyan_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "aether:recipes/combat/iron_sword_repairing": {"criteria": {"has_iron_sword": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/white_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "biomesoplenty:biomesoplenty/root": {"criteria": {"rocky_rainforest": "2025-07-21 22:21:31 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/black_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwbridges:recipes/bridge_torch": {"criteria": {"has_planks": "2025-07-21 22:28:00 +0800"}, "done": true}, "additionallanterns:recipes/misc/iron_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_desk": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_table": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwwindows:recipes/oak": {"criteria": {"has_wood": "2025-07-21 22:23:01 +0800", "has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_blinds": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "cfm:recipes/decorations/oak_chair": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "mcwwindows:recipes/granite": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/deepslate_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/yellow_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/grit_sand": {"criteria": {"has_gravel": "2025-07-21 22:24:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/sandstone": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "cfm:recipes/decorations/oak_cabinet": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "biomesoplenty:recipes/building_blocks/mossy_black_sand": {"criteria": {"has_moss_block": "2025-07-21 22:26:58 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_crate": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_swamp_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-21 22:21:38 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-21 22:18:59 +0800"}, "done": true}, "naturescompass:natures_compass": {"criteria": {"has_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-21 22:21:38 +0800", "has_fillet_knife": "2025-07-21 22:21:38 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwwindows:recipes/birch": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-21 22:18:59 +0800"}, "done": true}, "alchemistry:recipes/dissolver/lime_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-21 22:21:38 +0800"}, "done": true}, "naturescompass:natures_compass_log": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_desk": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_bedside_cabinet": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/oak_log_1x": {"criteria": {"has_oak_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/obsidian_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwfurnitures:recipes/oak": {"criteria": {"has_wood": "2025-07-21 22:23:01 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purple_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_wood": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-21 22:21:38 +0800", "has_wpt": "2025-07-21 22:21:38 +0800", "has_wct": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_crate": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_coffee_table": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwwindows:recipes/blackstone": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "utilitarian:recipes/food/utility/charcoal_from_campfire": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_desk_cabinet": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-21 22:21:38 +0800"}, "done": true}, "supplementaries:recipes/sconce": {"criteria": {"has_torch": "2025-07-21 22:28:00 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwtrpdoors:recipes/print_classic": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_park_bench": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/quartz_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-21 22:21:38 +0800", "has_fillet_knife": "2025-07-21 22:21:38 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_stairs": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwroofs:recipes/oak": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/moss_carpet": {"criteria": {"has_the_recipe": "2025-07-21 22:26:58 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_ranch_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-21 22:18:59 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_mystic_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_paper_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwwindows:recipes/acacia": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gray_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_slabs": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwroofs:recipes/concrete": {"criteria": {"has_planks": "2025-07-21 22:24:11 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-21 22:18:59 +0800"}, "done": true}, "additionallanterns:recipes/misc/blackstone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/oak_coffee_table": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-21 22:18:59 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-21 22:18:59 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_table": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/mossy_cobblestone_bricks_from_moss": {"criteria": {"has_item": "2025-07-21 22:26:58 +0800"}, "done": true}, "mcwwindows:recipes/quartz": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "railcraft:recipes/coke_oven/charcoal": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwwindows:recipes/spruce": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobblestone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_cabinet": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_counter": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/pink_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_chair": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_four_panel_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_upgraded_fence": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwwindows:recipes/mangrove": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "mcwwindows:recipes/red_sandstone": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "maidensmerrymaking:easter/root": {"criteria": {"requirement": "2025-07-21 22:21:17 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_slabs": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwwindows:recipes/prismarine": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwwindows:recipes/metal": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/acacia/jungle_bark_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "minecraft:recipes/building_blocks/red_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwwindows:recipes/dark_oak": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/green_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "additionallanterns:recipes/misc/emerald_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwwindows:recipes/components": {"criteria": {"has_wood": "2025-07-21 22:23:01 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/architectscutter": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_glass_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwwindows:recipes/cherry": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/andesite_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/logs_to_bowls": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_whispering_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "additionallanterns:recipes/misc/diorite_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/jungle_log_1x": {"criteria": {"has_jungle_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_cottage_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_doors": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwwindows:recipes/deepslate": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_doors": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwwindows:recipes/jungle": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobbled_deepslate_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_gray_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "additionallanterns:recipes/misc/crimson_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-21 22:18:59 +0800"}, "done": true}, "cfm:recipes/decorations/oak_bedside_cabinet": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_blue_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_bedside_cabinet": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "enderio:recipes/misc/stick": {"criteria": {"has_ingredient": "2025-07-21 22:23:01 +0800"}, "done": true}, "twigs:recipes/lamps/lamp": {"criteria": {"has_item": "2025-07-21 22:28:00 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_cobblestone_from_moss_block": {"criteria": {"has_moss_block": "2025-07-21 22:26:58 +0800"}, "done": true}, "dyenamics:recipes/misc/lavender_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_coffee_table": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/print_beach": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_desk": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwwindows:recipes/andesite": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbridges:recipes/oak_bridge_pier": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_nether_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwdoors:recipes/oak": {"criteria": {"has_wood": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwlights:recipes/lantern": {"criteria": {"has_wood": "2025-07-21 22:28:00 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_wood": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-21 22:21:38 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_cabinet": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-21 22:21:38 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/moss_block_1x": {"criteria": {"has_moss_block": "2025-07-21 22:26:58 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_drawer": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_tropical_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_glass_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "minecraft:recipes/building_blocks/magenta_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/oak_blinds": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_stairs": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/sandstone": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "naturescompass:natures_compass_sapling": {"criteria": {"has_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_windows": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_desk_cabinet": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "cfm:recipes/decorations/oak_table": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_planks": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_boats": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barred_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_park_bench": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cyan_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "twigs:recipes/bricks/mossy_bricks_from_moss_block": {"criteria": {"has_item": "2025-07-21 22:26:58 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_counter": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/light_gray_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_iron_sword": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_desk_cabinet": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/mossy_cobblestone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwdoors:recipes/jungle": {"criteria": {"has_wood": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-21 22:18:59 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_cabinet": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-21 22:21:38 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_four_panel_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "utilitarian:recipes/misc/snad/snad": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwlights:recipes/paper_lamp": {"criteria": {"has_wood": "2025-07-21 22:28:00 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/yellow_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "cfm:recipes/decorations/oak_park_bench": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-21 22:21:38 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "alchemistry:recipes/dissolver/blue_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "additionallanterns:recipes/misc/copper_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barrel_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "enderio:recipes/misc/conduit_binder_composite": {"criteria": {"has_ingredient_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_sword": {"criteria": {"has_iron_sword": "2025-07-21 22:21:38 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/black_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwwindows:recipes/warped": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blue_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-21 22:21:38 +0800", "has_fillet_knife": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_paper_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/purpur_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/oak_crate": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "additionallanterns:recipes/misc/granite_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_upgraded_gate": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/purple_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barn_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-21 22:18:59 +0800"}, "done": false}, "additionallanterns:recipes/misc/red_nether_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/orange_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "twigs:recipes/silt/silt_from_sand": {"criteria": {"has_item": "2025-07-21 22:24:11 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-21 22:21:38 +0800", "has_wct": "2025-07-21 22:21:38 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-21 22:21:38 +0800", "has_wct": "2025-07-21 22:21:38 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-21 22:21:38 +0800", "has_wpt": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_drawer": {"criteria": {"has_log": "2025-07-21 22:28:16 +0800"}, "done": true}, "cfm:recipes/decorations/oak_upgraded_gate": {"criteria": {"has_log": "2025-07-21 22:23:01 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_mossy_stone_bricks_from_vanilla_moss": {"criteria": {"has_moss": "2025-07-21 22:26:58 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_chair": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwwindows:recipes/stone": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwwindows:recipes/crimson": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "additionallanterns:recipes/misc/warped_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "additionallanterns:recipes/misc/amethyst_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-21 22:21:38 +0800"}, "done": true}, "additionallanterns:recipes/misc/red_sandstone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_coffee_table": {"criteria": {"has_planks": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_cottage_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "minecraft:recipes/building_blocks/glass": {"criteria": {"has_smelts_to_glass": "2025-07-21 22:24:11 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_sandstone_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "additionallanterns:recipes/misc/prismarine_lantern": {"criteria": {"recipe_condition": "2025-07-21 22:28:00 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "lootr:root": {"criteria": {"always_true": "2025-07-21 22:18:59 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-21 22:21:38 +0800"}, "done": true}, "alchemistry:recipes/dissolver/red_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-21 22:24:11 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-21 22:18:59 +0800"}, "done": true}, "alchemistry:recipes/dissolver/charcoal": {"criteria": {"has_the_recipe": "2025-07-21 22:23:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_planks": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_classic_trapdoor": {"criteria": {"has_logs": "2025-07-21 22:28:16 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_boat": {"criteria": {"in_water": "2025-07-21 22:26:40 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-21 22:21:38 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_chair": {"criteria": {"has_planks": "2025-07-21 22:23:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-21 22:21:38 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_iron_sword": "2025-07-21 22:21:38 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/sand_bricks": {"criteria": {"has_item2_domum_ornamentum_sand_bricks": "2025-07-21 22:24:11 +0800", "has_item1_domum_ornamentum_sand_bricks": "2025-07-21 22:24:11 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-21 22:21:38 +0800", "has_fillet_knife": "2025-07-21 22:21:38 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-21 22:21:38 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_concrete_powder": {"criteria": {"has_sand": "2025-07-21 22:24:11 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-21 22:21:38 +0800"}, "done": true}, "DataVersion": 3465}