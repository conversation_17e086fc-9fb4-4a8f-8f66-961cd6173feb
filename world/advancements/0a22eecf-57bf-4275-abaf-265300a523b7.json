{"undergarden:undergarden/root": {"criteria": {"tick": "2025-07-09 18:30:33 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-09 18:30:33 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-09 18:30:33 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-09 18:30:33 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-09 18:30:33 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-09 18:30:33 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-09 18:30:33 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-09 18:30:33 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-09 18:30:33 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-09 18:30:33 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-09 18:30:33 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-09 18:30:33 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-09 18:30:33 +0800"}, "done": false}, "lootr:root": {"criteria": {"always_true": "2025-07-09 18:30:33 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-09 18:30:33 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-09 18:30:34 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:bamboo_jungle": "2025-07-09 19:52:54 +0800", "minecraft:beach": "2025-07-09 18:30:34 +0800", "minecraft:lukewarm_ocean": "2025-07-09 20:01:21 +0800", "minecraft:river": "2025-07-09 20:08:06 +0800", "minecraft:desert": "2025-07-09 19:58:23 +0800", "minecraft:deep_lukewarm_ocean": "2025-07-10 10:51:59 +0800", "minecraft:deep_dark": "2025-07-11 09:43:39 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/all_biomes": {"criteria": {"floodplain": "2025-07-09 19:08:59 +0800", "lush_savanna": "2025-07-09 19:58:14 +0800", "crystalline_chasm": "2025-07-09 20:47:42 +0800", "rocky_rainforest": "2025-07-09 19:09:00 +0800", "undergrowth": "2025-07-09 20:15:48 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/root": {"criteria": {"floodplain": "2025-07-09 19:08:59 +0800"}, "done": true}, "maidensmerrymaking:easter/root": {"criteria": {"requirement": "2025-07-09 19:08:59 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-09 19:09:03 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-09 19:09:03 +0800", "has_carrot": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-09 19:09:03 +0800", "has_axe": "2025-07-09 19:09:03 +0800", "has_helmet": "2025-07-09 19:09:03 +0800", "has_fishing_rod": "2025-07-09 19:09:03 +0800", "has_bow": "2025-07-09 19:09:03 +0800", "has_chestplate": "2025-07-09 19:09:03 +0800", "has_sword": "2025-07-09 19:09:03 +0800", "has_fillet_knife": "2025-07-09 19:09:03 +0800", "has_shovel": "2025-07-09 19:09:03 +0800", "has_hoe": "2025-07-09 19:09:03 +0800", "has_boots": "2025-07-09 19:09:03 +0800", "has_pickaxe": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-09 19:09:03 +0800", "has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-09 19:09:03 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-09 19:09:03 +0800", "has_fillet_knife": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-09 19:09:03 +0800", "has_wpt": "2025-07-09 19:09:03 +0800", "has_wct": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-09 19:09:03 +0800", "has_fillet_knife": "2025-07-09 19:09:03 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-09 19:09:03 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-09 19:09:03 +0800", "has_fillet_knife": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-09 19:09:03 +0800", "has_wct": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-09 19:09:03 +0800", "has_wct": "2025-07-09 19:09:03 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-09 19:09:03 +0800", "has_wpt": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-09 19:09:03 +0800", "has_fillet_knife": "2025-07-09 19:09:03 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-09 19:09:03 +0800"}, "done": true}, "mcwpaths:recipes/soil": {"criteria": {"has_wood": "2025-07-09 19:09:05 +0800"}, "done": true}, "supplementaries:recipes/flower_box": {"criteria": {"has_dirt": "2025-07-09 19:09:05 +0800"}, "done": true}, "mcwroofs:recipes/grass": {"criteria": {"has_planks": "2025-07-09 19:09:05 +0800"}, "done": true}, "utilitarian:recipes/misc/snad/drit": {"criteria": {"has_dirt": "2025-07-09 19:09:05 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/dirt_1x": {"criteria": {"has_dirt": "2025-07-09 19:09:05 +0800"}, "done": true}, "minecraft:adventure/root": {"criteria": {"killed_by_something": "2025-07-09 19:35:00 +0800"}, "done": true}, "tombstone:adventure/cancel_ghostly_shape": {"criteria": {"coded_trigger": "2025-07-09 19:35:57 +0800"}, "done": true}, "minecraft:adventure/kill_a_mob": {"criteria": {"minecraft:zombie": "2025-07-09 19:36:41 +0800"}, "done": true}, "minecraft:adventure/kill_all_mobs": {"criteria": {"minecraft:blaze": "2025-07-09 20:59:14 +0800", "minecraft:skeleton": "2025-07-10 21:27:01 +0800", "minecraft:hoglin": "2025-07-09 20:16:30 +0800", "minecraft:vindicator": "2025-07-10 21:35:36 +0800", "minecraft:magma_cube": "2025-07-09 20:53:16 +0800", "minecraft:creeper": "2025-07-10 21:35:06 +0800", "minecraft:evoker": "2025-07-10 11:27:23 +0800", "minecraft:phantom": "2025-07-09 20:26:31 +0800", "minecraft:zombified_piglin": "2025-07-09 21:00:28 +0800", "minecraft:witch": "2025-07-10 21:11:10 +0800", "minecraft:wither_skeleton": "2025-07-09 20:45:22 +0800", "minecraft:husk": "2025-07-09 20:23:47 +0800", "minecraft:shulker": "2025-07-10 11:24:58 +0800", "minecraft:enderman": "2025-07-10 11:24:36 +0800", "minecraft:endermite": "2025-07-10 12:08:03 +0800", "minecraft:vex": "2025-07-10 11:25:17 +0800", "minecraft:zombie": "2025-07-09 19:36:41 +0800"}, "done": false}, "farmersdelight:recipes/cooking/dog_food": {"criteria": {"has_any_ingredient": "2025-07-09 19:36:42 +0800"}, "done": true}, "utilitix:recipes/misc/mob_bell": {"criteria": {"criterion8": "2025-07-09 19:36:42 +0800"}, "done": true}, "farmersdelight:recipes/building_blocks/organic_compost_from_rotten_flesh": {"criteria": {"has_rotten_flesh": "2025-07-09 19:36:42 +0800"}, "done": true}, "apotheosis:affix/gem": {"criteria": {"gem": "2025-07-09 19:36:42 +0800"}, "done": true}, "apotheosis:affix/rare_gem": {"criteria": {"gem": "2025-07-09 19:36:42 +0800"}, "done": true}, "aether:recipes/combat/iron_sword_repairing": {"criteria": {"has_iron_sword": "2025-07-09 19:52:57 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_iron_sword": "2025-07-09 19:52:57 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_sword": {"criteria": {"has_iron_sword": "2025-07-09 19:52:57 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_iron_sword": "2025-07-09 19:52:57 +0800"}, "done": true}, "mcwwindows:recipes/dark_prismarine": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_beach_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barred_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/acacia/oak_bark_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barrel_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/bricks": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_trapdoors": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_drawer": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_pressure_plates": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barn_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "securitycraft:recipes/misc/sc_manual": {"criteria": {"has_wood": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_tropical_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/sandstone": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/diorite": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_desk_cabinet": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/curtain_rods": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_mystic_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_upgraded_fence": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_desk": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_table": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/oak": {"criteria": {"has_wood": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_chair": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/granite": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_cabinet": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_swamp_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "naturescompass:natures_compass": {"criteria": {"has_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/birch": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "naturescompass:natures_compass_log": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_desk": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/oak_log_1x": {"criteria": {"has_oak_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwfurnitures:recipes/oak": {"criteria": {"has_wood": "2025-07-09 19:54:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_wood": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_crate": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/blackstone": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "utilitarian:recipes/food/utility/charcoal_from_campfire": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/print_classic": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_park_bench": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwroofs:recipes/oak": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_ranch_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/acacia": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_coffee_table": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/quartz": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "railcraft:recipes/coke_oven/charcoal": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/spruce": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_counter": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/mangrove": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/red_sandstone": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_slabs": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/prismarine": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/metal": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/dark_oak": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/components": {"criteria": {"has_wood": "2025-07-09 19:54:01 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/architectscutter": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_glass_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/cherry": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/logs_to_bowls": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_whispering_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_cottage_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_doors": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/deepslate": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/jungle": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_bedside_cabinet": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_bedside_cabinet": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "enderio:recipes/misc/stick": {"criteria": {"has_ingredient": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_coffee_table": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/andesite": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbridges:recipes/oak_bridge_pier": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwdoors:recipes/oak": {"criteria": {"has_wood": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_drawer": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_blinds": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_stairs": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "naturescompass:natures_compass_sapling": {"criteria": {"has_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_windows": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_table": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_planks": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_boats": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_counter": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_desk_cabinet": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_cabinet": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_four_panel_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_park_bench": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/warped": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_paper_trapdoor": {"criteria": {"has_logs": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_crate": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/oak_upgraded_gate": {"criteria": {"has_log": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/stone": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "mcwwindows:recipes/crimson": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "alchemistry:recipes/dissolver/charcoal": {"criteria": {"has_the_recipe": "2025-07-09 19:54:01 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_chair": {"criteria": {"has_planks": "2025-07-09 19:54:01 +0800"}, "done": true}, "constructionwand:recipes/tools/stone_wand": {"criteria": {"has_item": "2025-07-09 19:54:10 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite_with_vanilla_cobblestone": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "utilitix:recipes/misc/crude_furnace": {"criteria": {"criterion1": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_stairs": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/combat/stone_sword": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/decorations/cobblestone_wall": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/tools/stone_axe": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/stone_blacksmith_gavel": {"criteria": {"has_stone_tool_materials": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:story/mine_stone": {"criteria": {"get_stone": "2025-07-09 19:54:10 +0800"}, "done": true}, "caupona:recipes/decorations/mud_kitchen_stove": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "securitycraft:recipes/redstone/laser_block": {"criteria": {"has_stone": "2025-07-09 19:54:10 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_stairs_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/tools/stone_hoe": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/redstone/lever": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "supplementaries:recipes/checker": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_bricks": {"criteria": {"has_item": "2025-07-09 19:54:10 +0800"}, "done": true}, "aquaculture:recipes/tools/stone_fillet_knife": {"criteria": {"has_items": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/tools/stone_pickaxe": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobblestone_chain": {"criteria": {"recipe_condition": "2025-07-09 19:54:10 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/green_cobblestone_extra": {"criteria": {"has_material": "2025-07-09 19:54:10 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/purple_cobblestone_extra": {"criteria": {"has_material": "2025-07-09 19:54:10 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stone": {"criteria": {"has_the_recipe": "2025-07-09 19:54:10 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_stairs_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-09 19:54:10 +0800"}, "done": true}, "enderio:recipes/misc/stone_gear": {"criteria": {"has_ingredient": "2025-07-09 19:54:10 +0800"}, "done": true}, "supplementaries:recipes/turn_table": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_slab": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "mcwroofs:recipes/cobblestone": {"criteria": {"has_planks": "2025-07-09 19:54:10 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_bricks_stonecutting": {"criteria": {"has_item": "2025-07-09 19:54:10 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge_pier": {"criteria": {"has_planks": "2025-07-09 19:54:10 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_wall_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/decorations/furnace": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/cobblestone_1x": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite_with_vanilla_diorite": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/cobblestone_extra": {"criteria": {"has_material": "2025-07-09 19:54:10 +0800"}, "done": true}, "utilitix:recipes/misc/comparator_redirector_up": {"criteria": {"criterion1": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/tools/stone_shovel": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge": {"criteria": {"has_planks": "2025-07-09 19:54:10 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_diorite": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cobblestone_slab": {"criteria": {"has_the_recipe": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/decorations/cobblestone_wall_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_slab_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-09 19:54:10 +0800"}, "done": true}, "utilitix:recipes/misc/comparator_redirector_down": {"criteria": {"criterion1": "2025-07-09 19:54:10 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_cobblestone_bridge": {"criteria": {"has_planks": "2025-07-09 19:54:10 +0800"}, "done": true}, "mcwpaths:recipes/cobblestone": {"criteria": {"has_wood": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_slab_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stone": {"criteria": {"has_cobblestone": "2025-07-09 19:54:10 +0800"}, "done": true}, "silentgear:recipes/misc/stone_rod": {"criteria": {"has_item": "2025-07-09 19:54:10 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/blue_cobblestone_extra": {"criteria": {"has_material": "2025-07-09 19:54:10 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge_stair": {"criteria": {"has_planks": "2025-07-09 19:54:10 +0800"}, "done": true}, "handcrafted:recipes/misc/bear_trophy": {"criteria": {"has_bear_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "twigs:recipes/tables/oak_table": {"criteria": {"has_bamboo": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/goat_trophy": {"criteria": {"has_goat_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwbridges:recipes/asian_red_bridge_pier": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_onside": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_bench": {"criteria": {"has_oak_bench": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwbridges:recipes/asian_red_bridge": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwwindows:recipes/shutters": {"criteria": {"has_wood": "2025-07-09 19:55:02 +0800"}, "done": true}, "supplementaries:recipes/notice_board": {"criteria": {"minecraft:planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_cupboard": {"criteria": {"has_oak_cupboard": "2025-07-09 19:55:02 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_standing": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/skeleton_horse_trophy": {"criteria": {"has_skeleton_horse_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_nightstand": {"criteria": {"has_oak_nightstand": "2025-07-09 19:55:02 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/oak_planks_1x": {"criteria": {"has_oak_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/amber_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwbridges:recipes/oak_log_bridge_middle": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/peach_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "supplementaries:recipes/speaker_block": {"criteria": {"minecraft:planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/fox_trophy": {"criteria": {"has_fox_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_pillar_trim": {"criteria": {"has_oak_pillar_trim": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/pufferfish_trophy": {"criteria": {"has_pufferfish_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_side_table": {"criteria": {"has_oak_side_table": "2025-07-09 19:55:02 +0800"}, "done": true}, "supplementaries:recipes/pulley": {"criteria": {"minecraft:planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/bubblegum_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_blossom_trapdoor": {"criteria": {"has_plankss": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/conifer_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_1": {"criteria": {"has_oak_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_2": {"criteria": {"has_oak_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_3": {"criteria": {"has_oak_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_4": {"criteria": {"has_oak_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwbridges:recipes/rope_oak_bridge": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwbridges:recipes/oak_rail_bridge": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_chair": {"criteria": {"has_oak_chair": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/persimmon_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_barrel": {"criteria": {"has _plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/ultramarine_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_corner_trim": {"criteria": {"has_oak_corner_trim": "2025-07-09 19:55:02 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_dark": {"criteria": {"has_bottom": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/mint_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwroofs:recipes/oak_planks": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_desk": {"criteria": {"has_oak_desk": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/fluorescent_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stick": {"criteria": {"has_the_recipe": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/cherenkov_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/aquamarine_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_button": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_light": {"criteria": {"has_bottom": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwwindows:recipes/parapets": {"criteria": {"has_wood": "2025-07-09 19:55:02 +0800"}, "done": true}, "framedblocks:recipes/building_blocks/framed_cube": {"criteria": {"hasPlanks": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_slab": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_door": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/blaze_trophy": {"criteria": {"has_blaze_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/wolf_trophy": {"criteria": {"has_wolf_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwpaths:recipes/oak": {"criteria": {"has_wood": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_fancy_bed": {"criteria": {"has_oak_fancy_bed": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_table": {"criteria": {"has_oak_table": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/silverfish_trophy": {"criteria": {"has_silverfish_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_chest": {"criteria": {"has _plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_dining_bench": {"criteria": {"has_oak_dining_bench": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/icy_blue_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "supplementaries:recipes/clock_block": {"criteria": {"minecraft:planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/navy_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "silentgear:recipes/misc/upgrade_base": {"criteria": {"has_item": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_stairs": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/decorations/oak_fence": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_2": {"criteria": {"has _plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_3": {"criteria": {"has _plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_1": {"criteria": {"has _plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_4": {"criteria": {"has _plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/tropical_fish_trophy": {"criteria": {"has_tropical_fish_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_drawer": {"criteria": {"has_oak_drawer": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_shelf": {"criteria": {"has_oak_shelf": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/lavender_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/maroon_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_counter": {"criteria": {"has_oak_counter": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_trapdoor": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/honey_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "supplementaries:recipes/bed_from_feather_block": {"criteria": {"minecraft:planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_couch": {"criteria": {"has_oak_couch": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/spider_trophy": {"criteria": {"has_spider_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/wine_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_pressure_plate": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/rose_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/spring_green_bed": {"criteria": {"has_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/wither_skeleton_trophy": {"criteria": {"has_wither_skeleton_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/decorations/oak_sign": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "supplementaries:recipes/lock_block": {"criteria": {"minecraft:planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "mcwwindows:recipes/blinds": {"criteria": {"has_wood": "2025-07-09 19:55:02 +0800"}, "done": true}, "handcrafted:recipes/misc/salmon_trophy": {"criteria": {"has_salmon_trophy": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_chest": {"criteria": {"has_oak_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_fence_gate": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_barrel": {"criteria": {"has_oak_plank": "2025-07-09 19:55:02 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/wooden_blacksmith_gavel": {"criteria": {"has_planks": "2025-07-09 19:55:02 +0800"}, "done": true}, "create:recipes/building_blocks/oak_window": {"criteria": {"has_ingredient": "2025-07-09 19:55:02 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_jungle_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_magenta": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "utilitix:recipes/misc/armed_stand": {"criteria": {"criterion1": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/persimmon_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dark_oak_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_dark_oak_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_oak_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_bamboo_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/timber_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "utilitix:recipes/misc/reinforced_rail": {"criteria": {"criterion2": "2025-07-09 19:56:01 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear": {"criteria": {"has_ingredient": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_cyan": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/motion_activated_light": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_orange": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_spruce_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/maroon_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "aquaculture:recipes/tools/wooden_fillet_knife": {"criteria": {"has_items": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/peach_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_shovel": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_birch_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/conifer_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "undergarden:recipes/decorations/torch_ditchbulb_paste": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/edelwood_ladder": {"criteria": {"has_rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/spring_green_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_acacia_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/ultramarine_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_gray": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_lime": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecraft:recipes/decorations/ladder": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_axe": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecraft:recipes/decorations/campfire": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/no_soliciting_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_oak_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_purple": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_gray": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/daub_brace": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/crank": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_pink": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/timber_frame": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_hoe": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/sonic_security_system": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear_corner": {"criteria": {"has_ingredient": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/lavender_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/crank": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecolonies:recipes/misc/shapetool": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "undergarden:recipes/decorations/undergarden_scaffolding": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_jungle_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/navy_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/bubblegum_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "utilitix:recipes/misc/directional_highspeed_rail": {"criteria": {"criterion1": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecraft:recipes/combat/wooden_sword": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_green": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_white": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "utilitix:recipes/misc/directional_rail": {"criteria": {"criterion1": "2025-07-09 19:56:01 +0800"}, "done": true}, "undergarden:recipes/decorations/shard_torch": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_cherry_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/honey_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_pickaxe": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_mangrove_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_crimson_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/torch": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_acacia_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/cherenkov_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "utilitix:recipes/misc/hand_bell": {"criteria": {"criterion0": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/fluorescent_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_blue": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_warped_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_mangrove_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "farmersdelight:recipes/decorations/cutting_board": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_spruce_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_birch_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/amber_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "utilitix:recipes/misc/weak_redstone_torch": {"criteria": {"criterion1": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/daub_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_bamboo_fence_gate": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/timber_brace": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/aquamarine_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "paraglider:recipes/misc/paraglider": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_black": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_crimson_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/wine_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "undergarden:recipes/combat/slingshot": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/faucet": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "utilitix:recipes/misc/highspeed_rail": {"criteria": {"criterion1": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_warped_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_blue": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/rose_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/icy_blue_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_red": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/slingshot": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "silentgear:recipes/misc/rough_rod": {"criteria": {"has_item": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_brown": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "farmersdelight:recipes/combat/flint_knife": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_cherry_fence": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/daub_frame": {"criteria": {"forge:rods/wooden": "2025-07-09 19:56:01 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/mint_banner": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_yellow": {"criteria": {"has_stick": "2025-07-09 19:56:01 +0800"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-09 19:56:15 +0800"}, "done": true}, "mcwlights:recipes/torches": {"criteria": {"has_the_recipe": "2025-07-09 19:56:15 +0800"}, "done": true}, "mcwlights:recipes/tiki_torches": {"criteria": {"has_the_recipe": "2025-07-09 19:56:15 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_chest": {"criteria": {"has_lots_of_items": "2025-07-09 19:56:15 +0800"}, "done": true}, "minecraft:story/upgrade_tools": {"criteria": {"stone_pickaxe": "2025-07-09 19:56:15 +0800"}, "done": true}, "aether:recipes/tools/stone_pickaxe_repairing": {"criteria": {"has_stone_pickaxe": "2025-07-09 19:56:15 +0800"}, "done": true}, "mcwlights:recipes/lantern": {"criteria": {"has_the_recipe": "2025-07-09 19:56:15 +0800"}, "done": true}, "mcwlights:recipes/paper_lamp": {"criteria": {"has_the_recipe": "2025-07-09 19:56:15 +0800"}, "done": true}, "minecraft:recipes/decorations/torch": {"criteria": {"has_stone_pickaxe": "2025-07-09 19:56:15 +0800"}, "done": true}, "tombstone:recipes/from_grave_dust": {"criteria": {"has_ingredient": "2025-07-09 19:56:23 +0800"}, "done": true}, "travelersbackpack:recipes/misc/coal": {"criteria": {"has_coal": "2025-07-09 19:58:44 +0800"}, "done": true}, "silentgear:recipes/misc/stone_torch": {"criteria": {"has_item": "2025-07-09 19:58:44 +0800"}, "done": true}, "utilitix:recipes/misc/tiny_coal_to_tiny": {"criteria": {"criterion0": "2025-07-09 19:58:44 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/coal_dust_from_gem": {"criteria": {"has_coal_gem": "2025-07-09 19:58:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/coal_block": {"criteria": {"has_the_recipe": "2025-07-09 19:58:44 +0800"}, "done": true}, "railcraft:recipes/coke_oven/coal_coke": {"criteria": {"has_coal": "2025-07-09 19:58:44 +0800"}, "done": true}, "bigreactors:recipes/misc/blasting/graphite_from_coal": {"criteria": {"has_item": "2025-07-09 19:58:44 +0800"}, "done": true}, "mcwbridges:recipes/bridge_lantern": {"criteria": {"has_planks": "2025-07-09 19:58:44 +0800"}, "done": true}, "sgjourney:recipes/misc/fire_pit": {"criteria": {"has_coal": "2025-07-09 19:58:44 +0800"}, "done": true}, "bigreactors:recipes/misc/smelting/graphite_from_coal": {"criteria": {"has_item": "2025-07-09 19:58:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/coal_block": {"criteria": {"has_coal": "2025-07-09 19:58:44 +0800"}, "done": true}, "additionallanterns:recipes/misc/end_stone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/netherite_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/bricks_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/smooth_stone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/diamond_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "forbidden_arcanus:recipes/decorations/deorum_lantern": {"criteria": {"has_torch": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/gold_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/bone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/dark_prismarine_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "undergarden:recipes/decorations/gloom_o_lantern": {"criteria": {"has_torch": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/basalt_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "mcwbridges:recipes/bridge_torch": {"criteria": {"has_planks": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/iron_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/deepslate_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/obsidian_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "supplementaries:recipes/sconce": {"criteria": {"has_torch": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/quartz_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/blackstone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobblestone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/emerald_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/andesite_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/diorite_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobbled_deepslate_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/crimson_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "twigs:recipes/lamps/lamp": {"criteria": {"has_item": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_nether_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/mossy_cobblestone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/copper_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/purpur_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/granite_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/red_nether_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/warped_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/amethyst_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/red_sandstone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_sandstone_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "additionallanterns:recipes/misc/prismarine_lantern": {"criteria": {"recipe_condition": "2025-07-09 19:58:52 +0800"}, "done": true}, "utilitix:recipes/misc/tiny_coal_from_tiny": {"criteria": {"criterion0": "2025-07-09 19:58:53 +0800", "criterion7": "2025-07-09 19:58:53 +0800", "criterion6": "2025-07-09 19:58:53 +0800", "criterion5": "2025-07-09 19:58:53 +0800", "criterion4": "2025-07-09 19:58:53 +0800", "criterion3": "2025-07-09 19:58:53 +0800", "criterion2": "2025-07-09 19:58:53 +0800", "criterion1": "2025-07-09 19:58:53 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_boat": {"criteria": {"in_water": "2025-07-09 20:01:10 +0800"}, "done": true}, "repurposed_structures:pyramids": {"criteria": {"in_pyramid_ocean": "2025-07-09 20:01:26 +0800"}, "done": false}, "lootr:1chest": {"criteria": {"opened_chest": "2025-07-09 20:01:39 +0800"}, "done": true}, "lootr:social": {"criteria": {"opened_barrel": "2025-07-09 20:06:00 +0800", "opened_shulker": "2025-07-10 12:08:07 +0800", "opened_chest": "2025-07-09 20:01:39 +0800"}, "done": false}, "aether:recipes/misc/aether_iron_nugget_from_smelting": {"criteria": {"has_chainmail_gloves": "2025-07-09 20:01:44 +0800"}, "done": true}, "aether:recipes/misc/aether_iron_nugget_from_blasting": {"criteria": {"has_chainmail_gloves": "2025-07-09 20:01:44 +0800"}, "done": true}, "aether:recipes/combat/chainmail_gloves_repairing": {"criteria": {"has_chainmail_gloves": "2025-07-09 20:01:44 +0800"}, "done": true}, "sfm:recipes/misc/printing_press": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "littlelogistics:recipes/transportation/seater_car": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/combat/iron_helmet": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/decorations/floor_trap": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_gray": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_light_gray": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "alltheores:recipes/misc/iron_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_nugget": {"criteria": {"has_the_recipe": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/combat/iron_boots": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "alltheores:recipes/misc/iron_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/red_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/bubble_blower": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_bars": {"criteria": {"has_the_recipe": "2025-07-09 20:02:44 +0800"}, "done": true}, "croptopia:recipes/misc/knife": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/speed_module": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "farmersdelight:recipes/combat/iron_knife": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/limited_use_keycard": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/green_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_pink": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailboots": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/decorations/iron_bars": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/post_box": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/inventoryplus_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/redstone/iron_door": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "travelersbackpack:recipes/misc/backpack_tank": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/machineinformation_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/fluidplus_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/clock_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/fridge_light": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "enderio:recipes/misc/fluid_tank": {"criteria": {"has_ingredient": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/bomb": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "silentgear:recipes/misc/sturdy_repair_kit": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_door": {"criteria": {"has_the_recipe": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailhelmet": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "littlelogistics:recipes/transportation/seater_barge": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/combat/iron_leggings": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwlights:recipes/lava_lamp": {"criteria": {"has_wood": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/hammer": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/counterplus_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "croptopia:recipes/misc/cooking_pot": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "mythicbotany:recipes/misc/rune_holder": {"criteria": {"criterion0": "2025-07-09 20:02:44 +0800"}, "done": true}, "occultism:recipes/combat/crafting/butcher_knife": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/iron_blacksmith_gavel": {"criteria": {"has_ingots/iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/machine_frame": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwbridges:recipes/iron_bridge_pier": {"criteria": {"has_planks": "2025-07-09 20:02:44 +0800"}, "done": true}, "railcraft:recipes/misc/water_tank_siding": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/redstone/hopper": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "aether:recipes/combat/iron_gloves": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/text_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "croptopia:recipes/misc/frying_pan": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/mine": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_red": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/coil_mv": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/coil_lv": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/pink_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/gray_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "utilitix:recipes/misc/minecart_tinkerer": {"criteria": {"criterion0": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/door_indestructible_iron_item": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "undergarden:recipes/tools/catalyst": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/combat/iron_chestplate": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_block": {"criteria": {"has_the_recipe": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:story/smelt_iron": {"criteria": {"iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/misc/combiner": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/combat/iron_sword": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/smart_module": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/misc/liquifier": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "ad_astra:recipes/misc/compressor": {"criteria": {"has_compressor": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwbridges:recipes/pliers": {"criteria": {"has_planks": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwlights:recipes/garden_light": {"criteria": {"has_wood": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/sentry": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_warning_trapdoor": {"criteria": {"has_logs": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/smartwrench": {"criteria": {"iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_blue": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/black_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/tools/shears": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/iron_block": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/chain": {"criteria": {"has_the_recipe": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/gray_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/shape_card_def": {"criteria": {"iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/tools/iron_shovel": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_full_trapdoor": {"criteria": {"has_logs": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/combat/crossbow": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/camera_monitor": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "modularrouters:recipes/misc/modular_router": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/energy_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/transportation/minecart": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/redstone_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "ad_astra:recipes/compressing/compressing/iron_plate_from_compressing_iron_ingot": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/toolbox": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/goblet": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwlights:recipes/iron_stuff": {"criteria": {"has_wood": "2025-07-09 20:02:44 +0800"}, "done": true}, "travelersbackpack:recipes/misc/iron_tier_upgrade": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/purple_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/green_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/energyplus_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "enderio:recipes/misc/pressurized_fluid_tank": {"criteria": {"has_ingredient": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/lime_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "rechiseled:recipes/misc/chisel": {"criteria": {"recipe_condition": "2025-07-09 20:02:44 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reprocessor/casing": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "handcrafted:recipes/misc/hammer": {"criteria": {"has_hammer": "2025-07-09 20:02:44 +0800"}, "done": true}, "alltheores:recipes/misc/iron_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/black_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_trapdoor": {"criteria": {"has_the_recipe": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/misc/bucket": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/redstone/block_change_detector": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/upgrade_base": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_white": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "ad_astra:recipes/misc/coal_generator": {"criteria": {"has_coal_generator": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/stick_iron": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "simplemagnets:recipes/misc/basicmagnet": {"criteria": {"recipe_condition": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailchestplate": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/iron_gate": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/combat/shield": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "sfm:recipes/misc/manager": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_green": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/tools/iron_axe": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wirecutter": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "sfm:recipes/misc/fancy_cable": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_yellow": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "create:recipes/building_blocks/industrial_iron_block_from_ingots_iron_stonecutting": {"criteria": {"has_ingots_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/misc/atomizer": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "ae2:recipes/misc/decorative/light_detector": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/connector_hv": {"criteria": {"has_aluminum_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_light_blue": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/storage_module": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/inventory_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/security_camera": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/decorations/lantern": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "nethersdelight:recipes/tools/iron_machete": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwdoors:recipes/metal": {"criteria": {"has_wood": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/brown_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "railcraft:recipes/misc/invar_ingot_crafted_with_ingots": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/connector_hv_relay": {"criteria": {"has_aluminum_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/redstone_module": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/tools/iron_hoe": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/tools/spatula": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/misc/compactor": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "simplemagnets:recipes/misc/basic_demagnetization_coil": {"criteria": {"recipe_condition": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/white_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "travelersbackpack:recipes/misc/hose_nozzle": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/harming_module": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "handcrafted:recipes/misc/bench": {"criteria": {"has_bench": "2025-07-09 20:02:44 +0800"}, "done": true}, "aether:recipes/combat/iron_ring": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_cyan": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/red_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "utilitix:recipes/misc/advanced_brewery": {"criteria": {"criterion2": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/blue_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwroofs:recipes/gutters": {"criteria": {"has_planks": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/fridge_dark": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "alchemistry:recipes/misc/dissolver": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/transportation/track_mine": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/andesite_alloy": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/lime_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailleggings": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "railcraft:recipes/misc/iron_gear": {"criteria": {"has_material": "2025-07-09 20:02:44 +0800"}, "done": true}, "chimes:recipes/decorations/iron_chimes": {"criteria": {"has_wood_slabs": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/white_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "travelersbackpack:recipes/misc/iron": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_fence_gate": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_orange": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv5": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv3": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv4": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv1": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv2": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwbridges:recipes/iron_bridge": {"criteria": {"has_planks": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/redstone/iron_trapdoor": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_magenta": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "utilitarian:recipes/misc/fluid_hopper": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/blacklist_module": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/button_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/bouncing_betty": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/pink_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/drillhead_iron": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/purple_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_trapdoor": {"criteria": {"has_logs": "2025-07-09 20:02:44 +0800"}, "done": true}, "create:recipes/misc/crafting/kinetics/empty_blaze_burner": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_brown": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/fluid_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/tools/iron_pickaxe": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_black": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_smithing_table": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "aether:recipes/combat/skyroot_iron_vanilla_shield": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "aether:recipes/combat/iron_pendant": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/spring_launcher": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "constructionwand:recipes/tools/iron_wand": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/brown_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_lime": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "sfm:recipes/misc/cable": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "nethersdelight:recipes/decorations/blackstone_blast_furnace": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/cage": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "farmersdelight:recipes/decorations/cooking_pot": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/storage_control_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/blue_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/whitelist_module": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/briefcase": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/orange_trampoline": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/electrified_iron_fence": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_purple": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "sfm:recipes/misc/fancy_to_cable": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/screwdriver": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/trophy_system": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/component_iron": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "silentgear:recipes/misc/iron_rod": {"criteria": {"has_item": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_remover": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/redstone/heavy_weighted_pressure_plate": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/plate_iron_hammering": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "securitycraft:recipes/misc/disguise_module": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "createoreexcavation:recipes/misc/drill": {"criteria": {"iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_ingot": {"criteria": {"has_iron_ingots": "2025-07-09 20:02:44 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fillet_knife": {"criteria": {"has_items": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/decorations/chain": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "minecraft:recipes/decorations/smithing_table": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/dump_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/counter_module": {"criteria": {"ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "cfm:recipes/decorations/orange_grill": {"criteria": {"has_iron": "2025-07-09 20:02:44 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/iron_dust_from_ingot": {"criteria": {"has_iron_ingot": "2025-07-09 20:02:44 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/green_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "littlelogistics:recipes/transportation/spring": {"criteria": {"has_item": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/lime_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "minecraft:recipes/combat/bow": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/pink_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/purple_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "travelersbackpack:recipes/misc/spider": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "minecraft:recipes/decorations/loom": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "minecraft:recipes/decorations/candle": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_tripwire_hook": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/light_gray_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/gray_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/black_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/magenta_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_loom": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/orange_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/light_blue_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/cyan_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/yellow_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/red_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/brown_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "supplementaries:recipes/sack": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/white_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "minecraft:recipes/tools/fishing_rod": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_wool_from_string": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "minecraft:recipes/redstone/tripwire_hook": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/blue_floating_carpet": {"criteria": {"has_string": "2025-07-09 20:02:46 +0800"}, "done": true}, "occultism:recipes/building_blocks/crafting/silver_block": {"criteria": {"has_silver_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "occultism:recipes/misc/crafting/magic_lamp_empty": {"criteria": {"has_silver_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/silver_dust_from_ingot": {"criteria": {"has_silver_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "occultism:recipes/misc/crafting/silver_nugget": {"criteria": {"has_silver_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "alltheores:recipes/misc/silver_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/silver]_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/plate_silver_hammering": {"criteria": {"has_silver_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "occultism:recipes/misc/crafting/lens_frame": {"criteria": {"has_silver_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "alltheores:recipes/misc/silver_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/silver]_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "alltheores:recipes/misc/silver_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/silver]_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "delightful:recipes/knives/silver_knife": {"criteria": {"has_ingots/silver": "2025-07-09 20:02:49 +0800"}, "done": true}, "alltheores:recipes/misc/silver_block": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/silver]_ingot": "2025-07-09 20:02:49 +0800"}, "done": true}, "railcraft:recipes/misc/silver_gear": {"criteria": {"has_material": "2025-07-09 20:02:49 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_sword": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/decorations/jukebox": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_rod": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-09 20:03:04 +0800"}, "done": true}, "createoreexcavation:recipes/misc/diamond_drill": {"criteria": {"diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "occultism:recipes/spirit_fire/spirit_fire/spirit_attuned_gem": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "alchemistry:recipes/dissolver/jukebox": {"criteria": {"has_the_recipe": "2025-07-09 20:03:04 +0800"}, "done": true}, "utilitix:recipes/misc/diamond_shears": {"criteria": {"criterion0": "2025-07-09 20:03:04 +0800"}, "done": true}, "farmersdelight:recipes/combat/diamond_knife": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_jukebox": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/diamond_blacksmith_gavel": {"criteria": {"has_gems/diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "ae2additions:recipes/misc/blocks/wireless_transceiver": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_plate": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-09 20:03:04 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_exchanging": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_hoe": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "travelersbackpack:recipes/misc/diamond_tier_upgrade": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "constructionwand:recipes/tools/diamond_wand": {"criteria": {"has_item": "2025-07-09 20:03:04 +0800"}, "done": true}, "ae2additions:recipes/misc/super_cell_housing": {"criteria": {"has_item": "2025-07-09 20:03:04 +0800"}, "done": true}, "silentgear:recipes/misc/diamond_shard": {"criteria": {"has_item": "2025-07-09 20:03:04 +0800"}, "done": true}, "aether:recipes/combat/diamond_gloves": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "additionallanterns:recipes/misc/diamond_chain": {"criteria": {"recipe_condition": "2025-07-09 20:03:04 +0800"}, "done": true}, "silentgear:recipes/misc/diamond_from_shards": {"criteria": {"has_item": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_axe": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_pickaxe": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "dimstorage:recipes/misc/dim_core": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_building": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_helmet": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:story/mine_diamond": {"criteria": {"diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "charginggadgets:recipes/redstone/charging_station": {"criteria": {"has_diamonds": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/building_blocks/diamond_block": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_gear": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_boots": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/diamond_dust_from_gem": {"criteria": {"has_diamond_gem": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_chestplate": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_shovel": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "travelersbackpack:recipes/misc/diamond": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_leggings": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "mininggadgets:recipes/misc/upgrade_empty": {"criteria": {"has_diamonds": "2025-07-09 20:03:04 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fillet_knife": {"criteria": {"has_items": "2025-07-09 20:03:04 +0800"}, "done": true}, "alchemistry:recipes/dissolver/diamond_block": {"criteria": {"has_the_recipe": "2025-07-09 20:03:04 +0800"}, "done": true}, "nethersdelight:recipes/tools/diamond_machete": {"criteria": {"has_diamond": "2025-07-09 20:03:04 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/gold_backpack": {"criteria": {"has_iron_backpack": "2025-07-09 20:03:15 +0800"}, "done": true}, "artifacts:amateur_archaeologist": {"criteria": {"find_artifact": "2025-07-09 20:03:18 +0800"}, "done": true}, "nethersdelight:recipes/tools/golden_machete": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/combat/golden_boots": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "farmersdelight:recipes/combat/golden_knife": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/sanity_meter": {"criteria": {"has_ingots/gold": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gold_block": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "aquaculture:recipes/tools/gold_fillet_knife": {"criteria": {"has_items": "2025-07-09 20:03:22 +0800"}, "done": true}, "supplementaries:recipes/hourglass": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/golden_apple": {"criteria": {"has_the_recipe": "2025-07-09 20:03:22 +0800"}, "done": true}, "supplementaries:recipes/gold_door": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "aether:recipes/combat/golden_pendant": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/gold_nugget": {"criteria": {"has_the_recipe": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/tools/golden_axe": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/redstone/light_weighted_pressure_plate": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "utilitarian:recipes/misc/angel_block_rot": {"criteria": {"has_gold": "2025-07-09 20:03:22 +0800"}, "done": true}, "supplementaries:recipes/key": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "aether:recipes/combat/golden_gloves": {"criteria": {"has_gold": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/combat/golden_leggings": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/misc/gold_nugget": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "railcraft:recipes/misc/gold_gear": {"criteria": {"has_material": "2025-07-09 20:03:22 +0800"}, "done": true}, "aether:recipes/combat/golden_ring": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/gold_block": {"criteria": {"has_the_recipe": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/combat/golden_sword": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "mythicbotany:recipes/misc/alfsteel_template": {"criteria": {"criterion0": "2025-07-09 20:03:22 +0800"}, "done": true}, "utilitix:recipes/misc/gilding_crystal": {"criteria": {"criterion0": "2025-07-09 20:03:22 +0800"}, "done": true}, "bloodmagic:recipes/misc/blood_altar": {"criteria": {"has_gold": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/tools/golden_hoe": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "mcwlights:recipes/sea_lantern_slab": {"criteria": {"has_wood": "2025-07-09 20:03:22 +0800"}, "done": true}, "mcwlights:recipes/gold_stuff": {"criteria": {"has_wood": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/food/golden_apple": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "alltheores:recipes/misc/gold_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/gold]_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "utilitarian:recipes/misc/angel_block": {"criteria": {"has_gold": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/combat/golden_helmet": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "forbidden_arcanus:recipes/misc/deorum_ingot": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "alltheores:recipes/misc/gold_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/gold]_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/golden_blacksmith_gavel": {"criteria": {"has_ingots/gold": "2025-07-09 20:03:22 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/gold_dust_from_ingot": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "additionallanterns:recipes/misc/gold_chain": {"criteria": {"recipe_condition": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/tools/golden_shovel": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/combat/golden_chestplate": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "travelersbackpack:recipes/misc/gold": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/plate_gold_hammering": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "supplementaries:recipes/gold_trapdoor": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "travelersbackpack:recipes/misc/gold_tier_upgrade": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "minecraft:recipes/tools/golden_pickaxe": {"criteria": {"has_gold_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "supplementaries:recipes/redstone_illuminator": {"criteria": {"has_sea_lantern": "2025-07-09 20:03:22 +0800"}, "done": true}, "alltheores:recipes/misc/gold_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/gold]_ingot": "2025-07-09 20:03:22 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_axe": {"criteria": {"has_iron_axe": "2025-07-09 20:03:23 +0800"}, "done": true}, "aether:recipes/tools/iron_axe_repairing": {"criteria": {"has_iron_axe": "2025-07-09 20:03:23 +0800"}, "done": true}, "apotheosis:affix/root": {"criteria": {"sword": "2025-07-09 20:03:23 +0800"}, "done": true}, "sliceanddice:recipes/misc/slicer": {"criteria": {"has_tool": "2025-07-09 20:03:23 +0800"}, "done": true}, "apotheosis:affix/chests": {"criteria": {"sword": "2025-07-09 20:03:23 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard_smithing": {"criteria": {"has_standard": "2025-07-09 20:03:34 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/iron_backpack_from_copper": {"criteria": {"has_copper_backpack": "2025-07-09 20:03:41 +0800"}, "done": true}, "dungeons_arise:find_illager_corsair_or_illager_galley": {"criteria": {"location1": "2025-07-09 20:04:23 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_pillar_from_quartz_block_stonecutting": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_pillar": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/quartz_chain": {"criteria": {"recipe_condition": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_slab": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_bricks_from_quartz_block_stonecutting": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "alchemistry:recipes/dissolver/quartz_pillar": {"criteria": {"has_the_recipe": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_stairs_from_quartz_block_stonecutting": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "twigs:recipes/columns/quartz_column": {"criteria": {"has_item": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_stairs": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "handcrafted:recipes/misc/quartz_pillar_trim": {"criteria": {"has_quartz_pillar_trim": "2025-07-09 20:04:40 +0800"}, "done": true}, "alchemistry:recipes/dissolver/quartz_slab": {"criteria": {"has_the_recipe": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_bricks": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/quartz_block_1x": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_slab_from_stonecutting": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/tablet": {"criteria": {"quartz": "2025-07-09 20:04:40 +0800"}, "done": true}, "alchemistry:recipes/dissolver/smooth_quartz": {"criteria": {"has_the_recipe": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/smooth_quartz": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_quartz_block": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_quartz_block_from_quartz_block_stonecutting": {"criteria": {"has_quartz_block": "2025-07-09 20:04:40 +0800"}, "done": true}, "alchemistry:recipes/dissolver/quartz_bricks": {"criteria": {"has_the_recipe": "2025-07-09 20:04:40 +0800"}, "done": true}, "laserio:recipes/misc/logic_chip_raw": {"criteria": {"has_quartz": "2025-07-09 20:04:40 +0800"}, "done": true}, "alchemistry:recipes/dissolver/chiseled_quartz_block": {"criteria": {"has_the_recipe": "2025-07-09 20:04:40 +0800"}, "done": true}, "handcrafted:recipes/misc/quartz_corner_trim": {"criteria": {"has_quartz_corner_trim": "2025-07-09 20:04:40 +0800"}, "done": true}, "twigs:recipes/columns/quartz_column_stonecutting": {"criteria": {"has_item": "2025-07-09 20:04:40 +0800"}, "done": true}, "supplementaries:recipes/bamboo_spikes": {"criteria": {"minecraft:wooden_slabs": "2025-07-09 20:05:14 +0800"}, "done": true}, "supplementaries:recipes/jar": {"criteria": {"minecraft:wooden_slabs": "2025-07-09 20:05:14 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_bowl": {"criteria": {"has_wood_bowl": "2025-07-09 20:05:14 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_plate": {"criteria": {"has_wood_plate": "2025-07-09 20:05:14 +0800"}, "done": true}, "twigs:recipes/tables/oak_table_slab": {"criteria": {"has_bamboo": "2025-07-09 20:05:14 +0800"}, "done": true}, "supplementaries:recipes/item_shelf": {"criteria": {"minecraft:wooden_slabs": "2025-07-09 20:05:14 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/oak_banister": {"criteria": {"has_item": "2025-07-09 20:05:14 +0800"}, "done": true}, "supplementaries:recipes/bellows": {"criteria": {"minecraft:wooden_slabs": "2025-07-09 20:05:14 +0800"}, "done": true}, "supplementaries:recipes/blackboard": {"criteria": {"minecraft:wooden_slabs": "2025-07-09 20:05:14 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_barrel": {"criteria": {"has_slabs": "2025-07-09 20:05:14 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_cup": {"criteria": {"has_wood_cup": "2025-07-09 20:05:14 +0800"}, "done": true}, "minecraft:recipes/decorations/composter": {"criteria": {"has_wood_slab": "2025-07-09 20:05:14 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/trough": {"criteria": {"has_item": "2025-07-09 20:05:14 +0800"}, "done": true}, "aether:recipes/combat/iron_chestplate_repairing": {"criteria": {"has_iron_chestplate": "2025-07-09 20:05:26 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_chestplate": {"criteria": {"has_iron_chestplate": "2025-07-09 20:05:26 +0800"}, "done": true}, "minecraft:story/obtain_armor": {"criteria": {"iron_chestplate": "2025-07-09 20:05:26 +0800"}, "done": true}, "apotheosis:affix/rare": {"criteria": {"sword": "2025-07-09 20:05:26 +0800"}, "done": true}, "utilitix:recipes/misc/linked_crystal": {"criteria": {"criterion1": "2025-07-09 20:05:27 +0800"}, "done": true}, "mythicbotany:recipes/misc/central_rune_holder": {"criteria": {"criterion0": "2025-07-09 20:05:27 +0800"}, "done": true}, "utilitix:recipes/misc/experience_crystal": {"criteria": {"criterion1": "2025-07-09 20:05:27 +0800"}, "done": true}, "silentgear:recipes/misc/emerald_from_shards": {"criteria": {"has_item": "2025-07-09 20:05:27 +0800"}, "done": true}, "minecraft:recipes/building_blocks/emerald_block": {"criteria": {"has_emerald": "2025-07-09 20:05:27 +0800"}, "done": true}, "alchemistry:recipes/dissolver/emerald_block": {"criteria": {"has_the_recipe": "2025-07-09 20:05:27 +0800"}, "done": true}, "travelersbackpack:recipes/misc/emerald": {"criteria": {"has_emerald": "2025-07-09 20:05:27 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_copy_paste": {"criteria": {"has_emerald": "2025-07-09 20:05:27 +0800"}, "done": true}, "additionallanterns:recipes/misc/emerald_chain": {"criteria": {"recipe_condition": "2025-07-09 20:05:27 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/emerald_dust_from_gem": {"criteria": {"has_emerald_gem": "2025-07-09 20:05:27 +0800"}, "done": true}, "silentgear:recipes/misc/emerald_shard": {"criteria": {"has_item": "2025-07-09 20:05:27 +0800"}, "done": true}, "lootr:1barrel": {"criteria": {"opened_barrel": "2025-07-09 20:06:00 +0800"}, "done": true}, "computercraft:recipes/redstone/computer_advanced": {"criteria": {"has_components": "2025-07-09 20:06:13 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/rs_engineering": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "alchemistry:recipes/dissolver/redstone_block": {"criteria": {"has_the_recipe": "2025-07-09 20:06:13 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_smart_fluix": {"criteria": {"has_dusts/redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "sfm:recipes/misc/disk": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "appmek:recipes/misc/chemical_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "littlelogistics:recipes/tools/locomotive_route": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "littlelogistics:recipes/redstone/vessel_charger": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/redstone_information": {"criteria": {"redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "trashcans:recipes/misc/energy_trash_can": {"criteria": {"recipe_condition": "2025-07-09 20:06:13 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/rose_quartz": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/misc/username_logger": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "utilitix:recipes/misc/dimmable_redstone_lamp": {"criteria": {"criterion1": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_key_changer": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/misc/claymore": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "railcraft:recipes/misc/radio_circuit": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/basic/passivetap_fe": {"criteria": {"has_item2": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl1": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl3": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl2": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "supplementaries:recipes/altimeter": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "minecraft:recipes/redstone/dropper": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_modifier": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/misc/cage_trap": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "minecraft:recipes/redstone/piston": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "railcraft:recipes/misc/signal_circuit": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/redstone_acid": {"criteria": {"has_redstone_dust": "2025-07-09 20:06:13 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_note_block": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/pulse_repeater": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "minecraft:recipes/redstone/target": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "minecraft:recipes/tools/compass": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "modularrouters:recipes/misc/blank_module": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/reinforced/passivetap_fe": {"criteria": {"has_item2": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_redstone_lamp": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "railcraft:recipes/misc/controller_circuit": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "rftoolspower:recipes/rftoolspower/power_core1": {"criteria": {"core": "2025-07-09 20:06:13 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_piston": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/reinforced/passivetap_fe": {"criteria": {"has_item2": "2025-07-09 20:06:13 +0800"}, "done": true}, "botania:recipes/misc/redstone_root": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_piston": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/powered_latch": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "minecraft:recipes/redstone/note_block": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "travelersbackpack:recipes/misc/redstone": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/pulse_extender": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/fluid_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "quarryplus:recipes/workbench": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/tools/taser": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "littlelogistics:recipes/tools/tug_route": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/screen_link": {"criteria": {"redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/basic/passivetap_fe": {"criteria": {"has_item2": "2025-07-09 20:06:13 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/storage_module0": {"criteria": {"redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/smart_fluix": {"criteria": {"has_dusts/redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/misc/portable_tune_player": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/item_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/alarm": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "supplementaries:recipes/sconce_lever": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/misc/keypad_frame": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "utilitix:recipes/misc/linked_repeater": {"criteria": {"criterion0": "2025-07-09 20:06:13 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_torch": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "railcraft:recipes/misc/receiver_circuit": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/portable_radar": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "railcraft:recipes/misc/signal_tuner": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_block": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dropper": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "minecraft:recipes/tools/clock": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "railcraft:recipes/misc/signal_block_surveyor": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "utilitarian:recipes/misc/redstone_clock": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "computercraft:recipes/redstone/computer_normal": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "rftoolscontrol:recipes/rftoolscontrol/card_base": {"criteria": {"redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/powered_toggle_latch": {"criteria": {"has_item": "2025-07-09 20:06:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/panic_button": {"criteria": {"has_redstone": "2025-07-09 20:06:13 +0800"}, "done": true}, "lootr:10loot": {"criteria": {"loot_stat": "2025-07-09 20:06:41 +0800"}, "done": true}, "aether:recipes/combat/chainmail_leggings_repairing": {"criteria": {"has_chainmail_leggings": "2025-07-09 20:07:05 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/filter_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/stack_upgrade_tier_1": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/void_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/chipped/botanist_workbench_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/pump_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/anvil_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/chipped/alchemy_bench_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/stack_downgrade_tier_1": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/stack_downgrade_tier_3": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/stack_downgrade_tier_2": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/tool_swapper_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/deposit_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/smelting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/stack_upgrade_starter_tier": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/chipped/tinkering_table_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/refill_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/smithing_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/inception_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/battery_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/stonecutter_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/smoking_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/chipped/glassblower_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/blasting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/tank_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/crafting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/feeding_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/chipped/mason_table_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/chipped/carpenters_table_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/pickup_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/jukebox_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/everlasting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/compacting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/chipped/loom_table_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/restock_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:09:59 +0800"}, "done": true}, "mekanism:infused_alloy": {"criteria": {"alloy_infused": "2025-07-09 20:11:25 +0800"}, "done": true}, "mekanism:basic_control_circuit": {"criteria": {"basic_control_circuit": "2025-07-09 20:11:27 +0800"}, "done": true}, "mekanism:energy_transport": {"criteria": {"basic_universal_cable": "2025-07-09 20:11:31 +0800"}, "done": true}, "alltheores:recipes/misc/osmium_nugget_from_ingot": {"criteria": {"has_item": "2025-07-09 20:11:39 +0800"}, "done": true}, "alltheores:recipes/misc/osmium_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/osmium]_ingot": "2025-07-09 20:11:39 +0800"}, "done": true}, "alltheores:recipes/misc/osmium_block": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/osmium]_ingot": "2025-07-09 20:11:39 +0800"}, "done": true}, "delightful:recipes/knives/osmium_knife": {"criteria": {"has_ingots/osmium": "2025-07-09 20:11:39 +0800"}, "done": true}, "mekanism:infusing_efficiency": {"criteria": {"enriched_material": "2025-07-09 20:11:39 +0800"}, "done": true}, "alltheores:recipes/misc/osmium_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/osmium]_ingot": "2025-07-09 20:11:39 +0800"}, "done": true}, "alltheores:recipes/misc/osmium_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/osmium]_ingot": "2025-07-09 20:11:39 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/osmium_dust_from_ingot": {"criteria": {"has_osmium_ingot": "2025-07-09 20:11:39 +0800"}, "done": true}, "mekanism:advanced_control_circuit": {"criteria": {"advanced_control_circuit": "2025-07-09 20:11:50 +0800"}, "done": true}, "aether:recipes/tools/diamond_pickaxe_repairing": {"criteria": {"has_diamond_pickaxe": "2025-07-09 20:12:42 +0800"}, "done": true}, "quarryplus:recipes/solid_fuel_quarry": {"criteria": {"has_diamond_pickaxe": "2025-07-09 20:12:42 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_bucket": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/jerrycan": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "trashcans:recipes/misc/liquid_trash_can": {"criteria": {"recipe_condition": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "enderio:recipes/misc/basic_fluid_filter": {"criteria": {"has_ingredient": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_sink": {"criteria": {"has_bucket": "2025-07-09 20:14:37 +0800"}, "done": true}, "minecraft:nether/root": {"criteria": {"entered_nether": "2025-07-09 20:15:47 +0800"}, "done": true}, "silentgear:nether": {"criteria": {"entered_nether": "2025-07-09 20:15:47 +0800"}, "done": true}, "minecraft:story/enter_the_nether": {"criteria": {"entered_nether": "2025-07-09 20:15:47 +0800"}, "done": true}, "minecraft:recipes/combat/leather_chestplate": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "aether:recipes/tools/aether_saddle": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "minecraft:recipes/combat/leather_boots": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/conveyor_basic": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/backpack": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "minecraft:recipes/misc/leather_horse_armor": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "travelersbackpack:recipes/misc/horse": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "aether:recipes/combat/leather_gloves": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "railcraft:recipes/misc/villager_detector": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "minecraft:recipes/decorations/item_frame": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/powerpack": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "minecraft:recipes/combat/leather_helmet": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "toolbelt:recipes/pouch": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/upgrade_base": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "minecraft:recipes/combat/leather_leggings": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "travelersbackpack:recipes/misc/blank_upgrade": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "toolbelt:recipes/belt": {"criteria": {"has_leather": "2025-07-09 20:16:31 +0800"}, "done": true}, "nethersdelight:recipes/food/hoglin_sirloin_from_smoking": {"criteria": {"hoglin_sirloin": "2025-07-09 20:16:34 +0800"}, "done": true}, "nethersdelight:recipes/food/hoglin_sirloin": {"criteria": {"hoglin_sirloin": "2025-07-09 20:16:34 +0800"}, "done": true}, "nethersdelight:recipes/food/hoglin_sirloin_from_campfire_cooking": {"criteria": {"hoglin_sirloin": "2025-07-09 20:16:34 +0800"}, "done": true}, "croptopia:recipes/campfire_toast": {"criteria": {"has_bread": "2025-07-09 20:16:46 +0800"}, "done": true}, "croptopia:recipes/food/toast_from_smoking_bread": {"criteria": {"has_bread": "2025-07-09 20:16:46 +0800"}, "done": true}, "croptopia:recipes/cheeseburger": {"criteria": {"has_bread": "2025-07-09 20:16:46 +0800"}, "done": true}, "croptopia:recipes/food/toast_from_bread": {"criteria": {"has_bread": "2025-07-09 20:16:46 +0800"}, "done": true}, "minecraft:husbandry/balanced_diet": {"criteria": {"cooked_beef": "2025-07-09 20:54:10 +0800", "bread": "2025-07-09 20:16:50 +0800", "golden_apple": "2025-07-12 18:48:52 +0800", "enchanted_golden_apple": "2025-07-11 17:31:29 +0800"}, "done": false}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2025-07-09 20:16:50 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/obsidian_1x": {"criteria": {"has_obsidian": "2025-07-09 20:17:10 +0800"}, "done": true}, "minecraft:recipes/decorations/enchanting_table": {"criteria": {"has_obsidian": "2025-07-09 20:17:10 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/obsidian_dust": {"criteria": {"has_obsidian": "2025-07-09 20:17:10 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/obsidian_skull": {"criteria": {"has_obsidian": "2025-07-09 20:17:10 +0800"}, "done": true}, "utilitarian:recipes/misc/tps_meter": {"criteria": {"has_obsidian": "2025-07-09 20:17:10 +0800"}, "done": true}, "additionallanterns:recipes/misc/obsidian_chain": {"criteria": {"recipe_condition": "2025-07-09 20:17:10 +0800"}, "done": true}, "minecraft:recipes/tools/flint_and_steel": {"criteria": {"has_obsidian": "2025-07-09 20:17:10 +0800"}, "done": true}, "minecraft:story/form_obsidian": {"criteria": {"obsidian": "2025-07-09 20:17:10 +0800"}, "done": true}, "minecraft:story/lava_bucket": {"criteria": {"lava_bucket": "2025-07-09 20:17:27 +0800"}, "done": true}, "aether:recipes/building_blocks/obsidian_from_bucket_freezing": {"criteria": {"has_lava_bucket": "2025-07-09 20:17:27 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/reinforced/activefluidport_forge": {"criteria": {"has_item2": "2025-07-09 20:17:27 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/basic/activefluidport_forge": {"criteria": {"has_item2": "2025-07-09 20:17:27 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/reinforced/activefluidport_forge": {"criteria": {"has_item2": "2025-07-09 20:17:27 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "handcrafted:recipes/misc/terracotta_medium_pot": {"criteria": {"has_terracotta_medium_pot": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/lavender_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cyan_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/brown_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "handcrafted:recipes/misc/terracotta_thin_pot": {"criteria": {"has_terracotta_thin_pot": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/red_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/lime_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/orange_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/orange_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/magenta_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/yellow_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "handcrafted:recipes/misc/terracotta_plate": {"criteria": {"has_terracotta_plate": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/green_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/pink_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/magenta_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gray_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/yellow_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/light_gray_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/blue_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "handcrafted:recipes/misc/terracotta_bowl": {"criteria": {"has_terracotta_bowl": "2025-07-09 20:23:58 +0800"}, "done": true}, "handcrafted:recipes/misc/terracotta_thick_pot": {"criteria": {"has_terracotta_thick_pot": "2025-07-09 20:23:58 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/brown_stone_bricks": {"criteria": {"has_item1_domum_ornamentum_brown_stone_bricks": "2025-07-09 20:23:58 +0800", "has_item2_domum_ornamentum_brown_stone_bricks": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blue_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_gray_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/red_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "handcrafted:recipes/misc/terracotta_wide_pot": {"criteria": {"has_terracotta_wide_pot": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/green_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/black_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_blue_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/black_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "mcwroofs:recipes/terracotta": {"criteria": {"has_planks": "2025-07-09 20:23:58 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/terracotta_1x": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/brown_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/wine_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/pink_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/light_blue_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/gray_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lime_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/purple_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_drawer": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "handcrafted:recipes/misc/terracotta_cup": {"criteria": {"has_terracotta_cup": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/brown_bricks": {"criteria": {"has_item2_domum_ornamentum_brown_bricks": "2025-07-09 20:23:58 +0800", "has_item1_domum_ornamentum_brown_bricks": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purple_terracotta": {"criteria": {"has_terracotta": "2025-07-09 20:23:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cyan_terracotta": {"criteria": {"has_the_recipe": "2025-07-09 20:23:58 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_counter": {"criteria": {"has_stone": "2025-07-09 20:23:58 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wirecoil_structure_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/shovel_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/hoe_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/axe_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_chestplate": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/iron_tunnel_bore_head": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "alltheores:recipes/misc/steel_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/steel]_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_shovel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/coil_hv": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wirecoil_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_sword": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/steel_dust_from_ingot": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/diamond_tunnel_bore_head": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "alltheores:recipes/misc/steel_nugget_from_ingot": {"criteria": {"has_item": "2025-07-09 20:25:09 +0800"}, "done": true}, "ad_astra:recipes/misc/steel_block": {"criteria": {"has_steel_block": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_shears": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "ad_astra:recipes/compressing/compressing/steel_plate_from_compressing_steel_ingots": {"criteria": {"has_item": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_leggings": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/connector_structural": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/steel_fence": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "alltheores:recipes/misc/steel_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/steel]_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/gunpart_hammer": {"criteria": {"has_ingot_steel": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/goggles": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "delightful:recipes/knives/steel_knife": {"criteria": {"has_ingots/steel": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_axe": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/capacitor_mv": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/capacitor_hv": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/component_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_boots": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "alltheores:recipes/misc/steel_block": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/steel]_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_pickaxe": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/bronze_tunnel_bore_head": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/steel_scaffolding_standard": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/sawblade": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_gear": {"criteria": {"has_material": "2025-07-09 20:25:09 +0800"}, "done": true}, "alltheores:recipes/misc/steel_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/steel]_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/sword_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/steel_wallmount": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/drillhead_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/gunpart_drum": {"criteria": {"has_ingot_steel": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_tunnel_bore_head": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/pickaxe_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/stick_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/rockcutter": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/plate_steel_hammering": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_helmet": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wire_steel": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "railcraft:recipes/misc/steel_hoe": {"criteria": {"has_steel_ingot": "2025-07-09 20:25:09 +0800"}, "done": true}, "mekanism:fluid_transport": {"criteria": {"basic_mechanical_pipe": "2025-07-09 20:25:17 +0800"}, "done": true}, "tombstone:adventure/ambush": {"criteria": {"coded_trigger": "2025-07-09 20:26:31 +0800"}, "done": true}, "tombstone:adventure/first_knowledge": {"criteria": {"coded_trigger": "2025-07-09 20:26:31 +0800"}, "done": true}, "deeperdarker:main/kill_all_sculk_mobs": {"criteria": {"phantom": "2025-07-09 20:26:31 +0800", "warden": "2025-07-11 09:55:38 +0800"}, "done": false}, "aether:recipes/tools/diamond_axe_repairing": {"criteria": {"has_diamond_axe": "2025-07-09 20:32:00 +0800"}, "done": true}, "mekanismgenerators:wind_generator": {"criteria": {"wind_generator": "2025-07-09 20:33:52 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/chipped/carpenters_table_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/void_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/chipped/tinkering_table_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/filter_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/stack_downgrade_tier_3": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/stack_downgrade_tier_2": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/stack_downgrade_tier_1": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/chipped/alchemy_bench_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/stack_upgrade_tier_1": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/compacting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/smoking_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/compression_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/chipped/mason_table_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/pickup_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/chipped/loom_table_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jukebox_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/smelting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/feeding_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/stonecutter_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/blasting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/hopper_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/crafting_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/chipped/botanist_workbench_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/chipped/glassblower_upgrade": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/decoration_table": {"criteria": {"has_upgrade_base": "2025-07-09 20:34:39 +0800"}, "done": true}, "botanypots:husbandry/get_hopper_pot": {"criteria": {"get_hopper_pot": "2025-07-09 20:34:55 +0800"}, "done": true}, "croptopia:gather_food": {"criteria": {"buttered_toast": "2025-07-10 11:07:57 +0800", "avocado_toast": "2025-07-09 20:35:43 +0800", "toast": "2025-07-10 11:12:09 +0800"}, "done": false}, "minecraft:nether/explore_nether": {"criteria": {"minecraft:crimson_forest": "2025-07-09 20:40:54 +0800", "minecraft:nether_wastes": "2025-07-09 20:49:19 +0800", "minecraft:basalt_deltas": "2025-07-09 20:46:57 +0800"}, "done": false}, "minecraft:recipes/misc/nether_brick": {"criteria": {"has_netherrack": "2025-07-09 20:41:21 +0800"}, "done": true}, "alchemistry:recipes/dissolver/nether_brick": {"criteria": {"has_the_recipe": "2025-07-09 20:41:21 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/netherrack_1x": {"criteria": {"has_netherrack": "2025-07-09 20:41:21 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/yellow_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/black_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/orange_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "biomesoplenty:recipes/misc/orange_dye_from_burning_blossom": {"criteria": {"has_burning_blossom": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/green_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/magenta_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/brown_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/lime_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/purple_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/red_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/pink_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/cyan_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/white_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-09 20:41:27 +0800"}, "done": true}, "minecraft:recipes/building_blocks/diorite": {"criteria": {"has_quartz": "2025-07-09 20:44:19 +0800"}, "done": true}, "alchemistry:recipes/dissolver/diorite": {"criteria": {"has_the_recipe": "2025-07-09 20:44:19 +0800"}, "done": true}, "botania:recipes/misc/quartz_red": {"criteria": {"has_item": "2025-07-09 20:44:19 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_granite": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "botania:recipes/misc/quartz_blaze": {"criteria": {"has_item": "2025-07-09 20:44:20 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_observer": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/nether_quartz_pickaxe": {"criteria": {"has_nether_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "twigs:recipes/bloodstone/bloodstone": {"criteria": {"has_item": "2025-07-09 20:44:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/nether_quartz_cutting_knife": {"criteria": {"has_nether_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/nether_quartz_spade": {"criteria": {"has_nether_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "alchemistry:recipes/dissolver/granite": {"criteria": {"has_the_recipe": "2025-07-09 20:44:20 +0800"}, "done": true}, "botania:recipes/misc/quartz_lavender": {"criteria": {"has_item": "2025-07-09 20:44:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/quartz_block": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "railcraft:recipes/misc/bag_of_cement": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "travelersbackpack:recipes/misc/quartz": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/nether_quartz_hoe": {"criteria": {"has_nether_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "botania:recipes/misc/quartz_dark": {"criteria": {"has_item": "2025-07-09 20:44:20 +0800"}, "done": true}, "botania:recipes/misc/quartz_sunny": {"criteria": {"has_item": "2025-07-09 20:44:20 +0800"}, "done": true}, "twigs:recipes/schist/schist": {"criteria": {"has_item": "2025-07-09 20:44:20 +0800"}, "done": true}, "twigs:recipes/rhyolite/rhyolite": {"criteria": {"has_item": "2025-07-09 20:44:20 +0800"}, "done": true}, "minecraft:recipes/redstone/observer": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/nether_quartz_axe": {"criteria": {"has_nether_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "minecraft:recipes/redstone/daylight_detector": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/granite": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/quartz_dust_from_gem": {"criteria": {"has_quartz_gem": "2025-07-09 20:44:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/nether_quartz_wrench": {"criteria": {"has_nether_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "minecraft:recipes/redstone/comparator": {"criteria": {"has_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/nether_quartz_sword": {"criteria": {"has_nether_quartz": "2025-07-09 20:44:20 +0800"}, "done": true}, "minecraft:nether/find_fortress": {"criteria": {"in_better_fortress": "2025-07-09 20:45:08 +0800"}, "done": true}, "handcrafted:recipes/misc/phantom_trophy": {"criteria": {"has_phantom_trophy": "2025-07-09 20:50:40 +0800"}, "done": true}, "aether:recipes/combat/leather_boots_repairing": {"criteria": {"has_leather_boots": "2025-07-09 20:55:24 +0800"}, "done": true}, "lootr:25loot": {"criteria": {"loot_stat": "2025-07-09 20:55:40 +0800"}, "done": true}, "aether:recipes/combat/golden_leggings_repairing": {"criteria": {"has_golden_leggings": "2025-07-09 20:56:03 +0800"}, "done": true}, "apotheosis:affix/epic": {"criteria": {"sword": "2025-07-09 20:56:03 +0800"}, "done": true}, "minecraft:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_golden_leggings": "2025-07-09 20:56:03 +0800"}, "done": true}, "minecraft:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_golden_leggings": "2025-07-09 20:56:03 +0800"}, "done": true}, "aether:recipes/combat/diamond_helmet_repairing": {"criteria": {"has_diamond_helmet": "2025-07-09 20:56:09 +0800"}, "done": true}, "minecraft:story/shiny_gear": {"criteria": {"diamond_helmet": "2025-07-09 20:56:09 +0800"}, "done": true}, "aether:recipes/combat/leather_gloves_repairing": {"criteria": {"has_leather_gloves": "2025-07-09 20:56:17 +0800"}, "done": true}, "aether:recipes/combat/leather_leggings_repairing": {"criteria": {"has_leather_leggings": "2025-07-09 20:57:01 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/lantern": {"criteria": {"has_glowstone": "2025-07-09 20:58:21 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/glowing_blinding_stone": {"criteria": {"has_item": "2025-07-09 20:58:21 +0800"}, "done": true}, "minecraft:recipes/building_blocks/glowstone": {"criteria": {"has_glowstone_dust": "2025-07-09 20:58:21 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/glowing_nature_stone": {"criteria": {"has_item": "2025-07-09 20:58:21 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_dye": {"criteria": {"has_glowstone": "2025-07-09 20:58:21 +0800"}, "done": true}, "railcraft:recipes/misc/signal_lamp": {"criteria": {"has_glowstone_dust": "2025-07-09 20:58:21 +0800"}, "done": true}, "minecraft:recipes/combat/spectral_arrow": {"criteria": {"has_glowstone_dust": "2025-07-09 20:58:21 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/fluidizer/controller": {"criteria": {"has_item2": "2025-07-09 20:58:21 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/energizer/controller": {"criteria": {"has_item2": "2025-07-09 20:58:21 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/glowing_poison_stone": {"criteria": {"has_item": "2025-07-09 20:58:21 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_dye": {"criteria": {"has_glowstone": "2025-07-09 20:58:21 +0800"}, "done": true}, "alchemistry:recipes/dissolver/glowstone": {"criteria": {"has_the_recipe": "2025-07-09 20:58:21 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_dye": {"criteria": {"has_glowstone": "2025-07-09 20:58:21 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/glowing_nature_stonebrick_from_glowstone": {"criteria": {"has_item": "2025-07-09 20:58:21 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/energizer/computerport": {"criteria": {"has_item2": "2025-07-09 20:58:21 +0800"}, "done": true}, "tombstone:adventure/ashes_and_bones": {"criteria": {"coded_trigger": "2025-07-09 20:59:02 +0800"}, "done": true}, "tombstone:adventure/find_grave_dust": {"criteria": {"from_undead": "2025-07-09 21:00:28 +0800"}, "done": true}, "alchemistry:recipes/dissolver/emerald": {"criteria": {"has_the_recipe": "2025-07-10 10:56:30 +0800"}, "done": true}, "minecraft:recipes/misc/emerald": {"criteria": {"has_emerald_block": "2025-07-10 10:56:30 +0800"}, "done": true}, "pylons:recipes/misc/infusion_pylon": {"criteria": {"has_emerald_block": "2025-07-10 10:56:30 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/emerald_block_1x": {"criteria": {"has_emerald_block": "2025-07-10 10:56:30 +0800"}, "done": true}, "handcrafted:recipes/misc/kitchen_hood": {"criteria": {"has_kitchen_hood": "2025-07-10 10:56:34 +0800"}, "done": true}, "handcrafted:recipes/misc/kitchen_hood_pipe": {"criteria": {"has_kitchen_hood_pipe": "2025-07-10 10:56:34 +0800"}, "done": true}, "utilitix:recipes/misc/mob_yoinker": {"criteria": {"criterion0": "2025-07-10 10:56:34 +0800"}, "done": true}, "twilightforest:recipes/decorations/iron_ladder": {"criteria": {"has_iron_bars": "2025-07-10 10:56:34 +0800"}, "done": true}, "xnet:recipes/xnet/antenna": {"criteria": {"bars": "2025-07-10 10:56:34 +0800"}, "done": true}, "minecraft:recipes/redstone/polished_blackstone_button": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_bricks_from_polished_blackstone_stonecutting": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/decorations/polished_blackstone_brick_wall_from_polished_blackstone_stonecutting": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_slab_from_polished_blackstone_stonecutting": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/redstone/polished_blackstone_pressure_plate": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/decorations/polished_blackstone_wall": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_stairs": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_polished_blackstone_from_polished_blackstone_stonecutting": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_brick_slab_from_polished_blackstone_stonecutting": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_stairs_from_polished_blackstone_stonecutting": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_bricks": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "alchemistry:recipes/dissolver/polished_blackstone_bricks": {"criteria": {"has_the_recipe": "2025-07-10 10:56:37 +0800"}, "done": true}, "alchemistry:recipes/dissolver/polished_blackstone_slab": {"criteria": {"has_the_recipe": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_slab": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "minecraft:recipes/decorations/polished_blackstone_wall_from_polished_blackstone_stonecutting": {"criteria": {"has_polished_blackstone": "2025-07-10 10:56:37 +0800"}, "done": true}, "pylons:recipes/misc/potion_filter": {"criteria": {"has_infusion_pylon": "2025-07-10 10:56:50 +0800"}, "done": true}, "ad_astra:recipes/misc/cyan_industrial_lamp": {"criteria": {"has_cyan_industrial_lamp": "2025-07-10 10:57:18 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/glass_cyan": {"criteria": {"has_dyes/black": "2025-07-10 10:57:18 +0800"}, "done": true}, "connectedglass:recipes/misc/scratched_glass_cyan_pane2": {"criteria": {"recipe_condition2": "2025-07-10 10:57:18 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_advanced_overlays/turtle_rainbow_overlay": {"criteria": {"has_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "connectedglass:recipes/misc/scratched_glass_cyan2": {"criteria": {"recipe_condition2": "2025-07-10 10:57:18 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass_cyan_pane2": {"criteria": {"recipe_condition2": "2025-07-10 10:57:18 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_normal_overlays/turtle_rainbow_overlay": {"criteria": {"has_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dye_cyan_carpet": {"criteria": {"has_needed_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "connectedglass:recipes/misc/clear_glass_cyan2": {"criteria": {"recipe_condition2": "2025-07-10 10:57:18 +0800"}, "done": true}, "connectedglass:recipes/misc/tinted_borderless_glass_cyan2": {"criteria": {"recipe_condition2": "2025-07-10 10:57:18 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dye_cyan_bed": {"criteria": {"has_needed_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "cfm:recipes/decorations/dye_cyan_picket_gate": {"criteria": {"has_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_normal_overlays/turtle_trans_overlay": {"criteria": {"has_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "connectedglass:recipes/misc/clear_glass_cyan_pane2": {"criteria": {"recipe_condition2": "2025-07-10 10:57:18 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_cyan_dye": {"criteria": {"forge:dyes/cyan": "2025-07-10 10:57:18 +0800"}, "done": true}, "ad_astra:recipes/misc/small_cyan_industrial_lamp": {"criteria": {"has_small_cyan_industrial_lamp": "2025-07-10 10:57:18 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_dye": {"criteria": {"has_cyan_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/smart_cyan": {"criteria": {"has_dyes/black": "2025-07-10 10:57:18 +0800"}, "done": true}, "travelersbackpack:recipes/building_blocks/dye_cyan_sleeping_bag": {"criteria": {"has_needed_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_advanced_overlays/turtle_trans_overlay": {"criteria": {"has_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dye_cyan_wool": {"criteria": {"has_needed_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_covered_cyan": {"criteria": {"has_dyes/black": "2025-07-10 10:57:18 +0800"}, "done": true}, "cfm:recipes/decorations/dye_cyan_picket_fence": {"criteria": {"has_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "minecraft:recipes/decorations/cyan_stained_glass_pane_from_glass_pane": {"criteria": {"has_cyan_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "minecraft:recipes/decorations/cyan_candle": {"criteria": {"has_cyan_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_smart_cyan": {"criteria": {"has_dyes/black": "2025-07-10 10:57:18 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/cyan_brick_extra": {"criteria": {"has_dye": "2025-07-10 10:57:18 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass_cyan2": {"criteria": {"recipe_condition2": "2025-07-10 10:57:18 +0800"}, "done": true}, "minecraft:recipes/decorations/cyan_glazed_terracotta": {"criteria": {"has_cyan_terracotta": "2025-07-10 10:57:25 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cyan_glazed_terracotta": {"criteria": {"has_the_recipe": "2025-07-10 10:57:25 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_cooler": {"criteria": {"has_terracotta": "2025-07-10 10:57:25 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_glass_pane": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_gray_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "create:recipes/building_blocks/vertical_framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/green_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "xnet:recipes/xnet/facade": {"criteria": {"glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/brewing/glass_bottle": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purple_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge_pier": {"criteria": {"has_planks": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/black_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/wine_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/runic_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/decorations/glass_pane": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "mcwwindows:recipes/mosaic_glass": {"criteria": {"has_wood": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "create:recipes/building_blocks/horizontal_framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/red_shield_template_block": {"criteria": {"glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/glass_1x": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/lavender_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/brown_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecolonies:recipes/misc/large_empty_bottle": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "bloodmagic:recipes/misc/sacrificial_dagger": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge": {"criteria": {"has_planks": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/space_chamber_card": {"criteria": {"glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/yellow_shield_template_block": {"criteria": {"glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/green_shield_template_block": {"criteria": {"glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "littlelogistics:recipes/transportation/fluid_barge": {"criteria": {"has_item": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gray_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge_stair": {"criteria": {"has_planks": "2025-07-10 10:58:09 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/deorum_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/yellow_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "forbidden_arcanus:recipes/decorations/utrem_jar": {"criteria": {"has_glass/colorless": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/orange_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/red_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "create:recipes/building_blocks/framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lime_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/blue_shield_template_block": {"criteria": {"glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass1": {"criteria": {"recipe_condition": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/pink_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/dark_runic_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cyan_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "minecraft:recipes/building_blocks/magenta_stained_glass": {"criteria": {"has_glass": "2025-07-10 10:58:09 +0800"}, "done": true}, "create:recipes/building_blocks/tiled_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-10 10:58:09 +0800"}, "done": true}, "pylons:recipes/misc/clear_potion_filter": {"criteria": {"has_potion_filter": "2025-07-10 10:59:04 +0800"}, "done": true}, "aether:recipes/combat/diamond_sword_repairing": {"criteria": {"has_diamond_sword": "2025-07-10 11:01:58 +0800"}, "done": true}, "twilightdelight:recipes/frozen/cutting/ice_sword": {"criteria": {"has_diamond_sword": "2025-07-10 11:01:58 +0800"}, "done": true}, "ae2:recipes/misc/tools/portable_item_cell_64k": {"criteria": {"has_item_cell_housing": "2025-07-10 11:03:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/portable_item_cell_16k": {"criteria": {"has_item_cell_housing": "2025-07-10 11:03:20 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/item_storage_cell_1k_storage": {"criteria": {"has_item_cell_housing": "2025-07-10 11:03:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/portable_item_cell_256k": {"criteria": {"has_item_cell_housing": "2025-07-10 11:03:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/portable_item_cell_1k": {"criteria": {"has_item_cell_housing": "2025-07-10 11:03:20 +0800"}, "done": true}, "ae2:recipes/misc/tools/portable_item_cell_4k": {"criteria": {"has_item_cell_housing": "2025-07-10 11:03:20 +0800"}, "done": true}, "minecraft:nether/get_wither_skull": {"criteria": {"wither_skull": "2025-07-10 11:06:55 +0800"}, "done": true}, "minecraft:recipes/misc/skull_banner_pattern": {"criteria": {"has_wither_skeleton_skull": "2025-07-10 11:06:55 +0800"}, "done": true}, "travelersbackpack:recipes/misc/wither": {"criteria": {"has_wither_skeleton_skull": "2025-07-10 11:06:55 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/allthemodium_dust": {"criteria": {"has_allthemodium": "2025-07-10 11:06:58 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/vibranium_dust": {"criteria": {"has_vibranium": "2025-07-10 11:07:01 +0800"}, "done": true}, "alltheores:recipes/misc/enderium_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/enderium]_ingot": "2025-07-10 11:07:23 +0800"}, "done": true}, "alltheores:recipes/misc/enderium_block": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/enderium]_ingot": "2025-07-10 11:07:23 +0800"}, "done": true}, "alltheores:recipes/misc/enderium_nugget_from_ingot": {"criteria": {"has_item": "2025-07-10 11:07:23 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/enderium_dust_from_ingot": {"criteria": {"has_enderium_ingot": "2025-07-10 11:07:23 +0800"}, "done": true}, "alltheores:recipes/misc/enderium_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/enderium]_ingot": "2025-07-10 11:07:23 +0800"}, "done": true}, "alltheores:recipes/misc/enderium_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/enderium]_ingot": "2025-07-10 11:07:23 +0800"}, "done": true}, "croptopia:recipes/shaped_toast_sandwich": {"criteria": {"has_toast": "2025-07-10 11:07:57 +0800"}, "done": true}, "croptopia:recipes/buttered_toast": {"criteria": {"has_toast": "2025-07-10 11:12:09 +0800"}, "done": true}, "croptopia:recipes/toast_with_jam": {"criteria": {"has_toast": "2025-07-10 11:12:09 +0800"}, "done": true}, "botania:recipes/combat/manasteel_sword": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/obedience_stick": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/decorations/pump": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/ice_pendant": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/magnet_ring": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/combat/manasteel_leggings": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/manasteel_shovel": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/building_blocks/manasteel_block": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/travel_belt": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/monocle": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/sextant": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "delightful:recipes/knives/manasteel_knife": {"criteria": {"has_ingots/manasteel": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/glass_pickaxe": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/swap_ring": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/mining_ring": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/decorations/mana_fluxfield": {"criteria": {"has_alt_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/misc/conversions/manasteel_block_deconstruct": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/bauble_box": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/manasteel_hoe": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/knockback_belt": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/manasteel_axe": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/redstone/forest_eye": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/dodge_ring": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/combat/manasteel_chestplate": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/manasteel_shears": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/decorations/mana_distributor": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/lava_pendant": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/tools/manasteel_pick": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/combat/manasteel_helmet": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/combat/manasteel_boots": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "botania:recipes/misc/lens_normal": {"criteria": {"has_item": "2025-07-10 11:14:31 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/energizer/casing": {"criteria": {"has_item": "2025-07-10 11:14:37 +0800"}, "done": true}, "handcrafted:recipes/misc/golden_thin_pot": {"criteria": {"has_golden_thin_pot": "2025-07-10 11:14:37 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/gold_block_1x": {"criteria": {"has_gold_block": "2025-07-10 11:14:37 +0800"}, "done": true}, "handcrafted:recipes/misc/golden_thick_pot": {"criteria": {"has_golden_thick_pot": "2025-07-10 11:14:37 +0800"}, "done": true}, "handcrafted:recipes/misc/golden_medium_pot": {"criteria": {"has_golden_medium_pot": "2025-07-10 11:14:37 +0800"}, "done": true}, "minecraft:recipes/misc/gold_ingot_from_gold_block": {"criteria": {"has_gold_block": "2025-07-10 11:14:37 +0800"}, "done": true}, "handcrafted:recipes/misc/golden_wide_pot": {"criteria": {"has_golden_wide_pot": "2025-07-10 11:14:37 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/flippers_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/range_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "sgjourney:recipes/misc/sandstone_with_lapis": {"criteria": {"has_lapis": "2025-07-10 11:14:53 +0800"}, "done": true}, "minecraft:recipes/misc/blue_dye": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/stomp_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/standby_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/armor_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "alchemistry:recipes/dissolver/lapis_block": {"criteria": {"has_the_recipe": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/inventory_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "supplementaries:recipes/lapis_bricks": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/coordinate_tracker_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "modularrouters:recipes/misc/blank_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/speed_upgrade_from_glycerol": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/ender_visor_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/speed_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "travelersbackpack:recipes/misc/lapis": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/dispenser_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/charging_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/magnet_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/volume_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/item_life_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/gilded_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/security_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/search_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lapis_block": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/block_tracker_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/lapis_dust_from_gem": {"criteria": {"has_lapis_gem": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/minigun_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/entity_tracker_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/elytra_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-10 11:14:53 +0800"}, "done": true}, "minecraft:story/enter_the_end": {"criteria": {"entered_end": "2025-07-10 11:21:32 +0800"}, "done": true}, "silentgear:the_end": {"criteria": {"entered_the_end": "2025-07-10 11:21:32 +0800"}, "done": true}, "minecraft:end/root": {"criteria": {"entered_end": "2025-07-10 11:21:32 +0800"}, "done": true}, "ends_delight:root": {"criteria": {"the_end": "2025-07-10 11:21:32 +0800"}, "done": true}, "aether:recipes/combat/netherite_helmet_repairing": {"criteria": {"has_netherite_helmet": "2025-07-10 11:24:58 +0800"}, "done": true}, "apotheosis:affix/mythic": {"criteria": {"sword": "2025-07-10 11:24:58 +0800"}, "done": true}, "create:recipes/combat/crafting/appliances/netherite_diving_helmet_from_netherite": {"criteria": {"has_item": "2025-07-10 11:24:58 +0800"}, "done": true}, "apotheosis:affix/boss": {"criteria": {"sword": "2025-07-10 11:24:58 +0800"}, "done": true}, "wirelesschargers:recipes/misc/basic_wireless_block_charger": {"criteria": {"recipe_condition": "2025-07-10 11:27:06 +0800"}, "done": true}, "travelersbackpack:recipes/misc/enderman": {"criteria": {"has_ender_pearl": "2025-07-10 11:27:06 +0800"}, "done": true}, "entangled:recipes/misc/block": {"criteria": {"recipe_condition": "2025-07-10 11:27:06 +0800"}, "done": true}, "itemcollectors:recipes/misc/basic_collector": {"criteria": {"recipe_condition": "2025-07-10 11:27:06 +0800"}, "done": true}, "allthetweaks:recipes/misc/ender_pearl_block": {"criteria": {"has_ender_pearl": "2025-07-10 11:27:06 +0800"}, "done": true}, "shrink:recipes/tools/shrinking_device": {"criteria": {"has_enderpearls": "2025-07-10 11:27:06 +0800"}, "done": true}, "wirelesschargers:recipes/misc/basic_wireless_player_charger": {"criteria": {"recipe_condition": "2025-07-10 11:27:06 +0800"}, "done": true}, "entangled:recipes/misc/item": {"criteria": {"recipe_condition": "2025-07-10 11:27:06 +0800"}, "done": true}, "croptopia:recipes/fruit_smoothie": {"criteria": {"has_fruit": "2025-07-10 11:27:12 +0800"}, "done": true}, "alchemistry:recipes/dissolver/popped_chorus_fruit": {"criteria": {"has_the_recipe": "2025-07-10 11:27:12 +0800"}, "done": true}, "minecraft:recipes/misc/popped_chorus_fruit": {"criteria": {"has_chorus_fruit": "2025-07-10 11:27:12 +0800"}, "done": true}, "aether:recipes/combat/diamond_chestplate_repairing": {"criteria": {"has_diamond_chestplate": "2025-07-10 11:27:23 +0800"}, "done": true}, "voidtotem:recipes/combat/totem_of_void_undying": {"criteria": {"has_totem": "2025-07-10 11:27:23 +0800"}, "done": true}, "repurposed_structures:shipwrecks": {"criteria": {"in_shipwreck_end": "2025-07-10 11:28:21 +0800"}, "done": false}, "xnet:recipes/xnet/connector_upgrade": {"criteria": {"pearl": "2025-07-10 11:28:28 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_destruction": {"criteria": {"has_ender_pearl": "2025-07-10 11:28:28 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/charged_porter": {"criteria": {"pearl": "2025-07-10 11:28:28 +0800"}, "done": true}, "allthemodium:recipes/misc/teleport_pad": {"criteria": {"has_ender_pearl": "2025-07-10 11:28:28 +0800"}, "done": true}, "dimstorage:recipes/misc/dim_wall": {"criteria": {"has_ender_pearl": "2025-07-10 11:28:28 +0800"}, "done": true}, "railcraft:recipes/misc/world_spike": {"criteria": {"has_ender_pearl": "2025-07-10 11:28:28 +0800"}, "done": true}, "railcraft:recipes/misc/personal_world_spike": {"criteria": {"has_ender_pearl": "2025-07-10 11:28:28 +0800"}, "done": true}, "aether:recipes/tools/netherite_axe_repairing": {"criteria": {"has_netherite_axe": "2025-07-10 11:28:37 +0800"}, "done": true}, "aether:recipes/tools/netherite_shovel_repairing": {"criteria": {"has_netherite_shovel": "2025-07-10 11:33:32 +0800"}, "done": true}, "supplementaries:recipes/end_stone_lamp": {"criteria": {"has_end_stone": "2025-07-10 11:36:17 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/end_stone_1x": {"criteria": {"has_end_stone": "2025-07-10 11:36:17 +0800"}, "done": true}, "minecraft:recipes/building_blocks/end_stone_bricks_from_end_stone_stonecutting": {"criteria": {"has_end_stone": "2025-07-10 11:36:17 +0800"}, "done": true}, "alchemistry:recipes/dissolver/end_stone_bricks": {"criteria": {"has_the_recipe": "2025-07-10 11:36:17 +0800"}, "done": true}, "minecraft:recipes/building_blocks/end_stone_bricks": {"criteria": {"has_end_stone": "2025-07-10 11:36:17 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/end_stone_dust": {"criteria": {"has_end_stone": "2025-07-10 11:36:17 +0800"}, "done": true}, "additionallanterns:recipes/misc/end_stone_chain": {"criteria": {"recipe_condition": "2025-07-10 11:36:17 +0800"}, "done": true}, "minecraft:recipes/building_blocks/end_stone_brick_stairs_from_end_stone_stonecutting": {"criteria": {"has_end_stone": "2025-07-10 11:36:17 +0800"}, "done": true}, "minecraft:recipes/building_blocks/end_stone_brick_slab_from_end_stone_stonecutting": {"criteria": {"has_end_stone": "2025-07-10 11:36:17 +0800"}, "done": true}, "minecraft:recipes/decorations/end_stone_brick_wall_from_end_stone_stonecutting": {"criteria": {"has_end_stone": "2025-07-10 11:36:17 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/shield": {"criteria": {"has_shield": "2025-07-10 11:37:34 +0800"}, "done": true}, "aether:recipes/combat/shield_repairing": {"criteria": {"has_shield": "2025-07-10 11:37:34 +0800"}, "done": true}, "utilitix:recipes/misc/acacia_shulker_boat_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "utilitix:recipes/misc/bamboo_shulker_raft_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "utilitix:recipes/misc/dark_oak_shulker_boat_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "utilitix:recipes/misc/spruce_shulker_boat_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "utilitix:recipes/misc/mangrove_shulker_boat_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "minecraft:recipes/decorations/shulker_box": {"criteria": {"has_shulker_shell": "2025-07-10 11:39:50 +0800"}, "done": true}, "utilitix:recipes/misc/oak_shulker_boat_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "utilitix:recipes/misc/cherry_shulker_boat_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/shulker_box": {"criteria": {"has_shulker_shell": "2025-07-10 11:39:50 +0800"}, "done": true}, "utilitix:recipes/misc/jungle_shulker_boat_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "utilitix:recipes/misc/birch_shulker_boat_with_shell": {"criteria": {"criterion0": "2025-07-10 11:39:50 +0800"}, "done": true}, "aether:recipes/combat/diamond_leggings_repairing": {"criteria": {"has_diamond_leggings": "2025-07-10 11:39:57 +0800"}, "done": true}, "lootr:1shulker": {"criteria": {"opened_shulker": "2025-07-10 12:08:07 +0800"}, "done": true}, "lootr:50loot": {"criteria": {"loot_stat": "2025-07-10 12:08:07 +0800"}, "done": true}, "minecraft:end/find_end_city": {"criteria": {"in_city": "2025-07-10 12:09:06 +0800"}, "done": true}, "aether:recipes/combat/diamond_gloves_repairing": {"criteria": {"has_diamond_gloves": "2025-07-10 12:15:27 +0800"}, "done": true}, "aether:recipes/combat/netherite_boots_repairing": {"criteria": {"has_netherite_boots": "2025-07-10 12:17:41 +0800"}, "done": true}, "create:recipes/combat/crafting/appliances/netherite_diving_boots_from_netherite": {"criteria": {"has_item": "2025-07-10 12:17:41 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade": {"criteria": {"has_backpack_upgrade": "2025-07-10 12:24:18 +0800"}, "done": true}, "minecraft:recipes/building_blocks/end_stone_brick_slab_from_end_stone_brick_stonecutting": {"criteria": {"has_end_stone_brick": "2025-07-10 12:26:29 +0800"}, "done": true}, "mcwbridges:recipes/end_stone_bricks_bridge_pier": {"criteria": {"has_planks": "2025-07-10 12:26:29 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_end_stone_bricks_bridge": {"criteria": {"has_planks": "2025-07-10 12:26:29 +0800"}, "done": true}, "minecraft:recipes/decorations/end_stone_brick_wall_from_end_stone_brick_stonecutting": {"criteria": {"has_end_stone_brick": "2025-07-10 12:26:29 +0800"}, "done": true}, "minecraft:recipes/building_blocks/end_stone_brick_stairs_from_end_stone_brick_stonecutting": {"criteria": {"has_end_stone_brick": "2025-07-10 12:26:29 +0800"}, "done": true}, "alchemistry:recipes/dissolver/end_stone_brick_slab": {"criteria": {"has_the_recipe": "2025-07-10 12:26:29 +0800"}, "done": true}, "mcwbridges:recipes/end_stone_bricks_bridge": {"criteria": {"has_planks": "2025-07-10 12:26:29 +0800"}, "done": true}, "mcwbridges:recipes/end_stone_bricks_bridge_stair": {"criteria": {"has_planks": "2025-07-10 12:26:29 +0800"}, "done": true}, "minecraft:recipes/building_blocks/end_stone_brick_slab": {"criteria": {"has_end_stone_bricks": "2025-07-10 12:26:29 +0800"}, "done": true}, "minecraft:recipes/building_blocks/end_stone_brick_stairs": {"criteria": {"has_end_stone_bricks": "2025-07-10 12:26:29 +0800"}, "done": true}, "minecraft:recipes/decorations/end_stone_brick_wall": {"criteria": {"has_end_stone_bricks": "2025-07-10 12:26:29 +0800"}, "done": true}, "croptopia:eatcrafted": {"criteria": {"eat_crafted": "2025-07-10 12:27:39 +0800"}, "done": true}, "apotheosis:enchanting/enchanter": {"criteria": {"enchanting_table": "2025-07-10 20:41:16 +0800"}, "done": true}, "alchemistry:recipes/dissolver/blaze_powder": {"criteria": {"has_the_recipe": "2025-07-10 20:41:55 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/blaze_powder_from_rod": {"criteria": {"has_blaze_rod": "2025-07-10 20:41:55 +0800"}, "done": true}, "travelersbackpack:recipes/misc/blaze": {"criteria": {"has_blaze_rod": "2025-07-10 20:41:55 +0800"}, "done": true}, "botania:recipes/misc/conversions/blazeblock_deconstruct": {"criteria": {"has_item": "2025-07-10 20:41:55 +0800"}, "done": true}, "minecraft:nether/obtain_blaze_rod": {"criteria": {"blaze_rod": "2025-07-10 20:41:55 +0800"}, "done": true}, "minecraft:recipes/brewing/brewing_stand": {"criteria": {"has_blaze_rod": "2025-07-10 20:41:55 +0800"}, "done": true}, "botania:recipes/building_blocks/blaze_block": {"criteria": {"has_item": "2025-07-10 20:41:55 +0800"}, "done": true}, "minecraft:recipes/brewing/blaze_powder": {"criteria": {"has_blaze_rod": "2025-07-10 20:41:55 +0800"}, "done": true}, "minecraft:recipes/redstone/lectern": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_lectern": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_bookshelf": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/manual": {"criteria": {"book": "2025-07-10 20:42:32 +0800"}, "done": true}, "aether:recipes/misc/book_of_lore": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/comet_bookshelf": {"criteria": {"has_item": "2025-07-10 20:42:32 +0800"}, "done": true}, "croptopia:recipes/documentation": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/manual": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/dusk_bookshelf": {"criteria": {"has_item": "2025-07-10 20:42:32 +0800"}, "done": true}, "aether:recipes/building_blocks/skyroot_bookshelf": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "botania:recipes/tools/lexicon": {"criteria": {"has_alt_item": "2025-07-10 20:42:32 +0800"}, "done": true}, "handcrafted:recipes/misc/stackable_book": {"criteria": {"has_stackable_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/bluebright_bookshelf": {"criteria": {"has_item": "2025-07-10 20:42:32 +0800"}, "done": true}, "enderio:recipes/misc/enchanter": {"criteria": {"has_ingredient": "2025-07-10 20:42:32 +0800"}, "done": true}, "eccentrictome:recipes/tools/tome": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "minecraft:recipes/misc/writable_book": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_chiseled_bookshelf": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/frostbright_bookshelf": {"criteria": {"has_item": "2025-07-10 20:42:32 +0800"}, "done": true}, "minecraft:recipes/building_blocks/bookshelf": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/starlit_bookshelf": {"criteria": {"has_item": "2025-07-10 20:42:32 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_bookshelf": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/lunar_bookshelf": {"criteria": {"has_item": "2025-07-10 20:42:32 +0800"}, "done": true}, "travelersbackpack:recipes/misc/bookshelf": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/maple_bookshelf": {"criteria": {"has_item": "2025-07-10 20:42:32 +0800"}, "done": true}, "occultism:recipes/spirit_fire/spirit_fire/taboo_book": {"criteria": {"has_book": "2025-07-10 20:42:32 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_lectern": {"criteria": {"has_bookshelf": "2025-07-10 20:44:02 +0800"}, "done": true}, "apotheosis:enchanting/bookshelf": {"criteria": {"bookshelf": "2025-07-10 20:44:02 +0800"}, "done": true}, "minecraft:recipes/brewing/magma_cream": {"criteria": {"has_blaze_powder": "2025-07-10 20:44:15 +0800"}, "done": true}, "silentgear:recipes/misc/blaze_gold_ingot": {"criteria": {"has_item": "2025-07-10 20:44:15 +0800"}, "done": true}, "alchemistry:recipes/misc/reactor_casing": {"criteria": {"has_item": "2025-07-10 20:44:15 +0800"}, "done": true}, "minecraft:recipes/misc/fire_charge": {"criteria": {"has_blaze_powder": "2025-07-10 20:44:15 +0800"}, "done": true}, "minecraft:recipes/misc/ender_eye": {"criteria": {"has_blaze_powder": "2025-07-10 20:44:15 +0800"}, "done": true}, "botania:recipes/decorations/spark": {"criteria": {"has_item": "2025-07-10 20:44:15 +0800"}, "done": true}, "travelersbackpack:recipes/misc/ghast": {"criteria": {"has_ghast_tear": "2025-07-10 20:45:46 +0800"}, "done": true}, "botania:recipes/tools/blood_pendant": {"criteria": {"has_item": "2025-07-10 20:45:46 +0800"}, "done": true}, "mythicbotany:recipes/misc/alfsteel_pylon": {"criteria": {"criterion2": "2025-07-10 20:45:46 +0800"}, "done": true}, "botania:recipes/misc/incense_stick": {"criteria": {"has_item": "2025-07-10 20:45:46 +0800"}, "done": true}, "chimes:recipes/decorations/glass_bells": {"criteria": {"has_bottle": "2025-07-10 20:46:36 +0800"}, "done": true}, "minecraft:nether/brew_potion": {"criteria": {"potion": "2025-07-10 20:48:17 +0800"}, "done": true}, "croptopia:recipes/shaped_netherwart_stew": {"criteria": {"has_nether_wart": "2025-07-10 20:48:42 +0800"}, "done": true}, "reliquary:recipes/misc/glowing_water": {"criteria": {"has_nether_wart": "2025-07-10 20:48:42 +0800"}, "done": true}, "minecraft:recipes/building_blocks/nether_wart_block": {"criteria": {"has_nether_wart": "2025-07-10 20:48:42 +0800"}, "done": true}, "minecraft:recipes/building_blocks/red_nether_bricks": {"criteria": {"has_nether_wart": "2025-07-10 20:48:42 +0800"}, "done": true}, "alchemistry:recipes/dissolver/red_nether_bricks": {"criteria": {"has_the_recipe": "2025-07-10 20:48:42 +0800"}, "done": true}, "travelersbackpack:recipes/misc/nether": {"criteria": {"has_nether_wart": "2025-07-10 20:48:42 +0800"}, "done": true}, "botania:recipes/decorations/brewery": {"criteria": {"has_alt_item": "2025-07-10 20:49:23 +0800"}, "done": true}, "botania:recipes/decorations/alchemy_catalyst": {"criteria": {"has_alt_item": "2025-07-10 20:49:23 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_nether_bricks_bridge": {"criteria": {"has_planks": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/decorations/nether_brick_fence": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/decorations/nether_brick_wall_from_nether_bricks_stonecutting": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "mcwroofs:recipes/nether_bricks": {"criteria": {"has_planks": "2025-07-10 20:49:56 +0800"}, "done": true}, "mcwbridges:recipes/nether_bricks_bridge": {"criteria": {"has_planks": "2025-07-10 20:49:56 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cracked_nether_bricks": {"criteria": {"has_the_recipe": "2025-07-10 20:49:56 +0800"}, "done": true}, "mcwbridges:recipes/nether_bricks_bridge_stair": {"criteria": {"has_planks": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/decorations/nether_brick_wall": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "mcwbridges:recipes/nether_bricks_bridge_pier": {"criteria": {"has_planks": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_nether_bricks_from_nether_bricks_stonecutting": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "nethersdelight:recipes/decorations/nether_brick_smoker": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/building_blocks/nether_brick_stairs_from_nether_bricks_stonecutting": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cracked_nether_bricks": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/building_blocks/nether_brick_slab_from_nether_bricks_stonecutting": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/building_blocks/nether_brick_stairs": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_nether_bricks_chain": {"criteria": {"recipe_condition": "2025-07-10 20:49:56 +0800"}, "done": true}, "alchemistry:recipes/dissolver/nether_brick_slab": {"criteria": {"has_the_recipe": "2025-07-10 20:49:56 +0800"}, "done": true}, "minecraft:recipes/building_blocks/nether_brick_slab": {"criteria": {"has_nether_bricks": "2025-07-10 20:49:56 +0800"}, "done": true}, "apotheosis:enchanting/hellshelf": {"criteria": {"hellshelf": "2025-07-10 20:50:04 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/energizer/chargingport_fe": {"criteria": {"has_item2": "2025-07-10 20:57:04 +0800"}, "done": true}, "alchemistry:recipes/dissolver/lapis_lazuli": {"criteria": {"has_the_recipe": "2025-07-10 20:57:04 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/lapis_block_1x": {"criteria": {"has_lapis_block": "2025-07-10 20:57:04 +0800"}, "done": true}, "minecraft:recipes/misc/lapis_lazuli": {"criteria": {"has_lapis_block": "2025-07-10 20:57:04 +0800"}, "done": true}, "mekanism:fluid_tank": {"criteria": {"basic_fluid_tank": "2025-07-10 21:03:10 +0800"}, "done": true}, "minecraft:recipes/misc/netherite_upgrade_smithing_template": {"criteria": {"has_netherite_upgrade_smithing_template": "2025-07-10 21:05:09 +0800"}, "done": true}, "silentgear:recipes/misc/coating_smithing_template": {"criteria": {"has_item": "2025-07-10 21:05:09 +0800"}, "done": true}, "minecraft:recipes/misc/netherite_drill_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "farmersdelight:recipes/combat/netherite_knife_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/tools/netherite_axe_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/tools/netherite_pickaxe_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/decorations/lodestone": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "functionalstorage:recipes/misc/netherite_upgrade": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/combat/netherite_sword_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/combat/netherite_chestplate_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "aether:recipes/combat/netherite_gloves_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/tools/netherite_machete_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/combat/netherite_helmet_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "aeinfinitybooster:recipes/misc/infinity_card": {"criteria": {"has_item": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/tools/netherite_shovel_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "alchemistry:recipes/dissolver/netherite_block": {"criteria": {"has_the_recipe": "2025-07-10 21:06:14 +0800"}, "done": true}, "travelersbackpack:recipes/misc/netherite_tier_upgrade": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "supplementaries:recipes/safe": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "additionallanterns:recipes/misc/netherite_chain": {"criteria": {"recipe_condition": "2025-07-10 21:06:14 +0800"}, "done": true}, "travelersbackpack:recipes/misc/netherite": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/combat/netherite_leggings_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/netherite_dust_from_ingot": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/netherite_block": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "rsinfinitybooster:recipes/misc/infinity_card": {"criteria": {"has_item": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/combat/netherite_boots_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "minecraft:recipes/tools/netherite_hoe_smithing": {"criteria": {"has_netherite_ingot": "2025-07-10 21:06:14 +0800"}, "done": true}, "create:recipes/combat/crafting/appliances/netherite_backtank_from_netherite": {"criteria": {"has_item": "2025-07-10 21:06:42 +0800"}, "done": true}, "aether:recipes/combat/netherite_chestplate_repairing": {"criteria": {"has_netherite_chestplate": "2025-07-10 21:06:42 +0800"}, "done": true}, "minecraft:nether/netherite_armor": {"criteria": {"netherite_armor": "2025-07-10 21:07:05 +0800"}, "done": true}, "aether:recipes/combat/netherite_leggings_repairing": {"criteria": {"has_netherite_leggings": "2025-07-10 21:07:05 +0800"}, "done": true}, "minecraft:recipes/combat/allthemodium_mage_chestplate_smithing": {"criteria": {"allthewizardgear:has_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "minecraft:recipes/combat/allthemodium_spell_book_smithing": {"criteria": {"allthewizardgearhas_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "minecraft:recipes/combat/allthemodium_mage_leggings_smithing": {"criteria": {"allthewizardgear:has_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthemodium:recipes/misc/allthemodium_nugget_from_ingot": {"criteria": {"has_item": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthearcanistgear:recipes/elemental_hat_to_allthemodium_hat_smithing": {"criteria": {"allthearcanistgear:has_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthearcanistgear:recipes/elemental_boots_to_allthemodium_boots_smithing": {"criteria": {"allthearcanistgear:has_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/allthemodium_dust_from_ingot": {"criteria": {"has_allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthearcanistgear:recipes/elemental_legs_to_allthemodium_leggings_smithing": {"criteria": {"allthearcanistgear:has_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthemodium:recipes/misc/allthemodium_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/allthemodium]_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "minecraft:recipes/combat/allthemodium_mage_helmet_smithing": {"criteria": {"allthewizardgear:has_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "minecraft:recipes/combat/allthemodium_mage_boots_smithing": {"criteria": {"allthewizardgear:has_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthemodium:recipes/misc/allthemodium_block": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/allthemodium]_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthearcanistgear:recipes/elemental_chest_to_allthemodium_robes_smithing": {"criteria": {"allthearcanistgear:has_ingots/allthemodium_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthemodium:recipes/misc/allthemodium_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/allthemodium]_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthemodium:recipes/misc/allthemodium_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/allthemodium]_ingot": "2025-07-10 21:07:23 +0800"}, "done": true}, "allthemodium:recipes/misc/ancient_stone_slabs": {"criteria": {"has_TagKey[minecraft:block / allthemodium:ancient_stone]_block": "2025-07-10 21:11:42 +0800"}, "done": true}, "allthemodium:recipes/misc/ancient_smooth_stone_from_ancient_stone_blasting": {"criteria": {"has_item": "2025-07-10 21:11:42 +0800"}, "done": true}, "allthemodium:recipes/misc/ancient_stone_wall": {"criteria": {"has_TagKey[minecraft:block / allthemodium:ancient_stone]_block": "2025-07-10 21:11:42 +0800"}, "done": true}, "allthemodium:recipes/misc/ancient_smooth_stone_from_ancient_stone_smelting": {"criteria": {"has_item": "2025-07-10 21:11:42 +0800"}, "done": true}, "allthemodium:recipes/misc/ancient_stone_stairs": {"criteria": {"has_TagKey[minecraft:block / allthemodium:ancient_stone]_block": "2025-07-10 21:11:42 +0800"}, "done": true}, "allthemodium:recipes/misc/ancient_stone_bricks": {"criteria": {"has_TagKey[minecraft:block / allthemodium:ancient_stone]_block": "2025-07-10 21:11:42 +0800"}, "done": true}, "allthemodium:recipes/misc/ancient_mossy_stone_from_vinecrafting": {"criteria": {"has_item": "2025-07-10 21:11:42 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/ancient_stone_1x": {"criteria": {"has_ancient_stone": "2025-07-10 21:11:42 +0800"}, "done": true}, "farmersdelight:recipes/cooking/bone_broth": {"criteria": {"has_bone": "2025-07-10 21:27:02 +0800"}, "done": true}, "mcwtrpdoors:recipes/print_whispering": {"criteria": {"has_logs": "2025-07-10 21:27:02 +0800"}, "done": true}, "travelersbackpack:recipes/misc/wolf": {"criteria": {"has_bone": "2025-07-10 21:27:02 +0800"}, "done": true}, "alchemistry:recipes/dissolver/bone_meal": {"criteria": {"has_the_recipe": "2025-07-10 21:27:02 +0800"}, "done": true}, "minecraft:recipes/misc/bone_meal": {"criteria": {"has_bone": "2025-07-10 21:27:02 +0800"}, "done": true}, "delightful:recipes/knives/bone_knife": {"criteria": {"has_bones": "2025-07-10 21:27:02 +0800"}, "done": true}, "tombstone:adventure/fortuitous_militia": {"criteria": {"coded_trigger": "2025-07-10 21:35:47 +0800"}, "done": true}, "minecraft:adventure/voluntary_exile": {"criteria": {"voluntary_exile": "2025-07-10 21:40:35 +0800"}, "done": true}, "apotheosis:enchanting/quanta50": {"criteria": {"enchanted_item": "2025-07-11 09:21:36 +0800"}, "done": true}, "apotheosis:enchanting/30ench": {"criteria": {"enchanted_item": "2025-07-11 09:21:36 +0800"}, "done": true}, "minecraft:story/enchant_item": {"criteria": {"enchanted_item": "2025-07-11 09:21:36 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/glowstone_1x": {"criteria": {"has_glowstone": "2025-07-11 09:26:54 +0800"}, "done": true}, "mcwlights:recipes/glowstone_slab": {"criteria": {"has_wood": "2025-07-11 09:26:54 +0800"}, "done": true}, "mcwlights:recipes/lamps_ceilings": {"criteria": {"has_the_recipe": "2025-07-11 09:26:54 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_lamp": {"criteria": {"has_glowstone": "2025-07-11 09:26:54 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/glow_ink_sac": {"criteria": {"has_glowstone": "2025-07-11 09:26:54 +0800"}, "done": true}, "apotheosis:enchanting/upgrade_hellshelf": {"criteria": {"glowing": "2025-07-11 09:27:04 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/lavender_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/wine_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_candle": {"criteria": {"has_candle": "2025-07-11 09:33:42 +0800"}, "done": true}, "minecraft:recipes/decorations/honeycomb_block": {"criteria": {"has_honeycomb": "2025-07-11 09:34:00 +0800"}, "done": true}, "travelersbackpack:recipes/misc/bee": {"criteria": {"has_honeycomb": "2025-07-11 09:34:00 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_beehive": {"criteria": {"has_honeycomb": "2025-07-11 09:34:00 +0800"}, "done": true}, "minecraft:recipes/decorations/beehive": {"criteria": {"has_honeycomb": "2025-07-11 09:34:00 +0800"}, "done": true}, "alchemistry:recipes/dissolver/honeycomb_block": {"criteria": {"has_the_recipe": "2025-07-11 09:34:00 +0800"}, "done": true}, "apotheosis:enchanting/60ench": {"criteria": {"enchanted_item": "2025-07-11 09:34:45 +0800"}, "done": true}, "apotheosis:enchanting/enchanting_stats": {"criteria": {"enchanted_item": "2025-07-11 09:34:45 +0800"}, "done": true}, "apotheosis:enchanting/deepshelf": {"criteria": {"deepshelf": "2025-07-11 09:34:46 +0800"}, "done": true}, "deeperdarker:main/root": {"criteria": {"deep_dark": "2025-07-11 09:43:39 +0800"}, "done": true}, "deeperdarker:main/find_ancient_city": {"criteria": {"ancient_city": "2025-07-11 09:43:56 +0800"}, "done": true}, "aether:recipes/tools/diamond_hoe_repairing": {"criteria": {"has_diamond_hoe": "2025-07-11 09:44:38 +0800"}, "done": true}, "croptopia:root": {"criteria": {"root": "2025-07-11 09:44:38 +0800"}, "done": true}, "minecraft:recipes/misc/mojang_banner_pattern": {"criteria": {"has_enchanted_golden_apple": "2025-07-11 09:45:47 +0800"}, "done": true}, "deeperdarker:recipes/misc/reinforced_echo_shard": {"criteria": {"has_warden_carapace": "2025-07-11 09:47:32 +0800"}, "done": true}, "deeperdarker:main/kill_warden": {"criteria": {"warden": "2025-07-11 09:55:38 +0800"}, "done": true}, "deeperdarker:recipes/combat/sonorous_staff": {"criteria": {"has_heart_of_the_deep": "2025-07-11 09:55:38 +0800"}, "done": true}, "minecraft:adventure/kill_mob_near_sculk_catalyst": {"criteria": {"kill_mob_near_sculk_catalyst": "2025-07-11 09:55:38 +0800"}, "done": true}, "allthemodium:recipes/misc/vibranium_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/vibranium]_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "allthemodium:recipes/misc/vibranium_block": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/vibranium]_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "minecraft:recipes/combat/vibranium_spell_book_smithing": {"criteria": {"allthewizardgearhas_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "allthemodium:recipes/misc/vibranium_nugget_from_ingot": {"criteria": {"has_item": "2025-07-11 09:58:15 +0800"}, "done": true}, "minecraft:recipes/combat/vibranium_mage_boots_smithing": {"criteria": {"allthewizardgearhas_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "allthearcanistgear:recipes/vibranium_hat_smithing": {"criteria": {"allthearcanistgear:has_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "minecraft:recipes/combat/vibranium_mage_leggings_smithing": {"criteria": {"allthewizardgearhas_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/vibranium_dust_from_ingot": {"criteria": {"has_vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "minecraft:recipes/combat/vibranium_mage_chestplate_smithing": {"criteria": {"allthewizardgearhas_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "allthemodium:recipes/misc/vibranium_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/vibranium]_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "allthearcanistgear:recipes/vibranium_boots_smithing": {"criteria": {"allthearcanistgear:has_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "allthearcanistgear:recipes/vibranium_leggings_smithing": {"criteria": {"allthearcanistgear:has_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "allthemodium:recipes/misc/vibranium_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/vibranium]_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "allthearcanistgear:recipes/vibranium_robes_smithing": {"criteria": {"allthearcanistgear:has_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "minecraft:recipes/combat/vibranium_mage_helmet_smithing": {"criteria": {"allthewizardgearhas_ingots/vibranium_ingot": "2025-07-11 09:58:15 +0800"}, "done": true}, "travelersbackpack:recipes/misc/warden": {"criteria": {"has_echo_shard": "2025-07-11 09:59:55 +0800"}, "done": true}, "minecraft:recipes/tools/recovery_compass": {"criteria": {"has_echo_shard": "2025-07-11 09:59:55 +0800"}, "done": true}, "apotheosis:enchanting/sculkshelf": {"criteria": {"echoing": "2025-07-11 10:05:14 +0800"}, "done": true}, "minecraft:end/dragon_breath": {"criteria": {"dragon_breath": "2025-07-11 10:17:55 +0800"}, "done": true}, "travelersbackpack:recipes/misc/dragon": {"criteria": {"has_dragon_breath": "2025-07-11 10:17:55 +0800"}, "done": true}, "apotheosis:enchanting/80ench": {"criteria": {"enchanted_item": "2025-07-11 10:18:13 +0800"}, "done": true}, "apotheosis:enchanting/arcana50": {"criteria": {"enchanted_item": "2025-07-11 10:18:13 +0800"}, "done": true}, "apotheosis:enchanting/endshelf": {"criteria": {"endshelf": "2025-07-11 10:26:29 +0800"}, "done": true}, "apotheosis:enchanting/upgrade_endshelf": {"criteria": {"draconic": "2025-07-11 10:27:18 +0800"}, "done": true}, "apotheosis:enchanting/seashelf": {"criteria": {"seashelf": "2025-07-11 10:29:24 +0800"}, "done": true}, "alchemistry:recipes/dissolver/dark_prismarine": {"criteria": {"has_the_recipe": "2025-07-11 10:31:36 +0800"}, "done": true}, "alchemistry:recipes/dissolver/prismarine": {"criteria": {"has_the_recipe": "2025-07-11 10:31:36 +0800"}, "done": true}, "alchemistry:recipes/dissolver/prismarine_bricks": {"criteria": {"has_the_recipe": "2025-07-11 10:31:36 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_bricks": {"criteria": {"has_prismarine_shard": "2025-07-11 10:31:36 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reprocessor/controller": {"criteria": {"has_item2": "2025-07-11 10:31:36 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dark_prismarine": {"criteria": {"has_prismarine_shard": "2025-07-11 10:31:36 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine": {"criteria": {"has_prismarine_shard": "2025-07-11 10:31:36 +0800"}, "done": true}, "mcwroofs:recipes/prismarine": {"criteria": {"has_planks": "2025-07-11 10:31:43 +0800"}, "done": true}, "mcwbridges:recipes/prismarine_bricks_bridge_pier": {"criteria": {"has_planks": "2025-07-11 10:31:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_brick_slab_from_prismarine_stonecutting": {"criteria": {"has_prismarine_brick": "2025-07-11 10:31:43 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_prismarine_bricks_bridge": {"criteria": {"has_planks": "2025-07-11 10:31:43 +0800"}, "done": true}, "mcwbridges:recipes/prismarine_bricks_bridge_stair": {"criteria": {"has_planks": "2025-07-11 10:31:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_brick_stairs_from_prismarine_stonecutting": {"criteria": {"has_prismarine_brick": "2025-07-11 10:31:43 +0800"}, "done": true}, "additionallanterns:recipes/misc/prismarine_chain": {"criteria": {"recipe_condition": "2025-07-11 10:31:43 +0800"}, "done": true}, "mcwbridges:recipes/prismarine_bricks_bridge": {"criteria": {"has_planks": "2025-07-11 10:31:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_brick_slab": {"criteria": {"has_prismarine_bricks": "2025-07-11 10:31:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_brick_stairs": {"criteria": {"has_prismarine_bricks": "2025-07-11 10:31:43 +0800"}, "done": true}, "mekanism:factory": {"criteria": {"factory": "2025-07-11 10:42:38 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purpur_block": {"criteria": {"has_chorus_fruit_popped": "2025-07-11 10:59:19 +0800"}, "done": true}, "alchemistry:recipes/dissolver/purpur_block": {"criteria": {"has_the_recipe": "2025-07-11 10:59:19 +0800"}, "done": true}, "minecraft:recipes/decorations/end_rod": {"criteria": {"has_chorus_fruit_popped": "2025-07-11 10:59:19 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purpur_slab": {"criteria": {"has_purpur_block": "2025-07-11 10:59:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purpur_stairs": {"criteria": {"has_purpur_block": "2025-07-11 10:59:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purpur_slab_from_purpur_block_stonecutting": {"criteria": {"has_purpur_block": "2025-07-11 10:59:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/purpur_slab": {"criteria": {"has_the_recipe": "2025-07-11 10:59:22 +0800"}, "done": true}, "additionallanterns:recipes/misc/purpur_chain": {"criteria": {"recipe_condition": "2025-07-11 10:59:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purpur_pillar": {"criteria": {"has_purpur_block": "2025-07-11 10:59:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/purpur_pillar": {"criteria": {"has_the_recipe": "2025-07-11 10:59:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purpur_pillar_from_purpur_block_stonecutting": {"criteria": {"has_purpur_block": "2025-07-11 10:59:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purpur_stairs_from_purpur_block_stonecutting": {"criteria": {"has_purpur_block": "2025-07-11 10:59:22 +0800"}, "done": true}, "supplementaries:recipes/ash_brick": {"criteria": {"forge:ash": "2025-07-11 11:14:46 +0800"}, "done": true}, "supplementaries:recipes/soap": {"criteria": {"forge:ash": "2025-07-11 11:14:46 +0800"}, "done": true}, "aether:recipes/tools/flint_and_steel_repairing": {"criteria": {"has_flint_and_steel": "2025-07-11 11:15:45 +0800"}, "done": true}, "minecraft:adventure/avoid_vibration": {"criteria": {"avoid_vibration": "2025-07-11 11:15:59 +0800"}, "done": true}, "undergarden:undergarden/enter_undergarden": {"criteria": {"enter_undergarden": "2025-07-11 11:17:52 +0800"}, "done": true}, "undergarden:undergarden/all_undergarden_biomes": {"criteria": {"undergarden:ancient_sea": "2025-07-11 11:17:54 +0800"}, "done": false}, "undergarden:recipes/misc/smelt_catalyst": {"criteria": {"has_undergarden:catalyst": "2025-07-11 11:18:11 +0800"}, "done": true}, "undergarden:undergarden/catalyst": {"criteria": {"has_catalyst": "2025-07-11 11:18:11 +0800"}, "done": true}, "undergarden:recipes/misc/blast_catalyst": {"criteria": {"has_undergarden:catalyst": "2025-07-11 11:18:11 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_blue_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_yellow_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "botania:recipes/redstone/red_string_container": {"criteria": {"has_base_block": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_red_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_blue_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/red_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_red_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "minecolonies:recipes/misc/supplycampdeployer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/blue_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_chest_from_vanilla_chest": {"criteria": {"has_vanilla_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_routing": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_yellow": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_blue_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/lime_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_routing": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/purple_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/black_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/brown_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_green": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard_no_tanks": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/pink_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/green_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/gray_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_yellow_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_red": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/orange_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "littlelogistics:recipes/transportation/barge": {"criteria": {"has_item": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_green_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_red_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_blue": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_yellow": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_blue": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/white_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/birch_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_yellow_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_green_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_green": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/warped_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/connector_green_dye": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/oak_mail_box": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_red": {"criteria": {"chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "trashcans:recipes/misc/item_trash_can": {"criteria": {"recipe_condition": "2025-07-11 11:23:47 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_cooler": {"criteria": {"has_chest": "2025-07-11 11:23:47 +0800"}, "done": true}, "apotheosis:enchanting/100ench": {"criteria": {"enchanted_item": "2025-07-11 16:14:44 +0800"}, "done": true}, "apotheosis:enchanting/arcana100": {"criteria": {"enchanted_item": "2025-07-11 16:14:44 +0800"}, "done": true}, "apotheosis:enchanting/max_rectified": {"criteria": {"enchanted_item": "2025-07-11 16:15:44 +0800"}, "done": true}, "apotheosis:enchanting/max_stats": {"criteria": {"enchanted_item": "2025-07-11 16:15:44 +0800"}, "done": true}, "apotheosis:enchanting/quanta100": {"criteria": {"enchanted_item": "2025-07-11 16:15:44 +0800"}, "done": true}, "minecraft:recipes/combat/unobtainium_mage_chestplate_smithing": {"criteria": {"allthewizardgearhas_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthemodium:recipes/misc/unobtainium_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/unobtainium]_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthearcanistgear:recipes/unobtainium_robes_smithing": {"criteria": {"allthearcanistgear:has_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "minecraft:recipes/combat/unobtainium_spell_book_smithing": {"criteria": {"allthewizardgearhas_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "minecraft:recipes/combat/unobtainium_mage_leggings_smithing": {"criteria": {"allthewizardgearhas_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthearcanistgear:recipes/unobtainium_boots_smithing": {"criteria": {"allthearcanistgear:has_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "minecraft:recipes/combat/unobtainium_mage_helmet_smithing": {"criteria": {"allthewizardgearhas_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthearcanistgear:recipes/unobtainium_leggings_smithing": {"criteria": {"allthearcanistgear:has_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/unobtainium_dust_from_ingot": {"criteria": {"has_unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthemodium:recipes/misc/unobtainium_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/unobtainium]_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthearcanistgear:recipes/unobtainium_hat_smithing": {"criteria": {"allthearcanistgear:has_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthemodium:recipes/misc/unobtainium_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/unobtainium]_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthemodium:recipes/misc/unobtainium_block": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/unobtainium]_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "minecraft:recipes/combat/unobtainium_mage_boots_smithing": {"criteria": {"allthewizardgearhas_ingots/unobtainium_ingot": "2025-07-11 16:43:22 +0800"}, "done": true}, "allthemodium:recipes/misc/unobtainium_nugget_from_ingot": {"criteria": {"has_item": "2025-07-11 16:43:22 +0800"}, "done": true}, "utilitix:recipes/misc/anvil_cart": {"criteria": {"criterion0": "2025-07-11 17:30:18 +0800"}, "done": true}, "apotheosis:affix/socket": {"criteria": {"socket": "2025-07-11 17:37:57 +0800"}, "done": true}, "computercraft:recipes/redstone/pocket_computer_advanced": {"criteria": {"has_apple": "2025-07-11 17:38:45 +0800"}, "done": true}, "computercraft:recipes/redstone/pocket_computer_normal": {"criteria": {"has_apple": "2025-07-11 17:38:45 +0800"}, "done": true}, "reliquary:recipes/misc/glowing_water_from_potion_vial": {"criteria": {"has_empty_potion_vial": "2025-07-11 17:43:26 +0800"}, "done": true}, "aether:recipes/building_blocks/blue_ice_freezing": {"criteria": {"has_water_bucket": "2025-07-11 17:57:01 +0800"}, "done": true}, "aether:recipes/building_blocks/packed_ice_freezing": {"criteria": {"has_water_bucket": "2025-07-11 17:57:01 +0800"}, "done": true}, "minecraft:recipes/brewing/cauldron": {"criteria": {"has_water_bucket": "2025-07-11 17:57:01 +0800"}, "done": true}, "minecraft:recipes/misc/ice_from_freezing": {"criteria": {"has_water": "2025-07-11 17:57:01 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/basic/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-11 17:57:01 +0800"}, "done": true}, "sfm:recipes/misc/water_tank": {"criteria": {"has_water": "2025-07-11 17:57:01 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/reinforced/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-11 17:57:01 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/reinforced/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-11 17:57:01 +0800"}, "done": true}, "aether:recipes/building_blocks/ice_from_bucket_freezing": {"criteria": {"has_water_bucket": "2025-07-11 17:57:01 +0800"}, "done": true}, "potionblender:recipes/brewing/brewing_cauldron": {"criteria": {"has_water_bucket": "2025-07-11 17:57:01 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/radiator": {"criteria": {"has_water_bucket": "2025-07-11 17:57:01 +0800"}, "done": true}, "croptopia:recipes/shaped_water_bottle": {"criteria": {"has_water": "2025-07-11 17:57:01 +0800"}, "done": true}, "travelersbackpack:recipes/misc/skeleton": {"criteria": {"has_arrow": "2025-07-11 18:02:42 +0800"}, "done": true}, "botania:main/gaia_guardian_kill": {"criteria": {"guardian": "2025-07-11 18:03:45 +0800"}, "done": true}, "botania:recipes/tools/diva_charm": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/tools/flighttiara_0": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/misc/mana_bomb": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/tools/super_travel_belt": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/combat/unholy_cloak": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/tools/astrolabe": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/tools/laputa_shard": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/combat/balance_cloak": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/combat/missile_rod": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/misc/gaia_ingot": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/combat/holy_cloak": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "appbot:recipes/misc/mana_cell_housing": {"criteria": {"has_life_essence": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/redstone/gaia_spreader": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/tools/spawner_mover": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/tools/black_hole_talisman": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/tools/super_cloud_pendant": {"criteria": {"has_alt_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "botania:recipes/tools/super_lava_pendant": {"criteria": {"has_item": "2025-07-11 18:04:07 +0800"}, "done": true}, "ae2:recipes/misc/misc/tiny_tnt": {"criteria": {"has_gunpowder": "2025-07-11 18:04:44 +0800"}, "done": true}, "biomesoplenty:recipes/redstone/tnt_from_bop_sand": {"criteria": {"has_gunpowder": "2025-07-11 18:04:44 +0800"}, "done": true}, "minecraft:recipes/misc/firework_rocket_simple": {"criteria": {"has_gunpowder": "2025-07-11 18:04:44 +0800"}, "done": true}, "travelersbackpack:recipes/misc/creeper": {"criteria": {"has_gunpowder": "2025-07-11 18:04:44 +0800"}, "done": true}, "minecraft:recipes/redstone/tnt": {"criteria": {"has_gunpowder": "2025-07-11 18:04:44 +0800"}, "done": true}, "tombstone:adventure/bone_crusher": {"criteria": {"coded_trigger": "2025-07-11 18:05:26 +0800"}, "done": true}, "tombstone:adventure/village_defender": {"criteria": {"coded_trigger": "2025-07-11 18:05:32 +0800"}, "done": true}, "aether:recipes/combat/bow_repairing": {"criteria": {"has_bow": "2025-07-11 18:05:41 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dispenser": {"criteria": {"has_bow": "2025-07-11 18:05:41 +0800"}, "done": true}, "twilightdelight:recipes/frozen/cutting/ice_bow": {"criteria": {"has_bow": "2025-07-11 18:05:41 +0800"}, "done": true}, "minecraft:recipes/redstone/dispenser": {"criteria": {"has_bow": "2025-07-11 18:05:41 +0800"}, "done": true}, "botania:challenge/gaia_guardian_hardmode": {"criteria": {"guardian": "2025-07-11 18:07:02 +0800"}, "done": true}, "botania:recipes/decorations/terra_plate": {"criteria": {"has_item": "2025-07-11 18:07:43 +0800"}, "done": true}, "mythicbotany:mythicbotany/all_runes": {"criteria": {"inventory_changed11": "2025-07-11 18:07:43 +0800", "inventory_changed15": "2025-07-11 18:12:06 +0800", "inventory_changed7": "2025-07-11 18:12:32 +0800", "inventory_changed4": "2025-07-11 18:12:06 +0800", "inventory_changed2": "2025-07-11 18:12:07 +0800"}, "done": false}, "botania:recipes/tools/exchange_rod": {"criteria": {"has_item": "2025-07-11 18:07:43 +0800"}, "done": true}, "botania:main/runic_altar_pickup": {"criteria": {"rune": "2025-07-11 18:07:43 +0800"}, "done": true}, "botania:main/rune_pickup": {"criteria": {"rune": "2025-07-11 18:07:43 +0800"}, "done": true}, "botania:challenge/king_key": {"criteria": {"key": "2025-07-11 18:09:10 +0800"}, "done": true}, "mythicbotany:recipes/misc/mana_infuser": {"criteria": {"criterion6": "2025-07-11 18:12:06 +0800"}, "done": true}, "aether:recipes/misc/aether_tune_enchanting": {"criteria": {"has_disc": "2025-07-11 18:12:07 +0800"}, "done": true}, "botania:challenge/loki_ring": {"criteria": {"ring": "2025-07-11 18:12:20 +0800"}, "done": true}, "botania:main/bauble_wear": {"criteria": {"code_triggered": "2025-07-11 18:12:26 +0800"}, "done": true}, "botania:recipes/misc/phantom_ink": {"criteria": {"has_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "botania:recipes/tools/mana_tablet": {"criteria": {"has_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "botania:recipes/tools/tiny_planet": {"criteria": {"has_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "botania:recipes/decorations/tiny_planet_block": {"criteria": {"has_alt_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "botania:recipes/tools/invisibility_cloak": {"criteria": {"has_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "botania:recipes/combat/ender_dagger": {"criteria": {"has_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "botania:recipes/decorations/abstruse_platform": {"criteria": {"has_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "botania:recipes/tools/crafting_halo": {"criteria": {"has_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "botania:recipes/decorations/runic_altar": {"criteria": {"has_item": "2025-07-11 18:12:32 +0800"}, "done": true}, "reliquary:recipes/misc/uncrafting/redstone": {"criteria": {"has_witch_hat": "2025-07-11 18:12:33 +0800"}, "done": true}, "reliquary:recipes/misc/uncrafting/sugar": {"criteria": {"has_witch_hat": "2025-07-11 18:12:33 +0800"}, "done": true}, "reliquary:recipes/misc/mob_charm_fragments/witch": {"criteria": {"has_witch_hat": "2025-07-11 18:12:33 +0800"}, "done": true}, "reliquary:recipes/misc/uncrafting/glowstone_dust": {"criteria": {"has_witch_hat": "2025-07-11 18:12:33 +0800"}, "done": true}, "reliquary:recipes/misc/uncrafting/gunpowder_witch_hat": {"criteria": {"has_witch_hat": "2025-07-11 18:12:33 +0800"}, "done": true}, "reliquary:recipes/misc/uncrafting/stick": {"criteria": {"has_witch_hat": "2025-07-11 18:12:33 +0800"}, "done": true}, "reliquary:recipes/misc/uncrafting/glass_bottle": {"criteria": {"has_witch_hat": "2025-07-11 18:12:33 +0800"}, "done": true}, "advanced_ae:recipes/misc/smallappupgrade": {"criteria": {"hasItem": "2025-07-12 16:32:21 +0800"}, "done": true}, "advanced_ae:recipes/misc/advpartenc": {"criteria": {"hasItem": "2025-07-12 16:32:21 +0800"}, "done": true}, "supplementaries:recipes/pancake_fd": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "delightful:recipes/food/marshmallow_stick": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "croptopia:recipes/food/caramel_from_sugar": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "croptopia:recipes/candied_nuts": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "croptopia:recipes/candy_corn": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800", "has_the_recipe": "2025-07-12 17:46:53 +0800"}, "done": true}, "supplementaries:recipes/sugar_cube": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "croptopia:recipes/campfire_caramel": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "croptopia:recipes/nutty_cookie": {"criteria": {"has_the_recipe": "2025-07-12 17:46:53 +0800"}, "done": true}, "croptopia:recipes/food/caramel_from_smoking_sugar": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "supplementaries:recipes/candy": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "croptopia:recipes/nougat": {"criteria": {"has_sugar": "2025-07-12 17:46:53 +0800"}, "done": true}, "botania:recipes/redstone/mana_detector": {"criteria": {"has_item": "2025-07-12 18:15:17 +0800"}, "done": true}, "apotheosis:affix/mythic_gem": {"criteria": {"gem": "2025-07-12 18:53:24 +0800"}, "done": true}, "minecraft:adventure/totem_of_undying": {"criteria": {"used_totem": "2025-07-12 19:20:05 +0800"}, "done": true}, "tombstone:adventure/strong_or_careful": {"criteria": {"coded_trigger": "2025-07-12 19:24:01 +0800"}, "done": true}, "minecraft:end/enter_end_gateway": {"criteria": {"entered_end_gateway": "2025-07-13 09:22:14 +0800"}, "done": true}, "minecraft:end/elytra": {"criteria": {"elytra": "2025-07-13 09:25:36 +0800"}, "done": true}, "deeperdarker:recipes/transportation/soul_elytra": {"criteria": {"has_elytra": "2025-07-13 09:25:36 +0800"}, "done": true}, "reliquary:recipes/misc/fortune_coin": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "reliquary:recipes/misc/angelic_feather": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "reliquary:recipes/misc/magicbane": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "reliquary:recipes/misc/alkahestry_altar": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "reliquary:recipes/misc/crimson_cloth": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "tombstone:adventure/chain_death": {"criteria": {"coded_trigger": "2025-07-13 10:37:47 +0800"}, "done": true}, "reliquary:recipes/misc/uncrafting/ender_pearl": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "reliquary:recipes/misc/mob_charm_fragments/enderman": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "reliquary:recipes/misc/wraith_node": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "reliquary:recipes/misc/ender_staff": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "reliquary:recipes/misc/void_tear": {"criteria": {"has_nebulous_heart": "2025-07-13 10:11:17 +0800"}, "done": true}, "DataVersion": 3465}