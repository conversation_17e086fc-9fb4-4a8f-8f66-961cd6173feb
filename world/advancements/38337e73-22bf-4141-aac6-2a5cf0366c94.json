{"undergarden:undergarden/root": {"criteria": {"tick": "2025-07-10 21:50:44 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-10 21:50:44 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-10 21:50:44 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-10 21:50:44 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-10 21:50:44 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-10 21:50:44 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-10 21:50:44 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-10 21:50:44 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-10 21:50:44 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-10 21:50:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-10 21:50:44 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-10 21:50:44 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-10 21:50:44 +0800"}, "done": false}, "lootr:root": {"criteria": {"always_true": "2025-07-10 21:50:44 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-10 21:50:44 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-10 21:50:45 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:bamboo_jungle": "2025-07-10 22:03:12 +0800", "minecraft:beach": "2025-07-10 21:50:45 +0800", "minecraft:lush_caves": "2025-07-10 22:01:38 +0800"}, "done": false}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-10 21:53:22 +0800", "has_axe": "2025-07-10 21:53:21 +0800", "has_helmet": "2025-07-10 21:53:21 +0800", "has_fishing_rod": "2025-07-10 21:53:21 +0800", "has_bow": "2025-07-10 21:53:22 +0800", "has_chestplate": "2025-07-10 21:53:21 +0800", "has_sword": "2025-07-10 21:53:21 +0800", "has_fillet_knife": "2025-07-10 21:53:21 +0800", "has_shovel": "2025-07-10 21:53:21 +0800", "has_hoe": "2025-07-10 21:53:21 +0800", "has_boots": "2025-07-10 21:53:21 +0800", "has_pickaxe": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-10 21:53:21 +0800", "has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-10 21:53:21 +0800", "has_fillet_knife": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-10 21:53:22 +0800", "has_wpt": "2025-07-10 21:53:21 +0800", "has_wct": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-10 21:53:21 +0800", "has_fillet_knife": "2025-07-10 21:53:21 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-10 21:53:21 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-10 21:53:21 +0800", "has_fillet_knife": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-10 21:53:21 +0800", "has_wct": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-10 21:53:22 +0800", "has_wct": "2025-07-10 21:53:21 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-10 21:53:21 +0800", "has_wpt": "2025-07-10 21:53:22 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-10 21:53:21 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-10 21:53:21 +0800", "has_fillet_knife": "2025-07-10 21:53:21 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:21 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-10 21:53:22 +0800", "has_carrot": "2025-07-10 21:53:22 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-10 21:53:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:22 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-10 21:53:22 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-10 21:53:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-10 21:53:22 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:22 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-10 21:53:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-10 21:53:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-10 21:53:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-10 21:53:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-10 21:53:22 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-10 21:53:22 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-10 21:53:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-10 21:53:22 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_boat": {"criteria": {"in_water": "2025-07-10 22:01:07 +0800"}, "done": true}, "biomesoplenty:biomesoplenty/all_biomes": {"criteria": {"floodplain": "2025-07-10 22:01:09 +0800", "rocky_rainforest": "2025-07-10 22:03:13 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/root": {"criteria": {"floodplain": "2025-07-10 22:01:09 +0800"}, "done": true}, "tombstone:adventure/cancel_ghostly_shape": {"criteria": {"coded_trigger": "2025-07-10 22:02:50 +0800"}, "done": true}, "tombstone:recipes/from_grave_key": {"criteria": {"has_ingredient": "2025-07-10 22:03:32 +0800"}, "done": true}, "maidensmerrymaking:easter/root": {"criteria": {"requirement": "2025-07-10 22:14:50 +0800"}, "done": true}, "utilitarian:recipes/misc/snad/soul_snad": {"criteria": {"has_soul_sand": "2025-07-21 18:05:01 +0800"}, "done": true}, "minecraft:recipes/decorations/soul_campfire": {"criteria": {"has_soul_sand": "2025-07-21 18:05:01 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/soul_sand_1x": {"criteria": {"has_soul_sand": "2025-07-21 18:05:01 +0800"}, "done": true}, "minecraft:recipes/decorations/soul_torch": {"criteria": {"has_soul_sand": "2025-07-21 18:05:01 +0800"}, "done": true}, "create:recipes/misc/smelting/scoria": {"criteria": {"has_item": "2025-07-21 18:05:01 +0800"}, "done": true}, "DataVersion": 3465}