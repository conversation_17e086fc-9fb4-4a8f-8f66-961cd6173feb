{"undergarden:undergarden/root": {"criteria": {"tick": "2025-07-10 23:19:02 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-10 23:19:02 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-10 23:19:02 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-10 23:19:02 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-10 23:19:02 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-10 23:19:02 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-10 23:19:02 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-10 23:19:02 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-10 23:19:02 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-10 23:19:02 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-10 23:19:02 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-10 23:19:02 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-10 23:19:02 +0800"}, "done": false}, "lootr:root": {"criteria": {"always_true": "2025-07-10 23:19:02 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-10 23:19:02 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-10 23:19:03 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:frozen_river": "2025-07-11 00:06:51 +0800", "minecraft:cherry_grove": "2025-07-11 00:07:36 +0800", "minecraft:bamboo_jungle": "2025-07-10 23:22:00 +0800", "minecraft:beach": "2025-07-10 23:19:03 +0800", "minecraft:snowy_plains": "2025-07-11 00:05:42 +0800", "minecraft:swamp": "2025-07-11 00:08:27 +0800", "minecraft:desert": "2025-07-10 23:22:45 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/all_biomes": {"criteria": {"floodplain": "2025-07-11 00:00:02 +0800", "lush_savanna": "2025-07-10 23:22:36 +0800", "crystalline_chasm": "2025-07-11 00:02:12 +0800", "field": "2025-07-11 00:08:59 +0800", "coniferous_forest": "2025-07-11 00:08:31 +0800", "rocky_rainforest": "2025-07-10 23:22:02 +0800", "undergrowth": "2025-07-11 00:01:58 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/root": {"criteria": {"rocky_rainforest": "2025-07-10 23:22:02 +0800"}, "done": true}, "maidensmerrymaking:easter/root": {"criteria": {"requirement": "2025-07-10 23:22:04 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_swamp_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_ranch_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-10 23:23:22 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-10 23:23:22 +0800", "has_carrot": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_trapdoors": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_counter": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_crate": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_boats": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfurnitures:recipes/jungle": {"criteria": {"has_wood": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 23:23:22 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_pressure_plates": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_whispering_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwroofs:recipes/jungle": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-10 23:23:22 +0800", "has_axe": "2025-07-10 23:23:22 +0800", "has_helmet": "2025-07-10 23:23:22 +0800", "has_fishing_rod": "2025-07-10 23:23:22 +0800", "has_bow": "2025-07-10 23:23:22 +0800", "has_chestplate": "2025-07-10 23:23:22 +0800", "has_sword": "2025-07-10 23:23:22 +0800", "has_fillet_knife": "2025-07-10 23:23:22 +0800", "has_shovel": "2025-07-10 23:23:22 +0800", "has_hoe": "2025-07-10 23:23:22 +0800", "has_boots": "2025-07-10 23:23:22 +0800", "has_pickaxe": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-10 23:23:22 +0800", "has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "securitycraft:recipes/misc/sc_manual": {"criteria": {"has_wood": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_drawer": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_counter": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_park_bench": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_table": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbridges:recipes/jungle_bridge_pier": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_desk": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-10 23:23:22 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_bedside_cabinet": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_blinds": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_crate": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-10 23:23:22 +0800"}, "done": true}, "naturescompass:natures_compass": {"criteria": {"has_recipe": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-10 23:23:22 +0800", "has_fillet_knife": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-10 23:23:22 +0800"}, "done": true}, "botania:recipes/tools/lexicon": {"criteria": {"has_item": "2025-07-10 23:23:22 +0800"}, "done": true}, "naturescompass:natures_compass_log": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_bedside_cabinet": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-10 23:23:22 +0800", "has_wpt": "2025-07-10 23:23:22 +0800", "has_wct": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_coffee_table": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "utilitarian:recipes/food/utility/charcoal_from_campfire": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_desk_cabinet": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-10 23:23:22 +0800", "has_fillet_knife": "2025-07-10 23:23:22 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_stairs": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_mystic_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_paper_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_slabs": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_table": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "railcraft:recipes/coke_oven/charcoal": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_cabinet": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_chair": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_four_panel_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_upgraded_fence": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/acacia/jungle_bark_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/architectscutter": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/logs_to_bowls": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/jungle_log_1x": {"criteria": {"has_jungle_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_doors": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwwindows:recipes/jungle": {"criteria": {"has_wood": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "enderio:recipes/misc/stick": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/print_beach": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_desk": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_wood": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-10 23:23:22 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_cabinet": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_tropical_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_glass_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-10 23:23:22 +0800"}, "done": true}, "naturescompass:natures_compass_sapling": {"criteria": {"has_recipe": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_desk_cabinet": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barred_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_park_bench": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwdoors:recipes/jungle": {"criteria": {"has_wood": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barrel_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-10 23:23:22 +0800", "has_fillet_knife": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_upgraded_gate": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barn_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-10 23:23:22 +0800", "has_wct": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-10 23:23:22 +0800", "has_wct": "2025-07-10 23:23:22 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-10 23:23:22 +0800", "has_wpt": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_drawer": {"criteria": {"has_log": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_chair": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-10 23:23:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_coffee_table": {"criteria": {"has_planks": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_cottage_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-10 23:23:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/charcoal": {"criteria": {"has_the_recipe": "2025-07-10 23:23:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_planks": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_classic_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-10 23:23:22 +0800", "has_fillet_knife": "2025-07-10 23:23:22 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-10 23:23:22 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-10 23:23:22 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_boat": {"criteria": {"in_water": "2025-07-10 23:23:26 +0800"}, "done": true}, "aether:recipes/combat/bow_repairing": {"criteria": {"has_bow": "2025-07-10 23:23:27 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dispenser": {"criteria": {"has_bow": "2025-07-10 23:23:27 +0800"}, "done": true}, "twilightdelight:recipes/frozen/cutting/ice_bow": {"criteria": {"has_bow": "2025-07-10 23:23:27 +0800"}, "done": true}, "minecraft:recipes/redstone/dispenser": {"criteria": {"has_bow": "2025-07-10 23:23:27 +0800"}, "done": true}, "twigs:recipes/stick_from_twig": {"criteria": {"has_item": "2025-07-10 23:23:35 +0800"}, "done": true}, "twigs:recipes/azalea_flowers_from_flowering_azalea": {"criteria": {"has_item": "2025-07-10 23:23:40 +0800"}, "done": true}, "croptopia:recipes/campfire_molasses": {"criteria": {"has_sugar_cane": "2025-07-10 23:25:55 +0800"}, "done": true}, "minecraft:recipes/misc/paper": {"criteria": {"has_reeds": "2025-07-10 23:25:55 +0800"}, "done": true}, "minecraft:recipes/misc/sugar_from_sugar_cane": {"criteria": {"has_sugar_cane": "2025-07-10 23:25:55 +0800"}, "done": true}, "croptopia:recipes/food/molasses_from_sugar_cane": {"criteria": {"has_sugar_cane": "2025-07-10 23:25:55 +0800"}, "done": true}, "alchemistry:recipes/dissolver/paper": {"criteria": {"has_the_recipe": "2025-07-10 23:25:55 +0800"}, "done": true}, "croptopia:recipes/food/molasses_from_smoking_sugar_cane": {"criteria": {"has_sugar_cane": "2025-07-10 23:25:55 +0800"}, "done": true}, "productivebees:husbandry/bee_cage/overworld_nest": {"criteria": {"get_item": "2025-07-10 23:25:55 +0800"}, "done": true}, "mcwbridges:recipes/rope_jungle_bridge": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_stairs": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/bear_trophy": {"criteria": {"has_bear_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/sign_post_jungle": {"criteria": {"has_the_recipe": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_trapdoor": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/goat_trophy": {"criteria": {"has_goat_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "twigs:recipes/tables/jungle_table": {"criteria": {"has_jungle": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_table": {"criteria": {"has_jungle_table": "2025-07-10 23:26:14 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_onside": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/notice_board": {"criteria": {"minecraft:planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_standing": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/skeleton_horse_trophy": {"criteria": {"has_skeleton_horse_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_counter": {"criteria": {"has_jungle_counter": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/amber_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_door": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_cupboard": {"criteria": {"has_jungle_cupboard": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/peach_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_slab": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/speaker_block": {"criteria": {"minecraft:planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/fox_trophy": {"criteria": {"has_fox_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/pufferfish_trophy": {"criteria": {"has_pufferfish_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/pulley": {"criteria": {"minecraft:planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/bubblegum_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_side_table": {"criteria": {"has_jungle_side_table": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/conifer_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_corner_trim": {"criteria": {"has_jungle_corner_trim": "2025-07-10 23:26:14 +0800"}, "done": true}, "mcwroofs:recipes/jungle_planks": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/decorations/sign_post_jungle": {"criteria": {"has_item": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/persimmon_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_barrel": {"criteria": {"has _plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_3": {"criteria": {"has_jungle_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_2": {"criteria": {"has_jungle_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_1": {"criteria": {"has_jungle_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_4": {"criteria": {"has_jungle_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/ultramarine_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "mcwbridges:recipes/jungle_log_bridge_middle": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_pillar_trim": {"criteria": {"has_jungle_pillar_trim": "2025-07-10 23:26:14 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/jungle_planks_1x": {"criteria": {"has_jungle_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/mint_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/fluorescent_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stick": {"criteria": {"has_the_recipe": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/cherenkov_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_chair": {"criteria": {"has_jungle_chair": "2025-07-10 23:26:14 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_light": {"criteria": {"has_bottom": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/aquamarine_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_button": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_fancy_bed": {"criteria": {"has_jungle_fancy_bed": "2025-07-10 23:26:14 +0800"}, "done": true}, "framedblocks:recipes/building_blocks/framed_cube": {"criteria": {"hasPlanks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_dining_bench": {"criteria": {"has_jungle_dining_bench": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/decorations/jungle_sign": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/turn_table": {"criteria": {"minecraft:planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/blaze_trophy": {"criteria": {"has_blaze_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/wolf_trophy": {"criteria": {"has_wolf_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/silverfish_trophy": {"criteria": {"has_silverfish_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_chest": {"criteria": {"has _plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_nightstand": {"criteria": {"has_jungle_nightstand": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/icy_blue_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/clock_block": {"criteria": {"minecraft:planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/navy_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "silentgear:recipes/misc/upgrade_base": {"criteria": {"has_item": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_2": {"criteria": {"has _plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_3": {"criteria": {"has _plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_1": {"criteria": {"has _plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_4": {"criteria": {"has _plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/tropical_fish_trophy": {"criteria": {"has_tropical_fish_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_fence_gate": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_barrel": {"criteria": {"has_jungle_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_bench": {"criteria": {"has_jungle_bench": "2025-07-10 23:26:14 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_blossom_trapdoor": {"criteria": {"has_plankss": "2025-07-10 23:26:14 +0800"}, "done": true}, "mcwpaths:recipes/jungle": {"criteria": {"has_wood": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_drawer": {"criteria": {"has_jungle_drawer": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/lavender_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/decorations/jungle_fence": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_shelf": {"criteria": {"has_jungle_shelf": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/maroon_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "create:recipes/building_blocks/jungle_window": {"criteria": {"has_ingredient": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/honey_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/bed_from_feather_block": {"criteria": {"minecraft:planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/spider_trophy": {"criteria": {"has_spider_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "mcwbridges:recipes/jungle_rail_bridge": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/wine_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_pressure_plate": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/rose_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/spring_green_bed": {"criteria": {"has_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/wither_skeleton_trophy": {"criteria": {"has_wither_skeleton_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_dark": {"criteria": {"has_bottom": "2025-07-10 23:26:14 +0800"}, "done": true}, "supplementaries:recipes/lock_block": {"criteria": {"minecraft:planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_chest": {"criteria": {"has_jungle_plank": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/salmon_trophy": {"criteria": {"has_salmon_trophy": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_desk": {"criteria": {"has_jungle_desk": "2025-07-10 23:26:14 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/wooden_blacksmith_gavel": {"criteria": {"has_planks": "2025-07-10 23:26:14 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_couch": {"criteria": {"has_jungle_couch": "2025-07-10 23:26:14 +0800"}, "done": true}, "silentgear:root": {"criteria": {"get_item": "2025-07-10 23:26:19 +0800"}, "done": true}, "minecraft:story/root": {"criteria": {"crafting_table": "2025-07-10 23:26:19 +0800"}, "done": true}, "paraglider:root": {"criteria": {"crafting_table": "2025-07-10 23:26:19 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/crafting_card": {"criteria": {"crafter": "2025-07-10 23:26:19 +0800"}, "done": true}, "create:recipes/misc/crafting/appliances/crafting_blueprint": {"criteria": {"has_item": "2025-07-10 23:26:19 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_jungle_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_magenta": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "utilitix:recipes/misc/armed_stand": {"criteria": {"criterion1": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/persimmon_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dark_oak_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_dark_oak_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_oak_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_bamboo_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/timber_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "utilitix:recipes/misc/reinforced_rail": {"criteria": {"criterion2": "2025-07-10 23:26:22 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear": {"criteria": {"has_ingredient": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_cyan": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/motion_activated_light": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_orange": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_spruce_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/maroon_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "aquaculture:recipes/tools/wooden_fillet_knife": {"criteria": {"has_items": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/peach_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_shovel": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_birch_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/conifer_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "undergarden:recipes/decorations/torch_ditchbulb_paste": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/edelwood_ladder": {"criteria": {"has_rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/spring_green_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_acacia_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/ultramarine_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_gray": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_lime": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecraft:recipes/decorations/ladder": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_axe": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "mcwwindows:recipes/curtain_rods": {"criteria": {"has_wood": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecraft:recipes/decorations/campfire": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/no_soliciting_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_oak_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_purple": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_gray": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/daub_brace": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/crank": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_pink": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/timber_frame": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_hoe": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/sonic_security_system": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear_corner": {"criteria": {"has_ingredient": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/lavender_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/crank": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecolonies:recipes/misc/shapetool": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "undergarden:recipes/decorations/undergarden_scaffolding": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_jungle_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/navy_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/bubblegum_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "utilitix:recipes/misc/directional_highspeed_rail": {"criteria": {"criterion1": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecraft:recipes/combat/wooden_sword": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_green": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_white": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "utilitix:recipes/misc/directional_rail": {"criteria": {"criterion1": "2025-07-10 23:26:22 +0800"}, "done": true}, "undergarden:recipes/decorations/shard_torch": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_cherry_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/honey_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_pickaxe": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_mangrove_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_crimson_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/torch": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_acacia_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/cherenkov_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "utilitix:recipes/misc/hand_bell": {"criteria": {"criterion0": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/fluorescent_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_blue": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_warped_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_mangrove_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "farmersdelight:recipes/decorations/cutting_board": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_spruce_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_birch_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/amber_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "utilitix:recipes/misc/weak_redstone_torch": {"criteria": {"criterion1": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/daub_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_bamboo_fence_gate": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/timber_brace": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/aquamarine_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "paraglider:recipes/misc/paraglider": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_black": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_crimson_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/wine_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "undergarden:recipes/combat/slingshot": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/faucet": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "utilitix:recipes/misc/highspeed_rail": {"criteria": {"criterion1": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_warped_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_blue": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/rose_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/icy_blue_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_red": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/slingshot": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "silentgear:recipes/misc/rough_rod": {"criteria": {"has_item": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_brown": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "farmersdelight:recipes/combat/flint_knife": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_cherry_fence": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/daub_frame": {"criteria": {"forge:rods/wooden": "2025-07-10 23:26:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/mint_banner": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_yellow": {"criteria": {"has_stick": "2025-07-10 23:26:22 +0800"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-10 23:26:31 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_chest": {"criteria": {"has_lots_of_items": "2025-07-10 23:26:31 +0800"}, "done": true}, "aether:recipes/tools/wooden_pickaxe_repairing": {"criteria": {"has_wooden_pickaxe": "2025-07-10 23:26:31 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_mossy_cobblestone_from_vanilla_moss": {"criteria": {"has_moss": "2025-07-10 23:26:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_stone_bricks_from_moss_block": {"criteria": {"has_moss_block": "2025-07-10 23:26:40 +0800"}, "done": true}, "minecraft:recipes/decorations/moss_carpet": {"criteria": {"has_moss_block": "2025-07-10 23:26:40 +0800"}, "done": true}, "biomesoplenty:recipes/building_blocks/mossy_black_sand": {"criteria": {"has_moss_block": "2025-07-10 23:26:40 +0800"}, "done": true}, "alchemistry:recipes/dissolver/moss_carpet": {"criteria": {"has_the_recipe": "2025-07-10 23:26:40 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/mossy_cobblestone_bricks_from_moss": {"criteria": {"has_item": "2025-07-10 23:26:40 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_cobblestone_from_moss_block": {"criteria": {"has_moss_block": "2025-07-10 23:26:40 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/moss_block_1x": {"criteria": {"has_moss_block": "2025-07-10 23:26:40 +0800"}, "done": true}, "twigs:recipes/bricks/mossy_bricks_from_moss_block": {"criteria": {"has_item": "2025-07-10 23:26:40 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_mossy_stone_bricks_from_vanilla_moss": {"criteria": {"has_moss": "2025-07-10 23:26:40 +0800"}, "done": true}, "dyenamics:recipes/misc/wine_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/pink_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/gray_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/magenta_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/sand_stone_bricks": {"criteria": {"has_item1_domum_ornamentum_sand_stone_bricks": "2025-07-10 23:26:41 +0800", "has_item2_domum_ornamentum_sand_stone_bricks": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/orange_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/green_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/sand_1x": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/brown_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/light_blue_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lime_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/brown_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cyan_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/white_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/black_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/yellow_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/grit_sand": {"criteria": {"has_gravel": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/sandstone": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/lime_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purple_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gray_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "mcwroofs:recipes/concrete": {"criteria": {"has_planks": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/pink_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/red_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/green_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_gray_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_blue_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/lavender_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/magenta_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/sandstone": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cyan_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/light_gray_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "utilitarian:recipes/misc/snad/snad": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/yellow_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/blue_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "enderio:recipes/misc/conduit_binder_composite": {"criteria": {"has_ingredient_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/black_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blue_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/purple_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/orange_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "twigs:recipes/silt/silt_from_sand": {"criteria": {"has_item": "2025-07-10 23:26:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/glass": {"criteria": {"has_smelts_to_glass": "2025-07-10 23:26:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/red_concrete_powder": {"criteria": {"has_the_recipe": "2025-07-10 23:26:41 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/sand_bricks": {"criteria": {"has_item2_domum_ornamentum_sand_bricks": "2025-07-10 23:26:41 +0800", "has_item1_domum_ornamentum_sand_bricks": "2025-07-10 23:26:41 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_concrete_powder": {"criteria": {"has_sand": "2025-07-10 23:26:41 +0800"}, "done": true}, "handcrafted:recipes/misc/sandstone_corner_trim": {"criteria": {"has_sandstone_corner_trim": "2025-07-10 23:26:43 +0800"}, "done": true}, "mcwbridges:recipes/sandstone_bridge": {"criteria": {"has_planks": "2025-07-10 23:26:43 +0800"}, "done": true}, "mcwpaths:recipes/sandstone": {"criteria": {"has_wood": "2025-07-10 23:26:43 +0800"}, "done": true}, "mcwwindows:recipes/sandstone": {"criteria": {"has_wood": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/sandstone_stairs_from_sandstone_stonecutting": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/decorations/sandstone_wall": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cut_sandstone_from_sandstone_stonecutting": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/sandstone_slab_from_sandstone_stonecutting": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "mcwbridges:recipes/sandstone_bridge_pier": {"criteria": {"has_planks": "2025-07-10 23:26:43 +0800"}, "done": true}, "mcwroofs:recipes/sandstone": {"criteria": {"has_planks": "2025-07-10 23:26:43 +0800"}, "done": true}, "handcrafted:recipes/misc/sandstone_pillar_trim": {"criteria": {"has_sandstone_pillar_trim": "2025-07-10 23:26:43 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_sandstone_chain": {"criteria": {"recipe_condition": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/decorations/sandstone_wall_from_sandstone_stonecutting": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cut_sandstone": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/sandstone_slab": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/smooth_sandstone": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/cream_stone_bricks": {"criteria": {"has_item1_domum_ornamentum_cream_stone_bricks": "2025-07-10 23:26:43 +0800", "has_item2_domum_ornamentum_cream_stone_bricks": "2025-07-10 23:26:43 +0800"}, "done": true}, "alchemistry:recipes/dissolver/sandstone_slab": {"criteria": {"has_the_recipe": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cut_sandstone_slab_from_sandstone_stonecutting": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cut_sandstone": {"criteria": {"has_the_recipe": "2025-07-10 23:26:43 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_sandstone_bridge": {"criteria": {"has_planks": "2025-07-10 23:26:43 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/cream_bricks": {"criteria": {"has_item2_domum_ornamentum_cream_bricks": "2025-07-10 23:26:43 +0800", "has_item1_domum_ornamentum_cream_bricks": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_sandstone_from_sandstone_stonecutting": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "alchemistry:recipes/dissolver/smooth_sandstone": {"criteria": {"has_the_recipe": "2025-07-10 23:26:43 +0800"}, "done": true}, "travelersbackpack:recipes/misc/sandstone": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/sandstone_stairs": {"criteria": {"has_sandstone": "2025-07-10 23:26:43 +0800"}, "done": true}, "constructionwand:recipes/tools/stone_wand": {"criteria": {"has_item": "2025-07-10 23:26:49 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite_with_vanilla_cobblestone": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "utilitix:recipes/misc/crude_furnace": {"criteria": {"criterion1": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_stairs": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/combat/stone_sword": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/decorations/cobblestone_wall": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/tools/stone_axe": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/stone_blacksmith_gavel": {"criteria": {"has_stone_tool_materials": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:story/mine_stone": {"criteria": {"get_stone": "2025-07-10 23:26:49 +0800"}, "done": true}, "caupona:recipes/decorations/mud_kitchen_stove": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "securitycraft:recipes/redstone/laser_block": {"criteria": {"has_stone": "2025-07-10 23:26:49 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_stairs_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/tools/stone_hoe": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/redstone/lever": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "supplementaries:recipes/checker": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_bricks": {"criteria": {"has_item": "2025-07-10 23:26:49 +0800"}, "done": true}, "aquaculture:recipes/tools/stone_fillet_knife": {"criteria": {"has_items": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/tools/stone_pickaxe": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobblestone_chain": {"criteria": {"recipe_condition": "2025-07-10 23:26:49 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/green_cobblestone_extra": {"criteria": {"has_material": "2025-07-10 23:26:49 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/purple_cobblestone_extra": {"criteria": {"has_material": "2025-07-10 23:26:49 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stone": {"criteria": {"has_the_recipe": "2025-07-10 23:26:49 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_stairs_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-10 23:26:49 +0800"}, "done": true}, "enderio:recipes/misc/stone_gear": {"criteria": {"has_ingredient": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_slab": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "mcwroofs:recipes/cobblestone": {"criteria": {"has_planks": "2025-07-10 23:26:49 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_bricks_stonecutting": {"criteria": {"has_item": "2025-07-10 23:26:49 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge_pier": {"criteria": {"has_planks": "2025-07-10 23:26:49 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_wall_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/decorations/furnace": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/cobblestone_1x": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_andesite_with_vanilla_diorite": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/cobblestone_extra": {"criteria": {"has_material": "2025-07-10 23:26:49 +0800"}, "done": true}, "utilitix:recipes/misc/comparator_redirector_up": {"criteria": {"criterion1": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/tools/stone_shovel": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge": {"criteria": {"has_planks": "2025-07-10 23:26:49 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_diorite": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cobblestone_slab": {"criteria": {"has_the_recipe": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/decorations/cobblestone_wall_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "twigs:recipes/cobblestone_bricks/cobblestone_brick_slab_from_cobblestone_stonecutting": {"criteria": {"has_item": "2025-07-10 23:26:49 +0800"}, "done": true}, "utilitix:recipes/misc/comparator_redirector_down": {"criteria": {"criterion1": "2025-07-10 23:26:49 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_cobblestone_bridge": {"criteria": {"has_planks": "2025-07-10 23:26:49 +0800"}, "done": true}, "mcwpaths:recipes/cobblestone": {"criteria": {"has_wood": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobblestone_slab_from_cobblestone_stonecutting": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stone": {"criteria": {"has_cobblestone": "2025-07-10 23:26:49 +0800"}, "done": true}, "silentgear:recipes/misc/stone_rod": {"criteria": {"has_item": "2025-07-10 23:26:49 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/blue_cobblestone_extra": {"criteria": {"has_material": "2025-07-10 23:26:49 +0800"}, "done": true}, "mcwbridges:recipes/cobblestone_bridge_stair": {"criteria": {"has_planks": "2025-07-10 23:26:49 +0800"}, "done": true}, "occultism:recipes/spirit_trade/spirit_trade/stone_to_otherstone": {"criteria": {"has_stone": "2025-07-10 23:27:14 +0800"}, "done": true}, "occultism:recipes/spirit_trade/spirit_trade/test": {"criteria": {"has_stone": "2025-07-10 23:27:14 +0800"}, "done": true}, "utilitix:recipes/misc/linked_repeater": {"criteria": {"criterion2": "2025-07-10 23:27:14 +0800"}, "done": true}, "mcwlights:recipes/torches": {"criteria": {"has_the_recipe": "2025-07-10 23:27:52 +0800"}, "done": true}, "mcwlights:recipes/tiki_torches": {"criteria": {"has_the_recipe": "2025-07-10 23:27:52 +0800"}, "done": true}, "minecraft:story/upgrade_tools": {"criteria": {"stone_pickaxe": "2025-07-10 23:27:52 +0800"}, "done": true}, "aether:recipes/tools/stone_pickaxe_repairing": {"criteria": {"has_stone_pickaxe": "2025-07-10 23:27:52 +0800"}, "done": true}, "mcwlights:recipes/lantern": {"criteria": {"has_the_recipe": "2025-07-10 23:27:52 +0800"}, "done": true}, "mcwlights:recipes/paper_lamp": {"criteria": {"has_the_recipe": "2025-07-10 23:27:52 +0800"}, "done": true}, "minecraft:recipes/decorations/torch": {"criteria": {"has_stone_pickaxe": "2025-07-10 23:27:52 +0800"}, "done": true}, "minecraft:recipes/building_blocks/raw_iron_block": {"criteria": {"has_raw_iron": "2025-07-10 23:28:01 +0800"}, "done": true}, "minecraft:recipes/misc/iron_ingot_from_smelting_raw_iron": {"criteria": {"has_raw_iron": "2025-07-10 23:28:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/iron_block_from_blasting": {"criteria": {"has_raw_iron": "2025-07-10 23:28:01 +0800"}, "done": true}, "minecraft:recipes/misc/iron_ingot_from_blasting_raw_iron": {"criteria": {"has_raw_iron": "2025-07-10 23:28:01 +0800"}, "done": true}, "forbidden_arcanus:recipes/misc/clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron": {"criteria": {"has_item": "2025-07-10 23:28:01 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/iron_block_from_smelting": {"criteria": {"has_raw_iron": "2025-07-10 23:28:01 +0800"}, "done": true}, "alchemistry:recipes/dissolver/raw_iron_block": {"criteria": {"has_the_recipe": "2025-07-10 23:28:01 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/iron_dust_from_raw": {"criteria": {"has_raw_iron": "2025-07-10 23:28:01 +0800"}, "done": true}, "minecraft:recipes/decorations/smoker": {"criteria": {"has_furnace": "2025-07-10 23:28:17 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/food_prep_table": {"criteria": {"has_item": "2025-07-10 23:28:17 +0800"}, "done": true}, "sfm:recipes/misc/printing_press": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "littlelogistics:recipes/transportation/seater_car": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/combat/iron_helmet": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/decorations/floor_trap": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_gray": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_light_gray": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "alltheores:recipes/misc/iron_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_nugget": {"criteria": {"has_the_recipe": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/combat/iron_boots": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "alltheores:recipes/misc/iron_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/red_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/bubble_blower": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_bars": {"criteria": {"has_the_recipe": "2025-07-10 23:47:39 +0800"}, "done": true}, "croptopia:recipes/misc/knife": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/speed_module": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "farmersdelight:recipes/combat/iron_knife": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/limited_use_keycard": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/green_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_pink": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailboots": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/decorations/iron_bars": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/post_box": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/inventoryplus_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/redstone/iron_door": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "travelersbackpack:recipes/misc/backpack_tank": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/machineinformation_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/fluidplus_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/clock_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/fridge_light": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "enderio:recipes/misc/fluid_tank": {"criteria": {"has_ingredient": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/bomb": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "silentgear:recipes/misc/sturdy_repair_kit": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_door": {"criteria": {"has_the_recipe": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailhelmet": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "littlelogistics:recipes/transportation/seater_barge": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/combat/iron_leggings": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwlights:recipes/lava_lamp": {"criteria": {"has_wood": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/hammer": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/counterplus_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "croptopia:recipes/misc/cooking_pot": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "mythicbotany:recipes/misc/rune_holder": {"criteria": {"criterion0": "2025-07-10 23:47:39 +0800"}, "done": true}, "occultism:recipes/combat/crafting/butcher_knife": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/iron_blacksmith_gavel": {"criteria": {"has_ingots/iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/machine_frame": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwbridges:recipes/iron_bridge_pier": {"criteria": {"has_planks": "2025-07-10 23:47:39 +0800"}, "done": true}, "railcraft:recipes/misc/water_tank_siding": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/redstone/hopper": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "aether:recipes/combat/iron_gloves": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/text_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "croptopia:recipes/misc/frying_pan": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/mine": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_red": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/coil_mv": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/coil_lv": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/pink_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/gray_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "utilitix:recipes/misc/minecart_tinkerer": {"criteria": {"criterion0": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/door_indestructible_iron_item": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "undergarden:recipes/tools/catalyst": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/combat/iron_chestplate": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_block": {"criteria": {"has_the_recipe": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:story/smelt_iron": {"criteria": {"iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/misc/combiner": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/combat/iron_sword": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/smart_module": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/misc/liquifier": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "ad_astra:recipes/misc/compressor": {"criteria": {"has_compressor": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwbridges:recipes/pliers": {"criteria": {"has_planks": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwlights:recipes/garden_light": {"criteria": {"has_wood": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/sentry": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_warning_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/smartwrench": {"criteria": {"iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_blue": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/black_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/tools/shears": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/building_blocks/iron_block": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/dissolver/chain": {"criteria": {"has_the_recipe": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/gray_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/shape_card_def": {"criteria": {"iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/tools/iron_shovel": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_full_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/combat/crossbow": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/camera_monitor": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "modularrouters:recipes/misc/modular_router": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/energy_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/transportation/minecart": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/redstone_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "ad_astra:recipes/compressing/compressing/iron_plate_from_compressing_iron_ingot": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/toolbox": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/goblet": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwlights:recipes/iron_stuff": {"criteria": {"has_wood": "2025-07-10 23:47:39 +0800"}, "done": true}, "travelersbackpack:recipes/misc/iron_tier_upgrade": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/purple_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/green_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/energyplus_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "enderio:recipes/misc/pressurized_fluid_tank": {"criteria": {"has_ingredient": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/lime_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "rechiseled:recipes/misc/chisel": {"criteria": {"recipe_condition": "2025-07-10 23:47:39 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reprocessor/casing": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "handcrafted:recipes/misc/hammer": {"criteria": {"has_hammer": "2025-07-10 23:47:39 +0800"}, "done": true}, "alltheores:recipes/misc/iron_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/black_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_trapdoor": {"criteria": {"has_the_recipe": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/misc/bucket": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/redstone/block_change_detector": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/upgrade_base": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_white": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "ad_astra:recipes/misc/coal_generator": {"criteria": {"has_coal_generator": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/stick_iron": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "simplemagnets:recipes/misc/basicmagnet": {"criteria": {"recipe_condition": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailchestplate": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/iron_gate": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/combat/shield": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "sfm:recipes/misc/manager": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_green": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/tools/iron_axe": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wirecutter": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwwindows:recipes/metal": {"criteria": {"has_wood": "2025-07-10 23:47:39 +0800"}, "done": true}, "sfm:recipes/misc/fancy_cable": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_yellow": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "create:recipes/building_blocks/industrial_iron_block_from_ingots_iron_stonecutting": {"criteria": {"has_ingots_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/misc/atomizer": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "ae2:recipes/misc/decorative/light_detector": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/connector_hv": {"criteria": {"has_aluminum_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_light_blue": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/storage_module": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/inventory_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/security_camera": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/decorations/lantern": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "nethersdelight:recipes/tools/iron_machete": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwdoors:recipes/metal": {"criteria": {"has_wood": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/brown_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "railcraft:recipes/misc/invar_ingot_crafted_with_ingots": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/connector_hv_relay": {"criteria": {"has_aluminum_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/redstone_module": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/tools/iron_hoe": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/tools/spatula": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/misc/compactor": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "simplemagnets:recipes/misc/basic_demagnetization_coil": {"criteria": {"recipe_condition": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/white_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "travelersbackpack:recipes/misc/hose_nozzle": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/harming_module": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "handcrafted:recipes/misc/bench": {"criteria": {"has_bench": "2025-07-10 23:47:39 +0800"}, "done": true}, "aether:recipes/combat/iron_ring": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_cyan": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/red_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "utilitix:recipes/misc/advanced_brewery": {"criteria": {"criterion2": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/blue_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwroofs:recipes/gutters": {"criteria": {"has_planks": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/fridge_dark": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "alchemistry:recipes/misc/dissolver": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/transportation/track_mine": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/andesite_alloy": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/lime_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailleggings": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "railcraft:recipes/misc/iron_gear": {"criteria": {"has_material": "2025-07-10 23:47:39 +0800"}, "done": true}, "chimes:recipes/decorations/iron_chimes": {"criteria": {"has_wood_slabs": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/white_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "travelersbackpack:recipes/misc/iron": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_fence_gate": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_orange": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv5": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv3": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv4": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv1": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv2": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwbridges:recipes/iron_bridge": {"criteria": {"has_planks": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/redstone/iron_trapdoor": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_magenta": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "utilitarian:recipes/misc/fluid_hopper": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/blacklist_module": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/button_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/bouncing_betty": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/pink_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/drillhead_iron": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/purple_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_trapdoor": {"criteria": {"has_logs": "2025-07-10 23:47:39 +0800"}, "done": true}, "create:recipes/misc/crafting/kinetics/empty_blaze_burner": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_brown": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/fluid_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/tools/iron_pickaxe": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_black": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_smithing_table": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "aether:recipes/combat/skyroot_iron_vanilla_shield": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "aether:recipes/combat/iron_pendant": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/spring_launcher": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "constructionwand:recipes/tools/iron_wand": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/brown_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_lime": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "sfm:recipes/misc/cable": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "nethersdelight:recipes/decorations/blackstone_blast_furnace": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/cage": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "farmersdelight:recipes/decorations/cooking_pot": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/storage_control_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/blue_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/whitelist_module": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/briefcase": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/orange_trampoline": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/electrified_iron_fence": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_purple": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "sfm:recipes/misc/fancy_to_cable": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/screwdriver": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/trophy_system": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/component_iron": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "silentgear:recipes/misc/iron_rod": {"criteria": {"has_item": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_remover": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/redstone/heavy_weighted_pressure_plate": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/plate_iron_hammering": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "securitycraft:recipes/misc/disguise_module": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "createoreexcavation:recipes/misc/drill": {"criteria": {"iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_ingot": {"criteria": {"has_iron_ingots": "2025-07-10 23:47:39 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fillet_knife": {"criteria": {"has_items": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/decorations/chain": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "minecraft:recipes/decorations/smithing_table": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/dump_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/counter_module": {"criteria": {"ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/orange_grill": {"criteria": {"has_iron": "2025-07-10 23:47:39 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/iron_dust_from_ingot": {"criteria": {"has_iron_ingot": "2025-07-10 23:47:39 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_bucket": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/jerrycan": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "trashcans:recipes/misc/liquid_trash_can": {"criteria": {"recipe_condition": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_kitchen_sink_light": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "enderio:recipes/misc/basic_fluid_filter": {"criteria": {"has_ingredient": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_sink_dark": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_sink": {"criteria": {"has_bucket": "2025-07-10 23:47:49 +0800"}, "done": true}, "alltheores:recipes/misc/nickel_ingot_from_raw": {"criteria": {"has_item": "2025-07-10 23:48:06 +0800"}, "done": true}, "alltheores:recipes/misc/nickel_ingot_from_raw_blasting": {"criteria": {"has_item": "2025-07-10 23:48:06 +0800"}, "done": true}, "alltheores:recipes/misc/raw_nickel_block": {"criteria": {"has_raw_TagKey[minecraft:item / forge:raw_materials/nickel]": "2025-07-10 23:48:06 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/nickel_dust_from_raw": {"criteria": {"has_raw_nickel": "2025-07-10 23:48:06 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_pickaxe": {"criteria": {"has_iron_pickaxe": "2025-07-10 23:48:26 +0800"}, "done": true}, "aether:recipes/tools/iron_pickaxe_repairing": {"criteria": {"has_iron_pickaxe": "2025-07-10 23:48:26 +0800"}, "done": true}, "minecraft:story/iron_tools": {"criteria": {"iron_pickaxe": "2025-07-10 23:48:26 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_iron_pickaxe": "2025-07-10 23:48:26 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_iron_pickaxe": "2025-07-10 23:48:26 +0800"}, "done": true}, "aether:recipes/building_blocks/blue_ice_freezing": {"criteria": {"has_water_bucket": "2025-07-10 23:48:47 +0800"}, "done": true}, "aether:recipes/building_blocks/packed_ice_freezing": {"criteria": {"has_water_bucket": "2025-07-10 23:48:47 +0800"}, "done": true}, "supplementaries:recipes/soap": {"criteria": {"has_water_bucket": "2025-07-10 23:48:47 +0800"}, "done": true}, "minecraft:recipes/brewing/cauldron": {"criteria": {"has_water_bucket": "2025-07-10 23:48:47 +0800"}, "done": true}, "minecraft:recipes/misc/ice_from_freezing": {"criteria": {"has_water": "2025-07-10 23:48:47 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/basic/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-10 23:48:47 +0800"}, "done": true}, "sfm:recipes/misc/water_tank": {"criteria": {"has_water": "2025-07-10 23:48:47 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/reinforced/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-10 23:48:47 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/reinforced/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-10 23:48:47 +0800"}, "done": true}, "aether:recipes/building_blocks/ice_from_bucket_freezing": {"criteria": {"has_water_bucket": "2025-07-10 23:48:47 +0800"}, "done": true}, "potionblender:recipes/brewing/brewing_cauldron": {"criteria": {"has_water_bucket": "2025-07-10 23:48:47 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/radiator": {"criteria": {"has_water_bucket": "2025-07-10 23:48:47 +0800"}, "done": true}, "croptopia:recipes/shaped_water_bottle": {"criteria": {"has_water": "2025-07-10 23:48:47 +0800"}, "done": true}, "minecraft:recipes/misc/copper_ingot_from_blasting_raw_copper": {"criteria": {"has_raw_copper": "2025-07-10 23:48:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/raw_copper_block": {"criteria": {"has_raw_copper": "2025-07-10 23:48:58 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/copper_block_from_blasting": {"criteria": {"has_raw_copper": "2025-07-10 23:48:58 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/copper_block_from_smelting": {"criteria": {"has_raw_copper": "2025-07-10 23:48:58 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/copper_dust_from_raw": {"criteria": {"has_raw_copper": "2025-07-10 23:48:58 +0800"}, "done": true}, "alchemistry:recipes/dissolver/raw_copper_block": {"criteria": {"has_the_recipe": "2025-07-10 23:48:58 +0800"}, "done": true}, "forbidden_arcanus:recipes/misc/clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper": {"criteria": {"has_item": "2025-07-10 23:48:58 +0800"}, "done": true}, "minecraft:recipes/misc/copper_ingot_from_smelting_raw_copper": {"criteria": {"has_raw_copper": "2025-07-10 23:48:58 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_deepslate": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "mcwpaths:recipes/cobbled_deepslate": {"criteria": {"has_wood": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_deepslate_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "alchemistry:recipes/dissolver/polished_deepslate": {"criteria": {"has_the_recipe": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobbled_deepslate_stairs": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/deepslate_tile_slab_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/decorations/deepslate_tile_wall_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_deepslate_stairs_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/decorations/deepslate_brick_wall_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/deepslate_brick_stairs_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/decorations/cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/deepslate_brick_slab_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobbled_deepslate_chain": {"criteria": {"recipe_condition": "2025-07-10 23:49:14 +0800"}, "done": true}, "alchemistry:recipes/dissolver/deepslate": {"criteria": {"has_the_recipe": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/decorations/cobbled_deepslate_wall": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/decorations/polished_deepslate_wall_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/deepslate_bricks_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cobbled_deepslate_slab": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cobbled_deepslate_slab": {"criteria": {"has_the_recipe": "2025-07-10 23:49:14 +0800"}, "done": true}, "mcwwindows:recipes/deepslate": {"criteria": {"has_wood": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_deepslate_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/deepslate_tiles_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_deepslate_slab_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/deepslate_tile_stairs_from_cobbled_deepslate_stonecutting": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/cobbled_deepslate_1x": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/building_blocks/deepslate": {"criteria": {"has_cobbled_deepslate": "2025-07-10 23:49:14 +0800"}, "done": true}, "minecraft:recipes/decorations/mossy_stone_brick_wall": {"criteria": {"has_mossy_stone_bricks": "2025-07-10 23:50:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_stone_brick_slab": {"criteria": {"has_mossy_stone_bricks": "2025-07-10 23:50:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_stone_bricks": {"criteria": {"has_tag": "2025-07-10 23:50:11 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_mossy_stone_bricks_bridge": {"criteria": {"has_planks": "2025-07-10 23:50:11 +0800"}, "done": true}, "minecraft:recipes/decorations/mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting": {"criteria": {"has_mossy_stone_bricks": "2025-07-10 23:50:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_stone_brick_stairs": {"criteria": {"has_mossy_stone_bricks": "2025-07-10 23:50:11 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stone_brick_slab": {"criteria": {"has_the_recipe": "2025-07-10 23:50:11 +0800"}, "done": true}, "alchemistry:recipes/dissolver/chiseled_stone_bricks": {"criteria": {"has_the_recipe": "2025-07-10 23:50:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting": {"criteria": {"has_mossy_stone_bricks": "2025-07-10 23:50:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting": {"criteria": {"has_mossy_stone_bricks": "2025-07-10 23:50:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stone_brick_stairs": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:11 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stone_brick_slab": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:11 +0800"}, "done": true}, "railcraft:recipes/misc/empty_detector": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "minecraft:recipes/decorations/stone_brick_wall": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_stone_bricks_from_stone_bricks_stonecutting": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_stone_bricks_bridge": {"criteria": {"has_planks": "2025-07-10 23:50:26 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cracked_stone_bricks": {"criteria": {"has_the_recipe": "2025-07-10 23:50:26 +0800"}, "done": true}, "supplementaries:recipes/stone_tile": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cracked_stone_bricks": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "mcwbridges:recipes/stone_bridge_pier": {"criteria": {"has_planks": "2025-07-10 23:50:26 +0800"}, "done": true}, "supplementaries:recipes/stonecutting/stone_tile": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "supplementaries:recipes/pedestal": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_bricks_chain": {"criteria": {"recipe_condition": "2025-07-10 23:50:26 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stone_brick_slab_from_stone_bricks_stonecutting": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "minecraft:recipes/building_blocks/stone_brick_stairs_from_stone_bricks_stonecutting": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "minecraft:recipes/decorations/stone_brick_wall_from_stone_bricks_stonecutting": {"criteria": {"has_stone_bricks": "2025-07-10 23:50:26 +0800"}, "done": true}, "mcwbridges:recipes/stone_brick_bridge": {"criteria": {"has_planks": "2025-07-10 23:50:26 +0800"}, "done": true}, "sliceanddice:recipes/misc/slicer": {"criteria": {"has_tool": "2025-07-10 23:51:15 +0800"}, "done": true}, "aether:recipes/tools/stone_axe_repairing": {"criteria": {"has_stone_axe": "2025-07-10 23:51:15 +0800"}, "done": true}, "minecraft:adventure/kill_a_mob": {"criteria": {"minecraft:skeleton": "2025-07-10 23:52:18 +0800"}, "done": true}, "tombstone:adventure/find_grave_dust": {"criteria": {"from_undead": "2025-07-10 23:52:18 +0800"}, "done": true}, "minecraft:adventure/kill_all_mobs": {"criteria": {"minecraft:skeleton": "2025-07-10 23:52:18 +0800", "minecraft:zombie": "2025-07-11 00:06:46 +0800"}, "done": false}, "minecraft:adventure/root": {"criteria": {"killed_something": "2025-07-10 23:52:18 +0800"}, "done": true}, "aether:recipes/combat/leather_chestplate_repairing": {"criteria": {"has_leather_chestplate": "2025-07-10 23:52:20 +0800"}, "done": true}, "tombstone:recipes/from_grave_dust": {"criteria": {"has_ingredient": "2025-07-10 23:52:20 +0800"}, "done": true}, "apotheosis:affix/root": {"criteria": {"sword": "2025-07-10 23:52:20 +0800"}, "done": true}, "apotheosis:affix/rare": {"criteria": {"sword": "2025-07-10 23:52:20 +0800"}, "done": true}, "apotheosis:affix/boss": {"criteria": {"sword": "2025-07-10 23:52:20 +0800"}, "done": true}, "lootr:1chest": {"criteria": {"opened_chest": "2025-07-10 23:52:35 +0800"}, "done": true}, "lootr:social": {"criteria": {"opened_chest": "2025-07-10 23:52:35 +0800"}, "done": false}, "minecraft:recipes/building_blocks/packed_mud": {"criteria": {"has_mud": "2025-07-10 23:53:27 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/yellow_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/black_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/orange_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/green_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/magenta_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/brown_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "minecraft:recipes/misc/orange_dye_from_torchflower": {"criteria": {"has_torchflower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/lime_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/purple_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/red_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "twigs:recipes/paper_lanterns/torchflower_paper_lantern": {"criteria": {"has_item": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/pink_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/cyan_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/white_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 23:53:29 +0800"}, "done": true}, "minecraft:recipes/misc/lime_dye_from_smelting": {"criteria": {"has_sea_pickle": "2025-07-10 23:53:34 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_sword": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/decorations/jukebox": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_rod": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-10 23:55:02 +0800"}, "done": true}, "createoreexcavation:recipes/misc/diamond_drill": {"criteria": {"diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "occultism:recipes/spirit_fire/spirit_fire/spirit_attuned_gem": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "alchemistry:recipes/dissolver/jukebox": {"criteria": {"has_the_recipe": "2025-07-10 23:55:02 +0800"}, "done": true}, "utilitix:recipes/misc/diamond_shears": {"criteria": {"criterion0": "2025-07-10 23:55:02 +0800"}, "done": true}, "farmersdelight:recipes/combat/diamond_knife": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_jukebox": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/diamond_blacksmith_gavel": {"criteria": {"has_gems/diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "ae2additions:recipes/misc/blocks/wireless_transceiver": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_plate": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-10 23:55:02 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_exchanging": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_hoe": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "travelersbackpack:recipes/misc/diamond_tier_upgrade": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "constructionwand:recipes/tools/diamond_wand": {"criteria": {"has_item": "2025-07-10 23:55:02 +0800"}, "done": true}, "ae2additions:recipes/misc/super_cell_housing": {"criteria": {"has_item": "2025-07-10 23:55:02 +0800"}, "done": true}, "silentgear:recipes/misc/diamond_shard": {"criteria": {"has_item": "2025-07-10 23:55:02 +0800"}, "done": true}, "aether:recipes/combat/diamond_gloves": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "additionallanterns:recipes/misc/diamond_chain": {"criteria": {"recipe_condition": "2025-07-10 23:55:02 +0800"}, "done": true}, "silentgear:recipes/misc/diamond_from_shards": {"criteria": {"has_item": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_axe": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_pickaxe": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "dimstorage:recipes/misc/dim_core": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_building": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_helmet": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:story/mine_diamond": {"criteria": {"diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "charginggadgets:recipes/redstone/charging_station": {"criteria": {"has_diamonds": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/building_blocks/diamond_block": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_gear": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_boots": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/diamond_dust_from_gem": {"criteria": {"has_diamond_gem": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_chestplate": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_shovel": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "travelersbackpack:recipes/misc/diamond": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_leggings": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "mininggadgets:recipes/misc/upgrade_empty": {"criteria": {"has_diamonds": "2025-07-10 23:55:02 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fillet_knife": {"criteria": {"has_items": "2025-07-10 23:55:02 +0800"}, "done": true}, "alchemistry:recipes/dissolver/diamond_block": {"criteria": {"has_the_recipe": "2025-07-10 23:55:02 +0800"}, "done": true}, "nethersdelight:recipes/tools/diamond_machete": {"criteria": {"has_diamond": "2025-07-10 23:55:02 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_blue_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_yellow_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "botania:recipes/redstone/red_string_container": {"criteria": {"has_base_block": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_red_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_blue_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/red_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_red_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "minecolonies:recipes/misc/supplycampdeployer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/blue_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_chest_from_vanilla_chest": {"criteria": {"has_vanilla_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_routing": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_yellow": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_blue_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/lime_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_routing": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/purple_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/black_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/brown_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_green": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard_no_tanks": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/pink_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/green_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/gray_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_yellow_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_red": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/orange_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "littlelogistics:recipes/transportation/barge": {"criteria": {"has_item": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_green_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_red_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_blue": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_yellow": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_blue": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/white_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/birch_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_yellow_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_green_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_green": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/warped_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/connector_green_dye": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/oak_mail_box": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_red": {"criteria": {"chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "trashcans:recipes/misc/item_trash_can": {"criteria": {"recipe_condition": "2025-07-10 23:55:32 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_cooler": {"criteria": {"has_chest": "2025-07-10 23:55:32 +0800"}, "done": true}, "croptopia:recipes/campfire_toast": {"criteria": {"has_bread": "2025-07-10 23:55:49 +0800"}, "done": true}, "croptopia:recipes/food/toast_from_smoking_bread": {"criteria": {"has_bread": "2025-07-10 23:55:49 +0800"}, "done": true}, "croptopia:recipes/cheeseburger": {"criteria": {"has_bread": "2025-07-10 23:55:49 +0800"}, "done": true}, "croptopia:recipes/food/toast_from_bread": {"criteria": {"has_bread": "2025-07-10 23:55:49 +0800"}, "done": true}, "minecraft:husbandry/balanced_diet": {"criteria": {"cooked_beef": "2025-07-10 23:56:01 +0800", "bread": "2025-07-10 23:55:57 +0800"}, "done": false}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2025-07-10 23:55:57 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/rs_engineering": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "alchemistry:recipes/dissolver/redstone_block": {"criteria": {"has_the_recipe": "2025-07-10 23:57:08 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_smart_fluix": {"criteria": {"has_dusts/redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "sfm:recipes/misc/disk": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "appmek:recipes/misc/chemical_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "littlelogistics:recipes/tools/locomotive_route": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "utilitix:recipes/misc/linked_crystal": {"criteria": {"criterion0": "2025-07-10 23:57:08 +0800"}, "done": true}, "littlelogistics:recipes/redstone/vessel_charger": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/redstone_information": {"criteria": {"redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "trashcans:recipes/misc/energy_trash_can": {"criteria": {"recipe_condition": "2025-07-10 23:57:08 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/rose_quartz": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/misc/username_logger": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "utilitix:recipes/misc/dimmable_redstone_lamp": {"criteria": {"criterion1": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_key_changer": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/misc/claymore": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "railcraft:recipes/misc/radio_circuit": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/basic/passivetap_fe": {"criteria": {"has_item2": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl1": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl3": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl2": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "supplementaries:recipes/altimeter": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "minecraft:recipes/redstone/dropper": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_modifier": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/misc/cage_trap": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "minecraft:recipes/redstone/piston": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "railcraft:recipes/misc/signal_circuit": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/redstone_acid": {"criteria": {"has_redstone_dust": "2025-07-10 23:57:08 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_note_block": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/pulse_repeater": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "minecraft:recipes/redstone/target": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "minecraft:recipes/tools/compass": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "modularrouters:recipes/misc/blank_module": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/reinforced/passivetap_fe": {"criteria": {"has_item2": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_redstone_lamp": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "railcraft:recipes/misc/controller_circuit": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "rftoolspower:recipes/rftoolspower/power_core1": {"criteria": {"core": "2025-07-10 23:57:08 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_piston": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/reinforced/passivetap_fe": {"criteria": {"has_item2": "2025-07-10 23:57:08 +0800"}, "done": true}, "botania:recipes/misc/redstone_root": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_piston": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/powered_latch": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "minecraft:recipes/redstone/note_block": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "travelersbackpack:recipes/misc/redstone": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/pulse_extender": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/fluid_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "quarryplus:recipes/workbench": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/tools/taser": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "littlelogistics:recipes/tools/tug_route": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/screen_link": {"criteria": {"redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/basic/passivetap_fe": {"criteria": {"has_item2": "2025-07-10 23:57:08 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/storage_module0": {"criteria": {"redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/smart_fluix": {"criteria": {"has_dusts/redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/misc/portable_tune_player": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/item_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/redstone/alarm": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "supplementaries:recipes/sconce_lever": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/misc/keypad_frame": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_torch": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "railcraft:recipes/misc/receiver_circuit": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/redstone/portable_radar": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "supplementaries:recipes/redstone_illuminator": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "railcraft:recipes/misc/signal_tuner": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_block": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dropper": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "minecraft:recipes/tools/clock": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "railcraft:recipes/misc/signal_block_surveyor": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "utilitarian:recipes/misc/redstone_clock": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "computercraft:recipes/redstone/computer_normal": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "rftoolscontrol:recipes/rftoolscontrol/card_base": {"criteria": {"redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/powered_toggle_latch": {"criteria": {"has_item": "2025-07-10 23:57:08 +0800"}, "done": true}, "securitycraft:recipes/redstone/panic_button": {"criteria": {"has_redstone": "2025-07-10 23:57:08 +0800"}, "done": true}, "create:recipes/building_blocks/cut_tuff_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/polished_cut_tuff_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/cut_tuff_stairs_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "twigs:recipes/tuff/tuff_wall": {"criteria": {"has_item": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/polished_cut_tuff_wall_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/cut_tuff_brick_slab_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "twigs:recipes/tuff/tuff_stairs": {"criteria": {"has_item": "2025-07-10 23:57:55 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/tuff_1x": {"criteria": {"has_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/polished_cut_tuff_slab_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "twigs:recipes/tuff/tuff_stairs_stonecutting": {"criteria": {"has_item": "2025-07-10 23:57:55 +0800"}, "done": true}, "twigs:recipes/tuff/polished_tuff_bricks_from_bloodstone_stonecutting": {"criteria": {"has_item": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/layered_tuff_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/small_tuff_brick_slab_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "twigs:recipes/tuff/polished_tuff": {"criteria": {"has_item": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/tuff_pillar_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/small_tuff_brick_stairs_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/small_tuff_bricks_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "twigs:recipes/tuff/tuff_slab": {"criteria": {"has_item": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/tuff_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/cut_tuff_wall_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:55 +0800"}, "done": true}, "create:recipes/building_blocks/cut_tuff_brick_wall_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:56 +0800"}, "done": true}, "create:recipes/building_blocks/cut_tuff_slab_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:56 +0800"}, "done": true}, "create:recipes/building_blocks/cut_tuff_bricks_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:56 +0800"}, "done": true}, "create:recipes/building_blocks/polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:56 +0800"}, "done": true}, "twigs:recipes/tuff/tuff_slab_stonecutting": {"criteria": {"has_item": "2025-07-10 23:57:56 +0800"}, "done": true}, "twigs:recipes/tuff/polished_tuff_stonecutting": {"criteria": {"has_item": "2025-07-10 23:57:56 +0800"}, "done": true}, "twigs:recipes/tuff/tuff_wall_stonecutting": {"criteria": {"has_item": "2025-07-10 23:57:56 +0800"}, "done": true}, "create:recipes/building_blocks/small_tuff_brick_wall_from_stone_types_tuff_stonecutting": {"criteria": {"has_stone_types_tuff": "2025-07-10 23:57:56 +0800"}, "done": true}, "aether:recipes/tools/diamond_pickaxe_repairing": {"criteria": {"has_diamond_pickaxe": "2025-07-10 23:58:21 +0800"}, "done": true}, "quarryplus:recipes/solid_fuel_quarry": {"criteria": {"has_diamond_pickaxe": "2025-07-10 23:58:21 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/obsidian_1x": {"criteria": {"has_obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "utilitarian:recipes/misc/angel_block_rot": {"criteria": {"has_obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "minecraft:recipes/decorations/enchanting_table": {"criteria": {"has_obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/obsidian_dust": {"criteria": {"has_obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "utilitarian:recipes/misc/angel_block": {"criteria": {"has_obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/obsidian_skull": {"criteria": {"has_obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "utilitarian:recipes/misc/tps_meter": {"criteria": {"has_obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "additionallanterns:recipes/misc/obsidian_chain": {"criteria": {"recipe_condition": "2025-07-10 23:58:51 +0800"}, "done": true}, "minecraft:recipes/tools/flint_and_steel": {"criteria": {"has_obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "minecraft:story/form_obsidian": {"criteria": {"obsidian": "2025-07-10 23:58:51 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/filter_material": {"criteria": {"has_gravel": "2025-07-11 00:00:10 +0800"}, "done": true}, "minecraft:recipes/building_blocks/coarse_dirt": {"criteria": {"has_gravel": "2025-07-11 00:00:10 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/beige_stone_bricks": {"criteria": {"has_item1_domum_ornamentum_beige_stone_bricks": "2025-07-11 00:00:10 +0800", "has_item2_domum_ornamentum_beige_stone_bricks": "2025-07-11 00:00:10 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/beige_bricks": {"criteria": {"has_item1_domum_ornamentum_beige_bricks": "2025-07-11 00:00:10 +0800", "has_item2_domum_ornamentum_beige_bricks": "2025-07-11 00:00:10 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/gravel_1x": {"criteria": {"has_gravel": "2025-07-11 00:00:10 +0800"}, "done": true}, "twigs:recipes/rocky_dirt": {"criteria": {"has_item": "2025-07-11 00:00:10 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/coarse_turquoise_dirt": {"criteria": {"has_item": "2025-07-11 00:00:10 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/coarse_lunar_dirt": {"criteria": {"has_item": "2025-07-11 00:00:10 +0800"}, "done": true}, "twigs:recipes/cobblestone_from_pebble": {"criteria": {"has_item": "2025-07-11 00:00:10 +0800"}, "done": true}, "twigs:recipes/gravel_bricks/gravel_bricks": {"criteria": {"has_item": "2025-07-11 00:00:10 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_fletching_table": {"criteria": {"has_flint": "2025-07-11 00:00:11 +0800"}, "done": true}, "minecraft:recipes/combat/arrow": {"criteria": {"has_flint": "2025-07-11 00:00:11 +0800"}, "done": true}, "supplementaries:recipes/flint_block": {"criteria": {"has_flint": "2025-07-11 00:00:11 +0800"}, "done": true}, "minecraft:recipes/decorations/fletching_table": {"criteria": {"has_flint": "2025-07-11 00:00:11 +0800"}, "done": true}, "aether:recipes/tools/flint_and_steel_repairing": {"criteria": {"has_flint_and_steel": "2025-07-11 00:01:17 +0800"}, "done": true}, "minecraft:nether/root": {"criteria": {"entered_nether": "2025-07-11 00:01:57 +0800"}, "done": true}, "silentgear:nether": {"criteria": {"entered_nether": "2025-07-11 00:01:57 +0800"}, "done": true}, "minecraft:story/enter_the_nether": {"criteria": {"entered_nether": "2025-07-11 00:01:57 +0800"}, "done": true}, "minecraft:nether/explore_nether": {"criteria": {"minecraft:nether_wastes": "2025-07-11 00:05:17 +0800", "minecraft:soul_sand_valley": "2025-07-11 00:05:04 +0800"}, "done": false}, "minecraft:nether/fast_travel": {"criteria": {"travelled": "2025-07-11 00:05:38 +0800"}, "done": true}, "croptopia:recipes/fruit_smoothie": {"criteria": {"has_fruit": "2025-07-11 00:06:01 +0800"}, "done": true}, "integrateddynamics:root": {"criteria": {"criteria_0": "2025-07-11 00:06:01 +0800"}, "done": true}, "integrateddynamics:meneglin_basics/meneglin_discovery": {"criteria": {"criteria_0": "2025-07-11 00:06:14 +0800"}, "done": true}, "botania:recipes/tools/flower_bag": {"criteria": {"has_item": "2025-07-11 00:06:18 +0800"}, "done": true}, "botania:recipes/misc/petal_white": {"criteria": {"has_item": "2025-07-11 00:06:18 +0800"}, "done": true}, "botania:main/flower_pickup": {"criteria": {"flower": "2025-07-11 00:06:18 +0800"}, "done": true}, "botania:recipes/decorations/white_shiny_flower": {"criteria": {"has_item": "2025-07-11 00:06:18 +0800"}, "done": true}, "botania:challenge/root": {"criteria": {"flower": "2025-07-11 00:06:18 +0800"}, "done": true}, "botania:main/root": {"criteria": {"flower": "2025-07-11 00:06:18 +0800"}, "done": true}, "botania:main/flower_pickup_lexicon": {"criteria": {"flower": "2025-07-11 00:06:18 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cherry_planks": {"criteria": {"has_log": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_barred_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_classic_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800", "has_the_recipe": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_paper_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwfurnitures:recipes/cherry": {"criteria": {"has_wood": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_mystic_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_tropical_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_beach_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/cherry_logs_to_stairs": {"criteria": {"has_log": "2025-07-11 00:07:18 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/cherry_logs_to_boats": {"criteria": {"has_log": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwroofs:recipes/cherry": {"criteria": {"has_planks": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_glass_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_swamp_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_whispering_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/cherry_logs_to_slabs": {"criteria": {"has_log": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwwindows:recipes/cherry": {"criteria": {"has_wood": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwdoors:recipes/cherry": {"criteria": {"has_wood": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwbridges:recipes/cherry_bridge_pier": {"criteria": {"has_planks": "2025-07-11 00:07:18 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/cherry_logs_to_trapdoors": {"criteria": {"has_log": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_barn_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cherry_wood": {"criteria": {"has_log": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_four_panel_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_cottage_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "mcwtrpdoors:recipes/cherry/cherry_barrel_trapdoor": {"criteria": {"has_logs": "2025-07-11 00:07:18 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/cherry_logs_to_pressure_plates": {"criteria": {"has_log": "2025-07-11 00:07:18 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/cherry_logs_to_doors": {"criteria": {"has_log": "2025-07-11 00:07:18 +0800"}, "done": true}, "silentgear:recipes/misc/sinew_fiber": {"criteria": {"has_item": "2025-07-11 00:08:40 +0800"}, "done": true}, "silentgear:recipes/misc/dried_sinew": {"criteria": {"has_item": "2025-07-11 00:08:40 +0800"}, "done": true}, "farmersdelight:recipes/cooking/pasta_with_meatballs": {"criteria": {"has_any_ingredient": "2025-07-11 00:08:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cooked_beef": {"criteria": {"has_the_recipe": "2025-07-11 00:08:41 +0800"}, "done": true}, "croptopia:recipes/burrito": {"criteria": {"has_the_recipe": "2025-07-11 00:08:41 +0800"}, "done": true}, "croptopia:recipes/stuffed_poblanos": {"criteria": {"has_beef": "2025-07-11 00:08:41 +0800"}, "done": true}, "farmersdelight:recipes/cooking/beef_stew": {"criteria": {"has_any_ingredient": "2025-07-11 00:08:41 +0800"}, "done": true}, "croptopia:recipes/beef_jerky": {"criteria": {"has_beef": "2025-07-11 00:08:41 +0800"}, "done": true}, "croptopia:recipes/food/beef_jerky": {"criteria": {"has_the_recipe": "2025-07-11 00:08:41 +0800"}, "done": true}, "croptopia:recipes/shaped_beef_stew": {"criteria": {"has_beef": "2025-07-11 00:08:41 +0800"}, "done": true}, "minecraft:recipes/food/cooked_beef_from_smoking": {"criteria": {"has_beef": "2025-07-11 00:08:41 +0800"}, "done": true}, "croptopia:recipes/hamburger": {"criteria": {"has_beef": "2025-07-11 00:08:41 +0800"}, "done": true}, "minecraft:recipes/food/cooked_beef_from_grill_cooking": {"criteria": {"has_beef": "2025-07-11 00:08:41 +0800"}, "done": true}, "minecraft:recipes/food/cooked_beef_from_campfire_cooking": {"criteria": {"has_beef": "2025-07-11 00:08:41 +0800"}, "done": true}, "croptopia:recipes/pork_jerky": {"criteria": {"has_the_recipe": "2025-07-11 00:08:41 +0800"}, "done": true}, "croptopia:recipes/food/pork_jerky": {"criteria": {"has_the_recipe": "2025-07-11 00:08:41 +0800"}, "done": true}, "minecraft:recipes/food/cooked_beef": {"criteria": {"has_beef": "2025-07-11 00:08:41 +0800"}, "done": true}, "minecraft:recipes/combat/leather_chestplate": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "aether:recipes/tools/aether_saddle": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "minecraft:recipes/combat/leather_boots": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/conveyor_basic": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/backpack": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "minecraft:recipes/misc/leather_horse_armor": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "supplementaries:recipes/bellows": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "travelersbackpack:recipes/misc/horse": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "aether:recipes/combat/leather_gloves": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "railcraft:recipes/misc/villager_detector": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "minecraft:recipes/decorations/item_frame": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/powerpack": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "minecraft:recipes/combat/leather_helmet": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "toolbelt:recipes/pouch": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/upgrade_base": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "minecraft:recipes/combat/leather_leggings": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "travelersbackpack:recipes/misc/blank_upgrade": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "toolbelt:recipes/belt": {"criteria": {"has_leather": "2025-07-11 00:08:43 +0800"}, "done": true}, "minecraft:recipes/misc/red_dye_from_rose_bush": {"criteria": {"has_rose_bush": "2025-07-11 00:08:57 +0800"}, "done": true}, "biomesoplenty:recipes/misc/white_dye_from_tall_white_lavender": {"criteria": {"has_tall_white_lavender": "2025-07-11 00:11:13 +0800"}, "done": true}, "minecraft:recipes/misc/yellow_dye_from_sunflower": {"criteria": {"has_sunflower": "2025-07-11 00:11:18 +0800"}, "done": true}, "croptopia:recipes/misc/roasted_sunflower_seeds": {"criteria": {"has_sunflower": "2025-07-11 00:11:18 +0800"}, "done": true}, "biomesoplenty:recipes/misc/magenta_dye_from_wildflower": {"criteria": {"has_wildflower": "2025-07-11 00:11:18 +0800"}, "done": true}, "DataVersion": 3465}