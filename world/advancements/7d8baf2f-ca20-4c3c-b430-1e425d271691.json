{"undergarden:undergarden/root": {"criteria": {"tick": "2025-07-26 13:33:42 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-26 13:33:42 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-26 13:33:42 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-26 13:33:42 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-26 13:33:42 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-26 13:33:42 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-26 13:33:42 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-26 13:33:42 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-26 13:33:42 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-26 13:33:42 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-26 13:33:42 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-26 13:33:42 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-26 13:33:42 +0800"}, "done": false}, "lootr:root": {"criteria": {"always_true": "2025-07-26 13:33:42 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-26 13:33:42 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-26 13:33:46 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:bamboo_jungle": "2025-07-26 14:07:00 +0800", "minecraft:beach": "2025-07-26 13:33:46 +0800", "minecraft:lukewarm_ocean": "2025-07-26 13:54:41 +0800", "minecraft:river": "2025-07-26 14:01:33 +0800", "minecraft:desert": "2025-07-26 13:38:02 +0800", "minecraft:jungle": "2025-07-26 13:55:20 +0800", "minecraft:warm_ocean": "2025-07-26 14:01:45 +0800", "minecraft:deep_lukewarm_ocean": "2025-07-26 14:02:21 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/all_biomes": {"criteria": {"lush_savanna": "2025-07-26 13:37:46 +0800", "rocky_rainforest": "2025-07-26 13:36:14 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/root": {"criteria": {"rocky_rainforest": "2025-07-26 13:36:14 +0800"}, "done": true}, "maidensmerrymaking:easter/root": {"criteria": {"requirement": "2025-07-26 13:36:20 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:44 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:44 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwwindows:recipes/bricks": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-26 13:36:44 +0800", "has_axe": "2025-07-26 13:36:45 +0800", "has_helmet": "2025-07-26 13:36:45 +0800", "has_fishing_rod": "2025-07-26 13:36:45 +0800", "has_bow": "2025-07-26 13:36:44 +0800", "has_chestplate": "2025-07-26 13:36:45 +0800", "has_sword": "2025-07-26 13:36:45 +0800", "has_fillet_knife": "2025-07-26 13:36:45 +0800", "has_shovel": "2025-07-26 13:36:44 +0800", "has_hoe": "2025-07-26 13:36:45 +0800", "has_boots": "2025-07-26 13:36:45 +0800", "has_pickaxe": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barn_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:44 +0800"}, "done": true}, "securitycraft:recipes/misc/sc_manual": {"criteria": {"has_wood": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:44 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwwindows:recipes/diorite": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "cfm:recipes/decorations/oak_desk_cabinet": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_mystic_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwwindows:recipes/granite": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "cfm:recipes/decorations/oak_cabinet": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_swamp_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:44 +0800"}, "done": true}, "naturescompass:natures_compass": {"criteria": {"has_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-26 13:36:44 +0800", "has_fillet_knife": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwwindows:recipes/birch": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "naturescompass:natures_compass_log": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_crate": {"criteria": {"has_planks": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwroofs:recipes/oak": {"criteria": {"has_planks": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_ranch_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:44 +0800"}, "done": true}, "railcraft:recipes/coke_oven/charcoal": {"criteria": {"has_logs": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:44 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_slabs": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwwindows:recipes/prismarine": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwwindows:recipes/dark_oak": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwwindows:recipes/components": {"criteria": {"has_wood": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_glass_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:44 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-26 13:36:44 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_doors": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwwindows:recipes/jungle": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "cfm:recipes/decorations/oak_bedside_cabinet": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_bedside_cabinet": {"criteria": {"has_planks": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:44 +0800"}, "done": true}, "naturescompass:natures_compass_sapling": {"criteria": {"has_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-26 13:36:44 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_cabinet": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_four_panel_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:44 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:44 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-26 13:36:45 +0800", "has_fillet_knife": "2025-07-26 13:36:44 +0800"}, "done": true}, "cfm:recipes/decorations/oak_crate": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-26 13:36:44 +0800", "has_wct": "2025-07-26 13:36:45 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-26 13:36:44 +0800", "has_wct": "2025-07-26 13:36:44 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-26 13:36:45 +0800", "has_wpt": "2025-07-26 13:36:44 +0800"}, "done": true}, "cfm:recipes/decorations/oak_upgraded_gate": {"criteria": {"has_log": "2025-07-26 13:36:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/charcoal": {"criteria": {"has_the_recipe": "2025-07-26 13:36:44 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/dark_prismarine": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-26 13:36:45 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-26 13:36:45 +0800", "has_carrot": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_beach_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barred_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/acacia/oak_bark_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_barrel_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_trapdoors": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_drawer": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-26 13:36:45 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_pressure_plates": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-26 13:36:45 +0800", "has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_tropical_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/sandstone": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-26 13:36:45 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-26 13:36:45 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/curtain_rods": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_upgraded_fence": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_desk": {"criteria": {"has_planks": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_table": {"criteria": {"has_planks": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/oak": {"criteria": {"has_wood": "2025-07-26 13:36:45 +0800", "has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_chair": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_desk": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/oak_log_1x": {"criteria": {"has_oak_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfurnitures:recipes/oak": {"criteria": {"has_wood": "2025-07-26 13:36:45 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_wood": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-26 13:36:45 +0800", "has_wpt": "2025-07-26 13:36:45 +0800", "has_wct": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/blackstone": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "utilitarian:recipes/food/utility/charcoal_from_campfire": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/print_classic": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_park_bench": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-26 13:36:45 +0800", "has_fillet_knife": "2025-07-26 13:36:45 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/acacia": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_coffee_table": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/quartz": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/spruce": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_counter": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/mangrove": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/red_sandstone": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/metal": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/architectscutter": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/cherry": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/logs_to_bowls": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_whispering_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_cottage_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/deepslate": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "enderio:recipes/misc/stick": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_coffee_table": {"criteria": {"has_planks": "2025-07-26 13:36:45 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/andesite": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbridges:recipes/oak_bridge_pier": {"criteria": {"has_planks": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwdoors:recipes/oak": {"criteria": {"has_wood": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-26 13:36:45 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_drawer": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_blinds": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_stairs": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_windows": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_table": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_planks": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/oak_logs_to_boats": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_counter": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_desk_cabinet": {"criteria": {"has_planks": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/oak_park_bench": {"criteria": {"has_log": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/warped": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_paper_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/stone": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwwindows:recipes/crimson": {"criteria": {"has_the_recipe": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-26 13:36:45 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_chair": {"criteria": {"has_planks": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-26 13:36:45 +0800", "has_fillet_knife": "2025-07-26 13:36:45 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-26 13:36:45 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-26 13:36:45 +0800"}, "done": true}, "delightful:recipes/campfire/roasted_acorn": {"criteria": {"roasted_acorn": "2025-07-26 13:37:03 +0800"}, "done": true}, "croptopia:recipes/roasted_nuts_from_campfire": {"criteria": {"has_nut": "2025-07-26 13:37:03 +0800"}, "done": true}, "croptopia:recipes/trail_mix": {"criteria": {"has_nuts": "2025-07-26 13:37:03 +0800"}, "done": true}, "delightful:recipes/storage/acorn_storage_block": {"criteria": {"has_acorn": "2025-07-26 13:37:03 +0800"}, "done": true}, "delightful:recipes/food/baklava": {"criteria": {"has_nuts": "2025-07-26 13:37:03 +0800"}, "done": true}, "delightful:recipes/smelting/roasted_acorn": {"criteria": {"roasted_acorn": "2025-07-26 13:37:03 +0800"}, "done": true}, "delightful:recipes/smoking/roasted_acorn": {"criteria": {"roasted_acorn": "2025-07-26 13:37:03 +0800"}, "done": true}, "croptopia:recipes/roasted_nuts": {"criteria": {"has_nut": "2025-07-26 13:37:03 +0800"}, "done": true}, "delightful:recipes/food/cooking/nut_butter_bottle": {"criteria": {"has_nuts": "2025-07-26 13:37:03 +0800"}, "done": true}, "croptopia:recipes/candied_nuts": {"criteria": {"has_nut": "2025-07-26 13:37:03 +0800"}, "done": true}, "croptopia:recipes/candy_corn": {"criteria": {"has_the_recipe": "2025-07-26 13:37:03 +0800"}, "done": true}, "croptopia:recipes/roasted_smoking": {"criteria": {"has_nut": "2025-07-26 13:37:03 +0800"}, "done": true}, "croptopia:recipes/nutty_cookie": {"criteria": {"has_nuts": "2025-07-26 13:37:03 +0800"}, "done": true}, "croptopia:recipes/nougat": {"criteria": {"has_nuts": "2025-07-26 13:37:03 +0800", "has_the_recipe": "2025-07-26 13:37:03 +0800"}, "done": true}, "twigs:recipes/stick_from_twig": {"criteria": {"has_item": "2025-07-26 13:37:05 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_swamp_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_ranch_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_trapdoors": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_counter": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_crate": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_boats": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwfurnitures:recipes/jungle": {"criteria": {"has_wood": "2025-07-26 13:37:13 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_pressure_plates": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_whispering_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwroofs:recipes/jungle": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_drawer": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_counter": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_park_bench": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_table": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwbridges:recipes/jungle_bridge_pier": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_desk": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_bedside_cabinet": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_blinds": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_crate": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_bedside_cabinet": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_coffee_table": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_desk_cabinet": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_stairs": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_mystic_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_paper_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_slabs": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_table": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_cabinet": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_chair": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_four_panel_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_upgraded_fence": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/acacia/jungle_bark_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/jungle_log_1x": {"criteria": {"has_jungle_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/jungle_logs_to_doors": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/print_beach": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_desk": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_wood": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_cabinet": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_tropical_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_glass_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_desk_cabinet": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barred_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_park_bench": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwdoors:recipes/jungle": {"criteria": {"has_wood": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barrel_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_upgraded_gate": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_barn_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_drawer": {"criteria": {"has_log": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_chair": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_coffee_table": {"criteria": {"has_planks": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_cottage_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_planks": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_classic_trapdoor": {"criteria": {"has_logs": "2025-07-26 13:37:13 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_boat": {"criteria": {"in_water": "2025-07-26 13:37:37 +0800"}, "done": true}, "mcwbridges:recipes/rope_jungle_bridge": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "sfm:recipes/misc/printing_press": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "littlelogistics:recipes/transportation/seater_car": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_jungle_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_glass_pane": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/raw_iron_block": {"criteria": {"has_raw_iron": "2025-07-26 15:04:21 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_sword": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "minecraft:recipes/decorations/jukebox": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_magenta": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/combat/iron_helmet": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "twigs:recipes/tables/spruce_table": {"criteria": {"has_bamboo": "2025-07-26 15:20:16 +0800"}, "done": true}, "securitycraft:recipes/decorations/floor_trap": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/combat/leather_chestplate": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "minecraft:recipes/decorations/yellow_candle": {"criteria": {"has_yellow_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_table": {"criteria": {"has_spruce_table": "2025-07-26 15:20:16 +0800"}, "done": true}, "railcraft:recipes/misc/golden_ticket": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "farmersdelight:recipes/food/fried_egg_from_campfire_cooking": {"criteria": {"fried_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_pickaxe": {"criteria": {"has_iron_pickaxe": "2025-07-26 15:02:33 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_gray": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-26 14:07:53 +0800"}, "done": true}, "xnet:recipes/xnet/connector_blue_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "aether:recipes/tools/aether_saddle": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_magenta": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "minecraft:recipes/misc/red_dye_from_poppy": {"criteria": {"has_poppy": "2025-07-26 14:08:43 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/resonating_plate_block": {"criteria": {"has_resonant_plate": "2025-07-26 15:11:16 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_light_gray": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "alltheores:recipes/misc/iron_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/spruce_limited_barrel_2": {"criteria": {"has_spruce_plank": "2025-07-26 15:20:16 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/spruce_limited_barrel_1": {"criteria": {"has_spruce_plank": "2025-07-26 15:20:16 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/spruce_limited_barrel_4": {"criteria": {"has_spruce_plank": "2025-07-26 15:20:16 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/spruce_limited_barrel_3": {"criteria": {"has_spruce_plank": "2025-07-26 15:20:16 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_stairs": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_nugget": {"criteria": {"has_the_recipe": "2025-07-26 15:00:05 +0800"}, "done": true}, "croptopia:recipes/campfire_toast": {"criteria": {"has_bread": "2025-07-26 14:16:24 +0800"}, "done": true}, "handcrafted:recipes/misc/bear_trophy": {"criteria": {"has_bear_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "utilitix:recipes/misc/armed_stand": {"criteria": {"criterion1": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/combat/iron_boots": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "travelersbackpack:recipes/misc/skeleton": {"criteria": {"has_arrow": "2025-07-26 14:15:44 +0800"}, "done": true}, "twigs:recipes/tables/oak_table": {"criteria": {"has_bamboo": "2025-07-26 15:20:51 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_gray_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/gold_block_from_smelting": {"criteria": {"has_raw_gold": "2025-07-26 15:06:41 +0800"}, "done": true}, "supplementaries:recipes/sign_post_jungle": {"criteria": {"has_the_recipe": "2025-07-26 13:58:20 +0800"}, "done": true}, "alltheores:recipes/misc/iron_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_trapdoor": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "cfm:recipes/decorations/red_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/rs_engineering": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "supplementaries:recipes/bubble_blower": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/food/cooked_salmon_from_campfire_cooking": {"criteria": {"has_salmon": "2025-07-26 14:16:16 +0800"}, "done": true}, "croptopia:recipes/onion_rings": {"criteria": {"has_onions": "2025-07-26 14:08:43 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_bars": {"criteria": {"has_the_recipe": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_dye": {"criteria": {"has_yellow_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "farmersdelight:recipes/cooking/pasta_with_meatballs": {"criteria": {"has_any_ingredient": "2025-07-26 14:16:24 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_yellow_dye": {"criteria": {"forge:dyes/yellow": "2025-07-26 15:00:02 +0800"}, "done": true}, "additionallanterns:recipes/misc/end_stone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "alchemistry:recipes/dissolver/redstone_block": {"criteria": {"has_the_recipe": "2025-07-26 15:05:56 +0800"}, "done": true}, "handcrafted:recipes/misc/goat_trophy": {"criteria": {"has_goat_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "aether:recipes/combat/leather_helmet_repairing": {"criteria": {"has_leather_helmet": "2025-07-26 14:07:09 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_light_gray": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/persimmon_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dark_oak_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_black": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "minecraft:recipes/food/cooked_salmon_from_grill_cooking": {"criteria": {"has_salmon": "2025-07-26 14:16:16 +0800"}, "done": true}, "croptopia:recipes/misc/knife": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_smoking": {"criteria": {"has_porkchop": "2025-07-26 14:08:42 +0800"}, "done": true}, "minecraft:recipes/combat/leather_boots": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_smart_fluix": {"criteria": {"has_dusts/redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "securitycraft:recipes/misc/speed_module": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_dark_oak_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_oak_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "farmersdelight:recipes/combat/iron_knife": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "mcwbridges:recipes/asian_red_bridge_pier": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/flippers_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "sfm:recipes/misc/disk": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_bamboo_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/vehicle_status_module": {"criteria": {"paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wirecoil_structure_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/yellow_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "supplementaries:recipes/hourglass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "travelersbackpack:recipes/misc/coal": {"criteria": {"has_coal": "2025-07-26 14:17:44 +0800"}, "done": true}, "supplementaries:recipes/timber_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:story/obtain_armor": {"criteria": {"iron_leggings": "2025-07-26 14:15:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/shovel_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_rod": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-26 14:17:46 +0800"}, "done": true}, "twigs:recipes/tables/jungle_table": {"criteria": {"has_jungle": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/food/baked_potato_from_campfire_cooking": {"criteria": {"has_potato": "2025-07-26 14:16:24 +0800"}, "done": true}, "create:recipes/building_blocks/vertical_framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/green_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitix:recipes/misc/reinforced_rail": {"criteria": {"criterion0": "2025-07-26 15:00:05 +0800"}, "done": true}, "silentgear:recipes/misc/stone_torch": {"criteria": {"has_item": "2025-07-26 14:17:44 +0800"}, "done": true}, "securitycraft:recipes/misc/limited_use_keycard": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/green_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "createoreexcavation:recipes/misc/diamond_drill": {"criteria": {"diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "minecraft:recipes/decorations/spruce_sign": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_pink": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/range_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_table": {"criteria": {"has_jungle_table": "2025-07-26 13:58:20 +0800"}, "done": true}, "aether:recipes/building_blocks/blue_ice_freezing": {"criteria": {"has_water_bucket": "2025-07-26 14:16:25 +0800"}, "done": true}, "appmek:recipes/misc/chemical_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_onside": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "croptopia:recipes/fruit_smoothie": {"criteria": {"has_fruit": "2025-07-26 14:15:44 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_yellow_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "supplementaries:recipes/bamboo_spikes": {"criteria": {"minecraft:wooden_slabs": "2025-07-26 15:20:18 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/hoe_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear": {"criteria": {"has_ingredient": "2025-07-26 15:18:44 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_bench": {"criteria": {"has_oak_bench": "2025-07-26 15:20:51 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/conveyor_basic": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_advanced_overlays/turtle_rainbow_overlay": {"criteria": {"has_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "silentgear:root": {"criteria": {"get_item": "2025-07-26 13:58:14 +0800"}, "done": true}, "croptopia:recipes/food/toast_from_smoking_bread": {"criteria": {"has_bread": "2025-07-26 14:16:24 +0800"}, "done": true}, "littlelogistics:recipes/tools/locomotive_route": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "mcwbridges:recipes/asian_red_bridge": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "aether:recipes/food/skyroot_milk_bucket_cake": {"criteria": {"has_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_crate": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "xnet:recipes/xnet/facade": {"criteria": {"glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "mcwwindows:recipes/shutters": {"criteria": {"has_wood": "2025-07-26 15:20:51 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/netherite_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "minecraft:recipes/misc/light_gray_dye_from_white_tulip": {"criteria": {"has_white_tulip": "2025-07-26 15:20:51 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cooked_beef": {"criteria": {"has_the_recipe": "2025-07-26 14:16:24 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailboots": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/decorations/iron_bars": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "sgjourney:recipes/misc/sandstone_with_lapis": {"criteria": {"has_lapis": "2025-07-26 15:04:29 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "supplementaries:recipes/notice_board": {"criteria": {"minecraft:planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/brewing/glass_bottle": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/post_box": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "occultism:recipes/spirit_fire/spirit_fire/spirit_attuned_gem": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "minecraft:recipes/misc/iron_ingot_from_smelting_raw_iron": {"criteria": {"has_raw_iron": "2025-07-26 15:04:21 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_cyan": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_drawer": {"criteria": {"has_spruce_drawer": "2025-07-26 15:20:16 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitix:recipes/misc/linked_crystal": {"criteria": {"criterion1": "2025-07-26 14:15:45 +0800"}, "done": true}, "handcrafted:recipes/misc/yellow_plate": {"criteria": {"has_yellow_plate": "2025-07-26 15:00:02 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/inventoryplus_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/redstone/spruce_trapdoor": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "additionallanterns:recipes/misc/bricks_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purple_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_cupboard": {"criteria": {"has_oak_cupboard": "2025-07-26 15:20:51 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_brown": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge_pier": {"criteria": {"has_planks": "2025-07-26 15:19:13 +0800"}, "done": true}, "littlelogistics:recipes/redstone/vessel_charger": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/redstone_information": {"criteria": {"redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/black_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "additionallanterns:recipes/misc/smooth_stone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/smart_yellow": {"criteria": {"has_dyes/black": "2025-07-26 15:00:02 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_standing": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "connectedglass:recipes/misc/scratched_glass_yellow2": {"criteria": {"recipe_condition2": "2025-07-26 15:00:02 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_cupboard": {"criteria": {"has_spruce_cupboard": "2025-07-26 15:20:16 +0800"}, "done": true}, "trashcans:recipes/misc/energy_trash_can": {"criteria": {"recipe_condition": "2025-07-26 15:05:56 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/orange_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "minecraft:husbandry/balanced_diet": {"criteria": {"cooked_beef": "2025-07-26 15:03:10 +0800"}, "done": false}, "minecraft:recipes/building_blocks/dye_yellow_bed": {"criteria": {"has_needed_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "croptopia:recipes/french_fries": {"criteria": {"has_potato": "2025-07-26 14:16:24 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/rose_quartz": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "aether:recipes/combat/leather_gloves_repairing": {"criteria": {"has_leather_gloves": "2025-07-26 15:01:40 +0800"}, "done": true}, "mythicbotany:recipes/misc/central_rune_holder": {"criteria": {"criterion0": "2025-07-26 14:15:45 +0800"}, "done": true}, "alchemistry:recipes/dissolver/raw_gold_block": {"criteria": {"has_the_recipe": "2025-07-26 15:06:41 +0800"}, "done": true}, "handcrafted:recipes/misc/skeleton_horse_trophy": {"criteria": {"has_skeleton_horse_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_counter": {"criteria": {"has_jungle_counter": "2025-07-26 13:58:20 +0800"}, "done": true}, "mcwlights:recipes/torches": {"criteria": {"has_wood": "2025-07-26 14:17:44 +0800"}, "done": true}, "minecraft:recipes/redstone/iron_door": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/decorations/blast_furnace": {"criteria": {"has_smooth_stone": "2025-07-26 15:05:56 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_nightstand": {"criteria": {"has_oak_nightstand": "2025-07-26 15:20:51 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_bed": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "securitycraft:recipes/misc/motion_activated_light": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/food/cooked_mutton_from_smoking": {"criteria": {"has_mutton": "2025-07-26 14:17:48 +0800"}, "done": true}, "minecraft:recipes/food/baked_potato_from_grill_cooking": {"criteria": {"has_potato": "2025-07-26 14:16:24 +0800"}, "done": true}, "alchemistry:recipes/dissolver/jukebox": {"criteria": {"has_the_recipe": "2025-07-26 14:17:46 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/oak_planks_1x": {"criteria": {"has_oak_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_coffee_table": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "travelersbackpack:recipes/misc/backpack_tank": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "croptopia:recipes/burrito": {"criteria": {"has_the_recipe": "2025-07-26 14:16:24 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/axe_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_orange": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "railcraft:recipes/misc/steel_chestplate": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_sink_light": {"criteria": {"has_bottom": "2025-07-26 15:20:16 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/machineinformation_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_spruce_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/black_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_shelf": {"criteria": {"has_spruce_shelf": "2025-07-26 15:20:16 +0800"}, "done": true}, "botania:recipes/redstone/red_string_container": {"criteria": {"has_base_block": "2025-07-26 15:22:40 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/fluidplus_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/clock_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/fridge_light": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "enderio:recipes/misc/fluid_tank": {"criteria": {"has_ingredient": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/bomb": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/amber_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "xnet:recipes/xnet/connector_red_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_dye": {"criteria": {"has_yellow_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "securitycraft:recipes/misc/username_logger": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "travelersbackpack:recipes/misc/chicken": {"criteria": {"has_feather": "2025-07-26 14:08:42 +0800"}, "done": true}, "croptopia:recipes/misc/apple_sapling": {"criteria": {"has_apple": "2025-07-26 14:15:44 +0800"}, "done": true}, "mcwbridges:recipes/oak_log_bridge_middle": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "railcraft:recipes/misc/iron_tunnel_bore_head": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/green_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "utilitix:recipes/misc/experience_crystal": {"criteria": {"criterion1": "2025-07-26 14:15:45 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_blue_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "silentgear:recipes/misc/sturdy_repair_kit": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitix:recipes/misc/dimmable_redstone_lamp": {"criteria": {"criterion1": "2025-07-26 15:05:56 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/backpack": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/maroon_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/gold_dust_from_raw": {"criteria": {"has_raw_gold": "2025-07-26 15:06:41 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/gold_block_from_blasting": {"criteria": {"has_raw_gold": "2025-07-26 15:06:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_door": {"criteria": {"has_the_recipe": "2025-07-26 15:00:05 +0800"}, "done": true}, "utilitix:recipes/misc/diamond_shears": {"criteria": {"criterion0": "2025-07-26 14:17:46 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/spruce_barrel_from_vanilla_barrel": {"criteria": {"has_vanilla_barrel": "2025-07-26 14:17:48 +0800"}, "done": true}, "repurposed_structures:city_overworld": {"criteria": {"city_overworld": "2025-07-26 14:06:40 +0800"}, "done": true}, "aquaculture:recipes/tools/wooden_fillet_knife": {"criteria": {"has_items": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/red_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "cfm:recipes/decorations/brown_sofa": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/peach_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_door": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "utilitarian:recipes/misc/angel_block_rot": {"criteria": {"has_feather": "2025-07-26 14:08:42 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailhelmet": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_shovel": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/diamond_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/purple_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_birch_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "mcwlights:recipes/tiki_torches": {"criteria": {"has_wood": "2025-07-26 14:07:59 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_cupboard": {"criteria": {"has_jungle_cupboard": "2025-07-26 13:58:20 +0800"}, "done": true}, "dyenamics:recipes/misc/wine_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/peach_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/jungle_slab": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "littlelogistics:recipes/transportation/seater_barge": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/conifer_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "farmersdelight:recipes/combat/diamond_knife": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "lootr:1chest": {"criteria": {"opened_chest": "2025-07-26 14:08:40 +0800"}, "done": true}, "minecraft:recipes/combat/iron_leggings": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "croptopia:recipes/pork_and_beans": {"criteria": {"has_pork": "2025-07-26 14:08:42 +0800"}, "done": true}, "minecraft:recipes/misc/blue_dye": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_normal_overlays/turtle_rainbow_overlay": {"criteria": {"has_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_key_changer": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/spruce_planks_1x": {"criteria": {"has_spruce_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "croptopia:recipes/stuffed_poblanos": {"criteria": {"has_beef": "2025-07-26 14:16:24 +0800"}, "done": true}, "silentgear:recipes/misc/emerald_from_shards": {"criteria": {"has_item": "2025-07-26 14:15:45 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/iron_block_from_blasting": {"criteria": {"has_raw_iron": "2025-07-26 15:04:21 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/stomp_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "undergarden:recipes/decorations/torch_ditchbulb_paste": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "forbidden_arcanus:recipes/decorations/deorum_lantern": {"criteria": {"has_torch": "2025-07-26 14:07:59 +0800"}, "done": true}, "supplementaries:recipes/flower_box": {"criteria": {"minecraft:wooden_slabs": "2025-07-26 15:20:18 +0800"}, "done": true}, "connectedglass:recipes/misc/clear_glass_yellow2": {"criteria": {"recipe_condition2": "2025-07-26 15:00:02 +0800"}, "done": true}, "croptopia:recipes/carnitas": {"criteria": {"has_porkchop": "2025-07-26 14:08:42 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_jukebox": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "minecraft:recipes/decorations/painting": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/food/cooked_salmon": {"criteria": {"has_salmon": "2025-07-26 14:16:16 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/orange_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/runic_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/diamond_blacksmith_gavel": {"criteria": {"has_gems/diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "mcwlights:recipes/lava_lamp": {"criteria": {"has_wood": "2025-07-26 15:00:05 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/edelwood_ladder": {"criteria": {"has_rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "additionallanterns:recipes/misc/gold_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "alltheores:recipes/misc/steel_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/steel]_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_yellow": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "create:recipes/misc/crafting/schematics/schematic_and_quill": {"criteria": {"has_item": "2025-07-26 14:08:43 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "additionallanterns:recipes/misc/bone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "croptopia:recipes/shaped_bacon": {"criteria": {"has_porkchop": "2025-07-26 14:08:42 +0800"}, "done": true}, "ae2additions:recipes/misc/blocks/wireless_transceiver": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "supplementaries:recipes/speaker_block": {"criteria": {"minecraft:planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "dankstorage:recipes/tools/dank_1": {"criteria": {"has_barrel": "2025-07-26 14:17:48 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/spring_green_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "handcrafted:recipes/misc/fox_trophy": {"criteria": {"has_fox_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_couch": {"criteria": {"has_spruce_couch": "2025-07-26 15:20:16 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/radiation_suit_leggings": {"criteria": {"has_resonant_plate": "2025-07-26 15:11:16 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "minecraft:recipes/decorations/glass_pane": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_acacia_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/ultramarine_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_pillar_trim": {"criteria": {"has_oak_pillar_trim": "2025-07-26 15:20:51 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/red_sand_paper": {"criteria": {"has_item": "2025-07-26 14:08:43 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "handcrafted:recipes/misc/pufferfish_trophy": {"criteria": {"has_pufferfish_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "aether:recipes/combat/leather_chestplate_repairing": {"criteria": {"has_leather_chestplate": "2025-07-26 14:07:08 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/radiation_monitor": {"criteria": {"": "2025-07-26 15:11:16 +0800"}, "done": true}, "farmersdelight:recipes/cooking/beef_stew": {"criteria": {"has_any_ingredient": "2025-07-26 14:16:24 +0800"}, "done": true}, "securitycraft:recipes/misc/claymore": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "utilitix:recipes/misc/tiny_coal_to_tiny": {"criteria": {"criterion0": "2025-07-26 14:17:44 +0800"}, "done": true}, "croptopia:recipes/documentation": {"criteria": {"has_crop": "2025-07-26 14:08:43 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/hammer": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/radiation_suit_helmet": {"criteria": {"has_resonant_plate": "2025-07-26 15:11:16 +0800"}, "done": true}, "mcwwindows:recipes/mosaic_glass": {"criteria": {"has_wood": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_counter": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_gray": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_side_table": {"criteria": {"has_oak_side_table": "2025-07-26 15:20:51 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_plate": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-26 14:17:46 +0800"}, "done": true}, "supplementaries:recipes/jar": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "railcraft:recipes/misc/steel_shovel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "supplementaries:recipes/pulley": {"criteria": {"minecraft:planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_bowl": {"criteria": {"has_wood_bowl": "2025-07-26 15:20:18 +0800"}, "done": true}, "additionallanterns:recipes/misc/smooth_stone_chain": {"criteria": {"recipe_condition": "2025-07-26 15:05:56 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/counterplus_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_exchanging": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_plate": {"criteria": {"has_wood_plate": "2025-07-26 15:20:18 +0800"}, "done": true}, "minecraft:story/root": {"criteria": {"crafting_table": "2025-07-26 13:58:14 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/yellow_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "croptopia:recipes/misc/cooking_pot": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/decorations/honeycomb_block": {"criteria": {"has_honeycomb": "2025-07-26 15:11:47 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_lime": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "farmersdelight:recipes/food/fried_egg": {"criteria": {"fried_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "croptopia:recipes/beef_jerky": {"criteria": {"has_beef": "2025-07-26 14:16:24 +0800"}, "done": true}, "minecraft:recipes/decorations/ladder": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/magenta_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "aether:recipes/building_blocks/packed_ice_freezing": {"criteria": {"has_water_bucket": "2025-07-26 14:16:25 +0800"}, "done": true}, "additionallanterns:recipes/misc/dark_prismarine_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "mythicbotany:recipes/misc/rune_holder": {"criteria": {"criterion0": "2025-07-26 15:00:05 +0800"}, "done": true}, "occultism:recipes/combat/crafting/butcher_knife": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "railcraft:recipes/misc/radio_circuit": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_axe": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/iron_blacksmith_gavel": {"criteria": {"has_ingots/iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/blue_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_red_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/basic/passivetap_fe": {"criteria": {"has_item2": "2025-07-26 15:05:56 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/bubblegum_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "create:recipes/building_blocks/horizontal_framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_side_table": {"criteria": {"has_jungle_side_table": "2025-07-26 13:58:20 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/coil_hv": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "createoreexcavation:recipes/misc/vein_atlas": {"criteria": {"map": "2025-07-26 14:08:17 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wirecoil_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/machine_frame": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "farmersdelight:recipes/cooking/baked_cod_stew": {"criteria": {"has_any_ingredient": "2025-07-26 14:06:15 +0800"}, "done": true}, "minecraft:recipes/decorations/candle": {"criteria": {"has_honeycomb": "2025-07-26 15:11:47 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_blue": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "minecraft:recipes/misc/leather_horse_armor": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "railcraft:recipes/misc/steel_sword": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_hoe": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "mcwbridges:recipes/iron_bridge_pier": {"criteria": {"has_planks": "2025-07-26 15:00:05 +0800"}, "done": true}, "undergarden:recipes/decorations/gloom_o_lantern": {"criteria": {"has_torch": "2025-07-26 14:07:59 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_sink": {"criteria": {"has_top": "2025-07-26 15:20:53 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/brown_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "supplementaries:recipes/feather_block": {"criteria": {"has_feather": "2025-07-26 14:08:42 +0800"}, "done": true}, "minecraft:recipes/redstone/spruce_button": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "travelersbackpack:recipes/misc/diamond_tier_upgrade": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "railcraft:recipes/misc/water_tank_siding": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/redstone/hopper": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "additionallanterns:recipes/misc/basalt_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "aether:recipes/combat/iron_gloves": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/text_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "aether:recipes/combat/iron_sword_repairing": {"criteria": {"has_iron_sword": "2025-07-26 15:01:40 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_helmet": {"criteria": {"has_iron_helmet": "2025-07-26 15:08:39 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "croptopia:recipes/misc/frying_pan": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/coal_dust_from_gem": {"criteria": {"has_coal_gem": "2025-07-26 14:17:44 +0800"}, "done": true}, "securitycraft:recipes/misc/mine": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/decorations/campfire": {"criteria": {"has_coal": "2025-07-26 14:17:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_red": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "constructionwand:recipes/tools/diamond_wand": {"criteria": {"has_item": "2025-07-26 14:17:46 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/no_soliciting_banner": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/coil_mv": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/coil_lv": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_blossom_trapdoor": {"criteria": {"has_plankss": "2025-07-26 15:20:51 +0800"}, "done": true}, "cfm:recipes/decorations/pink_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/conifer_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "cfm:recipes/decorations/gray_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/decorations/yellow_stained_glass_pane_from_glass_pane": {"criteria": {"has_yellow_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "aether:recipes/combat/iron_helmet_repairing": {"criteria": {"has_iron_helmet": "2025-07-26 15:08:39 +0800"}, "done": true}, "minecraft:recipes/misc/iron_ingot_from_blasting_raw_iron": {"criteria": {"has_raw_iron": "2025-07-26 15:04:21 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/vehicle_card": {"criteria": {"paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "mcwbridges:recipes/bridge_torch": {"criteria": {"has_planks": "2025-07-26 14:07:59 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_corner_trim": {"criteria": {"has_jungle_corner_trim": "2025-07-26 13:58:20 +0800"}, "done": true}, "additionallanterns:recipes/misc/iron_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/steel_dust_from_ingot": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_oak_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_purple": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "utilitix:recipes/misc/minecart_tinkerer": {"criteria": {"criterion0": "2025-07-26 15:00:05 +0800"}, "done": true}, "aether:recipes/tools/iron_pickaxe_repairing": {"criteria": {"has_iron_pickaxe": "2025-07-26 15:02:33 +0800"}, "done": true}, "securitycraft:recipes/misc/door_indestructible_iron_item": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/standby_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "farmersdelight:recipes/cooking/dog_food": {"criteria": {"has_any_ingredient": "2025-07-26 15:08:40 +0800"}, "done": true}, "railcraft:recipes/misc/diamond_tunnel_bore_head": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "undergarden:recipes/tools/catalyst": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "minecraft:recipes/combat/iron_chestplate": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_1": {"criteria": {"has_oak_plank": "2025-07-26 15:20:51 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_2": {"criteria": {"has_oak_plank": "2025-07-26 15:20:51 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_3": {"criteria": {"has_oak_plank": "2025-07-26 15:20:51 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_4": {"criteria": {"has_oak_plank": "2025-07-26 15:20:51 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/spruce_chest": {"criteria": {"has_spruce_plank": "2025-07-26 15:20:16 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_light_blue": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_block": {"criteria": {"has_the_recipe": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_gray": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "mcwroofs:recipes/jungle_planks": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/white_paper_extra": {"criteria": {"has_material": "2025-07-26 14:08:43 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/yellow_bowl": {"criteria": {"has_yellow_bowl": "2025-07-26 15:00:02 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/red_shield_template_block": {"criteria": {"glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:story/smelt_iron": {"criteria": {"iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl1": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl3": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl2": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "minecraft:recipes/building_blocks/emerald_block": {"criteria": {"has_emerald": "2025-07-26 14:15:45 +0800"}, "done": true}, "ad_astra:recipes/misc/yellow_industrial_lamp": {"criteria": {"has_yellow_industrial_lamp": "2025-07-26 15:00:02 +0800"}, "done": true}, "supplementaries:recipes/decorations/sign_post_jungle": {"criteria": {"has_item": "2025-07-26 13:58:20 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_colorless": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/baked_potato": {"criteria": {"has_the_recipe": "2025-07-26 14:16:24 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/blueprint_components": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "supplementaries:recipes/pancake_fd": {"criteria": {"forge:eggs": "2025-07-26 14:06:15 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "alchemistry:recipes/misc/combiner": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "mcwbridges:recipes/rope_oak_bridge": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "mcwbridges:recipes/oak_rail_bridge": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "aether:recipes/tools/iron_shovel_repairing": {"criteria": {"has_iron_shovel": "2025-07-26 14:17:44 +0800"}, "done": true}, "minecraft:recipes/combat/iron_sword": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/armor_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "supplementaries:recipes/daub_brace": {"criteria": {"forge:rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_chair": {"criteria": {"has_oak_chair": "2025-07-26 15:20:51 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_shovel": {"criteria": {"has_iron_shovel": "2025-07-26 14:17:44 +0800"}, "done": true}, "croptopia:recipes/egg_roll": {"criteria": {"has_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/persimmon_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "supplementaries:recipes/altimeter": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/paper_extra": {"criteria": {"has_material": "2025-07-26 14:08:43 +0800"}, "done": true}, "minecolonies:recipes/food/potato_soup": {"criteria": {"has_potato": "2025-07-26 14:16:24 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "additionallanterns:recipes/misc/deepslate_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_bedside_cabinet": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_orange": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_barrel": {"criteria": {"has _plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/food/baked_potato_from_smoking": {"criteria": {"has_potato": "2025-07-26 14:16:24 +0800"}, "done": true}, "minecraft:recipes/redstone/dropper": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "securitycraft:recipes/misc/smart_module": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/tank": {"criteria": {"has_resonant_plate": "2025-07-26 15:11:16 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "supplementaries:recipes/slice_map": {"criteria": {"has_map": "2025-07-26 14:08:17 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_3": {"criteria": {"has_jungle_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_2": {"criteria": {"has_jungle_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_1": {"criteria": {"has_jungle_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_limited_barrel_4": {"criteria": {"has_jungle_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "alchemistry:recipes/misc/liquifier": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/food/cooked_cod_from_campfire_cooking": {"criteria": {"has_cod": "2025-07-26 14:08:43 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_modifier": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_drawer": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "ad_astra:recipes/misc/compressor": {"criteria": {"has_compressor": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "farmersdelight:recipes/cooking/vegetable_soup": {"criteria": {"has_any_ingredient": "2025-07-26 14:08:43 +0800"}, "done": true}, "mcwbridges:recipes/pliers": {"criteria": {"has_planks": "2025-07-26 15:00:05 +0800"}, "done": true}, "alchemistry:recipes/dissolver/emerald_block": {"criteria": {"has_the_recipe": "2025-07-26 14:15:45 +0800"}, "done": true}, "securitycraft:recipes/misc/cage_trap": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "ae2additions:recipes/misc/super_cell_housing": {"criteria": {"has_item": "2025-07-26 14:17:46 +0800"}, "done": true}, "minecraft:recipes/redstone/piston": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "mcwlights:recipes/garden_light": {"criteria": {"has_wood": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "securitycraft:recipes/misc/sentry": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/ultramarine_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "alchemistry:recipes/dissolver/lapis_block": {"criteria": {"has_the_recipe": "2025-07-26 15:04:29 +0800"}, "done": true}, "minecolonies:recipes/misc/supplycampdeployer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "mcwbridges:recipes/jungle_log_bridge_middle": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "alltheores:recipes/misc/steel_nugget_from_ingot": {"criteria": {"has_item": "2025-07-26 15:11:25 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_warning_trapdoor": {"criteria": {"has_logs": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/crank": {"criteria": {"forge:rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_chair": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/smartwrench": {"criteria": {"iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "croptopia:recipes/food/beef_jerky": {"criteria": {"has_the_recipe": "2025-07-26 14:16:24 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_pillar_trim": {"criteria": {"has_jungle_pillar_trim": "2025-07-26 13:58:20 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_blue": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_shears": {"criteria": {"has_shears": "2025-07-26 14:16:25 +0800"}, "done": true}, "ad_astra:recipes/misc/steel_block": {"criteria": {"has_steel_block": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecraft:recipes/misc/gold_ingot_from_blasting_raw_gold": {"criteria": {"has_raw_gold": "2025-07-26 15:06:41 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "supplementaries:recipes/soap": {"criteria": {"has_porkchop": "2025-07-26 14:08:42 +0800"}, "done": true}, "cfm:recipes/decorations/blue_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "minecraft:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_golden_horse_armor": "2025-07-26 14:16:25 +0800"}, "done": true}, "farmersdelight:main/get_fd_seed": {"criteria": {"onion": "2025-07-26 14:08:43 +0800"}, "done": true}, "railcraft:recipes/misc/signal_circuit": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "oceansdelight:recipes/oceansdelight/stuffed_cod": {"criteria": {"has_cod": "2025-07-26 14:08:43 +0800"}, "done": true}, "silentgear:recipes/misc/diamond_shard": {"criteria": {"has_item": "2025-07-26 14:17:46 +0800"}, "done": true}, "croptopia:recipes/shaped_beef_stew": {"criteria": {"has_beef": "2025-07-26 14:16:24 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/redstone_acid": {"criteria": {"has_redstone_dust": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/black_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/tools/shears": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_corner_trim": {"criteria": {"has_oak_corner_trim": "2025-07-26 15:20:51 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_dark": {"criteria": {"has_bottom": "2025-07-26 15:20:51 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dye_yellow_wool": {"criteria": {"has_needed_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_note_block": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/pulse_repeater": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "minecraft:recipes/building_blocks/iron_block": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "alchemistry:recipes/dissolver/chain": {"criteria": {"has_the_recipe": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "comforts:hammock_brown": {"criteria": {"has_brown_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass_yellow_pane2": {"criteria": {"recipe_condition2": "2025-07-26 15:00:02 +0800"}, "done": true}, "minecraft:recipes/food/bread": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/hay_block": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "minecraft:recipes/redstone/target": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "railcraft:recipes/misc/steel_shears": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecraft:recipes/decorations/spruce_fence": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/glass_1x": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/jungle_planks_1x": {"criteria": {"has_jungle_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_covered_yellow": {"criteria": {"has_dyes/black": "2025-07-26 15:00:02 +0800"}, "done": true}, "minecraft:recipes/tools/compass": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "ad_astra:recipes/compressing/compressing/steel_plate_from_compressing_steel_ingots": {"criteria": {"has_item": "2025-07-26 15:11:25 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/mint_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "supplementaries:recipes/sign_post_spruce": {"criteria": {"has_the_recipe": "2025-07-26 15:20:16 +0800"}, "done": true}, "lootr:social": {"criteria": {"opened_chest": "2025-07-26 14:08:40 +0800"}, "done": false}, "dyenamics:recipes/misc/lavender_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/wheat_extra": {"criteria": {"has_material": "2025-07-26 14:08:44 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_pink": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "supplementaries:recipes/timber_frame": {"criteria": {"forge:rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/gray_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "mcwroofs:recipes/oak_planks": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_hoe": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "botania:recipes/tools/lexicon": {"criteria": {"has_item": "2025-07-26 14:08:43 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_desk": {"criteria": {"has_oak_desk": "2025-07-26 15:20:51 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/shape_card_def": {"criteria": {"iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/tools/iron_shovel": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/sonic_security_system": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear_corner": {"criteria": {"has_ingredient": "2025-07-26 15:18:44 +0800"}, "done": true}, "railcraft:recipes/misc/steel_leggings": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/fluorescent_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stick": {"criteria": {"has_the_recipe": "2025-07-26 13:58:20 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cooked_mutton": {"criteria": {"has_the_recipe": "2025-07-26 14:17:48 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_grill_cooking": {"criteria": {"has_porkchop": "2025-07-26 14:08:42 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_full_trapdoor": {"criteria": {"has_logs": "2025-07-26 15:00:05 +0800"}, "done": true}, "aether:recipes/combat/diamond_gloves": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/lavender_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_chest_from_vanilla_chest": {"criteria": {"has_vanilla_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_park_bench": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "handcrafted:recipes/misc/yellow_crockery_combo": {"criteria": {"has_yellow_crockery_combo": "2025-07-26 15:00:02 +0800"}, "done": true}, "minecraft:recipes/brewing/cauldron": {"criteria": {"has_water_bucket": "2025-07-26 14:16:25 +0800"}, "done": true}, "additionallanterns:recipes/misc/obsidian_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "supplementaries:recipes/item_shelf": {"criteria": {"minecraft:wooden_slabs": "2025-07-26 15:20:18 +0800"}, "done": true}, "minecraft:recipes/combat/crossbow": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_horse_armor": {"criteria": {"has_iron_horse_armor": "2025-07-26 14:08:43 +0800"}, "done": true}, "aether:recipes/combat/stone_sword_repairing": {"criteria": {"has_stone_sword": "2025-07-26 14:07:01 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_fletching_table": {"criteria": {"has_flint": "2025-07-26 14:15:45 +0800"}, "done": true}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "additionallanterns:recipes/misc/diamond_chain": {"criteria": {"recipe_condition": "2025-07-26 14:17:46 +0800"}, "done": true}, "securitycraft:recipes/misc/camera_monitor": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/connector_structural": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "modularrouters:recipes/misc/modular_router": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/inventory_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/misc/ice_from_freezing": {"criteria": {"has_water": "2025-07-26 14:16:25 +0800"}, "done": true}, "modularrouters:recipes/misc/blank_module": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/crank": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "farmersdelight:recipes/food/fried_egg_from_smoking": {"criteria": {"fried_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "minecraft:recipes/food/cake": {"criteria": {"has_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/cherenkov_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/brown_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/energy_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "aether:recipes/combat/leather_leggings_repairing": {"criteria": {"has_leather_leggings": "2025-07-26 14:07:11 +0800"}, "done": true}, "dimstorage:recipes/misc/dimensional_tank": {"criteria": {"has_cauldron": "2025-07-26 15:05:03 +0800"}, "done": true}, "minecraft:recipes/transportation/minecart": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/gray_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecolonies:recipes/misc/shapetool": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "farmersdelight:recipes/cooking/noodle_soup": {"criteria": {"has_any_ingredient": "2025-07-26 14:08:42 +0800"}, "done": true}, "mcwtrpdoors:recipes/spruce/spruce_blossom_trapdoor": {"criteria": {"has_plankss": "2025-07-26 15:20:16 +0800"}, "done": true}, "minecraft:recipes/combat/arrow": {"criteria": {"has_feather": "2025-07-26 14:08:42 +0800"}, "done": true}, "undergarden:recipes/decorations/undergarden_scaffolding": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "mcwroofs:recipes/spruce_planks": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/redstone_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_jungle_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "farmersdelight:recipes/food/wheat_dough_from_water": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_campfire_cooking": {"criteria": {"has_porkchop": "2025-07-26 14:08:42 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_routing": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_chair": {"criteria": {"has_jungle_chair": "2025-07-26 13:58:20 +0800"}, "done": true}, "supplementaries:recipes/sconce": {"criteria": {"has_torch": "2025-07-26 14:07:59 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_light": {"criteria": {"has_bottom": "2025-07-26 13:58:20 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/aquamarine_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecolonies:recipes/misc/blockconstructiontape": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "ad_astra:recipes/compressing/compressing/iron_plate_from_compressing_iron_ingot": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecolonies:recipes/misc/supplychestdeployer": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/radiation_suit_boots": {"criteria": {"has_resonant_plate": "2025-07-26 15:11:16 +0800"}, "done": true}, "supplementaries:recipes/bellows": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/steel_fence": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "productivetrees:recipes/misc/crates/red_delicious_apple_crate": {"criteria": {"has_apple": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_button": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/reinforced/passivetap_fe": {"criteria": {"has_item2": "2025-07-26 15:05:56 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/toolbox": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/goblet": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_button": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_redstone_lamp": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "mcwlights:recipes/iron_stuff": {"criteria": {"has_wood": "2025-07-26 15:00:05 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_nightstand": {"criteria": {"has_spruce_nightstand": "2025-07-26 15:20:16 +0800"}, "done": true}, "additionallanterns:recipes/misc/quartz_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_bench": {"criteria": {"has_spruce_bench": "2025-07-26 15:20:16 +0800"}, "done": true}, "travelersbackpack:recipes/misc/iron_tier_upgrade": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "alltheores:recipes/misc/steel_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/steel]_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "mcwbridges:recipes/spruce_log_bridge_middle": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "create:recipes/misc/crafting/kinetics/fluid_tank": {"criteria": {"has_item": "2025-07-26 14:17:48 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "lootr:10loot": {"criteria": {"loot_stat": "2025-07-26 15:02:31 +0800"}, "done": true}, "cfm:recipes/decorations/purple_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "silentgear:recipes/misc/diamond_from_shards": {"criteria": {"has_item": "2025-07-26 14:17:46 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_chest": {"criteria": {"has_lots_of_items": "2025-07-26 14:07:53 +0800"}, "done": true}, "cfm:recipes/decorations/green_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/redstone/spruce_door": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "connectedglass:recipes/misc/clear_glass_yellow_pane2": {"criteria": {"recipe_condition2": "2025-07-26 15:00:02 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_fancy_bed": {"criteria": {"has_jungle_fancy_bed": "2025-07-26 13:58:20 +0800"}, "done": true}, "railcraft:recipes/misc/controller_circuit": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/white_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "rftoolspower:recipes/rftoolspower/powercell_card": {"criteria": {"paper": "2025-07-26 15:06:47 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_light": {"criteria": {"has_bottom": "2025-07-26 15:20:51 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/gunpart_hammer": {"criteria": {"has_ingot_steel": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecolonies:recipes/misc/large_empty_bottle": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/energyplus_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/lime_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "rftoolspower:recipes/rftoolspower/power_core1": {"criteria": {"core": "2025-07-26 15:09:55 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "twigs:recipes/paper_lanterns/dandelion_paper_lantern": {"criteria": {"has_item": "2025-07-26 14:17:47 +0800"}, "done": true}, "enderio:recipes/misc/pressurized_fluid_tank": {"criteria": {"has_ingredient": "2025-07-26 15:00:05 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/spruce_barrel": {"criteria": {"has_spruce_plank": "2025-07-26 15:20:16 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "cfm:recipes/decorations/lime_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "supplementaries:recipes/lapis_bricks": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "aether:recipes/combat/crossbow_repairing": {"criteria": {"has_crossbow": "2025-07-26 14:16:25 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_axe": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "rechiseled:recipes/misc/chisel": {"criteria": {"recipe_condition": "2025-07-26 15:00:05 +0800"}, "done": true}, "additionallanterns:recipes/misc/blackstone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "xnet:recipes/xnet/connector_yellow": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_counter": {"criteria": {"has_spruce_counter": "2025-07-26 15:20:16 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/navy_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/coal_block": {"criteria": {"has_the_recipe": "2025-07-26 14:17:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_blue_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "railcraft:recipes/misc/goggles": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/blueprint_bannerpatterns": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "cfm:recipes/decorations/dye_yellow_picket_gate": {"criteria": {"has_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_blue_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "cfm:recipes/decorations/dye_yellow_picket_fence": {"criteria": {"has_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reprocessor/casing": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "ad_astra:recipes/misc/brown_flag": {"criteria": {"has_brown_flag": "2025-07-26 14:15:44 +0800"}, "done": true}, "apotheosis:affix/mythic_gem": {"criteria": {"gem": "2025-07-26 15:06:47 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "handcrafted:recipes/misc/hammer": {"criteria": {"has_hammer": "2025-07-26 15:00:05 +0800"}, "done": true}, "croptopia:recipes/potato_chips": {"criteria": {"has_potato": "2025-07-26 14:16:24 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_piston": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "delightful:recipes/knives/steel_knife": {"criteria": {"has_ingots/steel": "2025-07-26 15:11:25 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_packed_mud": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_normal_overlays/turtle_trans_overlay": {"criteria": {"has_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_pickaxe": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/light_blue_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "supplementaries:recipes/flint_block": {"criteria": {"has_flint": "2025-07-26 14:15:45 +0800"}, "done": true}, "twilightforest:recipes/decorations/wood/spruce_banister": {"criteria": {"has_item": "2025-07-26 15:20:18 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_desk": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "comforts:sleeping_bag_brown": {"criteria": {"has_brown_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecraft:recipes/food/cooked_cod_from_grill_cooking": {"criteria": {"has_cod": "2025-07-26 14:08:43 +0800"}, "done": true}, "farmersdelight:recipes/cooking/apple_cider": {"criteria": {"has_apple": "2025-07-26 14:15:44 +0800"}, "done": true}, "mcwwindows:recipes/parapets": {"criteria": {"has_wood": "2025-07-26 15:20:51 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_dining_bench": {"criteria": {"has_spruce_dining_bench": "2025-07-26 15:20:16 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/bubblegum_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/reinforced/passivetap_fe": {"criteria": {"has_item2": "2025-07-26 15:05:56 +0800"}, "done": true}, "travelersbackpack:recipes/misc/horse": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "utilitix:recipes/misc/directional_highspeed_rail": {"criteria": {"criterion0": "2025-07-26 14:15:45 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/vehicle_control_module": {"criteria": {"paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "framedblocks:recipes/building_blocks/framed_cube": {"criteria": {"hasPlanks": "2025-07-26 13:58:20 +0800"}, "done": true}, "railcraft:recipes/misc/steel_axe": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/capacitor_mv": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_axe": {"criteria": {"has_iron_axe": "2025-07-26 15:18:47 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/capacitor_hv": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "forbidden_arcanus:recipes/misc/clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron": {"criteria": {"has_item": "2025-07-26 15:04:21 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_slab": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_dining_bench": {"criteria": {"has_jungle_dining_bench": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/misc/orange_dye_from_red_yellow": {"criteria": {"has_yellow_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/coordinate_tracker_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "bloodmagic:recipes/misc/sacrificial_dagger": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/glass_yellow": {"criteria": {"has_dyes/black": "2025-07-26 15:00:02 +0800"}, "done": true}, "minecraft:recipes/decorations/jungle_sign": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_door": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "alltheores:recipes/misc/iron_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/iron]_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/component_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "supplementaries:recipes/turn_table": {"criteria": {"minecraft:planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "utilitix:recipes/misc/jungle_shulker_boat": {"criteria": {"criterion17": "2025-07-26 13:58:21 +0800"}, "done": true}, "cfm:recipes/decorations/black_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "enderio:recipes/food/enderios": {"criteria": {"has_ingredient": "2025-07-26 14:08:44 +0800"}, "done": true}, "railcraft:recipes/misc/steel_boots": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge": {"criteria": {"has_planks": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitix:recipes/misc/mob_bell": {"criteria": {"criterion8": "2025-07-26 15:08:40 +0800"}, "done": true}, "minecraft:recipes/combat/wooden_sword": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_green": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_cartography_table": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "modularrouters:recipes/misc/blank_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "cfm:recipes/decorations/lime_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/lime_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "botania:recipes/misc/redstone_root": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "silentgear:recipes/misc/blueprint_paper": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "aether:recipes/combat/leather_gloves": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobblestone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "alchemistry:recipes/dissolver/smooth_stone_slab": {"criteria": {"has_the_recipe": "2025-07-26 15:05:56 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cooked_porkchop": {"criteria": {"has_the_recipe": "2025-07-26 14:08:42 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "utilitarian:recipes/misc/angel_block": {"criteria": {"has_feather": "2025-07-26 14:08:42 +0800"}, "done": true}, "handcrafted:recipes/misc/blaze_trophy": {"criteria": {"has_blaze_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_white": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "travelersbackpack:recipes/misc/hay": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "handcrafted:recipes/misc/wolf_trophy": {"criteria": {"has_wolf_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitix:recipes/misc/directional_rail": {"criteria": {"criterion2": "2025-07-26 15:05:56 +0800"}, "done": true}, "supplementaries:recipes/boat_jar": {"criteria": {"minecraft:boats": "2025-07-26 13:58:21 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/speed_upgrade_from_glycerol": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "minecraft:story/iron_tools": {"criteria": {"iron_pickaxe": "2025-07-26 15:02:33 +0800"}, "done": true}, "alchemistry:recipes/dissolver/iron_trapdoor": {"criteria": {"has_the_recipe": "2025-07-26 15:00:05 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_golden_horse_armor": "2025-07-26 14:16:25 +0800"}, "done": true}, "xnet:recipes/xnet/connector_routing": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "alltheores:recipes/misc/steel_block": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/steel]_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecraft:recipes/misc/bucket": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/decorations/orange_glazed_terracotta": {"criteria": {"has_orange_terracotta": "2025-07-26 15:20:53 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/basic/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-26 14:16:25 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/ender_visor_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/purple_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "railcraft:recipes/misc/steel_pickaxe": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "securitycraft:recipes/redstone/block_change_detector": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "mcwpaths:recipes/oak": {"criteria": {"has_wood": "2025-07-26 15:20:51 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/upgrade_base": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "dimstorage:recipes/misc/dim_core": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_white": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "productivetrees:recipes/misc/crates/red_delicious_apple_crate_unpack": {"criteria": {"has_apple": "2025-07-26 14:15:44 +0800"}, "done": true}, "ad_astra:recipes/misc/coal_generator": {"criteria": {"has_coal_generator": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/purple_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/stick_iron": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "croptopia:recipes/cheeseburger": {"criteria": {"has_bread": "2025-07-26 14:16:24 +0800"}, "done": true}, "undergarden:recipes/decorations/shard_torch": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/black_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_piston": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "simplemagnets:recipes/misc/basicmagnet": {"criteria": {"recipe_condition": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/voltmeter": {"criteria": {"has_compass": "2025-07-26 14:17:45 +0800"}, "done": true}, "farmersdelight:recipes/food/barbecue_stick": {"criteria": {"has_onion": "2025-07-26 14:08:43 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/powered_latch": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_cherry_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/redstone/note_block": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailchestplate": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/building_blocks/smooth_stone_slab": {"criteria": {"has_smooth_stone": "2025-07-26 15:05:56 +0800"}, "done": true}, "supplementaries:recipes/blackboard": {"criteria": {"minecraft:wooden_slabs": "2025-07-26 15:20:18 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "travelersbackpack:recipes/misc/ocelot": {"criteria": {"has_cod": "2025-07-26 14:08:43 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/red_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/space_chamber_card": {"criteria": {"glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "aether:recipes/combat/iron_leggings_repairing": {"criteria": {"has_iron_leggings": "2025-07-26 14:15:44 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "supplementaries:recipes/iron_gate": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/combat/shield": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "railcraft:recipes/misc/bronze_tunnel_bore_head": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "travelersbackpack:recipes/misc/redstone": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/speed_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/honey_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "sfm:recipes/misc/manager": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_green": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_pickaxe": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "occultism:recipes/spirit_fire/spirit_fire/otherworld_sapling_natural": {"criteria": {"has_oak_sapling": "2025-07-26 14:08:43 +0800"}, "done": true}, "croptopia:recipes/misc/meringue": {"criteria": {"has_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "sfm:recipes/misc/water_tank": {"criteria": {"has_water": "2025-07-26 14:16:25 +0800"}, "done": true}, "railcraft:recipes/coke_oven/coal_coke": {"criteria": {"has_coal": "2025-07-26 14:17:44 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_building": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_cyan": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_mangrove_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_advanced_overlays/turtle_trans_overlay": {"criteria": {"has_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "farmersdelight:recipes/cooking/pasta_with_mutton_chop": {"criteria": {"has_any_ingredient": "2025-07-26 14:17:48 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "travelersbackpack:recipes/misc/lapis": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/reinforced/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-26 14:16:25 +0800"}, "done": true}, "farmersdelight:recipes/building_blocks/organic_compost_from_rotten_flesh": {"criteria": {"has_rotten_flesh": "2025-07-26 15:08:40 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_fancy_bed": {"criteria": {"has_spruce_fancy_bed": "2025-07-26 15:20:16 +0800"}, "done": true}, "minecraft:recipes/tools/iron_axe": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wirecutter": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/yellow_shield_template_block": {"criteria": {"glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/food/cooked_cod": {"criteria": {"has_cod": "2025-07-26 14:08:43 +0800"}, "done": true}, "supplementaries:recipes/daub": {"criteria": {"supplementaries:straw": "2025-07-26 14:08:44 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/green_shield_template_block": {"criteria": {"glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/brown_cushion": {"criteria": {"has_brown_cushion": "2025-07-26 14:15:44 +0800"}, "done": true}, "littlelogistics:recipes/transportation/fluid_barge": {"criteria": {"has_item": "2025-07-26 15:19:13 +0800"}, "done": true}, "sfm:recipes/misc/fancy_cable": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/brown_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_yellow": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/industrial_iron_block_from_ingots_iron_stonecutting": {"criteria": {"has_ingots_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/decorations/brown_banner": {"criteria": {"has_brown_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "alchemistry:recipes/misc/atomizer": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_crimson_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/decorations/fletching_table": {"criteria": {"has_flint": "2025-07-26 14:15:45 +0800"}, "done": true}, "railcraft:recipes/misc/villager_detector": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/orange_glazed_terracotta": {"criteria": {"has_the_recipe": "2025-07-26 15:20:53 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "mcwbridges:recipes/spruce_rail_bridge": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "ae2:recipes/misc/decorative/light_detector": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/food/cooked_cod_from_smoking": {"criteria": {"has_cod": "2025-07-26 14:08:43 +0800"}, "done": true}, "mcwbridges:recipes/rope_spruce_bridge": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/torch": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_fancy_bed": {"criteria": {"has_oak_fancy_bed": "2025-07-26 15:20:51 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/pulse_extender": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/diving_board": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/connector_hv": {"criteria": {"has_aluminum_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_light_blue": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/magenta_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gray_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/steel_scaffolding_standard": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "securitycraft:recipes/misc/storage_module": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/misc/map": {"criteria": {"has_compass": "2025-07-26 14:17:45 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_table": {"criteria": {"has_oak_table": "2025-07-26 15:20:51 +0800"}, "done": true}, "minecraft:recipes/food/cooked_mutton_from_grill_cooking": {"criteria": {"has_mutton": "2025-07-26 14:17:48 +0800"}, "done": true}, "additionallanterns:recipes/misc/emerald_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/reinforced/passivefluidport_forge": {"criteria": {"has_item2": "2025-07-26 14:16:25 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "xnet:recipes/xnet/connector_green": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "aether:recipes/combat/leather_boots_repairing": {"criteria": {"has_leather_boots": "2025-07-26 14:07:12 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge_stair": {"criteria": {"has_planks": "2025-07-26 15:19:13 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/inventory_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_purple": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_acacia_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "littlelogistics:recipes/transportation/barrel_barge": {"criteria": {"has_item": "2025-07-26 14:17:48 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard_no_tanks": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "handcrafted:recipes/misc/silverfish_trophy": {"criteria": {"has_silverfish_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "create:recipes/building_blocks/spruce_window": {"criteria": {"has_ingredient": "2025-07-26 15:20:16 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/fluid_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cooked_cod": {"criteria": {"has_the_recipe": "2025-07-26 14:08:43 +0800"}, "done": true}, "securitycraft:recipes/misc/security_camera": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "additionallanterns:recipes/misc/andesite_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "minecraft:recipes/misc/gold_ingot_from_smelting_raw_gold": {"criteria": {"has_raw_gold": "2025-07-26 15:06:41 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_chest": {"criteria": {"has _plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/food/cooked_beef_from_smoking": {"criteria": {"has_beef": "2025-07-26 14:16:24 +0800"}, "done": true}, "mcwpaths:recipes/spruce": {"criteria": {"has_wood": "2025-07-26 15:20:16 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_chest_raft": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "minecraft:recipes/redstone/spruce_pressure_plate": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "aether:recipes/tools/iron_axe_repairing": {"criteria": {"has_iron_axe": "2025-07-26 15:18:47 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_pillar_trim": {"criteria": {"has_spruce_pillar_trim": "2025-07-26 15:20:16 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_sink_dark": {"criteria": {"has_bottom": "2025-07-26 15:20:16 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/sawblade": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecraft:recipes/decorations/lantern": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_nightstand": {"criteria": {"has_jungle_nightstand": "2025-07-26 13:58:20 +0800"}, "done": true}, "farmersdelight:recipes/cooking/dumplings": {"criteria": {"has_any_ingredient": "2025-07-26 14:08:43 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_cut_paste": {"criteria": {"has_shear": "2025-07-26 14:16:25 +0800"}, "done": true}, "quarryplus:recipes/workbench": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_dining_bench": {"criteria": {"has_oak_dining_bench": "2025-07-26 15:20:51 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/blueprint_bullets": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "minecraft:recipes/decorations/item_frame": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "bigreactors:recipes/misc/blasting/graphite_from_coal": {"criteria": {"has_item": "2025-07-26 14:17:44 +0800"}, "done": true}, "nethersdelight:recipes/tools/iron_machete": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "mcwdoors:recipes/metal": {"criteria": {"has_wood": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_helmet": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/cherenkov_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "connectedglass:recipes/misc/tinted_borderless_glass_yellow2": {"criteria": {"recipe_condition2": "2025-07-26 15:00:02 +0800"}, "done": true}, "apotheosis:affix/gem": {"criteria": {"gem": "2025-07-26 14:17:44 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/resonating_plate": {"criteria": {"has_ore": "2025-07-26 15:11:12 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/deorum_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/pink_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/icy_blue_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "mcwbridges:recipes/bridge_lantern": {"criteria": {"has_planks": "2025-07-26 14:17:44 +0800"}, "done": true}, "cfm:recipes/decorations/brown_trampoline": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/paper_from_tag_filter": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "additionallanterns:recipes/misc/diorite_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "securitycraft:recipes/tools/taser": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "handcrafted:recipes/misc/brown_sheet": {"criteria": {"has_brown_sheet": "2025-07-26 14:15:44 +0800"}, "done": true}, "railcraft:recipes/misc/invar_ingot_crafted_with_ingots": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/green_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "supplementaries:recipes/clock_block": {"criteria": {"minecraft:planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "utilitix:recipes/misc/hand_bell": {"criteria": {"criterion1": "2025-07-26 14:06:57 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/brown_floating_carpet": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "croptopia:recipes/hamburger": {"criteria": {"has_beef": "2025-07-26 14:16:24 +0800"}, "done": true}, "minecraft:recipes/misc/orange_dye_from_orange_tulip": {"criteria": {"has_orange_tulip": "2025-07-26 15:05:08 +0800"}, "done": true}, "twigs:recipes/paper_lanterns/paper_lantern": {"criteria": {"has_item": "2025-07-26 14:08:43 +0800"}, "done": true}, "utilitix:recipes/misc/comparator_redirector_up": {"criteria": {"criterion2": "2025-07-26 15:00:05 +0800"}, "done": true}, "croptopia:recipes/flour": {"criteria": {"has_grain": "2025-07-26 14:08:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/connector_hv_relay": {"criteria": {"has_aluminum_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/dispenser_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "minecraft:story/mine_diamond": {"criteria": {"diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "securitycraft:recipes/misc/redstone_module": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "travelersbackpack:recipes/misc/pig": {"criteria": {"has_porkchop": "2025-07-26 14:08:42 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/navy_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "croptopia:recipes/food/toast_from_bread": {"criteria": {"has_bread": "2025-07-26 14:16:24 +0800"}, "done": true}, "cfm:recipes/decorations/door_mat": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "silentgear:recipes/misc/upgrade_base": {"criteria": {"has_item": "2025-07-26 13:58:20 +0800"}, "done": true}, "cfm:recipes/decorations/gray_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/fluorescent_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobbled_deepslate_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "additionallanterns:recipes/misc/crimson_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "littlelogistics:recipes/tools/tug_route": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_yellow_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "securitycraft:recipes/decorations/glow_display_case": {"criteria": {"has_item_frame": "2025-07-26 15:20:54 +0800"}, "done": true}, "minecraft:recipes/misc/book": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/brown_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "railcraft:recipes/misc/steel_gear": {"criteria": {"has_material": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecraft:recipes/tools/iron_hoe": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/tools/spatula": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/pink_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "dyenamics:recipes/misc/lavender_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "croptopia:recipes/misc/apple_pie": {"criteria": {"has_apple": "2025-07-26 14:15:44 +0800"}, "done": true}, "twigs:recipes/tables/spruce_table_slab": {"criteria": {"has_bamboo": "2025-07-26 15:20:18 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_stairs": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "minecraft:recipes/decorations/oak_fence": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "farmersdelight:recipes/cooking/pumpkin_soup": {"criteria": {"has_any_ingredient": "2025-07-26 14:08:42 +0800"}, "done": true}, "travelersbackpack:recipes/misc/emerald": {"criteria": {"has_emerald": "2025-07-26 14:15:45 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_2": {"criteria": {"has _plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_3": {"criteria": {"has _plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_1": {"criteria": {"has _plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_4": {"criteria": {"has _plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/screen_link": {"criteria": {"redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "twigs:recipes/lamps/lamp": {"criteria": {"has_item": "2025-07-26 14:07:59 +0800"}, "done": true}, "apotheosis:affix/root": {"criteria": {"sword": "2025-07-26 14:16:17 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/basic/passivetap_fe": {"criteria": {"has_item2": "2025-07-26 15:05:56 +0800"}, "done": true}, "alchemistry:recipes/misc/compactor": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "eidolon:recipes/building_blocks/smooth_stone_masonry_stonecutter_0": {"criteria": {"has_journal": "2025-07-26 15:05:56 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "minecraft:recipes/building_blocks/smooth_stone_slab_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-26 15:05:56 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/storage_module0": {"criteria": {"redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "handcrafted:recipes/misc/tropical_fish_trophy": {"criteria": {"has_tropical_fish_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecolonies:recipes/food/apple_pie": {"criteria": {"has_apple": "2025-07-26 14:15:44 +0800"}, "done": true}, "farmersdelight:recipes/cooking/fried_rice": {"criteria": {"has_any_ingredient": "2025-07-26 14:06:15 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/sand_paper": {"criteria": {"has_item": "2025-07-26 14:08:43 +0800"}, "done": true}, "simplemagnets:recipes/misc/basic_demagnetization_coil": {"criteria": {"recipe_condition": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_fence_gate": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_blue": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/white_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "alltheores:recipes/misc/steel_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/steel]_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_barrel": {"criteria": {"has_jungle_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_warped_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "enderio:recipes/misc/black_paper": {"criteria": {"has_ingredient": "2025-07-26 14:08:43 +0800"}, "done": true}, "travelersbackpack:recipes/misc/hose_nozzle": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/black_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_bench": {"criteria": {"has_jungle_bench": "2025-07-26 13:58:20 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/sword_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_bricks_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-26 15:05:56 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_nether_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "minecraft:recipes/food/cooked_mutton": {"criteria": {"has_mutton": "2025-07-26 14:17:48 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/charging_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/powerpack": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "minecraft:recipes/food/cooked_beef_from_grill_cooking": {"criteria": {"has_beef": "2025-07-26 14:16:24 +0800"}, "done": true}, "securitycraft:recipes/misc/harming_module": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "alchemistry:recipes/dissolver/hay_block": {"criteria": {"has_the_recipe": "2025-07-26 14:08:44 +0800"}, "done": true}, "handcrafted:recipes/misc/bench": {"criteria": {"has_bench": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/steel_wallmount": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/radiation_suit_chestplate": {"criteria": {"has_resonant_plate": "2025-07-26 15:11:16 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_desk_cabinet": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "aether:recipes/combat/iron_ring": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_mangrove_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "charginggadgets:recipes/redstone/charging_station": {"criteria": {"has_diamonds": "2025-07-26 14:17:46 +0800"}, "done": true}, "minecraft:recipes/building_blocks/yellow_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_smart_yellow": {"criteria": {"has_dyes/black": "2025-07-26 15:00:02 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_cyan": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/restraining_order": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "minecraft:recipes/decorations/cartography_table": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_chair": {"criteria": {"has_spruce_chair": "2025-07-26 15:20:16 +0800"}, "done": true}, "mcwroofs:recipes/thatch": {"criteria": {"has_planks": "2025-07-26 14:08:44 +0800"}, "done": true}, "xnet:recipes/xnet/connector_red": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "forbidden_arcanus:recipes/decorations/utrem_jar": {"criteria": {"has_glass/colorless": "2025-07-26 15:19:13 +0800"}, "done": true}, "farmersdelight:recipes/decorations/cutting_board": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "mcwtrpdoors:recipes/jungle/jungle_blossom_trapdoor": {"criteria": {"has_plankss": "2025-07-26 13:58:20 +0800"}, "done": true}, "create:recipes/misc/crafting/kinetics/item_vault": {"criteria": {"has_item": "2025-07-26 14:17:48 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/gadget_copy_paste": {"criteria": {"has_emerald": "2025-07-26 14:15:45 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_spruce_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "mcwlights:recipes/lantern": {"criteria": {"has_wood": "2025-07-26 14:07:59 +0800"}, "done": true}, "cfm:recipes/decorations/red_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/food/cooked_beef_from_campfire_cooking": {"criteria": {"has_beef": "2025-07-26 14:16:24 +0800"}, "done": true}, "minecraft:recipes/combat/leather_helmet": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "cfm:recipes/decorations/orange_cooler": {"criteria": {"has_terracotta": "2025-07-26 15:20:53 +0800"}, "done": true}, "farmersdelight:recipes/food/grilled_salmon": {"criteria": {"has_salmon": "2025-07-26 14:16:16 +0800"}, "done": true}, "croptopia:recipes/misc/apple_juice": {"criteria": {"has_apple": "2025-07-26 14:15:44 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_birch_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "croptopia:recipes/pork_jerky": {"criteria": {"has_pork": "2025-07-26 14:08:42 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/amber_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "paraglider:root": {"criteria": {"crafting_table": "2025-07-26 13:58:14 +0800"}, "done": true}, "littlelogistics:recipes/transportation/barge": {"criteria": {"has_item": "2025-07-26 15:22:40 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_barrel": {"criteria": {"has_slabs": "2025-07-26 15:20:18 +0800"}, "done": true}, "utilitix:recipes/misc/advanced_brewery": {"criteria": {"criterion2": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/blue_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/iron_block_from_smelting": {"criteria": {"has_raw_iron": "2025-07-26 15:04:21 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/drillhead_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecraft:recipes/building_blocks/orange_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "mcwroofs:recipes/gutters": {"criteria": {"has_planks": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "structurecompass:recipes/tools/structure_compass": {"criteria": {"has_compass": "2025-07-26 14:17:45 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_bricks": {"criteria": {"has_smooth_stone": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/fridge_dark": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "alchemistry:recipes/misc/dissolver": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_green_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_red_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "dyenamics:recipes/misc/wine_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_blue": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_drawer": {"criteria": {"has_oak_drawer": "2025-07-26 15:20:51 +0800"}, "done": true}, "securitycraft:recipes/transportation/track_mine": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/andesite_alloy": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/magnet_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "utilitix:recipes/misc/weak_redstone_torch": {"criteria": {"criterion0": "2025-07-26 15:05:56 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/gunpart_drum": {"criteria": {"has_ingot_steel": "2025-07-26 15:11:25 +0800"}, "done": true}, "cfm:recipes/decorations/lime_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecolonies:recipes/misc/chainmailleggings": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_lime": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "railcraft:recipes/misc/iron_gear": {"criteria": {"has_material": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "supplementaries:recipes/daub_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/cyan_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_shelf": {"criteria": {"has_oak_shelf": "2025-07-26 15:20:51 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_brick_stairs_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-26 15:05:56 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/smart_fluix": {"criteria": {"has_dusts/redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "chimes:recipes/decorations/iron_chimes": {"criteria": {"has_wood_slabs": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/portable_tune_player": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/white_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "travelersbackpack:recipes/misc/iron": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/building_blocks/red_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_bamboo_fence_gate": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_fence_gate": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop": {"criteria": {"has_porkchop": "2025-07-26 14:08:42 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_orange": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv5": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv3": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv4": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv1": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/keycard_lv2": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_white": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_green": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "alchemistry:recipes/dissolver/raw_iron_block": {"criteria": {"has_the_recipe": "2025-07-26 15:04:21 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/white_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/volume_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "mcwpaths:recipes/jungle": {"criteria": {"has_wood": "2025-07-26 13:58:20 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/crafting_card": {"criteria": {"crafter": "2025-07-26 13:58:14 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/item_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_drawer": {"criteria": {"has_jungle_drawer": "2025-07-26 13:58:20 +0800"}, "done": true}, "create:recipes/building_blocks/framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/timber_brace": {"criteria": {"forge:rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/lavender_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dye_yellow_carpet": {"criteria": {"has_needed_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "occultism:recipes/misc/crafting/brush": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "mcwbridges:recipes/iron_bridge": {"criteria": {"has_planks": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/aquamarine_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "paraglider:recipes/misc/paraglider": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/redstone/iron_trapdoor": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/decorations/jungle_fence": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_black": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/item_life_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/building_blocks/diamond_block": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "delightful:recipes/food/cooking/jam_jar": {"criteria": {"has_sweet_fruit": "2025-07-26 14:15:44 +0800"}, "done": true}, "alltheores:recipes/misc/diamond_gear": {"criteria": {"has_TagKey[minecraft:item / forge:gems/diamond]_ingot": "2025-07-26 14:17:46 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_shelf": {"criteria": {"has_jungle_shelf": "2025-07-26 13:58:20 +0800"}, "done": true}, "farmersdelight:recipes/food/pie_crust": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_magenta": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_corner_trim": {"criteria": {"has_spruce_corner_trim": "2025-07-26 15:20:16 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/maroon_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "travelersbackpack:recipes/misc/cake": {"criteria": {"has_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_crimson_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "create:recipes/building_blocks/jungle_window": {"criteria": {"has_ingredient": "2025-07-26 13:58:20 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_counter": {"criteria": {"has_oak_counter": "2025-07-26 15:20:51 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_desk": {"criteria": {"has_spruce_desk": "2025-07-26 15:20:16 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/wine_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/wood_cup": {"criteria": {"has_wood_cup": "2025-07-26 15:20:18 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_iron_horse_armor": "2025-07-26 14:08:43 +0800"}, "done": true}, "utilitarian:recipes/misc/fluid_hopper": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "undergarden:recipes/combat/slingshot": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "securitycraft:recipes/misc/blacklist_module": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/button_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_yellow": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "railcraft:recipes/misc/steel_tunnel_bore_head": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_trapdoor": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "securitycraft:recipes/redstone/alarm": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/pickaxe_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "farmersdelight:recipes/food/wheat_dough_from_eggs": {"criteria": {"has_wheat": "2025-07-26 14:08:44 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "additionallanterns:recipes/misc/mossy_cobblestone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "securitycraft:recipes/misc/bouncing_betty": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blue_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "supplementaries:recipes/faucet": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/honey_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "cfm:recipes/decorations/pink_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/stick_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/red_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/drillhead_iron": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/light_gray_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecolonies:recipes/food/mutton_dinner": {"criteria": {"has_mutton": "2025-07-26 14:17:48 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/rockcutter": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "xnet:recipes/xnet/connector_blue": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "minecraft:recipes/decorations/composter": {"criteria": {"has_wood_slab": "2025-07-26 15:20:18 +0800"}, "done": true}, "cfm:recipes/decorations/purple_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass_yellow2": {"criteria": {"recipe_condition2": "2025-07-26 15:00:02 +0800"}, "done": true}, "toolbelt:recipes/pouch": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "supplementaries:recipes/bed_from_feather_block": {"criteria": {"minecraft:planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "create:recipes/misc/crafting/schematics/empty_schematic": {"criteria": {"has_item": "2025-07-26 14:08:43 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_cabinet": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lime_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "utilitix:recipes/misc/highspeed_rail": {"criteria": {"criterion0": "2025-07-26 14:15:45 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_warped_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/misc/yellow_dye_from_dandelion": {"criteria": {"has_dandelion": "2025-07-26 14:17:47 +0800"}, "done": true}, "aether:recipes/building_blocks/ice_from_bucket_freezing": {"criteria": {"has_water_bucket": "2025-07-26 14:16:25 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/gilded_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "mcwtrpdoors:recipes/metal/metal_trapdoor": {"criteria": {"has_logs": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/decorations/sign_post_spruce": {"criteria": {"has_item": "2025-07-26 15:20:16 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_blue": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "create:recipes/misc/crafting/kinetics/empty_blaze_burner": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/cyan_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "buildinggadgets2:recipes/misc/template_manager": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_couch": {"criteria": {"has_oak_couch": "2025-07-26 15:20:51 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_brown": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/spider_trophy": {"criteria": {"has_spider_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_boots": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/fluid_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_dye": {"criteria": {"has_yellow_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "minecraft:recipes/tools/iron_pickaxe": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/diamond_dust_from_gem": {"criteria": {"has_diamond_gem": "2025-07-26 14:17:46 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_black": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/pink_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "mcwbridges:recipes/jungle_rail_bridge": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/food/cooked_salmon_from_smoking": {"criteria": {"has_salmon": "2025-07-26 14:16:16 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/plate_steel_hammering": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_smithing_table": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/rose_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "supplementaries:recipes/sconce_lever": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "mcwlights:recipes/paper_lamp": {"criteria": {"has_wood": "2025-07-26 14:07:59 +0800"}, "done": true}, "aether:recipes/combat/skyroot_iron_vanilla_shield": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/keypad_frame": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/security_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "minecraft:recipes/tools/flint_and_steel": {"criteria": {"has_flint": "2025-07-26 14:15:45 +0800"}, "done": true}, "utilitix:recipes/misc/linked_repeater": {"criteria": {"criterion0": "2025-07-26 15:05:56 +0800"}, "done": true}, "utilitix:recipes/misc/comparator_redirector_down": {"criteria": {"criterion2": "2025-07-26 15:00:05 +0800"}, "done": true}, "railcraft:recipes/misc/steel_helmet": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/wine_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_brick_slab_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-26 15:05:56 +0800"}, "done": true}, "additionallanterns:recipes/misc/copper_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/upgrade_base": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_pressure_plate": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "minecraft:recipes/food/cooked_mutton_from_campfire_cooking": {"criteria": {"has_mutton": "2025-07-26 14:17:48 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "ad_astra:recipes/misc/small_yellow_industrial_lamp": {"criteria": {"has_small_yellow_industrial_lamp": "2025-07-26 15:00:02 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "cfm:recipes/decorations/white_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "farmersdelight:recipes/cooking/ratatouille": {"criteria": {"has_any_ingredient": "2025-07-26 14:08:43 +0800"}, "done": true}, "minecraft:recipes/redstone/jungle_pressure_plate": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/icy_blue_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "connectedglass:recipes/misc/scratched_glass_yellow_pane2": {"criteria": {"recipe_condition2": "2025-07-26 15:00:02 +0800"}, "done": true}, "additionallanterns:recipes/misc/emerald_chain": {"criteria": {"recipe_condition": "2025-07-26 14:15:45 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_red": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_sword": {"criteria": {"has_iron_sword": "2025-07-26 15:01:40 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/rose_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/spring_green_bed": {"criteria": {"has_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "aether:recipes/combat/iron_boots_repairing": {"criteria": {"has_iron_boots": "2025-07-26 14:16:17 +0800"}, "done": true}, "handcrafted:recipes/misc/wither_skeleton_trophy": {"criteria": {"has_wither_skeleton_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "travelersbackpack:recipes/misc/bee": {"criteria": {"has_honeycomb": "2025-07-26 15:11:47 +0800"}, "done": true}, "occultism:recipes/spirit_fire/spirit_fire/awakened_feather": {"criteria": {"has_feather": "2025-07-26 14:08:42 +0800"}, "done": true}, "supplementaries:recipes/soap/map": {"criteria": {"has_filled_map": "2025-07-26 15:02:11 +0800"}, "done": true}, "aether:recipes/combat/iron_pendant": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "botania:recipes/tools/speed_up_belt": {"criteria": {"has_item": "2025-07-26 14:08:17 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/decorations/brown_bed": {"criteria": {"has_brown_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cooked_salmon": {"criteria": {"has_the_recipe": "2025-07-26 14:16:16 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/blue_shield_template_block": {"criteria": {"glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecraft:recipes/decorations/oak_sign": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/search_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_sink_dark": {"criteria": {"has_bottom": "2025-07-26 13:58:20 +0800"}, "done": true}, "supplementaries:recipes/spring_launcher": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lapis_block": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass1": {"criteria": {"recipe_condition": "2025-07-26 15:19:13 +0800"}, "done": true}, "constructionwand:recipes/tools/iron_wand": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "cfm:recipes/decorations/brown_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_chestplate": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_torch": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "potionblender:recipes/brewing/brewing_cauldron": {"criteria": {"has_water_bucket": "2025-07-26 14:16:25 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_lime": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "supplementaries:recipes/lock_block": {"criteria": {"minecraft:planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_boots": {"criteria": {"has_iron_boots": "2025-07-26 14:16:17 +0800"}, "done": true}, "mcwwindows:recipes/blinds": {"criteria": {"has_wood": "2025-07-26 15:20:51 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/yellow_brick_extra": {"criteria": {"has_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "cfm:recipes/decorations/birch_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "sgjourney:recipes/misc/fire_pit": {"criteria": {"has_coal": "2025-07-26 14:17:44 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "create:recipes/misc/crafting/appliances/crafting_blueprint": {"criteria": {"has_item": "2025-07-26 13:58:14 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_gray": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "sfm:recipes/misc/cable": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "nethersdelight:recipes/decorations/blackstone_blast_furnace": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/building_blocks/pink_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "additionallanterns:recipes/misc/purpur_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "supplementaries:recipes/cage": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "xnet:recipes/xnet/connector_yellow_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/granite_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "farmersdelight:recipes/decorations/cooking_pot": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/dark_runic_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/jungle_chest": {"criteria": {"has_jungle_plank": "2025-07-26 13:58:20 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/storage_control_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/blue_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "croptopia:recipes/chilli_relleno": {"criteria": {"has_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "securitycraft:recipes/misc/whitelist_module": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "handcrafted:recipes/misc/salmon_trophy": {"criteria": {"has_salmon_trophy": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/tools/diamond_shovel": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/green_dye": {"criteria": {"has_yellow_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "croptopia:recipes/food/pork_jerky": {"criteria": {"has_the_recipe": "2025-07-26 14:08:42 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "forbidden_arcanus:recipes/misc/clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold": {"criteria": {"has_item": "2025-07-26 15:06:41 +0800"}, "done": true}, "additionallanterns:recipes/misc/red_nether_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "railcraft:recipes/misc/receiver_circuit": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/briefcase": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "cfm:recipes/decorations/orange_trampoline": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/misc/electrified_iron_fence": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_purple": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/building_blocks/raw_gold_block": {"criteria": {"has_raw_gold": "2025-07-26 15:06:41 +0800"}, "done": true}, "handcrafted:recipes/misc/yellow_cup": {"criteria": {"has_yellow_cup": "2025-07-26 15:00:02 +0800"}, "done": true}, "apotheosis:affix/rare": {"criteria": {"sword": "2025-07-26 14:16:17 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "securitycraft:recipes/redstone/portable_radar": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "supplementaries:recipes/redstone_illuminator": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_chest": {"criteria": {"has_oak_plank": "2025-07-26 15:20:51 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_fence_gate": {"criteria": {"has_planks": "2025-07-26 15:20:51 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/iron_dust_from_raw": {"criteria": {"has_raw_iron": "2025-07-26 15:04:21 +0800"}, "done": true}, "sliceanddice:recipes/misc/slicer": {"criteria": {"has_tool": "2025-07-26 15:18:47 +0800"}, "done": true}, "croptopia:recipes/scrambled_eggs": {"criteria": {"has_egg": "2025-07-26 14:06:15 +0800"}, "done": true}, "sfm:recipes/misc/fancy_to_cable": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/slingshot": {"criteria": {"forge:rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cyan_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/screwdriver": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "supplementaries:recipes/candy": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "travelersbackpack:recipes/decorations/brown_sleeping_bag": {"criteria": {"has_brown_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "railcraft:recipes/misc/signal_tuner": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_beehive": {"criteria": {"has_honeycomb": "2025-07-26 15:11:47 +0800"}, "done": true}, "securitycraft:recipes/misc/trophy_system": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/component_iron": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_block": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "travelersbackpack:recipes/misc/diamond": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/wire_steel": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "create:recipes/building_blocks/crafting/kinetics/green_seat": {"criteria": {"has_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "supplementaries:recipes/present": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "silentgear:recipes/misc/iron_rod": {"criteria": {"has_item": "2025-07-26 15:00:05 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/emerald_dust_from_gem": {"criteria": {"has_emerald_gem": "2025-07-26 14:15:45 +0800"}, "done": true}, "bigreactors:recipes/misc/smelting/graphite_from_coal": {"criteria": {"has_item": "2025-07-26 14:17:44 +0800"}, "done": true}, "railcraft:recipes/misc/steel_hoe": {"criteria": {"has_steel_ingot": "2025-07-26 15:11:25 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dropper": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_green_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_green": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/warped_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "cfm:recipes/decorations/warped_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/amethyst_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecraft:recipes/food/cooked_beef": {"criteria": {"has_beef": "2025-07-26 14:16:24 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_remover": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/redstone/heavy_weighted_pressure_plate": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/blueprint_molds": {"criteria": {"has_paper": "2025-07-26 14:08:43 +0800"}, "done": true}, "xnet:recipes/xnet/connector_green_dye": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/plate_iron_hammering": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/combat/leather_leggings": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "silentgear:recipes/misc/rough_rod": {"criteria": {"has_item": "2025-07-26 15:18:44 +0800"}, "done": true}, "additionallanterns:recipes/misc/red_sandstone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "minecraft:recipes/combat/diamond_leggings": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "mininggadgets:recipes/misc/upgrade_empty": {"criteria": {"has_diamonds": "2025-07-26 14:17:46 +0800"}, "done": true}, "farmersdelight:recipes/cooking/fish_stew": {"criteria": {"has_any_ingredient": "2025-07-26 14:08:43 +0800"}, "done": true}, "minecraft:recipes/decorations/brown_carpet": {"criteria": {"has_brown_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_brown": {"criteria": {"has_brown_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "blue_skies:recipes/food/cake_compat": {"criteria": {"has_item": "2025-07-26 14:06:15 +0800"}, "done": true}, "farmersdelight:recipes/combat/flint_knife": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fillet_knife": {"criteria": {"has_items": "2025-07-26 14:17:46 +0800"}, "done": true}, "securitycraft:recipes/misc/disguise_module": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "createoreexcavation:recipes/misc/drill": {"criteria": {"iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/tools/clock": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_ingot": {"criteria": {"has_iron_ingots": "2025-07-26 15:00:05 +0800"}, "done": true}, "minecraft:recipes/food/baked_potato": {"criteria": {"has_potato": "2025-07-26 14:16:24 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_barrel": {"criteria": {"has_oak_plank": "2025-07-26 15:20:51 +0800"}, "done": true}, "cfm:recipes/decorations/oak_mail_box": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_sandstone_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "aether:recipes/combat/golden_dart": {"criteria": {"has_feather": "2025-07-26 14:08:42 +0800"}, "done": true}, "alchemistry:recipes/dissolver/diamond_block": {"criteria": {"has_the_recipe": "2025-07-26 14:17:46 +0800"}, "done": true}, "railcraft:recipes/misc/signal_block_surveyor": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "additionallanterns:recipes/misc/prismarine_lantern": {"criteria": {"recipe_condition": "2025-07-26 14:07:59 +0800"}, "done": true}, "minecraft:recipes/decorations/beehive": {"criteria": {"has_honeycomb": "2025-07-26 15:11:47 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_pink": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fillet_knife": {"criteria": {"has_items": "2025-07-26 15:00:05 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/block_tracker_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "minecraft:recipes/decorations/chain": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "railcraft:recipes/blast_furnace/blasting_iron_leggings": {"criteria": {"has_iron_leggings": "2025-07-26 14:15:44 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/radiator": {"criteria": {"has_water_bucket": "2025-07-26 14:16:25 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_lantern_red": {"criteria": {"recipe_condition": "2025-07-26 15:19:22 +0800"}, "done": true}, "minecraft:recipes/decorations/smithing_table": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/dump_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_desk": {"criteria": {"has_jungle_desk": "2025-07-26 13:58:20 +0800"}, "done": true}, "silentgear:recipes/misc/emerald_shard": {"criteria": {"has_item": "2025-07-26 14:15:45 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2025-07-26 14:01:17 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/counter_module": {"criteria": {"ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "apotheosis:affix/chests": {"criteria": {"sword": "2025-07-26 14:16:17 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_wool": {"criteria": {"has_white_wool": "2025-07-26 14:15:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/magenta_stained_glass": {"criteria": {"has_glass": "2025-07-26 15:19:13 +0800"}, "done": true}, "minecraft:recipes/building_blocks/coal_block": {"criteria": {"has_coal": "2025-07-26 14:17:44 +0800"}, "done": true}, "minecraft:recipes/building_blocks/spruce_slab": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_chest_boat": {"criteria": {"has_boat": "2025-07-26 13:58:21 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/wooden_blacksmith_gavel": {"criteria": {"has_planks": "2025-07-26 13:58:20 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-26 14:08:43 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_cherry_fence": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "constructionwand:recipes/misc/core_angel": {"criteria": {"has_item": "2025-07-26 14:08:42 +0800"}, "done": true}, "nethersdelight:recipes/tools/diamond_machete": {"criteria": {"has_diamond": "2025-07-26 14:17:46 +0800"}, "done": true}, "supplementaries:recipes/daub_frame": {"criteria": {"forge:rods/wooden": "2025-07-26 15:18:44 +0800"}, "done": true}, "utilitarian:recipes/misc/redstone_clock": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "computercraft:recipes/redstone/computer_normal": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/lapis_dust_from_gem": {"criteria": {"has_lapis_gem": "2025-07-26 15:04:29 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_red": {"criteria": {"chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "trashcans:recipes/misc/item_trash_can": {"criteria": {"recipe_condition": "2025-07-26 15:22:40 +0800"}, "done": true}, "rftoolscontrol:recipes/rftoolscontrol/card_base": {"criteria": {"redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_cooler": {"criteria": {"has_chest": "2025-07-26 15:22:40 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/mint_banner": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "travelersbackpack:recipes/misc/blank_upgrade": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "toolbelt:recipes/belt": {"criteria": {"has_leather": "2025-07-26 14:17:44 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/minigun_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/powered_toggle_latch": {"criteria": {"has_item": "2025-07-26 15:05:56 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_yellow": {"criteria": {"has_stick": "2025-07-26 15:18:44 +0800"}, "done": true}, "travelersbackpack:recipes/building_blocks/dye_yellow_sleeping_bag": {"criteria": {"has_needed_dye": "2025-07-26 15:00:02 +0800"}, "done": true}, "securitycraft:recipes/redstone/panic_button": {"criteria": {"has_redstone": "2025-07-26 15:05:56 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/entity_tracker_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "croptopia:recipes/fruit_salad": {"criteria": {"has_apple": "2025-07-26 14:15:44 +0800"}, "done": true}, "handcrafted:recipes/misc/jungle_couch": {"criteria": {"has_jungle_couch": "2025-07-26 13:58:20 +0800"}, "done": true}, "minecraft:recipes/redstone/spruce_fence_gate": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "croptopia:recipes/shaped_water_bottle": {"criteria": {"has_water": "2025-07-26 14:16:25 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/elytra_upgrade": {"criteria": {"has_lapis_lazuli": "2025-07-26 15:04:29 +0800"}, "done": true}, "cfm:recipes/decorations/orange_grill": {"criteria": {"has_iron": "2025-07-26 15:00:05 +0800"}, "done": true}, "create:recipes/building_blocks/oak_window": {"criteria": {"has_ingredient": "2025-07-26 15:20:51 +0800"}, "done": true}, "minecraft:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_iron_horse_armor": "2025-07-26 14:08:43 +0800"}, "done": true}, "blue_skies:recipes/building_blocks/trough": {"criteria": {"has_item": "2025-07-26 15:20:18 +0800"}, "done": true}, "twigs:recipes/smooth_stone_bricks/smooth_stone_brick_wall_from_smooth_stone_stonecutting": {"criteria": {"has_smooth_stone": "2025-07-26 15:05:56 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/iron_dust_from_ingot": {"criteria": {"has_iron_ingot": "2025-07-26 15:00:05 +0800"}, "done": true}, "alchemistry:recipes/dissolver/honeycomb_block": {"criteria": {"has_the_recipe": "2025-07-26 15:11:47 +0800"}, "done": true}, "minecraft:recipes/building_blocks/spruce_stairs": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "farmersdelight:recipes/building_blocks/onion_crate": {"criteria": {"has_onion": "2025-07-26 14:08:43 +0800"}, "done": true}, "create:recipes/building_blocks/tiled_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-26 15:19:13 +0800"}, "done": true}, "handcrafted:recipes/misc/spruce_side_table": {"criteria": {"has_spruce_side_table": "2025-07-26 15:20:16 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_table": {"criteria": {"has_planks": "2025-07-26 15:20:16 +0800"}, "done": true}, "DataVersion": 3465}