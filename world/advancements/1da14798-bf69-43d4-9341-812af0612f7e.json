{"undergarden:undergarden/root": {"criteria": {"tick": "2025-07-10 08:23:20 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-10 08:23:20 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-10 08:23:20 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-10 08:23:20 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-10 08:23:20 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-10 08:23:20 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-10 08:23:20 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-10 08:23:20 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-10 08:23:20 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-10 08:23:20 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-10 08:23:20 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-10 08:23:20 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-10 08:23:20 +0800"}, "done": false}, "lootr:root": {"criteria": {"always_true": "2025-07-10 08:23:20 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-10 08:23:20 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-10 08:23:21 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:bamboo_jungle": "2025-07-10 08:35:32 +0800", "minecraft:beach": "2025-07-10 08:23:21 +0800", "minecraft:sparse_jungle": "2025-07-10 08:38:26 +0800", "minecraft:lukewarm_ocean": "2025-07-10 08:27:36 +0800", "minecraft:desert": "2025-07-10 08:35:38 +0800", "minecraft:deep_lukewarm_ocean": "2025-07-10 08:27:47 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/all_biomes": {"criteria": {"floodplain": "2025-07-10 08:26:21 +0800", "lush_savanna": "2025-07-10 08:27:22 +0800", "rocky_rainforest": "2025-07-10 08:26:25 +0800"}, "done": false}, "biomesoplenty:biomesoplenty/root": {"criteria": {"floodplain": "2025-07-10 08:26:21 +0800"}, "done": true}, "maidensmerrymaking:easter/root": {"criteria": {"requirement": "2025-07-10 08:26:34 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-10 08:26:48 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-10 08:26:48 +0800", "has_carrot": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-10 08:26:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-10 08:26:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-10 08:26:49 +0800", "has_axe": "2025-07-10 08:26:49 +0800", "has_helmet": "2025-07-10 08:26:49 +0800", "has_fishing_rod": "2025-07-10 08:26:49 +0800", "has_bow": "2025-07-10 08:26:49 +0800", "has_chestplate": "2025-07-10 08:26:48 +0800", "has_sword": "2025-07-10 08:26:48 +0800", "has_fillet_knife": "2025-07-10 08:26:49 +0800", "has_shovel": "2025-07-10 08:26:48 +0800", "has_hoe": "2025-07-10 08:26:49 +0800", "has_boots": "2025-07-10 08:26:49 +0800", "has_pickaxe": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-10 08:26:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-10 08:26:48 +0800"}, "done": true}, "railcraft:recipes/coke_oven/charcoal": {"criteria": {"has_logs": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-10 08:26:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-10 08:26:48 +0800"}, "done": true}, "enderio:recipes/misc/stick": {"criteria": {"has_ingredient": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-10 08:26:48 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:48 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:48 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:48 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-10 08:26:48 +0800", "has_wpt": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:48 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-10 08:26:48 +0800", "has_fillet_knife": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "minecraft:recipes/misc/charcoal": {"criteria": {"has_log": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-10 08:26:49 +0800", "has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "securitycraft:recipes/misc/sc_manual": {"criteria": {"has_wood": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-10 08:26:49 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-10 08:26:49 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-10 08:26:49 +0800"}, "done": true}, "naturescompass:natures_compass": {"criteria": {"has_recipe": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-10 08:26:49 +0800", "has_fillet_knife": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-10 08:26:49 +0800"}, "done": true}, "naturescompass:natures_compass_log": {"criteria": {"has_log": "2025-07-10 08:26:49 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-10 08:26:49 +0800", "has_wpt": "2025-07-10 08:26:49 +0800", "has_wct": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "utilitarian:recipes/food/utility/charcoal_from_campfire": {"criteria": {"has_logs": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-10 08:26:49 +0800", "has_fillet_knife": "2025-07-10 08:26:49 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/architectscutter": {"criteria": {"has_log": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/logs_to_bowls": {"criteria": {"has_log": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-10 08:26:49 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:49 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-10 08:26:49 +0800"}, "done": true}, "naturescompass:natures_compass_sapling": {"criteria": {"has_recipe": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-10 08:26:49 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-10 08:26:49 +0800", "has_fillet_knife": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-10 08:26:49 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-10 08:26:49 +0800", "has_wct": "2025-07-10 08:26:49 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-10 08:26:49 +0800", "has_wct": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-10 08:26:49 +0800"}, "done": true}, "alchemistry:recipes/dissolver/charcoal": {"criteria": {"has_the_recipe": "2025-07-10 08:26:49 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-10 08:26:49 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-10 08:26:49 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-10 08:26:56 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_blue_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_yellow_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/orange_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "botania:recipes/redstone/red_string_container": {"criteria": {"has_base_block": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/fridge_light": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_red_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_blue_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/red_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/pink_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_red_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/green_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/white_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/spruce_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "minecolonies:recipes/misc/supplycampdeployer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/gray_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/blue_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_mangrove_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_chest_from_vanilla_chest": {"criteria": {"has_vanilla_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/blue_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_routing": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_yellow": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_blue_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_acacia_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/crimson_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/purple_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/lime_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/magenta_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_routing": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_warped_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/purple_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/black_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "sfm:recipes/misc/manager": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/black_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_dark_oak_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/mangrove_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_spruce_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "sfm:recipes/misc/fancy_cable": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/brown_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/brown_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_green": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard_no_tanks": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/red_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/pink_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/green_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/gray_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_yellow_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_red": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_birch_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/orange_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "littlelogistics:recipes/transportation/barge": {"criteria": {"has_item": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/fridge_dark": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/netcable_green_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_red_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_blue": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_jungle_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/stripped_crimson_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_yellow": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/birch_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_blue": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/light_blue_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/yellow_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/white_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/warped_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/lime_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/birch_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "sfm:recipes/misc/cable": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_yellow_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/acacia_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/cyan_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/jungle_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "sfm:recipes/misc/fancy_to_cable": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_green_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_green": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/warped_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/connector_green_dye": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/oak_mail_box": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/dark_oak_kitchen_drawer": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "travelersbackpack:recipes/misc/standard": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "xnet:recipes/xnet/advanced_connector_red": {"criteria": {"chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "trashcans:recipes/misc/item_trash_can": {"criteria": {"recipe_condition": "2025-07-10 08:26:57 +0800"}, "done": true}, "cfm:recipes/decorations/light_gray_cooler": {"criteria": {"has_chest": "2025-07-10 08:26:57 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/willow_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/dead_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/magic_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/palm_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/empyreal_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/birch_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "aether:recipes/transportation/skyroot_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "deeperdarker:recipes/transportation/bloom_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/cherry_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/fir_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/spruce_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/hellbark_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/jacaranda_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/dark_oak_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/umbran_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "twilightforest:recipes/transportation/time_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "undergarden:recipes/transportation/wigglewood_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "undergarden:recipes/transportation/grongle_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/pine_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "twilightforest:recipes/transportation/mangrove_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/redwood_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "twilightforest:recipes/transportation/canopy_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/bamboo_raft": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/oak_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "deeperdarker:recipes/transportation/echo_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/acacia_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "minecraft:recipes/transportation/jungle_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "twilightforest:recipes/transportation/mining_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/maple_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "undergarden:recipes/transportation/smogstem_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "twilightforest:recipes/transportation/sorting_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "twilightforest:recipes/transportation/twilight_oak_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "twilightforest:recipes/transportation/transformation_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "biomesoplenty:recipes/transportation/mahogany_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "twilightforest:recipes/transportation/dark_boat": {"criteria": {"in_water": "2025-07-10 08:27:35 +0800"}, "done": true}, "cataclysm:find_sunken_city": {"criteria": {"factory": "2025-07-10 08:27:36 +0800"}, "done": true}, "dungeons_arise:find_illager_corsair_or_illager_galley": {"criteria": {"location1": "2025-07-10 08:28:29 +0800"}, "done": true}, "lootr:1chest": {"criteria": {"opened_chest": "2025-07-10 08:28:30 +0800"}, "done": true}, "lootr:social": {"criteria": {"opened_barrel": "2025-07-10 08:28:33 +0800", "opened_chest": "2025-07-10 08:28:30 +0800"}, "done": false}, "lootr:1barrel": {"criteria": {"opened_barrel": "2025-07-10 08:28:33 +0800"}, "done": true}, "nethersdelight:recipes/tools/golden_machete": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/combat/golden_boots": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "farmersdelight:recipes/combat/golden_knife": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/sanity_meter": {"criteria": {"has_ingots/gold": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gold_block": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "aquaculture:recipes/tools/gold_fillet_knife": {"criteria": {"has_items": "2025-07-10 08:28:41 +0800"}, "done": true}, "supplementaries:recipes/hourglass": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/golden_apple": {"criteria": {"has_the_recipe": "2025-07-10 08:28:41 +0800"}, "done": true}, "supplementaries:recipes/gold_door": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "aether:recipes/combat/golden_pendant": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/gold_nugget": {"criteria": {"has_the_recipe": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/tools/golden_axe": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/redstone/light_weighted_pressure_plate": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "utilitarian:recipes/misc/angel_block_rot": {"criteria": {"has_gold": "2025-07-10 08:28:41 +0800"}, "done": true}, "supplementaries:recipes/key": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "aether:recipes/combat/golden_gloves": {"criteria": {"has_gold": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/combat/golden_leggings": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/misc/gold_nugget": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "railcraft:recipes/misc/gold_gear": {"criteria": {"has_material": "2025-07-10 08:28:41 +0800"}, "done": true}, "aether:recipes/combat/golden_ring": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "alchemistry:recipes/dissolver/gold_block": {"criteria": {"has_the_recipe": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/combat/golden_sword": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "mythicbotany:recipes/misc/alfsteel_template": {"criteria": {"criterion0": "2025-07-10 08:28:41 +0800"}, "done": true}, "undergarden:recipes/tools/catalyst": {"criteria": {"has_gold": "2025-07-10 08:28:41 +0800"}, "done": true}, "utilitix:recipes/misc/gilding_crystal": {"criteria": {"criterion0": "2025-07-10 08:28:41 +0800"}, "done": true}, "bloodmagic:recipes/misc/blood_altar": {"criteria": {"has_gold": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/tools/golden_hoe": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "mcwlights:recipes/gold_stuff": {"criteria": {"has_wood": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/food/golden_apple": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "alltheores:recipes/misc/gold_gear": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/gold]_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "utilitarian:recipes/misc/angel_block": {"criteria": {"has_gold": "2025-07-10 08:28:41 +0800"}, "done": true}, "utilitix:recipes/misc/directional_rail": {"criteria": {"criterion0": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/combat/golden_helmet": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "forbidden_arcanus:recipes/misc/deorum_ingot": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "alltheores:recipes/misc/gold_rod": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/gold]_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/golden_blacksmith_gavel": {"criteria": {"has_ingots/gold": "2025-07-10 08:28:41 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/gold_dust_from_ingot": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "additionallanterns:recipes/misc/gold_chain": {"criteria": {"recipe_condition": "2025-07-10 08:28:41 +0800"}, "done": true}, "utilitix:recipes/misc/advanced_brewery": {"criteria": {"criterion3": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/tools/golden_shovel": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/combat/golden_chestplate": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "travelersbackpack:recipes/misc/gold": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/plate_gold_hammering": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "supplementaries:recipes/gold_trapdoor": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "travelersbackpack:recipes/misc/gold_tier_upgrade": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/tools/golden_pickaxe": {"criteria": {"has_gold_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "alltheores:recipes/misc/gold_plate": {"criteria": {"has_TagKey[minecraft:item / forge:ingots/gold]_ingot": "2025-07-10 08:28:41 +0800"}, "done": true}, "minecraft:recipes/food/cooked_mutton_from_smoking": {"criteria": {"has_mutton": "2025-07-10 08:28:59 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cooked_mutton": {"criteria": {"has_the_recipe": "2025-07-10 08:28:59 +0800"}, "done": true}, "farmersdelight:recipes/cooking/pasta_with_mutton_chop": {"criteria": {"has_any_ingredient": "2025-07-10 08:28:59 +0800"}, "done": true}, "minecraft:recipes/food/cooked_mutton_from_grill_cooking": {"criteria": {"has_mutton": "2025-07-10 08:28:59 +0800"}, "done": true}, "minecraft:recipes/food/cooked_mutton": {"criteria": {"has_mutton": "2025-07-10 08:28:59 +0800"}, "done": true}, "minecolonies:recipes/food/mutton_dinner": {"criteria": {"has_mutton": "2025-07-10 08:28:59 +0800"}, "done": true}, "minecraft:recipes/food/cooked_mutton_from_campfire_cooking": {"criteria": {"has_mutton": "2025-07-10 08:28:59 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_smoking": {"criteria": {"has_porkchop": "2025-07-10 08:29:00 +0800"}, "done": true}, "croptopia:recipes/pork_and_beans": {"criteria": {"has_pork": "2025-07-10 08:29:00 +0800"}, "done": true}, "croptopia:recipes/carnitas": {"criteria": {"has_porkchop": "2025-07-10 08:29:00 +0800"}, "done": true}, "croptopia:recipes/shaped_bacon": {"criteria": {"has_porkchop": "2025-07-10 08:29:00 +0800"}, "done": true}, "supplementaries:recipes/soap": {"criteria": {"has_porkchop": "2025-07-10 08:29:00 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_grill_cooking": {"criteria": {"has_porkchop": "2025-07-10 08:29:00 +0800"}, "done": true}, "farmersdelight:recipes/cooking/noodle_soup": {"criteria": {"has_any_ingredient": "2025-07-10 08:29:00 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop_from_campfire_cooking": {"criteria": {"has_porkchop": "2025-07-10 08:29:00 +0800"}, "done": true}, "alchemistry:recipes/dissolver/cooked_porkchop": {"criteria": {"has_the_recipe": "2025-07-10 08:29:00 +0800"}, "done": true}, "travelersbackpack:recipes/misc/pig": {"criteria": {"has_porkchop": "2025-07-10 08:29:00 +0800"}, "done": true}, "farmersdelight:recipes/cooking/pumpkin_soup": {"criteria": {"has_any_ingredient": "2025-07-10 08:29:00 +0800"}, "done": true}, "croptopia:recipes/pork_jerky": {"criteria": {"has_pork": "2025-07-10 08:29:00 +0800"}, "done": true}, "minecraft:recipes/food/cooked_porkchop": {"criteria": {"has_porkchop": "2025-07-10 08:29:00 +0800"}, "done": true}, "croptopia:recipes/food/pork_jerky": {"criteria": {"has_the_recipe": "2025-07-10 08:29:00 +0800"}, "done": true}, "travelersbackpack:recipes/misc/coal": {"criteria": {"has_coal": "2025-07-10 08:29:02 +0800"}, "done": true}, "silentgear:recipes/misc/stone_torch": {"criteria": {"has_item": "2025-07-10 08:29:02 +0800"}, "done": true}, "mcwlights:recipes/torches": {"criteria": {"has_wood": "2025-07-10 08:29:02 +0800"}, "done": true}, "utilitix:recipes/misc/tiny_coal_to_tiny": {"criteria": {"criterion0": "2025-07-10 08:29:02 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/coal_dust_from_gem": {"criteria": {"has_coal_gem": "2025-07-10 08:29:02 +0800"}, "done": true}, "minecraft:recipes/decorations/campfire": {"criteria": {"has_coal": "2025-07-10 08:29:02 +0800"}, "done": true}, "alchemistry:recipes/dissolver/coal_block": {"criteria": {"has_the_recipe": "2025-07-10 08:29:02 +0800"}, "done": true}, "railcraft:recipes/coke_oven/coal_coke": {"criteria": {"has_coal": "2025-07-10 08:29:02 +0800"}, "done": true}, "bigreactors:recipes/misc/blasting/graphite_from_coal": {"criteria": {"has_item": "2025-07-10 08:29:02 +0800"}, "done": true}, "mcwbridges:recipes/bridge_lantern": {"criteria": {"has_planks": "2025-07-10 08:29:02 +0800"}, "done": true}, "sgjourney:recipes/misc/fire_pit": {"criteria": {"has_coal": "2025-07-10 08:29:02 +0800"}, "done": true}, "bigreactors:recipes/misc/smelting/graphite_from_coal": {"criteria": {"has_item": "2025-07-10 08:29:02 +0800"}, "done": true}, "minecraft:recipes/building_blocks/coal_block": {"criteria": {"has_coal": "2025-07-10 08:29:02 +0800"}, "done": true}, "repurposed_structures:pyramids": {"criteria": {"in_pyramid_ocean": "2025-07-10 08:31:05 +0800"}, "done": false}, "handcrafted:recipes/misc/bear_trophy": {"criteria": {"has_bear_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "twigs:recipes/tables/oak_table": {"criteria": {"has_bamboo": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/goat_trophy": {"criteria": {"has_goat_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwbridges:recipes/asian_red_bridge_pier": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_onside": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_bench": {"criteria": {"has_oak_bench": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwbridges:recipes/asian_red_bridge": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwwindows:recipes/shutters": {"criteria": {"has_wood": "2025-07-10 08:31:30 +0800"}, "done": true}, "supplementaries:recipes/notice_board": {"criteria": {"minecraft:planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_cupboard": {"criteria": {"has_oak_cupboard": "2025-07-10 08:31:30 +0800"}, "done": true}, "domum_ornamentum:recipes/building_blocks/blockbarreldeco_standing": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/skeleton_horse_trophy": {"criteria": {"has_skeleton_horse_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_nightstand": {"criteria": {"has_oak_nightstand": "2025-07-10 08:31:30 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/oak_planks_1x": {"criteria": {"has_oak_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/amber_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwbridges:recipes/oak_log_bridge_middle": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/peach_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "supplementaries:recipes/speaker_block": {"criteria": {"minecraft:planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/fox_trophy": {"criteria": {"has_fox_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_pillar_trim": {"criteria": {"has_oak_pillar_trim": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/pufferfish_trophy": {"criteria": {"has_pufferfish_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_side_table": {"criteria": {"has_oak_side_table": "2025-07-10 08:31:30 +0800"}, "done": true}, "supplementaries:recipes/pulley": {"criteria": {"minecraft:planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/bubblegum_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/misc/stick": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_desk_cabinet": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwtrpdoors:recipes/oak/oak_blossom_trapdoor": {"criteria": {"has_plankss": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/conifer_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_1": {"criteria": {"has_oak_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_2": {"criteria": {"has_oak_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_3": {"criteria": {"has_oak_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_limited_barrel_4": {"criteria": {"has_oak_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_chair": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwbridges:recipes/rope_oak_bridge": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwbridges:recipes/oak_rail_bridge": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_chair": {"criteria": {"has_oak_chair": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/persimmon_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_barrel": {"criteria": {"has _plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_cabinet": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/ultramarine_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_corner_trim": {"criteria": {"has_oak_corner_trim": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_dark": {"criteria": {"has_bottom": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/mint_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwroofs:recipes/oak_planks": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_desk": {"criteria": {"has_oak_desk": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_desk": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/fluorescent_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "alchemistry:recipes/dissolver/stick": {"criteria": {"has_the_recipe": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/decorations/barrel": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/cherenkov_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/aquamarine_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_button": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_sink_light": {"criteria": {"has_bottom": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_coffee_table": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwwindows:recipes/parapets": {"criteria": {"has_wood": "2025-07-10 08:31:30 +0800"}, "done": true}, "framedblocks:recipes/building_blocks/framed_cube": {"criteria": {"hasPlanks": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_slab": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_door": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "supplementaries:recipes/turn_table": {"criteria": {"minecraft:planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/blaze_trophy": {"criteria": {"has_blaze_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/wolf_trophy": {"criteria": {"has_wolf_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwpaths:recipes/oak": {"criteria": {"has_wood": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_fancy_bed": {"criteria": {"has_oak_fancy_bed": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_table": {"criteria": {"has_oak_table": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/silverfish_trophy": {"criteria": {"has_silverfish_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_chest": {"criteria": {"has _plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_dining_bench": {"criteria": {"has_oak_dining_bench": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/icy_blue_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "supplementaries:recipes/clock_block": {"criteria": {"minecraft:planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/navy_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "silentgear:recipes/misc/upgrade_base": {"criteria": {"has_item": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_bedside_cabinet": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/oak_stairs": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/decorations/oak_fence": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_2": {"criteria": {"has _plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_3": {"criteria": {"has _plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_1": {"criteria": {"has _plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/generic_limited_barrel_4": {"criteria": {"has _plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/tropical_fish_trophy": {"criteria": {"has_tropical_fish_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_drawer": {"criteria": {"has_oak_drawer": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_shelf": {"criteria": {"has_oak_shelf": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/lavender_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_table": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/maroon_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_counter": {"criteria": {"has_oak_counter": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_kitchen_counter": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_trapdoor": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/honey_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "supplementaries:recipes/bed_from_feather_block": {"criteria": {"minecraft:planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/oak_couch": {"criteria": {"has_oak_couch": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/spider_trophy": {"criteria": {"has_spider_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_park_bench": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/wine_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_pressure_plate": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/rose_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/spring_green_bed": {"criteria": {"has_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/wither_skeleton_trophy": {"criteria": {"has_wither_skeleton_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/decorations/oak_sign": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "supplementaries:recipes/lock_block": {"criteria": {"minecraft:planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "mcwwindows:recipes/blinds": {"criteria": {"has_wood": "2025-07-10 08:31:30 +0800"}, "done": true}, "cfm:recipes/decorations/oak_crate": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "handcrafted:recipes/misc/salmon_trophy": {"criteria": {"has_salmon_trophy": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_chest": {"criteria": {"has_oak_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/redstone/oak_fence_gate": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/oak_barrel": {"criteria": {"has_oak_plank": "2025-07-10 08:31:30 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/wooden_blacksmith_gavel": {"criteria": {"has_planks": "2025-07-10 08:31:30 +0800"}, "done": true}, "create:recipes/building_blocks/oak_window": {"criteria": {"has_ingredient": "2025-07-10 08:31:30 +0800"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-10 08:31:42 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_chest": {"criteria": {"has_lots_of_items": "2025-07-10 08:31:42 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_jungle_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_magenta": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "utilitix:recipes/misc/armed_stand": {"criteria": {"criterion1": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/persimmon_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dark_oak_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_dark_oak_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_oak_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_bamboo_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/timber_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "utilitix:recipes/misc/reinforced_rail": {"criteria": {"criterion2": "2025-07-10 08:31:58 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear": {"criteria": {"has_ingredient": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_cyan": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/motion_activated_light": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_orange": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_spruce_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/maroon_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "aquaculture:recipes/tools/wooden_fillet_knife": {"criteria": {"has_items": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/peach_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_shovel": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_birch_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/conifer_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "undergarden:recipes/decorations/torch_ditchbulb_paste": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/edelwood_ladder": {"criteria": {"has_rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/spring_green_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_acacia_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/ultramarine_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_gray": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_lime": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "minecraft:recipes/decorations/ladder": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_axe": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "mcwwindows:recipes/curtain_rods": {"criteria": {"has_wood": "2025-07-10 08:31:58 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/no_soliciting_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_oak_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_purple": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_gray": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/daub_brace": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/crank": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_pink": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/timber_frame": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_hoe": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/sonic_security_system": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "enderio:recipes/misc/wood_gear_corner": {"criteria": {"has_ingredient": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/lavender_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/crank": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "minecolonies:recipes/misc/shapetool": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "undergarden:recipes/decorations/undergarden_scaffolding": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_jungle_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/navy_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/bubblegum_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "utilitix:recipes/misc/directional_highspeed_rail": {"criteria": {"criterion1": "2025-07-10 08:31:58 +0800"}, "done": true}, "minecraft:recipes/combat/wooden_sword": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_green": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_white": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "undergarden:recipes/decorations/shard_torch": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_cherry_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/honey_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "minecraft:recipes/tools/wooden_pickaxe": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_mangrove_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_crimson_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/torch": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_acacia_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/cherenkov_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "utilitix:recipes/misc/hand_bell": {"criteria": {"criterion0": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/fluorescent_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_blue": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_warped_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_mangrove_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "farmersdelight:recipes/decorations/cutting_board": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_spruce_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_birch_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/amber_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "utilitix:recipes/misc/weak_redstone_torch": {"criteria": {"criterion1": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/daub_cross_brace": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_bamboo_fence_gate": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/timber_brace": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/aquamarine_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "paraglider:recipes/misc/paraglider": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_black": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_crimson_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/wine_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "undergarden:recipes/combat/slingshot": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/faucet": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "utilitix:recipes/misc/highspeed_rail": {"criteria": {"criterion1": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_warped_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_light_blue": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/rose_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/icy_blue_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_red": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/slingshot": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "silentgear:recipes/misc/rough_rod": {"criteria": {"has_item": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_brown": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "farmersdelight:recipes/combat/flint_knife": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "securitycraft:recipes/misc/reinforced_cherry_fence": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/daub_frame": {"criteria": {"forge:rods/wooden": "2025-07-10 08:31:58 +0800"}, "done": true}, "dyenamics:recipes/misc/banner/mint_banner": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "supplementaries:recipes/flags/flag_yellow": {"criteria": {"has_stick": "2025-07-10 08:31:58 +0800"}, "done": true}, "aether:recipes/tools/wooden_pickaxe_repairing": {"criteria": {"has_wooden_pickaxe": "2025-07-10 08:32:00 +0800"}, "done": true}, "mcwwindows:recipes/dark_prismarine": {"criteria": {"has_wood": "2025-07-10 08:32:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dark_prismarine_slab_from_dark_prismarine_stonecutting": {"criteria": {"has_dark_prismarine": "2025-07-10 08:32:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dark_prismarine_stairs": {"criteria": {"has_dark_prismarine": "2025-07-10 08:32:22 +0800"}, "done": true}, "mcwpaths:recipes/dark_prismarine": {"criteria": {"has_wood": "2025-07-10 08:32:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dark_prismarine_stairs_from_dark_prismarine_stonecutting": {"criteria": {"has_dark_prismarine": "2025-07-10 08:32:22 +0800"}, "done": true}, "additionallanterns:recipes/misc/dark_prismarine_chain": {"criteria": {"recipe_condition": "2025-07-10 08:32:22 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dark_prismarine_slab": {"criteria": {"has_dark_prismarine": "2025-07-10 08:32:22 +0800"}, "done": true}, "mcwroofs:recipes/prismarine": {"criteria": {"has_planks": "2025-07-10 08:32:24 +0800"}, "done": true}, "mcwbridges:recipes/prismarine_bricks_bridge_pier": {"criteria": {"has_planks": "2025-07-10 08:32:24 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_brick_slab_from_prismarine_stonecutting": {"criteria": {"has_prismarine_brick": "2025-07-10 08:32:24 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_prismarine_bricks_bridge": {"criteria": {"has_planks": "2025-07-10 08:32:24 +0800"}, "done": true}, "mcwbridges:recipes/prismarine_bricks_bridge_stair": {"criteria": {"has_planks": "2025-07-10 08:32:24 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_brick_stairs_from_prismarine_stonecutting": {"criteria": {"has_prismarine_brick": "2025-07-10 08:32:24 +0800"}, "done": true}, "additionallanterns:recipes/misc/prismarine_chain": {"criteria": {"recipe_condition": "2025-07-10 08:32:24 +0800"}, "done": true}, "mcwbridges:recipes/prismarine_bricks_bridge": {"criteria": {"has_planks": "2025-07-10 08:32:24 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_brick_slab": {"criteria": {"has_prismarine_bricks": "2025-07-10 08:32:24 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_brick_stairs": {"criteria": {"has_prismarine_bricks": "2025-07-10 08:32:24 +0800"}, "done": true}, "minecraft:recipes/decorations/prismarine_wall_from_prismarine_stonecutting": {"criteria": {"has_prismarine": "2025-07-10 08:32:55 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_slab": {"criteria": {"has_prismarine": "2025-07-10 08:32:55 +0800"}, "done": true}, "mcwwindows:recipes/prismarine": {"criteria": {"has_wood": "2025-07-10 08:32:55 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_stairs": {"criteria": {"has_prismarine": "2025-07-10 08:32:55 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_slab_from_prismarine_stonecutting": {"criteria": {"has_prismarine": "2025-07-10 08:32:55 +0800"}, "done": true}, "minecraft:recipes/building_blocks/prismarine_stairs_from_prismarine_stonecutting": {"criteria": {"has_prismarine": "2025-07-10 08:32:55 +0800"}, "done": true}, "minecraft:recipes/decorations/prismarine_wall": {"criteria": {"has_prismarine": "2025-07-10 08:32:55 +0800"}, "done": true}, "lootr:10loot": {"criteria": {"loot_stat": "2025-07-10 08:33:02 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/logistics_frame_storage": {"criteria": {"has_logistics_core": "2025-07-10 08:33:07 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/logistics_frame_default_storage": {"criteria": {"has_logistics_core": "2025-07-10 08:33:07 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/logistics_frame_requester": {"criteria": {"has_logistics_core": "2025-07-10 08:33:07 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/logistics_frame_active_provider": {"criteria": {"has_logistics_core": "2025-07-10 08:33:07 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/logistics_frame_passive_provider": {"criteria": {"has_logistics_core": "2025-07-10 08:33:07 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/classify_filter": {"criteria": {"has_logistics_core": "2025-07-10 08:33:07 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/logistics_module": {"criteria": {"has_logistics_core": "2025-07-10 08:33:07 +0800"}, "done": true}, "silentgear:root": {"criteria": {"get_item": "2025-07-10 08:33:13 +0800"}, "done": true}, "minecraft:story/root": {"criteria": {"crafting_table": "2025-07-10 08:33:13 +0800"}, "done": true}, "paraglider:root": {"criteria": {"crafting_table": "2025-07-10 08:33:13 +0800"}, "done": true}, "rftoolsbase:recipes/rftoolsbase/crafting_card": {"criteria": {"crafter": "2025-07-10 08:33:13 +0800"}, "done": true}, "create:recipes/misc/crafting/appliances/crafting_blueprint": {"criteria": {"has_item": "2025-07-10 08:33:13 +0800"}, "done": true}, "tombstone:adventure/cancel_ghostly_shape": {"criteria": {"coded_trigger": "2025-07-10 08:33:40 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/iron_backpack_from_copper": {"criteria": {"has_copper_backpack": "2025-07-10 08:33:50 +0800"}, "done": true}, "enderio:recipes/building_blocks/dark_steel_ladder": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/misc/dark_steel_nugget": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/building_blocks/dark_steel_door": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/building_blocks/dark_steel_bars": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/misc/dark_steel_grinding_ball": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/tools/dark_steel_sword": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/tools/glider_wing": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/building_blocks/dark_steel_trapdoor": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/tools/cold_fire_igniter": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/misc/dark_steel_nugget_to_ingot": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "enderio:recipes/building_blocks/dark_steel_pressure_plate": {"criteria": {"has_ingredient": "2025-07-10 08:33:57 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/green_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "littlelogistics:recipes/transportation/spring": {"criteria": {"has_item": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/lime_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "minecraft:recipes/combat/bow": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/pink_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/purple_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "supplementaries:recipes/bomb": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "travelersbackpack:recipes/misc/spider": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "minecraft:recipes/decorations/loom": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "minecraft:recipes/decorations/candle": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_tripwire_hook": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/light_gray_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/gray_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "minecraft:recipes/combat/crossbow": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/black_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/magenta_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_loom": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/orange_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/light_blue_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/cyan_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/yellow_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/red_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/brown_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "supplementaries:recipes/sack": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/white_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "minecraft:recipes/tools/fishing_rod": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_wool_from_string": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "minecraft:recipes/redstone/tripwire_hook": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "domum_ornamentum:recipes/decorations/blue_floating_carpet": {"criteria": {"has_string": "2025-07-10 08:34:01 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/storage_magnet_upgrade_from_backpack_magnet_upgrade": {"criteria": {"has_backpack_upgrade": "2025-07-10 08:34:17 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/advanced_magnet_upgrade_from_basic": {"criteria": {"has_magnet_upgrade": "2025-07-10 08:34:17 +0800"}, "done": true}, "botania:recipes/tools/flower_bag": {"criteria": {"has_item": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/yellow_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/black_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/orange_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/green_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "botania:main/flower_pickup": {"criteria": {"flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/magenta_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/brown_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "botania:recipes/decorations/blue_shiny_flower": {"criteria": {"has_item": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/light_gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "botania:challenge/root": {"criteria": {"flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/lime_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "botania:main/root": {"criteria": {"flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/purple_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/red_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/blue_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "botania:main/flower_pickup_lexicon": {"criteria": {"flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/pink_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/cyan_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/white_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "botania:recipes/misc/petal_blue": {"criteria": {"has_item": "2025-07-10 08:36:19 +0800"}, "done": true}, "utilitarian:recipes/misc/no_soliciting/soliciting_carpets/gray_soliciting_carpet": {"criteria": {"has_flower": "2025-07-10 08:36:19 +0800"}, "done": true}, "handcrafted:recipes/misc/blue_bowl": {"criteria": {"has_blue_bowl": "2025-07-10 08:36:23 +0800"}, "done": true}, "connectedglass:recipes/misc/clear_glass_blue_pane2": {"criteria": {"recipe_condition2": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dye_blue_bed": {"criteria": {"has_needed_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "handcrafted:recipes/misc/blue_plate": {"criteria": {"has_blue_plate": "2025-07-10 08:36:23 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_advanced_overlays/turtle_rainbow_overlay": {"criteria": {"has_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_covered_blue": {"criteria": {"has_dyes/black": "2025-07-10 08:36:23 +0800"}, "done": true}, "botania:recipes/misc/dye_blue": {"criteria": {"has_the_recipe": "2025-07-10 08:36:23 +0800"}, "done": true}, "handcrafted:recipes/misc/blue_cup": {"criteria": {"has_blue_cup": "2025-07-10 08:36:23 +0800"}, "done": true}, "cfm:recipes/decorations/dye_blue_picket_fence": {"criteria": {"has_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_smart_blue": {"criteria": {"has_dyes/black": "2025-07-10 08:36:23 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/smart_blue": {"criteria": {"has_dyes/black": "2025-07-10 08:36:23 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass_blue2": {"criteria": {"recipe_condition2": "2025-07-10 08:36:23 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_normal_overlays/turtle_rainbow_overlay": {"criteria": {"has_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/misc/cyan_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "connectedglass:recipes/misc/tinted_borderless_glass_blue2": {"criteria": {"recipe_condition2": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/decorations/blue_stained_glass_pane_from_glass_pane": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "travelersbackpack:recipes/building_blocks/dye_blue_sleeping_bag": {"criteria": {"has_needed_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dye_blue_wool": {"criteria": {"has_needed_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass_blue_pane2": {"criteria": {"recipe_condition2": "2025-07-10 08:36:23 +0800"}, "done": true}, "connectedglass:recipes/misc/scratched_glass_blue2": {"criteria": {"recipe_condition2": "2025-07-10 08:36:23 +0800"}, "done": true}, "ad_astra:recipes/misc/small_blue_industrial_lamp": {"criteria": {"has_small_blue_industrial_lamp": "2025-07-10 08:36:23 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/glass_blue": {"criteria": {"has_dyes/black": "2025-07-10 08:36:23 +0800"}, "done": true}, "cfm:recipes/decorations/dye_blue_picket_gate": {"criteria": {"has_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "botania:recipes/building_blocks/azulejo_0": {"criteria": {"has_item": "2025-07-10 08:36:23 +0800"}, "done": true}, "croptopia:recipes/misc/purple_dye": {"criteria": {"has_the_recipe": "2025-07-10 08:36:23 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_normal_overlays/turtle_trans_overlay": {"criteria": {"has_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/decorations/blue_candle": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "connectedglass:recipes/misc/clear_glass_blue2": {"criteria": {"recipe_condition2": "2025-07-10 08:36:23 +0800"}, "done": true}, "computercraft:recipes/redstone/turtle_advanced_overlays/turtle_trans_overlay": {"criteria": {"has_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "supplementaries:recipes/candle_holders/candle_holder_blue_dye": {"criteria": {"forge:dyes/blue": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/misc/light_blue_dye_from_blue_white_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/building_blocks/dye_blue_carpet": {"criteria": {"has_needed_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/misc/magenta_dye_from_blue_red_pink": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/misc/purple_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "connectedglass:recipes/misc/scratched_glass_blue_pane2": {"criteria": {"recipe_condition2": "2025-07-10 08:36:23 +0800"}, "done": true}, "utilitarian:recipes/misc/utility/green_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/blue_brick_extra": {"criteria": {"has_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "ad_astra:recipes/misc/blue_industrial_lamp": {"criteria": {"has_blue_industrial_lamp": "2025-07-10 08:36:23 +0800"}, "done": true}, "minecraft:recipes/misc/magenta_dye_from_blue_red_white_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "domum_ornamentum:recipes/tools/blue_cobblestone_extra": {"criteria": {"has_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_dye": {"criteria": {"has_blue_dye": "2025-07-10 08:36:23 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/energizer/computerport": {"criteria": {"has_item3": "2025-07-10 08:36:23 +0800"}, "done": true}, "handcrafted:recipes/misc/blue_crockery_combo": {"criteria": {"has_blue_crockery_combo": "2025-07-10 08:36:23 +0800"}, "done": true}, "pneumaticcraft:recipes/misc/logistics_frame_requester_self": {"criteria": {"has_logistics_frame_requester": "2025-07-10 08:36:42 +0800"}, "done": true}, "minecraft:recipes/building_blocks/spruce_planks": {"criteria": {"has_logs": "2025-07-10 08:36:50 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_glass_pane": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "utilitix:recipes/misc/glue_ball": {"criteria": {"criterion0": "2025-07-10 08:40:53 +0800", "criterion1": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_gray_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/end_stone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_sticky_piston": {"criteria": {"has_slime_ball": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "create:recipes/building_blocks/vertical_framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/green_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "xnet:recipes/xnet/facade": {"criteria": {"glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/netherite_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/brewing/glass_bottle": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/bricks_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/purple_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge_pier": {"criteria": {"has_planks": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/smooth_stone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/peach_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/black_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/diamond_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/navy_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "mcwlights:recipes/tiki_torches": {"criteria": {"has_wood": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/wine_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/packing_tape": {"criteria": {"has_slime": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "forbidden_arcanus:recipes/decorations/deorum_lantern": {"criteria": {"has_torch": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/spring_green_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/runic_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/mint_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/gold_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/bone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/decorations/glass_pane": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/ultramarine_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "mcwwindows:recipes/mosaic_glass": {"criteria": {"has_wood": "2025-07-10 08:40:53 +0800"}, "done": true}, "supplementaries:recipes/jar": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/dark_prismarine_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "create:recipes/building_blocks/horizontal_framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-10 08:40:53 +0800"}, "done": true}, "createoreexcavation:recipes/misc/vein_atlas": {"criteria": {"map": "2025-07-10 08:40:53 +0800"}, "done": true}, "undergarden:recipes/decorations/gloom_o_lantern": {"criteria": {"has_torch": "2025-07-10 08:40:53 +0800"}, "done": true}, "railcraft:recipes/misc/water_tank_siding": {"criteria": {"has_slime_ball": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/basalt_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "mcwbridges:recipes/bridge_torch": {"criteria": {"has_planks": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/iron_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/rose_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/red_shield_template_block": {"criteria": {"glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_gray_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/deepslate_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/icy_blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "supplementaries:recipes/slice_map": {"criteria": {"has_map": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_black_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/glass_1x": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/lavender_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "botania:recipes/tools/lexicon": {"criteria": {"has_item": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/obsidian_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "packingtape:recipes/tools/tape": {"criteria": {"has_slime_ball": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/bubblegum_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/brown_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "supplementaries:recipes/sconce": {"criteria": {"has_torch": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/quartz_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/redstone/sticky_piston": {"criteria": {"has_slime_ball": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecolonies:recipes/misc/large_empty_bottle": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/blackstone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/light_blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/persimmon_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/fluorescent_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "bloodmagic:recipes/misc/sacrificial_dagger": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge": {"criteria": {"has_planks": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/redstone/slime_block": {"criteria": {"has_slime_ball": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobblestone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/amber_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_white_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/white_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/tools/lead": {"criteria": {"has_slime_ball": "2025-07-10 08:40:53 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/space_chamber_card": {"criteria": {"glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/cherenkov_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/yellow_shield_template_block": {"criteria": {"glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/green_shield_template_block": {"criteria": {"glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "littlelogistics:recipes/transportation/fluid_barge": {"criteria": {"has_item": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/gray_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/emerald_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_blue_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "mcwbridges:recipes/glass_bridge_stair": {"criteria": {"has_planks": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/andesite_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "deepresonance:recipes/deepresonance/resonating_plate": {"criteria": {"has_ore": "2025-07-10 08:40:53 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/deorum_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/diorite_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_light_gray_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/cobbled_deepslate_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/crimson_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "twigs:recipes/lamps/lamp": {"criteria": {"has_item": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/aquamarine_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_nether_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/yellow_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "forbidden_arcanus:recipes/decorations/utrem_jar": {"criteria": {"has_glass/colorless": "2025-07-10 08:40:53 +0800"}, "done": true}, "mcwlights:recipes/lantern": {"criteria": {"has_wood": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_magenta_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/orange_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_purple_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/red_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_green_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "create:recipes/building_blocks/framed_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_red_stained_glass_pane_from_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_lime_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/mossy_cobblestone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blue_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/lime_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_brown_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/stone_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "mcwlights:recipes/paper_lamp": {"criteria": {"has_wood": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/copper_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_yellow_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_pink_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "botania:recipes/tools/speed_up_belt": {"criteria": {"has_item": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/maroon_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_orange_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "rftoolsbuilder:recipes/misc/blue_shield_template_block": {"criteria": {"glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "connectedglass:recipes/misc/borderless_glass1": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_blue_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/pink_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/purpur_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/honey_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/granite_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "forbidden_arcanus:recipes/building_blocks/dark_runic_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/red_nether_bricks_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "dyenamics:recipes/misc/conifer_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/cyan_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/warped_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/amethyst_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/red_sandstone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "alchemistry:recipes/dissolver/slime_block": {"criteria": {"has_the_recipe": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/normal_sandstone_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "additionallanterns:recipes/misc/prismarine_lantern": {"criteria": {"recipe_condition": "2025-07-10 08:40:53 +0800"}, "done": true}, "securitycraft:recipes/building_blocks/reinforced_cyan_stained_glass_pane_from_dye": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:recipes/building_blocks/magenta_stained_glass": {"criteria": {"has_glass": "2025-07-10 08:40:53 +0800"}, "done": true}, "create:recipes/building_blocks/tiled_glass_from_glass_colorless_stonecutting": {"criteria": {"has_glass_colorless": "2025-07-10 08:40:53 +0800"}, "done": true}, "minecraft:husbandry/root": {"criteria": {"consumed_item": "2025-07-10 08:40:59 +0800"}, "done": true}, "sophisticatedbackpacks:recipes/misc/netherite_backpack": {"criteria": {"has_diamond_backpack": "2025-07-10 08:42:50 +0800"}, "done": true}, "sophisticatedstorage:recipes/misc/storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade": {"criteria": {"has_backpack_upgrade": "2025-07-10 08:43:11 +0800"}, "done": true}, "artifacts:amateur_archaeologist": {"criteria": {"find_artifact": "2025-07-10 08:43:11 +0800"}, "done": true}, "DataVersion": 3465}