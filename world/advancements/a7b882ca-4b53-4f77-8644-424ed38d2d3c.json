{"twigs:recipes/columns/blackstone_column": {"criteria": {"has_item": "2025-07-10 16:13:30 +0800"}, "done": true}, "constructionwand:recipes/tools/stone_wand": {"criteria": {"has_item": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/origin_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "undergarden:undergarden/root": {"criteria": {"tick": "2025-07-10 16:10:57 +0800"}, "done": true}, "minecraft:recipes/misc/coal": {"criteria": {"has_coal_block": "2025-07-10 16:14:10 +0800"}, "done": true}, "minecraft:recipes/decorations/chest": {"criteria": {"has_lots_of_items": "2025-07-10 16:13:32 +0800"}, "done": true}, "mcwfences:recipes/jungle": {"criteria": {"has_jungle": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:adventure/sleep_in_bed": {"criteria": {"slept_in_bed": "2025-07-10 17:27:00 +0800"}, "done": true}, "mekanism:root": {"criteria": {"automatic": "2025-07-10 16:10:57 +0800"}, "done": true}, "mcwfences:recipes/warped": {"criteria": {"has_warped": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/honey_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "solcarrot:recipes/food_book": {"criteria": {"has_book": "2025-07-10 16:13:30 +0800", "has_carrot": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blackstone_stairs": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_spade": {"criteria": {"has_certus_quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/rs_engineering": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "alchemistry:recipes/dissolver/redstone_block": {"criteria": {"has_the_recipe": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/from_mangrove_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "betterdungeons:root": {"criteria": {"always": "2025-07-10 16:10:57 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/dense_smart_fluix": {"criteria": {"has_dusts/redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "tombstone:adventure/strong_or_careful": {"criteria": {"coded_trigger": "2025-07-10 17:28:03 +0800"}, "done": true}, "sfm:recipes/misc/disk": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwfences:recipes/birch": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/red_maple_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/tools/iron_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/combat/stone_sword": {"criteria": {"has_cobblestone": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/tools/stone_axe": {"criteria": {"has_cobblestone": "2025-07-10 16:13:30 +0800"}, "done": true}, "appmek:recipes/misc/chemical_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/decorations/oak_fish_mount": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2:recipes/misc/decorative/quartz_block": {"criteria": {"has_certus_quartz_crystal": "2025-07-10 18:24:35 +0800"}, "done": true}, "littlelogistics:recipes/tools/locomotive_route": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_encoding_terminal": {"criteria": {"has_wt": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/from_oak_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "betterdeserttemples:root": {"criteria": {"always": "2025-07-10 16:10:57 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/fluorescent_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "railcraft:recipes/coke_oven/coal_coke_block": {"criteria": {"has_coal_block": "2025-07-10 16:14:10 +0800"}, "done": true}, "forbidden_arcanus:recipes/tools/stone_blacksmith_gavel": {"criteria": {"has_stone_tool_materials": "2025-07-10 16:13:30 +0800"}, "done": true}, "utilitix:recipes/misc/linked_crystal": {"criteria": {"criterion0": "2025-07-10 18:24:35 +0800"}, "done": true}, "ironfurnaces:coal": {"criteria": {"rainbowcoal": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "littlelogistics:recipes/redstone/vessel_charger": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/redstone_information": {"criteria": {"redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "corail_woodcutter:recipes/from_birch_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "trashcans:recipes/misc/energy_trash_can": {"criteria": {"recipe_condition": "2025-07-10 18:24:35 +0800"}, "done": true}, "create:recipes/misc/crafting/materials/rose_quartz": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blackstone_stairs_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/navy_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_pickaxe": {"criteria": {"has_certus_quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/decorations/birch_fish_mount": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:story/mine_stone": {"criteria": {"get_stone": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/prismarine": {"criteria": {"has_qq": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbridges:recipes/balustrade_blackstone_bridge": {"criteria": {"has_planks": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/mangrove": {"criteria": {"has_mangrove": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/misc/username_logger": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "handcrafted:recipes/misc/blackstone_pillar_trim": {"criteria": {"has_blackstone_pillar_trim": "2025-07-10 16:13:30 +0800"}, "done": true}, "utilitix:recipes/misc/dimmable_redstone_lamp": {"criteria": {"criterion1": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/brown_mushroom_from_fish": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/ultramarine_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "aquaculture:recipes/misc/neptinium_ingot_from_blasting": {"criteria": {"has_leggings": "2025-07-10 16:13:30 +0800", "has_axe": "2025-07-10 16:13:30 +0800", "has_helmet": "2025-07-10 16:13:30 +0800", "has_fishing_rod": "2025-07-10 16:13:30 +0800", "has_bow": "2025-07-10 16:13:30 +0800", "has_chestplate": "2025-07-10 16:13:30 +0800", "has_sword": "2025-07-10 16:13:30 +0800", "has_fillet_knife": "2025-07-10 16:13:30 +0800", "has_shovel": "2025-07-10 16:13:30 +0800", "has_hoe": "2025-07-10 16:13:30 +0800", "has_boots": "2025-07-10 16:13:30 +0800", "has_pickaxe": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/decorations/dark_oak_fish_mount": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/food/sushi": {"criteria": {"has_alt_items": "2025-07-10 16:13:30 +0800", "has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbridges:recipes/blackstone_bridge_pier": {"criteria": {"has_planks": "2025-07-10 16:13:30 +0800"}, "done": true}, "tombstone:adventure/root": {"criteria": {"auto": "2025-07-10 16:10:58 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/worm_farm": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_hellbark_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_key_changer": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwpaths:recipes/blackstone": {"criteria": {"has_wood": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/decorations/polished_blackstone_brick_wall_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/blackstone_1x": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/note_hook": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/redstone/laser_block": {"criteria": {"has_stone": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/gold_hook": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/decorations/spruce_fish_mount": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/maroon_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "bigreactors:recipes/misc/blasting/graphite_from_coalblock": {"criteria": {"has_item": "2025-07-10 16:14:10 +0800"}, "done": true}, "biomesoplenty:recipes/decorations/blackstone_bulb": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_willow_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_cutting_knife": {"criteria": {"has_certus_quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwfences:recipes/deepslate": {"criteria": {"has_deepslate": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_umbran_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/misc/claymore": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/from_crimson_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:adventure/adventuring_time": {"criteria": {"minecraft:beach": "2025-07-10 16:10:58 +0800", "minecraft:deep_lukewarm_ocean": "2025-07-10 16:13:12 +0800"}, "done": false}, "minecraft:recipes/tools/stone_hoe": {"criteria": {"has_cobblestone": "2025-07-10 16:13:30 +0800"}, "done": true}, "alchemistry:recipes/dissolver/coal": {"criteria": {"has_the_recipe": "2025-07-10 16:14:10 +0800"}, "done": true}, "railcraft:recipes/misc/radio_circuit": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_neptunium_block": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/crimson": {"criteria": {"has_crimson": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_access": {"criteria": {"has_wit": "2025-07-10 16:13:30 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/basic/passivetap_fe": {"criteria": {"has_item2": "2025-07-10 18:24:35 +0800"}, "done": true}, "naturalist:recipes/food/cooked_egg": {"criteria": {"has_eggs": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_brick_stairs_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/palm_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/from_dark_oak_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "delightful:recipes/knives/certus_quartz_knife": {"criteria": {"has_gems/certus_quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/maple_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_mahogany_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/light_hook": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/redstone/lever": {"criteria": {"has_cobblestone": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl1": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl3": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_reinforcer_lvl2": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "supplementaries:recipes/checker": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/cypress_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "twigs:recipes/columns/blackstone_column_stonecutting": {"criteria": {"has_item": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/fishing_line": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "supplementaries:recipes/altimeter": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "occultism:recipes/crushing/crushing/certus_quartz_dust_from_gem": {"criteria": {"has_certus_quartz_gem": "2025-07-10 18:24:35 +0800"}, "done": true}, "minecraft:recipes/redstone/dropper": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "minecraft:recipes/decorations/blackstone_wall_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/tools/universal_block_modifier": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "nethersdelight:recipes/decorations/blackstone_furnace": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/misc/cage_trap": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "minecraft:recipes/redstone/piston": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwfences:recipes/quartz": {"criteria": {"has_qq": "2025-07-10 16:13:30 +0800"}, "done": true}, "railcraft:recipes/misc/signal_circuit": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "immersiveengineering:recipes/misc/crafting/redstone_acid": {"criteria": {"has_redstone_dust": "2025-07-10 18:24:35 +0800"}, "done": true}, "repurposed_structures:root": {"criteria": {"always": "2025-07-10 16:10:57 +0800"}, "done": true}, "ae2:recipes/misc/misc/tiny_tnt": {"criteria": {"has_dusts/quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_palm_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-10 16:13:30 +0800", "has_fillet_knife": "2025-07-10 16:13:30 +0800"}, "done": true}, "supplementaries:recipes/blackstone_lamp": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_bricks_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_note_block": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/pulse_repeater": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_sword": {"criteria": {"has_certus_quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/bonemeal_from_fish_bones": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/flowering_oak_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/redstone/target": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/lavender_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "minecraft:recipes/tools/compass": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/tools/stone_pickaxe": {"criteria": {"has_cobblestone": "2025-07-10 16:13:30 +0800"}, "done": true}, "dungeons_arise:wda_root": {"criteria": {"WDA": "2025-07-10 16:10:57 +0800"}, "done": true}, "mcwfences:recipes/spruce": {"criteria": {"has_spruce": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/diamond_hook": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2wtlib:recipes/quantum_bridge_card": {"criteria": {"has_wit": "2025-07-10 16:13:30 +0800", "has_wpt": "2025-07-10 16:13:30 +0800", "has_wct": "2025-07-10 16:13:30 +0800"}, "done": true}, "modularrouters:recipes/misc/blank_module": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_campfire": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/snowblossom_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "allthecompressed:recipes/building_blocks/compress/coal_block_1x": {"criteria": {"has_coal_block": "2025-07-10 16:14:10 +0800"}, "done": true}, "handcrafted:recipes/misc/blackstone_corner_trim": {"criteria": {"has_blackstone_corner_trim": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwwindows:recipes/blackstone": {"criteria": {"has_wood": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/wine_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/mud_brick": {"criteria": {"has_qq": "2025-07-10 16:13:30 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/reinforced/passivetap_fe": {"criteria": {"has_item2": "2025-07-10 18:24:35 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_redstone_lamp": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwbridges:recipes/blackstone_bridge": {"criteria": {"has_planks": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-10 16:13:30 +0800", "has_fillet_knife": "2025-07-10 16:13:30 +0800"}, "done": true}, "energymeter:recipes/energymeter/meter": {"criteria": {"has_item": "2025-07-10 16:13:30 +0800"}, "done": true}, "alchemistry:recipes/dissolver/blackstone_slab": {"criteria": {"has_the_recipe": "2025-07-10 16:13:30 +0800"}, "done": true}, "railcraft:grant_book_on_first_join": {"criteria": {"tick": "2025-07-10 16:10:57 +0800"}, "done": true}, "aether:recipes/decorations/skyroot_chest": {"criteria": {"has_lots_of_items": "2025-07-10 16:13:32 +0800"}, "done": true}, "railcraft:recipes/misc/controller_circuit": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/neptunium_ingot_from_nuggets": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "occultism:occultism/root": {"criteria": {"occultism_present": "2025-07-10 16:10:57 +0800"}, "done": true}, "aquaculture:recipes/decorations/jungle_fish_mount": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "biomesoplenty:recipes/decorations/blackstone_spines_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "aether:recipes/redstone/skyroot_piston": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "minecraft:recipes/decorations/crafting_table": {"criteria": {"unlock_right_away": "2025-07-10 16:10:57 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/reactor/reinforced/passivetap_fe": {"criteria": {"has_item2": "2025-07-10 18:24:35 +0800"}, "done": true}, "irons_spellbooks:grant_patchouli": {"criteria": {"tick": "2025-07-10 16:10:57 +0800"}, "done": true}, "utilitix:recipes/misc/directional_highspeed_rail": {"criteria": {"criterion2": "2025-07-10 18:24:35 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_crafting": {"criteria": {"has_wct": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_brick_slab_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "supplementaries:recipes/turn_table": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "botania:recipes/misc/redstone_root": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "additionallanterns:recipes/misc/blackstone_chain": {"criteria": {"recipe_condition": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/stone": {"criteria": {"has_stone": "2025-07-10 16:13:30 +0800"}, "done": true}, "utilitix:recipes/misc/directional_rail": {"criteria": {"criterion2": "2025-07-10 18:24:35 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_piston": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/amber_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/double_hook": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/powered_latch": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwfences:recipes/hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/redstone/note_block": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "supplementaries:recipes/blackboard": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/iron_hook": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "travelersbackpack:recipes/misc/redstone": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/jellyfish_to_slimeball": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/persimmon_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/cherenkov_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "ae2:recipes/misc/blasting/silicon_from_certus_quartz_dust": {"criteria": {"has_certus_quartz_dust": "2025-07-10 18:24:35 +0800"}, "done": true}, "minecraft:recipes/decorations/furnace": {"criteria": {"has_cobblestone": "2025-07-10 16:13:30 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/pulse_extender": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_redwood_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "tombstone:adventure/first_knowledge": {"criteria": {"coded_trigger": "2025-07-10 17:28:03 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/fluid_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_slab_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/mint_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/upgrade_pattern_encoding": {"criteria": {"has_wpt": "2025-07-10 16:13:30 +0800"}, "done": true}, "quarryplus:recipes/workbench": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "corail_woodcutter:recipes/from_bamboo_mosaic": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "bigreactors:recipes/misc/smelting/graphite_from_coalblock": {"criteria": {"has_item": "2025-07-10 16:14:10 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/rainbow_birch_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/tools/taser": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/building_blocks/planks_from_driftwood": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/tools/stone_shovel": {"criteria": {"has_cobblestone": "2025-07-10 16:13:30 +0800"}, "done": true}, "littlelogistics:recipes/tools/tug_route": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "alchemistry:recipes/dissolver/crafting_table": {"criteria": {"has_the_recipe": "2025-07-10 16:10:57 +0800"}, "done": true}, "corail_woodcutter:recipes/from_cherry_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/yellow_maple_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/bubblegum_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_wrench": {"criteria": {"has_certus_quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "rftoolsutility:recipes/rftoolsutility/screen_link": {"criteria": {"redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "bigreactors:recipes/building_blocks/turbine/basic/passivetap_fe": {"criteria": {"has_item2": "2025-07-10 18:24:35 +0800"}, "done": true}, "rftoolsstorage:recipes/rftoolsstorage/storage_module0": {"criteria": {"redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget_from_blasting": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "biomesoplenty:recipes/decorations/blackstone_spines": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "totw_modded:root": {"criteria": {"sample_trigger": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/redstone_hook": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/mahogany_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/bobber": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_dead_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/food/cooked_fish_fillet_from_smoking": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "farmersdelight:main/root": {"criteria": {"seeds": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/conifer_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/dead_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/aquaculture/heavy_hook": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/dark_oak": {"criteria": {"has_dark_oak": "2025-07-10 16:13:30 +0800"}, "done": true}, "create:root": {"criteria": {"0": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/decorations/blackstone_wall": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/metal": {"criteria": {"has_deepslate": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/from_acacia_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "utilitix:recipes/misc/weak_redstone_torch": {"criteria": {"criterion0": "2025-07-10 18:24:35 +0800"}, "done": true}, "ae2:recipes/misc/network/parts/quartz_fiber_part": {"criteria": {"has_dusts/quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "ae2:recipes/misc/network/cables/smart_fluix": {"criteria": {"has_dusts/redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "securitycraft:recipes/misc/portable_tune_player": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "corail_woodcutter:recipes/from_jungle_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/magic_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/willow_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2:recipes/misc/network/cells/item_cell_housing": {"criteria": {"has_dusts/redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_pattern_access_terminal": {"criteria": {"has_wt": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/decorations/acacia_fish_mount": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_magic_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/orange_maple_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/jacaranda_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_axe": {"criteria": {"has_certus_quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "securitycraft:recipes/redstone/alarm": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/pine_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "theurgy:book_root": {"criteria": {"theurgy_present": "2025-07-10 16:10:57 +0800"}, "done": true}, "mcwfences:recipes/sandstone": {"criteria": {"has_sand": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/acacia": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/oak": {"criteria": {"has_oak": "2025-07-10 16:13:30 +0800"}, "done": true}, "utilitix:recipes/misc/highspeed_rail": {"criteria": {"criterion2": "2025-07-10 18:24:35 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/aquamarine_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "ae2:recipes/misc/decorative/quartz_glass": {"criteria": {"has_quartz_dust": "2025-07-10 18:24:35 +0800"}, "done": true}, "supplementaries:recipes/sconce_lever": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "corail_woodcutter:recipes/from_warped_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_jacaranda_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/misc/keypad_frame": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "utilitix:recipes/misc/linked_repeater": {"criteria": {"criterion0": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/tools/diamond_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/icy_blue_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/spring_green_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "aquaculture:recipes/misc/tin_can_to_iron_nugget": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/umbran_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2wtlib:recipes/magnet_card": {"criteria": {"has_wct": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_torch": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "supplementaries:recipes/lock_block": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_smelting": {"criteria": {"has_fishing_rod": "2025-07-10 16:13:30 +0800", "has_fillet_knife": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/tools/golden_fishing_rod": {"criteria": {"has_vanilla_rod": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/peach_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "minecraft:recipes/building_blocks/polished_blackstone_stairs_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/nether": {"criteria": {"has_nether_brick": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/redwood_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/hellbark_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "maidensmerrymaking:easter/eggstraordinaire": {"criteria": {"striped_eggs": "2025-07-10 16:10:57 +0800"}, "done": false}, "railcraft:recipes/misc/receiver_circuit": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwfences:recipes/bamboo": {"criteria": {"has_bamboo": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2:recipes/misc/network/blocks/energy_energy_cell": {"criteria": {"has_crystals/certus": "2025-07-10 18:24:35 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ce": {"criteria": {"has_wpt": "2025-07-10 16:13:30 +0800", "has_wct": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ca": {"criteria": {"has_wit": "2025-07-10 16:13:30 +0800", "has_wct": "2025-07-10 16:13:30 +0800"}, "done": true}, "ae2wtlib:recipes/wireless_universal_terminal/ae": {"criteria": {"has_wit": "2025-07-10 16:13:30 +0800", "has_wpt": "2025-07-10 16:13:30 +0800"}, "done": true}, "securitycraft:recipes/redstone/portable_radar": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "supplementaries:recipes/redstone_illuminator": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blackstone_slab": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "railcraft:recipes/misc/signal_tuner": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "minecraft:adventure/root": {"criteria": {"killed_by_something": "2025-07-10 17:28:03 +0800"}, "done": true}, "minecraft:recipes/redstone/redstone_block": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "ae2:recipes/misc/tools/certus_quartz_hoe": {"criteria": {"has_certus_quartz": "2025-07-10 18:24:35 +0800"}, "done": true}, "securitycraft:recipes/redstone/reinforced_dropper": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/red_mushroom_from_fish": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwfences:recipes/end": {"criteria": {"has_end_brick": "2025-07-10 16:13:30 +0800"}, "done": true}, "dyenamics:recipes/misc/bed/rose_bed_frm_white_bed": {"criteria": {"has_white_bed": "2025-07-10 17:26:57 +0800"}, "done": true}, "minecraft:recipes/tools/clock": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "railcraft:recipes/misc/signal_block_surveyor": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "ae2:recipes/misc/smelting/silicon_from_certus_quartz_dust": {"criteria": {"has_certus_quartz_dust": "2025-07-10 18:24:35 +0800"}, "done": true}, "lootr:root": {"criteria": {"always_true": "2025-07-10 16:10:57 +0800"}, "done": true}, "mcwfences:recipes/blackstone": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "cataclysm:root": {"criteria": {"custom_test_name": "2025-07-10 16:10:57 +0800"}, "done": true}, "minecraft:recipes/decorations/polished_blackstone_wall_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/chiseled_polished_blackstone_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "minecraft:recipes/building_blocks/blackstone_slab_from_blackstone_stonecutting": {"criteria": {"has_blackstone": "2025-07-10 16:13:30 +0800"}, "done": true}, "utilitarian:recipes/misc/redstone_clock": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "computercraft:recipes/redstone/computer_normal": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "rftoolscontrol:recipes/rftoolscontrol/card_base": {"criteria": {"redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "aquaculture:recipes/misc/gold_nugget_from_gold_fish": {"criteria": {"has_items": "2025-07-10 16:13:30 +0800"}, "done": true}, "create:recipes/misc/crafting/logistics/powered_toggle_latch": {"criteria": {"has_item": "2025-07-10 18:24:35 +0800"}, "done": true}, "securitycraft:recipes/redstone/panic_button": {"criteria": {"has_redstone": "2025-07-10 18:24:35 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/empyreal_fences": {"criteria": {"has_acacia": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/biomesoplenty/from_fir_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "aquaculture:recipes/misc/iron_nugget_from_blasting": {"criteria": {"has_fishing_rod": "2025-07-10 16:13:30 +0800", "has_fillet_knife": "2025-07-10 16:13:30 +0800"}, "done": true}, "corail_woodcutter:recipes/from_spruce_planks": {"criteria": {"has_ingredient": "2025-07-10 16:13:30 +0800"}, "done": true}, "mcwbiomesoplenty:recipes/fir_hedge": {"criteria": {"has_birch": "2025-07-10 16:13:30 +0800"}, "done": true}, "DataVersion": 3465}