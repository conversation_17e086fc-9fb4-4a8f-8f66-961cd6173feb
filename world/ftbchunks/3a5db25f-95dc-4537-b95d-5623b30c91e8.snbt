{
	max_claim_chunks: 200
	max_force_load_chunks: 5
	last_login_time: 1752336982410L
	chunks: {
		"minecraft:overworld": [
			{ x: 18, z: 24, time: 1752253065530L }
			{ x: 21, z: 23, time: 1752220010805L }
			{ x: 44, z: 20, time: 1752120257111L }
			{ x: 19, z: 25, time: 1752216568473L }
			{ x: 45, z: 19, time: 1752120257111L }
			{ x: 22, z: 24, time: 1752061329685L }
			{ x: 23, z: 25, time: 1752253064083L }
			{ x: 22, z: 20, time: 1752061329684L }
			{ x: 21, z: 21, time: 1752061329685L }
			{ x: 20, z: 22, time: 1752061329684L, force_loaded: 1752066981816L }
			{ x: 18, z: 20, time: 1752253061758L }
			{ x: 22, z: 23, time: 1752061329684L }
			{ x: 20, z: 25, time: 1752312550498L }
			{ x: 19, z: 20, time: 1752228718726L }
			{ x: 21, z: 24, time: 1752061329685L }
			{ x: 18, z: 23, time: 1752253063170L }
			{ x: 21, z: 22, time: 1752166511889L }
			{ x: 23, z: 20, time: 1752061329685L }
			{ x: 20, z: 21, time: 1752061329684L }
			{ x: 23, z: 23, time: 1752061329684L }
			{ x: 20, z: 24, time: 1752228712832L, force_loaded: 1752253075954L }
			{ x: 18, z: 22, time: 1752312558633L, force_loaded: 1752312559468L }
			{ x: 19, z: 21, time: 1752061329684L }
			{ x: 21, z: 25, time: 1752222562893L }
			{ x: 19, z: 23, time: 1752061329684L, force_loaded: 1752166507984L }
			{ x: 22, z: 22, time: 1752061329684L }
			{ x: 23, z: 21, time: 1752061329685L }
			{ x: 20, z: 20, time: 1752061329684L }
			{ x: 18, z: 25, time: 1752216568473L }
			{ x: 44, z: 19, time: 1752120257111L }
			{ x: 45, z: 20, time: 1752120257111L }
			{ x: 19, z: 24, time: 1752061329684L }
			{ x: 23, z: 24, time: 1752061329684L }
			{ x: 20, z: 23, time: 1752061329685L }
			{ x: 22, z: 25, time: 1752253064083L }
			{ x: 23, z: 22, time: 1752061329684L }
			{ x: 22, z: 21, time: 1752061329685L }
			{ x: 21, z: 20, time: 1752061329685L }
			{ x: 19, z: 22, time: 1752061329685L }
			{ x: 18, z: 21, time: 1752253061758L }
		]
		"allthemodium:mining": [
			{ x: 23, z: 21, time: 1752071992460L }
			{ x: 25, z: 19, time: 1752071992459L }
			{ x: 21, z: 19, time: 1752071992458L }
			{ x: 24, z: 20, time: 1752071992458L }
			{ x: 22, z: 22, time: 1752071992458L }
			{ x: 17, z: 25, time: 1752071992459L }
			{ x: 21, z: 24, time: 1752071992458L }
			{ x: 25, z: 24, time: 1752071992459L }
			{ x: 24, z: 25, time: 1752071992459L }
			{ x: 23, z: 26, time: 1752071992459L }
			{ x: 16, z: 24, time: 1752071992459L }
			{ x: 18, z: 26, time: 1752071992459L }
			{ x: 19, z: 27, time: 1752071992460L }
			{ x: 22, z: 27, time: 1752071992458L }
			{ x: 19, z: 18, time: 1752071992458L }
			{ x: 18, z: 23, time: 1752071992458L }
			{ x: 20, z: 24, time: 1752071992459L }
			{ x: 24, z: 18, time: 1752071992458L }
			{ x: 23, z: 23, time: 1752071992458L }
			{ x: 18, z: 19, time: 1752071992459L }
			{ x: 17, z: 22, time: 1752071992459L }
			{ x: 25, z: 20, time: 1752071992459L }
			{ x: 24, z: 19, time: 1752071992459L }
			{ x: 19, z: 20, time: 1752071992459L }
			{ x: 16, z: 21, time: 1752071992460L }
			{ x: 20, z: 21, time: 1752071992459L }
			{ x: 21, z: 20, time: 1752071992458L }
			{ x: 22, z: 21, time: 1752071992458L }
			{ x: 23, z: 22, time: 1752071992459L }
			{ x: 25, z: 27, time: 1752071992459L }
			{ x: 17, z: 26, time: 1752071992459L }
			{ x: 22, z: 24, time: 1752071992458L }
			{ x: 24, z: 26, time: 1752071992459L }
			{ x: 22, z: 18, time: 1752071992459L }
			{ x: 19, z: 24, time: 1752071992459L }
			{ x: 20, z: 27, time: 1752071992459L }
			{ x: 23, z: 25, time: 1752071992459L }
			{ x: 16, z: 27, time: 1752071992459L }
			{ x: 24, z: 23, time: 1752071992458L }
			{ x: 18, z: 25, time: 1752071992460L }
			{ x: 21, z: 27, time: 1752071992458L }
			{ x: 23, z: 18, time: 1752071992458L }
			{ x: 17, z: 23, time: 1752071992458L }
			{ x: 18, z: 18, time: 1752071992458L }
			{ x: 20, z: 22, time: 1752071992459L }
			{ x: 16, z: 22, time: 1752071992459L }
			{ x: 25, z: 21, time: 1752071992459L }
			{ x: 23, z: 19, time: 1752071992459L }
			{ x: 18, z: 20, time: 1752071992459L }
			{ x: 17, z: 21, time: 1752071992460L }
			{ x: 19, z: 19, time: 1752071992459L }
			{ x: 22, z: 20, time: 1752071992458L }
			{ x: 21, z: 21, time: 1752071992458L }
			{ x: 24, z: 22, time: 1752071992460L }
			{ x: 23, z: 24, time: 1752071992459L }
			{ x: 20, z: 23, time: 1752071992458L }
			{ x: 25, z: 18, time: 1752155066626L }
			{ x: 16, z: 26, time: 1752071992459L }
			{ x: 20, z: 26, time: 1752071992459L }
			{ x: 21, z: 18, time: 1752071992459L }
			{ x: 24, z: 27, time: 1752071992459L }
			{ x: 18, z: 24, time: 1752071992460L }
			{ x: 25, z: 26, time: 1752071992459L }
			{ x: 17, z: 27, time: 1752071992459L }
			{ x: 16, z: 19, time: 1752071992460L }
			{ x: 16, z: 23, time: 1752071992458L }
			{ x: 22, z: 25, time: 1752071992458L }
			{ x: 21, z: 26, time: 1752071992458L }
			{ x: 25, z: 23, time: 1752071992458L }
			{ x: 19, z: 25, time: 1752071992458L }
			{ x: 21, z: 23, time: 1752071992459L }
			{ x: 20, z: 19, time: 1752071992459L }
			{ x: 25, z: 22, time: 1752071992459L }
			{ x: 18, z: 21, time: 1752071992459L }
			{ x: 17, z: 20, time: 1752071992459L }
			{ x: 19, z: 22, time: 1752071992459L }
			{ x: 17, z: 18, time: 1752071992458L }
			{ x: 127, z: 22, time: 1752155051551L, force_loaded: 1752155052348L }
			{ x: 23, z: 20, time: 1752071992460L }
			{ x: 22, z: 19, time: 1752071992458L }
			{ x: 21, z: 22, time: 1752071992458L }
			{ x: 24, z: 21, time: 1752071992459L }
			{ x: 23, z: 27, time: 1752071992459L }
			{ x: 16, z: 25, time: 1752071992459L }
			{ x: 20, z: 25, time: 1752071992459L }
			{ x: 17, z: 24, time: 1752071992459L }
			{ x: 25, z: 25, time: 1752071992459L }
			{ x: 24, z: 24, time: 1752071992459L }
			{ x: 17, z: 19, time: 1752071992460L }
			{ x: 19, z: 23, time: 1752071992458L }
			{ x: 21, z: 25, time: 1752071992458L }
			{ x: 22, z: 26, time: 1752071992458L }
			{ x: 20, z: 18, time: 1752071992458L }
			{ x: 22, z: 23, time: 1752071992458L }
			{ x: 18, z: 27, time: 1752071992459L }
			{ x: 19, z: 26, time: 1752071992459L }
			{ x: 19, z: 21, time: 1752071992459L }
			{ x: 18, z: 22, time: 1752071992459L }
			{ x: 16, z: 20, time: 1752071992459L }
			{ x: 20, z: 20, time: 1752071992459L }
			{ x: 16, z: 18, time: 1752071992458L }
		]
		"minecraft:the_end": [{ x: 2, z: 4, time: 1752229226825L }]
	}
	member_data: {
		a7b882ca-4b53-4f77-8644-424ed38d2d3c: {
			max_force_loaded_chunks: 5
			max_claimed_chunks: 200
			offline_force_loader: 0b
		}
		82acaaf0-960a-42b2-9f68-9f3f43225304: {
			original_claims: {
				"minecraft:overworld": [
					{ x: 21, z: 22 }
					{ x: 22, z: 22 }
					{ x: 23, z: 23 }
					{ x: 23, z: 20 }
					{ x: 23, z: 21 }
					{ x: 23, z: 22 }
					{ x: 22, z: 20 }
					{ x: 21, z: 21 }
					{ x: 20, z: 24 }
					{ x: 21, z: 23 }
					{ x: 44, z: 20 }
					{ x: 22, z: 23 }
					{ x: 45, z: 19 }
					{ x: 22, z: 21 }
					{ x: 21, z: 20 }
					{ x: 44, z: 19 }
					{ x: 45, z: 20 }
					{ x: 19, z: 24 }
					{ x: 19, z: 21 }
					{ x: 19, z: 20 }
					{ x: 20, z: 21 }
					{ x: 20, z: 20 }
					{ x: 22, z: 24 }
					{ x: 21, z: 24 }
					{ x: 23, z: 24 }
					{ x: 20, z: 22 }
					{ x: 20, z: 23 }
					{ x: 19, z: 23 }
					{ x: 19, z: 22 }
				]
				"allthemodium:mining": [
					{ x: 24, z: 23 }
					{ x: 18, z: 26 }
					{ x: 22, z: 20 }
					{ x: 21, z: 21 }
					{ x: 19, z: 27 }
					{ x: 23, z: 21 }
					{ x: 24, z: 22 }
					{ x: 16, z: 19 }
					{ x: 21, z: 20 }
					{ x: 23, z: 20 }
					{ x: 18, z: 25 }
					{ x: 17, z: 19 }
					{ x: 16, z: 23 }
					{ x: 21, z: 27 }
					{ x: 22, z: 27 }
					{ x: 23, z: 18 }
					{ x: 19, z: 18 }
					{ x: 22, z: 21 }
					{ x: 18, z: 23 }
					{ x: 17, z: 23 }
					{ x: 22, z: 25 }
					{ x: 21, z: 26 }
					{ x: 19, z: 23 }
					{ x: 20, z: 24 }
					{ x: 25, z: 19 }
					{ x: 21, z: 19 }
					{ x: 25, z: 23 }
					{ x: 23, z: 22 }
					{ x: 21, z: 25 }
					{ x: 22, z: 26 }
					{ x: 20, z: 18 }
					{ x: 22, z: 19 }
					{ x: 19, z: 25 }
					{ x: 21, z: 22 }
					{ x: 24, z: 20 }
					{ x: 18, z: 18 }
					{ x: 24, z: 18 }
					{ x: 22, z: 22 }
					{ x: 21, z: 23 }
					{ x: 24, z: 21 }
					{ x: 23, z: 23 }
					{ x: 22, z: 23 }
					{ x: 18, z: 27 }
					{ x: 19, z: 26 }
					{ x: 25, z: 27 }
					{ x: 20, z: 22 }
					{ x: 23, z: 24 }
					{ x: 17, z: 25 }
					{ x: 20, z: 23 }
					{ x: 23, z: 27 }
					{ x: 21, z: 24 }
					{ x: 16, z: 25 }
					{ x: 25, z: 24 }
					{ x: 17, z: 26 }
					{ x: 18, z: 19 }
					{ x: 25, z: 18 }
					{ x: 22, z: 24 }
					{ x: 16, z: 26 }
					{ x: 20, z: 26 }
					{ x: 17, z: 22 }
					{ x: 25, z: 20 }
					{ x: 20, z: 25 }
					{ x: 16, z: 22 }
					{ x: 21, z: 18 }
					{ x: 24, z: 26 }
					{ x: 22, z: 18 }
					{ x: 19, z: 21 }
					{ x: 24, z: 19 }
					{ x: 19, z: 24 }
					{ x: 20, z: 19 }
					{ x: 19, z: 20 }
					{ x: 25, z: 21 }
					{ x: 18, z: 22 }
					{ x: 24, z: 25 }
					{ x: 25, z: 22 }
					{ x: 20, z: 27 }
					{ x: 23, z: 19 }
					{ x: 18, z: 21 }
					{ x: 17, z: 20 }
					{ x: 24, z: 27 }
					{ x: 19, z: 22 }
					{ x: 23, z: 26 }
					{ x: 16, z: 20 }
					{ x: 16, z: 24 }
					{ x: 18, z: 20 }
					{ x: 18, z: 24 }
					{ x: 16, z: 21 }
					{ x: 23, z: 25 }
					{ x: 17, z: 24 }
					{ x: 25, z: 26 }
					{ x: 20, z: 20 }
					{ x: 17, z: 27 }
					{ x: 17, z: 21 }
					{ x: 25, z: 25 }
					{ x: 17, z: 18 }
					{ x: 24, z: 24 }
					{ x: 19, z: 19 }
					{ x: 16, z: 18 }
					{ x: 20, z: 21 }
					{ x: 16, z: 27 }
				]
			}
			max_force_loaded_chunks: 5
			max_claimed_chunks: 200
			offline_force_loader: 0b
		}
	}
}
