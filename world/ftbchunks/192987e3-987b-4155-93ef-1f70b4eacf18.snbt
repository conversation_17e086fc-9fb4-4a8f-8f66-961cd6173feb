{
	max_claim_chunks: 200
	max_force_load_chunks: 5
	last_login_time: 1752399116609L
	chunks: {
		"minecraft:overworld": [
			{ x: -15, z: -74, time: 1752080559190L }
			{ x: -16, z: -75, time: 1752080559190L }
			{ x: -15, z: -75, time: 1752080559190L }
			{ x: -16, z: -74, time: 1752080559190L }
		]
		"allthemodium:mining": [
			{ x: -15, z: -76, time: 1752086421880L }
			{ x: -14, z: -75, time: 1752086421862L }
			{ x: -17, z: -73, time: 1752086418537L }
			{ x: -15, z: -75, time: 1752086418537L, force_loaded: 1752126921402L }
			{ x: -16, z: -74, time: 1752086418536L }
			{ x: -18, z: -76, time: 1752086421862L }
			{ x: -14, z: -76, time: 1752086421879L }
			{ x: -14, z: -72, time: 1752086421863L }
			{ x: -17, z: -74, time: 1752086418537L }
			{ x: -18, z: -75, time: 1752086421879L }
			{ x: -16, z: -73, time: 1752086418537L, force_loaded: 1752167547926L }
			{ x: -15, z: -72, time: 1752086421862L }
			{ x: -18, z: -73, time: 1752086421880L }
			{ x: -17, z: -76, time: 1752086421863L }
			{ x: -14, z: -73, time: 1752086421862L }
			{ x: -17, z: -75, time: 1752086418537L }
			{ x: -18, z: -74, time: 1752086421862L }
			{ x: -16, z: -72, time: 1752086421880L }
			{ x: -15, z: -73, time: 1752086418537L }
			{ x: -16, z: -76, time: 1752086421862L }
			{ x: -18, z: -72, time: 1752086421880L }
			{ x: -14, z: -74, time: 1752086421879L }
			{ x: -19, z: -74, time: 1752137092385L, force_loaded: 1752137093345L }
			{ x: -17, z: -72, time: 1752086421879L }
			{ x: -23, z: -74, time: 1752166981032L, force_loaded: 1752166981449L }
			{ x: -15, z: -74, time: 1752086418537L }
			{ x: -16, z: -75, time: 1752086418537L }
		]
		"minecraft:the_end": [{ x: 23, z: 21, time: 1752212002723L }]
	}
	member_data: {
		93fb9161-69c5-457f-b220-f68895d9da62: {
			original_claims: {
				"minecraft:overworld": [
					{ x: -15, z: -74 }
					{ x: -16, z: -75 }
					{ x: -16, z: -74 }
					{ x: -15, z: -75 }
				]
				"allthemodium:mining": [
					{ x: -17, z: -75 }
					{ x: -18, z: -76 }
					{ x: -16, z: -76 }
					{ x: -14, z: -75 }
					{ x: -18, z: -74 }
					{ x: -15, z: -75 }
					{ x: -14, z: -74 }
					{ x: -16, z: -73 }
					{ x: -18, z: -72 }
					{ x: -18, z: -73 }
					{ x: -16, z: -74 }
					{ x: -16, z: -72 }
					{ x: -15, z: -72 }
					{ x: -15, z: -73 }
					{ x: -17, z: -76 }
					{ x: -14, z: -73 }
					{ x: -14, z: -72 }
					{ x: -17, z: -74 }
					{ x: -17, z: -73 }
					{ x: -14, z: -76 }
					{ x: -15, z: -74 }
					{ x: -16, z: -75 }
					{ x: -17, z: -72 }
					{ x: -15, z: -76 }
					{ x: -18, z: -75 }
				]
			}
			max_force_loaded_chunks: 5
			max_claimed_chunks: 200
			offline_force_loader: 0b
		}
	}
}
