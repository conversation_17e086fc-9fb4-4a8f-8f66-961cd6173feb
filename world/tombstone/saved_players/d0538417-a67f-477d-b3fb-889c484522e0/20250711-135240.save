{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:8,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:jump_boost"}],Air:300s,Attributes:[{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"attributeslib:current_hp_damage"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:4.0d,Modifiers:[{Amount:0.4d,Name:"artifacts:feral_claws_attack_speed_bonus",Operation:0,UUID:[I;-1626820019,-1493154539,-1848454353,1821068796]}],Name:"minecraft:generic.attack_speed"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.05d,Name:"attributeslib:crit_chance"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.5d,Name:"attributeslib:crit_damage"},{Base:20.0d,Modifiers:[{Amount:8.0d,Name:"Heart Containers",Operation:0,UUID:[I;-**********,-990297069,-**********,-847674717]},{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:1.0d,Name:"irons_spellbooks:summon_damage"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-**********,870076243,-**********,-**********],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:antidote_vessel"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"simplemagnets:basicmagnet",tag:{active:1b}}],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:diamond_gloves",tag:{Damage:15,Enchantments:[{id:"ensorcellation:soulbound",lvl:1s},{id:"minecraft:unbreaking",lvl:4s},{id:"enderio:xp_boost",lvl:4s},{id:"evilcraft:unusing",lvl:1s}]}},{Count:1b,Slot:1,id:"aether:diamond_gloves",tag:{Damage:0,Enchantments:[{id:"minecraft:unbreaking",lvl:4s},{id:"naturesaura:aura_mending",lvl:1s}]}}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:villager_hat"}],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:cross_necklace",tag:{CanApplyBonus:1b}}],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:gold_backpack",tag:{contentsUuid:[I;872707142,-1026079264,-1418764731,-1751640986],inventorySlots:81,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}]},upgradeSlots:3}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:pickaxe_heater"},{Count:1b,Slot:1,id:"artifacts:feral_claws"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:bunny_hoppers"}],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"crafting_on_a_stick:crafting_table"}],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:golden_gloves",tag:{Damage:0,Enchantments:[{id:"minecraft:vanishing_curse",lvl:1s}]}}],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:3,furnaces:{furnace0:{X:-1304,Y:253,Z:717},furnace1:{X:-1304,Y:253,Z:719},furnace2:{X:-1304,Y:253,Z:720}}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:125,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:352061},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:62},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:4,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"terralith:piston_alt",ItemStack:{Count:1b,id:"minecraft:piston"}}],SelectedRecipe:"terralith:piston_alt"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:20,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:cooked_porkchop","minecraft:carrot","minecraft:cooked_beef","maidensmerrymaking:chocolate_bunny","farmersdelight:tomato","minecraft:melon_slice","minecraft:chicken","minecraft:bread"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:20s,knowledge:6,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:-45079976333259L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-355142252875713L,UID:[I;-1384169498,-*********,-1582474015,-*********]},{FromDim:"minecraft:overworld",FromPos:54150877880291L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-29137058447294L,UID:[I;*********,1533297299,-1495035988,1977290092]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["dc0db45e-8dc6-4050-92fe-e768c66fb343","c37b8562-06a0-488e-8d3a-2033a4c05f9a"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:-1592,tb_last_ground_location_y:87,tb_last_ground_location_z:106,tb_last_offhand_item:"minecraft:torch",twilightforest_banished:1b},apoth_reforge_seed:0,"quark:locked_once":1b,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedStorageSettings:{}},Health:28.0f,HurtByTimestamp:329390,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"allthemodium:allthemodium_pickaxe",tag:{Enchantments:[{id:"minecraft:fortune",lvl:8s}],RepairCost:15}},{Count:23b,Slot:1b,id:"minecraft:bread"},{Count:1b,Slot:2b,id:"minecraft:diamond_pickaxe",tag:{Damage:19,Enchantments:[{id:"enderio:shimmer",lvl:1s}]}},{Count:1b,Slot:3b,id:"sophisticatedbackpacks:iron_backpack",tag:{contentsUuid:[I;**********,**********,-**********,-**********],inventorySlots:54,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}]},upgradeSlots:2}},{Count:64b,Slot:4b,id:"minecraft:sand"},{Count:1b,Slot:5b,id:"ironfurnaces:netherite_furnace",tag:{BlockEntityTag:{Augment:[I;0,0,0],Jovial:0}}},{Count:3b,Slot:6b,id:"minecraft:paper"},{Count:64b,Slot:7b,id:"alltheores:osmium_ingot"},{Count:25b,Slot:8b,id:"minecraft:coal"},{Count:54b,Slot:9b,id:"minecraft:iron_ingot"},{Count:31b,Slot:10b,id:"alltheores:iron_dust"},{Count:1b,Slot:11b,id:"alltheores:iron_ore_hammer",tag:{Damage:32}},{Count:64b,Slot:12b,id:"minecraft:gold_ingot"},{Count:41b,Slot:13b,id:"mekanism:alloy_infused"},{Count:1b,Slot:14b,id:"minecraft:diamond_shovel",tag:{Damage:79}},{Count:3b,Slot:15b,id:"minecraft:polished_blackstone_bricks"},{Count:64b,Slot:16b,id:"minecraft:sand"},{Count:64b,Slot:17b,id:"minecraft:sand"},{Count:64b,Slot:18b,id:"minecraft:sand"},{Count:23b,Slot:19b,id:"minecraft:coal"},{Count:1b,Slot:20b,id:"minecraft:piston"},{Count:64b,Slot:21b,id:"minecraft:gold_ingot"},{Count:24b,Slot:22b,id:"minecraft:gold_ingot"},{Count:51b,Slot:23b,id:"minecraft:cobblestone"},{Count:32b,Slot:24b,id:"alltheores:steel_ingot"},{Count:4b,Slot:26b,id:"minecraft:diamond"},{Count:8b,Slot:27b,id:"minecraft:magma_block"},{Count:64b,Slot:28b,id:"minecraft:glowstone_dust"},{Count:64b,Slot:29b,id:"minecraft:glowstone_dust"},{Count:3b,Slot:30b,id:"minecraft:glowstone_dust"},{Count:1b,Slot:31b,id:"minecraft:diamond_pickaxe",tag:{Damage:0}},{Count:7b,Slot:32b,id:"minecraft:stone"},{Count:15b,Slot:33b,id:"minecraft:acacia_planks"},{Count:64b,Slot:34b,id:"minecraft:iron_ingot"},{Count:1b,Slot:35b,id:"ad_astra:steel_ingot"},{Count:1b,Slot:100b,id:"minecraft:iron_boots",tag:{Damage:74,Enchantments:[{id:"minecraft:unbreaking",lvl:3s}],affix_data:{affixes:{"apotheosis:armor/attribute/aquatic":0.6951589f,"apotheosis:armor/attribute/ironforged":0.060661495f,"apotheosis:armor/attribute/windswept":0.50156903f},name:'{"italic":false,"color":"#33FF33","translate":"misc.apotheosis.affix_name.four","with":["Tiara\'s",{"translate":"affix.apotheosis:armor/attribute/ironforged"},"",{"translate":"affix.apotheosis:armor/attribute/aquatic.suffix"}]}',rarity:"apotheosis:uncommon",sockets:1,uuids:[[I;543605852,1596017109,-1258907759,738223676]]},apoth_boss:1b,display:{Name:'{"extra":[{"text":"Sabatons"}],"text":""}'}}},{Count:1b,Slot:101b,id:"minecraft:diamond_leggings",tag:{Damage:0,Enchantments:[{id:"minecraft:mending",lvl:1s}],affix_data:{affixes:{"irons_spellbooks:armor/attribute/mana":0.46328282f},name:'{"color":"#808080","translate":"misc.apotheosis.affix_name.two","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",""]}',rarity:"apotheosis:common",uuids:[[I;951207470,-633256206,-1572923371,921843544]]}}},{Count:1b,Slot:102b,id:"mekanism:jetpack_armored",tag:{mekData:{GasTanks:[{Tank:0b,stored:{amount:12642L,gasName:"mekanism:hydrogen"}}]}}},{Count:1b,Slot:103b,id:"minecraft:diamond_helmet",tag:{Damage:88}}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:overworld",pos:[I;-1345,-42,530]},Motion:[0.0d,0.2209648317373603d,0.0d],OnGround:0b,PortalCooldown:0,Pos:[-1349.3104501742728d,50.3654805824588d,691.2388165678018d],Railways_DataVersion:2,Rotation:[-121.34766f,68.54951f],Score:5475,SelectedItemSlot:0,SleepTimer:0s,SpawnAngle:-165.90857f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:-1310,SpawnY:69,SpawnZ:708,Spigot.ticksLived:352053,UUID:[I;-799833065,-1501608067,-1275361124,1212490464],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-371360049434547L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:46,XpP:0.6875001f,XpSeed:-1745993829,XpTotal:4443,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752069914362L,keepLevel:0b,lastKnownName:"mubaiyo",lastPlayed:1752213159989L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.5264611f,foodLevel:20,foodSaturationLevel:1.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwfurnitures:stripped_jungle_desk","mcwfurnitures:stripped_acacia_counter","biomesoplenty:fir_boat","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwdoors:bamboo_stable_door","minecraft:kjs/structurecompass_structure_compass","dyenamics:banner/lavender_banner","minecraft:stonecutter","littlelogistics:seater_barge","handcrafted:spider_trophy","minecraft:bone_block","alltheores:gold_dust_from_hammer_crushing","mcwbiomesoplenty:willow_waffle_door","mcwpaths:cobbled_deepslate_crystal_floor_path","croptopia:shaped_beef_stew","minecraft:melon","ae2:network/cables/dense_smart_green","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","mcwtrpdoors:acacia_barrel_trapdoor","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mcwfurnitures:stripped_acacia_bookshelf_cupboard","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","mekanism:chemical_tank/basic","create:crafting/kinetics/light_gray_seat","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","dyenamics:maroon_candle","mcwroofs:acacia_upper_steep_roof","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","buildinggadgets2:gadget_cut_paste","mcwtrpdoors:acacia_barn_trapdoor","reliquary:mob_charm_fragments/cave_spider","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","rftoolsbuilder:mover_status","alltheores:brass_plate","simplylight:illuminant_light_blue_block_dyed","aether:iron_gloves_repairing","railcraft:controller_circuit","twigs:polished_rhyolite_stonecutting","handcrafted:deepslate_corner_trim","minecraft:spruce_sign","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","create:crafting/logistics/brass_tunnel","create:crafting/kinetics/gearbox","sophisticatedstorage:copper_to_iron_tier_upgrade","mcwbiomesoplenty:umbran_modern_door","handcrafted:spruce_side_table","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwbiomesoplenty:redwood_nether_door","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","mcwroofs:acacia_planks_top_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","elevatorid:elevator_white","supplementaries:bubble_blower","naturalist:glow_goop","biomesoplenty:palm_fence","supplementaries:crank","minecraft:recovery_compass","biomesoplenty:palm_button","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","minecraft:magenta_dye_from_blue_red_white_dye","alltheores:iron_plate","mcwroofs:acacia_planks_upper_lower_roof","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","supplementaries:netherite_trapdoor","productivebees:stonecutter/fir_canvas_expansion_box","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","cfm:acacia_cabinet","littlelogistics:rapid_hopper","mcwbiomesoplenty:willow_bark_glass_door","railcraft:energy_minecart","mcwbiomesoplenty:maple_nether_door","mcwbiomesoplenty:empyreal_classic_door","rftoolsutility:destination_analyzer","twigs:bamboo_thatch","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwbiomesoplenty:palm_modern_chair","immersiveengineering:crafting/rockcutter","minecraft:cut_sandstone_slab","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mekanism:energy_tablet","mcwbiomesoplenty:hellbark_barn_door","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:palm_steep_roof","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","minecraft:acacia_door","travelersbackpack:diamond_tier_upgrade","additionallanterns:mossy_cobblestone_chain","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","mcwfurnitures:acacia_lower_triple_drawer","cfm:oak_kitchen_counter","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","mekanism:factory/basic/infusing","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:dye_green_bed","alltheores:platinum_dust_from_hammer_crushing","railcraft:any_detector","botania:mana_distributor","biomesoplenty:mahogany_boat","quark:tweaks/crafting/utility/misc/charcoal_to_black_dye","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","minecraft:wild_armor_trim_smithing_template_smithing_trim","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","mcwroofs:stone_bricks_upper_steep_roof","biomesoplenty:palm_chest_boat","mcwtrpdoors:bamboo_mystic_trapdoor","minecraft:stone_button","mcwbiomesoplenty:redwood_bark_glass_door","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","minecraft:tinted_glass","pneumaticcraft:regulator_tube_module","minecraft:sandstone","minecraft:lodestone","mcwbiomesoplenty:umbran_japanese2_door","occultism:crafting/magic_lamp_empty","handcrafted:acacia_pillar_trim","mcwbiomesoplenty:palm_planks_upper_steep_roof","railcraft:brass_ingot_crafted_with_ingots","minecraft:cut_copper_stairs_from_copper_block_stonecutting","domum_ornamentum:blue_brick_extra","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","mcwbiomesoplenty:palm_large_drawer","pneumaticcraft:transfer_gadget","silentgear:blaze_gold_block","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","enderio:extraction_speed_upgrade_1","create:industrial_iron_block_from_ingots_iron_stonecutting","quark:building/crafting/furnaces/deepslate_furnace","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","tombstone:grave_plate","mcwbiomesoplenty:palm_planks_top_roof","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","productivebees:stonecutter/oak_canvas_hive","cfm:gray_trampoline","mcwbiomesoplenty:hellbark_beach_door","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","mcwbiomesoplenty:umbran_classic_door","comforts:hammock_to_green","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","littlelogistics:barrel_barge","croptopia:fried_calamari","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:hellbark_bark_glass_door","cfm:spruce_bedside_cabinet","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","xnet:connector_routing","blue_skies:glowing_nature_stone","mcwwindows:warped_louvered_shutter","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","mcwbiomesoplenty:umbran_stable_head_door","twilightforest:wood/jungle_banister","productivebees:stonecutter/palm_canvas_expansion_box","mcwbiomesoplenty:palm_bark_glass_door","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","farmersdelight:barbecue_stick","immersiveengineering:crafting/shield","mcwbiomesoplenty:umbran_cottage_door","utilitix:directional_highspeed_rail","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","mcwbiomesoplenty:palm_roof","immersiveengineering:crafting/blastbrick","dyenamics:ultramarine_candle","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","xnet:antenna_base","mcwbiomesoplenty:pine_stable_head_door","cfm:green_grill","minecraft:cooked_chicken_from_smoking","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","cfm:orange_grill","dyenamics:fluorescent_candle","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","cfm:stripped_acacia_park_bench","rftoolsutility:crafter1","cfm:jungle_desk","minecraft:mangrove_chest_boat","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mekanismgenerators:generator/wind","mcwroofs:light_blue_concrete_lower_roof","mcwfurnitures:jungle_drawer","twigs:chiseled_bricks_stonecutting","create:crafting/kinetics/fluid_tank","mcwroofs:purple_striped_awning","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","mcwbiomesoplenty:palm_japanese2_door","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","dyenamics:lavender_candle","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwfences:blackstone_brick_railing_gate","mcwtrpdoors:bamboo_tropical_trapdoor","mcwbiomesoplenty:maple_japanese_door","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwpaths:mossy_stone_running_bond_slab","minecraft:golden_pickaxe","handcrafted:acacia_desk","mcwpaths:cobbled_deepslate_flagstone_stairs","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","mcwbiomesoplenty:palm_whispering_trapdoor","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwfurnitures:acacia_stool_chair","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","supplementaries:evilcraft/sign_post_undead","xnet:wireless_router","mcwroofs:gutter_base_gray","mcwfurnitures:stripped_acacia_modern_chair","simplylight:illuminant_light_gray_block_on_dyed","mcwwindows:dark_prismarine_brick_gothic","additionallanterns:normal_lantern_pink","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","mcwfurnitures:stripped_acacia_lower_triple_drawer","cfm:stripped_birch_kitchen_drawer","pneumaticcraft:compressed_bricks_from_tile_stonecutting","twigs:chiseled_bricks","enderio:stone_gear","silentgear:blaze_gold_dust_blasting","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","farmersdelight:tomato_crate","xnet:netcable_yellow","mcwlights:tavern_wall_lantern","mcwfurnitures:jungle_wardrobe","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","biomesoplenty:palm_sign","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","rftoolscontrol:tank","mcwwindows:gray_mosaic_glass_pane","additionallanterns:normal_lantern_orange","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwbiomesoplenty:willow_beach_door","minecraft:chiseled_stone_bricks_stone_from_stonecutting","croptopia:potato_chips","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","minecraft:netherite_sword_smithing","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","croptopia:shaped_toast_sandwich","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwtrpdoors:acacia_cottage_trapdoor","mcwwindows:oak_blinds","minecraft:green_bed","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwbiomesoplenty:empyreal_beach_door","farmersdelight:rice_bale","handcrafted:spruce_counter","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","comforts:hammock_to_black","railcraft:signal_circuit","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","evilcraft:special/vengeance_pickaxe","sophisticatedstorage:oak_chest_from_vanilla_chest","immersiveengineering:crafting/gunpart_barrel","mcwbiomesoplenty:mahogany_swamp_door","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","cfm:stripped_acacia_kitchen_counter","cfm:pink_cooler","rftoolsbuilder:vehicle_builder","twilightforest:twilight_oak_chest_boat","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","minecraft:smooth_sandstone_slab","mcwbiomesoplenty:umbran_barn_door","mcwbiomesoplenty:palm_double_drawer","ad_astra:blue_industrial_lamp","mcwbiomesoplenty:maple_modern_door","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","croptopia:chicken_and_dumplings","additionallanterns:emerald_chain","mekanism:transmitter/mechanical_pipe/basic","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwbiomesoplenty:empyreal_cottage_door","utilitix:tiny_charcoal_to_tiny","railcraft:polished_quarried_stone_from_quarried_stone","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","farmersdelight:cooking/dumplings","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","dyenamics:bed/bubblegum_bed","cfm:stripped_warped_mail_box","mcwbridges:spruce_rail_bridge","immersiveengineering:crafting/toolbox","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","sophisticatedstorage:spruce_limited_barrel_3","sophisticatedstorage:spruce_limited_barrel_2","sophisticatedstorage:spruce_limited_barrel_1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","forbidden_arcanus:clibano_core","sophisticatedstorage:spruce_limited_barrel_4","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","sophisticatedstorage:storage_tool","mcwbiomesoplenty:palm_barrel_trapdoor","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_bricks_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:iron_panel","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:blue_candle","railcraft:blast_furnace_bricks","pneumaticcraft:wall_lamp_pink","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","reliquary:uncrafting/glass_bottle","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:jacaranda_nether_door","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","mcwfurnitures:acacia_counter","biomesoplenty:orange_dye_from_burning_blossom","mcwbiomesoplenty:stripped_palm_covered_desk","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","utilitarian:utility/acacia_logs_to_doors","cfm:stripped_acacia_chair","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_bricks_stonecutting","mcwpaths:mossy_stone_windmill_weave_path","minecraft:gold_ingot_from_blasting_raw_gold","mcwbiomesoplenty:palm_striped_chair","minecraft:iron_nugget_from_blasting","railcraft:iron_tank_wall","productivebees:stonecutter/aspen_canvas_expansion_box","pneumaticcraft:liquid_hopper","handcrafted:wood_cup","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","aether:golden_dart","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","mcwroofs:acacia_attic_roof","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","croptopia:fried_chicken","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:mahogany_nether_door","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:fir_bark_glass_door","mcwlights:oak_ceiling_fan_light","rftoolsbuilder:shape_card_quarry","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","evilcraft:crafting/undead_planks","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","create:deepslate_from_stone_types_deepslate_stonecutting","xnet:netcable_blue","cfm:orange_trampoline","cfm:diving_board","wirelesschargers:basic_wireless_player_charger","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","cfm:rock_path","connectedglass:scratched_glass_black2","minecraft:wooden_hoe","minecraft:cooked_beef_from_campfire_cooking","mekanismgenerators:generator/heat","mcwbiomesoplenty:redwood_waffle_door","createoreexcavation:diamond_drill","immersiveengineering:crafting/axe_steel","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","productivebees:stonecutter/jungle_canvas_hive","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","minecraft:waxed_cut_copper_from_honeycomb","ad_astra:black_industrial_lamp","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","mcwbiomesoplenty:willow_barn_door","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","biomesoplenty:brimstone_bud","sophisticatedstorage:gold_to_netherite_tier_upgrade","aether:obsidian_from_bucket_freezing","minecraft:blue_dye","mcwbiomesoplenty:palm_desk","railcraft:steel_leggings","mekanism:energized_smelter","mcwbiomesoplenty:umbran_nether_door","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:piston","mcwfurnitures:jungle_coffee_table","mcwbiomesoplenty:maple_waffle_door","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","allthemodium:allthemodium_ingot_from_raw_smelting","simplylight:illuminant_yellow_block_on_dyed","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","mcwdoors:bamboo_four_panel_door","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwbiomesoplenty:dead_modern_door","mcwbiomesoplenty:fir_stable_door","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","mcwbiomesoplenty:palm_classic_door","mcwdoors:acacia_cottage_door","mcwlights:cross_wall_lantern","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","delightful:food/cooking/rock_candy","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwbiomesoplenty:magic_modern_door","handcrafted:stackable_book","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","minecraft:stone_stairs_from_stone_stonecutting","silentgear:sinew_fiber","mcwfences:crimson_curved_gate","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","supplementaries:candle_holders/candle_holder_red","mcwbiomesoplenty:palm_triple_drawer","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwbridges:acacia_rail_bridge","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:palm_tropical_door","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","handcrafted:creeper_trophy","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","minecraft:dye_blue_wool","mcwlights:covered_wall_lantern","mcwbiomesoplenty:palm_cottage_door","productivebees:stonecutter/oak_canvas_expansion_box","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","packingtape:tape","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","silentgear:crimson_iron_dust","mcwbiomesoplenty:pine_japanese2_door","enderio:dark_steel_nugget_to_ingot","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwbiomesoplenty:stripped_palm_triple_drawer","immersiveengineering:crafting/pickaxe_steel","pneumaticcraft:pressure_chamber_interface","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","travelersbackpack:diamond","dyenamics:persimmon_candle","mekanism:transmitter/universal_cable/advanced","cfm:stripped_warped_kitchen_drawer","mcwpaths:mossy_stone_crystal_floor_stairs","mcwbiomesoplenty:palm_drawer_counter","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","mcwtrpdoors:jungle_ranch_trapdoor","minecraft:black_carpet","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","mcwfurnitures:acacia_double_wardrobe","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","alltheores:brass_rod","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","mcwfurnitures:stripped_jungle_stool_chair","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mcwwindows:white_curtain","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwpaths:acacia_planks_path","cfm:light_gray_grill","create:crafting/kinetics/black_seat","cfm:oak_desk","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","everythingcopper:copper_hopper","mcwbiomesoplenty:dead_bark_glass_door","minecolonies:potato_soup","mcwbiomesoplenty:pine_mystic_door","mcwbiomesoplenty:fir_paper_door","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","cfm:dye_black_picket_gate","aether:skyroot_grindstone","mcwbiomesoplenty:fir_japanese_door","minecraft:green_stained_glass_pane_from_glass_pane","minecraft:cut_copper_slab","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbridges:dry_bamboo_bridge_pier","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","ad_astra:wrench","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwbiomesoplenty:palm_stool_chair","mcwfurnitures:stripped_acacia_bookshelf_drawer","cfm:red_cooler","handcrafted:jungle_dining_bench","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","aether:golden_pendant","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","cfm:acacia_desk_cabinet","rftoolsutility:matter_beamer","mcwbiomesoplenty:dead_nether_door","cfm:acacia_blinds","mcwbiomesoplenty:dead_paper_door","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","minecraft:carrot_on_a_stick","rftoolspower:blazing_agitator","railcraft:signal_capacitor_box","mcwwindows:spruce_curtain_rod","cfm:dye_blue_picket_gate","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_acacia_glass_table","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","handcrafted:acacia_shelf","mcwfurnitures:acacia_chair","mcwbiomesoplenty:palm_nether_door","enderio:dark_steel_nugget","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","croptopia:fruit_smoothie","mcwbiomesoplenty:fir_western_door","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwfurnitures:jungle_striped_chair","bambooeverything:bamboo_torch","dyenamics:honey_wool","mcwroofs:acacia_top_roof","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","additionallanterns:diamond_lantern","railcraft:steel_shears","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:mossy_cobblestone_slab_from_mossy_cobblestone_stonecutting","mcwfurnitures:acacia_glass_table","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","cfm:stripped_acacia_table","minecraft:compass","bigreactors:energizer/computerport","littlelogistics:vacuum_barge","allthemodium:allthemodium_apple","mcwbiomesoplenty:maple_mystic_door","mcwpaths:mossy_stone_running_bond_stairs","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","itemcollectors:basic_collector","minecraft:loom","supplementaries:sign_post_spruce","railcraft:steel_axe","domum_ornamentum:red_brick_extra","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","rftoolsutility:tank","aether:netherite_gloves_smithing","mcwbiomesoplenty:palm_wardrobe","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbiomesoplenty:mahogany_stable_head_door","mcwbridges:pliers","botania:red_string_comparator","mcwbiomesoplenty:empyreal_stockade_fence","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","alltheores:nickel_plate","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","handcrafted:blue_crockery_combo","mcwbiomesoplenty:palm_log_bridge_middle","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","mcwbiomesoplenty:palm_modern_desk","dyenamics:banner/rose_banner","enderio:redstone_alloy_nugget_to_ingot","everythingcopper:copper_pressure_plate","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","handcrafted:wood_plate","mcwbiomesoplenty:jacaranda_beach_door","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","mcwroofs:stone_top_roof","bigreactors:turbine/reinforced/passivefluidport_forge","travelersbackpack:horse","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","aether:diamond_gloves_repairing","cfm:brown_cooler","xnet:netcable_red","mcwroofs:acacia_planks_roof","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","minecraft:spruce_trapdoor","mcwwindows:dark_prismarine_parapet","dyenamics:amber_candle","bigreactors:reprocessor/wasteinjector","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","handcrafted:goat_trophy","paraglider:paraglider","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","mcwpaths:cobbled_deepslate_flagstone","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","mcwbiomesoplenty:mahogany_modern_door","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","utilitarian:utility/acacia_logs_to_boats","buildinggadgets2:gadget_exchanging","mcwbiomesoplenty:stripped_palm_desk","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","handcrafted:spruce_cupboard","create:crafting/kinetics/fluid_valve","supplementaries:crystal_display","minecraft:composter","minecraft:sandstone_stairs","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","mcwbiomesoplenty:palm_barn_glass_door","cfm:acacia_chair","aether:aether_gold_nugget_from_blasting","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","domum_ornamentum:cyan_floating_carpet","minecraft:acacia_sign","minecraft:coarse_dirt","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwroofs:stone_bricks_steep_roof","mcwwindows:red_curtain","create:crafting/logistics/brass_funnel","minecraft:fermented_spider_eye","mcwbiomesoplenty:stripped_palm_drawer_counter","mcwbiomesoplenty:palm_rail_bridge","mcwwindows:light_gray_mosaic_glass","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","supplementaries:flags/flag_black","mcwwindows:acacia_louvered_shutter","minecraft:deepslate_bricks_from_polished_deepslate_stonecutting","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","cfm:light_gray_cooler","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","allthemodium:allthemodium_dust_from_ore_crushing","mcwroofs:bricks_attic_roof","mcwdoors:jungle_whispering_door","reliquary:uncrafting/spider_eye","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","mcwbiomesoplenty:dead_waffle_door","twigs:bamboo_mat","connectedglass:borderless_glass_black2","railcraft:iron_gear","handcrafted:acacia_table","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","mcwfurnitures:stripped_acacia_drawer_counter","handcrafted:oak_pillar_trim","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","securitycraft:redstone_module","minecraft:lime_dye","mcwbiomesoplenty:pine_bamboo_door","ad_astra:green_flag","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","biomesoplenty:palm_door","supplementaries:flags/flag_purple","create:framed_glass_from_glass_colorless_stonecutting","sophisticatedstorage:jungle_barrel","botania:quartz_sunny","productivebees:stonecutter/maple_canvas_expansion_box","mcwfurnitures:stripped_acacia_cupboard_counter","mcwbiomesoplenty:dead_swamp_door","biomesoplenty:brimstone_fumarole","railcraft:signal_lamp","cfm:jungle_blinds","reliquary:uncrafting/sugar","tombstone:bone_needle","mcwpaths:mossy_stone_windmill_weave_slab","minecraft:stone_brick_wall","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","mcwbiomesoplenty:redwood_classic_door","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","sophisticatedstorage:jungle_limited_barrel_3","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","minecraft:cyan_dye","mcwwindows:acacia_window2","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","evilcraft:crafting/dark_stick","handcrafted:skeleton_horse_trophy","enderio:stone_gear_upgrade","mcwbiomesoplenty:magic_waffle_door","additionallanterns:warped_lantern","minecraft:spruce_boat","minecraft:gold_block","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","handcrafted:jungle_counter","mcwbiomesoplenty:redwood_cottage_door","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","handcrafted:black_sheet","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","cfm:acacia_bedside_cabinet","pneumaticcraft:compressed_bricks_from_tile","utilitix:stone_wall_stonecutting","cfm:spruce_kitchen_counter","travelersbackpack:warden","ad_astra:gas_tank","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","additionallanterns:copper_lantern","twilightforest:canopy_chest_boat","mcwroofs:jungle_planks_attic_roof","mcwpaths:spruce_planks_path","bambooeverything:dry_bamboo","mcwlights:golden_small_chandelier","mcwbiomesoplenty:willow_paper_door","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","cfm:crimson_mail_box","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","twigs:soul_lamp","supplementaries:deepslate_lamp","rftoolsutility:screen_controller","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","mcwbiomesoplenty:redwood_swamp_door","farmersdelight:cooking/baked_cod_stew","immersiveengineering:crafting/wirecoil_structure_rope","mcwbiomesoplenty:jacaranda_western_door","alltheores:lead_rod","utilitix:stonecutter_cart","cfm:dye_green_picket_fence","mcwwindows:oak_log_parapet","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwfurnitures:stripped_acacia_modern_wardrobe","connectedglass:tinted_borderless_glass_green2","ad_astra:iron_plateblock","aquaculture:dark_oak_fish_mount","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","allthecompressed:compress/jungle_planks_1x","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","minecraft:iron_block","farmersdelight:spruce_cabinet","mcwlights:garden_light","mcwdoors:acacia_beach_door","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwpaths:mossy_cobblestone_diamond_paving","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","allthecompressed:compress/spruce_planks_1x","deeperdarker:warden_upgrade_smithing_template","mcwbridges:bamboo_bridge_pier","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","aether:iron_helmet_repairing","pneumaticcraft:reinforced_brick_wall","mcwroofs:acacia_planks_upper_steep_roof","mcwroofs:purple_concrete_upper_steep_roof","utilitarian:utility/acacia_logs_to_stairs","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","cfm:acacia_coffee_table","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","supplementaries:flute","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","reliquary:mob_charm_fragments/ghast","mcwfences:warped_horse_fence","farmersdelight:cooking/squid_ink_pasta","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","mcwbiomesoplenty:stripped_palm_modern_desk","mcwbiomesoplenty:palm_bookshelf_drawer","utilitarian:redstone_clock","mcwdoors:bamboo_classic_door","securitycraft:harming_module","minecraft:golden_boots","silentgear:bort_block","immersiveengineering:crafting/grit_sand","mcwbiomesoplenty:palm_lower_roof","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","create:crafting/kinetics/item_vault","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","rftoolspower:blazing_generator","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","mcwbiomesoplenty:mahogany_tropical_door","immersiveengineering:crafting/tinted_glass_lead_wire","minecraft:dye_green_carpet","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","enderio:dark_steel_grinding_ball","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","mcwbiomesoplenty:stripped_palm_drawer","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","mcwbiomesoplenty:maple_stable_head_door","rftoolsutility:counterplus_module","minecraft:polished_blackstone_brick_stairs","mcwbiomesoplenty:palm_planks_upper_lower_roof","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","minecraft:stick_from_bamboo_item","alltheores:bronze_plate","botania:swap_ring","mcwpaths:brick_crystal_floor_slab","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","mcwfurnitures:acacia_double_drawer","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","allthecompressed:compress/acacia_log_1x","pneumaticcraft:pressure_chamber_wall","minecraft:glass","mcwbiomesoplenty:pine_classic_door","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","minecraft:polished_deepslate_stairs_from_polished_deepslate_stonecutting","mcwroofs:brown_concrete_attic_roof","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwbiomesoplenty:pine_cottage_door","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","minecraft:light_blue_concrete_powder","minecraft:smooth_sandstone_stairs_from_smooth_sandstone_stonecutting","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","enderio:dark_steel_ladder","sophisticatedstorage:acacia_chest","mcwwindows:light_blue_mosaic_glass","ad_astra:green_industrial_lamp","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","chimes:bamboo_chimes","rftoolsstorage:storage_control_module","mekanism:factory/basic/smelting","mcwbiomesoplenty:pine_nether_door","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwbiomesoplenty:hellbark_barn_glass_door","mcwbiomesoplenty:palm_mystic_trapdoor","twigs:mossy_cobblestone_bricks","deepresonance:radiation_monitor","railcraft:steel_gear","aether:diamond_shovel_repairing","ad_astra:encased_iron_block","enderio:pulsating_alloy_nugget","bigreactors:smelting/graphite_from_charcoal","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","mcwbiomesoplenty:palm_four_panel_trapdoor","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","minecraft:spruce_chest_boat","utilitarian:utility/acacia_logs_to_slabs","xnet:netcable_green_dye","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:jacaranda_bamboo_door","handcrafted:bricks_pillar_trim","botania:quartz_lavender","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","mcwbiomesoplenty:redwood_four_panel_door","minecraft:acacia_fence","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","immersiveengineering:crafting/alu_wallmount","mcwbiomesoplenty:palm_lower_bookshelf_drawer","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","minecraft:glistering_melon_slice","create:crafting/kinetics/magenta_seat","simplylight:illuminant_magenta_block_toggle","handcrafted:spruce_fancy_bed","minecraft:acacia_fence_gate","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","handcrafted:spruce_chair","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","supplementaries:candle_holders/candle_holder_black_dye","bigreactors:crafting/raw_yellorium_component_to_storage","twigs:rhyolite_slab","immersiveengineering:crafting/stick_iron","mcwbiomesoplenty:stripped_palm_modern_chair","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","alltheores:aluminum_gear","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","pneumaticcraft:compressed_bricks_from_stone_stonecutting","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwfurnitures:acacia_table","railcraft:player_detector","create:crafting/appliances/copper_diving_boots","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","immersiveengineering:crafting/component_iron","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","botania:azulejo_0","mcwtrpdoors:bamboo_blossom_trapdoor","mcwbiomesoplenty:fir_tropical_door","sfm:fancy_to_cable","mcwbiomesoplenty:stripped_palm_table","aquaculture:heavy_hook","mcwbiomesoplenty:palm_western_door","minecraft:white_concrete_powder","biomesoplenty:dead_chest_boat","mcwwindows:acacia_window","croptopia:food_press","railcraft:mob_detector","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","enderio:enchanter","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwbiomesoplenty:willow_four_panel_door","mcwpaths:brick_honeycomb_paving","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","xnet:router","silentgear:material_grader","mcwfurnitures:stripped_jungle_modern_chair","pneumaticcraft:small_tank","mcwbiomesoplenty:willow_classic_door","ae2:network/cables/glass_green","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","aether:golden_gloves_repairing","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","create:tuff_pillar_from_stone_types_tuff_stonecutting","mcwpaths:brick_crystal_floor","handcrafted:acacia_side_table","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwdoors:acacia_swamp_door","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","minecraft:deepslate_brick_slab_from_polished_deepslate_stonecutting","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","railcraft:charge_terminal","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","minecraft:deepslate_tile_slab_from_polished_deepslate_stonecutting","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","croptopia:chocolate","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","domum_ornamentum:architectscutter","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","immersiveengineering:crafting/alu_fence","domum_ornamentum:red_floating_carpet","aether:iron_boots_repairing","minecraft:diamond_shovel","mcwdoors:bamboo_beach_door","mcwwindows:end_brick_gothic","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","mcwpaths:brick_running_bond_slab","aether:iron_ring","tombstone:impregnated_diamond","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","dyenamics:peach_concrete_powder","mcwbiomesoplenty:dead_beach_door","cfm:cyan_kitchen_sink","mcwroofs:jungle_planks_upper_lower_roof","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","mcwbiomesoplenty:mahogany_four_panel_door","utilitix:directional_rail","mcwfurnitures:stripped_acacia_covered_desk","mcwbiomesoplenty:fir_barn_glass_door","railcraft:copper_gear","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","dyenamics:mint_wool","connectedglass:borderless_glass_green2","mcwfences:dark_oak_curved_gate","mcwdoors:bamboo_bark_glass_door","mcwwindows:cherry_curtain_rod","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","minecraft:chest","alltheores:diamond_plate","mcwbiomesoplenty:stripped_palm_bookshelf","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwbiomesoplenty:magic_paper_door","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","mcwbiomesoplenty:stripped_palm_large_drawer","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","dyenamics:navy_dye","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwbiomesoplenty:palm_lower_triple_drawer","mcwbiomesoplenty:hellbark_stable_head_door","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:jacaranda_swamp_door","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwroofs:gutter_base_red","mcwtrpdoors:acacia_glass_trapdoor","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","productivebees:stonecutter/concrete_canvas_hive","mcwdoors:print_bamboo","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","aether:aether_gold_nugget_from_smelting","mcwroofs:cobblestone_steep_roof","securitycraft:reinforced_tinted_glass","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","domum_ornamentum:mossy_cobblestone_extra","mcwfurnitures:acacia_modern_desk","mcwbiomesoplenty:palm_bridge_pier","minecraft:copper_ingot_from_smelting_raw_copper","cfm:dye_black_picket_fence","mcwwindows:crimson_curtain_rod","comforts:sleeping_bag_green","securitycraft:universal_key_changer","comforts:sleeping_bag_to_green","minecraft:netherite_ingot","naturalist:bug_net","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_2","mcwbiomesoplenty:jacaranda_mystic_door","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","additionallanterns:normal_lantern_gray","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","mcwfurnitures:acacia_end_table","aether:chainmail_helmet_repairing","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mekanism:metallurgic_infuser","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","alltheores:aluminum_rod","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","minecraft:spruce_stairs","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","railcraft:bushing_gear_brass","mcwbiomesoplenty:palm_chair","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","cfm:cyan_cooler","railcraft:silver_gear","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwdoors:acacia_modern_door","mcwbiomesoplenty:mahogany_mystic_door","create:crafting/kinetics/whisk","mcwroofs:light_blue_concrete_top_roof","enderio:redstone_alloy_grinding_ball","mcwbiomesoplenty:umbran_beach_door","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","dyenamics:bubblegum_candle","mcwbiomesoplenty:umbran_bark_glass_door","mcwlights:iron_triple_candle_holder","mcwdoors:bamboo_tropical_door","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","mcwfurnitures:stripped_acacia_end_table","travelersbackpack:redstone","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","biomesoplenty:brimstone_brick_wall_from_brimstone_stonecutting","minecraft:acacia_planks","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwbiomesoplenty:fir_nether_door","chimes:glass_bells","mcwwindows:iron_shutter","create:crafting/kinetics/large_water_wheel","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","aether:diamond_chestplate_repairing","biomesoplenty:chiseled_brimstone_bricks_from_brimstone_stonecutting","mcwpaths:mossy_stone_crystal_floor","travelersbackpack:emerald","comforts:sleeping_bag_to_black","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:mossy_cobblestone_square_paving","mcwbiomesoplenty:empyreal_japanese2_door","immersiveengineering:crafting/sawblade","mcwbiomesoplenty:empyreal_swamp_door","ae2:tools/nether_quartz_axe","mcwwindows:brown_mosaic_glass_pane","mcwpaths:stone_windmill_weave_stairs","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","everythingcopper:copper_shears","mcwfurnitures:stripped_acacia_coffee_table","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","productivebees:stonecutter/cherry_canvas_hive","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","botania:pump","minecraft:white_carpet","dyenamics:maroon_dye","aether:diamond_gloves","minecraft:mossy_cobblestone_stairs_from_mossy_cobblestone_stonecutting","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwdoors:jungle_four_panel_door","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwbiomesoplenty:dead_stable_door","minecraft:cracked_stone_bricks","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","simplylight:illuminant_cyan_block_on_toggle","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","travelersbackpack:dye_green_sleeping_bag","dimstorage:dim_wall","cfm:spruce_park_bench","sophisticatedstorage:iron_to_diamond_tier_upgrade","minecraft:magenta_stained_glass","mcwbiomesoplenty:willow_western_door","railcraft:nickel_gear","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","mcwpaths:cobbled_deepslate_running_bond_path","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","minecraft:acacia_pressure_plate","cfm:spruce_cabinet","sophisticatedstorage:spruce_barrel","botania:knockback_belt","mcwroofs:spruce_planks_steep_roof","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","handcrafted:spruce_shelf","mcwlights:acacia_tiki_torch","sophisticatedstorage:iron_to_netherite_tier_upgrade","corail_woodcutter:warped_woodcutter","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","securitycraft:reinforced_warped_fence_gate","mcwfurnitures:acacia_bookshelf","handcrafted:acacia_cupboard","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","alltheores:aluminum_plate","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","supplementaries:biomesoplenty/sign_post_palm","biomesoplenty:palm_trapdoor","additionallanterns:normal_lantern_black","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mekanism:factory/basic/crushing","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","mythicbotany:rune_holder","immersiveengineering:crafting/plate_silver_hammering","biomesoplenty:orange_sandstone","cfm:stripped_acacia_crate","farmersdelight:cabbage_crate","mcwbiomesoplenty:dead_barn_glass_door","allthecompressed:compress/cobbled_deepslate_1x","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","pneumaticcraft:thermostat_module","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwpaths:cobbled_deepslate_dumble_paving","mcwfurnitures:stripped_acacia_double_wardrobe","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","mcwbiomesoplenty:pine_western_door","minecraft:dye_blue_bed","utilitarian:utility/acacia_logs_to_trapdoors","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:hellbark_japanese_door","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","reliquary:uncrafting/string","connectedglass:tinted_borderless_glass_blue2","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","mcwlights:golden_wall_candle_holder","alltheores:diamond_rod","mcwbiomesoplenty:palm_stable_door","minecraft:stone_slab_from_stone_stonecutting","rftoolsbuilder:shield_block2","rftoolsbuilder:shield_block1","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwtrpdoors:jungle_whispering_trapdoor","farmersdelight:cooking/cabbage_rolls","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","silentgear:crimson_iron_dust_blasting","minecraft:stone_brick_slab_from_stone_stonecutting","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwbiomesoplenty:palm_four_panel_door","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwlights:white_paper_lamp","cfm:spruce_chair","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","littlelogistics:locomotive_route","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","mcwbiomesoplenty:stripped_palm_wardrobe","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","create:crafting/kinetics/placard","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","minecolonies:soy_pea_soup","minecraft:stone_brick_stairs_from_stone_stonecutting","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","alltheores:tin_plate","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","handcrafted:wood_bowl","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwdoors:bamboo_barn_glass_door","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","mcwbiomesoplenty:fir_glass_door","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","twilightforest:mangrove_chest_boat","minecraft:netherite_machete_smithing","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","mcwbiomesoplenty:palm_barn_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwpaths:mossy_stone_strewn_rocky_path","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","farmersdelight:safety_net","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","botania:water_ring","minecraft:music_disc_5","mekanism:factory/basic/compressing","minecraft:coal","mcwbiomesoplenty:mahogany_bamboo_door","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","croptopia:chicken_and_rice","aether:iron_axe_repairing","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwwindows:nether_brick_gothic","botania:forest_eye","additionallanterns:netherite_lantern","mcwbiomesoplenty:willow_mystic_door","biomesoplenty:palm_pressure_plate","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","mcwbiomesoplenty:pine_modern_door","handcrafted:wither_skeleton_trophy","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","minecraft:brown_dye","mcwtrpdoors:acacia_mystic_trapdoor","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","minecraft:brick_slab","mcwbiomesoplenty:umbran_paper_door","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","cfm:lime_cooler","rftoolsbuilder:shape_card_quarry_clear_fortune","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:empyreal_stable_door","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","dyenamics:mint_dye","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","minecraft:polished_deepslate_stairs","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","mcwroofs:pink_concrete_lower_roof","handcrafted:acacia_dining_bench","mcwroofs:acacia_planks_lower_roof","paraglider:cosmetic/goron_goddess_statue","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwlights:iron_chandelier","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_bricks_stonecutting","mcwpaths:mossy_cobblestone_honeycomb_paving","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwbiomesoplenty:palm_classic_trapdoor","cfm:white_kitchen_drawer","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","dyenamics:banner/persimmon_banner","handcrafted:acacia_nightstand","mcwbiomesoplenty:pine_horse_fence","mcwpaths:mossy_stone_windmill_weave","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","minecraft:golden_carrot","enderio:redstone_alloy_nugget","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","deepresonance:machine_frame","mcwdoors:bamboo_nether_door","blue_skies:starlit_bookshelf","mcwdoors:jungle_glass_door","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","dyenamics:cherenkov_candle","xnet:netcable_red_dye","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","dyenamics:honey_stained_glass","cfm:stripped_acacia_cabinet","create:crafting/kinetics/elevator_pulley","mcwfences:sandstone_grass_topped_wall","alltheores:invar_rod","travelersbackpack:black_sleeping_bag","minecraft:glowstone","alltheores:gold_rod","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","mcwbiomesoplenty:stripped_palm_double_drawer","biomesoplenty:jacaranda_chest_boat","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","create:crafting/kinetics/attribute_filter","supplementaries:key","ae2:block_cutter/walls/sky_stone_wall","blue_skies:maple_bookshelf","securitycraft:reinforced_black_stained_glass_pane_from_dye","rftoolspower:power_core1","pneumaticcraft:logistics_frame_default_storage","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","biomesoplenty:palm_stairs","mcwbiomesoplenty:palm_planks_lower_roof","handcrafted:spruce_table","handcrafted:jungle_fancy_bed","farmersdelight:onion_crate","create:crafting/kinetics/purple_seat","farmersdelight:cooking/tomato_sauce","mcwpaths:brick_running_bond_path","mcwfurnitures:stripped_acacia_stool_chair","securitycraft:security_camera","rftoolsbuilder:shape_card_quarry_dirt","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","allthemodium:allthemodium_ingot_from_dust_smelting","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","cfm:acacia_table","mcwpaths:brick_windmill_weave_stairs","additionallanterns:normal_sandstone_lantern","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","farmersdelight:tomato_seeds","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwtrpdoors:bamboo_barn_trapdoor","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwbiomesoplenty:dead_barn_door","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","everythingcopper:copper_axe","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","minecraft:black_stained_glass_pane_from_glass_pane","connectedglass:scratched_glass_green_pane2","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","twigs:acacia_table","biomesoplenty:brimstone_cluster_from_brimstone_stonecutting","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","mcwdoors:bamboo_paper_door","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","bigreactors:energizer/powerport_fe","minecraft:acacia_trapdoor","immersiveengineering:crafting/stick_aluminum","mcwroofs:thatch_steep_roof","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","mcwfurnitures:acacia_modern_wardrobe","dyenamics:bubblegum_concrete_powder","xnet:redstoneproxy_update","minecraft:cut_copper_slab_from_cut_copper_stonecutting","mcwroofs:spruce_planks_attic_roof","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:fir_four_panel_door","aether:iron_chestplate_repairing","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","supplementaries:slingshot","silentgear:crimson_steel_ingot","allthemodium:allthemodium_plate","mcwdoors:acacia_japanese2_door","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","everythingcopper:copper_rail","cfm:black_grill","handcrafted:deepslate_pillar_trim","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","pneumaticcraft:wall_lamp_inverted_black","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","evilcraft:crafting/blood_infuser","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_four_panel_door","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","dyenamics:rose_candle","aether:aether_iron_nugget_from_blasting","cfm:black_sofa","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwroofs:bricks_upper_steep_roof","enderio:coordinate_selector","minecraft:iron_pickaxe","aether:shield_repairing","ae2:shaped/not_so_mysterious_cube","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","minecraft:spruce_button","mcwroofs:magenta_concrete_attic_roof","rftoolsutility:matter_booster","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","pipez:wrench","dyenamics:bed/wine_bed","mcwpaths:mossy_stone_windmill_weave_stairs","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","minecraft:birch_boat","mcwfences:spruce_highley_gate","mcwbiomesoplenty:redwood_stable_head_door","cfm:post_box","dyenamics:aquamarine_candle","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","mcwtrpdoors:bamboo_classic_trapdoor","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwbiomesoplenty:stripped_palm_striped_chair","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","mcwbiomesoplenty:empyreal_western_door","rftoolsstorage:dump_module","cfm:orange_cooler","mcwtrpdoors:bamboo_barred_trapdoor","mcwbiomesoplenty:empyreal_japanese_door","minecraft:lime_stained_glass","mcwroofs:acacia_lower_roof","dyenamics:mint_candle","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","biomesoplenty:pine_chest_boat","alltheores:steel_plate","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","mcwbiomesoplenty:hellbark_paper_door","mcwpaths:mossy_stone_flagstone","mcwfurnitures:acacia_coffee_table","additionallanterns:normal_lantern_lime","mcwbiomesoplenty:umbran_stable_door","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","pneumaticcraft:spawner_extractor","aether:packed_ice_freezing","mcwbiomesoplenty:willow_stable_head_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","silentgear:blaze_gold_ingot_from_nugget","mcwpaths:stone_crystal_floor","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwdoors:bamboo_modern_door","cfm:fridge_dark","chimes:copper_chimes","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","minecraft:cut_sandstone_slab_from_cut_sandstone_stonecutting","croptopia:frying_pan","travelersbackpack:green_sleeping_bag","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:spruce_fence_gate","minecraft:leather_chestplate","mcwwindows:black_mosaic_glass","alltheores:steel_dust_from_alloy_blending","supplementaries:sign_post_acacia","immersiveengineering:crafting/earmuffs","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","minecraft:scaffolding","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwpaths:mossy_cobblestone_clover_paving","sophisticatedstorage:basic_to_diamond_tier_upgrade","alltheores:sapphire_from_hammer_crushing","mcwbiomesoplenty:fir_cottage_door","mcwroofs:purple_concrete_attic_roof","minecraft:acacia_button","constructionwand:core_angel","mcwfurnitures:acacia_bookshelf_cupboard","xnet:connector_red_dye","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwbiomesoplenty:willow_glass_door","mcwroofs:gutter_base_light_gray","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","delightful:knives/tin_knife","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","mcwtrpdoors:bamboo_swamp_trapdoor","mcwfurnitures:acacia_covered_desk","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:bamboo_chest_raft","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","additionallanterns:normal_lantern_yellow","farmersdelight:melon_juice","productivebees:stonecutter/willow_canvas_expansion_box","aquaculture:birch_fish_mount","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","simplylight:illuminant_red_block_toggle","immersiveengineering:crafting/heavy_engineering","mcwwindows:metal_window2","aether:blue_ice_freezing","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","cfm:stripped_acacia_coffee_table","allthecompressed:compress/gravel_1x","mcwbiomesoplenty:empyreal_barn_glass_door","reliquary:uncrafting/glowstone_dust","tombstone:ankh_of_prayer","cfm:white_sofa","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwpaths:mossy_stone_flagstone_slab","rftoolsbuilder:mover_control2","mcwfences:acacia_highley_gate","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","connectedglass:tinted_borderless_glass_black2","handcrafted:acacia_bench","travelersbackpack:squid","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","minecraft:polished_andesite_slab","create:crafting/logistics/powered_latch","productivebees:stonecutter/warped_canvas_expansion_box","mcwbiomesoplenty:maple_swamp_door","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","cfm:acacia_kitchen_drawer","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","mcwbiomesoplenty:maple_bark_glass_door","handcrafted:oak_bench","farmersdelight:cooking/pasta_with_meatballs","silentgear:blaze_gold_dust_smelting","enderio:copper_alloy_nugget_to_ingot","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","utilitarian:angel_block","mcwroofs:spruce_planks_upper_steep_roof","minecraft:repeater","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:maple_stable_door","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","simplylight:illuminant_orange_block_on_toggle","rftoolsbuilder:shape_card_quarry_silk_dirt","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwbiomesoplenty:empyreal_glass_door","cfm:jungle_cabinet","additionallanterns:normal_lantern_white","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwbiomesoplenty:fir_japanese2_door","mcwtrpdoors:acacia_beach_trapdoor","alltheores:uranium_dust_from_hammer_ingot_crushing","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","mcwfurnitures:acacia_lower_bookshelf_drawer","mcwbiomesoplenty:fir_waffle_door","mcwbridges:balustrade_mossy_cobblestone_bridge","cfm:pink_kitchen_sink","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:navy_candle","mcwwindows:spruce_blinds","mcwbiomesoplenty:stripped_palm_lower_triple_drawer","immersiveengineering:crafting/gunpart_hammer","mcwwindows:jungle_four_window","mcwfences:deepslate_pillar_wall","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","securitycraft:reinforced_orange_stained_glass_pane_from_dye","cfm:jungle_chair","mcwlights:birch_tiki_torch","handcrafted:spruce_drawer","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","ironfurnaces:furnaces/iron_furnace","mcwbiomesoplenty:pine_beach_door","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","biomesoplenty:brimstone_bricks","minecraft:jungle_door","additionallanterns:normal_lantern_blue","handcrafted:green_sheet","mcwpaths:mossy_cobblestone_dumble_paving","aether:chainmail_gloves_repairing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwtrpdoors:spruce_blossom_trapdoor","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","allthemodium:smithing/allthemodium_upgrade_smithing_template","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","handcrafted:witch_trophy","biomesoplenty:rose_quartz_block","littlelogistics:seater_car","minecraft:decorated_pot_simple","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","minecraft:oak_pressure_plate","minecraft:stone_brick_stairs","bigreactors:turbine/basic/activefluidport_forge","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwwindows:quartz_window","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:acacia_crate","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","mcwbiomesoplenty:redwood_tropical_door","reliquary:mob_charm_fragments/creeper","dyenamics:peach_candle","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","mcwbiomesoplenty:palm_beach_trapdoor","minecraft:jungle_planks","minecraft:netherite_axe_smithing","minecraft:clock","mcwroofs:red_concrete_top_roof","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","railcraft:lead_gear","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","biomesoplenty:brimstone_brick_slab_from_brimstone_stonecutting","twilightforest:transformation_chest_boat","additionallanterns:normal_lantern_light_gray","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","dyenamics:bed/conifer_bed","minecraft:polished_deepslate_slab_from_polished_deepslate_stonecutting","mcwbiomesoplenty:palm_cottage_trapdoor","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","mcwdoors:acacia_western_door","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","ad_astra:iron_plating","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwbiomesoplenty:maple_paper_door","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","minecraft:cut_copper_stairs","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","alltheores:tin_rod","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","cfm:acacia_kitchen_sink_dark","mcwroofs:jungle_upper_steep_roof","rftoolsbase:machine_frame","minecraft:mossy_cobblestone_slab","pneumaticcraft:remote","cfm:stripped_jungle_chair","minecraft:dye_black_bed","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","mcwbiomesoplenty:magic_western_door","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwbiomesoplenty:palm_drawer","mcwbiomesoplenty:dead_four_panel_door","biomesoplenty:empyreal_chest_boat","mcwbiomesoplenty:rope_palm_bridge","sophisticatedstorage:basic_to_iron_tier_upgrade","railcraft:brass_gear","twigs:rhyolite","pylons:infusion_pylon","mcwbiomesoplenty:pine_swamp_door","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","domum_ornamentum:light_blue_brick_extra","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","handcrafted:acacia_couch","mcwroofs:thatch_top_roof","mekanism:processing/steel/ingot/from_dust_smelting","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","minecraft:lime_concrete_powder","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","mcwlights:festive_lantern","dyenamics:honey_candle","mcwbiomesoplenty:magic_tropical_door","securitycraft:block_change_detector","twigs:rocky_dirt","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","minecraft:netherite_scrap_from_blasting","mcwbiomesoplenty:dead_bamboo_door","mcwtrpdoors:bamboo_four_panel_trapdoor","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","biomesoplenty:brimstone_brick_stairs_from_brimstone_stonecutting","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwbiomesoplenty:stripped_palm_chair","utilitix:acacia_shulker_boat","createoreexcavation:vein_atlas","twilightforest:sorting_chest_boat","travelersbackpack:standard_smithing","mcwbiomesoplenty:palm_blossom_trapdoor","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","pneumaticcraft:wall_lamp_cyan","create:crafting/kinetics/fluid_pipe","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbiomesoplenty:palm_top_roof","enderio:pulsating_alloy_grinding_ball","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","mcwbiomesoplenty:stripped_palm_cupboard_counter","pneumaticcraft:pressure_chamber_glass_x1","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","undergarden:gloom_o_lantern","minecraft:sandstone_slab","enderio:copper_alloy_grinding_ball","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwbiomesoplenty:pine_bark_glass_door","croptopia:cinnamon_wood","minecraft:gold_ingot_from_smelting_raw_gold","mcwbiomesoplenty:palm_beach_door","minecraft:wild_armor_trim_smithing_template","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwwindows:acacia_plank_window","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","minecraft:cracked_polished_blackstone_bricks","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","mcwbiomesoplenty:magic_bamboo_door","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwfurnitures:stripped_acacia_double_drawer","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwroofs:thatch_lower_roof","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","handcrafted:spruce_couch","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwwindows:jungle_blinds","mcwbiomesoplenty:mahogany_stable_door","mcwlights:oak_tiki_torch","travelersbackpack:melon","minecraft:white_dye_from_lily_of_the_valley","securitycraft:block_pocket_manager","enderio:pulsating_alloy_nugget_to_ingot","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","alltheores:copper_ore_hammer","dyenamics:conifer_stained_glass","mcwbiomesoplenty:stripped_palm_counter","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","bloodmagic:blood_altar","mcwbiomesoplenty:stripped_palm_bookshelf_drawer","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","alltheores:brass_gear","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:acacia_triple_drawer","minecraft:netherite_chestplate_smithing","mcwbiomesoplenty:palm_swamp_trapdoor","mcwbridges:acacia_log_bridge_middle","cfm:jungle_kitchen_counter","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","mcwfurnitures:stripped_acacia_double_drawer_counter","cfm:spruce_coffee_table","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","railcraft:bag_of_cement","bigreactors:fluidizer/powerport","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","connectedglass:borderless_glass_blue2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","mcwroofs:yellow_concrete_lower_roof","handcrafted:blaze_trophy","cfm:acacia_desk","allthecompressed:compress/jungle_log_1x","mcwbiomesoplenty:pine_waffle_door","mcwwindows:dark_oak_plank_parapet","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwfurnitures:stripped_acacia_drawer","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","mekanism:osmium_compressor","create:spruce_window","immersiveengineering:crafting/alloybrick","biomesoplenty:brimstone_bud_from_brimstone_stonecutting","mcwbiomesoplenty:maple_pyramid_gate","minecraft:smooth_sandstone_slab_from_smooth_sandstone_stonecutting","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","minecraft:black_bed","sfm:manager","mcwroofs:purple_concrete_roof","cfm:purple_grill","mcwbiomesoplenty:willow_bamboo_door","create:crafting/kinetics/hose_pulley","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","deeperdarker:bloom_boat","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","dyenamics:rose_stained_glass","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","additionallanterns:normal_lantern_green","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwroofs:acacia_steep_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","minecraft:spectral_arrow","allthecompressed:compress/copper_block_1x","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","twigs:copper_pillar_from_cut_copper_stonecutting","mcwpaths:mossy_stone_crystal_floor_path","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","mcwbiomesoplenty:palm_upper_steep_roof","mcwdoors:bamboo_waffle_door","mcwfurnitures:stripped_acacia_desk","mcwroofs:oak_planks_attic_roof","evilcraft:crafting/blood_infusion_core","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","mcwbiomesoplenty:palm_paper_trapdoor","sophisticatedstorage:basic_to_copper_tier_upgrade","mcwbiomesoplenty:dead_tropical_door","sophisticatedstorage:acacia_limited_barrel_1","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","sophisticatedstorage:acacia_limited_barrel_3","sophisticatedstorage:acacia_limited_barrel_2","alltheores:sapphire_dust_from_hammer_crushing","bigreactors:reprocessor/powerport","mcwbiomesoplenty:jacaranda_paper_door","sophisticatedstorage:acacia_limited_barrel_4","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","aether:netherite_pickaxe_repairing","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwbiomesoplenty:palm_ranch_trapdoor","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwfences:panelled_metal_fence_gate","comforts:sleeping_bag_black","mcwlights:golden_double_candle_holder","cfm:light_gray_kitchen_sink","mcwbiomesoplenty:empyreal_nether_door","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","supplementaries:flags/flag_lime","sophisticatedstorage:acacia_barrel","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","minecraft:green_banner","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwpaths:brick_dumble_paving","utilitarian:snad/drit","handcrafted:jungle_desk","mcwtrpdoors:acacia_swamp_trapdoor","corail_woodcutter:bamboo_woodcutter","mcwbiomesoplenty:fir_modern_door","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","mcwbiomesoplenty:mahogany_barn_glass_door","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","securitycraft:alarm","pneumaticcraft:manometer","mcwbiomesoplenty:dead_japanese2_door","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","cfm:stripped_acacia_desk_cabinet","mcwbiomesoplenty:palm_paper_door","pneumaticcraft:wall_lamp_inverted_magenta","ae2:block_cutter/stairs/sky_stone_stairs","allthecompressed:compress/acacia_planks_1x","additionallanterns:amethyst_lantern","utilitix:crude_furnace","ad_astra:black_flag","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwfurnitures:stripped_acacia_striped_chair","mcwwindows:stone_window","rftoolspower:endergenic","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","biomesoplenty:palm_planks","productivebees:stonecutter/yucca_canvas_hive","integrateddynamics:crafting/drying_basin","additionallanterns:normal_lantern_cyan","mcwtrpdoors:jungle_classic_trapdoor","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","ae2:network/cables/glass_black","twigs:mossy_cobblestone_bricks_cobblestone","mcwpaths:cobbled_deepslate_flagstone_path","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","everythingcopper:copper_ladder","railcraft:invar_gear","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","allthemods:constructionwand/iron_wand","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","rftoolsbuilder:shape_card_pump_dirt","cfm:black_trampoline","mcwfurnitures:acacia_cupboard_counter","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","minecraft:deepslate_tiles_from_polished_deepslate_stonecutting","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:magic_mystic_door","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","minecraft:dropper","farmersdelight:cooking/fried_rice","mcwbiomesoplenty:palm_end_table","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwfurnitures:stripped_acacia_bookshelf","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","create:crafting/kinetics/brass_hand","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","mcwbiomesoplenty:empyreal_bamboo_door","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","immersiveengineering:crafting/plate_nickel_hammering","mcwdoors:acacia_bamboo_door","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","productivebees:stonecutter/yucca_canvas_expansion_box","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","supplementaries:stonecutting/blackstone_tile","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","twigs:polished_rhyolite","immersiveengineering:crafting/wirecoil_redstone","additionallanterns:stone_bricks_chain","mcwbridges:mossy_cobblestone_bridge_pier","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_bricks_bridge","alltheores:nickel_rod","supplementaries:candle_holders/candle_holder_blue_dye","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","minecraft:acacia_stairs","mcwbiomesoplenty:dead_mystic_door","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:stripped_palm_glass_table","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwroofs:jungle_planks_steep_roof","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","enderio:black_paper","twigs:polished_tuff_stonecutting","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","mcwbiomesoplenty:pine_japanese_door","ae2:network/cables/dense_smart_fluix","handcrafted:acacia_corner_trim","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwbiomesoplenty:redwood_glass_door","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","enderio:copper_alloy_nugget","mcwroofs:orange_concrete_upper_lower_roof","pneumaticcraft:compressed_iron_leggings","productivebees:stonecutter/hellbark_canvas_hive","mcwbiomesoplenty:palm_table","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwbridges:bamboo_bridge","mcwbiomesoplenty:mahogany_western_door","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","mcwbiomesoplenty:maple_beach_door","mcwwindows:metal_pane_window","rftoolsutility:matter_receiver","mcwbiomesoplenty:magic_four_panel_door","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","mcwfurnitures:jungle_large_drawer","mcwfurnitures:acacia_drawer_counter","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:hellbark_four_panel_door","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","productivebees:stonecutter/crimson_canvas_expansion_box","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","mcwfurnitures:acacia_striped_chair","mcwdoors:acacia_paper_door","mcwtrpdoors:bamboo_beach_trapdoor","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","silentgear:blaze_gold_dust","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwbiomesoplenty:palm_barn_door","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","mcwbiomesoplenty:palm_mystic_door","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","mcwbiomesoplenty:maple_japanese2_door","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","rftoolsstorage:storage_module0","immersiveengineering:crafting/fluid_placer","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwdoors:acacia_whispering_door","mcwbiomesoplenty:jacaranda_cottage_door","mcwfurnitures:stripped_jungle_table","rftoolsutility:matter_transmitter","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwbiomesoplenty:palm_planks_path","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwtrpdoors:acacia_whispering_trapdoor","travelersbackpack:magma_cube","allthetweaks:ender_pearl_block","croptopia:beetroot_salad","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","mcwbiomesoplenty:hellbark_glass_door","forbidden_arcanus:deorum_lantern","everythingcopper:copper_door","enderio:redstone_filter_base","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","farmersdelight:cooking/glow_berry_custard","create:crafting/kinetics/red_seat","mcwroofs:orange_concrete_steep_roof","mekanism:jetpack","supplementaries:bamboo_spikes","mcwroofs:acacia_roof","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","minecraft:polished_blackstone_brick_wall","mcwbiomesoplenty:stripped_palm_log_four_window","mcwbiomesoplenty:empyreal_mystic_door","handcrafted:spruce_bench","twigs:spruce_table","mcwroofs:gutter_base_light_blue","create:crafting/kinetics/orange_seat","rftoolsutility:syringe","gtceu:shaped/block_compress_steel","minecraft:diamond_block","mekanism:configurator","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","mcwbiomesoplenty:palm_cupboard_counter","mcwroofs:jungle_planks_upper_steep_roof","minecraft:yellow_stained_glass","rftoolsutility:spawner","cfm:green_cooler","additionallanterns:normal_lantern_magenta","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","farmersdelight:horse_feed","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","immersiveengineering:crafting/strip_curtain","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","computercraft:turtle_normal_overlays/turtle_trans_overlay","utilitarian:snad/snad","rftoolsstorage:modular_storage","handcrafted:bricks_corner_trim","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:white_bed","mcwbiomesoplenty:snowblossom_hedge","minecraft:spruce_slab","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","handcrafted:green_cushion","cfm:dye_green_picket_gate","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","rftoolsbuilder:shape_card_pump","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:acacia_paper_trapdoor","bigreactors:blasting/yellorium_from_raw","mekanism:electrolytic_separator","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:stripped_palm_end_table","cfm:red_trampoline","mcwbiomesoplenty:pine_paper_door","botania:manasteel_shears","ae2:network/cables/smart_green","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","supplementaries:soap/piston","cfm:acacia_upgraded_gate","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","twilightdelight:obsidian","domum_ornamentum:green_cobblestone_extra","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","twigs:cracked_bricks","supplementaries:slice_map","mcwbridges:brick_bridge_pier","connectedglass:borderless_glass_green_pane2","silentgear:crimson_iron_ingot_from_block","mcwdoors:acacia_mystic_door","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","alltheores:invar_plate","occultism:crafting/butcher_knife","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","silentgear:crimson_iron_nugget","blue_skies:trough","botania:lens_normal","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","dyenamics:ultramarine_dye","mcwroofs:bricks_steep_roof","cfm:gray_grill","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwbiomesoplenty:magic_barn_door","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","minecraft:purple_dye","minecraft:deepslate_tile_wall_from_polished_deepslate_stonecutting","mcwfurnitures:jungle_modern_chair","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","modularrouters:modular_router","delightful:knives/nickel_knife","mcwfurnitures:stripped_jungle_coffee_table","create:brass_bars_from_ingots_brass_stonecutting","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","mcwfurnitures:stripped_jungle_double_drawer","mcwbiomesoplenty:palm_bamboo_door","ae2:network/cables/smart_black","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","minecraft:observer","minecraft:mossy_cobblestone_wall_from_mossy_cobblestone_stonecutting","cfm:gray_kitchen_sink","minecraft:polished_deepslate_wall","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwtrpdoors:acacia_bark_trapdoor","mcwbiomesoplenty:hellbark_waffle_door","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","mcwbridges:spruce_log_bridge_middle","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","mcwbiomesoplenty:palm_swamp_door","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwwindows:birch_log_parapet","ad_astra:iron_factory_block","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","mcwroofs:black_concrete_attic_roof","minecraft:charcoal","handcrafted:blue_bowl","mcwdoors:bamboo_stable_head_door","twigs:tuff_wall","minecraft:polished_blackstone_brick_slab","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:acacia_drawer","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","twigs:polished_tuff_bricks_from_tuff_stonecutting","supplementaries:fodder","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","biomesoplenty:brimstone_cluster","mcwwindows:crimson_blinds","mcwbiomesoplenty:fir_swamp_door","dyenamics:banner/aquamarine_banner","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","create:crafting/kinetics/super_glue","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","rftoolsbuilder:shape_card_liquid","mcwbiomesoplenty:dead_stable_head_door","mcwroofs:gutter_base_blue","minecraft:gray_dye","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwbiomesoplenty:palm_tropical_trapdoor","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","mcwwindows:nether_brick_arrow_slit","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","allthemodium:allthemodium_ingot_from_dust_blasting","botania:travel_belt","bigreactors:reprocessor/fluidinjector","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:maple_bamboo_door","mcwroofs:acacia_upper_lower_roof","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","mcwbiomesoplenty:palm_glass_trapdoor","silentgear:blaze_gold_nugget","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwbiomesoplenty:redwood_bamboo_door","handcrafted:spruce_desk","immersiveengineering:crafting/hempcrete","mcwwindows:oak_plank_parapet","minecraft:iron_sword","minecraft:spruce_fence","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","silentgear:crimson_iron_dust_smelting","additionallanterns:normal_lantern_red","mcwbiomesoplenty:magic_japanese2_door","domum_ornamentum:purple_brick_extra","mcwwindows:one_way_glass_pane","mcwbiomesoplenty:hellbark_stable_door","cfm:green_sofa","mcwfurnitures:stripped_acacia_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","minecraft:cut_copper_stairs_from_cut_copper_stonecutting","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","productivetrees:sawmill","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","everythingcopper:copper_anvil","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","productivebees:stonecutter/maple_canvas_hive","mcwbiomesoplenty:mahogany_picket_fence","mcwdoors:acacia_barn_glass_door","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","supplementaries:candle_holders/candle_holder_green_dye","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","handcrafted:jungle_table","supplementaries:planter_rich","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","productivebees:stonecutter/dead_canvas_hive","mcwwindows:magenta_curtain","minecraft:conduit","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","sophisticatedstorage:spruce_chest","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","biomesoplenty:palm_slab","mcwbiomesoplenty:empyreal_barn_door","sgjourney:sandstone_hieroglyphs","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","supplementaries:flags/flag_red","cfm:dye_blue_picket_fence","mcwtrpdoors:acacia_classic_trapdoor","mcwroofs:magenta_concrete_upper_lower_roof","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:cyan_concrete_steep_roof","mcwbiomesoplenty:hellbark_nether_door","mcwbiomesoplenty:stripped_palm_double_wardrobe","supplementaries:stone_lamp","minecolonies:pea_soup","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","handcrafted:black_cushion","pneumaticcraft:gilded_upgrade","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","minecraft:yellow_concrete_powder","mcwbiomesoplenty:empyreal_paper_door","mcwwindows:deepslate_window","mcwroofs:spruce_planks_upper_lower_roof","mcwfurnitures:acacia_modern_chair","minecraft:comparator","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","minecraft:netherite_boots_smithing","mcwdoors:bamboo_mystic_door","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","mcwbiomesoplenty:magic_bark_glass_door","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","rftoolspower:powercell_card","delightful:knives/silver_knife","mcwwindows:acacia_blinds","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwdoors:acacia_waffle_door","mcwroofs:roofing_hammer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","twigs:copper_pillar_stonecutting","mcwbiomesoplenty:palm_bookshelf_cupboard","blue_skies:lunar_bookshelf","sophisticatedstorage:packing_tape","immersiveengineering:crafting/buzzsaw","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","create:crafting/kinetics/vertical_gearbox","productivebees:stonecutter/redwood_canvas_hive","mcwlights:double_street_lamp","railcraft:tin_gear","minecraft:tnt","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","minecraft:black_dye","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","minecraft:deepslate_brick_stairs_from_polished_deepslate_stonecutting","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","alltheores:invar_dust_from_alloy_blending","mekanism:steel_casing","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:umbran_bamboo_door","ae2:network/cells/item_cell_housing","minecraft:cobblestone_wall_from_cobblestone_stonecutting","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","domum_ornamentum:light_gray_brick_extra","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","securitycraft:reinforced_lime_stained_glass_pane_from_dye","farmersdelight:rice","supplementaries:globe_sepia","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","additionallanterns:bone_chain","allthecompressed:compress/bone_block_1x","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","sophisticatedstorage:storage_link","biomesoplenty:palm_fence_gate","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","cfm:door_mat","mcwbiomesoplenty:palm_planks_roof","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:item_shelf","supplementaries:stone_tile","mcwbiomesoplenty:willow_barn_glass_door","rftoolsbuilder:blue_shield_template_block","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","rftoolsbuilder:space_chamber","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","supplementaries:flags/flag_white","comforts:hammock_black","silentgear:coating_smithing_template","handcrafted:jungle_shelf","enderio:yeta_wrench","minecraft:grindstone","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:palm_planks_attic_roof","mcwwindows:metal_window","domum_ornamentum:brown_brick_extra","minecraft:glass_pane","supplementaries:timber_brace","mcwbiomesoplenty:maple_barn_glass_door","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","bigreactors:reactor/reinforced/activetap_fe","mcwlights:copper_chain","mcwroofs:acacia_planks_steep_roof","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","additionallanterns:normal_lantern_colorless","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwbiomesoplenty:mahogany_paper_door","cfm:orange_kitchen_drawer","mcwroofs:spruce_planks_top_roof","enderio:cold_fire_igniter","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","handcrafted:spruce_pillar_trim","minecraft:leather_boots","railcraft:tank_detector","railcraft:steel_spike_maul","ad_astra:etrionic_blast_furnace","pneumaticcraft:classify_filter","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","mcwbridges:mossy_stone_brick_bridge","wirelesschargers:basic_wireless_block_charger","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","mcwtrpdoors:acacia_barred_trapdoor","mcwlights:pink_paper_lamp","mcwbiomesoplenty:magic_nether_door","mcwbiomesoplenty:stripped_palm_lower_bookshelf_drawer","pneumaticcraft:wall_lamp_inverted_light_blue","immersiveengineering:crafting/wire_aluminum","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","rftoolscontrol:workbench","aether:iron_gloves","mcwbiomesoplenty:palm_modern_wardrobe","minecraft:dye_black_carpet","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","mcwfurnitures:acacia_bookshelf_drawer","corail_woodcutter:oak_woodcutter","mcwbiomesoplenty:palm_planks_steep_roof","occultism:crafting/dictionary_of_spirits","cfm:oak_table","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","croptopia:tomato_juice","pneumaticcraft:stomp_upgrade","minecraft:magma_block","mcwwindows:crimson_stem_parapet","minecraft:spruce_pressure_plate","cfm:acacia_park_bench","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","connectedglass:clear_glass_black_pane2","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivebees:stonecutter/jungle_canvas_expansion_box","aether:book_of_lore","mcwfurnitures:stripped_acacia_triple_drawer","minecraft:brush","railcraft:steel_pickaxe","mcwroofs:jungle_planks_roof","handcrafted:spruce_nightstand","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","aether:aether_iron_nugget_from_smelting","mcwfurnitures:acacia_wardrobe","sfm:cable","mcwbiomesoplenty:palm_japanese_door","minecraft:hay_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","alltheores:ruby_from_hammer_crushing","delightful:knives/invar_knife","create:crafting/kinetics/vertical_gearboxfrom_conversion","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:jacaranda_barn_glass_door","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbridges:mossy_cobblestone_bridge_stair","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","twigs:mossy_cobblestone_brick_slab_from_mossy_cobblestone_stonecutting","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:fir_barn_door","mcwroofs:blue_concrete_top_roof","bambooeverything:bamboo_ladder","evilcraft:smelting/hardened_blood_shard","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","handcrafted:acacia_fancy_bed","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:cut_copper","biomesoplenty:dead_boat","handcrafted:acacia_drawer","supplementaries:flags/flag_orange","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwbiomesoplenty:willow_japanese2_door","domum_ornamentum:brick_extra","travelersbackpack:iron","minecraft:smooth_sandstone_stairs","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","mcwtrpdoors:bamboo_barrel_trapdoor","mcwbiomesoplenty:pine_barn_glass_door","mcwpaths:mossy_stone_running_bond","mcwpaths:stone_flagstone_slab","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","reliquary:uncrafting/redstone","mcwwindows:granite_louvered_shutter","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/generator","securitycraft:sentry","biomesoplenty:palm_wood","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","twilightforest:wood/acacia_banister","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwfurnitures:stripped_acacia_lower_bookshelf_drawer","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","mcwbiomesoplenty:stripped_palm_coffee_table","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:fir_mystic_door","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwbiomesoplenty:fir_beach_door","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mekanism:jetpack_armored","mcwlights:orange_paper_lamp","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","ae2:network/cables/dense_covered_black","mcwroofs:gray_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","create:crafting/curiosities/peculiar_bell","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","mcwpaths:mossy_cobblestone_basket_weave_paving","delightful:knives/brass_knife","mcwbiomesoplenty:umbran_mystic_door","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","immersiveengineering:crafting/metal_ladder_none","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwroofs:acacia_planks_attic_roof","domum_ornamentum:white_brick_extra","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwfurnitures:acacia_double_drawer_counter","minecraft:mossy_cobblestone_stairs","mcwfences:acacia_horse_fence","immersiveengineering:crafting/chute_iron","comforts:hammock_green","mcwfences:spruce_hedge","sfm:labelgun","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","connectedglass:scratched_glass_blue2","immersiveengineering:crafting/clinker_brick_quoin","mcwbiomesoplenty:palm_barred_trapdoor","domum_ornamentum:pink_floating_carpet","botania:virus_nullodermal","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbridges:rope_spruce_bridge","supplementaries:end_stone_lamp","allthecompressed:compress/deepslate_1x","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","mcwbiomesoplenty:stripped_palm_stool_chair","cfm:red_kitchen_drawer","mcwbiomesoplenty:palm_bookshelf","cfm:stripped_jungle_desk","botania:manasteel_shovel","handcrafted:oven","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwbiomesoplenty:palm_glass_table","mcwpaths:cobbled_deepslate_crystal_floor_slab","minecraft:green_carpet","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","rftoolsbuilder:shape_card_quarry_fortune","mcwbiomesoplenty:magic_stable_door","mcwlights:jungle_tiki_torch","immersiveengineering:crafting/plate_aluminum_hammering","twigs:stone_column_stonecutting","connectedglass:borderless_glass_blue_pane2","delightful:knives/bronze_knife","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwbiomesoplenty:mahogany_classic_door","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","twigs:mossy_cobblestone_bricks_stonecutting","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","mcwbiomesoplenty:willow_nether_door","minecraft:stone_slab","mcwbiomesoplenty:magic_classic_door","pneumaticcraft:compressed_bricks","mcwpaths:brick_flagstone","productivebees:stonecutter/comb_canvas_hive","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","silentgear:blaze_gold_ingot_from_block","mcwbiomesoplenty:maple_four_panel_door","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:acacia_slab","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","travelersbackpack:backpack_tank","minecraft:jungle_stairs","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","additionallanterns:normal_lantern_light_blue","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","croptopia:campfire_molasses","mcwdoors:acacia_stable_door","securitycraft:reinforced_blue_stained_glass_pane_from_dye","mcwbiomesoplenty:pine_four_panel_door","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","pneumaticcraft:wall_lamp_green","minecraft:blue_stained_glass_pane_from_glass_pane","mcwroofs:thatch_upper_lower_roof","silentgear:crimson_iron_ingot_from_nugget","mcwbiomesoplenty:hellbark_cottage_door","additionallanterns:netherite_chain","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","mcwtrpdoors:bamboo_bark_trapdoor","mcwbiomesoplenty:stripped_palm_modern_wardrobe","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","silentgear:upgrade_base","create:acacia_window","croptopia:melon_juice","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","mcwbiomesoplenty:jacaranda_glass_door","securitycraft:mine","rftoolsbuilder:space_chamber_controller","mcwfurnitures:stripped_acacia_chair","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","minecraft:mossy_cobblestone_wall","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","mcwwindows:one_way_glass","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mekanism:electrolytic_core","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","biomesoplenty:brimstone_bricks_from_brimstone_stonecutting","minecraft:oak_chest_boat","mcwpaths:mossy_stone_flagstone_path","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","mcwbridges:acacia_bridge_pier","minecraft:stone_pressure_plate","mcwbiomesoplenty:hellbark_classic_door","mcwtrpdoors:print_beach","mcwroofs:spruce_planks_lower_roof","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","mcwbiomesoplenty:dead_glass_door","mcwbiomesoplenty:palm_double_drawer_counter","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwbiomesoplenty:willow_modern_door","pneumaticcraft:logistics_core","create:crafting/kinetics/yellow_seat","everythingcopper:copper_helmet","mcwbiomesoplenty:palm_glass_door","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","mcwbridges:rope_acacia_bridge","minecraft:mangrove_boat","mcwdoors:bamboo_whispering_door","minecraft:bread","minecraft:bone_meal_from_bone_block","mcwbiomesoplenty:redwood_western_door","allthecompressed:compress/hay_block_1x","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","alltheores:silver_rod","minecraft:lever","alltheores:osmium_plate","immersiveengineering:crafting/wirecoil_structure_steel","mcwbiomesoplenty:dead_classic_door","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","mcwbiomesoplenty:umbran_swamp_door","mcwbiomesoplenty:redwood_paper_door","mcwtrpdoors:acacia_blossom_trapdoor","mcwbiomesoplenty:willow_pyramid_gate","mcwbiomesoplenty:mahogany_bark_glass_door","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","additionallanterns:normal_lantern_brown","mcwfurnitures:acacia_desk","minecraft:black_banner","tombstone:dark_marble","minecraft:iron_shovel","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwwindows:stone_brick_arrow_slit","mcwpaths:cobbled_deepslate_windmill_weave_stairs","chimes:amethyst_chimes","ae2:smelting/smooth_sky_stone_block","mcwbiomesoplenty:mahogany_cottage_door","mcwfurnitures:jungle_desk","mcwbiomesoplenty:umbran_waffle_door","mcwbiomesoplenty:palm_bark_trapdoor","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","mcwroofs:jungle_planks_lower_roof","handcrafted:acacia_chair","croptopia:ham_sandwich","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","minecraft:polished_deepslate_slab","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","mcwbiomesoplenty:willow_swamp_door","securitycraft:disguise_module","mcwdoors:acacia_glass_door","mcwwindows:deepslate_four_window","mcwbiomesoplenty:fir_stable_head_door","pneumaticcraft:camo_applicator","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwwindows:dark_oak_louvered_shutter","mcwbiomesoplenty:redwood_barn_door","mcwtrpdoors:acacia_four_panel_trapdoor","domum_ornamentum:blockbarreldeco_standing","mcwfences:gothic_metal_fence","railcraft:polished_quarried_stone_from_quarried_stone_in_stonecutter","deeperdarker:echo_boat","minecraft:deepslate_bricks","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","supplementaries:gold_trapdoor","mcwbiomesoplenty:palm_attic_roof","mcwfences:birch_picket_fence","minecraft:cooked_chicken","mcwbiomesoplenty:palm_modern_door","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwdoors:bamboo_barn_door","mcwbiomesoplenty:maple_cottage_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","minecraft:nether_brick","mcwbiomesoplenty:empyreal_modern_door","mcwdoors:jungle_waffle_door","handcrafted:spruce_dining_bench","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwroofs:spruce_planks_roof","minecraft:spruce_door","mcwbiomesoplenty:palm_counter","mcwbridges:asian_red_bridge","create:crafting/kinetics/propeller","mcwfurnitures:stripped_acacia_large_drawer","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","immersiveengineering:crafting/fluid_pipe","mcwbiomesoplenty:magic_highley_gate","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","rftoolspower:pearl_injector","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","pylons:harvester_pylon","minecraft:wheat","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","mcwdoors:bamboo_swamp_door","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","cfm:acacia_kitchen_counter","deepresonance:radiation_suit_boots","minecraft:bow","supplementaries:wrench","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","delightful:knives/aluminum_knife","handcrafted:bear_trophy","mcwdoors:bamboo_japanese_door","cfm:purple_kitchen_sink","cfm:black_cooler","minecraft:jungle_slab","handcrafted:acacia_counter","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","minecraft:dark_oak_boat","mcwpaths:mossy_stone_crystal_floor_slab","aquaculture:gold_hook","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:acacia_large_drawer","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:pine_stable_door","minecraft:melon_seeds","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","cfm:stripped_acacia_desk","minecraft:firework_rocket_simple","create:crafting/kinetics/encased_fan","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","mcwbiomesoplenty:magic_barn_glass_door","mcwbiomesoplenty:redwood_mystic_door","enderio:enderios","pneumaticcraft:wall_lamp_inverted_orange","mcwbiomesoplenty:fir_bamboo_door","pneumaticcraft:wall_lamp_inverted_brown","supplementaries:pedestal","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwfurnitures:stripped_acacia_wardrobe","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","mcwbiomesoplenty:stripped_palm_double_drawer_counter","mcwdoors:metal_windowed_door","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","twigs:lamp","mcwfurnitures:stripped_acacia_modern_desk","mcwwindows:orange_curtain","minecraft:stone","dyenamics:bed/lavender_bed","railcraft:iron_tank_valve","mcwroofs:brown_concrete_lower_roof","mcwbridges:mossy_cobblestone_bridge","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","forbidden_arcanus:deorum_soul_lantern","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwbiomesoplenty:palm_coffee_table","crafting_on_a_stick:crafting_table","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","cfm:acacia_upgraded_fence","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","cfm:oak_park_bench","rftoolsutility:environmental_controller","productivebees:stonecutter/hellbark_canvas_expansion_box","mcwroofs:cyan_concrete_attic_roof","silentgear:crimson_iron_block","immersiveengineering:crafting/conveyor_basic","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","thermal_extra:crafting/iron_rod","mcwbridges:mossy_stone_bridge_pier","mcwbiomesoplenty:empyreal_horse_fence","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","xnet:controller","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","alltheores:lead_plate","additionallanterns:obsidian_chain","minecraft:polished_deepslate_wall_from_polished_deepslate_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwtrpdoors:acacia_ranch_trapdoor","mcwlights:cross_lantern","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","immersiveengineering:crafting/hemp_fabric","minecraft:green_candle","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","enderio:wood_gear_corner","rftoolsbase:manual","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","mcwbiomesoplenty:palm_double_wardrobe","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","additionallanterns:diamond_chain","supplementaries:stonecutting/stone_tile","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","additionallanterns:normal_lantern_purple","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","enderio:dark_steel_trapdoor","minecraft:dye_black_wool","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","ad_astra:small_green_industrial_lamp","rftoolsbuilder:shape_card_quarry_silk","pneumaticcraft:reinforced_brick_from_slab","minecraft:cooked_beef","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","supplementaries:flags/flag_gray","mcwpaths:mossy_stone_flagstone_stairs","mcwlights:yellow_paper_lamp","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","railcraft:iron_tank_gauge","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","cfm:magenta_grill","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","mcwbiomesoplenty:mahogany_japanese_door","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","utilitarian:utility/acacia_logs_to_pressure_plates","mcwbiomesoplenty:umbran_four_panel_door","handcrafted:spruce_corner_trim","mcwdoors:garage_white_door","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","mcwpaths:stone_running_bond","minecraft:deepslate_brick_wall_from_polished_deepslate_stonecutting","trashcans:liquid_trash_can","mcwbiomesoplenty:redwood_beach_door","ae2:network/cables/smart_blue","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","mcwbiomesoplenty:palm_upper_lower_roof","immersiveengineering:crafting/plate_gold_hammering","botania:turntable","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","domum_ornamentum:black_brick_extra","supplementaries:daub","railcraft:steel_chestplate","alltheores:zinc_dust_from_hammer_crushing","minecraft:netherite_scrap","mcwfurnitures:stripped_jungle_end_table","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","ad_astra:iron_rod","mcwbiomesoplenty:palm_covered_desk","mcwbiomesoplenty:magic_cottage_door","mcwpaths:stone_flagstone","bigreactors:energizer/casing","aether:chainmail_boots_repairing","simplylight:illuminant_block_toggle","xnet:connector_blue","supplementaries:blackstone_tile","mcwbiomesoplenty:hellbark_highley_gate","minecraft:deepslate_tile_stairs_from_polished_deepslate_stonecutting","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","mcwbiomesoplenty:stripped_palm_bookshelf_cupboard","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","immersiveengineering:crafting/coil_lv","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","ad_astra:small_blue_industrial_lamp","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwpaths:mossy_stone_running_bond_path","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","minecraft:acacia_wood","aether:skyroot_note_block","mcwbiomesoplenty:maple_tropical_door","mcwbiomesoplenty:mahogany_beach_door","pneumaticcraft:logistics_configurator","create:cut_tuff_from_stone_types_tuff_stonecutting","bigreactors:smelting/yellorium_from_raw","mcwtrpdoors:print_tropical","rftoolsutility:dialing_device","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"],toBeDisplayed:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwfurnitures:stripped_jungle_desk","mcwfurnitures:stripped_acacia_counter","biomesoplenty:fir_boat","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwdoors:bamboo_stable_door","minecraft:kjs/structurecompass_structure_compass","dyenamics:banner/lavender_banner","minecraft:stonecutter","littlelogistics:seater_barge","handcrafted:spider_trophy","minecraft:bone_block","alltheores:gold_dust_from_hammer_crushing","mcwbiomesoplenty:willow_waffle_door","mcwpaths:cobbled_deepslate_crystal_floor_path","croptopia:shaped_beef_stew","minecraft:melon","ae2:network/cables/dense_smart_green","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","mcwtrpdoors:acacia_barrel_trapdoor","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mcwfurnitures:stripped_acacia_bookshelf_cupboard","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","mekanism:chemical_tank/basic","create:crafting/kinetics/light_gray_seat","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","dyenamics:maroon_candle","mcwroofs:acacia_upper_steep_roof","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","buildinggadgets2:gadget_cut_paste","mcwtrpdoors:acacia_barn_trapdoor","reliquary:mob_charm_fragments/cave_spider","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","rftoolsbuilder:mover_status","alltheores:brass_plate","simplylight:illuminant_light_blue_block_dyed","aether:iron_gloves_repairing","railcraft:controller_circuit","twigs:polished_rhyolite_stonecutting","handcrafted:deepslate_corner_trim","minecraft:spruce_sign","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","create:crafting/logistics/brass_tunnel","create:crafting/kinetics/gearbox","sophisticatedstorage:copper_to_iron_tier_upgrade","mcwbiomesoplenty:umbran_modern_door","handcrafted:spruce_side_table","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwbiomesoplenty:redwood_nether_door","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","mcwroofs:acacia_planks_top_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","elevatorid:elevator_white","supplementaries:bubble_blower","naturalist:glow_goop","biomesoplenty:palm_fence","supplementaries:crank","minecraft:recovery_compass","biomesoplenty:palm_button","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","minecraft:magenta_dye_from_blue_red_white_dye","alltheores:iron_plate","mcwroofs:acacia_planks_upper_lower_roof","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","supplementaries:netherite_trapdoor","productivebees:stonecutter/fir_canvas_expansion_box","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","cfm:acacia_cabinet","littlelogistics:rapid_hopper","mcwbiomesoplenty:willow_bark_glass_door","railcraft:energy_minecart","mcwbiomesoplenty:maple_nether_door","mcwbiomesoplenty:empyreal_classic_door","rftoolsutility:destination_analyzer","twigs:bamboo_thatch","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwbiomesoplenty:palm_modern_chair","immersiveengineering:crafting/rockcutter","minecraft:cut_sandstone_slab","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mekanism:energy_tablet","mcwbiomesoplenty:hellbark_barn_door","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:palm_steep_roof","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","minecraft:acacia_door","travelersbackpack:diamond_tier_upgrade","additionallanterns:mossy_cobblestone_chain","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","mcwfurnitures:acacia_lower_triple_drawer","cfm:oak_kitchen_counter","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","mekanism:factory/basic/infusing","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:dye_green_bed","alltheores:platinum_dust_from_hammer_crushing","railcraft:any_detector","botania:mana_distributor","biomesoplenty:mahogany_boat","quark:tweaks/crafting/utility/misc/charcoal_to_black_dye","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","minecraft:wild_armor_trim_smithing_template_smithing_trim","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","mcwroofs:stone_bricks_upper_steep_roof","biomesoplenty:palm_chest_boat","mcwtrpdoors:bamboo_mystic_trapdoor","minecraft:stone_button","mcwbiomesoplenty:redwood_bark_glass_door","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","minecraft:tinted_glass","pneumaticcraft:regulator_tube_module","minecraft:sandstone","minecraft:lodestone","mcwbiomesoplenty:umbran_japanese2_door","occultism:crafting/magic_lamp_empty","handcrafted:acacia_pillar_trim","mcwbiomesoplenty:palm_planks_upper_steep_roof","railcraft:brass_ingot_crafted_with_ingots","minecraft:cut_copper_stairs_from_copper_block_stonecutting","domum_ornamentum:blue_brick_extra","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","mcwbiomesoplenty:palm_large_drawer","pneumaticcraft:transfer_gadget","silentgear:blaze_gold_block","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","enderio:extraction_speed_upgrade_1","create:industrial_iron_block_from_ingots_iron_stonecutting","quark:building/crafting/furnaces/deepslate_furnace","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","tombstone:grave_plate","mcwbiomesoplenty:palm_planks_top_roof","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","productivebees:stonecutter/oak_canvas_hive","cfm:gray_trampoline","mcwbiomesoplenty:hellbark_beach_door","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","mcwbiomesoplenty:umbran_classic_door","comforts:hammock_to_green","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","littlelogistics:barrel_barge","croptopia:fried_calamari","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:hellbark_bark_glass_door","cfm:spruce_bedside_cabinet","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","xnet:connector_routing","blue_skies:glowing_nature_stone","mcwwindows:warped_louvered_shutter","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","mcwbiomesoplenty:umbran_stable_head_door","twilightforest:wood/jungle_banister","productivebees:stonecutter/palm_canvas_expansion_box","mcwbiomesoplenty:palm_bark_glass_door","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","farmersdelight:barbecue_stick","immersiveengineering:crafting/shield","mcwbiomesoplenty:umbran_cottage_door","utilitix:directional_highspeed_rail","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","mcwbiomesoplenty:palm_roof","immersiveengineering:crafting/blastbrick","dyenamics:ultramarine_candle","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","xnet:antenna_base","mcwbiomesoplenty:pine_stable_head_door","cfm:green_grill","minecraft:cooked_chicken_from_smoking","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","cfm:orange_grill","dyenamics:fluorescent_candle","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","cfm:stripped_acacia_park_bench","rftoolsutility:crafter1","cfm:jungle_desk","minecraft:mangrove_chest_boat","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mekanismgenerators:generator/wind","mcwroofs:light_blue_concrete_lower_roof","mcwfurnitures:jungle_drawer","twigs:chiseled_bricks_stonecutting","create:crafting/kinetics/fluid_tank","mcwroofs:purple_striped_awning","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","mcwbiomesoplenty:palm_japanese2_door","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","dyenamics:lavender_candle","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwfences:blackstone_brick_railing_gate","mcwtrpdoors:bamboo_tropical_trapdoor","mcwbiomesoplenty:maple_japanese_door","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwpaths:mossy_stone_running_bond_slab","minecraft:golden_pickaxe","handcrafted:acacia_desk","mcwpaths:cobbled_deepslate_flagstone_stairs","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","mcwbiomesoplenty:palm_whispering_trapdoor","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwfurnitures:acacia_stool_chair","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","supplementaries:evilcraft/sign_post_undead","xnet:wireless_router","mcwroofs:gutter_base_gray","mcwfurnitures:stripped_acacia_modern_chair","simplylight:illuminant_light_gray_block_on_dyed","mcwwindows:dark_prismarine_brick_gothic","additionallanterns:normal_lantern_pink","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","mcwfurnitures:stripped_acacia_lower_triple_drawer","cfm:stripped_birch_kitchen_drawer","pneumaticcraft:compressed_bricks_from_tile_stonecutting","twigs:chiseled_bricks","enderio:stone_gear","silentgear:blaze_gold_dust_blasting","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","farmersdelight:tomato_crate","xnet:netcable_yellow","mcwlights:tavern_wall_lantern","mcwfurnitures:jungle_wardrobe","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","biomesoplenty:palm_sign","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","rftoolscontrol:tank","mcwwindows:gray_mosaic_glass_pane","additionallanterns:normal_lantern_orange","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwbiomesoplenty:willow_beach_door","minecraft:chiseled_stone_bricks_stone_from_stonecutting","croptopia:potato_chips","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","minecraft:netherite_sword_smithing","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","croptopia:shaped_toast_sandwich","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwtrpdoors:acacia_cottage_trapdoor","mcwwindows:oak_blinds","minecraft:green_bed","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwbiomesoplenty:empyreal_beach_door","farmersdelight:rice_bale","handcrafted:spruce_counter","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","comforts:hammock_to_black","railcraft:signal_circuit","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","evilcraft:special/vengeance_pickaxe","sophisticatedstorage:oak_chest_from_vanilla_chest","immersiveengineering:crafting/gunpart_barrel","mcwbiomesoplenty:mahogany_swamp_door","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","cfm:stripped_acacia_kitchen_counter","cfm:pink_cooler","rftoolsbuilder:vehicle_builder","twilightforest:twilight_oak_chest_boat","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","minecraft:smooth_sandstone_slab","mcwbiomesoplenty:umbran_barn_door","mcwbiomesoplenty:palm_double_drawer","ad_astra:blue_industrial_lamp","mcwbiomesoplenty:maple_modern_door","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","croptopia:chicken_and_dumplings","additionallanterns:emerald_chain","mekanism:transmitter/mechanical_pipe/basic","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwbiomesoplenty:empyreal_cottage_door","utilitix:tiny_charcoal_to_tiny","railcraft:polished_quarried_stone_from_quarried_stone","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","farmersdelight:cooking/dumplings","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","dyenamics:bed/bubblegum_bed","cfm:stripped_warped_mail_box","mcwbridges:spruce_rail_bridge","immersiveengineering:crafting/toolbox","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","sophisticatedstorage:spruce_limited_barrel_3","sophisticatedstorage:spruce_limited_barrel_2","sophisticatedstorage:spruce_limited_barrel_1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","forbidden_arcanus:clibano_core","sophisticatedstorage:spruce_limited_barrel_4","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","sophisticatedstorage:storage_tool","mcwbiomesoplenty:palm_barrel_trapdoor","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_bricks_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:iron_panel","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:blue_candle","railcraft:blast_furnace_bricks","pneumaticcraft:wall_lamp_pink","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","reliquary:uncrafting/glass_bottle","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:jacaranda_nether_door","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","mcwfurnitures:acacia_counter","biomesoplenty:orange_dye_from_burning_blossom","mcwbiomesoplenty:stripped_palm_covered_desk","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","utilitarian:utility/acacia_logs_to_doors","cfm:stripped_acacia_chair","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_bricks_stonecutting","mcwpaths:mossy_stone_windmill_weave_path","minecraft:gold_ingot_from_blasting_raw_gold","mcwbiomesoplenty:palm_striped_chair","minecraft:iron_nugget_from_blasting","railcraft:iron_tank_wall","productivebees:stonecutter/aspen_canvas_expansion_box","pneumaticcraft:liquid_hopper","handcrafted:wood_cup","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","aether:golden_dart","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","mcwroofs:acacia_attic_roof","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","croptopia:fried_chicken","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:mahogany_nether_door","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:fir_bark_glass_door","mcwlights:oak_ceiling_fan_light","rftoolsbuilder:shape_card_quarry","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","evilcraft:crafting/undead_planks","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","create:deepslate_from_stone_types_deepslate_stonecutting","xnet:netcable_blue","cfm:orange_trampoline","cfm:diving_board","wirelesschargers:basic_wireless_player_charger","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","cfm:rock_path","connectedglass:scratched_glass_black2","minecraft:wooden_hoe","minecraft:cooked_beef_from_campfire_cooking","mekanismgenerators:generator/heat","mcwbiomesoplenty:redwood_waffle_door","createoreexcavation:diamond_drill","immersiveengineering:crafting/axe_steel","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","productivebees:stonecutter/jungle_canvas_hive","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","minecraft:waxed_cut_copper_from_honeycomb","ad_astra:black_industrial_lamp","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","mcwbiomesoplenty:willow_barn_door","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","biomesoplenty:brimstone_bud","sophisticatedstorage:gold_to_netherite_tier_upgrade","aether:obsidian_from_bucket_freezing","minecraft:blue_dye","mcwbiomesoplenty:palm_desk","railcraft:steel_leggings","mekanism:energized_smelter","mcwbiomesoplenty:umbran_nether_door","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:piston","mcwfurnitures:jungle_coffee_table","mcwbiomesoplenty:maple_waffle_door","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","allthemodium:allthemodium_ingot_from_raw_smelting","simplylight:illuminant_yellow_block_on_dyed","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","mcwdoors:bamboo_four_panel_door","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwbiomesoplenty:dead_modern_door","mcwbiomesoplenty:fir_stable_door","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","mcwbiomesoplenty:palm_classic_door","mcwdoors:acacia_cottage_door","mcwlights:cross_wall_lantern","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","delightful:food/cooking/rock_candy","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwbiomesoplenty:magic_modern_door","handcrafted:stackable_book","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","minecraft:stone_stairs_from_stone_stonecutting","silentgear:sinew_fiber","mcwfences:crimson_curved_gate","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","supplementaries:candle_holders/candle_holder_red","mcwbiomesoplenty:palm_triple_drawer","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwbridges:acacia_rail_bridge","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:palm_tropical_door","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","handcrafted:creeper_trophy","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","minecraft:dye_blue_wool","mcwlights:covered_wall_lantern","mcwbiomesoplenty:palm_cottage_door","productivebees:stonecutter/oak_canvas_expansion_box","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","packingtape:tape","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","silentgear:crimson_iron_dust","mcwbiomesoplenty:pine_japanese2_door","enderio:dark_steel_nugget_to_ingot","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwbiomesoplenty:stripped_palm_triple_drawer","immersiveengineering:crafting/pickaxe_steel","pneumaticcraft:pressure_chamber_interface","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","travelersbackpack:diamond","dyenamics:persimmon_candle","mekanism:transmitter/universal_cable/advanced","cfm:stripped_warped_kitchen_drawer","mcwpaths:mossy_stone_crystal_floor_stairs","mcwbiomesoplenty:palm_drawer_counter","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","mcwtrpdoors:jungle_ranch_trapdoor","minecraft:black_carpet","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","mcwfurnitures:acacia_double_wardrobe","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","alltheores:brass_rod","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","mcwfurnitures:stripped_jungle_stool_chair","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mcwwindows:white_curtain","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwpaths:acacia_planks_path","cfm:light_gray_grill","create:crafting/kinetics/black_seat","cfm:oak_desk","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","everythingcopper:copper_hopper","mcwbiomesoplenty:dead_bark_glass_door","minecolonies:potato_soup","mcwbiomesoplenty:pine_mystic_door","mcwbiomesoplenty:fir_paper_door","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","cfm:dye_black_picket_gate","aether:skyroot_grindstone","mcwbiomesoplenty:fir_japanese_door","minecraft:green_stained_glass_pane_from_glass_pane","minecraft:cut_copper_slab","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbridges:dry_bamboo_bridge_pier","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","ad_astra:wrench","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwbiomesoplenty:palm_stool_chair","mcwfurnitures:stripped_acacia_bookshelf_drawer","cfm:red_cooler","handcrafted:jungle_dining_bench","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","aether:golden_pendant","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","cfm:acacia_desk_cabinet","rftoolsutility:matter_beamer","mcwbiomesoplenty:dead_nether_door","cfm:acacia_blinds","mcwbiomesoplenty:dead_paper_door","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","minecraft:carrot_on_a_stick","rftoolspower:blazing_agitator","railcraft:signal_capacitor_box","mcwwindows:spruce_curtain_rod","cfm:dye_blue_picket_gate","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_acacia_glass_table","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","handcrafted:acacia_shelf","mcwfurnitures:acacia_chair","mcwbiomesoplenty:palm_nether_door","enderio:dark_steel_nugget","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","croptopia:fruit_smoothie","mcwbiomesoplenty:fir_western_door","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwfurnitures:jungle_striped_chair","bambooeverything:bamboo_torch","dyenamics:honey_wool","mcwroofs:acacia_top_roof","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","additionallanterns:diamond_lantern","railcraft:steel_shears","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:mossy_cobblestone_slab_from_mossy_cobblestone_stonecutting","mcwfurnitures:acacia_glass_table","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","cfm:stripped_acacia_table","minecraft:compass","bigreactors:energizer/computerport","littlelogistics:vacuum_barge","allthemodium:allthemodium_apple","mcwbiomesoplenty:maple_mystic_door","mcwpaths:mossy_stone_running_bond_stairs","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","itemcollectors:basic_collector","minecraft:loom","supplementaries:sign_post_spruce","railcraft:steel_axe","domum_ornamentum:red_brick_extra","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","rftoolsutility:tank","aether:netherite_gloves_smithing","mcwbiomesoplenty:palm_wardrobe","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbiomesoplenty:mahogany_stable_head_door","mcwbridges:pliers","botania:red_string_comparator","mcwbiomesoplenty:empyreal_stockade_fence","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","alltheores:nickel_plate","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","handcrafted:blue_crockery_combo","mcwbiomesoplenty:palm_log_bridge_middle","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","mcwbiomesoplenty:palm_modern_desk","dyenamics:banner/rose_banner","enderio:redstone_alloy_nugget_to_ingot","everythingcopper:copper_pressure_plate","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","handcrafted:wood_plate","mcwbiomesoplenty:jacaranda_beach_door","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","mcwroofs:stone_top_roof","bigreactors:turbine/reinforced/passivefluidport_forge","travelersbackpack:horse","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","aether:diamond_gloves_repairing","cfm:brown_cooler","xnet:netcable_red","mcwroofs:acacia_planks_roof","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","minecraft:spruce_trapdoor","mcwwindows:dark_prismarine_parapet","dyenamics:amber_candle","bigreactors:reprocessor/wasteinjector","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","handcrafted:goat_trophy","paraglider:paraglider","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","mcwpaths:cobbled_deepslate_flagstone","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","mcwbiomesoplenty:mahogany_modern_door","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","utilitarian:utility/acacia_logs_to_boats","buildinggadgets2:gadget_exchanging","mcwbiomesoplenty:stripped_palm_desk","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","handcrafted:spruce_cupboard","create:crafting/kinetics/fluid_valve","supplementaries:crystal_display","minecraft:composter","minecraft:sandstone_stairs","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","mcwbiomesoplenty:palm_barn_glass_door","cfm:acacia_chair","aether:aether_gold_nugget_from_blasting","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","domum_ornamentum:cyan_floating_carpet","minecraft:acacia_sign","minecraft:coarse_dirt","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwroofs:stone_bricks_steep_roof","mcwwindows:red_curtain","create:crafting/logistics/brass_funnel","minecraft:fermented_spider_eye","mcwbiomesoplenty:stripped_palm_drawer_counter","mcwbiomesoplenty:palm_rail_bridge","mcwwindows:light_gray_mosaic_glass","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","supplementaries:flags/flag_black","mcwwindows:acacia_louvered_shutter","minecraft:deepslate_bricks_from_polished_deepslate_stonecutting","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","cfm:light_gray_cooler","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","allthemodium:allthemodium_dust_from_ore_crushing","mcwroofs:bricks_attic_roof","mcwdoors:jungle_whispering_door","reliquary:uncrafting/spider_eye","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","mcwbiomesoplenty:dead_waffle_door","twigs:bamboo_mat","connectedglass:borderless_glass_black2","railcraft:iron_gear","handcrafted:acacia_table","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","mcwfurnitures:stripped_acacia_drawer_counter","handcrafted:oak_pillar_trim","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","securitycraft:redstone_module","minecraft:lime_dye","mcwbiomesoplenty:pine_bamboo_door","ad_astra:green_flag","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","biomesoplenty:palm_door","supplementaries:flags/flag_purple","create:framed_glass_from_glass_colorless_stonecutting","sophisticatedstorage:jungle_barrel","botania:quartz_sunny","productivebees:stonecutter/maple_canvas_expansion_box","mcwfurnitures:stripped_acacia_cupboard_counter","mcwbiomesoplenty:dead_swamp_door","biomesoplenty:brimstone_fumarole","railcraft:signal_lamp","cfm:jungle_blinds","reliquary:uncrafting/sugar","tombstone:bone_needle","mcwpaths:mossy_stone_windmill_weave_slab","minecraft:stone_brick_wall","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","mcwbiomesoplenty:redwood_classic_door","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","sophisticatedstorage:jungle_limited_barrel_3","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","minecraft:cyan_dye","mcwwindows:acacia_window2","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","evilcraft:crafting/dark_stick","handcrafted:skeleton_horse_trophy","enderio:stone_gear_upgrade","mcwbiomesoplenty:magic_waffle_door","additionallanterns:warped_lantern","minecraft:spruce_boat","minecraft:gold_block","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","handcrafted:jungle_counter","mcwbiomesoplenty:redwood_cottage_door","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","handcrafted:black_sheet","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","cfm:acacia_bedside_cabinet","pneumaticcraft:compressed_bricks_from_tile","utilitix:stone_wall_stonecutting","cfm:spruce_kitchen_counter","travelersbackpack:warden","ad_astra:gas_tank","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","additionallanterns:copper_lantern","twilightforest:canopy_chest_boat","mcwroofs:jungle_planks_attic_roof","mcwpaths:spruce_planks_path","bambooeverything:dry_bamboo","mcwlights:golden_small_chandelier","mcwbiomesoplenty:willow_paper_door","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","cfm:crimson_mail_box","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","twigs:soul_lamp","supplementaries:deepslate_lamp","rftoolsutility:screen_controller","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","mcwbiomesoplenty:redwood_swamp_door","farmersdelight:cooking/baked_cod_stew","immersiveengineering:crafting/wirecoil_structure_rope","mcwbiomesoplenty:jacaranda_western_door","alltheores:lead_rod","utilitix:stonecutter_cart","cfm:dye_green_picket_fence","mcwwindows:oak_log_parapet","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwfurnitures:stripped_acacia_modern_wardrobe","connectedglass:tinted_borderless_glass_green2","ad_astra:iron_plateblock","aquaculture:dark_oak_fish_mount","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","allthecompressed:compress/jungle_planks_1x","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","minecraft:iron_block","farmersdelight:spruce_cabinet","mcwlights:garden_light","mcwdoors:acacia_beach_door","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwpaths:mossy_cobblestone_diamond_paving","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","allthecompressed:compress/spruce_planks_1x","deeperdarker:warden_upgrade_smithing_template","mcwbridges:bamboo_bridge_pier","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","aether:iron_helmet_repairing","pneumaticcraft:reinforced_brick_wall","mcwroofs:acacia_planks_upper_steep_roof","mcwroofs:purple_concrete_upper_steep_roof","utilitarian:utility/acacia_logs_to_stairs","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","cfm:acacia_coffee_table","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","supplementaries:flute","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","reliquary:mob_charm_fragments/ghast","mcwfences:warped_horse_fence","farmersdelight:cooking/squid_ink_pasta","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","mcwbiomesoplenty:stripped_palm_modern_desk","mcwbiomesoplenty:palm_bookshelf_drawer","utilitarian:redstone_clock","mcwdoors:bamboo_classic_door","securitycraft:harming_module","minecraft:golden_boots","silentgear:bort_block","immersiveengineering:crafting/grit_sand","mcwbiomesoplenty:palm_lower_roof","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","create:crafting/kinetics/item_vault","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","rftoolspower:blazing_generator","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","mcwbiomesoplenty:mahogany_tropical_door","immersiveengineering:crafting/tinted_glass_lead_wire","minecraft:dye_green_carpet","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","enderio:dark_steel_grinding_ball","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","mcwbiomesoplenty:stripped_palm_drawer","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","mcwbiomesoplenty:maple_stable_head_door","rftoolsutility:counterplus_module","minecraft:polished_blackstone_brick_stairs","mcwbiomesoplenty:palm_planks_upper_lower_roof","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","minecraft:stick_from_bamboo_item","alltheores:bronze_plate","botania:swap_ring","mcwpaths:brick_crystal_floor_slab","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","mcwfurnitures:acacia_double_drawer","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","allthecompressed:compress/acacia_log_1x","pneumaticcraft:pressure_chamber_wall","minecraft:glass","mcwbiomesoplenty:pine_classic_door","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","minecraft:polished_deepslate_stairs_from_polished_deepslate_stonecutting","mcwroofs:brown_concrete_attic_roof","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwbiomesoplenty:pine_cottage_door","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","minecraft:light_blue_concrete_powder","minecraft:smooth_sandstone_stairs_from_smooth_sandstone_stonecutting","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","enderio:dark_steel_ladder","sophisticatedstorage:acacia_chest","mcwwindows:light_blue_mosaic_glass","ad_astra:green_industrial_lamp","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","chimes:bamboo_chimes","rftoolsstorage:storage_control_module","mekanism:factory/basic/smelting","mcwbiomesoplenty:pine_nether_door","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwbiomesoplenty:hellbark_barn_glass_door","mcwbiomesoplenty:palm_mystic_trapdoor","twigs:mossy_cobblestone_bricks","deepresonance:radiation_monitor","railcraft:steel_gear","aether:diamond_shovel_repairing","ad_astra:encased_iron_block","enderio:pulsating_alloy_nugget","bigreactors:smelting/graphite_from_charcoal","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","mcwbiomesoplenty:palm_four_panel_trapdoor","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","minecraft:spruce_chest_boat","utilitarian:utility/acacia_logs_to_slabs","xnet:netcable_green_dye","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:jacaranda_bamboo_door","handcrafted:bricks_pillar_trim","botania:quartz_lavender","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","mcwbiomesoplenty:redwood_four_panel_door","minecraft:acacia_fence","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","immersiveengineering:crafting/alu_wallmount","mcwbiomesoplenty:palm_lower_bookshelf_drawer","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","minecraft:glistering_melon_slice","create:crafting/kinetics/magenta_seat","simplylight:illuminant_magenta_block_toggle","handcrafted:spruce_fancy_bed","minecraft:acacia_fence_gate","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","handcrafted:spruce_chair","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","supplementaries:candle_holders/candle_holder_black_dye","bigreactors:crafting/raw_yellorium_component_to_storage","twigs:rhyolite_slab","immersiveengineering:crafting/stick_iron","mcwbiomesoplenty:stripped_palm_modern_chair","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","alltheores:aluminum_gear","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","pneumaticcraft:compressed_bricks_from_stone_stonecutting","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwfurnitures:acacia_table","railcraft:player_detector","create:crafting/appliances/copper_diving_boots","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","immersiveengineering:crafting/component_iron","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","botania:azulejo_0","mcwtrpdoors:bamboo_blossom_trapdoor","mcwbiomesoplenty:fir_tropical_door","sfm:fancy_to_cable","mcwbiomesoplenty:stripped_palm_table","aquaculture:heavy_hook","mcwbiomesoplenty:palm_western_door","minecraft:white_concrete_powder","biomesoplenty:dead_chest_boat","mcwwindows:acacia_window","croptopia:food_press","railcraft:mob_detector","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","enderio:enchanter","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwbiomesoplenty:willow_four_panel_door","mcwpaths:brick_honeycomb_paving","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","xnet:router","silentgear:material_grader","mcwfurnitures:stripped_jungle_modern_chair","pneumaticcraft:small_tank","mcwbiomesoplenty:willow_classic_door","ae2:network/cables/glass_green","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","aether:golden_gloves_repairing","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","create:tuff_pillar_from_stone_types_tuff_stonecutting","mcwpaths:brick_crystal_floor","handcrafted:acacia_side_table","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwdoors:acacia_swamp_door","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","minecraft:deepslate_brick_slab_from_polished_deepslate_stonecutting","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","railcraft:charge_terminal","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","minecraft:deepslate_tile_slab_from_polished_deepslate_stonecutting","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","croptopia:chocolate","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","domum_ornamentum:architectscutter","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","immersiveengineering:crafting/alu_fence","domum_ornamentum:red_floating_carpet","aether:iron_boots_repairing","minecraft:diamond_shovel","mcwdoors:bamboo_beach_door","mcwwindows:end_brick_gothic","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","mcwpaths:brick_running_bond_slab","aether:iron_ring","tombstone:impregnated_diamond","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","dyenamics:peach_concrete_powder","mcwbiomesoplenty:dead_beach_door","cfm:cyan_kitchen_sink","mcwroofs:jungle_planks_upper_lower_roof","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","mcwbiomesoplenty:mahogany_four_panel_door","utilitix:directional_rail","mcwfurnitures:stripped_acacia_covered_desk","mcwbiomesoplenty:fir_barn_glass_door","railcraft:copper_gear","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","dyenamics:mint_wool","connectedglass:borderless_glass_green2","mcwfences:dark_oak_curved_gate","mcwdoors:bamboo_bark_glass_door","mcwwindows:cherry_curtain_rod","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","minecraft:chest","alltheores:diamond_plate","mcwbiomesoplenty:stripped_palm_bookshelf","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwbiomesoplenty:magic_paper_door","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","mcwbiomesoplenty:stripped_palm_large_drawer","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","dyenamics:navy_dye","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwbiomesoplenty:palm_lower_triple_drawer","mcwbiomesoplenty:hellbark_stable_head_door","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:jacaranda_swamp_door","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwroofs:gutter_base_red","mcwtrpdoors:acacia_glass_trapdoor","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","productivebees:stonecutter/concrete_canvas_hive","mcwdoors:print_bamboo","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","aether:aether_gold_nugget_from_smelting","mcwroofs:cobblestone_steep_roof","securitycraft:reinforced_tinted_glass","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","domum_ornamentum:mossy_cobblestone_extra","mcwfurnitures:acacia_modern_desk","mcwbiomesoplenty:palm_bridge_pier","minecraft:copper_ingot_from_smelting_raw_copper","cfm:dye_black_picket_fence","mcwwindows:crimson_curtain_rod","comforts:sleeping_bag_green","securitycraft:universal_key_changer","comforts:sleeping_bag_to_green","minecraft:netherite_ingot","naturalist:bug_net","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_2","mcwbiomesoplenty:jacaranda_mystic_door","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","additionallanterns:normal_lantern_gray","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","mcwfurnitures:acacia_end_table","aether:chainmail_helmet_repairing","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mekanism:metallurgic_infuser","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","alltheores:aluminum_rod","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","minecraft:spruce_stairs","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","railcraft:bushing_gear_brass","mcwbiomesoplenty:palm_chair","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","cfm:cyan_cooler","railcraft:silver_gear","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwdoors:acacia_modern_door","mcwbiomesoplenty:mahogany_mystic_door","create:crafting/kinetics/whisk","mcwroofs:light_blue_concrete_top_roof","enderio:redstone_alloy_grinding_ball","mcwbiomesoplenty:umbran_beach_door","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","dyenamics:bubblegum_candle","mcwbiomesoplenty:umbran_bark_glass_door","mcwlights:iron_triple_candle_holder","mcwdoors:bamboo_tropical_door","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","mcwfurnitures:stripped_acacia_end_table","travelersbackpack:redstone","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","biomesoplenty:brimstone_brick_wall_from_brimstone_stonecutting","minecraft:acacia_planks","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwbiomesoplenty:fir_nether_door","chimes:glass_bells","mcwwindows:iron_shutter","create:crafting/kinetics/large_water_wheel","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","aether:diamond_chestplate_repairing","biomesoplenty:chiseled_brimstone_bricks_from_brimstone_stonecutting","mcwpaths:mossy_stone_crystal_floor","travelersbackpack:emerald","comforts:sleeping_bag_to_black","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:mossy_cobblestone_square_paving","mcwbiomesoplenty:empyreal_japanese2_door","immersiveengineering:crafting/sawblade","mcwbiomesoplenty:empyreal_swamp_door","ae2:tools/nether_quartz_axe","mcwwindows:brown_mosaic_glass_pane","mcwpaths:stone_windmill_weave_stairs","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","everythingcopper:copper_shears","mcwfurnitures:stripped_acacia_coffee_table","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","productivebees:stonecutter/cherry_canvas_hive","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","botania:pump","minecraft:white_carpet","dyenamics:maroon_dye","aether:diamond_gloves","minecraft:mossy_cobblestone_stairs_from_mossy_cobblestone_stonecutting","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwdoors:jungle_four_panel_door","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwbiomesoplenty:dead_stable_door","minecraft:cracked_stone_bricks","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","simplylight:illuminant_cyan_block_on_toggle","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","travelersbackpack:dye_green_sleeping_bag","dimstorage:dim_wall","cfm:spruce_park_bench","sophisticatedstorage:iron_to_diamond_tier_upgrade","minecraft:magenta_stained_glass","mcwbiomesoplenty:willow_western_door","railcraft:nickel_gear","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","mcwpaths:cobbled_deepslate_running_bond_path","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","minecraft:acacia_pressure_plate","cfm:spruce_cabinet","sophisticatedstorage:spruce_barrel","botania:knockback_belt","mcwroofs:spruce_planks_steep_roof","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","handcrafted:spruce_shelf","mcwlights:acacia_tiki_torch","sophisticatedstorage:iron_to_netherite_tier_upgrade","corail_woodcutter:warped_woodcutter","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","securitycraft:reinforced_warped_fence_gate","mcwfurnitures:acacia_bookshelf","handcrafted:acacia_cupboard","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","alltheores:aluminum_plate","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","supplementaries:biomesoplenty/sign_post_palm","biomesoplenty:palm_trapdoor","additionallanterns:normal_lantern_black","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mekanism:factory/basic/crushing","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","mythicbotany:rune_holder","immersiveengineering:crafting/plate_silver_hammering","biomesoplenty:orange_sandstone","cfm:stripped_acacia_crate","farmersdelight:cabbage_crate","mcwbiomesoplenty:dead_barn_glass_door","allthecompressed:compress/cobbled_deepslate_1x","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","pneumaticcraft:thermostat_module","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwpaths:cobbled_deepslate_dumble_paving","mcwfurnitures:stripped_acacia_double_wardrobe","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","mcwbiomesoplenty:pine_western_door","minecraft:dye_blue_bed","utilitarian:utility/acacia_logs_to_trapdoors","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:hellbark_japanese_door","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","reliquary:uncrafting/string","connectedglass:tinted_borderless_glass_blue2","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","mcwlights:golden_wall_candle_holder","alltheores:diamond_rod","mcwbiomesoplenty:palm_stable_door","minecraft:stone_slab_from_stone_stonecutting","rftoolsbuilder:shield_block2","rftoolsbuilder:shield_block1","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwtrpdoors:jungle_whispering_trapdoor","farmersdelight:cooking/cabbage_rolls","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","silentgear:crimson_iron_dust_blasting","minecraft:stone_brick_slab_from_stone_stonecutting","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwbiomesoplenty:palm_four_panel_door","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwlights:white_paper_lamp","cfm:spruce_chair","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","littlelogistics:locomotive_route","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","mcwbiomesoplenty:stripped_palm_wardrobe","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","create:crafting/kinetics/placard","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","minecolonies:soy_pea_soup","minecraft:stone_brick_stairs_from_stone_stonecutting","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","alltheores:tin_plate","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","handcrafted:wood_bowl","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwdoors:bamboo_barn_glass_door","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","mcwbiomesoplenty:fir_glass_door","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","twilightforest:mangrove_chest_boat","minecraft:netherite_machete_smithing","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","mcwbiomesoplenty:palm_barn_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwpaths:mossy_stone_strewn_rocky_path","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","farmersdelight:safety_net","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","botania:water_ring","minecraft:music_disc_5","mekanism:factory/basic/compressing","minecraft:coal","mcwbiomesoplenty:mahogany_bamboo_door","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","croptopia:chicken_and_rice","aether:iron_axe_repairing","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwwindows:nether_brick_gothic","botania:forest_eye","additionallanterns:netherite_lantern","mcwbiomesoplenty:willow_mystic_door","biomesoplenty:palm_pressure_plate","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","mcwbiomesoplenty:pine_modern_door","handcrafted:wither_skeleton_trophy","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","minecraft:brown_dye","mcwtrpdoors:acacia_mystic_trapdoor","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","minecraft:brick_slab","mcwbiomesoplenty:umbran_paper_door","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","cfm:lime_cooler","rftoolsbuilder:shape_card_quarry_clear_fortune","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:empyreal_stable_door","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","dyenamics:mint_dye","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","minecraft:polished_deepslate_stairs","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","mcwroofs:pink_concrete_lower_roof","handcrafted:acacia_dining_bench","mcwroofs:acacia_planks_lower_roof","paraglider:cosmetic/goron_goddess_statue","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwlights:iron_chandelier","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_bricks_stonecutting","mcwpaths:mossy_cobblestone_honeycomb_paving","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwbiomesoplenty:palm_classic_trapdoor","cfm:white_kitchen_drawer","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","dyenamics:banner/persimmon_banner","handcrafted:acacia_nightstand","mcwbiomesoplenty:pine_horse_fence","mcwpaths:mossy_stone_windmill_weave","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","minecraft:golden_carrot","enderio:redstone_alloy_nugget","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","deepresonance:machine_frame","mcwdoors:bamboo_nether_door","blue_skies:starlit_bookshelf","mcwdoors:jungle_glass_door","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","dyenamics:cherenkov_candle","xnet:netcable_red_dye","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","dyenamics:honey_stained_glass","cfm:stripped_acacia_cabinet","create:crafting/kinetics/elevator_pulley","mcwfences:sandstone_grass_topped_wall","alltheores:invar_rod","travelersbackpack:black_sleeping_bag","minecraft:glowstone","alltheores:gold_rod","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","mcwbiomesoplenty:stripped_palm_double_drawer","biomesoplenty:jacaranda_chest_boat","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","create:crafting/kinetics/attribute_filter","supplementaries:key","ae2:block_cutter/walls/sky_stone_wall","blue_skies:maple_bookshelf","securitycraft:reinforced_black_stained_glass_pane_from_dye","rftoolspower:power_core1","pneumaticcraft:logistics_frame_default_storage","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","biomesoplenty:palm_stairs","mcwbiomesoplenty:palm_planks_lower_roof","handcrafted:spruce_table","handcrafted:jungle_fancy_bed","farmersdelight:onion_crate","create:crafting/kinetics/purple_seat","farmersdelight:cooking/tomato_sauce","mcwpaths:brick_running_bond_path","mcwfurnitures:stripped_acacia_stool_chair","securitycraft:security_camera","rftoolsbuilder:shape_card_quarry_dirt","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","allthemodium:allthemodium_ingot_from_dust_smelting","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","cfm:acacia_table","mcwpaths:brick_windmill_weave_stairs","additionallanterns:normal_sandstone_lantern","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","farmersdelight:tomato_seeds","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwtrpdoors:bamboo_barn_trapdoor","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwbiomesoplenty:dead_barn_door","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","everythingcopper:copper_axe","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","minecraft:black_stained_glass_pane_from_glass_pane","connectedglass:scratched_glass_green_pane2","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","twigs:acacia_table","biomesoplenty:brimstone_cluster_from_brimstone_stonecutting","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","mcwdoors:bamboo_paper_door","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","bigreactors:energizer/powerport_fe","minecraft:acacia_trapdoor","immersiveengineering:crafting/stick_aluminum","mcwroofs:thatch_steep_roof","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","mcwfurnitures:acacia_modern_wardrobe","dyenamics:bubblegum_concrete_powder","xnet:redstoneproxy_update","minecraft:cut_copper_slab_from_cut_copper_stonecutting","mcwroofs:spruce_planks_attic_roof","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:fir_four_panel_door","aether:iron_chestplate_repairing","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","supplementaries:slingshot","silentgear:crimson_steel_ingot","allthemodium:allthemodium_plate","mcwdoors:acacia_japanese2_door","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","everythingcopper:copper_rail","cfm:black_grill","handcrafted:deepslate_pillar_trim","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","pneumaticcraft:wall_lamp_inverted_black","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","evilcraft:crafting/blood_infuser","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_four_panel_door","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","dyenamics:rose_candle","aether:aether_iron_nugget_from_blasting","cfm:black_sofa","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwroofs:bricks_upper_steep_roof","enderio:coordinate_selector","minecraft:iron_pickaxe","aether:shield_repairing","ae2:shaped/not_so_mysterious_cube","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","minecraft:spruce_button","mcwroofs:magenta_concrete_attic_roof","rftoolsutility:matter_booster","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","pipez:wrench","dyenamics:bed/wine_bed","mcwpaths:mossy_stone_windmill_weave_stairs","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","minecraft:birch_boat","mcwfences:spruce_highley_gate","mcwbiomesoplenty:redwood_stable_head_door","cfm:post_box","dyenamics:aquamarine_candle","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","mcwtrpdoors:bamboo_classic_trapdoor","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwbiomesoplenty:stripped_palm_striped_chair","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","mcwbiomesoplenty:empyreal_western_door","rftoolsstorage:dump_module","cfm:orange_cooler","mcwtrpdoors:bamboo_barred_trapdoor","mcwbiomesoplenty:empyreal_japanese_door","minecraft:lime_stained_glass","mcwroofs:acacia_lower_roof","dyenamics:mint_candle","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","biomesoplenty:pine_chest_boat","alltheores:steel_plate","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","mcwbiomesoplenty:hellbark_paper_door","mcwpaths:mossy_stone_flagstone","mcwfurnitures:acacia_coffee_table","additionallanterns:normal_lantern_lime","mcwbiomesoplenty:umbran_stable_door","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","pneumaticcraft:spawner_extractor","aether:packed_ice_freezing","mcwbiomesoplenty:willow_stable_head_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","silentgear:blaze_gold_ingot_from_nugget","mcwpaths:stone_crystal_floor","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwdoors:bamboo_modern_door","cfm:fridge_dark","chimes:copper_chimes","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","minecraft:cut_sandstone_slab_from_cut_sandstone_stonecutting","croptopia:frying_pan","travelersbackpack:green_sleeping_bag","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:spruce_fence_gate","minecraft:leather_chestplate","mcwwindows:black_mosaic_glass","alltheores:steel_dust_from_alloy_blending","supplementaries:sign_post_acacia","immersiveengineering:crafting/earmuffs","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","minecraft:scaffolding","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwpaths:mossy_cobblestone_clover_paving","sophisticatedstorage:basic_to_diamond_tier_upgrade","alltheores:sapphire_from_hammer_crushing","mcwbiomesoplenty:fir_cottage_door","mcwroofs:purple_concrete_attic_roof","minecraft:acacia_button","constructionwand:core_angel","mcwfurnitures:acacia_bookshelf_cupboard","xnet:connector_red_dye","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwbiomesoplenty:willow_glass_door","mcwroofs:gutter_base_light_gray","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","delightful:knives/tin_knife","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","mcwtrpdoors:bamboo_swamp_trapdoor","mcwfurnitures:acacia_covered_desk","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:bamboo_chest_raft","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","additionallanterns:normal_lantern_yellow","farmersdelight:melon_juice","productivebees:stonecutter/willow_canvas_expansion_box","aquaculture:birch_fish_mount","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","simplylight:illuminant_red_block_toggle","immersiveengineering:crafting/heavy_engineering","mcwwindows:metal_window2","aether:blue_ice_freezing","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","cfm:stripped_acacia_coffee_table","allthecompressed:compress/gravel_1x","mcwbiomesoplenty:empyreal_barn_glass_door","reliquary:uncrafting/glowstone_dust","tombstone:ankh_of_prayer","cfm:white_sofa","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwpaths:mossy_stone_flagstone_slab","rftoolsbuilder:mover_control2","mcwfences:acacia_highley_gate","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","connectedglass:tinted_borderless_glass_black2","handcrafted:acacia_bench","travelersbackpack:squid","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","minecraft:polished_andesite_slab","create:crafting/logistics/powered_latch","productivebees:stonecutter/warped_canvas_expansion_box","mcwbiomesoplenty:maple_swamp_door","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","cfm:acacia_kitchen_drawer","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","mcwbiomesoplenty:maple_bark_glass_door","handcrafted:oak_bench","farmersdelight:cooking/pasta_with_meatballs","silentgear:blaze_gold_dust_smelting","enderio:copper_alloy_nugget_to_ingot","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","utilitarian:angel_block","mcwroofs:spruce_planks_upper_steep_roof","minecraft:repeater","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:maple_stable_door","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","simplylight:illuminant_orange_block_on_toggle","rftoolsbuilder:shape_card_quarry_silk_dirt","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwbiomesoplenty:empyreal_glass_door","cfm:jungle_cabinet","additionallanterns:normal_lantern_white","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwbiomesoplenty:fir_japanese2_door","mcwtrpdoors:acacia_beach_trapdoor","alltheores:uranium_dust_from_hammer_ingot_crushing","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","mcwfurnitures:acacia_lower_bookshelf_drawer","mcwbiomesoplenty:fir_waffle_door","mcwbridges:balustrade_mossy_cobblestone_bridge","cfm:pink_kitchen_sink","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:navy_candle","mcwwindows:spruce_blinds","mcwbiomesoplenty:stripped_palm_lower_triple_drawer","immersiveengineering:crafting/gunpart_hammer","mcwwindows:jungle_four_window","mcwfences:deepslate_pillar_wall","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","securitycraft:reinforced_orange_stained_glass_pane_from_dye","cfm:jungle_chair","mcwlights:birch_tiki_torch","handcrafted:spruce_drawer","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","ironfurnaces:furnaces/iron_furnace","mcwbiomesoplenty:pine_beach_door","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","biomesoplenty:brimstone_bricks","minecraft:jungle_door","additionallanterns:normal_lantern_blue","handcrafted:green_sheet","mcwpaths:mossy_cobblestone_dumble_paving","aether:chainmail_gloves_repairing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwtrpdoors:spruce_blossom_trapdoor","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","allthemodium:smithing/allthemodium_upgrade_smithing_template","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","handcrafted:witch_trophy","biomesoplenty:rose_quartz_block","littlelogistics:seater_car","minecraft:decorated_pot_simple","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","minecraft:oak_pressure_plate","minecraft:stone_brick_stairs","bigreactors:turbine/basic/activefluidport_forge","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwwindows:quartz_window","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:acacia_crate","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","mcwbiomesoplenty:redwood_tropical_door","reliquary:mob_charm_fragments/creeper","dyenamics:peach_candle","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","mcwbiomesoplenty:palm_beach_trapdoor","minecraft:jungle_planks","minecraft:netherite_axe_smithing","minecraft:clock","mcwroofs:red_concrete_top_roof","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","railcraft:lead_gear","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","biomesoplenty:brimstone_brick_slab_from_brimstone_stonecutting","twilightforest:transformation_chest_boat","additionallanterns:normal_lantern_light_gray","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","dyenamics:bed/conifer_bed","minecraft:polished_deepslate_slab_from_polished_deepslate_stonecutting","mcwbiomesoplenty:palm_cottage_trapdoor","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","mcwdoors:acacia_western_door","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","ad_astra:iron_plating","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwbiomesoplenty:maple_paper_door","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","minecraft:cut_copper_stairs","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","alltheores:tin_rod","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","cfm:acacia_kitchen_sink_dark","mcwroofs:jungle_upper_steep_roof","rftoolsbase:machine_frame","minecraft:mossy_cobblestone_slab","pneumaticcraft:remote","cfm:stripped_jungle_chair","minecraft:dye_black_bed","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","mcwbiomesoplenty:magic_western_door","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwbiomesoplenty:palm_drawer","mcwbiomesoplenty:dead_four_panel_door","biomesoplenty:empyreal_chest_boat","mcwbiomesoplenty:rope_palm_bridge","sophisticatedstorage:basic_to_iron_tier_upgrade","railcraft:brass_gear","twigs:rhyolite","pylons:infusion_pylon","mcwbiomesoplenty:pine_swamp_door","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","domum_ornamentum:light_blue_brick_extra","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","handcrafted:acacia_couch","mcwroofs:thatch_top_roof","mekanism:processing/steel/ingot/from_dust_smelting","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","minecraft:lime_concrete_powder","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","mcwlights:festive_lantern","dyenamics:honey_candle","mcwbiomesoplenty:magic_tropical_door","securitycraft:block_change_detector","twigs:rocky_dirt","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","minecraft:netherite_scrap_from_blasting","mcwbiomesoplenty:dead_bamboo_door","mcwtrpdoors:bamboo_four_panel_trapdoor","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","biomesoplenty:brimstone_brick_stairs_from_brimstone_stonecutting","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwbiomesoplenty:stripped_palm_chair","utilitix:acacia_shulker_boat","createoreexcavation:vein_atlas","twilightforest:sorting_chest_boat","travelersbackpack:standard_smithing","mcwbiomesoplenty:palm_blossom_trapdoor","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","pneumaticcraft:wall_lamp_cyan","create:crafting/kinetics/fluid_pipe","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbiomesoplenty:palm_top_roof","enderio:pulsating_alloy_grinding_ball","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","mcwbiomesoplenty:stripped_palm_cupboard_counter","pneumaticcraft:pressure_chamber_glass_x1","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","undergarden:gloom_o_lantern","minecraft:sandstone_slab","enderio:copper_alloy_grinding_ball","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwbiomesoplenty:pine_bark_glass_door","croptopia:cinnamon_wood","minecraft:gold_ingot_from_smelting_raw_gold","mcwbiomesoplenty:palm_beach_door","minecraft:wild_armor_trim_smithing_template","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwwindows:acacia_plank_window","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","minecraft:cracked_polished_blackstone_bricks","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","mcwbiomesoplenty:magic_bamboo_door","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwfurnitures:stripped_acacia_double_drawer","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwroofs:thatch_lower_roof","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","handcrafted:spruce_couch","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwwindows:jungle_blinds","mcwbiomesoplenty:mahogany_stable_door","mcwlights:oak_tiki_torch","travelersbackpack:melon","minecraft:white_dye_from_lily_of_the_valley","securitycraft:block_pocket_manager","enderio:pulsating_alloy_nugget_to_ingot","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","alltheores:copper_ore_hammer","dyenamics:conifer_stained_glass","mcwbiomesoplenty:stripped_palm_counter","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","bloodmagic:blood_altar","mcwbiomesoplenty:stripped_palm_bookshelf_drawer","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","alltheores:brass_gear","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:acacia_triple_drawer","minecraft:netherite_chestplate_smithing","mcwbiomesoplenty:palm_swamp_trapdoor","mcwbridges:acacia_log_bridge_middle","cfm:jungle_kitchen_counter","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","mcwfurnitures:stripped_acacia_double_drawer_counter","cfm:spruce_coffee_table","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","railcraft:bag_of_cement","bigreactors:fluidizer/powerport","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","connectedglass:borderless_glass_blue2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","mcwroofs:yellow_concrete_lower_roof","handcrafted:blaze_trophy","cfm:acacia_desk","allthecompressed:compress/jungle_log_1x","mcwbiomesoplenty:pine_waffle_door","mcwwindows:dark_oak_plank_parapet","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwfurnitures:stripped_acacia_drawer","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","mekanism:osmium_compressor","create:spruce_window","immersiveengineering:crafting/alloybrick","biomesoplenty:brimstone_bud_from_brimstone_stonecutting","mcwbiomesoplenty:maple_pyramid_gate","minecraft:smooth_sandstone_slab_from_smooth_sandstone_stonecutting","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","minecraft:black_bed","sfm:manager","mcwroofs:purple_concrete_roof","cfm:purple_grill","mcwbiomesoplenty:willow_bamboo_door","create:crafting/kinetics/hose_pulley","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","deeperdarker:bloom_boat","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","dyenamics:rose_stained_glass","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","additionallanterns:normal_lantern_green","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwroofs:acacia_steep_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","minecraft:spectral_arrow","allthecompressed:compress/copper_block_1x","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","twigs:copper_pillar_from_cut_copper_stonecutting","mcwpaths:mossy_stone_crystal_floor_path","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","mcwbiomesoplenty:palm_upper_steep_roof","mcwdoors:bamboo_waffle_door","mcwfurnitures:stripped_acacia_desk","mcwroofs:oak_planks_attic_roof","evilcraft:crafting/blood_infusion_core","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","mcwbiomesoplenty:palm_paper_trapdoor","sophisticatedstorage:basic_to_copper_tier_upgrade","mcwbiomesoplenty:dead_tropical_door","sophisticatedstorage:acacia_limited_barrel_1","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","sophisticatedstorage:acacia_limited_barrel_3","sophisticatedstorage:acacia_limited_barrel_2","alltheores:sapphire_dust_from_hammer_crushing","bigreactors:reprocessor/powerport","mcwbiomesoplenty:jacaranda_paper_door","sophisticatedstorage:acacia_limited_barrel_4","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","aether:netherite_pickaxe_repairing","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwbiomesoplenty:palm_ranch_trapdoor","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwfences:panelled_metal_fence_gate","comforts:sleeping_bag_black","mcwlights:golden_double_candle_holder","cfm:light_gray_kitchen_sink","mcwbiomesoplenty:empyreal_nether_door","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","supplementaries:flags/flag_lime","sophisticatedstorage:acacia_barrel","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","minecraft:green_banner","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwpaths:brick_dumble_paving","utilitarian:snad/drit","handcrafted:jungle_desk","mcwtrpdoors:acacia_swamp_trapdoor","corail_woodcutter:bamboo_woodcutter","mcwbiomesoplenty:fir_modern_door","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","mcwbiomesoplenty:mahogany_barn_glass_door","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","securitycraft:alarm","pneumaticcraft:manometer","mcwbiomesoplenty:dead_japanese2_door","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","cfm:stripped_acacia_desk_cabinet","mcwbiomesoplenty:palm_paper_door","pneumaticcraft:wall_lamp_inverted_magenta","ae2:block_cutter/stairs/sky_stone_stairs","allthecompressed:compress/acacia_planks_1x","additionallanterns:amethyst_lantern","utilitix:crude_furnace","ad_astra:black_flag","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwfurnitures:stripped_acacia_striped_chair","mcwwindows:stone_window","rftoolspower:endergenic","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","biomesoplenty:palm_planks","productivebees:stonecutter/yucca_canvas_hive","integrateddynamics:crafting/drying_basin","additionallanterns:normal_lantern_cyan","mcwtrpdoors:jungle_classic_trapdoor","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","ae2:network/cables/glass_black","twigs:mossy_cobblestone_bricks_cobblestone","mcwpaths:cobbled_deepslate_flagstone_path","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","everythingcopper:copper_ladder","railcraft:invar_gear","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","allthemods:constructionwand/iron_wand","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","rftoolsbuilder:shape_card_pump_dirt","cfm:black_trampoline","mcwfurnitures:acacia_cupboard_counter","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","minecraft:deepslate_tiles_from_polished_deepslate_stonecutting","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:magic_mystic_door","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","minecraft:dropper","farmersdelight:cooking/fried_rice","mcwbiomesoplenty:palm_end_table","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwfurnitures:stripped_acacia_bookshelf","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","create:crafting/kinetics/brass_hand","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","mcwbiomesoplenty:empyreal_bamboo_door","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","immersiveengineering:crafting/plate_nickel_hammering","mcwdoors:acacia_bamboo_door","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","productivebees:stonecutter/yucca_canvas_expansion_box","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","supplementaries:stonecutting/blackstone_tile","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","twigs:polished_rhyolite","immersiveengineering:crafting/wirecoil_redstone","additionallanterns:stone_bricks_chain","mcwbridges:mossy_cobblestone_bridge_pier","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_bricks_bridge","alltheores:nickel_rod","supplementaries:candle_holders/candle_holder_blue_dye","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","minecraft:acacia_stairs","mcwbiomesoplenty:dead_mystic_door","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:stripped_palm_glass_table","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwroofs:jungle_planks_steep_roof","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","enderio:black_paper","twigs:polished_tuff_stonecutting","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","mcwbiomesoplenty:pine_japanese_door","ae2:network/cables/dense_smart_fluix","handcrafted:acacia_corner_trim","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwbiomesoplenty:redwood_glass_door","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","enderio:copper_alloy_nugget","mcwroofs:orange_concrete_upper_lower_roof","pneumaticcraft:compressed_iron_leggings","productivebees:stonecutter/hellbark_canvas_hive","mcwbiomesoplenty:palm_table","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwbridges:bamboo_bridge","mcwbiomesoplenty:mahogany_western_door","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","mcwbiomesoplenty:maple_beach_door","mcwwindows:metal_pane_window","rftoolsutility:matter_receiver","mcwbiomesoplenty:magic_four_panel_door","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","mcwfurnitures:jungle_large_drawer","mcwfurnitures:acacia_drawer_counter","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:hellbark_four_panel_door","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","productivebees:stonecutter/crimson_canvas_expansion_box","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","mcwfurnitures:acacia_striped_chair","mcwdoors:acacia_paper_door","mcwtrpdoors:bamboo_beach_trapdoor","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","silentgear:blaze_gold_dust","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwbiomesoplenty:palm_barn_door","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","mcwbiomesoplenty:palm_mystic_door","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","mcwbiomesoplenty:maple_japanese2_door","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","rftoolsstorage:storage_module0","immersiveengineering:crafting/fluid_placer","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwdoors:acacia_whispering_door","mcwbiomesoplenty:jacaranda_cottage_door","mcwfurnitures:stripped_jungle_table","rftoolsutility:matter_transmitter","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwbiomesoplenty:palm_planks_path","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwtrpdoors:acacia_whispering_trapdoor","travelersbackpack:magma_cube","allthetweaks:ender_pearl_block","croptopia:beetroot_salad","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","mcwbiomesoplenty:hellbark_glass_door","forbidden_arcanus:deorum_lantern","everythingcopper:copper_door","enderio:redstone_filter_base","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","farmersdelight:cooking/glow_berry_custard","create:crafting/kinetics/red_seat","mcwroofs:orange_concrete_steep_roof","mekanism:jetpack","supplementaries:bamboo_spikes","mcwroofs:acacia_roof","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","minecraft:polished_blackstone_brick_wall","mcwbiomesoplenty:stripped_palm_log_four_window","mcwbiomesoplenty:empyreal_mystic_door","handcrafted:spruce_bench","twigs:spruce_table","mcwroofs:gutter_base_light_blue","create:crafting/kinetics/orange_seat","rftoolsutility:syringe","gtceu:shaped/block_compress_steel","minecraft:diamond_block","mekanism:configurator","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","mcwbiomesoplenty:palm_cupboard_counter","mcwroofs:jungle_planks_upper_steep_roof","minecraft:yellow_stained_glass","rftoolsutility:spawner","cfm:green_cooler","additionallanterns:normal_lantern_magenta","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","farmersdelight:horse_feed","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","immersiveengineering:crafting/strip_curtain","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","computercraft:turtle_normal_overlays/turtle_trans_overlay","utilitarian:snad/snad","rftoolsstorage:modular_storage","handcrafted:bricks_corner_trim","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:white_bed","mcwbiomesoplenty:snowblossom_hedge","minecraft:spruce_slab","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","handcrafted:green_cushion","cfm:dye_green_picket_gate","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","rftoolsbuilder:shape_card_pump","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:acacia_paper_trapdoor","bigreactors:blasting/yellorium_from_raw","mekanism:electrolytic_separator","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:stripped_palm_end_table","cfm:red_trampoline","mcwbiomesoplenty:pine_paper_door","botania:manasteel_shears","ae2:network/cables/smart_green","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","supplementaries:soap/piston","cfm:acacia_upgraded_gate","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","twilightdelight:obsidian","domum_ornamentum:green_cobblestone_extra","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","twigs:cracked_bricks","supplementaries:slice_map","mcwbridges:brick_bridge_pier","connectedglass:borderless_glass_green_pane2","silentgear:crimson_iron_ingot_from_block","mcwdoors:acacia_mystic_door","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","alltheores:invar_plate","occultism:crafting/butcher_knife","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","silentgear:crimson_iron_nugget","blue_skies:trough","botania:lens_normal","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","dyenamics:ultramarine_dye","mcwroofs:bricks_steep_roof","cfm:gray_grill","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwbiomesoplenty:magic_barn_door","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","minecraft:purple_dye","minecraft:deepslate_tile_wall_from_polished_deepslate_stonecutting","mcwfurnitures:jungle_modern_chair","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","modularrouters:modular_router","delightful:knives/nickel_knife","mcwfurnitures:stripped_jungle_coffee_table","create:brass_bars_from_ingots_brass_stonecutting","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","mcwfurnitures:stripped_jungle_double_drawer","mcwbiomesoplenty:palm_bamboo_door","ae2:network/cables/smart_black","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","minecraft:observer","minecraft:mossy_cobblestone_wall_from_mossy_cobblestone_stonecutting","cfm:gray_kitchen_sink","minecraft:polished_deepslate_wall","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwtrpdoors:acacia_bark_trapdoor","mcwbiomesoplenty:hellbark_waffle_door","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","mcwbridges:spruce_log_bridge_middle","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","mcwbiomesoplenty:palm_swamp_door","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwwindows:birch_log_parapet","ad_astra:iron_factory_block","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","mcwroofs:black_concrete_attic_roof","minecraft:charcoal","handcrafted:blue_bowl","mcwdoors:bamboo_stable_head_door","twigs:tuff_wall","minecraft:polished_blackstone_brick_slab","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:acacia_drawer","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","twigs:polished_tuff_bricks_from_tuff_stonecutting","supplementaries:fodder","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","biomesoplenty:brimstone_cluster","mcwwindows:crimson_blinds","mcwbiomesoplenty:fir_swamp_door","dyenamics:banner/aquamarine_banner","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","create:crafting/kinetics/super_glue","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","rftoolsbuilder:shape_card_liquid","mcwbiomesoplenty:dead_stable_head_door","mcwroofs:gutter_base_blue","minecraft:gray_dye","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwbiomesoplenty:palm_tropical_trapdoor","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","mcwwindows:nether_brick_arrow_slit","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","allthemodium:allthemodium_ingot_from_dust_blasting","botania:travel_belt","bigreactors:reprocessor/fluidinjector","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:maple_bamboo_door","mcwroofs:acacia_upper_lower_roof","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","mcwbiomesoplenty:palm_glass_trapdoor","silentgear:blaze_gold_nugget","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwbiomesoplenty:redwood_bamboo_door","handcrafted:spruce_desk","immersiveengineering:crafting/hempcrete","mcwwindows:oak_plank_parapet","minecraft:iron_sword","minecraft:spruce_fence","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","silentgear:crimson_iron_dust_smelting","additionallanterns:normal_lantern_red","mcwbiomesoplenty:magic_japanese2_door","domum_ornamentum:purple_brick_extra","mcwwindows:one_way_glass_pane","mcwbiomesoplenty:hellbark_stable_door","cfm:green_sofa","mcwfurnitures:stripped_acacia_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","minecraft:cut_copper_stairs_from_cut_copper_stonecutting","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","productivetrees:sawmill","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","everythingcopper:copper_anvil","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","productivebees:stonecutter/maple_canvas_hive","mcwbiomesoplenty:mahogany_picket_fence","mcwdoors:acacia_barn_glass_door","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","supplementaries:candle_holders/candle_holder_green_dye","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","handcrafted:jungle_table","supplementaries:planter_rich","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","productivebees:stonecutter/dead_canvas_hive","mcwwindows:magenta_curtain","minecraft:conduit","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","sophisticatedstorage:spruce_chest","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","biomesoplenty:palm_slab","mcwbiomesoplenty:empyreal_barn_door","sgjourney:sandstone_hieroglyphs","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","supplementaries:flags/flag_red","cfm:dye_blue_picket_fence","mcwtrpdoors:acacia_classic_trapdoor","mcwroofs:magenta_concrete_upper_lower_roof","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:cyan_concrete_steep_roof","mcwbiomesoplenty:hellbark_nether_door","mcwbiomesoplenty:stripped_palm_double_wardrobe","supplementaries:stone_lamp","minecolonies:pea_soup","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","handcrafted:black_cushion","pneumaticcraft:gilded_upgrade","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","minecraft:yellow_concrete_powder","mcwbiomesoplenty:empyreal_paper_door","mcwwindows:deepslate_window","mcwroofs:spruce_planks_upper_lower_roof","mcwfurnitures:acacia_modern_chair","minecraft:comparator","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","minecraft:netherite_boots_smithing","mcwdoors:bamboo_mystic_door","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","mcwbiomesoplenty:magic_bark_glass_door","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","rftoolspower:powercell_card","delightful:knives/silver_knife","mcwwindows:acacia_blinds","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwdoors:acacia_waffle_door","mcwroofs:roofing_hammer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","twigs:copper_pillar_stonecutting","mcwbiomesoplenty:palm_bookshelf_cupboard","blue_skies:lunar_bookshelf","sophisticatedstorage:packing_tape","immersiveengineering:crafting/buzzsaw","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","create:crafting/kinetics/vertical_gearbox","productivebees:stonecutter/redwood_canvas_hive","mcwlights:double_street_lamp","railcraft:tin_gear","minecraft:tnt","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","minecraft:black_dye","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","minecraft:deepslate_brick_stairs_from_polished_deepslate_stonecutting","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","alltheores:invar_dust_from_alloy_blending","mekanism:steel_casing","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:umbran_bamboo_door","ae2:network/cells/item_cell_housing","minecraft:cobblestone_wall_from_cobblestone_stonecutting","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","domum_ornamentum:light_gray_brick_extra","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","securitycraft:reinforced_lime_stained_glass_pane_from_dye","farmersdelight:rice","supplementaries:globe_sepia","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","additionallanterns:bone_chain","allthecompressed:compress/bone_block_1x","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","sophisticatedstorage:storage_link","biomesoplenty:palm_fence_gate","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","cfm:door_mat","mcwbiomesoplenty:palm_planks_roof","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:item_shelf","supplementaries:stone_tile","mcwbiomesoplenty:willow_barn_glass_door","rftoolsbuilder:blue_shield_template_block","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","rftoolsbuilder:space_chamber","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","supplementaries:flags/flag_white","comforts:hammock_black","silentgear:coating_smithing_template","handcrafted:jungle_shelf","enderio:yeta_wrench","minecraft:grindstone","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:palm_planks_attic_roof","mcwwindows:metal_window","domum_ornamentum:brown_brick_extra","minecraft:glass_pane","supplementaries:timber_brace","mcwbiomesoplenty:maple_barn_glass_door","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","bigreactors:reactor/reinforced/activetap_fe","mcwlights:copper_chain","mcwroofs:acacia_planks_steep_roof","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","additionallanterns:normal_lantern_colorless","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwbiomesoplenty:mahogany_paper_door","cfm:orange_kitchen_drawer","mcwroofs:spruce_planks_top_roof","enderio:cold_fire_igniter","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","handcrafted:spruce_pillar_trim","minecraft:leather_boots","railcraft:tank_detector","railcraft:steel_spike_maul","ad_astra:etrionic_blast_furnace","pneumaticcraft:classify_filter","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","mcwbridges:mossy_stone_brick_bridge","wirelesschargers:basic_wireless_block_charger","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","mcwtrpdoors:acacia_barred_trapdoor","mcwlights:pink_paper_lamp","mcwbiomesoplenty:magic_nether_door","mcwbiomesoplenty:stripped_palm_lower_bookshelf_drawer","pneumaticcraft:wall_lamp_inverted_light_blue","immersiveengineering:crafting/wire_aluminum","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","rftoolscontrol:workbench","aether:iron_gloves","mcwbiomesoplenty:palm_modern_wardrobe","minecraft:dye_black_carpet","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","mcwfurnitures:acacia_bookshelf_drawer","corail_woodcutter:oak_woodcutter","mcwbiomesoplenty:palm_planks_steep_roof","occultism:crafting/dictionary_of_spirits","cfm:oak_table","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","croptopia:tomato_juice","pneumaticcraft:stomp_upgrade","minecraft:magma_block","mcwwindows:crimson_stem_parapet","minecraft:spruce_pressure_plate","cfm:acacia_park_bench","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","connectedglass:clear_glass_black_pane2","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivebees:stonecutter/jungle_canvas_expansion_box","aether:book_of_lore","mcwfurnitures:stripped_acacia_triple_drawer","minecraft:brush","railcraft:steel_pickaxe","mcwroofs:jungle_planks_roof","handcrafted:spruce_nightstand","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","aether:aether_iron_nugget_from_smelting","mcwfurnitures:acacia_wardrobe","sfm:cable","mcwbiomesoplenty:palm_japanese_door","minecraft:hay_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","alltheores:ruby_from_hammer_crushing","delightful:knives/invar_knife","create:crafting/kinetics/vertical_gearboxfrom_conversion","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:jacaranda_barn_glass_door","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbridges:mossy_cobblestone_bridge_stair","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","twigs:mossy_cobblestone_brick_slab_from_mossy_cobblestone_stonecutting","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:fir_barn_door","mcwroofs:blue_concrete_top_roof","bambooeverything:bamboo_ladder","evilcraft:smelting/hardened_blood_shard","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","handcrafted:acacia_fancy_bed","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:cut_copper","biomesoplenty:dead_boat","handcrafted:acacia_drawer","supplementaries:flags/flag_orange","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwbiomesoplenty:willow_japanese2_door","domum_ornamentum:brick_extra","travelersbackpack:iron","minecraft:smooth_sandstone_stairs","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","mcwtrpdoors:bamboo_barrel_trapdoor","mcwbiomesoplenty:pine_barn_glass_door","mcwpaths:mossy_stone_running_bond","mcwpaths:stone_flagstone_slab","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","reliquary:uncrafting/redstone","mcwwindows:granite_louvered_shutter","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/generator","securitycraft:sentry","biomesoplenty:palm_wood","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","twilightforest:wood/acacia_banister","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwfurnitures:stripped_acacia_lower_bookshelf_drawer","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","mcwbiomesoplenty:stripped_palm_coffee_table","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:fir_mystic_door","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwbiomesoplenty:fir_beach_door","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mekanism:jetpack_armored","mcwlights:orange_paper_lamp","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","ae2:network/cables/dense_covered_black","mcwroofs:gray_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","create:crafting/curiosities/peculiar_bell","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","mcwpaths:mossy_cobblestone_basket_weave_paving","delightful:knives/brass_knife","mcwbiomesoplenty:umbran_mystic_door","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","immersiveengineering:crafting/metal_ladder_none","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwroofs:acacia_planks_attic_roof","domum_ornamentum:white_brick_extra","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwfurnitures:acacia_double_drawer_counter","minecraft:mossy_cobblestone_stairs","mcwfences:acacia_horse_fence","immersiveengineering:crafting/chute_iron","comforts:hammock_green","mcwfences:spruce_hedge","sfm:labelgun","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","connectedglass:scratched_glass_blue2","immersiveengineering:crafting/clinker_brick_quoin","mcwbiomesoplenty:palm_barred_trapdoor","domum_ornamentum:pink_floating_carpet","botania:virus_nullodermal","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbridges:rope_spruce_bridge","supplementaries:end_stone_lamp","allthecompressed:compress/deepslate_1x","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","mcwbiomesoplenty:stripped_palm_stool_chair","cfm:red_kitchen_drawer","mcwbiomesoplenty:palm_bookshelf","cfm:stripped_jungle_desk","botania:manasteel_shovel","handcrafted:oven","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwbiomesoplenty:palm_glass_table","mcwpaths:cobbled_deepslate_crystal_floor_slab","minecraft:green_carpet","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","rftoolsbuilder:shape_card_quarry_fortune","mcwbiomesoplenty:magic_stable_door","mcwlights:jungle_tiki_torch","immersiveengineering:crafting/plate_aluminum_hammering","twigs:stone_column_stonecutting","connectedglass:borderless_glass_blue_pane2","delightful:knives/bronze_knife","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwbiomesoplenty:mahogany_classic_door","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","twigs:mossy_cobblestone_bricks_stonecutting","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","mcwbiomesoplenty:willow_nether_door","minecraft:stone_slab","mcwbiomesoplenty:magic_classic_door","pneumaticcraft:compressed_bricks","mcwpaths:brick_flagstone","productivebees:stonecutter/comb_canvas_hive","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","silentgear:blaze_gold_ingot_from_block","mcwbiomesoplenty:maple_four_panel_door","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:acacia_slab","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","travelersbackpack:backpack_tank","minecraft:jungle_stairs","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","additionallanterns:normal_lantern_light_blue","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","croptopia:campfire_molasses","mcwdoors:acacia_stable_door","securitycraft:reinforced_blue_stained_glass_pane_from_dye","mcwbiomesoplenty:pine_four_panel_door","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","pneumaticcraft:wall_lamp_green","minecraft:blue_stained_glass_pane_from_glass_pane","mcwroofs:thatch_upper_lower_roof","silentgear:crimson_iron_ingot_from_nugget","mcwbiomesoplenty:hellbark_cottage_door","additionallanterns:netherite_chain","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","mcwtrpdoors:bamboo_bark_trapdoor","mcwbiomesoplenty:stripped_palm_modern_wardrobe","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","silentgear:upgrade_base","create:acacia_window","croptopia:melon_juice","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","mcwbiomesoplenty:jacaranda_glass_door","securitycraft:mine","rftoolsbuilder:space_chamber_controller","mcwfurnitures:stripped_acacia_chair","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","minecraft:mossy_cobblestone_wall","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","mcwwindows:one_way_glass","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mekanism:electrolytic_core","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","biomesoplenty:brimstone_bricks_from_brimstone_stonecutting","minecraft:oak_chest_boat","mcwpaths:mossy_stone_flagstone_path","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","mcwbridges:acacia_bridge_pier","minecraft:stone_pressure_plate","mcwbiomesoplenty:hellbark_classic_door","mcwtrpdoors:print_beach","mcwroofs:spruce_planks_lower_roof","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","mcwbiomesoplenty:dead_glass_door","mcwbiomesoplenty:palm_double_drawer_counter","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwbiomesoplenty:willow_modern_door","pneumaticcraft:logistics_core","create:crafting/kinetics/yellow_seat","everythingcopper:copper_helmet","mcwbiomesoplenty:palm_glass_door","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","mcwbridges:rope_acacia_bridge","minecraft:mangrove_boat","mcwdoors:bamboo_whispering_door","minecraft:bread","minecraft:bone_meal_from_bone_block","mcwbiomesoplenty:redwood_western_door","allthecompressed:compress/hay_block_1x","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","alltheores:silver_rod","minecraft:lever","alltheores:osmium_plate","immersiveengineering:crafting/wirecoil_structure_steel","mcwbiomesoplenty:dead_classic_door","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","mcwbiomesoplenty:umbran_swamp_door","mcwbiomesoplenty:redwood_paper_door","mcwtrpdoors:acacia_blossom_trapdoor","mcwbiomesoplenty:willow_pyramid_gate","mcwbiomesoplenty:mahogany_bark_glass_door","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","additionallanterns:normal_lantern_brown","mcwfurnitures:acacia_desk","minecraft:black_banner","tombstone:dark_marble","minecraft:iron_shovel","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwwindows:stone_brick_arrow_slit","mcwpaths:cobbled_deepslate_windmill_weave_stairs","chimes:amethyst_chimes","ae2:smelting/smooth_sky_stone_block","mcwbiomesoplenty:mahogany_cottage_door","mcwfurnitures:jungle_desk","mcwbiomesoplenty:umbran_waffle_door","mcwbiomesoplenty:palm_bark_trapdoor","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","mcwroofs:jungle_planks_lower_roof","handcrafted:acacia_chair","croptopia:ham_sandwich","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","minecraft:polished_deepslate_slab","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","mcwbiomesoplenty:willow_swamp_door","securitycraft:disguise_module","mcwdoors:acacia_glass_door","mcwwindows:deepslate_four_window","mcwbiomesoplenty:fir_stable_head_door","pneumaticcraft:camo_applicator","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwwindows:dark_oak_louvered_shutter","mcwbiomesoplenty:redwood_barn_door","mcwtrpdoors:acacia_four_panel_trapdoor","domum_ornamentum:blockbarreldeco_standing","mcwfences:gothic_metal_fence","railcraft:polished_quarried_stone_from_quarried_stone_in_stonecutter","deeperdarker:echo_boat","minecraft:deepslate_bricks","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","supplementaries:gold_trapdoor","mcwbiomesoplenty:palm_attic_roof","mcwfences:birch_picket_fence","minecraft:cooked_chicken","mcwbiomesoplenty:palm_modern_door","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwdoors:bamboo_barn_door","mcwbiomesoplenty:maple_cottage_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","minecraft:nether_brick","mcwbiomesoplenty:empyreal_modern_door","mcwdoors:jungle_waffle_door","handcrafted:spruce_dining_bench","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwroofs:spruce_planks_roof","minecraft:spruce_door","mcwbiomesoplenty:palm_counter","mcwbridges:asian_red_bridge","create:crafting/kinetics/propeller","mcwfurnitures:stripped_acacia_large_drawer","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","immersiveengineering:crafting/fluid_pipe","mcwbiomesoplenty:magic_highley_gate","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","rftoolspower:pearl_injector","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","pylons:harvester_pylon","minecraft:wheat","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","mcwdoors:bamboo_swamp_door","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","cfm:acacia_kitchen_counter","deepresonance:radiation_suit_boots","minecraft:bow","supplementaries:wrench","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","delightful:knives/aluminum_knife","handcrafted:bear_trophy","mcwdoors:bamboo_japanese_door","cfm:purple_kitchen_sink","cfm:black_cooler","minecraft:jungle_slab","handcrafted:acacia_counter","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","minecraft:dark_oak_boat","mcwpaths:mossy_stone_crystal_floor_slab","aquaculture:gold_hook","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:acacia_large_drawer","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:pine_stable_door","minecraft:melon_seeds","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","cfm:stripped_acacia_desk","minecraft:firework_rocket_simple","create:crafting/kinetics/encased_fan","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","mcwbiomesoplenty:magic_barn_glass_door","mcwbiomesoplenty:redwood_mystic_door","enderio:enderios","pneumaticcraft:wall_lamp_inverted_orange","mcwbiomesoplenty:fir_bamboo_door","pneumaticcraft:wall_lamp_inverted_brown","supplementaries:pedestal","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwfurnitures:stripped_acacia_wardrobe","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","mcwbiomesoplenty:stripped_palm_double_drawer_counter","mcwdoors:metal_windowed_door","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","twigs:lamp","mcwfurnitures:stripped_acacia_modern_desk","mcwwindows:orange_curtain","minecraft:stone","dyenamics:bed/lavender_bed","railcraft:iron_tank_valve","mcwroofs:brown_concrete_lower_roof","mcwbridges:mossy_cobblestone_bridge","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","forbidden_arcanus:deorum_soul_lantern","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwbiomesoplenty:palm_coffee_table","crafting_on_a_stick:crafting_table","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","cfm:acacia_upgraded_fence","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","cfm:oak_park_bench","rftoolsutility:environmental_controller","productivebees:stonecutter/hellbark_canvas_expansion_box","mcwroofs:cyan_concrete_attic_roof","silentgear:crimson_iron_block","immersiveengineering:crafting/conveyor_basic","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","thermal_extra:crafting/iron_rod","mcwbridges:mossy_stone_bridge_pier","mcwbiomesoplenty:empyreal_horse_fence","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","xnet:controller","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","alltheores:lead_plate","additionallanterns:obsidian_chain","minecraft:polished_deepslate_wall_from_polished_deepslate_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwtrpdoors:acacia_ranch_trapdoor","mcwlights:cross_lantern","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","immersiveengineering:crafting/hemp_fabric","minecraft:green_candle","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","enderio:wood_gear_corner","rftoolsbase:manual","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","mcwbiomesoplenty:palm_double_wardrobe","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","additionallanterns:diamond_chain","supplementaries:stonecutting/stone_tile","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","additionallanterns:normal_lantern_purple","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","enderio:dark_steel_trapdoor","minecraft:dye_black_wool","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","ad_astra:small_green_industrial_lamp","rftoolsbuilder:shape_card_quarry_silk","pneumaticcraft:reinforced_brick_from_slab","minecraft:cooked_beef","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","supplementaries:flags/flag_gray","mcwpaths:mossy_stone_flagstone_stairs","mcwlights:yellow_paper_lamp","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","railcraft:iron_tank_gauge","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","cfm:magenta_grill","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","mcwbiomesoplenty:mahogany_japanese_door","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","utilitarian:utility/acacia_logs_to_pressure_plates","mcwbiomesoplenty:umbran_four_panel_door","handcrafted:spruce_corner_trim","mcwdoors:garage_white_door","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","mcwpaths:stone_running_bond","minecraft:deepslate_brick_wall_from_polished_deepslate_stonecutting","trashcans:liquid_trash_can","mcwbiomesoplenty:redwood_beach_door","ae2:network/cables/smart_blue","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","mcwbiomesoplenty:palm_upper_lower_roof","immersiveengineering:crafting/plate_gold_hammering","botania:turntable","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","domum_ornamentum:black_brick_extra","supplementaries:daub","railcraft:steel_chestplate","alltheores:zinc_dust_from_hammer_crushing","minecraft:netherite_scrap","mcwfurnitures:stripped_jungle_end_table","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","ad_astra:iron_rod","mcwbiomesoplenty:palm_covered_desk","mcwbiomesoplenty:magic_cottage_door","mcwpaths:stone_flagstone","bigreactors:energizer/casing","aether:chainmail_boots_repairing","simplylight:illuminant_block_toggle","xnet:connector_blue","supplementaries:blackstone_tile","mcwbiomesoplenty:hellbark_highley_gate","minecraft:deepslate_tile_stairs_from_polished_deepslate_stonecutting","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","mcwbiomesoplenty:stripped_palm_bookshelf_cupboard","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","immersiveengineering:crafting/coil_lv","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","ad_astra:small_blue_industrial_lamp","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwpaths:mossy_stone_running_bond_path","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","minecraft:acacia_wood","aether:skyroot_note_block","mcwbiomesoplenty:maple_tropical_door","mcwbiomesoplenty:mahogany_beach_door","pneumaticcraft:logistics_configurator","create:cut_tuff_from_stone_types_tuff_stonecutting","bigreactors:smelting/yellorium_from_raw","mcwtrpdoors:print_tropical","rftoolsutility:dialing_device","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:232,warning_level:0}}