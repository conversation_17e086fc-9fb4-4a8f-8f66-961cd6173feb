{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:4.5d,Name:"forge:block_reach"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"attributeslib:creative_flight"}],BalmData:{TrashSlot:{Count:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:[]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:9378},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{},"rftoolsutility:properties":{allowFlying:0b,buffTicks:163,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:[]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],twilightforest_banished:1b},"quark:trying_crawl":0b,simplylight_unlocked:2},Health:19.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"sushigocrafting:soy_seeds"},{Count:6b,Slot:1b,id:"minecraft:jungle_log"},{Count:1b,Slot:2b,id:"minecraft:wheat_seeds"},{Count:7b,Slot:3b,id:"minecraft:poppy"},{Count:3b,Slot:4b,id:"minecraft:red_mushroom"},{Count:1b,Slot:5b,id:"minecraft:moss_carpet"},{Count:63b,Slot:6b,id:"minecraft:sand"},{Count:1b,Slot:7b,id:"minecraft:sand"},{Count:1b,Slot:8b,id:"patchouli:guide_book",tag:{"patchouli:book":"allthemodium:allthemodium_book"}},{Count:1b,Slot:9b,id:"twigs:twig"},{Count:8b,Slot:10b,id:"minecraft:torch"},{Count:8b,Slot:11b,id:"minecraft:cooked_beef"},{Count:16b,Slot:12b,id:"minecraft:stick"},{Count:1b,Slot:13b,id:"productivetrees:upgrade_pollen_sieve"},{Count:1b,Slot:14b,id:"minecraft:map"},{Count:10b,Slot:15b,id:"minecraft:glass"}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-224.14702959137745d,68.0d,-273.3983629481056d],Railways_DataVersion:2,Rotation:[175.87622f,18.346508f],Score:230,SelectedItemSlot:1,SleepTimer:0s,Spigot.ticksLived:9378,UUID:[I;-1627254113,-934460036,-2110585653,-14231042],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-61297774370748L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:12,XpP:0.45161283f,XpSeed:0,XpTotal:230,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752507368069L,keepLevel:0b,lastKnownName:"zhkdxc",lastPlayed:1752508430476L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:1.0289234f,foodLevel:4,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwroofs:magenta_concrete_top_roof","additionallanterns:copper_lantern","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","mcwwindows:lime_mosaic_glass","dyenamics:banner/lavender_banner","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","mcwtrpdoors:jungle_cottage_trapdoor","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","cfm:jungle_coffee_table","mcwroofs:yellow_concrete_roof","dyenamics:banner/aquamarine_banner","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","utilitix:reinforced_rail","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/jungle_logs_to_doors","mcwbiomesoplenty:hellbark_picket_fence","corail_woodcutter:crimson_woodcutter","mcwdoors:jungle_nether_door","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","aquaculture:dark_oak_fish_mount","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","securitycraft:reinforced_bamboo_fence","simplylight:illuminant_light_blue_block_dyed","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","mcwwindows:black_mosaic_glass","simplylight:illuminant_lime_block_toggle","mcwfurnitures:jungle_modern_desk","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","securitycraft:reinforced_cherry_fence_gate","dyenamics:maroon_stained_glass","mcwfences:acacia_hedge","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","mcwroofs:purple_concrete_upper_lower_roof","minecolonies:shapetool","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","rftoolsbuilder:green_shield_template_block","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","mcwfences:acacia_stockade_fence","aquaculture:birch_fish_mount","minecraft:orange_stained_glass","biomesoplenty:mossy_black_sand","aquaculture:wooden_fillet_knife","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwfences:warped_horse_fence","mcwbiomesoplenty:mahogany_curved_gate","twilightforest:canopy_boat","simplylight:illuminant_red_block_toggle","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","dyenamics:mint_stained_glass","dyenamics:aquamarine_concrete_powder","mcwlights:striped_lantern","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","mcwlights:chain_lantern","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfurnitures:stripped_jungle_modern_desk","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","ae2wtlib:wireless_universal_terminal/upgrade_crafting","supplementaries:flags/flag_cyan","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","undergarden:torch_ditchbulb_paste","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:dirt_path_block","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:prismarine_lantern","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:wooden_pickaxe","biomesoplenty:mahogany_boat","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwwindows:orange_mosaic_glass","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","additionallanterns:stone_lantern","utilitarian:utility/charcoal_from_campfire","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwroofs:magenta_concrete_upper_lower_roof","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","aquaculture:double_hook","mcwroofs:cyan_concrete_steep_roof","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","securitycraft:reinforced_yellow_stained_glass","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","minecraft:yellow_concrete_powder","aquaculture:tin_can_to_iron_nugget","minecraft:red_concrete_powder","mcwwindows:white_mosaic_glass_pane","mcwwindows:purple_mosaic_glass","simplylight:illuminant_orange_block_on_toggle","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","dyenamics:spring_green_concrete_powder","mcwroofs:red_concrete_steep_roof","minecraft:glass","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwlights:tavern_lantern","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","minecraft:gray_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwdoors:jungle_bark_glass_door","minecraft:light_blue_concrete_powder","supplementaries:timber_frame","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","supplementaries:daub_cross_brace","mcwwindows:jungle_four_window","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","mcwwindows:light_blue_mosaic_glass","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:soul_oak_tiki_torch","mcwlights:birch_tiki_torch","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","additionallanterns:quartz_lantern","twigs:mossy_cobblestone_bricks","mcwfences:stone_grass_topped_wall","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","cfm:stripped_jungle_bedside_cabinet","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","mcwbiomesoplenty:jacaranda_wired_fence","additionallanterns:normal_nether_bricks_lantern","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","minecraft:light_gray_stained_glass","mcwroofs:cyan_concrete_roof","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwbiomesoplenty:magic_picket_fence","securitycraft:reinforced_purple_stained_glass","farmersdelight:cooking/rabbit_stew","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","botania:mushroom_9","botania:mushroom_8","minecraft:cyan_stained_glass","botania:mushroom_7","securitycraft:reinforced_lime_stained_glass_pane_from_dye","botania:mushroom_6","botania:mushroom_5","botania:mushroom_4","mcwroofs:red_concrete_roof","botania:mushroom_3","securitycraft:reinforced_dark_oak_fence","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","cfm:stripped_jungle_cabinet","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","cfm:jungle_desk","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","mcwfences:nether_brick_pillar_wall","mcwroofs:light_blue_concrete_steep_roof","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","rftoolsbuilder:blue_shield_template_block","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","minecraft:glass_pane","dyenamics:peach_stained_glass","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","mcwbiomesoplenty:maple_curved_gate","mcwroofs:orange_concrete_roof","mcwroofs:lime_concrete_upper_steep_roof","securitycraft:reinforced_brown_stained_glass","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","minecraft:bamboo_raft","mcwroofs:magenta_concrete_roof","enderio:wood_gear","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfurnitures:stripped_jungle_chair","mcwroofs:green_concrete_roof","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","cfm:stripped_jungle_chair","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","securitycraft:reinforced_red_stained_glass_pane_from_dye","mcwroofs:pink_concrete_steep_roof","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","minecraft:white_concrete_powder","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","minecraft:mossy_stone_bricks_from_moss_block","mcwtrpdoors:jungle_swamp_trapdoor","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","simplylight:illuminant_light_gray_block_on_dyed","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwbiomesoplenty:redwood_pyramid_gate","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwwindows:gray_mosaic_glass","mcwbiomesoplenty:hellbark_wired_fence","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","mcwlights:pink_paper_lamp","mcwwindows:magenta_mosaic_glass_pane","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","aquaculture:golden_fishing_rod","mcwwindows:gray_mosaic_glass_pane","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","simplylight:illuminant_light_gray_block_toggle","utilitix:hand_bell","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","undergarden:wigglewood_boat","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwpaths:gravel_path_block","mcwwindows:birch_curtain_rod","mcwfurnitures:jungle_cupboard_counter","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbiomesoplenty:mahogany_highley_gate","immersiveengineering:crafting/torch","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwpaths:red_sand_path_block","allthecompressed:compress/moss_block_1x","mcwdoors:jungle_stable_door","domum_ornamentum:architectscutter","mcwfences:jungle_picket_fence","mcwdoors:print_jungle","dyenamics:ultramarine_concrete_powder","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","undergarden:gloom_o_lantern","mcwlights:dark_oak_tiki_torch","mcwfences:acacia_picket_fence","mcwroofs:grass_roof","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","utilitarian:utility/jungle_logs_to_stairs","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","dyenamics:peach_concrete_powder","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","supplementaries:faucet","simplylight:illuminant_purple_block_toggle","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","mcwfences:railing_blackstone_wall","mcwlights:soul_crimson_tiki_torch","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","supplementaries:flags/flag_light_blue","mcwroofs:blue_concrete_top_roof","minecraft:gray_stained_glass","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwroofs:green_concrete_steep_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:modern_sandstone_wall","allthecompressed:compress/glass_1x","dyenamics:banner/peach_banner","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","farmersdelight:cooking/mushroom_rice","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","biomesoplenty:dead_boat","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","supplementaries:flags/flag_orange","mcwpaths:sand_path_block","mcwfences:mangrove_stockade_fence","mcwlights:soul_cherry_tiki_torch","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","securitycraft:reinforced_mangrove_fence","mcwroofs:grass_lower_roof","dyenamics:conifer_stained_glass","aquaculture:iron_hook","connectedglass:borderless_glass1","aquaculture:diamond_fishing_rod","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","additionallanterns:basalt_lantern","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","forbidden_arcanus:edelwood_ladder","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","securitycraft:reinforced_gray_stained_glass","cfm:jungle_kitchen_counter","simplylight:illuminant_black_block_on_toggle","mcwfurnitures:stripped_jungle_drawer_counter","mcwdoors:jungle_japanese2_door","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","mcwroofs:gray_concrete_roof","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","mcwroofs:yellow_concrete_lower_roof","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","additionallanterns:emerald_lantern","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwwindows:golden_curtain_rod","mcwroofs:magenta_concrete_lower_roof","mcwbiomesoplenty:red_maple_hedge","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","mcwroofs:purple_concrete_roof","mcwfences:railing_mud_brick_wall","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwlights:classic_street_lamp","enderio:conduit_binder_composite","mcwfences:dark_oak_horse_fence","minecraft:bowl","mcwroofs:light_blue_concrete_top_roof","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","dyenamics:rose_stained_glass","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwroofs:black_concrete_upper_lower_roof","minecraft:purple_concrete_powder","mcwroofs:gray_concrete_upper_lower_roof","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwroofs:pink_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwfences:spruce_curved_gate","mcwbiomesoplenty:hellbark_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","minecraft:white_stained_glass","minecraft:wooden_hoe","mcwbiomesoplenty:maple_picket_fence","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","farmersdelight:cutting_board","mcwbiomesoplenty:magic_stockade_fence","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","securitycraft:reinforced_pink_stained_glass","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","mcwdoors:jungle_classic_door","mcwwindows:stripped_jungle_pane_window","mcwfurnitures:jungle_coffee_table","mcwwindows:brown_mosaic_glass_pane","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwroofs:lime_concrete_roof","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","minecraft:wooden_axe","aquaculture:cooked_fish_fillet_from_smoking","utilitarian:utility/jungle_logs_to_slabs","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","mcwdoors:jungle_four_panel_door","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","minecraft:mossy_cobblestone_from_moss_block","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwlights:jungle_tiki_torch","mcwwindows:jungle_plank_four_window","supplementaries:flags/flag_lime","mcwfences:bamboo_wired_fence","mcwwindows:jungle_pane_window","mcwfences:crimson_curved_gate","mcwfurnitures:cabinet_door","additionallanterns:red_sandstone_lantern","allthecompressed:compress/sand_1x","minecraft:magenta_stained_glass","securitycraft:reinforced_green_stained_glass","littlelogistics:fluid_barge","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","supplementaries:flags/flag_magenta","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","minecraft:jungle_wood","mcwroofs:lime_concrete_attic_roof","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","mcwroofs:green_concrete_attic_roof","corail_woodcutter:spruce_woodcutter","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:light_gray_concrete_top_roof","mcwfurnitures:jungle_bookshelf_cupboard","cfm:jungle_desk_cabinet","mcwlights:covered_wall_lantern","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","dyenamics:banner/bubblegum_banner","mcwfences:majestic_metal_fence_gate","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","additionallanterns:amethyst_lantern","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","securitycraft:reinforced_crimson_fence_gate","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwroofs:black_concrete_steep_roof","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","dyenamics:mint_concrete_powder","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwroofs:brown_concrete_roof","mcwfurnitures:jungle_chair","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:red_concrete_upper_steep_roof","minecraft:pink_stained_glass","additionallanterns:gold_lantern","simplylight:illuminant_purple_block_on_toggle","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwtrpdoors:print_beach","cfm:jungle_upgraded_fence","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_barrel_trapdoor","mcwtrpdoors:jungle_ranch_trapdoor","mcwfences:bamboo_curved_gate","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:pine_wired_fence","mcwwindows:black_mosaic_glass_pane","mcwfences:bamboo_highley_gate","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","mcwroofs:jungle_roof","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwfences:jungle_curved_gate","securitycraft:reinforced_red_stained_glass_pane_from_glass","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwlights:striped_wall_lantern","mcwwindows:red_mosaic_glass","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","forbidden_arcanus:runic_glass","mcwroofs:pink_concrete_top_roof","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","farmersdelight:cooking/mushroom_stew","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","supplementaries:daub_frame","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","aquaculture:iron_fishing_rod","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","mcwfurnitures:jungle_desk","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwwindows:green_mosaic_glass","domum_ornamentum:blockbarreldeco_standing","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","supplementaries:jar","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","dyenamics:ultramarine_stained_glass","securitycraft:motion_activated_light","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","mcwfences:double_curved_metal_fence","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","xnet:facade","mcwroofs:red_concrete_attic_roof","mcwfences:nether_brick_railing_gate","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwdoors:jungle_waffle_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwfences:oak_picket_fence","mcwroofs:brown_concrete_top_roof","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","mcwroofs:blue_concrete_upper_steep_roof","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwroofs:lime_concrete_upper_lower_roof","dyenamics:spring_green_stained_glass","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","aquaculture:spruce_fish_mount","mcwroofs:light_gray_concrete_upper_steep_roof","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","aquaculture:redstone_hook","supplementaries:flags/flag_blue","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","rftoolsbuilder:yellow_shield_template_block","mcwroofs:lime_concrete_top_roof","additionallanterns:diamond_lantern","twigs:mossy_bricks_from_moss_block","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","securitycraft:reinforced_purple_stained_glass_pane_from_dye","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aquaculture:bobber","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","securitycraft:reinforced_lime_stained_glass_pane_from_glass","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","mcwwindows:light_gray_mosaic_glass_pane","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","simplylight:illuminant_brown_block_on_dyed","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","ae2wtlib:quantum_bridge_card","supplementaries:sconce","silentgear:rough_rod","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","forbidden_arcanus:deorum_glass","mcwroofs:light_blue_concrete_upper_lower_roof","mcwroofs:light_gray_concrete_steep_roof","mcwbiomesoplenty:palm_wired_fence","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","rftoolsbuilder:space_chamber_card","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","aquaculture:oak_fish_mount","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","mcwfences:red_sandstone_grass_topped_wall","mcwlights:light_blue_paper_lamp","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","additionallanterns:granite_lantern","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","botania:mushroom_11","mcwroofs:orange_concrete_steep_roof","botania:mushroom_10","mcwdoors:jungle_paper_door","botania:mushroom_15","botania:mushroom_14","dyenamics:banner/rose_banner","botania:mushroom_13","botania:mushroom_12","mcwroofs:jungle_upper_lower_roof","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","mcwdoors:jungle_tropical_door","twigs:lamp","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwroofs:brown_concrete_lower_roof","mcwdoors:jungle_glass_door","simplylight:illuminant_cyan_block_dyed","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfurnitures:jungle_triple_drawer","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwwindows:pink_mosaic_glass_pane","minecraft:yellow_stained_glass","mcwfurnitures:jungle_end_table","dyenamics:honey_stained_glass","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:sandstone_grass_topped_wall","mcwfences:bamboo_picket_fence","mcwfurnitures:jungle_lower_bookshelf_drawer","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","paraglider:paraglider","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","supplementaries:timber_cross_brace","simplylight:illuminant_lime_block_on_dyed","mcwroofs:purple_concrete_top_roof","securitycraft:reinforced_white_stained_glass_pane_from_dye","simplylight:illuminant_orange_block_on_dyed","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbridges:jungle_bridge_pier","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfences:bamboo_pyramid_gate","mcwroofs:white_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_picket_fence","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","supplementaries:flower_box","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_orange_stained_glass_pane_from_glass","securitycraft:reinforced_lime_stained_glass","mcwroofs:white_concrete_attic_roof","mcwroofs:blue_concrete_lower_roof","utilitarian:snad/snad","mcwwindows:blue_mosaic_glass","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:snowblossom_hedge","mcwlights:cross_lantern","minecraft:oak_boat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwwindows:jungle_window2","mcwtrpdoors:jungle_mystic_trapdoor","mcwroofs:lime_concrete_lower_roof","dyenamics:conifer_concrete_powder","minecraft:glass_bottle","mcwfences:mesh_metal_fence","mcwbiomesoplenty:empyreal_picket_fence","mcwtrpdoors:jungle_glass_trapdoor","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwbiomesoplenty:pine_curved_gate","supplementaries:flags/flag_gray","securitycraft:reinforced_light_gray_stained_glass","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","botania:speed_up_belt","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","simplylight:edge_light","mcwlights:bamboo_tiki_torch","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","mcwroofs:grass_steep_roof","cfm:stripped_jungle_kitchen_drawer","dyenamics:wine_stained_glass","mcwdoors:jungle_stable_head_door","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","supplementaries:slice_map","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:yellow_concrete_steep_roof","mcwroofs:magenta_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:stone_pillar_wall","mcwfences:birch_hedge","securitycraft:reinforced_light_blue_stained_glass","simplylight:illuminant_gray_block_toggle","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","mcwfences:spruce_stockade_fence","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwlights:mangrove_tiki_torch","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","utilitix:armed_stand","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","supplementaries:flags/flag_purple","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:palm_hedge","mcwlights:green_paper_lamp","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","mcwfences:oak_hedge","mcwfurnitures:stripped_jungle_coffee_table","mcwfences:railing_sandstone_wall","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwroofs:magenta_concrete_attic_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","mcwfurnitures:stripped_jungle_double_drawer","additionallanterns:warped_lantern","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","minecraft:spruce_boat","dyenamics:banner/navy_banner","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","mcwwindows:pink_mosaic_glass","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:lime_stained_glass","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwroofs:grass_upper_lower_roof","mcwlights:bell_wall_lantern","dyenamics:fluorescent_concrete_powder","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","minecraft:black_stained_glass"],toBeDisplayed:["securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwroofs:magenta_concrete_top_roof","additionallanterns:copper_lantern","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","mcwwindows:lime_mosaic_glass","dyenamics:banner/lavender_banner","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","mcwtrpdoors:jungle_cottage_trapdoor","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","cfm:jungle_coffee_table","mcwroofs:yellow_concrete_roof","dyenamics:banner/aquamarine_banner","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","utilitix:reinforced_rail","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/jungle_logs_to_doors","mcwbiomesoplenty:hellbark_picket_fence","corail_woodcutter:crimson_woodcutter","mcwdoors:jungle_nether_door","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","aquaculture:dark_oak_fish_mount","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","securitycraft:reinforced_bamboo_fence","simplylight:illuminant_light_blue_block_dyed","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","mcwwindows:black_mosaic_glass","simplylight:illuminant_lime_block_toggle","mcwfurnitures:jungle_modern_desk","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","securitycraft:reinforced_cherry_fence_gate","dyenamics:maroon_stained_glass","mcwfences:acacia_hedge","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","mcwroofs:purple_concrete_upper_lower_roof","minecolonies:shapetool","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","rftoolsbuilder:green_shield_template_block","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","mcwfences:acacia_stockade_fence","aquaculture:birch_fish_mount","minecraft:orange_stained_glass","biomesoplenty:mossy_black_sand","aquaculture:wooden_fillet_knife","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwfences:warped_horse_fence","mcwbiomesoplenty:mahogany_curved_gate","twilightforest:canopy_boat","simplylight:illuminant_red_block_toggle","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","dyenamics:mint_stained_glass","dyenamics:aquamarine_concrete_powder","mcwlights:striped_lantern","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","mcwlights:chain_lantern","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfurnitures:stripped_jungle_modern_desk","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","ae2wtlib:wireless_universal_terminal/upgrade_crafting","supplementaries:flags/flag_cyan","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","undergarden:torch_ditchbulb_paste","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:dirt_path_block","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:prismarine_lantern","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:wooden_pickaxe","biomesoplenty:mahogany_boat","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwwindows:orange_mosaic_glass","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","additionallanterns:stone_lantern","utilitarian:utility/charcoal_from_campfire","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwroofs:magenta_concrete_upper_lower_roof","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","aquaculture:double_hook","mcwroofs:cyan_concrete_steep_roof","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","securitycraft:reinforced_yellow_stained_glass","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","minecraft:yellow_concrete_powder","aquaculture:tin_can_to_iron_nugget","minecraft:red_concrete_powder","mcwwindows:white_mosaic_glass_pane","mcwwindows:purple_mosaic_glass","simplylight:illuminant_orange_block_on_toggle","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","dyenamics:spring_green_concrete_powder","mcwroofs:red_concrete_steep_roof","minecraft:glass","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwlights:tavern_lantern","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","minecraft:gray_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwdoors:jungle_bark_glass_door","minecraft:light_blue_concrete_powder","supplementaries:timber_frame","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","supplementaries:daub_cross_brace","mcwwindows:jungle_four_window","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","mcwwindows:light_blue_mosaic_glass","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:soul_oak_tiki_torch","mcwlights:birch_tiki_torch","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","additionallanterns:quartz_lantern","twigs:mossy_cobblestone_bricks","mcwfences:stone_grass_topped_wall","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","cfm:stripped_jungle_bedside_cabinet","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","mcwbiomesoplenty:jacaranda_wired_fence","additionallanterns:normal_nether_bricks_lantern","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","minecraft:light_gray_stained_glass","mcwroofs:cyan_concrete_roof","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwbiomesoplenty:magic_picket_fence","securitycraft:reinforced_purple_stained_glass","farmersdelight:cooking/rabbit_stew","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","botania:mushroom_9","botania:mushroom_8","minecraft:cyan_stained_glass","botania:mushroom_7","securitycraft:reinforced_lime_stained_glass_pane_from_dye","botania:mushroom_6","botania:mushroom_5","botania:mushroom_4","mcwroofs:red_concrete_roof","botania:mushroom_3","securitycraft:reinforced_dark_oak_fence","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","cfm:stripped_jungle_cabinet","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","cfm:jungle_desk","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","mcwfences:nether_brick_pillar_wall","mcwroofs:light_blue_concrete_steep_roof","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","rftoolsbuilder:blue_shield_template_block","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","minecraft:glass_pane","dyenamics:peach_stained_glass","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","mcwbiomesoplenty:maple_curved_gate","mcwroofs:orange_concrete_roof","mcwroofs:lime_concrete_upper_steep_roof","securitycraft:reinforced_brown_stained_glass","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","minecraft:bamboo_raft","mcwroofs:magenta_concrete_roof","enderio:wood_gear","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfurnitures:stripped_jungle_chair","mcwroofs:green_concrete_roof","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","cfm:stripped_jungle_chair","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","securitycraft:reinforced_red_stained_glass_pane_from_dye","mcwroofs:pink_concrete_steep_roof","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","minecraft:white_concrete_powder","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","minecraft:mossy_stone_bricks_from_moss_block","mcwtrpdoors:jungle_swamp_trapdoor","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","simplylight:illuminant_light_gray_block_on_dyed","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwbiomesoplenty:redwood_pyramid_gate","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwwindows:gray_mosaic_glass","mcwbiomesoplenty:hellbark_wired_fence","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","mcwlights:pink_paper_lamp","mcwwindows:magenta_mosaic_glass_pane","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","aquaculture:golden_fishing_rod","mcwwindows:gray_mosaic_glass_pane","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","simplylight:illuminant_light_gray_block_toggle","utilitix:hand_bell","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","undergarden:wigglewood_boat","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwpaths:gravel_path_block","mcwwindows:birch_curtain_rod","mcwfurnitures:jungle_cupboard_counter","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbiomesoplenty:mahogany_highley_gate","immersiveengineering:crafting/torch","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwpaths:red_sand_path_block","allthecompressed:compress/moss_block_1x","mcwdoors:jungle_stable_door","domum_ornamentum:architectscutter","mcwfences:jungle_picket_fence","mcwdoors:print_jungle","dyenamics:ultramarine_concrete_powder","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","undergarden:gloom_o_lantern","mcwlights:dark_oak_tiki_torch","mcwfences:acacia_picket_fence","mcwroofs:grass_roof","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","utilitarian:utility/jungle_logs_to_stairs","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","dyenamics:peach_concrete_powder","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","supplementaries:faucet","simplylight:illuminant_purple_block_toggle","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","mcwfences:railing_blackstone_wall","mcwlights:soul_crimson_tiki_torch","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","supplementaries:flags/flag_light_blue","mcwroofs:blue_concrete_top_roof","minecraft:gray_stained_glass","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwroofs:green_concrete_steep_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:modern_sandstone_wall","allthecompressed:compress/glass_1x","dyenamics:banner/peach_banner","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","farmersdelight:cooking/mushroom_rice","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","biomesoplenty:dead_boat","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","supplementaries:flags/flag_orange","mcwpaths:sand_path_block","mcwfences:mangrove_stockade_fence","mcwlights:soul_cherry_tiki_torch","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","securitycraft:reinforced_mangrove_fence","mcwroofs:grass_lower_roof","dyenamics:conifer_stained_glass","aquaculture:iron_hook","connectedglass:borderless_glass1","aquaculture:diamond_fishing_rod","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","additionallanterns:basalt_lantern","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","forbidden_arcanus:edelwood_ladder","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","securitycraft:reinforced_gray_stained_glass","cfm:jungle_kitchen_counter","simplylight:illuminant_black_block_on_toggle","mcwfurnitures:stripped_jungle_drawer_counter","mcwdoors:jungle_japanese2_door","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","mcwroofs:gray_concrete_roof","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","mcwroofs:yellow_concrete_lower_roof","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","additionallanterns:emerald_lantern","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwwindows:golden_curtain_rod","mcwroofs:magenta_concrete_lower_roof","mcwbiomesoplenty:red_maple_hedge","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","mcwroofs:purple_concrete_roof","mcwfences:railing_mud_brick_wall","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwlights:classic_street_lamp","enderio:conduit_binder_composite","mcwfences:dark_oak_horse_fence","minecraft:bowl","mcwroofs:light_blue_concrete_top_roof","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","dyenamics:rose_stained_glass","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwroofs:black_concrete_upper_lower_roof","minecraft:purple_concrete_powder","mcwroofs:gray_concrete_upper_lower_roof","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwroofs:pink_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwfences:spruce_curved_gate","mcwbiomesoplenty:hellbark_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","minecraft:white_stained_glass","minecraft:wooden_hoe","mcwbiomesoplenty:maple_picket_fence","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","farmersdelight:cutting_board","mcwbiomesoplenty:magic_stockade_fence","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","securitycraft:reinforced_pink_stained_glass","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","mcwdoors:jungle_classic_door","mcwwindows:stripped_jungle_pane_window","mcwfurnitures:jungle_coffee_table","mcwwindows:brown_mosaic_glass_pane","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwroofs:lime_concrete_roof","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","minecraft:wooden_axe","aquaculture:cooked_fish_fillet_from_smoking","utilitarian:utility/jungle_logs_to_slabs","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","mcwdoors:jungle_four_panel_door","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","minecraft:mossy_cobblestone_from_moss_block","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwlights:jungle_tiki_torch","mcwwindows:jungle_plank_four_window","supplementaries:flags/flag_lime","mcwfences:bamboo_wired_fence","mcwwindows:jungle_pane_window","mcwfences:crimson_curved_gate","mcwfurnitures:cabinet_door","additionallanterns:red_sandstone_lantern","allthecompressed:compress/sand_1x","minecraft:magenta_stained_glass","securitycraft:reinforced_green_stained_glass","littlelogistics:fluid_barge","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","supplementaries:flags/flag_magenta","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","minecraft:jungle_wood","mcwroofs:lime_concrete_attic_roof","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","mcwroofs:green_concrete_attic_roof","corail_woodcutter:spruce_woodcutter","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:light_gray_concrete_top_roof","mcwfurnitures:jungle_bookshelf_cupboard","cfm:jungle_desk_cabinet","mcwlights:covered_wall_lantern","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","dyenamics:banner/bubblegum_banner","mcwfences:majestic_metal_fence_gate","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","additionallanterns:amethyst_lantern","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","securitycraft:reinforced_crimson_fence_gate","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwroofs:black_concrete_steep_roof","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","dyenamics:mint_concrete_powder","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwroofs:brown_concrete_roof","mcwfurnitures:jungle_chair","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:red_concrete_upper_steep_roof","minecraft:pink_stained_glass","additionallanterns:gold_lantern","simplylight:illuminant_purple_block_on_toggle","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwtrpdoors:print_beach","cfm:jungle_upgraded_fence","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_barrel_trapdoor","mcwtrpdoors:jungle_ranch_trapdoor","mcwfences:bamboo_curved_gate","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:pine_wired_fence","mcwwindows:black_mosaic_glass_pane","mcwfences:bamboo_highley_gate","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","mcwroofs:jungle_roof","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwfences:jungle_curved_gate","securitycraft:reinforced_red_stained_glass_pane_from_glass","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwlights:striped_wall_lantern","mcwwindows:red_mosaic_glass","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","forbidden_arcanus:runic_glass","mcwroofs:pink_concrete_top_roof","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","farmersdelight:cooking/mushroom_stew","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","supplementaries:daub_frame","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","aquaculture:iron_fishing_rod","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","mcwfurnitures:jungle_desk","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwwindows:green_mosaic_glass","domum_ornamentum:blockbarreldeco_standing","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","supplementaries:jar","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","dyenamics:ultramarine_stained_glass","securitycraft:motion_activated_light","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","mcwfences:double_curved_metal_fence","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","xnet:facade","mcwroofs:red_concrete_attic_roof","mcwfences:nether_brick_railing_gate","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwdoors:jungle_waffle_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwfences:oak_picket_fence","mcwroofs:brown_concrete_top_roof","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","mcwroofs:blue_concrete_upper_steep_roof","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwroofs:lime_concrete_upper_lower_roof","dyenamics:spring_green_stained_glass","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","aquaculture:spruce_fish_mount","mcwroofs:light_gray_concrete_upper_steep_roof","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","aquaculture:redstone_hook","supplementaries:flags/flag_blue","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","rftoolsbuilder:yellow_shield_template_block","mcwroofs:lime_concrete_top_roof","additionallanterns:diamond_lantern","twigs:mossy_bricks_from_moss_block","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","securitycraft:reinforced_purple_stained_glass_pane_from_dye","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aquaculture:bobber","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","securitycraft:reinforced_lime_stained_glass_pane_from_glass","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","mcwwindows:light_gray_mosaic_glass_pane","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","simplylight:illuminant_brown_block_on_dyed","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","ae2wtlib:quantum_bridge_card","supplementaries:sconce","silentgear:rough_rod","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","forbidden_arcanus:deorum_glass","mcwroofs:light_blue_concrete_upper_lower_roof","mcwroofs:light_gray_concrete_steep_roof","mcwbiomesoplenty:palm_wired_fence","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","rftoolsbuilder:space_chamber_card","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","aquaculture:oak_fish_mount","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","mcwfences:red_sandstone_grass_topped_wall","mcwlights:light_blue_paper_lamp","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","additionallanterns:granite_lantern","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","botania:mushroom_11","mcwroofs:orange_concrete_steep_roof","botania:mushroom_10","mcwdoors:jungle_paper_door","botania:mushroom_15","botania:mushroom_14","dyenamics:banner/rose_banner","botania:mushroom_13","botania:mushroom_12","mcwroofs:jungle_upper_lower_roof","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","mcwdoors:jungle_tropical_door","twigs:lamp","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwroofs:brown_concrete_lower_roof","mcwdoors:jungle_glass_door","simplylight:illuminant_cyan_block_dyed","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfurnitures:jungle_triple_drawer","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwwindows:pink_mosaic_glass_pane","minecraft:yellow_stained_glass","mcwfurnitures:jungle_end_table","dyenamics:honey_stained_glass","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:sandstone_grass_topped_wall","mcwfences:bamboo_picket_fence","mcwfurnitures:jungle_lower_bookshelf_drawer","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","paraglider:paraglider","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","supplementaries:timber_cross_brace","simplylight:illuminant_lime_block_on_dyed","mcwroofs:purple_concrete_top_roof","securitycraft:reinforced_white_stained_glass_pane_from_dye","simplylight:illuminant_orange_block_on_dyed","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbridges:jungle_bridge_pier","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfences:bamboo_pyramid_gate","mcwroofs:white_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_picket_fence","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","supplementaries:flower_box","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_orange_stained_glass_pane_from_glass","securitycraft:reinforced_lime_stained_glass","mcwroofs:white_concrete_attic_roof","mcwroofs:blue_concrete_lower_roof","utilitarian:snad/snad","mcwwindows:blue_mosaic_glass","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:snowblossom_hedge","mcwlights:cross_lantern","minecraft:oak_boat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwwindows:jungle_window2","mcwtrpdoors:jungle_mystic_trapdoor","mcwroofs:lime_concrete_lower_roof","dyenamics:conifer_concrete_powder","minecraft:glass_bottle","mcwfences:mesh_metal_fence","mcwbiomesoplenty:empyreal_picket_fence","mcwtrpdoors:jungle_glass_trapdoor","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwbiomesoplenty:pine_curved_gate","supplementaries:flags/flag_gray","securitycraft:reinforced_light_gray_stained_glass","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","botania:speed_up_belt","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","simplylight:edge_light","mcwlights:bamboo_tiki_torch","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","mcwroofs:grass_steep_roof","cfm:stripped_jungle_kitchen_drawer","dyenamics:wine_stained_glass","mcwdoors:jungle_stable_head_door","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","supplementaries:slice_map","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:yellow_concrete_steep_roof","mcwroofs:magenta_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:stone_pillar_wall","mcwfences:birch_hedge","securitycraft:reinforced_light_blue_stained_glass","simplylight:illuminant_gray_block_toggle","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","mcwfences:spruce_stockade_fence","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwlights:mangrove_tiki_torch","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","utilitix:armed_stand","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","supplementaries:flags/flag_purple","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:palm_hedge","mcwlights:green_paper_lamp","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","mcwfences:oak_hedge","mcwfurnitures:stripped_jungle_coffee_table","mcwfences:railing_sandstone_wall","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwroofs:magenta_concrete_attic_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","mcwfurnitures:stripped_jungle_double_drawer","additionallanterns:warped_lantern","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","minecraft:spruce_boat","dyenamics:banner/navy_banner","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","mcwwindows:pink_mosaic_glass","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:lime_stained_glass","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwroofs:grass_upper_lower_roof","mcwlights:bell_wall_lantern","dyenamics:fluorescent_concrete_powder","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:9378,warning_level:0}}