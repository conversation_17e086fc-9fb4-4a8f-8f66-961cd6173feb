{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:397,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:4b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:397,Id:11,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:resistance"},{Ambient:0b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:8,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:jump_boost"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:397,Id:222,ShowIcon:0b,ShowParticles:0b,"forge:id":"potionsmaster:unobtainiumpotioneffect"},{Ambient:1b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:397,Id:43,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:knowledge"}],Air:300s,Attributes:[{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"attributeslib:life_steal"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.05d,Name:"attributeslib:crit_chance"},{Base:0.0d,Name:"attributeslib:prot_pierce"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,1341410454,-1583091198,1302321304]}],Name:"attributeslib:creative_flight"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.5d,Name:"attributeslib:crit_damage"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:20.0d,Modifiers:[{Amount:4.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:1.0d,Modifiers:[{Amount:16.0d,Name:"effect.attributeslib.knowledge 1",Operation:2,UUID:[I;**********,**********,-**********,-**********]}],Name:"attributeslib:experience_gained"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;41877505,211963738,-**********,-**********],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"ae2wtlib:wireless_universal_terminal",tag:{accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},blankPattern:[{Count:10b,Slot:0,id:"ae2:blank_pattern"}],crafting:1b,craftingGrid:[{Count:1b,Slot:1,id:"allthemodium:unobtainium_ingot"}],currentTerminal:"crafting",encodedInputs:[{"#":1L,"#c":"ae2:i",id:"minecraft:cobblestone"}],encodedOutputs:[{"#":1L,"#c":"ae2:i",id:"minecraft:gravel"}],filter_type:"ALL",internalCurrentPower:4800000.0d,internalMaxPower:4800000.0d,mode:"PROCESSING",pattern_access:1b,pattern_encoding:1b,show_pattern_providers:"VISIBLE",sort_by:"NAME",sort_direction:"ASCENDING",substitute:0b,substituteFluids:0b,view_mode:"ALL"}}],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"simplemagnets:basicmagnet",tag:{active:0b}}],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:1,id:"artifacts:bunny_hoppers"}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:night_vision_goggles"}],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[{Count:1b,Slot:0,id:"irons_spellbooks:frostward_ring"},{Count:1b,Slot:1,id:"botania:loki_ring",tag:{soulbindUUID:"4eb6f29f-2455-4f7a-b3df-2f07f67d18ac",xOrigin:0,yOrigin:-2147483648,zOrigin:0}}],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:gold_backpack",tag:{contentsUuid:[I;1992353911,-808105343,-2108701531,-2768561],inventorySlots:81,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}}]},upgradeSlots:3}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:netherite_gloves",tag:{Damage:575,Enchantments:[{id:"ensorcellation:soulbound",lvl:1s}]}}],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:63,wirelessNetwork:3},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:1,furnaces:{furnace0:{X:-31,Y:253,Z:-8}}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:183991},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:153},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE1:"emerald",MAHOUTSUKAI_LAST_RECIPE2:"iron",MAHOUTSUKAI_LAST_RECIPE3:"iron",MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:0b,MAHOUTSUKAI_MANA_UP_COUNTER:70,MAHOUTSUKAI_PAGE:28,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:1b,MAHOUTSUKAI_PLAYER_MAX_MANA:104,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:104,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"allthemodium:unobtainium_nugget_from_ingot",ItemStack:{Count:9b,id:"allthemodium:unobtainium_nugget"}}],SelectedRecipe:"allthemodium:unobtainium_nugget_from_ingot"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:150,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:1b,isshrunk:0b,scale:10.51f,width:0.0f},"solcarrot:food":{foodList:["minecraft:mutton","minecraft:carrot","minecraft:baked_potato","potionsmaster:bezoar","caupona:water","croptopia:toast","nethersdelight:hoglin_loin","maidensmerrymaking:chocolate_bunny","aquaculture:atlantic_herring","minecraft:bread","croptopia:fruit_salad","allthemodium:ancient_soulberries","minecraft:melon_slice","minecraft:chicken","artifacts:eternal_steak"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:360s,knowledge:36,perks:[{id:0s,level:2b},{id:10s,level:3b}]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:3b,MythicBotanyPlayerInfo:{},PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:-6047314239421L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-6047314239421L,UID:[I;2115829805,1282752843,-1294489897,-42637522]},{FromDim:"minecraft:overworld",FromPos:-6597070016445L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:274877898813L,UID:[I;-*********,-1409136227,-1803431229,-*********]},{FromDim:"minecraft:the_nether",FromPos:549755805757L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;2139506912,-450344425,-1081592081,-1074743686]},{FromDim:"minecraft:overworld",FromPos:-824633905086L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:274877898813L,UID:[I;1009578179,-1021687873,-1287994535,743039712]},{FromDim:"minecraft:overworld",FromPos:96207268925505L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;-290836869,-1669838777,-1236047150,-1044263544]},{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;1198727473,-664190546,-1927668513,-1424407168]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["4f5f98b0-b346-45b6-83f1-59fad95c9199","70e61f07-a350-437a-ae01-15e3ef9a1bab","a6c7cf59-f9ce-4cee-a4a2-87f3a130c769","3c0ccabf-fdf5-42f9-8d79-************","4bd2eaba-34b7-42f1-9686-b242b09f7396","acbb9b65-af61-4518-8a6d-3385b8af8c1b","cda626e7-708a-4089-b4f4-6d22ebbd9c6a","6c8881a8-dbdf-465b-bb3e-0c97f47d45a4","db9fa2f7-bedb-4c7b-91c7-5ef71c247526","d1811b83-cc8c-4821-80c6-3e5ce576ae62","88320f8d-ea59-4cc6-896d-79cc52d4aa80","42d85d81-dfae-42b5-ab86-9ebce283d4c6"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:25,tb_last_ground_location_y:263,tb_last_ground_location_z:2,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},"apoth.affix_cooldown.apotheosis:armor/mob_effect/blinding":3040003L,"apoth.affix_cooldown.apotheosis:sword/mob_effect/sophisticated":3154116L,apoth_reforge_seed:-226540028,"quark:locked_once":1b,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedBackpackSettings:{},sophisticatedStorageSettings:{}},Health:24.0f,HurtByTimestamp:182326,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"allthemodium:unobtainium_sword",tag:{Enchantments:[{id:"minecraft:sharpness",lvl:6s},{id:"tombstone:decrepitude",lvl:13s},{id:"minecraft:looting",lvl:7s},{id:"tombstone:ruthless_strike",lvl:12s},{id:"evilcraft:life_stealing",lvl:6s}],RepairCost:31,affix_data:{affixes:{"apotheosis:sword/attribute/intricate":1.0f,"apotheosis:sword/attribute/lacerating":1.0f,"apotheosis:sword/attribute/vampiric":0.31685f,"apotheosis:sword/attribute/violent":1.0f,"apotheosis:sword/mob_effect/sophisticated":1.0f,"apotheosis:sword/special/festive":1.0f,"apotheosis:sword/special/thunderstruck":0.32461303f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/attribute/violent"},"",{"translate":"affix.apotheosis:sword/special/festive.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-966049454,**********,-**********,**********]]}}},{Count:1b,Slot:1b,id:"allthemodium:vibranium_pickaxe",tag:{Damage:107,Enchantments:[{id:"minecraft:fortune",lvl:8s},{id:"minecraft:mending",lvl:1s},{id:"minecraft:efficiency",lvl:5s}],RepairCost:63}},{Count:1b,Slot:2b,id:"mekanism:configurator",tag:{Enchantments:[{id:"tombstone:soulbound",lvl:1s}],RepairCost:1,mekData:{EnergyContainers:[{Container:0b,stored:"60000"}],state:10}}},{Count:1b,Slot:3b,id:"pneumaticcraft:micromissiles",tag:{Damage:5,Enchantments:[{id:"minecraft:mending",lvl:1s},{id:"minecraft:unbreaking",lvl:4s},{id:"tombstone:soulbound",lvl:1s}],damage:0.66201514f,filter:"",fireMode:"DUMB",px:76,py:66,topSpeed:0.19101465f,turnSpeed:0.14697023f}},{Count:60b,Slot:4b,id:"allthemodium:ancient_soulberries"},{Count:1b,Slot:6b,id:"tiab:time_in_a_bottle",tag:{storedTime:860,totalAccumulatedTime:181920}},{Count:1b,Slot:100b,id:"allthemodium:unobtainium_boots",tag:{Enchantments:[{id:"apotheosis:stable_footing",lvl:1s}],RepairCost:1}},{Count:1b,Slot:101b,id:"allthemodium:unobtainium_leggings",tag:{Damage:0}},{Count:1b,Slot:102b,id:"allthemodium:unobtainium_chestplate",tag:{Damage:0}},{Count:1b,Slot:103b,id:"allthemodium:unobtainium_helmet",tag:{Damage:0,affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.6379978f,"apotheosis:armor/attribute/ironforged":0.8256452f,"apotheosis:armor/attribute/stalwart":0.66032857f,"apotheosis:armor/dmg_reduction/runed":0.14106661f,"apotheosis:armor/mob_effect/blinding":0.55516964f,"apotheosis:durable":0.32999998f,"irons_spellbooks:armor/attribute/cooldown":0.42494625f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/dmg_reduction/runed"},"",{"translate":"affix.irons_spellbooks:armor/attribute/cooldown.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;480213758,1185893786,-1992353704,-713124725]]}}}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:the_end",pos:[I;-20,3,377]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-6.64654112948327d,254.625d,-26.749765360403387d],Railways_DataVersion:2,Rotation:[57.98645f,14.549978f],Score:631716,SelectedItemSlot:0,SleepTimer:0s,SpawnAngle:91.04966f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:-19,SpawnY:66,SpawnZ:-66,Spigot.ticksLived:183991,UUID:[I;1320612511,609570682,-1277219065,-159573844],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-1649267560195L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:382,XpP:0.66615963f,XpSeed:1348356826,XpTotal:598988,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752055593076L,keepLevel:0b,lastKnownName:"laowoQAQ",lastPlayed:1752240762461L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.298479f,foodLevel:20,foodSaturationLevel:7.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","croptopia:mead","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwdoors:bamboo_stable_door","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","botania:ender_hand","minecraft:stonecutter","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","mcwpaths:dark_prismarine_dumble_paving","minecraft:bone_block","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","minecraft:melon","ae2:network/cables/dense_smart_green","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","ad_astra:cyan_industrial_lamp","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","cfm:jungle_coffee_table","mcwpaths:granite_windmill_weave_slab","xnet:connector_green","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","create:oak_window","minecolonies:cheese_ravioli","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","botania:missile_rod","mekanism:chemical_tank/basic","create:crafting/kinetics/light_gray_seat","sophisticatedbackpacks:void_upgrade","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","railcraft:bushing_gear_bronze","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","undergarden:gloomgourd_pie","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","appbot:mana_storage_cell_16k","buildinggadgets2:gadget_cut_paste","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","rftoolsbuilder:mover_status","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwpaths:granite_running_bond_slab","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","create:crafting/kinetics/gearbox","sophisticatedstorage:copper_to_iron_tier_upgrade","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","rftoolsutility:screen_link","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mekanism:fluid_tank/basic","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","botania:elementium_sword","supplementaries:bubble_blower","naturalist:glow_goop","supplementaries:crank","securitycraft:secret_bamboo_sign_item","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:magenta_dye_from_blue_red_white_dye","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","allthecompressed:compress/ancient_stone_1x","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","minecraft:pink_terracotta","aiotbotania:livingrock_axe","mcwbiomesoplenty:mahogany_plank_pane_window","rftoolsutility:destination_analyzer","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","ae2:materials/cardfuzzy","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mekanism:energy_tablet","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","allthecompressed:compress/quartz_block_1x","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","minecraft:golden_hoe","cfm:oak_kitchen_counter","minecraft:fire_charge","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:dye_green_bed","alltheores:platinum_dust_from_hammer_crushing","railcraft:any_detector","connectedglass:borderless_glass_purple2","mcwpaths:dark_prismarine_clover_paving","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","sophisticatedstorage:controller","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","mcwtrpdoors:bamboo_mystic_trapdoor","minecraft:stone_button","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","farmingforblockheads:market","minecraft:sandstone","mcwwindows:birch_plank_window","allthemodium:vibranium_ingot_from_raw_blasting","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:lodestone","railcraft:brass_ingot_crafted_with_ingots","megacells:crafting/greater_energy_card","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwpaths:sandstone_running_bond_stairs","minecraft:polished_blackstone_slab","domum_ornamentum:blue_brick_extra","create:granite_from_stone_types_granite_stonecutting","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","mysticalagriculture:essence/common/silicon","aether:red_cape","pneumaticcraft:transfer_gadget","connectedglass:borderless_glass_red2","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","allthemodium:unobtainium_dust_from_ore_crushing","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","botania:open_bucket","blue_skies:glowing_blinding_stone","ae2:tools/paintballs_light_blue","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","tombstone:grave_plate","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","enderio:resetting_lever_five","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","undergarden:depthrock_button","ae2:network/parts/terminals","securitycraft:keypad_frame","minecolonies:pasta_tomato","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","allthemodium:smithing/vibranium_helmet_smithing","pneumaticcraft:vortex_tube","supplementaries:candle_holders/candle_holder_red_dye","xnet:connector_routing","mcwwindows:warped_louvered_shutter","blue_skies:glowing_nature_stone","aether:iron_shovel_repairing","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:palm_plank_window2","travelersbackpack:cake","megacells:network/mega_interface","mcwroofs:green_terracotta_attic_roof","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","immersiveengineering:crafting/shield","utilitix:directional_highspeed_rail","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","ae2:network/crafting/cpu_crafting_monitor","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","create:cut_granite_bricks_from_stone_types_granite_stonecutting","everythingcopper:copper_chestplate","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","mcwroofs:prismarine_brick_top_roof","cfm:warped_mail_box","mekanism:fluid_tank/elite","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","rftoolsutility:crafter1","ae2:network/blocks/storage_chest","securitycraft:secret_bamboo_hanging_sign","cfm:jungle_desk","mcwroofs:oak_roof","aether:diamond_boots_repairing","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwroofs:light_gray_roof_slab","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","mcwbridges:balustrade_prismarine_bricks_bridge","minecraft:jungle_trapdoor","ae2:network/wireless_booster","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","ae2:tools/paintballs_orange","mcwfences:blackstone_brick_railing_gate","mcwtrpdoors:bamboo_tropical_trapdoor","sophisticatedbackpacks:filter_upgrade","minecraft:granite_stairs_from_granite_stonecutting","aether:skyroot_barrel","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","minecraft:iron_ingot_from_nuggets","ae2:network/cables/dense_covered_purple","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","undergarden:depthrock_bricks_stonecutting","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","minecraft:gray_bed","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","supplementaries:evilcraft/sign_post_undead","xnet:wireless_router","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","pneumaticcraft:compressed_bricks_from_tile_stonecutting","twigs:chiseled_bricks","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","cfm:red_sofa","dyenamics:navy_wool","mcwwindows:magenta_mosaic_glass_pane","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","rftoolscontrol:tank","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","reliquary:void_tear","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","appbot:mana_cell_housing","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","minecraft:chiseled_stone_bricks_stone_from_stonecutting","croptopia:potato_chips","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","botania:red_string_fertilizer","minecraft:netherite_sword_smithing","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","croptopia:shaped_toast_sandwich","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","megacells:cells/mega_item_cell_housing","minecolonies:mutton_dinner","allthecompressed:compress/allthemodium_block_1x","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","delightful:food/baklava","oceansdelight:seagrass_salad","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","comforts:hammock_to_black","arseng:source_storage_cell_16k","railcraft:signal_circuit","ae2:tools/paintballs_red","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","minecraft:prismarine_brick_slab_from_prismarine_stonecutting","evilcraft:special/vengeance_pickaxe","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","connectedglass:borderless_glass_pane3","croptopia:buttered_toast","minecraft:gray_banner","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","rftoolsbuilder:vehicle_builder","dyenamics:ultramarine_terracotta","cfm:red_grill","ae2:network/cells/fluid_storage_cell_16k_storage","ae2:tools/paintballs_gray","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","dankstorage:1_to_2","ad_astra:blue_industrial_lamp","supplementaries:flags/flag_light_blue","mcwpaths:cobbled_deepslate_running_bond","cfm:birch_kitchen_sink_light","allthemodium:ancient_chiseled_stone_bricks_from_crushing","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","croptopia:chicken_and_dumplings","mcwwindows:dark_oak_plank_four_window","mekanism:transmitter/mechanical_pipe/basic","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwpaths:granite_flagstone_path","utilitix:tiny_charcoal_to_tiny","connectedglass:borderless_glass_light_blue2","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","mahoutsukai:powdered_emerald","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","alltheores:smelting_dust/osmium_ingot","additionallanterns:prismarine_chain","nethersdelight:hoglin_sirloin_from_campfire_cooking","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","supplementaries:sign_post_oak","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwroofs:dark_prismarine_steep_roof","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","pneumaticcraft:air_cannon","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","additionallanterns:granite_chain","bigreactors:turbine/basic/passivetap_fe","mcwpaths:dark_prismarine_windmill_weave_slab","mcwpaths:brick_running_bond","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","create:small_granite_brick_wall_from_stone_types_granite_stonecutting","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwpaths:granite_honeycomb_paving","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwroofs:gray_attic_roof","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:blue_candle","railcraft:blast_furnace_bricks","ae2:network/cables/dense_covered_pink","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","reliquary:uncrafting/glass_bottle","ae2:tools/portable_item_cell_256k","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","securitycraft:secret_crimson_sign_item","mcwroofs:purple_terracotta_upper_lower_roof","ae2:network/cells/item_storage_cell_16k_storage","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","ae2:network/parts/cable_anchor","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","blue_skies:cake_compat","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","pneumaticcraft:liquid_hopper","gtceu:smelting/smelt_dust_iron_to_ingot","handcrafted:wood_cup","botania:green_pavement","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","ad_astra:ti_69","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","croptopia:fried_chicken","railcraft:sheep_detector","minecraft:tin_ingot_from_smelting_tin_raw","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","rftoolsbuilder:shape_card_quarry","mcwbiomesoplenty:magic_hedge","evilcraft:crafting/undead_planks","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime2","ae2:network/parts/terminals_crafting","xnet:netcable_blue","cfm:stripped_oak_chair","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","connectedglass:scratched_glass_black2","cfm:rock_path","botania:mana_spreader","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","minecraft:quartz_pillar","botania:slingshot","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:network/cables/glass_gray","ae2:tools/certus_quartz_pickaxe","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","minecraft:dye_red_wool","ae2:network/blocks/crystal_processing_charger","botania:lens_tripwire","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","ad_astra:black_industrial_lamp","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","allthemodium:unobtainium_ingot_from_dust_blasting","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","mekanism:energized_smelter","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","mcwwindows:crimson_planks_four_window","minecraft:piston","ad_astra:large_gas_tank","mcwfurnitures:jungle_coffee_table","appmek:chemical_storage_cell_4k","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","allthemodium:allthemodium_ingot_from_raw_smelting","undergarden:regalium_block","ae2:network/cells/item_storage_components_cell_16k_part","mcwbiomesoplenty:umbran_plank_pane_window","connectedglass:borderless_glass_red_pane2","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","botania:mana_bomb","minecraft:wooden_axe","cfm:stripped_oak_desk","connectedglass:borderless_glass_light_gray2","mcwdoors:bamboo_four_panel_door","mcwtrpdoors:oak_glass_trapdoor","railcraft:villager_detector","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwroofs:gray_upper_steep_roof","botania:third_eye","mcwwindows:metal_curtain_rod","croptopia:apple_sapling","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","laserio:logic_chip","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","handcrafted:stackable_book","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","mcwpaths:granite_windmill_weave","mcwpaths:granite_windmill_weave_path","silentgear:sinew_fiber","handcrafted:gray_cushion","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","silentgear:raw_crimson_iron_block","ad_astra:space_helmet","supplementaries:flags/flag_magenta","minecraft:diamond_axe","mcwpaths:granite_running_bond_path","mcwbridges:end_stone_bricks_bridge_pier","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","appmek:chemical_storage_cell_1k","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","minecraft:dye_blue_wool","mcwlights:covered_wall_lantern","allthemodium:smithing/vibranium_leggings_smithing","dyenamics:banner/bubblegum_banner","expatternprovider:ex_io_port","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","botania:fire_rod","securitycraft:reinforced_crimson_fence_gate","create:polished_cut_granite_wall_from_stone_types_granite_stonecutting","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","create:cut_granite_stairs_from_stone_types_granite_stonecutting","botania:spawner_mover","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","botania:elementium_shears","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:prismarine_brick_upper_steep_roof","mcwroofs:brown_concrete_roof","minecraft:red_banner","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","minecraft:black_carpet","securitycraft:secret_mangrove_hanging_sign","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","botania:manasteel_chestplate","botania:magenta_shiny_flower","allthecompressed:compress/silver_block_1x","mcwfences:bamboo_highley_gate","mcwpaths:dark_prismarine_windmill_weave","mythicbotany:yggdrasil_branch","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","ae2:tools/paintballs_blue","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","mcwfurnitures:stripped_jungle_stool_chair","ae2:tools/paintballs_lime","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","minecraft:torch","ad_astra:gray_flag","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","mcwroofs:light_gray_top_roof","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","travelersbackpack:gray_sleeping_bag","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","cfm:dye_black_picket_gate","minecraft:sandstone_wall","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbridges:dry_bamboo_bridge_pier","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","connectedglass:scratched_glass_red2","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/dense_covered_white","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","mcwroofs:sandstone_lower_roof","domum_ornamentum:brown_bricks","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","mcwpaths:sandstone_basket_weave_paving","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_2","minecraft:allthemodium_spell_book_smithing","sophisticatedstorage:stack_upgrade_tier_3","mcwwindows:stripped_acacia_log_four_window","silentgear:crimson_iron_raw_ore_blasting","undergarden:grongle_boat","rftoolsutility:matter_beamer","ae2:decorative/quartz_fixture","ae2:network/cells/item_storage_cell_16k","domum_ornamentum:green_floating_carpet","mcwwindows:cherry_plank_window2","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","minecraft:carrot_on_a_stick","rftoolspower:blazing_agitator","allthemodium:unobtainium_ingot_from_raw_smelting","mcwwindows:spruce_curtain_rod","cfm:dye_blue_picket_gate","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","aether:netherite_helmet_repairing","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","expatternprovider:cobblestone_cell","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","minecraft:granite_wall","mcwroofs:prismarine_brick_attic_roof","simplylight:illuminant_purple_block_on_dyed","handcrafted:granite_corner_trim","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","pneumaticcraft:tube_junction","bambooeverything:bamboo_torch","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","alltheores:constantan_ingot_from_dust","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","expatternprovider:silicon_block","minecraft:item_frame","mekanism:tier_installer/elite","itemcollectors:basic_collector","minecraft:loom","enderio:resetting_lever_three_hundred_inv","ad_astra:steel_engine","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","rftoolsutility:tank","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","mcwpaths:granite_running_bond_stairs","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","mcwpaths:granite_flagstone","create:cut_granite_brick_wall_from_stone_types_granite_stonecutting","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","minecraft:polished_blackstone","handcrafted:blue_crockery_combo","advanced_ae:quantumaccel","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","dyenamics:bed/maroon_bed_frm_white_bed","travelersbackpack:dye_red_sleeping_bag","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","botania:gaia_spreader","mcwroofs:black_upper_lower_roof","undergarden:depthrock_brick_stairs_stonecutting","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","mcwpaths:dark_prismarine_diamond_paving","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwpaths:dark_prismarine_flagstone","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:dye_red_picket_fence","travelersbackpack:horse","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","bigreactors:reprocessor/wasteinjector","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","comforts:sleeping_bag_to_gray","mcwpaths:granite_crystal_floor_stairs","undergarden:shard_torch","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwpaths:dark_prismarine_honeycomb_paving","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","mcwpaths:dark_prismarine_flagstone_path","botania:petal_yellow","aether:golden_leggings_repairing","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","undergarden:gloomgourd_seeds","mcwroofs:blue_striped_awning","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","minecraft:dark_prismarine_stairs","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","ae2:tools/paintballs_purple","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","sophisticatedbackpacks:compacting_upgrade","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cells/item_storage_cell_64k_storage","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","ironfurnaces:furnaces/crystal_furnace","minecraft:iron_axe","modularrouters:puller_module_2_x4","mcwroofs:dark_prismarine_roof","create:crafting/kinetics/fluid_valve","enderio:resetting_lever_thirty_inv_from_base","supplementaries:crystal_display","minecraft:sandstone_stairs","minecraft:composter","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","create:layered_granite_from_stone_types_granite_stonecutting","undergarden:polished_depthrock","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","appmek:chemical_storage_cell_16k","alltheores:signalum_rod","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","occultism:crafting/demons_dream_essence_from_seeds","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","farmersdelight:cooking/pasta_with_mutton_chop","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwroofs:bricks_attic_roof","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","connectedglass:borderless_glass_yellow2","mcwdoors:jungle_whispering_door","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwpaths:granite_clover_paving","allthemodium:smithing/unobtainium_chestplate_smithing","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","ad_astra:airlock","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","minecolonies:pottage","croptopia:egg_roll","twigs:bamboo_mat","connectedglass:borderless_glass_black2","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","railcraft:solid_fueled_firebox","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","ae2:network/cells/item_storage_cell_1k_storage","botania:lens_firework","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","aether:iron_pendant","securitycraft:redstone_module","minecraft:lime_dye","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","botania:quartz_sunny","mcwbiomesoplenty:stripped_redwood_log_window","botania:flighttiara_0","railcraft:signal_lamp","cfm:jungle_blinds","reliquary:uncrafting/sugar","tombstone:bone_needle","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","minecraft:cyan_dye","mcwwindows:acacia_window2","evilcraft:crafting/dark_stick","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwroofs:white_top_roof","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","botania:floating_pure_daisy","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","ae2:network/cables/dense_smart_cyan","handcrafted:black_sheet","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","connectedglass:scratched_glass_cyan2","securitycraft:secret_mangrove_sign_item","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","travelersbackpack:warden","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","dyenamics:fluorescent_concrete_powder","botania:incense_plate","allthecompressed:compress/clay_1x","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","mcwpaths:dark_prismarine_square_paving","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","bambooeverything:dry_bamboo","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","railways:stonecutting/riveted_locometal","securitycraft:reinforced_observer","functionalstorage:oak_1","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwpaths:dark_prismarine_running_bond","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","allthemodium:unobtainium_rod","mcwbiomesoplenty:dead_pane_window","rftoolsutility:screen_controller","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","allthecompressed:compress/blazing_crystal_block_1x","cfm:dye_green_picket_fence","mcwwindows:oak_plank_window","mcwroofs:granite_roof","nethersdelight:hoglin_sirloin","mcwwindows:oak_log_parapet","ae2wtlib:wireless_universal_terminal/ca","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","ae2:network/cells/item_storage_components_cell_64k_part","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwbridges:bamboo_bridge_pier","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:flower_pot","create:crafting/schematics/empty_schematic","ae2wtlib:wireless_universal_terminal/ae","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","allthecompressed:compress/niotic_crystal_block_1x","farmersdelight:steak_and_potatoes","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","mcwpaths:sandstone_crystal_floor_stairs","pneumaticcraft:wall_lamp_inverted_blue","allthemodium:vibranium_plate","securitycraft:codebreaker","supplementaries:flute","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:light_blue_picket_gate","allthearcanistgear:unobtainium_leggings_smithing","constructionwand:infinity_wand","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","farmersdelight:cooking/squid_ink_pasta","comforts:hammock_to_gray","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","cfm:blue_kitchen_counter","mcwbiomesoplenty:maple_four_window","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","handcrafted:granite_pillar_trim","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwbridges:end_stone_bricks_bridge_stair","mcwdoors:bamboo_classic_door","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","silentgear:bort_block","minecraft:pink_dye_from_red_white_dye","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","rftoolspower:blazing_generator","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","mcwpaths:dark_prismarine_crystal_floor_path","supplementaries:statue","reliquary:wraith_node","immersiveengineering:crafting/tinted_glass_lead_wire","minecraft:dye_green_carpet","botania:diva_charm","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","securitycraft:secret_jungle_sign_item","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","ae2:network/crafting/cpu_crafting_accelerator","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwroofs:white_terracotta_upper_steep_roof","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","minecraft:stick_from_bamboo_item","alltheores:bronze_plate","mcwpaths:brick_crystal_floor_slab","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","twilightforest:wood/oak_banister","alltheores:enderium_plate","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","modularrouters:player_module","pneumaticcraft:pressure_chamber_wall","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:end_stone_brick_stairs","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwroofs:dark_prismarine_upper_lower_roof","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwpaths:granite_windmill_weave_stairs","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","ae2:network/cables/smart_red","ae2additions:components/super/64k","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","travelersbackpack:bookshelf_smithing","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","minecraft:granite_stairs","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","ad_astra:green_industrial_lamp","appmek:chemical_storage_cell_64k","undergarden:depthrock_tile_slab_stonecutting","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","chimes:bamboo_chimes","rftoolsstorage:storage_control_module","securitycraft:secret_spruce_sign_item","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwpaths:granite_square_paving","cfm:oak_upgraded_fence","mob_grinding_utils:recipe_mob_swab","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","botania:super_cloud_pendant","railcraft:steel_gear","allthemodium:piglich_heart_block","bigreactors:smelting/graphite_from_charcoal","minecraft:prismarine_bricks","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","connectedglass:borderless_glass_cyan2","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","ae2:tools/paintballs_cyan","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","xnet:netcable_green_dye","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","twilightdelight:cutting/ice_bomb","minecolonies:soysauce","biomesoplenty:orange_dye_from_orange_cosmos","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","botania:quartz_lavender","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","botania:petal_yellow_double","immersiveengineering:crafting/paper_from_sawdust","reliquary:glowing_water_from_potion_vial","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","pneumaticcraft:logistics_frame_requester_self","botania:dye_light_gray","megacells:cells/mega_fluid_cell_housing","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","pylons:potion_filter","enderio:resetting_lever_thirty","minecraft:glistering_melon_slice","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:crafting/kinetics/gray_seat","botania:conversions/light_gray_petal_block_deconstruct","everythingcopper:copper_hoe","create:crafting/kinetics/brown_seat","cfm:stripped_mangrove_kitchen_sink_light","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","handcrafted:quartz_corner_trim","ad_astra:steel_factory_block","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","supplementaries:candle_holders/candle_holder_black_dye","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","occultism:crafting/spirit_torch","undergarden:depthrock_brick_wall_stonecutting","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","create:polished_cut_granite_from_stone_types_granite_stonecutting","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","twigs:stone_column","aeinfinitybooster:dimension_card","minecraft:lectern","pneumaticcraft:compressed_bricks_from_stone_stonecutting","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","silentgear:crimson_iron_raw_ore_smelting","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","botania:azulejo_0","mcwtrpdoors:bamboo_blossom_trapdoor","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","securitycraft:secret_sign_item","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","connectedglass:borderless_glass_white2","ae2additions:components/super/1k","aquaculture:heavy_hook","aether:netherite_sword_repairing","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","mcwwindows:acacia_window","allthemodium:smithing/unobtainium_leggings_smithing","croptopia:food_press","mcwbridges:prismarine_bricks_bridge","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","xnet:router","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","mcwpaths:dark_prismarine_running_bond_stairs","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","mcwroofs:orange_concrete_lower_roof","farmersdelight:nether_salad","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","pneumaticcraft:reinforced_brick_pillar","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","mcwpaths:brick_crystal_floor","botania:dodge_ring","ae2:materials/basiccard","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","botania:laputa_shard","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwfences:oak_highley_gate","xnet:connector_green_dye","minecraft:dark_prismarine_slab_from_dark_prismarine_stonecutting","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","allthemodium:ancient_cracked_stone_brick_slabs","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","comforts:hammock_to_red","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","dyenamics:wine_concrete_powder","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:end_stone_brick_slab","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","mekanism:control_circuit/elite","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","delightful:smelting/roasted_acorn","mcwpaths:dark_prismarine_crystal_floor_stairs","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","mcwdoors:bamboo_beach_door","ae2:block_cutter/walls/smooth_sky_stone_wall","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:netherite_gloves_repairing","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dimstorage:dim_core","dyenamics:bed/navy_bed_frm_white_bed","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","allthemods:mekanismgenerators/gas_burning_gen","sophisticatedbackpacks:refill_upgrade","ae2additions:components/super/4k","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","alltheores:enderium_rod","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","connectedglass:borderless_glass_green2","dyenamics:bed/persimmon_bed_frm_white_bed","mythicbotany:gaia_pylon","mcwfences:dark_oak_curved_gate","mcwpaths:granite_basket_weave_paving","undergarden:depthrock_brick_slab_stonecutting","mcwdoors:bamboo_bark_glass_door","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","ad_astra:compressor","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","minecraft:red_bed","farmersdelight:cooking/mushroom_rice","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","ae2:network/cells/item_storage_cell_4k","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","dyenamics:navy_dye","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","ae2:network/wireless_part","mcwpaths:brick_windmill_weave_slab","ae2:materials/cardspeed","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","botania:holy_cloak","mcwdoors:print_bamboo","createoreexcavation:vein_finder","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","minecraft:copper_ingot_from_smelting_raw_copper","sophisticatedbackpacks:chipped/carpenters_table_upgrade","cfm:dye_black_picket_fence","mcwbiomesoplenty:stripped_maple_log_window2","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","naturalist:bug_net","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","supplementaries:hourglass","ae2:tools/paintballs_light_gray","everythingcopper:copper_leggings","ae2:network/cells/item_storage_cell_1k","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","minecraft:orange_dye_from_red_yellow","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","travelersbackpack:hay_smithing","mcwwindows:spruce_plank_window","supplementaries:checker","croptopia:flour","minecraft:jungle_sign","minecraft:cyan_candle","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","railcraft:steel_tank_valve","domum_ornamentum:cactus_extra","cfm:cyan_cooler","ae2:network/parts/formation_plane","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","ad_astra:steel_rod","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwroofs:light_gray_roof","securitycraft:secret_jungle_hanging_sign","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwdoors:bamboo_tropical_door","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","minecraft:light_gray_dye_from_black_white_dye","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","travelersbackpack:redstone","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwroofs:granite_upper_lower_roof","chimes:glass_bells","mcwwindows:iron_shutter","minecraft:netherite_upgrade_smithing_template","evilcraft:crafting/blood_extractor","immersiveengineering:crafting/blueprint_molds","aether:diamond_chestplate_repairing","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","comforts:sleeping_bag_to_black","farmersdelight:cutting_board","croptopia:trail_mix","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","ad_astra:nasa_workbench","mcwpaths:sandstone_flagstone","ad_astra:etrionic_capacitor","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","mcwbridges:blackstone_bridge_pier","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","rftoolspower:coalgenerator","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","enderio:resetting_lever_sixty_inv","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","minecraft:end_crystal","croptopia:shaped_nether_wart_stew","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:magenta_terracotta_attic_roof","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","allthemodium:smithing/unobtainium_sword_smithing","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","dyenamics:maroon_dye","utilitix:filter_rail","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","enderio:resetting_lever_sixty_from_inv","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","minecraft:mossy_cobblestone_from_moss_block","railcraft:steel_tunnel_bore_head","securitycraft:secret_birch_sign_item","minecolonies:chainmailhelmet","gtceu:smelting/smelt_dust_electrum_to_ingot","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","botania:unholy_cloak","mcwbridges:granite_bridge_pier","ae2:network/parts/tunnels_me","allthecompressed:compress/vibranium_block_1x","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","travelersbackpack:dye_green_sleeping_bag","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","arseng:source_storage_cell_4k","ae2:network/cells/fluid_storage_cell_4k","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","ae2:network/cables/dense_covered_yellow","alltheores:gold_dust_from_hammer_ingot_crushing","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","modularrouters:void_module","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","dankstorage:2_to_3","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","allthemodium:vibranium_ingot_from_raw_smelting","mysticalagriculture:imperium_essence_uncraft","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","create:cut_granite_from_stone_types_granite_stonecutting","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","securitycraft:reinforced_warped_fence_gate","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwroofs:prismarine_brick_upper_lower_roof","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","caupona:clay_cistern","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","allthemodium:smithing/vibranium_chestplate_smithing","bloodmagic:synthetic_point","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","supplementaries:candle_holders/candle_holder_cyan_dye","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","alltheores:lumium_ingot_from_dust","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","handcrafted:terracotta_bowl","botania:black_hole_talisman","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","connectedglass:borderless_glass_pink2","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","cfm:dye_cyan_picket_gate","create:crafting/schematics/schematic_and_quill","minecraft:dye_blue_bed","allthemodium:vibranium_ingot","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","botania:diluted_pool","modularrouters:energy_output_module","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","appbot:mana_storage_cell_64k","mekanism:fluid_tank/advanced","undergarden:chiseled_depthrock_bricks_stonecutting","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","connectedglass:tinted_borderless_glass_blue2","minecraft:polished_blackstone_pressure_plate","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","securitycraft:secret_dark_oak_hanging_sign","alltheores:diamond_rod","rftoolsbuilder:shield_block2","minecraft:stone_slab_from_stone_stonecutting","rftoolsbuilder:shield_block1","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","ae2:network/crafting/64k_cpu_crafting_storage","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwroofs:granite_lower_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","handcrafted:wood_bowl","pneumaticcraft:charging_station","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwdoors:bamboo_barn_glass_door","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","delightful:cantaloupe_slice","mcwbridges:balustrade_blackstone_bridge","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","delightful:food/cooking/stuffed_cantaloupe","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","mysticalagriculture:essence/appliedenergistics2/certus_quartz_dust","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","securitycraft:secret_acacia_hanging_sign","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","botania:water_ring","mcwroofs:purple_terracotta_attic_roof","arseng:source_storage_cell_1k","mcwroofs:orange_terracotta_lower_roof","sophisticatedstorage:stack_upgrade_tier_1_plus","ae2:network/cells/fluid_storage_cell_1k","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwroofs:brown_terracotta_upper_steep_roof","mcwdoors:oak_swamp_door","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","allthemodium:smithing/allthemodium_helmet_smithing","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","allthemodium:smithing/allthemodium_boots_smithing","mcwwindows:mangrove_shutter","expatternprovider:ex_drive","cfm:lime_cooler","rftoolsbuilder:shape_card_quarry_clear_fortune","mcwpaths:andesite_crystal_floor","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","extradisks:advanced_machine_casing","alltheores:copper_ingot_from_block","ae2:network/cells/fluid_storage_cell_64k_storage","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","dyenamics:mint_dye","silentgear:emerald_from_shards","supplementaries:candy","minecolonies:chicken_broth","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","ae2:network/cells/fluid_storage_cell_16k","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwroofs:gray_roof_block","mcwbiomesoplenty:fir_plank_four_window","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","ae2:network/crafting/1k_cpu_crafting_storage","allthecompressed:compress/blaze_block_1x","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwdoors:bamboo_nether_door","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","ae2:decorative/cut_quartz_block","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","allthemodium:smithing/allthemodium_leggings_smithing","mcwfences:sandstone_grass_topped_wall","allthemodium:vibranium_ingot_from_dust_smelting","travelersbackpack:black_sleeping_bag","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","reliquary:mercy_cross","farmersdelight:fried_egg","minecraft:magma_cream","botania:slime_bottle","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","botania:dirt_rod","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","blue_skies:maple_bookshelf","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","allthemodium:unobtainium_ingot_from_dust_smelting","connectedglass:clear_glass_red2","rftoolspower:power_core1","pneumaticcraft:logistics_frame_default_storage","botania:gaia_ingot","ae2:network/blocks/io_port","mcwroofs:white_concrete_upper_steep_roof","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","allthemodium:smithing/vibranium_pickaxe_smithing","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","simplylight:illuminant_lime_block_on_toggle","ae2:misc/deconstruction_certus_quartz_block","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","reliquary:mob_charm_fragments/enderman","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","mcwpaths:brick_running_bond_path","supplementaries:sugar_cube","securitycraft:security_camera","rftoolsbuilder:shape_card_quarry_dirt","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","botania:mana_pylon","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","mcwroofs:dark_prismarine_top_roof","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","mcwpaths:brick_windmill_weave_stairs","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","undergarden:polished_depthrock_stonecutting","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","minecraft:dye_cyan_wool","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","advanced_ae:smalladvpatpro","botania:apothecary_livingrock","mcwdoors:oak_whispering_door","alltheores:signalum_ingot_from_dust","mcwtrpdoors:bamboo_barn_trapdoor","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","ae2:tools/paintballs_green","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","railcraft:feed_station","minecraft:black_stained_glass_pane_from_glass_pane","create:polished_cut_granite_stairs_from_stone_types_granite_stonecutting","connectedglass:scratched_glass_green_pane2","pneumaticcraft:wall_lamp_inverted_green","mcwbridges:balustrade_granite_bridge","mcwroofs:stone_upper_steep_roof","mcwroofs:dark_prismarine_lower_roof","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","arseng:source_storage_cell_64k","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","immersiveengineering:crafting/plate_uranium_hammering","mcwdoors:bamboo_paper_door","railcraft:personal_world_spike","handcrafted:oak_nightstand","immersiveengineering:crafting/armor_steel_chestplate","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","megacells:crafting/greater_energy_card_upgraded","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","xnet:redstoneproxy_update","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","expatternprovider:water_cell","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","delightful:food/cooking/ender_nectar","merequester:requester","mcwfences:stone_brick_railing_gate","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","minecraft:dark_prismarine_stairs_from_dark_prismarine_stonecutting","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","supplementaries:slingshot","allthemodium:allthemodium_plate","ae2:network/blocks/quantum_link","advanced_ae:quantumunit","twigs:cobblestone_from_pebble","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","everythingcopper:copper_rail","ae2:network/cells/item_storage_components_cell_4k_part","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","evilcraft:crafting/blood_infuser","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","cfm:black_sofa","aquaculture:neptunium_ingot_from_nuggets","mcwroofs:bricks_upper_steep_roof","croptopia:french_fries","create:small_granite_bricks_from_stone_types_granite_stonecutting","minecraft:iron_pickaxe","aether:shield_repairing","ae2:shaped/not_so_mysterious_cube","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","reliquary:fortune_coin","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","rftoolsutility:matter_booster","ae2:network/cables/glass_orange","deepresonance:resonating_plate","ae2:materials/cardinverter","minecraft:vibranium_mage_chestplate_smithing","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","mcwfences:spruce_highley_gate","minecraft:birch_boat","handcrafted:gray_sheet","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","mcwtrpdoors:bamboo_classic_trapdoor","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","mcwtrpdoors:bamboo_barred_trapdoor","minecraft:lime_stained_glass","ae2:tools/paintballs_black","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","cfm:white_kitchen_counter","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","alltheores:enderium_ingot_from_dust","ae2:network/cables/covered_fluix_clean","botania:elementium_block","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","minecraft:yellow_dye_from_dandelion","mcwroofs:light_blue_terracotta_lower_roof","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwpaths:dark_prismarine_running_bond_slab","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","trashcans:energy_trash_can","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","pneumaticcraft:air_compressor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:granite_attic_roof","mcwdoors:bamboo_modern_door","minecolonies:baked_salmon","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","chimes:copper_chimes","powah:smelting/uraninite_from_raw","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","reliquary:uncrafting/ghast_tear","ae2:tools/paintballs_yellow","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","integrateddynamics:crafting/squeezer","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","botania:dye_yellow","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","expatternprovider:wireless_connector","reliquary:mob_charm_fragments/wither_skeleton","mcwbridges:granite_bridge_stair","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","mcwroofs:black_terracotta_lower_roof","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","ae2:network/cables/dense_covered_fluix_clean","minecraft:scaffolding","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","minecraft:polished_granite_from_granite_stonecutting","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","mcwtrpdoors:bamboo_swamp_trapdoor","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","minecraft:honey_block","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","farmersdelight:melon_juice","dyenamics:bed/bubblegum_bed_frm_white_bed","sophisticatedbackpacks:crafting_upgrade","aquaculture:birch_fish_mount","ae2:network/cells/fluid_storage_cell_1k_storage","mcwwindows:stripped_mangrove_log_window","securitycraft:secret_crimson_hanging_sign","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","reliquary:uncrafting/glowstone_dust","tombstone:ankh_of_prayer","cfm:white_sofa","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwfences:ornate_metal_fence","rftoolsbuilder:mover_control2","mcwfences:acacia_highley_gate","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","aiotbotania:livingrock_sword","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","connectedglass:tinted_borderless_glass_black2","travelersbackpack:squid","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","allthecompressed:compress/melon_1x","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","ae2:network/blocks/cell_workbench","nethersdelight:hoglin_sirloin_from_smoking","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","ae2:shaped/slabs/sky_stone_block","allthemodium:allthemodium_rod","mcwroofs:prismarine_brick_steep_roof","handcrafted:oak_bench","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwpaths:dark_prismarine_flagstone_stairs","minecraft:repeater","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","forbidden_arcanus:test_tube","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","rftoolsbuilder:shape_card_quarry_silk_dirt","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","botania:elementium_boots","undergarden:depthrock_slab_stonecutting","mcwpaths:blackstone_crystal_floor_path","cfm:jungle_cabinet","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwroofs:base_roof_block","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","ae2:tools/paintballs_white","mcwbiomesoplenty:palm_window","twigs:blackstone_column","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwroofs:dark_prismarine_upper_steep_roof","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","gtceu:smelting/smelt_raw_nickel_ore_to_ingot","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","ad_astra:red_industrial_lamp","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","minecraft:red_candle","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","ae2:block_cutter/slabs/quartz_slab","sophisticatedbackpacks:chipped/glassblower_upgrade","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:jungle_door","minecraft:prismarine_brick_slab","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","ae2:network/cells/item_storage_components_cell_256k_part","dyenamics:bed/fluorescent_bed_frm_white_bed","minecraft:polished_andesite_from_andesite_stonecutting","allthearcanistgear:vibranium_robes_smithing","mcwbridges:sandstone_bridge_pier","mysticalagriculture:essence/minecraft/iron_ingot","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","botania:reach_ring","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","handcrafted:jungle_drawer","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","botania:mushroom_5","minecraft:oak_pressure_plate","botania:mushroom_4","ae2:misc/chests_smooth_sky_stone","minecraft:stone_brick_stairs","allthemodium:ancient_cracked_stone_brick_wall","botania:mushroom_3","aether:skyroot_beehive","bigreactors:turbine/basic/activefluidport_forge","aether:netherite_leggings_repairing","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","mcwwindows:quartz_window","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","botania:glimmering_livingwood_log","reliquary:mob_charm_fragments/creeper","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","minecraft:clock","mcwroofs:red_concrete_top_roof","create:crafting/appliances/netherite_diving_helmet_from_netherite","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","megacells:crafting/compression_card","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","allthemods:ae2/skystone_dust","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","minecraft:prismarine","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","minecolonies:pasta_plain","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","silentgear:leather_scrap","mcwroofs:purple_terracotta_roof","mcwfurnitures:stripped_jungle_chair","botania:spark_changer","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","cfm:acacia_kitchen_sink_dark","rftoolsbase:machine_frame","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","minecraft:dye_black_bed","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","pylons:infusion_pylon","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","expatternprovider:assembler_matrix_speed","utilitix:mob_yoinker","mcwwindows:andesite_four_window","domum_ornamentum:light_blue_brick_extra","minecraft:granite_slab","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","mcwroofs:thatch_top_roof","farmersdelight:iron_nugget_from_smelting_knife","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:leather_leggings","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","securitycraft:block_change_detector","croptopia:fruit_salad","mcwpaths:granite_flagstone_stairs","twigs:rocky_dirt","minecraft:polished_blackstone_button","advanced_ae:advpatpro2","supplementaries:pancake_fd","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","minecraft:netherite_scrap_from_blasting","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","mcwtrpdoors:bamboo_four_panel_trapdoor","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","enderio:silent_stone_pressure_plate","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","additionallanterns:quartz_chain","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","mekanism:processing/gold/ingot/from_dust_smelting","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","connectedglass:scratched_glass_cyan_pane2","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","create:small_granite_brick_slab_from_stone_types_granite_stonecutting","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","ad_astra:wheel","minecraft:waxed_copper_block_from_honeycomb","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","minecraft:warped_fungus_on_a_stick","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","minecraft:sandstone_slab","mcwwindows:birch_window","mcwfences:acacia_picket_fence","reliquary:uncrafting/wither_skeleton_skull","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","securitycraft:secret_dark_oak_sign_item","ae2:network/cables/dense_covered_cyan","sophisticatedbackpacks:smithing_upgrade","minecraft:gold_ingot_from_smelting_raw_gold","mekanism:tier_installer/ultimate","mcwfences:mud_brick_railing_gate","twigs:oak_table","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","pneumaticcraft:heat_pipe","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","comforts:hammock_gray","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","railcraft:logbook","ae2:misc/tiny_tnt","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","mcwfences:mangrove_wired_fence","minecraft:red_terracotta","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","connectedglass:clear_glass1","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwpaths:blackstone_dumble_paving","mcwroofs:white_roof_block","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","travelersbackpack:melon","minecraft:white_dye_from_lily_of_the_valley","securitycraft:block_pocket_manager","mcwlights:soul_cherry_tiki_torch","expatternprovider:ei","botania:red_pavement","minecraft:end_stone_brick_wall","securitycraft:reinforced_mangrove_fence","alltheores:copper_ore_hammer","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","mcwpaths:granite_diamond_paving","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","botania:flower_bag","ad_astra:steel_plateblock","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","allthemodium:unobtainium_ingot_from_block","mcwfences:dark_oak_wired_fence","minecraft:netherite_chestplate_smithing","cfm:jungle_kitchen_counter","ae2:network/cables/dense_smart_red","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwwindows:stone_pane_window","botania:light_gray_petal_block","domum_ornamentum:white_paper_extra","mcwpaths:dark_prismarine_crystal_floor","connectedglass:borderless_glass_blue2","mcwbiomesoplenty:fir_window2","dyenamics:bed/wine_bed_frm_white_bed","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","minecraft:black_bed","sfm:manager","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","comforts:sleeping_bag_to_red","allthemodium:smithing/allthemodium_sword_smithing","connectedglass:tinted_borderless_glass_cyan2","botania:livingwood_twig","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","ae2:tools/network_tool","minecraft:bowl","deeperdarker:bloom_boat","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwpaths:blackstone_diamond_paving","pneumaticcraft:vortex_cannon","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","securitycraft:reinforced_jungle_fence","ae2:network/cables/dense_covered_red","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwroofs:light_gray_upper_steep_roof","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","allthecompressed:compress/copper_block_1x","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","allthemodium:ancient_stone_stairs","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","minecraft:red_carpet","mcwroofs:dark_prismarine_attic_roof","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","allthemodium:smithing/vibranium_boots_smithing","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","reliquary:mob_charm_fragments/zombified_piglin","create:crafting/materials/red_sand_paper","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwdoors:bamboo_waffle_door","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","allthearcanistgear:vibranium_hat_smithing","mcwroofs:oak_planks_attic_roof","bigreactors:crafting/graphite_component_to_storage","rftoolsutility:moduleplus_template","dyenamics:amber_terracotta","evilcraft:crafting/blood_infusion_core","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","sophisticatedstorage:basic_to_copper_tier_upgrade","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","ae2:tools/misctools_entropy_manipulator","aether:netherite_pickaxe_repairing","pneumaticcraft:manual_compressor","botania:elementium_hoe","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","ae2:decorative/sky_stone_brick_from_stonecutting","securitycraft:secret_acacia_sign_item","comforts:sleeping_bag_black","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","allthemodium:ancient_stone_wall","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","ae2:network/crafting/patterns_blank","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","handcrafted:jungle_desk","utilitarian:snad/drit","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","minecraft:cooked_cod","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","ad_astra:steel_trapdoor","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:granite_top_roof","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","securitycraft:alarm","pneumaticcraft:manometer","allthemodium:vibranium_ingot_from_dust_blasting","ad_astra:launch_pad","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","ae2:tools/fluix_shovel","reliquary:magicbane","pneumaticcraft:wall_lamp_inverted_magenta","modularrouters:sender_module_1_alt","botania:elementium_leggings","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","mcwpaths:dark_prismarine_crystal_floor_slab","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","comforts:sleeping_bag_red","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","ad_astra:black_flag","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","botania:petal_light_gray","mysticalagriculture:essence/minecraft/netherite_ingot","mcwroofs:gray_steep_roof","rftoolspower:endergenic","create:copper_shingles_from_ingots_copper_stonecutting","reliquary:mob_charm_belt","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","alltheores:iron_ore_hammer","allthemodium:raw_unobtainium_block","integrateddynamics:crafting/drying_basin","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","dankstorage:dank_3","ae2:network/cables/glass_black","mcwroofs:yellow_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","delightful:food/cooking/glow_jam_jar","everythingcopper:copper_ladder","botania:polished_livingrock","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","rftoolsbuilder:shape_card_pump_dirt","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","allthemodium:smithing/vibranium_sword_smithing","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","mysticalagriculture:essence/appliedenergistics2/certus_quartz","simplylight:illuminant_orange_block_dyed","minecraft:polished_blackstone_stairs","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","travelersbackpack:sandstone","allthemodium:ancient_cracked_stone_brick_stairs","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","mcwpaths:granite_crystal_floor_path","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","create:cut_granite_slab_from_stone_types_granite_stonecutting","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","ae2:network/crafting/4k_cpu_crafting_storage","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:red_cushion","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","pneumaticcraft:air_grate_module","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","minecraft:dye_red_carpet","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","mcwbridges:balustrade_end_stone_bricks_bridge","minecraft:quartz_bricks","botania:temperance_stone","undergarden:polished_depthrock_slab_stonecutting","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","mcwbridges:balustrade_bricks_bridge","mcwbridges:prismarine_bricks_bridge_stair","supplementaries:candle_holders/candle_holder_blue_dye","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","undergarden:polished_depthrock_wall_stonecutting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","cfm:dye_red_picket_gate","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","botania:elementium_helmet","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","ae2:network/cables/dense_smart_fluix","farmersdelight:iron_nugget_from_blasting_knife","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","ae2:network/blocks/pattern_providers_interface_part","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","expatternprovider:assembler_matrix_pattern","mcwbridges:bamboo_bridge","mcwfences:modern_andesite_wall","mcwpaths:granite_crystal_floor","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","ae2:network/cells/view_cell","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","rftoolsutility:matter_receiver","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwroofs:deepslate_lower_roof","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","ae2wtlib:wireless_universal_terminal/upgrade_pattern_encoding","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","forbidden_arcanus:dark_rune","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","undergarden:polished_depthrock_stairs_stonecutting","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwpaths:dark_prismarine_running_bond_path","mcwtrpdoors:bamboo_beach_trapdoor","minecolonies:apple_pie","allthecompressed:compress/cobblestone_1x","twigs:mossy_bricks_from_moss_block","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","minecraft:andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","industrialforegoing:plastic","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","eccentrictome:tome","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","ad_astra:zip_gun","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","rftoolsutility:matter_transmitter","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","minecraft:gray_carpet","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","travelersbackpack:magma_cube","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwbridges:prismarine_bricks_bridge_pier","mcwfences:red_sandstone_grass_topped_wall","undergarden:depthrock_tiles_stonecutting","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","farmersdelight:cooking/glow_berry_custard","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","mcwroofs:orange_concrete_steep_roof","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwwindows:warped_planks_window","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","evilcraft:crafting/dark_spike","extradisks:advanced_storage_housing","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","ae2:network/wireless_crafting_terminal","create:crafting/kinetics/orange_seat","rftoolsutility:syringe","minecraft:diamond_block","mcwbridges:granite_bridge","mekanism:configurator","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","rftoolsutility:spawner","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","bigreactors:blasting/graphite_from_coal","mcwpaths:dark_prismarine_windmill_weave_stairs","botania:astrolabe","sophisticatedbackpacks:stack_upgrade_tier_1","mcwwindows:dark_prismarine_four_window","sophisticatedstorage:iron_to_gold_tier_upgrade","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","sophisticatedstorage:backpack_stack_upgrade_tier_1_from_storage_stack_upgrade_tier_2","minecolonies:raw_noodle","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","minecraft:cooked_mutton","farmersdelight:wheat_dough_from_eggs","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","connectedglass:tinted_borderless_glass_red2","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","ae2:tools/certus_quartz_cutting_knife","rftoolsstorage:modular_storage","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","cfm:dye_green_picket_gate","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","rftoolsbuilder:shape_card_pump","mcwfurnitures:stripped_jungle_covered_desk","ae2:shaped/stairs/smooth_sky_stone_block","occultism:smelting/burnt_otherstone","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mekanism:electrolytic_separator","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","ae2:network/cables/smart_green","ae2:network/cells/item_storage_cell_64k","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwwindows:stone_brick_gothic","botania:elementium_pickaxe","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","supplementaries:soap/piston","minecraft:polished_granite","mcwbiomesoplenty:magic_plank_window2","undergarden:depthrock_wall","mcwroofs:pink_terracotta_upper_lower_roof","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","minecraft:soul_campfire","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","undergarden:depthrock_wall_stonecutting","botania:speed_up_belt","domum_ornamentum:green_cactus_extra","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","twilightdelight:obsidian","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","dyenamics:bed/amber_bed_frm_white_bed","simplylight:illuminant_block_dyed","ae2:tools/paintballs_magenta","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","twigs:cracked_bricks","supplementaries:slice_map","appflux:insulating_resin","mcwbridges:brick_bridge_pier","connectedglass:borderless_glass_green_pane2","alltheores:aluminum_dust_from_hammer_ingot_crushing","travelersbackpack:fox_smithing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mekanism:control_circuit/advanced","mekanism:upgrade/speed","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","blue_skies:trough","botania:lens_normal","mcwfences:end_brick_pillar_wall","dyenamics:ultramarine_dye","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","undergarden:boomgourd","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","delightful:knives/elementium_knife","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","minecraft:purple_dye","mcwfurnitures:jungle_modern_chair","ae2:network/cells/item_storage_cell_4k_storage","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","handcrafted:phantom_trophy","mcwroofs:cyan_terracotta_upper_steep_roof","minecraft:quartz_slab","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","mcwroofs:stone_bricks_roof","cfm:cyan_grill","thermal_extra:smelting/twinite_ingot_from_dust_smelting","simplylight:illuminant_purple_block_dyed","ae2:block_cutter/stairs/smooth_sky_stone_stairs","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","minecraft:quartz_pillar_from_quartz_block_stonecutting","mcwfurnitures:stripped_jungle_double_drawer","ae2:network/cables/smart_black","botania:conversions/yellow_petal_block_deconstruct","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","mcwpaths:dark_prismarine_strewn_rocky_path","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","undergarden:stonecutter_from_depthrock","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","railcraft:switch_track_lever","mcwpaths:andesite_honeycomb_paving","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","handcrafted:blue_bowl","mcwdoors:bamboo_stable_head_door","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","cfm:white_picket_fence","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","handcrafted:kitchen_hood","appbot:mana_storage_cell_4k","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","rftoolsbuilder:shape_card_liquid","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","reliquary:ender_staff","minecraft:gray_dye","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","travelersbackpack:netherite_tier_upgrade","securitycraft:secret_birch_hanging_sign","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","botania:travel_belt","allthemodium:unobtainium_ingot_from_raw_blasting","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","ae2:materials/advancedcard","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwroofs:andesite_lower_roof","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","mysticalagriculture:prosperity_seed_base","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:iron_sword","botania:elementium_chestplate","mcwpaths:sandstone_clover_paving","aquaculture:sushi","ae2:network/blocks/interfaces_interface_alt","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","minecraft:dark_prismarine_slab","botania:runic_altar","additionallanterns:blackstone_chain","domum_ornamentum:purple_brick_extra","minecraft:green_dye","mcwwindows:one_way_glass_pane","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","minecraft:chiseled_sandstone_from_sandstone_stonecutting","ae2:tools/fluix_upgrade_smithing_template","comforts:hammock_red","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","pneumaticcraft:logistics_frame_active_provider","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","appbot:mana_storage_cell_1k","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","everythingcopper:copper_anvil","minecraft:prismarine_brick_stairs_from_prismarine_stonecutting","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","supplementaries:candle_holders/candle_holder_green_dye","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","undergarden:coarse_deepsoil","handcrafted:jungle_table","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","botania:abstruse_platform","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","minecraft:conduit","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","mcwwindows:diorite_pane_window","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","mcwpaths:granite_crystal_floor_slab","supplementaries:flags/flag_red","cfm:dye_blue_picket_fence","enderio:resetting_lever_ten_from_prev","ae2:blasting/sky_stone_block","mcwbridges:end_stone_bricks_bridge","mcwroofs:magenta_concrete_upper_lower_roof","botania:lens_flare","reliquary:crimson_cloth","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","forbidden_arcanus:clibano_combustion/diamond_from_clibano_combusting","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","pneumaticcraft:gilded_upgrade","handcrafted:black_cushion","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","minecraft:gray_terracotta","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","mcwdoors:bamboo_mystic_door","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","mcwroofs:prismarine_brick_lower_roof","dyenamics:peach_terracotta","botania:livingrock_wall","minecraft:fishing_rod","xnet:connector_yellow_dye","rftoolspower:powercell_card","minecraft:terracotta","reliquary:alkahestry_altar","mcwwindows:acacia_blinds","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","botania:blue_pavement","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","minecraft:granite_wall_from_granite_stonecutting","supplementaries:timber_frame","aether:diamond_pickaxe_repairing","pneumaticcraft:seismic_sensor","comforts:sleeping_bag_gray","twigs:copper_pillar_stonecutting","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mekanism:control_circuit/ultimate","mcwroofs:brown_terracotta_roof","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","ae2:network/cells/fluid_storage_cell_4k_storage","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","minecraft:beacon","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","aquaculture:nether_star_hook","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","minecraft:black_dye","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","create:cut_granite_wall_from_stone_types_granite_stonecutting","mekanism:dynamic_tank","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","allthemodium:vibranium_dust_from_ore_crushing","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","mekanism:steel_casing","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","minecolonies:fish_n_chips","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mysticalagriculture:essence/appliedenergistics2/sky_stone","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","ae2:network/wireless_access_point","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","ae2:shaped/walls/smooth_sky_stone_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","rftoolsbase:tablet","ae2:network/crafting/256k_cpu_crafting_storage","mysticalagriculture:essence/appliedenergistics2/fluix_dust","mcwroofs:magenta_terracotta_lower_roof","railcraft:routing_table_book","ad_astra:vent","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mysticalagriculture:essence/minecraft/quartz","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","handcrafted:terracotta_plate","supplementaries:item_shelf","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","rftoolsbuilder:space_chamber","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","comforts:hammock_black","silentgear:coating_smithing_template","handcrafted:jungle_shelf","mcwpaths:dark_prismarine_basket_weave_paving","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","minecraft:granite_slab_from_granite_stonecutting","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","create:polished_cut_granite_slab_from_stone_types_granite_stonecutting","ae2:network/crafting/cpu_crafting_unit","mcwbiomesoplenty:pine_pane_window","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","connectedglass:borderless_glass_magenta2","connectedglass:borderless_glass_gray2","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","ae2:network/blocks/interfaces_interface_part","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwpaths:granite_running_bond","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","ae2:materials/annihilationcore","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","minecraft:leather_boots","railcraft:tank_detector","railcraft:steel_spike_maul","create:crafting/appliances/netherite_diving_boots_from_netherite","handcrafted:sandstone_corner_trim","pneumaticcraft:classify_filter","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwlights:pink_paper_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","rftoolscontrol:workbench","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:dye_black_carpet","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","mysticalagriculture:essence/minecraft/glowstone_dust","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","occultism:crafting/dictionary_of_spirits","cfm:oak_table","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwpaths:dark_prismarine_flagstone_slab","minecraft:magma_block","minecraft:polished_granite_stairs_from_granite_stonecutting","mcwwindows:crimson_stem_parapet","undergarden:depthrock_pebble_stonecutting","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","domum_ornamentum:magenta_floating_carpet","connectedglass:clear_glass_black_pane2","create:small_granite_brick_stairs_from_stone_types_granite_stonecutting","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","littlelogistics:transmitter_component","aether:book_of_lore","minecraft:brush","securitycraft:secret_spruce_hanging_sign","allthemodium:smithing/unobtainium_helmet_smithing","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","sfm:cable","minecraft:hay_block","minecolonies:soy_milk","silentgear:raw_crimson_iron_from_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","botania:phantom_ink","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","bambooeverything:bamboo_ladder","mcwroofs:blue_concrete_top_roof","evilcraft:smelting/hardened_blood_shard","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","botania:petal_light_gray_double","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","railcraft:goggles","twigs:paper_lantern","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","modularrouters:activator_module","domum_ornamentum:brick_extra","travelersbackpack:iron","modularrouters:augment_core","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwtrpdoors:bamboo_barrel_trapdoor","mcwbiomesoplenty:empyreal_window2","minecraft:prismarine_brick_stairs","connectedglass:clear_glass_red_pane2","mcwpaths:stone_flagstone_slab","ae2:decorative/sky_stone_small_brick_from_stonecutting","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","reliquary:uncrafting/redstone","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwwindows:granite_louvered_shutter","ad_astra:red_flag","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","ad_astra:small_red_industrial_lamp","mcwwindows:warped_stem_four_window","securitycraft:sentry","botania:tornado_rod","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","alltheores:uranium_rod","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","minecolonies:tofu","mcwwindows:blue_mosaic_glass_pane","minecolonies:meat_ravioli","travelersbackpack:wither","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","forbidden_arcanus:rune_block_from_rune","mcwbiomesoplenty:maple_wired_fence","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","minecraft:calibrated_sculk_sensor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","create:cut_granite_brick_stairs_from_stone_types_granite_stonecutting","railcraft:bronze_gear","securitycraft:secret_warped_hanging_sign","ae2:tools/certus_quartz_spade","mcwroofs:gray_upper_lower_roof","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","ae2:network/cables/dense_covered_black","undergarden:depthrock_stairs_stonecutting","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","expatternprovider:epp_alt","occultism:crafting/spirit_campfire","croptopia:roasted_smoking","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","minecraft:white_stained_glass","securitycraft:secret_warped_sign_item","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","alltheores:aluminum_dust_from_hammer_crushing","securitycraft:taser","allthemodium:vibranium_nugget_from_ingot","ae2:network/cells/fluid_storage_cell_64k","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","ae2:tools/paintballs_pink","domum_ornamentum:white_brick_extra","aiotbotania:livingrock_hoe","connectedglass:scratched_glass_red_pane2","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","supplementaries:gold_door","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","sfm:labelgun","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","reliquary:angelic_feather","ae2:network/parts/toggle_bus","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","connectedglass:scratched_glass_blue2","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","botania:virus_nullodermal","mysticalagriculture:essence/minecraft/redstone","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","gtceu:smelting/smelt_dust_bronze_to_ingot","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","botania:manasteel_shovel","handcrafted:oven","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","minecraft:red_stained_glass","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","thermal_extra:smelting/soul_infused_ingot_from_dust_smelting","mcwpaths:cobbled_deepslate_crystal_floor_slab","silentgear:leather_from_scraps","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","rftoolsbuilder:shape_card_quarry_fortune","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","connectedglass:borderless_glass_blue_pane2","delightful:knives/bronze_knife","mcwpaths:blackstone_crystal_floor","travelersbackpack:quartz","mekanismgenerators:generator/bio","botania:yellow_petal_block","mcwfurnitures:cabinet_door","sophisticatedbackpacks:tool_swapper_upgrade","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","botania:super_travel_belt","minecraft:lime_terracotta","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","securitycraft:display_case","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","allthecompressed:compress/andesite_1x","mcwwindows:diorite_four_window","minecraft:stone_slab","pneumaticcraft:compressed_bricks","cfm:gray_sofa","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","mekanism:tier_installer/basic","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","allthemodium:vibranium_ingot_from_block","minecraft:jungle_stairs","travelersbackpack:backpack_tank","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","ae2:materials/cardenergy","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","dyenamics:bed/mint_bed_frm_white_bed","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","ae2:materials/carddistribution","ae2:network/parts/import_bus","croptopia:campfire_molasses","minecraft:dye_red_bed","ae2:network/crafting/16k_cpu_crafting_storage","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","ae2:network/upgrade_wireless_crafting_terminal","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","minecraft:blue_stained_glass_pane_from_glass_pane","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","railcraft:water_tank_siding","expatternprovider:assembler_matrix_crafter","pneumaticcraft:wall_lamp_inverted_light_gray","mcwtrpdoors:bamboo_bark_trapdoor","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","securitycraft:secret_cherry_hanging_sign","croptopia:melon_juice","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","ae2:network/cables/smart_cyan","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","rftoolsbuilder:space_chamber_controller","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","aiotbotania:elementium_aiot","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","mcwwindows:one_way_glass","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mekanism:electrolytic_core","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","botania:starfield","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","mekanism:tier_installer/advanced","cfm:mangrove_mail_box","mekanism:upgrade/energy","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","allthemodium:smithing/allthemodium_chestplate_smithing","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","ae2additions:components/super/16k","connectedglass:borderless_glass_orange2","botania:yellow_pavement","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","connectedglass:scratched_glass_black_pane2","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","create:crafting/kinetics/yellow_seat","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","pneumaticcraft:logistics_core","mcwpaths:andesite_windmill_weave_path","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","mcwdoors:bamboo_whispering_door","minecraft:mangrove_boat","minecraft:bread","mcwroofs:green_terracotta_steep_roof","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","minecolonies:eggdrop_soup","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","botania:water_rod","securitycraft:secret_cherry_sign_item","botania:horn_grass","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","allthemodium:allthemodium_ingot_from_block","mcwfences:iron_cheval_de_frise","handcrafted:red_sheet","minecraft:black_banner","tombstone:dark_marble","minecraft:iron_shovel","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","ironfurnaces:furnaces/diamond_furnace","biomesoplenty:willow_boat","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","ae2:smelting/smooth_sky_stone_block","mcwwindows:stone_brick_arrow_slit","chimes:amethyst_chimes","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","ad_astra:space_suit","securitycraft:disguise_module","mcwwindows:deepslate_four_window","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","mcwwindows:dark_oak_louvered_shutter","mcwpaths:granite_strewn_rocky_path","cfm:light_blue_picket_fence","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mythicbotany:ice_ring","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","rftoolsutility:counter_module","aether:netherite_boots_repairing","botania:lens_redirect","cfm:stripped_jungle_crate","create:granite_pillar_from_stone_types_granite_stonecutting","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","minecraft:oak_door","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwroofs:granite_steep_roof","mcwdoors:bamboo_barn_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwdoors:jungle_waffle_door","mcwroofs:black_roof_block","mcwbiomesoplenty:hellbark_pane_window","botania:elementium_shovel","advancedperipherals:peripheral_casing","connectedglass:clear_glass_cyan_pane2","mythicbotany:alfsteel_template","mythicbotany:fire_ring","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwbiomesoplenty:stripped_hellbark_log_four_window","allthemodium:smithing/unobtainium_boots_smithing","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","modularrouters:dropper_module","mcwbiomesoplenty:magic_highley_gate","minecraft:dark_prismarine","ae2:tools/fluix_sword","botania:balance_cloak","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","dyenamics:mint_terracotta","mcwwindows:pink_curtain","rftoolspower:pearl_injector","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","delightful:knives/refined_obsidian_knife","expatternprovider:ei_alt","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","mcwdoors:bamboo_swamp_door","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","deepresonance:radiation_suit_boots","minecraft:bow","supplementaries:wrench","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","undergarden:depthrock_stairs","securitycraft:reinforced_oak_fence_gate","botania:mushroom_stew","minecraft:andesite_stairs","botania:super_lava_pendant","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwdoors:bamboo_japanese_door","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","mcwroofs:prismarine_brick_roof","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","allthecompressed:compress/unobtainium_block_1x","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","connectedglass:borderless_glass_brown2","dyenamics:bed/aquamarine_bed_frm_white_bed","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","sophisticatedbackpacks:feeding_upgrade","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:melon_seeds","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","cfm:dye_cyan_picket_fence","minecraft:firework_rocket_simple","minecraft:black_dye_from_wither_rose","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwroofs:andesite_roof","botania:black_pavement","rftoolsbase:infused_enderpearl","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","functionalstorage:collector_upgrade","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","ae2:network/cables/glass_yellow","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","dyenamics:rose_terracotta","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","alltheores:signalum_plate","mcwdoors:jungle_tropical_door","minecraft:stone","twigs:lamp","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","minecraft:blackstone_stairs","cfm:lime_kitchen_counter","travelersbackpack:red_sleeping_bag","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","cfm:stripped_oak_kitchen_sink_dark","minecraft:dye_cyan_bed","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","crafting_on_a_stick:crafting_table","mcwdoors:oak_barn_door","botania:alchemy_catalyst","allthemodium:ancient_stone_bricks","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","ae2:tools/paintballs_brown","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","undergarden:depthrock_pressure_plate","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","mcwpaths:sandstone_windmill_weave_slab","undergarden:depthrock_slab","minecraft:smooth_stone","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","botania:conversions/elementium_block_deconstruct","rftoolsutility:environmental_controller","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","aether:golden_ring","minecraft:polished_granite_slab_from_granite_stonecutting","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","xnet:controller","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","reliquary:uncrafting/ender_pearl","additionallanterns:obsidian_chain","minecraft:ender_eye","supplementaries:blackstone_lamp","mcwpaths:brick_strewn_rocky_path","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","mcwlights:cross_lantern","mcwwindows:bricks_four_window","railcraft:receiver_circuit","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_packed_mud","minecraft:green_candle","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","rftoolsbase:manual","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","allthecompressed:compress/granite_1x","mekanism:pressurized_reaction_chamber","ae2:network/parts/terminals_pattern_access","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","ae2:block_cutter/walls/quartz_wall","ae2:network/blocks/io_condenser","mahoutsukai:powdered_iron","sophisticatedbackpacks:smoking_upgrade","railcraft:steel_tank_wall","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","minecraft:dye_black_wool","securitycraft:trophy_system","botania:elementium_axe","rftoolsbuilder:shape_card_quarry_silk","ad_astra:small_green_industrial_lamp","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","mcwbiomesoplenty:pine_curved_gate","undergarden:depthrock_tile_stairs_stonecutting","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","advanced_ae:quantumstorage128","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","allthemodium:raw_vibranium_block","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","botania:drum_gathering","mcwpaths:dark_prismarine_windmill_weave_path","mcwwindows:jungle_plank_window2","ad_astra:space_pants","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","ae2:network/cables/dense_smart_black","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","mysticalagriculture:supremium_essence_uncraft","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","additionallanterns:dark_prismarine_chain","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","enderio:resetting_lever_five_inv","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","utilitix:armed_stand","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","ae2:network/cables/smart_blue","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","botania:turntable","ae2:network/cells/item_storage_components_cell_1k_part","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","supplementaries:daub","alltheores:zinc_dust_from_hammer_crushing","railcraft:steel_chestplate","minecraft:netherite_scrap","mcwfurnitures:stripped_jungle_end_table","securitycraft:secret_oak_hanging_sign","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","ae2:decorative/quartz_block","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","ae2:block_cutter/slabs/smooth_sky_stone_slab","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","botania:mana_tablet","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deeperdarker:reinforced_echo_shard","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","ad_astra:small_blue_industrial_lamp","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","mcwpaths:granite_dumble_paving","mcwpaths:granite_flagstone_slab","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","pneumaticcraft:logistics_configurator","rftoolsutility:dialing_device","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","create:cut_granite_brick_slab_from_stone_types_granite_stonecutting","mcwroofs:granite_upper_steep_roof","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","croptopia:mead","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwdoors:bamboo_stable_door","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","botania:ender_hand","minecraft:stonecutter","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","mcwpaths:dark_prismarine_dumble_paving","minecraft:bone_block","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","minecraft:melon","ae2:network/cables/dense_smart_green","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","ad_astra:cyan_industrial_lamp","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","cfm:jungle_coffee_table","mcwpaths:granite_windmill_weave_slab","xnet:connector_green","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","create:oak_window","minecolonies:cheese_ravioli","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","botania:missile_rod","mekanism:chemical_tank/basic","create:crafting/kinetics/light_gray_seat","sophisticatedbackpacks:void_upgrade","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","railcraft:bushing_gear_bronze","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","undergarden:gloomgourd_pie","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","appbot:mana_storage_cell_16k","buildinggadgets2:gadget_cut_paste","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","rftoolsbuilder:mover_status","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwpaths:granite_running_bond_slab","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","create:crafting/kinetics/gearbox","sophisticatedstorage:copper_to_iron_tier_upgrade","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","rftoolsutility:screen_link","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mekanism:fluid_tank/basic","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","botania:elementium_sword","supplementaries:bubble_blower","naturalist:glow_goop","supplementaries:crank","securitycraft:secret_bamboo_sign_item","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:magenta_dye_from_blue_red_white_dye","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","allthecompressed:compress/ancient_stone_1x","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","minecraft:pink_terracotta","aiotbotania:livingrock_axe","mcwbiomesoplenty:mahogany_plank_pane_window","rftoolsutility:destination_analyzer","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","ae2:materials/cardfuzzy","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mekanism:energy_tablet","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","allthecompressed:compress/quartz_block_1x","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","minecraft:golden_hoe","cfm:oak_kitchen_counter","minecraft:fire_charge","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:dye_green_bed","alltheores:platinum_dust_from_hammer_crushing","railcraft:any_detector","connectedglass:borderless_glass_purple2","mcwpaths:dark_prismarine_clover_paving","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","sophisticatedstorage:controller","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","mcwtrpdoors:bamboo_mystic_trapdoor","minecraft:stone_button","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","farmingforblockheads:market","minecraft:sandstone","mcwwindows:birch_plank_window","allthemodium:vibranium_ingot_from_raw_blasting","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:lodestone","railcraft:brass_ingot_crafted_with_ingots","megacells:crafting/greater_energy_card","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwpaths:sandstone_running_bond_stairs","minecraft:polished_blackstone_slab","domum_ornamentum:blue_brick_extra","create:granite_from_stone_types_granite_stonecutting","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","mysticalagriculture:essence/common/silicon","aether:red_cape","pneumaticcraft:transfer_gadget","connectedglass:borderless_glass_red2","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","allthemodium:unobtainium_dust_from_ore_crushing","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","botania:open_bucket","blue_skies:glowing_blinding_stone","ae2:tools/paintballs_light_blue","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","tombstone:grave_plate","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","enderio:resetting_lever_five","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","undergarden:depthrock_button","ae2:network/parts/terminals","securitycraft:keypad_frame","minecolonies:pasta_tomato","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","allthemodium:smithing/vibranium_helmet_smithing","pneumaticcraft:vortex_tube","supplementaries:candle_holders/candle_holder_red_dye","xnet:connector_routing","mcwwindows:warped_louvered_shutter","blue_skies:glowing_nature_stone","aether:iron_shovel_repairing","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:palm_plank_window2","travelersbackpack:cake","megacells:network/mega_interface","mcwroofs:green_terracotta_attic_roof","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","immersiveengineering:crafting/shield","utilitix:directional_highspeed_rail","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","ae2:network/crafting/cpu_crafting_monitor","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","create:cut_granite_bricks_from_stone_types_granite_stonecutting","everythingcopper:copper_chestplate","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","mcwroofs:prismarine_brick_top_roof","cfm:warped_mail_box","mekanism:fluid_tank/elite","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","rftoolsutility:crafter1","ae2:network/blocks/storage_chest","securitycraft:secret_bamboo_hanging_sign","cfm:jungle_desk","mcwroofs:oak_roof","aether:diamond_boots_repairing","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwroofs:light_gray_roof_slab","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","mcwbridges:balustrade_prismarine_bricks_bridge","minecraft:jungle_trapdoor","ae2:network/wireless_booster","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","ae2:tools/paintballs_orange","mcwfences:blackstone_brick_railing_gate","mcwtrpdoors:bamboo_tropical_trapdoor","sophisticatedbackpacks:filter_upgrade","minecraft:granite_stairs_from_granite_stonecutting","aether:skyroot_barrel","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","minecraft:iron_ingot_from_nuggets","ae2:network/cables/dense_covered_purple","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","undergarden:depthrock_bricks_stonecutting","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","minecraft:gray_bed","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","supplementaries:evilcraft/sign_post_undead","xnet:wireless_router","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","pneumaticcraft:compressed_bricks_from_tile_stonecutting","twigs:chiseled_bricks","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","cfm:red_sofa","dyenamics:navy_wool","mcwwindows:magenta_mosaic_glass_pane","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","rftoolscontrol:tank","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","reliquary:void_tear","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","appbot:mana_cell_housing","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","minecraft:chiseled_stone_bricks_stone_from_stonecutting","croptopia:potato_chips","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","botania:red_string_fertilizer","minecraft:netherite_sword_smithing","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","croptopia:shaped_toast_sandwich","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","megacells:cells/mega_item_cell_housing","minecolonies:mutton_dinner","allthecompressed:compress/allthemodium_block_1x","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","delightful:food/baklava","oceansdelight:seagrass_salad","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","comforts:hammock_to_black","arseng:source_storage_cell_16k","railcraft:signal_circuit","ae2:tools/paintballs_red","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","minecraft:prismarine_brick_slab_from_prismarine_stonecutting","evilcraft:special/vengeance_pickaxe","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","connectedglass:borderless_glass_pane3","croptopia:buttered_toast","minecraft:gray_banner","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","rftoolsbuilder:vehicle_builder","dyenamics:ultramarine_terracotta","cfm:red_grill","ae2:network/cells/fluid_storage_cell_16k_storage","ae2:tools/paintballs_gray","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","dankstorage:1_to_2","ad_astra:blue_industrial_lamp","supplementaries:flags/flag_light_blue","mcwpaths:cobbled_deepslate_running_bond","cfm:birch_kitchen_sink_light","allthemodium:ancient_chiseled_stone_bricks_from_crushing","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","croptopia:chicken_and_dumplings","mcwwindows:dark_oak_plank_four_window","mekanism:transmitter/mechanical_pipe/basic","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwpaths:granite_flagstone_path","utilitix:tiny_charcoal_to_tiny","connectedglass:borderless_glass_light_blue2","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","mahoutsukai:powdered_emerald","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","alltheores:smelting_dust/osmium_ingot","additionallanterns:prismarine_chain","nethersdelight:hoglin_sirloin_from_campfire_cooking","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","supplementaries:sign_post_oak","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwroofs:dark_prismarine_steep_roof","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","pneumaticcraft:air_cannon","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","additionallanterns:granite_chain","bigreactors:turbine/basic/passivetap_fe","mcwpaths:dark_prismarine_windmill_weave_slab","mcwpaths:brick_running_bond","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","create:small_granite_brick_wall_from_stone_types_granite_stonecutting","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwpaths:granite_honeycomb_paving","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwroofs:gray_attic_roof","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:blue_candle","railcraft:blast_furnace_bricks","ae2:network/cables/dense_covered_pink","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","reliquary:uncrafting/glass_bottle","ae2:tools/portable_item_cell_256k","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","securitycraft:secret_crimson_sign_item","mcwroofs:purple_terracotta_upper_lower_roof","ae2:network/cells/item_storage_cell_16k_storage","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","ae2:network/parts/cable_anchor","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","blue_skies:cake_compat","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","pneumaticcraft:liquid_hopper","gtceu:smelting/smelt_dust_iron_to_ingot","handcrafted:wood_cup","botania:green_pavement","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","ad_astra:ti_69","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","croptopia:fried_chicken","railcraft:sheep_detector","minecraft:tin_ingot_from_smelting_tin_raw","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","rftoolsbuilder:shape_card_quarry","mcwbiomesoplenty:magic_hedge","evilcraft:crafting/undead_planks","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime2","ae2:network/parts/terminals_crafting","xnet:netcable_blue","cfm:stripped_oak_chair","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","connectedglass:scratched_glass_black2","cfm:rock_path","botania:mana_spreader","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","minecraft:quartz_pillar","botania:slingshot","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:network/cables/glass_gray","ae2:tools/certus_quartz_pickaxe","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","minecraft:dye_red_wool","ae2:network/blocks/crystal_processing_charger","botania:lens_tripwire","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","ad_astra:black_industrial_lamp","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","allthemodium:unobtainium_ingot_from_dust_blasting","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","mekanism:energized_smelter","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","mcwwindows:crimson_planks_four_window","minecraft:piston","ad_astra:large_gas_tank","mcwfurnitures:jungle_coffee_table","appmek:chemical_storage_cell_4k","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","allthemodium:allthemodium_ingot_from_raw_smelting","undergarden:regalium_block","ae2:network/cells/item_storage_components_cell_16k_part","mcwbiomesoplenty:umbran_plank_pane_window","connectedglass:borderless_glass_red_pane2","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","botania:mana_bomb","minecraft:wooden_axe","cfm:stripped_oak_desk","connectedglass:borderless_glass_light_gray2","mcwdoors:bamboo_four_panel_door","mcwtrpdoors:oak_glass_trapdoor","railcraft:villager_detector","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwroofs:gray_upper_steep_roof","botania:third_eye","mcwwindows:metal_curtain_rod","croptopia:apple_sapling","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","laserio:logic_chip","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","handcrafted:stackable_book","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","mcwpaths:granite_windmill_weave","mcwpaths:granite_windmill_weave_path","silentgear:sinew_fiber","handcrafted:gray_cushion","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","silentgear:raw_crimson_iron_block","ad_astra:space_helmet","supplementaries:flags/flag_magenta","minecraft:diamond_axe","mcwpaths:granite_running_bond_path","mcwbridges:end_stone_bricks_bridge_pier","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","appmek:chemical_storage_cell_1k","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","minecraft:dye_blue_wool","mcwlights:covered_wall_lantern","allthemodium:smithing/vibranium_leggings_smithing","dyenamics:banner/bubblegum_banner","expatternprovider:ex_io_port","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","botania:fire_rod","securitycraft:reinforced_crimson_fence_gate","create:polished_cut_granite_wall_from_stone_types_granite_stonecutting","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","create:cut_granite_stairs_from_stone_types_granite_stonecutting","botania:spawner_mover","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","botania:elementium_shears","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:prismarine_brick_upper_steep_roof","mcwroofs:brown_concrete_roof","minecraft:red_banner","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","minecraft:black_carpet","securitycraft:secret_mangrove_hanging_sign","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","botania:manasteel_chestplate","botania:magenta_shiny_flower","allthecompressed:compress/silver_block_1x","mcwfences:bamboo_highley_gate","mcwpaths:dark_prismarine_windmill_weave","mythicbotany:yggdrasil_branch","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","ae2:tools/paintballs_blue","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","mcwfurnitures:stripped_jungle_stool_chair","ae2:tools/paintballs_lime","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","minecraft:torch","ad_astra:gray_flag","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","mcwroofs:light_gray_top_roof","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","travelersbackpack:gray_sleeping_bag","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","cfm:dye_black_picket_gate","minecraft:sandstone_wall","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbridges:dry_bamboo_bridge_pier","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","connectedglass:scratched_glass_red2","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/dense_covered_white","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","mcwroofs:sandstone_lower_roof","domum_ornamentum:brown_bricks","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","mcwpaths:sandstone_basket_weave_paving","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_2","minecraft:allthemodium_spell_book_smithing","sophisticatedstorage:stack_upgrade_tier_3","mcwwindows:stripped_acacia_log_four_window","silentgear:crimson_iron_raw_ore_blasting","undergarden:grongle_boat","rftoolsutility:matter_beamer","ae2:decorative/quartz_fixture","ae2:network/cells/item_storage_cell_16k","domum_ornamentum:green_floating_carpet","mcwwindows:cherry_plank_window2","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","minecraft:carrot_on_a_stick","rftoolspower:blazing_agitator","allthemodium:unobtainium_ingot_from_raw_smelting","mcwwindows:spruce_curtain_rod","cfm:dye_blue_picket_gate","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","aether:netherite_helmet_repairing","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","expatternprovider:cobblestone_cell","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","minecraft:granite_wall","mcwroofs:prismarine_brick_attic_roof","simplylight:illuminant_purple_block_on_dyed","handcrafted:granite_corner_trim","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","pneumaticcraft:tube_junction","bambooeverything:bamboo_torch","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","alltheores:constantan_ingot_from_dust","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","expatternprovider:silicon_block","minecraft:item_frame","mekanism:tier_installer/elite","itemcollectors:basic_collector","minecraft:loom","enderio:resetting_lever_three_hundred_inv","ad_astra:steel_engine","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","rftoolsutility:tank","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","mcwpaths:granite_running_bond_stairs","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","mcwpaths:granite_flagstone","create:cut_granite_brick_wall_from_stone_types_granite_stonecutting","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","minecraft:polished_blackstone","handcrafted:blue_crockery_combo","advanced_ae:quantumaccel","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","dyenamics:bed/maroon_bed_frm_white_bed","travelersbackpack:dye_red_sleeping_bag","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","botania:gaia_spreader","mcwroofs:black_upper_lower_roof","undergarden:depthrock_brick_stairs_stonecutting","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","mcwpaths:dark_prismarine_diamond_paving","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwpaths:dark_prismarine_flagstone","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:dye_red_picket_fence","travelersbackpack:horse","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","bigreactors:reprocessor/wasteinjector","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","comforts:sleeping_bag_to_gray","mcwpaths:granite_crystal_floor_stairs","undergarden:shard_torch","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwpaths:dark_prismarine_honeycomb_paving","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","mcwpaths:dark_prismarine_flagstone_path","botania:petal_yellow","aether:golden_leggings_repairing","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","undergarden:gloomgourd_seeds","mcwroofs:blue_striped_awning","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","minecraft:dark_prismarine_stairs","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","ae2:tools/paintballs_purple","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","sophisticatedbackpacks:compacting_upgrade","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cells/item_storage_cell_64k_storage","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","ironfurnaces:furnaces/crystal_furnace","minecraft:iron_axe","modularrouters:puller_module_2_x4","mcwroofs:dark_prismarine_roof","create:crafting/kinetics/fluid_valve","enderio:resetting_lever_thirty_inv_from_base","supplementaries:crystal_display","minecraft:sandstone_stairs","minecraft:composter","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","create:layered_granite_from_stone_types_granite_stonecutting","undergarden:polished_depthrock","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","appmek:chemical_storage_cell_16k","alltheores:signalum_rod","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","occultism:crafting/demons_dream_essence_from_seeds","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","farmersdelight:cooking/pasta_with_mutton_chop","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwroofs:bricks_attic_roof","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","connectedglass:borderless_glass_yellow2","mcwdoors:jungle_whispering_door","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwpaths:granite_clover_paving","allthemodium:smithing/unobtainium_chestplate_smithing","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","ad_astra:airlock","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","minecolonies:pottage","croptopia:egg_roll","twigs:bamboo_mat","connectedglass:borderless_glass_black2","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","railcraft:solid_fueled_firebox","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","ae2:network/cells/item_storage_cell_1k_storage","botania:lens_firework","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","aether:iron_pendant","securitycraft:redstone_module","minecraft:lime_dye","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","botania:quartz_sunny","mcwbiomesoplenty:stripped_redwood_log_window","botania:flighttiara_0","railcraft:signal_lamp","cfm:jungle_blinds","reliquary:uncrafting/sugar","tombstone:bone_needle","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","minecraft:cyan_dye","mcwwindows:acacia_window2","evilcraft:crafting/dark_stick","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwroofs:white_top_roof","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","botania:floating_pure_daisy","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","ae2:network/cables/dense_smart_cyan","handcrafted:black_sheet","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","connectedglass:scratched_glass_cyan2","securitycraft:secret_mangrove_sign_item","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","travelersbackpack:warden","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","dyenamics:fluorescent_concrete_powder","botania:incense_plate","allthecompressed:compress/clay_1x","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","mcwpaths:dark_prismarine_square_paving","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","bambooeverything:dry_bamboo","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","railways:stonecutting/riveted_locometal","securitycraft:reinforced_observer","functionalstorage:oak_1","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwpaths:dark_prismarine_running_bond","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","allthemodium:unobtainium_rod","mcwbiomesoplenty:dead_pane_window","rftoolsutility:screen_controller","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","allthecompressed:compress/blazing_crystal_block_1x","cfm:dye_green_picket_fence","mcwwindows:oak_plank_window","mcwroofs:granite_roof","nethersdelight:hoglin_sirloin","mcwwindows:oak_log_parapet","ae2wtlib:wireless_universal_terminal/ca","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","ae2:network/cells/item_storage_components_cell_64k_part","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwbridges:bamboo_bridge_pier","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:flower_pot","create:crafting/schematics/empty_schematic","ae2wtlib:wireless_universal_terminal/ae","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","allthecompressed:compress/niotic_crystal_block_1x","farmersdelight:steak_and_potatoes","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","mcwpaths:sandstone_crystal_floor_stairs","pneumaticcraft:wall_lamp_inverted_blue","allthemodium:vibranium_plate","securitycraft:codebreaker","supplementaries:flute","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:light_blue_picket_gate","allthearcanistgear:unobtainium_leggings_smithing","constructionwand:infinity_wand","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","farmersdelight:cooking/squid_ink_pasta","comforts:hammock_to_gray","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","cfm:blue_kitchen_counter","mcwbiomesoplenty:maple_four_window","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","handcrafted:granite_pillar_trim","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwbridges:end_stone_bricks_bridge_stair","mcwdoors:bamboo_classic_door","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","silentgear:bort_block","minecraft:pink_dye_from_red_white_dye","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","rftoolspower:blazing_generator","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","mcwpaths:dark_prismarine_crystal_floor_path","supplementaries:statue","reliquary:wraith_node","immersiveengineering:crafting/tinted_glass_lead_wire","minecraft:dye_green_carpet","botania:diva_charm","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","securitycraft:secret_jungle_sign_item","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","ae2:network/crafting/cpu_crafting_accelerator","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwroofs:white_terracotta_upper_steep_roof","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","minecraft:stick_from_bamboo_item","alltheores:bronze_plate","mcwpaths:brick_crystal_floor_slab","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","twilightforest:wood/oak_banister","alltheores:enderium_plate","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","modularrouters:player_module","pneumaticcraft:pressure_chamber_wall","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:end_stone_brick_stairs","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwroofs:dark_prismarine_upper_lower_roof","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwpaths:granite_windmill_weave_stairs","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","ae2:network/cables/smart_red","ae2additions:components/super/64k","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","travelersbackpack:bookshelf_smithing","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","minecraft:granite_stairs","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","ad_astra:green_industrial_lamp","appmek:chemical_storage_cell_64k","undergarden:depthrock_tile_slab_stonecutting","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","chimes:bamboo_chimes","rftoolsstorage:storage_control_module","securitycraft:secret_spruce_sign_item","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwpaths:granite_square_paving","cfm:oak_upgraded_fence","mob_grinding_utils:recipe_mob_swab","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","botania:super_cloud_pendant","railcraft:steel_gear","allthemodium:piglich_heart_block","bigreactors:smelting/graphite_from_charcoal","minecraft:prismarine_bricks","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","connectedglass:borderless_glass_cyan2","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","ae2:tools/paintballs_cyan","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","xnet:netcable_green_dye","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","twilightdelight:cutting/ice_bomb","minecolonies:soysauce","biomesoplenty:orange_dye_from_orange_cosmos","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","botania:quartz_lavender","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","botania:petal_yellow_double","immersiveengineering:crafting/paper_from_sawdust","reliquary:glowing_water_from_potion_vial","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","pneumaticcraft:logistics_frame_requester_self","botania:dye_light_gray","megacells:cells/mega_fluid_cell_housing","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","pylons:potion_filter","enderio:resetting_lever_thirty","minecraft:glistering_melon_slice","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:crafting/kinetics/gray_seat","botania:conversions/light_gray_petal_block_deconstruct","everythingcopper:copper_hoe","create:crafting/kinetics/brown_seat","cfm:stripped_mangrove_kitchen_sink_light","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","handcrafted:quartz_corner_trim","ad_astra:steel_factory_block","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","supplementaries:candle_holders/candle_holder_black_dye","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","occultism:crafting/spirit_torch","undergarden:depthrock_brick_wall_stonecutting","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","create:polished_cut_granite_from_stone_types_granite_stonecutting","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","twigs:stone_column","aeinfinitybooster:dimension_card","minecraft:lectern","pneumaticcraft:compressed_bricks_from_stone_stonecutting","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","silentgear:crimson_iron_raw_ore_smelting","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","botania:azulejo_0","mcwtrpdoors:bamboo_blossom_trapdoor","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","securitycraft:secret_sign_item","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","connectedglass:borderless_glass_white2","ae2additions:components/super/1k","aquaculture:heavy_hook","aether:netherite_sword_repairing","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","mcwwindows:acacia_window","allthemodium:smithing/unobtainium_leggings_smithing","croptopia:food_press","mcwbridges:prismarine_bricks_bridge","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","xnet:router","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","mcwpaths:dark_prismarine_running_bond_stairs","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","mcwroofs:orange_concrete_lower_roof","farmersdelight:nether_salad","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","pneumaticcraft:reinforced_brick_pillar","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","mcwpaths:brick_crystal_floor","botania:dodge_ring","ae2:materials/basiccard","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","botania:laputa_shard","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwfences:oak_highley_gate","xnet:connector_green_dye","minecraft:dark_prismarine_slab_from_dark_prismarine_stonecutting","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","allthemodium:ancient_cracked_stone_brick_slabs","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","comforts:hammock_to_red","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","dyenamics:wine_concrete_powder","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:end_stone_brick_slab","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","mekanism:control_circuit/elite","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","delightful:smelting/roasted_acorn","mcwpaths:dark_prismarine_crystal_floor_stairs","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","mcwdoors:bamboo_beach_door","ae2:block_cutter/walls/smooth_sky_stone_wall","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:netherite_gloves_repairing","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dimstorage:dim_core","dyenamics:bed/navy_bed_frm_white_bed","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","allthemods:mekanismgenerators/gas_burning_gen","sophisticatedbackpacks:refill_upgrade","ae2additions:components/super/4k","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","alltheores:enderium_rod","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","connectedglass:borderless_glass_green2","dyenamics:bed/persimmon_bed_frm_white_bed","mythicbotany:gaia_pylon","mcwfences:dark_oak_curved_gate","mcwpaths:granite_basket_weave_paving","undergarden:depthrock_brick_slab_stonecutting","mcwdoors:bamboo_bark_glass_door","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","ad_astra:compressor","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","minecraft:red_bed","farmersdelight:cooking/mushroom_rice","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","ae2:network/cells/item_storage_cell_4k","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","dyenamics:navy_dye","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","ae2:network/wireless_part","mcwpaths:brick_windmill_weave_slab","ae2:materials/cardspeed","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","botania:holy_cloak","mcwdoors:print_bamboo","createoreexcavation:vein_finder","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","minecraft:copper_ingot_from_smelting_raw_copper","sophisticatedbackpacks:chipped/carpenters_table_upgrade","cfm:dye_black_picket_fence","mcwbiomesoplenty:stripped_maple_log_window2","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","naturalist:bug_net","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","supplementaries:hourglass","ae2:tools/paintballs_light_gray","everythingcopper:copper_leggings","ae2:network/cells/item_storage_cell_1k","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","minecraft:orange_dye_from_red_yellow","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","travelersbackpack:hay_smithing","mcwwindows:spruce_plank_window","supplementaries:checker","croptopia:flour","minecraft:jungle_sign","minecraft:cyan_candle","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","railcraft:steel_tank_valve","domum_ornamentum:cactus_extra","cfm:cyan_cooler","ae2:network/parts/formation_plane","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","ad_astra:steel_rod","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwroofs:light_gray_roof","securitycraft:secret_jungle_hanging_sign","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwdoors:bamboo_tropical_door","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","minecraft:light_gray_dye_from_black_white_dye","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","travelersbackpack:redstone","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwroofs:granite_upper_lower_roof","chimes:glass_bells","mcwwindows:iron_shutter","minecraft:netherite_upgrade_smithing_template","evilcraft:crafting/blood_extractor","immersiveengineering:crafting/blueprint_molds","aether:diamond_chestplate_repairing","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","comforts:sleeping_bag_to_black","farmersdelight:cutting_board","croptopia:trail_mix","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","ad_astra:nasa_workbench","mcwpaths:sandstone_flagstone","ad_astra:etrionic_capacitor","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","mcwbridges:blackstone_bridge_pier","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","rftoolspower:coalgenerator","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","enderio:resetting_lever_sixty_inv","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","minecraft:end_crystal","croptopia:shaped_nether_wart_stew","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:magenta_terracotta_attic_roof","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","allthemodium:smithing/unobtainium_sword_smithing","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","dyenamics:maroon_dye","utilitix:filter_rail","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","enderio:resetting_lever_sixty_from_inv","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","minecraft:mossy_cobblestone_from_moss_block","railcraft:steel_tunnel_bore_head","securitycraft:secret_birch_sign_item","minecolonies:chainmailhelmet","gtceu:smelting/smelt_dust_electrum_to_ingot","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","botania:unholy_cloak","mcwbridges:granite_bridge_pier","ae2:network/parts/tunnels_me","allthecompressed:compress/vibranium_block_1x","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","travelersbackpack:dye_green_sleeping_bag","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","arseng:source_storage_cell_4k","ae2:network/cells/fluid_storage_cell_4k","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","ae2:network/cables/dense_covered_yellow","alltheores:gold_dust_from_hammer_ingot_crushing","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","modularrouters:void_module","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","dankstorage:2_to_3","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","allthemodium:vibranium_ingot_from_raw_smelting","mysticalagriculture:imperium_essence_uncraft","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","create:cut_granite_from_stone_types_granite_stonecutting","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","securitycraft:reinforced_warped_fence_gate","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwroofs:prismarine_brick_upper_lower_roof","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","caupona:clay_cistern","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","allthemodium:smithing/vibranium_chestplate_smithing","bloodmagic:synthetic_point","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","supplementaries:candle_holders/candle_holder_cyan_dye","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","alltheores:lumium_ingot_from_dust","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","handcrafted:terracotta_bowl","botania:black_hole_talisman","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","connectedglass:borderless_glass_pink2","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","cfm:dye_cyan_picket_gate","create:crafting/schematics/schematic_and_quill","minecraft:dye_blue_bed","allthemodium:vibranium_ingot","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","botania:diluted_pool","modularrouters:energy_output_module","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","appbot:mana_storage_cell_64k","mekanism:fluid_tank/advanced","undergarden:chiseled_depthrock_bricks_stonecutting","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","connectedglass:tinted_borderless_glass_blue2","minecraft:polished_blackstone_pressure_plate","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","securitycraft:secret_dark_oak_hanging_sign","alltheores:diamond_rod","rftoolsbuilder:shield_block2","minecraft:stone_slab_from_stone_stonecutting","rftoolsbuilder:shield_block1","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","ae2:network/crafting/64k_cpu_crafting_storage","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwroofs:granite_lower_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","handcrafted:wood_bowl","pneumaticcraft:charging_station","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwdoors:bamboo_barn_glass_door","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","delightful:cantaloupe_slice","mcwbridges:balustrade_blackstone_bridge","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","delightful:food/cooking/stuffed_cantaloupe","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","mysticalagriculture:essence/appliedenergistics2/certus_quartz_dust","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","securitycraft:secret_acacia_hanging_sign","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","botania:water_ring","mcwroofs:purple_terracotta_attic_roof","arseng:source_storage_cell_1k","mcwroofs:orange_terracotta_lower_roof","sophisticatedstorage:stack_upgrade_tier_1_plus","ae2:network/cells/fluid_storage_cell_1k","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwroofs:brown_terracotta_upper_steep_roof","mcwdoors:oak_swamp_door","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","allthemodium:smithing/allthemodium_helmet_smithing","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","allthemodium:smithing/allthemodium_boots_smithing","mcwwindows:mangrove_shutter","expatternprovider:ex_drive","cfm:lime_cooler","rftoolsbuilder:shape_card_quarry_clear_fortune","mcwpaths:andesite_crystal_floor","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","extradisks:advanced_machine_casing","alltheores:copper_ingot_from_block","ae2:network/cells/fluid_storage_cell_64k_storage","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","dyenamics:mint_dye","silentgear:emerald_from_shards","supplementaries:candy","minecolonies:chicken_broth","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","ae2:network/cells/fluid_storage_cell_16k","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwroofs:gray_roof_block","mcwbiomesoplenty:fir_plank_four_window","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","ae2:network/crafting/1k_cpu_crafting_storage","allthecompressed:compress/blaze_block_1x","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwdoors:bamboo_nether_door","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","ae2:decorative/cut_quartz_block","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","allthemodium:smithing/allthemodium_leggings_smithing","mcwfences:sandstone_grass_topped_wall","allthemodium:vibranium_ingot_from_dust_smelting","travelersbackpack:black_sleeping_bag","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","reliquary:mercy_cross","farmersdelight:fried_egg","minecraft:magma_cream","botania:slime_bottle","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","botania:dirt_rod","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","blue_skies:maple_bookshelf","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","allthemodium:unobtainium_ingot_from_dust_smelting","connectedglass:clear_glass_red2","rftoolspower:power_core1","pneumaticcraft:logistics_frame_default_storage","botania:gaia_ingot","ae2:network/blocks/io_port","mcwroofs:white_concrete_upper_steep_roof","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","allthemodium:smithing/vibranium_pickaxe_smithing","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","simplylight:illuminant_lime_block_on_toggle","ae2:misc/deconstruction_certus_quartz_block","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","reliquary:mob_charm_fragments/enderman","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","mcwpaths:brick_running_bond_path","supplementaries:sugar_cube","securitycraft:security_camera","rftoolsbuilder:shape_card_quarry_dirt","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","botania:mana_pylon","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","mcwroofs:dark_prismarine_top_roof","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","mcwpaths:brick_windmill_weave_stairs","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","undergarden:polished_depthrock_stonecutting","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","minecraft:dye_cyan_wool","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","advanced_ae:smalladvpatpro","botania:apothecary_livingrock","mcwdoors:oak_whispering_door","alltheores:signalum_ingot_from_dust","mcwtrpdoors:bamboo_barn_trapdoor","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","ae2:tools/paintballs_green","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","railcraft:feed_station","minecraft:black_stained_glass_pane_from_glass_pane","create:polished_cut_granite_stairs_from_stone_types_granite_stonecutting","connectedglass:scratched_glass_green_pane2","pneumaticcraft:wall_lamp_inverted_green","mcwbridges:balustrade_granite_bridge","mcwroofs:stone_upper_steep_roof","mcwroofs:dark_prismarine_lower_roof","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","arseng:source_storage_cell_64k","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","immersiveengineering:crafting/plate_uranium_hammering","mcwdoors:bamboo_paper_door","railcraft:personal_world_spike","handcrafted:oak_nightstand","immersiveengineering:crafting/armor_steel_chestplate","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","megacells:crafting/greater_energy_card_upgraded","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","xnet:redstoneproxy_update","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","expatternprovider:water_cell","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","delightful:food/cooking/ender_nectar","merequester:requester","mcwfences:stone_brick_railing_gate","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","minecraft:dark_prismarine_stairs_from_dark_prismarine_stonecutting","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","supplementaries:slingshot","allthemodium:allthemodium_plate","ae2:network/blocks/quantum_link","advanced_ae:quantumunit","twigs:cobblestone_from_pebble","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","everythingcopper:copper_rail","ae2:network/cells/item_storage_components_cell_4k_part","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","evilcraft:crafting/blood_infuser","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","cfm:black_sofa","aquaculture:neptunium_ingot_from_nuggets","mcwroofs:bricks_upper_steep_roof","croptopia:french_fries","create:small_granite_bricks_from_stone_types_granite_stonecutting","minecraft:iron_pickaxe","aether:shield_repairing","ae2:shaped/not_so_mysterious_cube","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","reliquary:fortune_coin","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","rftoolsutility:matter_booster","ae2:network/cables/glass_orange","deepresonance:resonating_plate","ae2:materials/cardinverter","minecraft:vibranium_mage_chestplate_smithing","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","mcwfences:spruce_highley_gate","minecraft:birch_boat","handcrafted:gray_sheet","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","mcwtrpdoors:bamboo_classic_trapdoor","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","mcwtrpdoors:bamboo_barred_trapdoor","minecraft:lime_stained_glass","ae2:tools/paintballs_black","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","cfm:white_kitchen_counter","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","alltheores:enderium_ingot_from_dust","ae2:network/cables/covered_fluix_clean","botania:elementium_block","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","minecraft:yellow_dye_from_dandelion","mcwroofs:light_blue_terracotta_lower_roof","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwpaths:dark_prismarine_running_bond_slab","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","trashcans:energy_trash_can","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","pneumaticcraft:air_compressor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:granite_attic_roof","mcwdoors:bamboo_modern_door","minecolonies:baked_salmon","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","chimes:copper_chimes","powah:smelting/uraninite_from_raw","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","reliquary:uncrafting/ghast_tear","ae2:tools/paintballs_yellow","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","integrateddynamics:crafting/squeezer","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","botania:dye_yellow","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","expatternprovider:wireless_connector","reliquary:mob_charm_fragments/wither_skeleton","mcwbridges:granite_bridge_stair","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","mcwroofs:black_terracotta_lower_roof","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","ae2:network/cables/dense_covered_fluix_clean","minecraft:scaffolding","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","minecraft:polished_granite_from_granite_stonecutting","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","mcwtrpdoors:bamboo_swamp_trapdoor","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","minecraft:honey_block","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","farmersdelight:melon_juice","dyenamics:bed/bubblegum_bed_frm_white_bed","sophisticatedbackpacks:crafting_upgrade","aquaculture:birch_fish_mount","ae2:network/cells/fluid_storage_cell_1k_storage","mcwwindows:stripped_mangrove_log_window","securitycraft:secret_crimson_hanging_sign","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","reliquary:uncrafting/glowstone_dust","tombstone:ankh_of_prayer","cfm:white_sofa","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwfences:ornate_metal_fence","rftoolsbuilder:mover_control2","mcwfences:acacia_highley_gate","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","aiotbotania:livingrock_sword","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","connectedglass:tinted_borderless_glass_black2","travelersbackpack:squid","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","allthecompressed:compress/melon_1x","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","ae2:network/blocks/cell_workbench","nethersdelight:hoglin_sirloin_from_smoking","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","ae2:shaped/slabs/sky_stone_block","allthemodium:allthemodium_rod","mcwroofs:prismarine_brick_steep_roof","handcrafted:oak_bench","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwpaths:dark_prismarine_flagstone_stairs","minecraft:repeater","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","forbidden_arcanus:test_tube","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","rftoolsbuilder:shape_card_quarry_silk_dirt","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","botania:elementium_boots","undergarden:depthrock_slab_stonecutting","mcwpaths:blackstone_crystal_floor_path","cfm:jungle_cabinet","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwroofs:base_roof_block","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","ae2:tools/paintballs_white","mcwbiomesoplenty:palm_window","twigs:blackstone_column","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwroofs:dark_prismarine_upper_steep_roof","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","gtceu:smelting/smelt_raw_nickel_ore_to_ingot","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","ad_astra:red_industrial_lamp","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","minecraft:red_candle","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","ae2:block_cutter/slabs/quartz_slab","sophisticatedbackpacks:chipped/glassblower_upgrade","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:jungle_door","minecraft:prismarine_brick_slab","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","ae2:network/cells/item_storage_components_cell_256k_part","dyenamics:bed/fluorescent_bed_frm_white_bed","minecraft:polished_andesite_from_andesite_stonecutting","allthearcanistgear:vibranium_robes_smithing","mcwbridges:sandstone_bridge_pier","mysticalagriculture:essence/minecraft/iron_ingot","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","botania:reach_ring","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","handcrafted:jungle_drawer","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","botania:mushroom_5","minecraft:oak_pressure_plate","botania:mushroom_4","ae2:misc/chests_smooth_sky_stone","minecraft:stone_brick_stairs","allthemodium:ancient_cracked_stone_brick_wall","botania:mushroom_3","aether:skyroot_beehive","bigreactors:turbine/basic/activefluidport_forge","aether:netherite_leggings_repairing","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","mcwwindows:quartz_window","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","botania:glimmering_livingwood_log","reliquary:mob_charm_fragments/creeper","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","minecraft:clock","mcwroofs:red_concrete_top_roof","create:crafting/appliances/netherite_diving_helmet_from_netherite","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","megacells:crafting/compression_card","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","allthemods:ae2/skystone_dust","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","minecraft:prismarine","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","minecolonies:pasta_plain","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","silentgear:leather_scrap","mcwroofs:purple_terracotta_roof","mcwfurnitures:stripped_jungle_chair","botania:spark_changer","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","cfm:acacia_kitchen_sink_dark","rftoolsbase:machine_frame","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","minecraft:dye_black_bed","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","pylons:infusion_pylon","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","expatternprovider:assembler_matrix_speed","utilitix:mob_yoinker","mcwwindows:andesite_four_window","domum_ornamentum:light_blue_brick_extra","minecraft:granite_slab","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","mcwroofs:thatch_top_roof","farmersdelight:iron_nugget_from_smelting_knife","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:leather_leggings","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","securitycraft:block_change_detector","croptopia:fruit_salad","mcwpaths:granite_flagstone_stairs","twigs:rocky_dirt","minecraft:polished_blackstone_button","advanced_ae:advpatpro2","supplementaries:pancake_fd","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","minecraft:netherite_scrap_from_blasting","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","mcwtrpdoors:bamboo_four_panel_trapdoor","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","enderio:silent_stone_pressure_plate","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","additionallanterns:quartz_chain","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","mekanism:processing/gold/ingot/from_dust_smelting","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","connectedglass:scratched_glass_cyan_pane2","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","create:small_granite_brick_slab_from_stone_types_granite_stonecutting","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","ad_astra:wheel","minecraft:waxed_copper_block_from_honeycomb","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","minecraft:warped_fungus_on_a_stick","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","minecraft:sandstone_slab","mcwwindows:birch_window","mcwfences:acacia_picket_fence","reliquary:uncrafting/wither_skeleton_skull","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","securitycraft:secret_dark_oak_sign_item","ae2:network/cables/dense_covered_cyan","sophisticatedbackpacks:smithing_upgrade","minecraft:gold_ingot_from_smelting_raw_gold","mekanism:tier_installer/ultimate","mcwfences:mud_brick_railing_gate","twigs:oak_table","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","pneumaticcraft:heat_pipe","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","comforts:hammock_gray","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","railcraft:logbook","ae2:misc/tiny_tnt","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","mcwfences:mangrove_wired_fence","minecraft:red_terracotta","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","connectedglass:clear_glass1","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwpaths:blackstone_dumble_paving","mcwroofs:white_roof_block","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","travelersbackpack:melon","minecraft:white_dye_from_lily_of_the_valley","securitycraft:block_pocket_manager","mcwlights:soul_cherry_tiki_torch","expatternprovider:ei","botania:red_pavement","minecraft:end_stone_brick_wall","securitycraft:reinforced_mangrove_fence","alltheores:copper_ore_hammer","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","mcwpaths:granite_diamond_paving","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","botania:flower_bag","ad_astra:steel_plateblock","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","allthemodium:unobtainium_ingot_from_block","mcwfences:dark_oak_wired_fence","minecraft:netherite_chestplate_smithing","cfm:jungle_kitchen_counter","ae2:network/cables/dense_smart_red","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwwindows:stone_pane_window","botania:light_gray_petal_block","domum_ornamentum:white_paper_extra","mcwpaths:dark_prismarine_crystal_floor","connectedglass:borderless_glass_blue2","mcwbiomesoplenty:fir_window2","dyenamics:bed/wine_bed_frm_white_bed","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","minecraft:black_bed","sfm:manager","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","comforts:sleeping_bag_to_red","allthemodium:smithing/allthemodium_sword_smithing","connectedglass:tinted_borderless_glass_cyan2","botania:livingwood_twig","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","ae2:tools/network_tool","minecraft:bowl","deeperdarker:bloom_boat","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwpaths:blackstone_diamond_paving","pneumaticcraft:vortex_cannon","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","securitycraft:reinforced_jungle_fence","ae2:network/cables/dense_covered_red","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwroofs:light_gray_upper_steep_roof","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","allthecompressed:compress/copper_block_1x","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","allthemodium:ancient_stone_stairs","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","minecraft:red_carpet","mcwroofs:dark_prismarine_attic_roof","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","allthemodium:smithing/vibranium_boots_smithing","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","reliquary:mob_charm_fragments/zombified_piglin","create:crafting/materials/red_sand_paper","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwdoors:bamboo_waffle_door","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","allthearcanistgear:vibranium_hat_smithing","mcwroofs:oak_planks_attic_roof","bigreactors:crafting/graphite_component_to_storage","rftoolsutility:moduleplus_template","dyenamics:amber_terracotta","evilcraft:crafting/blood_infusion_core","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","sophisticatedstorage:basic_to_copper_tier_upgrade","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","ae2:tools/misctools_entropy_manipulator","aether:netherite_pickaxe_repairing","pneumaticcraft:manual_compressor","botania:elementium_hoe","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","ae2:decorative/sky_stone_brick_from_stonecutting","securitycraft:secret_acacia_sign_item","comforts:sleeping_bag_black","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","allthemodium:ancient_stone_wall","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","ae2:network/crafting/patterns_blank","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","handcrafted:jungle_desk","utilitarian:snad/drit","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","minecraft:cooked_cod","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","ad_astra:steel_trapdoor","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:granite_top_roof","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","securitycraft:alarm","pneumaticcraft:manometer","allthemodium:vibranium_ingot_from_dust_blasting","ad_astra:launch_pad","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","ae2:tools/fluix_shovel","reliquary:magicbane","pneumaticcraft:wall_lamp_inverted_magenta","modularrouters:sender_module_1_alt","botania:elementium_leggings","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","mcwpaths:dark_prismarine_crystal_floor_slab","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","comforts:sleeping_bag_red","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","ad_astra:black_flag","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","botania:petal_light_gray","mysticalagriculture:essence/minecraft/netherite_ingot","mcwroofs:gray_steep_roof","rftoolspower:endergenic","create:copper_shingles_from_ingots_copper_stonecutting","reliquary:mob_charm_belt","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","alltheores:iron_ore_hammer","allthemodium:raw_unobtainium_block","integrateddynamics:crafting/drying_basin","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","dankstorage:dank_3","ae2:network/cables/glass_black","mcwroofs:yellow_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","delightful:food/cooking/glow_jam_jar","everythingcopper:copper_ladder","botania:polished_livingrock","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","rftoolsbuilder:shape_card_pump_dirt","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","allthemodium:smithing/vibranium_sword_smithing","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","mysticalagriculture:essence/appliedenergistics2/certus_quartz","simplylight:illuminant_orange_block_dyed","minecraft:polished_blackstone_stairs","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","travelersbackpack:sandstone","allthemodium:ancient_cracked_stone_brick_stairs","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","mcwpaths:granite_crystal_floor_path","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","create:cut_granite_slab_from_stone_types_granite_stonecutting","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","ae2:network/crafting/4k_cpu_crafting_storage","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:red_cushion","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","pneumaticcraft:air_grate_module","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","minecraft:dye_red_carpet","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","mcwbridges:balustrade_end_stone_bricks_bridge","minecraft:quartz_bricks","botania:temperance_stone","undergarden:polished_depthrock_slab_stonecutting","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","mcwbridges:balustrade_bricks_bridge","mcwbridges:prismarine_bricks_bridge_stair","supplementaries:candle_holders/candle_holder_blue_dye","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","undergarden:polished_depthrock_wall_stonecutting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","cfm:dye_red_picket_gate","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","botania:elementium_helmet","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","ae2:network/cables/dense_smart_fluix","farmersdelight:iron_nugget_from_blasting_knife","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","ae2:network/blocks/pattern_providers_interface_part","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","expatternprovider:assembler_matrix_pattern","mcwbridges:bamboo_bridge","mcwfences:modern_andesite_wall","mcwpaths:granite_crystal_floor","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","ae2:network/cells/view_cell","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","rftoolsutility:matter_receiver","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwroofs:deepslate_lower_roof","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","ae2wtlib:wireless_universal_terminal/upgrade_pattern_encoding","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","forbidden_arcanus:dark_rune","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","undergarden:polished_depthrock_stairs_stonecutting","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwpaths:dark_prismarine_running_bond_path","mcwtrpdoors:bamboo_beach_trapdoor","minecolonies:apple_pie","allthecompressed:compress/cobblestone_1x","twigs:mossy_bricks_from_moss_block","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","minecraft:andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","industrialforegoing:plastic","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","eccentrictome:tome","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","ad_astra:zip_gun","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","rftoolsutility:matter_transmitter","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","minecraft:gray_carpet","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","travelersbackpack:magma_cube","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwbridges:prismarine_bricks_bridge_pier","mcwfences:red_sandstone_grass_topped_wall","undergarden:depthrock_tiles_stonecutting","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","farmersdelight:cooking/glow_berry_custard","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","mcwroofs:orange_concrete_steep_roof","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwwindows:warped_planks_window","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","evilcraft:crafting/dark_spike","extradisks:advanced_storage_housing","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","ae2:network/wireless_crafting_terminal","create:crafting/kinetics/orange_seat","rftoolsutility:syringe","minecraft:diamond_block","mcwbridges:granite_bridge","mekanism:configurator","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","rftoolsutility:spawner","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","bigreactors:blasting/graphite_from_coal","mcwpaths:dark_prismarine_windmill_weave_stairs","botania:astrolabe","sophisticatedbackpacks:stack_upgrade_tier_1","mcwwindows:dark_prismarine_four_window","sophisticatedstorage:iron_to_gold_tier_upgrade","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","sophisticatedstorage:backpack_stack_upgrade_tier_1_from_storage_stack_upgrade_tier_2","minecolonies:raw_noodle","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","minecraft:cooked_mutton","farmersdelight:wheat_dough_from_eggs","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","connectedglass:tinted_borderless_glass_red2","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","ae2:tools/certus_quartz_cutting_knife","rftoolsstorage:modular_storage","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","cfm:dye_green_picket_gate","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","rftoolsbuilder:shape_card_pump","mcwfurnitures:stripped_jungle_covered_desk","ae2:shaped/stairs/smooth_sky_stone_block","occultism:smelting/burnt_otherstone","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mekanism:electrolytic_separator","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","ae2:network/cables/smart_green","ae2:network/cells/item_storage_cell_64k","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwwindows:stone_brick_gothic","botania:elementium_pickaxe","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","supplementaries:soap/piston","minecraft:polished_granite","mcwbiomesoplenty:magic_plank_window2","undergarden:depthrock_wall","mcwroofs:pink_terracotta_upper_lower_roof","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","minecraft:soul_campfire","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","undergarden:depthrock_wall_stonecutting","botania:speed_up_belt","domum_ornamentum:green_cactus_extra","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","twilightdelight:obsidian","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","dyenamics:bed/amber_bed_frm_white_bed","simplylight:illuminant_block_dyed","ae2:tools/paintballs_magenta","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","twigs:cracked_bricks","supplementaries:slice_map","appflux:insulating_resin","mcwbridges:brick_bridge_pier","connectedglass:borderless_glass_green_pane2","alltheores:aluminum_dust_from_hammer_ingot_crushing","travelersbackpack:fox_smithing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mekanism:control_circuit/advanced","mekanism:upgrade/speed","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","blue_skies:trough","botania:lens_normal","mcwfences:end_brick_pillar_wall","dyenamics:ultramarine_dye","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","undergarden:boomgourd","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","delightful:knives/elementium_knife","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","minecraft:purple_dye","mcwfurnitures:jungle_modern_chair","ae2:network/cells/item_storage_cell_4k_storage","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","handcrafted:phantom_trophy","mcwroofs:cyan_terracotta_upper_steep_roof","minecraft:quartz_slab","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","mcwroofs:stone_bricks_roof","cfm:cyan_grill","thermal_extra:smelting/twinite_ingot_from_dust_smelting","simplylight:illuminant_purple_block_dyed","ae2:block_cutter/stairs/smooth_sky_stone_stairs","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","minecraft:quartz_pillar_from_quartz_block_stonecutting","mcwfurnitures:stripped_jungle_double_drawer","ae2:network/cables/smart_black","botania:conversions/yellow_petal_block_deconstruct","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","mcwpaths:dark_prismarine_strewn_rocky_path","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","undergarden:stonecutter_from_depthrock","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","railcraft:switch_track_lever","mcwpaths:andesite_honeycomb_paving","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","handcrafted:blue_bowl","mcwdoors:bamboo_stable_head_door","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","cfm:white_picket_fence","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","handcrafted:kitchen_hood","appbot:mana_storage_cell_4k","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","rftoolsbuilder:shape_card_liquid","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","reliquary:ender_staff","minecraft:gray_dye","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","travelersbackpack:netherite_tier_upgrade","securitycraft:secret_birch_hanging_sign","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","botania:travel_belt","allthemodium:unobtainium_ingot_from_raw_blasting","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","ae2:materials/advancedcard","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwroofs:andesite_lower_roof","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","mysticalagriculture:prosperity_seed_base","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:iron_sword","botania:elementium_chestplate","mcwpaths:sandstone_clover_paving","aquaculture:sushi","ae2:network/blocks/interfaces_interface_alt","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","minecraft:dark_prismarine_slab","botania:runic_altar","additionallanterns:blackstone_chain","domum_ornamentum:purple_brick_extra","minecraft:green_dye","mcwwindows:one_way_glass_pane","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","minecraft:chiseled_sandstone_from_sandstone_stonecutting","ae2:tools/fluix_upgrade_smithing_template","comforts:hammock_red","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","pneumaticcraft:logistics_frame_active_provider","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","appbot:mana_storage_cell_1k","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","everythingcopper:copper_anvil","minecraft:prismarine_brick_stairs_from_prismarine_stonecutting","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","supplementaries:candle_holders/candle_holder_green_dye","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","undergarden:coarse_deepsoil","handcrafted:jungle_table","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","botania:abstruse_platform","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","minecraft:conduit","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","mcwwindows:diorite_pane_window","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","mcwpaths:granite_crystal_floor_slab","supplementaries:flags/flag_red","cfm:dye_blue_picket_fence","enderio:resetting_lever_ten_from_prev","ae2:blasting/sky_stone_block","mcwbridges:end_stone_bricks_bridge","mcwroofs:magenta_concrete_upper_lower_roof","botania:lens_flare","reliquary:crimson_cloth","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","forbidden_arcanus:clibano_combustion/diamond_from_clibano_combusting","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","pneumaticcraft:gilded_upgrade","handcrafted:black_cushion","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","minecraft:gray_terracotta","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","mcwdoors:bamboo_mystic_door","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","mcwroofs:prismarine_brick_lower_roof","dyenamics:peach_terracotta","botania:livingrock_wall","minecraft:fishing_rod","xnet:connector_yellow_dye","rftoolspower:powercell_card","minecraft:terracotta","reliquary:alkahestry_altar","mcwwindows:acacia_blinds","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","botania:blue_pavement","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","minecraft:granite_wall_from_granite_stonecutting","supplementaries:timber_frame","aether:diamond_pickaxe_repairing","pneumaticcraft:seismic_sensor","comforts:sleeping_bag_gray","twigs:copper_pillar_stonecutting","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mekanism:control_circuit/ultimate","mcwroofs:brown_terracotta_roof","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","ae2:network/cells/fluid_storage_cell_4k_storage","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","minecraft:beacon","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","aquaculture:nether_star_hook","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","minecraft:black_dye","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","create:cut_granite_wall_from_stone_types_granite_stonecutting","mekanism:dynamic_tank","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","allthemodium:vibranium_dust_from_ore_crushing","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","mekanism:steel_casing","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","minecolonies:fish_n_chips","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mysticalagriculture:essence/appliedenergistics2/sky_stone","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","ae2:network/wireless_access_point","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","ae2:shaped/walls/smooth_sky_stone_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","rftoolsbase:tablet","ae2:network/crafting/256k_cpu_crafting_storage","mysticalagriculture:essence/appliedenergistics2/fluix_dust","mcwroofs:magenta_terracotta_lower_roof","railcraft:routing_table_book","ad_astra:vent","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mysticalagriculture:essence/minecraft/quartz","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","handcrafted:terracotta_plate","supplementaries:item_shelf","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","rftoolsbuilder:space_chamber","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","comforts:hammock_black","silentgear:coating_smithing_template","handcrafted:jungle_shelf","mcwpaths:dark_prismarine_basket_weave_paving","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","minecraft:granite_slab_from_granite_stonecutting","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","create:polished_cut_granite_slab_from_stone_types_granite_stonecutting","ae2:network/crafting/cpu_crafting_unit","mcwbiomesoplenty:pine_pane_window","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","connectedglass:borderless_glass_magenta2","connectedglass:borderless_glass_gray2","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","ae2:network/blocks/interfaces_interface_part","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwpaths:granite_running_bond","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","ae2:materials/annihilationcore","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","minecraft:leather_boots","railcraft:tank_detector","railcraft:steel_spike_maul","create:crafting/appliances/netherite_diving_boots_from_netherite","handcrafted:sandstone_corner_trim","pneumaticcraft:classify_filter","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwlights:pink_paper_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","rftoolscontrol:workbench","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:dye_black_carpet","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","mysticalagriculture:essence/minecraft/glowstone_dust","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","occultism:crafting/dictionary_of_spirits","cfm:oak_table","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwpaths:dark_prismarine_flagstone_slab","minecraft:magma_block","minecraft:polished_granite_stairs_from_granite_stonecutting","mcwwindows:crimson_stem_parapet","undergarden:depthrock_pebble_stonecutting","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","domum_ornamentum:magenta_floating_carpet","connectedglass:clear_glass_black_pane2","create:small_granite_brick_stairs_from_stone_types_granite_stonecutting","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","littlelogistics:transmitter_component","aether:book_of_lore","minecraft:brush","securitycraft:secret_spruce_hanging_sign","allthemodium:smithing/unobtainium_helmet_smithing","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","sfm:cable","minecraft:hay_block","minecolonies:soy_milk","silentgear:raw_crimson_iron_from_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","botania:phantom_ink","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","bambooeverything:bamboo_ladder","mcwroofs:blue_concrete_top_roof","evilcraft:smelting/hardened_blood_shard","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","botania:petal_light_gray_double","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","railcraft:goggles","twigs:paper_lantern","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","modularrouters:activator_module","domum_ornamentum:brick_extra","travelersbackpack:iron","modularrouters:augment_core","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwtrpdoors:bamboo_barrel_trapdoor","mcwbiomesoplenty:empyreal_window2","minecraft:prismarine_brick_stairs","connectedglass:clear_glass_red_pane2","mcwpaths:stone_flagstone_slab","ae2:decorative/sky_stone_small_brick_from_stonecutting","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","reliquary:uncrafting/redstone","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwwindows:granite_louvered_shutter","ad_astra:red_flag","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","ad_astra:small_red_industrial_lamp","mcwwindows:warped_stem_four_window","securitycraft:sentry","botania:tornado_rod","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","alltheores:uranium_rod","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","minecolonies:tofu","mcwwindows:blue_mosaic_glass_pane","minecolonies:meat_ravioli","travelersbackpack:wither","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","forbidden_arcanus:rune_block_from_rune","mcwbiomesoplenty:maple_wired_fence","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","minecraft:calibrated_sculk_sensor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","create:cut_granite_brick_stairs_from_stone_types_granite_stonecutting","railcraft:bronze_gear","securitycraft:secret_warped_hanging_sign","ae2:tools/certus_quartz_spade","mcwroofs:gray_upper_lower_roof","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","ae2:network/cables/dense_covered_black","undergarden:depthrock_stairs_stonecutting","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","expatternprovider:epp_alt","occultism:crafting/spirit_campfire","croptopia:roasted_smoking","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","minecraft:white_stained_glass","securitycraft:secret_warped_sign_item","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","alltheores:aluminum_dust_from_hammer_crushing","securitycraft:taser","allthemodium:vibranium_nugget_from_ingot","ae2:network/cells/fluid_storage_cell_64k","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","ae2:tools/paintballs_pink","domum_ornamentum:white_brick_extra","aiotbotania:livingrock_hoe","connectedglass:scratched_glass_red_pane2","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","supplementaries:gold_door","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","sfm:labelgun","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","reliquary:angelic_feather","ae2:network/parts/toggle_bus","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","connectedglass:scratched_glass_blue2","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","botania:virus_nullodermal","mysticalagriculture:essence/minecraft/redstone","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","gtceu:smelting/smelt_dust_bronze_to_ingot","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","botania:manasteel_shovel","handcrafted:oven","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","minecraft:red_stained_glass","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","thermal_extra:smelting/soul_infused_ingot_from_dust_smelting","mcwpaths:cobbled_deepslate_crystal_floor_slab","silentgear:leather_from_scraps","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","rftoolsbuilder:shape_card_quarry_fortune","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","connectedglass:borderless_glass_blue_pane2","delightful:knives/bronze_knife","mcwpaths:blackstone_crystal_floor","travelersbackpack:quartz","mekanismgenerators:generator/bio","botania:yellow_petal_block","mcwfurnitures:cabinet_door","sophisticatedbackpacks:tool_swapper_upgrade","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","botania:super_travel_belt","minecraft:lime_terracotta","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","securitycraft:display_case","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","allthecompressed:compress/andesite_1x","mcwwindows:diorite_four_window","minecraft:stone_slab","pneumaticcraft:compressed_bricks","cfm:gray_sofa","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","mekanism:tier_installer/basic","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","allthemodium:vibranium_ingot_from_block","minecraft:jungle_stairs","travelersbackpack:backpack_tank","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","ae2:materials/cardenergy","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","dyenamics:bed/mint_bed_frm_white_bed","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","ae2:materials/carddistribution","ae2:network/parts/import_bus","croptopia:campfire_molasses","minecraft:dye_red_bed","ae2:network/crafting/16k_cpu_crafting_storage","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","ae2:network/upgrade_wireless_crafting_terminal","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","minecraft:blue_stained_glass_pane_from_glass_pane","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","railcraft:water_tank_siding","expatternprovider:assembler_matrix_crafter","pneumaticcraft:wall_lamp_inverted_light_gray","mcwtrpdoors:bamboo_bark_trapdoor","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","securitycraft:secret_cherry_hanging_sign","croptopia:melon_juice","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","ae2:network/cables/smart_cyan","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","rftoolsbuilder:space_chamber_controller","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","aiotbotania:elementium_aiot","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","mcwwindows:one_way_glass","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mekanism:electrolytic_core","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","botania:starfield","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","mekanism:tier_installer/advanced","cfm:mangrove_mail_box","mekanism:upgrade/energy","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","allthemodium:smithing/allthemodium_chestplate_smithing","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","ae2additions:components/super/16k","connectedglass:borderless_glass_orange2","botania:yellow_pavement","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","connectedglass:scratched_glass_black_pane2","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","create:crafting/kinetics/yellow_seat","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","pneumaticcraft:logistics_core","mcwpaths:andesite_windmill_weave_path","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","mcwdoors:bamboo_whispering_door","minecraft:mangrove_boat","minecraft:bread","mcwroofs:green_terracotta_steep_roof","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","minecolonies:eggdrop_soup","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","botania:water_rod","securitycraft:secret_cherry_sign_item","botania:horn_grass","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","allthemodium:allthemodium_ingot_from_block","mcwfences:iron_cheval_de_frise","handcrafted:red_sheet","minecraft:black_banner","tombstone:dark_marble","minecraft:iron_shovel","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","ironfurnaces:furnaces/diamond_furnace","biomesoplenty:willow_boat","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","ae2:smelting/smooth_sky_stone_block","mcwwindows:stone_brick_arrow_slit","chimes:amethyst_chimes","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","ad_astra:space_suit","securitycraft:disguise_module","mcwwindows:deepslate_four_window","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","mcwwindows:dark_oak_louvered_shutter","mcwpaths:granite_strewn_rocky_path","cfm:light_blue_picket_fence","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mythicbotany:ice_ring","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","rftoolsutility:counter_module","aether:netherite_boots_repairing","botania:lens_redirect","cfm:stripped_jungle_crate","create:granite_pillar_from_stone_types_granite_stonecutting","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","minecraft:oak_door","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwroofs:granite_steep_roof","mcwdoors:bamboo_barn_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwdoors:jungle_waffle_door","mcwroofs:black_roof_block","mcwbiomesoplenty:hellbark_pane_window","botania:elementium_shovel","advancedperipherals:peripheral_casing","connectedglass:clear_glass_cyan_pane2","mythicbotany:alfsteel_template","mythicbotany:fire_ring","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwbiomesoplenty:stripped_hellbark_log_four_window","allthemodium:smithing/unobtainium_boots_smithing","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","modularrouters:dropper_module","mcwbiomesoplenty:magic_highley_gate","minecraft:dark_prismarine","ae2:tools/fluix_sword","botania:balance_cloak","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","dyenamics:mint_terracotta","mcwwindows:pink_curtain","rftoolspower:pearl_injector","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","delightful:knives/refined_obsidian_knife","expatternprovider:ei_alt","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","mcwdoors:bamboo_swamp_door","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","deepresonance:radiation_suit_boots","minecraft:bow","supplementaries:wrench","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","undergarden:depthrock_stairs","securitycraft:reinforced_oak_fence_gate","botania:mushroom_stew","minecraft:andesite_stairs","botania:super_lava_pendant","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwdoors:bamboo_japanese_door","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","mcwroofs:prismarine_brick_roof","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","allthecompressed:compress/unobtainium_block_1x","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","connectedglass:borderless_glass_brown2","dyenamics:bed/aquamarine_bed_frm_white_bed","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","sophisticatedbackpacks:feeding_upgrade","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:melon_seeds","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","cfm:dye_cyan_picket_fence","minecraft:firework_rocket_simple","minecraft:black_dye_from_wither_rose","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwroofs:andesite_roof","botania:black_pavement","rftoolsbase:infused_enderpearl","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","functionalstorage:collector_upgrade","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","ae2:network/cables/glass_yellow","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","dyenamics:rose_terracotta","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","alltheores:signalum_plate","mcwdoors:jungle_tropical_door","minecraft:stone","twigs:lamp","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","minecraft:blackstone_stairs","cfm:lime_kitchen_counter","travelersbackpack:red_sleeping_bag","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","cfm:stripped_oak_kitchen_sink_dark","minecraft:dye_cyan_bed","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","crafting_on_a_stick:crafting_table","mcwdoors:oak_barn_door","botania:alchemy_catalyst","allthemodium:ancient_stone_bricks","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","ae2:tools/paintballs_brown","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","undergarden:depthrock_pressure_plate","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","mcwpaths:sandstone_windmill_weave_slab","undergarden:depthrock_slab","minecraft:smooth_stone","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","botania:conversions/elementium_block_deconstruct","rftoolsutility:environmental_controller","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","aether:golden_ring","minecraft:polished_granite_slab_from_granite_stonecutting","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","xnet:controller","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","reliquary:uncrafting/ender_pearl","additionallanterns:obsidian_chain","minecraft:ender_eye","supplementaries:blackstone_lamp","mcwpaths:brick_strewn_rocky_path","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","mcwlights:cross_lantern","mcwwindows:bricks_four_window","railcraft:receiver_circuit","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_packed_mud","minecraft:green_candle","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","rftoolsbase:manual","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","allthecompressed:compress/granite_1x","mekanism:pressurized_reaction_chamber","ae2:network/parts/terminals_pattern_access","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","ae2:block_cutter/walls/quartz_wall","ae2:network/blocks/io_condenser","mahoutsukai:powdered_iron","sophisticatedbackpacks:smoking_upgrade","railcraft:steel_tank_wall","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","minecraft:dye_black_wool","securitycraft:trophy_system","botania:elementium_axe","rftoolsbuilder:shape_card_quarry_silk","ad_astra:small_green_industrial_lamp","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","mcwbiomesoplenty:pine_curved_gate","undergarden:depthrock_tile_stairs_stonecutting","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","advanced_ae:quantumstorage128","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","allthemodium:raw_vibranium_block","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","botania:drum_gathering","mcwpaths:dark_prismarine_windmill_weave_path","mcwwindows:jungle_plank_window2","ad_astra:space_pants","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","ae2:network/cables/dense_smart_black","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","mysticalagriculture:supremium_essence_uncraft","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","additionallanterns:dark_prismarine_chain","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","enderio:resetting_lever_five_inv","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","utilitix:armed_stand","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","ae2:network/cables/smart_blue","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","botania:turntable","ae2:network/cells/item_storage_components_cell_1k_part","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","supplementaries:daub","alltheores:zinc_dust_from_hammer_crushing","railcraft:steel_chestplate","minecraft:netherite_scrap","mcwfurnitures:stripped_jungle_end_table","securitycraft:secret_oak_hanging_sign","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","ae2:decorative/quartz_block","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","ae2:block_cutter/slabs/smooth_sky_stone_slab","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","botania:mana_tablet","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deeperdarker:reinforced_echo_shard","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","ad_astra:small_blue_industrial_lamp","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","mcwpaths:granite_dumble_paving","mcwpaths:granite_flagstone_slab","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","pneumaticcraft:logistics_configurator","rftoolsutility:dialing_device","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","create:cut_granite_brick_slab_from_stone_types_granite_stonecutting","mcwroofs:granite_upper_steep_roof","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:7017,warning_level:0}}