{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"attributeslib:prot_pierce"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.5d,Name:"attributeslib:crit_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;*********,-1536278497,-1945827711,-1989455215]},{FromDim:"minecraft:overworld",FromPos:108301899784259L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;*********,**********,-**********,-*********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","9d474303-fe2f-4381-a7d7-89b0756e98c8","70e61f07-a350-437a-ae01-15e3ef9a1bab"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:11509393L,tiredTime:11509438L,wakeTime:11509423L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:chainmail_gloves",tag:{Damage:11}}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[{Count:1b,Slot:0,id:"irons_spellbooks:fireward_ring"}],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:145,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:61178},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:oak_button",ItemStack:{Count:1b,id:"minecraft:oak_button"}}],SelectedRecipe:"minecraft:oak_button"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:23,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["minecolonies:mint","minecraft:bread","minecraft:baked_potato"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;*********,-1536278497,-1945827711,-1989455215]},{FromDim:"minecraft:overworld",FromPos:108301899784259L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;*********,**********,-**********,-*********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","9d474303-fe2f-4381-a7d7-89b0756e98c8","70e61f07-a350-437a-ae01-15e3ef9a1bab"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],twilightforest_banished:1b},"quark:locked_once":1b,"quark:trying_crawl":0b,simplylight_unlocked:2},Health:24.0f,HurtByTimestamp:37535,HurtTime:0s,Inventory:[{Count:19b,Slot:0b,id:"railcraft:sulfur_dust"},{Count:1b,Slot:1b,id:"minecraft:stone_pickaxe",tag:{Damage:55}},{Count:13b,Slot:2b,id:"gtceu:sticky_resin"},{Count:21b,Slot:3b,id:"minecraft:raw_copper"},{Count:3b,Slot:4b,id:"minecraft:jungle_sapling"},{Count:1b,Slot:5b,id:"crafting_on_a_stick:crafting_table"},{Count:1b,Slot:6b,id:"minecraft:oak_sapling"},{Count:1b,Slot:8b,id:"silentgear:pickaxe",tag:{Damage:81,SGear_Data:{Construction:{Parts:[{ID:"silentgear:pickaxe_head",Item:{Count:1b,id:"silentgear:pickaxe_head",tag:{Damage:0,Materials:[{ID:"silentgear:iron"}]}}},{ID:"silentgear:rod",Item:{Count:1b,id:"silentgear:rod",tag:{Materials:[{ID:"silentgear:wood"}]}}}]},Properties:{HarvestTier:"minecraft:iron",LockStats:0b,ModVersion:"3.6.6",Stats:{"silentgear:attack_reach":3.0f,"silentgear:attack_speed":-2.8f,"silentgear:charging_value":0.7f,"silentgear:durability":250.0f,"silentgear:enchantment_value":14.0f,"silentgear:harvest_speed":6.0f,"silentgear:magic_damage":1.0f,"silentgear:melee_damage":3.0f,"silentgear:rarity":20.0f,"silentgear:repair_efficiency":1.0f},Traits:[{Level:3b,Name:"silentgear:malleable"},{Level:1b,Name:"silentgear:magnetic"},{Level:2b,Name:"silentgear:flexible"}]},Rendering:{Model:3,ModelKey:"pickaxe:pickaxe_head{iron},rod{wood},"}},SGear_UUID:[I;1812937517,1112163831,-1393588685,-1224139774]}},{Count:2b,Slot:9b,id:"minecraft:chest"},{Count:8b,Slot:10b,id:"minecraft:iron_ingot"},{Count:1b,Slot:11b,id:"minecraft:potion",tag:{Potion:"minecraft:luck"}},{Count:5b,Slot:12b,id:"minecraft:cooked_chicken"},{Count:1b,Slot:13b,id:"minecraft:bucket"},{Count:6b,Slot:14b,id:"alltheores:steel_ingot"},{Count:4b,Slot:15b,id:"minecraft:melon_seeds"},{Count:1b,Slot:16b,id:"patchouli:guide_book",tag:{"patchouli:book":"ad_astra:astrodux"}},{Count:15b,Slot:17b,id:"minecraft:coal"},{Count:1b,Slot:18b,id:"minecraft:cobblestone"},{Count:1b,Slot:19b,id:"minecraft:apple"},{Count:4b,Slot:20b,id:"twigs:twig"},{Count:1b,Slot:21b,id:"immersiveengineering:seed"},{Count:1b,Slot:22b,id:"minecraft:dirt"},{Count:10b,Slot:25b,id:"minecraft:oak_planks"},{Count:34b,Slot:31b,id:"alltheores:iron_dust"},{Count:8b,Slot:34b,id:"minecraft:cobbled_deepslate"},{Count:1b,Slot:101b,id:"minecraft:iron_leggings",tag:{Damage:28,affix_data:{affixes:{"apotheosis:armor/attribute/ironforged":0.91983974f},name:'{"color":"#808080","translate":"misc.apotheosis.affix_name.two","with":[{"translate":"affix.apotheosis:armor/attribute/ironforged"},"",""]}',rarity:"apotheosis:common",uuids:[[I;951810640,-1785248472,-1226696792,-1506455276]]},apoth_rchest:1b}},{Count:1b,Slot:102b,id:"minecraft:chainmail_chestplate",tag:{Damage:25,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.28710568f,"apotheosis:armor/dmg_reduction/blockading":0.5720847f,"apotheosis:armor/dmg_reduction/dwarven":0.20756513f,"apotheosis:durable":0.13f,"irons_spellbooks:armor/attribute/cooldown":0.8644289f,"irons_spellbooks:armor/attribute/spell_resist":0.92645544f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist"},"",{"translate":"affix.apotheosis:armor/attribute/blessed.suffix"}]}',rarity:"apotheosis:rare",sockets:2,uuids:[[I;-60292517,933316670,-1275747799,-647435076]]},apoth_rchest:1b}},{Count:1b,Slot:103b,id:"minecraft:iron_helmet",tag:{Damage:26,affix_data:{affixes:{"apotheosis:armor/attribute/stalwart":0.14640558f,"apotheosis:armor/dmg_reduction/runed":0.6454965f,"apotheosis:durable":0.07f,"irons_spellbooks:armor/attribute/cooldown":0.53369707f,"irons_spellbooks:armor/attribute/mana":0.3291387f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",{"translate":"affix.irons_spellbooks:armor/attribute/cooldown.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;-129930195,233390820,-1664290225,1313487713]]},apoth_rchest:1b}},{Count:14b,Slot:-106b,id:"minecraft:torch"}],Invulnerable:0b,Motion:[0.01803226939984047d,-0.0784000015258789d,0.002458996349444916d],OnGround:0b,PortalCooldown:0,Pos:[-539.8090104401906d,62.07326432466506d,410.6694883838968d],Railways_DataVersion:2,RootVehicle:{Attach:[I;-916703875,-1110423313,-1284566070,1816044767],Entity:{Air:300s,BalmData:{},Bukkit.updateLevel:2,CanUpdate:1b,FallDistance:0.0f,Fire:0s,ForgeCaps:{"blueflame:blue_flame_on":{isOnFire:0b},"pneumaticcraft:hacking":{},"structure_gel:gel_entity":{portal:"structure_gel:empty"}},ForgeData:{},Invulnerable:0b,Motion:[0.0d,0.0d,0.0d],OnGround:0b,PortalCooldown:0,Pos:[-539.8090104401906d,62.52326431274413d,410.6694883838968d],Rotation:[-37.234085f,5.8498683f],Spigot.ticksLived:2362,Type:"oak",UUID:[I;-916703875,-1110423313,-1284566070,1816044767],WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},id:"minecraft:boat"}},Rotation:[-37.234085f,5.8498683f],Score:826,SelectedItemSlot:7,SleepTimer:0s,SpawnAngle:122.09277f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:392,SpawnY:67,SpawnZ:1084,Spigot.ticksLived:61178,UUID:[I;-1386109166,1126780181,-1254012010,596822890],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-114624085135298L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:23,XpP:0.9740261f,XpSeed:0,XpTotal:826,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752912705892L,keepLevel:0b,lastKnownName:"isanKash",lastPlayed:1752917933183L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:3.5175838f,foodLevel:20,foodSaturationLevel:9.6f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","immersiveengineering:crafting/gunpowder_from_dusts","handcrafted:spider_trophy","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwtrpdoors:jungle_cottage_trapdoor","croptopia:shaped_beef_stew","cfm:red_kitchen_sink","minecolonies:baked_salmon","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","xnet:connector_green","mcwpaths:jungle_planks_path","mcwbiomesoplenty:palm_plank_window","minecolonies:cheese_ravioli","mcwbiomesoplenty:dead_window2","create:oak_window","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","minecraft:cooked_salmon_from_campfire_cooking","aether:chainmail_leggings_repairing","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","mcwwindows:oak_plank_pane_window","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","railcraft:controller_circuit","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","botania:manasteel_leggings","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","minecraft:cooked_salmon_from_smoking","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","xnet:connector_red_dye","minecolonies:shapetool","supplementaries:bubble_blower","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","supplementaries:crank","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","securitycraft:reinforced_andesite_with_vanilla_diorite","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:bamboo_chest_raft","dyenamics:bed/bubblegum_bed_frm_white_bed","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","buildinggadgets2:template_manager","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","immersiveengineering:crafting/rockcutter","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","aether:blue_ice_freezing","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","cfm:crimson_kitchen_sink_dark","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","botania:obedience_stick","travelersbackpack:diamond_tier_upgrade","mcwwindows:bricks_pane_window","botania:bauble_box","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","cfm:oak_kitchen_counter","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","alltheores:platinum_dust_from_hammer_crushing","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","botania:mana_fluxfield","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","biomesoplenty:palm_chest_boat","mcwpaths:cobbled_deepslate_windmill_weave_path","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","handcrafted:oak_bench","mcwfurnitures:oak_striped_chair","farmersdelight:cooking/pasta_with_meatballs","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwwindows:birch_plank_window","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","mcwbiomesoplenty:stripped_dead_log_window2","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","cfm:jungle_cabinet","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","cfm:purple_cooler","minecolonies:pasta_tomato","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","croptopia:hamburger","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","alltheores:nickel_dust_from_hammer_crushing","twilightforest:time_chest_boat","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:pine_plank_window2","minecraft:jungle_door","xnet:connector_routing","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","aether:chainmail_gloves_repairing","mcwbiomesoplenty:palm_plank_window2","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","farmersdelight:barbecue_stick","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","mcwwindows:oak_four_window","cfm:oak_coffee_table","farmersdelight:stuffed_potato","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","railcraft:world_spike","mcwdoors:garage_gray_door","botania:petal_green","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","dyenamics:bed/fluorescent_bed_frm_white_bed","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","littlelogistics:seater_car","mcwwindows:granite_pane_window","mcwbiomesoplenty:stripped_dead_pane_window","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","cfm:green_grill","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","minecraft:oak_pressure_plate","cfm:orange_grill","immersiveengineering:crafting/steel_scaffolding_standard","cfm:brown_grill","mcwwindows:quartz_window","mcwwindows:dark_oak_shutter","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:mangrove_chest_boat","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","cfm:gray_kitchen_drawer","mcwtrpdoors:oak_barred_trapdoor","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","minecolonies:mint_tea","xnet:netcable_routing","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","botania:twig_wand","twilightforest:transformation_chest_boat","twigs:silt","additionallanterns:end_stone_lantern","gtceu:shaped/compress_platinum_to_ore_block","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","cfm:mangrove_kitchen_sink_dark","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","mcwdoors:oak_four_panel_door","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","botania:magnet_ring","botania:conversions/green_petal_block_deconstruct","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","minecraft:iron_ingot_from_nuggets","minecolonies:pasta_plain","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","cfm:green_kitchen_sink","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwwindows:white_mosaic_glass","minecraft:redstone_torch","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","rftoolsbase:machine_frame","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwfences:railing_andesite_wall","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","biomesoplenty:empyreal_chest_boat","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","create:crafting/materials/andesite_alloy","mcwwindows:andesite_four_window","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","botania:petal_green_double","pneumaticcraft:armor_upgrade","mcwwindows:birch_plank_pane_window","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","croptopia:fruit_salad","securitycraft:block_change_detector","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","croptopia:potato_chips","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","botania:manasteel_axe","mcwwindows:oak_blinds","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","twilightforest:sorting_chest_boat","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","minecolonies:mutton_dinner","aether:skyroot_cartography_table","mcwdoors:oak_cottage_door","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwpaths:red_sand_path_block","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","cfm:light_blue_cooler","railcraft:signal_circuit","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","undergarden:gloom_o_lantern","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:birch_window","mcwwindows:blackstone_four_window","mcwfences:acacia_picket_fence","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","twigs:oak_table","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","twilightforest:twilight_oak_chest_boat","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","mcwroofs:green_concrete_steep_roof","immersiveengineering:crafting/sword_steel","botania:conversions/lime_petal_block_deconstruct","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwfurnitures:oak_wardrobe","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","modularrouters:blank_module","botania:glass_pickaxe","mcwbiomesoplenty:dead_curved_gate","sgjourney:sandstone_with_lapis","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","farmersdelight:cooking/dumplings","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwwindows:jungle_blinds","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwwindows:stripped_dark_oak_log_window2","dyenamics:conifer_stained_glass","immersiveengineering:crafting/toolbox","cfm:light_gray_trampoline","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","alltheores:lumium_dust_from_alloy_blending","alltheores:netherite_dust_from_hammer_crushing","cfm:purple_kitchen_drawer","botania:flower_bag","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","dyenamics:aquamarine_stained_glass","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:oak_sign","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwroofs:gutter_base_magenta","mcwbiomesoplenty:fir_plank_window2","domum_ornamentum:cobblestone_extra","additionallanterns:emerald_lantern","handcrafted:oak_counter","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:red_maple_hedge","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","botania:apothecary_mossy","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","farmersdelight:cooking/hot_cocoa","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:purple_concrete_powder","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","securitycraft:reinforced_jungle_fence","biomesoplenty:maple_chest_boat","cfm:stripped_spruce_kitchen_drawer","xnet:advanced_connector_yellow","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","xnet:netcable_blue","alltheores:copper_dust_from_hammer_ingot_crushing","cfm:stripped_oak_chair","mcwfurnitures:jungle_counter","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","minecraft:cooked_beef_from_campfire_cooking","cfm:light_blue_kitchen_sink","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/axe_steel","botania:dye_green","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","alltheores:lead_dust_from_hammer_crushing","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","railcraft:steel_leggings","mcwroofs:oak_planks_attic_roof","securitycraft:reinforced_pink_stained_glass","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","littlelogistics:vessel_charger","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwlights:soul_dark_oak_tiki_torch","alltheores:iridium_dust_from_hammer_crushing","deepresonance:resonating_plate_block","naturescompass:natures_compass","railcraft:radio_circuit","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","mcwpaths:cobbled_deepslate_clover_paving","mcwbiomesoplenty:mahogany_plank_window2","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","croptopia:apple_sapling","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","cfm:birch_kitchen_sink_dark","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","croptopia:beef_jerky","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","create:crafting/logistics/powered_toggle_latch","supplementaries:flags/flag_lime","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:mangrove_louvered_shutter","minecraft:cooked_cod","supplementaries:flags/flag_magenta","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","minecraft:diamond_axe","securitycraft:reinforced_pink_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_red","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","minecraft:cartography_table","securitycraft:alarm","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","botania:petal_lime_double","dyenamics:banner/bubblegum_banner","mcwroofs:oak_top_roof","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","rftoolsbuilder:vehicle_control_module","additionallanterns:amethyst_lantern","utilitix:crude_furnace","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwwindows:stone_window","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwpaths:cobbled_deepslate_flagstone_path","cfm:stripped_warped_kitchen_drawer","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","mcwroofs:cobblestone_roof","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","sfm:water_tank","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","alchemistry:compactor","cfm:jungle_upgraded_fence","minecraft:dropper","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","botania:manasteel_chestplate","botania:magenta_shiny_flower","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","mcwdoors:oak_japanese2_door","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","minecraft:torch","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwwindows:mangrove_window","mcwwindows:quartz_pane_window","cfm:oak_desk","mcwtrpdoors:metal_trapdoor","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","supplementaries:daub_frame","botania:manasteel_sword","pneumaticcraft:minigun_upgrade","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","botania:dreamwood_wand","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","mcwpaths:stone_running_bond_stairs","securitycraft:briefcase","mcwwindows:green_mosaic_glass","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:stripped_oak_large_drawer","railcraft:steel_sword","cfm:white_grill","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","travelersbackpack:hose_nozzle","securitycraft:claymore","handcrafted:jungle_dining_bench","cfm:red_cooler","railcraft:golden_ticket","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","enderio:black_paper","supplementaries:lock_block","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","ae2:network/cables/dense_smart_fluix","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwwindows:stripped_acacia_log_four_window","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","botania:sextant","securitycraft:laser_block","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","botania:manasteel_boots","mcwfences:modern_andesite_wall","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:raw_iron_block","mcwwindows:stone_window2","mcwwindows:metal_four_window","minecraft:oak_button","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","supplementaries:flags/flag_blue","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","mcwroofs:jungle_planks_top_roof","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","xnet:advanced_connector_yellow_dye","minecolonies:apple_pie","railcraft:steel_shears","additionallanterns:diamond_lantern","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","securitycraft:reinforced_lime_stained_glass_pane_from_glass","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","itemcollectors:basic_collector","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","minecraft:oak_fence_gate","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","alltheores:iron_dust_from_hammer_ingot_crushing","pneumaticcraft:item_life_upgrade","rftoolsbuilder:space_chamber_card","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","alltheores:brass_dust_from_alloy_blending","mcwfurnitures:stripped_jungle_table","buildinggadgets2:gadget_copy_paste","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","supplementaries:flags/flag_green","cfm:stripped_jungle_mail_box","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","croptopia:beetroot_salad","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwwindows:acacia_plank_four_window","allthecompressed:compress/oak_planks_1x","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","dyenamics:bed/maroon_bed_frm_white_bed","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","mcwbiomesoplenty:stripped_palm_log_four_window","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","mcwroofs:gutter_base_light_blue","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","minecraft:diamond_block","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:deepslate_roof","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","minecraft:yellow_stained_glass","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","utilitix:experience_crystal","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwwindows:red_sandstone_pane_window","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","cfm:green_cooler","alltheores:steel_rod","handcrafted:goat_trophy","bigreactors:blasting/graphite_from_coal","mcwpaths:cobbled_deepslate_flagstone","paraglider:paraglider","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","minecolonies:raw_noodle","securitycraft:reinforced_white_stained_glass_pane_from_dye","minecraft:cooked_mutton","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","mcwbiomesoplenty:willow_pane_window","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","minecraft:lapis_block","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","mcwbiomesoplenty:redwood_plank_window","minecraft:iron_axe","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","minecraft:jukebox","securitycraft:blacklist_module","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwwindows:spruce_plank_pane_window","sophisticatedstorage:jungle_chest","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","minecraft:jungle_fence","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","occultism:crafting/demons_dream_essence_from_seeds","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","mcwbiomesoplenty:magic_plank_window2","farmersdelight:cooking/pasta_with_mutton_chop","securitycraft:reinforced_light_gray_stained_glass","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwwindows:spruce_four_window","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","mcwwindows:prismarine_pane_window","minecolonies:pottage","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","supplementaries:slice_map","dyenamics:bed/lavender_bed_frm_white_bed","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","handcrafted:oak_pillar_trim","occultism:crafting/butcher_knife","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","delightful:knives/manasteel_knife","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","securitycraft:reinforced_sticky_piston","aether:iron_pendant","botania:lens_normal","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","cfm:gray_grill","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","aether:skyroot_iron_vanilla_shield","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:stripped_redwood_log_window","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","xnet:netcable_green","mcwfurnitures:stripped_jungle_coffee_table","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","railcraft:steel_helmet","farmersdelight:iron_knife","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","handcrafted:jungle_counter","dyenamics:banner/navy_banner","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","minecraft:cooked_cod_from_campfire_cooking","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","mcwdoors:oak_stable_door","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","cfm:oak_upgraded_gate","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","dyenamics:bed/honey_bed_frm_white_bed","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","croptopia:cashew_chicken","mcwpaths:stone_crystal_floor_slab","minecraft:copper_ingot_from_blasting_raw_copper","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","farmersdelight:chicken_sandwich","securitycraft:reinforced_cherry_fence","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbiomesoplenty:redwood_four_window","mcwwindows:birch_log_parapet","mcwbiomesoplenty:magic_plank_window","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","botania:lime_petal_block","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:dead_pane_window","mcwwindows:crimson_blinds","mcwwindows:diorite_window2","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","farmersdelight:cooking/baked_cod_stew","xnet:connector_blue_dye","mcwfurnitures:oak_bookshelf","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","mcwwindows:oak_log_parapet","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","botania:travel_belt","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","mcwpaths:cobbled_deepslate_flagstone_slab","create:crafting/schematics/empty_schematic","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwroofs:deepslate_attic_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwwindows:dark_prismarine_pane_window","aether:iron_helmet_repairing","farmersdelight:steak_and_potatoes","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwfurnitures:jungle_table","mcwbiomesoplenty:umbran_plank_window2","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","rftoolsbuilder:green_shield_template_block","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","securitycraft:reinforced_glass_pane","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwroofs:gutter_base_lime","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","dyenamics:bed/fluorescent_bed","botania:green_petal_block","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwbiomesoplenty:jacaranda_four_window","mcwbridges:iron_bridge_pier","cfm:pink_grill","pneumaticcraft:magnet_upgrade","mcwroofs:gutter_middle_orange","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","minecraft:sticky_piston","mcwwindows:granite_window2","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","croptopia:date_sapling","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","cfm:oak_kitchen_sink_light","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwwindows:diorite_pane_window","mcwbridges:asian_red_bridge_pier","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","minecraft:oak_stairs","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","alltheores:bronze_plate","botania:swap_ring","pneumaticcraft:gilded_upgrade","mcwwindows:jungle_log_parapet","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwwindows:white_mosaic_glass_pane","mcwwindows:purple_mosaic_glass","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwwindows:acacia_blinds","mcwroofs:brown_concrete_attic_roof","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwlights:festive_wall_lantern","mcwbiomesoplenty:pine_plank_pane_window","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","biomesoplenty:umbran_chest_boat","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","sophisticatedstorage:packing_tape","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwlights:soul_oak_tiki_torch","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","deepresonance:radiation_monitor","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","railcraft:steel_gear","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","croptopia:shaped_sticky_toffee_pudding","mcwfurnitures:jungle_drawer_counter","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","delightful:knives/steel_knife","ad_astra:steel_block","xnet:advanced_connector_blue_dye","cfm:spruce_kitchen_sink_light","mcwroofs:cyan_concrete_roof","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:maple_plank_window2","mcwwindows:dark_prismarine_window","mcwwindows:cherry_plank_window","minecolonies:fish_n_chips","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","xnet:advanced_connector_red_dye","immersiveengineering:crafting/gunpart_drum","minecraft:brown_concrete_powder","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","minecraft:gunpowder","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","minecraft:glass_pane","pneumaticcraft:entity_tracker_upgrade","supplementaries:timber_brace","handcrafted:oak_side_table","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","securitycraft:reinforced_brown_stained_glass","cfm:lime_grill","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","biomesoplenty:dead_chest_boat","mcwwindows:acacia_window","cfm:orange_kitchen_drawer","minecraft:cherry_boat","silentgear:stone_rod","dyenamics:icy_blue_stained_glass","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:spruce_mail_box","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwlights:pink_paper_lamp","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","xnet:connector_green_dye","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","minecolonies:supplychestdeployer","aether:iron_gloves","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfurnitures:oak_glass_table","occultism:crafting/dictionary_of_spirits","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","pneumaticcraft:stomp_upgrade","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","mcwwindows:crimson_stem_parapet","supplementaries:lapis_bricks","croptopia:chocolate","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","forbidden_arcanus:lens_of_veritatis","mcwbridges:glass_bridge_pier","xnet:advanced_connector_routing","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","aether:aether_iron_nugget_from_smelting","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","sfm:cable","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","minecraft:cobblestone_slab","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwwindows:red_sandstone_window2","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:umbran_window2","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","alltheores:lead_dust_from_hammer_ingot_crushing","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","supplementaries:pulley","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","allthecompressed:compress/glass_1x","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","mcwlights:crimson_ceiling_fan_light","alltheores:zinc_dust_from_hammer_ingot_crushing","biomesoplenty:dead_boat","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","supplementaries:flags/flag_orange","travelersbackpack:standard","botania:manasteel_pick","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","railcraft:goggles","twigs:paper_lantern","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","botania:petal_lime","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","simplylight:illuminant_brown_block_toggle","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwbiomesoplenty:empyreal_window2","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwwindows:granite_louvered_shutter","botania:dye_lime","sophisticatedstorage:oak_limited_barrel_1","mcwroofs:gray_concrete_roof","pneumaticcraft:range_upgrade","cfm:magenta_cooler","constructionwand:diamond_wand","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","mcwwindows:warped_stem_four_window","securitycraft:sentry","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","immersiveengineering:crafting/blueprint_bullets","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","minecraft:deepslate","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","minecolonies:meat_ravioli","mcwwindows:spruce_plank_window","supplementaries:checker","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","minecraft:crossbow","cfm:cyan_cooler","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:orange_paper_lamp","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","minecraft:slime_block","immersiveengineering:crafting/shovel_steel","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","supplementaries:soap","mcwwindows:iron_shutter","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","immersiveengineering:crafting/blueprint_molds","rftoolsutility:redstone_information","travelersbackpack:emerald","cfm:dark_oak_kitchen_drawer","farmersdelight:cutting_board","mcwbiomesoplenty:pine_plank_window","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","supplementaries:altimeter","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:lime_concrete_roof","allthecompressed:compress/oak_log_1x","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","alltheores:electrum_dust_from_alloy_blending","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","mcwfences:warped_stockade_fence","botania:manasteel_shovel","botania:pump","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","utilitix:filter_rail","cfm:stripped_dark_oak_mail_box","aether:diamond_gloves","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","railcraft:steel_tunnel_bore_head","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:magenta_stained_glass","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwbiomesoplenty:stripped_maple_log_four_window","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","dyenamics:bed/conifer_bed_frm_white_bed","cfm:stripped_jungle_desk_cabinet","aquaculture:jellyfish_to_slimeball","mcwpaths:cobbled_deepslate_strewn_rocky_path","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:diorite_four_window","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwwindows:mangrove_plank_window2","minecraft:jungle_stairs","travelersbackpack:backpack_tank","botania:knockback_belt","mcwwindows:mangrove_pane_window","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","dyenamics:bed/mint_bed_frm_white_bed","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","farmersdelight:flint_knife","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","mcwwindows:stripped_warped_stem_window","supplementaries:candle_holders/candle_holder_white","mcwwindows:stripped_birch_log_four_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","minecraft:cooked_salmon","immersiveengineering:crafting/plate_steel_hammering","securitycraft:reinforced_orange_stained_glass","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","bloodmagic:synthetic_point","cfm:mangrove_mail_box","minecraft:oak_chest_boat","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","handcrafted:oak_dining_bench","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwwindows:spruce_shutter","mcwbiomesoplenty:stripped_umbran_pane_window","twilightforest:mining_chest_boat","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mythicbotany:rune_holder","mcwwindows:spruce_plank_four_window","modularrouters:blank_upgrade","croptopia:shaped_figgy_pudding","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","allthecompressed:compress/cobbled_deepslate_1x","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","productivetrees:crates/red_delicious_apple_crate_unpack","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","securitycraft:reinforced_red_stained_glass_pane_from_glass","cfm:black_kitchen_sink","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","mcwroofs:white_concrete_steep_roof","create:crafting/schematics/schematic_and_quill","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","cfm:lime_kitchen_sink","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","mcwwindows:crimson_planks_window2","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","domum_ornamentum:blockbarreldeco_standing","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","mcwwindows:stripped_spruce_log_window2","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","enderio:basic_fluid_filter","rftoolsutility:counter_module","minecraft:oak_trapdoor","littlelogistics:locomotive_route","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","mcwlights:cyan_paper_lamp","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:nether_brick","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","farmersdelight:grilled_salmon","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","alltheores:platinum_ingot_from_raw_blasting","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","mcwwindows:stripped_mangrove_log_window2","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","alltheores:iron_dust_from_hammer_crushing","biomesoplenty:fir_chest_boat","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","cfm:stripped_oak_table","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","mcwfences:oak_curved_gate","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","mcwdoors:oak_swamp_door","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","croptopia:apple_juice","cfm:purple_kitchen_sink","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","cfm:black_cooler","minecraft:jungle_slab","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","cfm:stripped_oak_mail_box","silentgear:rough_rod","botania:forest_eye","mcwwindows:stripped_oak_pane_window","mcwwindows:jungle_plank_parapet","additionallanterns:netherite_lantern","twilightforest:dark_chest_boat","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","alltheores:diamond_dust_from_hammer_crushing","railcraft:iron_tunnel_bore_head","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","dyenamics:bed/aquamarine_bed_frm_white_bed","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","minecraft:brown_dye","mcwbiomesoplenty:jacaranda_plank_window2","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwwindows:dark_oak_plank_pane_window","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwwindows:stripped_spruce_log_four_window","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","cfm:lime_cooler","dyenamics:bed/rose_bed_frm_white_bed","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","mcwfurnitures:oak_coffee_table","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","supplementaries:candy","minecolonies:chicken_broth","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","alltheores:ruby_dust_from_hammer_crushing","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","immersiveengineering:crafting/drillhead_steel","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","farmersdelight:cooking/apple_cider","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","mcwdoors:jungle_tropical_door","minecraft:golden_carrot","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","deepresonance:machine_frame","travelersbackpack:lapis","mcwdoors:jungle_glass_door","terralith:lever_alt","mcwbiomesoplenty:hellbark_window","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","xnet:netcable_red_dye","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","biomesoplenty:jacaranda_chest_boat","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","farmersdelight:cooking/beetroot_soup","sfm:printing_press","supplementaries:timber_cross_brace","mcwbiomesoplenty:maple_pane_window","botania:lava_pendant","mcwfurnitures:oak_bookshelf_cupboard","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","rftoolspower:power_core1","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwwindows:blue_mosaic_glass","mcwbiomesoplenty:redwood_window2","farmersdelight:onion_crate","securitycraft:security_camera","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwlights:cross_lantern","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","railcraft:receiver_circuit","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","mcwroofs:blackstone_top_roof","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","cfm:gray_cooler","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","minecraft:cooked_beef","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","mcwroofs:oak_planks_upper_lower_roof","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:personal_world_spike","handcrafted:oak_nightstand","dyenamics:wine_stained_glass","mcwlights:lava_lamp","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","immersiveengineering:crafting/blueprint_bannerpatterns","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","securitycraft:reinforced_light_blue_stained_glass","mcwfences:spruce_stockade_fence","cfm:jungle_table","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwwindows:cherry_pane_window","simplylight:illuminant_green_block_toggle","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","mcwdoors:garage_white_door","cfm:black_grill","utilitix:armed_stand","botania:lexicon","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","botania:ice_pendant","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","mcwwindows:birch_plank_parapet","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:gutter_middle_white","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aether:aether_iron_nugget_from_blasting","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","railcraft:steel_chestplate","alltheores:zinc_dust_from_hammer_crushing","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","mcwpaths:stone_flagstone","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","xnet:connector_blue","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","handcrafted:oak_drawer","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","mcwbiomesoplenty:maple_highley_gate","cfm:orange_cooler","minecolonies:mint_jelly","minecraft:lime_stained_glass","aether:skyroot_note_block","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","mcwbiomesoplenty:stripped_umbran_log_four_window","minecraft:black_stained_glass"],toBeDisplayed:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","immersiveengineering:crafting/gunpowder_from_dusts","handcrafted:spider_trophy","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwtrpdoors:jungle_cottage_trapdoor","croptopia:shaped_beef_stew","cfm:red_kitchen_sink","minecolonies:baked_salmon","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","xnet:connector_green","mcwpaths:jungle_planks_path","mcwbiomesoplenty:palm_plank_window","minecolonies:cheese_ravioli","mcwbiomesoplenty:dead_window2","create:oak_window","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","minecraft:cooked_salmon_from_campfire_cooking","aether:chainmail_leggings_repairing","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","mcwwindows:oak_plank_pane_window","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","railcraft:controller_circuit","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","botania:manasteel_leggings","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","minecraft:cooked_salmon_from_smoking","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","xnet:connector_red_dye","minecolonies:shapetool","supplementaries:bubble_blower","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","supplementaries:crank","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","securitycraft:reinforced_andesite_with_vanilla_diorite","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:bamboo_chest_raft","dyenamics:bed/bubblegum_bed_frm_white_bed","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","buildinggadgets2:template_manager","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","immersiveengineering:crafting/rockcutter","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","aether:blue_ice_freezing","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","cfm:crimson_kitchen_sink_dark","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","botania:obedience_stick","travelersbackpack:diamond_tier_upgrade","mcwwindows:bricks_pane_window","botania:bauble_box","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","cfm:oak_kitchen_counter","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","alltheores:platinum_dust_from_hammer_crushing","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","botania:mana_fluxfield","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","biomesoplenty:palm_chest_boat","mcwpaths:cobbled_deepslate_windmill_weave_path","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","handcrafted:oak_bench","mcwfurnitures:oak_striped_chair","farmersdelight:cooking/pasta_with_meatballs","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwwindows:birch_plank_window","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","mcwbiomesoplenty:stripped_dead_log_window2","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","cfm:jungle_cabinet","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","cfm:purple_cooler","minecolonies:pasta_tomato","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","croptopia:hamburger","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","alltheores:nickel_dust_from_hammer_crushing","twilightforest:time_chest_boat","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:pine_plank_window2","minecraft:jungle_door","xnet:connector_routing","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","aether:chainmail_gloves_repairing","mcwbiomesoplenty:palm_plank_window2","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","farmersdelight:barbecue_stick","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","mcwwindows:oak_four_window","cfm:oak_coffee_table","farmersdelight:stuffed_potato","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","railcraft:world_spike","mcwdoors:garage_gray_door","botania:petal_green","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","dyenamics:bed/fluorescent_bed_frm_white_bed","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","littlelogistics:seater_car","mcwwindows:granite_pane_window","mcwbiomesoplenty:stripped_dead_pane_window","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","cfm:green_grill","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","minecraft:oak_pressure_plate","cfm:orange_grill","immersiveengineering:crafting/steel_scaffolding_standard","cfm:brown_grill","mcwwindows:quartz_window","mcwwindows:dark_oak_shutter","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:mangrove_chest_boat","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","cfm:gray_kitchen_drawer","mcwtrpdoors:oak_barred_trapdoor","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","minecolonies:mint_tea","xnet:netcable_routing","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","botania:twig_wand","twilightforest:transformation_chest_boat","twigs:silt","additionallanterns:end_stone_lantern","gtceu:shaped/compress_platinum_to_ore_block","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","cfm:mangrove_kitchen_sink_dark","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","mcwdoors:oak_four_panel_door","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","botania:magnet_ring","botania:conversions/green_petal_block_deconstruct","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","minecraft:iron_ingot_from_nuggets","minecolonies:pasta_plain","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","cfm:green_kitchen_sink","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwwindows:white_mosaic_glass","minecraft:redstone_torch","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","rftoolsbase:machine_frame","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwfences:railing_andesite_wall","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","biomesoplenty:empyreal_chest_boat","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","create:crafting/materials/andesite_alloy","mcwwindows:andesite_four_window","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","botania:petal_green_double","pneumaticcraft:armor_upgrade","mcwwindows:birch_plank_pane_window","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","croptopia:fruit_salad","securitycraft:block_change_detector","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","croptopia:potato_chips","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","botania:manasteel_axe","mcwwindows:oak_blinds","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","twilightforest:sorting_chest_boat","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","minecolonies:mutton_dinner","aether:skyroot_cartography_table","mcwdoors:oak_cottage_door","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwpaths:red_sand_path_block","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","cfm:light_blue_cooler","railcraft:signal_circuit","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","undergarden:gloom_o_lantern","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:birch_window","mcwwindows:blackstone_four_window","mcwfences:acacia_picket_fence","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","twigs:oak_table","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","twilightforest:twilight_oak_chest_boat","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","mcwroofs:green_concrete_steep_roof","immersiveengineering:crafting/sword_steel","botania:conversions/lime_petal_block_deconstruct","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwfurnitures:oak_wardrobe","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","modularrouters:blank_module","botania:glass_pickaxe","mcwbiomesoplenty:dead_curved_gate","sgjourney:sandstone_with_lapis","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","farmersdelight:cooking/dumplings","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwwindows:jungle_blinds","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwwindows:stripped_dark_oak_log_window2","dyenamics:conifer_stained_glass","immersiveengineering:crafting/toolbox","cfm:light_gray_trampoline","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","alltheores:lumium_dust_from_alloy_blending","alltheores:netherite_dust_from_hammer_crushing","cfm:purple_kitchen_drawer","botania:flower_bag","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","dyenamics:aquamarine_stained_glass","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:oak_sign","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwroofs:gutter_base_magenta","mcwbiomesoplenty:fir_plank_window2","domum_ornamentum:cobblestone_extra","additionallanterns:emerald_lantern","handcrafted:oak_counter","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:red_maple_hedge","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","botania:apothecary_mossy","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","farmersdelight:cooking/hot_cocoa","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:purple_concrete_powder","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","securitycraft:reinforced_jungle_fence","biomesoplenty:maple_chest_boat","cfm:stripped_spruce_kitchen_drawer","xnet:advanced_connector_yellow","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","xnet:netcable_blue","alltheores:copper_dust_from_hammer_ingot_crushing","cfm:stripped_oak_chair","mcwfurnitures:jungle_counter","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","minecraft:cooked_beef_from_campfire_cooking","cfm:light_blue_kitchen_sink","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/axe_steel","botania:dye_green","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","alltheores:lead_dust_from_hammer_crushing","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","railcraft:steel_leggings","mcwroofs:oak_planks_attic_roof","securitycraft:reinforced_pink_stained_glass","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","littlelogistics:vessel_charger","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwlights:soul_dark_oak_tiki_torch","alltheores:iridium_dust_from_hammer_crushing","deepresonance:resonating_plate_block","naturescompass:natures_compass","railcraft:radio_circuit","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","mcwpaths:cobbled_deepslate_clover_paving","mcwbiomesoplenty:mahogany_plank_window2","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","croptopia:apple_sapling","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","cfm:birch_kitchen_sink_dark","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","croptopia:beef_jerky","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","create:crafting/logistics/powered_toggle_latch","supplementaries:flags/flag_lime","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:mangrove_louvered_shutter","minecraft:cooked_cod","supplementaries:flags/flag_magenta","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","minecraft:diamond_axe","securitycraft:reinforced_pink_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_red","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","minecraft:cartography_table","securitycraft:alarm","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","botania:petal_lime_double","dyenamics:banner/bubblegum_banner","mcwroofs:oak_top_roof","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","rftoolsbuilder:vehicle_control_module","additionallanterns:amethyst_lantern","utilitix:crude_furnace","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwwindows:stone_window","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwpaths:cobbled_deepslate_flagstone_path","cfm:stripped_warped_kitchen_drawer","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","mcwroofs:cobblestone_roof","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","sfm:water_tank","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","alchemistry:compactor","cfm:jungle_upgraded_fence","minecraft:dropper","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","botania:manasteel_chestplate","botania:magenta_shiny_flower","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","mcwdoors:oak_japanese2_door","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","minecraft:torch","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwwindows:mangrove_window","mcwwindows:quartz_pane_window","cfm:oak_desk","mcwtrpdoors:metal_trapdoor","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","supplementaries:daub_frame","botania:manasteel_sword","pneumaticcraft:minigun_upgrade","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","botania:dreamwood_wand","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","mcwpaths:stone_running_bond_stairs","securitycraft:briefcase","mcwwindows:green_mosaic_glass","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:stripped_oak_large_drawer","railcraft:steel_sword","cfm:white_grill","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","travelersbackpack:hose_nozzle","securitycraft:claymore","handcrafted:jungle_dining_bench","cfm:red_cooler","railcraft:golden_ticket","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","enderio:black_paper","supplementaries:lock_block","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","ae2:network/cables/dense_smart_fluix","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwwindows:stripped_acacia_log_four_window","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","botania:sextant","securitycraft:laser_block","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","botania:manasteel_boots","mcwfences:modern_andesite_wall","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:raw_iron_block","mcwwindows:stone_window2","mcwwindows:metal_four_window","minecraft:oak_button","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","supplementaries:flags/flag_blue","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","mcwroofs:jungle_planks_top_roof","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","xnet:advanced_connector_yellow_dye","minecolonies:apple_pie","railcraft:steel_shears","additionallanterns:diamond_lantern","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","securitycraft:reinforced_lime_stained_glass_pane_from_glass","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","itemcollectors:basic_collector","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","minecraft:oak_fence_gate","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","alltheores:iron_dust_from_hammer_ingot_crushing","pneumaticcraft:item_life_upgrade","rftoolsbuilder:space_chamber_card","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","alltheores:brass_dust_from_alloy_blending","mcwfurnitures:stripped_jungle_table","buildinggadgets2:gadget_copy_paste","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","supplementaries:flags/flag_green","cfm:stripped_jungle_mail_box","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","croptopia:beetroot_salad","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwwindows:acacia_plank_four_window","allthecompressed:compress/oak_planks_1x","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","dyenamics:bed/maroon_bed_frm_white_bed","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","mcwbiomesoplenty:stripped_palm_log_four_window","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","mcwroofs:gutter_base_light_blue","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","minecraft:diamond_block","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:deepslate_roof","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","minecraft:yellow_stained_glass","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","utilitix:experience_crystal","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwwindows:red_sandstone_pane_window","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","cfm:green_cooler","alltheores:steel_rod","handcrafted:goat_trophy","bigreactors:blasting/graphite_from_coal","mcwpaths:cobbled_deepslate_flagstone","paraglider:paraglider","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","minecolonies:raw_noodle","securitycraft:reinforced_white_stained_glass_pane_from_dye","minecraft:cooked_mutton","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","mcwbiomesoplenty:willow_pane_window","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","minecraft:lapis_block","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","mcwbiomesoplenty:redwood_plank_window","minecraft:iron_axe","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","minecraft:jukebox","securitycraft:blacklist_module","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwwindows:spruce_plank_pane_window","sophisticatedstorage:jungle_chest","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","minecraft:jungle_fence","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","occultism:crafting/demons_dream_essence_from_seeds","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","mcwbiomesoplenty:magic_plank_window2","farmersdelight:cooking/pasta_with_mutton_chop","securitycraft:reinforced_light_gray_stained_glass","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwwindows:spruce_four_window","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","mcwwindows:prismarine_pane_window","minecolonies:pottage","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","supplementaries:slice_map","dyenamics:bed/lavender_bed_frm_white_bed","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","handcrafted:oak_pillar_trim","occultism:crafting/butcher_knife","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","delightful:knives/manasteel_knife","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","securitycraft:reinforced_sticky_piston","aether:iron_pendant","botania:lens_normal","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","cfm:gray_grill","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","aether:skyroot_iron_vanilla_shield","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:stripped_redwood_log_window","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","xnet:netcable_green","mcwfurnitures:stripped_jungle_coffee_table","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","railcraft:steel_helmet","farmersdelight:iron_knife","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","handcrafted:jungle_counter","dyenamics:banner/navy_banner","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","minecraft:cooked_cod_from_campfire_cooking","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","mcwdoors:oak_stable_door","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","cfm:oak_upgraded_gate","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","dyenamics:bed/honey_bed_frm_white_bed","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","croptopia:cashew_chicken","mcwpaths:stone_crystal_floor_slab","minecraft:copper_ingot_from_blasting_raw_copper","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","farmersdelight:chicken_sandwich","securitycraft:reinforced_cherry_fence","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbiomesoplenty:redwood_four_window","mcwwindows:birch_log_parapet","mcwbiomesoplenty:magic_plank_window","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","botania:lime_petal_block","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:dead_pane_window","mcwwindows:crimson_blinds","mcwwindows:diorite_window2","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","farmersdelight:cooking/baked_cod_stew","xnet:connector_blue_dye","mcwfurnitures:oak_bookshelf","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","mcwwindows:oak_log_parapet","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","botania:travel_belt","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","mcwpaths:cobbled_deepslate_flagstone_slab","create:crafting/schematics/empty_schematic","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwroofs:deepslate_attic_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwwindows:dark_prismarine_pane_window","aether:iron_helmet_repairing","farmersdelight:steak_and_potatoes","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwfurnitures:jungle_table","mcwbiomesoplenty:umbran_plank_window2","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","rftoolsbuilder:green_shield_template_block","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","securitycraft:reinforced_glass_pane","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwroofs:gutter_base_lime","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","dyenamics:bed/fluorescent_bed","botania:green_petal_block","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwbiomesoplenty:jacaranda_four_window","mcwbridges:iron_bridge_pier","cfm:pink_grill","pneumaticcraft:magnet_upgrade","mcwroofs:gutter_middle_orange","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","minecraft:sticky_piston","mcwwindows:granite_window2","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","croptopia:date_sapling","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","cfm:oak_kitchen_sink_light","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwwindows:diorite_pane_window","mcwbridges:asian_red_bridge_pier","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","minecraft:oak_stairs","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","alltheores:bronze_plate","botania:swap_ring","pneumaticcraft:gilded_upgrade","mcwwindows:jungle_log_parapet","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwwindows:white_mosaic_glass_pane","mcwwindows:purple_mosaic_glass","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwwindows:acacia_blinds","mcwroofs:brown_concrete_attic_roof","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwlights:festive_wall_lantern","mcwbiomesoplenty:pine_plank_pane_window","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","biomesoplenty:umbran_chest_boat","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","sophisticatedstorage:packing_tape","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwlights:soul_oak_tiki_torch","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","deepresonance:radiation_monitor","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","railcraft:steel_gear","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","croptopia:shaped_sticky_toffee_pudding","mcwfurnitures:jungle_drawer_counter","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","delightful:knives/steel_knife","ad_astra:steel_block","xnet:advanced_connector_blue_dye","cfm:spruce_kitchen_sink_light","mcwroofs:cyan_concrete_roof","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:maple_plank_window2","mcwwindows:dark_prismarine_window","mcwwindows:cherry_plank_window","minecolonies:fish_n_chips","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","xnet:advanced_connector_red_dye","immersiveengineering:crafting/gunpart_drum","minecraft:brown_concrete_powder","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","minecraft:gunpowder","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","minecraft:glass_pane","pneumaticcraft:entity_tracker_upgrade","supplementaries:timber_brace","handcrafted:oak_side_table","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","securitycraft:reinforced_brown_stained_glass","cfm:lime_grill","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","biomesoplenty:dead_chest_boat","mcwwindows:acacia_window","cfm:orange_kitchen_drawer","minecraft:cherry_boat","silentgear:stone_rod","dyenamics:icy_blue_stained_glass","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:spruce_mail_box","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwlights:pink_paper_lamp","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","xnet:connector_green_dye","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","minecolonies:supplychestdeployer","aether:iron_gloves","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfurnitures:oak_glass_table","occultism:crafting/dictionary_of_spirits","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","pneumaticcraft:stomp_upgrade","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","mcwwindows:crimson_stem_parapet","supplementaries:lapis_bricks","croptopia:chocolate","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","forbidden_arcanus:lens_of_veritatis","mcwbridges:glass_bridge_pier","xnet:advanced_connector_routing","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","aether:aether_iron_nugget_from_smelting","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","sfm:cable","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","minecraft:cobblestone_slab","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwwindows:red_sandstone_window2","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:umbran_window2","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","alltheores:lead_dust_from_hammer_ingot_crushing","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","supplementaries:pulley","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","allthecompressed:compress/glass_1x","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","mcwlights:crimson_ceiling_fan_light","alltheores:zinc_dust_from_hammer_ingot_crushing","biomesoplenty:dead_boat","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","supplementaries:flags/flag_orange","travelersbackpack:standard","botania:manasteel_pick","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","railcraft:goggles","twigs:paper_lantern","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","botania:petal_lime","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","simplylight:illuminant_brown_block_toggle","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwbiomesoplenty:empyreal_window2","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwwindows:granite_louvered_shutter","botania:dye_lime","sophisticatedstorage:oak_limited_barrel_1","mcwroofs:gray_concrete_roof","pneumaticcraft:range_upgrade","cfm:magenta_cooler","constructionwand:diamond_wand","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","mcwwindows:warped_stem_four_window","securitycraft:sentry","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","immersiveengineering:crafting/blueprint_bullets","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","minecraft:deepslate","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","minecolonies:meat_ravioli","mcwwindows:spruce_plank_window","supplementaries:checker","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","minecraft:crossbow","cfm:cyan_cooler","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:orange_paper_lamp","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","minecraft:slime_block","immersiveengineering:crafting/shovel_steel","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","supplementaries:soap","mcwwindows:iron_shutter","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","immersiveengineering:crafting/blueprint_molds","rftoolsutility:redstone_information","travelersbackpack:emerald","cfm:dark_oak_kitchen_drawer","farmersdelight:cutting_board","mcwbiomesoplenty:pine_plank_window","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","supplementaries:altimeter","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:lime_concrete_roof","allthecompressed:compress/oak_log_1x","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","alltheores:electrum_dust_from_alloy_blending","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","mcwfences:warped_stockade_fence","botania:manasteel_shovel","botania:pump","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","utilitix:filter_rail","cfm:stripped_dark_oak_mail_box","aether:diamond_gloves","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","railcraft:steel_tunnel_bore_head","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:magenta_stained_glass","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwbiomesoplenty:stripped_maple_log_four_window","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","dyenamics:bed/conifer_bed_frm_white_bed","cfm:stripped_jungle_desk_cabinet","aquaculture:jellyfish_to_slimeball","mcwpaths:cobbled_deepslate_strewn_rocky_path","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:diorite_four_window","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwwindows:mangrove_plank_window2","minecraft:jungle_stairs","travelersbackpack:backpack_tank","botania:knockback_belt","mcwwindows:mangrove_pane_window","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","dyenamics:bed/mint_bed_frm_white_bed","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","farmersdelight:flint_knife","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","mcwwindows:stripped_warped_stem_window","supplementaries:candle_holders/candle_holder_white","mcwwindows:stripped_birch_log_four_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","minecraft:cooked_salmon","immersiveengineering:crafting/plate_steel_hammering","securitycraft:reinforced_orange_stained_glass","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","bloodmagic:synthetic_point","cfm:mangrove_mail_box","minecraft:oak_chest_boat","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","handcrafted:oak_dining_bench","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwwindows:spruce_shutter","mcwbiomesoplenty:stripped_umbran_pane_window","twilightforest:mining_chest_boat","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mythicbotany:rune_holder","mcwwindows:spruce_plank_four_window","modularrouters:blank_upgrade","croptopia:shaped_figgy_pudding","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","allthecompressed:compress/cobbled_deepslate_1x","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","productivetrees:crates/red_delicious_apple_crate_unpack","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","securitycraft:reinforced_red_stained_glass_pane_from_glass","cfm:black_kitchen_sink","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","mcwroofs:white_concrete_steep_roof","create:crafting/schematics/schematic_and_quill","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","cfm:lime_kitchen_sink","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","mcwwindows:crimson_planks_window2","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","domum_ornamentum:blockbarreldeco_standing","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","mcwwindows:stripped_spruce_log_window2","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","enderio:basic_fluid_filter","rftoolsutility:counter_module","minecraft:oak_trapdoor","littlelogistics:locomotive_route","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","mcwlights:cyan_paper_lamp","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:nether_brick","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","farmersdelight:grilled_salmon","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","alltheores:platinum_ingot_from_raw_blasting","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","mcwwindows:stripped_mangrove_log_window2","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","alltheores:iron_dust_from_hammer_crushing","biomesoplenty:fir_chest_boat","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","cfm:stripped_oak_table","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","mcwfences:oak_curved_gate","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","mcwdoors:oak_swamp_door","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","croptopia:apple_juice","cfm:purple_kitchen_sink","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","cfm:black_cooler","minecraft:jungle_slab","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","cfm:stripped_oak_mail_box","silentgear:rough_rod","botania:forest_eye","mcwwindows:stripped_oak_pane_window","mcwwindows:jungle_plank_parapet","additionallanterns:netherite_lantern","twilightforest:dark_chest_boat","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","alltheores:diamond_dust_from_hammer_crushing","railcraft:iron_tunnel_bore_head","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","dyenamics:bed/aquamarine_bed_frm_white_bed","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","minecraft:brown_dye","mcwbiomesoplenty:jacaranda_plank_window2","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwwindows:dark_oak_plank_pane_window","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwwindows:stripped_spruce_log_four_window","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","cfm:lime_cooler","dyenamics:bed/rose_bed_frm_white_bed","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","mcwfurnitures:oak_coffee_table","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","supplementaries:candy","minecolonies:chicken_broth","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","alltheores:ruby_dust_from_hammer_crushing","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","immersiveengineering:crafting/drillhead_steel","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","farmersdelight:cooking/apple_cider","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","mcwdoors:jungle_tropical_door","minecraft:golden_carrot","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","deepresonance:machine_frame","travelersbackpack:lapis","mcwdoors:jungle_glass_door","terralith:lever_alt","mcwbiomesoplenty:hellbark_window","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","xnet:netcable_red_dye","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","biomesoplenty:jacaranda_chest_boat","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","farmersdelight:cooking/beetroot_soup","sfm:printing_press","supplementaries:timber_cross_brace","mcwbiomesoplenty:maple_pane_window","botania:lava_pendant","mcwfurnitures:oak_bookshelf_cupboard","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","rftoolspower:power_core1","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwwindows:blue_mosaic_glass","mcwbiomesoplenty:redwood_window2","farmersdelight:onion_crate","securitycraft:security_camera","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwlights:cross_lantern","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","railcraft:receiver_circuit","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","mcwroofs:blackstone_top_roof","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","cfm:gray_cooler","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","minecraft:cooked_beef","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","mcwroofs:oak_planks_upper_lower_roof","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:personal_world_spike","handcrafted:oak_nightstand","dyenamics:wine_stained_glass","mcwlights:lava_lamp","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","immersiveengineering:crafting/blueprint_bannerpatterns","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","securitycraft:reinforced_light_blue_stained_glass","mcwfences:spruce_stockade_fence","cfm:jungle_table","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwwindows:cherry_pane_window","simplylight:illuminant_green_block_toggle","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","mcwdoors:garage_white_door","cfm:black_grill","utilitix:armed_stand","botania:lexicon","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","botania:ice_pendant","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","mcwwindows:birch_plank_parapet","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:gutter_middle_white","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aether:aether_iron_nugget_from_blasting","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","railcraft:steel_chestplate","alltheores:zinc_dust_from_hammer_crushing","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","mcwpaths:stone_flagstone","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","xnet:connector_blue","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","handcrafted:oak_drawer","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","mcwbiomesoplenty:maple_highley_gate","cfm:orange_cooler","minecolonies:mint_jelly","minecraft:lime_stained_glass","aether:skyroot_note_block","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","mcwbiomesoplenty:stripped_umbran_log_four_window","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:1169,warning_level:0}}