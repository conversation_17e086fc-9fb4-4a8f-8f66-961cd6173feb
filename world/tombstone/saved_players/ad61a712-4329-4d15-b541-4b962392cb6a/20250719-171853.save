{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"attributeslib:prot_pierce"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.5d,Name:"attributeslib:crit_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;*********,-1536278497,-1945827711,-1989455215]},{FromDim:"minecraft:overworld",FromPos:108301899784259L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;*********,**********,-**********,-*********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","9d474303-fe2f-4381-a7d7-89b0756e98c8","70e61f07-a350-437a-ae01-15e3ef9a1bab"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:11509393L,tiredTime:11509438L,wakeTime:11509423L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:chainmail_gloves",tag:{Damage:11}}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[{Count:1b,Slot:0,id:"irons_spellbooks:fireward_ring"}],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:145,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:46990},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"gtceu:shapeless/decompress_platinum_from_ore_block",ItemStack:{Count:9b,id:"alltheores:raw_platinum"}}],SelectedRecipe:"gtceu:shapeless/decompress_platinum_from_ore_block"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:171,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["minecraft:bread","minecraft:baked_potato"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;*********,-1536278497,-1945827711,-1989455215]},{FromDim:"minecraft:overworld",FromPos:108301899784259L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;*********,**********,-**********,-*********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","9d474303-fe2f-4381-a7d7-89b0756e98c8","70e61f07-a350-437a-ae01-15e3ef9a1bab"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],twilightforest_banished:1b},"quark:locked_once":1b,"quark:trying_crawl":0b,simplylight_unlocked:2},Health:24.0f,HurtByTimestamp:37535,HurtTime:0s,Inventory:[{Count:19b,Slot:0b,id:"railcraft:sulfur_dust"},{Count:1b,Slot:1b,id:"minecraft:stone_pickaxe",tag:{Damage:55}},{Count:7b,Slot:2b,id:"gtceu:sticky_resin"},{Count:3b,Slot:3b,id:"minecraft:baked_potato"},{Count:3b,Slot:4b,id:"minecraft:jungle_sapling"},{Count:1b,Slot:5b,id:"crafting_on_a_stick:crafting_table"},{Count:1b,Slot:6b,id:"enderio:dark_steel_sword",tag:{Damage:9}},{Count:1b,Slot:7b,id:"silentgear:pickaxe",tag:{Damage:50,SGear_Data:{Construction:{Parts:[{ID:"silentgear:pickaxe_head",Item:{Count:1b,id:"silentgear:pickaxe_head",tag:{Damage:0,Materials:[{ID:"silentgear:iron"}]}}},{ID:"silentgear:rod",Item:{Count:1b,id:"silentgear:rod",tag:{Materials:[{ID:"silentgear:wood"}]}}}]},Properties:{HarvestTier:"minecraft:iron",LockStats:0b,ModVersion:"3.6.6",Stats:{"silentgear:attack_reach":3.0f,"silentgear:attack_speed":-2.8f,"silentgear:charging_value":0.7f,"silentgear:durability":250.0f,"silentgear:enchantment_value":14.0f,"silentgear:harvest_speed":6.0f,"silentgear:magic_damage":1.0f,"silentgear:melee_damage":3.0f,"silentgear:rarity":20.0f,"silentgear:repair_efficiency":1.0f},Traits:[{Level:3b,Name:"silentgear:malleable"},{Level:1b,Name:"silentgear:magnetic"},{Level:2b,Name:"silentgear:flexible"}]},Rendering:{Model:3,ModelKey:"pickaxe:pickaxe_head{iron},rod{wood},"}},SGear_UUID:[I;1812937517,1112163831,-1393588685,-1224139774]}},{Count:1b,Slot:8b,id:"powah:uraninite_raw"},{Count:21b,Slot:9b,id:"minecraft:raw_copper"},{Count:12b,Slot:10b,id:"minecraft:raw_iron"},{Count:6b,Slot:11b,id:"minecraft:emerald"},{Count:1b,Slot:12b,id:"forbidden_arcanus:arcane_crystal"},{Count:7b,Slot:13b,id:"minecraft:diamond"},{Count:64b,Slot:14b,id:"minecraft:dirt"},{Count:56b,Slot:15b,id:"minecraft:cobbled_deepslate"},{Count:9b,Slot:16b,id:"alltheores:raw_platinum"},{Count:5b,Slot:20b,id:"minecraft:raw_gold"},{Count:17b,Slot:23b,id:"minecraft:cobblestone"},{Count:1b,Slot:30b,id:"minecraft:water_bucket"},{Count:29b,Slot:31b,id:"minecraft:jungle_log"},{Count:1b,Slot:33b,id:"minecraft:iron_sword",tag:{Damage:0,affix_data:{affixes:{"apotheosis:durable":0.14f,"apotheosis:sword/attribute/lacerating":0.5231444f,"apotheosis:sword/attribute/spellbreaking":0.7052661f,"apotheosis:sword/attribute/violent":0.68840533f,"apotheosis:sword/special/thunderstruck":0.43175685f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/attribute/violent"},"",{"translate":"affix.apotheosis:sword/special/thunderstruck.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;-1313032264,1604339494,-1301996218,93544638]]},apoth_rchest:1b}},{Count:1b,Slot:34b,id:"constructionwand:stone_wand",tag:{Damage:9,wand_options:{}}},{Count:4b,Slot:35b,id:"minecraft:jungle_planks"},{Count:1b,Slot:101b,id:"minecraft:iron_leggings",tag:{Damage:28,affix_data:{affixes:{"apotheosis:armor/attribute/ironforged":0.91983974f},name:'{"color":"#808080","translate":"misc.apotheosis.affix_name.two","with":[{"translate":"affix.apotheosis:armor/attribute/ironforged"},"",""]}',rarity:"apotheosis:common",uuids:[[I;951810640,-1785248472,-1226696792,-1506455276]]},apoth_rchest:1b}},{Count:1b,Slot:102b,id:"minecraft:chainmail_chestplate",tag:{Damage:25,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.28710568f,"apotheosis:armor/dmg_reduction/blockading":0.5720847f,"apotheosis:armor/dmg_reduction/dwarven":0.20756513f,"apotheosis:durable":0.13f,"irons_spellbooks:armor/attribute/cooldown":0.8644289f,"irons_spellbooks:armor/attribute/spell_resist":0.92645544f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist"},"",{"translate":"affix.apotheosis:armor/attribute/blessed.suffix"}]}',rarity:"apotheosis:rare",sockets:2,uuids:[[I;-60292517,933316670,-1275747799,-647435076]]},apoth_rchest:1b}},{Count:1b,Slot:103b,id:"minecraft:iron_helmet",tag:{Damage:26,affix_data:{affixes:{"apotheosis:armor/attribute/stalwart":0.14640558f,"apotheosis:armor/dmg_reduction/runed":0.6454965f,"apotheosis:durable":0.07f,"irons_spellbooks:armor/attribute/cooldown":0.53369707f,"irons_spellbooks:armor/attribute/mana":0.3291387f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",{"translate":"affix.irons_spellbooks:armor/attribute/cooldown.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;-129930195,233390820,-1664290225,1313487713]]},apoth_rchest:1b}},{Count:8b,Slot:-106b,id:"minecraft:torch"}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[481.46974249085264d,-48.0d,1213.1498984089603d],Railways_DataVersion:2,Rotation:[127.34961f,15.300011f],Score:87,SelectedItemSlot:7,SleepTimer:0s,SpawnAngle:122.09277f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:392,SpawnY:67,SpawnZ:1084,Spigot.ticksLived:46990,UUID:[I;-1386109166,1126780181,-1254012010,596822890],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":132216278212560L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:6,XpP:0.7894739f,XpSeed:0,XpTotal:87,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752912705892L,keepLevel:0b,lastKnownName:"isanKash",lastPlayed:1752916733102L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.702666f,foodLevel:20,foodSaturationLevel:3.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","immersiveengineering:crafting/gunpowder_from_dusts","handcrafted:spider_trophy","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwtrpdoors:jungle_cottage_trapdoor","croptopia:shaped_beef_stew","cfm:red_kitchen_sink","minecolonies:baked_salmon","cfm:fridge_dark","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","xnet:connector_green","mcwpaths:jungle_planks_path","minecolonies:cheese_ravioli","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","minecraft:cooked_salmon_from_campfire_cooking","aether:chainmail_leggings_repairing","mcwbiomesoplenty:empyreal_hedge","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwdoors:jungle_nether_door","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","botania:manasteel_leggings","mcwlights:crimson_tiki_torch","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","minecraft:cooked_salmon_from_smoking","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","xnet:connector_red_dye","supplementaries:bubble_blower","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","securitycraft:reinforced_andesite_with_vanilla_diorite","dyenamics:bed/bubblegum_bed_frm_white_bed","aquaculture:birch_fish_mount","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","immersiveengineering:crafting/rockcutter","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","aether:blue_ice_freezing","mcwwindows:metal_window2","mcwpaths:cobblestone_dumble_paving","dyenamics:aquamarine_concrete_powder","cfm:crimson_kitchen_sink_dark","handcrafted:jungle_cupboard","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","botania:obedience_stick","travelersbackpack:diamond_tier_upgrade","botania:bauble_box","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","additionallanterns:prismarine_lantern","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","botania:mana_fluxfield","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","utilitarian:utility/charcoal_from_campfire","cfm:acacia_kitchen_drawer","mcwpaths:cobbled_deepslate_windmill_weave_path","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","farmersdelight:cooking/pasta_with_meatballs","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","minecraft:red_concrete_powder","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","simplylight:edge_light_bottom_from_top","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","cfm:stripped_dark_oak_kitchen_sink_dark","create:industrial_iron_block_from_ingots_iron_stonecutting","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","cfm:purple_cooler","minecolonies:pasta_tomato","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","croptopia:hamburger","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","minecraft:jungle_door","xnet:connector_routing","aether:chainmail_gloves_repairing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","farmersdelight:barbecue_stick","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","farmersdelight:stuffed_potato","railcraft:world_spike","mcwdoors:garage_gray_door","mcwbiomesoplenty:magic_picket_fence","dyenamics:bed/fluorescent_bed_frm_white_bed","littlelogistics:seater_car","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","cfm:green_grill","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","immersiveengineering:crafting/steel_scaffolding_standard","cfm:brown_grill","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","cfm:jungle_desk","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","cfm:gray_kitchen_drawer","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","minecolonies:mint_tea","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","cfm:mangrove_kitchen_sink_dark","silentgear:diamond_shard","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","botania:magnet_ring","mcwlights:blue_paper_lamp","minecraft:iron_ingot_from_nuggets","minecolonies:pasta_plain","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:bamboo_raft","cfm:green_kitchen_sink","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","rftoolsbase:machine_frame","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwfences:railing_andesite_wall","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","forbidden_arcanus:diamond_blacksmith_gavel","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","croptopia:fruit_salad","securitycraft:block_change_detector","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwfences:end_brick_railing_gate","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","croptopia:potato_chips","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","minecolonies:mutton_dinner","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwfences:warped_pyramid_gate","mcwpaths:red_sand_path_block","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","cfm:light_blue_cooler","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","undergarden:gloom_o_lantern","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwfences:acacia_picket_fence","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","domum_ornamentum:sand_bricks","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","cfm:red_grill","mcwfences:railing_blackstone_wall","travelersbackpack:ocelot","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbridges:jungle_log_bridge_middle","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","mcwroofs:green_concrete_steep_roof","immersiveengineering:crafting/sword_steel","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:dead_curved_gate","sgjourney:sandstone_with_lapis","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","farmersdelight:cooking/dumplings","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","cfm:stripped_warped_mail_box","immersiveengineering:crafting/toolbox","cfm:light_gray_trampoline","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","cfm:purple_kitchen_drawer","botania:flower_bag","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","additionallanterns:emerald_lantern","mcwbiomesoplenty:red_maple_hedge","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","minecraft:gold_ingot_from_blasting_raw_gold","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","cfm:purple_grill","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","deeperdarker:bloom_boat","farmersdelight:cooking/hot_cocoa","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","minecraft:purple_concrete_powder","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","xnet:advanced_connector_yellow","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","xnet:netcable_blue","mcwfurnitures:jungle_counter","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","minecraft:cooked_beef_from_campfire_cooking","cfm:light_blue_kitchen_sink","createoreexcavation:diamond_drill","immersiveengineering:crafting/axe_steel","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","railcraft:steel_leggings","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","mcwpaths:cobbled_deepslate_clover_paving","mcwfences:panelled_metal_fence_gate","supplementaries:sign_post_jungle","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","croptopia:apple_sapling","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","cfm:birch_kitchen_sink_dark","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","croptopia:beef_jerky","mcwwindows:jungle_plank_four_window","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","minecraft:iron_bars","corail_woodcutter:mangrove_woodcutter","handcrafted:jungle_desk","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","minecraft:cooked_cod","minecraft:iron_helmet","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","rftoolsutility:charged_porter","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","additionallanterns:amethyst_lantern","utilitix:crude_furnace","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwpaths:cobbled_deepslate_flagstone_path","cfm:stripped_warped_kitchen_drawer","mcwfences:crimson_picket_fence","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","mcwroofs:cobblestone_roof","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","alchemistry:compactor","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","botania:manasteel_chestplate","botania:magenta_shiny_flower","mcwfences:bamboo_highley_gate","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwfences:jungle_curved_gate","minecraft:torch","mcwlights:striped_wall_lantern","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","botania:manasteel_sword","pneumaticcraft:minigun_upgrade","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","twilightforest:time_boat","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","mcwpaths:stone_running_bond_stairs","securitycraft:briefcase","mcwfences:spruce_wired_fence","railcraft:steel_sword","cfm:white_grill","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","mcwroofs:jungle_planks_steep_roof","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","handcrafted:jungle_dining_bench","cfm:red_cooler","mcwbiomesoplenty:magic_wired_fence","supplementaries:lock_block","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","botania:sextant","securitycraft:laser_block","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","botania:manasteel_boots","mcwfences:modern_andesite_wall","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:raw_iron_block","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","minecraft:shears","croptopia:fruit_smoothie","mcwtrpdoors:metal_warning_trapdoor","mcwroofs:jungle_planks_top_roof","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","mcwfurnitures:jungle_striped_chair","xnet:advanced_connector_yellow_dye","minecolonies:apple_pie","railcraft:steel_shears","additionallanterns:diamond_lantern","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","aquaculture:bobber","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","simplylight:illuminant_brown_block_on_dyed","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","itemcollectors:basic_collector","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","railcraft:steel_axe","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:turn_table","pneumaticcraft:item_life_upgrade","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","buildinggadgets2:gadget_copy_paste","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","aquaculture:oak_fish_mount","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","croptopia:beetroot_salad","allthetweaks:ender_pearl_block","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwroofs:orange_concrete_steep_roof","dyenamics:bed/maroon_bed_frm_white_bed","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwroofs:gutter_base_light_blue","additionallanterns:diorite_lantern","pneumaticcraft:dispenser_upgrade","minecraft:diamond_block","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:deepslate_roof","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","rftoolsutility:inventoryplus_module","mcwfurnitures:jungle_end_table","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","cfm:green_cooler","alltheores:steel_rod","handcrafted:goat_trophy","bigreactors:blasting/graphite_from_coal","mcwpaths:cobbled_deepslate_flagstone","cfm:stripped_crimson_kitchen_sink_light","simplylight:illuminant_lime_block_on_dyed","minecolonies:raw_noodle","minecraft:cooked_mutton","cfm:dark_oak_kitchen_sink_light","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","buildinggadgets2:gadget_exchanging","minecraft:lapis_block","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","utilitarian:snad/snad","minecraft:jukebox","securitycraft:blacklist_module","mcwbiomesoplenty:snowblossom_hedge","sophisticatedstorage:jungle_chest","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","simplylight:illuminant_blue_block_on_toggle","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","occultism:crafting/demons_dream_essence_from_seeds","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","mcwroofs:gutter_base_orange","farmersdelight:cooking/pasta_with_mutton_chop","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwfences:quartz_railing_gate","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","minecolonies:pottage","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:candle_holders/candle_holder_yellow","dyenamics:bed/lavender_bed_frm_white_bed","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwroofs:yellow_concrete_steep_roof","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","delightful:knives/manasteel_knife","mcwlights:mangrove_tiki_torch","aether:iron_pendant","botania:lens_normal","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","simplylight:illuminant_slab_from_panel","cfm:gray_grill","sophisticatedstorage:jungle_barrel","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","xnet:netcable_green","mcwfurnitures:stripped_jungle_coffee_table","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","cfm:stripped_mangrove_mail_box","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","railcraft:steel_helmet","farmersdelight:iron_knife","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","additionallanterns:warped_lantern","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:spruce_boat","handcrafted:jungle_counter","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwfences:deepslate_brick_railing_gate","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","dyenamics:bed/honey_bed_frm_white_bed","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","minecraft:copper_ingot_from_blasting_raw_copper","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","cfm:crimson_mail_box","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","minecraft:iron_nugget","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","farmersdelight:cooking/baked_cod_stew","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","pneumaticcraft:volume_upgrade","aquaculture:dark_oak_fish_mount","botania:travel_belt","allthecompressed:compress/jungle_planks_1x","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","mcwpaths:cobbled_deepslate_flagstone_slab","mcwlights:black_paper_lamp","simplylight:rodlamp","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwroofs:deepslate_attic_roof","aether:iron_helmet_repairing","farmersdelight:steak_and_potatoes","minecraft:iron_sword","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:gutter_base_lime","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","mcwbridges:iron_bridge_pier","cfm:pink_grill","pneumaticcraft:magnet_upgrade","mcwroofs:gutter_middle_orange","mcwroofs:light_blue_concrete_roof","croptopia:date_sapling","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","utilitix:diamond_shears","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwbiomesoplenty:fir_stockade_fence","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwroofs:cyan_concrete_steep_roof","aether:stone_pickaxe_repairing","alltheores:bronze_plate","botania:swap_ring","pneumaticcraft:gilded_upgrade","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","aquaculture:tin_can_to_iron_nugget","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","xnet:connector_yellow","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwlights:soul_oak_tiki_torch","mcwlights:double_street_lamp","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","additionallanterns:quartz_lantern","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","railcraft:steel_gear","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","croptopia:shaped_sticky_toffee_pudding","mcwfurnitures:jungle_drawer_counter","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","delightful:knives/steel_knife","ad_astra:steel_block","xnet:advanced_connector_blue_dye","cfm:spruce_kitchen_sink_light","mcwroofs:cyan_concrete_roof","xnet:netcable_green_dye","minecolonies:fish_n_chips","minecraft:cobblestone_wall_from_cobblestone_stonecutting","xnet:advanced_connector_red_dye","immersiveengineering:crafting/gunpart_drum","minecraft:brown_concrete_powder","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","minecraft:gunpowder","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwlights:wall_lantern","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","pneumaticcraft:entity_tracker_upgrade","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","simplylight:illuminant_yellow_block_toggle","mcwfurnitures:stripped_jungle_glass_table","mcwroofs:magenta_concrete_roof","supplementaries:bed_from_feather_block","immersiveengineering:crafting/component_iron","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","cfm:orange_kitchen_drawer","minecraft:cherry_boat","silentgear:stone_rod","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","immersiveengineering:crafting/stick_steel","wirelesschargers:basic_wireless_block_charger","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:spruce_mail_box","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwlights:pink_paper_lamp","immersiveengineering:crafting/connector_hv","botania:dodge_ring","pneumaticcraft:elytra_upgrade","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","xnet:connector_green_dye","minecraft:baked_potato_from_smoking","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","aether:iron_gloves","mcwfurnitures:stripped_jungle_drawer","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","occultism:crafting/dictionary_of_spirits","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","pneumaticcraft:stomp_upgrade","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","cfm:yellow_grill","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","nethersdelight:blackstone_blast_furnace","aether:aether_iron_nugget_from_smelting","minecraft:diamond_shovel","sfm:cable","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","biomesoplenty:dead_boat","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","railcraft:goggles","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","minecraft:chain","cfm:stripped_acacia_kitchen_drawer","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","minecraft:copper_ingot_from_smelting_raw_copper","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","immersiveengineering:crafting/fertilizer","mcwroofs:gray_concrete_roof","pneumaticcraft:range_upgrade","cfm:magenta_cooler","constructionwand:diamond_wand","securitycraft:sentry","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","minecraft:deepslate","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","minecolonies:meat_ravioli","supplementaries:checker","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","minecraft:crossbow","cfm:cyan_cooler","pneumaticcraft:coordinate_tracker_upgrade","mcwbridges:cobblestone_bridge_pier","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:orange_paper_lamp","mcwlights:iron_triple_candle_holder","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","xnet:advanced_connector_green_dye","immersiveengineering:crafting/shovel_steel","supplementaries:soap","mcwbridges:cobblestone_bridge","travelersbackpack:emerald","cfm:dark_oak_kitchen_drawer","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","utilitix:linked_crystal","immersiveengineering:crafting/sawblade","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwpaths:stone_windmill_weave_stairs","railcraft:steel_shovel","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","mcwroofs:lime_concrete_roof","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","utilitarian:utility/jungle_logs_to_slabs","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","mcwfences:warped_stockade_fence","botania:manasteel_shovel","botania:pump","mcwpaths:cobblestone_square_paving","dyenamics:bed/spring_green_bed","utilitix:filter_rail","cfm:stripped_dark_oak_mail_box","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","railcraft:steel_tunnel_bore_head","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","dyenamics:bed/conifer_bed_frm_white_bed","cfm:stripped_jungle_desk_cabinet","aquaculture:jellyfish_to_slimeball","mcwpaths:cobbled_deepslate_strewn_rocky_path","minecraft:jungle_wood","handcrafted:bench","mcwfences:panelled_metal_fence","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","botania:knockback_belt","additionallanterns:smooth_stone_lantern","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","dyenamics:bed/mint_bed_frm_white_bed","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","supplementaries:candle_holders/candle_holder_white","corail_woodcutter:warped_woodcutter","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","silentgear:upgrade_base","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","minecraft:cooked_salmon","immersiveengineering:crafting/plate_steel_hammering","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:gutter_middle_cyan","bloodmagic:synthetic_point","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","handcrafted:wolf_trophy","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mythicbotany:rune_holder","modularrouters:blank_upgrade","croptopia:shaped_figgy_pudding","allthecompressed:compress/cobbled_deepslate_1x","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","productivetrees:crates/red_delicious_apple_crate_unpack","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","cfm:black_kitchen_sink","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","minecraft:lever","mcwpaths:cobbled_deepslate_dumble_paving","immersiveengineering:crafting/wirecoil_structure_steel","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:willow_pyramid_gate","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","cfm:lime_kitchen_sink","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","mcwlights:cyan_paper_lamp","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:nether_brick","mcwdoors:jungle_waffle_door","farmersdelight:grilled_salmon","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","alltheores:platinum_ingot_from_raw_blasting","cfm:stripped_oak_kitchen_sink_light","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","cfm:oak_kitchen_sink_dark","mcwbiomesoplenty:magic_highley_gate","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","xnet:netcable_blue_dye","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","mcwfences:oak_curved_gate","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","biomesoplenty:palm_boat","handcrafted:bear_trophy","croptopia:apple_juice","cfm:purple_kitchen_sink","cfm:black_cooler","minecraft:jungle_slab","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","cfm:stripped_oak_mail_box","botania:forest_eye","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","dyenamics:bed/aquamarine_bed_frm_white_bed","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","minecraft:brown_dye","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","cfm:lime_cooler","dyenamics:bed/rose_bed_frm_white_bed","mcwroofs:gray_concrete_upper_steep_roof","silentgear:emerald_from_shards","minecolonies:chicken_broth","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwroofs:pink_concrete_lower_roof","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","rftoolsbase:smartwrench","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","cfm:white_kitchen_drawer","farmersdelight:cooking/apple_cider","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","forbidden_arcanus:wooden_blacksmith_gavel","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","securitycraft:floor_trap","xnet:connector_red","mcwdoors:jungle_tropical_door","minecraft:golden_carrot","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","mcwroofs:brown_concrete_lower_roof","deepresonance:machine_frame","travelersbackpack:lapis","mcwdoors:jungle_glass_door","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","xnet:netcable_red_dye","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","sfm:printing_press","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","mcwfences:bamboo_pyramid_gate","minecraft:iron_ingot_from_blasting_raw_iron","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","farmersdelight:onion_crate","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","mcwlights:cross_lantern","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:deepslate_window2","cfm:mangrove_kitchen_drawer","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","mcwroofs:blackstone_top_roof","additionallanterns:diamond_chain","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwwindows:jungle_window2","cfm:gray_cooler","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","cfm:green_kitchen_drawer","minecraft:cooked_beef","mcwtrpdoors:jungle_glass_trapdoor","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","mcwlights:warped_tiki_torch","cfm:light_blue_trampoline","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:personal_world_spike","mcwlights:lava_lamp","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwfences:spruce_stockade_fence","cfm:jungle_table","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","mcwbiomesoplenty:empyreal_pyramid_gate","simplylight:illuminant_green_block_toggle","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","mcwdoors:garage_white_door","cfm:black_grill","botania:lexicon","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","botania:ice_pendant","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:gutter_middle_white","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aether:aether_iron_nugget_from_blasting","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","railcraft:steel_chestplate","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","enderio:fluid_tank","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","xnet:connector_blue","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","mcwbiomesoplenty:maple_highley_gate","cfm:orange_cooler","minecolonies:mint_jelly","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern"],toBeDisplayed:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","immersiveengineering:crafting/gunpowder_from_dusts","handcrafted:spider_trophy","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwtrpdoors:jungle_cottage_trapdoor","croptopia:shaped_beef_stew","cfm:red_kitchen_sink","minecolonies:baked_salmon","cfm:fridge_dark","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","xnet:connector_green","mcwpaths:jungle_planks_path","minecolonies:cheese_ravioli","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","minecraft:cooked_salmon_from_campfire_cooking","aether:chainmail_leggings_repairing","mcwbiomesoplenty:empyreal_hedge","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwdoors:jungle_nether_door","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","botania:manasteel_leggings","mcwlights:crimson_tiki_torch","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","minecraft:cooked_salmon_from_smoking","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","xnet:connector_red_dye","supplementaries:bubble_blower","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","securitycraft:reinforced_andesite_with_vanilla_diorite","dyenamics:bed/bubblegum_bed_frm_white_bed","aquaculture:birch_fish_mount","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","immersiveengineering:crafting/rockcutter","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","aether:blue_ice_freezing","mcwwindows:metal_window2","mcwpaths:cobblestone_dumble_paving","dyenamics:aquamarine_concrete_powder","cfm:crimson_kitchen_sink_dark","handcrafted:jungle_cupboard","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","botania:obedience_stick","travelersbackpack:diamond_tier_upgrade","botania:bauble_box","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","additionallanterns:prismarine_lantern","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","botania:mana_fluxfield","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","utilitarian:utility/charcoal_from_campfire","cfm:acacia_kitchen_drawer","mcwpaths:cobbled_deepslate_windmill_weave_path","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","farmersdelight:cooking/pasta_with_meatballs","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","minecraft:red_concrete_powder","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","simplylight:edge_light_bottom_from_top","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","cfm:stripped_dark_oak_kitchen_sink_dark","create:industrial_iron_block_from_ingots_iron_stonecutting","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","cfm:purple_cooler","minecolonies:pasta_tomato","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","croptopia:hamburger","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","minecraft:jungle_door","xnet:connector_routing","aether:chainmail_gloves_repairing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","farmersdelight:barbecue_stick","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","farmersdelight:stuffed_potato","railcraft:world_spike","mcwdoors:garage_gray_door","mcwbiomesoplenty:magic_picket_fence","dyenamics:bed/fluorescent_bed_frm_white_bed","littlelogistics:seater_car","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","cfm:green_grill","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","immersiveengineering:crafting/steel_scaffolding_standard","cfm:brown_grill","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","cfm:jungle_desk","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","cfm:gray_kitchen_drawer","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","minecolonies:mint_tea","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","cfm:mangrove_kitchen_sink_dark","silentgear:diamond_shard","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","botania:magnet_ring","mcwlights:blue_paper_lamp","minecraft:iron_ingot_from_nuggets","minecolonies:pasta_plain","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:bamboo_raft","cfm:green_kitchen_sink","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","rftoolsbase:machine_frame","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwfences:railing_andesite_wall","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","forbidden_arcanus:diamond_blacksmith_gavel","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","croptopia:fruit_salad","securitycraft:block_change_detector","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwfences:end_brick_railing_gate","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","croptopia:potato_chips","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","minecolonies:mutton_dinner","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwfences:warped_pyramid_gate","mcwpaths:red_sand_path_block","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","cfm:light_blue_cooler","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","undergarden:gloom_o_lantern","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwfences:acacia_picket_fence","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","domum_ornamentum:sand_bricks","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","cfm:red_grill","mcwfences:railing_blackstone_wall","travelersbackpack:ocelot","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbridges:jungle_log_bridge_middle","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","mcwroofs:green_concrete_steep_roof","immersiveengineering:crafting/sword_steel","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:dead_curved_gate","sgjourney:sandstone_with_lapis","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","farmersdelight:cooking/dumplings","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","cfm:stripped_warped_mail_box","immersiveengineering:crafting/toolbox","cfm:light_gray_trampoline","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","cfm:purple_kitchen_drawer","botania:flower_bag","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","additionallanterns:emerald_lantern","mcwbiomesoplenty:red_maple_hedge","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","minecraft:gold_ingot_from_blasting_raw_gold","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","cfm:purple_grill","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","deeperdarker:bloom_boat","farmersdelight:cooking/hot_cocoa","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","minecraft:purple_concrete_powder","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","xnet:advanced_connector_yellow","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","xnet:netcable_blue","mcwfurnitures:jungle_counter","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","minecraft:cooked_beef_from_campfire_cooking","cfm:light_blue_kitchen_sink","createoreexcavation:diamond_drill","immersiveengineering:crafting/axe_steel","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","railcraft:steel_leggings","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","mcwpaths:cobbled_deepslate_clover_paving","mcwfences:panelled_metal_fence_gate","supplementaries:sign_post_jungle","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","croptopia:apple_sapling","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","cfm:birch_kitchen_sink_dark","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","croptopia:beef_jerky","mcwwindows:jungle_plank_four_window","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","minecraft:iron_bars","corail_woodcutter:mangrove_woodcutter","handcrafted:jungle_desk","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","minecraft:cooked_cod","minecraft:iron_helmet","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","rftoolsutility:charged_porter","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","additionallanterns:amethyst_lantern","utilitix:crude_furnace","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwpaths:cobbled_deepslate_flagstone_path","cfm:stripped_warped_kitchen_drawer","mcwfences:crimson_picket_fence","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","mcwroofs:cobblestone_roof","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","alchemistry:compactor","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","botania:manasteel_chestplate","botania:magenta_shiny_flower","mcwfences:bamboo_highley_gate","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwfences:jungle_curved_gate","minecraft:torch","mcwlights:striped_wall_lantern","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","botania:manasteel_sword","pneumaticcraft:minigun_upgrade","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","twilightforest:time_boat","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","mcwpaths:stone_running_bond_stairs","securitycraft:briefcase","mcwfences:spruce_wired_fence","railcraft:steel_sword","cfm:white_grill","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","mcwroofs:jungle_planks_steep_roof","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","handcrafted:jungle_dining_bench","cfm:red_cooler","mcwbiomesoplenty:magic_wired_fence","supplementaries:lock_block","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","botania:sextant","securitycraft:laser_block","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","botania:manasteel_boots","mcwfences:modern_andesite_wall","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:raw_iron_block","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","minecraft:shears","croptopia:fruit_smoothie","mcwtrpdoors:metal_warning_trapdoor","mcwroofs:jungle_planks_top_roof","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","mcwfurnitures:jungle_striped_chair","xnet:advanced_connector_yellow_dye","minecolonies:apple_pie","railcraft:steel_shears","additionallanterns:diamond_lantern","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","aquaculture:bobber","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","simplylight:illuminant_brown_block_on_dyed","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","itemcollectors:basic_collector","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","railcraft:steel_axe","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:turn_table","pneumaticcraft:item_life_upgrade","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","buildinggadgets2:gadget_copy_paste","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","aquaculture:oak_fish_mount","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","croptopia:beetroot_salad","allthetweaks:ender_pearl_block","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwroofs:orange_concrete_steep_roof","dyenamics:bed/maroon_bed_frm_white_bed","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwroofs:gutter_base_light_blue","additionallanterns:diorite_lantern","pneumaticcraft:dispenser_upgrade","minecraft:diamond_block","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:deepslate_roof","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","rftoolsutility:inventoryplus_module","mcwfurnitures:jungle_end_table","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","cfm:green_cooler","alltheores:steel_rod","handcrafted:goat_trophy","bigreactors:blasting/graphite_from_coal","mcwpaths:cobbled_deepslate_flagstone","cfm:stripped_crimson_kitchen_sink_light","simplylight:illuminant_lime_block_on_dyed","minecolonies:raw_noodle","minecraft:cooked_mutton","cfm:dark_oak_kitchen_sink_light","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","buildinggadgets2:gadget_exchanging","minecraft:lapis_block","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","utilitarian:snad/snad","minecraft:jukebox","securitycraft:blacklist_module","mcwbiomesoplenty:snowblossom_hedge","sophisticatedstorage:jungle_chest","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","simplylight:illuminant_blue_block_on_toggle","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","occultism:crafting/demons_dream_essence_from_seeds","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","mcwroofs:gutter_base_orange","farmersdelight:cooking/pasta_with_mutton_chop","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwfences:quartz_railing_gate","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","minecolonies:pottage","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:candle_holders/candle_holder_yellow","dyenamics:bed/lavender_bed_frm_white_bed","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwroofs:yellow_concrete_steep_roof","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","delightful:knives/manasteel_knife","mcwlights:mangrove_tiki_torch","aether:iron_pendant","botania:lens_normal","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","simplylight:illuminant_slab_from_panel","cfm:gray_grill","sophisticatedstorage:jungle_barrel","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","xnet:netcable_green","mcwfurnitures:stripped_jungle_coffee_table","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","cfm:stripped_mangrove_mail_box","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","railcraft:steel_helmet","farmersdelight:iron_knife","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","additionallanterns:warped_lantern","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:spruce_boat","handcrafted:jungle_counter","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwfences:deepslate_brick_railing_gate","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","dyenamics:bed/honey_bed_frm_white_bed","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","minecraft:copper_ingot_from_blasting_raw_copper","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","cfm:crimson_mail_box","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","minecraft:iron_nugget","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","farmersdelight:cooking/baked_cod_stew","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","pneumaticcraft:volume_upgrade","aquaculture:dark_oak_fish_mount","botania:travel_belt","allthecompressed:compress/jungle_planks_1x","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","mcwpaths:cobbled_deepslate_flagstone_slab","mcwlights:black_paper_lamp","simplylight:rodlamp","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwroofs:deepslate_attic_roof","aether:iron_helmet_repairing","farmersdelight:steak_and_potatoes","minecraft:iron_sword","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:gutter_base_lime","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","mcwbridges:iron_bridge_pier","cfm:pink_grill","pneumaticcraft:magnet_upgrade","mcwroofs:gutter_middle_orange","mcwroofs:light_blue_concrete_roof","croptopia:date_sapling","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","utilitix:diamond_shears","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwbiomesoplenty:fir_stockade_fence","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwroofs:cyan_concrete_steep_roof","aether:stone_pickaxe_repairing","alltheores:bronze_plate","botania:swap_ring","pneumaticcraft:gilded_upgrade","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","aquaculture:tin_can_to_iron_nugget","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","xnet:connector_yellow","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwlights:soul_oak_tiki_torch","mcwlights:double_street_lamp","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","additionallanterns:quartz_lantern","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","railcraft:steel_gear","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","croptopia:shaped_sticky_toffee_pudding","mcwfurnitures:jungle_drawer_counter","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","delightful:knives/steel_knife","ad_astra:steel_block","xnet:advanced_connector_blue_dye","cfm:spruce_kitchen_sink_light","mcwroofs:cyan_concrete_roof","xnet:netcable_green_dye","minecolonies:fish_n_chips","minecraft:cobblestone_wall_from_cobblestone_stonecutting","xnet:advanced_connector_red_dye","immersiveengineering:crafting/gunpart_drum","minecraft:brown_concrete_powder","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","minecraft:gunpowder","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwlights:wall_lantern","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","pneumaticcraft:entity_tracker_upgrade","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","simplylight:illuminant_yellow_block_toggle","mcwfurnitures:stripped_jungle_glass_table","mcwroofs:magenta_concrete_roof","supplementaries:bed_from_feather_block","immersiveengineering:crafting/component_iron","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","cfm:orange_kitchen_drawer","minecraft:cherry_boat","silentgear:stone_rod","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","immersiveengineering:crafting/stick_steel","wirelesschargers:basic_wireless_block_charger","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:spruce_mail_box","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwlights:pink_paper_lamp","immersiveengineering:crafting/connector_hv","botania:dodge_ring","pneumaticcraft:elytra_upgrade","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","xnet:connector_green_dye","minecraft:baked_potato_from_smoking","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","aether:iron_gloves","mcwfurnitures:stripped_jungle_drawer","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","occultism:crafting/dictionary_of_spirits","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","pneumaticcraft:stomp_upgrade","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","cfm:yellow_grill","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","nethersdelight:blackstone_blast_furnace","aether:aether_iron_nugget_from_smelting","minecraft:diamond_shovel","sfm:cable","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","biomesoplenty:dead_boat","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","railcraft:goggles","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","minecraft:chain","cfm:stripped_acacia_kitchen_drawer","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","minecraft:copper_ingot_from_smelting_raw_copper","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","immersiveengineering:crafting/fertilizer","mcwroofs:gray_concrete_roof","pneumaticcraft:range_upgrade","cfm:magenta_cooler","constructionwand:diamond_wand","securitycraft:sentry","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","minecraft:deepslate","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","minecolonies:meat_ravioli","supplementaries:checker","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","minecraft:crossbow","cfm:cyan_cooler","pneumaticcraft:coordinate_tracker_upgrade","mcwbridges:cobblestone_bridge_pier","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:orange_paper_lamp","mcwlights:iron_triple_candle_holder","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","xnet:advanced_connector_green_dye","immersiveengineering:crafting/shovel_steel","supplementaries:soap","mcwbridges:cobblestone_bridge","travelersbackpack:emerald","cfm:dark_oak_kitchen_drawer","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","utilitix:linked_crystal","immersiveengineering:crafting/sawblade","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwpaths:stone_windmill_weave_stairs","railcraft:steel_shovel","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","mcwroofs:lime_concrete_roof","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","utilitarian:utility/jungle_logs_to_slabs","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","mcwfences:warped_stockade_fence","botania:manasteel_shovel","botania:pump","mcwpaths:cobblestone_square_paving","dyenamics:bed/spring_green_bed","utilitix:filter_rail","cfm:stripped_dark_oak_mail_box","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","railcraft:steel_tunnel_bore_head","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","dyenamics:bed/conifer_bed_frm_white_bed","cfm:stripped_jungle_desk_cabinet","aquaculture:jellyfish_to_slimeball","mcwpaths:cobbled_deepslate_strewn_rocky_path","minecraft:jungle_wood","handcrafted:bench","mcwfences:panelled_metal_fence","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","botania:knockback_belt","additionallanterns:smooth_stone_lantern","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","dyenamics:bed/mint_bed_frm_white_bed","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","supplementaries:candle_holders/candle_holder_white","corail_woodcutter:warped_woodcutter","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","silentgear:upgrade_base","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","minecraft:cooked_salmon","immersiveengineering:crafting/plate_steel_hammering","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:gutter_middle_cyan","bloodmagic:synthetic_point","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","handcrafted:wolf_trophy","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mythicbotany:rune_holder","modularrouters:blank_upgrade","croptopia:shaped_figgy_pudding","allthecompressed:compress/cobbled_deepslate_1x","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","productivetrees:crates/red_delicious_apple_crate_unpack","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","cfm:black_kitchen_sink","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","minecraft:lever","mcwpaths:cobbled_deepslate_dumble_paving","immersiveengineering:crafting/wirecoil_structure_steel","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:willow_pyramid_gate","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","cfm:lime_kitchen_sink","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","mcwlights:cyan_paper_lamp","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:nether_brick","mcwdoors:jungle_waffle_door","farmersdelight:grilled_salmon","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","alltheores:platinum_ingot_from_raw_blasting","cfm:stripped_oak_kitchen_sink_light","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","cfm:oak_kitchen_sink_dark","mcwbiomesoplenty:magic_highley_gate","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","xnet:netcable_blue_dye","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","mcwfences:oak_curved_gate","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","biomesoplenty:palm_boat","handcrafted:bear_trophy","croptopia:apple_juice","cfm:purple_kitchen_sink","cfm:black_cooler","minecraft:jungle_slab","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","cfm:stripped_oak_mail_box","botania:forest_eye","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","dyenamics:bed/aquamarine_bed_frm_white_bed","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","minecraft:brown_dye","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","cfm:lime_cooler","dyenamics:bed/rose_bed_frm_white_bed","mcwroofs:gray_concrete_upper_steep_roof","silentgear:emerald_from_shards","minecolonies:chicken_broth","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwroofs:pink_concrete_lower_roof","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","rftoolsbase:smartwrench","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","cfm:white_kitchen_drawer","farmersdelight:cooking/apple_cider","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","forbidden_arcanus:wooden_blacksmith_gavel","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","securitycraft:floor_trap","xnet:connector_red","mcwdoors:jungle_tropical_door","minecraft:golden_carrot","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","mcwroofs:brown_concrete_lower_roof","deepresonance:machine_frame","travelersbackpack:lapis","mcwdoors:jungle_glass_door","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","xnet:netcable_red_dye","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","sfm:printing_press","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","mcwfences:bamboo_pyramid_gate","minecraft:iron_ingot_from_blasting_raw_iron","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","farmersdelight:onion_crate","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","mcwlights:cross_lantern","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:deepslate_window2","cfm:mangrove_kitchen_drawer","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","mcwroofs:blackstone_top_roof","additionallanterns:diamond_chain","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwwindows:jungle_window2","cfm:gray_cooler","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","cfm:green_kitchen_drawer","minecraft:cooked_beef","mcwtrpdoors:jungle_glass_trapdoor","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","mcwlights:warped_tiki_torch","cfm:light_blue_trampoline","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:personal_world_spike","mcwlights:lava_lamp","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwfences:spruce_stockade_fence","cfm:jungle_table","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","mcwbiomesoplenty:empyreal_pyramid_gate","simplylight:illuminant_green_block_toggle","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","mcwdoors:garage_white_door","cfm:black_grill","botania:lexicon","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","botania:ice_pendant","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:gutter_middle_white","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aether:aether_iron_nugget_from_blasting","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","railcraft:steel_chestplate","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","enderio:fluid_tank","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","xnet:connector_blue","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","mcwbiomesoplenty:maple_highley_gate","cfm:orange_cooler","minecolonies:mint_jelly","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:10985,warning_level:0}}