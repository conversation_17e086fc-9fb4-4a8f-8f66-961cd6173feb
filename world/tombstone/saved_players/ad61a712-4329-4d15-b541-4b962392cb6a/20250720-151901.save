{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:384,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:2259,Id:47,ShowIcon:1b,ShowParticles:1b,"forge:id":"tombstone:ghostly_shape"}],Air:300s,Attributes:[{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Modifiers:[{Amount:0.5d,Name:"effect.tombstone.ghostly_shape 0",Operation:0,UUID:[I;**********,-**********,-**********,**********]}],Name:"forge:swim_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:chainmail_gloves",tag:{Damage:52}},{Count:1b,Slot:1,id:"artifacts:charm_of_sinking"}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:cross_necklace",tag:{CanApplyBonus:1b}}],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[{Count:1b,Slot:0,id:"irons_spellbooks:fireward_ring"}],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"crafting_on_a_stick:crafting_table"}],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:2,CorruptionTimer:809},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:14,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:141},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:0b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{},"rftoolsutility:properties":{allowFlying:0b,buffTicks:40,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["delightful:pumpkin_pie_slice","minecraft:rotten_flesh","minecolonies:mint","minecraft:beef","minecolonies:garlic","minecraft:bread","minecraft:baked_potato"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:4,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:3b,PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;*********,-1536278497,-1945827711,-1989455215]},{FromDim:"minecraft:overworld",FromPos:108301899784259L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;*********,1426604845,-1726604697,-*********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","70e61f07-a350-437a-ae01-15e3ef9a1bab","40348b0e-9441-4434-bda0-75b36e449a83","2d293ddf-66fb-4b60-862a-67f687e6668b"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:-1244,tb_last_ground_location_y:65,tb_last_ground_location_z:2231,tb_last_offhand_item:"minecraft:torch",twilightforest_banished:1b},apoth_reforge_seed:0,"quark:locked_once":1b,"quark:trying_crawl":0b},Health:20.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"minecraft:stone_pickaxe",tag:{Damage:58}},{Count:1b,Slot:1b,id:"minecraft:iron_sword",tag:{Damage:17,affix_data:{affixes:{"apotheosis:durable":0.14f,"apotheosis:sword/attribute/lacerating":0.5231444f,"apotheosis:sword/attribute/spellbreaking":0.7052661f,"apotheosis:sword/attribute/violent":0.68840533f,"apotheosis:sword/special/thunderstruck":0.43175685f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/attribute/violent"},"",{"translate":"affix.apotheosis:sword/special/thunderstruck.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;-**********,**********,-**********,93544638]]},apoth_rchest:1b}},{Count:1b,Slot:2b,id:"silentgear:pickaxe",tag:{Damage:153,SGear_Data:{Construction:{Parts:[{ID:"silentgear:pickaxe_head",Item:{Count:1b,id:"silentgear:pickaxe_head",tag:{Damage:0,Materials:[{ID:"silentgear:iron"}]}}},{ID:"silentgear:rod",Item:{Count:1b,id:"silentgear:rod",tag:{Materials:[{ID:"silentgear:wood"}]}}}]},Properties:{HarvestTier:"minecraft:iron",LockStats:0b,ModVersion:"3.6.6",Stats:{"silentgear:attack_reach":3.0f,"silentgear:attack_speed":-2.8f,"silentgear:charging_value":0.7f,"silentgear:durability":250.0f,"silentgear:enchantment_value":14.0f,"silentgear:harvest_speed":6.0f,"silentgear:magic_damage":1.0f,"silentgear:melee_damage":3.0f,"silentgear:rarity":20.0f,"silentgear:repair_efficiency":1.0f},Traits:[{Level:3b,Name:"silentgear:malleable"},{Level:1b,Name:"silentgear:magnetic"},{Level:2b,Name:"silentgear:flexible"}]},Rendering:{Model:3,ModelKey:"pickaxe:pickaxe_head{iron},rod{wood},"}},SGear_UUID:[I;1812937517,1112163831,-1393588685,-1224139774]}},{Count:1b,Slot:3b,id:"minecraft:iron_pickaxe",tag:{Damage:0}},{Count:1b,Slot:4b,id:"mekanismtools:refined_obsidian_sword",tag:{Damage:16}},{Count:2b,Slot:6b,id:"minecraft:iron_bars"},{Count:37b,Slot:7b,id:"minecraft:bread"},{Count:1b,Slot:8b,id:"mekanismtools:refined_obsidian_pickaxe",tag:{Damage:46}},{Count:1b,Slot:9b,id:"silentgear:sinew"},{Count:6b,Slot:10b,id:"minecraft:mutton"},{Count:1b,Slot:11b,id:"potionsmaster:bezoar"},{Count:3b,Slot:12b,id:"minecraft:white_wool"},{Count:8b,Slot:13b,id:"minecraft:cherry_log"},{Count:6b,Slot:21b,id:"minecraft:gold_ingot"},{Count:9b,Slot:23b,id:"minecraft:potato"},{Count:10b,Slot:31b,id:"minecraft:apple"},{Count:1b,Slot:100b,id:"mekanismtools:refined_obsidian_boots",tag:{Damage:4}},{Count:1b,Slot:101b,id:"mekanismtools:refined_obsidian_leggings",tag:{Damage:4}},{Count:1b,Slot:102b,id:"mekanism:jetpack",tag:{mekData:{GasTanks:[{Tank:0b,stored:{amount:22843L,gasName:"mekanism:hydrogen"}}],mode:1}}},{Count:1b,Slot:103b,id:"mekanismtools:refined_obsidian_helmet",tag:{Damage:4}},{Count:13b,Slot:-106b,id:"minecraft:torch"}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:overworld",pos:[I;-1773,67,2000]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[70.75730092978546d,48.0d,-11.729602594387565d],Railways_DataVersion:2,Rotation:[49.35022f,10.799921f],Score:1071,SelectedItemSlot:5,SleepTimer:0s,Spigot.ticksLived:141,UUID:[I;-1386109166,1126780181,-1254012010,596822890],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":19516331343920L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:0,XpP:0.42857143f,XpSeed:175683731,XpTotal:3,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752912705892L,keepLevel:0b,lastKnownName:"isanKash",lastPlayed:1752995941060L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.0f,foodLevel:20,foodSaturationLevel:5.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","handcrafted:spider_trophy","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","croptopia:shaped_beef_stew","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","minecolonies:cheese_ravioli","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","mcwfurnitures:cherry_chair","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwwindows:oak_plank_pane_window","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","minecraft:glow_item_frame","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","supplementaries:bubble_blower","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwdoors:cherry_bamboo_door","mcwbiomesoplenty:mahogany_plank_pane_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","forbidden_arcanus:deorum_ingot","cfm:oak_kitchen_counter","minecraft:golden_hoe","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","biomesoplenty:palm_chest_boat","mcwfurnitures:stripped_cherry_cupboard_counter","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","mcwpaths:sandstone_running_bond_stairs","minecraft:polished_blackstone_slab","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","mcwtrpdoors:cherry_glass_trapdoor","mcwdoors:cherry_japanese_door","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","farmersdelight:beef_patty_from_campfire_cooking","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwtrpdoors:cherry_barred_trapdoor","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","handcrafted:jungle_side_table","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","cfm:purple_cooler","minecolonies:pasta_tomato","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:pine_plank_window2","xnet:connector_routing","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","mcwbiomesoplenty:palm_plank_window2","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","farmersdelight:barbecue_stick","utilitix:directional_highspeed_rail","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","mcwwindows:granite_pane_window","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","cfm:green_grill","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwwindows:dark_oak_shutter","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:mangrove_chest_boat","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","utilitarian:utility/cherry_logs_to_stairs","mcwroofs:purple_striped_awning","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","minecraft:jungle_trapdoor","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","mcwroofs:cherry_attic_roof","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","mcwfurnitures:stripped_cherry_table","minecraft:iron_ingot_from_nuggets","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","cfm:green_kitchen_sink","mcwfurnitures:cherry_counter","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","botania:petal_green_double","mcwwindows:birch_plank_pane_window","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","mcwfurnitures:stripped_cherry_lower_bookshelf_drawer","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwpaths:sandstone_flagstone_slab","croptopia:potato_chips","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","mcwfurnitures:cherry_double_wardrobe","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwfurnitures:stripped_cherry_bookshelf_drawer","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","twilightforest:twilight_oak_chest_boat","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","minecraft:smooth_sandstone_slab","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","sgjourney:sandstone_with_lapis","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","farmersdelight:cooking/dumplings","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwtrpdoors:cherry_tropical_trapdoor","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","minecraft:smooth_basalt","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwfurnitures:cherry_large_drawer","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","handcrafted:wood_cup","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","xnet:advanced_connector_yellow","xnet:netcable_blue","cfm:stripped_oak_chair","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","minecraft:cooked_beef_from_campfire_cooking","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/axe_steel","botania:dye_green","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwdoors:cherry_four_panel_door","mcwroofs:gutter_base_green","minecraft:blue_dye","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","minecraft:piston","mcwwindows:crimson_planks_four_window","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","croptopia:apple_sapling","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwfurnitures:stripped_cherry_double_wardrobe","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","silentgear:sinew_fiber","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","comforts:sleeping_bag_to_white","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwfurnitures:stripped_cherry_coffee_table","mcwpaths:blackstone_flagstone_path","mcwdoors:print_waffle","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","xnet:antenna","mcwfurnitures:jungle_stool_chair","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwfences:dark_oak_hedge","botania:manasteel_chestplate","botania:magenta_shiny_flower","mcwfurnitures:cherry_bookshelf","mcwfences:bamboo_highley_gate","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","mcwfurnitures:stripped_jungle_stool_chair","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwwindows:white_curtain","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","mcwtrpdoors:cherry_mystic_trapdoor","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","croptopia:carnitas","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","supplementaries:daub_frame","botania:manasteel_sword","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","botania:dreamwood_wand","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","utilitarian:utility/cherry_logs_to_boats","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","railcraft:steel_sword","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwwindows:oak_plank_four_window","mcwroofs:sandstone_lower_roof","handcrafted:jungle_dining_bench","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfurnitures:stripped_cherry_large_drawer","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","mcwdoors:cherry_mystic_door","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwtrpdoors:cherry_paper_trapdoor","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","railcraft:steel_shears","additionallanterns:diamond_lantern","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","utilitarian:utility/cherry_logs_to_doors","securitycraft:reinforced_lime_stained_glass_pane_from_glass","mcwfurnitures:cherry_bookshelf_cupboard","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","mcwroofs:cherry_upper_steep_roof","minecraft:item_frame","itemcollectors:basic_collector","minecraft:loom","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfurnitures:stripped_cherry_chair","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwbiomesoplenty:empyreal_stockade_fence","mcwfurnitures:stripped_cherry_triple_drawer","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","minecraft:polished_blackstone","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwdoors:cherry_beach_door","dyenamics:bed/maroon_bed_frm_white_bed","dyenamics:banner/rose_banner","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","utilitix:experience_crystal","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwpaths:cobbled_deepslate_flagstone","paraglider:paraglider","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","mcwroofs:blue_striped_awning","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","minecraft:composter","minecraft:sandstone_stairs","mcwfurnitures:stripped_cherry_modern_chair","minecraft:jukebox","mcwwindows:jungle_curtain_rod","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_cherry_drawer","domum_ornamentum:cyan_floating_carpet","mcwfurnitures:cherry_modern_chair","mcwwindows:spruce_plank_pane_window","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwwindows:red_curtain","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","occultism:crafting/demons_dream_essence_from_seeds","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","farmersdelight:cooking/pasta_with_mutton_chop","securitycraft:reinforced_light_gray_stained_glass","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwwindows:blackstone_pane_window","mcwdoors:jungle_whispering_door","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","mcwdoors:jungle_stable_head_door","minecolonies:pottage","mcwtrpdoors:cherry_barrel_trapdoor","railcraft:iron_gear","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:stripped_redwood_log_window","cfm:jungle_blinds","tombstone:bone_needle","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_cherry_covered_desk","xnet:netcable_green","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","mcwtrpdoors:cherry_cottage_trapdoor","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","minecraft:gold_block","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","travelersbackpack:gold","minecraft:paper","minecraft:cherry_wood","mcwfences:warped_curved_gate","mcwdoors:oak_stable_door","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","botania:lime_petal_block","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","supplementaries:notice_board","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:acacia_hedge","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","aether:iron_helmet_repairing","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","farmersdelight:steak_and_potatoes","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","mcwwindows:stripped_oak_log_window","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","minecraft:golden_boots","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","supplementaries:flags/flag_cyan","cfm:pink_grill","mcwroofs:gutter_middle_orange","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","croptopia:date_sapling","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","minecraft:cherry_planks","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","alltheores:bronze_plate","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","twilightforest:wood/oak_banister","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","minecraft:glass","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","minecraft:smooth_sandstone_stairs_from_smooth_sandstone_stonecutting","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","cfm:oak_upgraded_fence","deepresonance:radiation_monitor","railcraft:steel_gear","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","mcwfurnitures:stripped_cherry_drawer_counter","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","mcwbiomesoplenty:dead_plank_window2","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","minecraft:gunpowder","minecraft:cyan_stained_glass","mcwroofs:red_concrete_roof","mcwtrpdoors:cherry_barn_trapdoor","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","pylons:potion_filter","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","immersiveengineering:crafting/stick_iron","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","mcwwindows:cyan_curtain","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","aquaculture:heavy_hook","minecraft:white_concrete_powder","biomesoplenty:dead_chest_boat","mcwwindows:acacia_window","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwroofs:orange_concrete_lower_roof","cfm:spruce_mail_box","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:pink_concrete_powder","minecraft:cauldron","mcwbridges:glass_bridge_pier","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","minecraft:cobblestone_slab","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","allthecompressed:compress/glass_1x","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwroofs:gutter_base_red","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","botania:dye_lime","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwlights:iron_framed_torch","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","croptopia:flour","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","cfm:cyan_cooler","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwfences:spruce_picket_fence","travelersbackpack:redstone","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwwindows:iron_shutter","mcwdoors:cherry_paper_door","immersiveengineering:crafting/blueprint_molds","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:sandstone_flagstone","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwbridges:blackstone_bridge_pier","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","botania:pump","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","utilitix:filter_rail","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","minecraft:polished_blackstone_bricks","railcraft:steel_tunnel_bore_head","mcwdoors:cherry_classic_door","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwroofs:oak_lower_roof","utilitarian:utility/cherry_logs_to_pressure_plates","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","minecraft:iron_trapdoor","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","botania:knockback_belt","mcwwindows:mangrove_pane_window","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","handcrafted:white_sheet","securitycraft:reinforced_warped_fence_gate","mcwbiomesoplenty:stripped_jacaranda_pane_window","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfurnitures:cherry_table","mcwroofs:jungle_steep_roof","mcwfurnitures:stripped_cherry_double_drawer","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","bloodmagic:synthetic_point","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfurnitures:cherry_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","mythicbotany:rune_holder","mcwwindows:spruce_plank_four_window","croptopia:shaped_figgy_pudding","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","productivetrees:crates/red_delicious_apple_crate_unpack","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","minecraft:polished_blackstone_pressure_plate","biomesoplenty:blackstone_bulb","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","mcwroofs:cherry_roof","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","farmersdelight:grilled_salmon","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwbiomesoplenty:dead_hedge","handcrafted:wood_bowl","mcwfurnitures:cherry_drawer","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","mcwbridges:balustrade_blackstone_bridge","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","alltheores:iron_dust_from_hammer_crushing","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","mcwfurnitures:stripped_cherry_striped_chair","domum_ornamentum:brown_floating_carpet","mcwdoors:cherry_cottage_door","cfm:stripped_oak_table","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwdoors:cherry_stable_door","mcwroofs:deepslate_upper_lower_roof","cfm:stripped_oak_mail_box","botania:forest_eye","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","minecraft:brown_dye","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","supplementaries:candy","minecolonies:chicken_broth","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwdoors:cherry_barn_glass_door","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","allthecompressed:compress/emerald_block_1x","comforts:hammock_to_white","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","deepresonance:machine_frame","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","xnet:netcable_red_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","alltheores:gold_rod","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","mcwfurnitures:stripped_cherry_bookshelf","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwfurnitures:cherry_double_drawer_counter","supplementaries:key","minecraft:blackstone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","rftoolspower:power_core1","mcwroofs:white_concrete_upper_steep_roof","croptopia:pork_and_beans","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","farmersdelight:onion_crate","create:crafting/kinetics/purple_seat","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","mcwtrpdoors:jungle_mystic_trapdoor","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aether:aether_iron_nugget_from_blasting","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","minecraft:iron_pickaxe","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","cfm:orange_cooler","minecolonies:mint_jelly","minecraft:lime_stained_glass","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwbiomesoplenty:stripped_umbran_log_four_window","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","mcwfurnitures:stripped_cherry_modern_desk","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","minecolonies:baked_salmon","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","minecraft:cooked_salmon_from_campfire_cooking","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","immersiveengineering:crafting/radiator","mcwfurnitures:cherry_modern_desk","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","minecraft:cooked_salmon_from_smoking","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","xnet:connector_red_dye","minecolonies:shapetool","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:bamboo_chest_raft","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","dyenamics:bed/bubblegum_bed_frm_white_bed","mcwtrpdoors:cherry_whispering_trapdoor","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","buildinggadgets2:template_manager","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","aether:blue_ice_freezing","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","cfm:crimson_kitchen_sink_dark","tombstone:ankh_of_prayer","cfm:white_sofa","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","minecraft:polished_basalt","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","mcwdoors:cherry_whispering_door","create:crafting/logistics/powered_latch","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","handcrafted:oak_bench","farmersdelight:cooking/pasta_with_meatballs","mcwfurnitures:cherry_cupboard_counter","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwpaths:blackstone_crystal_floor_path","cfm:jungle_cabinet","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","minecraft:polished_basalt_from_basalt_stonecutting","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","twigs:blackstone_column","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","farmersdelight:cooked_mutton_chops_from_smoking","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:jungle_door","aether:chainmail_gloves_repairing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwbiomesoplenty:jacaranda_horse_fence","mcwwindows:oak_four_window","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","railcraft:world_spike","mcwdoors:garage_gray_door","botania:petal_green","mcwfurnitures:stripped_oak_counter","dyenamics:bed/fluorescent_bed_frm_white_bed","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","minecraft:oak_pressure_plate","immersiveengineering:crafting/steel_scaffolding_standard","cfm:brown_grill","mcwwindows:quartz_window","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecolonies:mint_tea","minecraft:clock","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","twigs:silt","gtceu:shaped/compress_platinum_to_ore_block","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","botania:conversions/green_petal_block_deconstruct","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","minecolonies:pasta_plain","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","rftoolsbase:machine_frame","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","pylons:infusion_pylon","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","croptopia:fruit_salad","securitycraft:block_change_detector","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwbridges:cherry_bridge_pier","cfm:stripped_mangrove_kitchen_drawer","additionallanterns:basalt_chain","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","mcwfurnitures:cherry_double_drawer","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","mcwpaths:red_sand_path_block","cfm:light_blue_cooler","mcwdoors:print_jungle","undergarden:gloom_o_lantern","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","twigs:oak_table","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","botania:conversions/lime_petal_block_deconstruct","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwpaths:blackstone_dumble_paving","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","botania:flower_bag","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","mcwfurnitures:cherry_striped_chair","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","farmersdelight:diamond_knife","minecraft:oak_sign","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","minecraft:smooth_sandstone_slab_from_smooth_sandstone_stonecutting","mcwroofs:gutter_middle_blue","mcwdoors:cherry_glass_door","sfm:manager","mcwroofs:purple_concrete_roof","botania:apothecary_mossy","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","twilightforest:wood/birch_banister","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwpaths:blackstone_diamond_paving","farmersdelight:cooking/hot_cocoa","minecraft:purple_concrete_powder","mcwfurnitures:stripped_cherry_modern_wardrobe","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","mcwfurnitures:cherry_glass_table","mcwfurnitures:stripped_cherry_desk","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwbiomesoplenty:hellbark_pyramid_gate","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","farmersdelight:cooking/noodle_soup","mcwroofs:oak_planks_attic_roof","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwfurnitures:cherry_desk","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","tombstone:fishing_rod_of_misadventure","mcwpaths:cobbled_deepslate_clover_paving","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","mcwfurnitures:cherry_coffee_table","cfm:birch_kitchen_sink_dark","mcwtrpdoors:cherry_four_panel_trapdoor","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","minecraft:cut_sandstone_from_sandstone_stonecutting","create:crafting/logistics/powered_toggle_latch","supplementaries:flags/flag_lime","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","minecraft:cooked_cod","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","securitycraft:alarm","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","minecraft:cooked_porkchop_from_smoking","mcwfurnitures:stripped_cherry_double_drawer_counter","botania:petal_lime_double","mcwroofs:oak_top_roof","mcwpaths:sandstone_honeycomb_paving","additionallanterns:amethyst_lantern","utilitix:crude_furnace","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","mcwfurnitures:cherry_stool_chair","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","mcwwindows:warped_stem_window","mcwfurnitures:cherry_wardrobe","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_orange_block_dyed","minecraft:polished_blackstone_stairs","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","minecraft:golden_helmet","mcwfurnitures:stripped_cherry_bookshelf_cupboard","mcwdoors:cherry_stable_head_door","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:cooked_porkchop_from_campfire_cooking","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwroofs:cherry_steep_roof","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwfurnitures:stripped_cherry_end_table","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","mcwfences:modern_andesite_wall","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","mcwroofs:cherry_upper_lower_roof","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","mcwroofs:jungle_planks_top_roof","cfm:lime_kitchen_drawer","mcwbiomesoplenty:redwood_pane_window","minecolonies:apple_pie","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","alltheores:iron_dust_from_hammer_ingot_crushing","mcwfurnitures:stripped_cherry_glass_table","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwroofs:cherry_top_roof","allthecompressed:compress/basalt_1x","buildinggadgets2:gadget_copy_paste","mcwfurnitures:cherry_modern_wardrobe","mcwfences:modern_stone_brick_wall","utilitarian:utility/cherry_logs_to_slabs","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","croptopia:beetroot_salad","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","mcwfurnitures:cherry_drawer_counter","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","allthecompressed:compress/oak_planks_1x","create:crafting/kinetics/red_seat","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","supplementaries:bamboo_spikes","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","create:crafting/kinetics/orange_seat","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","minecraft:yellow_stained_glass","mcwdoors:cherry_western_door","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","minecolonies:raw_noodle","securitycraft:reinforced_white_stained_glass_pane_from_dye","minecraft:cooked_mutton","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:wheat_dough_from_eggs","mcwbiomesoplenty:willow_pane_window","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","travelersbackpack:pig","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","croptopia:shaped_bacon","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:white_bed","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","sophisticatedstorage:jungle_chest","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","simplylight:illuminant_blue_block_on_toggle","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mcwbiomesoplenty:magic_plank_window2","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","mcwwindows:prismarine_pane_window","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:slice_map","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","blue_skies:trough","botania:lens_normal","mcwfences:end_brick_pillar_wall","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","mcwfurnitures:jungle_modern_chair","undergarden:undergarden_scaffolding","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","cfm:cyan_grill","mcwdoors:cherry_bark_glass_door","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","railcraft:steel_helmet","farmersdelight:iron_knife","mcwfurnitures:stripped_jungle_double_drawer","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","minecraft:cooked_cod_from_campfire_cooking","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","farmersdelight:chicken_sandwich","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwwindows:birch_log_parapet","mcwbridges:sandstone_bridge","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","supplementaries:bellows","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","handcrafted:kitchen_hood","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","farmersdelight:cooked_mutton_chops_from_campfire_cooking","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","minecraft:cooked_porkchop","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","mcwpaths:sandstone_dumble_paving","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwwindows:dark_prismarine_pane_window","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","additionallanterns:blackstone_chain","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwdoors:cherry_tropical_door","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","botania:green_petal_block","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","sgjourney:sandstone_hieroglyphs","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","twigs:birch_table","mcwroofs:magenta_concrete_upper_lower_roof","mcwroofs:cyan_concrete_steep_roof","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","farmersdelight:beef_patty_from_smoking","mcwwindows:white_mosaic_glass_pane","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwdoors:cherry_swamp_door","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","mcwfurnitures:cherry_covered_desk","sophisticatedstorage:packing_tape","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","mcwlights:double_street_lamp","mcwdoors:cherry_japanese2_door","minecolonies:chainmailboots","additionallanterns:quartz_lantern","ad_astra:white_flag","mcwdoors:cherry_nether_door","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","croptopia:shaped_sticky_toffee_pudding","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwdoors:cherry_barn_door","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecolonies:fish_n_chips","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:hay","xnet:advanced_connector_red_dye","mcwdoors:cherry_modern_door","handcrafted:sandstone_pillar_trim","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwfurnitures:cherry_triple_drawer","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:light_blue_concrete_steep_roof","supplementaries:item_shelf","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","silentgear:stone_rod","minecraft:leather_boots","handcrafted:sandstone_corner_trim","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","mcwlights:pink_paper_lamp","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","mcwfurnitures:cherry_lower_triple_drawer","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","minecolonies:supplychestdeployer","aether:iron_gloves","tombstone:white_marble","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","occultism:crafting/dictionary_of_spirits","cfm:oak_table","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","mcwbiomesoplenty:mahogany_highley_gate","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","aether:aether_iron_nugget_from_smelting","sfm:cable","minecraft:hay_block","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","mcwfences:mangrove_highley_gate","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","alltheores:zinc_dust_from_hammer_ingot_crushing","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","railcraft:goggles","twigs:paper_lantern","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","travelersbackpack:iron","botania:petal_lime","minecraft:smooth_sandstone_stairs","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwwindows:granite_louvered_shutter","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","immersiveengineering:crafting/blueprint_bullets","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwwindows:blue_mosaic_glass_pane","minecolonies:meat_ravioli","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwtrpdoors:cherry_beach_trapdoor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwtrpdoors:cherry_classic_trapdoor","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","supplementaries:soap","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","domum_ornamentum:pink_floating_carpet","securitycraft:reinforced_andesite","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","botania:manasteel_shovel","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwpaths:cobbled_deepslate_crystal_floor_slab","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","farmersdelight:beef_patty","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwpaths:blackstone_crystal_floor","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","securitycraft:display_case","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","handcrafted:bench","mcwwindows:diorite_four_window","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","dyenamics:bed/mint_bed_frm_white_bed","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","mcwfurnitures:stripped_cherry_lower_triple_drawer","croptopia:campfire_molasses","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","minecraft:cooked_salmon","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","minecraft:oak_chest_boat","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","mcwfences:modern_mud_brick_wall","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","create:crafting/kinetics/yellow_seat","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","mcwfurnitures:stripped_cherry_stool_chair","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","minecraft:bread","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","minecraft:nether_brick","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:cherry_end_table","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","mcwbiomesoplenty:magic_highley_gate","utilitarian:utility/cherry_logs_to_trapdoors","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","mcwfurnitures:stripped_cherry_counter","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","minecraft:bow","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwroofs:cherry_lower_roof","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","twilightforest:dark_chest_boat","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","dyenamics:bed/aquamarine_bed_frm_white_bed","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwwindows:stripped_spruce_log_four_window","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","dyenamics:bed/rose_bed_frm_white_bed","enderio:enderios","mcwfurnitures:oak_coffee_table","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","mcwwindows:deepslate_pane_window","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwdoors:jungle_tropical_door","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","minecraft:blackstone_stairs","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","domum_ornamentum:cream_stone_bricks","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","mcwdoors:oak_barn_door","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","mcwwindows:blue_mosaic_glass","supplementaries:blackstone_lamp","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","railcraft:receiver_circuit","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","mcwfurnitures:cherry_bookshelf_drawer","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","minecraft:cooked_beef","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwfurnitures:stripped_cherry_wardrobe","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","dyenamics:wine_stained_glass","mcwwindows:black_curtain","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","railcraft:steel_chestplate","alltheores:zinc_dust_from_hammer_crushing","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","farmersdelight:cooked_mutton_chops","mcwpaths:stone_flagstone","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","aether:skyroot_note_block","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"],toBeDisplayed:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","handcrafted:spider_trophy","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","croptopia:shaped_beef_stew","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","minecolonies:cheese_ravioli","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","mcwfurnitures:cherry_chair","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwwindows:oak_plank_pane_window","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","minecraft:glow_item_frame","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","supplementaries:bubble_blower","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwdoors:cherry_bamboo_door","mcwbiomesoplenty:mahogany_plank_pane_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","forbidden_arcanus:deorum_ingot","cfm:oak_kitchen_counter","minecraft:golden_hoe","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","biomesoplenty:palm_chest_boat","mcwfurnitures:stripped_cherry_cupboard_counter","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","mcwpaths:sandstone_running_bond_stairs","minecraft:polished_blackstone_slab","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","mcwtrpdoors:cherry_glass_trapdoor","mcwdoors:cherry_japanese_door","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","farmersdelight:beef_patty_from_campfire_cooking","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwtrpdoors:cherry_barred_trapdoor","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","handcrafted:jungle_side_table","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","cfm:purple_cooler","minecolonies:pasta_tomato","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:pine_plank_window2","xnet:connector_routing","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","mcwbiomesoplenty:palm_plank_window2","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","farmersdelight:barbecue_stick","utilitix:directional_highspeed_rail","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","mcwwindows:granite_pane_window","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","cfm:green_grill","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwwindows:dark_oak_shutter","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:mangrove_chest_boat","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","utilitarian:utility/cherry_logs_to_stairs","mcwroofs:purple_striped_awning","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","minecraft:jungle_trapdoor","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","mcwroofs:cherry_attic_roof","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","mcwfurnitures:stripped_cherry_table","minecraft:iron_ingot_from_nuggets","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","cfm:green_kitchen_sink","mcwfurnitures:cherry_counter","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","botania:petal_green_double","mcwwindows:birch_plank_pane_window","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","mcwfurnitures:stripped_cherry_lower_bookshelf_drawer","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwpaths:sandstone_flagstone_slab","croptopia:potato_chips","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","mcwfurnitures:cherry_double_wardrobe","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwfurnitures:stripped_cherry_bookshelf_drawer","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","twilightforest:twilight_oak_chest_boat","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","minecraft:smooth_sandstone_slab","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","sgjourney:sandstone_with_lapis","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","farmersdelight:cooking/dumplings","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwtrpdoors:cherry_tropical_trapdoor","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","minecraft:smooth_basalt","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwfurnitures:cherry_large_drawer","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","handcrafted:wood_cup","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","xnet:advanced_connector_yellow","xnet:netcable_blue","cfm:stripped_oak_chair","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","minecraft:cooked_beef_from_campfire_cooking","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/axe_steel","botania:dye_green","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwdoors:cherry_four_panel_door","mcwroofs:gutter_base_green","minecraft:blue_dye","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","minecraft:piston","mcwwindows:crimson_planks_four_window","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","croptopia:apple_sapling","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwfurnitures:stripped_cherry_double_wardrobe","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","silentgear:sinew_fiber","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","comforts:sleeping_bag_to_white","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwfurnitures:stripped_cherry_coffee_table","mcwpaths:blackstone_flagstone_path","mcwdoors:print_waffle","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","xnet:antenna","mcwfurnitures:jungle_stool_chair","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwfences:dark_oak_hedge","botania:manasteel_chestplate","botania:magenta_shiny_flower","mcwfurnitures:cherry_bookshelf","mcwfences:bamboo_highley_gate","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","mcwfurnitures:stripped_jungle_stool_chair","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwwindows:white_curtain","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","mcwtrpdoors:cherry_mystic_trapdoor","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","croptopia:carnitas","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","supplementaries:daub_frame","botania:manasteel_sword","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","botania:dreamwood_wand","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","utilitarian:utility/cherry_logs_to_boats","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","railcraft:steel_sword","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwwindows:oak_plank_four_window","mcwroofs:sandstone_lower_roof","handcrafted:jungle_dining_bench","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfurnitures:stripped_cherry_large_drawer","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","mcwdoors:cherry_mystic_door","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwtrpdoors:cherry_paper_trapdoor","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","railcraft:steel_shears","additionallanterns:diamond_lantern","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","utilitarian:utility/cherry_logs_to_doors","securitycraft:reinforced_lime_stained_glass_pane_from_glass","mcwfurnitures:cherry_bookshelf_cupboard","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","mcwroofs:cherry_upper_steep_roof","minecraft:item_frame","itemcollectors:basic_collector","minecraft:loom","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfurnitures:stripped_cherry_chair","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwbiomesoplenty:empyreal_stockade_fence","mcwfurnitures:stripped_cherry_triple_drawer","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","minecraft:polished_blackstone","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwdoors:cherry_beach_door","dyenamics:bed/maroon_bed_frm_white_bed","dyenamics:banner/rose_banner","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","utilitix:experience_crystal","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwpaths:cobbled_deepslate_flagstone","paraglider:paraglider","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","mcwroofs:blue_striped_awning","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","minecraft:composter","minecraft:sandstone_stairs","mcwfurnitures:stripped_cherry_modern_chair","minecraft:jukebox","mcwwindows:jungle_curtain_rod","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_cherry_drawer","domum_ornamentum:cyan_floating_carpet","mcwfurnitures:cherry_modern_chair","mcwwindows:spruce_plank_pane_window","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwwindows:red_curtain","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","occultism:crafting/demons_dream_essence_from_seeds","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","farmersdelight:cooking/pasta_with_mutton_chop","securitycraft:reinforced_light_gray_stained_glass","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwwindows:blackstone_pane_window","mcwdoors:jungle_whispering_door","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","mcwdoors:jungle_stable_head_door","minecolonies:pottage","mcwtrpdoors:cherry_barrel_trapdoor","railcraft:iron_gear","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:stripped_redwood_log_window","cfm:jungle_blinds","tombstone:bone_needle","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_cherry_covered_desk","xnet:netcable_green","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","mcwtrpdoors:cherry_cottage_trapdoor","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","minecraft:gold_block","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","travelersbackpack:gold","minecraft:paper","minecraft:cherry_wood","mcwfences:warped_curved_gate","mcwdoors:oak_stable_door","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","botania:lime_petal_block","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","supplementaries:notice_board","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:acacia_hedge","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","aether:iron_helmet_repairing","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","farmersdelight:steak_and_potatoes","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","mcwwindows:stripped_oak_log_window","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","minecraft:golden_boots","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","supplementaries:flags/flag_cyan","cfm:pink_grill","mcwroofs:gutter_middle_orange","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","croptopia:date_sapling","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","minecraft:cherry_planks","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","alltheores:bronze_plate","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","twilightforest:wood/oak_banister","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","minecraft:glass","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","minecraft:smooth_sandstone_stairs_from_smooth_sandstone_stonecutting","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","cfm:oak_upgraded_fence","deepresonance:radiation_monitor","railcraft:steel_gear","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","mcwfurnitures:stripped_cherry_drawer_counter","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","mcwbiomesoplenty:dead_plank_window2","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","minecraft:gunpowder","minecraft:cyan_stained_glass","mcwroofs:red_concrete_roof","mcwtrpdoors:cherry_barn_trapdoor","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","pylons:potion_filter","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","immersiveengineering:crafting/stick_iron","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","mcwwindows:cyan_curtain","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","aquaculture:heavy_hook","minecraft:white_concrete_powder","biomesoplenty:dead_chest_boat","mcwwindows:acacia_window","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwroofs:orange_concrete_lower_roof","cfm:spruce_mail_box","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:pink_concrete_powder","minecraft:cauldron","mcwbridges:glass_bridge_pier","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","minecraft:cobblestone_slab","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","allthecompressed:compress/glass_1x","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwroofs:gutter_base_red","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","botania:dye_lime","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwlights:iron_framed_torch","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","croptopia:flour","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","cfm:cyan_cooler","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwfences:spruce_picket_fence","travelersbackpack:redstone","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwwindows:iron_shutter","mcwdoors:cherry_paper_door","immersiveengineering:crafting/blueprint_molds","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:sandstone_flagstone","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwbridges:blackstone_bridge_pier","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","botania:pump","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","utilitix:filter_rail","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","minecraft:polished_blackstone_bricks","railcraft:steel_tunnel_bore_head","mcwdoors:cherry_classic_door","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwroofs:oak_lower_roof","utilitarian:utility/cherry_logs_to_pressure_plates","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","minecraft:iron_trapdoor","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","botania:knockback_belt","mcwwindows:mangrove_pane_window","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","handcrafted:white_sheet","securitycraft:reinforced_warped_fence_gate","mcwbiomesoplenty:stripped_jacaranda_pane_window","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfurnitures:cherry_table","mcwroofs:jungle_steep_roof","mcwfurnitures:stripped_cherry_double_drawer","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","bloodmagic:synthetic_point","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfurnitures:cherry_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","mythicbotany:rune_holder","mcwwindows:spruce_plank_four_window","croptopia:shaped_figgy_pudding","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","productivetrees:crates/red_delicious_apple_crate_unpack","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","minecraft:polished_blackstone_pressure_plate","biomesoplenty:blackstone_bulb","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","mcwroofs:cherry_roof","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","farmersdelight:grilled_salmon","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwbiomesoplenty:dead_hedge","handcrafted:wood_bowl","mcwfurnitures:cherry_drawer","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","mcwbridges:balustrade_blackstone_bridge","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","alltheores:iron_dust_from_hammer_crushing","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","mcwfurnitures:stripped_cherry_striped_chair","domum_ornamentum:brown_floating_carpet","mcwdoors:cherry_cottage_door","cfm:stripped_oak_table","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwdoors:cherry_stable_door","mcwroofs:deepslate_upper_lower_roof","cfm:stripped_oak_mail_box","botania:forest_eye","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","minecraft:brown_dye","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","supplementaries:candy","minecolonies:chicken_broth","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwdoors:cherry_barn_glass_door","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","allthecompressed:compress/emerald_block_1x","comforts:hammock_to_white","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","deepresonance:machine_frame","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","xnet:netcable_red_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","alltheores:gold_rod","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","mcwfurnitures:stripped_cherry_bookshelf","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwfurnitures:cherry_double_drawer_counter","supplementaries:key","minecraft:blackstone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","rftoolspower:power_core1","mcwroofs:white_concrete_upper_steep_roof","croptopia:pork_and_beans","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","farmersdelight:onion_crate","create:crafting/kinetics/purple_seat","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","mcwtrpdoors:jungle_mystic_trapdoor","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aether:aether_iron_nugget_from_blasting","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","minecraft:iron_pickaxe","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","cfm:orange_cooler","minecolonies:mint_jelly","minecraft:lime_stained_glass","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwbiomesoplenty:stripped_umbran_log_four_window","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","mcwfurnitures:stripped_cherry_modern_desk","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","minecolonies:baked_salmon","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","minecraft:cooked_salmon_from_campfire_cooking","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","immersiveengineering:crafting/radiator","mcwfurnitures:cherry_modern_desk","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","minecraft:cooked_salmon_from_smoking","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","xnet:connector_red_dye","minecolonies:shapetool","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:bamboo_chest_raft","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","dyenamics:bed/bubblegum_bed_frm_white_bed","mcwtrpdoors:cherry_whispering_trapdoor","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","buildinggadgets2:template_manager","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","aether:blue_ice_freezing","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","cfm:crimson_kitchen_sink_dark","tombstone:ankh_of_prayer","cfm:white_sofa","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","minecraft:polished_basalt","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","mcwdoors:cherry_whispering_door","create:crafting/logistics/powered_latch","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","handcrafted:oak_bench","farmersdelight:cooking/pasta_with_meatballs","mcwfurnitures:cherry_cupboard_counter","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwpaths:blackstone_crystal_floor_path","cfm:jungle_cabinet","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","minecraft:polished_basalt_from_basalt_stonecutting","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","twigs:blackstone_column","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","farmersdelight:cooked_mutton_chops_from_smoking","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:jungle_door","aether:chainmail_gloves_repairing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwbiomesoplenty:jacaranda_horse_fence","mcwwindows:oak_four_window","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","railcraft:world_spike","mcwdoors:garage_gray_door","botania:petal_green","mcwfurnitures:stripped_oak_counter","dyenamics:bed/fluorescent_bed_frm_white_bed","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","minecraft:oak_pressure_plate","immersiveengineering:crafting/steel_scaffolding_standard","cfm:brown_grill","mcwwindows:quartz_window","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecolonies:mint_tea","minecraft:clock","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","twigs:silt","gtceu:shaped/compress_platinum_to_ore_block","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","botania:conversions/green_petal_block_deconstruct","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","minecolonies:pasta_plain","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","rftoolsbase:machine_frame","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","pylons:infusion_pylon","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","croptopia:fruit_salad","securitycraft:block_change_detector","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwbridges:cherry_bridge_pier","cfm:stripped_mangrove_kitchen_drawer","additionallanterns:basalt_chain","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","mcwfurnitures:cherry_double_drawer","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","mcwpaths:red_sand_path_block","cfm:light_blue_cooler","mcwdoors:print_jungle","undergarden:gloom_o_lantern","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","twigs:oak_table","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","botania:conversions/lime_petal_block_deconstruct","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwpaths:blackstone_dumble_paving","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","botania:flower_bag","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","mcwfurnitures:cherry_striped_chair","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","farmersdelight:diamond_knife","minecraft:oak_sign","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","minecraft:smooth_sandstone_slab_from_smooth_sandstone_stonecutting","mcwroofs:gutter_middle_blue","mcwdoors:cherry_glass_door","sfm:manager","mcwroofs:purple_concrete_roof","botania:apothecary_mossy","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","twilightforest:wood/birch_banister","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwpaths:blackstone_diamond_paving","farmersdelight:cooking/hot_cocoa","minecraft:purple_concrete_powder","mcwfurnitures:stripped_cherry_modern_wardrobe","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","mcwfurnitures:cherry_glass_table","mcwfurnitures:stripped_cherry_desk","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwbiomesoplenty:hellbark_pyramid_gate","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","farmersdelight:cooking/noodle_soup","mcwroofs:oak_planks_attic_roof","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwfurnitures:cherry_desk","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","tombstone:fishing_rod_of_misadventure","mcwpaths:cobbled_deepslate_clover_paving","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","mcwfurnitures:cherry_coffee_table","cfm:birch_kitchen_sink_dark","mcwtrpdoors:cherry_four_panel_trapdoor","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","minecraft:cut_sandstone_from_sandstone_stonecutting","create:crafting/logistics/powered_toggle_latch","supplementaries:flags/flag_lime","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","minecraft:cooked_cod","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","securitycraft:alarm","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","minecraft:cooked_porkchop_from_smoking","mcwfurnitures:stripped_cherry_double_drawer_counter","botania:petal_lime_double","mcwroofs:oak_top_roof","mcwpaths:sandstone_honeycomb_paving","additionallanterns:amethyst_lantern","utilitix:crude_furnace","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","mcwfurnitures:cherry_stool_chair","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","mcwwindows:warped_stem_window","mcwfurnitures:cherry_wardrobe","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_orange_block_dyed","minecraft:polished_blackstone_stairs","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","minecraft:golden_helmet","mcwfurnitures:stripped_cherry_bookshelf_cupboard","mcwdoors:cherry_stable_head_door","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:cooked_porkchop_from_campfire_cooking","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwroofs:cherry_steep_roof","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwfurnitures:stripped_cherry_end_table","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","mcwfences:modern_andesite_wall","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","mcwroofs:cherry_upper_lower_roof","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","mcwroofs:jungle_planks_top_roof","cfm:lime_kitchen_drawer","mcwbiomesoplenty:redwood_pane_window","minecolonies:apple_pie","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","alltheores:iron_dust_from_hammer_ingot_crushing","mcwfurnitures:stripped_cherry_glass_table","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwroofs:cherry_top_roof","allthecompressed:compress/basalt_1x","buildinggadgets2:gadget_copy_paste","mcwfurnitures:cherry_modern_wardrobe","mcwfences:modern_stone_brick_wall","utilitarian:utility/cherry_logs_to_slabs","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","croptopia:beetroot_salad","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","mcwfurnitures:cherry_drawer_counter","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","allthecompressed:compress/oak_planks_1x","create:crafting/kinetics/red_seat","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","supplementaries:bamboo_spikes","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","create:crafting/kinetics/orange_seat","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","minecraft:yellow_stained_glass","mcwdoors:cherry_western_door","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","minecolonies:raw_noodle","securitycraft:reinforced_white_stained_glass_pane_from_dye","minecraft:cooked_mutton","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:wheat_dough_from_eggs","mcwbiomesoplenty:willow_pane_window","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","travelersbackpack:pig","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","croptopia:shaped_bacon","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:white_bed","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","sophisticatedstorage:jungle_chest","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","simplylight:illuminant_blue_block_on_toggle","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mcwbiomesoplenty:magic_plank_window2","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","mcwwindows:prismarine_pane_window","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:slice_map","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","blue_skies:trough","botania:lens_normal","mcwfences:end_brick_pillar_wall","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","mcwfurnitures:jungle_modern_chair","undergarden:undergarden_scaffolding","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","cfm:cyan_grill","mcwdoors:cherry_bark_glass_door","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","railcraft:steel_helmet","farmersdelight:iron_knife","mcwfurnitures:stripped_jungle_double_drawer","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","minecraft:cooked_cod_from_campfire_cooking","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","farmersdelight:chicken_sandwich","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwwindows:birch_log_parapet","mcwbridges:sandstone_bridge","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","supplementaries:bellows","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","handcrafted:kitchen_hood","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","farmersdelight:cooked_mutton_chops_from_campfire_cooking","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","minecraft:cooked_porkchop","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","mcwpaths:sandstone_dumble_paving","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwwindows:dark_prismarine_pane_window","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","additionallanterns:blackstone_chain","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwdoors:cherry_tropical_door","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","botania:green_petal_block","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","sgjourney:sandstone_hieroglyphs","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","twigs:birch_table","mcwroofs:magenta_concrete_upper_lower_roof","mcwroofs:cyan_concrete_steep_roof","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","farmersdelight:beef_patty_from_smoking","mcwwindows:white_mosaic_glass_pane","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwdoors:cherry_swamp_door","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","mcwfurnitures:cherry_covered_desk","sophisticatedstorage:packing_tape","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","mcwlights:double_street_lamp","mcwdoors:cherry_japanese2_door","minecolonies:chainmailboots","additionallanterns:quartz_lantern","ad_astra:white_flag","mcwdoors:cherry_nether_door","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","croptopia:shaped_sticky_toffee_pudding","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwdoors:cherry_barn_door","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecolonies:fish_n_chips","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:hay","xnet:advanced_connector_red_dye","mcwdoors:cherry_modern_door","handcrafted:sandstone_pillar_trim","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwfurnitures:cherry_triple_drawer","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:light_blue_concrete_steep_roof","supplementaries:item_shelf","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","silentgear:stone_rod","minecraft:leather_boots","handcrafted:sandstone_corner_trim","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","mcwlights:pink_paper_lamp","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","mcwfurnitures:cherry_lower_triple_drawer","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","minecolonies:supplychestdeployer","aether:iron_gloves","tombstone:white_marble","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","occultism:crafting/dictionary_of_spirits","cfm:oak_table","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","mcwbiomesoplenty:mahogany_highley_gate","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","aether:aether_iron_nugget_from_smelting","sfm:cable","minecraft:hay_block","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","mcwfences:mangrove_highley_gate","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","alltheores:zinc_dust_from_hammer_ingot_crushing","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","railcraft:goggles","twigs:paper_lantern","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","travelersbackpack:iron","botania:petal_lime","minecraft:smooth_sandstone_stairs","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwwindows:granite_louvered_shutter","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","immersiveengineering:crafting/blueprint_bullets","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwwindows:blue_mosaic_glass_pane","minecolonies:meat_ravioli","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwtrpdoors:cherry_beach_trapdoor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwtrpdoors:cherry_classic_trapdoor","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","supplementaries:soap","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","domum_ornamentum:pink_floating_carpet","securitycraft:reinforced_andesite","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","botania:manasteel_shovel","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwpaths:cobbled_deepslate_crystal_floor_slab","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","farmersdelight:beef_patty","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwpaths:blackstone_crystal_floor","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","securitycraft:display_case","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","handcrafted:bench","mcwwindows:diorite_four_window","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","dyenamics:bed/mint_bed_frm_white_bed","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","mcwfurnitures:stripped_cherry_lower_triple_drawer","croptopia:campfire_molasses","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","minecraft:cooked_salmon","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","minecraft:oak_chest_boat","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","mcwfences:modern_mud_brick_wall","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","create:crafting/kinetics/yellow_seat","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","mcwfurnitures:stripped_cherry_stool_chair","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","minecraft:bread","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","minecraft:nether_brick","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:cherry_end_table","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","mcwbiomesoplenty:magic_highley_gate","utilitarian:utility/cherry_logs_to_trapdoors","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","mcwfurnitures:stripped_cherry_counter","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","minecraft:bow","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwroofs:cherry_lower_roof","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","twilightforest:dark_chest_boat","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","dyenamics:bed/aquamarine_bed_frm_white_bed","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwwindows:stripped_spruce_log_four_window","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","dyenamics:bed/rose_bed_frm_white_bed","enderio:enderios","mcwfurnitures:oak_coffee_table","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","mcwwindows:deepslate_pane_window","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwdoors:jungle_tropical_door","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","minecraft:blackstone_stairs","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","domum_ornamentum:cream_stone_bricks","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","mcwdoors:oak_barn_door","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","mcwwindows:blue_mosaic_glass","supplementaries:blackstone_lamp","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","railcraft:receiver_circuit","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","mcwfurnitures:cherry_bookshelf_drawer","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","minecraft:cooked_beef","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwfurnitures:stripped_cherry_wardrobe","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","dyenamics:wine_stained_glass","mcwwindows:black_curtain","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","railcraft:steel_chestplate","alltheores:zinc_dust_from_hammer_crushing","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","farmersdelight:cooked_mutton_chops","mcwpaths:stone_flagstone","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","aether:skyroot_note_block","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:3997,warning_level:0}}