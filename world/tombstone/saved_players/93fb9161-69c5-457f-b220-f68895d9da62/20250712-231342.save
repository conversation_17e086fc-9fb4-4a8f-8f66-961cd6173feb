{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:1b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:392,Id:43,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:knowledge"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:292,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:392,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],Air:300s,Attributes:[{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.5d,Name:"attributeslib:crit_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:1.0d,Name:"eidolon:magic_power"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:1.0d,Modifiers:[{Amount:16.0d,Name:"effect.attributeslib.knowledge 1",Operation:2,UUID:[I;1432915503,2109230347,-1131352079,-1806406652]}],Name:"attributeslib:experience_gained"},{Base:0.0d,Name:"attributeslib:armor_shred"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:0.0d,Name:"attributeslib:prot_pierce"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"attributeslib:current_hp_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:20.0d,Modifiers:[{Amount:40.0d,Name:"Heart Containers",Operation:0,UUID:[I;-**********,-990297069,-**********,-847674717]},{Amount:8.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"attributeslib:overheal"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"attributeslib:cold_damage"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.05d,Name:"attributeslib:crit_chance"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:4.0d,Modifiers:[{Amount:0.4d,Name:"artifacts:feral_claws_attack_speed_bonus",Operation:0,UUID:[I;-1626820019,-1493154539,-1848454353,1821068796]}],Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"attributeslib:life_steal"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.0d,Name:"minecraft:generic.luck"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;**********,-95206743,-**********,-274118390],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:antidote_vessel"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:chorus_totem"},{Count:1b,Slot:1,id:"sophisticatedbackpacks:diamond_backpack",tag:{contentsUuid:[I;598115586,-907000104,-2095761619,910168030],inventorySlots:108,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}]},upgradeSlots:5}}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"travelersbackpack:bat",tag:{RenderInfo:{Capacity:3000,LeftTank:{Amount:0,FluidName:"minecraft:empty"},RightTank:{Amount:0,FluidName:"minecraft:empty"}},StorageSlots:27,Tier:0,ToolSlots:2,UpgradeSlots:2,Upgrades:{Items:[{Count:1b,Slot:0,id:"travelersbackpack:tanks_upgrade"},{Count:1b,Slot:1,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],Size:2}}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:feral_claws"},{Count:1b,Slot:1,id:"artifacts:onion_ring"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:kitty_slippers"}],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"crafting_on_a_stick:crafting_table"}],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:netherite_gloves",tag:{Damage:321,Enchantments:[{id:"minecraft:unbreaking",lvl:4s}]}}],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:160,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:534968},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:6908},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:3000},vessels:{essences:0,heartContainers:20,staminaVessels:10}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:netherite_upgrade_smithing_template",ItemStack:{Count:2b,id:"minecraft:netherite_upgrade_smithing_template"}}],SelectedRecipe:"minecraft:netherite_upgrade_smithing_template"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:173,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:cooked_rabbit","minecraft:baked_potato","croptopia:toast","minecraft:cooked_chicken","minecraft:cooked_porkchop","minecraft:bread","minecraft:rotten_flesh","minecraft:glow_berries","minecraft:chorus_fruit","allthemodium:ancient_soulberries","minecraft:golden_apple","minecraft:cooked_beef","integrateddynamics:menril_berries","maidensmerrymaking:sweet_potato","minecraft:rabbit_stew","minecraft:mushroom_stew","croptopia:kiwi","minecolonies:garlic","blue_skies:cooked_horizofin_tunid","maidensmerrymaking:chocolate_bunny","gtceu:purple_drink"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:30s,knowledge:38,perks:[{id:10s,level:5b}]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:3b,PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:overworld",FromPos:-223475740831582L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-29137058447294L,UID:[I;1719655610,*********,-1137110787,-*********]},{FromDim:"minecraft:the_nether",FromPos:-29137058447294L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-65420946681793L,UID:[I;-*********,-*********,-1206985890,-*********]}],TrashSlot:{Count:1b,id:"sophisticatedbackpacks:jukebox_upgrade",tag:{discFinishTime:2894170L,discInventory:{Items:[{Count:1b,Slot:0,id:"minecraft:music_disc_mall"}],Size:1},discLength:3940L,discSlotActive:-1,isPlaying:0b}},WaystonesData:{Waystones:["a7ec193b-d141-4664-ba7a-0f1d05513d75","4f5f98b0-b346-45b6-83f1-59fad95c9199","c7e04059-1629-4eac-a477-0e59e345a88f","2ed386e9-5730-4aff-816d-209261d26e15","d46866c9-573a-4143-bbd2-61c5bb0783db","935f926c-25fe-49fd-9683-4de002d130ab","266a6564-8196-425c-af59-4242a982e8fd","ea4c9b90-7090-4f28-b882-958af543dab3","34be36ed-7328-464c-b508-76e4236cb4fd","88320f8d-ea59-4cc6-896d-79cc52d4aa80","554f78ea-838c-43ca-b9c6-39637449ef4a","36d86fd7-e43f-48b5-a2b9-6969a1ba515c","d1811b83-cc8c-4821-80c6-3e5ce576ae62"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-235,tb_last_ground_location_y:253,tb_last_ground_location_z:-1178,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},"apoth.affix_cooldown.apotheosis:armor/mob_effect/blinding":2886590L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/nimble":2880660L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/revitalizing":2880660L,"apoth.affix_cooldown.apotheosis:sword/mob_effect/elusive":2904111L,apoth_reforge_seed:0,"quark:locked_once":1b,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedBackpackSettings:{},sophisticatedStorageSettings:{}},Health:75.0f,HurtByTimestamp:434150,HurtTime:0s,Inventory:[{Count:1b,Slot:1b,id:"minecraft:netherite_sword",tag:{Damage:193,Enchantments:[{id:"minecraft:looting",lvl:4s},{id:"tombstone:sanctified",lvl:6s},{id:"minecraft:fire_aspect",lvl:2s}],affix_data:{affixes:{"apotheosis:durable":0.27f,"apotheosis:sword/attribute/glacial":0.075672865f,"apotheosis:sword/attribute/intricate":0.82484186f,"apotheosis:sword/attribute/vampiric":0.0920704f,"apotheosis:sword/special/festive":0.25693315f},name:'{"color":"#BB00BB","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/special/festive"},"",{"translate":"affix.apotheosis:sword/attribute/intricate.suffix"}]}',rarity:"apotheosis:epic",sockets:2,uuids:[[I;-**********,**********,-**********,-181297930]]}}},{Count:1b,Slot:2b,id:"minecraft:diamond_pickaxe",tag:{Damage:494}},{Count:23b,Slot:3b,id:"minecraft:cherry_log"},{Count:12b,Slot:4b,id:"ae2:fluix_glass_cable"},{Count:1b,Slot:6b,id:"ae2:terminal"},{Count:47b,Slot:7b,id:"minecraft:netherite_upgrade_smithing_template"},{Count:1b,Slot:8b,id:"pipez:wrench"},{Count:1b,Slot:9b,id:"minecraft:diamond_sword",tag:{Damage:56,Enchantments:[{id:"naturesaura:aura_mending",lvl:1s},{id:"minecraft:sharpness",lvl:6s},{id:"minecraft:unbreaking",lvl:6s},{id:"enderio:xp_boost",lvl:5s},{id:"evilcraft:life_stealing",lvl:4s}],affix_data:{affixes:{"apotheosis:durable":0.37f,"apotheosis:sword/attribute/elongated":0.7957111f,"apotheosis:sword/attribute/spellbreaking":0.1984185f,"apotheosis:sword/mob_effect/elusive":0.12400502f,"apotheosis:sword/special/festive":0.19284827f,"irons_spellbooks:sword/attribute/spell_power":0.39207053f},name:'{"color":"#BB00BB","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/attribute/elongated"},"",{"translate":"affix.apotheosis:sword/special/festive.suffix"}]}',rarity:"apotheosis:epic",sockets:3,uuids:[[I;-1103106639,-1049081578,-1323305959,9275021]]}}},{Count:1b,Slot:10b,id:"minecraft:netherite_sword",tag:{Damage:639,Enchantments:[{id:"minecraft:bane_of_arthropods",lvl:6s},{id:"minecraft:sharpness",lvl:5s},{id:"reliquary:severing",lvl:5s},{id:"ensorcellation:soulbound",lvl:1s}]}},{Count:1b,Slot:100b,id:"minecraft:netherite_boots",tag:{Damage:92,Enchantments:[{id:"minecraft:unbreaking",lvl:4s},{id:"minecraft:fire_protection",lvl:5s}],affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.83991164f,"apotheosis:armor/attribute/elastic":0.014244497f,"apotheosis:armor/dmg_reduction/feathery":0.25003046f,"apotheosis:armor/mob_effect/nimble":0.22149348f,"apotheosis:durable":0.42f,"irons_spellbooks:armor/attribute/cooldown":0.5306866f,"irons_spellbooks:armor/attribute/spell_resist":0.25140357f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist"},"",{"translate":"affix.apotheosis:armor/attribute/elastic.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;864448555,-1097249982,-1511363975,-357856700]]}}},{Count:1b,Slot:101b,id:"minecraft:netherite_leggings",tag:{Damage:184,Enchantments:[{id:"minecraft:projectile_protection",lvl:6s},{id:"ensorcellation:soulbound",lvl:1s}],affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.81819254f,"apotheosis:armor/attribute/windswept":0.3628621f,"apotheosis:armor/dmg_reduction/blast_forged":0.1758756f,"apotheosis:armor/mob_effect/revitalizing":0.40314913f,"apotheosis:durable":0.32f,"irons_spellbooks:armor/attribute/spell_power":0.5899373f,"irons_spellbooks:armor/attribute/spell_resist":0.950843f},name:'{"color":"#BB00BB","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist"},"",{"translate":"affix.apotheosis:armor/mob_effect/revitalizing.suffix"}]}',rarity:"apotheosis:epic",sockets:3,uuids:[[I;-1328962222,638864131,-1794721548,1604250239]]}}},{Count:1b,Slot:102b,id:"minecraft:netherite_chestplate",tag:{Damage:241,Enchantments:[{id:"minecraft:unbreaking",lvl:4s},{id:"naturesaura:aura_mending",lvl:1s},{id:"ars_nouveau:mana_regen",lvl:4s}]}},{Count:1b,Slot:103b,id:"minecraft:netherite_helmet",tag:{Damage:103,Enchantments:[{id:"minecraft:protection",lvl:4s},{id:"tombstone:spectral_conjurer",lvl:5s},{id:"minecraft:unbreaking",lvl:4s}],affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.19558f,"apotheosis:armor/attribute/ironforged":0.9333135f,"apotheosis:armor/dmg_reduction/runed":0.20818198f,"apotheosis:armor/mob_effect/blinding":0.80696386f,"apotheosis:durable":0.33f,"irons_spellbooks:armor/attribute/cooldown":0.6605909f,"irons_spellbooks:armor/attribute/mana":0.9136297f},name:'{"color":"#BB00BB","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",{"translate":"affix.apotheosis:armor/mob_effect/blinding.suffix"}]}',rarity:"apotheosis:epic",sockets:2,uuids:[[I;-584726374,-1064942982,-1905768611,-1751872652]]}}}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:overworld",pos:[I;-12488,67,-24098]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-222.48864764526502d,253.0d,-1177.901496144342d],Railways_DataVersion:2,Rotation:[120.59956f,6.900935f],Score:9911,SelectedItemSlot:0,SleepTimer:0s,SpawnAngle:90.60791f,SpawnDimension:"allthemodium:mining",SpawnForced:0b,SpawnX:-246,SpawnY:253,SpawnZ:-1181,Spigot.ticksLived:533211,UUID:[I;-1812229791,1774536063,-1306462584,-1780884894],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-61022900166403L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:34,XpP:0.6217379f,XpSeed:1383143253,XpTotal:1989,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752079625174L,keepLevel:0b,lastKnownName:"sphe",lastPlayed:1752333222379L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.14259243f,foodLevel:13,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["botania:metamorphic_fungal_stone_wall","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwbiomesoplenty:jacaranda_window2","minecraft:stonecutter","littlelogistics:seater_barge","mcwroofs:blue_terracotta_attic_roof","alltheores:gold_dust_from_hammer_crushing","minecraft:bone_block","mcwbiomesoplenty:willow_waffle_door","mcwpaths:cobbled_deepslate_crystal_floor_path","croptopia:shaped_beef_stew","pneumaticcraft:wall_lamp_white","mcwroofs:red_terracotta_lower_roof","ad_astra:cyan_industrial_lamp","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","create:oak_window","minecraft:gold_nugget_from_blasting","minecolonies:chainmailchestplate","aether:chainmail_leggings_repairing","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","minecraft:cobblestone_wall","dyenamics:maroon_candle","immersiveengineering:crafting/plate_iron_hammering","minecraft:lime_stained_glass_pane","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","reliquary:mob_charm_fragments/cave_spider","integrateddynamics:crafting/part_display_panel","alltheores:brass_plate","botania:metamorphic_forest_bricks_slab","railcraft:controller_circuit","integrateddynamics:crafting/variable_transformer_input","mcwroofs:gray_top_roof","create:crafting/logistics/brass_tunnel","sophisticatedstorage:copper_to_iron_tier_upgrade","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwbiomesoplenty:redwood_nether_door","mcwroofs:gray_terracotta_roof","mcwlights:covered_lantern","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","minecraft:sandstone_slab_from_sandstone_stonecutting","modularrouters:bulk_item_filter","mcwbiomesoplenty:empyreal_classic_door","biomesoplenty:mossy_black_sand","immersiveengineering:crafting/windmill_blade","mcwbiomesoplenty:willow_highley_gate","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","supplementaries:candle_holders/candle_holder_white_dye","minecraft:cherry_door","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","dyenamics:spring_green_wool","railcraft:standard_rail_from_rail","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","travelersbackpack:diamond_tier_upgrade","minecraft:cyan_concrete_powder","cfm:oak_kitchen_counter","minecraft:golden_hoe","minecraft:fire_charge","cfm:purple_trampoline","dyenamics:cherenkov_wool","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","minecraft:dye_green_bed","botania:floating_tigerseye","railcraft:any_detector","computercraft:printer","mcwroofs:stone_bricks_upper_steep_roof","advanced_ae:reactionchamber","minecraft:stone_button","mcwfurnitures:stripped_cherry_cupboard_counter","mcwroofs:cyan_concrete_lower_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","farmingforblockheads:market","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:umbran_japanese2_door","occultism:crafting/magic_lamp_empty","silentgear:shield_template","minecraft:purpur_block","travelersbackpack:dye_lime_sleeping_bag","croptopia:soy_sauce","railcraft:brass_ingot_crafted_with_ingots","megacells:crafting/greater_energy_card","minecraft:polished_blackstone_slab","domum_ornamentum:blue_brick_extra","simplylight:illuminant_light_blue_block_toggle","botania:metamorphic_desert_stone_slab","aether:red_cape","mcwtrpdoors:cherry_glass_trapdoor","pneumaticcraft:transfer_gadget","mcwdoors:cherry_japanese_door","silentgear:blaze_gold_block","minecraft:gray_concrete_powder","dyenamics:lavender_dye","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwtrpdoors:cherry_barred_trapdoor","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:crafting/kinetics/mechanical_roller","chemlib:silver_ingot_from_blasting_silver_dust","blue_skies:glowing_blinding_stone","handcrafted:jungle_side_table","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","minecraft:lime_candle","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","mcwbiomesoplenty:umbran_classic_door","cfm:purple_cooler","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","littlelogistics:barrel_barge","mcwroofs:yellow_terracotta_steep_roof","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:hellbark_bark_glass_door","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","supplementaries:candle_holders/candle_holder_red_dye","blue_skies:glowing_nature_stone","immersiveengineering:crafting/charging_station","silentgear:saw_template","megacells:network/mega_interface","mcwbiomesoplenty:mahogany_drawer_counter","productivebees:stonecutter/palm_canvas_expansion_box","mcwbiomesoplenty:umbran_cottage_door","mcwbiomesoplenty:mahogany_classic_trapdoor","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","croptopia:kiwi_seed","pneumaticcraft:wall_lamp_inverted_white","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","create:crafting/kinetics/chute","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","dyenamics:fluorescent_candle","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","rftoolsutility:crafter1","farmersdelight:milk_bottle","mcwroofs:oak_roof","aether:crossbow_repairing","minecraft:iron_ingot_from_iron_block","handcrafted:blue_cushion","mcwroofs:light_gray_roof_slab","mcwfurnitures:jungle_drawer","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","mcwroofs:thatch_roof","mcwbridges:rope_jungle_bridge","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","dyenamics:lavender_candle","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","silentgear:katana_template","sophisticatedstorage:storage_output","connectedglass:clear_glass_cyan2","botania:metamorphic_forest_cobblestone_stairs","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","mcwfurnitures:stripped_cherry_table","minecraft:iron_ingot_from_nuggets","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","modularrouters:security_upgrade","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_bookshelf","productivebees:stonecutter/mangrove_canvas_hive","mcwpaths:cobblestone_basket_weave_paving","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","supplementaries:evilcraft/sign_post_undead","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwlights:golden_chandelier","minecraft:armor_stand","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","cfm:stripped_birch_kitchen_drawer","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","twigs:chiseled_bricks","enderio:stone_gear","silentgear:blaze_gold_dust_blasting","ae2:network/parts/panels_monitor","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwroofs:nether_bricks_upper_lower_roof","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","mcwlights:tavern_wall_lantern","croptopia:cheese","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","sophisticatedstorage:backpack_stack_upgrade_tier_4_from_storage_stack_upgrade_tier_5","create:cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwfences:railing_end_brick_wall","reliquary:void_tear","mcwpaths:sandstone_flagstone_slab","croptopia:potato_chips","mcwfurnitures:stripped_oak_lower_triple_drawer","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","handcrafted:jungle_pillar_trim","mcwbiomesoplenty:mahogany_large_drawer","mcwbiomesoplenty:empyreal_beach_door","mcwroofs:red_concrete_lower_roof","ae2:block_cutter/slabs/fluix_slab","mcwfences:warped_pyramid_gate","mcwfurnitures:stripped_cherry_bookshelf_drawer","mcwdoors:jungle_stable_door","mcwbiomesoplenty:pine_window2","evilcraft:special/vengeance_pickaxe","mcwwindows:blackstone_four_window","mcwbiomesoplenty:mahogany_swamp_door","mcwlights:cherry_tiki_torch","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","rftoolsbuilder:vehicle_builder","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","create:small_calcite_brick_slab_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:stripped_mahogany_modern_wardrobe","mcwbiomesoplenty:dead_plank_pane_window","mcwbiomesoplenty:stripped_palm_log_window2","mcwwindows:blackstone_brick_gothic","sophisticatedstorage:cherry_chest","mcwlights:soul_double_street_lamp","minecraft:smooth_sandstone_slab","botania:chiseled_metamorphic_mesa_bricks","minecraft:rail","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","minecraft:diamond_sword","mcwbiomesoplenty:empyreal_cottage_door","botania:metamorphic_mountain_cobblestone_slab","modularrouters:blank_module","mcwbiomesoplenty:mahogany_lower_triple_drawer","sgjourney:sandstone_with_lapis","farmersdelight:cooking/dumplings","mcwlights:upgraded_torch","cfm:jungle_park_bench","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","forbidden_arcanus:clibano_core","alltheores:netherite_dust_from_hammer_crushing","botania:metamorphic_plains_cobblestone_wall","minecraft:polished_blackstone_from_blackstone_stonecutting","mcwpaths:brick_running_bond","mcwwindows:dark_oak_window","mininggadgets:upgrade_size_2","mininggadgets:upgrade_size_3","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_bricks_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","botania:metamorphic_plains_stone_slab","silentgear:fishing_rod_template","mcwfurnitures:cherry_large_drawer","dyenamics:amber_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_pink","handcrafted:terracotta_wide_pot","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:jacaranda_nether_door","mcwwindows:lime_curtain","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","ae2:network/blocks/crystal_processing_growth_accelerator","mcwfurnitures:oak_double_wardrobe","evilcraft:crafting/broom_part_cap_bare","minecraft:iron_nugget_from_blasting","railcraft:iron_tank_wall","pneumaticcraft:liquid_hopper","twilightforest:mining_boat","aether:golden_dart","mcwroofs:white_concrete_roof","additionallanterns:normal_nether_bricks_chain","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","mininggadgets:upgrade_size_1","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:fir_bark_glass_door","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","botania:chiseled_metamorphic_swamp_bricks","biomesoplenty:maple_chest_boat","evilcraft:crafting/undead_planks","ae2:network/parts/terminals_crafting","cfm:stripped_oak_chair","xnet:netcable_blue","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwwindows:oak_shutter","cfm:rock_path","connectedglass:scratched_glass_black2","connectedglass:clear_glass_yellow2","minecraft:cooked_beef_from_campfire_cooking","mcwbiomesoplenty:magic_plank_pane_window","botania:floating_labellia","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","ae2:tools/certus_quartz_pickaxe","minecraft:dye_red_wool","mcwroofs:nether_bricks_top_roof","productivebees:stonecutter/jungle_canvas_hive","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:nether_bricks_lower_roof","ad_astra:black_industrial_lamp","mcwroofs:cyan_concrete_top_roof","mcwbiomesoplenty:willow_barn_door","aquaculture:stone_fillet_knife","handcrafted:jungle_couch","mcwbiomesoplenty:magic_stockade_fence","mcwbridges:blackstone_bridge","mcwdoors:cherry_four_panel_door","mcwroofs:gutter_base_green","fluxnetworks:fluxblock","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","mininggadgets:upgrade_efficiency_1","mininggadgets:upgrade_efficiency_3","mininggadgets:upgrade_efficiency_2","mininggadgets:upgrade_efficiency_5","mininggadgets:upgrade_efficiency_4","securitycraft:reinforced_pink_stained_glass","twigs:allium_paper_lantern","farmersdelight:organic_compost_from_rotten_flesh","mcwwindows:crimson_planks_four_window","mcwroofs:cherry_planks_steep_roof","mcwlights:white_lamp","mcwbiomesoplenty:maple_waffle_door","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:mahogany_blossom_trapdoor","mcwbiomesoplenty:umbran_plank_pane_window","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","mcwbiomesoplenty:fir_stable_door","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","mcwbiomesoplenty:palm_classic_door","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","supplementaries:daub_brace","delightful:food/cooking/rock_candy","mcwfurnitures:stripped_cherry_double_wardrobe","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","ad_astra:small_white_industrial_lamp","computercraft:speaker","mcwwindows:mangrove_louvered_shutter","create:crafting/appliances/clipboard","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:palm_tropical_door","handcrafted:creeper_trophy","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","minecraft:dye_blue_wool","mcwbiomesoplenty:palm_cottage_door","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","farmingforblockheads:red_fertilizer","mcwfurnitures:stripped_cherry_coffee_table","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","silentgear:crimson_iron_dust","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","create:crafting/curiosities/minecart_coupling","mcwfences:granite_grass_topped_wall","silentgear:sword_template","securitycraft:reinforced_crimson_fence_gate","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","bigreactors:reactor/basic/controller_ingots_yellorium","mcwroofs:light_gray_steep_roof","dyenamics:persimmon_candle","minecraft:red_banner","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","matc:tertium_essence","mcwfences:oak_stockade_fence","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","croptopia:shaped_water_bottle","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","pylons:expulsion_pylon","mcwfences:bamboo_highley_gate","alltheores:brass_rod","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","mcwroofs:jungle_roof","mcwpaths:andesite_running_bond","minecraft:torch","minecraft:polished_granite_stairs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","mcwtrpdoors:cherry_mystic_trapdoor","create:crafting/kinetics/black_seat","mcwpaths:brick_crystal_floor_path","connectedglass:clear_glass_lime_pane2","croptopia:carnitas","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","railcraft:signal_tuner","cfm:pink_kitchen_counter","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwbiomesoplenty:dead_bark_glass_door","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:cherenkov_dye","twilightforest:time_boat","cfm:dye_black_picket_gate","minecraft:sandstone_wall","aether:skyroot_grindstone","mcwbiomesoplenty:fir_japanese_door","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","minecraft:cherry_slab","cfm:white_grill","ad_astra:wrench","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","connectedglass:scratched_glass_red2","mininggadgets:upgrade_magnet","mcwroofs:sandstone_lower_roof","handcrafted:jungle_dining_bench","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfurnitures:stripped_cherry_large_drawer","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","mcwpaths:sandstone_basket_weave_paving","sophisticatedstorage:stack_upgrade_tier_1","sophisticatedstorage:stack_upgrade_tier_2","sophisticatedstorage:stack_upgrade_tier_3","reliquary:mob_charm_fragments/blaze","mcwwindows:stripped_acacia_log_four_window","silentgear:crimson_iron_raw_ore_blasting","sophisticatedstorage:stack_upgrade_tier_4","sophisticatedstorage:stack_upgrade_tier_5","mcwtrpdoors:cherry_paper_trapdoor","mcwbiomesoplenty:dead_paper_door","domum_ornamentum:green_floating_carpet","mcwbiomesoplenty:magic_beach_door","rftoolspower:blazing_agitator","mcwwindows:spruce_curtain_rod","create:crafting/kinetics/andesite_door","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","aether:netherite_helmet_repairing","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:stripped_willow_log_window","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","railcraft:track_undercutter","silentgear:arrow_template","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwbiomesoplenty:palm_nether_door","enderio:dark_steel_nugget","simplylight:illuminant_purple_block_on_dyed","cfm:blue_sofa","mcwbiomesoplenty:fir_western_door","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","botania:floating_manastar","mcwfurnitures:jungle_striped_chair","dyenamics:honey_wool","additionallanterns:diamond_lantern","railcraft:steel_shears","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","railways:crafting/track_switch_andesite","utilitarian:utility/cherry_logs_to_doors","connectedglass:borderless_glass_lime_pane2","mcwfurnitures:cherry_bookshelf_cupboard","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","minecraft:compass","mcwroofs:cherry_upper_steep_roof","bigreactors:energizer/computerport","allthemodium:allthemodium_apple","minecraft:loom","mininggadgets:upgrade_fortune_3","mininggadgets:upgrade_fortune_2","railcraft:steel_axe","mininggadgets:upgrade_fortune_1","ad_astra:small_lime_industrial_lamp","domum_ornamentum:red_brick_extra","sophisticatedstorage:stack_downgrade_tier_3","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","dyenamics:wine_wool","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","silentgear:ring_template","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","botania:chiseled_metamorphic_taiga_bricks","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwfurnitures:stripped_cherry_triple_drawer","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_white_stained_glass_pane_from_glass","botania:metamorphic_mesa_cobblestone_wall","minecraft:polished_blackstone","handcrafted:blue_crockery_combo","botania:floating_hyacidus","mcwdoors:cherry_beach_door","botania:floating_tangleberrie","modularrouters:pushing_augment","dyenamics:bed/maroon_bed_frm_white_bed","travelersbackpack:dye_red_sleeping_bag","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","minecraft:spire_armor_trim_smithing_template","mcwfences:warped_wired_fence","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","additionallanterns:diorite_lantern","botania:metamorphic_mountain_bricks_wall","mcwbiomesoplenty:pine_window","mcwroofs:stone_top_roof","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","forbidden_arcanus:blacksmith_gavel_head","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfurnitures:stripped_oak_chair","paraglider:paraglider","dankstorage:dock","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","botania:metamorphic_plains_stone_stairs","utilitix:anvil_cart","immersiveengineering:crafting/component_steel","botania:metamorphic_mountain_cobblestone_stairs","mcwtrpdoors:oak_barrel_trapdoor","aether:golden_helmet_repairing","alchemistry:dissolver","mcwfences:mangrove_hedge","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","mcwbiomesoplenty:mahogany_chair","mcwbiomesoplenty:mahogany_planks_upper_steep_roof","ae2:decorative/quartz_vibrant_glass","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_barn_glass_door","mcwbiomesoplenty:mahogany_waffle_door","silentgear:rod_template","mcwbiomesoplenty:empyreal_stable_head_door","botania:metamorphic_taiga_stone_wall","mcwbiomesoplenty:mahogany_glass_table","minecraft:chest_minecart","mcwbiomesoplenty:mahogany_top_roof","ae2:network/cables/covered_orange","mcwwindows:red_curtain","create:crafting/logistics/brass_funnel","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","mcwroofs:white_upper_lower_roof","minecraft:magenta_terracotta","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwroofs:red_terracotta_top_roof","matc:crystals/inferium","mcwroofs:lime_concrete_lower_roof","occultism:crafting/demons_dream_essence_from_seeds","handcrafted:white_cushion","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","mcwroofs:gray_terracotta_attic_roof","mcwroofs:gutter_base_orange","aether:bow_repairing","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwroofs:bricks_attic_roof","enderio:resetting_lever_three_hundred_from_inv","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","reliquary:uncrafting/spider_eye","simplylight:illuminant_yellow_block_dyed","occultism:crafting/brush","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","mcwbiomesoplenty:dead_waffle_door","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","sophisticatedstorage:chipped/loom_table_upgrade","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwlights:mangrove_tiki_torch","securitycraft:redstone_module","mcwbiomesoplenty:jacaranda_plank_window","mcwbiomesoplenty:stripped_redwood_log_window","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","botania:metamorphic_swamp_stone_wall","mcwbiomesoplenty:dead_swamp_door","botania:metamorphic_desert_bricks_wall","railcraft:signal_lamp","cfm:jungle_blinds","createaddition:crafting/rolling_mill","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","xnet:netcable_green","mcwbiomesoplenty:redwood_classic_door","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","pneumaticcraft:paper_from_tag_filter","utilitarian:utility/glow_ink_sac","create:crafting/kinetics/spout","sophisticatedstorage:jungle_limited_barrel_2","bigreactors:reactor/basic/glass","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","minecraft:orange_terracotta","mcwroofs:white_top_roof","reliquary:holy_hand_grenade","mcwbiomesoplenty:magic_waffle_door","aquaculture:gold_fillet_knife","mcwbiomesoplenty:redwood_cottage_door","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","ae2:network/cables/dense_smart_cyan","pneumaticcraft:compressed_bricks_from_tile","utilitix:stone_wall_stonecutting","travelersbackpack:warden","minecraft:cherry_fence_gate","ad_astra:gas_tank","dyenamics:fluorescent_concrete_powder","securitycraft:reinforced_cherry_fence","additionallanterns:copper_lantern","mcwroofs:lime_terracotta_steep_roof","mcwbiomesoplenty:willow_paper_door","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","cfm:crimson_mail_box","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwfences:acacia_curved_gate","ae2:network/parts/panels_semi_dark_monitor","mysticalagriculture:infusion_pedestal","rftoolsutility:screen_controller","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:ender_cart","mcwbiomesoplenty:redwood_swamp_door","nethersdelight:hoglin_sirloin","silentgear:sickle_template","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","ad_astra:iron_plateblock","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:jacaranda_barn_door","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","minecraft:iron_block","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwdoors:acacia_beach_door","mcwroofs:gutter_base","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","immersiveengineering:crafting/treated_wood_vertical_from_horizontal","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","alltheores:platinum_plate","aether:iron_helmet_repairing","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","mcwpaths:sandstone_crystal_floor_stairs","pneumaticcraft:wall_lamp_inverted_blue","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","twigs:cherry_table","minecraft:orange_stained_glass","biomesoplenty:mahogany_trapdoor","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","botania:metamorphic_swamp_bricks_stairs","mcwpaths:sandstone_strewn_rocky_path","mcwbiomesoplenty:maple_four_window","mcwwindows:stripped_oak_log_window","minecraft:golden_boots","minecraft:pink_dye_from_red_white_dye","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","rftoolspower:blazing_generator","supplementaries:flags/flag_cyan","immersiveengineering:crafting/tinted_glass_lead_wire","reliquary:wraith_node","cfm:pink_grill","minecraft:polished_granite_slab","mcwroofs:gutter_middle_orange","croptopia:scrambled_eggs","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","mcwroofs:jungle_attic_roof","waystones:waystone","minecraft:cherry_planks","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","mcwbiomesoplenty:maple_stable_head_door","minecraft:polished_blackstone_brick_stairs","mcwroofs:white_terracotta_upper_steep_roof","securitycraft:reinforced_piston","create:crafting/kinetics/mechanical_saw","create:crafting/logistics/andesite_funnel","bigreactors:reactor/basic/redstoneport","mcwpaths:brick_crystal_floor_slab","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","utilitix:reinforced_piston_controller_rail","botania:metamorphic_mountain_stone_stairs","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","mcwpaths:blackstone_basket_weave_paving","modularrouters:player_module","minecraft:glass","mcwbiomesoplenty:pine_classic_door","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:brown_concrete_attic_roof","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwbiomesoplenty:pine_cottage_door","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","ae2:network/cables/smart_red","dyenamics:persimmon_concrete_powder","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","mcwwindows:light_blue_mosaic_glass","ad_astra:yellow_industrial_lamp","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","mob_grinding_utils:recipe_mob_swab","twigs:mossy_cobblestone_bricks","railcraft:steel_gear","ad_astra:encased_iron_block","sophisticatedstorage:crafting_upgrade","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","minecraft:stone_bricks","securitycraft:reinforced_red_stained_glass","ae2:tools/network_color_applicator","mcwfurnitures:stripped_cherry_drawer_counter","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","mininggadgets:upgrade_freezing","mcwbiomesoplenty:maple_plank_window2","biomesoplenty:mahogany_fence","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:jacaranda_bamboo_door","mcwbiomesoplenty:dead_plank_window2","botania:quartz_lavender","mysticalagriculture:prosperity_gemstone","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","mcwroofs:red_concrete_roof","mcwtrpdoors:cherry_barn_trapdoor","ae2:network/cables/smart_fluix","enderio:resetting_lever_thirty","integrateddynamics:crafting/variable","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","mininggadgets:modificationtable","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","pneumaticcraft:wall_lamp_light_gray","minecraft:dye_yellow_wool","create:crafting/kinetics/contraption_controls","dyenamics:amber_concrete_powder","mcwroofs:light_gray_concrete_lower_roof","bigreactors:crafting/raw_yellorium_component_to_storage","supplementaries:candle_holders/candle_holder_black_dye","nethersdelight:soul_compost_from_hoglin_hide","farmersdelight:cooking/vegetable_noodles","botania:apothecary_desert","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwbiomesoplenty:hellbark_plank_window2","mcwroofs:lime_terracotta_upper_steep_roof","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","minecraft:smooth_stone_slab","minecraft:magenta_dye_from_allium","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","botania:azulejo_0","minecraft:dye_lime_wool","mcwbiomesoplenty:fir_tropical_door","connectedglass:borderless_glass_white2","aquaculture:heavy_hook","mcwwindows:acacia_window","croptopia:food_press","blue_skies:frostbright_bookshelf","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","mcwfurnitures:oak_chair","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","silentgear:material_grader","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","quark:building/crafting/jungle_ladder","croptopia:shaped_milk_bottle","farmersdelight:nether_salad","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","mcwlights:orange_lamp","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","pneumaticcraft:reinforced_brick_pillar","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","mcwpaths:brick_crystal_floor","biomesoplenty:mahogany_sign","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","botania:metamorphic_desert_cobblestone_slab","aquaculture:fishing_line","comforts:hammock_to_red","mcwbiomesoplenty:stripped_jacaranda_log_window","sophisticatedstorage:cherry_limited_barrel_2","sophisticatedstorage:cherry_limited_barrel_1","mcwfurnitures:stripped_jungle_drawer","sophisticatedstorage:cherry_limited_barrel_4","sophisticatedstorage:cherry_limited_barrel_3","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","railcraft:charge_terminal","mcwpaths:blackstone_crystal_floor_slab","minecraft:end_stone_brick_slab","dyenamics:conifer_wool","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","botania:floating_orechid_ignem","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","domum_ornamentum:red_floating_carpet","immersiveengineering:crafting/alu_fence","minecraft:diamond_shovel","mcwwindows:end_brick_gothic","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:jungle_planks_upper_lower_roof","twigs:polished_calcite_bricks_from_calcite_stonecutting","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","dyenamics:mint_wool","connectedglass:borderless_glass_green2","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","alltheores:diamond_plate","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwroofs:oak_upper_lower_roof","bigreactors:reprocessor/casing","botania:metamorphic_fungal_cobblestone_stairs","productivebees:stonecutter/rosewood_canvas_hive","additionallanterns:end_stone_chain","modularrouters:camouflage_upgrade","connectedglass:clear_glass_lime2","silentgear:bracelet_template","farmersdelight:cooking/mushroom_rice","draconicevolution:components/draconium_core","minecraft:golden_shovel","modularrouters:energy_upgrade","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","mcwbiomesoplenty:hellbark_stable_head_door","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","ae2:materials/cardspeed","mcwroofs:gutter_base_red","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:receiver_component","securitycraft:reinforced_tinted_glass","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","mcwfences:sandstone_pillar_wall","minecraft:diamond_chestplate","securitycraft:reinforced_gray_stained_glass","mcwbiomesoplenty:stripped_maple_log_window2","cfm:dye_black_picket_fence","aether:wooden_sword_repairing","mcwlights:wall_lamp","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","mcwbiomesoplenty:jacaranda_mystic_door","supplementaries:hourglass","everythingcopper:copper_leggings","pneumaticcraft:reinforced_brick_tile","mcwbridges:andesite_bridge_stair","mcwroofs:lime_terracotta_attic_roof","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","minecraft:orange_dye_from_red_yellow","simplylight:illuminant_blue_block_on_dyed","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","railcraft:bushing_gear_brass","mcwwindows:crimson_planks_window","immersiveengineering:crafting/breaker_switch","allthecompressed:compress/amethyst_block_1x","botania:metamorphic_forest_cobblestone_wall","mcwdoors:acacia_modern_door","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","silentgear:very_crude_repair_kit","create:small_calcite_brick_stairs_from_stone_types_calcite_stonecutting","mcwwindows:stripped_jungle_log_four_window","dyenamics:bubblegum_candle","create:crafting/kinetics/weighted_ejector","mcwlights:iron_triple_candle_holder","mcwroofs:pink_terracotta_roof","travelersbackpack:redstone","minecraft:tnt_minecart","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","immersiveengineering:crafting/shovel_steel","botania:metamorphic_taiga_cobblestone_wall","mcwbiomesoplenty:fir_nether_door","minecraft:cherry_button","evilcraft:crafting/blood_extractor","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","travelersbackpack:emerald","mininggadgets:upgrade_battery_2","farmersdelight:cutting_board","croptopia:trail_mix","mininggadgets:upgrade_battery_3","mininggadgets:upgrade_battery_1","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:sandstone_flagstone","mcwbiomesoplenty:empyreal_japanese2_door","botania:metamorphic_forest_stone_slab","cfm:dye_yellow_picket_gate","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","rftoolspower:coalgenerator","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwpaths:blackstone_clover_paving","botania:chiseled_metamorphic_desert_bricks","railcraft:track_layer","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","minecraft:white_carpet","ae2:network/cables/covered_blue","dyenamics:maroon_dye","utilitix:filter_rail","aether:diamond_gloves","travelersbackpack:blank_upgrade","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwbiomesoplenty:dead_stable_door","minecraft:cracked_stone_bricks","railcraft:steel_tunnel_bore_head","mcwroofs:oak_lower_roof","utilitarian:utility/cherry_logs_to_pressure_plates","silentgear:elytra_template","mcwbiomesoplenty:willow_plank_pane_window","securitycraft:crystal_quartz_item","travelersbackpack:dye_green_sleeping_bag","dimstorage:dim_wall","botania:metamorphic_taiga_bricks_stairs","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","modularrouters:void_module","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","dankstorage:2_to_3","additionallanterns:dark_prismarine_lantern","twigs:polished_amethyst","mcwwindows:mangrove_plank_window2","mysticalagriculture:imperium_essence_uncraft","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","botania:conversions/blazeblock_deconstruct","integratedtunnels:crafting/part_exporter_item","mcwlights:acacia_tiki_torch","biomesoplenty:mahogany_door","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","securitycraft:reinforced_warped_fence_gate","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/gearshift","mcwfences:oak_horse_fence","caupona:clay_cistern","botania:floating_exoflame","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfurnitures:cherry_table","minecraft:light_gray_dye_from_gray_white_dye","mcwfurnitures:stripped_cherry_double_drawer","mcwfences:blackstone_pillar_wall","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","deepresonance:lens","croptopia:doughnut","mcwlights:square_wall_lamp","mcwfurnitures:cherry_lower_bookshelf_drawer","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","handcrafted:cherry_table","terralith:dispenser_alt","botania:blue_shiny_flower","mcwbiomesoplenty:mahogany_double_drawer_counter","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","allthecompressed:compress/cobbled_deepslate_1x","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","ae2:tools/fluix_axe","cfm:black_kitchen_sink","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","botania:metamorphic_mountain_stone_slab","evilcraft:crafting/broom_part_rod_netherrack","cfm:dye_cyan_picket_gate","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwroofs:pink_concrete_top_roof","travelersbackpack:blue_sleeping_bag","forbidden_arcanus:sanity_meter","mcwbiomesoplenty:hellbark_japanese_door","mcwlights:glowstone_slab","modularrouters:energy_output_module","mcwbiomesoplenty:stripped_mahogany_stool_chair","mcwbiomesoplenty:mahogany_log_bridge_middle","handcrafted:vindicator_trophy","immersiveengineering:crafting/turntable","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","enderio:resetting_lever_three_hundred_inv_from_base","reliquary:uncrafting/string","minecraft:polished_blackstone_pressure_plate","biomesoplenty:blackstone_bulb","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alltheores:diamond_rod","minecraft:stone_slab_from_stone_stonecutting","mcwroofs:cherry_roof","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","silentgear:crimson_iron_dust_blasting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","botania:floating_jiyuulia","littlelogistics:locomotive_route","connectedglass:clear_glass_white2","securitycraft:universal_block_remover","mcwbiomesoplenty:stripped_mahogany_coffee_table","securitycraft:reinforced_bookshelf","minecraft:netherite_leggings_smithing","enderio:resetting_lever_sixty","mcwfences:quartz_grass_topped_wall","matc:crystals/prudentium","create:crafting/kinetics/sticky_mechanical_piston","minecraft:stone_brick_stairs_from_stone_stonecutting","mcwroofs:brown_concrete_top_roof","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","create:cut_dripstone_bricks_from_stone_types_dripstone_stonecutting","handcrafted:wood_bowl","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","mcwroofs:white_roof","mcwbiomesoplenty:fir_glass_door","connectedglass:scratched_glass_yellow_pane2","mcwwindows:stripped_oak_log_window2","create:crafting/kinetics/gantry_shaft","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","immersiveengineering:crafting/treated_wood_horizontal_to_slab","delightful:cantaloupe_slice","mcwbridges:balustrade_blackstone_bridge","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","modularrouters:range_down_from_up","delightful:food/cooking/stuffed_cantaloupe","minecraft:lapis_lazuli","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","mcwwindows:stripped_mangrove_log_window2","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","rftoolsbuilder:yellow_shield_template_block","farmersdelight:safety_net","domum_ornamentum:brown_floating_carpet","mcwroofs:lime_concrete_top_roof","sophisticatedstorage:stack_upgrade_tier_1_plus","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","reliquary:uncrafting/bone","utilitarian:no_soliciting/restraining_order","immersiveengineering:crafting/furnace_heater","mcwbiomesoplenty:stripped_mahogany_bookshelf_cupboard","pneumaticcraft:wall_lamp_brown","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","aether:wooden_hoe_repairing","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_paper_door","sophisticatedstorage:storage_pickup_upgrade_from_backpack_pickup_upgrade","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","rftoolsbuilder:shape_card_quarry_clear_fortune","mcwpaths:andesite_crystal_floor","mcwbiomesoplenty:empyreal_stable_door","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","railcraft:diamond_spike_maul","additionallanterns:granite_lantern","mcwdoors:cherry_barn_glass_door","minecraft:white_candle","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_bricks_stonecutting","mcwwindows:spruce_plank_window2","mcwwindows:lime_mosaic_glass_pane","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","comforts:hammock_to_white","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","create:crafting/kinetics/mechanical_bearing","mcwbiomesoplenty:stripped_mahogany_bookshelf","minecraft:golden_carrot","ae2:network/cells/fluid_cell_housing","mcwbiomesoplenty:hellbark_window","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","productivebees:stonecutter/magic_canvas_hive","mcwbiomesoplenty:stripped_empyreal_log_window2","create:crafting/kinetics/elevator_pulley","dyenamics:honey_stained_glass","additionallanterns:smooth_stone_chain","bigreactors:blasting/graphite_from_coalblock","mcwfurnitures:stripped_cherry_bookshelf","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","mcwroofs:green_terracotta_upper_steep_roof","minecraft:magma_cream","create:crafting/kinetics/attribute_filter","handcrafted:white_bowl","supplementaries:key","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","rftoolspower:power_core1","computercraft:cable","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","additionallanterns:stone_chain","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","aether:skyroot_chest_boat","ae2:misc/deconstruction_certus_quartz_block","minecraft:cherry_sign","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","farmersdelight:onion_crate","create:crafting/kinetics/purple_seat","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwfurnitures:stripped_oak_striped_chair","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","mcwpaths:brick_windmill_weave_stairs","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","create:polished_cut_calcite_stairs_from_stone_types_calcite_stonecutting","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","mininggadgets:upgrade_light_placer","mcwbiomesoplenty:dead_barn_door","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","minecraft:black_stained_glass_pane_from_glass_pane","modularrouters:pickup_delay_augment","pneumaticcraft:wall_lamp_inverted_green","create:cut_calcite_brick_slab_from_stone_types_calcite_stonecutting","twigs:gravel_bricks","dyenamics:persimmon_stained_glass","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","botania:apothecary_swamp","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","silentgear:guide_book","mcwroofs:oak_planks_upper_lower_roof","minecraft:cherry_trapdoor","toolbelt:pouch","silentgear:grip_template","domum_ornamentum:light_gray_floating_carpet","bigreactors:energizer/powerport_fe","megacells:crafting/greater_energy_card_upgraded","immersiveengineering:crafting/stick_aluminum","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","dyenamics:bubblegum_concrete_powder","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","minecraft:cracked_nether_bricks","ae2:tools/matter_cannon","mcwbiomesoplenty:fir_four_panel_door","supplementaries:slingshot","mcwdoors:acacia_japanese2_door","bigreactors:reactor/basic/casing_recycle_glass","twigs:cobblestone_from_pebble","everythingcopper:copper_rail","cfm:black_grill","pneumaticcraft:wall_lamp_inverted_black","create:small_calcite_bricks_from_stone_types_calcite_stonecutting","simplylight:illuminant_block","evilcraft:crafting/blood_infuser","mcwbridges:cherry_log_bridge_middle","blue_skies:glowing_nature_stonebrick_from_glowstone","dyenamics:dye_ultramarine_carpet","ae2:network/cables/glass_red","fluxnetworks:fluxplug","mcwbiomesoplenty:empyreal_four_panel_door","croptopia:shaped_kiwi_sorbet","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","dyenamics:rose_candle","aether:aether_iron_nugget_from_blasting","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mysticalagriculture:essence/minecraft/cherry_log","minecraft:iron_pickaxe","aether:shield_repairing","ae2:shaped/not_so_mysterious_cube","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","rftoolsutility:matter_booster","ae2:network/cables/glass_orange","minecraft:vibranium_mage_chestplate_smithing","ae2:materials/cardinverter","mcwfences:modern_diorite_wall","pipez:wrench","dyenamics:bed/wine_bed","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","simplylight:illuminant_black_block_on_dyed","mcwbiomesoplenty:mahogany_glass_trapdoor","mcwfences:spruce_highley_gate","botania:metamorphic_taiga_cobblestone_stairs","mcwbiomesoplenty:redwood_stable_head_door","dyenamics:aquamarine_candle","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","mcwdoors:jungle_barn_door","mcwbiomesoplenty:mahogany_planks_roof","handcrafted:oak_drawer","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","mcwbiomesoplenty:empyreal_japanese_door","minecraft:lime_stained_glass","dyenamics:mint_candle","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwbiomesoplenty:mahogany_steep_roof","minecraft:stone_brick_walls_from_stone_stonecutting","mcwbiomesoplenty:hellbark_paper_door","cfm:light_blue_kitchen_counter","ae2:network/cables/covered_fluix_clean","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","biomesoplenty:mahogany_slab","mcwbiomesoplenty:stripped_palm_pane_window","create:crafting/kinetics/item_drain","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","cfm:fridge_dark","chimes:copper_chimes","simplylight:illuminant_magenta_block_on_dyed","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","mysticalagriculture:essence/minecraft/diamond","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","mcwwindows:black_mosaic_glass","mcwroofs:black_terracotta_lower_roof","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","mcwfurnitures:cherry_modern_desk","mcwroofs:gray_concrete_top_roof","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","minecraft:cooked_salmon_from_smoking","mcwwindows:prismarine_brick_gothic","cfm:spatula","ae2:network/cables/dense_covered_fluix_clean","littlelogistics:switch_rail","dyenamics:dye_honey_carpet","botania:metamorphic_taiga_bricks_slab","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwbiomesoplenty:fir_cottage_door","constructionwand:core_angel","ae2:network/cables/covered_fluix","create:cut_dripstone_brick_slab_from_stone_types_dripstone_stonecutting","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","botania:floating_bellethorn","mcwwindows:stripped_oak_log_four_window","mcwbiomesoplenty:willow_glass_door","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwroofs:orange_terracotta_upper_lower_roof","wstweaks:wither_skeleton_skull","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","create:cut_dripstone_slab_from_stone_types_dripstone_stonecutting","blue_skies:comet_bookshelf","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","tombstone:ankh_of_prayer","cfm:white_sofa","immersiveengineering:crafting/dynamo","mcwwindows:spruce_louvered_shutter","ae2:shaped/slabs/fluix_block","mcwfences:acacia_highley_gate","mcwroofs:andesite_steep_roof","mcwroofs:pink_concrete_roof","botania:apothecary_taiga","connectedglass:tinted_borderless_glass_black2","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","minecraft:polished_andesite_slab","mcwlights:magenta_lamp","nethersdelight:hoglin_sirloin_from_smoking","productivebees:stonecutter/warped_canvas_expansion_box","ae2:network/cables/dense_covered_lime","mcwbiomesoplenty:maple_swamp_door","botania:metamorphic_fungal_bricks","botania:mana_fluxfield","mcwwindows:orange_mosaic_glass","mcwwindows:warped_planks_window2","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","ae2:shaped/slabs/sky_stone_block","mcwbiomesoplenty:maple_bark_glass_door","handcrafted:oak_bench","silentgear:blaze_gold_dust_smelting","minecraft:yellow_candle","railcraft:track_remover","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","minecraft:repeater","minecraft:red_concrete_powder","rftoolsbuilder:shape_card_quarry_silk_dirt","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwbiomesoplenty:empyreal_glass_door","minecraft:iron_leggings","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","handcrafted:cherry_counter","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:fir_waffle_door","cfm:pink_kitchen_sink","dyenamics:banner/amber_banner","mcwbiomesoplenty:mahogany_lower_bookshelf_drawer","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","farmersdelight:cooked_mutton_chops_from_smoking","create:crafting/kinetics/analog_lever","create:andesite_scaffolding_from_andesite_alloy_stonecutting","securitycraft:reinforced_orange_stained_glass_pane_from_dye","ad_astra:red_industrial_lamp","domum_ornamentum:light_blue_floating_carpet","minecraft:dye_white_carpet","ae2:shaped/stairs/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","mcwbiomesoplenty:pine_beach_door","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","ae2:block_cutter/slabs/quartz_slab","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","create:cut_dripstone_stairs_from_stone_types_dripstone_stonecutting","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","mcwdoors:garage_gray_door","handcrafted:witch_trophy","minecraft:polished_andesite_from_andesite_stonecutting","littlelogistics:seater_car","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","botania:mushroom_5","evilcraft:crafting/broom_part_rod_bare","minecraft:stone_brick_stairs","botania:mushroom_4","ae2:misc/chests_smooth_sky_stone","botania:mushroom_3","mcwwindows:quartz_window","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","blue_skies:coarse_lunar_dirt","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","dyenamics:peach_candle","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","botania:metamorphic_swamp_cobblestone_wall","create:cut_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","minecraft:clock","dyenamics:conifer_dye","create:crafting/appliances/netherite_diving_helmet_from_netherite","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","dyenamics:dye_peach_carpet","mcwlights:soul_birch_tiki_torch","megacells:crafting/compression_card","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","biomesoplenty:mahogany_button","travelersbackpack:coal","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","railways:benchcart","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","minecraft:diamond_leggings","ad_astra:iron_plating","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_paper_door","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","silentgear:leather_scrap","mcwbridges:nether_bricks_bridge_stair","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","botania:keep_ivy","minecraft:polished_andesite_slab_from_andesite_stonecutting","minecraft:dye_black_bed","securitycraft:reinforced_red_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_western_door","handcrafted:jungle_corner_trim","biomesoplenty:empyreal_chest_boat","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","mcwbiomesoplenty:pine_swamp_door","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","alchemistry:atomizer","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","mcwfences:railing_prismarine_wall","minecraft:brick_slab_from_bricks_stonecutting","botania:metamorphic_taiga_stone_stairs","cfm:stripped_jungle_table","dyenamics:honey_candle","supplementaries:pancake_fd","twigs:rocky_dirt","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","mcwbiomesoplenty:dead_bamboo_door","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","create:crafting/kinetics/mechanical_piston","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","botania:white_shiny_flower","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","mcwroofs:white_concrete_upper_lower_roof","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","aether:stone_axe_repairing","twilightforest:sorting_chest_boat","reliquary:interdiction_torch","aether:diamond_axe_repairing","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","mcwfurnitures:cherry_double_drawer","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbridges:brick_bridge","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","cfm:light_blue_cooler","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","mcwbiomesoplenty:stripped_mahogany_lower_triple_drawer","reliquary:uncrafting/wither_skeleton_skull","supplementaries:flags/flag_yellow","mcwbiomesoplenty:pine_bark_glass_door","modularrouters:breaker_module","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","mcwfurnitures:oak_lower_triple_drawer","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","create:crafting/kinetics/basin","naturalist:glow_goop_from_campfire_cooking","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwpaths:blackstone_dumble_paving","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","botania:metamorphic_desert_cobblestone_wall","minecraft:end_stone_brick_wall","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","alltheores:brass_gear","evilcraft:crafting/broom_part_cap_metal_silver","dyenamics:aquamarine_stained_glass","minecraft:netherite_chestplate_smithing","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","terralith:observer_alt","minecraft:oak_sign","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","domum_ornamentum:white_paper_extra","connectedglass:borderless_glass_blue2","dyenamics:fluorescent_dye","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","mcwbiomesoplenty:pine_waffle_door","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","minecraft:smooth_sandstone_slab_from_smooth_sandstone_stonecutting","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","mcwdoors:cherry_glass_door","sfm:manager","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:willow_bamboo_door","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","mysticalagriculture:infusion_altar","mcwwindows:cyan_mosaic_glass_pane","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","comforts:sleeping_bag_to_red","connectedglass:tinted_borderless_glass_cyan2","create_enchantment_industry:crafting/printer","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","ae2:tools/network_tool","deeperdarker:bloom_boat","utilitix:linked_repeater","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","connectedglass:clear_glass_yellow_pane2","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","botania:red_string","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:spruce_curved_gate","allthemodium:ancient_stone_stairs","mcwdoors:jungle_swamp_door","mcwpaths:andesite_flagstone_stairs","domum_ornamentum:green_brick_extra","mcwwindows:warped_planks_four_window","mcwwindows:stripped_crimson_stem_window2","croptopia:meringue","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","create:dripstone_block_from_stone_types_dripstone_stonecutting","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","farmersdelight:cooking/noodle_soup","rftoolsutility:moduleplus_template","evilcraft:crafting/blood_infusion_core","allthecompressed:compress/netherite_block_1x","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","mcwbiomesoplenty:dead_tropical_door","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwbiomesoplenty:jacaranda_paper_door","mcwfurnitures:cherry_desk","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","railcraft:radio_circuit","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwpaths:cobbled_deepslate_clover_paving","mcwfurnitures:oak_modern_wardrobe","ae2:decorative/sky_stone_brick_from_stonecutting","handcrafted:yellow_plate","create:crafting/kinetics/hand_crank","mcwlights:golden_double_candle_holder","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","croptopia:soybean_seed","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","simplylight:illuminant_light_blue_block_on_dyed","mcwroofs:yellow_terracotta_upper_steep_roof","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:cut_calcite_stairs_from_stone_types_calcite_stonecutting","ae2:network/crafting/patterns_blank","create:crafting/appliances/copper_backtank","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","mcwbiomesoplenty:fir_modern_door","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","create:crafting/kinetics/portable_storage_interface","securitycraft:reinforced_pink_stained_glass_pane_from_dye","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","securitycraft:alarm","pneumaticcraft:manometer","mcwbiomesoplenty:dead_japanese2_door","mcwfurnitures:oak_counter","corail_woodcutter:spruce_woodcutter","silentgear:spear_template","dyenamics:icy_blue_dye","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","mcwroofs:oak_top_roof","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","comforts:sleeping_bag_red","utilitix:crude_furnace","dyenamics:peach_dye","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mysticalagriculture:essence/minecraft/netherite_ingot","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","sophisticatedstorage:jukebox_upgrade","integrateddynamics:crafting/drying_basin","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","aether:blue_cape_blue_wool","ae2:network/cables/glass_black","mcwfurnitures:stripped_oak_bookshelf","delightful:food/cooking/glow_jam_jar","mcwfurnitures:cherry_wardrobe","mcwbiomesoplenty:fir_curved_gate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","additionallanterns:gold_lantern","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","mcwbiomesoplenty:mahogany_planks_lower_roof","matc:prudentium_essence","simplylight:illuminant_orange_block_dyed","alchemistry:compactor","minecraft:dropper","create:crafting/kinetics/piston_extension_pole","dyenamics:bed/maroon_bed","farmersdelight:cooking/stuffed_pumpkin_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","create:crafting/kinetics/brass_hand","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","mcwbiomesoplenty:empyreal_bamboo_door","create:crafting/kinetics/lime_seat","mcwbiomesoplenty:stripped_mahogany_double_drawer_counter","mcwdoors:oak_japanese2_door","mcwroofs:cherry_planks_roof","simplylight:illuminant_black_block_dyed","mcwbiomesoplenty:mahogany_mystic_trapdoor","mcwdoors:acacia_bamboo_door","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","create:layered_dripstone_from_stone_types_dripstone_stonecutting","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","productivebees:stonecutter/yucca_canvas_expansion_box","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:red_cushion","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","minecraft:blackstone_slab","mcwwindows:stripped_cherry_log_window","pipez:item_pipe","create:small_dripstone_bricks_from_stone_types_dripstone_stonecutting","mcwpaths:sandstone_running_bond_slab","mcwbridges:balustrade_end_stone_bricks_bridge","dyenamics:dye_rose_carpet","minecraft:quartz_bricks","immersiveengineering:crafting/wirecoil_redstone","botania:metamorphic_plains_cobblestone_stairs","mcwbiomesoplenty:mahogany_tropical_trapdoor","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","mcwbridges:balustrade_bricks_bridge","supplementaries:candle_holders/candle_holder_blue_dye","rftoolsbuilder:red_shield_template_block","mcwfurnitures:stripped_oak_large_drawer","mcwbiomesoplenty:dead_mystic_door","easy_villagers:iron_farm","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","mcwroofs:jungle_planks_steep_roof","pneumaticcraft:wall_lamp_magenta","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","cfm:dye_red_picket_gate","travelersbackpack:hose_nozzle","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","mcwbiomesoplenty:pine_japanese_door","mcwroofs:gutter_base_cyan","allthemodium:allthemodium_ingot","undergarden:smogstem_chest_boat","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","mcwwindows:prismarine_window","securitycraft:laser_block","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","rftoolsutility:matter_receiver","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:hellbark_four_panel_door","rftoolsutility:clock_module","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_3","modularrouters:sender_module_2","modularrouters:sender_module_1","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","supplementaries:flags/flag_blue","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","ae2:network/cables/glass_light_blue","immersiveengineering:crafting/crate","minecolonies:apple_pie","twigs:mossy_bricks_from_moss_block","silentgear:blaze_gold_dust","minecraft:andesite_stairs_from_andesite_stonecutting","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","create:vertical_framed_glass_from_glass_colorless_stonecutting","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","create:crafting/kinetics/goggles","mcwbiomesoplenty:maple_japanese2_door","farmersdelight:wheat_dough_from_water","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","croptopia:nougat","immersiveengineering:crafting/fluid_placer","dyenamics:icy_blue_concrete_powder","botania:apothecary_forest","rftoolsbase:crafting_card","mcwroofs:cyan_terracotta_attic_roof","mcwfurnitures:stripped_cherry_glass_table","mcwlights:soul_bamboo_tiki_torch","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwroofs:cherry_top_roof","rftoolsutility:matter_transmitter","sophisticatedstorage:chipped/tinkering_table_upgrade","mcwfurnitures:cherry_modern_wardrobe","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","utilitarian:utility/cherry_logs_to_slabs","simplylight:illuminant_pink_block_on_toggle","bigreactors:turbine/reinforced/passivetap_fe","cfm:stripped_warped_kitchen_sink_light","twigs:blackstone_column_stonecutting","minecraft:diamond_boots","mcwbiomesoplenty:mahogany_end_table","pneumaticcraft:redstone_module","handcrafted:oak_couch","travelersbackpack:magma_cube","allthetweaks:ender_pearl_block","croptopia:beetroot_salad","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","mcwbiomesoplenty:hellbark_glass_door","forbidden_arcanus:deorum_lantern","everythingcopper:copper_door","farmersdelight:cooking/glow_berry_custard","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","cfm:orange_kitchen_counter","botania:metamorphic_swamp_cobblestone_stairs","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwbiomesoplenty:mahogany_counter","mcwroofs:jungle_upper_lower_roof","minecraft:polished_blackstone_brick_wall","mcwbiomesoplenty:empyreal_mystic_door","mcwpaths:blackstone_running_bond_stairs","minecraft:dried_kelp_from_campfire_cooking","ae2:network/wireless_crafting_terminal","mcwdoors:metal_door","mcwroofs:jungle_planks_upper_steep_roof","mcwdoors:cherry_western_door","modularrouters:vacuum_module","rftoolsutility:spawner","cfm:green_cooler","alltheores:steel_rod","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","ae2:network/cables/dense_covered_light_blue","sophisticatedstorage:backpack_stack_upgrade_tier_1_from_storage_stack_upgrade_tier_2","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","connectedglass:tinted_borderless_glass_red2","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","sophisticatedstorage:basic_to_netherite_tier_upgrade","immersiveengineering:crafting/strip_curtain","mcwbiomesoplenty:fir_wired_fence","travelersbackpack:pig","minecraft:black_terracotta","mysticalagriculture:essence/integrateddynamics/menril_berries","alltheores:platinum_ingot_from_raw","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","computercraft:turtle_normal_overlays/turtle_trans_overlay","ae2:tools/certus_quartz_cutting_knife","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:white_bed","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:wine_terracotta","cfm:dye_green_picket_gate","minecraft:cobblestone_slab_from_cobblestone_stonecutting","sophisticatedstorage:jungle_chest","botania:corporea_spark","ae2:shaped/stairs/smooth_sky_stone_block","mcwbiomesoplenty:palm_highley_gate","bigreactors:blasting/yellorium_from_raw","mcwroofs:black_attic_roof","create:cut_dripstone_from_stone_types_dripstone_stonecutting","sophisticatedstorage:chipped/botanist_workbench_upgrade","minecraft:pumpkin_pie","mcwfurnitures:stripped_jungle_large_drawer","ae2:block_cutter/walls/fluix_wall","mcwfences:mesh_metal_fence","supplementaries:soap/piston","minecraft:soul_campfire","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwwindows:prismarine_pane_window","mcwpaths:brick_windmill_weave_path","mcwlights:spruce_tiki_torch","dyenamics:bed/amber_bed_frm_white_bed","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","twigs:cracked_bricks","appflux:insulating_resin","botania:apothecary_fungal","connectedglass:borderless_glass_green_pane2","mcwdoors:acacia_mystic_door","ae2:misc/deconstruction_fluix_block","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwbridges:rope_cherry_bridge","blue_skies:trough","create:polished_cut_dripstone_wall_from_stone_types_dripstone_stonecutting","computercraft:monitor_normal","mcwroofs:bricks_steep_roof","cfm:gray_grill","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","dankstorage:6_to_7","create:crafting/kinetics/radial_chassis","allthemodium:allthemodium_hoe","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","create:brass_bars_from_ingots_brass_stonecutting","mcwdoors:cherry_bark_glass_door","ae2:block_cutter/stairs/smooth_sky_stone_stairs","railcraft:steel_helmet","minecraft:quartz_pillar_from_quartz_block_stonecutting","mcwroofs:nether_bricks_roof","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","minecraft:observer","cfm:gray_kitchen_sink","mcwfences:bamboo_stockade_fence","mcwbiomesoplenty:hellbark_waffle_door","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","minecraft:pink_dye_from_peony","immersiveengineering:crafting/hoe_steel","delightful:smoking/roasted_acorn","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","connectedglass:clear_glass_white_pane2","modularrouters:muffler_upgrade","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwbridges:sandstone_bridge","ad_astra:iron_factory_block","mcwfences:railing_deepslate_brick_wall","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:red_terracotta_steep_roof","utilitix:oak_shulker_boat","cfm:white_trampoline","handcrafted:oak_desk","create:crafting/kinetics/super_glue","xnet:connector_blue_dye","botania:metamorphic_fungal_cobblestone_slab","rftoolsbuilder:shape_card_liquid","mcwbiomesoplenty:dead_stable_head_door","mcwfurnitures:stripped_oak_desk","immersiveengineering:crafting/treated_fence","minecraft:gray_dye","terralith:piston_alt","farmersdelight:cooked_mutton_chops_from_campfire_cooking","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mininggadgets:mininggadget_fancy","twigs:polished_amethyst_stonecutting","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","computercraft:skull_dan200","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","mob_grinding_utils:recipe_tank","ae2:network/parts/energy_acceptor","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwwindows:warped_pane_window","minecraft:emerald_block","allthecompressed:compress/pumpkin_1x","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","create:calcite_from_stone_types_calcite_stonecutting","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","integrateddynamics:crafting/part_static_light_panel","mcwwindows:oak_plank_parapet","farmersdelight:cooking/cooked_rice","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:iron_sword","mcwfurnitures:jungle_table","domum_ornamentum:blue_cobblestone_extra","additionallanterns:blackstone_chain","mcwbiomesoplenty:stripped_mahogany_wardrobe","domum_ornamentum:purple_brick_extra","mcwwindows:one_way_glass_pane","mcwbiomesoplenty:hellbark_stable_door","connectedglass:scratched_glass_lime_pane2","mcwpaths:cherry_planks_path","minecraft:chiseled_sandstone_from_sandstone_stonecutting","comforts:hammock_red","mcwpaths:andesite_dumble_paving","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","chemlib:iron_ingot_from_blasting_iron_dust","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","productivetrees:sawmill","silentgear:helmet_template","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","productivebees:stonecutter/maple_canvas_hive","mcwbiomesoplenty:mahogany_picket_fence","mcwdoors:acacia_barn_glass_door","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","supplementaries:candle_holders/candle_holder_green_dye","railcraft:electric_locomotive","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","supplementaries:planter_rich","create:crafting/kinetics/metal_girder","minecolonies:blockconstructiontape","create:crafting/kinetics/millstone","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","travelersbackpack:dye_white_sleeping_bag","dyenamics:wine_stained_glass_pane_from_glass_pane","delightful:storage/acorn_storage_block","mcwroofs:gutter_base_white","mcwbridges:jungle_rail_bridge","silentgear:crimson_steel_ingot_from_block","enderio:resetting_lever_ten_from_prev","mcwroofs:magenta_concrete_upper_lower_roof","reliquary:crimson_cloth","immersiveengineering:crafting/cushion","mcwroofs:cyan_concrete_steep_roof","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","farmersdelight:beef_patty_from_smoking","mekanism:processing/copper/ingot/from_dust_blasting","minecraft:comparator","silentgear:cord_template","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwroofs:red_concrete_steep_roof","mcwbiomesoplenty:magic_bark_glass_door","xnet:connector_yellow_dye","rftoolspower:powercell_card","reliquary:alkahestry_altar","mcwwindows:crimson_stem_window","mcwdoors:acacia_waffle_door","mcwlights:yellow_lamp","mcwdoors:cherry_swamp_door","mcwbiomesoplenty:mahogany_bridge_pier","supplementaries:timber_frame","quark:tweaks/crafting/utility/misc/easy_hopper","mcwfurnitures:cherry_covered_desk","sophisticatedstorage:packing_tape","enderio:resetting_lever_thirty_from_inv","mcwlights:soul_oak_tiki_torch","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","mcwdoors:cherry_japanese2_door","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","ad_astra:white_flag","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","railcraft:wooden_tie","mcwfurnitures:jungle_drawer_counter","mcwroofs:stone_steep_roof","mekanism:steel_casing","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:creeper","botania:floating_daffomill","pneumaticcraft:wall_lamp_inverted_pink","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","ae2:shaped/walls/smooth_sky_stone_block","mcwfurnitures:cherry_triple_drawer","mcwroofs:light_gray_roof_block","minecraft:yellow_dye_from_sunflower","mcwroofs:magenta_terracotta_lower_roof","create:crafting/kinetics/windmill_bearing","utilitarian:fluid_hopper","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:stone_attic_roof","immersiveengineering:crafting/concrete","mekanism:paper","handcrafted:calcite_corner_trim","rftoolsbuilder:blue_shield_template_block","rftoolsbuilder:space_chamber","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwwindows:metal_window","mcwbiomesoplenty:mahogany_swamp_trapdoor","minecraft:glass_pane","mcwbiomesoplenty:maple_barn_glass_door","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","simplylight:illuminant_yellow_block_toggle","minecraft:clay","ae2:network/crafting/cpu_crafting_unit","mcwbiomesoplenty:pine_pane_window","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","ad_astra:blue_flag","create:crafting/kinetics/mechanical_plough","enderio:wood_gear","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","ae2:network/blocks/interfaces_interface_part","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwbiomesoplenty:mahogany_paper_door","modularrouters:range_down_augment","ae2:materials/annihilationcore","silentgear:stone_rod","botania:metamorphic_mesa_stone_stairs","ae2:network/cables/dense_covered_green","minecraft:leather_boots","railcraft:tank_detector","handcrafted:sandstone_corner_trim","ad_astra:etrionic_blast_furnace","create:crafting/logistics/andesite_tunnel","immersiveengineering:crafting/balloon","silentgear:leggings_template","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","dyenamics:persimmon_dye","productivebees:stonecutter/birch_canvas_expansion_box","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwbiomesoplenty:magic_nether_door","mob_grinding_utils:recipe_ender_inhibitor","pneumaticcraft:wall_lamp_inverted_light_blue","ae2:network/crystal_resonance_generator","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","mcwfurnitures:cherry_lower_triple_drawer","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","minecraft:dye_black_carpet","tombstone:white_marble","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","ae2:network/cables/covered_purple","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","chemlib:gold_ingot_from_blasting_gold_dust","minecraft:magma_block","enderio:resetting_lever_thirty_inv","botania:petal_blue","nethersdelight:nether_brick_smoker","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","botania:metamorphic_swamp_stone_stairs","aether:aether_iron_nugget_from_smelting","mcwbiomesoplenty:mahogany_planks_top_roof","silentgear:raw_crimson_iron_from_block","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","supplementaries:ash_brick","mcwfences:modern_granite_wall","productivebees:stonecutter/grimwood_canvas_hive","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_modern_wardrobe","mcwbiomesoplenty:fir_barn_door","simplylight:illuminant_block_on","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","silentgear:blueprint_book","alltheores:zinc_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:mahogany_coffee_table","biomesoplenty:dead_boat","mysticalagriculture:inferium_gemstone","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","handcrafted:quartz_pillar_trim","mcwbiomesoplenty:willow_japanese2_door","mcwwindows:andesite_window","domum_ornamentum:brick_extra","modularrouters:activator_module","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","minecraft:blaze_powder","cfm:brown_kitchen_drawer","evilcraft:crafting/broom_part_brush_bare","minecraft:chain","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","integratedtunnels:crafting/part_interface_item","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","create:polished_cut_calcite_wall_from_stone_types_calcite_stonecutting","mcwfences:crimson_highley_gate","mcwbiomesoplenty:pine_barn_glass_door","mcwbiomesoplenty:empyreal_window2","mininggadgets:upgrade_void_junk","mcwpaths:stone_flagstone_slab","minecraft:kjs/mininggadgets_upgrade_empty","ae2:decorative/sky_stone_small_brick_from_stonecutting","silentgear:crimson_steel_dust_smelting","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwroofs:magenta_terracotta_upper_steep_roof","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/generator","mcwwindows:warped_stem_four_window","immersiveengineering:crafting/blueprint_bullets","handcrafted:white_crockery_combo","mcwroofs:magenta_concrete_lower_roof","supplementaries:dispenser_minecart","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","travelersbackpack:wither","mcwdoors:jungle_mystic_door","silentgear:fine_silk_cloth","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","immersiveengineering:crafting/wooden_barrel","minecraft:calibrated_sculk_sensor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:fir_mystic_door","railcraft:bronze_gear","mcwbiomesoplenty:fir_beach_door","ae2:tools/certus_quartz_spade","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","ae2:network/cables/dense_covered_black","ae2:network/blocks/controller","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","croptopia:roasted_smoking","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","itemcollectors:advanced_collector","minecraft:green_terracotta","delightful:knives/brass_knife","evilcraft:crafting/broom_part_brush_wool","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","croptopia:pumpkin_soup","supplementaries:biomesoplenty/sign_post_mahogany","domum_ornamentum:white_brick_extra","supplementaries:gold_door","ae2:misc/fluixpearl","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","sfm:labelgun","mcwbiomesoplenty:mahogany_lower_roof","mcwfences:jungle_hedge","reliquary:angelic_feather","mcwdoors:jungle_classic_door","create:crafting/kinetics/large_cogwheel_from_little","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","immersiveengineering:crafting/clinker_brick_quoin","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","cfm:stripped_jungle_desk","handcrafted:oven","mcwbiomesoplenty:stripped_mahogany_table","handcrafted:calcite_pillar_trim","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","supplementaries:candle_holders/candle_holder_blue","rftoolsbuilder:shape_card_quarry_fortune","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","immersiveengineering:crafting/plate_aluminum_hammering","delightful:knives/bronze_knife","travelersbackpack:quartz","mcwbiomesoplenty:mahogany_classic_door","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","bigreactors:energizer/controller","mcwbiomesoplenty:stripped_maple_log_four_window","mcwwindows:stripped_dark_oak_log_window","mcwbiomesoplenty:stripped_mahogany_double_drawer","allthecompressed:compress/andesite_1x","mcwbiomesoplenty:willow_nether_door","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwpaths:brick_flagstone","create:small_calcite_brick_wall_from_stone_types_calcite_stonecutting","minecraft:blast_furnace","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","securitycraft:electrified_iron_fence","minecraft:light_blue_stained_glass_pane_from_glass_pane","minecraft:magenta_dye_from_lilac","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwwindows:warped_plank_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","mcwfurnitures:stripped_cherry_lower_triple_drawer","ae2:materials/carddistribution","ae2:network/parts/import_bus","minecraft:dye_red_bed","mcwdoors:acacia_stable_door","mcwbiomesoplenty:mahogany_bookshelf_cupboard","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","minecraft:blue_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_cupboard_counter","silentgear:crimson_iron_ingot_from_nugget","mcwbiomesoplenty:hellbark_cottage_door","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","silentgear:upgrade_base","ae2:network/cables/glass_pink","ae2:network/cables/smart_cyan","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","minecraft:dye_lime_bed","rftoolsbuilder:space_chamber_controller","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwwindows:one_way_glass","minecraft:spyglass","mcwdoors:jungle_cottage_door","botania:metamorphic_mountain_cobblestone_wall","mcwroofs:bricks_top_roof","minecraft:pink_stained_glass","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","minecraft:oak_chest_boat","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwbiomesoplenty:hellbark_classic_door","functionalstorage:armory_cabinet","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:dead_glass_door","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","mcwbiomesoplenty:willow_modern_door","pneumaticcraft:logistics_core","comforts:hammock_blue","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","create:polished_cut_dripstone_from_stone_types_dripstone_stonecutting","corail_woodcutter:acacia_woodcutter","minecraft:bread","mcwbiomesoplenty:redwood_western_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","mcwbiomesoplenty:dead_classic_door","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","dyenamics:peach_wool","mcwbiomesoplenty:umbran_swamp_door","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","chimes:amethyst_chimes","ae2:smelting/smooth_sky_stone_block","mcwbiomesoplenty:mahogany_cottage_door","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","travelersbackpack:bat_smithing","securitycraft:track_mine","mcwbiomesoplenty:willow_swamp_door","mcwdoors:acacia_glass_door","mcwbiomesoplenty:fir_stable_head_door","alltheores:osmium_dust_from_hammer_ingot_crushing","pneumaticcraft:camo_applicator","silentgear:template_board","deeperdarker:echo_boat","mcwbiomesoplenty:hellbark_swamp_door","create:calcite_pillar_from_stone_types_calcite_stonecutting","cfm:fridge_light","twigs:smooth_basalt_brick_wall_from_smooth_basalt_stonecutting","mcwroofs:andesite_upper_lower_roof","mcwfences:birch_picket_fence","botania:metamorphic_taiga_cobblestone_slab","enderio:basic_fluid_filter","aether:netherite_boots_repairing","biomesoplenty:jacaranda_boat","minecraft:oak_door","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwbiomesoplenty:maple_cottage_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","botania:floating_kekimurus","mcwlights:cyan_paper_lamp","mcwbiomesoplenty:empyreal_modern_door","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","connectedglass:tinted_borderless_glass_lime2","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","botania:petal_purple","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","botania:metamorphic_swamp_bricks_slab","create:crafting/kinetics/propeller","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:cherry_end_table","immersiveengineering:crafting/fluid_pipe","modularrouters:dropper_module","mcwbiomesoplenty:magic_highley_gate","mcwroofs:yellow_concrete_upper_lower_roof","dyenamics:mint_terracotta","ae2:network/cables/glass_magenta","supplementaries:candle_holders/candle_holder","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","biomesoplenty:fir_chest_boat","ae2:network/blocks/spatial_io_port","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","supplementaries:wrench","aether:skyroot_smithing_table","mcwroofs:cherry_lower_roof","securitycraft:reinforced_oak_fence_gate","botania:mushroom_stew","delightful:knives/aluminum_knife","bigreactors:energizer/chargingport_fe","mcwbiomesoplenty:stripped_redwood_log_four_window","croptopia:apple_juice","cfm:purple_kitchen_sink","minecraft:jungle_slab","botania:floating_dreadthorn","supplementaries:sconce","silentgear:rough_rod","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:pine_stable_door","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:granite","cfm:dye_cyan_picket_fence","minecraft:black_dye_from_wither_rose","sophisticatedstorage:smoking_upgrade","mcwwindows:stripped_spruce_log_four_window","mcwbiomesoplenty:magic_barn_glass_door","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","mcwbiomesoplenty:fir_bamboo_door","pneumaticcraft:wall_lamp_inverted_brown","mcwroofs:andesite_roof","rftoolsbase:infused_enderpearl","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwwindows:deepslate_pane_window","minecraft:dye_yellow_carpet","ae2:network/cables/glass_yellow","minecraft:dye_white_wool","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","dyenamics:rose_terracotta","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","handcrafted:silverfish_trophy","cfm:yellow_kitchen_counter","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","railcraft:iron_tank_valve","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","blue_skies:glowing_poison_stone","create:crafting/kinetics/copper_door","mcwdoors:oak_barn_door","silentgear:hammer_template","create:andesite_bars_from_andesite_alloy_stonecutting","botania:alchemy_catalyst","mcwbiomesoplenty:mahogany_barred_trapdoor","mcwbiomesoplenty:stripped_mahogany_bookshelf_drawer","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","minecraft:cobbled_deepslate_wall","supplementaries:timber_cross_brace","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","minecraft:smooth_stone","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","rftoolsutility:environmental_controller","mcwroofs:cyan_concrete_attic_roof","silentgear:crimson_iron_block","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","mcwbiomesoplenty:stripped_mahogany_desk","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","mcwbiomesoplenty:stripped_mahogany_large_drawer","sophisticatedstorage:chipped/mason_table_upgrade","mcwroofs:white_concrete_attic_roof","xnet:controller","cookingforblockheads:sink","botania:metamorphic_desert_bricks","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","reliquary:uncrafting/ender_pearl","additionallanterns:obsidian_chain","minecraft:ender_eye","supplementaries:blackstone_lamp","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","rftoolsbase:manual","mininggadgets:upgrade_range_1","mininggadgets:upgrade_range_2","mininggadgets:upgrade_range_3","immersiveengineering:crafting/connector_hv_relay","ae2:block_cutter/walls/quartz_wall","ae2:network/blocks/io_condenser","minecraft:cooked_mutton_from_campfire_cooking","mcwpaths:brick_clover_paving","minecraft:dye_black_wool","securitycraft:trophy_system","ad_astra:small_green_industrial_lamp","minecraft:cooked_beef","mcwbiomesoplenty:pine_curved_gate","mcwfurnitures:stripped_cherry_wardrobe","mcwbiomesoplenty:jacaranda_four_panel_door","cfm:green_trampoline","mcwlights:yellow_paper_lamp","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","railcraft:iron_tank_gauge","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","dyenamics:wine_stained_glass","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwroofs:pink_striped_awning","mcwbiomesoplenty:mahogany_japanese_door","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:umbran_four_panel_door","twigs:calcite_stairs","silentgear:crimson_steel_dust","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","silentgear:pickaxe_template","mcwbiomesoplenty:pine_glass_door","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","ae2:network/cells/item_storage_components_cell_1k_part","mcwroofs:oak_planks_top_roof","botania:floating_hopperhock_chibi","mcwroofs:oak_planks_roof","enderio:fluid_tank","farmersdelight:cooked_mutton_chops","ad_astra:iron_rod","botania:petal_white","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","aether:chainmail_boots_repairing","supplementaries:blackstone_tile","ae2:block_cutter/slabs/smooth_sky_stone_slab","create:small_dripstone_brick_slab_from_stone_types_dripstone_stonecutting","minecraft:enchanting_table","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","mcwbiomesoplenty:mahogany_striped_chair","farmersdelight:roast_chicken_block","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","mcwroofs:blue_terracotta_lower_roof","ad_astra:small_blue_industrial_lamp","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","aether:skyroot_note_block","mcwbiomesoplenty:maple_tropical_door","pneumaticcraft:logistics_configurator","bigreactors:smelting/yellorium_from_raw","create:dripstone_pillar_from_stone_types_dripstone_stonecutting","rftoolsutility:dialing_device","botania:metamorphic_swamp_bricks","allthecompressed:compress/diamond_block_1x","sfm:disk","minecraft:kjs/structurecompass_structure_compass","dyenamics:banner/lavender_banner","botania:ender_hand","farmersdelight:bread_from_smoking","modularrouters:energy_distributor_module","handcrafted:spider_trophy","allthecompressed:compress/calcite_1x","mcwroofs:lime_terracotta_lower_roof","ae2:network/cables/dense_smart_green","mininggadgets:upgrade_silk","mcwlights:chain_wall_lantern","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","mythicbotany:central_rune_holder","mcwfurnitures:cherry_chair","create:crafting/kinetics/light_gray_seat","railcraft:diamond_crowbar","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","productivebees:stonecutter/dark_oak_canvas_expansion_box","mcwpaths:cobbled_deepslate_running_bond_slab","mcwbiomesoplenty:stripped_mahogany_chair","buildinggadgets2:gadget_cut_paste","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","rftoolsbuilder:mover_status","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","aether:iron_gloves_repairing","supplementaries:candle_holders/candle_holder_green","aether:skyroot_fletching_table","create:crafting/kinetics/gearbox","mcwbiomesoplenty:umbran_modern_door","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","mekanism:fluid_tank/basic","mcwroofs:white_roof_slab","energymeter:meter","mcwroofs:purple_concrete_upper_lower_roof","comforts:sleeping_bag_to_blue","minecraft:nether_brick_stairs","supplementaries:bubble_blower","naturalist:glow_goop","dyenamics:conifer_stained_glass_pane_from_glass_pane","minecraft:recovery_compass","minecraft:magenta_dye_from_blue_red_white_dye","minecraft:cherry_fence","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","allthecompressed:compress/ancient_stone_1x","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","productivebees:stonecutter/fir_canvas_expansion_box","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","mcwbiomesoplenty:willow_bark_glass_door","mcwdoors:cherry_bamboo_door","minecraft:pink_terracotta","botania:metamorphic_taiga_bricks","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","minecraft:spire_armor_trim_smithing_template_smithing_trim","rftoolsutility:destination_analyzer","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","ae2:materials/cardfuzzy","mcwbiomesoplenty:mahogany_curved_gate","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","securitycraft:camera_monitor","immersiveengineering:crafting/clinker_brick_sill","allthecompressed:compress/quartz_block_1x","mcwbiomesoplenty:umbran_curved_gate","mcwroofs:black_top_roof","mcwfences:modern_nether_brick_wall","botania:metamorphic_mountain_bricks","silentgear:crimson_steel_nugget","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","cfm:jungle_kitchen_sink_light","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","biomesoplenty:mahogany_boat","quark:tweaks/crafting/utility/misc/charcoal_to_black_dye","botania:floating_loonium","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","sophisticatedstorage:controller","biomesoplenty:palm_chest_boat","mcwbiomesoplenty:redwood_bark_glass_door","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwroofs:magenta_concrete_upper_steep_roof","minecraft:tinted_glass","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:lodestone","mcwlights:black_lamp","apotheosis:scrap_tome","mcwpaths:sandstone_running_bond_stairs","create:crafting/kinetics/speedometer","mcwbiomesoplenty:mahogany_window","minecraft:stone_stairs","handcrafted:evoker_trophy","mysticalagriculture:essence/common/silicon","biomesoplenty:magenta_dye_from_wildflower","minecraft:nether_brick_wall","connectedglass:borderless_glass_red2","constructionwand:core_destruction","mcwfences:oak_pyramid_gate","farmersdelight:beef_patty_from_campfire_cooking","handcrafted:terracotta_thick_pot","mcwbiomesoplenty:mahogany_modern_chair","productivetrees:sawdust_to_paper_water_bottle","create:polished_cut_dripstone_slab_from_stone_types_dripstone_stonecutting","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","enderio:resetting_lever_five","mcwbiomesoplenty:hellbark_beach_door","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","integrateddynamics:crafting/crystalized_menril_chunk","ae2:network/parts/terminals","securitycraft:keypad_frame","enderio:resetting_lever_thirty_inv_from_prev","securitycraft:whitelist_module","ae2:network/blocks/energy_energy_acceptor","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","xnet:connector_routing","minecraft:blue_carpet","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","mcwroofs:green_terracotta_attic_roof","mcwbiomesoplenty:palm_bark_glass_door","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","farmersdelight:barbecue_stick","immersiveengineering:crafting/shield","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","dyenamics:ultramarine_candle","mcwroofs:cyan_terracotta_steep_roof","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwbiomesoplenty:pine_stable_head_door","pneumaticcraft:search_upgrade","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","ae2:network/blocks/storage_chest","cfm:jungle_desk","minecraft:dripstone_block","minecraft:mangrove_chest_boat","mcwbiomesoplenty:stripped_mahogany_drawer","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_blue_concrete_lower_roof","utilitarian:utility/cherry_logs_to_stairs","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","mcwbiomesoplenty:palm_japanese2_door","mcwbiomesoplenty:mahogany_bark_trapdoor","mcwbiomesoplenty:willow_curved_gate","draconicevolution:disenchanter","minecraft:jungle_trapdoor","croptopia:mashed_potatoes","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","mcwbiomesoplenty:maple_japanese_door","mcwroofs:cherry_attic_roof","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","modularrouters:xp_vacuum_augment","ae2:network/cables/dense_covered_purple","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","botania:metamorphic_fungal_cobblestone_wall","mcwfurnitures:cherry_counter","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","modularrouters:redstone_augment","mcwtrpdoors:jungle_swamp_trapdoor","xnet:wireless_router","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","mcwwindows:dark_prismarine_brick_gothic","mcwroofs:black_roof_slab","mcwfurnitures:oak_double_drawer_counter","mcwroofs:orange_terracotta_top_roof","travelersbackpack:gold_tier_upgrade","mcwfences:mesh_metal_fence_gate","xnet:netcable_yellow","mcwroofs:blue_terracotta_roof","mcwfurnitures:jungle_wardrobe","mcwpaths:blackstone_crystal_floor_stairs","mcwfurnitures:stripped_cherry_lower_bookshelf_drawer","cfm:red_sofa","dyenamics:navy_wool","alltheores:platinum_gear","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","botania:chiseled_metamorphic_forest_bricks","rftoolscontrol:tank","utilitix:mob_bell","mcwbiomesoplenty:willow_beach_door","mcwroofs:green_terracotta_upper_lower_roof","mcwpaths:andesite_running_bond_slab","utilitarian:utility/oak_logs_to_boats","minecraft:chiseled_stone_bricks_stone_from_stonecutting","mcwbiomesoplenty:fir_hedge","minecraft:netherite_sword_smithing","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","mcwfurnitures:cherry_double_wardrobe","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","dankstorage:4_to_5","delightful:food/baklava","quark:building/crafting/oak_bookshelf","mcwdoors:metal_hospital_door","railcraft:signal_circuit","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","dyenamics:dye_conifer_carpet","sophisticatedstorage:oak_chest_from_vanilla_chest","supplementaries:sign_post_cherry","immersiveengineering:crafting/screwdriver","connectedglass:borderless_glass_pane1","cfm:pink_cooler","mcwfurnitures:oak_double_drawer","mcwfences:railing_blackstone_wall","minecraft:stone_bricks_from_stone_stonecutting","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","ad_astra:blue_industrial_lamp","dankstorage:1_to_2","mcwbiomesoplenty:maple_modern_door","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","botania:apothecary_plains","botania:metamorphic_plains_bricks_slab","mcwbiomesoplenty:mahogany_planks_upper_lower_roof","mcwfences:modern_sandstone_wall","mcwpaths:andesite_strewn_rocky_path","utilitix:tiny_charcoal_to_tiny","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","croptopia:sushi","mcwroofs:blue_concrete_roof","nethersdelight:hoglin_sirloin_from_campfire_cooking","mcwbiomesoplenty:fir_plank_window","mcwtrpdoors:cherry_tropical_trapdoor","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwwindows:crimson_shutter","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","ad_astra:iron_panel","mcwpaths:cobblestone_clover_paving","minecraft:kjs/silentgear_salvager","minecraft:blue_candle","railcraft:blast_furnace_bricks","silentgear:crude_repair_kit","ae2:network/cables/dense_covered_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","handcrafted:dripstone_pillar_trim","mcwroofs:purple_terracotta_upper_lower_roof","mcwroofs:gray_striped_awning","biomesoplenty:orange_dye_from_burning_blossom","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","blue_skies:cake_compat","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_bricks_stonecutting","minecraft:gold_ingot_from_blasting_raw_gold","botania:floating_dandelifeon","productivebees:stonecutter/aspen_canvas_expansion_box","handcrafted:wood_cup","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:steamed_rice","mcwbiomesoplenty:mahogany_nether_door","mcwbiomesoplenty:hellbark_window2","rftoolsbuilder:shape_card_quarry","mcwbiomesoplenty:magic_hedge","mcwbiomesoplenty:mahogany_barrel_trapdoor","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime1","connectedglass:borderless_glass_lime2","twigs:calcite_wall","cfm:orange_trampoline","mcwroofs:lime_concrete_steep_roof","silentgear:shovel_template","minecraft:wooden_hoe","mcwbiomesoplenty:redwood_waffle_door","createoreexcavation:diamond_drill","handcrafted:white_cup","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","mcwlights:light_gray_lamp","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","botania:floating_marimorphosis","mcwfences:deepslate_brick_grass_topped_wall","mcwwindows:andesite_window2","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","railcraft:steel_leggings","delightful:food/cooking/nut_butter_bottle","mcwbiomesoplenty:umbran_nether_door","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","minecraft:piston","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","connectedglass:borderless_glass_red_pane2","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwbiomesoplenty:dead_modern_door","mcwdoors:acacia_cottage_door","croptopia:apple_sapling","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:cherry_planks_lower_roof","mcwroofs:oak_steep_roof","forbidden_arcanus:edelwood_planks","botania:metamorphic_forest_cobblestone_slab","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwbiomesoplenty:magic_modern_door","handcrafted:stackable_book","handcrafted:cherry_fancy_bed","mcwroofs:andesite_attic_roof","mcwroofs:sandstone_top_roof","modularrouters:blast_upgrade","securitycraft:reinforced_green_stained_glass","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","silentgear:raw_crimson_iron_block","allthecompressed:compress/soul_soil_1x","productivebees:stonecutter/jacaranda_canvas_expansion_box","mcwbridges:end_stone_bricks_bridge_pier","mcwbiomesoplenty:stripped_mahogany_glass_table","mcwroofs:red_terracotta_upper_lower_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwroofs:lime_striped_awning","botania:metamorphic_forest_bricks","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","productivebees:stonecutter/oak_canvas_expansion_box","immersiveengineering:crafting/stick_treated","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwdoors:print_waffle","enderio:dark_steel_nugget_to_ingot","mcwwindows:birch_shutter","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","sfm:water_tank","create:polished_cut_calcite_from_stone_types_calcite_stonecutting","mcwfences:crimson_wired_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","cfm:dye_lime_picket_fence","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","mcwfurnitures:cherry_bookshelf","mcwroofs:red_terracotta_attic_roof","mcwdoors:acacia_classic_door","create:small_dripstone_brick_wall_from_stone_types_dripstone_stonecutting","mcwfences:end_brick_grass_topped_wall","computercraft:pocket_computer_advanced","dyenamics:bed/ultramarine_bed_frm_white_bed","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_jungle_stool_chair","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwwindows:white_curtain","mcwroofs:white_terracotta_roof","bigreactors:reactor/basic/solidaccessport","handcrafted:cherry_desk","cfm:light_gray_grill","cfm:oak_desk","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","allthecompressed:compress/dripstone_block_1x","supplementaries:daub_frame","mcwroofs:light_gray_top_roof","minecolonies:potato_soup","mcwbiomesoplenty:pine_mystic_door","mcwbiomesoplenty:fir_paper_door","botania:metamorphic_mesa_bricks_stairs","mcwfurnitures:jungle_double_drawer","immersiveengineering:crafting/minecart_metalbarrel","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:lavender_stained_glass","forbidden_arcanus:ender_pearl","minecraft:green_stained_glass_pane_from_glass_pane","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","utilitarian:utility/cherry_logs_to_boats","securitycraft:briefcase","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","mcwwindows:cobblestone_arrow_slit","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/dense_covered_white","mcwpaths:andesite_crystal_floor_stairs","modularrouters:distributor_module","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","create:crafting/logistics/pulse_extender","mekanismtools:osmium/tools/pickaxe","aether:golden_pendant","mcwdoors:cherry_mystic_door","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","undergarden:grongle_boat","rftoolsutility:matter_beamer","mcwbiomesoplenty:dead_nether_door","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","littlelogistics:automatic_switch_rail","minecraft:carrot_on_a_stick","mcwbiomesoplenty:stripped_mahogany_triple_drawer","cfm:dye_blue_picket_gate","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","mcwlights:copper_double_candle_holder","mcwroofs:blue_concrete_upper_steep_roof","laserio:logic_chip_raw","supplementaries:cage","mcwfences:diorite_grass_topped_wall","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","silentgear:tip_template","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","simplylight:illuminant_brown_block_dyed","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","mcwroofs:lime_terracotta_upper_lower_roof","mcwbiomesoplenty:maple_mystic_door","mekanism:processing/osmium/raw/from_raw_block","chemlib:platinum_ingot_from_blasting_platinum_dust","expatternprovider:silicon_block","minecraft:item_frame","itemcollectors:basic_collector","enderio:resetting_lever_three_hundred_inv","minecraft:polished_granite_stairs_from_polished_granite_stonecutting","botania:cell_block","mcwtrpdoors:oak_bark_trapdoor","silentgear:slingshot_template","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","travelersbackpack:cow","alltheores:brass_dust_from_alloy_blending","mcwroofs:sandstone_attic_roof","integrateddynamics:crafting/cable","handcrafted:skeleton_trophy","handcrafted:golden_wide_pot","mcwfurnitures:stripped_cherry_chair","rftoolsutility:tank","aether:netherite_gloves_smithing","mcwbiomesoplenty:mahogany_stable_head_door","railcraft:steam_locomotive","mcwpaths:brick_flagstone_slab","reliquary:uncrafting/magma_cream","supplementaries:flags/flag_green","undergarden:smogstem_boat","computercraft:computer_advanced","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","quark:building/crafting/furnaces/cobblestone_furnace","sophisticatedstorage:generic_chest","minecraft:jungle_button","handcrafted:wood_plate","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","botania:metamorphic_taiga_bricks_wall","travelersbackpack:horse","cfm:dye_red_picket_fence","cfm:brown_cooler","xnet:netcable_red","mcwfences:diorite_pillar_wall","mcwwindows:dark_prismarine_parapet","rftoolsutility:inventoryplus_module","dyenamics:amber_candle","bigreactors:reprocessor/wasteinjector","undergarden:shard_torch","mcwfences:bamboo_picket_fence","handcrafted:goat_trophy","minecraft:respawn_anchor","mcwpaths:cobbled_deepslate_flagstone","dyenamics:persimmon_stained_glass_pane_from_glass_pane","computercraft:disk_drive","integrateddynamics:crafting/variable_transformer_output","mcwroofs:blue_striped_awning","pneumaticcraft:wall_lamp_red","mcwbiomesoplenty:mahogany_modern_door","alltheores:signalum_dust_from_alloy_blending","mcwbridges:jungle_bridge_pier","buildinggadgets2:gadget_exchanging","comforts:hammock_to_blue","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cables/covered_light_blue","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","minecraft:composter","mcwfurnitures:stripped_cherry_modern_chair","aether:diamond_sword_repairing","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_cherry_drawer","domum_ornamentum:cyan_floating_carpet","mcwfurnitures:cherry_modern_chair","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","dyenamics:dye_spring_green_carpet","securitycraft:reinforced_light_gray_stained_glass","farmersdelight:cooking/pasta_with_mutton_chop","minecraft:chiseled_quartz_block","mcwwindows:blackstone_pane_window","connectedglass:borderless_glass_yellow2","mcwdoors:jungle_whispering_door","mcwlights:bamboo_tiki_torch","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","ae2:network/cables/glass_white","croptopia:egg_roll","mcwtrpdoors:cherry_barrel_trapdoor","connectedglass:borderless_glass_black2","mcwtrpdoors:oak_whispering_trapdoor","dyenamics:bed/lavender_bed_frm_white_bed","forbidden_arcanus:rotten_leather","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","create:crafting/kinetics/mechanical_mixer","domum_ornamentum:yellow_brick_extra","aether:iron_pendant","minecraft:lime_dye","mcwbiomesoplenty:pine_bamboo_door","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:rope_mahogany_bridge","supplementaries:flags/flag_purple","botania:quartz_sunny","productivebees:stonecutter/maple_canvas_expansion_box","botania:metamorphic_forest_stone_wall","tombstone:bone_needle","minecraft:stone_brick_wall","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_cherry_covered_desk","cfm:warped_kitchen_sink_light","sophisticatedstorage:decoration_table","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","mcwtrpdoors:cherry_cottage_trapdoor","minecraft:cyan_dye","evilcraft:crafting/dark_stick","connectedglass:scratched_glass_white_pane2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","immersiveengineering:crafting/glider","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","minecraft:gold_block","botania:floating_pure_daisy","dyenamics:banner/navy_banner","handcrafted:jungle_counter","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","minecraft:cherry_wood","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","connectedglass:scratched_glass_cyan2","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","twilightforest:canopy_chest_boat","mcwroofs:jungle_planks_attic_roof","mcwlights:golden_small_chandelier","botania:metamorphic_swamp_stone_slab","mcwwindows:lime_mosaic_glass","create_enchantment_industry:crafting/disenchanter","securitycraft:reinforced_observer","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwbiomesoplenty:umbran_highley_gate","mcwbiomesoplenty:dead_pane_window","utilitix:reinforced_rail","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","mcwbiomesoplenty:jacaranda_western_door","utilitix:stonecutter_cart","easy_villagers:auto_trader","cfm:dye_green_picket_fence","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","farmersdelight:cherry_cabinet","mcwroofs:cherry_planks_top_roof","mcwroofs:yellow_terracotta_lower_roof","littlelogistics:spring","allthecompressed:compress/jungle_planks_1x","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:mahogany_planks_path","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_middle_purple","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","create:cut_dripstone_brick_wall_from_stone_types_dripstone_stonecutting","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","supplementaries:candle_holders/candle_holder_yellow_dye","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","mcwroofs:orange_terracotta_upper_steep_roof","farmersdelight:steak_and_potatoes","mcwroofs:green_striped_awning","gtceu:shapeless/decompress_aluminium_from_ore_block","allthemodium:vibranium_plate","securitycraft:codebreaker","gtceu:shaped/piston_aluminium","rftoolsutility:energy_module","constructionwand:infinity_wand","aquaculture:wooden_fillet_knife","apotheosis:salvaging_table","reliquary:mob_charm_fragments/ghast","botania:metamorphic_desert_stone_wall","minecraft:polished_blackstone_wall","dyenamicsandfriends:green_dye","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","alltheores:gold_plate","dyenamics:spring_green_stained_glass_pane_from_glass_pane","cfm:blue_kitchen_counter","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwbridges:end_stone_bricks_bridge_stair","mcwroofs:base_top_roof","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","immersiveengineering:crafting/grit_sand","croptopia:soy_milk","create:crafting/kinetics/item_vault","mcwfences:warped_highley_gate","pneumaticcraft:minigun","megacells:network/cell_dock","supplementaries:statue","mcwbiomesoplenty:mahogany_tropical_door","minecraft:dye_green_carpet","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","enderio:dark_steel_grinding_ball","sophisticatedstorage:chipped/glassblower_upgrade","mcwpaths:sandstone_crystal_floor","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","mcwbiomesoplenty:fir_stockade_fence","silentgear:lining_template","ae2:network/blocks/pattern_providers_interface","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","alltheores:bronze_plate","create:crafting/kinetics/depot","pneumaticcraft:wall_lamp_yellow","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","mcwbiomesoplenty:mahogany_rail_bridge","pneumaticcraft:pressure_chamber_wall","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:end_stone_brick_stairs","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","domum_ornamentum:gray_floating_carpet","securitycraft:reinforced_dark_oak_fence_gate","minecraft:smooth_sandstone_stairs_from_smooth_sandstone_stonecutting","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","ae2:network/cables/dense_covered_brown","enderio:dark_steel_ladder","minecraft:cyan_terracotta","botania:floating_rafflowsia","ad_astra:green_industrial_lamp","silentgear:fine_silk","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","mcwbiomesoplenty:stripped_maple_pane_window","botania:spawner_claw","rftoolsstorage:storage_control_module","mcwbiomesoplenty:pine_nether_door","enderio:pressurized_fluid_tank","mcwbiomesoplenty:mahogany_barn_trapdoor","mcwbiomesoplenty:hellbark_barn_glass_door","fluxnetworks:fluxcore","immersiveengineering:crafting/treated_wood_horizontal","bigreactors:smelting/graphite_from_charcoal","mcwroofs:green_concrete_top_roof","connectedglass:borderless_glass_cyan2","mcwbiomesoplenty:jacaranda_wired_fence","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","reliquary:salamander_eye","mcwwindows:cherry_plank_window","handcrafted:bricks_pillar_trim","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","botania:metamorphic_plains_cobblestone_slab","pneumaticcraft:compressed_brick_slab","mcwbiomesoplenty:redwood_four_panel_door","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","immersiveengineering:crafting/alu_wallmount","pylons:potion_filter","create:crafting/kinetics/magenta_seat","create:crafting/kinetics/brown_seat","littlelogistics:energy_tug","handcrafted:quartz_corner_trim","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","handcrafted:cherry_corner_trim","dyenamics:fluorescent_stained_glass","alltheores:bronze_rod","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","pylons:interdiction_pylon","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","alltheores:aluminum_gear","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","railcraft:player_detector","create:crafting/appliances/copper_diving_boots","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","ae2:network/cables/smart_yellow","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","silentgear:crimson_iron_raw_ore_smelting","integratedterminals:crafting/part_terminal_storage","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","sfm:fancy_to_cable","mcwbiomesoplenty:palm_western_door","aether:netherite_sword_repairing","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","biomesoplenty:rabbit_stew_from_toadstool","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","cfm:black_picket_fence","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","xnet:router","mcwfurnitures:stripped_jungle_modern_chair","sliceanddice:sprinkler","mcwbiomesoplenty:willow_classic_door","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","mcwroofs:orange_concrete_lower_roof","cfm:magenta_kitchen_counter","easy_villagers:trader","minecraft:barrel","utilitix:tiny_coal_to_tiny","create:cut_calcite_brick_stairs_from_stone_types_calcite_stonecutting","supplementaries:boat_jar","ae2:materials/basiccard","handcrafted:oak_table","handcrafted:white_plate","modularrouters:regulator_augment","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwdoors:acacia_swamp_door","forbidden_arcanus:obsidian_skull","cfm:stripped_jungle_kitchen_sink_dark","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","mcwlights:blue_lamp","littlelogistics:car_dock_rail","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","mcwroofs:blue_concrete_attic_roof","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:cauldron","mcwbridges:nether_bricks_bridge","delightful:smelting/roasted_acorn","mcwwindows:stripped_mangrove_pane_window","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","mcwfurnitures:oak_kitchen_cabinet","aether:iron_boots_repairing","ae2:block_cutter/walls/smooth_sky_stone_wall","ae2:network/cables/dense_covered_magenta","biomesoplenty:mahogany_fence_gate","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:netherite_gloves_repairing","tombstone:impregnated_diamond","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwbiomesoplenty:dead_beach_door","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","minecraft:popped_chorus_fruit","supplementaries:candle_holders/candle_holder_lime_dye","mcwbiomesoplenty:fir_barn_glass_door","mcwbiomesoplenty:umbran_window2","mcwfurnitures:oak_double_kitchen_cabinet","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","minecraft:red_bed","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","dyenamics:dye_persimmon_carpet","utilitix:stone_wall","dyenamics:navy_dye","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwbiomesoplenty:jacaranda_swamp_door","silentgear:paxel_template","mcwbiomesoplenty:mahogany_stool_chair","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","botania:apothecary_mountain","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","productivebees:stonecutter/concrete_canvas_hive","createoreexcavation:vein_finder","immersiveengineering:crafting/reinforced_crate","silentgear:hoe_template","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwwindows:light_blue_mosaic_glass_pane","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","minecraft:copper_ingot_from_smelting_raw_copper","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","sophisticatedstorage:oak_limited_barrel_1","sophisticatedstorage:oak_limited_barrel_2","cfm:magenta_cooler","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","domum_ornamentum:beige_bricks","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mekanism:metallurgic_infuser","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","sophisticatedstorage:backpack_stack_upgrade_tier_3_from_storage_stack_upgrade_tier_4","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","alltheores:aluminum_rod","mcwroofs:yellow_striped_awning","minecraft:light_blue_terracotta","mcwwindows:spruce_plank_window","sophisticatedstorage:cherry_barrel","botania:chiseled_metamorphic_plains_bricks","botania:floating_hopperhock","minecraft:jungle_sign","minecraft:cyan_candle","mcwfences:railing_mud_brick_wall","expatternprovider:precise_storage_bus","domum_ornamentum:cactus_extra","cfm:cyan_cooler","ae2:network/parts/formation_plane","railcraft:silver_gear","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwbiomesoplenty:mahogany_mystic_door","create:crafting/kinetics/whisk","create:crafting/kinetics/mechanical_drill","create:cut_dripstone_wall_from_stone_types_dripstone_stonecutting","mcwroofs:light_gray_roof","mcwbiomesoplenty:umbran_beach_door","mcwroofs:gutter_base_purple","mcwbiomesoplenty:umbran_bark_glass_door","dyenamics:fluorescent_terracotta","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","mcwroofs:cherry_planks_upper_lower_roof","minecraft:slime_block","mcwwindows:blue_curtain","mekanismtools:bronze/tools/pickaxe","dyenamics:lavender_stained_glass_pane_from_glass_pane","create:cut_calcite_from_stone_types_calcite_stonecutting","chimes:glass_bells","mcwwindows:iron_shutter","mcwdoors:cherry_paper_door","minecraft:netherite_upgrade_smithing_template","handcrafted:kitchen_hood_pipe","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","mcwpaths:blackstone_running_bond_path","allthemodium:allthemodium_pickaxe","immersiveengineering:crafting/sawblade","mcwbiomesoplenty:empyreal_swamp_door","mcwbridges:blackstone_bridge_pier","modularrouters:flinger_module","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","sophisticatedstorage:basic_to_gold_tier_upgrade","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","minecraft:magenta_stained_glass_pane_from_glass_pane","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","alltheores:copper_rod","productivebees:stonecutter/cherry_canvas_hive","reliquary:uncrafting/gunpowder_creeper_gland","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","botania:metamorphic_desert_bricks_slab","mcwfences:warped_stockade_fence","mcwfurnitures:stripped_oak_double_drawer","aquaculture:cooked_fish_fillet","silentgear:crossbow_template","cfm:orange_kitchen_sink","mcwbiomesoplenty:mahogany_attic_roof","croptopia:pork_jerky","enderio:resetting_lever_sixty_from_inv","minecraft:polished_blackstone_bricks","minecraft:mossy_cobblestone_from_moss_block","mininggadgets:mininggadget_simple","mcwdoors:cherry_classic_door","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwbiomesoplenty:redwood_window","quark:building/crafting/cherry_bookshelf","mcwbiomesoplenty:willow_western_door","mcwbridges:nether_bricks_bridge_pier","ae2:network/cables/dense_covered_yellow","handcrafted:stone_corner_trim","mcwpaths:cobbled_deepslate_running_bond_path","create:crafting/kinetics/shaft","delightful:knives/bone_knife","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","mcwpaths:sandstone_square_paving","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","mcwwindows:mangrove_pane_window","productivebees:stonecutter/river_canvas_expansion_box","botania:metamorphic_taiga_stone_slab","farmersdelight:flint_knife","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","silentgear:chestplate_template","ae2:network/cables/glass_purple","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","botania:metamorphic_mesa_cobblestone_stairs","immersiveengineering:crafting/plate_steel_hammering","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","modularrouters:stack_upgrade","botania:metamorphic_mesa_stone_wall","alltheores:aluminum_plate","aquaculture:gold_nugget_from_blasting","bloodmagic:synthetic_point","ad_astra:small_yellow_industrial_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","minecraft:redstone_lamp","mcwbiomesoplenty:stripped_umbran_pane_window","supplementaries:candle_holders/candle_holder_cyan_dye","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwbiomesoplenty:dead_barn_glass_door","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","silentgear:coating_template","littlecontraptions:contraption_barge","productivetrees:crates/red_delicious_apple_crate_unpack","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwwindows:green_mosaic_glass_pane","mcwbiomesoplenty:pine_western_door","minecraft:dye_blue_bed","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:mahogany_upper_lower_roof","dyenamics:banner/fluorescent_banner","securitycraft:bouncing_betty","minecraft:painting","connectedglass:tinted_borderless_glass_blue2","connectedglass:tinted_borderless_glass_white2","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwbiomesoplenty:palm_stable_door","rftoolsbuilder:shield_block2","rftoolsbuilder:shield_block1","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","minecraft:stone_brick_slab_from_stone_stonecutting","mcwbiomesoplenty:palm_four_panel_door","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","terralith:dropper_alt","mcwlights:white_paper_lamp","create:crafting/materials/andesite_alloy_block","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","mcwwindows:mangrove_plank_pane_window","modularrouters:speed_upgrade","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","create:crafting/kinetics/placard","chimes:iron_chimes","farmersdelight:grilled_salmon","securitycraft:storage_module","immersiveengineering:crafting/chute_copper","mcwfurnitures:cherry_drawer","handcrafted:cherry_drawer","mcwfences:prismarine_grass_topped_wall","mcwbiomesoplenty:mahogany_cottage_trapdoor","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","twilightforest:mangrove_chest_boat","modularrouters:sync_upgrade","simplylight:illuminant_green_block_on_toggle","travelersbackpack:wolf","productivebees:stonecutter/mahogany_canvas_expansion_box","forbidden_arcanus:stone_blacksmith_gavel","minecraft:brick","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","mcwfurnitures:stripped_cherry_striped_chair","pneumaticcraft:wall_lamp_inverted_yellow","mcwdoors:cherry_cottage_door","cfm:stripped_oak_table","twigs:bone_meal_from_seashells","mcwlights:golden_triple_candle_holder","connectedglass:scratched_glass_lime2","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:coal","mcwbiomesoplenty:mahogany_bamboo_door","mcwroofs:blue_terracotta_upper_lower_roof","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","supplementaries:sack","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwdoors:cherry_stable_door","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","handcrafted:cherry_cupboard","cfm:stripped_oak_mail_box","mcwwindows:nether_brick_gothic","mcwbiomesoplenty:willow_mystic_door","mcwbiomesoplenty:pine_modern_door","create:andesite_ladder_from_andesite_alloy_stonecutting","reliquary:uncrafting/blaze_rod","minecraft:brown_dye","alltheores:silver_plate","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","cfm:lime_cooler","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","croptopia:roasted_sunflower_seeds","botania:fel_pumpkin","handcrafted:cherry_shelf","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","dyenamics:mint_dye","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","productivebees:stonecutter/cherry_canvas_expansion_box","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","mcwlights:iron_chandelier","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:prismarine_window2","utilitarian:tps_meter","mcwbiomesoplenty:pine_horse_fence","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","domum_ornamentum:yellow_floating_carpet","minecraft:blue_banner","mcwroofs:nether_bricks_upper_steep_roof","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","ae2:decorative/cut_quartz_block","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","handcrafted:cherry_pillar_trim","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","alltheores:gold_rod","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","reliquary:mercy_cross","farmersdelight:fried_egg","mcwbiomesoplenty:maple_pane_window","botania:metamorphic_fungal_stone_slab","mcwfurnitures:oak_bookshelf_cupboard","mcwfurnitures:cherry_double_drawer_counter","travelersbackpack:pumpkin","mcwfurnitures:stripped_oak_table","connectedglass:clear_glass_red2","create:polished_cut_dripstone_stairs_from_stone_types_dripstone_stonecutting","mcwbiomesoplenty:mahogany_table","botania:metamorphic_mesa_bricks_slab","croptopia:pork_and_beans","mcwpaths:blackstone_windmill_weave_stairs","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","create:crafting/kinetics/belt_connector","mcwdoors:jail_door","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","reliquary:mob_charm_fragments/enderman","allthemodium:allthemodium_axe","pneumaticcraft:wall_lamp_inverted_gray","minecraft:dye_lime_carpet","mcwpaths:brick_running_bond_path","rftoolsbuilder:shape_card_quarry_dirt","mcwbiomesoplenty:magic_window2","immersiveengineering:crafting/minecart_reinforcedcrate","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","silentgear:crimson_repair_kit","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","minecraft:sea_lantern","immersiveengineering:crafting/lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","minecraft:dye_cyan_wool","mcwbiomesoplenty:stripped_mahogany_counter","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","mcwroofs:blackstone_top_roof","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","cfm:gray_cooler","everythingcopper:copper_axe","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","connectedglass:scratched_glass_green_pane2","mcwroofs:stone_upper_steep_roof","connectedglass:scratched_glass_white2","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","simplylight:edge_light","mcwfences:railing_granite_wall","immersiveengineering:crafting/plate_uranium_hammering","handcrafted:oak_nightstand","railcraft:personal_world_spike","mcwlights:lava_lamp","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","xnet:redstoneproxy_update","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","immersiveengineering:crafting/wirecoil_copper_ins","mcwroofs:nether_bricks_attic_roof","delightful:food/cooking/ender_nectar","merequester:requester","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","silentgear:crimson_steel_ingot","allthemodium:allthemodium_plate","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","botania:metamorphic_fungal_bricks_wall","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","mcwroofs:nether_bricks_steep_roof","travelersbackpack:end","croptopia:campfire_toast","reliquary:midas_touchstone","minecraft:cherry_stairs","mcwroofs:bricks_upper_steep_roof","create:crafting/appliances/slime_ball","mcwbiomesoplenty:willow_plank_four_window","reliquary:fortune_coin","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","create:crafting/kinetics/cart_assembler","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","mcwpaths:blackstone_running_bond_slab","minecraft:birch_boat","cfm:post_box","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwwindows:brown_curtain","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwbiomesoplenty:empyreal_western_door","cfm:orange_cooler","minecolonies:mint_jelly","dyenamics:dye_wine_carpet","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","pneumaticcraft:wall_lamp_light_blue","computercraft:monitor_advanced","create:layered_calcite_from_stone_types_calcite_stonecutting","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwroofs:gray_terracotta_top_roof","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","create:crafting/kinetics/filter","modularrouters:puller_module_1","mcwbiomesoplenty:umbran_stable_door","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mysticalagriculture:inferium_seeds","mcwbiomesoplenty:willow_stable_head_door","trashcans:energy_trash_can","silentgear:blaze_gold_ingot_from_nugget","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","mcwfurnitures:stripped_cherry_modern_desk","mcwroofs:sandstone_upper_lower_roof","minecolonies:baked_salmon","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","croptopia:burrito","pneumaticcraft:security_upgrade","mcwbiomesoplenty:umbran_horse_fence","minecraft:cooked_salmon_from_campfire_cooking","integrateddynamics:crafting/squeezer","dyenamics:dye_icy_blue_carpet","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwbiomesoplenty:stripped_mahogany_covered_desk","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","mcwroofs:grass_attic_roof","reliquary:mob_charm_fragments/wither_skeleton","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","immersiveengineering:crafting/radiator","minecraft:white_stained_glass_pane_from_glass_pane","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","biomesoplenty:mahogany_chest_boat","handcrafted:cherry_nightstand","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","cfm:dye_lime_picket_gate","modularrouters:mimic_augment","alltheores:sapphire_from_hammer_crushing","sophisticatedstorage:basic_to_diamond_tier_upgrade","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","twigs:schist","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","minecraft:andesite_slab","minecraft:bamboo_chest_raft","dyenamics:bed/bubblegum_bed_frm_white_bed","create:crafting/kinetics/large_cogwheel","productivebees:stonecutter/willow_canvas_expansion_box","mcwtrpdoors:cherry_whispering_trapdoor","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","reliquary:destruction_catalyst","create:crafting/kinetics/metal_bracket","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","croptopia:oatmeal","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","mcwbiomesoplenty:empyreal_barn_glass_door","mcwbiomesoplenty:pine_highley_gate","handcrafted:cherry_chair","mcwfences:ornate_metal_fence","rftoolsbuilder:mover_control2","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","cfm:black_kitchen_drawer","immersiveengineering:crafting/drillhead_iron","allthemods:easy_sticks","mcwwindows:bricks_pane_window","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","create:crafting/kinetics/gantry_carriage","mcwroofs:base_upper_steep_roof","mcwbiomesoplenty:stripped_fir_log_four_window","ae2:network/blocks/cell_workbench","mcwdoors:cherry_whispering_door","create:crafting/logistics/powered_latch","solcarrot:food_book","botania:metamorphic_forest_bricks_stairs","utilitarian:utility/charcoal_from_campfire","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","sophisticatedstorage:stonecutter_upgrade","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","farmersdelight:cooking/pasta_with_meatballs","botania:floating_agricarnation","mcwfurnitures:cherry_cupboard_counter","create:crafting/materials/andesite_alloy_from_block","minecraft:jungle_fence_gate","create:crafting/kinetics/water_wheel","mcwwindows:birch_louvered_shutter","mcwbiomesoplenty:maple_stable_door","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","travelersbackpack:dye_yellow_sleeping_bag","mcwpaths:blackstone_crystal_floor_path","cfm:jungle_cabinet","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:base_roof_block","cfm:oak_blinds","mcwbiomesoplenty:palm_window","twigs:blackstone_column","botania:floating_spectrolus","mcwbiomesoplenty:mahogany_beach_trapdoor","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","littlecontraptions:barge_assembler","dyenamics:navy_candle","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","minecraft:polished_granite_slab_from_polished_granite_stonecutting","mcwfences:deepslate_pillar_wall","cfm:jungle_chair","mcwlights:birch_tiki_torch","matc:imperium_essence","create:crafting/kinetics/sticker","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","mcwpaths:brick_square_paving","minecraft:red_candle","mcwbiomesoplenty:stripped_mahogany_end_table","mcwbiomesoplenty:hellbark_four_window","sophisticatedstorage:backpack_stack_upgrade_tier_2_from_storage_stack_upgrade_tier_3","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","fluxnetworks:fluxpoint","alltheores:platinum_rod","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","dyenamics:bed/fluorescent_bed_frm_white_bed","allthearcanistgear:vibranium_robes_smithing","mcwbridges:sandstone_bridge_pier","biomesoplenty:rose_quartz_block","botania:ender_eye_block","minecraft:decorated_pot_simple","mcwbiomesoplenty:stripped_dead_pane_window","ae2:shaped/walls/fluix_block","mcwroofs:magenta_terracotta_steep_roof","biomesoplenty:mahogany_stairs","minecraft:oak_pressure_plate","aether:netherite_leggings_repairing","bigreactors:turbine/basic/activefluidport_forge","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","cfm:stripped_jungle_cabinet","mcwbiomesoplenty:redwood_tropical_door","reliquary:mob_charm_fragments/creeper","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","minecolonies:mint_tea","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","twilightforest:transformation_chest_boat","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","dyenamics:rose_wool","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","mcwbiomesoplenty:mahogany_bookshelf_drawer","minecraft:red_nether_bricks","mcwdoors:acacia_western_door","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","botania:metamorphic_swamp_bricks_wall","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","modularrouters:fluid_module","mcwlights:purple_lamp","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","mcwroofs:purple_terracotta_roof","mcwfurnitures:stripped_jungle_chair","minecraft:moss_carpet","mcwroofs:jungle_upper_steep_roof","rftoolsbase:machine_frame","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","railcraft:brass_gear","botania:floating_rannuncarpus_chibi","pylons:infusion_pylon","create:crafting/kinetics/linear_chassis","forbidden_arcanus:diamond_blacksmith_gavel","domum_ornamentum:light_blue_brick_extra","mcwpaths:stone_windmill_weave_slab","mcwroofs:thatch_top_roof","supplementaries:cog_block","pneumaticcraft:armor_upgrade","enderio:dark_steel_sword","minecraft:lime_concrete_powder","minecraft:leather_leggings","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","mcwbiomesoplenty:magic_tropical_door","croptopia:fruit_salad","securitycraft:block_change_detector","minecraft:cherry_pressure_plate","minecraft:netherite_scrap_from_blasting","additionallanterns:amethyst_chain","immersiveengineering:crafting/minecart_woodencrate","mcwbridges:bridge_torch","mcwbridges:cherry_bridge_pier","domum_ornamentum:magenta_brick_extra","undergarden:wigglewood_boat","minecraft:book","mcwfurnitures:oak_drawer_counter","handcrafted:blue_sheet","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","additionallanterns:quartz_chain","travelersbackpack:standard_smithing","silentgear:prospector_hammer_template","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","connectedglass:scratched_glass_cyan_pane2","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","immersiveengineering:crafting/strip_lv","pneumaticcraft:pressure_chamber_glass_x4","pneumaticcraft:pressure_chamber_glass_x1","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","silentgear:axe_template","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","delightful:knives/allthemodium_knife","domum_ornamentum:sand_bricks","ae2:network/cables/dense_covered_cyan","mcwbiomesoplenty:palm_beach_door","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","pneumaticcraft:heat_pipe","minecraft:cracked_polished_blackstone_bricks","matc:crystals/tertium","pneumaticcraft:wall_lamp_inverted_purple","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","immersiveengineering:crafting/thermoelectric_generator","mcwbiomesoplenty:magic_bamboo_door","ae2:misc/tiny_tnt","ae2:decorative/quartz_glass","minecraft:red_terracotta","mcwdoors:jungle_japanese_door","immersiveengineering:crafting/stairs_treated_wood_horizontal","mcwbiomesoplenty:mahogany_ranch_trapdoor","silentgear:boots_template","mcwroofs:white_roof_block","mcwbiomesoplenty:mahogany_stable_door","mob_grinding_utils:recipe_tank_sink","securitycraft:block_pocket_manager","create:crafting/kinetics/encased_chain_drive","mcwlights:soul_cherry_tiki_torch","immersiveengineering:crafting/treated_wallmount","securitycraft:reinforced_mangrove_fence","torchmaster:megatorch","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","mcwlights:red_lamp","bloodmagic:blood_altar","botania:flower_bag","botania:metamorphic_mesa_stone_slab","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:cherry_striped_chair","aether:stone_sword_repairing","botania:chiseled_metamorphic_fungal_bricks","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwlights:light_blue_lamp","mcwbiomesoplenty:fir_window2","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwbiomesoplenty:fir_plank_window2","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","pneumaticcraft:block_tracker_upgrade","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","create:crafting/kinetics/hose_pulley","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","biomesoplenty:mahogany_wood","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","littlelogistics:tee_junction_rail","functionalstorage:framed_1","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:bowl","mcwwindows:ender_brick_arrow_slit","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwpaths:blackstone_diamond_paving","ad_astra:lime_industrial_lamp","minecraft:purple_concrete_powder","mcwfurnitures:stripped_cherry_modern_wardrobe","mcwfences:bastion_metal_fence","modularrouters:fluid_upgrade","mcwbiomesoplenty:mahogany_wardrobe","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:jungle_counter","minecraft:red_carpet","cfm:light_blue_kitchen_sink","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","waystones:warp_stone","silentgear:excavator_template","mcwfurnitures:cherry_glass_table","mcwfurnitures:stripped_cherry_desk","supplementaries:candle_holders/candle_holder_light_gray","mcwbiomesoplenty:jacaranda_japanese2_door","reliquary:mob_charm_fragments/zombified_piglin","create:crafting/materials/red_sand_paper","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:blue_terracotta_steep_roof","alltheores:lead_dust_from_hammer_crushing","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","allthearcanistgear:vibranium_hat_smithing","mcwroofs:oak_planks_attic_roof","bigreactors:crafting/graphite_component_to_storage","dyenamics:amber_terracotta","sophisticatedstorage:basic_to_copper_tier_upgrade","create:crafting/kinetics/white_sail","twigs:smooth_basalt_bricks","aether:white_cape","botania:metamorphic_fungal_bricks_slab","ae2:tools/misctools_entropy_manipulator","mcwbiomesoplenty:mahogany_cupboard_counter","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","minecraft:daylight_detector","mcwfences:panelled_metal_fence_gate","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","allthemodium:ancient_stone_wall","aquaculture:planks_from_driftwood","mcwfurnitures:oak_drawer","mcwfurnitures:cherry_coffee_table","create:crafting/kinetics/mechanical_pump","mcwtrpdoors:cherry_four_panel_trapdoor","simplylight:lamp_post","minecraft:hopper","mcwroofs:base_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","create:crafting/logistics/powered_toggle_latch","silentgear:machete_template","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","corail_woodcutter:mangrove_woodcutter","mcwpaths:brick_dumble_paving","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","botania:metamorphic_mesa_bricks_wall","minecraft:iron_helmet","botania:red_string_alt","mcwbiomesoplenty:mahogany_barn_glass_door","create:crafting/kinetics/cyan_seat","mcwroofs:lime_concrete_attic_roof","allthemodium:allthemodium_sword","minecraft:cartography_table","railcraft:manual_rolling_machine","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","mcwbiomesoplenty:umbran_pane_window","pneumaticcraft:compressed_brick_tile","ae2:network/blocks/energy_vibration_chamber","ae2:tools/fluix_shovel","reliquary:magicbane","pneumaticcraft:wall_lamp_inverted_magenta","minecraft:cooked_porkchop_from_smoking","mcwfurnitures:stripped_cherry_double_drawer_counter","ae2:block_cutter/stairs/sky_stone_stairs","reliquary:uncrafting/gold_nugget","additionallanterns:amethyst_lantern","mcwwindows:green_curtain","mcwwindows:stone_window","mcwroofs:gray_steep_roof","mcwfurnitures:cherry_stool_chair","rftoolspower:endergenic","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","croptopia:pumpkin_bars","productivebees:stonecutter/yucca_canvas_hive","mcwtrpdoors:jungle_classic_trapdoor","dankstorage:dank_6","dankstorage:dank_7","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","dankstorage:dank_3","dankstorage:dank_4","mcwroofs:yellow_terracotta_upper_lower_roof","dankstorage:dank_5","mcwpaths:cobbled_deepslate_flagstone_path","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","pneumaticcraft:speed_upgrade_from_glycerol","allthemods:constructionwand/iron_wand","mcwfurnitures:jungle_chair","computercraft:turtle_normal/minecraft/diamond_pickaxe","minecraft:heavy_weighted_pressure_plate","rftoolsbuilder:shape_card_pump_dirt","cfm:stripped_oak_crate","immersiveengineering:crafting/wirecutter","mcwdoors:oak_bark_glass_door","minecraft:polished_blackstone_stairs","mcwbiomesoplenty:magic_mystic_door","handcrafted:cherry_side_table","minecraft:netherite_pickaxe_smithing","travelersbackpack:sandstone","farmersdelight:cooking/fried_rice","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","enderio:resetting_lever_ten","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","mcwfurnitures:stripped_cherry_bookshelf_cupboard","mcwdoors:cherry_stable_head_door","minecraft:polished_deepslate","connectedglass:borderless_glass_yellow_pane2","simplylight:illuminant_gray_block_on_toggle","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","travelersbackpack:netherite","mcwwindows:quartz_pane_window","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:cooked_porkchop_from_campfire_cooking","minecraft:cyan_stained_glass_pane_from_glass_pane","create:cut_calcite_wall_from_stone_types_calcite_stonecutting","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","supplementaries:stonecutting/blackstone_tile","silentgear:fletching_template","minecraft:dye_red_carpet","mcwroofs:light_blue_terracotta_attic_roof","croptopia:tofu","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","botania:chiseled_metamorphic_mountain_bricks","additionallanterns:stone_bricks_chain","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwfences:spruce_wired_fence","handcrafted:andesite_corner_trim","mcwroofs:magenta_terracotta_top_roof","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","mcwbiomesoplenty:stripped_mahogany_double_wardrobe","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","securitycraft:claymore","railcraft:golden_ticket","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwbiomesoplenty:redwood_glass_door","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","mcwroofs:cherry_steep_roof","domum_ornamentum:sand_stone_bricks","mcwfurnitures:stripped_cherry_end_table","mcwroofs:orange_concrete_upper_lower_roof","productivebees:stonecutter/hellbark_canvas_hive","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","botania:metamorphic_plains_bricks_wall","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","ae2:network/cells/view_cell","mcwbiomesoplenty:maple_beach_door","enderio:resetting_lever_ten_from_inv","mcwbiomesoplenty:magic_four_panel_door","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","minecraft:cookie","aquaculture:spruce_fish_mount","mcwroofs:cherry_upper_lower_roof","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","minecraft:shears","dyenamics:lavender_terracotta","mcwroofs:jungle_planks_top_roof","ae2:tools/certus_quartz_hoe","minecraft:yellow_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:redwood_pane_window","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwbiomesoplenty:palm_barn_door","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","silentgear:binding_template","mcwbiomesoplenty:palm_mystic_door","deeperdarker:echo_chest_boat","eccentrictome:tome","securitycraft:reinforced_warped_fence","modularrouters:filter_round_robin_augment","alltheores:osmium_dust_from_hammer_crushing","computercraft:wireless_modem_normal","aether:aether_saddle","create:crafting/kinetics/rope_pulley","create:crafting/kinetics/cuckoo_clock","mcwbiomesoplenty:redwood_hedge","ae2:network/cables/covered_magenta","rftoolsstorage:storage_module0","minecraft:kjs/bigreactors_basic_reactorcasing","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","mcwfurnitures:stripped_jungle_cupboard_counter","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","mcwroofs:cherry_planks_upper_steep_roof","supplementaries:turn_table","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","silentgear:crimson_steel_dust_blasting","mcwdoors:acacia_whispering_door","twilightforest:sorting_boat","mcwbiomesoplenty:jacaranda_cottage_door","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mekanism:cardboard_box","ae2:network/cables/covered_gray","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","computercraft:turtle_normal/minecraft/diamond_sword","mcwwindows:stone_four_window","immersiveengineering:crafting/craftingtable","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","reliquary:lantern_of_paranoia","mysticalagriculture:essence/integrateddynamics/menril_log","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","mcwfurnitures:cherry_drawer_counter","mcwwindows:gray_curtain","minecraft:brewing_stand","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","supplementaries:bamboo_spikes","evilcraft:crafting/dark_spike","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:andesite_crystal_floor_path","create:crafting/kinetics/orange_seat","rftoolsutility:syringe","minecraft:diamond_block","mcwroofs:deepslate_roof","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","bigreactors:blasting/graphite_from_coal","minecraft:pumpkin_seeds","mcwwindows:dark_prismarine_four_window","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","minecraft:cooked_mutton","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","computercraft:wireless_modem_advanced","minecraft:lapis_block","aether:skyroot_chest","deepresonance:filter_material","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","dankstorage:3_to_4","utilitarian:snad/snad","rftoolsstorage:modular_storage","botania:metamorphic_mountain_stone_wall","croptopia:shaped_bacon","handcrafted:bricks_corner_trim","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","mcwroofs:pink_terracotta_attic_roof","dyenamics:spring_green_dye","mcwroofs:light_gray_attic_roof","minecraft:oak_boat","rftoolsbuilder:shape_card_pump","mcwfurnitures:stripped_jungle_covered_desk","cfm:black_picket_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwbiomesoplenty:pine_paper_door","silentgear:knife_template","ae2:network/cables/smart_green","minecraft:jungle_fence","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","twilightforest:transformation_boat","domum_ornamentum:green_cactus_extra","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","domum_ornamentum:green_cobblestone_extra","pneumaticcraft:air_canister","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","mcwbridges:cherry_rail_bridge","littlelogistics:automatic_tee_junction_rail","mcwbridges:brick_bridge_pier","silentgear:crimson_iron_ingot_from_block","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","silentgear:crimson_iron_nugget","mcwfences:end_brick_pillar_wall","dyenamics:ultramarine_dye","dyenamics:honey_dye","mcwwindows:prismarine_brick_arrow_slit","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:purple_dye","mcwfurnitures:jungle_modern_chair","create:cut_calcite_brick_wall_from_stone_types_calcite_stonecutting","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","handcrafted:phantom_trophy","minecraft:quartz_slab","reliquary:mob_charm_fragments/magma_cube","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","mcwfurnitures:stripped_jungle_double_drawer","mcwbiomesoplenty:palm_bamboo_door","farmersdelight:bread_from_smelting","ae2:network/cables/smart_black","minecraft:kjs/ender_pearl","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","mcwbiomesoplenty:stripped_mahogany_drawer_counter","botania:apothecary_mesa","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","silentgear:mattock_template","create:small_dripstone_brick_stairs_from_stone_types_dripstone_stonecutting","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:dark_oak_mail_box","botania:metamorphic_desert_bricks_stairs","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/windmill_sail","farmersdelight:rice_bag","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","ae2:block_cutter/stairs/fluix_stairs","farmersdelight:chicken_sandwich","mcwbiomesoplenty:palm_swamp_door","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","handcrafted:blue_bowl","mcwfurnitures:stripped_oak_double_wardrobe","minecraft:polished_blackstone_brick_slab","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","silentgear:crimson_steel_ingot_from_nugget","dyenamics:dye_amber_carpet","immersiveengineering:crafting/workbench","ae2:network/cables/smart_lime","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","handcrafted:kitchen_hood","supplementaries:fodder","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","mcwbiomesoplenty:fir_swamp_door","dyenamics:banner/aquamarine_banner","mcwfurnitures:stripped_oak_glass_table","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","utilitarian:utility/jungle_logs_to_doors","create:polished_cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","twigs:cut_amethyst_from_amethyst_block_stonecutting","reliquary:ender_staff","mcwwindows:diorite_louvered_shutter","mcwfurnitures:oak_end_table","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","mcwbiomesoplenty:mahogany_roof","silentgear:mod_kit","bigreactors:reprocessor/fluidinjector","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwfurnitures:jungle_modern_desk","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","ae2:materials/advancedcard","minecraft:cooked_porkchop","silentgear:blaze_gold_nugget","mcwlights:gray_lamp","dyenamics:maroon_stained_glass","botania:metamorphic_swamp_cobblestone_slab","minecraft:oak_wood","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","pneumaticcraft:pressure_chamber_valve_x1","mysticalagriculture:prosperity_seed_base","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwbiomesoplenty:redwood_bamboo_door","mcwroofs:cherry_planks_attic_roof","mcwpaths:sandstone_clover_paving","aether:leather_helmet_repairing","aquaculture:sushi","mcwdoors:metal_reinforced_door","silentgear:crimson_iron_dust_smelting","mcwbiomesoplenty:magic_japanese2_door","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","ae2:tools/fluix_upgrade_smithing_template","mcwdoors:cherry_tropical_door","mcwbiomesoplenty:jacaranda_pyramid_gate","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","mcwlights:jungle_ceiling_fan_light","botania:metamorphic_plains_bricks","railcraft:tunnel_bore","botania:metamorphic_mesa_cobblestone_slab","mcwpaths:sandstone_flagstone_stairs","bigreactors:turbine/basic/activetap_fe","ae2:network/cables/covered_light_gray","mcwroofs:gutter_base_lime","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","modularrouters:fast_pickup_augment","handcrafted:jungle_table","minecraft:andesite_wall_from_andesite_stonecutting","mcwfences:crimson_stockade_fence","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","deeperdarker:bloom_chest_boat","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","mcwbiomesoplenty:empyreal_barn_door","minecraft:powered_rail","sgjourney:sandstone_hieroglyphs","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","additionallanterns:stone_lantern","botania:metamorphic_mountain_bricks_slab","supplementaries:flags/flag_red","cfm:dye_blue_picket_fence","mcwbridges:end_stone_bricks_bridge","minecraft:dye_white_bed","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:light_gray_terracotta_steep_roof","mcwbiomesoplenty:hellbark_nether_door","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","mcwbiomesoplenty:empyreal_tropical_door","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","mcwbiomesoplenty:empyreal_paper_door","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","minecraft:gray_terracotta","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","minecraft:netherite_boots_smithing","mob_grinding_utils:recipe_tintedglass","mcwbiomesoplenty:mahogany_triple_drawer","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","dyenamics:peach_terracotta","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","minecraft:terracotta","delightful:knives/silver_knife","mcwwindows:acacia_blinds","aether:leather_chestplate_repairing","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwwindows:dark_oak_window2","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","connectedglass:scratched_glass_yellow2","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","immersiveengineering:crafting/minecart_woodenbarrel","blue_skies:lunar_bookshelf","mcwroofs:cyan_terracotta_top_roof","dankstorage:5_to_6","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","productivebees:stonecutter/redwood_canvas_hive","minecraft:beacon","minecraft:tnt","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:dye_yellow_bed","minecraft:flint_and_steel","mcwdoors:cherry_nether_door","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwfurnitures:stripped_jungle_double_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","handcrafted:dripstone_corner_trim","alltheores:invar_dust_from_alloy_blending","xnet:advanced_connector_blue_dye","modularrouters:stack_augment","mcwdoors:cherry_barn_door","mcwbiomesoplenty:umbran_bamboo_door","mcwbiomesoplenty:empyreal_plank_pane_window","botania:purple_shiny_flower","mcwdoors:oak_bamboo_door","travelersbackpack:hay","xnet:advanced_connector_red_dye","mcwdoors:cherry_modern_door","mcwroofs:brown_striped_awning","mcwbiomesoplenty:mahogany_whispering_trapdoor","minecraft:brown_concrete_powder","botania:metamorphic_plains_stone_wall","mcwbiomesoplenty:stripped_mahogany_modern_desk","securitycraft:reinforced_lime_stained_glass_pane_from_dye","rftoolsbase:tablet","create:crafting/kinetics/turntable","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","simplylight:bulb","travelersbackpack:cactus","mcwwindows:granite_four_window","cfm:door_mat","mcwroofs:light_blue_concrete_steep_roof","supplementaries:stone_tile","handcrafted:terracotta_plate","supplementaries:item_shelf","bigreactors:reactor/basic/chargingfe","mcwbiomesoplenty:willow_barn_glass_door","mcwroofs:light_blue_terracotta_roof","allthemodium:vibranium_gear","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","silentgear:coating_smithing_template","minecraft:grindstone","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","supplementaries:timber_brace","botania:metamorphic_desert_stone_stairs","handcrafted:jungle_nightstand","domum_ornamentum:gray_brick_extra","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","twilightforest:wood/cherry_banister","supplementaries:bed_from_feather_block","mcwbiomesoplenty:stripped_mahogany_striped_chair","bigreactors:reactor/reinforced/activetap_fe","bigreactors:reactor/basic/controlrod","cfm:red_kitchen_counter","mcwlights:copper_chain","minecraft:furnace_minecart","allthemods:rechiseled/chisel","create:crafting/kinetics/cogwheel","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","cfm:orange_kitchen_drawer","enderio:cold_fire_igniter","ae2:network/cables/covered_green","minecraft:netherite_ingot_from_netherite_block","create:crafting/appliances/netherite_diving_boots_from_netherite","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","twigs:smooth_basalt_bricks_stonecutting","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwlights:pink_paper_lamp","ad_astra:white_industrial_lamp","immersiveengineering:crafting/wire_aluminum","dyenamics:bubblegum_terracotta","utilitix:hand_bell","rftoolscontrol:workbench","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","bigreactors:turbine/basic/bearing","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","mcwroofs:cyan_striped_awning","dyenamics:dye_mint_carpet","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","aether:wooden_shovel_repairing","functionalstorage:cherry_1","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","connectedglass:clear_glass_black_pane2","mcwfences:railing_stone_brick_wall","botania:pool_minecart","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","sfm:cable","mcwbiomesoplenty:palm_japanese_door","minecraft:hay_block","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:jacaranda_barn_glass_door","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","create:jungle_window","ae2:network/parts/monitors_storage","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_planks_attic_roof","mcwroofs:blue_concrete_top_roof","evilcraft:smelting/hardened_blood_shard","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwbiomesoplenty:mahogany_paper_trapdoor","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","supplementaries:flags/flag_orange","botania:metamorphic_plains_bricks_stairs","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","modularrouters:augment_core","ae2:network/cables/covered_black","minecraft:smooth_sandstone_stairs","mcwbiomesoplenty:stripped_mahogany_modern_chair","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","pneumaticcraft:liquid_compressor","connectedglass:clear_glass_red_pane2","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","mcwroofs:black_concrete_top_roof","enderio:basic_item_filter","ae2:network/blocks/inscribers","mcwwindows:granite_louvered_shutter","ad_astra:red_flag","ad_astra:small_red_industrial_lamp","securitycraft:sentry","minecraft:netherite_block","alltheores:uranium_rod","cfm:cyan_kitchen_counter","mcwwindows:blue_mosaic_glass_pane","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","connectedglass:tinted_borderless_glass_yellow2","create:cut_dripstone_brick_stairs_from_stone_types_dripstone_stonecutting","mcwbiomesoplenty:mahogany_desk","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","mcwtrpdoors:cherry_beach_trapdoor","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","reliquary:glowing_bread","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwtrpdoors:cherry_classic_trapdoor","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","botania:metamorphic_mesa_bricks","quark:building/crafting/gold_bars","mcwroofs:gray_concrete_upper_lower_roof","utilitix:piston_controller_rail","handcrafted:yellow_crockery_combo","create:crafting/curiosities/peculiar_bell","mcwbiomesoplenty:umbran_mystic_door","expatternprovider:assembler_matrix_frame","securitycraft:reinforced_granite","alltheores:aluminum_dust_from_hammer_crushing","allthemodium:vibranium_nugget_from_ingot","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","handcrafted:cherry_dining_bench","mcwwindows:oak_plank_window2","connectedglass:scratched_glass_red_pane2","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:network/cables/glass_light_gray","botania:metamorphic_forest_bricks_wall","mcwbiomesoplenty:mahogany_covered_desk","immersiveengineering:crafting/coke_to_coal_coke","immersiveengineering:crafting/chute_iron","modularrouters:range_up_from_down","twigs:mixed_bricks_stonecutting","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","botania:metamorphic_fungal_stone_stairs","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","connectedglass:scratched_glass_blue2","minecraft:blue_bed","domum_ornamentum:pink_floating_carpet","botania:metamorphic_mountain_bricks_stairs","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","create:crafting/kinetics/mechanical_harvester","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","minecraft:quartz_stairs","immersiveengineering:crafting/waterwheel_segment","minecraft:red_stained_glass","minecraft:light_gray_stained_glass_pane_from_glass_pane","silentgear:leather_from_scraps","mcwpaths:cobbled_deepslate_crystal_floor_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","mcwpaths:cobbled_deepslate_crystal_floor","farmersdelight:beef_patty","mcwbiomesoplenty:magic_stable_door","utilitix:reinforced_crossing_rail","connectedglass:borderless_glass_blue_pane2","mcwbiomesoplenty:mahogany_modern_desk","mcwpaths:blackstone_crystal_floor","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","twigs:smooth_basalt_brick_slab_from_smooth_basalt_stonecutting","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","delightful:food/marshmallow_stick","minecraft:lime_terracotta","mcwbiomesoplenty:mahogany_drawer","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","bigreactors:reactor/basic/casing_recycle","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","minecraft:gray_stained_glass_pane_from_glass_pane","minecraft:stone_slab","mcwbiomesoplenty:magic_classic_door","productivebees:stonecutter/comb_canvas_hive","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","silentgear:blaze_gold_ingot_from_block","mcwbiomesoplenty:maple_four_panel_door","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","mcwfurnitures:jungle_bookshelf_cupboard","cfm:jungle_desk_cabinet","travelersbackpack:backpack_tank","minecraft:jungle_stairs","mcwbridges:glass_bridge","ae2:materials/cardenergy","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","enderio:resetting_lever_ten_inv_from_base","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","dyenamics:bed/mint_bed_frm_white_bed","croptopia:campfire_molasses","mcwbiomesoplenty:pine_four_panel_door","ae2:network/upgrade_wireless_crafting_terminal","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","utilitix:weak_redstone_torch","mcwbiomesoplenty:mahogany_planks_steep_roof","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","botania:metamorphic_forest_stone_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:jacaranda_glass_door","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","minecraft:cooked_salmon","mcwfences:expanded_mesh_metal_fence","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","simplylight:illuminant_purple_block_on_toggle","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","modularrouters:range_up_augment","mcwwindows:spruce_shutter","trashcans:ultimate_trash_can","twilightforest:mining_chest_boat","croptopia:roasted_pumpkin_seeds","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","create:crafting/kinetics/yellow_seat","mcwpaths:andesite_windmill_weave_path","mcwbiomesoplenty:palm_glass_door","mcwfurnitures:stripped_cherry_stool_chair","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwbiomesoplenty:mahogany_upper_steep_roof","mcwroofs:green_terracotta_steep_roof","allthecompressed:compress/hay_block_1x","mcwdoors:garage_silver_door","comforts:hammock_white","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwfurnitures:stripped_oak_cupboard_counter","immersiveengineering:crafting/wirecoil_structure_steel","botania:floating_hydroangeas","mcwwindows:dark_oak_curtain_rod","mcwroofs:gray_concrete_lower_roof","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","create:crafting/kinetics/portable_fluid_interface","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","handcrafted:red_sheet","sophisticatedstorage:paintbrush","biomesoplenty:willow_boat","mcwwindows:stone_brick_arrow_slit","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","mcwbiomesoplenty:umbran_waffle_door","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","croptopia:ham_sandwich","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","mcwbiomesoplenty:redwood_barn_door","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","silentgear:dagger_template","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwbiomesoplenty:palm_modern_door","rftoolsutility:counter_module","handcrafted:cherry_bench","minecraft:amethyst_block","cfm:stripped_jungle_crate","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwroofs:black_roof_block","create:crafting/kinetics/wrench","immersiveengineering:crafting/wooden_grip","connectedglass:clear_glass_cyan_pane2","mcwlights:warped_ceiling_fan_light","botania:prism","mcwwindows:window_centre_bar_base","modularrouters:sender_module_2_x4","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","handcrafted:oak_corner_trim","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","computercraft:wired_modem","ae2:tools/fluix_sword","utilitarian:utility/cherry_logs_to_trapdoors","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","rftoolspower:pearl_injector","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","twigs:cobblestone_bricks_stonecutting","allthemodium:allthemodium_shovel","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","pylons:harvester_pylon","mcwfurnitures:stripped_cherry_counter","minecraft:wheat","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","mcwbiomesoplenty:mahogany_four_panel_trapdoor","ae2:network/cables/glass_fluix_clean","minecraft:bow","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aquaculture:brown_mushroom_from_fish","railways:jukeboxcart","expatternprovider:silicon_block_disassembler","mcwwindows:light_gray_mosaic_glass_pane","minecraft:andesite_stairs","handcrafted:bear_trophy","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","botania:metamorphic_desert_cobblestone_stairs","twigs:polished_calcite_stonecutting","ae2:network/parts/storage_bus","mcwwindows:jungle_plank_parapet","botania:spark","minecraft:dark_oak_boat","dyenamics:maroon_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:palm_wired_fence","dyenamics:bed/aquamarine_bed_frm_white_bed","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","create:crafting/kinetics/clutch","botania:ghost_rail","minecraft:firework_rocket_simple","create:crafting/kinetics/encased_fan","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","silentgear:crimson_steel_block","dyenamics:bed/rose_bed_frm_white_bed","nethersdelight:soul_compost_from_warped_roots","biomesoplenty:mahogany_planks","mcwbiomesoplenty:redwood_mystic_door","enderio:enderios","mcwbridges:balustrade_nether_bricks_bridge","silentgear:bow_template","supplementaries:pedestal","travelersbackpack:blaze","mininggadgets:mininggadget","cfm:lime_trampoline","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","twigs:smooth_basalt_brick_stairs_from_smooth_basalt_stonecutting","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","alltheores:ruby_dust_from_hammer_crushing","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","forbidden_arcanus:wooden_blacksmith_gavel","mcwdoors:metal_windowed_door","twigs:lamp","minecraft:stone","minecraft:blackstone_stairs","travelersbackpack:red_sleeping_bag","comforts:sleeping_bag_blue","cfm:stripped_oak_kitchen_sink_dark","minecraft:dye_cyan_bed","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","crafting_on_a_stick:crafting_table","allthemodium:ancient_stone_bricks","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","dyenamics:dye_bubblegum_carpet","botania:redstone_root","create:crafting/kinetics/display_board","mcwpaths:sandstone_diamond_paving","connectedglass:clear_glass_black2","dyenamics:dye_aquamarine_carpet","botania:metamorphic_fungal_bricks_stairs","mcwbiomesoplenty:mahogany_double_wardrobe","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwpaths:sandstone_windmill_weave_slab","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","immersiveengineering:crafting/ersatz_leather","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","minecraft:birch_chest_boat","cfm:stripped_oak_bedside_cabinet","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","littlelogistics:tug","create:crafting/kinetics/mechanical_press","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","twigs:compacted_dripstone","mcwpaths:brick_strewn_rocky_path","enderio:resetting_lever_three_hundred","immersiveengineering:crafting/treated_wood_horizontal_from_slab","silentgear:shears_template","mcwlights:cross_lantern","mcwlights:brown_lamp","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","minecraft:green_candle","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","mcwfurnitures:cherry_bookshelf_drawer","additionallanterns:diamond_chain","handcrafted:yellow_bowl","supplementaries:stonecutting/stone_tile","ae2:network/cables/covered_pink","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","enderio:dark_steel_trapdoor","rftoolsbuilder:shape_card_quarry_silk","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","minecraft:hopper_minecart","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","mcwwindows:black_curtain","productivebees:stonecutter/wisteria_canvas_expansion_box","chemlib:osmium_ingot_from_blasting_osmium_dust","mcwpaths:sandstone_crystal_floor_slab","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwbiomesoplenty:mahogany_double_drawer","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","enderio:resetting_lever_five_inv","utilitix:armed_stand","botania:lexicon","handcrafted:yellow_cup","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwbiomesoplenty:redwood_beach_door","ae2:network/cables/smart_blue","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","botania:turntable","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","domum_ornamentum:black_brick_extra","alltheores:zinc_dust_from_hammer_crushing","railcraft:steel_chestplate","supplementaries:daub","minecraft:netherite_scrap","mcwfurnitures:stripped_jungle_end_table","create:crafting/kinetics/wooden_bracket","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","minecraft:cut_sandstone","alltheores:tin_dust_from_hammer_ingot_crushing","ae2:decorative/quartz_block","mcwbiomesoplenty:magic_cottage_door","mcwpaths:stone_flagstone","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:nether_brick_slab","mcwbiomesoplenty:hellbark_highley_gate","immersiveengineering:crafting/treated_scaffold","mcwpaths:cobbled_deepslate_square_paving","railcraft:track_relayer","immersiveengineering:crafting/coil_lv","biomesoplenty:mahogany_pressure_plate","mcwlights:brown_paper_lamp","connectedglass:borderless_glass_white_pane2","minecraft:campfire","dyenamics:peach_stained_glass_pane_from_glass_pane","cfm:white_kitchen_sink","mcwbiomesoplenty:stripped_mahogany_lower_bookshelf_drawer","cfm:dye_yellow_picket_fence","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","handcrafted:cherry_couch","farmersdelight:cooking/chicken_soup","blue_skies:bluebright_bookshelf","mcwbiomesoplenty:mahogany_beach_door","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["botania:metamorphic_fungal_stone_wall","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwbiomesoplenty:jacaranda_window2","minecraft:stonecutter","littlelogistics:seater_barge","mcwroofs:blue_terracotta_attic_roof","alltheores:gold_dust_from_hammer_crushing","minecraft:bone_block","mcwbiomesoplenty:willow_waffle_door","mcwpaths:cobbled_deepslate_crystal_floor_path","croptopia:shaped_beef_stew","pneumaticcraft:wall_lamp_white","mcwroofs:red_terracotta_lower_roof","ad_astra:cyan_industrial_lamp","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","create:oak_window","minecraft:gold_nugget_from_blasting","minecolonies:chainmailchestplate","aether:chainmail_leggings_repairing","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","minecraft:cobblestone_wall","dyenamics:maroon_candle","immersiveengineering:crafting/plate_iron_hammering","minecraft:lime_stained_glass_pane","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","reliquary:mob_charm_fragments/cave_spider","integrateddynamics:crafting/part_display_panel","alltheores:brass_plate","botania:metamorphic_forest_bricks_slab","railcraft:controller_circuit","integrateddynamics:crafting/variable_transformer_input","mcwroofs:gray_top_roof","create:crafting/logistics/brass_tunnel","sophisticatedstorage:copper_to_iron_tier_upgrade","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwbiomesoplenty:redwood_nether_door","mcwroofs:gray_terracotta_roof","mcwlights:covered_lantern","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","minecraft:sandstone_slab_from_sandstone_stonecutting","modularrouters:bulk_item_filter","mcwbiomesoplenty:empyreal_classic_door","biomesoplenty:mossy_black_sand","immersiveengineering:crafting/windmill_blade","mcwbiomesoplenty:willow_highley_gate","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","supplementaries:candle_holders/candle_holder_white_dye","minecraft:cherry_door","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","dyenamics:spring_green_wool","railcraft:standard_rail_from_rail","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","travelersbackpack:diamond_tier_upgrade","minecraft:cyan_concrete_powder","cfm:oak_kitchen_counter","minecraft:golden_hoe","minecraft:fire_charge","cfm:purple_trampoline","dyenamics:cherenkov_wool","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","minecraft:dye_green_bed","botania:floating_tigerseye","railcraft:any_detector","computercraft:printer","mcwroofs:stone_bricks_upper_steep_roof","advanced_ae:reactionchamber","minecraft:stone_button","mcwfurnitures:stripped_cherry_cupboard_counter","mcwroofs:cyan_concrete_lower_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","farmingforblockheads:market","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:umbran_japanese2_door","occultism:crafting/magic_lamp_empty","silentgear:shield_template","minecraft:purpur_block","travelersbackpack:dye_lime_sleeping_bag","croptopia:soy_sauce","railcraft:brass_ingot_crafted_with_ingots","megacells:crafting/greater_energy_card","minecraft:polished_blackstone_slab","domum_ornamentum:blue_brick_extra","simplylight:illuminant_light_blue_block_toggle","botania:metamorphic_desert_stone_slab","aether:red_cape","mcwtrpdoors:cherry_glass_trapdoor","pneumaticcraft:transfer_gadget","mcwdoors:cherry_japanese_door","silentgear:blaze_gold_block","minecraft:gray_concrete_powder","dyenamics:lavender_dye","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwtrpdoors:cherry_barred_trapdoor","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:crafting/kinetics/mechanical_roller","chemlib:silver_ingot_from_blasting_silver_dust","blue_skies:glowing_blinding_stone","handcrafted:jungle_side_table","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","minecraft:lime_candle","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","mcwbiomesoplenty:umbran_classic_door","cfm:purple_cooler","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","littlelogistics:barrel_barge","mcwroofs:yellow_terracotta_steep_roof","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:hellbark_bark_glass_door","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","supplementaries:candle_holders/candle_holder_red_dye","blue_skies:glowing_nature_stone","immersiveengineering:crafting/charging_station","silentgear:saw_template","megacells:network/mega_interface","mcwbiomesoplenty:mahogany_drawer_counter","productivebees:stonecutter/palm_canvas_expansion_box","mcwbiomesoplenty:umbran_cottage_door","mcwbiomesoplenty:mahogany_classic_trapdoor","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","croptopia:kiwi_seed","pneumaticcraft:wall_lamp_inverted_white","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","create:crafting/kinetics/chute","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","dyenamics:fluorescent_candle","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","rftoolsutility:crafter1","farmersdelight:milk_bottle","mcwroofs:oak_roof","aether:crossbow_repairing","minecraft:iron_ingot_from_iron_block","handcrafted:blue_cushion","mcwroofs:light_gray_roof_slab","mcwfurnitures:jungle_drawer","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","mcwroofs:thatch_roof","mcwbridges:rope_jungle_bridge","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","dyenamics:lavender_candle","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","silentgear:katana_template","sophisticatedstorage:storage_output","connectedglass:clear_glass_cyan2","botania:metamorphic_forest_cobblestone_stairs","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","mcwfurnitures:stripped_cherry_table","minecraft:iron_ingot_from_nuggets","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","modularrouters:security_upgrade","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_bookshelf","productivebees:stonecutter/mangrove_canvas_hive","mcwpaths:cobblestone_basket_weave_paving","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","supplementaries:evilcraft/sign_post_undead","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwlights:golden_chandelier","minecraft:armor_stand","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","cfm:stripped_birch_kitchen_drawer","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","twigs:chiseled_bricks","enderio:stone_gear","silentgear:blaze_gold_dust_blasting","ae2:network/parts/panels_monitor","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwroofs:nether_bricks_upper_lower_roof","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","mcwlights:tavern_wall_lantern","croptopia:cheese","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","sophisticatedstorage:backpack_stack_upgrade_tier_4_from_storage_stack_upgrade_tier_5","create:cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwfences:railing_end_brick_wall","reliquary:void_tear","mcwpaths:sandstone_flagstone_slab","croptopia:potato_chips","mcwfurnitures:stripped_oak_lower_triple_drawer","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","handcrafted:jungle_pillar_trim","mcwbiomesoplenty:mahogany_large_drawer","mcwbiomesoplenty:empyreal_beach_door","mcwroofs:red_concrete_lower_roof","ae2:block_cutter/slabs/fluix_slab","mcwfences:warped_pyramid_gate","mcwfurnitures:stripped_cherry_bookshelf_drawer","mcwdoors:jungle_stable_door","mcwbiomesoplenty:pine_window2","evilcraft:special/vengeance_pickaxe","mcwwindows:blackstone_four_window","mcwbiomesoplenty:mahogany_swamp_door","mcwlights:cherry_tiki_torch","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","rftoolsbuilder:vehicle_builder","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","create:small_calcite_brick_slab_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:stripped_mahogany_modern_wardrobe","mcwbiomesoplenty:dead_plank_pane_window","mcwbiomesoplenty:stripped_palm_log_window2","mcwwindows:blackstone_brick_gothic","sophisticatedstorage:cherry_chest","mcwlights:soul_double_street_lamp","minecraft:smooth_sandstone_slab","botania:chiseled_metamorphic_mesa_bricks","minecraft:rail","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","minecraft:diamond_sword","mcwbiomesoplenty:empyreal_cottage_door","botania:metamorphic_mountain_cobblestone_slab","modularrouters:blank_module","mcwbiomesoplenty:mahogany_lower_triple_drawer","sgjourney:sandstone_with_lapis","farmersdelight:cooking/dumplings","mcwlights:upgraded_torch","cfm:jungle_park_bench","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","forbidden_arcanus:clibano_core","alltheores:netherite_dust_from_hammer_crushing","botania:metamorphic_plains_cobblestone_wall","minecraft:polished_blackstone_from_blackstone_stonecutting","mcwpaths:brick_running_bond","mcwwindows:dark_oak_window","mininggadgets:upgrade_size_2","mininggadgets:upgrade_size_3","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_bricks_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","botania:metamorphic_plains_stone_slab","silentgear:fishing_rod_template","mcwfurnitures:cherry_large_drawer","dyenamics:amber_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_pink","handcrafted:terracotta_wide_pot","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:jacaranda_nether_door","mcwwindows:lime_curtain","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","ae2:network/blocks/crystal_processing_growth_accelerator","mcwfurnitures:oak_double_wardrobe","evilcraft:crafting/broom_part_cap_bare","minecraft:iron_nugget_from_blasting","railcraft:iron_tank_wall","pneumaticcraft:liquid_hopper","twilightforest:mining_boat","aether:golden_dart","mcwroofs:white_concrete_roof","additionallanterns:normal_nether_bricks_chain","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","mininggadgets:upgrade_size_1","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:fir_bark_glass_door","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","botania:chiseled_metamorphic_swamp_bricks","biomesoplenty:maple_chest_boat","evilcraft:crafting/undead_planks","ae2:network/parts/terminals_crafting","cfm:stripped_oak_chair","xnet:netcable_blue","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwwindows:oak_shutter","cfm:rock_path","connectedglass:scratched_glass_black2","connectedglass:clear_glass_yellow2","minecraft:cooked_beef_from_campfire_cooking","mcwbiomesoplenty:magic_plank_pane_window","botania:floating_labellia","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","ae2:tools/certus_quartz_pickaxe","minecraft:dye_red_wool","mcwroofs:nether_bricks_top_roof","productivebees:stonecutter/jungle_canvas_hive","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:nether_bricks_lower_roof","ad_astra:black_industrial_lamp","mcwroofs:cyan_concrete_top_roof","mcwbiomesoplenty:willow_barn_door","aquaculture:stone_fillet_knife","handcrafted:jungle_couch","mcwbiomesoplenty:magic_stockade_fence","mcwbridges:blackstone_bridge","mcwdoors:cherry_four_panel_door","mcwroofs:gutter_base_green","fluxnetworks:fluxblock","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","mininggadgets:upgrade_efficiency_1","mininggadgets:upgrade_efficiency_3","mininggadgets:upgrade_efficiency_2","mininggadgets:upgrade_efficiency_5","mininggadgets:upgrade_efficiency_4","securitycraft:reinforced_pink_stained_glass","twigs:allium_paper_lantern","farmersdelight:organic_compost_from_rotten_flesh","mcwwindows:crimson_planks_four_window","mcwroofs:cherry_planks_steep_roof","mcwlights:white_lamp","mcwbiomesoplenty:maple_waffle_door","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:mahogany_blossom_trapdoor","mcwbiomesoplenty:umbran_plank_pane_window","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","mcwbiomesoplenty:fir_stable_door","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","mcwbiomesoplenty:palm_classic_door","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","supplementaries:daub_brace","delightful:food/cooking/rock_candy","mcwfurnitures:stripped_cherry_double_wardrobe","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","ad_astra:small_white_industrial_lamp","computercraft:speaker","mcwwindows:mangrove_louvered_shutter","create:crafting/appliances/clipboard","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:palm_tropical_door","handcrafted:creeper_trophy","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","minecraft:dye_blue_wool","mcwbiomesoplenty:palm_cottage_door","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","farmingforblockheads:red_fertilizer","mcwfurnitures:stripped_cherry_coffee_table","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","silentgear:crimson_iron_dust","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","create:crafting/curiosities/minecart_coupling","mcwfences:granite_grass_topped_wall","silentgear:sword_template","securitycraft:reinforced_crimson_fence_gate","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","bigreactors:reactor/basic/controller_ingots_yellorium","mcwroofs:light_gray_steep_roof","dyenamics:persimmon_candle","minecraft:red_banner","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","matc:tertium_essence","mcwfences:oak_stockade_fence","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","croptopia:shaped_water_bottle","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","pylons:expulsion_pylon","mcwfences:bamboo_highley_gate","alltheores:brass_rod","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","mcwroofs:jungle_roof","mcwpaths:andesite_running_bond","minecraft:torch","minecraft:polished_granite_stairs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","mcwtrpdoors:cherry_mystic_trapdoor","create:crafting/kinetics/black_seat","mcwpaths:brick_crystal_floor_path","connectedglass:clear_glass_lime_pane2","croptopia:carnitas","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","railcraft:signal_tuner","cfm:pink_kitchen_counter","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwbiomesoplenty:dead_bark_glass_door","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:cherenkov_dye","twilightforest:time_boat","cfm:dye_black_picket_gate","minecraft:sandstone_wall","aether:skyroot_grindstone","mcwbiomesoplenty:fir_japanese_door","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","minecraft:cherry_slab","cfm:white_grill","ad_astra:wrench","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","connectedglass:scratched_glass_red2","mininggadgets:upgrade_magnet","mcwroofs:sandstone_lower_roof","handcrafted:jungle_dining_bench","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfurnitures:stripped_cherry_large_drawer","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","mcwpaths:sandstone_basket_weave_paving","sophisticatedstorage:stack_upgrade_tier_1","sophisticatedstorage:stack_upgrade_tier_2","sophisticatedstorage:stack_upgrade_tier_3","reliquary:mob_charm_fragments/blaze","mcwwindows:stripped_acacia_log_four_window","silentgear:crimson_iron_raw_ore_blasting","sophisticatedstorage:stack_upgrade_tier_4","sophisticatedstorage:stack_upgrade_tier_5","mcwtrpdoors:cherry_paper_trapdoor","mcwbiomesoplenty:dead_paper_door","domum_ornamentum:green_floating_carpet","mcwbiomesoplenty:magic_beach_door","rftoolspower:blazing_agitator","mcwwindows:spruce_curtain_rod","create:crafting/kinetics/andesite_door","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","aether:netherite_helmet_repairing","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:stripped_willow_log_window","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","railcraft:track_undercutter","silentgear:arrow_template","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwbiomesoplenty:palm_nether_door","enderio:dark_steel_nugget","simplylight:illuminant_purple_block_on_dyed","cfm:blue_sofa","mcwbiomesoplenty:fir_western_door","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","botania:floating_manastar","mcwfurnitures:jungle_striped_chair","dyenamics:honey_wool","additionallanterns:diamond_lantern","railcraft:steel_shears","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","railways:crafting/track_switch_andesite","utilitarian:utility/cherry_logs_to_doors","connectedglass:borderless_glass_lime_pane2","mcwfurnitures:cherry_bookshelf_cupboard","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","minecraft:compass","mcwroofs:cherry_upper_steep_roof","bigreactors:energizer/computerport","allthemodium:allthemodium_apple","minecraft:loom","mininggadgets:upgrade_fortune_3","mininggadgets:upgrade_fortune_2","railcraft:steel_axe","mininggadgets:upgrade_fortune_1","ad_astra:small_lime_industrial_lamp","domum_ornamentum:red_brick_extra","sophisticatedstorage:stack_downgrade_tier_3","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","dyenamics:wine_wool","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","silentgear:ring_template","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","botania:chiseled_metamorphic_taiga_bricks","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwfurnitures:stripped_cherry_triple_drawer","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_white_stained_glass_pane_from_glass","botania:metamorphic_mesa_cobblestone_wall","minecraft:polished_blackstone","handcrafted:blue_crockery_combo","botania:floating_hyacidus","mcwdoors:cherry_beach_door","botania:floating_tangleberrie","modularrouters:pushing_augment","dyenamics:bed/maroon_bed_frm_white_bed","travelersbackpack:dye_red_sleeping_bag","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","minecraft:spire_armor_trim_smithing_template","mcwfences:warped_wired_fence","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","additionallanterns:diorite_lantern","botania:metamorphic_mountain_bricks_wall","mcwbiomesoplenty:pine_window","mcwroofs:stone_top_roof","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","forbidden_arcanus:blacksmith_gavel_head","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfurnitures:stripped_oak_chair","paraglider:paraglider","dankstorage:dock","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","botania:metamorphic_plains_stone_stairs","utilitix:anvil_cart","immersiveengineering:crafting/component_steel","botania:metamorphic_mountain_cobblestone_stairs","mcwtrpdoors:oak_barrel_trapdoor","aether:golden_helmet_repairing","alchemistry:dissolver","mcwfences:mangrove_hedge","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","mcwbiomesoplenty:mahogany_chair","mcwbiomesoplenty:mahogany_planks_upper_steep_roof","ae2:decorative/quartz_vibrant_glass","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_barn_glass_door","mcwbiomesoplenty:mahogany_waffle_door","silentgear:rod_template","mcwbiomesoplenty:empyreal_stable_head_door","botania:metamorphic_taiga_stone_wall","mcwbiomesoplenty:mahogany_glass_table","minecraft:chest_minecart","mcwbiomesoplenty:mahogany_top_roof","ae2:network/cables/covered_orange","mcwwindows:red_curtain","create:crafting/logistics/brass_funnel","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","mcwroofs:white_upper_lower_roof","minecraft:magenta_terracotta","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwroofs:red_terracotta_top_roof","matc:crystals/inferium","mcwroofs:lime_concrete_lower_roof","occultism:crafting/demons_dream_essence_from_seeds","handcrafted:white_cushion","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","mcwroofs:gray_terracotta_attic_roof","mcwroofs:gutter_base_orange","aether:bow_repairing","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwroofs:bricks_attic_roof","enderio:resetting_lever_three_hundred_from_inv","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","reliquary:uncrafting/spider_eye","simplylight:illuminant_yellow_block_dyed","occultism:crafting/brush","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","mcwbiomesoplenty:dead_waffle_door","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","sophisticatedstorage:chipped/loom_table_upgrade","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwlights:mangrove_tiki_torch","securitycraft:redstone_module","mcwbiomesoplenty:jacaranda_plank_window","mcwbiomesoplenty:stripped_redwood_log_window","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","botania:metamorphic_swamp_stone_wall","mcwbiomesoplenty:dead_swamp_door","botania:metamorphic_desert_bricks_wall","railcraft:signal_lamp","cfm:jungle_blinds","createaddition:crafting/rolling_mill","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","xnet:netcable_green","mcwbiomesoplenty:redwood_classic_door","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","pneumaticcraft:paper_from_tag_filter","utilitarian:utility/glow_ink_sac","create:crafting/kinetics/spout","sophisticatedstorage:jungle_limited_barrel_2","bigreactors:reactor/basic/glass","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","minecraft:orange_terracotta","mcwroofs:white_top_roof","reliquary:holy_hand_grenade","mcwbiomesoplenty:magic_waffle_door","aquaculture:gold_fillet_knife","mcwbiomesoplenty:redwood_cottage_door","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","ae2:network/cables/dense_smart_cyan","pneumaticcraft:compressed_bricks_from_tile","utilitix:stone_wall_stonecutting","travelersbackpack:warden","minecraft:cherry_fence_gate","ad_astra:gas_tank","dyenamics:fluorescent_concrete_powder","securitycraft:reinforced_cherry_fence","additionallanterns:copper_lantern","mcwroofs:lime_terracotta_steep_roof","mcwbiomesoplenty:willow_paper_door","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","cfm:crimson_mail_box","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwfences:acacia_curved_gate","ae2:network/parts/panels_semi_dark_monitor","mysticalagriculture:infusion_pedestal","rftoolsutility:screen_controller","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:ender_cart","mcwbiomesoplenty:redwood_swamp_door","nethersdelight:hoglin_sirloin","silentgear:sickle_template","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","ad_astra:iron_plateblock","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:jacaranda_barn_door","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","minecraft:iron_block","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwdoors:acacia_beach_door","mcwroofs:gutter_base","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","immersiveengineering:crafting/treated_wood_vertical_from_horizontal","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","alltheores:platinum_plate","aether:iron_helmet_repairing","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","mcwpaths:sandstone_crystal_floor_stairs","pneumaticcraft:wall_lamp_inverted_blue","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","twigs:cherry_table","minecraft:orange_stained_glass","biomesoplenty:mahogany_trapdoor","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","botania:metamorphic_swamp_bricks_stairs","mcwpaths:sandstone_strewn_rocky_path","mcwbiomesoplenty:maple_four_window","mcwwindows:stripped_oak_log_window","minecraft:golden_boots","minecraft:pink_dye_from_red_white_dye","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","rftoolspower:blazing_generator","supplementaries:flags/flag_cyan","immersiveengineering:crafting/tinted_glass_lead_wire","reliquary:wraith_node","cfm:pink_grill","minecraft:polished_granite_slab","mcwroofs:gutter_middle_orange","croptopia:scrambled_eggs","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","mcwroofs:jungle_attic_roof","waystones:waystone","minecraft:cherry_planks","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","mcwbiomesoplenty:maple_stable_head_door","minecraft:polished_blackstone_brick_stairs","mcwroofs:white_terracotta_upper_steep_roof","securitycraft:reinforced_piston","create:crafting/kinetics/mechanical_saw","create:crafting/logistics/andesite_funnel","bigreactors:reactor/basic/redstoneport","mcwpaths:brick_crystal_floor_slab","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","utilitix:reinforced_piston_controller_rail","botania:metamorphic_mountain_stone_stairs","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","mcwpaths:blackstone_basket_weave_paving","modularrouters:player_module","minecraft:glass","mcwbiomesoplenty:pine_classic_door","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:brown_concrete_attic_roof","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwbiomesoplenty:pine_cottage_door","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","ae2:network/cables/smart_red","dyenamics:persimmon_concrete_powder","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","mcwwindows:light_blue_mosaic_glass","ad_astra:yellow_industrial_lamp","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","mob_grinding_utils:recipe_mob_swab","twigs:mossy_cobblestone_bricks","railcraft:steel_gear","ad_astra:encased_iron_block","sophisticatedstorage:crafting_upgrade","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","minecraft:stone_bricks","securitycraft:reinforced_red_stained_glass","ae2:tools/network_color_applicator","mcwfurnitures:stripped_cherry_drawer_counter","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","mininggadgets:upgrade_freezing","mcwbiomesoplenty:maple_plank_window2","biomesoplenty:mahogany_fence","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:jacaranda_bamboo_door","mcwbiomesoplenty:dead_plank_window2","botania:quartz_lavender","mysticalagriculture:prosperity_gemstone","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","mcwroofs:red_concrete_roof","mcwtrpdoors:cherry_barn_trapdoor","ae2:network/cables/smart_fluix","enderio:resetting_lever_thirty","integrateddynamics:crafting/variable","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","mininggadgets:modificationtable","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","pneumaticcraft:wall_lamp_light_gray","minecraft:dye_yellow_wool","create:crafting/kinetics/contraption_controls","dyenamics:amber_concrete_powder","mcwroofs:light_gray_concrete_lower_roof","bigreactors:crafting/raw_yellorium_component_to_storage","supplementaries:candle_holders/candle_holder_black_dye","nethersdelight:soul_compost_from_hoglin_hide","farmersdelight:cooking/vegetable_noodles","botania:apothecary_desert","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwbiomesoplenty:hellbark_plank_window2","mcwroofs:lime_terracotta_upper_steep_roof","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","minecraft:smooth_stone_slab","minecraft:magenta_dye_from_allium","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","botania:azulejo_0","minecraft:dye_lime_wool","mcwbiomesoplenty:fir_tropical_door","connectedglass:borderless_glass_white2","aquaculture:heavy_hook","mcwwindows:acacia_window","croptopia:food_press","blue_skies:frostbright_bookshelf","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","mcwfurnitures:oak_chair","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","silentgear:material_grader","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","quark:building/crafting/jungle_ladder","croptopia:shaped_milk_bottle","farmersdelight:nether_salad","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","mcwlights:orange_lamp","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","pneumaticcraft:reinforced_brick_pillar","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","mcwpaths:brick_crystal_floor","biomesoplenty:mahogany_sign","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","botania:metamorphic_desert_cobblestone_slab","aquaculture:fishing_line","comforts:hammock_to_red","mcwbiomesoplenty:stripped_jacaranda_log_window","sophisticatedstorage:cherry_limited_barrel_2","sophisticatedstorage:cherry_limited_barrel_1","mcwfurnitures:stripped_jungle_drawer","sophisticatedstorage:cherry_limited_barrel_4","sophisticatedstorage:cherry_limited_barrel_3","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","railcraft:charge_terminal","mcwpaths:blackstone_crystal_floor_slab","minecraft:end_stone_brick_slab","dyenamics:conifer_wool","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","botania:floating_orechid_ignem","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","domum_ornamentum:red_floating_carpet","immersiveengineering:crafting/alu_fence","minecraft:diamond_shovel","mcwwindows:end_brick_gothic","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:jungle_planks_upper_lower_roof","twigs:polished_calcite_bricks_from_calcite_stonecutting","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","dyenamics:mint_wool","connectedglass:borderless_glass_green2","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","alltheores:diamond_plate","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwroofs:oak_upper_lower_roof","bigreactors:reprocessor/casing","botania:metamorphic_fungal_cobblestone_stairs","productivebees:stonecutter/rosewood_canvas_hive","additionallanterns:end_stone_chain","modularrouters:camouflage_upgrade","connectedglass:clear_glass_lime2","silentgear:bracelet_template","farmersdelight:cooking/mushroom_rice","draconicevolution:components/draconium_core","minecraft:golden_shovel","modularrouters:energy_upgrade","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","mcwbiomesoplenty:hellbark_stable_head_door","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","ae2:materials/cardspeed","mcwroofs:gutter_base_red","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:receiver_component","securitycraft:reinforced_tinted_glass","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","mcwfences:sandstone_pillar_wall","minecraft:diamond_chestplate","securitycraft:reinforced_gray_stained_glass","mcwbiomesoplenty:stripped_maple_log_window2","cfm:dye_black_picket_fence","aether:wooden_sword_repairing","mcwlights:wall_lamp","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","mcwbiomesoplenty:jacaranda_mystic_door","supplementaries:hourglass","everythingcopper:copper_leggings","pneumaticcraft:reinforced_brick_tile","mcwbridges:andesite_bridge_stair","mcwroofs:lime_terracotta_attic_roof","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","minecraft:orange_dye_from_red_yellow","simplylight:illuminant_blue_block_on_dyed","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","railcraft:bushing_gear_brass","mcwwindows:crimson_planks_window","immersiveengineering:crafting/breaker_switch","allthecompressed:compress/amethyst_block_1x","botania:metamorphic_forest_cobblestone_wall","mcwdoors:acacia_modern_door","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","silentgear:very_crude_repair_kit","create:small_calcite_brick_stairs_from_stone_types_calcite_stonecutting","mcwwindows:stripped_jungle_log_four_window","dyenamics:bubblegum_candle","create:crafting/kinetics/weighted_ejector","mcwlights:iron_triple_candle_holder","mcwroofs:pink_terracotta_roof","travelersbackpack:redstone","minecraft:tnt_minecart","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","immersiveengineering:crafting/shovel_steel","botania:metamorphic_taiga_cobblestone_wall","mcwbiomesoplenty:fir_nether_door","minecraft:cherry_button","evilcraft:crafting/blood_extractor","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","travelersbackpack:emerald","mininggadgets:upgrade_battery_2","farmersdelight:cutting_board","croptopia:trail_mix","mininggadgets:upgrade_battery_3","mininggadgets:upgrade_battery_1","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:sandstone_flagstone","mcwbiomesoplenty:empyreal_japanese2_door","botania:metamorphic_forest_stone_slab","cfm:dye_yellow_picket_gate","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","rftoolspower:coalgenerator","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwpaths:blackstone_clover_paving","botania:chiseled_metamorphic_desert_bricks","railcraft:track_layer","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","minecraft:white_carpet","ae2:network/cables/covered_blue","dyenamics:maroon_dye","utilitix:filter_rail","aether:diamond_gloves","travelersbackpack:blank_upgrade","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwbiomesoplenty:dead_stable_door","minecraft:cracked_stone_bricks","railcraft:steel_tunnel_bore_head","mcwroofs:oak_lower_roof","utilitarian:utility/cherry_logs_to_pressure_plates","silentgear:elytra_template","mcwbiomesoplenty:willow_plank_pane_window","securitycraft:crystal_quartz_item","travelersbackpack:dye_green_sleeping_bag","dimstorage:dim_wall","botania:metamorphic_taiga_bricks_stairs","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","modularrouters:void_module","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","dankstorage:2_to_3","additionallanterns:dark_prismarine_lantern","twigs:polished_amethyst","mcwwindows:mangrove_plank_window2","mysticalagriculture:imperium_essence_uncraft","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","botania:conversions/blazeblock_deconstruct","integratedtunnels:crafting/part_exporter_item","mcwlights:acacia_tiki_torch","biomesoplenty:mahogany_door","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","securitycraft:reinforced_warped_fence_gate","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/gearshift","mcwfences:oak_horse_fence","caupona:clay_cistern","botania:floating_exoflame","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfurnitures:cherry_table","minecraft:light_gray_dye_from_gray_white_dye","mcwfurnitures:stripped_cherry_double_drawer","mcwfences:blackstone_pillar_wall","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","deepresonance:lens","croptopia:doughnut","mcwlights:square_wall_lamp","mcwfurnitures:cherry_lower_bookshelf_drawer","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","handcrafted:cherry_table","terralith:dispenser_alt","botania:blue_shiny_flower","mcwbiomesoplenty:mahogany_double_drawer_counter","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","allthecompressed:compress/cobbled_deepslate_1x","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","ae2:tools/fluix_axe","cfm:black_kitchen_sink","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","botania:metamorphic_mountain_stone_slab","evilcraft:crafting/broom_part_rod_netherrack","cfm:dye_cyan_picket_gate","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwroofs:pink_concrete_top_roof","travelersbackpack:blue_sleeping_bag","forbidden_arcanus:sanity_meter","mcwbiomesoplenty:hellbark_japanese_door","mcwlights:glowstone_slab","modularrouters:energy_output_module","mcwbiomesoplenty:stripped_mahogany_stool_chair","mcwbiomesoplenty:mahogany_log_bridge_middle","handcrafted:vindicator_trophy","immersiveengineering:crafting/turntable","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","enderio:resetting_lever_three_hundred_inv_from_base","reliquary:uncrafting/string","minecraft:polished_blackstone_pressure_plate","biomesoplenty:blackstone_bulb","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alltheores:diamond_rod","minecraft:stone_slab_from_stone_stonecutting","mcwroofs:cherry_roof","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","silentgear:crimson_iron_dust_blasting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","botania:floating_jiyuulia","littlelogistics:locomotive_route","connectedglass:clear_glass_white2","securitycraft:universal_block_remover","mcwbiomesoplenty:stripped_mahogany_coffee_table","securitycraft:reinforced_bookshelf","minecraft:netherite_leggings_smithing","enderio:resetting_lever_sixty","mcwfences:quartz_grass_topped_wall","matc:crystals/prudentium","create:crafting/kinetics/sticky_mechanical_piston","minecraft:stone_brick_stairs_from_stone_stonecutting","mcwroofs:brown_concrete_top_roof","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","create:cut_dripstone_bricks_from_stone_types_dripstone_stonecutting","handcrafted:wood_bowl","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","mcwroofs:white_roof","mcwbiomesoplenty:fir_glass_door","connectedglass:scratched_glass_yellow_pane2","mcwwindows:stripped_oak_log_window2","create:crafting/kinetics/gantry_shaft","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","immersiveengineering:crafting/treated_wood_horizontal_to_slab","delightful:cantaloupe_slice","mcwbridges:balustrade_blackstone_bridge","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","modularrouters:range_down_from_up","delightful:food/cooking/stuffed_cantaloupe","minecraft:lapis_lazuli","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","mcwwindows:stripped_mangrove_log_window2","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","rftoolsbuilder:yellow_shield_template_block","farmersdelight:safety_net","domum_ornamentum:brown_floating_carpet","mcwroofs:lime_concrete_top_roof","sophisticatedstorage:stack_upgrade_tier_1_plus","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","reliquary:uncrafting/bone","utilitarian:no_soliciting/restraining_order","immersiveengineering:crafting/furnace_heater","mcwbiomesoplenty:stripped_mahogany_bookshelf_cupboard","pneumaticcraft:wall_lamp_brown","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","aether:wooden_hoe_repairing","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_paper_door","sophisticatedstorage:storage_pickup_upgrade_from_backpack_pickup_upgrade","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","rftoolsbuilder:shape_card_quarry_clear_fortune","mcwpaths:andesite_crystal_floor","mcwbiomesoplenty:empyreal_stable_door","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","railcraft:diamond_spike_maul","additionallanterns:granite_lantern","mcwdoors:cherry_barn_glass_door","minecraft:white_candle","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_bricks_stonecutting","mcwwindows:spruce_plank_window2","mcwwindows:lime_mosaic_glass_pane","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","comforts:hammock_to_white","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","create:crafting/kinetics/mechanical_bearing","mcwbiomesoplenty:stripped_mahogany_bookshelf","minecraft:golden_carrot","ae2:network/cells/fluid_cell_housing","mcwbiomesoplenty:hellbark_window","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","productivebees:stonecutter/magic_canvas_hive","mcwbiomesoplenty:stripped_empyreal_log_window2","create:crafting/kinetics/elevator_pulley","dyenamics:honey_stained_glass","additionallanterns:smooth_stone_chain","bigreactors:blasting/graphite_from_coalblock","mcwfurnitures:stripped_cherry_bookshelf","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","mcwroofs:green_terracotta_upper_steep_roof","minecraft:magma_cream","create:crafting/kinetics/attribute_filter","handcrafted:white_bowl","supplementaries:key","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","rftoolspower:power_core1","computercraft:cable","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","additionallanterns:stone_chain","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","aether:skyroot_chest_boat","ae2:misc/deconstruction_certus_quartz_block","minecraft:cherry_sign","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","farmersdelight:onion_crate","create:crafting/kinetics/purple_seat","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwfurnitures:stripped_oak_striped_chair","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","mcwpaths:brick_windmill_weave_stairs","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","create:polished_cut_calcite_stairs_from_stone_types_calcite_stonecutting","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","mininggadgets:upgrade_light_placer","mcwbiomesoplenty:dead_barn_door","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","minecraft:black_stained_glass_pane_from_glass_pane","modularrouters:pickup_delay_augment","pneumaticcraft:wall_lamp_inverted_green","create:cut_calcite_brick_slab_from_stone_types_calcite_stonecutting","twigs:gravel_bricks","dyenamics:persimmon_stained_glass","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","botania:apothecary_swamp","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","silentgear:guide_book","mcwroofs:oak_planks_upper_lower_roof","minecraft:cherry_trapdoor","toolbelt:pouch","silentgear:grip_template","domum_ornamentum:light_gray_floating_carpet","bigreactors:energizer/powerport_fe","megacells:crafting/greater_energy_card_upgraded","immersiveengineering:crafting/stick_aluminum","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","dyenamics:bubblegum_concrete_powder","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","minecraft:cracked_nether_bricks","ae2:tools/matter_cannon","mcwbiomesoplenty:fir_four_panel_door","supplementaries:slingshot","mcwdoors:acacia_japanese2_door","bigreactors:reactor/basic/casing_recycle_glass","twigs:cobblestone_from_pebble","everythingcopper:copper_rail","cfm:black_grill","pneumaticcraft:wall_lamp_inverted_black","create:small_calcite_bricks_from_stone_types_calcite_stonecutting","simplylight:illuminant_block","evilcraft:crafting/blood_infuser","mcwbridges:cherry_log_bridge_middle","blue_skies:glowing_nature_stonebrick_from_glowstone","dyenamics:dye_ultramarine_carpet","ae2:network/cables/glass_red","fluxnetworks:fluxplug","mcwbiomesoplenty:empyreal_four_panel_door","croptopia:shaped_kiwi_sorbet","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","dyenamics:rose_candle","aether:aether_iron_nugget_from_blasting","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mysticalagriculture:essence/minecraft/cherry_log","minecraft:iron_pickaxe","aether:shield_repairing","ae2:shaped/not_so_mysterious_cube","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","rftoolsutility:matter_booster","ae2:network/cables/glass_orange","minecraft:vibranium_mage_chestplate_smithing","ae2:materials/cardinverter","mcwfences:modern_diorite_wall","pipez:wrench","dyenamics:bed/wine_bed","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","simplylight:illuminant_black_block_on_dyed","mcwbiomesoplenty:mahogany_glass_trapdoor","mcwfences:spruce_highley_gate","botania:metamorphic_taiga_cobblestone_stairs","mcwbiomesoplenty:redwood_stable_head_door","dyenamics:aquamarine_candle","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","mcwdoors:jungle_barn_door","mcwbiomesoplenty:mahogany_planks_roof","handcrafted:oak_drawer","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","mcwbiomesoplenty:empyreal_japanese_door","minecraft:lime_stained_glass","dyenamics:mint_candle","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwbiomesoplenty:mahogany_steep_roof","minecraft:stone_brick_walls_from_stone_stonecutting","mcwbiomesoplenty:hellbark_paper_door","cfm:light_blue_kitchen_counter","ae2:network/cables/covered_fluix_clean","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","biomesoplenty:mahogany_slab","mcwbiomesoplenty:stripped_palm_pane_window","create:crafting/kinetics/item_drain","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","cfm:fridge_dark","chimes:copper_chimes","simplylight:illuminant_magenta_block_on_dyed","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","mysticalagriculture:essence/minecraft/diamond","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","mcwwindows:black_mosaic_glass","mcwroofs:black_terracotta_lower_roof","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","mcwfurnitures:cherry_modern_desk","mcwroofs:gray_concrete_top_roof","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","minecraft:cooked_salmon_from_smoking","mcwwindows:prismarine_brick_gothic","cfm:spatula","ae2:network/cables/dense_covered_fluix_clean","littlelogistics:switch_rail","dyenamics:dye_honey_carpet","botania:metamorphic_taiga_bricks_slab","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwbiomesoplenty:fir_cottage_door","constructionwand:core_angel","ae2:network/cables/covered_fluix","create:cut_dripstone_brick_slab_from_stone_types_dripstone_stonecutting","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","botania:floating_bellethorn","mcwwindows:stripped_oak_log_four_window","mcwbiomesoplenty:willow_glass_door","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwroofs:orange_terracotta_upper_lower_roof","wstweaks:wither_skeleton_skull","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","create:cut_dripstone_slab_from_stone_types_dripstone_stonecutting","blue_skies:comet_bookshelf","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","tombstone:ankh_of_prayer","cfm:white_sofa","immersiveengineering:crafting/dynamo","mcwwindows:spruce_louvered_shutter","ae2:shaped/slabs/fluix_block","mcwfences:acacia_highley_gate","mcwroofs:andesite_steep_roof","mcwroofs:pink_concrete_roof","botania:apothecary_taiga","connectedglass:tinted_borderless_glass_black2","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","minecraft:polished_andesite_slab","mcwlights:magenta_lamp","nethersdelight:hoglin_sirloin_from_smoking","productivebees:stonecutter/warped_canvas_expansion_box","ae2:network/cables/dense_covered_lime","mcwbiomesoplenty:maple_swamp_door","botania:metamorphic_fungal_bricks","botania:mana_fluxfield","mcwwindows:orange_mosaic_glass","mcwwindows:warped_planks_window2","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","ae2:shaped/slabs/sky_stone_block","mcwbiomesoplenty:maple_bark_glass_door","handcrafted:oak_bench","silentgear:blaze_gold_dust_smelting","minecraft:yellow_candle","railcraft:track_remover","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","minecraft:repeater","minecraft:red_concrete_powder","rftoolsbuilder:shape_card_quarry_silk_dirt","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwbiomesoplenty:empyreal_glass_door","minecraft:iron_leggings","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","handcrafted:cherry_counter","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:fir_waffle_door","cfm:pink_kitchen_sink","dyenamics:banner/amber_banner","mcwbiomesoplenty:mahogany_lower_bookshelf_drawer","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","farmersdelight:cooked_mutton_chops_from_smoking","create:crafting/kinetics/analog_lever","create:andesite_scaffolding_from_andesite_alloy_stonecutting","securitycraft:reinforced_orange_stained_glass_pane_from_dye","ad_astra:red_industrial_lamp","domum_ornamentum:light_blue_floating_carpet","minecraft:dye_white_carpet","ae2:shaped/stairs/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","mcwbiomesoplenty:pine_beach_door","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","ae2:block_cutter/slabs/quartz_slab","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","create:cut_dripstone_stairs_from_stone_types_dripstone_stonecutting","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","mcwdoors:garage_gray_door","handcrafted:witch_trophy","minecraft:polished_andesite_from_andesite_stonecutting","littlelogistics:seater_car","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","botania:mushroom_5","evilcraft:crafting/broom_part_rod_bare","minecraft:stone_brick_stairs","botania:mushroom_4","ae2:misc/chests_smooth_sky_stone","botania:mushroom_3","mcwwindows:quartz_window","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","blue_skies:coarse_lunar_dirt","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","dyenamics:peach_candle","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","botania:metamorphic_swamp_cobblestone_wall","create:cut_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","minecraft:clock","dyenamics:conifer_dye","create:crafting/appliances/netherite_diving_helmet_from_netherite","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","dyenamics:dye_peach_carpet","mcwlights:soul_birch_tiki_torch","megacells:crafting/compression_card","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","biomesoplenty:mahogany_button","travelersbackpack:coal","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","railways:benchcart","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","minecraft:diamond_leggings","ad_astra:iron_plating","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_paper_door","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","silentgear:leather_scrap","mcwbridges:nether_bricks_bridge_stair","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","botania:keep_ivy","minecraft:polished_andesite_slab_from_andesite_stonecutting","minecraft:dye_black_bed","securitycraft:reinforced_red_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_western_door","handcrafted:jungle_corner_trim","biomesoplenty:empyreal_chest_boat","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","mcwbiomesoplenty:pine_swamp_door","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","alchemistry:atomizer","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","mcwfences:railing_prismarine_wall","minecraft:brick_slab_from_bricks_stonecutting","botania:metamorphic_taiga_stone_stairs","cfm:stripped_jungle_table","dyenamics:honey_candle","supplementaries:pancake_fd","twigs:rocky_dirt","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","mcwbiomesoplenty:dead_bamboo_door","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","create:crafting/kinetics/mechanical_piston","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","botania:white_shiny_flower","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","mcwroofs:white_concrete_upper_lower_roof","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","aether:stone_axe_repairing","twilightforest:sorting_chest_boat","reliquary:interdiction_torch","aether:diamond_axe_repairing","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","mcwfurnitures:cherry_double_drawer","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbridges:brick_bridge","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","cfm:light_blue_cooler","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","mcwbiomesoplenty:stripped_mahogany_lower_triple_drawer","reliquary:uncrafting/wither_skeleton_skull","supplementaries:flags/flag_yellow","mcwbiomesoplenty:pine_bark_glass_door","modularrouters:breaker_module","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","mcwfurnitures:oak_lower_triple_drawer","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","create:crafting/kinetics/basin","naturalist:glow_goop_from_campfire_cooking","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwpaths:blackstone_dumble_paving","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","botania:metamorphic_desert_cobblestone_wall","minecraft:end_stone_brick_wall","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","alltheores:brass_gear","evilcraft:crafting/broom_part_cap_metal_silver","dyenamics:aquamarine_stained_glass","minecraft:netherite_chestplate_smithing","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","terralith:observer_alt","minecraft:oak_sign","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","domum_ornamentum:white_paper_extra","connectedglass:borderless_glass_blue2","dyenamics:fluorescent_dye","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","mcwbiomesoplenty:pine_waffle_door","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","minecraft:smooth_sandstone_slab_from_smooth_sandstone_stonecutting","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","mcwdoors:cherry_glass_door","sfm:manager","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:willow_bamboo_door","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","mysticalagriculture:infusion_altar","mcwwindows:cyan_mosaic_glass_pane","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","comforts:sleeping_bag_to_red","connectedglass:tinted_borderless_glass_cyan2","create_enchantment_industry:crafting/printer","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","ae2:tools/network_tool","deeperdarker:bloom_boat","utilitix:linked_repeater","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","connectedglass:clear_glass_yellow_pane2","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","botania:red_string","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:spruce_curved_gate","allthemodium:ancient_stone_stairs","mcwdoors:jungle_swamp_door","mcwpaths:andesite_flagstone_stairs","domum_ornamentum:green_brick_extra","mcwwindows:warped_planks_four_window","mcwwindows:stripped_crimson_stem_window2","croptopia:meringue","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","create:dripstone_block_from_stone_types_dripstone_stonecutting","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","farmersdelight:cooking/noodle_soup","rftoolsutility:moduleplus_template","evilcraft:crafting/blood_infusion_core","allthecompressed:compress/netherite_block_1x","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","mcwbiomesoplenty:dead_tropical_door","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwbiomesoplenty:jacaranda_paper_door","mcwfurnitures:cherry_desk","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","railcraft:radio_circuit","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwpaths:cobbled_deepslate_clover_paving","mcwfurnitures:oak_modern_wardrobe","ae2:decorative/sky_stone_brick_from_stonecutting","handcrafted:yellow_plate","create:crafting/kinetics/hand_crank","mcwlights:golden_double_candle_holder","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","croptopia:soybean_seed","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","simplylight:illuminant_light_blue_block_on_dyed","mcwroofs:yellow_terracotta_upper_steep_roof","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:cut_calcite_stairs_from_stone_types_calcite_stonecutting","ae2:network/crafting/patterns_blank","create:crafting/appliances/copper_backtank","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","mcwbiomesoplenty:fir_modern_door","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","create:crafting/kinetics/portable_storage_interface","securitycraft:reinforced_pink_stained_glass_pane_from_dye","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","securitycraft:alarm","pneumaticcraft:manometer","mcwbiomesoplenty:dead_japanese2_door","mcwfurnitures:oak_counter","corail_woodcutter:spruce_woodcutter","silentgear:spear_template","dyenamics:icy_blue_dye","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","mcwroofs:oak_top_roof","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","comforts:sleeping_bag_red","utilitix:crude_furnace","dyenamics:peach_dye","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mysticalagriculture:essence/minecraft/netherite_ingot","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","sophisticatedstorage:jukebox_upgrade","integrateddynamics:crafting/drying_basin","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","aether:blue_cape_blue_wool","ae2:network/cables/glass_black","mcwfurnitures:stripped_oak_bookshelf","delightful:food/cooking/glow_jam_jar","mcwfurnitures:cherry_wardrobe","mcwbiomesoplenty:fir_curved_gate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","additionallanterns:gold_lantern","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","mcwbiomesoplenty:mahogany_planks_lower_roof","matc:prudentium_essence","simplylight:illuminant_orange_block_dyed","alchemistry:compactor","minecraft:dropper","create:crafting/kinetics/piston_extension_pole","dyenamics:bed/maroon_bed","farmersdelight:cooking/stuffed_pumpkin_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","create:crafting/kinetics/brass_hand","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","mcwbiomesoplenty:empyreal_bamboo_door","create:crafting/kinetics/lime_seat","mcwbiomesoplenty:stripped_mahogany_double_drawer_counter","mcwdoors:oak_japanese2_door","mcwroofs:cherry_planks_roof","simplylight:illuminant_black_block_dyed","mcwbiomesoplenty:mahogany_mystic_trapdoor","mcwdoors:acacia_bamboo_door","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","create:layered_dripstone_from_stone_types_dripstone_stonecutting","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","productivebees:stonecutter/yucca_canvas_expansion_box","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:red_cushion","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","minecraft:blackstone_slab","mcwwindows:stripped_cherry_log_window","pipez:item_pipe","create:small_dripstone_bricks_from_stone_types_dripstone_stonecutting","mcwpaths:sandstone_running_bond_slab","mcwbridges:balustrade_end_stone_bricks_bridge","dyenamics:dye_rose_carpet","minecraft:quartz_bricks","immersiveengineering:crafting/wirecoil_redstone","botania:metamorphic_plains_cobblestone_stairs","mcwbiomesoplenty:mahogany_tropical_trapdoor","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","mcwbridges:balustrade_bricks_bridge","supplementaries:candle_holders/candle_holder_blue_dye","rftoolsbuilder:red_shield_template_block","mcwfurnitures:stripped_oak_large_drawer","mcwbiomesoplenty:dead_mystic_door","easy_villagers:iron_farm","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","mcwroofs:jungle_planks_steep_roof","pneumaticcraft:wall_lamp_magenta","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","cfm:dye_red_picket_gate","travelersbackpack:hose_nozzle","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","mcwbiomesoplenty:pine_japanese_door","mcwroofs:gutter_base_cyan","allthemodium:allthemodium_ingot","undergarden:smogstem_chest_boat","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","mcwwindows:prismarine_window","securitycraft:laser_block","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","rftoolsutility:matter_receiver","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:hellbark_four_panel_door","rftoolsutility:clock_module","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_3","modularrouters:sender_module_2","modularrouters:sender_module_1","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","supplementaries:flags/flag_blue","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","ae2:network/cables/glass_light_blue","immersiveengineering:crafting/crate","minecolonies:apple_pie","twigs:mossy_bricks_from_moss_block","silentgear:blaze_gold_dust","minecraft:andesite_stairs_from_andesite_stonecutting","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","create:vertical_framed_glass_from_glass_colorless_stonecutting","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","create:crafting/kinetics/goggles","mcwbiomesoplenty:maple_japanese2_door","farmersdelight:wheat_dough_from_water","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","croptopia:nougat","immersiveengineering:crafting/fluid_placer","dyenamics:icy_blue_concrete_powder","botania:apothecary_forest","rftoolsbase:crafting_card","mcwroofs:cyan_terracotta_attic_roof","mcwfurnitures:stripped_cherry_glass_table","mcwlights:soul_bamboo_tiki_torch","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwroofs:cherry_top_roof","rftoolsutility:matter_transmitter","sophisticatedstorage:chipped/tinkering_table_upgrade","mcwfurnitures:cherry_modern_wardrobe","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","utilitarian:utility/cherry_logs_to_slabs","simplylight:illuminant_pink_block_on_toggle","bigreactors:turbine/reinforced/passivetap_fe","cfm:stripped_warped_kitchen_sink_light","twigs:blackstone_column_stonecutting","minecraft:diamond_boots","mcwbiomesoplenty:mahogany_end_table","pneumaticcraft:redstone_module","handcrafted:oak_couch","travelersbackpack:magma_cube","allthetweaks:ender_pearl_block","croptopia:beetroot_salad","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","mcwbiomesoplenty:hellbark_glass_door","forbidden_arcanus:deorum_lantern","everythingcopper:copper_door","farmersdelight:cooking/glow_berry_custard","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","cfm:orange_kitchen_counter","botania:metamorphic_swamp_cobblestone_stairs","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwbiomesoplenty:mahogany_counter","mcwroofs:jungle_upper_lower_roof","minecraft:polished_blackstone_brick_wall","mcwbiomesoplenty:empyreal_mystic_door","mcwpaths:blackstone_running_bond_stairs","minecraft:dried_kelp_from_campfire_cooking","ae2:network/wireless_crafting_terminal","mcwdoors:metal_door","mcwroofs:jungle_planks_upper_steep_roof","mcwdoors:cherry_western_door","modularrouters:vacuum_module","rftoolsutility:spawner","cfm:green_cooler","alltheores:steel_rod","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","ae2:network/cables/dense_covered_light_blue","sophisticatedstorage:backpack_stack_upgrade_tier_1_from_storage_stack_upgrade_tier_2","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","connectedglass:tinted_borderless_glass_red2","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","sophisticatedstorage:basic_to_netherite_tier_upgrade","immersiveengineering:crafting/strip_curtain","mcwbiomesoplenty:fir_wired_fence","travelersbackpack:pig","minecraft:black_terracotta","mysticalagriculture:essence/integrateddynamics/menril_berries","alltheores:platinum_ingot_from_raw","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","computercraft:turtle_normal_overlays/turtle_trans_overlay","ae2:tools/certus_quartz_cutting_knife","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:white_bed","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:wine_terracotta","cfm:dye_green_picket_gate","minecraft:cobblestone_slab_from_cobblestone_stonecutting","sophisticatedstorage:jungle_chest","botania:corporea_spark","ae2:shaped/stairs/smooth_sky_stone_block","mcwbiomesoplenty:palm_highley_gate","bigreactors:blasting/yellorium_from_raw","mcwroofs:black_attic_roof","create:cut_dripstone_from_stone_types_dripstone_stonecutting","sophisticatedstorage:chipped/botanist_workbench_upgrade","minecraft:pumpkin_pie","mcwfurnitures:stripped_jungle_large_drawer","ae2:block_cutter/walls/fluix_wall","mcwfences:mesh_metal_fence","supplementaries:soap/piston","minecraft:soul_campfire","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwwindows:prismarine_pane_window","mcwpaths:brick_windmill_weave_path","mcwlights:spruce_tiki_torch","dyenamics:bed/amber_bed_frm_white_bed","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","twigs:cracked_bricks","appflux:insulating_resin","botania:apothecary_fungal","connectedglass:borderless_glass_green_pane2","mcwdoors:acacia_mystic_door","ae2:misc/deconstruction_fluix_block","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwbridges:rope_cherry_bridge","blue_skies:trough","create:polished_cut_dripstone_wall_from_stone_types_dripstone_stonecutting","computercraft:monitor_normal","mcwroofs:bricks_steep_roof","cfm:gray_grill","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","dankstorage:6_to_7","create:crafting/kinetics/radial_chassis","allthemodium:allthemodium_hoe","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","create:brass_bars_from_ingots_brass_stonecutting","mcwdoors:cherry_bark_glass_door","ae2:block_cutter/stairs/smooth_sky_stone_stairs","railcraft:steel_helmet","minecraft:quartz_pillar_from_quartz_block_stonecutting","mcwroofs:nether_bricks_roof","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","minecraft:observer","cfm:gray_kitchen_sink","mcwfences:bamboo_stockade_fence","mcwbiomesoplenty:hellbark_waffle_door","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","minecraft:pink_dye_from_peony","immersiveengineering:crafting/hoe_steel","delightful:smoking/roasted_acorn","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","connectedglass:clear_glass_white_pane2","modularrouters:muffler_upgrade","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwbridges:sandstone_bridge","ad_astra:iron_factory_block","mcwfences:railing_deepslate_brick_wall","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:red_terracotta_steep_roof","utilitix:oak_shulker_boat","cfm:white_trampoline","handcrafted:oak_desk","create:crafting/kinetics/super_glue","xnet:connector_blue_dye","botania:metamorphic_fungal_cobblestone_slab","rftoolsbuilder:shape_card_liquid","mcwbiomesoplenty:dead_stable_head_door","mcwfurnitures:stripped_oak_desk","immersiveengineering:crafting/treated_fence","minecraft:gray_dye","terralith:piston_alt","farmersdelight:cooked_mutton_chops_from_campfire_cooking","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mininggadgets:mininggadget_fancy","twigs:polished_amethyst_stonecutting","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","computercraft:skull_dan200","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","mob_grinding_utils:recipe_tank","ae2:network/parts/energy_acceptor","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwwindows:warped_pane_window","minecraft:emerald_block","allthecompressed:compress/pumpkin_1x","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","create:calcite_from_stone_types_calcite_stonecutting","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","integrateddynamics:crafting/part_static_light_panel","mcwwindows:oak_plank_parapet","farmersdelight:cooking/cooked_rice","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:iron_sword","mcwfurnitures:jungle_table","domum_ornamentum:blue_cobblestone_extra","additionallanterns:blackstone_chain","mcwbiomesoplenty:stripped_mahogany_wardrobe","domum_ornamentum:purple_brick_extra","mcwwindows:one_way_glass_pane","mcwbiomesoplenty:hellbark_stable_door","connectedglass:scratched_glass_lime_pane2","mcwpaths:cherry_planks_path","minecraft:chiseled_sandstone_from_sandstone_stonecutting","comforts:hammock_red","mcwpaths:andesite_dumble_paving","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","chemlib:iron_ingot_from_blasting_iron_dust","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","productivetrees:sawmill","silentgear:helmet_template","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","productivebees:stonecutter/maple_canvas_hive","mcwbiomesoplenty:mahogany_picket_fence","mcwdoors:acacia_barn_glass_door","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","supplementaries:candle_holders/candle_holder_green_dye","railcraft:electric_locomotive","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","supplementaries:planter_rich","create:crafting/kinetics/metal_girder","minecolonies:blockconstructiontape","create:crafting/kinetics/millstone","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","travelersbackpack:dye_white_sleeping_bag","dyenamics:wine_stained_glass_pane_from_glass_pane","delightful:storage/acorn_storage_block","mcwroofs:gutter_base_white","mcwbridges:jungle_rail_bridge","silentgear:crimson_steel_ingot_from_block","enderio:resetting_lever_ten_from_prev","mcwroofs:magenta_concrete_upper_lower_roof","reliquary:crimson_cloth","immersiveengineering:crafting/cushion","mcwroofs:cyan_concrete_steep_roof","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","farmersdelight:beef_patty_from_smoking","mekanism:processing/copper/ingot/from_dust_blasting","minecraft:comparator","silentgear:cord_template","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwroofs:red_concrete_steep_roof","mcwbiomesoplenty:magic_bark_glass_door","xnet:connector_yellow_dye","rftoolspower:powercell_card","reliquary:alkahestry_altar","mcwwindows:crimson_stem_window","mcwdoors:acacia_waffle_door","mcwlights:yellow_lamp","mcwdoors:cherry_swamp_door","mcwbiomesoplenty:mahogany_bridge_pier","supplementaries:timber_frame","quark:tweaks/crafting/utility/misc/easy_hopper","mcwfurnitures:cherry_covered_desk","sophisticatedstorage:packing_tape","enderio:resetting_lever_thirty_from_inv","mcwlights:soul_oak_tiki_torch","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","mcwdoors:cherry_japanese2_door","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","ad_astra:white_flag","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","railcraft:wooden_tie","mcwfurnitures:jungle_drawer_counter","mcwroofs:stone_steep_roof","mekanism:steel_casing","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:creeper","botania:floating_daffomill","pneumaticcraft:wall_lamp_inverted_pink","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","ae2:shaped/walls/smooth_sky_stone_block","mcwfurnitures:cherry_triple_drawer","mcwroofs:light_gray_roof_block","minecraft:yellow_dye_from_sunflower","mcwroofs:magenta_terracotta_lower_roof","create:crafting/kinetics/windmill_bearing","utilitarian:fluid_hopper","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:stone_attic_roof","immersiveengineering:crafting/concrete","mekanism:paper","handcrafted:calcite_corner_trim","rftoolsbuilder:blue_shield_template_block","rftoolsbuilder:space_chamber","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwwindows:metal_window","mcwbiomesoplenty:mahogany_swamp_trapdoor","minecraft:glass_pane","mcwbiomesoplenty:maple_barn_glass_door","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","simplylight:illuminant_yellow_block_toggle","minecraft:clay","ae2:network/crafting/cpu_crafting_unit","mcwbiomesoplenty:pine_pane_window","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","ad_astra:blue_flag","create:crafting/kinetics/mechanical_plough","enderio:wood_gear","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","ae2:network/blocks/interfaces_interface_part","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwbiomesoplenty:mahogany_paper_door","modularrouters:range_down_augment","ae2:materials/annihilationcore","silentgear:stone_rod","botania:metamorphic_mesa_stone_stairs","ae2:network/cables/dense_covered_green","minecraft:leather_boots","railcraft:tank_detector","handcrafted:sandstone_corner_trim","ad_astra:etrionic_blast_furnace","create:crafting/logistics/andesite_tunnel","immersiveengineering:crafting/balloon","silentgear:leggings_template","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","dyenamics:persimmon_dye","productivebees:stonecutter/birch_canvas_expansion_box","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwbiomesoplenty:magic_nether_door","mob_grinding_utils:recipe_ender_inhibitor","pneumaticcraft:wall_lamp_inverted_light_blue","ae2:network/crystal_resonance_generator","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","mcwfurnitures:cherry_lower_triple_drawer","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","minecraft:dye_black_carpet","tombstone:white_marble","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","ae2:network/cables/covered_purple","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","chemlib:gold_ingot_from_blasting_gold_dust","minecraft:magma_block","enderio:resetting_lever_thirty_inv","botania:petal_blue","nethersdelight:nether_brick_smoker","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","botania:metamorphic_swamp_stone_stairs","aether:aether_iron_nugget_from_smelting","mcwbiomesoplenty:mahogany_planks_top_roof","silentgear:raw_crimson_iron_from_block","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","supplementaries:ash_brick","mcwfences:modern_granite_wall","productivebees:stonecutter/grimwood_canvas_hive","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_modern_wardrobe","mcwbiomesoplenty:fir_barn_door","simplylight:illuminant_block_on","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","silentgear:blueprint_book","alltheores:zinc_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:mahogany_coffee_table","biomesoplenty:dead_boat","mysticalagriculture:inferium_gemstone","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","handcrafted:quartz_pillar_trim","mcwbiomesoplenty:willow_japanese2_door","mcwwindows:andesite_window","domum_ornamentum:brick_extra","modularrouters:activator_module","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","minecraft:blaze_powder","cfm:brown_kitchen_drawer","evilcraft:crafting/broom_part_brush_bare","minecraft:chain","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","integratedtunnels:crafting/part_interface_item","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","create:polished_cut_calcite_wall_from_stone_types_calcite_stonecutting","mcwfences:crimson_highley_gate","mcwbiomesoplenty:pine_barn_glass_door","mcwbiomesoplenty:empyreal_window2","mininggadgets:upgrade_void_junk","mcwpaths:stone_flagstone_slab","minecraft:kjs/mininggadgets_upgrade_empty","ae2:decorative/sky_stone_small_brick_from_stonecutting","silentgear:crimson_steel_dust_smelting","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwroofs:magenta_terracotta_upper_steep_roof","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/generator","mcwwindows:warped_stem_four_window","immersiveengineering:crafting/blueprint_bullets","handcrafted:white_crockery_combo","mcwroofs:magenta_concrete_lower_roof","supplementaries:dispenser_minecart","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","travelersbackpack:wither","mcwdoors:jungle_mystic_door","silentgear:fine_silk_cloth","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","immersiveengineering:crafting/wooden_barrel","minecraft:calibrated_sculk_sensor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:fir_mystic_door","railcraft:bronze_gear","mcwbiomesoplenty:fir_beach_door","ae2:tools/certus_quartz_spade","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","ae2:network/cables/dense_covered_black","ae2:network/blocks/controller","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","croptopia:roasted_smoking","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","itemcollectors:advanced_collector","minecraft:green_terracotta","delightful:knives/brass_knife","evilcraft:crafting/broom_part_brush_wool","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","croptopia:pumpkin_soup","supplementaries:biomesoplenty/sign_post_mahogany","domum_ornamentum:white_brick_extra","supplementaries:gold_door","ae2:misc/fluixpearl","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","sfm:labelgun","mcwbiomesoplenty:mahogany_lower_roof","mcwfences:jungle_hedge","reliquary:angelic_feather","mcwdoors:jungle_classic_door","create:crafting/kinetics/large_cogwheel_from_little","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","immersiveengineering:crafting/clinker_brick_quoin","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","cfm:stripped_jungle_desk","handcrafted:oven","mcwbiomesoplenty:stripped_mahogany_table","handcrafted:calcite_pillar_trim","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","supplementaries:candle_holders/candle_holder_blue","rftoolsbuilder:shape_card_quarry_fortune","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","immersiveengineering:crafting/plate_aluminum_hammering","delightful:knives/bronze_knife","travelersbackpack:quartz","mcwbiomesoplenty:mahogany_classic_door","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","bigreactors:energizer/controller","mcwbiomesoplenty:stripped_maple_log_four_window","mcwwindows:stripped_dark_oak_log_window","mcwbiomesoplenty:stripped_mahogany_double_drawer","allthecompressed:compress/andesite_1x","mcwbiomesoplenty:willow_nether_door","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwpaths:brick_flagstone","create:small_calcite_brick_wall_from_stone_types_calcite_stonecutting","minecraft:blast_furnace","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","securitycraft:electrified_iron_fence","minecraft:light_blue_stained_glass_pane_from_glass_pane","minecraft:magenta_dye_from_lilac","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwwindows:warped_plank_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","mcwfurnitures:stripped_cherry_lower_triple_drawer","ae2:materials/carddistribution","ae2:network/parts/import_bus","minecraft:dye_red_bed","mcwdoors:acacia_stable_door","mcwbiomesoplenty:mahogany_bookshelf_cupboard","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","minecraft:blue_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_cupboard_counter","silentgear:crimson_iron_ingot_from_nugget","mcwbiomesoplenty:hellbark_cottage_door","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","silentgear:upgrade_base","ae2:network/cables/glass_pink","ae2:network/cables/smart_cyan","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","minecraft:dye_lime_bed","rftoolsbuilder:space_chamber_controller","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwwindows:one_way_glass","minecraft:spyglass","mcwdoors:jungle_cottage_door","botania:metamorphic_mountain_cobblestone_wall","mcwroofs:bricks_top_roof","minecraft:pink_stained_glass","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","minecraft:oak_chest_boat","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwbiomesoplenty:hellbark_classic_door","functionalstorage:armory_cabinet","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:dead_glass_door","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","mcwbiomesoplenty:willow_modern_door","pneumaticcraft:logistics_core","comforts:hammock_blue","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","create:polished_cut_dripstone_from_stone_types_dripstone_stonecutting","corail_woodcutter:acacia_woodcutter","minecraft:bread","mcwbiomesoplenty:redwood_western_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","mcwbiomesoplenty:dead_classic_door","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","dyenamics:peach_wool","mcwbiomesoplenty:umbran_swamp_door","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","chimes:amethyst_chimes","ae2:smelting/smooth_sky_stone_block","mcwbiomesoplenty:mahogany_cottage_door","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","travelersbackpack:bat_smithing","securitycraft:track_mine","mcwbiomesoplenty:willow_swamp_door","mcwdoors:acacia_glass_door","mcwbiomesoplenty:fir_stable_head_door","alltheores:osmium_dust_from_hammer_ingot_crushing","pneumaticcraft:camo_applicator","silentgear:template_board","deeperdarker:echo_boat","mcwbiomesoplenty:hellbark_swamp_door","create:calcite_pillar_from_stone_types_calcite_stonecutting","cfm:fridge_light","twigs:smooth_basalt_brick_wall_from_smooth_basalt_stonecutting","mcwroofs:andesite_upper_lower_roof","mcwfences:birch_picket_fence","botania:metamorphic_taiga_cobblestone_slab","enderio:basic_fluid_filter","aether:netherite_boots_repairing","biomesoplenty:jacaranda_boat","minecraft:oak_door","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwbiomesoplenty:maple_cottage_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","botania:floating_kekimurus","mcwlights:cyan_paper_lamp","mcwbiomesoplenty:empyreal_modern_door","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","connectedglass:tinted_borderless_glass_lime2","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","botania:petal_purple","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","botania:metamorphic_swamp_bricks_slab","create:crafting/kinetics/propeller","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:cherry_end_table","immersiveengineering:crafting/fluid_pipe","modularrouters:dropper_module","mcwbiomesoplenty:magic_highley_gate","mcwroofs:yellow_concrete_upper_lower_roof","dyenamics:mint_terracotta","ae2:network/cables/glass_magenta","supplementaries:candle_holders/candle_holder","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","biomesoplenty:fir_chest_boat","ae2:network/blocks/spatial_io_port","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","supplementaries:wrench","aether:skyroot_smithing_table","mcwroofs:cherry_lower_roof","securitycraft:reinforced_oak_fence_gate","botania:mushroom_stew","delightful:knives/aluminum_knife","bigreactors:energizer/chargingport_fe","mcwbiomesoplenty:stripped_redwood_log_four_window","croptopia:apple_juice","cfm:purple_kitchen_sink","minecraft:jungle_slab","botania:floating_dreadthorn","supplementaries:sconce","silentgear:rough_rod","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:pine_stable_door","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:granite","cfm:dye_cyan_picket_fence","minecraft:black_dye_from_wither_rose","sophisticatedstorage:smoking_upgrade","mcwwindows:stripped_spruce_log_four_window","mcwbiomesoplenty:magic_barn_glass_door","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","mcwbiomesoplenty:fir_bamboo_door","pneumaticcraft:wall_lamp_inverted_brown","mcwroofs:andesite_roof","rftoolsbase:infused_enderpearl","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwwindows:deepslate_pane_window","minecraft:dye_yellow_carpet","ae2:network/cables/glass_yellow","minecraft:dye_white_wool","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","dyenamics:rose_terracotta","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","handcrafted:silverfish_trophy","cfm:yellow_kitchen_counter","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","railcraft:iron_tank_valve","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","blue_skies:glowing_poison_stone","create:crafting/kinetics/copper_door","mcwdoors:oak_barn_door","silentgear:hammer_template","create:andesite_bars_from_andesite_alloy_stonecutting","botania:alchemy_catalyst","mcwbiomesoplenty:mahogany_barred_trapdoor","mcwbiomesoplenty:stripped_mahogany_bookshelf_drawer","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","minecraft:cobbled_deepslate_wall","supplementaries:timber_cross_brace","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","minecraft:smooth_stone","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","rftoolsutility:environmental_controller","mcwroofs:cyan_concrete_attic_roof","silentgear:crimson_iron_block","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","mcwbiomesoplenty:stripped_mahogany_desk","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","mcwbiomesoplenty:stripped_mahogany_large_drawer","sophisticatedstorage:chipped/mason_table_upgrade","mcwroofs:white_concrete_attic_roof","xnet:controller","cookingforblockheads:sink","botania:metamorphic_desert_bricks","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","reliquary:uncrafting/ender_pearl","additionallanterns:obsidian_chain","minecraft:ender_eye","supplementaries:blackstone_lamp","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","rftoolsbase:manual","mininggadgets:upgrade_range_1","mininggadgets:upgrade_range_2","mininggadgets:upgrade_range_3","immersiveengineering:crafting/connector_hv_relay","ae2:block_cutter/walls/quartz_wall","ae2:network/blocks/io_condenser","minecraft:cooked_mutton_from_campfire_cooking","mcwpaths:brick_clover_paving","minecraft:dye_black_wool","securitycraft:trophy_system","ad_astra:small_green_industrial_lamp","minecraft:cooked_beef","mcwbiomesoplenty:pine_curved_gate","mcwfurnitures:stripped_cherry_wardrobe","mcwbiomesoplenty:jacaranda_four_panel_door","cfm:green_trampoline","mcwlights:yellow_paper_lamp","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","railcraft:iron_tank_gauge","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","dyenamics:wine_stained_glass","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwroofs:pink_striped_awning","mcwbiomesoplenty:mahogany_japanese_door","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:umbran_four_panel_door","twigs:calcite_stairs","silentgear:crimson_steel_dust","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","silentgear:pickaxe_template","mcwbiomesoplenty:pine_glass_door","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","ae2:network/cells/item_storage_components_cell_1k_part","mcwroofs:oak_planks_top_roof","botania:floating_hopperhock_chibi","mcwroofs:oak_planks_roof","enderio:fluid_tank","farmersdelight:cooked_mutton_chops","ad_astra:iron_rod","botania:petal_white","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","aether:chainmail_boots_repairing","supplementaries:blackstone_tile","ae2:block_cutter/slabs/smooth_sky_stone_slab","create:small_dripstone_brick_slab_from_stone_types_dripstone_stonecutting","minecraft:enchanting_table","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","mcwbiomesoplenty:mahogany_striped_chair","farmersdelight:roast_chicken_block","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","mcwroofs:blue_terracotta_lower_roof","ad_astra:small_blue_industrial_lamp","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","aether:skyroot_note_block","mcwbiomesoplenty:maple_tropical_door","pneumaticcraft:logistics_configurator","bigreactors:smelting/yellorium_from_raw","create:dripstone_pillar_from_stone_types_dripstone_stonecutting","rftoolsutility:dialing_device","botania:metamorphic_swamp_bricks","allthecompressed:compress/diamond_block_1x","sfm:disk","minecraft:kjs/structurecompass_structure_compass","dyenamics:banner/lavender_banner","botania:ender_hand","farmersdelight:bread_from_smoking","modularrouters:energy_distributor_module","handcrafted:spider_trophy","allthecompressed:compress/calcite_1x","mcwroofs:lime_terracotta_lower_roof","ae2:network/cables/dense_smart_green","mininggadgets:upgrade_silk","mcwlights:chain_wall_lantern","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","mythicbotany:central_rune_holder","mcwfurnitures:cherry_chair","create:crafting/kinetics/light_gray_seat","railcraft:diamond_crowbar","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","productivebees:stonecutter/dark_oak_canvas_expansion_box","mcwpaths:cobbled_deepslate_running_bond_slab","mcwbiomesoplenty:stripped_mahogany_chair","buildinggadgets2:gadget_cut_paste","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","rftoolsbuilder:mover_status","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","aether:iron_gloves_repairing","supplementaries:candle_holders/candle_holder_green","aether:skyroot_fletching_table","create:crafting/kinetics/gearbox","mcwbiomesoplenty:umbran_modern_door","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","mekanism:fluid_tank/basic","mcwroofs:white_roof_slab","energymeter:meter","mcwroofs:purple_concrete_upper_lower_roof","comforts:sleeping_bag_to_blue","minecraft:nether_brick_stairs","supplementaries:bubble_blower","naturalist:glow_goop","dyenamics:conifer_stained_glass_pane_from_glass_pane","minecraft:recovery_compass","minecraft:magenta_dye_from_blue_red_white_dye","minecraft:cherry_fence","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","allthecompressed:compress/ancient_stone_1x","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","productivebees:stonecutter/fir_canvas_expansion_box","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","mcwbiomesoplenty:willow_bark_glass_door","mcwdoors:cherry_bamboo_door","minecraft:pink_terracotta","botania:metamorphic_taiga_bricks","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","minecraft:spire_armor_trim_smithing_template_smithing_trim","rftoolsutility:destination_analyzer","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","ae2:materials/cardfuzzy","mcwbiomesoplenty:mahogany_curved_gate","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","securitycraft:camera_monitor","immersiveengineering:crafting/clinker_brick_sill","allthecompressed:compress/quartz_block_1x","mcwbiomesoplenty:umbran_curved_gate","mcwroofs:black_top_roof","mcwfences:modern_nether_brick_wall","botania:metamorphic_mountain_bricks","silentgear:crimson_steel_nugget","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","cfm:jungle_kitchen_sink_light","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","biomesoplenty:mahogany_boat","quark:tweaks/crafting/utility/misc/charcoal_to_black_dye","botania:floating_loonium","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","sophisticatedstorage:controller","biomesoplenty:palm_chest_boat","mcwbiomesoplenty:redwood_bark_glass_door","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwroofs:magenta_concrete_upper_steep_roof","minecraft:tinted_glass","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:lodestone","mcwlights:black_lamp","apotheosis:scrap_tome","mcwpaths:sandstone_running_bond_stairs","create:crafting/kinetics/speedometer","mcwbiomesoplenty:mahogany_window","minecraft:stone_stairs","handcrafted:evoker_trophy","mysticalagriculture:essence/common/silicon","biomesoplenty:magenta_dye_from_wildflower","minecraft:nether_brick_wall","connectedglass:borderless_glass_red2","constructionwand:core_destruction","mcwfences:oak_pyramid_gate","farmersdelight:beef_patty_from_campfire_cooking","handcrafted:terracotta_thick_pot","mcwbiomesoplenty:mahogany_modern_chair","productivetrees:sawdust_to_paper_water_bottle","create:polished_cut_dripstone_slab_from_stone_types_dripstone_stonecutting","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","enderio:resetting_lever_five","mcwbiomesoplenty:hellbark_beach_door","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","integrateddynamics:crafting/crystalized_menril_chunk","ae2:network/parts/terminals","securitycraft:keypad_frame","enderio:resetting_lever_thirty_inv_from_prev","securitycraft:whitelist_module","ae2:network/blocks/energy_energy_acceptor","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","xnet:connector_routing","minecraft:blue_carpet","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","mcwroofs:green_terracotta_attic_roof","mcwbiomesoplenty:palm_bark_glass_door","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","farmersdelight:barbecue_stick","immersiveengineering:crafting/shield","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","dyenamics:ultramarine_candle","mcwroofs:cyan_terracotta_steep_roof","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwbiomesoplenty:pine_stable_head_door","pneumaticcraft:search_upgrade","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","ae2:network/blocks/storage_chest","cfm:jungle_desk","minecraft:dripstone_block","minecraft:mangrove_chest_boat","mcwbiomesoplenty:stripped_mahogany_drawer","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_blue_concrete_lower_roof","utilitarian:utility/cherry_logs_to_stairs","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","mcwbiomesoplenty:palm_japanese2_door","mcwbiomesoplenty:mahogany_bark_trapdoor","mcwbiomesoplenty:willow_curved_gate","draconicevolution:disenchanter","minecraft:jungle_trapdoor","croptopia:mashed_potatoes","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","mcwbiomesoplenty:maple_japanese_door","mcwroofs:cherry_attic_roof","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","modularrouters:xp_vacuum_augment","ae2:network/cables/dense_covered_purple","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","botania:metamorphic_fungal_cobblestone_wall","mcwfurnitures:cherry_counter","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","modularrouters:redstone_augment","mcwtrpdoors:jungle_swamp_trapdoor","xnet:wireless_router","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","mcwwindows:dark_prismarine_brick_gothic","mcwroofs:black_roof_slab","mcwfurnitures:oak_double_drawer_counter","mcwroofs:orange_terracotta_top_roof","travelersbackpack:gold_tier_upgrade","mcwfences:mesh_metal_fence_gate","xnet:netcable_yellow","mcwroofs:blue_terracotta_roof","mcwfurnitures:jungle_wardrobe","mcwpaths:blackstone_crystal_floor_stairs","mcwfurnitures:stripped_cherry_lower_bookshelf_drawer","cfm:red_sofa","dyenamics:navy_wool","alltheores:platinum_gear","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","botania:chiseled_metamorphic_forest_bricks","rftoolscontrol:tank","utilitix:mob_bell","mcwbiomesoplenty:willow_beach_door","mcwroofs:green_terracotta_upper_lower_roof","mcwpaths:andesite_running_bond_slab","utilitarian:utility/oak_logs_to_boats","minecraft:chiseled_stone_bricks_stone_from_stonecutting","mcwbiomesoplenty:fir_hedge","minecraft:netherite_sword_smithing","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","mcwfurnitures:cherry_double_wardrobe","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","dankstorage:4_to_5","delightful:food/baklava","quark:building/crafting/oak_bookshelf","mcwdoors:metal_hospital_door","railcraft:signal_circuit","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","dyenamics:dye_conifer_carpet","sophisticatedstorage:oak_chest_from_vanilla_chest","supplementaries:sign_post_cherry","immersiveengineering:crafting/screwdriver","connectedglass:borderless_glass_pane1","cfm:pink_cooler","mcwfurnitures:oak_double_drawer","mcwfences:railing_blackstone_wall","minecraft:stone_bricks_from_stone_stonecutting","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","ad_astra:blue_industrial_lamp","dankstorage:1_to_2","mcwbiomesoplenty:maple_modern_door","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","botania:apothecary_plains","botania:metamorphic_plains_bricks_slab","mcwbiomesoplenty:mahogany_planks_upper_lower_roof","mcwfences:modern_sandstone_wall","mcwpaths:andesite_strewn_rocky_path","utilitix:tiny_charcoal_to_tiny","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","croptopia:sushi","mcwroofs:blue_concrete_roof","nethersdelight:hoglin_sirloin_from_campfire_cooking","mcwbiomesoplenty:fir_plank_window","mcwtrpdoors:cherry_tropical_trapdoor","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwwindows:crimson_shutter","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","ad_astra:iron_panel","mcwpaths:cobblestone_clover_paving","minecraft:kjs/silentgear_salvager","minecraft:blue_candle","railcraft:blast_furnace_bricks","silentgear:crude_repair_kit","ae2:network/cables/dense_covered_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","handcrafted:dripstone_pillar_trim","mcwroofs:purple_terracotta_upper_lower_roof","mcwroofs:gray_striped_awning","biomesoplenty:orange_dye_from_burning_blossom","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","blue_skies:cake_compat","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_bricks_stonecutting","minecraft:gold_ingot_from_blasting_raw_gold","botania:floating_dandelifeon","productivebees:stonecutter/aspen_canvas_expansion_box","handcrafted:wood_cup","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:steamed_rice","mcwbiomesoplenty:mahogany_nether_door","mcwbiomesoplenty:hellbark_window2","rftoolsbuilder:shape_card_quarry","mcwbiomesoplenty:magic_hedge","mcwbiomesoplenty:mahogany_barrel_trapdoor","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime1","connectedglass:borderless_glass_lime2","twigs:calcite_wall","cfm:orange_trampoline","mcwroofs:lime_concrete_steep_roof","silentgear:shovel_template","minecraft:wooden_hoe","mcwbiomesoplenty:redwood_waffle_door","createoreexcavation:diamond_drill","handcrafted:white_cup","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","mcwlights:light_gray_lamp","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","botania:floating_marimorphosis","mcwfences:deepslate_brick_grass_topped_wall","mcwwindows:andesite_window2","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","railcraft:steel_leggings","delightful:food/cooking/nut_butter_bottle","mcwbiomesoplenty:umbran_nether_door","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","minecraft:piston","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","connectedglass:borderless_glass_red_pane2","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwbiomesoplenty:dead_modern_door","mcwdoors:acacia_cottage_door","croptopia:apple_sapling","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:cherry_planks_lower_roof","mcwroofs:oak_steep_roof","forbidden_arcanus:edelwood_planks","botania:metamorphic_forest_cobblestone_slab","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwbiomesoplenty:magic_modern_door","handcrafted:stackable_book","handcrafted:cherry_fancy_bed","mcwroofs:andesite_attic_roof","mcwroofs:sandstone_top_roof","modularrouters:blast_upgrade","securitycraft:reinforced_green_stained_glass","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","silentgear:raw_crimson_iron_block","allthecompressed:compress/soul_soil_1x","productivebees:stonecutter/jacaranda_canvas_expansion_box","mcwbridges:end_stone_bricks_bridge_pier","mcwbiomesoplenty:stripped_mahogany_glass_table","mcwroofs:red_terracotta_upper_lower_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwroofs:lime_striped_awning","botania:metamorphic_forest_bricks","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","productivebees:stonecutter/oak_canvas_expansion_box","immersiveengineering:crafting/stick_treated","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwdoors:print_waffle","enderio:dark_steel_nugget_to_ingot","mcwwindows:birch_shutter","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","sfm:water_tank","create:polished_cut_calcite_from_stone_types_calcite_stonecutting","mcwfences:crimson_wired_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","cfm:dye_lime_picket_fence","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","mcwfurnitures:cherry_bookshelf","mcwroofs:red_terracotta_attic_roof","mcwdoors:acacia_classic_door","create:small_dripstone_brick_wall_from_stone_types_dripstone_stonecutting","mcwfences:end_brick_grass_topped_wall","computercraft:pocket_computer_advanced","dyenamics:bed/ultramarine_bed_frm_white_bed","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_jungle_stool_chair","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwwindows:white_curtain","mcwroofs:white_terracotta_roof","bigreactors:reactor/basic/solidaccessport","handcrafted:cherry_desk","cfm:light_gray_grill","cfm:oak_desk","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","allthecompressed:compress/dripstone_block_1x","supplementaries:daub_frame","mcwroofs:light_gray_top_roof","minecolonies:potato_soup","mcwbiomesoplenty:pine_mystic_door","mcwbiomesoplenty:fir_paper_door","botania:metamorphic_mesa_bricks_stairs","mcwfurnitures:jungle_double_drawer","immersiveengineering:crafting/minecart_metalbarrel","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:lavender_stained_glass","forbidden_arcanus:ender_pearl","minecraft:green_stained_glass_pane_from_glass_pane","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","utilitarian:utility/cherry_logs_to_boats","securitycraft:briefcase","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","mcwwindows:cobblestone_arrow_slit","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/dense_covered_white","mcwpaths:andesite_crystal_floor_stairs","modularrouters:distributor_module","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","create:crafting/logistics/pulse_extender","mekanismtools:osmium/tools/pickaxe","aether:golden_pendant","mcwdoors:cherry_mystic_door","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","undergarden:grongle_boat","rftoolsutility:matter_beamer","mcwbiomesoplenty:dead_nether_door","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","littlelogistics:automatic_switch_rail","minecraft:carrot_on_a_stick","mcwbiomesoplenty:stripped_mahogany_triple_drawer","cfm:dye_blue_picket_gate","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","mcwlights:copper_double_candle_holder","mcwroofs:blue_concrete_upper_steep_roof","laserio:logic_chip_raw","supplementaries:cage","mcwfences:diorite_grass_topped_wall","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","silentgear:tip_template","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","simplylight:illuminant_brown_block_dyed","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","mcwroofs:lime_terracotta_upper_lower_roof","mcwbiomesoplenty:maple_mystic_door","mekanism:processing/osmium/raw/from_raw_block","chemlib:platinum_ingot_from_blasting_platinum_dust","expatternprovider:silicon_block","minecraft:item_frame","itemcollectors:basic_collector","enderio:resetting_lever_three_hundred_inv","minecraft:polished_granite_stairs_from_polished_granite_stonecutting","botania:cell_block","mcwtrpdoors:oak_bark_trapdoor","silentgear:slingshot_template","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","travelersbackpack:cow","alltheores:brass_dust_from_alloy_blending","mcwroofs:sandstone_attic_roof","integrateddynamics:crafting/cable","handcrafted:skeleton_trophy","handcrafted:golden_wide_pot","mcwfurnitures:stripped_cherry_chair","rftoolsutility:tank","aether:netherite_gloves_smithing","mcwbiomesoplenty:mahogany_stable_head_door","railcraft:steam_locomotive","mcwpaths:brick_flagstone_slab","reliquary:uncrafting/magma_cream","supplementaries:flags/flag_green","undergarden:smogstem_boat","computercraft:computer_advanced","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","quark:building/crafting/furnaces/cobblestone_furnace","sophisticatedstorage:generic_chest","minecraft:jungle_button","handcrafted:wood_plate","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","botania:metamorphic_taiga_bricks_wall","travelersbackpack:horse","cfm:dye_red_picket_fence","cfm:brown_cooler","xnet:netcable_red","mcwfences:diorite_pillar_wall","mcwwindows:dark_prismarine_parapet","rftoolsutility:inventoryplus_module","dyenamics:amber_candle","bigreactors:reprocessor/wasteinjector","undergarden:shard_torch","mcwfences:bamboo_picket_fence","handcrafted:goat_trophy","minecraft:respawn_anchor","mcwpaths:cobbled_deepslate_flagstone","dyenamics:persimmon_stained_glass_pane_from_glass_pane","computercraft:disk_drive","integrateddynamics:crafting/variable_transformer_output","mcwroofs:blue_striped_awning","pneumaticcraft:wall_lamp_red","mcwbiomesoplenty:mahogany_modern_door","alltheores:signalum_dust_from_alloy_blending","mcwbridges:jungle_bridge_pier","buildinggadgets2:gadget_exchanging","comforts:hammock_to_blue","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cables/covered_light_blue","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","minecraft:composter","mcwfurnitures:stripped_cherry_modern_chair","aether:diamond_sword_repairing","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_cherry_drawer","domum_ornamentum:cyan_floating_carpet","mcwfurnitures:cherry_modern_chair","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","dyenamics:dye_spring_green_carpet","securitycraft:reinforced_light_gray_stained_glass","farmersdelight:cooking/pasta_with_mutton_chop","minecraft:chiseled_quartz_block","mcwwindows:blackstone_pane_window","connectedglass:borderless_glass_yellow2","mcwdoors:jungle_whispering_door","mcwlights:bamboo_tiki_torch","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","ae2:network/cables/glass_white","croptopia:egg_roll","mcwtrpdoors:cherry_barrel_trapdoor","connectedglass:borderless_glass_black2","mcwtrpdoors:oak_whispering_trapdoor","dyenamics:bed/lavender_bed_frm_white_bed","forbidden_arcanus:rotten_leather","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","create:crafting/kinetics/mechanical_mixer","domum_ornamentum:yellow_brick_extra","aether:iron_pendant","minecraft:lime_dye","mcwbiomesoplenty:pine_bamboo_door","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:rope_mahogany_bridge","supplementaries:flags/flag_purple","botania:quartz_sunny","productivebees:stonecutter/maple_canvas_expansion_box","botania:metamorphic_forest_stone_wall","tombstone:bone_needle","minecraft:stone_brick_wall","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_cherry_covered_desk","cfm:warped_kitchen_sink_light","sophisticatedstorage:decoration_table","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","mcwtrpdoors:cherry_cottage_trapdoor","minecraft:cyan_dye","evilcraft:crafting/dark_stick","connectedglass:scratched_glass_white_pane2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","immersiveengineering:crafting/glider","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","minecraft:gold_block","botania:floating_pure_daisy","dyenamics:banner/navy_banner","handcrafted:jungle_counter","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","minecraft:cherry_wood","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","connectedglass:scratched_glass_cyan2","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","twilightforest:canopy_chest_boat","mcwroofs:jungle_planks_attic_roof","mcwlights:golden_small_chandelier","botania:metamorphic_swamp_stone_slab","mcwwindows:lime_mosaic_glass","create_enchantment_industry:crafting/disenchanter","securitycraft:reinforced_observer","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwbiomesoplenty:umbran_highley_gate","mcwbiomesoplenty:dead_pane_window","utilitix:reinforced_rail","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","mcwbiomesoplenty:jacaranda_western_door","utilitix:stonecutter_cart","easy_villagers:auto_trader","cfm:dye_green_picket_fence","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","farmersdelight:cherry_cabinet","mcwroofs:cherry_planks_top_roof","mcwroofs:yellow_terracotta_lower_roof","littlelogistics:spring","allthecompressed:compress/jungle_planks_1x","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:mahogany_planks_path","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_middle_purple","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","create:cut_dripstone_brick_wall_from_stone_types_dripstone_stonecutting","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","supplementaries:candle_holders/candle_holder_yellow_dye","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","mcwroofs:orange_terracotta_upper_steep_roof","farmersdelight:steak_and_potatoes","mcwroofs:green_striped_awning","gtceu:shapeless/decompress_aluminium_from_ore_block","allthemodium:vibranium_plate","securitycraft:codebreaker","gtceu:shaped/piston_aluminium","rftoolsutility:energy_module","constructionwand:infinity_wand","aquaculture:wooden_fillet_knife","apotheosis:salvaging_table","reliquary:mob_charm_fragments/ghast","botania:metamorphic_desert_stone_wall","minecraft:polished_blackstone_wall","dyenamicsandfriends:green_dye","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","alltheores:gold_plate","dyenamics:spring_green_stained_glass_pane_from_glass_pane","cfm:blue_kitchen_counter","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwbridges:end_stone_bricks_bridge_stair","mcwroofs:base_top_roof","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","immersiveengineering:crafting/grit_sand","croptopia:soy_milk","create:crafting/kinetics/item_vault","mcwfences:warped_highley_gate","pneumaticcraft:minigun","megacells:network/cell_dock","supplementaries:statue","mcwbiomesoplenty:mahogany_tropical_door","minecraft:dye_green_carpet","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","enderio:dark_steel_grinding_ball","sophisticatedstorage:chipped/glassblower_upgrade","mcwpaths:sandstone_crystal_floor","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","mcwbiomesoplenty:fir_stockade_fence","silentgear:lining_template","ae2:network/blocks/pattern_providers_interface","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","alltheores:bronze_plate","create:crafting/kinetics/depot","pneumaticcraft:wall_lamp_yellow","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","mcwbiomesoplenty:mahogany_rail_bridge","pneumaticcraft:pressure_chamber_wall","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:end_stone_brick_stairs","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","domum_ornamentum:gray_floating_carpet","securitycraft:reinforced_dark_oak_fence_gate","minecraft:smooth_sandstone_stairs_from_smooth_sandstone_stonecutting","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","ae2:network/cables/dense_covered_brown","enderio:dark_steel_ladder","minecraft:cyan_terracotta","botania:floating_rafflowsia","ad_astra:green_industrial_lamp","silentgear:fine_silk","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","mcwbiomesoplenty:stripped_maple_pane_window","botania:spawner_claw","rftoolsstorage:storage_control_module","mcwbiomesoplenty:pine_nether_door","enderio:pressurized_fluid_tank","mcwbiomesoplenty:mahogany_barn_trapdoor","mcwbiomesoplenty:hellbark_barn_glass_door","fluxnetworks:fluxcore","immersiveengineering:crafting/treated_wood_horizontal","bigreactors:smelting/graphite_from_charcoal","mcwroofs:green_concrete_top_roof","connectedglass:borderless_glass_cyan2","mcwbiomesoplenty:jacaranda_wired_fence","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","reliquary:salamander_eye","mcwwindows:cherry_plank_window","handcrafted:bricks_pillar_trim","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","botania:metamorphic_plains_cobblestone_slab","pneumaticcraft:compressed_brick_slab","mcwbiomesoplenty:redwood_four_panel_door","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","immersiveengineering:crafting/alu_wallmount","pylons:potion_filter","create:crafting/kinetics/magenta_seat","create:crafting/kinetics/brown_seat","littlelogistics:energy_tug","handcrafted:quartz_corner_trim","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","handcrafted:cherry_corner_trim","dyenamics:fluorescent_stained_glass","alltheores:bronze_rod","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","pylons:interdiction_pylon","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","alltheores:aluminum_gear","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","railcraft:player_detector","create:crafting/appliances/copper_diving_boots","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","ae2:network/cables/smart_yellow","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","silentgear:crimson_iron_raw_ore_smelting","integratedterminals:crafting/part_terminal_storage","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","sfm:fancy_to_cable","mcwbiomesoplenty:palm_western_door","aether:netherite_sword_repairing","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","biomesoplenty:rabbit_stew_from_toadstool","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","cfm:black_picket_fence","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","xnet:router","mcwfurnitures:stripped_jungle_modern_chair","sliceanddice:sprinkler","mcwbiomesoplenty:willow_classic_door","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","mcwroofs:orange_concrete_lower_roof","cfm:magenta_kitchen_counter","easy_villagers:trader","minecraft:barrel","utilitix:tiny_coal_to_tiny","create:cut_calcite_brick_stairs_from_stone_types_calcite_stonecutting","supplementaries:boat_jar","ae2:materials/basiccard","handcrafted:oak_table","handcrafted:white_plate","modularrouters:regulator_augment","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwdoors:acacia_swamp_door","forbidden_arcanus:obsidian_skull","cfm:stripped_jungle_kitchen_sink_dark","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","mcwlights:blue_lamp","littlelogistics:car_dock_rail","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","mcwroofs:blue_concrete_attic_roof","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:cauldron","mcwbridges:nether_bricks_bridge","delightful:smelting/roasted_acorn","mcwwindows:stripped_mangrove_pane_window","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","mcwfurnitures:oak_kitchen_cabinet","aether:iron_boots_repairing","ae2:block_cutter/walls/smooth_sky_stone_wall","ae2:network/cables/dense_covered_magenta","biomesoplenty:mahogany_fence_gate","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:netherite_gloves_repairing","tombstone:impregnated_diamond","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwbiomesoplenty:dead_beach_door","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","minecraft:popped_chorus_fruit","supplementaries:candle_holders/candle_holder_lime_dye","mcwbiomesoplenty:fir_barn_glass_door","mcwbiomesoplenty:umbran_window2","mcwfurnitures:oak_double_kitchen_cabinet","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","minecraft:red_bed","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","dyenamics:dye_persimmon_carpet","utilitix:stone_wall","dyenamics:navy_dye","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwbiomesoplenty:jacaranda_swamp_door","silentgear:paxel_template","mcwbiomesoplenty:mahogany_stool_chair","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","botania:apothecary_mountain","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","productivebees:stonecutter/concrete_canvas_hive","createoreexcavation:vein_finder","immersiveengineering:crafting/reinforced_crate","silentgear:hoe_template","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwwindows:light_blue_mosaic_glass_pane","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","minecraft:copper_ingot_from_smelting_raw_copper","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","sophisticatedstorage:oak_limited_barrel_1","sophisticatedstorage:oak_limited_barrel_2","cfm:magenta_cooler","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","domum_ornamentum:beige_bricks","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mekanism:metallurgic_infuser","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","sophisticatedstorage:backpack_stack_upgrade_tier_3_from_storage_stack_upgrade_tier_4","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","alltheores:aluminum_rod","mcwroofs:yellow_striped_awning","minecraft:light_blue_terracotta","mcwwindows:spruce_plank_window","sophisticatedstorage:cherry_barrel","botania:chiseled_metamorphic_plains_bricks","botania:floating_hopperhock","minecraft:jungle_sign","minecraft:cyan_candle","mcwfences:railing_mud_brick_wall","expatternprovider:precise_storage_bus","domum_ornamentum:cactus_extra","cfm:cyan_cooler","ae2:network/parts/formation_plane","railcraft:silver_gear","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwbiomesoplenty:mahogany_mystic_door","create:crafting/kinetics/whisk","create:crafting/kinetics/mechanical_drill","create:cut_dripstone_wall_from_stone_types_dripstone_stonecutting","mcwroofs:light_gray_roof","mcwbiomesoplenty:umbran_beach_door","mcwroofs:gutter_base_purple","mcwbiomesoplenty:umbran_bark_glass_door","dyenamics:fluorescent_terracotta","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","mcwroofs:cherry_planks_upper_lower_roof","minecraft:slime_block","mcwwindows:blue_curtain","mekanismtools:bronze/tools/pickaxe","dyenamics:lavender_stained_glass_pane_from_glass_pane","create:cut_calcite_from_stone_types_calcite_stonecutting","chimes:glass_bells","mcwwindows:iron_shutter","mcwdoors:cherry_paper_door","minecraft:netherite_upgrade_smithing_template","handcrafted:kitchen_hood_pipe","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","mcwpaths:blackstone_running_bond_path","allthemodium:allthemodium_pickaxe","immersiveengineering:crafting/sawblade","mcwbiomesoplenty:empyreal_swamp_door","mcwbridges:blackstone_bridge_pier","modularrouters:flinger_module","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","sophisticatedstorage:basic_to_gold_tier_upgrade","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","minecraft:magenta_stained_glass_pane_from_glass_pane","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","alltheores:copper_rod","productivebees:stonecutter/cherry_canvas_hive","reliquary:uncrafting/gunpowder_creeper_gland","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","botania:metamorphic_desert_bricks_slab","mcwfences:warped_stockade_fence","mcwfurnitures:stripped_oak_double_drawer","aquaculture:cooked_fish_fillet","silentgear:crossbow_template","cfm:orange_kitchen_sink","mcwbiomesoplenty:mahogany_attic_roof","croptopia:pork_jerky","enderio:resetting_lever_sixty_from_inv","minecraft:polished_blackstone_bricks","minecraft:mossy_cobblestone_from_moss_block","mininggadgets:mininggadget_simple","mcwdoors:cherry_classic_door","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwbiomesoplenty:redwood_window","quark:building/crafting/cherry_bookshelf","mcwbiomesoplenty:willow_western_door","mcwbridges:nether_bricks_bridge_pier","ae2:network/cables/dense_covered_yellow","handcrafted:stone_corner_trim","mcwpaths:cobbled_deepslate_running_bond_path","create:crafting/kinetics/shaft","delightful:knives/bone_knife","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","mcwpaths:sandstone_square_paving","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","mcwwindows:mangrove_pane_window","productivebees:stonecutter/river_canvas_expansion_box","botania:metamorphic_taiga_stone_slab","farmersdelight:flint_knife","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","silentgear:chestplate_template","ae2:network/cables/glass_purple","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","botania:metamorphic_mesa_cobblestone_stairs","immersiveengineering:crafting/plate_steel_hammering","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","modularrouters:stack_upgrade","botania:metamorphic_mesa_stone_wall","alltheores:aluminum_plate","aquaculture:gold_nugget_from_blasting","bloodmagic:synthetic_point","ad_astra:small_yellow_industrial_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","minecraft:redstone_lamp","mcwbiomesoplenty:stripped_umbran_pane_window","supplementaries:candle_holders/candle_holder_cyan_dye","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwbiomesoplenty:dead_barn_glass_door","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","silentgear:coating_template","littlecontraptions:contraption_barge","productivetrees:crates/red_delicious_apple_crate_unpack","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwwindows:green_mosaic_glass_pane","mcwbiomesoplenty:pine_western_door","minecraft:dye_blue_bed","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:mahogany_upper_lower_roof","dyenamics:banner/fluorescent_banner","securitycraft:bouncing_betty","minecraft:painting","connectedglass:tinted_borderless_glass_blue2","connectedglass:tinted_borderless_glass_white2","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwbiomesoplenty:palm_stable_door","rftoolsbuilder:shield_block2","rftoolsbuilder:shield_block1","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","minecraft:stone_brick_slab_from_stone_stonecutting","mcwbiomesoplenty:palm_four_panel_door","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","terralith:dropper_alt","mcwlights:white_paper_lamp","create:crafting/materials/andesite_alloy_block","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","mcwwindows:mangrove_plank_pane_window","modularrouters:speed_upgrade","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","create:crafting/kinetics/placard","chimes:iron_chimes","farmersdelight:grilled_salmon","securitycraft:storage_module","immersiveengineering:crafting/chute_copper","mcwfurnitures:cherry_drawer","handcrafted:cherry_drawer","mcwfences:prismarine_grass_topped_wall","mcwbiomesoplenty:mahogany_cottage_trapdoor","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","twilightforest:mangrove_chest_boat","modularrouters:sync_upgrade","simplylight:illuminant_green_block_on_toggle","travelersbackpack:wolf","productivebees:stonecutter/mahogany_canvas_expansion_box","forbidden_arcanus:stone_blacksmith_gavel","minecraft:brick","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","mcwfurnitures:stripped_cherry_striped_chair","pneumaticcraft:wall_lamp_inverted_yellow","mcwdoors:cherry_cottage_door","cfm:stripped_oak_table","twigs:bone_meal_from_seashells","mcwlights:golden_triple_candle_holder","connectedglass:scratched_glass_lime2","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:coal","mcwbiomesoplenty:mahogany_bamboo_door","mcwroofs:blue_terracotta_upper_lower_roof","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","supplementaries:sack","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwdoors:cherry_stable_door","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","handcrafted:cherry_cupboard","cfm:stripped_oak_mail_box","mcwwindows:nether_brick_gothic","mcwbiomesoplenty:willow_mystic_door","mcwbiomesoplenty:pine_modern_door","create:andesite_ladder_from_andesite_alloy_stonecutting","reliquary:uncrafting/blaze_rod","minecraft:brown_dye","alltheores:silver_plate","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","cfm:lime_cooler","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","croptopia:roasted_sunflower_seeds","botania:fel_pumpkin","handcrafted:cherry_shelf","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","dyenamics:mint_dye","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","productivebees:stonecutter/cherry_canvas_expansion_box","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","mcwlights:iron_chandelier","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:prismarine_window2","utilitarian:tps_meter","mcwbiomesoplenty:pine_horse_fence","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","domum_ornamentum:yellow_floating_carpet","minecraft:blue_banner","mcwroofs:nether_bricks_upper_steep_roof","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","ae2:decorative/cut_quartz_block","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","handcrafted:cherry_pillar_trim","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","alltheores:gold_rod","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","reliquary:mercy_cross","farmersdelight:fried_egg","mcwbiomesoplenty:maple_pane_window","botania:metamorphic_fungal_stone_slab","mcwfurnitures:oak_bookshelf_cupboard","mcwfurnitures:cherry_double_drawer_counter","travelersbackpack:pumpkin","mcwfurnitures:stripped_oak_table","connectedglass:clear_glass_red2","create:polished_cut_dripstone_stairs_from_stone_types_dripstone_stonecutting","mcwbiomesoplenty:mahogany_table","botania:metamorphic_mesa_bricks_slab","croptopia:pork_and_beans","mcwpaths:blackstone_windmill_weave_stairs","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","create:crafting/kinetics/belt_connector","mcwdoors:jail_door","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","reliquary:mob_charm_fragments/enderman","allthemodium:allthemodium_axe","pneumaticcraft:wall_lamp_inverted_gray","minecraft:dye_lime_carpet","mcwpaths:brick_running_bond_path","rftoolsbuilder:shape_card_quarry_dirt","mcwbiomesoplenty:magic_window2","immersiveengineering:crafting/minecart_reinforcedcrate","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","silentgear:crimson_repair_kit","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","minecraft:sea_lantern","immersiveengineering:crafting/lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","minecraft:dye_cyan_wool","mcwbiomesoplenty:stripped_mahogany_counter","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","mcwroofs:blackstone_top_roof","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","cfm:gray_cooler","everythingcopper:copper_axe","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","connectedglass:scratched_glass_green_pane2","mcwroofs:stone_upper_steep_roof","connectedglass:scratched_glass_white2","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","simplylight:edge_light","mcwfences:railing_granite_wall","immersiveengineering:crafting/plate_uranium_hammering","handcrafted:oak_nightstand","railcraft:personal_world_spike","mcwlights:lava_lamp","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","xnet:redstoneproxy_update","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","immersiveengineering:crafting/wirecoil_copper_ins","mcwroofs:nether_bricks_attic_roof","delightful:food/cooking/ender_nectar","merequester:requester","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","silentgear:crimson_steel_ingot","allthemodium:allthemodium_plate","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","botania:metamorphic_fungal_bricks_wall","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","mcwroofs:nether_bricks_steep_roof","travelersbackpack:end","croptopia:campfire_toast","reliquary:midas_touchstone","minecraft:cherry_stairs","mcwroofs:bricks_upper_steep_roof","create:crafting/appliances/slime_ball","mcwbiomesoplenty:willow_plank_four_window","reliquary:fortune_coin","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","create:crafting/kinetics/cart_assembler","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","mcwpaths:blackstone_running_bond_slab","minecraft:birch_boat","cfm:post_box","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwwindows:brown_curtain","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwbiomesoplenty:empyreal_western_door","cfm:orange_cooler","minecolonies:mint_jelly","dyenamics:dye_wine_carpet","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","pneumaticcraft:wall_lamp_light_blue","computercraft:monitor_advanced","create:layered_calcite_from_stone_types_calcite_stonecutting","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwroofs:gray_terracotta_top_roof","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","create:crafting/kinetics/filter","modularrouters:puller_module_1","mcwbiomesoplenty:umbran_stable_door","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mysticalagriculture:inferium_seeds","mcwbiomesoplenty:willow_stable_head_door","trashcans:energy_trash_can","silentgear:blaze_gold_ingot_from_nugget","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","mcwfurnitures:stripped_cherry_modern_desk","mcwroofs:sandstone_upper_lower_roof","minecolonies:baked_salmon","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","croptopia:burrito","pneumaticcraft:security_upgrade","mcwbiomesoplenty:umbran_horse_fence","minecraft:cooked_salmon_from_campfire_cooking","integrateddynamics:crafting/squeezer","dyenamics:dye_icy_blue_carpet","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwbiomesoplenty:stripped_mahogany_covered_desk","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","mcwroofs:grass_attic_roof","reliquary:mob_charm_fragments/wither_skeleton","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","immersiveengineering:crafting/radiator","minecraft:white_stained_glass_pane_from_glass_pane","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","biomesoplenty:mahogany_chest_boat","handcrafted:cherry_nightstand","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","cfm:dye_lime_picket_gate","modularrouters:mimic_augment","alltheores:sapphire_from_hammer_crushing","sophisticatedstorage:basic_to_diamond_tier_upgrade","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","twigs:schist","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","minecraft:andesite_slab","minecraft:bamboo_chest_raft","dyenamics:bed/bubblegum_bed_frm_white_bed","create:crafting/kinetics/large_cogwheel","productivebees:stonecutter/willow_canvas_expansion_box","mcwtrpdoors:cherry_whispering_trapdoor","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","reliquary:destruction_catalyst","create:crafting/kinetics/metal_bracket","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","croptopia:oatmeal","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","mcwbiomesoplenty:empyreal_barn_glass_door","mcwbiomesoplenty:pine_highley_gate","handcrafted:cherry_chair","mcwfences:ornate_metal_fence","rftoolsbuilder:mover_control2","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","cfm:black_kitchen_drawer","immersiveengineering:crafting/drillhead_iron","allthemods:easy_sticks","mcwwindows:bricks_pane_window","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","create:crafting/kinetics/gantry_carriage","mcwroofs:base_upper_steep_roof","mcwbiomesoplenty:stripped_fir_log_four_window","ae2:network/blocks/cell_workbench","mcwdoors:cherry_whispering_door","create:crafting/logistics/powered_latch","solcarrot:food_book","botania:metamorphic_forest_bricks_stairs","utilitarian:utility/charcoal_from_campfire","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","sophisticatedstorage:stonecutter_upgrade","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","farmersdelight:cooking/pasta_with_meatballs","botania:floating_agricarnation","mcwfurnitures:cherry_cupboard_counter","create:crafting/materials/andesite_alloy_from_block","minecraft:jungle_fence_gate","create:crafting/kinetics/water_wheel","mcwwindows:birch_louvered_shutter","mcwbiomesoplenty:maple_stable_door","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","travelersbackpack:dye_yellow_sleeping_bag","mcwpaths:blackstone_crystal_floor_path","cfm:jungle_cabinet","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:base_roof_block","cfm:oak_blinds","mcwbiomesoplenty:palm_window","twigs:blackstone_column","botania:floating_spectrolus","mcwbiomesoplenty:mahogany_beach_trapdoor","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","littlecontraptions:barge_assembler","dyenamics:navy_candle","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","minecraft:polished_granite_slab_from_polished_granite_stonecutting","mcwfences:deepslate_pillar_wall","cfm:jungle_chair","mcwlights:birch_tiki_torch","matc:imperium_essence","create:crafting/kinetics/sticker","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","mcwpaths:brick_square_paving","minecraft:red_candle","mcwbiomesoplenty:stripped_mahogany_end_table","mcwbiomesoplenty:hellbark_four_window","sophisticatedstorage:backpack_stack_upgrade_tier_2_from_storage_stack_upgrade_tier_3","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","fluxnetworks:fluxpoint","alltheores:platinum_rod","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","dyenamics:bed/fluorescent_bed_frm_white_bed","allthearcanistgear:vibranium_robes_smithing","mcwbridges:sandstone_bridge_pier","biomesoplenty:rose_quartz_block","botania:ender_eye_block","minecraft:decorated_pot_simple","mcwbiomesoplenty:stripped_dead_pane_window","ae2:shaped/walls/fluix_block","mcwroofs:magenta_terracotta_steep_roof","biomesoplenty:mahogany_stairs","minecraft:oak_pressure_plate","aether:netherite_leggings_repairing","bigreactors:turbine/basic/activefluidport_forge","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","cfm:stripped_jungle_cabinet","mcwbiomesoplenty:redwood_tropical_door","reliquary:mob_charm_fragments/creeper","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","minecolonies:mint_tea","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","twilightforest:transformation_chest_boat","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","dyenamics:rose_wool","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","mcwbiomesoplenty:mahogany_bookshelf_drawer","minecraft:red_nether_bricks","mcwdoors:acacia_western_door","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","botania:metamorphic_swamp_bricks_wall","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","modularrouters:fluid_module","mcwlights:purple_lamp","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","mcwroofs:purple_terracotta_roof","mcwfurnitures:stripped_jungle_chair","minecraft:moss_carpet","mcwroofs:jungle_upper_steep_roof","rftoolsbase:machine_frame","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","railcraft:brass_gear","botania:floating_rannuncarpus_chibi","pylons:infusion_pylon","create:crafting/kinetics/linear_chassis","forbidden_arcanus:diamond_blacksmith_gavel","domum_ornamentum:light_blue_brick_extra","mcwpaths:stone_windmill_weave_slab","mcwroofs:thatch_top_roof","supplementaries:cog_block","pneumaticcraft:armor_upgrade","enderio:dark_steel_sword","minecraft:lime_concrete_powder","minecraft:leather_leggings","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","mcwbiomesoplenty:magic_tropical_door","croptopia:fruit_salad","securitycraft:block_change_detector","minecraft:cherry_pressure_plate","minecraft:netherite_scrap_from_blasting","additionallanterns:amethyst_chain","immersiveengineering:crafting/minecart_woodencrate","mcwbridges:bridge_torch","mcwbridges:cherry_bridge_pier","domum_ornamentum:magenta_brick_extra","undergarden:wigglewood_boat","minecraft:book","mcwfurnitures:oak_drawer_counter","handcrafted:blue_sheet","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","additionallanterns:quartz_chain","travelersbackpack:standard_smithing","silentgear:prospector_hammer_template","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","connectedglass:scratched_glass_cyan_pane2","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","immersiveengineering:crafting/strip_lv","pneumaticcraft:pressure_chamber_glass_x4","pneumaticcraft:pressure_chamber_glass_x1","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","silentgear:axe_template","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","delightful:knives/allthemodium_knife","domum_ornamentum:sand_bricks","ae2:network/cables/dense_covered_cyan","mcwbiomesoplenty:palm_beach_door","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","pneumaticcraft:heat_pipe","minecraft:cracked_polished_blackstone_bricks","matc:crystals/tertium","pneumaticcraft:wall_lamp_inverted_purple","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","immersiveengineering:crafting/thermoelectric_generator","mcwbiomesoplenty:magic_bamboo_door","ae2:misc/tiny_tnt","ae2:decorative/quartz_glass","minecraft:red_terracotta","mcwdoors:jungle_japanese_door","immersiveengineering:crafting/stairs_treated_wood_horizontal","mcwbiomesoplenty:mahogany_ranch_trapdoor","silentgear:boots_template","mcwroofs:white_roof_block","mcwbiomesoplenty:mahogany_stable_door","mob_grinding_utils:recipe_tank_sink","securitycraft:block_pocket_manager","create:crafting/kinetics/encased_chain_drive","mcwlights:soul_cherry_tiki_torch","immersiveengineering:crafting/treated_wallmount","securitycraft:reinforced_mangrove_fence","torchmaster:megatorch","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","mcwlights:red_lamp","bloodmagic:blood_altar","botania:flower_bag","botania:metamorphic_mesa_stone_slab","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:cherry_striped_chair","aether:stone_sword_repairing","botania:chiseled_metamorphic_fungal_bricks","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwlights:light_blue_lamp","mcwbiomesoplenty:fir_window2","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwbiomesoplenty:fir_plank_window2","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","pneumaticcraft:block_tracker_upgrade","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","create:crafting/kinetics/hose_pulley","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","biomesoplenty:mahogany_wood","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","littlelogistics:tee_junction_rail","functionalstorage:framed_1","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:bowl","mcwwindows:ender_brick_arrow_slit","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwpaths:blackstone_diamond_paving","ad_astra:lime_industrial_lamp","minecraft:purple_concrete_powder","mcwfurnitures:stripped_cherry_modern_wardrobe","mcwfences:bastion_metal_fence","modularrouters:fluid_upgrade","mcwbiomesoplenty:mahogany_wardrobe","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:jungle_counter","minecraft:red_carpet","cfm:light_blue_kitchen_sink","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","waystones:warp_stone","silentgear:excavator_template","mcwfurnitures:cherry_glass_table","mcwfurnitures:stripped_cherry_desk","supplementaries:candle_holders/candle_holder_light_gray","mcwbiomesoplenty:jacaranda_japanese2_door","reliquary:mob_charm_fragments/zombified_piglin","create:crafting/materials/red_sand_paper","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:blue_terracotta_steep_roof","alltheores:lead_dust_from_hammer_crushing","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","allthearcanistgear:vibranium_hat_smithing","mcwroofs:oak_planks_attic_roof","bigreactors:crafting/graphite_component_to_storage","dyenamics:amber_terracotta","sophisticatedstorage:basic_to_copper_tier_upgrade","create:crafting/kinetics/white_sail","twigs:smooth_basalt_bricks","aether:white_cape","botania:metamorphic_fungal_bricks_slab","ae2:tools/misctools_entropy_manipulator","mcwbiomesoplenty:mahogany_cupboard_counter","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","minecraft:daylight_detector","mcwfences:panelled_metal_fence_gate","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","allthemodium:ancient_stone_wall","aquaculture:planks_from_driftwood","mcwfurnitures:oak_drawer","mcwfurnitures:cherry_coffee_table","create:crafting/kinetics/mechanical_pump","mcwtrpdoors:cherry_four_panel_trapdoor","simplylight:lamp_post","minecraft:hopper","mcwroofs:base_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","create:crafting/logistics/powered_toggle_latch","silentgear:machete_template","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","corail_woodcutter:mangrove_woodcutter","mcwpaths:brick_dumble_paving","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","botania:metamorphic_mesa_bricks_wall","minecraft:iron_helmet","botania:red_string_alt","mcwbiomesoplenty:mahogany_barn_glass_door","create:crafting/kinetics/cyan_seat","mcwroofs:lime_concrete_attic_roof","allthemodium:allthemodium_sword","minecraft:cartography_table","railcraft:manual_rolling_machine","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","mcwbiomesoplenty:umbran_pane_window","pneumaticcraft:compressed_brick_tile","ae2:network/blocks/energy_vibration_chamber","ae2:tools/fluix_shovel","reliquary:magicbane","pneumaticcraft:wall_lamp_inverted_magenta","minecraft:cooked_porkchop_from_smoking","mcwfurnitures:stripped_cherry_double_drawer_counter","ae2:block_cutter/stairs/sky_stone_stairs","reliquary:uncrafting/gold_nugget","additionallanterns:amethyst_lantern","mcwwindows:green_curtain","mcwwindows:stone_window","mcwroofs:gray_steep_roof","mcwfurnitures:cherry_stool_chair","rftoolspower:endergenic","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","croptopia:pumpkin_bars","productivebees:stonecutter/yucca_canvas_hive","mcwtrpdoors:jungle_classic_trapdoor","dankstorage:dank_6","dankstorage:dank_7","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","dankstorage:dank_3","dankstorage:dank_4","mcwroofs:yellow_terracotta_upper_lower_roof","dankstorage:dank_5","mcwpaths:cobbled_deepslate_flagstone_path","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","pneumaticcraft:speed_upgrade_from_glycerol","allthemods:constructionwand/iron_wand","mcwfurnitures:jungle_chair","computercraft:turtle_normal/minecraft/diamond_pickaxe","minecraft:heavy_weighted_pressure_plate","rftoolsbuilder:shape_card_pump_dirt","cfm:stripped_oak_crate","immersiveengineering:crafting/wirecutter","mcwdoors:oak_bark_glass_door","minecraft:polished_blackstone_stairs","mcwbiomesoplenty:magic_mystic_door","handcrafted:cherry_side_table","minecraft:netherite_pickaxe_smithing","travelersbackpack:sandstone","farmersdelight:cooking/fried_rice","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","enderio:resetting_lever_ten","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","mcwfurnitures:stripped_cherry_bookshelf_cupboard","mcwdoors:cherry_stable_head_door","minecraft:polished_deepslate","connectedglass:borderless_glass_yellow_pane2","simplylight:illuminant_gray_block_on_toggle","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","travelersbackpack:netherite","mcwwindows:quartz_pane_window","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:cooked_porkchop_from_campfire_cooking","minecraft:cyan_stained_glass_pane_from_glass_pane","create:cut_calcite_wall_from_stone_types_calcite_stonecutting","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","supplementaries:stonecutting/blackstone_tile","silentgear:fletching_template","minecraft:dye_red_carpet","mcwroofs:light_blue_terracotta_attic_roof","croptopia:tofu","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","botania:chiseled_metamorphic_mountain_bricks","additionallanterns:stone_bricks_chain","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwfences:spruce_wired_fence","handcrafted:andesite_corner_trim","mcwroofs:magenta_terracotta_top_roof","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","mcwbiomesoplenty:stripped_mahogany_double_wardrobe","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","securitycraft:claymore","railcraft:golden_ticket","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwbiomesoplenty:redwood_glass_door","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","mcwroofs:cherry_steep_roof","domum_ornamentum:sand_stone_bricks","mcwfurnitures:stripped_cherry_end_table","mcwroofs:orange_concrete_upper_lower_roof","productivebees:stonecutter/hellbark_canvas_hive","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","botania:metamorphic_plains_bricks_wall","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","ae2:network/cells/view_cell","mcwbiomesoplenty:maple_beach_door","enderio:resetting_lever_ten_from_inv","mcwbiomesoplenty:magic_four_panel_door","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","minecraft:cookie","aquaculture:spruce_fish_mount","mcwroofs:cherry_upper_lower_roof","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","minecraft:shears","dyenamics:lavender_terracotta","mcwroofs:jungle_planks_top_roof","ae2:tools/certus_quartz_hoe","minecraft:yellow_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:redwood_pane_window","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwbiomesoplenty:palm_barn_door","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","silentgear:binding_template","mcwbiomesoplenty:palm_mystic_door","deeperdarker:echo_chest_boat","eccentrictome:tome","securitycraft:reinforced_warped_fence","modularrouters:filter_round_robin_augment","alltheores:osmium_dust_from_hammer_crushing","computercraft:wireless_modem_normal","aether:aether_saddle","create:crafting/kinetics/rope_pulley","create:crafting/kinetics/cuckoo_clock","mcwbiomesoplenty:redwood_hedge","ae2:network/cables/covered_magenta","rftoolsstorage:storage_module0","minecraft:kjs/bigreactors_basic_reactorcasing","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","mcwfurnitures:stripped_jungle_cupboard_counter","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","mcwroofs:cherry_planks_upper_steep_roof","supplementaries:turn_table","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","silentgear:crimson_steel_dust_blasting","mcwdoors:acacia_whispering_door","twilightforest:sorting_boat","mcwbiomesoplenty:jacaranda_cottage_door","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mekanism:cardboard_box","ae2:network/cables/covered_gray","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","computercraft:turtle_normal/minecraft/diamond_sword","mcwwindows:stone_four_window","immersiveengineering:crafting/craftingtable","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","reliquary:lantern_of_paranoia","mysticalagriculture:essence/integrateddynamics/menril_log","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","mcwfurnitures:cherry_drawer_counter","mcwwindows:gray_curtain","minecraft:brewing_stand","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","supplementaries:bamboo_spikes","evilcraft:crafting/dark_spike","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:andesite_crystal_floor_path","create:crafting/kinetics/orange_seat","rftoolsutility:syringe","minecraft:diamond_block","mcwroofs:deepslate_roof","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","bigreactors:blasting/graphite_from_coal","minecraft:pumpkin_seeds","mcwwindows:dark_prismarine_four_window","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","minecraft:cooked_mutton","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","computercraft:wireless_modem_advanced","minecraft:lapis_block","aether:skyroot_chest","deepresonance:filter_material","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","dankstorage:3_to_4","utilitarian:snad/snad","rftoolsstorage:modular_storage","botania:metamorphic_mountain_stone_wall","croptopia:shaped_bacon","handcrafted:bricks_corner_trim","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","mcwroofs:pink_terracotta_attic_roof","dyenamics:spring_green_dye","mcwroofs:light_gray_attic_roof","minecraft:oak_boat","rftoolsbuilder:shape_card_pump","mcwfurnitures:stripped_jungle_covered_desk","cfm:black_picket_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwbiomesoplenty:pine_paper_door","silentgear:knife_template","ae2:network/cables/smart_green","minecraft:jungle_fence","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","twilightforest:transformation_boat","domum_ornamentum:green_cactus_extra","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","domum_ornamentum:green_cobblestone_extra","pneumaticcraft:air_canister","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","mcwbridges:cherry_rail_bridge","littlelogistics:automatic_tee_junction_rail","mcwbridges:brick_bridge_pier","silentgear:crimson_iron_ingot_from_block","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","silentgear:crimson_iron_nugget","mcwfences:end_brick_pillar_wall","dyenamics:ultramarine_dye","dyenamics:honey_dye","mcwwindows:prismarine_brick_arrow_slit","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:purple_dye","mcwfurnitures:jungle_modern_chair","create:cut_calcite_brick_wall_from_stone_types_calcite_stonecutting","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","handcrafted:phantom_trophy","minecraft:quartz_slab","reliquary:mob_charm_fragments/magma_cube","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","mcwfurnitures:stripped_jungle_double_drawer","mcwbiomesoplenty:palm_bamboo_door","farmersdelight:bread_from_smelting","ae2:network/cables/smart_black","minecraft:kjs/ender_pearl","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","mcwbiomesoplenty:stripped_mahogany_drawer_counter","botania:apothecary_mesa","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","silentgear:mattock_template","create:small_dripstone_brick_stairs_from_stone_types_dripstone_stonecutting","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:dark_oak_mail_box","botania:metamorphic_desert_bricks_stairs","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/windmill_sail","farmersdelight:rice_bag","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","ae2:block_cutter/stairs/fluix_stairs","farmersdelight:chicken_sandwich","mcwbiomesoplenty:palm_swamp_door","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","handcrafted:blue_bowl","mcwfurnitures:stripped_oak_double_wardrobe","minecraft:polished_blackstone_brick_slab","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","silentgear:crimson_steel_ingot_from_nugget","dyenamics:dye_amber_carpet","immersiveengineering:crafting/workbench","ae2:network/cables/smart_lime","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","handcrafted:kitchen_hood","supplementaries:fodder","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","mcwbiomesoplenty:fir_swamp_door","dyenamics:banner/aquamarine_banner","mcwfurnitures:stripped_oak_glass_table","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","utilitarian:utility/jungle_logs_to_doors","create:polished_cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","twigs:cut_amethyst_from_amethyst_block_stonecutting","reliquary:ender_staff","mcwwindows:diorite_louvered_shutter","mcwfurnitures:oak_end_table","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","mcwbiomesoplenty:mahogany_roof","silentgear:mod_kit","bigreactors:reprocessor/fluidinjector","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwfurnitures:jungle_modern_desk","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","ae2:materials/advancedcard","minecraft:cooked_porkchop","silentgear:blaze_gold_nugget","mcwlights:gray_lamp","dyenamics:maroon_stained_glass","botania:metamorphic_swamp_cobblestone_slab","minecraft:oak_wood","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","pneumaticcraft:pressure_chamber_valve_x1","mysticalagriculture:prosperity_seed_base","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwbiomesoplenty:redwood_bamboo_door","mcwroofs:cherry_planks_attic_roof","mcwpaths:sandstone_clover_paving","aether:leather_helmet_repairing","aquaculture:sushi","mcwdoors:metal_reinforced_door","silentgear:crimson_iron_dust_smelting","mcwbiomesoplenty:magic_japanese2_door","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","ae2:tools/fluix_upgrade_smithing_template","mcwdoors:cherry_tropical_door","mcwbiomesoplenty:jacaranda_pyramid_gate","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","mcwlights:jungle_ceiling_fan_light","botania:metamorphic_plains_bricks","railcraft:tunnel_bore","botania:metamorphic_mesa_cobblestone_slab","mcwpaths:sandstone_flagstone_stairs","bigreactors:turbine/basic/activetap_fe","ae2:network/cables/covered_light_gray","mcwroofs:gutter_base_lime","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","modularrouters:fast_pickup_augment","handcrafted:jungle_table","minecraft:andesite_wall_from_andesite_stonecutting","mcwfences:crimson_stockade_fence","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","deeperdarker:bloom_chest_boat","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","mcwbiomesoplenty:empyreal_barn_door","minecraft:powered_rail","sgjourney:sandstone_hieroglyphs","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","additionallanterns:stone_lantern","botania:metamorphic_mountain_bricks_slab","supplementaries:flags/flag_red","cfm:dye_blue_picket_fence","mcwbridges:end_stone_bricks_bridge","minecraft:dye_white_bed","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:light_gray_terracotta_steep_roof","mcwbiomesoplenty:hellbark_nether_door","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","mcwbiomesoplenty:empyreal_tropical_door","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","mcwbiomesoplenty:empyreal_paper_door","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","minecraft:gray_terracotta","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","minecraft:netherite_boots_smithing","mob_grinding_utils:recipe_tintedglass","mcwbiomesoplenty:mahogany_triple_drawer","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","dyenamics:peach_terracotta","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","minecraft:terracotta","delightful:knives/silver_knife","mcwwindows:acacia_blinds","aether:leather_chestplate_repairing","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwwindows:dark_oak_window2","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","connectedglass:scratched_glass_yellow2","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","immersiveengineering:crafting/minecart_woodenbarrel","blue_skies:lunar_bookshelf","mcwroofs:cyan_terracotta_top_roof","dankstorage:5_to_6","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","productivebees:stonecutter/redwood_canvas_hive","minecraft:beacon","minecraft:tnt","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:dye_yellow_bed","minecraft:flint_and_steel","mcwdoors:cherry_nether_door","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwfurnitures:stripped_jungle_double_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","handcrafted:dripstone_corner_trim","alltheores:invar_dust_from_alloy_blending","xnet:advanced_connector_blue_dye","modularrouters:stack_augment","mcwdoors:cherry_barn_door","mcwbiomesoplenty:umbran_bamboo_door","mcwbiomesoplenty:empyreal_plank_pane_window","botania:purple_shiny_flower","mcwdoors:oak_bamboo_door","travelersbackpack:hay","xnet:advanced_connector_red_dye","mcwdoors:cherry_modern_door","mcwroofs:brown_striped_awning","mcwbiomesoplenty:mahogany_whispering_trapdoor","minecraft:brown_concrete_powder","botania:metamorphic_plains_stone_wall","mcwbiomesoplenty:stripped_mahogany_modern_desk","securitycraft:reinforced_lime_stained_glass_pane_from_dye","rftoolsbase:tablet","create:crafting/kinetics/turntable","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","simplylight:bulb","travelersbackpack:cactus","mcwwindows:granite_four_window","cfm:door_mat","mcwroofs:light_blue_concrete_steep_roof","supplementaries:stone_tile","handcrafted:terracotta_plate","supplementaries:item_shelf","bigreactors:reactor/basic/chargingfe","mcwbiomesoplenty:willow_barn_glass_door","mcwroofs:light_blue_terracotta_roof","allthemodium:vibranium_gear","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","silentgear:coating_smithing_template","minecraft:grindstone","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","supplementaries:timber_brace","botania:metamorphic_desert_stone_stairs","handcrafted:jungle_nightstand","domum_ornamentum:gray_brick_extra","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","twilightforest:wood/cherry_banister","supplementaries:bed_from_feather_block","mcwbiomesoplenty:stripped_mahogany_striped_chair","bigreactors:reactor/reinforced/activetap_fe","bigreactors:reactor/basic/controlrod","cfm:red_kitchen_counter","mcwlights:copper_chain","minecraft:furnace_minecart","allthemods:rechiseled/chisel","create:crafting/kinetics/cogwheel","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","cfm:orange_kitchen_drawer","enderio:cold_fire_igniter","ae2:network/cables/covered_green","minecraft:netherite_ingot_from_netherite_block","create:crafting/appliances/netherite_diving_boots_from_netherite","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","twigs:smooth_basalt_bricks_stonecutting","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwlights:pink_paper_lamp","ad_astra:white_industrial_lamp","immersiveengineering:crafting/wire_aluminum","dyenamics:bubblegum_terracotta","utilitix:hand_bell","rftoolscontrol:workbench","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","bigreactors:turbine/basic/bearing","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","mcwroofs:cyan_striped_awning","dyenamics:dye_mint_carpet","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","aether:wooden_shovel_repairing","functionalstorage:cherry_1","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","connectedglass:clear_glass_black_pane2","mcwfences:railing_stone_brick_wall","botania:pool_minecart","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","sfm:cable","mcwbiomesoplenty:palm_japanese_door","minecraft:hay_block","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:jacaranda_barn_glass_door","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","create:jungle_window","ae2:network/parts/monitors_storage","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_planks_attic_roof","mcwroofs:blue_concrete_top_roof","evilcraft:smelting/hardened_blood_shard","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwbiomesoplenty:mahogany_paper_trapdoor","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","supplementaries:flags/flag_orange","botania:metamorphic_plains_bricks_stairs","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","modularrouters:augment_core","ae2:network/cables/covered_black","minecraft:smooth_sandstone_stairs","mcwbiomesoplenty:stripped_mahogany_modern_chair","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","pneumaticcraft:liquid_compressor","connectedglass:clear_glass_red_pane2","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","mcwroofs:black_concrete_top_roof","enderio:basic_item_filter","ae2:network/blocks/inscribers","mcwwindows:granite_louvered_shutter","ad_astra:red_flag","ad_astra:small_red_industrial_lamp","securitycraft:sentry","minecraft:netherite_block","alltheores:uranium_rod","cfm:cyan_kitchen_counter","mcwwindows:blue_mosaic_glass_pane","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","connectedglass:tinted_borderless_glass_yellow2","create:cut_dripstone_brick_stairs_from_stone_types_dripstone_stonecutting","mcwbiomesoplenty:mahogany_desk","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","mcwtrpdoors:cherry_beach_trapdoor","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","reliquary:glowing_bread","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwtrpdoors:cherry_classic_trapdoor","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","botania:metamorphic_mesa_bricks","quark:building/crafting/gold_bars","mcwroofs:gray_concrete_upper_lower_roof","utilitix:piston_controller_rail","handcrafted:yellow_crockery_combo","create:crafting/curiosities/peculiar_bell","mcwbiomesoplenty:umbran_mystic_door","expatternprovider:assembler_matrix_frame","securitycraft:reinforced_granite","alltheores:aluminum_dust_from_hammer_crushing","allthemodium:vibranium_nugget_from_ingot","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","handcrafted:cherry_dining_bench","mcwwindows:oak_plank_window2","connectedglass:scratched_glass_red_pane2","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:network/cables/glass_light_gray","botania:metamorphic_forest_bricks_wall","mcwbiomesoplenty:mahogany_covered_desk","immersiveengineering:crafting/coke_to_coal_coke","immersiveengineering:crafting/chute_iron","modularrouters:range_up_from_down","twigs:mixed_bricks_stonecutting","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","botania:metamorphic_fungal_stone_stairs","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","connectedglass:scratched_glass_blue2","minecraft:blue_bed","domum_ornamentum:pink_floating_carpet","botania:metamorphic_mountain_bricks_stairs","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","create:crafting/kinetics/mechanical_harvester","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","minecraft:quartz_stairs","immersiveengineering:crafting/waterwheel_segment","minecraft:red_stained_glass","minecraft:light_gray_stained_glass_pane_from_glass_pane","silentgear:leather_from_scraps","mcwpaths:cobbled_deepslate_crystal_floor_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","mcwpaths:cobbled_deepslate_crystal_floor","farmersdelight:beef_patty","mcwbiomesoplenty:magic_stable_door","utilitix:reinforced_crossing_rail","connectedglass:borderless_glass_blue_pane2","mcwbiomesoplenty:mahogany_modern_desk","mcwpaths:blackstone_crystal_floor","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","twigs:smooth_basalt_brick_slab_from_smooth_basalt_stonecutting","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","delightful:food/marshmallow_stick","minecraft:lime_terracotta","mcwbiomesoplenty:mahogany_drawer","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","bigreactors:reactor/basic/casing_recycle","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","minecraft:gray_stained_glass_pane_from_glass_pane","minecraft:stone_slab","mcwbiomesoplenty:magic_classic_door","productivebees:stonecutter/comb_canvas_hive","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","silentgear:blaze_gold_ingot_from_block","mcwbiomesoplenty:maple_four_panel_door","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","mcwfurnitures:jungle_bookshelf_cupboard","cfm:jungle_desk_cabinet","travelersbackpack:backpack_tank","minecraft:jungle_stairs","mcwbridges:glass_bridge","ae2:materials/cardenergy","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","enderio:resetting_lever_ten_inv_from_base","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","dyenamics:bed/mint_bed_frm_white_bed","croptopia:campfire_molasses","mcwbiomesoplenty:pine_four_panel_door","ae2:network/upgrade_wireless_crafting_terminal","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","utilitix:weak_redstone_torch","mcwbiomesoplenty:mahogany_planks_steep_roof","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","botania:metamorphic_forest_stone_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:jacaranda_glass_door","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","minecraft:cooked_salmon","mcwfences:expanded_mesh_metal_fence","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","simplylight:illuminant_purple_block_on_toggle","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","modularrouters:range_up_augment","mcwwindows:spruce_shutter","trashcans:ultimate_trash_can","twilightforest:mining_chest_boat","croptopia:roasted_pumpkin_seeds","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","create:crafting/kinetics/yellow_seat","mcwpaths:andesite_windmill_weave_path","mcwbiomesoplenty:palm_glass_door","mcwfurnitures:stripped_cherry_stool_chair","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwbiomesoplenty:mahogany_upper_steep_roof","mcwroofs:green_terracotta_steep_roof","allthecompressed:compress/hay_block_1x","mcwdoors:garage_silver_door","comforts:hammock_white","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwfurnitures:stripped_oak_cupboard_counter","immersiveengineering:crafting/wirecoil_structure_steel","botania:floating_hydroangeas","mcwwindows:dark_oak_curtain_rod","mcwroofs:gray_concrete_lower_roof","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","create:crafting/kinetics/portable_fluid_interface","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","handcrafted:red_sheet","sophisticatedstorage:paintbrush","biomesoplenty:willow_boat","mcwwindows:stone_brick_arrow_slit","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","mcwbiomesoplenty:umbran_waffle_door","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","croptopia:ham_sandwich","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","mcwbiomesoplenty:redwood_barn_door","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","silentgear:dagger_template","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwbiomesoplenty:palm_modern_door","rftoolsutility:counter_module","handcrafted:cherry_bench","minecraft:amethyst_block","cfm:stripped_jungle_crate","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwroofs:black_roof_block","create:crafting/kinetics/wrench","immersiveengineering:crafting/wooden_grip","connectedglass:clear_glass_cyan_pane2","mcwlights:warped_ceiling_fan_light","botania:prism","mcwwindows:window_centre_bar_base","modularrouters:sender_module_2_x4","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","handcrafted:oak_corner_trim","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","computercraft:wired_modem","ae2:tools/fluix_sword","utilitarian:utility/cherry_logs_to_trapdoors","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","rftoolspower:pearl_injector","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","twigs:cobblestone_bricks_stonecutting","allthemodium:allthemodium_shovel","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","pylons:harvester_pylon","mcwfurnitures:stripped_cherry_counter","minecraft:wheat","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","mcwbiomesoplenty:mahogany_four_panel_trapdoor","ae2:network/cables/glass_fluix_clean","minecraft:bow","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aquaculture:brown_mushroom_from_fish","railways:jukeboxcart","expatternprovider:silicon_block_disassembler","mcwwindows:light_gray_mosaic_glass_pane","minecraft:andesite_stairs","handcrafted:bear_trophy","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","botania:metamorphic_desert_cobblestone_stairs","twigs:polished_calcite_stonecutting","ae2:network/parts/storage_bus","mcwwindows:jungle_plank_parapet","botania:spark","minecraft:dark_oak_boat","dyenamics:maroon_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:palm_wired_fence","dyenamics:bed/aquamarine_bed_frm_white_bed","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","create:crafting/kinetics/clutch","botania:ghost_rail","minecraft:firework_rocket_simple","create:crafting/kinetics/encased_fan","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","silentgear:crimson_steel_block","dyenamics:bed/rose_bed_frm_white_bed","nethersdelight:soul_compost_from_warped_roots","biomesoplenty:mahogany_planks","mcwbiomesoplenty:redwood_mystic_door","enderio:enderios","mcwbridges:balustrade_nether_bricks_bridge","silentgear:bow_template","supplementaries:pedestal","travelersbackpack:blaze","mininggadgets:mininggadget","cfm:lime_trampoline","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","twigs:smooth_basalt_brick_stairs_from_smooth_basalt_stonecutting","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","alltheores:ruby_dust_from_hammer_crushing","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","forbidden_arcanus:wooden_blacksmith_gavel","mcwdoors:metal_windowed_door","twigs:lamp","minecraft:stone","minecraft:blackstone_stairs","travelersbackpack:red_sleeping_bag","comforts:sleeping_bag_blue","cfm:stripped_oak_kitchen_sink_dark","minecraft:dye_cyan_bed","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","crafting_on_a_stick:crafting_table","allthemodium:ancient_stone_bricks","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","dyenamics:dye_bubblegum_carpet","botania:redstone_root","create:crafting/kinetics/display_board","mcwpaths:sandstone_diamond_paving","connectedglass:clear_glass_black2","dyenamics:dye_aquamarine_carpet","botania:metamorphic_fungal_bricks_stairs","mcwbiomesoplenty:mahogany_double_wardrobe","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwpaths:sandstone_windmill_weave_slab","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","immersiveengineering:crafting/ersatz_leather","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","minecraft:birch_chest_boat","cfm:stripped_oak_bedside_cabinet","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","littlelogistics:tug","create:crafting/kinetics/mechanical_press","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","twigs:compacted_dripstone","mcwpaths:brick_strewn_rocky_path","enderio:resetting_lever_three_hundred","immersiveengineering:crafting/treated_wood_horizontal_from_slab","silentgear:shears_template","mcwlights:cross_lantern","mcwlights:brown_lamp","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","minecraft:green_candle","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","mcwfurnitures:cherry_bookshelf_drawer","additionallanterns:diamond_chain","handcrafted:yellow_bowl","supplementaries:stonecutting/stone_tile","ae2:network/cables/covered_pink","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","enderio:dark_steel_trapdoor","rftoolsbuilder:shape_card_quarry_silk","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","minecraft:hopper_minecart","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","mcwwindows:black_curtain","productivebees:stonecutter/wisteria_canvas_expansion_box","chemlib:osmium_ingot_from_blasting_osmium_dust","mcwpaths:sandstone_crystal_floor_slab","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwbiomesoplenty:mahogany_double_drawer","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","enderio:resetting_lever_five_inv","utilitix:armed_stand","botania:lexicon","handcrafted:yellow_cup","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwbiomesoplenty:redwood_beach_door","ae2:network/cables/smart_blue","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","botania:turntable","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","domum_ornamentum:black_brick_extra","alltheores:zinc_dust_from_hammer_crushing","railcraft:steel_chestplate","supplementaries:daub","minecraft:netherite_scrap","mcwfurnitures:stripped_jungle_end_table","create:crafting/kinetics/wooden_bracket","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","minecraft:cut_sandstone","alltheores:tin_dust_from_hammer_ingot_crushing","ae2:decorative/quartz_block","mcwbiomesoplenty:magic_cottage_door","mcwpaths:stone_flagstone","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:nether_brick_slab","mcwbiomesoplenty:hellbark_highley_gate","immersiveengineering:crafting/treated_scaffold","mcwpaths:cobbled_deepslate_square_paving","railcraft:track_relayer","immersiveengineering:crafting/coil_lv","biomesoplenty:mahogany_pressure_plate","mcwlights:brown_paper_lamp","connectedglass:borderless_glass_white_pane2","minecraft:campfire","dyenamics:peach_stained_glass_pane_from_glass_pane","cfm:white_kitchen_sink","mcwbiomesoplenty:stripped_mahogany_lower_bookshelf_drawer","cfm:dye_yellow_picket_fence","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","handcrafted:cherry_couch","farmersdelight:cooking/chicken_soup","blue_skies:bluebright_bookshelf","mcwbiomesoplenty:mahogany_beach_door","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:1b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:2450,warning_level:0}}