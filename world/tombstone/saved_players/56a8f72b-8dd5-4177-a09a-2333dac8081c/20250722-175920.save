{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"}],Air:300s,Attributes:[{Base:0.08d,Name:"forge:entity_gravity"},{Base:4.5d,Name:"forge:block_reach"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:20.0d,Modifiers:[{Amount:10.0d,Name:"artifacts:crystal_heart_health_bonus",Operation:0,UUID:[I;-**********,-**********,-**********,-**********]},{Amount:4.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-232031173,-**********,-**********,-**********],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:crystal_heart"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"ae2:wireless_crafting_terminal",tag:{accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},craftingGrid:[{Count:1b,Slot:0,id:"minecraft:iron_ingot"}],filter_type:"ALL",internalCurrentPower:1.1530313E7d,internalMaxPower:1.44E7d,sort_by:"AMOUNT",sort_direction:"DESCENDING",upgrades:[{Count:1b,Slot:0,id:"megacells:greater_energy_card"}],view_mode:"ALL"}}],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:1,id:"tempad:he_who_remains_tempad"}],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:golden_hook"},{Count:1b,Slot:1,id:"artifacts:night_vision_goggles"}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:superstitious_hat"}],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"bloodmagic:soulgemcommon",tag:{souls:327.2352682940438d}}],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:backpack",tag:{contentsUuid:[I;-1794215611,-1219935260,-1686061635,574728977],inventorySlots:27,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:1}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:146213},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:3},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:iron_nugget",ItemStack:{Count:9b,id:"minecraft:iron_nugget"}}],SelectedRecipe:"minecraft:iron_nugget"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:128,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:apple","caupona:water","minecraft:potato","minecraft:golden_apple","minecraft:cookie","minecraft:bread","minecraft:spider_eye","minecraft:rotten_flesh","minecraft:cooked_beef","minecraft:cooked_chicken","farmersdelight:hamburger","minecraft:baked_potato","minecraft:glow_berries"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:10s,knowledge:33,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:3b,PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:-29137058447294L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-65420946681793L,UID:[I;-*********,2095137253,-1604793219,*********]},{FromDim:"minecraft:overworld",FromPos:-223475740831582L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-29137058447294L,UID:[I;-1779136176,88753121,-1664711339,1729566716]},{FromDim:"minecraft:overworld",FromPos:96207268921409L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;-1816825503,1415073189,-1460587355,1528964705]},{FromDim:"minecraft:overworld",FromPos:-9070971117498L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;1927771186,1609122112,-1760616123,793099803]},{FromDim:"minecraft:overworld",FromPos:73667278061633L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;69002816,-350007459,-2012102045,1376441674]},{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:79989469896768L,UID:[I;-1956369806,-1908060819,-1127274326,-503998652]},{FromDim:"minecraft:overworld",FromPos:600333352705998L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;764759307,664617603,-1524366671,462904968]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","70e61f07-a350-437a-ae01-15e3ef9a1bab","ca28c95d-ca7a-44e2-823a-0af2e1199d4d","2968051a-1dc8-457d-97c2-8af1ed99da77","6c8881a8-dbdf-465b-bb3e-0c97f47d45a4","ef733010-706d-4fa0-bdf9-4eb7dcbd9b79"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-27,tb_last_ground_location_y:253,tb_last_ground_location_z:-52,tb_last_offhand_item:"minecraft:bread",twilightforest_banished:1b},"aae$downkey":0b,"aae$nokey":1b,"aae$upkey":0b,apoth_reforge_seed:0,"quark:trying_crawl":0b,simplylight_unlocked:2},Health:45.0f,HurtByTimestamp:144453,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"allthemodium:allthemodium_pickaxe"},{Count:1b,Slot:1b,id:"allthemodium:allthemodium_shovel"},{Count:1b,Slot:2b,id:"allthemodium:allthemodium_sword",tag:{Enchantments:[{}],display:{Name:'[{"text":"巨龙大师","italic":false,"color":"gold"}]'}}},{Count:1b,Slot:3b,id:"allthemodium:allthemodium_axe"},{Count:1b,Slot:4b,id:"rechiseled:chisel",tag:{stack:{Count:64b,id:"minecraft:glass"}}},{Count:64b,Slot:7b,id:"connectedglass:clear_glass"},{Count:64b,Slot:8b,id:"connectedglass:clear_glass"},{Count:64b,Slot:18b,id:"minecraft:glass"},{Count:64b,Slot:19b,id:"minecraft:glass"},{Count:64b,Slot:20b,id:"minecraft:glass"},{Count:64b,Slot:21b,id:"minecraft:glass"},{Count:64b,Slot:26b,id:"minecraft:glass"},{Count:64b,Slot:27b,id:"minecraft:glass"},{Count:64b,Slot:28b,id:"minecraft:glass"},{Count:64b,Slot:29b,id:"minecraft:glass"},{Count:64b,Slot:30b,id:"minecraft:glass"},{Count:64b,Slot:31b,id:"minecraft:glass"},{Count:1b,Slot:100b,id:"allthemodium:allthemodium_boots",tag:{Damage:0}},{Count:1b,Slot:101b,id:"allthemodium:allthemodium_leggings",tag:{Damage:0}},{Count:1b,Slot:102b,id:"mekanism:mekasuit_bodyarmor",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"3828007540"}],modules:{"mekanism:energy_unit":{amount:8,enabled:1b},"mekanism:gravitational_modulating_unit":{amount:1,enabled:1b,handleModeChange:1b,renderHUD:1b,speed_boost:1}}}}},{Count:1b,Slot:103b,id:"allthemodium:allthemodium_helmet",tag:{affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.84155154f,"apotheosis:armor/attribute/fortunate":0.998058f,"apotheosis:armor/attribute/ironforged":0.6378598f,"apotheosis:armor/attribute/stalwart":0.6440308f,"apotheosis:armor/attribute/steel_touched":0.11399037f,"apotheosis:armor/dmg_reduction/runed":0.8986178f,"apotheosis:armor/mob_effect/blinding":0.09419179f,"apotheosis:durable":0.72f,"apotheosis:socket":3.0f},name:'{"color":"rainbow","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/blessed"},"",{"translate":"affix.apotheosis:armor/dmg_reduction/runed.suffix"}]}',rarity:"ancient",uuids:[[I;-370265463,703221486,-1585586250,-1427299350]]}}}],Invulnerable:0b,LastDeathLocation:{dimension:"bloodmagic:dungeon",pos:[I;-928,113,951]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-15.804029640442035d,253.0d,-53.42276388667885d],Railways_DataVersion:2,Rotation:[84.26074f,45.14994f],Score:70822,SelectedItemSlot:4,SleepTimer:0s,SpawnAngle:-4.5078735f,SpawnDimension:"allthemodium:mining",SpawnForced:0b,SpawnX:-60,SpawnY:253,SpawnZ:-102,Spigot.ticksLived:146205,UUID:[I;1453913899,-1915403913,-1600511181,-624424932],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-4123168825091L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:59,XpP:0.26259607f,XpSeed:920307554,XpTotal:7315,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752649577102L,keepLevel:0b,lastKnownName:"xiaoMing132",lastPlayed:1753178360182L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:1.3070316f,foodLevel:16,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["botania:star_sword","botania:dreamwood_planks","mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","croptopia:mead","botania:alfheim_portal","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwdoors:bamboo_stable_door","pneumaticcraft:chunkloader_upgrade","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","allthecompressed:compress/calcite_1x","minecraft:bone_block","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","minecraft:melon","mcwfurnitures:warped_table","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","ad_astra:light_blue_flag","create:oak_window","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","mcwfurnitures:cherry_chair","create:crafting/kinetics/light_gray_seat","sophisticatedbackpacks:void_upgrade","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","buildinggadgets2:gadget_cut_paste","reliquary:mob_charm_fragments/cave_spider","mcwfurnitures:spruce_modern_desk","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","mcwfurnitures:spruce_drawer","alltheores:brass_plate","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","aether:iron_gloves_repairing","railcraft:controller_circuit","handcrafted:deepslate_corner_trim","minecraft:spruce_sign","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","aether:skyroot_fletching_table","create:crafting/kinetics/gearbox","botania:terrasteel_block","create:crafting/logistics/brass_tunnel","sophisticatedstorage:copper_to_iron_tier_upgrade","handcrafted:spruce_side_table","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","minecraft:light_blue_bed","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","comforts:sleeping_bag_to_blue","sliceanddice:slicer","minecraft:nether_brick_stairs","pneumaticcraft:crop_support","botania:elementium_sword","supplementaries:bubble_blower","naturalist:glow_goop","mcwtrpdoors:warped_barn_trapdoor","dyenamics:conifer_stained_glass_pane_from_glass_pane","supplementaries:crank","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","minecraft:magenta_dye_from_blue_red_white_dye","alltheores:iron_plate","cfm:stripped_birch_park_bench","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","productivebees:stonecutter/fir_canvas_expansion_box","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","minecraft:pink_terracotta","mcwdoors:cherry_bamboo_door","aiotbotania:livingrock_axe","mcwbiomesoplenty:mahogany_plank_pane_window","twigs:bamboo_thatch","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwtrpdoors:spruce_barred_trapdoor","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","supplementaries:candle_holders/candle_holder_white_dye","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","mcwfurnitures:warped_modern_wardrobe","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","productivebees:hives/advanced_cherry_canvas_hive","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","allthecompressed:compress/quartz_block_1x","railcraft:standard_rail_from_rail","twilightdelight:torchberry_pie","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","mcwtrpdoors:warped_cottage_trapdoor","cfm:oak_kitchen_counter","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","minecraft:golden_hoe","minecraft:fire_charge","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:pink_petal_block","pneumaticcraft:tag_workbench","alltheores:platinum_dust_from_hammer_crushing","cfm:stripped_spruce_bedside_cabinet","railcraft:any_detector","alchemistry:reactor_energy","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","pneumaticcraft:reinforced_chest_kit","sophisticatedstorage:controller","computercraft:printer","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","biomesoplenty:palm_chest_boat","mcwtrpdoors:bamboo_mystic_trapdoor","minecraft:stone_button","mcwfurnitures:stripped_cherry_cupboard_counter","mcwfurnitures:birch_desk","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","pneumaticcraft:logistics_frame_active_provider_self","minecraft:lodestone","occultism:crafting/magic_lamp_empty","handcrafted:birch_counter","mcwlights:black_lamp","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","mcwdoors:spruce_four_panel_door","mcwpaths:sandstone_running_bond_stairs","domum_ornamentum:blue_brick_extra","cfm:stripped_spruce_chair","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","mcwfurnitures:stripped_warped_bookshelf","aether:red_cape","minecraft:nether_brick_wall","mcwtrpdoors:cherry_glass_trapdoor","mcwfurnitures:stripped_warped_drawer_counter","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","mcwdoors:cherry_japanese_door","connectedglass:borderless_glass_red2","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","productivebees:expansion_boxes/expansion_box_acacia_canvas","dyenamics:lavender_dye","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwdoors:jungle_bark_glass_door","mcwtrpdoors:cherry_barred_trapdoor","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","botania:open_bucket","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","cfm:stripped_crimson_mail_box","tombstone:grave_plate","handcrafted:jungle_side_table","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","mcwtrpdoors:warped_tropical_trapdoor","mcwroofs:warped_attic_roof","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","ae2:network/parts/terminals","securitycraft:keypad_frame","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","littlelogistics:barrel_barge","botania:terrasteel_leggings","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","cfm:spruce_bedside_cabinet","botania:incense_stick","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","allthecompressed:compress/spirited_crystal_block_1x","pneumaticcraft:vortex_tube","supplementaries:candle_holders/candle_holder_red_dye","xnet:connector_routing","mcwwindows:warped_louvered_shutter","undergarden:wigglewood_chest_boat","blue_skies:glowing_nature_stone","handcrafted:birch_nightstand","minecraft:blue_carpet","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","aether:iron_shovel_repairing","travelersbackpack:cake","bloodmagic:ritual_diviner_1","bloodmagic:ritual_diviner_0","mcwroofs:green_terracotta_attic_roof","productivebees:stonecutter/palm_canvas_expansion_box","minecraft:birch_stairs","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","immersiveengineering:crafting/shield","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","mcwdoors:birch_stable_head_door","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","sophisticatedstorage:birch_barrel","immersiveengineering:crafting/blastbrick","mcwfurnitures:stripped_spruce_double_drawer","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","farmersdelight:milk_bottle","ae2:network/blocks/storage_chest","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:mangrove_chest_boat","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","handcrafted:blue_cushion","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","mcwroofs:light_blue_concrete_lower_roof","mcwfurnitures:jungle_drawer","utilitarian:utility/cherry_logs_to_stairs","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwfurnitures:birch_striped_chair","mcwroofs:thatch_roof","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","pneumaticcraft:advanced_liquid_compressor","connectedglass:clear_glass_light_blue2","cfm:mangrove_kitchen_sink_dark","croptopia:mashed_potatoes","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","mcwfences:blackstone_brick_railing_gate","mcwtrpdoors:bamboo_tropical_trapdoor","mcwroofs:cherry_attic_roof","sophisticatedbackpacks:filter_upgrade","pneumaticcraft:gun_ammo_freezing","aether:skyroot_barrel","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","megacells:cells/portable/portable_fluid_cell_4m","mcwfurnitures:stripped_cherry_table","minecraft:iron_ingot_from_nuggets","ae2:network/cables/dense_covered_purple","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwroofs:warped_upper_steep_roof","mcwpaths:cobbled_deepslate_flagstone_stairs","mcwdoors:birch_glass_door","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","twilightforest:material/blasted_ironwood_ingot","mcwfurnitures:cherry_counter","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","productivebees:hives/advanced_oak_canvas_hive","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","handcrafted:light_blue_sheet","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","additionallanterns:normal_lantern_pink","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","mcwfurnitures:oak_double_drawer_counter","cfm:blue_grill","mcwlights:golden_chandelier","minecraft:armor_stand","mcwpaths:stone_flagstone_stairs","chemlib:sodium_block_to_ingot","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","pneumaticcraft:compressed_bricks_from_tile_stonecutting","twigs:chiseled_bricks","enderio:stone_gear","cfm:pink_sofa","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","mcwroofs:nether_bricks_upper_lower_roof","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","cfm:birch_bedside_cabinet","mcwroofs:blue_terracotta_roof","xnet:netcable_yellow","mcwlights:tavern_wall_lantern","mcwfurnitures:jungle_wardrobe","croptopia:cheese","dyenamics:bed/rose_bed","mcwfurnitures:stripped_cherry_lower_bookshelf_drawer","cfm:red_sofa","minecraft:warped_hyphae","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","megacells:cells/portable/portable_fluid_cell_1m","cfm:stripped_warped_crate","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwroofs:spruce_top_roof","supplementaries:bomb","dyenamics:navy_stained_glass","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","additionallanterns:normal_lantern_orange","mcwfurnitures:stripped_birch_drawer","mysticalagriculture:inferium_farmland_till","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","create:cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","mcwfurnitures:stripped_oak_lower_triple_drawer","utilitarian:utility/oak_logs_to_boats","croptopia:potato_chips","minecraft:chiseled_stone_bricks_stone_from_stonecutting","pneumaticcraft:compressed_brick_pillar","mcwfurnitures:stripped_birch_double_drawer_counter","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","chemlib:sodium_ingot_from_blasting_sodium_dust","botania:red_string_fertilizer","minecraft:netherite_sword_smithing","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","create:crafting/kinetics/mechanical_crafter","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwfurnitures:stripped_warped_table","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","mcwfurnitures:cherry_double_wardrobe","handcrafted:spruce_counter","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","mcwfurnitures:stripped_cherry_bookshelf_drawer","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","twilightforest:charm_of_life_2","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","connectedglass:borderless_glass_pane1","croptopia:buttered_toast","mcwtrpdoors:birch_mystic_trapdoor","mcwfurnitures:birch_bookshelf_cupboard","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","cfm:pink_cooler","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","cfm:red_grill","create:small_calcite_brick_slab_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","croptopia:chicken_and_dumplings","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","utilitix:tiny_charcoal_to_tiny","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","pneumaticcraft:uv_light_box","twigs:quartz_column","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","croptopia:sushi","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwdoors:warped_barn_glass_door","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwtrpdoors:cherry_tropical_trapdoor","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","mcwbridges:spruce_rail_bridge","mcwpaths:sandstone_windmill_weave","mcwtrpdoors:warped_whispering_trapdoor","immersiveengineering:crafting/toolbox","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","sophisticatedstorage:spruce_limited_barrel_3","botania:terrasteel_boots","sophisticatedstorage:spruce_limited_barrel_2","sophisticatedstorage:spruce_limited_barrel_1","mcwwindows:crimson_shutter","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","sophisticatedstorage:spruce_limited_barrel_4","pneumaticcraft:air_cannon","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","travelersbackpack:pink_sleeping_bag","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","railcraft:blast_furnace_bricks","mcwfurnitures:cherry_large_drawer","ae2:network/cables/dense_covered_pink","dyenamics:amber_stained_glass_pane_from_glass_pane","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","computercraft:printed_book","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","bloodmagic:bloodstonebrick","reliquary:uncrafting/glass_bottle","botania:red_string_relay","ae2:tools/portable_item_cell_256k","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwfurnitures:spruce_stool_chair","mcwroofs:purple_terracotta_upper_lower_roof","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwbiomesoplenty:stripped_hellbark_log_window","blue_skies:cake_compat","mcwlights:reinforced_torch","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwdoors:spruce_stable_head_door","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","productivebees:stonecutter/aspen_canvas_expansion_box","pneumaticcraft:liquid_hopper","handcrafted:wood_cup","botania:green_pavement","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","bloodmagic:path/path_stonetile","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","twilightforest:equipment/knightmetal_pickaxe","mcwroofs:white_concrete_roof","ad_astra:ti_69","additionallanterns:normal_nether_bricks_chain","mcwwindows:window_half_bar_base","megacells:cells/portable/portable_item_cell_4m","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwdoors:birch_modern_door","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","irons_spellbooks:wandering_magician_leggings","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","croptopia:fried_chicken","railcraft:sheep_detector","minecraft:magenta_dye_from_blue_red_pink","croptopia:steamed_rice","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","bloodmagic:experience_tome","cfm:birch_upgraded_gate","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","xnet:advanced_connector_yellow","ae2:network/parts/terminals_crafting","twigs:calcite_wall","create:deepslate_from_stone_types_deepslate_stonecutting","megacells:cells/portable/portable_item_cell_1m","cfm:stripped_oak_chair","xnet:netcable_blue","cfm:diving_board","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","cfm:rock_path","botania:mana_spreader","connectedglass:scratched_glass_black1","connectedglass:clear_glass_yellow2","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","handcrafted:white_cup","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","ae2:network/cables/glass_gray","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","botania:lens_tripwire","minecraft:dye_red_wool","mcwroofs:nether_bricks_top_roof","productivebees:stonecutter/jungle_canvas_hive","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwroofs:nether_bricks_lower_roof","mcwroofs:cyan_concrete_top_roof","twilightforest:wood/spruce_banister","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","bloodmagic:lava_crystal","allthecompressed:compress/birch_log_1x","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwtrpdoors:birch_bark_trapdoor","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_middle_brown","mcwdoors:cherry_four_panel_door","mcwfurnitures:birch_modern_chair","mcwroofs:gutter_base_green","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","mcwwindows:crimson_planks_four_window","minecraft:piston","ad_astra:large_gas_tank","pneumaticcraft:harvesting_drone","mcwlights:white_lamp","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","allthemodium:allthemodium_ingot_from_raw_smelting","undergarden:regalium_block","mcwbiomesoplenty:umbran_plank_pane_window","connectedglass:borderless_glass_red_pane2","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwdoors:bamboo_four_panel_door","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwroofs:gray_upper_steep_roof","productivetrees:wood/brown_amber_wood","botania:third_eye","mcwroofs:birch_planks_steep_roof","mcwwindows:metal_curtain_rod","croptopia:apple_sapling","farmersdelight:fried_egg_from_campfire_cooking","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","mcwfurnitures:stripped_cherry_double_wardrobe","croptopia:beef_jerky","handcrafted:oak_shelf","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","cfm:stripped_birch_kitchen_counter","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","mcwtrpdoors:spruce_mystic_trapdoor","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","ad_astra:small_white_industrial_lamp","create:crafting/kinetics/copper_valve_handle","computercraft:speaker","mcwwindows:mangrove_louvered_shutter","ad_astra:space_helmet","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","mcwfurnitures:birch_covered_desk","aether:blue_cape_light_blue_wool","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwroofs:black_terracotta_top_roof","mcwdoors:warped_nether_door","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","handcrafted:creeper_trophy","travelersbackpack:skeleton","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","productivebees:stonecutter/oak_canvas_expansion_box","dyenamics:banner/bubblegum_banner","farmersdelight:stove","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","handcrafted:birch_side_table","packingtape:tape","mcwfurnitures:stripped_cherry_coffee_table","mcwdoors:print_waffle","rftoolsbuilder:vehicle_control_module","utilitarian:utility/spruce_logs_to_pressure_plates","cfm:birch_kitchen_counter","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","minecraft:light_blue_carpet","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","botania:fire_rod","pneumaticcraft:large_tank","botania:petal_pink_double","mcwdoors:birch_mystic_door","securitycraft:reinforced_crimson_fence_gate","pneumaticcraft:spawner_core_shell","mcwfurnitures:stripped_jungle_wardrobe","undergarden:smelt_gloomper_leg","mcwbiomesoplenty:rainbow_birch_hedge","utilitarian:utility/birch_logs_to_stairs","immersiveengineering:crafting/pickaxe_steel","productivebees:expansion_boxes/expansion_box_crimson_canvas","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","mcwfurnitures:jungle_stool_chair","mcwfurnitures:birch_double_drawer","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","botania:elementium_shears","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","minecraft:red_banner","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","create:polished_cut_calcite_from_stone_types_calcite_stonecutting","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","mcwfurnitures:stripped_birch_double_drawer","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:warped_park_bench","twilightforest:tf_moss_to_vanilla","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwfurnitures:stripped_birch_glass_table","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","pylons:expulsion_pylon","botania:manasteel_chestplate","mcwfurnitures:cherry_bookshelf","mcwfences:bamboo_highley_gate","mythicbotany:yggdrasil_branch","alltheores:brass_rod","twilightforest:equipment/arctic_helmet","mcwroofs:red_terracotta_attic_roof","bloodmagic:arc","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","create:crafting/kinetics/sequenced_gearshift","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","cfm:light_gray_grill","mcwtrpdoors:cherry_mystic_trapdoor","create:crafting/kinetics/black_seat","cfm:oak_desk","cfm:warped_crate","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","mcwfurnitures:birch_large_drawer","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwfurnitures:warped_large_drawer","minecolonies:potato_soup","chemlib:sodium_ingot_from_smelting_sodium_dust","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:lavender_stained_glass","dyenamics:cherenkov_dye","twilightforest:time_boat","minecraft:sandstone_wall","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","utilitarian:utility/cherry_logs_to_boats","alchemistry:fusion_chamber_controller","securitycraft:briefcase","mcwwindows:green_mosaic_glass","create:crafting/logistics/stockpile_switch","mcwbridges:dry_bamboo_bridge_pier","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","alltheores:electrum_rod","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","croptopia:butter","connectedglass:scratched_glass_red2","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwfurnitures:warped_drawer","ae2:network/cables/dense_covered_white","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","botania:red_string_dispenser","mcwroofs:sandstone_lower_roof","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","handcrafted:jungle_dining_bench","cfm:red_cooler","botania:dreamwood_wall","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfurnitures:stripped_cherry_large_drawer","twilightdelight:knightmetal_knife","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","mcwpaths:sandstone_basket_weave_paving","mcwdoors:cherry_mystic_door","aether:golden_pendant","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_1","minecraft:allthemodium_spell_book_smithing","sophisticatedstorage:stack_upgrade_tier_2","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","ae2:decorative/quartz_fixture","mcwtrpdoors:cherry_paper_trapdoor","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","littlelogistics:automatic_switch_rail","quark:tweaks/crafting/utility/tools/stone_axe","alltheores:electrum_plate","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","aether:netherite_helmet_repairing","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","immersiveengineering:crafting/cokebrick_to_slab","mcwroofs:white_concrete_top_roof","ad_astra:pink_flag","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","mcwfurnitures:warped_chair","simplylight:illuminant_purple_block_on_dyed","cfm:blue_sofa","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","productivetrees:planks/brown_amber_planks","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwtrpdoors:birch_barred_trapdoor","mcwfurnitures:jungle_striped_chair","pneumaticcraft:tube_junction","bloodmagic:smelting/blasting_ingot_from_raw_hellforged","bambooeverything:bamboo_torch","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","mcwdoors:birch_waffle_door","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","utilitarian:utility/cherry_logs_to_doors","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bloodmagic:alchemy_table","mcwlights:redstone_lamp_slab","mcwfurnitures:cherry_bookshelf_cupboard","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","bloodmagic:blood_rune_speed","mcwbiomesoplenty:jacaranda_plank_pane_window","bloodmagic:path/path_wornstone","mcwroofs:cherry_upper_steep_roof","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","expatternprovider:silicon_block","mcwfurnitures:warped_modern_chair","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","itemcollectors:basic_collector","minecraft:loom","supplementaries:sign_post_spruce","ad_astra:steel_engine","enderio:resetting_lever_three_hundred_inv","mcwtrpdoors:spruce_barrel_trapdoor","immersiveengineering:crafting/connector_mv_relay","bloodmagic:blood_rune_capacity","railcraft:steel_axe","twilightdelight:ironwood_knife","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","mcwroofs:light_gray_concrete_steep_roof","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:charging_module","pneumaticcraft:item_life_upgrade","travelersbackpack:cow","allthemodium:smithing/allthemodium_shovel_smithing","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwfurnitures:stripped_cherry_chair","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","cfm:pink_picket_fence","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwfurnitures:stripped_cherry_triple_drawer","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","alltheores:nickel_plate","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","utilitarian:utility/spruce_logs_to_stairs","bloodmagic:smelting/ingot_from_raw_hellforged","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","mcwdoors:cherry_beach_door","dyenamics:bed/maroon_bed_frm_white_bed","travelersbackpack:dye_red_sleeping_bag","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","railways:crafting/smokestack_woodburner","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","cfm:dye_red_picket_fence","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","cfm:brown_cooler","delightful:knives/terra_knife","xnet:netcable_red","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","megacells:cells/portable/portable_item_cell_64m","minecraft:spruce_trapdoor","mcwwindows:dark_prismarine_parapet","rftoolsutility:inventoryplus_module","productivebees:hives/advanced_snake_block_canvas_hive","bigreactors:reprocessor/wasteinjector","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","cfm:birch_upgraded_fence","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","dyenamics:persimmon_stained_glass_pane_from_glass_pane","handcrafted:birch_corner_trim","aether:golden_leggings_repairing","mcwfurnitures:stripped_birch_lower_triple_drawer","computercraft:disk_drive","mcwroofs:blue_striped_awning","mcwfurnitures:spruce_desk","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","aether:golden_helmet_repairing","alchemistry:dissolver","comforts:hammock_to_blue","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwtrpdoors:spruce_swamp_trapdoor","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","sophisticatedbackpacks:compacting_upgrade","cfm:gray_kitchen_counter","minecraft:snow_block","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","handcrafted:spruce_cupboard","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","twilightforest:compressed_blocks/knightmetal_block","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:sandstone_stairs","minecraft:composter","mcwfurnitures:stripped_cherry_modern_chair","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","handcrafted:birch_dining_bench","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","mcwfurnitures:stripped_cherry_drawer","domum_ornamentum:cyan_floating_carpet","mcwfurnitures:cherry_modern_chair","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","mcwfurnitures:stripped_warped_covered_desk","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","create:crafting/logistics/brass_funnel","minecraft:fermented_spider_eye","railcraft:coal_coke_block_from_coal_coke","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwdoors:spruce_classic_door","mcwdoors:birch_nether_door","supplementaries:flags/flag_black","pneumaticcraft:vacuum_trap","mcwwindows:acacia_louvered_shutter","mcwdoors:print_whispering","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions","minecraft:cobbled_deepslate_slab","aether:golden_sword_repairing","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","railways:crafting/smokestack_diesel","mcwroofs:gutter_base_orange","aether:bow_repairing","mcwfurnitures:stripped_spruce_stool_chair","securitycraft:reinforced_light_gray_stained_glass","farmersdelight:cooking/pasta_with_mutton_chop","minecraft:chiseled_quartz_block","enderio:silent_oak_pressure_plate","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","allthemodium:allthemodium_dust_from_ore_crushing","mcwroofs:bricks_attic_roof","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","mcwfurnitures:warped_drawer_counter","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","reliquary:uncrafting/spider_eye","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","create:crafting/logistics/display_link","ad_astra:airlock","mcwroofs:grass_steep_roof","cfm:pink_picket_gate","alchemistry:combiner","occultism:crafting/brush","mcwroofs:birch_steep_roof","ae2:network/cables/glass_white","mcwdoors:jungle_stable_head_door","croptopia:egg_roll","mcwtrpdoors:cherry_barrel_trapdoor","twigs:bamboo_mat","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","sophisticatedstorage:chipped/loom_table_upgrade","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","botania:lens_firework","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","twilightforest:equipment/knightmetal_leggings","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","minecraft:lime_dye","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","cfm:warped_blinds","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:stripped_redwood_log_window","botania:quartz_sunny","productivebees:stonecutter/maple_canvas_expansion_box","botania:terrasteel_helmet","handcrafted:birch_chair","railcraft:signal_lamp","mcwfurnitures:stripped_warped_large_drawer","cfm:jungle_blinds","reliquary:uncrafting/sugar","tombstone:bone_needle","minecraft:stone_brick_wall","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_cherry_covered_desk","xnet:netcable_green","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:decoration_table","sophisticatedstorage:jungle_limited_barrel_4","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","sophisticatedstorage:jungle_limited_barrel_3","mcwdoors:warped_bamboo_door","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","mcwtrpdoors:cherry_cottage_trapdoor","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","connectedglass:scratched_glass_white_pane2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwfurnitures:stripped_warped_coffee_table","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","enderio:stone_gear_upgrade","botania:light_blue_petal_block","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","immersiveengineering:crafting/glider","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","minecraft:gold_block","botania:floating_pure_daisy","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","cfm:magenta_picket_fence","minecraft:cherry_wood","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","mcwfurnitures:stripped_birch_striped_chair","botania:conversions/terrasteel_block_deconstruct","cfm:spruce_kitchen_counter","travelersbackpack:warden","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","dyenamics:fluorescent_concrete_powder","botania:incense_plate","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","twilightforest:canopy_chest_boat","comforts:sleeping_bag_light_blue","mcwpaths:spruce_planks_path","bambooeverything:dry_bamboo","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","easy_villagers:breeder","railways:stonecutting/riveted_locometal","securitycraft:reinforced_observer","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","mcwdoors:spruce_nether_door","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:stripped_warped_desk","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","mcwfences:acacia_curved_gate","allthemodium:unobtainium_rod","mcwfurnitures:stripped_warped_double_drawer_counter","supplementaries:deepslate_lamp","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:stripped_birch_desk","farmersdelight:cooking/baked_cod_stew","immersiveengineering:crafting/wirecoil_structure_rope","mcwfurnitures:oak_bookshelf","utilitix:stonecutter_cart","alltheores:lead_rod","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","minecraft:iron_block","farmersdelight:spruce_cabinet","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","cfm:birch_blinds","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","allthecompressed:compress/spruce_planks_1x","mcwbridges:bamboo_bridge_pier","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","domum_ornamentum:blockbarreldeco_onside","minecraft:flower_pot","minecraft:diamond_pickaxe","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","productivebees:hives/advanced_jungle_canvas_hive","pneumaticcraft:reinforced_brick_wall","aether:iron_helmet_repairing","farmersdelight:steak_and_potatoes","mcwtrpdoors:spruce_ranch_trapdoor","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","mcwpaths:sandstone_crystal_floor_stairs","pneumaticcraft:wall_lamp_inverted_blue","securitycraft:codebreaker","rftoolsutility:energy_module","supplementaries:flute","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","constructionwand:infinity_wand","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwdoors:warped_waffle_door","mcwfences:warped_horse_fence","mcwfurnitures:warped_end_table","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","megacells:crafting/mega_energy_cell","mcwbiomesoplenty:maple_four_window","cfm:blue_kitchen_counter","dyenamics:spring_green_stained_glass_pane_from_glass_pane","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwdoors:bamboo_classic_door","mcwroofs:base_top_roof","securitycraft:harming_module","silentgear:bort_block","minecraft:golden_boots","mcwfurnitures:warped_wardrobe","mcwroofs:birch_roof","immersiveengineering:crafting/grit_sand","minecraft:pink_dye_from_red_white_dye","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","create:crafting/kinetics/item_vault","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","supplementaries:statue","pneumaticcraft:vacuum_pump","immersiveengineering:crafting/tinted_glass_lead_wire","create:crafting/kinetics/clockwork_bearing","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwroofs:gutter_middle_orange","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","mcwtrpdoors:birch_barrel_trapdoor","utilitix:diamond_shears","minecraft:wooden_pickaxe","handcrafted:pink_sheet","minecraft:cherry_planks","cfm:stripped_warped_kitchen_counter","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","twilightforest:compressed_blocks/ironwood_block","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","mcwdoors:birch_cottage_door","mcwfurnitures:stripped_birch_bookshelf_cupboard","rftoolsbase:machine_base","botania:aura_ring_greater","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","minecraft:stick_from_bamboo_item","mcwpaths:brick_crystal_floor_slab","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","twilightforest:wood/oak_banister","alltheores:enderium_plate","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","quark:tweaks/crafting/utility/bent/bread","pneumaticcraft:pressure_chamber_wall","modularrouters:player_module","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwdoors:spruce_paper_door","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwfurnitures:birch_lower_bookshelf_drawer","mcwroofs:brown_concrete_attic_roof","alltheores:raw_iridium_block","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","ae2:network/cables/smart_red","create:crafting/kinetics/nixie_tube","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","mcwfurnitures:birch_bookshelf_drawer","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","mcwfurnitures:stripped_birch_bookshelf_drawer","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","mcwfurnitures:warped_double_drawer","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","mcwfurnitures:birch_coffee_table","rftoolsstorage:storage_control_module","chimes:bamboo_chimes","enderio:pressurized_fluid_tank","pneumaticcraft:network_data_storage","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","botania:placeholder","railcraft:steel_gear","aether:diamond_shovel_repairing","immersiveengineering:crafting/treated_wood_horizontal","allthemodium:piglich_heart_block","bigreactors:smelting/graphite_from_charcoal","sophisticatedstorage:crafting_upgrade","allthemodium:smithing/allthemodium_axe_smithing","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","mcwfurnitures:stripped_cherry_drawer_counter","delightful:knives/steel_knife","cfm:warped_desk","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","pneumaticcraft:network_registry","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","mcwtrpdoors:warped_ranch_trapdoor","mysticalagriculture:prosperity_gemstone","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","minecraft:gunpowder","travelersbackpack:bookshelf","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","mcwtrpdoors:cherry_barn_trapdoor","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","immersiveengineering:crafting/alu_wallmount","ae2:network/cables/smart_fluix","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","pylons:potion_filter","enderio:resetting_lever_thirty","immersiveengineering:crafting/conveyor_splitter","minecraft:glistering_melon_slice","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","handcrafted:spruce_fancy_bed","minecraft:snow","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","handcrafted:quartz_corner_trim","pneumaticcraft:electrostatic_compressor","mcwfurnitures:warped_bookshelf","ad_astra:steel_factory_block","mcwfurnitures:stripped_spruce_triple_drawer","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","handcrafted:spruce_chair","mcwroofs:light_gray_concrete_lower_roof","occultism:crafting/spirit_torch","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","productivebees:expansion_boxes/expansion_box_cherry_canvas","farmersdelight:cooking/vegetable_noodles","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","mcwpaths:stone_crystal_floor_path","alltheores:aluminum_gear","twigs:stone_column","minecraft:lectern","pneumaticcraft:compressed_bricks_from_stone_stonecutting","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwbiomesoplenty:hellbark_plank_window2","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","mcwtrpdoors:bamboo_blossom_trapdoor","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","megacells:cells/portable/portable_fluid_cell_16m","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","connectedglass:borderless_glass_white2","aquaculture:heavy_hook","handcrafted:birch_fancy_bed","mcwfurnitures:warped_striped_chair","aether:netherite_sword_repairing","minecraft:white_concrete_powder","biomesoplenty:dead_chest_boat","pneumaticcraft:advanced_air_compressor","minecraft:yellow_terracotta","mcwwindows:acacia_window","mcwroofs:green_terracotta_roof","croptopia:food_press","biomesoplenty:rabbit_stew_from_toadstool","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","croptopia:shaped_milk_bottle","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","twilightforest:vanilla_to_tf_lilypad","botania:terrasteel_chestplate","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","farmersdelight:nether_salad","mcwbridges:birch_bridge_pier","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","mcwlights:orange_lamp","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","utilitix:tiny_coal_to_tiny","pneumaticcraft:reinforced_brick_pillar","create:cut_calcite_brick_stairs_from_stone_types_calcite_stonecutting","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","gtceu:shapeless/decompress_tin_from_ore_block","mcwdoors:birch_classic_door","mcwpaths:brick_crystal_floor","botania:dodge_ring","ae2:materials/basiccard","handcrafted:oak_table","handcrafted:white_plate","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwfences:oak_highley_gate","xnet:connector_green_dye","pneumaticcraft:safety_tube_module","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwlights:blue_lamp","productivebees:hives/advanced_birch_canvas_hive","comforts:hammock_to_red","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","railcraft:charge_terminal","mcwroofs:blue_concrete_attic_roof","mysticalagriculture:inferium_block","mcwfurnitures:stripped_warped_chair","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","croptopia:chocolate","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","mcwbridges:nether_bricks_bridge","mcwdoors:spruce_barn_door","allthecompressed:compress/moss_block_1x","twilightdelight:maze_stove","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","immersiveengineering:crafting/alu_fence","domum_ornamentum:red_floating_carpet","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","aether:iron_boots_repairing","mcwdoors:bamboo_beach_door","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","railcraft:steel_tank_gauge","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwdoors:spruce_modern_door","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","pneumaticcraft:network_io_port","twigs:polished_calcite_bricks_from_calcite_stonecutting","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","alltheores:enderium_rod","corail_woodcutter:cherry_woodcutter","handcrafted:birch_bench","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","dyenamics:bed/persimmon_bed_frm_white_bed","mythicbotany:gaia_pylon","botanypots:botanypots/crafting/terracotta_botany_pot","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwdoors:bamboo_bark_glass_door","mcwroofs:stone_bricks_top_roof","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","utilitarian:utility/spruce_logs_to_boats","minecraft:gray_stained_glass","cfm:magenta_trampoline","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwroofs:oak_upper_lower_roof","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","mcwfurnitures:stripped_birch_cupboard_counter","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","bloodmagic:smelting/saltpeter","mcwlights:crimson_ceiling_fan_light","minecraft:red_bed","connectedglass:clear_glass_lime2","farmersdelight:cooking/mushroom_rice","twilightdelight:twilight_spring","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","easy_villagers:incubator","ae2:blasting/silicon_from_certus_quartz_dust","mcwroofs:brown_terracotta_top_roof","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","mcwfurnitures:stripped_birch_counter","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","computercraft:printed_pages","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","mcwfurnitures:stripped_spruce_modern_chair","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","delightful:knives/certus_quartz_knife","productivebees:stonecutter/concrete_canvas_hive","mcwdoors:print_bamboo","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","mcwdoors:birch_barn_door","mcwtrpdoors:spruce_tropical_trapdoor","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","mcwtrpdoors:print_cottage","securitycraft:reinforced_gray_stained_glass","aether:iron_hoe_repairing","minecraft:copper_ingot_from_smelting_raw_copper","sophisticatedbackpacks:chipped/carpenters_table_upgrade","create:crafting/kinetics/mechanical_arm","mcwbiomesoplenty:stripped_maple_log_window2","aether:wooden_sword_repairing","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","twilightforest:equipment/arctic_leggings","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","naturalist:bug_net","minecraft:birch_sign","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","additionallanterns:normal_lantern_gray","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","domum_ornamentum:beige_bricks","supplementaries:hourglass","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","cfm:light_blue_sofa","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","cfm:stripped_spruce_table","alltheores:aluminum_rod","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","minecraft:orange_dye_from_red_yellow","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","minecraft:spruce_stairs","supplementaries:checker","croptopia:flour","railcraft:bushing_gear_brass","mcwdoors:birch_japanese_door","minecraft:jungle_sign","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","railcraft:steel_tank_valve","botania:conversions/light_blue_petal_block_deconstruct","domum_ornamentum:cactus_extra","cfm:cyan_cooler","minecraft:light_gray_dye_from_white_tulip","pneumaticcraft:network_node","ae2:network/parts/formation_plane","connectedglass:clear_glass_pane3","railcraft:silver_gear","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","ad_astra:steel_rod","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwroofs:light_gray_roof","immersiveengineering:crafting/conveyor_extract","create:small_calcite_brick_stairs_from_stone_types_calcite_stonecutting","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwdoors:bamboo_tropical_door","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","travelersbackpack:redstone","minecraft:slime_block","twilightforest:magic_map_focus","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","dyenamics:lavender_stained_glass_pane_from_glass_pane","mcwfurnitures:stripped_warped_modern_desk","create:cut_calcite_from_stone_types_calcite_stonecutting","chimes:glass_bells","mcwwindows:iron_shutter","mcwroofs:birch_planks_top_roof","mcwdoors:cherry_paper_door","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/connector_mv","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","ad_astra:nasa_workbench","mcwpaths:sandstone_flagstone","cfm:birch_crate","ad_astra:etrionic_capacitor","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","ae2:tools/nether_quartz_axe","mcwroofs:birch_planks_lower_roof","allthecompressed:compress/nether_star_block_3x","mcwwindows:brown_mosaic_glass_pane","mcwpaths:stone_windmill_weave_stairs","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","enderio:resetting_lever_sixty_inv","mcwfurnitures:stripped_spruce_large_drawer","minecraft:smooth_sandstone","minecraft:magenta_stained_glass_pane_from_glass_pane","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","cfm:stripped_warped_desk","alltheores:copper_rod","productivebees:stonecutter/cherry_canvas_hive","mcwfurnitures:stripped_birch_modern_chair","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","utilitix:filter_rail","dyenamics:maroon_dye","minecraft:spruce_planks","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","croptopia:pork_jerky","enderio:resetting_lever_sixty_from_inv","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","minecraft:mossy_cobblestone_from_moss_block","bloodmagic:blood_rune_self_sacrifice","mcwfurnitures:spruce_triple_drawer","minecraft:cracked_stone_bricks","mcwdoors:cherry_classic_door","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwfurnitures:birch_double_wardrobe","mcwfurnitures:stripped_birch_desk","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","utilitarian:utility/cherry_logs_to_pressure_plates","croptopia:toast_with_jam","mcwtrpdoors:birch_glass_trapdoor","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","minecraft:lantern","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","minecraft:magenta_stained_glass","minecraft:ender_chest","railcraft:nickel_gear","minecraft:iron_trapdoor","travelersbackpack:quartz_smithing","pneumaticcraft:thermal_lagging","mcwbridges:nether_bricks_bridge_pier","ae2:network/cables/dense_covered_yellow","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","handcrafted:stone_corner_trim","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","modularrouters:void_module","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","allthecompressed:decompress/nether_star_block_2x","sophisticatedstorage:spruce_barrel","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","cfm:warped_desk_cabinet","mcwroofs:spruce_planks_steep_roof","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","mcwfurnitures:spruce_large_drawer","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","handcrafted:spruce_shelf","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","aether:golden_boots_repairing","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","securitycraft:reinforced_warped_fence_gate","pneumaticcraft:gun_ammo_weighted","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","caupona:clay_cistern","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfurnitures:cherry_table","minecraft:light_gray_dye_from_gray_white_dye","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","mcwfurnitures:stripped_cherry_double_drawer","alltheores:aluminum_plate","mcwfences:blackstone_pillar_wall","mcwfurnitures:birch_wardrobe","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","bloodmagic:synthetic_point","deepresonance:lens","additionallanterns:normal_lantern_black","croptopia:doughnut","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfurnitures:cherry_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","minecraft:redstone_lamp","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_warped_lower_bookshelf_drawer","mcwbiomesoplenty:stripped_umbran_pane_window","mcwfurnitures:warped_lower_bookshelf_drawer","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwfurnitures:warped_counter","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","mcwfurnitures:stripped_birch_large_drawer","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","mcwdoors:warped_classic_door","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","twilightforest:equipment/knightmetal_sword","allthecompressed:compress/cobbled_deepslate_1x","productivebees:stonecutter/acacia_canvas_expansion_box","twilightdelight:berry_stick","silentgear:stone_torch","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","utilitarian:utility/warped_logs_to_pressure_plates","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","travelersbackpack:blue_sleeping_bag","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","botania:diluted_pool","mcwlights:glowstone_slab","modularrouters:energy_output_module","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","mcwdoors:birch_western_door","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","reliquary:uncrafting/string","connectedglass:tinted_borderless_glass_white2","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alchemistry:fission_chamber_controller","railcraft:animal_detector","alltheores:diamond_rod","minecraft:stone_slab_from_stone_stonecutting","mcwroofs:cherry_roof","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:light_blue_terracotta_upper_lower_roof","cfm:stripped_warped_desk_cabinet","mcwwindows:bamboo_shutter","mcwdoors:print_birch","chemlib:sodium_nugget_to_ingot","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwroofs:spruce_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwfurnitures:warped_desk","mcwlights:white_paper_lamp","cfm:spruce_chair","minecraft:oak_trapdoor","littlelogistics:locomotive_route","connectedglass:clear_glass_white2","mcwwindows:mangrove_plank_pane_window","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","minecraft:netherite_leggings_smithing","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","create:crafting/kinetics/placard","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:birch_button","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","mcwtrpdoors:cherry_ranch_trapdoor","minecraft:stone_brick_stairs_from_stone_stonecutting","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","comforts:sleeping_bag_to_light_blue","immersiveengineering:crafting/sorter","mcwbiomesoplenty:dead_hedge","botania:petal_pink","handcrafted:wood_bowl","mcwfurnitures:cherry_drawer","pneumaticcraft:charging_station","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwdoors:bamboo_barn_glass_door","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","create:crafting/kinetics/crafter_slot_cover","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","minecraft:netherite_machete_smithing","mcwtrpdoors:warped_barred_trapdoor","mcwroofs:warped_steep_roof","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","supplementaries:sign_post_birch","comforts:hammock_pink","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwdoors:warped_stable_door","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","mcwfurnitures:stripped_cherry_striped_chair","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","farmersdelight:safety_net","mcwdoors:cherry_cottage_door","immersiveengineering:crafting/maintenance_kit","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","twigs:bone_meal_from_seashells","mcwlights:golden_triple_candle_holder","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","sophisticatedstorage:stack_upgrade_tier_1_plus","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","supplementaries:sack","mcwfurnitures:stripped_birch_stool_chair","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwdoors:cherry_stable_door","cfm:warped_upgraded_fence","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwfurnitures:stripped_warped_bookshelf_cupboard","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","allthemodium:smithing/allthemodium_helmet_smithing","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","productivebees:hives/advanced_bamboo_canvas_hive","mcwroofs:blue_terracotta_top_roof","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","minecraft:brown_dye","bloodmagic:incense_altar","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","allthemodium:smithing/allthemodium_boots_smithing","twilightforest:vanilla_to_tf_moss","mcwwindows:mangrove_shutter","bloodmagic:blood_rune_sacrifice","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","dyenamics:mint_dye","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","alchemistry:reactor_input","railcraft:diamond_spike_maul","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwdoors:cherry_barn_glass_door","minecraft:white_candle","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","utilitarian:tps_meter","mcwdoors:birch_four_panel_door","utilitarian:utility/spruce_logs_to_doors","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","minecraft:blue_banner","ae2:network/cells/fluid_cell_housing","mcwroofs:nether_bricks_upper_steep_roof","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","mcwdoors:bamboo_nether_door","blue_skies:starlit_bookshelf","botania:petal_light_blue","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwfurnitures:spruce_modern_chair","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","allthemodium:smithing/allthemodium_leggings_smithing","create:crafting/kinetics/elevator_pulley","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:warped_bookshelf_cupboard","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","immersiveengineering:crafting/plate_electrum_hammering","mcwdoors:birch_stable_door","biomesoplenty:jacaranda_chest_boat","utilitarian:utility/warped_logs_to_doors","mcwfurnitures:stripped_cherry_bookshelf","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","reliquary:mercy_cross","farmersdelight:fried_egg","minecraft:magma_cream","botania:slime_bottle","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwfurnitures:cherry_double_drawer_counter","create:crafting/kinetics/attribute_filter","handcrafted:white_bowl","supplementaries:key","productivebees:expansion_boxes/expansion_box_bamboo_canvas","cfm:warped_kitchen_counter","ae2:block_cutter/walls/sky_stone_wall","mcwroofs:warped_upper_lower_roof","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","blue_skies:maple_bookshelf","rftoolspower:power_core1","connectedglass:clear_glass_red2","pneumaticcraft:logistics_frame_default_storage","aether:wooden_axe_repairing","computercraft:cable","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","bloodmagic:path/path_wood","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","mcwroofs:thatch_attic_roof","securitycraft:reinforced_orange_stained_glass_pane_from_glass","securitycraft:reinforced_lime_stained_glass","pneumaticcraft:collector_drone","pneumaticcraft:wall_lamp_inverted_gray","handcrafted:spruce_table","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","mcwpaths:brick_running_bond_path","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","botania:mana_pylon","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","mcwtrpdoors:birch_classic_trapdoor","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:birch_blinds","mcwfurnitures:stripped_oak_striped_chair","cfm:spruce_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","simplylight:illuminant_red_block_on_toggle","mcwpaths:brick_windmill_weave_stairs","mcwfurnitures:birch_double_drawer_counter","additionallanterns:normal_sandstone_lantern","mcwdoors:spruce_tropical_door","mcwroofs:magenta_terracotta_upper_lower_roof","create:polished_cut_calcite_stairs_from_stone_types_calcite_stonecutting","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","dyenamics:spring_green_terracotta","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","croptopia:mortar_and_pestle","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","twilightforest:equipment/knightmetal_ring","mcwfurnitures:warped_triple_drawer","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","botania:apothecary_livingrock","advanced_ae:smalladvpatpro","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","mcwtrpdoors:bamboo_barn_trapdoor","railcraft:iron_spike_maul","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","easy_villagers:farmer","mcwbiomesoplenty:fir_four_window","everythingcopper:copper_axe","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","railcraft:feed_station","minecraft:black_stained_glass_pane_from_glass_pane","minecraft:pink_banner","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","twilightforest:smelted_cracked_underbrick","connectedglass:scratched_glass_white2","mcwfurnitures:spruce_chair","create:cut_calcite_brick_slab_from_stone_types_calcite_stonecutting","productivebees:expansion_boxes/expansion_box_spruce_canvas","mcwdoors:warped_cottage_door","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","handcrafted:oak_nightstand","mcwdoors:bamboo_paper_door","railcraft:personal_world_spike","immersiveengineering:crafting/armor_steel_chestplate","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","connectedglass:clear_glass_pink2","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","mcwdoors:warped_glass_door","immersiveengineering:crafting/stick_aluminum","mcwroofs:thatch_steep_roof","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","dyenamics:bubblegum_concrete_powder","xnet:redstoneproxy_update","mcwfurnitures:stripped_warped_triple_drawer","mcwroofs:spruce_planks_attic_roof","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwroofs:nether_bricks_attic_roof","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","merequester:requester","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","minecraft:cracked_nether_bricks","utilitix:gilding_crystal","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","handcrafted:birch_couch","farmersdelight:cooking/fish_stew","supplementaries:slingshot","allthemodium:allthemodium_plate","twigs:cobblestone_from_pebble","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","productivebees:expansion_boxes/expansion_box_mangrove_canvas","utilitix:tiny_charcoal_from_tiny","everythingcopper:copper_rail","mcwfurnitures:birch_cupboard_counter","cfm:black_grill","handcrafted:deepslate_pillar_trim","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","pneumaticcraft:wall_lamp_inverted_black","mcwtrpdoors:warped_four_panel_trapdoor","mcwwindows:stripped_warped_stem_window2","create:small_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","cfm:birch_desk_cabinet","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:nether_bricks_steep_roof","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aether:aether_iron_nugget_from_blasting","cfm:stripped_warped_park_bench","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwroofs:bricks_upper_steep_roof","botania:terraform_rod","minecraft:iron_pickaxe","aether:shield_repairing","mcwwindows:jungle_shutter","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","minecraft:spruce_button","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","ae2:network/cables/glass_orange","mcwfurnitures:stripped_spruce_table","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwdoors:birch_swamp_door","mcwfences:modern_diorite_wall","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwtrpdoors:bamboo_classic_trapdoor","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","cfm:orange_cooler","mcwfurnitures:birch_drawer","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwtrpdoors:bamboo_barred_trapdoor","mcwbridges:balustrade_mossy_stone_bricks_bridge","aether:leather_gloves_repairing","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","computercraft:monitor_advanced","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","create:layered_calcite_from_stone_types_calcite_stonecutting","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","utilitarian:utility/birch_logs_to_slabs","securitycraft:keypad_item","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","additionallanterns:normal_lantern_lime","botania:elementium_block","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mysticalagriculture:inferium_seeds","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","trashcans:energy_trash_can","botania:dreamwood_twig","mcwpaths:stone_crystal_floor","mcwfurnitures:stripped_cherry_modern_desk","pneumaticcraft:air_compressor","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","mcwdoors:bamboo_modern_door","cfm:warped_coffee_table","cfm:fridge_dark","chimes:copper_chimes","handcrafted:birch_desk","minecraft:birch_fence","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","create:crafting/kinetics/brass_door","farmersdelight:cooking_pot","pneumaticcraft:diagnostic_subroutine","simplylight:illuminant_magenta_block_on_dyed","mcwtrpdoors:mangrove_bark_trapdoor","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","chemlib:sodium_ingot_to_block","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","cfm:stripped_jungle_kitchen_counter","mcwtrpdoors:warped_swamp_trapdoor","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:spruce_fence_gate","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","delightful:knives/electrum_knife","reliquary:mob_charm_fragments/wither_skeleton","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","alltheores:steel_dust_from_alloy_blending","immersiveengineering:crafting/earmuffs","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","comforts:sleeping_bag_pink","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","minecraft:white_stained_glass_pane_from_glass_pane","mcwfurnitures:cherry_modern_desk","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","minecraft:birch_wood","cfm:spatula","pneumaticcraft:charging_upgrade","ae2:network/cables/dense_covered_fluix_clean","littlelogistics:switch_rail","mcwdoors:warped_tropical_door","mcwfurnitures:stripped_jungle_double_wardrobe","minecraft:scaffolding","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","mcwtrpdoors:birch_ranch_trapdoor","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","sophisticatedstorage:birch_chest","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwfurnitures:birch_modern_wardrobe","mcwlights:copper_candle_holder","minecraft:mossy_stone_brick_slab","mcwtrpdoors:bamboo_swamp_trapdoor","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:bamboo_chest_raft","mcwtrpdoors:birch_four_panel_trapdoor","minecraft:honey_block","ae2:tools/nether_quartz_spade","additionallanterns:normal_lantern_yellow","farmersdelight:melon_juice","dyenamics:bed/bubblegum_bed_frm_white_bed","cfm:stripped_spruce_desk","sophisticatedbackpacks:crafting_upgrade","productivebees:stonecutter/willow_canvas_expansion_box","mcwtrpdoors:cherry_whispering_trapdoor","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","mcwwindows:jungle_window","minecraft:cobblestone_stairs","connectedglass:clear_glass_magenta2","mcwwindows:crimson_louvered_shutter","supplementaries:candle_holders/candle_holder_orange","twilightforest:equipment/arctic_boots","simplylight:illuminant_red_block_toggle","immersiveengineering:crafting/heavy_engineering","mcwwindows:metal_window2","aether:blue_ice_freezing","croptopia:oatmeal","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","cfm:birch_table","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","reliquary:uncrafting/glowstone_dust","cfm:white_sofa","tombstone:ankh_of_prayer","utilitarian:utility/birch_logs_to_boats","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","aiotbotania:livingrock_sword","cfm:black_kitchen_drawer","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","mcwtrpdoors:birch_cottage_trapdoor","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:spruce_roof","pneumaticcraft:aphorism_tile_reset","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","ae2:network/blocks/cell_workbench","minecraft:polished_andesite_slab","immersiveengineering:crafting/survey_tools","mcwdoors:cherry_whispering_door","mcwlights:magenta_lamp","productivebees:stonecutter/warped_canvas_expansion_box","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","sophisticatedstorage:stonecutter_upgrade","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","handcrafted:oak_bench","mcwfurnitures:cherry_cupboard_counter","mcwfences:nether_brick_grass_topped_wall","mcwdoors:warped_stable_head_door","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwroofs:spruce_planks_upper_steep_roof","mcwwindows:birch_louvered_shutter","minecraft:repeater","minecraft:red_concrete_powder","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","botania:elementium_boots","cfm:jungle_cabinet","additionallanterns:normal_lantern_white","mcwfurnitures:stripped_warped_end_table","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwroofs:base_roof_block","productivebees:hives/advanced_mangrove_canvas_hive","handcrafted:birch_pillar_trim","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","twilightforest:magic_map","botania:conversions/pink_petal_block_deconstruct","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwwindows:spruce_blinds","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwtrpdoors:warped_paper_trapdoor","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","minecraft:warped_planks","handcrafted:spruce_drawer","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","botania:terra_axe","ad_astra:red_industrial_lamp","domum_ornamentum:light_blue_floating_carpet","minecraft:dye_white_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","minecraft:red_candle","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","create:crafting/logistics/content_observer","sophisticatedbackpacks:chipped/glassblower_upgrade","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","additionallanterns:normal_lantern_blue","aether:chainmail_gloves_repairing","allthecompressed:compress/birch_planks_1x","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","comforts:sleeping_bag_to_pink","mcwtrpdoors:spruce_blossom_trapdoor","twilightforest:naga_banner_pattern","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","twilightforest:equipment/knightmetal_shield","dyenamics:bed/fluorescent_bed_frm_white_bed","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","botania:reach_ring","mcwbiomesoplenty:stripped_dead_pane_window","reliquary:uncrafting/packed_ice","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","handcrafted:jungle_drawer","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","minecraft:oak_pressure_plate","botania:mushroom_5","botania:mushroom_4","minecraft:stone_brick_stairs","bigreactors:turbine/basic/activefluidport_forge","aether:netherite_leggings_repairing","aether:skyroot_beehive","botania:mushroom_3","mcwwindows:quartz_window","cfm:brown_grill","mcwroofs:gray_roof","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","supplementaries:present","bloodmagic:blood_rune_orb","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","mysticalagriculture:inferium_block_uncraft","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","botania:glimmering_livingwood_log","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","create:cut_calcite_bricks_from_stone_types_calcite_stonecutting","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","minecraft:clock","mcwroofs:red_concrete_top_roof","create:crafting/appliances/netherite_diving_helmet_from_netherite","domum_ornamentum:cyan_brick_extra","mcwroofs:birch_planks_attic_roof","securitycraft:portable_tune_player","mcwroofs:birch_planks_upper_steep_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","railcraft:lead_gear","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","additionallanterns:normal_lantern_light_gray","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","aquaculture:iron_fillet_knife","silentgear:diamond_shard","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","cfm:lime_picket_fence","aether:leather_leggings_repairing","dyenamics:bed/conifer_bed","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwtrpdoors:spruce_whispering_trapdoor","dyenamics:peach_stained_glass","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwroofs:purple_terracotta_roof","computercraft:turtle_normal/computercraft/speaker","mcwbridges:rope_birch_bridge","mcwfurnitures:stripped_jungle_chair","mcwbridges:nether_bricks_bridge_stair","botania:spark_changer","mcwwindows:white_mosaic_glass","aether:stone_hoe_repairing","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","minecraft:moss_carpet","minecraft:polished_andesite_slab_from_andesite_stonecutting","rftoolsbase:machine_frame","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","cfm:stripped_spruce_crate","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","railcraft:brass_gear","pylons:infusion_pylon","minecraft:mossy_stone_bricks_from_moss_block","comforts:hammock_to_pink","twilightforest:material/armor_shard_cluster","mcwwindows:spruce_window2","mcwfurnitures:stripped_birch_table","mcwroofs:brown_terracotta_upper_lower_roof","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","mcwwindows:andesite_four_window","domum_ornamentum:light_blue_brick_extra","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","pneumaticcraft:armor_upgrade","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","mcwlights:festive_lantern","croptopia:fruit_salad","securitycraft:block_change_detector","supplementaries:pancake_fd","twigs:rocky_dirt","advanced_ae:advpatpro2","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","minecraft:netherite_scrap_from_blasting","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","mcwtrpdoors:bamboo_four_panel_trapdoor","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","botania:white_shiny_flower","mcwbridges:cherry_bridge_pier","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","connectedglass:scratched_glass1","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","handcrafted:blue_sheet","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwtrpdoors:birch_beach_trapdoor","createoreexcavation:vein_atlas","additionallanterns:quartz_chain","aether:stone_axe_repairing","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","mcwfurnitures:cherry_double_drawer","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","botania:dreamwood_slab","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","reliquary:shears_of_winter","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","minecraft:sandstone_slab","mcwwindows:birch_window","mcwfences:acacia_picket_fence","reliquary:uncrafting/wither_skeleton_skull","mcwfurnitures:warped_glass_table","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","ae2:network/cables/dense_covered_cyan","minecraft:gold_ingot_from_smelting_raw_gold","sophisticatedbackpacks:smithing_upgrade","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","pneumaticcraft:network_api","mcwdoors:birch_whispering_door","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","railcraft:logbook","ae2:misc/tiny_tnt","comforts:hammock_to_light_blue","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","mcwroofs:thatch_lower_roof","reliquary:ice_magus_rod","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","handcrafted:spruce_couch","botania:glass_pickaxe","twilightforest:nagastone/nagastone_spiral","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwwindows:jungle_blinds","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","travelersbackpack:melon","securitycraft:block_pocket_manager","mcwlights:soul_cherry_tiki_torch","botania:red_pavement","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","mcwlights:red_lamp","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","alltheores:lumium_dust_from_alloy_blending","bloodmagic:path/path_stone","cfm:purple_kitchen_drawer","botania:flower_bag","ad_astra:steel_plateblock","alltheores:brass_gear","pneumaticcraft:compressed_brick_wall","cfm:warped_upgraded_gate","twilightforest:berry_torch","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:cherry_striped_chair","aether:stone_sword_repairing","minecraft:netherite_chestplate_smithing","cfm:jungle_kitchen_counter","ae2:network/cables/dense_smart_red","pneumaticcraft:guard_drone","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","minecraft:quartz_stairs_from_quartz_block_stonecutting","mcwroofs:birch_lower_roof","mcwlights:light_blue_lamp","railcraft:bag_of_cement","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","domum_ornamentum:white_paper_extra","twilightforest:equipment/arctic_chestplate","mcwbiomesoplenty:fir_window2","dyenamics:bed/wine_bed_frm_white_bed","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","mcwwindows:dark_oak_plank_parapet","mcwwindows:spruce_log_parapet","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","minecraft:birch_pressure_plate","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","create:spruce_window","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","mcwdoors:cherry_glass_door","connectedglass:clear_glass_gray2","sfm:manager","mcwfurnitures:stripped_spruce_counter","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","travelersbackpack:fox","botania:mana_quartz","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","mcwroofs:spruce_lower_roof","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","littlelogistics:tee_junction_rail","comforts:sleeping_bag_to_red","allthemodium:smithing/allthemodium_sword_smithing","botania:livingwood_twig","mcwdoors:spruce_whispering_door","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","ae2:tools/network_tool","minecraft:bowl","deeperdarker:bloom_boat","mcwtrpdoors:birch_blossom_trapdoor","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","mcwtrpdoors:birch_barn_trapdoor","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwfurnitures:birch_chair","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","additionallanterns:normal_lantern_green","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","productivebees:stonecutter/wisteria_canvas_hive","mcwfurnitures:stripped_cherry_modern_wardrobe","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","minecraft:birch_planks","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfurnitures:stripped_warped_stool_chair","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwtrpdoors:print_paper","mcwdoors:jungle_swamp_door","minecraft:red_carpet","cfm:light_blue_kitchen_sink","cfm:stripped_birch_desk_cabinet","ad_astra:steel_panel","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","botania:dye_light_blue","domum_ornamentum:green_brick_extra","mcwfurnitures:cherry_glass_table","mcwfurnitures:stripped_cherry_desk","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","bloodmagic:enhanced_teleposer_focus","mcwwindows:stripped_crimson_stem_window2","croptopia:meringue","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwdoors:bamboo_waffle_door","mcwdoors:birch_barn_glass_door","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","handcrafted:birch_drawer","mcwbiomesoplenty:magic_horse_fence","alchemistry:reactor_output","sophisticatedstorage:basic_to_copper_tier_upgrade","mcwtrpdoors:birch_whispering_trapdoor","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwfurnitures:cherry_desk","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","mcwdoors:warped_swamp_door","cfm:green_picket_gate","railcraft:radio_circuit","ae2:tools/misctools_entropy_manipulator","twilightforest:mossy_underbrick","aether:netherite_pickaxe_repairing","pneumaticcraft:manual_compressor","botania:elementium_hoe","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","mcwtrpdoors:spruce_classic_trapdoor","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","mcwfurnitures:cherry_coffee_table","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","mcwtrpdoors:cherry_four_panel_trapdoor","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwroofs:gray_terracotta_steep_roof","create:cut_calcite_stairs_from_stone_types_calcite_stonecutting","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","minecraft:cooked_cod","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","ad_astra:steel_trapdoor","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","pneumaticcraft:manometer","pneumaticcraft:aerial_interface","securitycraft:alarm","ad_astra:launch_pad","cfm:magenta_picket_gate","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","ae2:network/blocks/energy_vibration_chamber","mcwfurnitures:birch_stool_chair","mcwfurnitures:warped_modern_desk","ae2:tools/fluix_shovel","mcwdoors:birch_japanese2_door","dyenamics:icy_blue_dye","pneumaticcraft:wall_lamp_inverted_magenta","mcwfurnitures:stripped_cherry_double_drawer_counter","modularrouters:sender_module_1_alt","botania:elementium_leggings","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","pneumaticcraft:smart_chest","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","comforts:sleeping_bag_red","additionallanterns:amethyst_lantern","utilitix:crude_furnace","dyenamics:peach_dye","cfm:warped_bedside_cabinet","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","mcwroofs:gray_steep_roof","mcwfurnitures:cherry_stool_chair","utilitarian:utility/birch_logs_to_pressure_plates","create:copper_shingles_from_ingots_copper_stonecutting","reliquary:mob_charm_belt","everythingcopper:copper_sword","twilightdelight:chocolate_wafer","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","mcwroofs:birch_planks_roof","productivebees:stonecutter/yucca_canvas_hive","cfm:stripped_birch_crate","sophisticatedstorage:jukebox_upgrade","mcwtrpdoors:jungle_classic_trapdoor","additionallanterns:normal_lantern_cyan","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","minecraft:light_blue_banner","mcwroofs:warped_roof","dyenamics:mint_concrete_powder","dankstorage:dank_2","aether:blue_cape_blue_wool","mcwfences:railing_nether_brick_wall","dankstorage:dank_4","mcwroofs:yellow_terracotta_upper_lower_roof","ae2:network/cables/glass_black","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","mcwroofs:birch_attic_roof","botania:polished_livingrock","immersiveengineering:crafting/wire_electrum","mcwwindows:warped_stem_window","mcwfurnitures:cherry_wardrobe","mcwtrpdoors:warped_glass_trapdoor","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","mcwroofs:thatch_upper_steep_roof","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","additionallanterns:gold_lantern","cfm:stripped_oak_crate","immersiveengineering:crafting/wirecutter","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","productivebees:expansion_boxes/expansion_box_warped_canvas","mcwfurnitures:stripped_warped_striped_chair","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","bloodmagic:path/path_wornstonetile","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","productivebees:stonecutter/spruce_canvas_hive","create:crafting/kinetics/brass_hand","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","mcwfurnitures:stripped_cherry_bookshelf_cupboard","botania:thunder_sword","mcwdoors:cherry_stable_head_door","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","immersiveengineering:crafting/plate_nickel_hammering","megacells:cells/portable/portable_item_cell_16m","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","productivebees:stonecutter/yucca_canvas_expansion_box","mcwfurnitures:birch_glass_table","mcwwindows:quartz_pane_window","bloodmagic:raw_hellforged_block","mcwtrpdoors:metal_trapdoor","immersiveengineering:crafting/conveyor_redstone","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:red_cushion","handcrafted:fancy_painting","mcwfurnitures:spruce_table","minecraft:pink_bed","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","bloodmagic:path/path_woodtile","cfm:jungle_bedside_cabinet","pneumaticcraft:air_grate_module","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:cyan_stained_glass_pane_from_glass_pane","create:cut_calcite_wall_from_stone_types_calcite_stonecutting","pneumaticcraft:minigun_upgrade","connectedglass:clear_glass_brown2","mcwwindows:stripped_cherry_log_window","minecraft:dye_red_carpet","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","minecraft:quartz_bricks","immersiveengineering:crafting/wirecoil_redstone","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","mcwbridges:balustrade_bricks_bridge","alltheores:nickel_rod","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","allthetweaks:atm_star_block","handcrafted:andesite_corner_trim","easy_villagers:iron_farm","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","minecraft:birch_door","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","bloodmagic:soul_forge","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","mcwfences:double_curved_metal_fence","cfm:dye_red_picket_gate","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","botania:elementium_helmet","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","ae2:network/parts/formation_plane_alt","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","cfm:green_picket_fence","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","mcwbridges:warped_bridge_pier","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:birch_fence_gate","cfm:stripped_oak_kitchen_drawer","mcwroofs:cherry_steep_roof","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwfurnitures:stripped_cherry_end_table","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","mcwtrpdoors:spruce_barn_trapdoor","mcwfurnitures:spruce_modern_wardrobe","productivebees:stonecutter/hellbark_canvas_hive","ae2:network/blocks/pattern_providers_interface_part","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwbridges:bamboo_bridge","twilightforest:equipment/block_and_chain","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","botania:terra_pick","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","mcwdoors:warped_western_door","botania:blood_pendant","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","mcwfurnitures:jungle_large_drawer","botania:monocle","pneumaticcraft:reinforced_air_canister","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_3","mcwroofs:cherry_upper_lower_roof","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","mcwfurnitures:warped_coffee_table","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","utilitarian:utility/warped_logs_to_stairs","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","bloodmagic:blood_rune_blank","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","minecraft:yellow_stained_glass_pane_from_glass_pane","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwfurnitures:spruce_drawer_counter","allthecompressed:compress/cobblestone_1x","minecolonies:apple_pie","mcwtrpdoors:bamboo_beach_trapdoor","mcwpaths:stone_running_bond_path","twigs:mossy_bricks_from_moss_block","cfm:stripped_birch_coffee_table","simplylight:illuminant_green_block_dyed","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","twilightdelight:cooking/torchberry_juice","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","deeperdarker:echo_chest_boat","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","pneumaticcraft:gun_ammo_ap","computercraft:wireless_modem_normal","farmersdelight:sweet_berry_cookie","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwfurnitures:stripped_warped_glass_table","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","ad_astra:zip_gun","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","mcwfurnitures:stripped_cherry_glass_table","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwroofs:cherry_top_roof","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","mcwfurnitures:cherry_modern_wardrobe","mcwfences:modern_stone_brick_wall","utilitarian:utility/cherry_logs_to_slabs","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","immersiveengineering:crafting/wirecoil_electrum","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwfurnitures:birch_lower_triple_drawer","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","mcwfurnitures:spruce_double_drawer","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","cfm:birch_coffee_table","travelersbackpack:magma_cube","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwfurnitures:stripped_birch_chair","mcwfurnitures:stripped_spruce_drawer_counter","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","mcwpaths:stone_windmill_weave_path","mcwfurnitures:cherry_drawer_counter","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","farmersdelight:cooking/glow_berry_custard","twigs:bloodstone","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","productivebees:expansion_boxes/expansion_box_oak_canvas","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","sophisticatedbackpacks:chipped/loom_table_upgrade","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","handcrafted:spruce_bench","twigs:spruce_table","mcwroofs:gutter_base_light_blue","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","ae2:network/wireless_crafting_terminal","immersiveengineering:crafting/conveyor_vertical","create:crafting/kinetics/orange_seat","mcwroofs:birch_upper_steep_roof","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwfurnitures:warped_covered_desk","minecraft:yellow_stained_glass","mcwdoors:cherry_western_door","chemlib:sodium_ingot_to_nugget","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","additionallanterns:normal_lantern_magenta","alltheores:steel_rod","botania:mana_gun","bigreactors:blasting/graphite_from_coal","sophisticatedbackpacks:stack_upgrade_tier_1","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","pneumaticcraft:compressed_iron_ingot_from_block","securitycraft:reinforced_white_stained_glass_pane_from_dye","sophisticatedbackpacks:restock_upgrade","farmersdelight:wheat_dough_from_eggs","securitycraft:reinforced_brown_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","minecraft:cooked_mutton","railways:crafting/smokestack_long","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","computercraft:wireless_modem_advanced","minecraft:lapis_block","connectedglass:tinted_borderless_glass_red2","twilightforest:material/smelted_ironwood_ingot","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","immersiveengineering:crafting/strip_curtain","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwfurnitures:birch_counter","dankstorage:3_to_4","mcwfurnitures:spruce_counter","utilitarian:snad/snad","ae2:tools/certus_quartz_cutting_knife","mcwfurnitures:warped_double_drawer_counter","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","dyenamics:amber_stained_glass","securitycraft:blacklist_module","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","bloodmagic:primitive_furnace_cell","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:spruce_slab","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","mcwpaths:birch_planks_path","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","cfm:stripped_warped_cabinet","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","productivebees:hives/advanced_acacia_canvas_hive","mcwfurnitures:stripped_jungle_covered_desk","travelersbackpack:snow","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","immersiveengineering:crafting/plate_lead_hammering","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","immersiveengineering:crafting/item_batcher","botania:manasteel_shears","sophisticatedstorage:chipped/botanist_workbench_upgrade","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","botania:elementium_pickaxe","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","irons_spellbooks:wandering_magician_chestplate","supplementaries:soap/piston","cfm:stripped_warped_bedside_cabinet","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","minecraft:soul_campfire","botania:clip","botania:red_string_interceptor","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","twilightdelight:obsidian","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","twilightforest:equipment/knightmetal_helmet","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwfurnitures:warped_double_wardrobe","mcwtrpdoors:jungle_barred_trapdoor","twigs:cracked_bricks","supplementaries:slice_map","productivebees:expansion_boxes/expansion_box_jungle_canvas","mcwdoors:warped_four_panel_door","littlelogistics:automatic_tee_junction_rail","appflux:insulating_resin","mcwbridges:brick_bridge_pier","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","cfm:stripped_spruce_desk_cabinet","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","blue_skies:trough","cfm:stripped_warped_chair","botania:lens_normal","mcwfences:end_brick_pillar_wall","computercraft:monitor_normal","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","mcwfurnitures:warped_stool_chair","delightful:knives/elementium_knife","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","mcwroofs:spruce_steep_roof","minecraft:purple_dye","mcwfurnitures:jungle_modern_chair","create:cut_calcite_brick_wall_from_stone_types_calcite_stonecutting","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","handcrafted:phantom_trophy","minecraft:quartz_slab","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","delightful:knives/nickel_knife","create:brass_bars_from_ingots_brass_stonecutting","mcwroofs:stone_bricks_roof","cfm:cyan_grill","mcwdoors:cherry_bark_glass_door","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","minecraft:quartz_pillar_from_quartz_block_stonecutting","mysticalagriculture:watering_can","mcwroofs:spruce_attic_roof","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:nether_bricks_roof","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","biomesoplenty:hellbark_chest_boat","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","mcwbridges:spruce_log_bridge_middle","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/windmill_sail","mcwdoors:warped_paper_door","farmersdelight:rice_bag","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","mcwfurnitures:stripped_warped_lower_triple_drawer","supplementaries:speaker_block","farmersdelight:chicken_sandwich","connectedglass:clear_glass_white_pane2","minecraft:polished_andesite","mcwfurnitures:stripped_warped_double_drawer","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwdoors:bamboo_stable_head_door","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","cfm:white_picket_fence","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","cfm:warped_chair","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","handcrafted:kitchen_hood","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwbridges:birch_log_bridge_middle","xnet:connector_blue_dye","twilightforest:equipment/knightmetal_axe","utilitarian:utility/jungle_logs_to_doors","create:polished_cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwroofs:black_lower_roof","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwfurnitures:stripped_oak_desk","mcwroofs:gutter_base_blue","immersiveengineering:crafting/treated_fence","mcwwindows:diorite_louvered_shutter","minecraft:gray_dye","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","computercraft:skull_dan200","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwfurnitures:stripped_spruce_bookshelf_drawer","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","botania:travel_belt","bloodmagic:blood_rune_acceleration","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","ae2:network/parts/energy_acceptor","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","additionallanterns:gold_chain","ae2:materials/advancedcard","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","mcwfences:curved_metal_fence_gate","mysticalagriculture:inferium_growth_accelerator","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","mcwfurnitures:warped_bookshelf_drawer","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwroofs:andesite_lower_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","create:calcite_from_stone_types_calcite_stonecutting","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","mcwfurnitures:stripped_birch_modern_wardrobe","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","handcrafted:spruce_desk","cfm:stripped_warped_coffee_table","farmersdelight:cooking/cooked_rice","mcwwindows:oak_plank_parapet","immersiveengineering:crafting/hempcrete","mcwfurnitures:oak_modern_desk","minecraft:spruce_wood","minecraft:iron_sword","botania:elementium_chestplate","mcwtrpdoors:birch_swamp_trapdoor","minecraft:spruce_fence","aether:leather_helmet_repairing","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","utilitarian:utility/birch_logs_to_trapdoors","railways:crafting/smokestack_caboosestyle","botania:runic_altar","additionallanterns:normal_lantern_red","domum_ornamentum:purple_brick_extra","minecraft:green_dye","quark:building/crafting/oak_ladder","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","minecraft:chiseled_sandstone_from_sandstone_stonecutting","ae2:tools/fluix_upgrade_smithing_template","mcwdoors:cherry_tropical_door","botania:petal_light_blue_double","comforts:hammock_red","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","twilightforest:canopy_boat","botania:quartz_red","sfm:network_tool","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","minecraft:birch_trapdoor","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","productivebees:stonecutter/maple_canvas_hive","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","create:birch_window","cfm:birch_park_bench","mcwroofs:gutter_middle_red","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","botania:abstruse_platform","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","pneumaticcraft:magnet_upgrade","mcwbridges:iron_bridge_pier","mcwwindows:mangrove_blinds","deeperdarker:bloom_chest_boat","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","sophisticatedstorage:spruce_chest","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","mcwfurnitures:warped_lower_triple_drawer","minecraft:powered_rail","productivebees:expansion_boxes/expansion_box_birch_canvas","botania:dye_pink","mcwdoors:birch_bamboo_door","mcwwindows:diorite_pane_window","travelersbackpack:dye_white_sleeping_bag","dyenamics:wine_stained_glass_pane_from_glass_pane","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","twigs:birch_table","enderio:resetting_lever_ten_from_prev","minecraft:dye_white_bed","mcwroofs:magenta_concrete_upper_lower_roof","botania:lens_flare","mcwtrpdoors:print_mystic","mcwroofs:light_gray_terracotta_steep_roof","immersiveengineering:crafting/cushion","mcwroofs:cyan_concrete_steep_roof","pneumaticcraft:gun_ammo_explosive","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","connectedglass:clear_glass_purple2","productivebees:stonecutter/comb_canvas_expansion_box","pneumaticcraft:stone_base","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwroofs:spruce_planks_upper_lower_roof","minecraft:gray_terracotta","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","cfm:stripped_spruce_coffee_table","mcwdoors:bamboo_mystic_door","mcwfurnitures:stripped_birch_drawer_counter","mcwroofs:pink_concrete_attic_roof","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","dyenamics:peach_terracotta","botania:livingrock_wall","minecraft:fishing_rod","xnet:connector_yellow_dye","minecraft:terracotta","delightful:knives/silver_knife","mcwwindows:acacia_blinds","aether:leather_chestplate_repairing","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:yellow_lamp","botania:blue_pavement","mcwroofs:roofing_hammer","mcwdoors:cherry_swamp_door","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","mcwfurnitures:stripped_warped_double_wardrobe","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","dimstorage:dimensional_tank","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","mcwfurnitures:cherry_covered_desk","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","immersiveengineering:crafting/buzzsaw","bloodmagic:largebloodstonebrick","forbidden_arcanus:dark_rune_from_dark_rune_block","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","productivebees:stonecutter/redwood_canvas_hive","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","minecraft:beacon","minecraft:tnt","mcwfurnitures:stripped_birch_bookshelf","mcwdoors:cherry_japanese2_door","minecraft:andesite_slab_from_andesite_stonecutting","mcwdoors:spruce_stable_door","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","utilitarian:utility/warped_logs_to_slabs","mcwfurnitures:stripped_warped_drawer","minecraft:flint_and_steel","railways:crafting/smokestack_streamlined","ad_astra:white_flag","mcwdoors:cherry_nether_door","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","cfm:spruce_upgraded_gate","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","cfm:stripped_birch_chair","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_birch_cabinet","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","farmersdelight:oak_cabinet","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","aiotbotania:terra_shovel","mcwfurnitures:spruce_cupboard_counter","mcwdoors:cherry_barn_door","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:hay","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwdoors:cherry_modern_door","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","bloodmagic:blood_rune_displacement","productivebees:expansion_boxes/expansion_box_snake_block_canvas","mcwroofs:warped_top_roof","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","mcwfurnitures:cherry_triple_drawer","rftoolsbase:tablet","mcwroofs:magenta_terracotta_lower_roof","megacells:cells/portable/portable_fluid_cell_64m","railcraft:routing_table_book","ad_astra:vent","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","cfm:stripped_warped_table","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:birch_planks_upper_lower_roof","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:item_shelf","handcrafted:terracotta_plate","supplementaries:stone_tile","reliquary:uncrafting/snowball","mcwroofs:light_blue_terracotta_roof","handcrafted:calcite_corner_trim","rftoolsbuilder:blue_shield_template_block","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","mcwfurnitures:stripped_warped_cupboard_counter","silentgear:coating_smithing_template","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","handcrafted:berry_jam_jar","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","mcwbridges:birch_rail_bridge","minecraft:glass_pane","supplementaries:timber_brace","alltheores:iridium_ingot_from_raw_blasting","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","handcrafted:birch_cupboard","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","railways:crafting/smokestack_oilburner","mcwroofs:warped_lower_roof","securitycraft:sonic_security_system","minecraft:clay","ae2:network/crafting/cpu_crafting_unit","mcwbiomesoplenty:pine_pane_window","mcwfurnitures:stripped_birch_lower_bookshelf_drawer","mcwroofs:magenta_concrete_roof","ad_astra:blue_flag","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","travelersbackpack:light_blue_sleeping_bag","securitycraft:protecto","immersiveengineering:crafting/conveyor_basic_covered","mcwpaths:andesite_basket_weave_paving","utilitix:jungle_shulker_boat","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","mcwfurnitures:stripped_birch_coffee_table","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","additionallanterns:normal_lantern_colorless","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_spruce_cupboard_counter","cfm:orange_kitchen_drawer","mcwroofs:spruce_planks_top_roof","ae2:materials/annihilationcore","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","handcrafted:spruce_pillar_trim","minecraft:leather_boots","railcraft:tank_detector","mcwdoors:warped_beach_door","create:crafting/appliances/netherite_diving_boots_from_netherite","railcraft:steel_spike_maul","handcrafted:sandstone_corner_trim","mcwfurnitures:stripped_warped_modern_chair","pneumaticcraft:classify_filter","immersiveengineering:crafting/balloon","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","mcwfurnitures:warped_cupboard_counter","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","mcwlights:pink_paper_lamp","pneumaticcraft:gun_ammo_incendiary","ad_astra:white_industrial_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","immersiveengineering:crafting/wire_aluminum","mcwfurnitures:birch_end_table","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","mcwfurnitures:cherry_lower_triple_drawer","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","mcwroofs:birch_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","minecraft:magma_block","mcwwindows:crimson_stem_parapet","minecraft:spruce_pressure_plate","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","nethersdelight:nether_brick_smoker","connectedglass:clear_glass_black_pane3","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","handcrafted:spruce_nightstand","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","aether:aether_iron_nugget_from_smelting","quark:tweaks/crafting/utility/tools/stone_hoe","sfm:cable","minecraft:hay_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:white_upper_steep_roof","alltheores:ruby_from_hammer_crushing","twilightforest:equipment/knightmetal_chestplate","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","botania:phantom_ink","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","railways:crafting/smokestack_coalburner","mcwfences:modern_granite_wall","mcwtrpdoors:warped_barrel_trapdoor","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","bloodmagic:primitive_hydration_cell","pneumaticcraft:etching_tank","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","bloodmagic:ritual_stone_master","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwroofs:blue_concrete_top_roof","bambooeverything:bamboo_ladder","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","mcwtrpdoors:warped_bark_trapdoor","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","mcwdoors:oak_waffle_door","bigreactors:blasting/graphite_from_charcoal","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","create:crafting/kinetics/rotation_speed_controller","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mysticalagriculture:inferium_gemstone","modularrouters:detector_module","mcwtrpdoors:warped_beach_trapdoor","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","railcraft:goggles","mcwdoors:birch_beach_door","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","domum_ornamentum:brick_extra","mcwdoors:birch_bark_glass_door","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","mcwfurnitures:birch_modern_desk","bloodmagic:blood_rune_aug_capacity","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","twilightforest:tf_to_vanilla_lilypad","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","pneumaticcraft:liquid_compressor","create:polished_cut_calcite_wall_from_stone_types_calcite_stonecutting","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwbiomesoplenty:empyreal_window2","mcwtrpdoors:bamboo_barrel_trapdoor","connectedglass:clear_glass_red_pane2","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","reliquary:uncrafting/redstone","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","ad_astra:red_flag","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/generator","ad_astra:small_red_industrial_lamp","immersiveengineering:crafting/cokebrick_from_slab","mcwwindows:warped_stem_four_window","securitycraft:sentry","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","handcrafted:white_crockery_combo","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","comforts:hammock_light_blue","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","twilightforest:compressed_blocks/arctic_block","mcwroofs:pink_terracotta_top_roof","cfm:warped_cabinet","create:crafting/logistics/redstone_contact","handcrafted:light_blue_cushion","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","mcwpaths:sandstone_crystal_floor_path","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","mcwtrpdoors:cherry_beach_trapdoor","mcwfurnitures:stripped_warped_wardrobe","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","ae2:tools/certus_quartz_spade","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","farmersdelight:skillet","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","megacells:cells/portable/portable_fluid_cell_256m","mcwlights:orange_paper_lamp","mcwtrpdoors:cherry_classic_trapdoor","mcwbiomesoplenty:stripped_hellbark_pane_window","twilightforest:equipment/fortification_scepter","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","ae2:network/cables/dense_covered_black","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","occultism:crafting/spirit_campfire","utilitix:piston_controller_rail","mcwroofs:bricks_upper_lower_roof","create:crafting/curiosities/peculiar_bell","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","delightful:knives/brass_knife","minecraft:white_stained_glass","connectedglass:clear_glass_light_gray2","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","immersiveengineering:crafting/metal_ladder_none","alltheores:aluminum_dust_from_hammer_crushing","securitycraft:taser","mcwdoors:warped_japanese_door","mcwfurnitures:stripped_warped_bookshelf_drawer","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","domum_ornamentum:white_brick_extra","aiotbotania:livingrock_hoe","connectedglass:scratched_glass_red_pane2","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","ae2:network/parts/toggle_bus","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","minecraft:blue_bed","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","mcwdoors:birch_tropical_door","croptopia:campfire_caramel","cfm:warped_table","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbridges:rope_spruce_bridge","mcwbiomesoplenty:stripped_hellbark_log_window2","allthecompressed:compress/deepslate_1x","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","mcwfurnitures:stripped_birch_double_wardrobe","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","botania:manasteel_shovel","handcrafted:oven","handcrafted:calcite_pillar_trim","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","minecraft:red_stained_glass","mcwroofs:white_steep_roof","minecraft:light_gray_stained_glass_pane_from_glass_pane","mcwpaths:cobbled_deepslate_crystal_floor_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwlights:jungle_tiki_torch","immersiveengineering:crafting/plate_aluminum_hammering","twigs:stone_column_stonecutting","cfm:lime_picket_gate","mcwfurnitures:stripped_warped_counter","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","sophisticatedbackpacks:tool_swapper_upgrade","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","minecraft:gray_stained_glass_pane_from_glass_pane","allthecompressed:compress/andesite_1x","cfm:birch_cabinet","mcwwindows:diorite_four_window","minecraft:stone_slab","mcwfurnitures:stripped_spruce_desk","pneumaticcraft:compressed_bricks","productivebees:stonecutter/comb_canvas_hive","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","create:small_calcite_brick_wall_from_stone_types_calcite_stonecutting","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","minecraft:blast_furnace","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","additionallanterns:normal_lantern_light_blue","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","minecraft:light_blue_stained_glass_pane_from_glass_pane","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","dyenamics:bed/mint_bed_frm_white_bed","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","mcwtrpdoors:warped_classic_trapdoor","mcwfurnitures:stripped_cherry_lower_triple_drawer","ae2:network/parts/import_bus","croptopia:campfire_molasses","minecraft:dye_red_bed","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","ae2:network/upgrade_wireless_crafting_terminal","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","minecraft:blue_stained_glass_pane_from_glass_pane","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwtrpdoors:bamboo_bark_trapdoor","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","silentgear:upgrade_base","croptopia:melon_juice","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mcwdoors:warped_whispering_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","botania:starfield","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","minecraft:oak_chest_boat","mcwdoors:spruce_waffle_door","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","pneumaticcraft:empty_pcb_from_failed_pcb","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","allthemodium:smithing/allthemodium_chestplate_smithing","mcwroofs:spruce_planks_lower_roof","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","botania:yellow_pavement","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","pneumaticcraft:logistics_core","create:crafting/kinetics/yellow_seat","mcwpaths:andesite_windmill_weave_path","comforts:hammock_blue","mcwdoors:spruce_swamp_door","modularrouters:blank_upgrade","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","mcwfurnitures:stripped_cherry_stool_chair","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","ae2:shaped/walls/sky_stone_block","cfm:birch_chair","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","cfm:stripped_spruce_cabinet","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","minecraft:mangrove_boat","minecraft:bread","mcwdoors:bamboo_whispering_door","mcwroofs:green_terracotta_steep_roof","mcwdoors:spruce_barn_glass_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","mcwdoors:garage_silver_door","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","cfm:birch_desk","mcwfurnitures:spruce_coffee_table","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","botania:horn_grass","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","undergarden:smoke_gloomper_leg","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","additionallanterns:normal_lantern_brown","handcrafted:red_sheet","minecraft:iron_shovel","tombstone:dark_marble","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwwindows:stone_brick_arrow_slit","ae2:smelting/smooth_sky_stone_block","chimes:amethyst_chimes","mcwfurnitures:jungle_desk","productivebees:stonecutter/willow_canvas_hive","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","croptopia:ham_sandwich","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","ad_astra:space_suit","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwwindows:dark_oak_louvered_shutter","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","alltheores:osmium_dust_from_hammer_ingot_crushing","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","minecraft:pink_carpet","botania:cacophonium","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","create:calcite_pillar_from_stone_types_calcite_stonecutting","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mythicbotany:ice_ring","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","handcrafted:birch_table","rftoolsutility:counter_module","aether:netherite_boots_repairing","botania:lens_redirect","cfm:stripped_jungle_crate","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwdoors:bamboo_barn_door","mcwlights:cyan_paper_lamp","mcwdoors:warped_japanese2_door","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:black_roof_block","handcrafted:spruce_dining_bench","mcwfurnitures:stripped_birch_wardrobe","botania:elementium_shovel","immersiveengineering:crafting/wooden_grip","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","mcwfurnitures:birch_table","mythicbotany:fire_ring","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwroofs:spruce_planks_roof","twilightforest:charm_of_keeping_2","mcwbiomesoplenty:stripped_hellbark_log_four_window","minecraft:spruce_door","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_birch_covered_desk","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","pneumaticcraft:huge_tank","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:cherry_end_table","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","modularrouters:dropper_module","twilightforest:lich_banner_pattern","mcwbiomesoplenty:magic_highley_gate","utilitarian:utility/birch_logs_to_doors","computercraft:wired_modem","ae2:tools/fluix_sword","utilitarian:utility/cherry_logs_to_trapdoors","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","dyenamics:mint_terracotta","mcwwindows:pink_curtain","sophisticatedstorage:birch_limited_barrel_1","mcwlights:soul_jungle_tiki_torch","productivebees:stonecutter/rosewood_canvas_expansion_box","mcwwindows:light_gray_curtain","sophisticatedstorage:birch_limited_barrel_4","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","sophisticatedstorage:birch_limited_barrel_2","twilightforest:equipment/knightmetal_boots","delightful:knives/refined_obsidian_knife","sophisticatedstorage:birch_limited_barrel_3","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","twilightforest:knight_phantom_banner_pattern","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwfurnitures:stripped_cherry_counter","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","ae2:network/blocks/spatial_io_port","mcwdoors:bamboo_swamp_door","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","deepresonance:radiation_suit_boots","supplementaries:wrench","mcwroofs:blue_terracotta_upper_steep_roof","minecraft:bow","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwroofs:cherry_lower_roof","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","chemlib:periodic_table","botania:mushroom_stew","minecraft:andesite_stairs","delightful:knives/aluminum_knife","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwdoors:bamboo_japanese_door","croptopia:apple_juice","cfm:purple_kitchen_sink","cfm:black_cooler","minecraft:jungle_slab","twigs:polished_calcite_stonecutting","supplementaries:sconce","silentgear:rough_rod","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","dyenamics:maroon_stained_glass_pane_from_glass_pane","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:birch_top_roof","aether:skyroot_bed","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","mcwfurnitures:spruce_glass_table","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","dyenamics:bed/aquamarine_bed_frm_white_bed","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:jacaranda_plank_window2","sophisticatedbackpacks:feeding_upgrade","computercraft:skull_cloudy","minecraft:melon_seeds","minecraft:granite","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwfurnitures:spruce_covered_desk","botania:ghost_rail","minecraft:firework_rocket_simple","pneumaticcraft:logistics_drone","sophisticatedstorage:smoking_upgrade","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","mcwdoors:spruce_mystic_door","mcwtrpdoors:spruce_beach_trapdoor","aether:netherite_shovel_repairing","enderio:enderios","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","mcwbridges:balustrade_nether_bricks_bridge","pneumaticcraft:wall_lamp_inverted_brown","cfm:stripped_birch_bedside_cabinet","botania:black_pavement","mcwroofs:andesite_roof","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","supplementaries:pedestal","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","ae2:network/cables/glass_yellow","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","minecraft:dye_white_wool","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","dyenamics:rose_terracotta","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","mcwfurnitures:stripped_birch_triple_drawer","handcrafted:silverfish_trophy","bloodmagic:blood_rune_charging","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwdoors:jungle_tropical_door","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","cfm:lime_kitchen_counter","mcwdoors:warped_barn_door","travelersbackpack:red_sleeping_bag","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","comforts:sleeping_bag_blue","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","botania:mana_ring_greater","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","tombstone:carmin_marble","botania:elf_quartz","mcwdoors:oak_barn_door","botania:alchemy_catalyst","mcwfurnitures:spruce_double_drawer_counter","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","connectedglass:clear_glass_black2","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","mcwpaths:sandstone_windmill_weave_slab","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","botania:conversions/elementium_block_deconstruct","productivebees:stonecutter/hellbark_canvas_expansion_box","immersiveengineering:crafting/ersatz_leather","bloodmagic:ritual_stone_blank","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","twilightdelight:torchberries_crate","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","alltheores:lead_plate","mcwfurnitures:stripped_spruce_coffee_table","additionallanterns:obsidian_chain","minecraft:ender_eye","mcwfurnitures:spruce_double_wardrobe","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwbiomesoplenty:umbran_plank_window","minecraft:birch_slab","enderio:resetting_lever_three_hundred","mcwdoors:warped_modern_door","mcwlights:cross_lantern","mcwlights:brown_lamp","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwwindows:bricks_four_window","railcraft:receiver_circuit","securitycraft:reinforced_packed_mud","immersiveengineering:crafting/hemp_fabric","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","bigreactors:fluidizer/fluidinjector","mcwtrpdoors:birch_tropical_trapdoor","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","rftoolsbase:manual","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","immersiveengineering:crafting/conveyor_dropper","sophisticatedstorage:feeding_upgrade","mcwfurnitures:cherry_bookshelf_drawer","ae2:network/parts/terminals_pattern_access","deepresonance:radiation_suit_leggings","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","mcwfurnitures:birch_triple_drawer","mcwdoors:print_mystic","ae2:network/blocks/io_condenser","sophisticatedbackpacks:smoking_upgrade","supplementaries:stonecutting/stone_tile","railcraft:steel_tank_wall","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","additionallanterns:normal_lantern_purple","handcrafted:birch_shelf","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","securitycraft:trophy_system","botania:elementium_axe","twilightforest:firefly_jar","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwfurnitures:stripped_cherry_wardrobe","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","twilightdelight:torchberry_cookie","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","farmersdelight:pie_crust","rftoolsbuilder:vehicle_card","mcwlights:warped_tiki_torch","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","railcraft:age_detector","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","botania:drum_gathering","mcwwindows:jungle_plank_window2","ad_astra:space_pants","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","mcwfurnitures:stripped_birch_end_table","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","productivebees:hives/advanced_dark_oak_canvas_hive","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","twigs:calcite_stairs","enderio:resetting_lever_five_inv","handcrafted:spruce_corner_trim","mcwdoors:garage_white_door","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwfurnitures:stripped_birch_modern_desk","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","bloodmagic:ritual_reader","pneumaticcraft:pressure_gauge_module","botania:turntable","cfm:stripped_birch_table","ae2:network/cells/item_storage_components_cell_1k_part","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","utilitarian:utility/warped_logs_to_trapdoors","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","supplementaries:daub","alltheores:zinc_dust_from_hammer_crushing","minecraft:netherite_scrap","railcraft:steel_chestplate","mcwfurnitures:stripped_jungle_end_table","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","mcwdoors:spruce_japanese2_door","minecraft:cut_sandstone","handcrafted:pink_cushion","mcwroofs:oak_planks_roof","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","botania:petal_white","ae2:decorative/quartz_block","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwfurnitures:birch_drawer_counter","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:nether_brick_slab","mcwfurnitures:birch_bookshelf","mcwbiomesoplenty:hellbark_highley_gate","create:crafting/logistics/redstone_link","immersiveengineering:crafting/treated_scaffold","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","botania:mana_tablet","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","connectedglass:borderless_glass_white_pane2","minecraft:campfire","dyenamics:peach_stained_glass_pane_from_glass_pane","allthearcanistgear:unobtainium_boots_smithing","mcwfurnitures:stripped_warped_modern_wardrobe","travelersbackpack:standard_no_tanks","cfm:white_kitchen_sink","securitycraft:reinforced_birch_fence","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","megacells:cells/portable/portable_item_cell_256m","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:logistics_configurator","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["botania:star_sword","botania:dreamwood_planks","mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","croptopia:mead","botania:alfheim_portal","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwdoors:bamboo_stable_door","pneumaticcraft:chunkloader_upgrade","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","allthecompressed:compress/calcite_1x","minecraft:bone_block","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","minecraft:melon","mcwfurnitures:warped_table","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","ad_astra:light_blue_flag","create:oak_window","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","mcwfurnitures:cherry_chair","create:crafting/kinetics/light_gray_seat","sophisticatedbackpacks:void_upgrade","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","buildinggadgets2:gadget_cut_paste","reliquary:mob_charm_fragments/cave_spider","mcwfurnitures:spruce_modern_desk","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","mcwfurnitures:spruce_drawer","alltheores:brass_plate","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","aether:iron_gloves_repairing","railcraft:controller_circuit","handcrafted:deepslate_corner_trim","minecraft:spruce_sign","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","aether:skyroot_fletching_table","create:crafting/kinetics/gearbox","botania:terrasteel_block","create:crafting/logistics/brass_tunnel","sophisticatedstorage:copper_to_iron_tier_upgrade","handcrafted:spruce_side_table","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","minecraft:light_blue_bed","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","comforts:sleeping_bag_to_blue","sliceanddice:slicer","minecraft:nether_brick_stairs","pneumaticcraft:crop_support","botania:elementium_sword","supplementaries:bubble_blower","naturalist:glow_goop","mcwtrpdoors:warped_barn_trapdoor","dyenamics:conifer_stained_glass_pane_from_glass_pane","supplementaries:crank","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","minecraft:magenta_dye_from_blue_red_white_dye","alltheores:iron_plate","cfm:stripped_birch_park_bench","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","productivebees:stonecutter/fir_canvas_expansion_box","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","minecraft:pink_terracotta","mcwdoors:cherry_bamboo_door","aiotbotania:livingrock_axe","mcwbiomesoplenty:mahogany_plank_pane_window","twigs:bamboo_thatch","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwtrpdoors:spruce_barred_trapdoor","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","supplementaries:candle_holders/candle_holder_white_dye","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","mcwfurnitures:warped_modern_wardrobe","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","productivebees:hives/advanced_cherry_canvas_hive","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","allthecompressed:compress/quartz_block_1x","railcraft:standard_rail_from_rail","twilightdelight:torchberry_pie","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","mcwtrpdoors:warped_cottage_trapdoor","cfm:oak_kitchen_counter","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","minecraft:golden_hoe","minecraft:fire_charge","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:pink_petal_block","pneumaticcraft:tag_workbench","alltheores:platinum_dust_from_hammer_crushing","cfm:stripped_spruce_bedside_cabinet","railcraft:any_detector","alchemistry:reactor_energy","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","pneumaticcraft:reinforced_chest_kit","sophisticatedstorage:controller","computercraft:printer","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","biomesoplenty:palm_chest_boat","mcwtrpdoors:bamboo_mystic_trapdoor","minecraft:stone_button","mcwfurnitures:stripped_cherry_cupboard_counter","mcwfurnitures:birch_desk","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","pneumaticcraft:logistics_frame_active_provider_self","minecraft:lodestone","occultism:crafting/magic_lamp_empty","handcrafted:birch_counter","mcwlights:black_lamp","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","mcwdoors:spruce_four_panel_door","mcwpaths:sandstone_running_bond_stairs","domum_ornamentum:blue_brick_extra","cfm:stripped_spruce_chair","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","mcwfurnitures:stripped_warped_bookshelf","aether:red_cape","minecraft:nether_brick_wall","mcwtrpdoors:cherry_glass_trapdoor","mcwfurnitures:stripped_warped_drawer_counter","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","mcwdoors:cherry_japanese_door","connectedglass:borderless_glass_red2","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","productivebees:expansion_boxes/expansion_box_acacia_canvas","dyenamics:lavender_dye","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwdoors:jungle_bark_glass_door","mcwtrpdoors:cherry_barred_trapdoor","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","botania:open_bucket","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","cfm:stripped_crimson_mail_box","tombstone:grave_plate","handcrafted:jungle_side_table","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","mcwtrpdoors:warped_tropical_trapdoor","mcwroofs:warped_attic_roof","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","ae2:network/parts/terminals","securitycraft:keypad_frame","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","littlelogistics:barrel_barge","botania:terrasteel_leggings","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","cfm:spruce_bedside_cabinet","botania:incense_stick","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","allthecompressed:compress/spirited_crystal_block_1x","pneumaticcraft:vortex_tube","supplementaries:candle_holders/candle_holder_red_dye","xnet:connector_routing","mcwwindows:warped_louvered_shutter","undergarden:wigglewood_chest_boat","blue_skies:glowing_nature_stone","handcrafted:birch_nightstand","minecraft:blue_carpet","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","aether:iron_shovel_repairing","travelersbackpack:cake","bloodmagic:ritual_diviner_1","bloodmagic:ritual_diviner_0","mcwroofs:green_terracotta_attic_roof","productivebees:stonecutter/palm_canvas_expansion_box","minecraft:birch_stairs","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","immersiveengineering:crafting/shield","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","mcwdoors:birch_stable_head_door","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","sophisticatedstorage:birch_barrel","immersiveengineering:crafting/blastbrick","mcwfurnitures:stripped_spruce_double_drawer","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","farmersdelight:milk_bottle","ae2:network/blocks/storage_chest","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:mangrove_chest_boat","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","handcrafted:blue_cushion","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","mcwroofs:light_blue_concrete_lower_roof","mcwfurnitures:jungle_drawer","utilitarian:utility/cherry_logs_to_stairs","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwfurnitures:birch_striped_chair","mcwroofs:thatch_roof","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","pneumaticcraft:advanced_liquid_compressor","connectedglass:clear_glass_light_blue2","cfm:mangrove_kitchen_sink_dark","croptopia:mashed_potatoes","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","mcwfences:blackstone_brick_railing_gate","mcwtrpdoors:bamboo_tropical_trapdoor","mcwroofs:cherry_attic_roof","sophisticatedbackpacks:filter_upgrade","pneumaticcraft:gun_ammo_freezing","aether:skyroot_barrel","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","megacells:cells/portable/portable_fluid_cell_4m","mcwfurnitures:stripped_cherry_table","minecraft:iron_ingot_from_nuggets","ae2:network/cables/dense_covered_purple","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwroofs:warped_upper_steep_roof","mcwpaths:cobbled_deepslate_flagstone_stairs","mcwdoors:birch_glass_door","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","twilightforest:material/blasted_ironwood_ingot","mcwfurnitures:cherry_counter","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","productivebees:hives/advanced_oak_canvas_hive","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","handcrafted:light_blue_sheet","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","additionallanterns:normal_lantern_pink","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","mcwfurnitures:oak_double_drawer_counter","cfm:blue_grill","mcwlights:golden_chandelier","minecraft:armor_stand","mcwpaths:stone_flagstone_stairs","chemlib:sodium_block_to_ingot","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","pneumaticcraft:compressed_bricks_from_tile_stonecutting","twigs:chiseled_bricks","enderio:stone_gear","cfm:pink_sofa","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","mcwroofs:nether_bricks_upper_lower_roof","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","cfm:birch_bedside_cabinet","mcwroofs:blue_terracotta_roof","xnet:netcable_yellow","mcwlights:tavern_wall_lantern","mcwfurnitures:jungle_wardrobe","croptopia:cheese","dyenamics:bed/rose_bed","mcwfurnitures:stripped_cherry_lower_bookshelf_drawer","cfm:red_sofa","minecraft:warped_hyphae","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","megacells:cells/portable/portable_fluid_cell_1m","cfm:stripped_warped_crate","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwroofs:spruce_top_roof","supplementaries:bomb","dyenamics:navy_stained_glass","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","additionallanterns:normal_lantern_orange","mcwfurnitures:stripped_birch_drawer","mysticalagriculture:inferium_farmland_till","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","create:cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","mcwfurnitures:stripped_oak_lower_triple_drawer","utilitarian:utility/oak_logs_to_boats","croptopia:potato_chips","minecraft:chiseled_stone_bricks_stone_from_stonecutting","pneumaticcraft:compressed_brick_pillar","mcwfurnitures:stripped_birch_double_drawer_counter","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","chemlib:sodium_ingot_from_blasting_sodium_dust","botania:red_string_fertilizer","minecraft:netherite_sword_smithing","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","create:crafting/kinetics/mechanical_crafter","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwfurnitures:stripped_warped_table","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","mcwfurnitures:cherry_double_wardrobe","handcrafted:spruce_counter","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","mcwfurnitures:stripped_cherry_bookshelf_drawer","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","twilightforest:charm_of_life_2","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","connectedglass:borderless_glass_pane1","croptopia:buttered_toast","mcwtrpdoors:birch_mystic_trapdoor","mcwfurnitures:birch_bookshelf_cupboard","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","cfm:pink_cooler","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","cfm:red_grill","create:small_calcite_brick_slab_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","croptopia:chicken_and_dumplings","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","utilitix:tiny_charcoal_to_tiny","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","pneumaticcraft:uv_light_box","twigs:quartz_column","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","croptopia:sushi","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwdoors:warped_barn_glass_door","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwtrpdoors:cherry_tropical_trapdoor","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","mcwbridges:spruce_rail_bridge","mcwpaths:sandstone_windmill_weave","mcwtrpdoors:warped_whispering_trapdoor","immersiveengineering:crafting/toolbox","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","sophisticatedstorage:spruce_limited_barrel_3","botania:terrasteel_boots","sophisticatedstorage:spruce_limited_barrel_2","sophisticatedstorage:spruce_limited_barrel_1","mcwwindows:crimson_shutter","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","sophisticatedstorage:spruce_limited_barrel_4","pneumaticcraft:air_cannon","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","travelersbackpack:pink_sleeping_bag","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","railcraft:blast_furnace_bricks","mcwfurnitures:cherry_large_drawer","ae2:network/cables/dense_covered_pink","dyenamics:amber_stained_glass_pane_from_glass_pane","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","computercraft:printed_book","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","bloodmagic:bloodstonebrick","reliquary:uncrafting/glass_bottle","botania:red_string_relay","ae2:tools/portable_item_cell_256k","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwfurnitures:spruce_stool_chair","mcwroofs:purple_terracotta_upper_lower_roof","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwbiomesoplenty:stripped_hellbark_log_window","blue_skies:cake_compat","mcwlights:reinforced_torch","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwdoors:spruce_stable_head_door","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","productivebees:stonecutter/aspen_canvas_expansion_box","pneumaticcraft:liquid_hopper","handcrafted:wood_cup","botania:green_pavement","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","bloodmagic:path/path_stonetile","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","twilightforest:equipment/knightmetal_pickaxe","mcwroofs:white_concrete_roof","ad_astra:ti_69","additionallanterns:normal_nether_bricks_chain","mcwwindows:window_half_bar_base","megacells:cells/portable/portable_item_cell_4m","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwdoors:birch_modern_door","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","irons_spellbooks:wandering_magician_leggings","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","croptopia:fried_chicken","railcraft:sheep_detector","minecraft:magenta_dye_from_blue_red_pink","croptopia:steamed_rice","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","bloodmagic:experience_tome","cfm:birch_upgraded_gate","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","xnet:advanced_connector_yellow","ae2:network/parts/terminals_crafting","twigs:calcite_wall","create:deepslate_from_stone_types_deepslate_stonecutting","megacells:cells/portable/portable_item_cell_1m","cfm:stripped_oak_chair","xnet:netcable_blue","cfm:diving_board","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","cfm:rock_path","botania:mana_spreader","connectedglass:scratched_glass_black1","connectedglass:clear_glass_yellow2","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","handcrafted:white_cup","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","ae2:network/cables/glass_gray","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","botania:lens_tripwire","minecraft:dye_red_wool","mcwroofs:nether_bricks_top_roof","productivebees:stonecutter/jungle_canvas_hive","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwroofs:nether_bricks_lower_roof","mcwroofs:cyan_concrete_top_roof","twilightforest:wood/spruce_banister","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","bloodmagic:lava_crystal","allthecompressed:compress/birch_log_1x","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwtrpdoors:birch_bark_trapdoor","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_middle_brown","mcwdoors:cherry_four_panel_door","mcwfurnitures:birch_modern_chair","mcwroofs:gutter_base_green","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","mcwwindows:crimson_planks_four_window","minecraft:piston","ad_astra:large_gas_tank","pneumaticcraft:harvesting_drone","mcwlights:white_lamp","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","allthemodium:allthemodium_ingot_from_raw_smelting","undergarden:regalium_block","mcwbiomesoplenty:umbran_plank_pane_window","connectedglass:borderless_glass_red_pane2","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwdoors:bamboo_four_panel_door","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwroofs:gray_upper_steep_roof","productivetrees:wood/brown_amber_wood","botania:third_eye","mcwroofs:birch_planks_steep_roof","mcwwindows:metal_curtain_rod","croptopia:apple_sapling","farmersdelight:fried_egg_from_campfire_cooking","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","mcwfurnitures:stripped_cherry_double_wardrobe","croptopia:beef_jerky","handcrafted:oak_shelf","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","cfm:stripped_birch_kitchen_counter","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","mcwtrpdoors:spruce_mystic_trapdoor","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","ad_astra:small_white_industrial_lamp","create:crafting/kinetics/copper_valve_handle","computercraft:speaker","mcwwindows:mangrove_louvered_shutter","ad_astra:space_helmet","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","mcwfurnitures:birch_covered_desk","aether:blue_cape_light_blue_wool","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwroofs:black_terracotta_top_roof","mcwdoors:warped_nether_door","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","handcrafted:creeper_trophy","travelersbackpack:skeleton","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","productivebees:stonecutter/oak_canvas_expansion_box","dyenamics:banner/bubblegum_banner","farmersdelight:stove","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","handcrafted:birch_side_table","packingtape:tape","mcwfurnitures:stripped_cherry_coffee_table","mcwdoors:print_waffle","rftoolsbuilder:vehicle_control_module","utilitarian:utility/spruce_logs_to_pressure_plates","cfm:birch_kitchen_counter","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","minecraft:light_blue_carpet","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","botania:fire_rod","pneumaticcraft:large_tank","botania:petal_pink_double","mcwdoors:birch_mystic_door","securitycraft:reinforced_crimson_fence_gate","pneumaticcraft:spawner_core_shell","mcwfurnitures:stripped_jungle_wardrobe","undergarden:smelt_gloomper_leg","mcwbiomesoplenty:rainbow_birch_hedge","utilitarian:utility/birch_logs_to_stairs","immersiveengineering:crafting/pickaxe_steel","productivebees:expansion_boxes/expansion_box_crimson_canvas","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","mcwfurnitures:jungle_stool_chair","mcwfurnitures:birch_double_drawer","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","botania:elementium_shears","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","minecraft:red_banner","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","create:polished_cut_calcite_from_stone_types_calcite_stonecutting","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","mcwfurnitures:stripped_birch_double_drawer","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:warped_park_bench","twilightforest:tf_moss_to_vanilla","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwfurnitures:stripped_birch_glass_table","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","pylons:expulsion_pylon","botania:manasteel_chestplate","mcwfurnitures:cherry_bookshelf","mcwfences:bamboo_highley_gate","mythicbotany:yggdrasil_branch","alltheores:brass_rod","twilightforest:equipment/arctic_helmet","mcwroofs:red_terracotta_attic_roof","bloodmagic:arc","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","dyenamics:bed/ultramarine_bed_frm_white_bed","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","create:crafting/kinetics/sequenced_gearshift","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","cfm:light_gray_grill","mcwtrpdoors:cherry_mystic_trapdoor","create:crafting/kinetics/black_seat","cfm:oak_desk","cfm:warped_crate","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","mcwfurnitures:birch_large_drawer","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwfurnitures:warped_large_drawer","minecolonies:potato_soup","chemlib:sodium_ingot_from_smelting_sodium_dust","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:lavender_stained_glass","dyenamics:cherenkov_dye","twilightforest:time_boat","minecraft:sandstone_wall","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","utilitarian:utility/cherry_logs_to_boats","alchemistry:fusion_chamber_controller","securitycraft:briefcase","mcwwindows:green_mosaic_glass","create:crafting/logistics/stockpile_switch","mcwbridges:dry_bamboo_bridge_pier","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","alltheores:electrum_rod","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","croptopia:butter","connectedglass:scratched_glass_red2","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwfurnitures:warped_drawer","ae2:network/cables/dense_covered_white","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","botania:red_string_dispenser","mcwroofs:sandstone_lower_roof","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","handcrafted:jungle_dining_bench","cfm:red_cooler","botania:dreamwood_wall","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfurnitures:stripped_cherry_large_drawer","twilightdelight:knightmetal_knife","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","mcwpaths:sandstone_basket_weave_paving","mcwdoors:cherry_mystic_door","aether:golden_pendant","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_1","minecraft:allthemodium_spell_book_smithing","sophisticatedstorage:stack_upgrade_tier_2","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","ae2:decorative/quartz_fixture","mcwtrpdoors:cherry_paper_trapdoor","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","littlelogistics:automatic_switch_rail","quark:tweaks/crafting/utility/tools/stone_axe","alltheores:electrum_plate","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","aether:netherite_helmet_repairing","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","immersiveengineering:crafting/cokebrick_to_slab","mcwroofs:white_concrete_top_roof","ad_astra:pink_flag","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","mcwfurnitures:warped_chair","simplylight:illuminant_purple_block_on_dyed","cfm:blue_sofa","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","productivetrees:planks/brown_amber_planks","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwtrpdoors:birch_barred_trapdoor","mcwfurnitures:jungle_striped_chair","pneumaticcraft:tube_junction","bloodmagic:smelting/blasting_ingot_from_raw_hellforged","bambooeverything:bamboo_torch","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","mcwdoors:birch_waffle_door","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","utilitarian:utility/cherry_logs_to_doors","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bloodmagic:alchemy_table","mcwlights:redstone_lamp_slab","mcwfurnitures:cherry_bookshelf_cupboard","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","bloodmagic:blood_rune_speed","mcwbiomesoplenty:jacaranda_plank_pane_window","bloodmagic:path/path_wornstone","mcwroofs:cherry_upper_steep_roof","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","expatternprovider:silicon_block","mcwfurnitures:warped_modern_chair","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","itemcollectors:basic_collector","minecraft:loom","supplementaries:sign_post_spruce","ad_astra:steel_engine","enderio:resetting_lever_three_hundred_inv","mcwtrpdoors:spruce_barrel_trapdoor","immersiveengineering:crafting/connector_mv_relay","bloodmagic:blood_rune_capacity","railcraft:steel_axe","twilightdelight:ironwood_knife","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","mcwroofs:light_gray_concrete_steep_roof","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:charging_module","pneumaticcraft:item_life_upgrade","travelersbackpack:cow","allthemodium:smithing/allthemodium_shovel_smithing","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwfurnitures:stripped_cherry_chair","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","cfm:pink_picket_fence","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwfurnitures:stripped_cherry_triple_drawer","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","alltheores:nickel_plate","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","utilitarian:utility/spruce_logs_to_stairs","bloodmagic:smelting/ingot_from_raw_hellforged","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","mcwdoors:cherry_beach_door","dyenamics:bed/maroon_bed_frm_white_bed","travelersbackpack:dye_red_sleeping_bag","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","railways:crafting/smokestack_woodburner","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","cfm:dye_red_picket_fence","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","cfm:brown_cooler","delightful:knives/terra_knife","xnet:netcable_red","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","megacells:cells/portable/portable_item_cell_64m","minecraft:spruce_trapdoor","mcwwindows:dark_prismarine_parapet","rftoolsutility:inventoryplus_module","productivebees:hives/advanced_snake_block_canvas_hive","bigreactors:reprocessor/wasteinjector","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","cfm:birch_upgraded_fence","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","dyenamics:persimmon_stained_glass_pane_from_glass_pane","handcrafted:birch_corner_trim","aether:golden_leggings_repairing","mcwfurnitures:stripped_birch_lower_triple_drawer","computercraft:disk_drive","mcwroofs:blue_striped_awning","mcwfurnitures:spruce_desk","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","aether:golden_helmet_repairing","alchemistry:dissolver","comforts:hammock_to_blue","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwtrpdoors:spruce_swamp_trapdoor","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","sophisticatedbackpacks:compacting_upgrade","cfm:gray_kitchen_counter","minecraft:snow_block","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","handcrafted:spruce_cupboard","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","twilightforest:compressed_blocks/knightmetal_block","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:sandstone_stairs","minecraft:composter","mcwfurnitures:stripped_cherry_modern_chair","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","handcrafted:birch_dining_bench","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","mcwfurnitures:stripped_cherry_drawer","domum_ornamentum:cyan_floating_carpet","mcwfurnitures:cherry_modern_chair","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","mcwfurnitures:stripped_warped_covered_desk","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","create:crafting/logistics/brass_funnel","minecraft:fermented_spider_eye","railcraft:coal_coke_block_from_coal_coke","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwdoors:spruce_classic_door","mcwdoors:birch_nether_door","supplementaries:flags/flag_black","pneumaticcraft:vacuum_trap","mcwwindows:acacia_louvered_shutter","mcwdoors:print_whispering","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions","minecraft:cobbled_deepslate_slab","aether:golden_sword_repairing","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","railways:crafting/smokestack_diesel","mcwroofs:gutter_base_orange","aether:bow_repairing","mcwfurnitures:stripped_spruce_stool_chair","securitycraft:reinforced_light_gray_stained_glass","farmersdelight:cooking/pasta_with_mutton_chop","minecraft:chiseled_quartz_block","enderio:silent_oak_pressure_plate","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","allthemodium:allthemodium_dust_from_ore_crushing","mcwroofs:bricks_attic_roof","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","mcwfurnitures:warped_drawer_counter","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","reliquary:uncrafting/spider_eye","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","create:crafting/logistics/display_link","ad_astra:airlock","mcwroofs:grass_steep_roof","cfm:pink_picket_gate","alchemistry:combiner","occultism:crafting/brush","mcwroofs:birch_steep_roof","ae2:network/cables/glass_white","mcwdoors:jungle_stable_head_door","croptopia:egg_roll","mcwtrpdoors:cherry_barrel_trapdoor","twigs:bamboo_mat","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","sophisticatedstorage:chipped/loom_table_upgrade","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","botania:lens_firework","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","twilightforest:equipment/knightmetal_leggings","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","minecraft:lime_dye","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","cfm:warped_blinds","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:stripped_redwood_log_window","botania:quartz_sunny","productivebees:stonecutter/maple_canvas_expansion_box","botania:terrasteel_helmet","handcrafted:birch_chair","railcraft:signal_lamp","mcwfurnitures:stripped_warped_large_drawer","cfm:jungle_blinds","reliquary:uncrafting/sugar","tombstone:bone_needle","minecraft:stone_brick_wall","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_cherry_covered_desk","xnet:netcable_green","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:decoration_table","sophisticatedstorage:jungle_limited_barrel_4","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","sophisticatedstorage:jungle_limited_barrel_3","mcwdoors:warped_bamboo_door","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","mcwtrpdoors:cherry_cottage_trapdoor","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","connectedglass:scratched_glass_white_pane2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwfurnitures:stripped_warped_coffee_table","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","enderio:stone_gear_upgrade","botania:light_blue_petal_block","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","immersiveengineering:crafting/glider","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","minecraft:gold_block","botania:floating_pure_daisy","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","cfm:magenta_picket_fence","minecraft:cherry_wood","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","mcwfurnitures:stripped_birch_striped_chair","botania:conversions/terrasteel_block_deconstruct","cfm:spruce_kitchen_counter","travelersbackpack:warden","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","dyenamics:fluorescent_concrete_powder","botania:incense_plate","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","twilightforest:canopy_chest_boat","comforts:sleeping_bag_light_blue","mcwpaths:spruce_planks_path","bambooeverything:dry_bamboo","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","easy_villagers:breeder","railways:stonecutting/riveted_locometal","securitycraft:reinforced_observer","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","mcwdoors:spruce_nether_door","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:stripped_warped_desk","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","mcwfences:acacia_curved_gate","allthemodium:unobtainium_rod","mcwfurnitures:stripped_warped_double_drawer_counter","supplementaries:deepslate_lamp","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:stripped_birch_desk","farmersdelight:cooking/baked_cod_stew","immersiveengineering:crafting/wirecoil_structure_rope","mcwfurnitures:oak_bookshelf","utilitix:stonecutter_cart","alltheores:lead_rod","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","minecraft:iron_block","farmersdelight:spruce_cabinet","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","cfm:birch_blinds","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","allthecompressed:compress/spruce_planks_1x","mcwbridges:bamboo_bridge_pier","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","domum_ornamentum:blockbarreldeco_onside","minecraft:flower_pot","minecraft:diamond_pickaxe","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","productivebees:hives/advanced_jungle_canvas_hive","pneumaticcraft:reinforced_brick_wall","aether:iron_helmet_repairing","farmersdelight:steak_and_potatoes","mcwtrpdoors:spruce_ranch_trapdoor","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","mcwpaths:sandstone_crystal_floor_stairs","pneumaticcraft:wall_lamp_inverted_blue","securitycraft:codebreaker","rftoolsutility:energy_module","supplementaries:flute","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","constructionwand:infinity_wand","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwdoors:warped_waffle_door","mcwfences:warped_horse_fence","mcwfurnitures:warped_end_table","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","megacells:crafting/mega_energy_cell","mcwbiomesoplenty:maple_four_window","cfm:blue_kitchen_counter","dyenamics:spring_green_stained_glass_pane_from_glass_pane","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwdoors:bamboo_classic_door","mcwroofs:base_top_roof","securitycraft:harming_module","silentgear:bort_block","minecraft:golden_boots","mcwfurnitures:warped_wardrobe","mcwroofs:birch_roof","immersiveengineering:crafting/grit_sand","minecraft:pink_dye_from_red_white_dye","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","create:crafting/kinetics/item_vault","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","supplementaries:statue","pneumaticcraft:vacuum_pump","immersiveengineering:crafting/tinted_glass_lead_wire","create:crafting/kinetics/clockwork_bearing","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwroofs:gutter_middle_orange","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","mcwtrpdoors:birch_barrel_trapdoor","utilitix:diamond_shears","minecraft:wooden_pickaxe","handcrafted:pink_sheet","minecraft:cherry_planks","cfm:stripped_warped_kitchen_counter","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","twilightforest:compressed_blocks/ironwood_block","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","mcwdoors:birch_cottage_door","mcwfurnitures:stripped_birch_bookshelf_cupboard","rftoolsbase:machine_base","botania:aura_ring_greater","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","minecraft:stick_from_bamboo_item","mcwpaths:brick_crystal_floor_slab","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","twilightforest:wood/oak_banister","alltheores:enderium_plate","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","quark:tweaks/crafting/utility/bent/bread","pneumaticcraft:pressure_chamber_wall","modularrouters:player_module","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwdoors:spruce_paper_door","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwfurnitures:birch_lower_bookshelf_drawer","mcwroofs:brown_concrete_attic_roof","alltheores:raw_iridium_block","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","ae2:network/cables/smart_red","create:crafting/kinetics/nixie_tube","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","mcwfurnitures:birch_bookshelf_drawer","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","mcwfurnitures:stripped_birch_bookshelf_drawer","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","mcwfurnitures:warped_double_drawer","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","mcwfurnitures:birch_coffee_table","rftoolsstorage:storage_control_module","chimes:bamboo_chimes","enderio:pressurized_fluid_tank","pneumaticcraft:network_data_storage","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","botania:placeholder","railcraft:steel_gear","aether:diamond_shovel_repairing","immersiveengineering:crafting/treated_wood_horizontal","allthemodium:piglich_heart_block","bigreactors:smelting/graphite_from_charcoal","sophisticatedstorage:crafting_upgrade","allthemodium:smithing/allthemodium_axe_smithing","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","mcwfurnitures:stripped_cherry_drawer_counter","delightful:knives/steel_knife","cfm:warped_desk","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","pneumaticcraft:network_registry","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","mcwtrpdoors:warped_ranch_trapdoor","mysticalagriculture:prosperity_gemstone","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","minecraft:gunpowder","travelersbackpack:bookshelf","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","mcwtrpdoors:cherry_barn_trapdoor","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","immersiveengineering:crafting/alu_wallmount","ae2:network/cables/smart_fluix","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","pylons:potion_filter","enderio:resetting_lever_thirty","immersiveengineering:crafting/conveyor_splitter","minecraft:glistering_melon_slice","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","handcrafted:spruce_fancy_bed","minecraft:snow","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","handcrafted:quartz_corner_trim","pneumaticcraft:electrostatic_compressor","mcwfurnitures:warped_bookshelf","ad_astra:steel_factory_block","mcwfurnitures:stripped_spruce_triple_drawer","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","handcrafted:spruce_chair","mcwroofs:light_gray_concrete_lower_roof","occultism:crafting/spirit_torch","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","productivebees:expansion_boxes/expansion_box_cherry_canvas","farmersdelight:cooking/vegetable_noodles","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","mcwpaths:stone_crystal_floor_path","alltheores:aluminum_gear","twigs:stone_column","minecraft:lectern","pneumaticcraft:compressed_bricks_from_stone_stonecutting","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwbiomesoplenty:hellbark_plank_window2","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","mcwtrpdoors:bamboo_blossom_trapdoor","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","megacells:cells/portable/portable_fluid_cell_16m","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","connectedglass:borderless_glass_white2","aquaculture:heavy_hook","handcrafted:birch_fancy_bed","mcwfurnitures:warped_striped_chair","aether:netherite_sword_repairing","minecraft:white_concrete_powder","biomesoplenty:dead_chest_boat","pneumaticcraft:advanced_air_compressor","minecraft:yellow_terracotta","mcwwindows:acacia_window","mcwroofs:green_terracotta_roof","croptopia:food_press","biomesoplenty:rabbit_stew_from_toadstool","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","croptopia:shaped_milk_bottle","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","twilightforest:vanilla_to_tf_lilypad","botania:terrasteel_chestplate","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","farmersdelight:nether_salad","mcwbridges:birch_bridge_pier","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","mcwlights:orange_lamp","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","utilitix:tiny_coal_to_tiny","pneumaticcraft:reinforced_brick_pillar","create:cut_calcite_brick_stairs_from_stone_types_calcite_stonecutting","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","gtceu:shapeless/decompress_tin_from_ore_block","mcwdoors:birch_classic_door","mcwpaths:brick_crystal_floor","botania:dodge_ring","ae2:materials/basiccard","handcrafted:oak_table","handcrafted:white_plate","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwfences:oak_highley_gate","xnet:connector_green_dye","pneumaticcraft:safety_tube_module","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwlights:blue_lamp","productivebees:hives/advanced_birch_canvas_hive","comforts:hammock_to_red","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","railcraft:charge_terminal","mcwroofs:blue_concrete_attic_roof","mysticalagriculture:inferium_block","mcwfurnitures:stripped_warped_chair","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","croptopia:chocolate","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","mcwbridges:nether_bricks_bridge","mcwdoors:spruce_barn_door","allthecompressed:compress/moss_block_1x","twilightdelight:maze_stove","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","immersiveengineering:crafting/alu_fence","domum_ornamentum:red_floating_carpet","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","aether:iron_boots_repairing","mcwdoors:bamboo_beach_door","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","railcraft:steel_tank_gauge","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwdoors:spruce_modern_door","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dyenamics:bed/navy_bed_frm_white_bed","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","pneumaticcraft:network_io_port","twigs:polished_calcite_bricks_from_calcite_stonecutting","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","alltheores:enderium_rod","corail_woodcutter:cherry_woodcutter","handcrafted:birch_bench","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","dyenamics:bed/persimmon_bed_frm_white_bed","mythicbotany:gaia_pylon","botanypots:botanypots/crafting/terracotta_botany_pot","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwdoors:bamboo_bark_glass_door","mcwroofs:stone_bricks_top_roof","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","utilitarian:utility/spruce_logs_to_boats","minecraft:gray_stained_glass","cfm:magenta_trampoline","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwroofs:oak_upper_lower_roof","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","mcwfurnitures:stripped_birch_cupboard_counter","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","bloodmagic:smelting/saltpeter","mcwlights:crimson_ceiling_fan_light","minecraft:red_bed","connectedglass:clear_glass_lime2","farmersdelight:cooking/mushroom_rice","twilightdelight:twilight_spring","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","easy_villagers:incubator","ae2:blasting/silicon_from_certus_quartz_dust","mcwroofs:brown_terracotta_top_roof","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","mcwfurnitures:stripped_birch_counter","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","computercraft:printed_pages","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","mcwfurnitures:stripped_spruce_modern_chair","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","delightful:knives/certus_quartz_knife","productivebees:stonecutter/concrete_canvas_hive","mcwdoors:print_bamboo","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","mcwdoors:birch_barn_door","mcwtrpdoors:spruce_tropical_trapdoor","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","mcwtrpdoors:print_cottage","securitycraft:reinforced_gray_stained_glass","aether:iron_hoe_repairing","minecraft:copper_ingot_from_smelting_raw_copper","sophisticatedbackpacks:chipped/carpenters_table_upgrade","create:crafting/kinetics/mechanical_arm","mcwbiomesoplenty:stripped_maple_log_window2","aether:wooden_sword_repairing","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","twilightforest:equipment/arctic_leggings","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","naturalist:bug_net","minecraft:birch_sign","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","additionallanterns:normal_lantern_gray","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","domum_ornamentum:beige_bricks","supplementaries:hourglass","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","cfm:light_blue_sofa","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","cfm:stripped_spruce_table","alltheores:aluminum_rod","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","minecraft:orange_dye_from_red_yellow","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","minecraft:spruce_stairs","supplementaries:checker","croptopia:flour","railcraft:bushing_gear_brass","mcwdoors:birch_japanese_door","minecraft:jungle_sign","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","railcraft:steel_tank_valve","botania:conversions/light_blue_petal_block_deconstruct","domum_ornamentum:cactus_extra","cfm:cyan_cooler","minecraft:light_gray_dye_from_white_tulip","pneumaticcraft:network_node","ae2:network/parts/formation_plane","connectedglass:clear_glass_pane3","railcraft:silver_gear","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","ad_astra:steel_rod","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwroofs:light_gray_roof","immersiveengineering:crafting/conveyor_extract","create:small_calcite_brick_stairs_from_stone_types_calcite_stonecutting","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwdoors:bamboo_tropical_door","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","travelersbackpack:redstone","minecraft:slime_block","twilightforest:magic_map_focus","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","dyenamics:lavender_stained_glass_pane_from_glass_pane","mcwfurnitures:stripped_warped_modern_desk","create:cut_calcite_from_stone_types_calcite_stonecutting","chimes:glass_bells","mcwwindows:iron_shutter","mcwroofs:birch_planks_top_roof","mcwdoors:cherry_paper_door","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/connector_mv","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","ad_astra:nasa_workbench","mcwpaths:sandstone_flagstone","cfm:birch_crate","ad_astra:etrionic_capacitor","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","ae2:tools/nether_quartz_axe","mcwroofs:birch_planks_lower_roof","allthecompressed:compress/nether_star_block_3x","mcwwindows:brown_mosaic_glass_pane","mcwpaths:stone_windmill_weave_stairs","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","sophisticatedstorage:basic_to_gold_tier_upgrade","enderio:resetting_lever_sixty_inv","mcwfurnitures:stripped_spruce_large_drawer","minecraft:smooth_sandstone","minecraft:magenta_stained_glass_pane_from_glass_pane","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","cfm:stripped_warped_desk","alltheores:copper_rod","productivebees:stonecutter/cherry_canvas_hive","mcwfurnitures:stripped_birch_modern_chair","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","utilitix:filter_rail","dyenamics:maroon_dye","minecraft:spruce_planks","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","croptopia:pork_jerky","enderio:resetting_lever_sixty_from_inv","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","minecraft:mossy_cobblestone_from_moss_block","bloodmagic:blood_rune_self_sacrifice","mcwfurnitures:spruce_triple_drawer","minecraft:cracked_stone_bricks","mcwdoors:cherry_classic_door","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwfurnitures:birch_double_wardrobe","mcwfurnitures:stripped_birch_desk","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","utilitarian:utility/cherry_logs_to_pressure_plates","croptopia:toast_with_jam","mcwtrpdoors:birch_glass_trapdoor","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","minecraft:lantern","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","minecraft:magenta_stained_glass","minecraft:ender_chest","railcraft:nickel_gear","minecraft:iron_trapdoor","travelersbackpack:quartz_smithing","pneumaticcraft:thermal_lagging","mcwbridges:nether_bricks_bridge_pier","ae2:network/cables/dense_covered_yellow","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","handcrafted:stone_corner_trim","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","modularrouters:void_module","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","allthecompressed:decompress/nether_star_block_2x","sophisticatedstorage:spruce_barrel","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","cfm:warped_desk_cabinet","mcwroofs:spruce_planks_steep_roof","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","mcwfurnitures:spruce_large_drawer","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","handcrafted:spruce_shelf","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","aether:golden_boots_repairing","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","securitycraft:reinforced_warped_fence_gate","pneumaticcraft:gun_ammo_weighted","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","caupona:clay_cistern","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfurnitures:cherry_table","minecraft:light_gray_dye_from_gray_white_dye","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","mcwfurnitures:stripped_cherry_double_drawer","alltheores:aluminum_plate","mcwfences:blackstone_pillar_wall","mcwfurnitures:birch_wardrobe","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","bloodmagic:synthetic_point","deepresonance:lens","additionallanterns:normal_lantern_black","croptopia:doughnut","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfurnitures:cherry_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","minecraft:redstone_lamp","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_warped_lower_bookshelf_drawer","mcwbiomesoplenty:stripped_umbran_pane_window","mcwfurnitures:warped_lower_bookshelf_drawer","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwfurnitures:warped_counter","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","mcwfurnitures:stripped_birch_large_drawer","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","mcwdoors:warped_classic_door","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","twilightforest:equipment/knightmetal_sword","allthecompressed:compress/cobbled_deepslate_1x","productivebees:stonecutter/acacia_canvas_expansion_box","twilightdelight:berry_stick","silentgear:stone_torch","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","utilitarian:utility/warped_logs_to_pressure_plates","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","travelersbackpack:blue_sleeping_bag","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","botania:diluted_pool","mcwlights:glowstone_slab","modularrouters:energy_output_module","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","mcwdoors:birch_western_door","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","reliquary:uncrafting/string","connectedglass:tinted_borderless_glass_white2","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alchemistry:fission_chamber_controller","railcraft:animal_detector","alltheores:diamond_rod","minecraft:stone_slab_from_stone_stonecutting","mcwroofs:cherry_roof","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:light_blue_terracotta_upper_lower_roof","cfm:stripped_warped_desk_cabinet","mcwwindows:bamboo_shutter","mcwdoors:print_birch","chemlib:sodium_nugget_to_ingot","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwroofs:spruce_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwfurnitures:warped_desk","mcwlights:white_paper_lamp","cfm:spruce_chair","minecraft:oak_trapdoor","littlelogistics:locomotive_route","connectedglass:clear_glass_white2","mcwwindows:mangrove_plank_pane_window","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","minecraft:netherite_leggings_smithing","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","create:crafting/kinetics/placard","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:birch_button","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","mcwtrpdoors:cherry_ranch_trapdoor","minecraft:stone_brick_stairs_from_stone_stonecutting","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","comforts:sleeping_bag_to_light_blue","immersiveengineering:crafting/sorter","mcwbiomesoplenty:dead_hedge","botania:petal_pink","handcrafted:wood_bowl","mcwfurnitures:cherry_drawer","pneumaticcraft:charging_station","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwdoors:bamboo_barn_glass_door","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","create:crafting/kinetics/crafter_slot_cover","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","minecraft:netherite_machete_smithing","mcwtrpdoors:warped_barred_trapdoor","mcwroofs:warped_steep_roof","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","supplementaries:sign_post_birch","comforts:hammock_pink","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwdoors:warped_stable_door","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","mcwfurnitures:stripped_cherry_striped_chair","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","farmersdelight:safety_net","mcwdoors:cherry_cottage_door","immersiveengineering:crafting/maintenance_kit","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","twigs:bone_meal_from_seashells","mcwlights:golden_triple_candle_holder","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","sophisticatedstorage:stack_upgrade_tier_1_plus","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","supplementaries:sack","mcwfurnitures:stripped_birch_stool_chair","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwdoors:cherry_stable_door","cfm:warped_upgraded_fence","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwfurnitures:stripped_warped_bookshelf_cupboard","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","allthemodium:smithing/allthemodium_helmet_smithing","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","productivebees:hives/advanced_bamboo_canvas_hive","mcwroofs:blue_terracotta_top_roof","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","minecraft:brown_dye","bloodmagic:incense_altar","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","allthemodium:smithing/allthemodium_boots_smithing","twilightforest:vanilla_to_tf_moss","mcwwindows:mangrove_shutter","bloodmagic:blood_rune_sacrifice","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","dyenamics:mint_dye","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","alchemistry:reactor_input","railcraft:diamond_spike_maul","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwdoors:cherry_barn_glass_door","minecraft:white_candle","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","utilitarian:tps_meter","mcwdoors:birch_four_panel_door","utilitarian:utility/spruce_logs_to_doors","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","minecraft:blue_banner","ae2:network/cells/fluid_cell_housing","mcwroofs:nether_bricks_upper_steep_roof","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","mcwdoors:bamboo_nether_door","blue_skies:starlit_bookshelf","botania:petal_light_blue","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwfurnitures:spruce_modern_chair","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","allthemodium:smithing/allthemodium_leggings_smithing","create:crafting/kinetics/elevator_pulley","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:warped_bookshelf_cupboard","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","immersiveengineering:crafting/plate_electrum_hammering","mcwdoors:birch_stable_door","biomesoplenty:jacaranda_chest_boat","utilitarian:utility/warped_logs_to_doors","mcwfurnitures:stripped_cherry_bookshelf","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","reliquary:mercy_cross","farmersdelight:fried_egg","minecraft:magma_cream","botania:slime_bottle","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwfurnitures:cherry_double_drawer_counter","create:crafting/kinetics/attribute_filter","handcrafted:white_bowl","supplementaries:key","productivebees:expansion_boxes/expansion_box_bamboo_canvas","cfm:warped_kitchen_counter","ae2:block_cutter/walls/sky_stone_wall","mcwroofs:warped_upper_lower_roof","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","blue_skies:maple_bookshelf","rftoolspower:power_core1","connectedglass:clear_glass_red2","pneumaticcraft:logistics_frame_default_storage","aether:wooden_axe_repairing","computercraft:cable","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","bloodmagic:path/path_wood","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","mcwroofs:thatch_attic_roof","securitycraft:reinforced_orange_stained_glass_pane_from_glass","securitycraft:reinforced_lime_stained_glass","pneumaticcraft:collector_drone","pneumaticcraft:wall_lamp_inverted_gray","handcrafted:spruce_table","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","mcwpaths:brick_running_bond_path","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","botania:mana_pylon","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","mcwtrpdoors:birch_classic_trapdoor","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:birch_blinds","mcwfurnitures:stripped_oak_striped_chair","cfm:spruce_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","simplylight:illuminant_red_block_on_toggle","mcwpaths:brick_windmill_weave_stairs","mcwfurnitures:birch_double_drawer_counter","additionallanterns:normal_sandstone_lantern","mcwdoors:spruce_tropical_door","mcwroofs:magenta_terracotta_upper_lower_roof","create:polished_cut_calcite_stairs_from_stone_types_calcite_stonecutting","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","dyenamics:spring_green_terracotta","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","croptopia:mortar_and_pestle","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","twilightforest:equipment/knightmetal_ring","mcwfurnitures:warped_triple_drawer","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","botania:apothecary_livingrock","advanced_ae:smalladvpatpro","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","mcwtrpdoors:bamboo_barn_trapdoor","railcraft:iron_spike_maul","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","easy_villagers:farmer","mcwbiomesoplenty:fir_four_window","everythingcopper:copper_axe","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","railcraft:feed_station","minecraft:black_stained_glass_pane_from_glass_pane","minecraft:pink_banner","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","twilightforest:smelted_cracked_underbrick","connectedglass:scratched_glass_white2","mcwfurnitures:spruce_chair","create:cut_calcite_brick_slab_from_stone_types_calcite_stonecutting","productivebees:expansion_boxes/expansion_box_spruce_canvas","mcwdoors:warped_cottage_door","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","handcrafted:oak_nightstand","mcwdoors:bamboo_paper_door","railcraft:personal_world_spike","immersiveengineering:crafting/armor_steel_chestplate","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","connectedglass:clear_glass_pink2","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","mcwdoors:warped_glass_door","immersiveengineering:crafting/stick_aluminum","mcwroofs:thatch_steep_roof","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","dyenamics:bubblegum_concrete_powder","xnet:redstoneproxy_update","mcwfurnitures:stripped_warped_triple_drawer","mcwroofs:spruce_planks_attic_roof","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwroofs:nether_bricks_attic_roof","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","merequester:requester","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","minecraft:cracked_nether_bricks","utilitix:gilding_crystal","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","handcrafted:birch_couch","farmersdelight:cooking/fish_stew","supplementaries:slingshot","allthemodium:allthemodium_plate","twigs:cobblestone_from_pebble","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","productivebees:expansion_boxes/expansion_box_mangrove_canvas","utilitix:tiny_charcoal_from_tiny","everythingcopper:copper_rail","mcwfurnitures:birch_cupboard_counter","cfm:black_grill","handcrafted:deepslate_pillar_trim","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","pneumaticcraft:wall_lamp_inverted_black","mcwtrpdoors:warped_four_panel_trapdoor","mcwwindows:stripped_warped_stem_window2","create:small_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","cfm:birch_desk_cabinet","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:nether_bricks_steep_roof","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aether:aether_iron_nugget_from_blasting","cfm:stripped_warped_park_bench","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwroofs:bricks_upper_steep_roof","botania:terraform_rod","minecraft:iron_pickaxe","aether:shield_repairing","mcwwindows:jungle_shutter","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","minecraft:spruce_button","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","ae2:network/cables/glass_orange","mcwfurnitures:stripped_spruce_table","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwdoors:birch_swamp_door","mcwfences:modern_diorite_wall","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwtrpdoors:bamboo_classic_trapdoor","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","cfm:orange_cooler","mcwfurnitures:birch_drawer","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwtrpdoors:bamboo_barred_trapdoor","mcwbridges:balustrade_mossy_stone_bricks_bridge","aether:leather_gloves_repairing","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","computercraft:monitor_advanced","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","create:layered_calcite_from_stone_types_calcite_stonecutting","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","utilitarian:utility/birch_logs_to_slabs","securitycraft:keypad_item","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","additionallanterns:normal_lantern_lime","botania:elementium_block","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mysticalagriculture:inferium_seeds","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","trashcans:energy_trash_can","botania:dreamwood_twig","mcwpaths:stone_crystal_floor","mcwfurnitures:stripped_cherry_modern_desk","pneumaticcraft:air_compressor","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","mcwdoors:bamboo_modern_door","cfm:warped_coffee_table","cfm:fridge_dark","chimes:copper_chimes","handcrafted:birch_desk","minecraft:birch_fence","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","create:crafting/kinetics/brass_door","farmersdelight:cooking_pot","pneumaticcraft:diagnostic_subroutine","simplylight:illuminant_magenta_block_on_dyed","mcwtrpdoors:mangrove_bark_trapdoor","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","chemlib:sodium_ingot_to_block","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","cfm:stripped_jungle_kitchen_counter","mcwtrpdoors:warped_swamp_trapdoor","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:spruce_fence_gate","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","delightful:knives/electrum_knife","reliquary:mob_charm_fragments/wither_skeleton","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","alltheores:steel_dust_from_alloy_blending","immersiveengineering:crafting/earmuffs","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","comforts:sleeping_bag_pink","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","minecraft:white_stained_glass_pane_from_glass_pane","mcwfurnitures:cherry_modern_desk","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","minecraft:birch_wood","cfm:spatula","pneumaticcraft:charging_upgrade","ae2:network/cables/dense_covered_fluix_clean","littlelogistics:switch_rail","mcwdoors:warped_tropical_door","mcwfurnitures:stripped_jungle_double_wardrobe","minecraft:scaffolding","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","mcwtrpdoors:birch_ranch_trapdoor","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","sophisticatedstorage:birch_chest","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwfurnitures:birch_modern_wardrobe","mcwlights:copper_candle_holder","minecraft:mossy_stone_brick_slab","mcwtrpdoors:bamboo_swamp_trapdoor","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:bamboo_chest_raft","mcwtrpdoors:birch_four_panel_trapdoor","minecraft:honey_block","ae2:tools/nether_quartz_spade","additionallanterns:normal_lantern_yellow","farmersdelight:melon_juice","dyenamics:bed/bubblegum_bed_frm_white_bed","cfm:stripped_spruce_desk","sophisticatedbackpacks:crafting_upgrade","productivebees:stonecutter/willow_canvas_expansion_box","mcwtrpdoors:cherry_whispering_trapdoor","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","mcwwindows:jungle_window","minecraft:cobblestone_stairs","connectedglass:clear_glass_magenta2","mcwwindows:crimson_louvered_shutter","supplementaries:candle_holders/candle_holder_orange","twilightforest:equipment/arctic_boots","simplylight:illuminant_red_block_toggle","immersiveengineering:crafting/heavy_engineering","mcwwindows:metal_window2","aether:blue_ice_freezing","croptopia:oatmeal","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","cfm:birch_table","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","reliquary:uncrafting/glowstone_dust","cfm:white_sofa","tombstone:ankh_of_prayer","utilitarian:utility/birch_logs_to_boats","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","aiotbotania:livingrock_sword","cfm:black_kitchen_drawer","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","mcwtrpdoors:birch_cottage_trapdoor","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:spruce_roof","pneumaticcraft:aphorism_tile_reset","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","ae2:network/blocks/cell_workbench","minecraft:polished_andesite_slab","immersiveengineering:crafting/survey_tools","mcwdoors:cherry_whispering_door","mcwlights:magenta_lamp","productivebees:stonecutter/warped_canvas_expansion_box","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","sophisticatedstorage:stonecutter_upgrade","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","handcrafted:oak_bench","mcwfurnitures:cherry_cupboard_counter","mcwfences:nether_brick_grass_topped_wall","mcwdoors:warped_stable_head_door","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwroofs:spruce_planks_upper_steep_roof","mcwwindows:birch_louvered_shutter","minecraft:repeater","minecraft:red_concrete_powder","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","botania:elementium_boots","cfm:jungle_cabinet","additionallanterns:normal_lantern_white","mcwfurnitures:stripped_warped_end_table","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwroofs:base_roof_block","productivebees:hives/advanced_mangrove_canvas_hive","handcrafted:birch_pillar_trim","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","twilightforest:magic_map","botania:conversions/pink_petal_block_deconstruct","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwwindows:spruce_blinds","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwtrpdoors:warped_paper_trapdoor","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","minecraft:warped_planks","handcrafted:spruce_drawer","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","botania:terra_axe","ad_astra:red_industrial_lamp","domum_ornamentum:light_blue_floating_carpet","minecraft:dye_white_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","minecraft:red_candle","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","create:crafting/logistics/content_observer","sophisticatedbackpacks:chipped/glassblower_upgrade","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","additionallanterns:normal_lantern_blue","aether:chainmail_gloves_repairing","allthecompressed:compress/birch_planks_1x","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","comforts:sleeping_bag_to_pink","mcwtrpdoors:spruce_blossom_trapdoor","twilightforest:naga_banner_pattern","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","twilightforest:equipment/knightmetal_shield","dyenamics:bed/fluorescent_bed_frm_white_bed","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","botania:reach_ring","mcwbiomesoplenty:stripped_dead_pane_window","reliquary:uncrafting/packed_ice","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","handcrafted:jungle_drawer","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","minecraft:oak_pressure_plate","botania:mushroom_5","botania:mushroom_4","minecraft:stone_brick_stairs","bigreactors:turbine/basic/activefluidport_forge","aether:netherite_leggings_repairing","aether:skyroot_beehive","botania:mushroom_3","mcwwindows:quartz_window","cfm:brown_grill","mcwroofs:gray_roof","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","supplementaries:present","bloodmagic:blood_rune_orb","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","mysticalagriculture:inferium_block_uncraft","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","botania:glimmering_livingwood_log","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","create:cut_calcite_bricks_from_stone_types_calcite_stonecutting","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","minecraft:clock","mcwroofs:red_concrete_top_roof","create:crafting/appliances/netherite_diving_helmet_from_netherite","domum_ornamentum:cyan_brick_extra","mcwroofs:birch_planks_attic_roof","securitycraft:portable_tune_player","mcwroofs:birch_planks_upper_steep_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","railcraft:lead_gear","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","additionallanterns:normal_lantern_light_gray","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","aquaculture:iron_fillet_knife","silentgear:diamond_shard","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","cfm:lime_picket_fence","aether:leather_leggings_repairing","dyenamics:bed/conifer_bed","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwtrpdoors:spruce_whispering_trapdoor","dyenamics:peach_stained_glass","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwroofs:purple_terracotta_roof","computercraft:turtle_normal/computercraft/speaker","mcwbridges:rope_birch_bridge","mcwfurnitures:stripped_jungle_chair","mcwbridges:nether_bricks_bridge_stair","botania:spark_changer","mcwwindows:white_mosaic_glass","aether:stone_hoe_repairing","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","minecraft:moss_carpet","minecraft:polished_andesite_slab_from_andesite_stonecutting","rftoolsbase:machine_frame","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","cfm:stripped_spruce_crate","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","railcraft:brass_gear","pylons:infusion_pylon","minecraft:mossy_stone_bricks_from_moss_block","comforts:hammock_to_pink","twilightforest:material/armor_shard_cluster","mcwwindows:spruce_window2","mcwfurnitures:stripped_birch_table","mcwroofs:brown_terracotta_upper_lower_roof","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","mcwwindows:andesite_four_window","domum_ornamentum:light_blue_brick_extra","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","pneumaticcraft:armor_upgrade","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","mcwlights:festive_lantern","croptopia:fruit_salad","securitycraft:block_change_detector","supplementaries:pancake_fd","twigs:rocky_dirt","advanced_ae:advpatpro2","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","minecraft:netherite_scrap_from_blasting","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","mcwtrpdoors:bamboo_four_panel_trapdoor","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","botania:white_shiny_flower","mcwbridges:cherry_bridge_pier","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","connectedglass:scratched_glass1","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","handcrafted:blue_sheet","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwtrpdoors:birch_beach_trapdoor","createoreexcavation:vein_atlas","additionallanterns:quartz_chain","aether:stone_axe_repairing","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","mcwfurnitures:cherry_double_drawer","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","botania:dreamwood_slab","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","reliquary:shears_of_winter","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","minecraft:sandstone_slab","mcwwindows:birch_window","mcwfences:acacia_picket_fence","reliquary:uncrafting/wither_skeleton_skull","mcwfurnitures:warped_glass_table","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","ae2:network/cables/dense_covered_cyan","minecraft:gold_ingot_from_smelting_raw_gold","sophisticatedbackpacks:smithing_upgrade","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","pneumaticcraft:network_api","mcwdoors:birch_whispering_door","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","railcraft:logbook","ae2:misc/tiny_tnt","comforts:hammock_to_light_blue","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","mcwroofs:thatch_lower_roof","reliquary:ice_magus_rod","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","handcrafted:spruce_couch","botania:glass_pickaxe","twilightforest:nagastone/nagastone_spiral","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwwindows:jungle_blinds","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","travelersbackpack:melon","securitycraft:block_pocket_manager","mcwlights:soul_cherry_tiki_torch","botania:red_pavement","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","mcwlights:red_lamp","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","alltheores:lumium_dust_from_alloy_blending","bloodmagic:path/path_stone","cfm:purple_kitchen_drawer","botania:flower_bag","ad_astra:steel_plateblock","alltheores:brass_gear","pneumaticcraft:compressed_brick_wall","cfm:warped_upgraded_gate","twilightforest:berry_torch","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:cherry_striped_chair","aether:stone_sword_repairing","minecraft:netherite_chestplate_smithing","cfm:jungle_kitchen_counter","ae2:network/cables/dense_smart_red","pneumaticcraft:guard_drone","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","minecraft:quartz_stairs_from_quartz_block_stonecutting","mcwroofs:birch_lower_roof","mcwlights:light_blue_lamp","railcraft:bag_of_cement","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","domum_ornamentum:white_paper_extra","twilightforest:equipment/arctic_chestplate","mcwbiomesoplenty:fir_window2","dyenamics:bed/wine_bed_frm_white_bed","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","mcwwindows:dark_oak_plank_parapet","mcwwindows:spruce_log_parapet","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","minecraft:birch_pressure_plate","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","create:spruce_window","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","mcwdoors:cherry_glass_door","connectedglass:clear_glass_gray2","sfm:manager","mcwfurnitures:stripped_spruce_counter","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","travelersbackpack:fox","botania:mana_quartz","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","mcwroofs:spruce_lower_roof","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","littlelogistics:tee_junction_rail","comforts:sleeping_bag_to_red","allthemodium:smithing/allthemodium_sword_smithing","botania:livingwood_twig","mcwdoors:spruce_whispering_door","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","ae2:tools/network_tool","minecraft:bowl","deeperdarker:bloom_boat","mcwtrpdoors:birch_blossom_trapdoor","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","mcwtrpdoors:birch_barn_trapdoor","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwfurnitures:birch_chair","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","additionallanterns:normal_lantern_green","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","productivebees:stonecutter/wisteria_canvas_hive","mcwfurnitures:stripped_cherry_modern_wardrobe","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","minecraft:birch_planks","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfurnitures:stripped_warped_stool_chair","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwtrpdoors:print_paper","mcwdoors:jungle_swamp_door","minecraft:red_carpet","cfm:light_blue_kitchen_sink","cfm:stripped_birch_desk_cabinet","ad_astra:steel_panel","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","botania:dye_light_blue","domum_ornamentum:green_brick_extra","mcwfurnitures:cherry_glass_table","mcwfurnitures:stripped_cherry_desk","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","bloodmagic:enhanced_teleposer_focus","mcwwindows:stripped_crimson_stem_window2","croptopia:meringue","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwdoors:bamboo_waffle_door","mcwdoors:birch_barn_glass_door","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","handcrafted:birch_drawer","mcwbiomesoplenty:magic_horse_fence","alchemistry:reactor_output","sophisticatedstorage:basic_to_copper_tier_upgrade","mcwtrpdoors:birch_whispering_trapdoor","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwfurnitures:cherry_desk","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","mcwdoors:warped_swamp_door","cfm:green_picket_gate","railcraft:radio_circuit","ae2:tools/misctools_entropy_manipulator","twilightforest:mossy_underbrick","aether:netherite_pickaxe_repairing","pneumaticcraft:manual_compressor","botania:elementium_hoe","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","mcwtrpdoors:spruce_classic_trapdoor","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","mcwfurnitures:cherry_coffee_table","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","mcwtrpdoors:cherry_four_panel_trapdoor","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwroofs:gray_terracotta_steep_roof","create:cut_calcite_stairs_from_stone_types_calcite_stonecutting","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","minecraft:cooked_cod","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","ad_astra:steel_trapdoor","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","pneumaticcraft:manometer","pneumaticcraft:aerial_interface","securitycraft:alarm","ad_astra:launch_pad","cfm:magenta_picket_gate","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","ae2:network/blocks/energy_vibration_chamber","mcwfurnitures:birch_stool_chair","mcwfurnitures:warped_modern_desk","ae2:tools/fluix_shovel","mcwdoors:birch_japanese2_door","dyenamics:icy_blue_dye","pneumaticcraft:wall_lamp_inverted_magenta","mcwfurnitures:stripped_cherry_double_drawer_counter","modularrouters:sender_module_1_alt","botania:elementium_leggings","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","pneumaticcraft:smart_chest","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","comforts:sleeping_bag_red","additionallanterns:amethyst_lantern","utilitix:crude_furnace","dyenamics:peach_dye","cfm:warped_bedside_cabinet","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","mcwroofs:gray_steep_roof","mcwfurnitures:cherry_stool_chair","utilitarian:utility/birch_logs_to_pressure_plates","create:copper_shingles_from_ingots_copper_stonecutting","reliquary:mob_charm_belt","everythingcopper:copper_sword","twilightdelight:chocolate_wafer","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","mcwroofs:birch_planks_roof","productivebees:stonecutter/yucca_canvas_hive","cfm:stripped_birch_crate","sophisticatedstorage:jukebox_upgrade","mcwtrpdoors:jungle_classic_trapdoor","additionallanterns:normal_lantern_cyan","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","minecraft:light_blue_banner","mcwroofs:warped_roof","dyenamics:mint_concrete_powder","dankstorage:dank_2","aether:blue_cape_blue_wool","mcwfences:railing_nether_brick_wall","dankstorage:dank_4","mcwroofs:yellow_terracotta_upper_lower_roof","ae2:network/cables/glass_black","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","mcwroofs:birch_attic_roof","botania:polished_livingrock","immersiveengineering:crafting/wire_electrum","mcwwindows:warped_stem_window","mcwfurnitures:cherry_wardrobe","mcwtrpdoors:warped_glass_trapdoor","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","mcwroofs:thatch_upper_steep_roof","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","additionallanterns:gold_lantern","cfm:stripped_oak_crate","immersiveengineering:crafting/wirecutter","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","productivebees:expansion_boxes/expansion_box_warped_canvas","mcwfurnitures:stripped_warped_striped_chair","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","bloodmagic:path/path_wornstonetile","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","productivebees:stonecutter/spruce_canvas_hive","create:crafting/kinetics/brass_hand","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","mcwfurnitures:stripped_cherry_bookshelf_cupboard","botania:thunder_sword","mcwdoors:cherry_stable_head_door","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","immersiveengineering:crafting/plate_nickel_hammering","megacells:cells/portable/portable_item_cell_16m","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","productivebees:stonecutter/yucca_canvas_expansion_box","mcwfurnitures:birch_glass_table","mcwwindows:quartz_pane_window","bloodmagic:raw_hellforged_block","mcwtrpdoors:metal_trapdoor","immersiveengineering:crafting/conveyor_redstone","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:red_cushion","handcrafted:fancy_painting","mcwfurnitures:spruce_table","minecraft:pink_bed","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","bloodmagic:path/path_woodtile","cfm:jungle_bedside_cabinet","pneumaticcraft:air_grate_module","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:cyan_stained_glass_pane_from_glass_pane","create:cut_calcite_wall_from_stone_types_calcite_stonecutting","pneumaticcraft:minigun_upgrade","connectedglass:clear_glass_brown2","mcwwindows:stripped_cherry_log_window","minecraft:dye_red_carpet","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","minecraft:quartz_bricks","immersiveengineering:crafting/wirecoil_redstone","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","mcwbridges:balustrade_bricks_bridge","alltheores:nickel_rod","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","allthetweaks:atm_star_block","handcrafted:andesite_corner_trim","easy_villagers:iron_farm","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","minecraft:birch_door","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","bloodmagic:soul_forge","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","mcwfences:double_curved_metal_fence","cfm:dye_red_picket_gate","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","botania:elementium_helmet","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","ae2:network/parts/formation_plane_alt","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","cfm:green_picket_fence","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","mcwbridges:warped_bridge_pier","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:birch_fence_gate","cfm:stripped_oak_kitchen_drawer","mcwroofs:cherry_steep_roof","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwfurnitures:stripped_cherry_end_table","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","mcwtrpdoors:spruce_barn_trapdoor","mcwfurnitures:spruce_modern_wardrobe","productivebees:stonecutter/hellbark_canvas_hive","ae2:network/blocks/pattern_providers_interface_part","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwbridges:bamboo_bridge","twilightforest:equipment/block_and_chain","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","botania:terra_pick","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","mcwdoors:warped_western_door","botania:blood_pendant","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","mcwfurnitures:jungle_large_drawer","botania:monocle","pneumaticcraft:reinforced_air_canister","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_3","mcwroofs:cherry_upper_lower_roof","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","mcwfurnitures:warped_coffee_table","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","utilitarian:utility/warped_logs_to_stairs","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","bloodmagic:blood_rune_blank","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","minecraft:yellow_stained_glass_pane_from_glass_pane","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwfurnitures:spruce_drawer_counter","allthecompressed:compress/cobblestone_1x","minecolonies:apple_pie","mcwtrpdoors:bamboo_beach_trapdoor","mcwpaths:stone_running_bond_path","twigs:mossy_bricks_from_moss_block","cfm:stripped_birch_coffee_table","simplylight:illuminant_green_block_dyed","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","twilightdelight:cooking/torchberry_juice","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","deeperdarker:echo_chest_boat","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","pneumaticcraft:gun_ammo_ap","computercraft:wireless_modem_normal","farmersdelight:sweet_berry_cookie","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwfurnitures:stripped_warped_glass_table","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","ad_astra:zip_gun","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","mcwfurnitures:stripped_cherry_glass_table","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwroofs:cherry_top_roof","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","mcwfurnitures:cherry_modern_wardrobe","mcwfences:modern_stone_brick_wall","utilitarian:utility/cherry_logs_to_slabs","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","immersiveengineering:crafting/wirecoil_electrum","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwfurnitures:birch_lower_triple_drawer","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","mcwfurnitures:spruce_double_drawer","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","cfm:birch_coffee_table","travelersbackpack:magma_cube","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwfurnitures:stripped_birch_chair","mcwfurnitures:stripped_spruce_drawer_counter","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","mcwpaths:stone_windmill_weave_path","mcwfurnitures:cherry_drawer_counter","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","farmersdelight:cooking/glow_berry_custard","twigs:bloodstone","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","productivebees:expansion_boxes/expansion_box_oak_canvas","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","sophisticatedbackpacks:chipped/loom_table_upgrade","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","handcrafted:spruce_bench","twigs:spruce_table","mcwroofs:gutter_base_light_blue","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","ae2:network/wireless_crafting_terminal","immersiveengineering:crafting/conveyor_vertical","create:crafting/kinetics/orange_seat","mcwroofs:birch_upper_steep_roof","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwfurnitures:warped_covered_desk","minecraft:yellow_stained_glass","mcwdoors:cherry_western_door","chemlib:sodium_ingot_to_nugget","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","additionallanterns:normal_lantern_magenta","alltheores:steel_rod","botania:mana_gun","bigreactors:blasting/graphite_from_coal","sophisticatedbackpacks:stack_upgrade_tier_1","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","pneumaticcraft:compressed_iron_ingot_from_block","securitycraft:reinforced_white_stained_glass_pane_from_dye","sophisticatedbackpacks:restock_upgrade","farmersdelight:wheat_dough_from_eggs","securitycraft:reinforced_brown_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","minecraft:cooked_mutton","railways:crafting/smokestack_long","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","computercraft:wireless_modem_advanced","minecraft:lapis_block","connectedglass:tinted_borderless_glass_red2","twilightforest:material/smelted_ironwood_ingot","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","immersiveengineering:crafting/strip_curtain","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwfurnitures:birch_counter","dankstorage:3_to_4","mcwfurnitures:spruce_counter","utilitarian:snad/snad","ae2:tools/certus_quartz_cutting_knife","mcwfurnitures:warped_double_drawer_counter","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","dyenamics:amber_stained_glass","securitycraft:blacklist_module","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","bloodmagic:primitive_furnace_cell","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:spruce_slab","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","mcwpaths:birch_planks_path","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","cfm:stripped_warped_cabinet","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","productivebees:hives/advanced_acacia_canvas_hive","mcwfurnitures:stripped_jungle_covered_desk","travelersbackpack:snow","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","immersiveengineering:crafting/plate_lead_hammering","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","immersiveengineering:crafting/item_batcher","botania:manasteel_shears","sophisticatedstorage:chipped/botanist_workbench_upgrade","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","botania:elementium_pickaxe","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","irons_spellbooks:wandering_magician_chestplate","supplementaries:soap/piston","cfm:stripped_warped_bedside_cabinet","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","minecraft:soul_campfire","botania:clip","botania:red_string_interceptor","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","twilightdelight:obsidian","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","twilightforest:equipment/knightmetal_helmet","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwfurnitures:warped_double_wardrobe","mcwtrpdoors:jungle_barred_trapdoor","twigs:cracked_bricks","supplementaries:slice_map","productivebees:expansion_boxes/expansion_box_jungle_canvas","mcwdoors:warped_four_panel_door","littlelogistics:automatic_tee_junction_rail","appflux:insulating_resin","mcwbridges:brick_bridge_pier","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","cfm:stripped_spruce_desk_cabinet","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","blue_skies:trough","cfm:stripped_warped_chair","botania:lens_normal","mcwfences:end_brick_pillar_wall","computercraft:monitor_normal","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","mcwfurnitures:warped_stool_chair","delightful:knives/elementium_knife","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","mcwroofs:spruce_steep_roof","minecraft:purple_dye","mcwfurnitures:jungle_modern_chair","create:cut_calcite_brick_wall_from_stone_types_calcite_stonecutting","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","handcrafted:phantom_trophy","minecraft:quartz_slab","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","delightful:knives/nickel_knife","create:brass_bars_from_ingots_brass_stonecutting","mcwroofs:stone_bricks_roof","cfm:cyan_grill","mcwdoors:cherry_bark_glass_door","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","minecraft:quartz_pillar_from_quartz_block_stonecutting","mysticalagriculture:watering_can","mcwroofs:spruce_attic_roof","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:nether_bricks_roof","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","biomesoplenty:hellbark_chest_boat","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","mcwbridges:spruce_log_bridge_middle","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/windmill_sail","mcwdoors:warped_paper_door","farmersdelight:rice_bag","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","mcwfurnitures:stripped_warped_lower_triple_drawer","supplementaries:speaker_block","farmersdelight:chicken_sandwich","connectedglass:clear_glass_white_pane2","minecraft:polished_andesite","mcwfurnitures:stripped_warped_double_drawer","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwdoors:bamboo_stable_head_door","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","cfm:white_picket_fence","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","cfm:warped_chair","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","handcrafted:kitchen_hood","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwbridges:birch_log_bridge_middle","xnet:connector_blue_dye","twilightforest:equipment/knightmetal_axe","utilitarian:utility/jungle_logs_to_doors","create:polished_cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwroofs:black_lower_roof","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwfurnitures:stripped_oak_desk","mcwroofs:gutter_base_blue","immersiveengineering:crafting/treated_fence","mcwwindows:diorite_louvered_shutter","minecraft:gray_dye","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","computercraft:skull_dan200","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwfurnitures:stripped_spruce_bookshelf_drawer","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","botania:travel_belt","bloodmagic:blood_rune_acceleration","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","ae2:network/parts/energy_acceptor","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","additionallanterns:gold_chain","ae2:materials/advancedcard","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","mcwfences:curved_metal_fence_gate","mysticalagriculture:inferium_growth_accelerator","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","mcwfurnitures:warped_bookshelf_drawer","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwroofs:andesite_lower_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","create:calcite_from_stone_types_calcite_stonecutting","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","mcwfurnitures:stripped_birch_modern_wardrobe","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","handcrafted:spruce_desk","cfm:stripped_warped_coffee_table","farmersdelight:cooking/cooked_rice","mcwwindows:oak_plank_parapet","immersiveengineering:crafting/hempcrete","mcwfurnitures:oak_modern_desk","minecraft:spruce_wood","minecraft:iron_sword","botania:elementium_chestplate","mcwtrpdoors:birch_swamp_trapdoor","minecraft:spruce_fence","aether:leather_helmet_repairing","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","utilitarian:utility/birch_logs_to_trapdoors","railways:crafting/smokestack_caboosestyle","botania:runic_altar","additionallanterns:normal_lantern_red","domum_ornamentum:purple_brick_extra","minecraft:green_dye","quark:building/crafting/oak_ladder","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","minecraft:chiseled_sandstone_from_sandstone_stonecutting","ae2:tools/fluix_upgrade_smithing_template","mcwdoors:cherry_tropical_door","botania:petal_light_blue_double","comforts:hammock_red","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","twilightforest:canopy_boat","botania:quartz_red","sfm:network_tool","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","minecraft:birch_trapdoor","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","productivebees:stonecutter/maple_canvas_hive","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","create:birch_window","cfm:birch_park_bench","mcwroofs:gutter_middle_red","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","botania:abstruse_platform","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","pneumaticcraft:magnet_upgrade","mcwbridges:iron_bridge_pier","mcwwindows:mangrove_blinds","deeperdarker:bloom_chest_boat","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","sophisticatedstorage:spruce_chest","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","mcwfurnitures:warped_lower_triple_drawer","minecraft:powered_rail","productivebees:expansion_boxes/expansion_box_birch_canvas","botania:dye_pink","mcwdoors:birch_bamboo_door","mcwwindows:diorite_pane_window","travelersbackpack:dye_white_sleeping_bag","dyenamics:wine_stained_glass_pane_from_glass_pane","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","twigs:birch_table","enderio:resetting_lever_ten_from_prev","minecraft:dye_white_bed","mcwroofs:magenta_concrete_upper_lower_roof","botania:lens_flare","mcwtrpdoors:print_mystic","mcwroofs:light_gray_terracotta_steep_roof","immersiveengineering:crafting/cushion","mcwroofs:cyan_concrete_steep_roof","pneumaticcraft:gun_ammo_explosive","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","connectedglass:clear_glass_purple2","productivebees:stonecutter/comb_canvas_expansion_box","pneumaticcraft:stone_base","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwroofs:spruce_planks_upper_lower_roof","minecraft:gray_terracotta","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","cfm:stripped_spruce_coffee_table","mcwdoors:bamboo_mystic_door","mcwfurnitures:stripped_birch_drawer_counter","mcwroofs:pink_concrete_attic_roof","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","dyenamics:peach_terracotta","botania:livingrock_wall","minecraft:fishing_rod","xnet:connector_yellow_dye","minecraft:terracotta","delightful:knives/silver_knife","mcwwindows:acacia_blinds","aether:leather_chestplate_repairing","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:yellow_lamp","botania:blue_pavement","mcwroofs:roofing_hammer","mcwdoors:cherry_swamp_door","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","mcwfurnitures:stripped_warped_double_wardrobe","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","dimstorage:dimensional_tank","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","mcwfurnitures:cherry_covered_desk","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","immersiveengineering:crafting/buzzsaw","bloodmagic:largebloodstonebrick","forbidden_arcanus:dark_rune_from_dark_rune_block","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","productivebees:stonecutter/redwood_canvas_hive","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","minecraft:beacon","minecraft:tnt","mcwfurnitures:stripped_birch_bookshelf","mcwdoors:cherry_japanese2_door","minecraft:andesite_slab_from_andesite_stonecutting","mcwdoors:spruce_stable_door","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","utilitarian:utility/warped_logs_to_slabs","mcwfurnitures:stripped_warped_drawer","minecraft:flint_and_steel","railways:crafting/smokestack_streamlined","ad_astra:white_flag","mcwdoors:cherry_nether_door","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","cfm:spruce_upgraded_gate","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","cfm:stripped_birch_chair","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_birch_cabinet","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","farmersdelight:oak_cabinet","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","aiotbotania:terra_shovel","mcwfurnitures:spruce_cupboard_counter","mcwdoors:cherry_barn_door","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:hay","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwdoors:cherry_modern_door","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","bloodmagic:blood_rune_displacement","productivebees:expansion_boxes/expansion_box_snake_block_canvas","mcwroofs:warped_top_roof","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","mcwfurnitures:cherry_triple_drawer","rftoolsbase:tablet","mcwroofs:magenta_terracotta_lower_roof","megacells:cells/portable/portable_fluid_cell_64m","railcraft:routing_table_book","ad_astra:vent","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","cfm:stripped_warped_table","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:birch_planks_upper_lower_roof","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:item_shelf","handcrafted:terracotta_plate","supplementaries:stone_tile","reliquary:uncrafting/snowball","mcwroofs:light_blue_terracotta_roof","handcrafted:calcite_corner_trim","rftoolsbuilder:blue_shield_template_block","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","mcwfurnitures:stripped_warped_cupboard_counter","silentgear:coating_smithing_template","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","handcrafted:berry_jam_jar","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","mcwbridges:birch_rail_bridge","minecraft:glass_pane","supplementaries:timber_brace","alltheores:iridium_ingot_from_raw_blasting","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","handcrafted:birch_cupboard","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","railways:crafting/smokestack_oilburner","mcwroofs:warped_lower_roof","securitycraft:sonic_security_system","minecraft:clay","ae2:network/crafting/cpu_crafting_unit","mcwbiomesoplenty:pine_pane_window","mcwfurnitures:stripped_birch_lower_bookshelf_drawer","mcwroofs:magenta_concrete_roof","ad_astra:blue_flag","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","travelersbackpack:light_blue_sleeping_bag","securitycraft:protecto","immersiveengineering:crafting/conveyor_basic_covered","mcwpaths:andesite_basket_weave_paving","utilitix:jungle_shulker_boat","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","mcwfurnitures:stripped_birch_coffee_table","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","additionallanterns:normal_lantern_colorless","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_spruce_cupboard_counter","cfm:orange_kitchen_drawer","mcwroofs:spruce_planks_top_roof","ae2:materials/annihilationcore","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","handcrafted:spruce_pillar_trim","minecraft:leather_boots","railcraft:tank_detector","mcwdoors:warped_beach_door","create:crafting/appliances/netherite_diving_boots_from_netherite","railcraft:steel_spike_maul","handcrafted:sandstone_corner_trim","mcwfurnitures:stripped_warped_modern_chair","pneumaticcraft:classify_filter","immersiveengineering:crafting/balloon","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","mcwfurnitures:warped_cupboard_counter","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","mcwlights:pink_paper_lamp","pneumaticcraft:gun_ammo_incendiary","ad_astra:white_industrial_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","immersiveengineering:crafting/wire_aluminum","mcwfurnitures:birch_end_table","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","mcwfurnitures:cherry_lower_triple_drawer","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","mcwroofs:birch_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","minecraft:magma_block","mcwwindows:crimson_stem_parapet","minecraft:spruce_pressure_plate","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","nethersdelight:nether_brick_smoker","connectedglass:clear_glass_black_pane3","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","handcrafted:spruce_nightstand","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","aether:aether_iron_nugget_from_smelting","quark:tweaks/crafting/utility/tools/stone_hoe","sfm:cable","minecraft:hay_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:white_upper_steep_roof","alltheores:ruby_from_hammer_crushing","twilightforest:equipment/knightmetal_chestplate","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","botania:phantom_ink","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","railways:crafting/smokestack_coalburner","mcwfences:modern_granite_wall","mcwtrpdoors:warped_barrel_trapdoor","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","bloodmagic:primitive_hydration_cell","pneumaticcraft:etching_tank","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","bloodmagic:ritual_stone_master","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwroofs:blue_concrete_top_roof","bambooeverything:bamboo_ladder","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","mcwtrpdoors:warped_bark_trapdoor","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","mcwdoors:oak_waffle_door","bigreactors:blasting/graphite_from_charcoal","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","create:crafting/kinetics/rotation_speed_controller","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mysticalagriculture:inferium_gemstone","modularrouters:detector_module","mcwtrpdoors:warped_beach_trapdoor","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","railcraft:goggles","mcwdoors:birch_beach_door","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","domum_ornamentum:brick_extra","mcwdoors:birch_bark_glass_door","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","mcwfurnitures:birch_modern_desk","bloodmagic:blood_rune_aug_capacity","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","twilightforest:tf_to_vanilla_lilypad","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","pneumaticcraft:liquid_compressor","create:polished_cut_calcite_wall_from_stone_types_calcite_stonecutting","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwbiomesoplenty:empyreal_window2","mcwtrpdoors:bamboo_barrel_trapdoor","connectedglass:clear_glass_red_pane2","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","reliquary:uncrafting/redstone","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","ad_astra:red_flag","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/generator","ad_astra:small_red_industrial_lamp","immersiveengineering:crafting/cokebrick_from_slab","mcwwindows:warped_stem_four_window","securitycraft:sentry","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","handcrafted:white_crockery_combo","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","comforts:hammock_light_blue","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","twilightforest:compressed_blocks/arctic_block","mcwroofs:pink_terracotta_top_roof","cfm:warped_cabinet","create:crafting/logistics/redstone_contact","handcrafted:light_blue_cushion","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","mcwpaths:sandstone_crystal_floor_path","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","mcwtrpdoors:cherry_beach_trapdoor","mcwfurnitures:stripped_warped_wardrobe","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","ae2:tools/certus_quartz_spade","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","farmersdelight:skillet","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","megacells:cells/portable/portable_fluid_cell_256m","mcwlights:orange_paper_lamp","mcwtrpdoors:cherry_classic_trapdoor","mcwbiomesoplenty:stripped_hellbark_pane_window","twilightforest:equipment/fortification_scepter","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","ae2:network/cables/dense_covered_black","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","occultism:crafting/spirit_campfire","utilitix:piston_controller_rail","mcwroofs:bricks_upper_lower_roof","create:crafting/curiosities/peculiar_bell","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","delightful:knives/brass_knife","minecraft:white_stained_glass","connectedglass:clear_glass_light_gray2","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","immersiveengineering:crafting/metal_ladder_none","alltheores:aluminum_dust_from_hammer_crushing","securitycraft:taser","mcwdoors:warped_japanese_door","mcwfurnitures:stripped_warped_bookshelf_drawer","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","domum_ornamentum:white_brick_extra","aiotbotania:livingrock_hoe","connectedglass:scratched_glass_red_pane2","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","ae2:network/parts/toggle_bus","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","minecraft:blue_bed","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","mcwdoors:birch_tropical_door","croptopia:campfire_caramel","cfm:warped_table","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbridges:rope_spruce_bridge","mcwbiomesoplenty:stripped_hellbark_log_window2","allthecompressed:compress/deepslate_1x","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","mcwfurnitures:stripped_birch_double_wardrobe","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","botania:manasteel_shovel","handcrafted:oven","handcrafted:calcite_pillar_trim","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","minecraft:red_stained_glass","mcwroofs:white_steep_roof","minecraft:light_gray_stained_glass_pane_from_glass_pane","mcwpaths:cobbled_deepslate_crystal_floor_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwlights:jungle_tiki_torch","immersiveengineering:crafting/plate_aluminum_hammering","twigs:stone_column_stonecutting","cfm:lime_picket_gate","mcwfurnitures:stripped_warped_counter","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","sophisticatedbackpacks:tool_swapper_upgrade","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","minecraft:gray_stained_glass_pane_from_glass_pane","allthecompressed:compress/andesite_1x","cfm:birch_cabinet","mcwwindows:diorite_four_window","minecraft:stone_slab","mcwfurnitures:stripped_spruce_desk","pneumaticcraft:compressed_bricks","productivebees:stonecutter/comb_canvas_hive","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","create:small_calcite_brick_wall_from_stone_types_calcite_stonecutting","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","minecraft:blast_furnace","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","additionallanterns:normal_lantern_light_blue","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","minecraft:light_blue_stained_glass_pane_from_glass_pane","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","dyenamics:bed/mint_bed_frm_white_bed","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","mcwtrpdoors:warped_classic_trapdoor","mcwfurnitures:stripped_cherry_lower_triple_drawer","ae2:network/parts/import_bus","croptopia:campfire_molasses","minecraft:dye_red_bed","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","ae2:network/upgrade_wireless_crafting_terminal","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","minecraft:blue_stained_glass_pane_from_glass_pane","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwtrpdoors:bamboo_bark_trapdoor","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","silentgear:upgrade_base","croptopia:melon_juice","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mcwdoors:warped_whispering_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","botania:starfield","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","minecraft:oak_chest_boat","mcwdoors:spruce_waffle_door","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","pneumaticcraft:empty_pcb_from_failed_pcb","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","allthemodium:smithing/allthemodium_chestplate_smithing","mcwroofs:spruce_planks_lower_roof","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","botania:yellow_pavement","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","pneumaticcraft:logistics_core","create:crafting/kinetics/yellow_seat","mcwpaths:andesite_windmill_weave_path","comforts:hammock_blue","mcwdoors:spruce_swamp_door","modularrouters:blank_upgrade","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","mcwfurnitures:stripped_cherry_stool_chair","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","ae2:shaped/walls/sky_stone_block","cfm:birch_chair","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","cfm:stripped_spruce_cabinet","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","minecraft:mangrove_boat","minecraft:bread","mcwdoors:bamboo_whispering_door","mcwroofs:green_terracotta_steep_roof","mcwdoors:spruce_barn_glass_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","mcwdoors:garage_silver_door","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","cfm:birch_desk","mcwfurnitures:spruce_coffee_table","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","botania:horn_grass","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","undergarden:smoke_gloomper_leg","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","additionallanterns:normal_lantern_brown","handcrafted:red_sheet","minecraft:iron_shovel","tombstone:dark_marble","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwwindows:stone_brick_arrow_slit","ae2:smelting/smooth_sky_stone_block","chimes:amethyst_chimes","mcwfurnitures:jungle_desk","productivebees:stonecutter/willow_canvas_hive","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","croptopia:ham_sandwich","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","ad_astra:space_suit","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwwindows:dark_oak_louvered_shutter","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","alltheores:osmium_dust_from_hammer_ingot_crushing","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","minecraft:pink_carpet","botania:cacophonium","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","create:calcite_pillar_from_stone_types_calcite_stonecutting","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mythicbotany:ice_ring","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","handcrafted:birch_table","rftoolsutility:counter_module","aether:netherite_boots_repairing","botania:lens_redirect","cfm:stripped_jungle_crate","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwdoors:bamboo_barn_door","mcwlights:cyan_paper_lamp","mcwdoors:warped_japanese2_door","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:black_roof_block","handcrafted:spruce_dining_bench","mcwfurnitures:stripped_birch_wardrobe","botania:elementium_shovel","immersiveengineering:crafting/wooden_grip","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","mcwfurnitures:birch_table","mythicbotany:fire_ring","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwroofs:spruce_planks_roof","twilightforest:charm_of_keeping_2","mcwbiomesoplenty:stripped_hellbark_log_four_window","minecraft:spruce_door","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_birch_covered_desk","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","pneumaticcraft:huge_tank","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:cherry_end_table","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","modularrouters:dropper_module","twilightforest:lich_banner_pattern","mcwbiomesoplenty:magic_highley_gate","utilitarian:utility/birch_logs_to_doors","computercraft:wired_modem","ae2:tools/fluix_sword","utilitarian:utility/cherry_logs_to_trapdoors","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","dyenamics:mint_terracotta","mcwwindows:pink_curtain","sophisticatedstorage:birch_limited_barrel_1","mcwlights:soul_jungle_tiki_torch","productivebees:stonecutter/rosewood_canvas_expansion_box","mcwwindows:light_gray_curtain","sophisticatedstorage:birch_limited_barrel_4","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","sophisticatedstorage:birch_limited_barrel_2","twilightforest:equipment/knightmetal_boots","delightful:knives/refined_obsidian_knife","sophisticatedstorage:birch_limited_barrel_3","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","twilightforest:knight_phantom_banner_pattern","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwfurnitures:stripped_cherry_counter","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","ae2:network/blocks/spatial_io_port","mcwdoors:bamboo_swamp_door","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","deepresonance:radiation_suit_boots","supplementaries:wrench","mcwroofs:blue_terracotta_upper_steep_roof","minecraft:bow","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwroofs:cherry_lower_roof","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","chemlib:periodic_table","botania:mushroom_stew","minecraft:andesite_stairs","delightful:knives/aluminum_knife","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwdoors:bamboo_japanese_door","croptopia:apple_juice","cfm:purple_kitchen_sink","cfm:black_cooler","minecraft:jungle_slab","twigs:polished_calcite_stonecutting","supplementaries:sconce","silentgear:rough_rod","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","dyenamics:maroon_stained_glass_pane_from_glass_pane","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:birch_top_roof","aether:skyroot_bed","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","mcwfurnitures:spruce_glass_table","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","dyenamics:bed/aquamarine_bed_frm_white_bed","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:jacaranda_plank_window2","sophisticatedbackpacks:feeding_upgrade","computercraft:skull_cloudy","minecraft:melon_seeds","minecraft:granite","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwfurnitures:spruce_covered_desk","botania:ghost_rail","minecraft:firework_rocket_simple","pneumaticcraft:logistics_drone","sophisticatedstorage:smoking_upgrade","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","mcwdoors:spruce_mystic_door","mcwtrpdoors:spruce_beach_trapdoor","aether:netherite_shovel_repairing","enderio:enderios","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","mcwbridges:balustrade_nether_bricks_bridge","pneumaticcraft:wall_lamp_inverted_brown","cfm:stripped_birch_bedside_cabinet","botania:black_pavement","mcwroofs:andesite_roof","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","supplementaries:pedestal","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","ae2:network/cables/glass_yellow","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","minecraft:dye_white_wool","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","dyenamics:rose_terracotta","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","mcwfurnitures:stripped_birch_triple_drawer","handcrafted:silverfish_trophy","bloodmagic:blood_rune_charging","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwdoors:jungle_tropical_door","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","cfm:lime_kitchen_counter","mcwdoors:warped_barn_door","travelersbackpack:red_sleeping_bag","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","comforts:sleeping_bag_blue","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","botania:mana_ring_greater","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","tombstone:carmin_marble","botania:elf_quartz","mcwdoors:oak_barn_door","botania:alchemy_catalyst","mcwfurnitures:spruce_double_drawer_counter","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","connectedglass:clear_glass_black2","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","mcwpaths:sandstone_windmill_weave_slab","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","botania:conversions/elementium_block_deconstruct","productivebees:stonecutter/hellbark_canvas_expansion_box","immersiveengineering:crafting/ersatz_leather","bloodmagic:ritual_stone_blank","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","twilightdelight:torchberries_crate","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","alltheores:lead_plate","mcwfurnitures:stripped_spruce_coffee_table","additionallanterns:obsidian_chain","minecraft:ender_eye","mcwfurnitures:spruce_double_wardrobe","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwbiomesoplenty:umbran_plank_window","minecraft:birch_slab","enderio:resetting_lever_three_hundred","mcwdoors:warped_modern_door","mcwlights:cross_lantern","mcwlights:brown_lamp","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwwindows:bricks_four_window","railcraft:receiver_circuit","securitycraft:reinforced_packed_mud","immersiveengineering:crafting/hemp_fabric","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","bigreactors:fluidizer/fluidinjector","mcwtrpdoors:birch_tropical_trapdoor","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","rftoolsbase:manual","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","immersiveengineering:crafting/conveyor_dropper","sophisticatedstorage:feeding_upgrade","mcwfurnitures:cherry_bookshelf_drawer","ae2:network/parts/terminals_pattern_access","deepresonance:radiation_suit_leggings","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","mcwfurnitures:birch_triple_drawer","mcwdoors:print_mystic","ae2:network/blocks/io_condenser","sophisticatedbackpacks:smoking_upgrade","supplementaries:stonecutting/stone_tile","railcraft:steel_tank_wall","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","additionallanterns:normal_lantern_purple","handcrafted:birch_shelf","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","securitycraft:trophy_system","botania:elementium_axe","twilightforest:firefly_jar","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwfurnitures:stripped_cherry_wardrobe","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","twilightdelight:torchberry_cookie","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","farmersdelight:pie_crust","rftoolsbuilder:vehicle_card","mcwlights:warped_tiki_torch","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","railcraft:age_detector","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","botania:drum_gathering","mcwwindows:jungle_plank_window2","ad_astra:space_pants","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","mcwfurnitures:stripped_birch_end_table","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","productivebees:hives/advanced_dark_oak_canvas_hive","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","twigs:calcite_stairs","enderio:resetting_lever_five_inv","handcrafted:spruce_corner_trim","mcwdoors:garage_white_door","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwfurnitures:stripped_birch_modern_desk","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","bloodmagic:ritual_reader","pneumaticcraft:pressure_gauge_module","botania:turntable","cfm:stripped_birch_table","ae2:network/cells/item_storage_components_cell_1k_part","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","utilitarian:utility/warped_logs_to_trapdoors","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","supplementaries:daub","alltheores:zinc_dust_from_hammer_crushing","minecraft:netherite_scrap","railcraft:steel_chestplate","mcwfurnitures:stripped_jungle_end_table","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","mcwdoors:spruce_japanese2_door","minecraft:cut_sandstone","handcrafted:pink_cushion","mcwroofs:oak_planks_roof","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","botania:petal_white","ae2:decorative/quartz_block","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwfurnitures:birch_drawer_counter","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:nether_brick_slab","mcwfurnitures:birch_bookshelf","mcwbiomesoplenty:hellbark_highley_gate","create:crafting/logistics/redstone_link","immersiveengineering:crafting/treated_scaffold","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","botania:mana_tablet","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","connectedglass:borderless_glass_white_pane2","minecraft:campfire","dyenamics:peach_stained_glass_pane_from_glass_pane","allthearcanistgear:unobtainium_boots_smithing","mcwfurnitures:stripped_warped_modern_wardrobe","travelersbackpack:standard_no_tanks","cfm:white_kitchen_sink","securitycraft:reinforced_birch_fence","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","megacells:cells/portable/portable_item_cell_256m","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:logistics_configurator","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:1372,warning_level:0}}