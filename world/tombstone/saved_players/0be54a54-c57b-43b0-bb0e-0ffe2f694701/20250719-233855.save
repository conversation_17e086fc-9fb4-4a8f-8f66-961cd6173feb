{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:8,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:jump_boost"}],Air:300s,Attributes:[{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"attributeslib:armor_shred"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]},{Amount:14.0d,Name:"Heart Containers",Operation:0,UUID:[I;-**********,-990297069,-**********,-847674717]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"minecraft:generic.attack_knockback"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:0.0d,Name:"attributeslib:overheal"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:1.0d,Name:"attributeslib:arrow_velocity"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"attributeslib:arrow_damage"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{TrashSlot:{Count:1b,id:"minecraft:stick"},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-123270031,103236873,-**********,551414798],hasTentacle:0b},"comforts:sleep_data":{sleepTime:11684998L,tiredTime:11685121L,wakeTime:11685080L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:villager_hat"}],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:pickaxe_heater"},{Count:1b,Slot:1,id:"artifacts:vampiric_glove"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:bunny_hoppers"}],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:142,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:11454},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:7,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:lapis_lazuli",ItemStack:{Count:9b,id:"minecraft:lapis_lazuli"}}],SelectedRecipe:"minecraft:lapis_lazuli"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:67,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:cooked_beef","minecraft:bread"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","c9c0e2c2-59f5-4640-a507-dbfea5bc9042"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:-10490,tb_last_ground_location_y:95,tb_last_ground_location_z:-20906,twilightforest_banished:1b},"quark:trying_crawl":0b,simplylight_unlocked:2},Health:34.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"sophisticatedbackpacks:iron_backpack",tag:{contentsUuid:[I;-612881550,**********,-**********,**********],inventorySlots:54,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:2}},{Count:1b,Slot:1b,id:"ars_nouveau:caster_tome",tag:{"ars_nouveau:caster":{current_slot:0,flavor:"Creates a temporary tunnel of blocks.",hidden_recipe:"",is_hidden:0b,spell_count:1,spells:{spell0:{name:"The Shadow's Temporary Tunnel",recipe:{part0:"ars_nouveau:glyph_touch",part1:"ars_nouveau:glyph_intangible",part2:"ars_nouveau:glyph_aoe",part3:"ars_nouveau:glyph_aoe",part4:"ars_nouveau:glyph_pierce",part5:"ars_nouveau:glyph_pierce",part6:"ars_nouveau:glyph_pierce",part7:"ars_nouveau:glyph_pierce",part8:"ars_nouveau:glyph_pierce",part9:"ars_nouveau:glyph_extend_time",size:10},sound:{pitch:1.0f,soundTag:{id:"ars_nouveau:fire_family"},volume:1.0f},spellColor:{b:180,g:25,r:255,type:"ars_nouveau:constant"}}}},display:{Name:'{"italic":true,"color":"dark_purple","text":"The Shadow\'s Temporary Tunnel"}'}}},{Count:34b,Slot:2b,id:"minecraft:torch"},{Count:2b,Slot:4b,id:"immersiveengineering:coal_coke"},{Count:1b,Slot:5b,id:"minecraft:bow",tag:{Damage:0,affix_data:{affixes:{"apotheosis:ranged/attribute/agile":0.33417398f,"apotheosis:ranged/attribute/elven":0.97314626f,"apotheosis:ranged/attribute/streamlined":0.49408966f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:ranged/attribute/streamlined"},"",{"translate":"affix.apotheosis:ranged/attribute/elven.suffix"}]}',rarity:"apotheosis:uncommon",uuids:[[I;-1076468719,1136083460,-1531668712,-1270021138]]},apoth_rchest:1b}},{Count:1b,Slot:6b,id:"minecraft:iron_axe",tag:{Damage:1,affix_data:{affixes:{"apotheosis:heavy_weapon/attribute/berserking":0.44821876f,"apotheosis:heavy_weapon/attribute/forceful":0.22788471f,"apotheosis:heavy_weapon/attribute/shredding":0.55436456f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:heavy_weapon/attribute/shredding"},"",{"translate":"affix.apotheosis:heavy_weapon/attribute/berserking.suffix"}]}',rarity:"apotheosis:uncommon",sockets:1,uuids:[[I;1449063602,-1632418275,-1692890297,-1406139525]]},apoth_rchest:1b}},{Count:1b,Slot:7b,id:"minecraft:iron_pickaxe",tag:{Damage:62}},{Count:1b,Slot:8b,id:"railcraft:iron_spike_maul",tag:{Damage:0}},{Count:64b,Slot:9b,id:"minecraft:jungle_planks"},{Count:1b,Slot:10b,id:"crafting_on_a_stick:crafting_table"},{Count:6b,Slot:11b,id:"minecraft:cooked_beef"},{Count:1b,Slot:14b,id:"sophisticatedbackpacks:backpack"},{Count:1b,Slot:15b,id:"sophisticatedbackpacks:backpack",tag:{contentsUuid:[I;1733112938,-1681175653,-2072223534,-157042649],inventorySlots:27,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:1}},{Count:1b,Slot:16b,id:"sophisticatedbackpacks:copper_backpack",tag:{contentsUuid:[I;108650512,-1306049842,-1338491301,-1710618992],inventorySlots:45,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:1}},{Count:11b,Slot:17b,id:"minecraft:bread"},{Count:1b,Slot:18b,id:"pneumaticcraft:micromissiles",tag:{Damage:0}},{Count:1b,Slot:21b,id:"ars_elemental:water_caster_tome",tag:{"ars_nouveau:caster":{current_slot:0,flavor:"Summon two rideable Dolphins for a short time.",hidden_recipe:"",is_hidden:0b,spell_count:1,spells:{spell0:{name:"Poseidon's Steed",recipe:{part0:"ars_nouveau:glyph_self",part1:"ars_nouveau:glyph_summon_steed",part2:"ars_nouveau:glyph_aoe",part3:"ars_nouveau:glyph_extend_time",part4:"ars_nouveau:glyph_extend_time",size:5},sound:{pitch:1.0f,soundTag:{id:"ars_nouveau:fire_family"},volume:1.0f},spellColor:{b:180,g:25,r:255,type:"ars_nouveau:constant"}}}},display:{Name:'{"italic":true,"color":"dark_purple","text":"Poseidon\'s Steed"}'}}},{Count:1b,Slot:22b,id:"minecraft:anvil"},{Count:1b,Slot:100b,id:"pneumaticcraft:compressed_iron_boots",tag:{Damage:0}},{Count:1b,Slot:101b,id:"minecraft:iron_leggings",tag:{Damage:0,affix_data:{affixes:{"irons_spellbooks:armor/attribute/mana":0.33041567f},name:'{"color":"#808080","translate":"misc.apotheosis.affix_name.two","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",""]}',rarity:"apotheosis:common",uuids:[[I;2119703976,-293453252,-1973081334,-1073326317]]},apoth_rchest:1b}},{Count:1b,Slot:102b,id:"minecraft:iron_chestplate",tag:{Damage:0,affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.631411f,"apotheosis:armor/attribute/steel_touched":0.59196067f,"apotheosis:armor/dmg_reduction/blast_forged":0.11222726f,"apotheosis:durable":0.07f,"irons_spellbooks:armor/attribute/mana":0.59923124f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",{"translate":"affix.apotheosis:armor/attribute/steel_touched.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;1538615368,-1778694774,-1544566921,1460440267]]},apoth_rchest:1b}},{Count:1b,Slot:103b,id:"pneumaticcraft:compressed_iron_helmet",tag:{Damage:0}}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-10250.707817242668d,76.0d,-20969.388506611518d],Railways_DataVersion:2,Rotation:[-3.557312f,16.095167f],Score:50,SelectedItemSlot:4,SleepTimer:0s,SpawnAngle:52.850258f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:-10296,SpawnY:64,SpawnZ:-21029,Spigot.ticksLived:11453,UUID:[I;199576148,-981777488,-1156706306,795428609],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-2817498632069044L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:4,XpP:0.6666668f,XpSeed:-78911929,XpTotal:50,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752937030289L,keepLevel:0b,lastKnownName:"TheYuXi",lastPlayed:1752939535258L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.20681055f,foodLevel:14,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","sfm:disk","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","trashcans:energy_trash_can","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","handcrafted:spider_trophy","mcwtrpdoors:jungle_cottage_trapdoor","cfm:fridge_dark","chimes:copper_chimes","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","mcwpaths:jungle_planks_path","create:crafting/appliances/crafting_blueprint","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwdoors:jungle_nether_door","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwfences:acacia_wired_fence","simplylight:illuminant_light_blue_block_dyed","mcwwindows:black_mosaic_glass","railcraft:controller_circuit","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","cfm:spatula","securitycraft:reinforced_cherry_fence_gate","pneumaticcraft:charging_upgrade","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","energymeter:meter","mcwlights:covered_lantern","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","minecolonies:shapetool","supplementaries:bubble_blower","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwroofs:gutter_base_light_gray","additionallanterns:red_sandstone_chain","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","mcwlights:copper_candle_holder","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","reliquary:mob_charm_fragments/slime","aquaculture:birch_fish_mount","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","blue_skies:comet_bookshelf","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","biomesoplenty:mahogany_boat","aquaculture:note_hook","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","aquaculture:double_hook","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","pneumaticcraft:regulator_tube_module","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","utilitarian:angel_block","simplylight:illuminant_orange_block_on_toggle","railcraft:brass_ingot_crafted_with_ingots","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","simplylight:edge_light_bottom_from_top","dyenamics:bed/navy_bed","pneumaticcraft:transfer_gadget","securitycraft:username_logger","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwwindows:red_sandstone_window","mcwwindows:jungle_four_window","handcrafted:jungle_side_table","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","handcrafted:jungle_bench","securitycraft:keypad_frame","pneumaticcraft:compressed_iron_chestplate","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","minecraft:jungle_door","pneumaticcraft:vortex_tube","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwpaths:red_sandstone_diamond_paving","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","twilightdelight:cutting/ice_bow","handcrafted:red_sandstone_pillar_trim","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","pneumaticcraft:wall_lamp_inverted_white","railcraft:world_spike","mcwlights:golden_chain","mcwdoors:garage_gray_door","mcwbiomesoplenty:magic_picket_fence","securitycraft:reinforced_purple_stained_glass","littlelogistics:seater_car","everythingcopper:copper_chestplate","cfm:green_grill","handcrafted:jungle_drawer","pneumaticcraft:search_upgrade","cfm:orange_grill","cfm:brown_grill","minecraft:cut_red_sandstone_from_red_sandstone_stonecutting","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","cfm:jungle_desk","reliquary:mob_charm_fragments/creeper","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","minecraft:clock","mcwfences:nether_brick_pillar_wall","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","additionallanterns:end_stone_lantern","aquaculture:iron_fillet_knife","travelersbackpack:coal","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","minecraft:golden_pickaxe","minecraft:bamboo_raft","pneumaticcraft:refinery","pneumaticcraft:thermal_compressor","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwwindows:white_mosaic_glass","minecraft:redstone_torch","mcwroofs:jungle_upper_steep_roof","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwfences:railing_andesite_wall","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","minecraft:target","additionallanterns:andesite_lantern","utilitix:mob_yoinker","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwlights:golden_low_candle_holder","mcwroofs:gutter_base_gray","mcwpaths:red_sandstone_crystal_floor_path","simplylight:illuminant_light_gray_block_on_dyed","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","supplementaries:cog_block","mcwlights:golden_chandelier","cfm:blue_grill","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwtrpdoors:metal_full_trapdoor","mcwpaths:red_sandstone_flagstone_slab","supplementaries:bomb","dyenamics:navy_stained_glass","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","pneumaticcraft:elevator_frame","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwwindows:birch_curtain_rod","pneumaticcraft:wall_lamp_cyan","minecraft:red_sandstone_slab_from_red_sandstone_stonecutting","create:crafting/kinetics/fluid_pipe","immersiveengineering:crafting/torch","pneumaticcraft:pressure_chamber_glass_x4","mcwfences:warped_pyramid_gate","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","pneumaticcraft:pressure_chamber_glass_x1","allthecompressed:compress/lapis_block_1x","railcraft:signal_circuit","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","dyenamics:bubblegum_stained_glass","undergarden:gloom_o_lantern","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","supplementaries:flags/flag_yellow","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","pneumaticcraft:heat_pipe","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","cfm:red_grill","mcwfences:railing_blackstone_wall","mcwroofs:red_sandstone_attic_roof","mcwlights:soul_double_street_lamp","mcwpaths:red_sandstone_flagstone","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","travelersbackpack:enderman","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwlights:oak_tiki_torch","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","dyenamics:conifer_stained_glass","immersiveengineering:crafting/toolbox","cfm:light_gray_trampoline","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","bloodmagic:blood_altar","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_stained_glass","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","shrink:shrinking_device","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","mcwroofs:red_sandstone_top_roof","mcwdoors:jungle_japanese2_door","minecraft:smooth_red_sandstone","pneumaticcraft:wall_lamp_pink","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:red_sandstone_four_window","mcwroofs:gutter_base_magenta","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","minecraft:red_sandstone_stairs_from_red_sandstone_stonecutting","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","cfm:purple_grill","create:crafting/kinetics/hose_pulley","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","minecraft:cut_red_sandstone_slab_from_red_sandstone_stonecutting","mcwtrpdoors:jungle_bark_trapdoor","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","rftoolsutility:fluidplus_module","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwbiomesoplenty:dead_picket_fence","pneumaticcraft:vortex_cannon","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwfurnitures:jungle_counter","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwdoors:jungle_swamp_door","minecraft:wooden_hoe","immersiveengineering:crafting/light_engineering","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","mcwpaths:red_sandstone_square_paving","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","securitycraft:reinforced_pink_stained_glass","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","littlelogistics:vessel_charger","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","railcraft:radio_circuit","minecraft:smooth_red_sandstone_stairs","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","minecraft:cut_red_sandstone_slab_from_cut_red_sandstone_stonecutting","minecraft:wooden_axe","minecraft:dispenser","mcwfences:panelled_metal_fence_gate","supplementaries:sign_post_jungle","mcwlights:golden_double_candle_holder","mcwwindows:metal_curtain_rod","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:red_sandstone_upper_lower_roof","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","supplementaries:daub_brace","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","securitycraft:reinforced_green_stained_glass","pneumaticcraft:wall_lamp_blue","minecraft:iron_bars","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","handcrafted:jungle_desk","create:crafting/kinetics/copper_valve_handle","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","supplementaries:flags/flag_magenta","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_red","handcrafted:red_sandstone_corner_trim","aether:skyroot_piston","pneumaticcraft:manometer","securitycraft:alarm","rftoolsutility:charged_porter","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","pneumaticcraft:wall_lamp_inverted_magenta","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","additionallanterns:amethyst_lantern","utilitix:comparator_redirector_down","create:copper_shingles_from_ingots_copper_stonecutting","mcwfences:granite_grass_topped_wall","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","pneumaticcraft:pressure_chamber_interface","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","mcwfences:railing_nether_brick_wall","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","cfm:black_trampoline","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","alchemistry:compactor","cfm:jungle_upgraded_fence","minecraft:dropper","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","dyenamics:bed/maroon_bed","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","mcwbiomesoplenty:pine_picket_fence","mcwfences:bamboo_highley_gate","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","mcwroofs:jungle_roof","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwfences:jungle_curved_gate","mcwpaths:red_sandstone_strewn_rocky_path","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:light_gray_grill","mcwtrpdoors:metal_trapdoor","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","supplementaries:daub_frame","railcraft:signal_tuner","pneumaticcraft:minigun_upgrade","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","aquaculture:iron_fishing_rod","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:red_sandstone_wall","mcwbiomesoplenty:mahogany_pyramid_gate","cfm:pink_trampoline","securitycraft:briefcase","mcwwindows:green_mosaic_glass","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwlights:copper_triple_candle_holder","cfm:white_grill","minecraft:iron_nugget_from_smelting","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","handcrafted:jungle_dining_bench","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","ae2:network/cables/dense_smart_fluix","aether:golden_pendant","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","simplylight:illuminant_brown_block_on_toggle","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwfences:oak_picket_fence","pneumaticcraft:compressed_iron_leggings","mcwfences:mangrove_curved_gate","mcwpaths:red_sandstone_flagstone_stairs","mcwfences:modern_andesite_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","utilitix:advanced_brewery","mcwlights:copper_double_candle_holder","supplementaries:cage","mcwfurnitures:jungle_large_drawer","mcwfences:diorite_grass_topped_wall","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:metal_four_window","simplylight:illuminant_purple_block_on_dyed","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","mcwroofs:jungle_planks_top_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","additionallanterns:diamond_lantern","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","securitycraft:reinforced_lime_stained_glass_pane_from_glass","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","simplylight:illuminant_brown_block_on_dyed","eccentrictome:tome","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","itemcollectors:basic_collector","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwpaths:red_sandstone_crystal_floor_slab","buildinggadgets2:gadget_copy_paste","handcrafted:golden_wide_pot","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","minecraft:red_sandstone_stairs","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","pneumaticcraft:redstone_module","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","mcwbiomesoplenty:redwood_wired_fence","allthetweaks:ender_pearl_block","computercraft:computer_advanced","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","everythingcopper:copper_door","mcwlights:copper_chandelier","reliquary:uncrafting/slime_ball","mcwdoors:jungle_paper_door","mcwpaths:red_sandstone_clover_paving","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","everythingcopper:copper_pressure_plate","mcwroofs:jungle_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","securitycraft:reinforced_birch_fence_gate","mcwroofs:gutter_base_light_blue","additionallanterns:diorite_lantern","mcwpaths:red_sandstone_running_bond_path","pneumaticcraft:dispenser_upgrade","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","rftoolsutility:inventoryplus_module","minecraft:yellow_stained_glass","mcwfurnitures:jungle_end_table","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwwindows:red_sandstone_pane_window","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","handcrafted:goat_trophy","mcwroofs:red_sandstone_lower_roof","bigreactors:blasting/graphite_from_coal","paraglider:paraglider","simplylight:illuminant_lime_block_on_dyed","utilitix:anvil_cart","securitycraft:reinforced_white_stained_glass_pane_from_dye","pneumaticcraft:wall_lamp_red","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbridges:jungle_bridge_pier","minecraft:lapis_block","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","undergarden:catalyst","immersiveengineering:crafting/voltmeter","minecraft:iron_axe","create:crafting/kinetics/fluid_valve","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","securitycraft:blacklist_module","mcwbiomesoplenty:snowblossom_hedge","minecraft:oak_boat","sophisticatedstorage:jungle_chest","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","securitycraft:smart_module","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","minecraft:jungle_fence","minecraft:gold_ingot_from_gold_block","minecraft:glass_bottle","mcwfences:mesh_metal_fence","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","botania:speed_up_belt","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwlights:bamboo_tiki_torch","mcwpaths:red_sandstone_crystal_floor","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alchemistry:combiner","mcwdoors:jungle_stable_head_door","pneumaticcraft:air_canister","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:candle_holders/candle_holder_yellow","supplementaries:slice_map","mcwpaths:red_sandstone_windmill_weave_stairs","appflux:insulating_resin","occultism:crafting/butcher_knife","dyenamics:banner/maroon_banner","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwlights:mangrove_tiki_torch","aether:iron_pendant","pneumaticcraft:kerosene_lamp","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","simplylight:illuminant_slab_from_panel","cfm:gray_grill","supplementaries:flags/flag_purple","paraglider:cosmetic/rito_goddess_statue","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","pneumaticcraft:wall_lamp_inverted_cyan","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","handcrafted:golden_thick_pot","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","farmersdelight:iron_knife","mcwroofs:red_sandstone_roof","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","additionallanterns:warped_lantern","minecraft:spruce_boat","minecraft:gold_block","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","pneumaticcraft:compressed_bricks_from_tile","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","mcwfences:jungle_horse_fence","handcrafted:salmon_trophy","minecraft:bucket","minecraft:cut_red_sandstone_slab","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","minecraft:chiseled_red_sandstone_from_red_sandstone_stonecutting","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwlights:golden_small_chandelier","mcwfences:railing_deepslate_brick_wall","silentgear:emerald_shard","mcwwindows:lime_mosaic_glass","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:white_trampoline","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","pneumaticcraft:volume_upgrade","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","additionallanterns:gold_chain","mcwroofs:gutter_base","forbidden_arcanus:processed_obsidian_block_from_obsidian_ingot","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","minecraft:writable_book","dyenamics:maroon_stained_glass","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","minecraft:golden_apple","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","simplylight:rodlamp","pneumaticcraft:cannon_barrel","pneumaticcraft:pressure_chamber_valve_x4","pneumaticcraft:pressure_chamber_valve_x1","pneumaticcraft:reinforced_brick_wall","minecraft:iron_sword","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","rftoolsbuilder:green_shield_template_block","pneumaticcraft:wall_lamp_inverted_blue","minecraft:green_dye","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","mcwfences:warped_horse_fence","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwlights:jungle_ceiling_fan_light","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","minecraft:smooth_red_sandstone_slab","dyenamics:mint_stained_glass","alltheores:gold_plate","supplementaries:redstone_illuminator","mcwroofs:gutter_base_lime","utilitarian:redstone_clock","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","minecraft:golden_boots","securitycraft:harming_module","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","mcwbridges:iron_bridge_pier","cfm:pink_grill","pneumaticcraft:magnet_upgrade","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","pneumaticcraft:wall_lamp_purple","mcwfences:railing_red_sandstone_wall","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbiomesoplenty:fir_stockade_fence","alltheores:copper_plate","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","minecraft:smooth_red_sandstone_slab_from_smooth_red_sandstone_stonecutting","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","rftoolsbuilder:shape_card_def","rftoolsutility:counterplus_module","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","alltheores:bronze_plate","pneumaticcraft:gilded_upgrade","pneumaticcraft:stone_base","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","mcwwindows:white_mosaic_glass_pane","paraglider:cosmetic/goddess_statue","mcwwindows:purple_mosaic_glass","forbidden_arcanus:dark_nether_star","mcwfences:andesite_railing_gate","pneumaticcraft:pressure_chamber_wall","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:umbran_wired_fence","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","ae2wtlib:magnet_card","securitycraft:reinforced_dark_oak_fence_gate","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","minecraft:cut_red_sandstone","blue_skies:lunar_bookshelf","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","mcwlights:soul_oak_tiki_torch","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","mcwfences:mud_brick_grass_topped_wall","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","ae2:network/cells/item_cell_housing","pneumaticcraft:wall_lamp_inverted_pink","minecraft:golden_axe","twilightforest:dark_boat","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","pneumaticcraft:compressed_brick_slab","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","travelersbackpack:cactus","simplylight:illuminant_magenta_block_toggle","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","everythingcopper:copper_hoe","pneumaticcraft:wall_lamp_light_gray","rftoolsbuilder:blue_shield_template_block","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:fluorescent_stained_glass","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","alltheores:bronze_rod","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","minecraft:glass_pane","supplementaries:timber_brace","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","minecraft:lectern","minecraft:golden_sword","mcwlights:acacia_ceiling_fan_light","securitycraft:reinforced_brown_stained_glass","cfm:lime_grill","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","create:crafting/appliances/copper_diving_boots","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","handcrafted:golden_medium_pot","enderio:wood_gear","supplementaries:bed_from_feather_block","immersiveengineering:crafting/component_iron","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","mcwlights:copper_chain","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwroofs:red_sandstone_steep_roof","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","pneumaticcraft:small_tank","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwlights:pink_paper_lamp","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","pneumaticcraft:wall_lamp_inverted_light_blue","pneumaticcraft:elytra_upgrade","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwpaths:red_sandstone_running_bond_slab","aquaculture:fishing_line","aether:iron_gloves","mcwfurnitures:stripped_jungle_drawer","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","corail_woodcutter:oak_woodcutter","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwpaths:red_sandstone_running_bond_stairs","minecraft:smithing_table","pneumaticcraft:stomp_upgrade","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","mcwbiomesoplenty:mahogany_highley_gate","mcwbridges:glass_bridge_pier","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","domum_ornamentum:architectscutter","aether:book_of_lore","minecraft:brush","mcwroofs:jungle_planks_roof","cfm:yellow_grill","mcwfences:jungle_picket_fence","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","sfm:cable","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","pneumaticcraft:compressed_iron_boots","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","railcraft:copper_gear","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","supplementaries:pulley","minecraft:gray_stained_glass","cfm:magenta_trampoline","immersiveengineering:crafting/connector_lv","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","pneumaticcraft:reinforced_chest","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","allthecompressed:compress/glass_1x","ad_astra:compressor","dyenamics:banner/peach_banner","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","pneumaticcraft:reinforced_stone_slab","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","additionallanterns:basalt_lantern","create:copper_ladder_from_ingots_copper_stonecutting","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","minecraft:chain","mcwpaths:red_sandstone_honeycomb_paving","simplylight:illuminant_brown_block_toggle","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","minecraft:iron_boots","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","simplylight:illuminant_black_block_on_toggle","mcwwindows:crimson_curtain_rod","mcwwindows:jungle_plank_pane_window","securitycraft:universal_key_changer","pneumaticcraft:range_upgrade","immersiveengineering:crafting/generator","securitycraft:reinforced_black_stained_glass","securitycraft:sentry","supplementaries:hourglass","everythingcopper:copper_leggings","pneumaticcraft:reinforced_brick_tile","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:gutter_base_pink","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","domum_ornamentum:cactus_extra","minecraft:crossbow","pneumaticcraft:coordinate_tracker_upgrade","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwpaths:red_sandstone_crystal_floor_stairs","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwpaths:red_sandstone_windmill_weave_slab","mcwwindows:orange_mosaic_glass_pane","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:white_stained_glass","securitycraft:taser","pneumaticcraft:heat_frame","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","travelersbackpack:emerald","farmersdelight:cutting_board","minecraft:note_block","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwwindows:brown_mosaic_glass_pane","mcwroofs:red_sandstone_upper_steep_roof","utilitarian:angel_block_rot","supplementaries:altimeter","everythingcopper:copper_shears","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","aquaculture:cooked_fish_fillet_from_smoking","utilitarian:utility/jungle_logs_to_slabs","cfm:stripped_jungle_desk","mcwfences:warped_stockade_fence","dyenamics:bed/spring_green_bed","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","minecraft:magenta_stained_glass","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","cfm:stripped_jungle_desk_cabinet","aquaculture:jellyfish_to_slimeball","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwfences:panelled_metal_fence","paraglider:cosmetic/kakariko_goddess_statue","immersiveengineering:crafting/redstone_acid","additionallanterns:dark_prismarine_lantern","pneumaticcraft:compressed_bricks","appmek:chemical_cell_housing","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","handcrafted:jungle_chair","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","pneumaticcraft:wall_lamp_green","corail_woodcutter:warped_woodcutter","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","minecraft:red_sandstone_wall_from_red_sandstone_stonecutting","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","silentgear:upgrade_base","create:crafting/kinetics/steam_whistle","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","simplylight:illuminant_purple_block_on_toggle","mcwpaths:red_sandstone_windmill_weave","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","aether:skyroot_bookshelf","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","pneumaticcraft:logistics_core","everythingcopper:copper_helmet","mythicbotany:rune_holder","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","pneumaticcraft:thermostat_module","securitycraft:reinforced_red_stained_glass_pane_from_glass","mcwdoors:garage_silver_door","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:iron_shovel","biomesoplenty:willow_boat","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwlights:golden_wall_candle_holder","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","pneumaticcraft:camo_applicator","reliquary:infernal_claw","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","supplementaries:gold_trapdoor","cfm:fridge_light","pneumaticcraft:speed_upgrade","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","rftoolsutility:counter_module","littlelogistics:locomotive_route","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","securitycraft:universal_block_remover","mcwfurnitures:stripped_jungle_bookshelf_cupboard","securitycraft:reinforced_bookshelf","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","securitycraft:storage_module","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbridges:red_sandstone_bridge_pier","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","mcwpaths:red_sandstone_dumble_paving","railcraft:bronze_ingot_crafted_with_ingots","mcwbiomesoplenty:magic_highley_gate","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","minecraft:lapis_lazuli","framedblocks:framed_cube","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","supplementaries:candle_holders/candle_holder_magenta","rftoolsbuilder:yellow_shield_template_block","mcwbridges:balustrade_orange_sandstone_bridge","pneumaticcraft:wall_lamp_inverted_yellow","mcwlights:golden_triple_candle_holder","mcwfences:oak_curved_gate","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","supplementaries:wrench","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","aether:iron_axe_repairing","mcwpaths:red_sandstone_basket_weave_paving","minecraft:jungle_slab","supplementaries:sconce","pneumaticcraft:wall_lamp_brown","silentgear:rough_rod","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:palm_wired_fence","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","minecraft:red_sandstone_slab","pneumaticcraft:wall_lamp_inverted_orange","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","pneumaticcraft:wall_lamp_inverted_brown","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwlights:iron_chandelier","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","minecraft:smooth_red_sandstone_stairs_from_smooth_red_sandstone_stonecutting","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","securitycraft:floor_trap","mcwdoors:jungle_tropical_door","twigs:lamp","dyenamics:bed/lavender_bed","mcwpaths:red_sandstone_flagstone_path","ae2:network/cells/fluid_cell_housing","deepresonance:machine_frame","travelersbackpack:lapis","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","mcwdoors:garage_black_door","mcwfurnitures:jungle_triple_drawer","crafting_on_a_stick:crafting_table","mcwwindows:pink_mosaic_glass_pane","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","alltheores:gold_rod","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwlights:mangrove_ceiling_fan_light","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","minecraft:chiseled_red_sandstone","sfm:printing_press","supplementaries:timber_cross_brace","minecolonies:chainmailleggings","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","mcwfences:bamboo_pyramid_gate","additionallanterns:cobbled_deepslate_lantern","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","pneumaticcraft:wall_lamp_inverted_gray","everythingcopper:copper_pickaxe","handcrafted:jungle_fancy_bed","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","mcwlights:cross_lantern","railcraft:receiver_circuit","pneumaticcraft:thermopneumatic_processing_plant","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","cfm:brown_trampoline","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","mcwtrpdoors:jungle_mystic_trapdoor","securitycraft:trophy_system","everythingcopper:copper_axe","pneumaticcraft:reinforced_brick_from_slab","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","pneumaticcraft:wall_lamp_inverted_green","mcwbiomesoplenty:palm_horse_fence","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","mcwroofs:jungle_lower_roof","mcwbridges:orange_sandstone_bridge","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:personal_world_spike","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwlights:lava_lamp","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","create:crafting/kinetics/empty_blaze_burner","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:reinforced_light_blue_stained_glass","securitycraft:door_indestructible_iron_item","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","cfm:jungle_table","aether:iron_chestplate_repairing","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","mcwdoors:garage_white_door","everythingcopper:copper_rail","utilitix:armed_stand","cfm:black_grill","botania:lexicon","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","simplylight:illuminant_block","mcwlights:green_paper_lamp","mcwpaths:red_sandstone_running_bond","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","enderio:fluid_tank","mcwfences:railing_sandstone_wall","bigreactors:energizer/casing","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","minecraft:birch_boat","mcwfences:spruce_highley_gate","immersiveengineering:crafting/coil_lv","cfm:post_box","mcwpaths:red_sandstone_windmill_weave_path","mcwlights:brown_paper_lamp","mcwdoors:jungle_barn_door","minecraft:campfire","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","rftoolsstorage:dump_module","mcwbiomesoplenty:maple_highley_gate","minecraft:lime_stained_glass","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","mcwfences:railing_quartz_wall","pneumaticcraft:logistics_configurator","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"],toBeDisplayed:["immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","sfm:disk","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","trashcans:energy_trash_can","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","handcrafted:spider_trophy","mcwtrpdoors:jungle_cottage_trapdoor","cfm:fridge_dark","chimes:copper_chimes","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","mcwpaths:jungle_planks_path","create:crafting/appliances/crafting_blueprint","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwdoors:jungle_nether_door","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwfences:acacia_wired_fence","simplylight:illuminant_light_blue_block_dyed","mcwwindows:black_mosaic_glass","railcraft:controller_circuit","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","cfm:spatula","securitycraft:reinforced_cherry_fence_gate","pneumaticcraft:charging_upgrade","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","energymeter:meter","mcwlights:covered_lantern","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","minecolonies:shapetool","supplementaries:bubble_blower","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwroofs:gutter_base_light_gray","additionallanterns:red_sandstone_chain","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","mcwlights:copper_candle_holder","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","reliquary:mob_charm_fragments/slime","aquaculture:birch_fish_mount","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","blue_skies:comet_bookshelf","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","biomesoplenty:mahogany_boat","aquaculture:note_hook","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","aquaculture:double_hook","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","pneumaticcraft:regulator_tube_module","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","utilitarian:angel_block","simplylight:illuminant_orange_block_on_toggle","railcraft:brass_ingot_crafted_with_ingots","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","simplylight:edge_light_bottom_from_top","dyenamics:bed/navy_bed","pneumaticcraft:transfer_gadget","securitycraft:username_logger","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwwindows:red_sandstone_window","mcwwindows:jungle_four_window","handcrafted:jungle_side_table","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","handcrafted:jungle_bench","securitycraft:keypad_frame","pneumaticcraft:compressed_iron_chestplate","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","minecraft:jungle_door","pneumaticcraft:vortex_tube","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwpaths:red_sandstone_diamond_paving","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","twilightdelight:cutting/ice_bow","handcrafted:red_sandstone_pillar_trim","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","pneumaticcraft:wall_lamp_inverted_white","railcraft:world_spike","mcwlights:golden_chain","mcwdoors:garage_gray_door","mcwbiomesoplenty:magic_picket_fence","securitycraft:reinforced_purple_stained_glass","littlelogistics:seater_car","everythingcopper:copper_chestplate","cfm:green_grill","handcrafted:jungle_drawer","pneumaticcraft:search_upgrade","cfm:orange_grill","cfm:brown_grill","minecraft:cut_red_sandstone_from_red_sandstone_stonecutting","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","cfm:jungle_desk","reliquary:mob_charm_fragments/creeper","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","minecraft:clock","mcwfences:nether_brick_pillar_wall","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","additionallanterns:end_stone_lantern","aquaculture:iron_fillet_knife","travelersbackpack:coal","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","minecraft:golden_pickaxe","minecraft:bamboo_raft","pneumaticcraft:refinery","pneumaticcraft:thermal_compressor","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwwindows:white_mosaic_glass","minecraft:redstone_torch","mcwroofs:jungle_upper_steep_roof","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwfences:railing_andesite_wall","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","minecraft:target","additionallanterns:andesite_lantern","utilitix:mob_yoinker","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwlights:golden_low_candle_holder","mcwroofs:gutter_base_gray","mcwpaths:red_sandstone_crystal_floor_path","simplylight:illuminant_light_gray_block_on_dyed","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","supplementaries:cog_block","mcwlights:golden_chandelier","cfm:blue_grill","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwtrpdoors:metal_full_trapdoor","mcwpaths:red_sandstone_flagstone_slab","supplementaries:bomb","dyenamics:navy_stained_glass","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","pneumaticcraft:elevator_frame","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwwindows:birch_curtain_rod","pneumaticcraft:wall_lamp_cyan","minecraft:red_sandstone_slab_from_red_sandstone_stonecutting","create:crafting/kinetics/fluid_pipe","immersiveengineering:crafting/torch","pneumaticcraft:pressure_chamber_glass_x4","mcwfences:warped_pyramid_gate","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","pneumaticcraft:pressure_chamber_glass_x1","allthecompressed:compress/lapis_block_1x","railcraft:signal_circuit","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","dyenamics:bubblegum_stained_glass","undergarden:gloom_o_lantern","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","supplementaries:flags/flag_yellow","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","pneumaticcraft:heat_pipe","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","cfm:red_grill","mcwfences:railing_blackstone_wall","mcwroofs:red_sandstone_attic_roof","mcwlights:soul_double_street_lamp","mcwpaths:red_sandstone_flagstone","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","travelersbackpack:enderman","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwlights:oak_tiki_torch","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","dyenamics:conifer_stained_glass","immersiveengineering:crafting/toolbox","cfm:light_gray_trampoline","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","bloodmagic:blood_altar","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_stained_glass","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","shrink:shrinking_device","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","mcwroofs:red_sandstone_top_roof","mcwdoors:jungle_japanese2_door","minecraft:smooth_red_sandstone","pneumaticcraft:wall_lamp_pink","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:red_sandstone_four_window","mcwroofs:gutter_base_magenta","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","minecraft:red_sandstone_stairs_from_red_sandstone_stonecutting","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","cfm:purple_grill","create:crafting/kinetics/hose_pulley","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","minecraft:cut_red_sandstone_slab_from_red_sandstone_stonecutting","mcwtrpdoors:jungle_bark_trapdoor","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","rftoolsutility:fluidplus_module","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwbiomesoplenty:dead_picket_fence","pneumaticcraft:vortex_cannon","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwfurnitures:jungle_counter","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwdoors:jungle_swamp_door","minecraft:wooden_hoe","immersiveengineering:crafting/light_engineering","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","mcwpaths:red_sandstone_square_paving","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","securitycraft:reinforced_pink_stained_glass","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","littlelogistics:vessel_charger","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","railcraft:radio_circuit","minecraft:smooth_red_sandstone_stairs","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","minecraft:cut_red_sandstone_slab_from_cut_red_sandstone_stonecutting","minecraft:wooden_axe","minecraft:dispenser","mcwfences:panelled_metal_fence_gate","supplementaries:sign_post_jungle","mcwlights:golden_double_candle_holder","mcwwindows:metal_curtain_rod","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:red_sandstone_upper_lower_roof","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","supplementaries:daub_brace","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","securitycraft:reinforced_green_stained_glass","pneumaticcraft:wall_lamp_blue","minecraft:iron_bars","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","handcrafted:jungle_desk","create:crafting/kinetics/copper_valve_handle","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","supplementaries:flags/flag_magenta","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_red","handcrafted:red_sandstone_corner_trim","aether:skyroot_piston","pneumaticcraft:manometer","securitycraft:alarm","rftoolsutility:charged_porter","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","pneumaticcraft:wall_lamp_inverted_magenta","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","additionallanterns:amethyst_lantern","utilitix:comparator_redirector_down","create:copper_shingles_from_ingots_copper_stonecutting","mcwfences:granite_grass_topped_wall","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","pneumaticcraft:pressure_chamber_interface","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","mcwfences:railing_nether_brick_wall","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","cfm:black_trampoline","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","alchemistry:compactor","cfm:jungle_upgraded_fence","minecraft:dropper","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","dyenamics:bed/maroon_bed","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","mcwbiomesoplenty:pine_picket_fence","mcwfences:bamboo_highley_gate","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","mcwroofs:jungle_roof","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwfences:jungle_curved_gate","mcwpaths:red_sandstone_strewn_rocky_path","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:light_gray_grill","mcwtrpdoors:metal_trapdoor","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","supplementaries:daub_frame","railcraft:signal_tuner","pneumaticcraft:minigun_upgrade","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","aquaculture:iron_fishing_rod","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:red_sandstone_wall","mcwbiomesoplenty:mahogany_pyramid_gate","cfm:pink_trampoline","securitycraft:briefcase","mcwwindows:green_mosaic_glass","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwlights:copper_triple_candle_holder","cfm:white_grill","minecraft:iron_nugget_from_smelting","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","handcrafted:jungle_dining_bench","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","ae2:network/cables/dense_smart_fluix","aether:golden_pendant","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","simplylight:illuminant_brown_block_on_toggle","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwfences:oak_picket_fence","pneumaticcraft:compressed_iron_leggings","mcwfences:mangrove_curved_gate","mcwpaths:red_sandstone_flagstone_stairs","mcwfences:modern_andesite_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","utilitix:advanced_brewery","mcwlights:copper_double_candle_holder","supplementaries:cage","mcwfurnitures:jungle_large_drawer","mcwfences:diorite_grass_topped_wall","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:metal_four_window","simplylight:illuminant_purple_block_on_dyed","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","mcwroofs:jungle_planks_top_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","additionallanterns:diamond_lantern","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","securitycraft:reinforced_lime_stained_glass_pane_from_glass","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","simplylight:illuminant_brown_block_on_dyed","eccentrictome:tome","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","itemcollectors:basic_collector","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwpaths:red_sandstone_crystal_floor_slab","buildinggadgets2:gadget_copy_paste","handcrafted:golden_wide_pot","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","minecraft:red_sandstone_stairs","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","pneumaticcraft:redstone_module","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","mcwbiomesoplenty:redwood_wired_fence","allthetweaks:ender_pearl_block","computercraft:computer_advanced","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","everythingcopper:copper_door","mcwlights:copper_chandelier","reliquary:uncrafting/slime_ball","mcwdoors:jungle_paper_door","mcwpaths:red_sandstone_clover_paving","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","everythingcopper:copper_pressure_plate","mcwroofs:jungle_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","securitycraft:reinforced_birch_fence_gate","mcwroofs:gutter_base_light_blue","additionallanterns:diorite_lantern","mcwpaths:red_sandstone_running_bond_path","pneumaticcraft:dispenser_upgrade","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","rftoolsutility:inventoryplus_module","minecraft:yellow_stained_glass","mcwfurnitures:jungle_end_table","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwwindows:red_sandstone_pane_window","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","handcrafted:goat_trophy","mcwroofs:red_sandstone_lower_roof","bigreactors:blasting/graphite_from_coal","paraglider:paraglider","simplylight:illuminant_lime_block_on_dyed","utilitix:anvil_cart","securitycraft:reinforced_white_stained_glass_pane_from_dye","pneumaticcraft:wall_lamp_red","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbridges:jungle_bridge_pier","minecraft:lapis_block","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","undergarden:catalyst","immersiveengineering:crafting/voltmeter","minecraft:iron_axe","create:crafting/kinetics/fluid_valve","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","securitycraft:blacklist_module","mcwbiomesoplenty:snowblossom_hedge","minecraft:oak_boat","sophisticatedstorage:jungle_chest","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","securitycraft:smart_module","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","minecraft:jungle_fence","minecraft:gold_ingot_from_gold_block","minecraft:glass_bottle","mcwfences:mesh_metal_fence","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","botania:speed_up_belt","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwlights:bamboo_tiki_torch","mcwpaths:red_sandstone_crystal_floor","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alchemistry:combiner","mcwdoors:jungle_stable_head_door","pneumaticcraft:air_canister","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:candle_holders/candle_holder_yellow","supplementaries:slice_map","mcwpaths:red_sandstone_windmill_weave_stairs","appflux:insulating_resin","occultism:crafting/butcher_knife","dyenamics:banner/maroon_banner","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwlights:mangrove_tiki_torch","aether:iron_pendant","pneumaticcraft:kerosene_lamp","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","simplylight:illuminant_slab_from_panel","cfm:gray_grill","supplementaries:flags/flag_purple","paraglider:cosmetic/rito_goddess_statue","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","pneumaticcraft:wall_lamp_inverted_cyan","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","handcrafted:golden_thick_pot","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","farmersdelight:iron_knife","mcwroofs:red_sandstone_roof","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","additionallanterns:warped_lantern","minecraft:spruce_boat","minecraft:gold_block","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","pneumaticcraft:compressed_bricks_from_tile","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","mcwfences:jungle_horse_fence","handcrafted:salmon_trophy","minecraft:bucket","minecraft:cut_red_sandstone_slab","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","minecraft:chiseled_red_sandstone_from_red_sandstone_stonecutting","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwlights:golden_small_chandelier","mcwfences:railing_deepslate_brick_wall","silentgear:emerald_shard","mcwwindows:lime_mosaic_glass","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:white_trampoline","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","pneumaticcraft:volume_upgrade","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","additionallanterns:gold_chain","mcwroofs:gutter_base","forbidden_arcanus:processed_obsidian_block_from_obsidian_ingot","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","minecraft:writable_book","dyenamics:maroon_stained_glass","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","minecraft:golden_apple","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","simplylight:rodlamp","pneumaticcraft:cannon_barrel","pneumaticcraft:pressure_chamber_valve_x4","pneumaticcraft:pressure_chamber_valve_x1","pneumaticcraft:reinforced_brick_wall","minecraft:iron_sword","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","rftoolsbuilder:green_shield_template_block","pneumaticcraft:wall_lamp_inverted_blue","minecraft:green_dye","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","mcwfences:warped_horse_fence","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwlights:jungle_ceiling_fan_light","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","minecraft:smooth_red_sandstone_slab","dyenamics:mint_stained_glass","alltheores:gold_plate","supplementaries:redstone_illuminator","mcwroofs:gutter_base_lime","utilitarian:redstone_clock","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","minecraft:golden_boots","securitycraft:harming_module","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","mcwbridges:iron_bridge_pier","cfm:pink_grill","pneumaticcraft:magnet_upgrade","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","pneumaticcraft:wall_lamp_purple","mcwfences:railing_red_sandstone_wall","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbiomesoplenty:fir_stockade_fence","alltheores:copper_plate","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","minecraft:smooth_red_sandstone_slab_from_smooth_red_sandstone_stonecutting","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","rftoolsbuilder:shape_card_def","rftoolsutility:counterplus_module","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","alltheores:bronze_plate","pneumaticcraft:gilded_upgrade","pneumaticcraft:stone_base","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","mcwwindows:white_mosaic_glass_pane","paraglider:cosmetic/goddess_statue","mcwwindows:purple_mosaic_glass","forbidden_arcanus:dark_nether_star","mcwfences:andesite_railing_gate","pneumaticcraft:pressure_chamber_wall","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:umbran_wired_fence","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","ae2wtlib:magnet_card","securitycraft:reinforced_dark_oak_fence_gate","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","minecraft:cut_red_sandstone","blue_skies:lunar_bookshelf","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","mcwlights:soul_oak_tiki_torch","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","mcwfences:mud_brick_grass_topped_wall","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","ae2:network/cells/item_cell_housing","pneumaticcraft:wall_lamp_inverted_pink","minecraft:golden_axe","twilightforest:dark_boat","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","pneumaticcraft:compressed_brick_slab","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","travelersbackpack:cactus","simplylight:illuminant_magenta_block_toggle","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","everythingcopper:copper_hoe","pneumaticcraft:wall_lamp_light_gray","rftoolsbuilder:blue_shield_template_block","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:fluorescent_stained_glass","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","alltheores:bronze_rod","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","minecraft:glass_pane","supplementaries:timber_brace","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","minecraft:lectern","minecraft:golden_sword","mcwlights:acacia_ceiling_fan_light","securitycraft:reinforced_brown_stained_glass","cfm:lime_grill","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","create:crafting/appliances/copper_diving_boots","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","handcrafted:golden_medium_pot","enderio:wood_gear","supplementaries:bed_from_feather_block","immersiveengineering:crafting/component_iron","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","mcwlights:copper_chain","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwroofs:red_sandstone_steep_roof","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","pneumaticcraft:small_tank","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwlights:pink_paper_lamp","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","pneumaticcraft:wall_lamp_inverted_light_blue","pneumaticcraft:elytra_upgrade","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwpaths:red_sandstone_running_bond_slab","aquaculture:fishing_line","aether:iron_gloves","mcwfurnitures:stripped_jungle_drawer","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","corail_woodcutter:oak_woodcutter","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwpaths:red_sandstone_running_bond_stairs","minecraft:smithing_table","pneumaticcraft:stomp_upgrade","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","mcwbiomesoplenty:mahogany_highley_gate","mcwbridges:glass_bridge_pier","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","domum_ornamentum:architectscutter","aether:book_of_lore","minecraft:brush","mcwroofs:jungle_planks_roof","cfm:yellow_grill","mcwfences:jungle_picket_fence","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","sfm:cable","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","pneumaticcraft:compressed_iron_boots","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","railcraft:copper_gear","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","supplementaries:pulley","minecraft:gray_stained_glass","cfm:magenta_trampoline","immersiveengineering:crafting/connector_lv","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","pneumaticcraft:reinforced_chest","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","allthecompressed:compress/glass_1x","ad_astra:compressor","dyenamics:banner/peach_banner","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","pneumaticcraft:reinforced_stone_slab","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","additionallanterns:basalt_lantern","create:copper_ladder_from_ingots_copper_stonecutting","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","minecraft:chain","mcwpaths:red_sandstone_honeycomb_paving","simplylight:illuminant_brown_block_toggle","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","minecraft:iron_boots","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","simplylight:illuminant_black_block_on_toggle","mcwwindows:crimson_curtain_rod","mcwwindows:jungle_plank_pane_window","securitycraft:universal_key_changer","pneumaticcraft:range_upgrade","immersiveengineering:crafting/generator","securitycraft:reinforced_black_stained_glass","securitycraft:sentry","supplementaries:hourglass","everythingcopper:copper_leggings","pneumaticcraft:reinforced_brick_tile","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:gutter_base_pink","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","domum_ornamentum:cactus_extra","minecraft:crossbow","pneumaticcraft:coordinate_tracker_upgrade","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwpaths:red_sandstone_crystal_floor_stairs","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwpaths:red_sandstone_windmill_weave_slab","mcwwindows:orange_mosaic_glass_pane","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:white_stained_glass","securitycraft:taser","pneumaticcraft:heat_frame","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","travelersbackpack:emerald","farmersdelight:cutting_board","minecraft:note_block","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwwindows:brown_mosaic_glass_pane","mcwroofs:red_sandstone_upper_steep_roof","utilitarian:angel_block_rot","supplementaries:altimeter","everythingcopper:copper_shears","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","aquaculture:cooked_fish_fillet_from_smoking","utilitarian:utility/jungle_logs_to_slabs","cfm:stripped_jungle_desk","mcwfences:warped_stockade_fence","dyenamics:bed/spring_green_bed","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","minecraft:magenta_stained_glass","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","cfm:stripped_jungle_desk_cabinet","aquaculture:jellyfish_to_slimeball","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwfences:panelled_metal_fence","paraglider:cosmetic/kakariko_goddess_statue","immersiveengineering:crafting/redstone_acid","additionallanterns:dark_prismarine_lantern","pneumaticcraft:compressed_bricks","appmek:chemical_cell_housing","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","handcrafted:jungle_chair","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","pneumaticcraft:wall_lamp_green","corail_woodcutter:warped_woodcutter","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","minecraft:red_sandstone_wall_from_red_sandstone_stonecutting","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","silentgear:upgrade_base","create:crafting/kinetics/steam_whistle","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","simplylight:illuminant_purple_block_on_toggle","mcwpaths:red_sandstone_windmill_weave","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","aether:skyroot_bookshelf","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","pneumaticcraft:logistics_core","everythingcopper:copper_helmet","mythicbotany:rune_holder","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","pneumaticcraft:thermostat_module","securitycraft:reinforced_red_stained_glass_pane_from_glass","mcwdoors:garage_silver_door","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:iron_shovel","biomesoplenty:willow_boat","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwlights:golden_wall_candle_holder","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","pneumaticcraft:camo_applicator","reliquary:infernal_claw","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","supplementaries:gold_trapdoor","cfm:fridge_light","pneumaticcraft:speed_upgrade","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","rftoolsutility:counter_module","littlelogistics:locomotive_route","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","securitycraft:universal_block_remover","mcwfurnitures:stripped_jungle_bookshelf_cupboard","securitycraft:reinforced_bookshelf","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","securitycraft:storage_module","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbridges:red_sandstone_bridge_pier","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","mcwpaths:red_sandstone_dumble_paving","railcraft:bronze_ingot_crafted_with_ingots","mcwbiomesoplenty:magic_highley_gate","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","minecraft:lapis_lazuli","framedblocks:framed_cube","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","supplementaries:candle_holders/candle_holder_magenta","rftoolsbuilder:yellow_shield_template_block","mcwbridges:balustrade_orange_sandstone_bridge","pneumaticcraft:wall_lamp_inverted_yellow","mcwlights:golden_triple_candle_holder","mcwfences:oak_curved_gate","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","supplementaries:wrench","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","aether:iron_axe_repairing","mcwpaths:red_sandstone_basket_weave_paving","minecraft:jungle_slab","supplementaries:sconce","pneumaticcraft:wall_lamp_brown","silentgear:rough_rod","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:palm_wired_fence","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","minecraft:red_sandstone_slab","pneumaticcraft:wall_lamp_inverted_orange","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","pneumaticcraft:wall_lamp_inverted_brown","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwlights:iron_chandelier","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","minecraft:smooth_red_sandstone_stairs_from_smooth_red_sandstone_stonecutting","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","securitycraft:floor_trap","mcwdoors:jungle_tropical_door","twigs:lamp","dyenamics:bed/lavender_bed","mcwpaths:red_sandstone_flagstone_path","ae2:network/cells/fluid_cell_housing","deepresonance:machine_frame","travelersbackpack:lapis","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","mcwdoors:garage_black_door","mcwfurnitures:jungle_triple_drawer","crafting_on_a_stick:crafting_table","mcwwindows:pink_mosaic_glass_pane","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","alltheores:gold_rod","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwlights:mangrove_ceiling_fan_light","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","minecraft:chiseled_red_sandstone","sfm:printing_press","supplementaries:timber_cross_brace","minecolonies:chainmailleggings","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","mcwfences:bamboo_pyramid_gate","additionallanterns:cobbled_deepslate_lantern","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","pneumaticcraft:wall_lamp_inverted_gray","everythingcopper:copper_pickaxe","handcrafted:jungle_fancy_bed","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","mcwlights:cross_lantern","railcraft:receiver_circuit","pneumaticcraft:thermopneumatic_processing_plant","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","cfm:brown_trampoline","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","mcwtrpdoors:jungle_mystic_trapdoor","securitycraft:trophy_system","everythingcopper:copper_axe","pneumaticcraft:reinforced_brick_from_slab","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","pneumaticcraft:wall_lamp_inverted_green","mcwbiomesoplenty:palm_horse_fence","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","mcwroofs:jungle_lower_roof","mcwbridges:orange_sandstone_bridge","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:personal_world_spike","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwlights:lava_lamp","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","create:crafting/kinetics/empty_blaze_burner","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:reinforced_light_blue_stained_glass","securitycraft:door_indestructible_iron_item","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","cfm:jungle_table","aether:iron_chestplate_repairing","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","mcwdoors:garage_white_door","everythingcopper:copper_rail","utilitix:armed_stand","cfm:black_grill","botania:lexicon","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","simplylight:illuminant_block","mcwlights:green_paper_lamp","mcwpaths:red_sandstone_running_bond","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","enderio:fluid_tank","mcwfences:railing_sandstone_wall","bigreactors:energizer/casing","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","minecraft:birch_boat","mcwfences:spruce_highley_gate","immersiveengineering:crafting/coil_lv","cfm:post_box","mcwpaths:red_sandstone_windmill_weave_path","mcwlights:brown_paper_lamp","mcwdoors:jungle_barn_door","minecraft:campfire","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","rftoolsstorage:dump_module","mcwbiomesoplenty:maple_highley_gate","minecraft:lime_stained_glass","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","mcwfences:railing_quartz_wall","pneumaticcraft:logistics_configurator","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:11451,warning_level:0}}