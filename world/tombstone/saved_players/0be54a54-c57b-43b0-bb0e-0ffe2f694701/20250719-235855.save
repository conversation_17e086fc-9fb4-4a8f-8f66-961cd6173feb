{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:8,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:jump_boost"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:200,Id:13,ShowIcon:1b,ShowParticles:0b,"forge:id":"minecraft:water_breathing"}],Air:300s,Attributes:[{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"attributeslib:armor_shred"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:20.0d,Modifiers:[{Amount:14.0d,Name:"Heart Containers",Operation:0,UUID:[I;-**********,-990297069,-**********,-847674717]},{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"attributeslib:current_hp_damage"},{Base:0.0d,Name:"minecraft:generic.attack_knockback"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"attributeslib:prot_shred"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:0.0d,Name:"attributeslib:overheal"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:1.0d,Name:"attributeslib:arrow_velocity"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"attributeslib:arrow_damage"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{TrashSlot:{Count:1b,id:"minecraft:stick"},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-123270031,103236873,-**********,551414798],hasTentacle:0b},"comforts:sleep_data":{sleepTime:11690919L,tiredTime:11685121L,wakeTime:11685080L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:villager_hat"}],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:1,id:"artifacts:vampiric_glove"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:bunny_hoppers"}],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:175,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:17517},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:7,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:oak_planks",ItemStack:{Count:4b,id:"minecraft:oak_planks"}}],SelectedRecipe:"minecraft:oak_planks"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:124,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:cooked_beef","minecraft:bread"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"patchouli:guide_book",tag:{"patchouli:book":"apotheosis:apoth_chronicle"}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","c9c0e2c2-59f5-4640-a507-dbfea5bc9042"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:-10490,tb_last_ground_location_y:95,tb_last_ground_location_z:-20906,twilightforest_banished:1b},"apoth.affix_cooldown.apotheosis:sword/mob_effect/elusive":11126528L,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedBackpackSettings:{}},Health:39.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"sophisticatedbackpacks:gold_backpack",tag:{contentsUuid:[I;-612881550,**********,-**********,**********],inventorySlots:81,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:3}},{Count:64b,Slot:1b,id:"minecraft:clay_ball"},{Count:1b,Slot:2b,id:"minecraft:furnace"},{Count:1b,Slot:3b,id:"minecraft:iron_shovel",tag:{Damage:107}},{Count:1b,Slot:4b,id:"crafting_on_a_stick:crafting_table"},{Count:1b,Slot:5b,id:"minecraft:bow",tag:{Damage:0,affix_data:{affixes:{"apotheosis:ranged/attribute/agile":0.33417398f,"apotheosis:ranged/attribute/elven":0.97314626f,"apotheosis:ranged/attribute/streamlined":0.49408966f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:ranged/attribute/streamlined"},"",{"translate":"affix.apotheosis:ranged/attribute/elven.suffix"}]}',rarity:"apotheosis:uncommon",uuids:[[I;-1076468719,1136083460,-1531668712,-1270021138]]},apoth_rchest:1b}},{Count:1b,Slot:6b,id:"minecraft:iron_axe",tag:{Damage:57,affix_data:{affixes:{"apotheosis:durable":0.05f,"apotheosis:heavy_weapon/attribute/giant_slaying":0.2691679f,"apotheosis:heavy_weapon/attribute/nullifying":0.32890612f,"apotheosis:heavy_weapon/attribute/shredding":0.4741544f,"apotheosis:sword/mob_effect/elusive":0.5666329f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:heavy_weapon/attribute/nullifying"},"",{"translate":"affix.apotheosis:heavy_weapon/attribute/giant_slaying.suffix"}]}',rarity:"apotheosis:rare",sockets:2,uuids:[[I;1470717474,-1028504272,-1851639743,780348724]]},apoth_rchest:1b}},{Count:1b,Slot:7b,id:"minecraft:iron_pickaxe",tag:{Damage:191}},{Count:1b,Slot:8b,id:"railcraft:iron_spike_maul",tag:{Damage:0}},{Count:64b,Slot:10b,id:"minecraft:clay_ball"},{Count:6b,Slot:11b,id:"minecraft:cooked_beef"},{Count:1b,Slot:13b,id:"sophisticatedbackpacks:backpack"},{Count:64b,Slot:14b,id:"minecraft:clay_ball"},{Count:1b,Slot:15b,id:"sophisticatedbackpacks:backpack",tag:{contentsUuid:[I;1733112938,-1681175653,-2072223534,-157042649],inventorySlots:27,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:1}},{Count:1b,Slot:16b,id:"sophisticatedbackpacks:copper_backpack",tag:{contentsUuid:[I;108650512,-1306049842,-1338491301,-1710618992],inventorySlots:45,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:1}},{Count:64b,Slot:17b,id:"minecraft:clay_ball"},{Count:64b,Slot:19b,id:"minecraft:clay_ball"},{Count:34b,Slot:21b,id:"minecraft:torch"},{Count:64b,Slot:22b,id:"minecraft:clay_ball"},{Count:1b,Slot:23b,id:"ars_nouveau:caster_tome",tag:{"ars_nouveau:caster":{current_slot:0,flavor:"Creates a temporary tunnel of blocks.",hidden_recipe:"",is_hidden:0b,spell_count:1,spells:{spell0:{name:"The Shadow's Temporary Tunnel",recipe:{part0:"ars_nouveau:glyph_touch",part1:"ars_nouveau:glyph_intangible",part2:"ars_nouveau:glyph_aoe",part3:"ars_nouveau:glyph_aoe",part4:"ars_nouveau:glyph_pierce",part5:"ars_nouveau:glyph_pierce",part6:"ars_nouveau:glyph_pierce",part7:"ars_nouveau:glyph_pierce",part8:"ars_nouveau:glyph_pierce",part9:"ars_nouveau:glyph_extend_time",size:10},sound:{pitch:1.0f,soundTag:{id:"ars_nouveau:fire_family"},volume:1.0f},spellColor:{b:180,g:25,r:255,type:"ars_nouveau:constant"}}}},display:{Name:'{"italic":true,"color":"dark_purple","text":"The Shadow\'s Temporary Tunnel"}'}}},{Count:1b,Slot:24b,id:"minecraft:iron_pickaxe",tag:{Damage:0,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":0.14051425f,"apotheosis:breaker/attribute/experienced":0.24261278f,"apotheosis:breaker/attribute/lengthy":0.3698218f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/attribute/experienced"},"",{"translate":"affix.apotheosis:breaker/attribute/lengthy.suffix"}]}',rarity:"apotheosis:uncommon"},apoth_rchest:1b}},{Count:56b,Slot:25b,id:"minecraft:clay_ball"},{Count:1b,Slot:27b,id:"minecraft:jungle_boat"},{Count:64b,Slot:28b,id:"minecraft:sandstone"},{Count:45b,Slot:29b,id:"minecraft:bread"},{Count:64b,Slot:30b,id:"minecraft:spruce_log"},{Count:41b,Slot:31b,id:"minecraft:jungle_planks"},{Count:47b,Slot:32b,id:"minecraft:cobblestone"},{Count:52b,Slot:33b,id:"minecraft:oak_planks"},{Count:32b,Slot:34b,id:"minecraft:gold_ingot"},{Count:23b,Slot:35b,id:"minecraft:iron_ingot"},{Count:1b,Slot:100b,id:"minecraft:iron_boots",tag:{Damage:1,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.058074832f,"apotheosis:armor/attribute/windswept":0.9141143f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/blessed"},"",{"translate":"affix.apotheosis:armor/attribute/windswept.suffix"}]}',rarity:"apotheosis:uncommon",sockets:1,uuids:[[I;-963693922,-1776663042,-1653785592,1076950278]]},apoth_rchest:1b}},{Count:1b,Slot:101b,id:"minecraft:iron_leggings",tag:{Damage:1,affix_data:{affixes:{"irons_spellbooks:armor/attribute/mana":0.33041567f},name:'{"color":"#808080","translate":"misc.apotheosis.affix_name.two","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",""]}',rarity:"apotheosis:common",uuids:[[I;2119703976,-293453252,-1973081334,-1073326317]]},apoth_rchest:1b}},{Count:1b,Slot:102b,id:"minecraft:iron_chestplate",tag:{Damage:1,affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.631411f,"apotheosis:armor/attribute/steel_touched":0.59196067f,"apotheosis:armor/dmg_reduction/blast_forged":0.11222726f,"apotheosis:durable":0.07f,"irons_spellbooks:armor/attribute/mana":0.59923124f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",{"translate":"affix.apotheosis:armor/attribute/steel_touched.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;1538615368,-1778694774,-1544566921,1460440267]]},apoth_rchest:1b}},{Count:1b,Slot:103b,id:"minecraft:turtle_helmet",tag:{Damage:1,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.5099083f,"apotheosis:armor/attribute/stalwart":0.7456047f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/stalwart"},"",{"translate":"affix.apotheosis:armor/attribute/blessed.suffix"}]}',rarity:"apotheosis:uncommon",uuids:[[I;1228033423,-1117042903,-1453230919,-520663938]]},apoth_rchest:1b}}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-10065.175057403054d,64.0d,-21137.089762661886d],Railways_DataVersion:2,Rotation:[88.4592f,59.39968f],Score:1095,SelectedItemSlot:0,SleepTimer:0s,SpawnAngle:-90.40112f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:-10207,SpawnY:72,SpawnZ:-21070,Spigot.ticksLived:17516,UUID:[I;199576148,-981777488,-1156706306,795428609],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-2766646219972544L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:27,XpP:0.92104596f,XpSeed:-78911929,XpTotal:1095,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752937030289L,keepLevel:0b,lastKnownName:"TheYuXi",lastPlayed:1752940735487L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:3.5814536f,foodLevel:14,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwfurnitures:stripped_spruce_bookshelf","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","handcrafted:spider_trophy","mcwtrpdoors:jungle_cottage_trapdoor","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","chimes:copper_chimes","pneumaticcraft:wall_lamp_white","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","mcwtrpdoors:mangrove_bark_trapdoor","mcwpaths:jungle_planks_path","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","create:oak_window","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","mcwwindows:oak_plank_pane_window","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwfences:acacia_wired_fence","mcwfurnitures:spruce_modern_desk","mcwfurnitures:spruce_drawer","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","railcraft:controller_circuit","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","cfm:spatula","securitycraft:reinforced_cherry_fence_gate","pneumaticcraft:charging_upgrade","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","minecolonies:shapetool","supplementaries:bubble_blower","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwroofs:gutter_base_light_gray","additionallanterns:red_sandstone_chain","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:bamboo_chest_raft","minecraft:sandstone_slab_from_sandstone_stonecutting","reliquary:mob_charm_fragments/slime","cfm:stripped_spruce_desk","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","immersiveengineering:crafting/rockcutter","mcwtrpdoors:spruce_barred_trapdoor","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","blue_skies:comet_bookshelf","dyenamics:aquamarine_concrete_powder","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:oak_kitchen_counter","mcwroofs:spruce_roof","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","cfm:stripped_spruce_bedside_cabinet","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","biomesoplenty:palm_chest_boat","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","handcrafted:oak_bench","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","mcwfences:nether_brick_grass_topped_wall","mcwwindows:birch_plank_window","minecraft:sandstone","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","railcraft:brass_ingot_crafted_with_ingots","dyenamics:spring_green_concrete_powder","mcwpaths:sandstone_running_bond_stairs","mcwdoors:spruce_four_panel_door","cfm:jungle_cabinet","mcwbiomesoplenty:mahogany_window","cfm:stripped_spruce_chair","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","dyenamics:bed/navy_bed","pneumaticcraft:transfer_gadget","securitycraft:username_logger","mcwfurnitures:stripped_spruce_striped_chair","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwfurnitures:stripped_oak_drawer_counter","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwbiomesoplenty:umbran_plank_four_window","handcrafted:jungle_side_table","immersiveengineering:crafting/coal_coke_to_coke","mcwbridges:spruce_bridge_pier","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","securitycraft:keypad_frame","pneumaticcraft:compressed_iron_chestplate","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:spruce_bedside_cabinet","twilightforest:time_chest_boat","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:pine_plank_window2","minecraft:jungle_door","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwpaths:red_sandstone_diamond_paving","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","handcrafted:red_sandstone_pillar_trim","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","pneumaticcraft:wall_lamp_inverted_white","railcraft:world_spike","mcwlights:golden_chain","mcwdoors:garage_gray_door","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","mcwbridges:sandstone_bridge_pier","securitycraft:reinforced_purple_stained_glass","mcwfurnitures:stripped_spruce_double_drawer","mcwpaths:oak_planks_path","littlelogistics:seater_car","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","mcwbiomesoplenty:stripped_dead_pane_window","cfm:green_grill","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","minecraft:oak_pressure_plate","cfm:orange_grill","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwwindows:quartz_window","minecraft:cut_red_sandstone_from_red_sandstone_stonecutting","mcwwindows:dark_oak_shutter","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","cfm:jungle_desk","mcwroofs:oak_roof","reliquary:mob_charm_fragments/creeper","minecraft:mangrove_chest_boat","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","mcwtrpdoors:oak_barred_trapdoor","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwroofs:thatch_roof","mcwwindows:warped_curtain_rod","mcwdoors:spruce_glass_door","biomesoplenty:empyreal_boat","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twilightforest:transformation_chest_boat","additionallanterns:end_stone_lantern","twigs:silt","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","travelersbackpack:coal","mcwdoors:oak_four_panel_door","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwtrpdoors:spruce_whispering_trapdoor","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","pneumaticcraft:refinery","pneumaticcraft:thermal_compressor","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwwindows:white_mosaic_glass","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwroofs:jungle_upper_steep_roof","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwfences:railing_andesite_wall","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","biomesoplenty:empyreal_chest_boat","cfm:stripped_spruce_crate","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","mcwwindows:spruce_window2","utilitix:mob_yoinker","create:crafting/materials/andesite_alloy","mcwwindows:andesite_four_window","mcwtrpdoors:jungle_swamp_trapdoor","mcwlights:golden_low_candle_holder","mcwroofs:gutter_base_gray","mcwpaths:red_sandstone_crystal_floor_path","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwroofs:thatch_top_roof","mcwpaths:cobblestone_diamond_paving","supplementaries:cog_block","mcwlights:golden_chandelier","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwwindows:birch_plank_pane_window","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","cfm:stripped_jungle_table","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwpaths:red_sandstone_flagstone_slab","mcwroofs:spruce_top_roof","supplementaries:bomb","dyenamics:navy_stained_glass","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwpaths:sandstone_flagstone_slab","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","handcrafted:jungle_pillar_trim","pneumaticcraft:elevator_frame","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwwindows:oak_blinds","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","twilightforest:sorting_chest_boat","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwdoors:oak_cottage_door","pneumaticcraft:wall_lamp_cyan","minecraft:red_sandstone_slab_from_red_sandstone_stonecutting","create:crafting/kinetics/fluid_pipe","immersiveengineering:crafting/torch","pneumaticcraft:pressure_chamber_glass_x4","mcwfences:warped_pyramid_gate","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","pneumaticcraft:pressure_chamber_glass_x1","allthecompressed:compress/lapis_block_1x","railcraft:signal_circuit","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","undergarden:gloom_o_lantern","mcwbiomesoplenty:pine_window2","mcwwindows:birch_window","mcwwindows:blackstone_four_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","twigs:oak_table","simplylight:illuminant_purple_block_toggle","pneumaticcraft:heat_pipe","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","mcwfurnitures:stripped_spruce_glass_table","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","twilightforest:twilight_oak_chest_boat","mcwpaths:stone_windmill_weave","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","mcwroofs:red_sandstone_attic_roof","mcwlights:soul_double_street_lamp","mcwpaths:red_sandstone_flagstone","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","mcwfurnitures:oak_wardrobe","mcwfurnitures:spruce_wardrobe","mcwroofs:thatch_lower_roof","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","mcwpaths:sandstone_windmill_weave_stairs","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwroofs:blue_concrete_roof","travelersbackpack:bee","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","mcwwindows:stripped_dark_oak_log_window2","dyenamics:conifer_stained_glass","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","cfm:light_gray_trampoline","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","pneumaticcraft:compressed_brick_wall","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","mcwroofs:red_sandstone_top_roof","mcwdoors:jungle_japanese2_door","minecraft:smooth_red_sandstone","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:oak_sign","mcwwindows:stone_pane_window","pneumaticcraft:wall_lamp_pink","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwroofs:gutter_base_magenta","mcwbiomesoplenty:fir_plank_window2","domum_ornamentum:cobblestone_extra","additionallanterns:emerald_lantern","handcrafted:oak_counter","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","mcwfurnitures:spruce_stool_chair","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","minecraft:red_sandstone_stairs_from_red_sandstone_stonecutting","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:oak_double_wardrobe","mcwdoors:spruce_stable_head_door","mcwfurnitures:stripped_spruce_counter","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","mcwroofs:spruce_lower_roof","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","minecraft:cut_red_sandstone_slab_from_red_sandstone_stonecutting","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwdoors:spruce_whispering_door","mcwroofs:oak_planks_steep_roof","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwbiomesoplenty:dead_picket_fence","pneumaticcraft:vortex_cannon","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:purple_concrete_powder","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","biomesoplenty:maple_chest_boat","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","cfm:stripped_oak_chair","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwwindows:stripped_cherry_pane_window","cfm:diving_board","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/light_engineering","immersiveengineering:crafting/axe_steel","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwpaths:red_sandstone_square_paving","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_base_green","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","railcraft:steel_leggings","mcwroofs:oak_planks_attic_roof","securitycraft:reinforced_pink_stained_glass","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","littlelogistics:vessel_charger","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","naturescompass:natures_compass","railcraft:radio_circuit","minecraft:smooth_red_sandstone_stairs","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:cut_red_sandstone_slab_from_cut_red_sandstone_stonecutting","minecraft:wooden_axe","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwlights:golden_double_candle_holder","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","mcwtrpdoors:spruce_classic_trapdoor","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:red_sandstone_upper_lower_roof","mcwroofs:oak_steep_roof","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","supplementaries:daub_brace","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","handcrafted:stackable_book","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","mcwroofs:sandstone_top_roof","securitycraft:reinforced_green_stained_glass","pneumaticcraft:wall_lamp_blue","minecraft:iron_bars","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwtrpdoors:spruce_mystic_trapdoor","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","create:crafting/kinetics/copper_valve_handle","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_red","handcrafted:red_sandstone_corner_trim","mcwroofs:lime_concrete_attic_roof","aether:skyroot_piston","pneumaticcraft:manometer","securitycraft:alarm","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","pneumaticcraft:wall_lamp_inverted_magenta","dyenamics:banner/bubblegum_banner","mcwroofs:oak_top_roof","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","mcwpaths:sandstone_honeycomb_paving","additionallanterns:amethyst_lantern","utilitix:crude_furnace","utilitarian:utility/spruce_logs_to_pressure_plates","utilitix:comparator_redirector_down","mcwwindows:stone_window","create:copper_shingles_from_ingots_copper_stonecutting","mcwfences:granite_grass_topped_wall","everythingcopper:copper_sword","mcwwindows:birch_shutter","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","mcwroofs:cobblestone_roof","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","mcwdoors:print_spruce","mcwfences:dark_oak_picket_fence","alchemistry:compactor","travelersbackpack:sandstone","cfm:jungle_upgraded_fence","minecraft:dropper","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","dyenamics:bed/maroon_bed","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","mcwbiomesoplenty:pine_picket_fence","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","mcwdoors:oak_japanese2_door","mcwroofs:jungle_roof","mcwfurnitures:stripped_spruce_wardrobe","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","mcwpaths:red_sandstone_strewn_rocky_path","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:light_gray_grill","mcwwindows:mangrove_window","dyenamics:cherenkov_concrete_powder","mcwwindows:quartz_pane_window","cfm:oak_desk","mcwtrpdoors:metal_trapdoor","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","immersiveengineering:crafting/conveyor_redstone","mcwbiomesoplenty:magic_pyramid_gate","mcwfurnitures:spruce_table","mcwbiomesoplenty:hellbark_hedge","cfm:spruce_desk_cabinet","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","supplementaries:daub_frame","railcraft:signal_tuner","pneumaticcraft:minigun_upgrade","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","mcwpaths:sandstone_running_bond_slab","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:sandstone_wall","minecraft:red_sandstone_wall","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:stripped_mahogany_log_window","blue_skies:food_prep_table","cfm:pink_trampoline","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","securitycraft:briefcase","mcwwindows:green_mosaic_glass","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:stripped_oak_large_drawer","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","minecraft:iron_nugget_from_smelting","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","mcwroofs:sandstone_lower_roof","travelersbackpack:hose_nozzle","securitycraft:claymore","handcrafted:jungle_dining_bench","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","ae2:network/cables/dense_smart_fluix","aether:golden_pendant","mcwpaths:sandstone_basket_weave_paving","mcwroofs:sandstone_steep_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","mcwwindows:stripped_acacia_log_four_window","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwroofs:orange_concrete_upper_lower_roof","mcwtrpdoors:spruce_barn_trapdoor","mcwfurnitures:spruce_modern_wardrobe","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwpaths:red_sandstone_flagstone_stairs","mcwfences:modern_andesite_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","utilitix:advanced_brewery","mcwlights:copper_double_candle_holder","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:stone_window2","mcwwindows:metal_four_window","minecraft:oak_button","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","pneumaticcraft:wall_lamp_lime","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","mcwbiomesoplenty:maple_window","supplementaries:flags/flag_blue","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:spruce_drawer_counter","additionallanterns:diamond_lantern","railcraft:steel_shears","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwpaths:cobblestone_honeycomb_paving","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","securitycraft:reinforced_lime_stained_glass_pane_from_glass","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","deeperdarker:echo_chest_boat","eccentrictome:tome","securitycraft:reinforced_warped_fence","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","itemcollectors:basic_collector","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","mcwtrpdoors:spruce_barrel_trapdoor","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","minecraft:oak_fence_gate","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwpaths:red_sandstone_crystal_floor_slab","mcwroofs:sandstone_attic_roof","buildinggadgets2:gadget_copy_paste","handcrafted:golden_wide_pot","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwlights:soul_spruce_tiki_torch","mcwroofs:yellow_concrete_attic_roof","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","minecraft:red_sandstone_stairs","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","utilitarian:utility/spruce_logs_to_trapdoors","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","pneumaticcraft:redstone_module","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwfurnitures:spruce_double_drawer","undergarden:smogstem_boat","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","computercraft:computer_advanced","mcwwindows:mangrove_plank_four_window","mcwfurnitures:stripped_spruce_drawer_counter","utilitarian:utility/spruce_logs_to_stairs","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwwindows:acacia_plank_four_window","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","mcwlights:copper_chandelier","mcwfurnitures:stripped_spruce_modern_wardrobe","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","reliquary:uncrafting/slime_ball","mcwdoors:jungle_paper_door","mcwpaths:red_sandstone_clover_paving","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","everythingcopper:copper_pressure_plate","mcwroofs:jungle_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","mcwbiomesoplenty:stripped_palm_log_four_window","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","mcwroofs:gutter_base_light_blue","additionallanterns:diorite_lantern","mcwpaths:red_sandstone_running_bond_path","mcwbiomesoplenty:pine_window","immersiveengineering:crafting/conveyor_vertical","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwroofs:deepslate_roof","cfm:oak_chair","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","minecraft:yellow_stained_glass","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwwindows:red_sandstone_pane_window","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","alltheores:steel_rod","handcrafted:goat_trophy","mcwroofs:red_sandstone_lower_roof","bigreactors:blasting/graphite_from_coal","paraglider:paraglider","mcwwindows:dark_prismarine_four_window","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","utilitix:anvil_cart","mcwfurnitures:spruce_desk","securitycraft:reinforced_white_stained_glass_pane_from_dye","pneumaticcraft:wall_lamp_red","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:wheat_dough_from_eggs","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","mcwbiomesoplenty:willow_pane_window","mcwtrpdoors:oak_barrel_trapdoor","minecraft:lapis_block","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","immersiveengineering:crafting/strip_curtain","mcwtrpdoors:spruce_swamp_trapdoor","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","undergarden:catalyst","immersiveengineering:crafting/voltmeter","domum_ornamentum:purple_cobblestone_extra","mcwbiomesoplenty:redwood_plank_window","minecraft:iron_axe","mcwfurnitures:spruce_counter","utilitarian:snad/snad","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","mcwbiomesoplenty:empyreal_plank_window2","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","securitycraft:blacklist_module","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","mcwwindows:spruce_plank_pane_window","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","immersiveengineering:crafting/item_batcher","mcwdoors:spruce_classic_door","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","minecraft:jungle_fence","minecraft:gold_ingot_from_gold_block","mcwdoors:print_whispering","mcwfurnitures:spruce_striped_chair","mcwwindows:acacia_louvered_shutter","minecraft:glass_bottle","mcwroofs:lime_concrete_lower_roof","mcwfences:mesh_metal_fence","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","mcwfurnitures:stripped_spruce_double_drawer_counter","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","mcwbiomesoplenty:magic_plank_window2","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","mcwfurnitures:stripped_spruce_stool_chair","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwwindows:spruce_four_window","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","botania:speed_up_belt","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","mcwpaths:red_sandstone_crystal_floor","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alchemistry:combiner","mcwdoors:jungle_stable_head_door","pneumaticcraft:air_canister","mcwwindows:prismarine_pane_window","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","supplementaries:slice_map","mcwpaths:red_sandstone_windmill_weave_stairs","appflux:insulating_resin","mcwroofs:cobblestone_lower_roof","handcrafted:oak_pillar_trim","occultism:crafting/butcher_knife","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwbiomesoplenty:dead_window","cfm:stripped_spruce_desk_cabinet","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","cfm:spruce_table","aether:iron_pendant","pneumaticcraft:kerosene_lamp","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","cfm:gray_grill","supplementaries:flags/flag_purple","paraglider:cosmetic/rito_goddess_statue","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:stripped_redwood_log_window","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","pneumaticcraft:wall_lamp_inverted_cyan","mcwroofs:spruce_steep_roof","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","mcwwindows:granite_parapet","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","farmersdelight:iron_knife","railcraft:steel_helmet","mcwwindows:acacia_window2","mcwroofs:red_sandstone_roof","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","immersiveengineering:crafting/glider","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:spruce_kitchen_counter","immersiveengineering:crafting/manual","cfm:oak_upgraded_gate","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","immersiveengineering:crafting/windmill_sail","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","minecraft:cut_red_sandstone_slab","supplementaries:speaker_block","dyenamics:fluorescent_concrete_powder","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","minecraft:chiseled_red_sandstone_from_red_sandstone_stonecutting","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbiomesoplenty:redwood_four_window","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","mcwbiomesoplenty:magic_plank_window","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","additionallanterns:copper_lantern","mcwroofs:black_concrete_attic_roof","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwlights:golden_small_chandelier","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","silentgear:emerald_shard","mcwwindows:lime_mosaic_glass","pneumaticcraft:reinforced_brick_stairs","sophisticatedstorage:oak_barrel","mcwdoors:spruce_nether_door","everythingcopper:copper_trapdoor","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:dead_pane_window","mcwwindows:crimson_blinds","mcwwindows:diorite_window2","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwfurnitures:oak_bookshelf","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","mcwwindows:oak_log_parapet","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","mcwfurnitures:stripped_spruce_bookshelf_drawer","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","additionallanterns:gold_chain","mcwroofs:gutter_base","forbidden_arcanus:processed_obsidian_block_from_obsidian_ingot","mcwroofs:gutter_middle_purple","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","minecraft:writable_book","dyenamics:maroon_stained_glass","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:golden_apple","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","simplylight:rodlamp","mcwwindows:stripped_acacia_log_window2","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","pneumaticcraft:pressure_chamber_valve_x1","pneumaticcraft:reinforced_brick_wall","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","minecraft:spruce_wood","mcwpaths:sandstone_clover_paving","mcwtrpdoors:spruce_ranch_trapdoor","aquaculture:sushi","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","mcwbiomesoplenty:umbran_plank_window2","mcwdoors:metal_reinforced_door","rftoolsbuilder:green_shield_template_block","domum_ornamentum:blue_cobblestone_extra","pneumaticcraft:wall_lamp_inverted_blue","mcwpaths:sandstone_crystal_floor_stairs","minecraft:green_dye","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:maple_plank_window","twilightforest:canopy_boat","mcwfences:warped_horse_fence","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwlights:jungle_ceiling_fan_light","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","minecraft:smooth_red_sandstone_slab","mcwpaths:sandstone_strewn_rocky_path","dyenamics:mint_stained_glass","alltheores:gold_plate","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","minecraft:golden_boots","securitycraft:harming_module","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","mcwbiomesoplenty:jacaranda_four_window","mcwbridges:iron_bridge_pier","cfm:pink_grill","pneumaticcraft:magnet_upgrade","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","mcwroofs:sandstone_roof","pneumaticcraft:wall_lamp_purple","mcwwindows:granite_window2","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","cfm:oak_kitchen_sink_light","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwwindows:diorite_pane_window","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","minecraft:smooth_red_sandstone_slab_from_smooth_red_sandstone_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","immersiveengineering:crafting/cushion","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_yellow_stained_glass","mcwfurnitures:oak_desk","alltheores:bronze_plate","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","mcwwindows:jungle_log_parapet","pneumaticcraft:stone_base","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","mcwwindows:white_mosaic_glass_pane","paraglider:cosmetic/goddess_statue","mcwwindows:purple_mosaic_glass","cfm:stripped_spruce_coffee_table","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","pneumaticcraft:pressure_chamber_wall","minecraft:glass","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","mcwdoors:spruce_paper_door","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","mcwroofs:brown_concrete_attic_roof","mcwwindows:crimson_stem_window","mcwlights:festive_wall_lantern","mcwbiomesoplenty:pine_plank_pane_window","mcwwindows:mangrove_log_parapet","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","minecraft:light_blue_concrete_powder","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","minecraft:cut_red_sandstone","blue_skies:lunar_bookshelf","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","mcwlights:soul_oak_tiki_torch","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","mcwdoors:spruce_stable_door","mcwlights:copper_small_chandelier","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","deepresonance:radiation_monitor","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","railcraft:steel_gear","mcwtrpdoors:oak_swamp_trapdoor","cfm:spruce_upgraded_gate","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","securitycraft:reinforced_red_stained_glass","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","mcwbridges:oak_rail_bridge","delightful:knives/steel_knife","ad_astra:steel_block","aether:leather_gloves","minecraft:spruce_chest_boat","mcwroofs:cyan_concrete_roof","mcwfurnitures:spruce_cupboard_counter","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","ae2:network/cells/item_cell_housing","minecraft:cobblestone_wall_from_cobblestone_stonecutting","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","travelersbackpack:hay","pneumaticcraft:wall_lamp_inverted_pink","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","travelersbackpack:cactus","immersiveengineering:crafting/conveyor_splitter","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","everythingcopper:copper_hoe","pneumaticcraft:wall_lamp_light_gray","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","mcwfurnitures:stripped_spruce_triple_drawer","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","alltheores:bronze_rod","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","minecraft:glass_pane","supplementaries:timber_brace","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","minecraft:lectern","mcwroofs:orange_concrete_roof","minecraft:golden_sword","mcwlights:acacia_ceiling_fan_light","securitycraft:reinforced_brown_stained_glass","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","minecraft:clay","mcwbiomesoplenty:pine_pane_window","handcrafted:golden_medium_pot","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","immersiveengineering:crafting/conveyor_basic_covered","utilitix:jungle_shulker_boat","sfm:fancy_to_cable","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwroofs:red_sandstone_steep_roof","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","mcwwindows:acacia_window","mcwfurnitures:stripped_spruce_cupboard_counter","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","silentgear:stone_rod","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","immersiveengineering:crafting/balloon","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwlights:pink_paper_lamp","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","pneumaticcraft:wall_lamp_inverted_light_blue","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","mcwpaths:red_sandstone_running_bond_slab","aquaculture:fishing_line","minecolonies:supplychestdeployer","aether:iron_gloves","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","dyenamics:wine_concrete_powder","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","mcwpaths:red_sandstone_running_bond_stairs","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","pneumaticcraft:stomp_upgrade","mcwfurnitures:jungle_cupboard_counter","mcwwindows:crimson_stem_parapet","supplementaries:lapis_bricks","mcwbiomesoplenty:mahogany_highley_gate","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","aether:book_of_lore","minecraft:brush","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","nethersdelight:blackstone_blast_furnace","aether:iron_boots_repairing","mcwfurnitures:oak_kitchen_cabinet","sfm:cable","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwdoors:spruce_modern_door","minecraft:hay_block","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:mahogany_window2","pneumaticcraft:compressed_iron_boots","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwbiomesoplenty:palm_pyramid_gate","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","supplementaries:pulley","minecraft:gray_stained_glass","cfm:magenta_trampoline","utilitarian:utility/spruce_logs_to_boats","mcwroofs:blue_concrete_top_roof","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","pneumaticcraft:reinforced_chest","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","allthecompressed:compress/glass_1x","ad_astra:compressor","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","pneumaticcraft:reinforced_stone_slab","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","railcraft:goggles","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","travelersbackpack:iron","mcwroofs:gutter_base_red","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","create:copper_ladder_from_ingots_copper_stonecutting","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","mcwpaths:red_sandstone_honeycomb_paving","simplylight:illuminant_brown_block_toggle","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","minecraft:iron_boots","mcwbiomesoplenty:empyreal_window2","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","mcwpaths:stone_flagstone_slab","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","mcwtrpdoors:spruce_tropical_trapdoor","securitycraft:reinforced_gray_stained_glass","mcwtrpdoors:print_cottage","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","mcwroofs:gray_concrete_roof","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","immersiveengineering:crafting/generator","sophisticatedstorage:oak_limited_barrel_4","mcwwindows:warped_stem_four_window","securitycraft:reinforced_black_stained_glass","securitycraft:sentry","supplementaries:hourglass","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:gutter_base_pink","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","cfm:stripped_spruce_table","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwwindows:spruce_plank_window","supplementaries:checker","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","croptopia:flour","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","domum_ornamentum:cactus_extra","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwpaths:red_sandstone_crystal_floor_stairs","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","utilitarian:utility/spruce_logs_to_slabs","mcwbiomesoplenty:willow_wired_fence","immersiveengineering:crafting/conveyor_extract","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwpaths:red_sandstone_windmill_weave_slab","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:white_stained_glass","immersiveengineering:crafting/shovel_steel","securitycraft:taser","mcwwindows:iron_shutter","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","travelersbackpack:emerald","farmersdelight:cutting_board","mcwbiomesoplenty:pine_plank_window","minecraft:note_block","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","immersiveengineering:crafting/sawblade","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwwindows:brown_mosaic_glass_pane","mcwroofs:red_sandstone_upper_steep_roof","mcwpaths:stone_windmill_weave_stairs","railcraft:steel_shovel","mcwfurnitures:stripped_spruce_large_drawer","minecraft:smooth_sandstone","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","supplementaries:altimeter","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","cfm:stripped_jungle_desk","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","minecraft:spruce_planks","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","mcwfurnitures:spruce_triple_drawer","pneumaticcraft:wall_lamp_inverted_red","railcraft:steel_tunnel_bore_head","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","allthecompressed:compress/sand_1x","minecraft:magenta_stained_glass","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwbiomesoplenty:stripped_maple_log_four_window","pneumaticcraft:thermal_lagging","cfm:stripped_jungle_desk_cabinet","aquaculture:jellyfish_to_slimeball","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","paraglider:cosmetic/kakariko_goddess_statue","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","cfm:spruce_cabinet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwwindows:mangrove_plank_window2","minecraft:jungle_stairs","travelersbackpack:backpack_tank","mcwwindows:mangrove_pane_window","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","farmersdelight:flint_knife","mcwfurnitures:spruce_large_drawer","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","mcwwindows:stripped_warped_stem_window","supplementaries:candle_holders/candle_holder_white","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","mcwfurnitures:stripped_spruce_drawer","mcwroofs:thatch_upper_lower_roof","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","minecraft:red_sandstone_wall_from_red_sandstone_stonecutting","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","mcwroofs:blackstone_roof","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","minecraft:oak_chest_boat","mcwdoors:spruce_waffle_door","simplylight:illuminant_purple_block_on_toggle","mcwpaths:red_sandstone_windmill_weave","simplylight:illuminant_red_block_dyed","handcrafted:oak_dining_bench","simplylight:illuminant_gray_block_dyed","aether:wooden_pickaxe_repairing","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","mcwbiomesoplenty:stripped_umbran_pane_window","twilightforest:mining_chest_boat","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","aether:skyroot_bookshelf","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","pneumaticcraft:logistics_core","everythingcopper:copper_helmet","mcwroofs:red_concrete_upper_lower_roof","mythicbotany:rune_holder","mcwwindows:spruce_plank_four_window","modularrouters:blank_upgrade","mcwdoors:spruce_swamp_door","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","cfm:stripped_spruce_cabinet","minecraft:mangrove_boat","minecraft:bread","pneumaticcraft:thermostat_module","mcwdoors:spruce_barn_glass_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","mcwdoors:garage_silver_door","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","mcwbiomesoplenty:dead_four_window","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwfurnitures:spruce_coffee_table","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:iron_shovel","mcwroofs:blue_concrete_upper_lower_roof","biomesoplenty:willow_boat","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwwindows:purple_mosaic_glass_pane","mcwfurnitures:stripped_spruce_bookshelf_cupboard","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwlights:golden_wall_candle_holder","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","mcwwindows:crimson_planks_window2","securitycraft:disguise_module","railcraft:animal_detector","mcwwindows:deepslate_four_window","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","pneumaticcraft:camo_applicator","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","reliquary:infernal_claw","domum_ornamentum:blockbarreldeco_standing","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwroofs:spruce_upper_lower_roof","supplementaries:gold_trapdoor","cfm:fridge_light","pneumaticcraft:speed_upgrade","mcwwindows:stripped_spruce_log_window2","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","rftoolsutility:counter_module","cfm:spruce_chair","minecraft:oak_trapdoor","littlelogistics:locomotive_route","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","mcwfurnitures:stripped_jungle_bookshelf_cupboard","securitycraft:reinforced_bookshelf","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwfurnitures:stripped_spruce_covered_desk","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mcwtrpdoors:cherry_ranch_trapdoor","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","immersiveengineering:crafting/sorter","mcwbridges:red_sandstone_bridge_pier","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","cfm:oak_cabinet","rftoolsutility:energyplus_module","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","mcwpaths:red_sandstone_dumble_paving","railcraft:bronze_ingot_crafted_with_ingots","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:lapis_lazuli","minecraft:candle","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","mcwwindows:stripped_mangrove_log_window2","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwbridges:balustrade_orange_sandstone_bridge","mcwwindows:stripped_cherry_log_four_window","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","mcwfences:oak_curved_gate","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","supplementaries:wrench","deepresonance:radiation_suit_boots","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","mcwdoors:oak_swamp_door","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","aether:iron_axe_repairing","mcwpaths:red_sandstone_basket_weave_paving","mcwwindows:cherry_window2","minecraft:jungle_slab","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","minecraft:smoker","pneumaticcraft:wall_lamp_brown","silentgear:rough_rod","mcwwindows:stripped_oak_pane_window","mcwwindows:jungle_plank_parapet","additionallanterns:netherite_lantern","twilightforest:dark_chest_boat","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","aether:iron_leggings_repairing","railcraft:iron_tunnel_bore_head","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:spruce_glass_table","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","pneumaticcraft:omnidirectional_hopper","mcwbiomesoplenty:jacaranda_plank_window2","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwroofs:green_concrete_upper_steep_roof","mcwfurnitures:spruce_covered_desk","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwwindows:stripped_spruce_log_four_window","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","minecraft:red_sandstone_slab","mcwroofs:gray_concrete_upper_steep_roof","mcwdoors:spruce_mystic_door","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwtrpdoors:spruce_beach_trapdoor","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","pneumaticcraft:wall_lamp_inverted_brown","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","immersiveengineering:crafting/drillhead_steel","mcwbiomesoplenty:fir_plank_four_window","mcwpaths:sandstone_flagstone_path","minecraft:smooth_red_sandstone_stairs_from_smooth_red_sandstone_stonecutting","utilitarian:utility/spruce_logs_to_doors","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwdoors:jungle_tropical_door","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","mcwpaths:red_sandstone_flagstone_path","mcwroofs:spruce_upper_steep_roof","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","deepresonance:machine_frame","travelersbackpack:lapis","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","mcwfurnitures:jungle_triple_drawer","mcwfurnitures:spruce_modern_chair","crafting_on_a_stick:crafting_table","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","dyenamics:honey_stained_glass","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:spruce_double_drawer_counter","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","alltheores:gold_rod","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwlights:mangrove_ceiling_fan_light","mcwpaths:sandstone_diamond_paving","biomesoplenty:jacaranda_chest_boat","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","minecraft:chiseled_red_sandstone","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","mcwpaths:sandstone_windmill_weave_slab","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","immersiveengineering:crafting/ersatz_leather","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","mcwroofs:white_concrete_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","handcrafted:jungle_fancy_bed","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","mcwbiomesoplenty:redwood_window2","mcwfurnitures:stripped_spruce_coffee_table","mcwfurnitures:spruce_double_wardrobe","securitycraft:security_camera","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","mcwlights:cross_lantern","mcwbiomesoplenty:magic_window2","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","cfm:spruce_blinds","railcraft:receiver_circuit","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","securitycraft:reinforced_packed_mud","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","mcwdoors:spruce_tropical_door","immersiveengineering:crafting/conveyor_dropper","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","mcwroofs:blackstone_top_roof","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","pneumaticcraft:wall_lamp_inverted_green","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwfurnitures:spruce_chair","mcwroofs:green_concrete_lower_roof","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","mcwroofs:jungle_lower_roof","mcwbridges:orange_sandstone_bridge","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","mcwroofs:oak_planks_upper_lower_roof","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:age_detector","railcraft:personal_world_spike","pneumaticcraft:wall_lamp_orange","handcrafted:oak_nightstand","dyenamics:wine_stained_glass","mcwlights:lava_lamp","toolbelt:pouch","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:reinforced_light_blue_stained_glass","securitycraft:door_indestructible_iron_item","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","cfm:jungle_table","aether:iron_chestplate_repairing","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwwindows:cherry_pane_window","simplylight:illuminant_green_block_toggle","mcwdoors:garage_white_door","everythingcopper:copper_rail","utilitix:armed_stand","cfm:black_grill","botania:lexicon","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwpaths:stone_running_bond","mcwlights:green_paper_lamp","mcwwindows:birch_plank_parapet","mcwpaths:red_sandstone_running_bond","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","railcraft:steel_chestplate","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","minecraft:cut_sandstone","mcwdoors:spruce_japanese2_door","mcwwindows:jungle_shutter","enderio:fluid_tank","mcwroofs:oak_planks_roof","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","bigreactors:energizer/casing","deepresonance:resonating_plate","mcwfurnitures:stripped_spruce_table","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","minecraft:birch_boat","mcwfences:spruce_highley_gate","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","mcwpaths:red_sandstone_windmill_weave_path","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","mcwdoors:jungle_barn_door","minecraft:campfire","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","minecraft:lime_stained_glass","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","pneumaticcraft:logistics_configurator","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","mcwbiomesoplenty:stripped_umbran_log_four_window","minecraft:black_stained_glass"],toBeDisplayed:["mcwfurnitures:stripped_spruce_bookshelf","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","handcrafted:spider_trophy","mcwtrpdoors:jungle_cottage_trapdoor","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","chimes:copper_chimes","pneumaticcraft:wall_lamp_white","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","mcwtrpdoors:mangrove_bark_trapdoor","mcwpaths:jungle_planks_path","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","create:oak_window","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","mcwwindows:oak_plank_pane_window","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwfences:acacia_wired_fence","mcwfurnitures:spruce_modern_desk","mcwfurnitures:spruce_drawer","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","railcraft:controller_circuit","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","cfm:spatula","securitycraft:reinforced_cherry_fence_gate","pneumaticcraft:charging_upgrade","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","minecolonies:shapetool","supplementaries:bubble_blower","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwroofs:gutter_base_light_gray","additionallanterns:red_sandstone_chain","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:bamboo_chest_raft","minecraft:sandstone_slab_from_sandstone_stonecutting","reliquary:mob_charm_fragments/slime","cfm:stripped_spruce_desk","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","immersiveengineering:crafting/rockcutter","mcwtrpdoors:spruce_barred_trapdoor","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","blue_skies:comet_bookshelf","dyenamics:aquamarine_concrete_powder","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:oak_kitchen_counter","mcwroofs:spruce_roof","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","cfm:stripped_spruce_bedside_cabinet","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","biomesoplenty:palm_chest_boat","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","handcrafted:oak_bench","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","mcwfences:nether_brick_grass_topped_wall","mcwwindows:birch_plank_window","minecraft:sandstone","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","railcraft:brass_ingot_crafted_with_ingots","dyenamics:spring_green_concrete_powder","mcwpaths:sandstone_running_bond_stairs","mcwdoors:spruce_four_panel_door","cfm:jungle_cabinet","mcwbiomesoplenty:mahogany_window","cfm:stripped_spruce_chair","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","dyenamics:bed/navy_bed","pneumaticcraft:transfer_gadget","securitycraft:username_logger","mcwfurnitures:stripped_spruce_striped_chair","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwfurnitures:stripped_oak_drawer_counter","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwbiomesoplenty:umbran_plank_four_window","handcrafted:jungle_side_table","immersiveengineering:crafting/coal_coke_to_coke","mcwbridges:spruce_bridge_pier","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","securitycraft:keypad_frame","pneumaticcraft:compressed_iron_chestplate","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:spruce_bedside_cabinet","twilightforest:time_chest_boat","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:pine_plank_window2","minecraft:jungle_door","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwpaths:red_sandstone_diamond_paving","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","handcrafted:red_sandstone_pillar_trim","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","pneumaticcraft:wall_lamp_inverted_white","railcraft:world_spike","mcwlights:golden_chain","mcwdoors:garage_gray_door","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","mcwbridges:sandstone_bridge_pier","securitycraft:reinforced_purple_stained_glass","mcwfurnitures:stripped_spruce_double_drawer","mcwpaths:oak_planks_path","littlelogistics:seater_car","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","mcwbiomesoplenty:stripped_dead_pane_window","cfm:green_grill","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","minecraft:oak_pressure_plate","cfm:orange_grill","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwwindows:quartz_window","minecraft:cut_red_sandstone_from_red_sandstone_stonecutting","mcwwindows:dark_oak_shutter","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","cfm:jungle_desk","mcwroofs:oak_roof","reliquary:mob_charm_fragments/creeper","minecraft:mangrove_chest_boat","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","mcwtrpdoors:oak_barred_trapdoor","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwroofs:thatch_roof","mcwwindows:warped_curtain_rod","mcwdoors:spruce_glass_door","biomesoplenty:empyreal_boat","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twilightforest:transformation_chest_boat","additionallanterns:end_stone_lantern","twigs:silt","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","travelersbackpack:coal","mcwdoors:oak_four_panel_door","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwtrpdoors:spruce_whispering_trapdoor","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","pneumaticcraft:refinery","pneumaticcraft:thermal_compressor","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwwindows:white_mosaic_glass","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwroofs:jungle_upper_steep_roof","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwfences:railing_andesite_wall","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","biomesoplenty:empyreal_chest_boat","cfm:stripped_spruce_crate","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","mcwwindows:spruce_window2","utilitix:mob_yoinker","create:crafting/materials/andesite_alloy","mcwwindows:andesite_four_window","mcwtrpdoors:jungle_swamp_trapdoor","mcwlights:golden_low_candle_holder","mcwroofs:gutter_base_gray","mcwpaths:red_sandstone_crystal_floor_path","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwroofs:thatch_top_roof","mcwpaths:cobblestone_diamond_paving","supplementaries:cog_block","mcwlights:golden_chandelier","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwwindows:birch_plank_pane_window","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","cfm:stripped_jungle_table","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwpaths:red_sandstone_flagstone_slab","mcwroofs:spruce_top_roof","supplementaries:bomb","dyenamics:navy_stained_glass","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwpaths:sandstone_flagstone_slab","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","handcrafted:jungle_pillar_trim","pneumaticcraft:elevator_frame","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwwindows:oak_blinds","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","twilightforest:sorting_chest_boat","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwdoors:oak_cottage_door","pneumaticcraft:wall_lamp_cyan","minecraft:red_sandstone_slab_from_red_sandstone_stonecutting","create:crafting/kinetics/fluid_pipe","immersiveengineering:crafting/torch","pneumaticcraft:pressure_chamber_glass_x4","mcwfences:warped_pyramid_gate","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","pneumaticcraft:pressure_chamber_glass_x1","allthecompressed:compress/lapis_block_1x","railcraft:signal_circuit","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","undergarden:gloom_o_lantern","mcwbiomesoplenty:pine_window2","mcwwindows:birch_window","mcwwindows:blackstone_four_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","twigs:oak_table","simplylight:illuminant_purple_block_toggle","pneumaticcraft:heat_pipe","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","mcwfurnitures:stripped_spruce_glass_table","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","twilightforest:twilight_oak_chest_boat","mcwpaths:stone_windmill_weave","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","mcwroofs:red_sandstone_attic_roof","mcwlights:soul_double_street_lamp","mcwpaths:red_sandstone_flagstone","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","mcwfurnitures:oak_wardrobe","mcwfurnitures:spruce_wardrobe","mcwroofs:thatch_lower_roof","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","mcwpaths:sandstone_windmill_weave_stairs","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwroofs:blue_concrete_roof","travelersbackpack:bee","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","mcwwindows:stripped_dark_oak_log_window2","dyenamics:conifer_stained_glass","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","cfm:light_gray_trampoline","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","pneumaticcraft:compressed_brick_wall","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","mcwroofs:red_sandstone_top_roof","mcwdoors:jungle_japanese2_door","minecraft:smooth_red_sandstone","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:oak_sign","mcwwindows:stone_pane_window","pneumaticcraft:wall_lamp_pink","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwroofs:gutter_base_magenta","mcwbiomesoplenty:fir_plank_window2","domum_ornamentum:cobblestone_extra","additionallanterns:emerald_lantern","handcrafted:oak_counter","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","mcwfurnitures:spruce_stool_chair","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","minecraft:red_sandstone_stairs_from_red_sandstone_stonecutting","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:oak_double_wardrobe","mcwdoors:spruce_stable_head_door","mcwfurnitures:stripped_spruce_counter","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","mcwroofs:spruce_lower_roof","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","minecraft:cut_red_sandstone_slab_from_red_sandstone_stonecutting","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwdoors:spruce_whispering_door","mcwroofs:oak_planks_steep_roof","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwbiomesoplenty:dead_picket_fence","pneumaticcraft:vortex_cannon","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:purple_concrete_powder","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","biomesoplenty:maple_chest_boat","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","cfm:stripped_oak_chair","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwwindows:stripped_cherry_pane_window","cfm:diving_board","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/light_engineering","immersiveengineering:crafting/axe_steel","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwpaths:red_sandstone_square_paving","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_base_green","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","railcraft:steel_leggings","mcwroofs:oak_planks_attic_roof","securitycraft:reinforced_pink_stained_glass","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","littlelogistics:vessel_charger","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","naturescompass:natures_compass","railcraft:radio_circuit","minecraft:smooth_red_sandstone_stairs","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:cut_red_sandstone_slab_from_cut_red_sandstone_stonecutting","minecraft:wooden_axe","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwlights:golden_double_candle_holder","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","mcwtrpdoors:spruce_classic_trapdoor","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:red_sandstone_upper_lower_roof","mcwroofs:oak_steep_roof","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","supplementaries:daub_brace","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","handcrafted:stackable_book","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","mcwroofs:sandstone_top_roof","securitycraft:reinforced_green_stained_glass","pneumaticcraft:wall_lamp_blue","minecraft:iron_bars","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwtrpdoors:spruce_mystic_trapdoor","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","create:crafting/kinetics/copper_valve_handle","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_red","handcrafted:red_sandstone_corner_trim","mcwroofs:lime_concrete_attic_roof","aether:skyroot_piston","pneumaticcraft:manometer","securitycraft:alarm","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","pneumaticcraft:wall_lamp_inverted_magenta","dyenamics:banner/bubblegum_banner","mcwroofs:oak_top_roof","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","mcwpaths:sandstone_honeycomb_paving","additionallanterns:amethyst_lantern","utilitix:crude_furnace","utilitarian:utility/spruce_logs_to_pressure_plates","utilitix:comparator_redirector_down","mcwwindows:stone_window","create:copper_shingles_from_ingots_copper_stonecutting","mcwfences:granite_grass_topped_wall","everythingcopper:copper_sword","mcwwindows:birch_shutter","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","mcwroofs:cobblestone_roof","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","mcwdoors:print_spruce","mcwfences:dark_oak_picket_fence","alchemistry:compactor","travelersbackpack:sandstone","cfm:jungle_upgraded_fence","minecraft:dropper","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","dyenamics:bed/maroon_bed","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","mcwbiomesoplenty:pine_picket_fence","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","mcwdoors:oak_japanese2_door","mcwroofs:jungle_roof","mcwfurnitures:stripped_spruce_wardrobe","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","mcwpaths:red_sandstone_strewn_rocky_path","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:light_gray_grill","mcwwindows:mangrove_window","dyenamics:cherenkov_concrete_powder","mcwwindows:quartz_pane_window","cfm:oak_desk","mcwtrpdoors:metal_trapdoor","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","immersiveengineering:crafting/conveyor_redstone","mcwbiomesoplenty:magic_pyramid_gate","mcwfurnitures:spruce_table","mcwbiomesoplenty:hellbark_hedge","cfm:spruce_desk_cabinet","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","supplementaries:daub_frame","railcraft:signal_tuner","pneumaticcraft:minigun_upgrade","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","mcwpaths:sandstone_running_bond_slab","minecraft:crafting_table","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:sandstone_wall","minecraft:red_sandstone_wall","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:stripped_mahogany_log_window","blue_skies:food_prep_table","cfm:pink_trampoline","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","securitycraft:briefcase","mcwwindows:green_mosaic_glass","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:stripped_oak_large_drawer","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","minecraft:iron_nugget_from_smelting","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","mcwroofs:sandstone_lower_roof","travelersbackpack:hose_nozzle","securitycraft:claymore","handcrafted:jungle_dining_bench","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","ae2:network/cables/dense_smart_fluix","aether:golden_pendant","mcwpaths:sandstone_basket_weave_paving","mcwroofs:sandstone_steep_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","mcwwindows:stripped_acacia_log_four_window","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwroofs:orange_concrete_upper_lower_roof","mcwtrpdoors:spruce_barn_trapdoor","mcwfurnitures:spruce_modern_wardrobe","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwpaths:red_sandstone_flagstone_stairs","mcwfences:modern_andesite_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","utilitix:advanced_brewery","mcwlights:copper_double_candle_holder","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:stone_window2","mcwwindows:metal_four_window","minecraft:oak_button","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","pneumaticcraft:wall_lamp_lime","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","mcwbiomesoplenty:maple_window","supplementaries:flags/flag_blue","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:spruce_drawer_counter","additionallanterns:diamond_lantern","railcraft:steel_shears","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwpaths:cobblestone_honeycomb_paving","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","securitycraft:reinforced_lime_stained_glass_pane_from_glass","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","deeperdarker:echo_chest_boat","eccentrictome:tome","securitycraft:reinforced_warped_fence","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","itemcollectors:basic_collector","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","mcwtrpdoors:spruce_barrel_trapdoor","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","minecraft:oak_fence_gate","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwpaths:red_sandstone_crystal_floor_slab","mcwroofs:sandstone_attic_roof","buildinggadgets2:gadget_copy_paste","handcrafted:golden_wide_pot","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwlights:soul_spruce_tiki_torch","mcwroofs:yellow_concrete_attic_roof","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","minecraft:red_sandstone_stairs","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","utilitarian:utility/spruce_logs_to_trapdoors","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","pneumaticcraft:redstone_module","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwfurnitures:spruce_double_drawer","undergarden:smogstem_boat","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","computercraft:computer_advanced","mcwwindows:mangrove_plank_four_window","mcwfurnitures:stripped_spruce_drawer_counter","utilitarian:utility/spruce_logs_to_stairs","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwwindows:acacia_plank_four_window","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","mcwlights:copper_chandelier","mcwfurnitures:stripped_spruce_modern_wardrobe","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","reliquary:uncrafting/slime_ball","mcwdoors:jungle_paper_door","mcwpaths:red_sandstone_clover_paving","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","everythingcopper:copper_pressure_plate","mcwroofs:jungle_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","mcwbiomesoplenty:stripped_palm_log_four_window","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","mcwroofs:gutter_base_light_blue","additionallanterns:diorite_lantern","mcwpaths:red_sandstone_running_bond_path","mcwbiomesoplenty:pine_window","immersiveengineering:crafting/conveyor_vertical","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwroofs:deepslate_roof","cfm:oak_chair","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","minecraft:yellow_stained_glass","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwwindows:red_sandstone_pane_window","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","alltheores:steel_rod","handcrafted:goat_trophy","mcwroofs:red_sandstone_lower_roof","bigreactors:blasting/graphite_from_coal","paraglider:paraglider","mcwwindows:dark_prismarine_four_window","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","utilitix:anvil_cart","mcwfurnitures:spruce_desk","securitycraft:reinforced_white_stained_glass_pane_from_dye","pneumaticcraft:wall_lamp_red","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:wheat_dough_from_eggs","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","mcwbiomesoplenty:willow_pane_window","mcwtrpdoors:oak_barrel_trapdoor","minecraft:lapis_block","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","immersiveengineering:crafting/strip_curtain","mcwtrpdoors:spruce_swamp_trapdoor","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","undergarden:catalyst","immersiveengineering:crafting/voltmeter","domum_ornamentum:purple_cobblestone_extra","mcwbiomesoplenty:redwood_plank_window","minecraft:iron_axe","mcwfurnitures:spruce_counter","utilitarian:snad/snad","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","mcwbiomesoplenty:empyreal_plank_window2","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","securitycraft:blacklist_module","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","mcwwindows:spruce_plank_pane_window","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","immersiveengineering:crafting/item_batcher","mcwdoors:spruce_classic_door","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","minecraft:jungle_fence","minecraft:gold_ingot_from_gold_block","mcwdoors:print_whispering","mcwfurnitures:spruce_striped_chair","mcwwindows:acacia_louvered_shutter","minecraft:glass_bottle","mcwroofs:lime_concrete_lower_roof","mcwfences:mesh_metal_fence","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","mcwfurnitures:stripped_spruce_double_drawer_counter","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","mcwbiomesoplenty:magic_plank_window2","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","mcwfurnitures:stripped_spruce_stool_chair","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwwindows:spruce_four_window","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","botania:speed_up_belt","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","mcwpaths:red_sandstone_crystal_floor","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alchemistry:combiner","mcwdoors:jungle_stable_head_door","pneumaticcraft:air_canister","mcwwindows:prismarine_pane_window","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","supplementaries:slice_map","mcwpaths:red_sandstone_windmill_weave_stairs","appflux:insulating_resin","mcwroofs:cobblestone_lower_roof","handcrafted:oak_pillar_trim","occultism:crafting/butcher_knife","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwbiomesoplenty:dead_window","cfm:stripped_spruce_desk_cabinet","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","cfm:spruce_table","aether:iron_pendant","pneumaticcraft:kerosene_lamp","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","cfm:gray_grill","supplementaries:flags/flag_purple","paraglider:cosmetic/rito_goddess_statue","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:stripped_redwood_log_window","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","pneumaticcraft:wall_lamp_inverted_cyan","mcwroofs:spruce_steep_roof","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","mcwwindows:granite_parapet","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","farmersdelight:iron_knife","railcraft:steel_helmet","mcwwindows:acacia_window2","mcwroofs:red_sandstone_roof","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","immersiveengineering:crafting/glider","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:spruce_kitchen_counter","immersiveengineering:crafting/manual","cfm:oak_upgraded_gate","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","immersiveengineering:crafting/windmill_sail","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","minecraft:cut_red_sandstone_slab","supplementaries:speaker_block","dyenamics:fluorescent_concrete_powder","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","minecraft:chiseled_red_sandstone_from_red_sandstone_stonecutting","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbiomesoplenty:redwood_four_window","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","mcwbiomesoplenty:magic_plank_window","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","additionallanterns:copper_lantern","mcwroofs:black_concrete_attic_roof","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwlights:golden_small_chandelier","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","silentgear:emerald_shard","mcwwindows:lime_mosaic_glass","pneumaticcraft:reinforced_brick_stairs","sophisticatedstorage:oak_barrel","mcwdoors:spruce_nether_door","everythingcopper:copper_trapdoor","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:dead_pane_window","mcwwindows:crimson_blinds","mcwwindows:diorite_window2","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwfurnitures:oak_bookshelf","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","mcwwindows:oak_log_parapet","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","mcwfurnitures:stripped_spruce_bookshelf_drawer","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","additionallanterns:gold_chain","mcwroofs:gutter_base","forbidden_arcanus:processed_obsidian_block_from_obsidian_ingot","mcwroofs:gutter_middle_purple","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","minecraft:writable_book","dyenamics:maroon_stained_glass","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:golden_apple","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","simplylight:rodlamp","mcwwindows:stripped_acacia_log_window2","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","pneumaticcraft:pressure_chamber_valve_x1","pneumaticcraft:reinforced_brick_wall","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","minecraft:spruce_wood","mcwpaths:sandstone_clover_paving","mcwtrpdoors:spruce_ranch_trapdoor","aquaculture:sushi","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","mcwbiomesoplenty:umbran_plank_window2","mcwdoors:metal_reinforced_door","rftoolsbuilder:green_shield_template_block","domum_ornamentum:blue_cobblestone_extra","pneumaticcraft:wall_lamp_inverted_blue","mcwpaths:sandstone_crystal_floor_stairs","minecraft:green_dye","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:maple_plank_window","twilightforest:canopy_boat","mcwfences:warped_horse_fence","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwlights:jungle_ceiling_fan_light","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","minecraft:smooth_red_sandstone_slab","mcwpaths:sandstone_strewn_rocky_path","dyenamics:mint_stained_glass","alltheores:gold_plate","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","minecraft:golden_boots","securitycraft:harming_module","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","mcwbiomesoplenty:jacaranda_four_window","mcwbridges:iron_bridge_pier","cfm:pink_grill","pneumaticcraft:magnet_upgrade","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","mcwroofs:sandstone_roof","pneumaticcraft:wall_lamp_purple","mcwwindows:granite_window2","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","cfm:oak_kitchen_sink_light","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwwindows:diorite_pane_window","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","minecraft:smooth_red_sandstone_slab_from_smooth_red_sandstone_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","immersiveengineering:crafting/cushion","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_yellow_stained_glass","mcwfurnitures:oak_desk","alltheores:bronze_plate","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","mcwwindows:jungle_log_parapet","pneumaticcraft:stone_base","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","mcwwindows:white_mosaic_glass_pane","paraglider:cosmetic/goddess_statue","mcwwindows:purple_mosaic_glass","cfm:stripped_spruce_coffee_table","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","pneumaticcraft:pressure_chamber_wall","minecraft:glass","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","mcwdoors:spruce_paper_door","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","mcwroofs:brown_concrete_attic_roof","mcwwindows:crimson_stem_window","mcwlights:festive_wall_lantern","mcwbiomesoplenty:pine_plank_pane_window","mcwwindows:mangrove_log_parapet","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","minecraft:light_blue_concrete_powder","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","minecraft:cut_red_sandstone","blue_skies:lunar_bookshelf","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","mcwlights:soul_oak_tiki_torch","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","mcwdoors:spruce_stable_door","mcwlights:copper_small_chandelier","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","deepresonance:radiation_monitor","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","railcraft:steel_gear","mcwtrpdoors:oak_swamp_trapdoor","cfm:spruce_upgraded_gate","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","securitycraft:reinforced_red_stained_glass","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","mcwbridges:oak_rail_bridge","delightful:knives/steel_knife","ad_astra:steel_block","aether:leather_gloves","minecraft:spruce_chest_boat","mcwroofs:cyan_concrete_roof","mcwfurnitures:spruce_cupboard_counter","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","ae2:network/cells/item_cell_housing","minecraft:cobblestone_wall_from_cobblestone_stonecutting","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","travelersbackpack:hay","pneumaticcraft:wall_lamp_inverted_pink","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","travelersbackpack:cactus","immersiveengineering:crafting/conveyor_splitter","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","everythingcopper:copper_hoe","pneumaticcraft:wall_lamp_light_gray","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","mcwfurnitures:stripped_spruce_triple_drawer","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","alltheores:bronze_rod","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","minecraft:glass_pane","supplementaries:timber_brace","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","minecraft:lectern","mcwroofs:orange_concrete_roof","minecraft:golden_sword","mcwlights:acacia_ceiling_fan_light","securitycraft:reinforced_brown_stained_glass","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","minecraft:clay","mcwbiomesoplenty:pine_pane_window","handcrafted:golden_medium_pot","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","immersiveengineering:crafting/conveyor_basic_covered","utilitix:jungle_shulker_boat","sfm:fancy_to_cable","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwroofs:red_sandstone_steep_roof","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","mcwwindows:acacia_window","mcwfurnitures:stripped_spruce_cupboard_counter","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","silentgear:stone_rod","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","immersiveengineering:crafting/balloon","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwlights:pink_paper_lamp","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","pneumaticcraft:wall_lamp_inverted_light_blue","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","mcwpaths:red_sandstone_running_bond_slab","aquaculture:fishing_line","minecolonies:supplychestdeployer","aether:iron_gloves","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","dyenamics:wine_concrete_powder","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","mcwpaths:red_sandstone_running_bond_stairs","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","pneumaticcraft:stomp_upgrade","mcwfurnitures:jungle_cupboard_counter","mcwwindows:crimson_stem_parapet","supplementaries:lapis_bricks","mcwbiomesoplenty:mahogany_highley_gate","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","aether:book_of_lore","minecraft:brush","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","nethersdelight:blackstone_blast_furnace","aether:iron_boots_repairing","mcwfurnitures:oak_kitchen_cabinet","sfm:cable","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwdoors:spruce_modern_door","minecraft:hay_block","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:mahogany_window2","pneumaticcraft:compressed_iron_boots","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwbiomesoplenty:palm_pyramid_gate","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","supplementaries:pulley","minecraft:gray_stained_glass","cfm:magenta_trampoline","utilitarian:utility/spruce_logs_to_boats","mcwroofs:blue_concrete_top_roof","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","pneumaticcraft:reinforced_chest","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","allthecompressed:compress/glass_1x","ad_astra:compressor","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","pneumaticcraft:reinforced_stone_slab","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","railcraft:goggles","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","travelersbackpack:iron","mcwroofs:gutter_base_red","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","create:copper_ladder_from_ingots_copper_stonecutting","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","mcwpaths:red_sandstone_honeycomb_paving","simplylight:illuminant_brown_block_toggle","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","minecraft:iron_boots","mcwbiomesoplenty:empyreal_window2","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","mcwpaths:stone_flagstone_slab","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","mcwtrpdoors:spruce_tropical_trapdoor","securitycraft:reinforced_gray_stained_glass","mcwtrpdoors:print_cottage","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","mcwroofs:gray_concrete_roof","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","immersiveengineering:crafting/generator","sophisticatedstorage:oak_limited_barrel_4","mcwwindows:warped_stem_four_window","securitycraft:reinforced_black_stained_glass","securitycraft:sentry","supplementaries:hourglass","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:gutter_base_pink","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","cfm:stripped_spruce_table","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwwindows:spruce_plank_window","supplementaries:checker","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","croptopia:flour","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","domum_ornamentum:cactus_extra","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","mcwpaths:red_sandstone_crystal_floor_stairs","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","utilitarian:utility/spruce_logs_to_slabs","mcwbiomesoplenty:willow_wired_fence","immersiveengineering:crafting/conveyor_extract","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwpaths:red_sandstone_windmill_weave_slab","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:white_stained_glass","immersiveengineering:crafting/shovel_steel","securitycraft:taser","mcwwindows:iron_shutter","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","travelersbackpack:emerald","farmersdelight:cutting_board","mcwbiomesoplenty:pine_plank_window","minecraft:note_block","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","immersiveengineering:crafting/sawblade","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwwindows:brown_mosaic_glass_pane","mcwroofs:red_sandstone_upper_steep_roof","mcwpaths:stone_windmill_weave_stairs","railcraft:steel_shovel","mcwfurnitures:stripped_spruce_large_drawer","minecraft:smooth_sandstone","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","supplementaries:altimeter","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","cfm:stripped_jungle_desk","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","minecraft:spruce_planks","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","mcwfurnitures:spruce_triple_drawer","pneumaticcraft:wall_lamp_inverted_red","railcraft:steel_tunnel_bore_head","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","allthecompressed:compress/sand_1x","minecraft:magenta_stained_glass","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwbiomesoplenty:stripped_maple_log_four_window","pneumaticcraft:thermal_lagging","cfm:stripped_jungle_desk_cabinet","aquaculture:jellyfish_to_slimeball","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","paraglider:cosmetic/kakariko_goddess_statue","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","cfm:spruce_cabinet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwwindows:mangrove_plank_window2","minecraft:jungle_stairs","travelersbackpack:backpack_tank","mcwwindows:mangrove_pane_window","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","farmersdelight:flint_knife","mcwfurnitures:spruce_large_drawer","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","mcwwindows:stripped_warped_stem_window","supplementaries:candle_holders/candle_holder_white","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","mcwfurnitures:stripped_spruce_drawer","mcwroofs:thatch_upper_lower_roof","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","minecraft:red_sandstone_wall_from_red_sandstone_stonecutting","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","mcwroofs:blackstone_roof","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","minecraft:oak_chest_boat","mcwdoors:spruce_waffle_door","simplylight:illuminant_purple_block_on_toggle","mcwpaths:red_sandstone_windmill_weave","simplylight:illuminant_red_block_dyed","handcrafted:oak_dining_bench","simplylight:illuminant_gray_block_dyed","aether:wooden_pickaxe_repairing","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","mcwbiomesoplenty:stripped_umbran_pane_window","twilightforest:mining_chest_boat","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","aether:skyroot_bookshelf","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","pneumaticcraft:logistics_core","everythingcopper:copper_helmet","mcwroofs:red_concrete_upper_lower_roof","mythicbotany:rune_holder","mcwwindows:spruce_plank_four_window","modularrouters:blank_upgrade","mcwdoors:spruce_swamp_door","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","cfm:stripped_spruce_cabinet","minecraft:mangrove_boat","minecraft:bread","pneumaticcraft:thermostat_module","mcwdoors:spruce_barn_glass_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","mcwdoors:garage_silver_door","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","mcwbiomesoplenty:dead_four_window","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwfurnitures:spruce_coffee_table","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:iron_shovel","mcwroofs:blue_concrete_upper_lower_roof","biomesoplenty:willow_boat","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwwindows:purple_mosaic_glass_pane","mcwfurnitures:stripped_spruce_bookshelf_cupboard","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwlights:golden_wall_candle_holder","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","mcwwindows:crimson_planks_window2","securitycraft:disguise_module","railcraft:animal_detector","mcwwindows:deepslate_four_window","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","pneumaticcraft:camo_applicator","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","reliquary:infernal_claw","domum_ornamentum:blockbarreldeco_standing","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwroofs:spruce_upper_lower_roof","supplementaries:gold_trapdoor","cfm:fridge_light","pneumaticcraft:speed_upgrade","mcwwindows:stripped_spruce_log_window2","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","rftoolsutility:counter_module","cfm:spruce_chair","minecraft:oak_trapdoor","littlelogistics:locomotive_route","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","mcwfurnitures:stripped_jungle_bookshelf_cupboard","securitycraft:reinforced_bookshelf","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwfurnitures:stripped_spruce_covered_desk","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mcwtrpdoors:cherry_ranch_trapdoor","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","immersiveengineering:crafting/sorter","mcwbridges:red_sandstone_bridge_pier","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","cfm:oak_cabinet","rftoolsutility:energyplus_module","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","mcwpaths:red_sandstone_dumble_paving","railcraft:bronze_ingot_crafted_with_ingots","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:lapis_lazuli","minecraft:candle","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","mcwwindows:stripped_mangrove_log_window2","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwbridges:balustrade_orange_sandstone_bridge","mcwwindows:stripped_cherry_log_four_window","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","mcwfences:oak_curved_gate","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","supplementaries:wrench","deepresonance:radiation_suit_boots","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","mcwdoors:oak_swamp_door","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","aether:iron_axe_repairing","mcwpaths:red_sandstone_basket_weave_paving","mcwwindows:cherry_window2","minecraft:jungle_slab","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","minecraft:smoker","pneumaticcraft:wall_lamp_brown","silentgear:rough_rod","mcwwindows:stripped_oak_pane_window","mcwwindows:jungle_plank_parapet","additionallanterns:netherite_lantern","twilightforest:dark_chest_boat","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","aether:iron_leggings_repairing","railcraft:iron_tunnel_bore_head","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:spruce_glass_table","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","pneumaticcraft:omnidirectional_hopper","mcwbiomesoplenty:jacaranda_plank_window2","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwroofs:green_concrete_upper_steep_roof","mcwfurnitures:spruce_covered_desk","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwwindows:stripped_spruce_log_four_window","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","minecraft:red_sandstone_slab","mcwroofs:gray_concrete_upper_steep_roof","mcwdoors:spruce_mystic_door","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwtrpdoors:spruce_beach_trapdoor","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","pneumaticcraft:wall_lamp_inverted_brown","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","immersiveengineering:crafting/drillhead_steel","mcwbiomesoplenty:fir_plank_four_window","mcwpaths:sandstone_flagstone_path","minecraft:smooth_red_sandstone_stairs_from_smooth_red_sandstone_stonecutting","utilitarian:utility/spruce_logs_to_doors","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwdoors:jungle_tropical_door","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","mcwpaths:red_sandstone_flagstone_path","mcwroofs:spruce_upper_steep_roof","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","deepresonance:machine_frame","travelersbackpack:lapis","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","mcwfurnitures:jungle_triple_drawer","mcwfurnitures:spruce_modern_chair","crafting_on_a_stick:crafting_table","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","dyenamics:honey_stained_glass","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:spruce_double_drawer_counter","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","alltheores:gold_rod","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwlights:mangrove_ceiling_fan_light","mcwpaths:sandstone_diamond_paving","biomesoplenty:jacaranda_chest_boat","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","minecraft:chiseled_red_sandstone","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","mcwpaths:sandstone_windmill_weave_slab","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","immersiveengineering:crafting/ersatz_leather","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","mcwroofs:white_concrete_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","handcrafted:jungle_fancy_bed","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","mcwbiomesoplenty:redwood_window2","mcwfurnitures:stripped_spruce_coffee_table","mcwfurnitures:spruce_double_wardrobe","securitycraft:security_camera","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","mcwlights:cross_lantern","mcwbiomesoplenty:magic_window2","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","cfm:spruce_blinds","railcraft:receiver_circuit","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","securitycraft:reinforced_packed_mud","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","mcwdoors:spruce_tropical_door","immersiveengineering:crafting/conveyor_dropper","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","mcwroofs:blackstone_top_roof","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","pneumaticcraft:wall_lamp_inverted_green","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwfurnitures:spruce_chair","mcwroofs:green_concrete_lower_roof","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","mcwroofs:jungle_lower_roof","mcwbridges:orange_sandstone_bridge","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","mcwroofs:oak_planks_upper_lower_roof","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:age_detector","railcraft:personal_world_spike","pneumaticcraft:wall_lamp_orange","handcrafted:oak_nightstand","dyenamics:wine_stained_glass","mcwlights:lava_lamp","toolbelt:pouch","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:reinforced_light_blue_stained_glass","securitycraft:door_indestructible_iron_item","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","cfm:jungle_table","aether:iron_chestplate_repairing","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwwindows:cherry_pane_window","simplylight:illuminant_green_block_toggle","mcwdoors:garage_white_door","everythingcopper:copper_rail","utilitix:armed_stand","cfm:black_grill","botania:lexicon","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwpaths:stone_running_bond","mcwlights:green_paper_lamp","mcwwindows:birch_plank_parapet","mcwpaths:red_sandstone_running_bond","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","railcraft:steel_chestplate","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","minecraft:cut_sandstone","mcwdoors:spruce_japanese2_door","mcwwindows:jungle_shutter","enderio:fluid_tank","mcwroofs:oak_planks_roof","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","bigreactors:energizer/casing","deepresonance:resonating_plate","mcwfurnitures:stripped_spruce_table","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","minecraft:birch_boat","mcwfences:spruce_highley_gate","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","mcwpaths:red_sandstone_windmill_weave_path","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","mcwdoors:jungle_barn_door","minecraft:campfire","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","minecraft:lime_stained_glass","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","pneumaticcraft:logistics_configurator","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","mcwbiomesoplenty:stripped_umbran_log_four_window","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:5512,warning_level:0}}