{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:8,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:jump_boost"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:200,Id:13,ShowIcon:1b,ShowParticles:0b,"forge:id":"minecraft:water_breathing"}],Air:300s,Attributes:[{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"attributeslib:armor_shred"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]},{Amount:14.0d,Name:"Heart Containers",Operation:0,UUID:[I;-**********,-990297069,-**********,-847674717]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"attributeslib:current_hp_damage"},{Base:0.0d,Name:"minecraft:generic.attack_knockback"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"attributeslib:prot_shred"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Name:"attributeslib:arrow_velocity"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"attributeslib:overheal"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"attributeslib:arrow_damage"}],BalmData:{TrashSlot:{Count:1b,id:"minecraft:stick"},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-123270031,103236873,-**********,551414798],hasTentacle:0b},"comforts:sleep_data":{sleepTime:11690919L,tiredTime:11685121L,wakeTime:11685080L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:villager_hat"}],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:1,id:"artifacts:vampiric_glove"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:bunny_hoppers"}],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:1,furnaces:{furnace0:{X:-10067,Y:64,Z:-21137}}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:175,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:27618},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:3},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:7,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"simplemagnets:advancedmagnet",ItemStack:{Count:1b,id:"simplemagnets:advancedmagnet"}}]},"rftoolsutility:properties":{allowFlying:0b,buffTicks:103,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:cooked_beef","minecraft:bread"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","c9c0e2c2-59f5-4640-a507-dbfea5bc9042"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:-10064,tb_last_ground_location_y:-20,tb_last_ground_location_z:-21103,twilightforest_banished:1b},"apoth.affix_cooldown.apotheosis:sword/mob_effect/elusive":11130727L,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedBackpackSettings:{}},Health:38.0f,HurtByTimestamp:21448,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"sophisticatedbackpacks:gold_backpack",tag:{contentsUuid:[I;-612881550,**********,-**********,**********],inventorySlots:81,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:3}},{Count:1b,Slot:1b,id:"crafting_on_a_stick:crafting_table"},{Count:1b,Slot:4b,id:"minecraft:diamond_shovel",tag:{Damage:0}},{Count:1b,Slot:5b,id:"minecraft:bow",tag:{Damage:0,affix_data:{affixes:{"apotheosis:ranged/attribute/agile":0.33417398f,"apotheosis:ranged/attribute/elven":0.97314626f,"apotheosis:ranged/attribute/streamlined":0.49408966f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:ranged/attribute/streamlined"},"",{"translate":"affix.apotheosis:ranged/attribute/elven.suffix"}]}',rarity:"apotheosis:uncommon",uuids:[[I;-**********,1136083460,-1531668712,-1270021138]]},apoth_rchest:1b}},{Count:1b,Slot:7b,id:"minecraft:iron_pickaxe",tag:{Damage:2,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":0.14051425f,"apotheosis:breaker/attribute/experienced":0.24261278f,"apotheosis:breaker/attribute/lengthy":0.3698218f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/attribute/experienced"},"",{"translate":"affix.apotheosis:breaker/attribute/lengthy.suffix"}]}',rarity:"apotheosis:uncommon",uuids:[[I;-382199040,1502822566,-1384833945,1551128204]]},apoth_rchest:1b}},{Count:1b,Slot:8b,id:"railcraft:iron_spike_maul",tag:{Damage:0}},{Count:5b,Slot:12b,id:"minecraft:cooked_beef"},{Count:45b,Slot:13b,id:"minecraft:bread"},{Count:1b,Slot:14b,id:"sophisticatedbackpacks:backpack"},{Count:1b,Slot:15b,id:"sophisticatedbackpacks:backpack",tag:{contentsUuid:[I;1733112938,-1681175653,-2072223534,-157042649],inventorySlots:27,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:1}},{Count:1b,Slot:16b,id:"sophisticatedbackpacks:copper_backpack",tag:{contentsUuid:[I;108650512,-1306049842,-1338491301,-1710618992],inventorySlots:45,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:1}},{Count:1b,Slot:17b,id:"ars_nouveau:caster_tome",tag:{"ars_nouveau:caster":{current_slot:0,flavor:"Creates a temporary tunnel of blocks.",hidden_recipe:"",is_hidden:0b,spell_count:1,spells:{spell0:{name:"The Shadow's Temporary Tunnel",recipe:{part0:"ars_nouveau:glyph_touch",part1:"ars_nouveau:glyph_intangible",part2:"ars_nouveau:glyph_aoe",part3:"ars_nouveau:glyph_aoe",part4:"ars_nouveau:glyph_pierce",part5:"ars_nouveau:glyph_pierce",part6:"ars_nouveau:glyph_pierce",part7:"ars_nouveau:glyph_pierce",part8:"ars_nouveau:glyph_pierce",part9:"ars_nouveau:glyph_extend_time",size:10},sound:{pitch:1.0f,soundTag:{id:"ars_nouveau:fire_family"},volume:1.0f},spellColor:{b:180,g:25,r:255,type:"ars_nouveau:constant"}}}},display:{Name:'{"italic":true,"color":"dark_purple","text":"The Shadow\'s Temporary Tunnel"}'}}},{Count:1b,Slot:18b,id:"simplemagnets:advancedmagnet",tag:{}},{Count:1b,Slot:31b,id:"immersiveengineering:hammer",tag:{Damage:0}},{Count:1b,Slot:34b,id:"minecraft:iron_axe",tag:{Damage:98,affix_data:{affixes:{"apotheosis:durable":0.05f,"apotheosis:heavy_weapon/attribute/giant_slaying":0.2691679f,"apotheosis:heavy_weapon/attribute/nullifying":0.32890612f,"apotheosis:heavy_weapon/attribute/shredding":0.4741544f,"apotheosis:sword/mob_effect/elusive":0.5666329f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:heavy_weapon/attribute/nullifying"},"",{"translate":"affix.apotheosis:heavy_weapon/attribute/giant_slaying.suffix"}]}',rarity:"apotheosis:rare",sockets:2,uuids:[[I;1470717474,-1028504272,-1851639743,780348724]]},apoth_rchest:1b}},{Count:1b,Slot:100b,id:"minecraft:iron_boots",tag:{Damage:7,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.058074832f,"apotheosis:armor/attribute/windswept":0.9141143f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/blessed"},"",{"translate":"affix.apotheosis:armor/attribute/windswept.suffix"}]}',rarity:"apotheosis:uncommon",sockets:1,uuids:[[I;-963693922,-1776663042,-1653785592,1076950278]]},apoth_rchest:1b}},{Count:1b,Slot:101b,id:"minecraft:iron_leggings",tag:{Damage:7,affix_data:{affixes:{"irons_spellbooks:armor/attribute/mana":0.33041567f},name:'{"color":"#808080","translate":"misc.apotheosis.affix_name.two","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",""]}',rarity:"apotheosis:common",uuids:[[I;2119703976,-293453252,-1973081334,-1073326317]]},apoth_rchest:1b}},{Count:1b,Slot:102b,id:"minecraft:iron_chestplate",tag:{Damage:5,affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.631411f,"apotheosis:armor/attribute/steel_touched":0.59196067f,"apotheosis:armor/dmg_reduction/blast_forged":0.11222726f,"apotheosis:durable":0.07f,"irons_spellbooks:armor/attribute/mana":0.59923124f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",{"translate":"affix.apotheosis:armor/attribute/steel_touched.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;1538615368,-1778694774,-1544566921,1460440267]]},apoth_rchest:1b}},{Count:1b,Slot:103b,id:"minecraft:turtle_helmet",tag:{Damage:7,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.5099083f,"apotheosis:armor/attribute/stalwart":0.7456047f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/stalwart"},"",{"translate":"affix.apotheosis:armor/attribute/blessed.suffix"}]}',rarity:"apotheosis:uncommon",uuids:[[I;1228033423,-1117042903,-1453230919,-520663938]]},apoth_rchest:1b}},{Count:31b,Slot:-106b,id:"minecraft:torch"}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-10066.603167601586d,-17.0d,-21103.514252547382d],Railways_DataVersion:2,Rotation:[-107.824066f,55.787678f],Score:1408,SelectedItemSlot:3,SleepTimer:0s,SpawnAngle:-90.40112f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:-10207,SpawnY:72,SpawnZ:-21070,Spigot.ticksLived:27617,UUID:[I;199576148,-981777488,-1156706306,795428609],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-2766921097736209L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:30,XpP:0.86019194f,XpSeed:-78911929,XpTotal:1408,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752937030289L,keepLevel:0b,lastKnownName:"TheYuXi",lastPlayed:1752988740187L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.08764839f,foodLevel:17,foodSaturationLevel:3.8000002f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwfurnitures:stripped_spruce_bookshelf","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","botania:ender_hand","littlelogistics:seater_barge","handcrafted:spider_trophy","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","mcwfurnitures:spruce_modern_desk","mcwfurnitures:spruce_drawer","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","supplementaries:candle_holders/candle_holder_green","aether:skyroot_fletching_table","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","dyenamics:banner/icy_blue_banner","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:bubble_blower","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:sandstone_slab_from_sandstone_stonecutting","productivebees:stonecutter/fir_canvas_expansion_box","mcwbiomesoplenty:mahogany_plank_pane_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwtrpdoors:spruce_barred_trapdoor","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","dyenamics:aquamarine_concrete_powder","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","productivebees:hives/advanced_cherry_canvas_hive","mcwlights:chain_lantern","securitycraft:camera_monitor","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:oak_kitchen_counter","minecraft:fire_charge","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","cfm:stripped_spruce_bedside_cabinet","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","biomesoplenty:palm_chest_boat","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:stripped_dead_log_window2","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","mcwpaths:sandstone_running_bond_stairs","mcwdoors:spruce_four_panel_door","domum_ornamentum:blue_brick_extra","mcwbiomesoplenty:mahogany_window","cfm:stripped_spruce_chair","simplylight:illuminant_light_blue_block_toggle","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","productivebees:expansion_boxes/expansion_box_acacia_canvas","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwbridges:spruce_bridge_pier","productivebees:stonecutter/oak_canvas_hive","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","securitycraft:whitelist_module","cfm:spruce_bedside_cabinet","mcwbiomesoplenty:pine_plank_window2","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","xnet:connector_routing","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","mcwpaths:red_sandstone_diamond_paving","productivebees:stonecutter/palm_canvas_expansion_box","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","utilitix:directional_highspeed_rail","handcrafted:red_sandstone_pillar_trim","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","securitycraft:reinforced_purple_stained_glass","mcwfurnitures:stripped_spruce_double_drawer","mcwpaths:oak_planks_path","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","cfm:green_grill","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","cfm:orange_grill","minecraft:cut_red_sandstone_from_red_sandstone_stonecutting","mcwwindows:dark_oak_shutter","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:mangrove_chest_boat","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","mcwfences:nether_brick_pillar_wall","mcwroofs:thatch_roof","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","minecraft:jungle_trapdoor","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","mcwbiomesoplenty:maple_curved_gate","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","cfm:green_kitchen_sink","pneumaticcraft:refinery","pneumaticcraft:thermal_compressor","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","productivebees:hives/advanced_oak_canvas_hive","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","botania:tiny_planet_block","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","mcwlights:golden_chandelier","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwwindows:birch_plank_pane_window","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwroofs:spruce_top_roof","supplementaries:bomb","dyenamics:navy_stained_glass","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwpaths:sandstone_flagstone_slab","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwdoors:oak_cottage_door","minecraft:red_sandstone_slab_from_red_sandstone_stonecutting","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","cfm:pink_cooler","twilightforest:twilight_oak_chest_boat","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwroofs:red_sandstone_attic_roof","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwfurnitures:spruce_wardrobe","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwdoors:jungle_japanese2_door","minecraft:smooth_red_sandstone","securitycraft:reinforced_andesite_with_vanilla_cobblestone","pneumaticcraft:wall_lamp_pink","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwfurnitures:spruce_stool_chair","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","mcwfurnitures:oak_double_wardrobe","mcwdoors:spruce_stable_head_door","minecraft:iron_nugget_from_blasting","productivebees:stonecutter/aspen_canvas_expansion_box","pneumaticcraft:liquid_hopper","twilightforest:mining_boat","minecraft:cut_red_sandstone_slab_from_red_sandstone_stonecutting","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","xnet:advanced_connector_yellow","cfm:stripped_oak_chair","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","immersiveengineering:crafting/axe_steel","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","productivebees:stonecutter/jungle_canvas_hive","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_base_green","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","securitycraft:reinforced_spruce_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:cut_red_sandstone_slab_from_cut_red_sandstone_stonecutting","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","handcrafted:stackable_book","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwtrpdoors:spruce_mystic_trapdoor","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","supplementaries:candle_holders/candle_holder_red","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwbiomesoplenty:fir_horse_fence","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","productivebees:stonecutter/oak_canvas_expansion_box","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","utilitarian:utility/spruce_logs_to_pressure_plates","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","productivebees:expansion_boxes/expansion_box_crimson_canvas","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","mcwfurnitures:jungle_stool_chair","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","mcwroofs:brown_concrete_roof","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwfences:bamboo_highley_gate","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","mcwroofs:jungle_roof","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","supplementaries:daub_frame","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwwindows:oak_plank_four_window","mcwroofs:sandstone_lower_roof","handcrafted:jungle_dining_bench","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","aether:golden_pendant","mcwpaths:sandstone_basket_weave_paving","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_1","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","mcwfences:mangrove_curved_gate","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","immersiveengineering:crafting/cokebrick_to_slab","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwfurnitures:jungle_striped_chair","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","additionallanterns:diamond_lantern","railcraft:steel_shears","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","itemcollectors:basic_collector","minecraft:loom","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","mcwroofs:light_gray_concrete_steep_roof","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mcwroofs:sandstone_attic_roof","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","minecraft:red_sandstone_stairs","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","utilitarian:utility/spruce_logs_to_stairs","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","reliquary:uncrafting/slime_ball","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","cfm:brown_cooler","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","productivebees:hives/advanced_snake_block_canvas_hive","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwroofs:red_sandstone_lower_roof","paraglider:paraglider","utilitix:anvil_cart","mcwfurnitures:spruce_desk","pneumaticcraft:wall_lamp_red","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwtrpdoors:spruce_swamp_trapdoor","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","minecraft:jukebox","mcwwindows:jungle_curtain_rod","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","minecraft:gold_ingot_from_gold_block","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","mcwroofs:lime_concrete_lower_roof","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","mcwfurnitures:stripped_spruce_double_drawer_counter","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","mcwfurnitures:stripped_spruce_stool_chair","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwwindows:blackstone_pane_window","mcwdoors:jungle_whispering_door","mcwlights:bamboo_tiki_torch","mcwpaths:red_sandstone_crystal_floor","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:stripped_redwood_log_window","productivebees:stonecutter/maple_canvas_expansion_box","cfm:jungle_blinds","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","sophisticatedstorage:decoration_table","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","immersiveengineering:crafting/glider","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","mcwfences:warped_curved_gate","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","cfm:spruce_kitchen_counter","cfm:oak_upgraded_gate","minecraft:copper_ingot_from_blasting_raw_copper","minecraft:cut_red_sandstone_slab","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","mcwdoors:spruce_nether_door","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","mcwfurnitures:oak_bookshelf","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","supplementaries:notice_board","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:flower_pot","minecraft:diamond_pickaxe","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","productivebees:hives/advanced_jungle_canvas_hive","pneumaticcraft:reinforced_brick_wall","mcwtrpdoors:spruce_ranch_trapdoor","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","pneumaticcraft:wall_lamp_inverted_blue","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","mcwwindows:stripped_oak_log_window","minecraft:golden_boots","securitycraft:harming_module","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:smooth_red_sandstone_slab_from_smooth_red_sandstone_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","alltheores:bronze_plate","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","pneumaticcraft:pressure_chamber_wall","minecraft:glass","allthecompressed:compress/gold_block_1x","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","mcwdoors:spruce_paper_door","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:light_blue_concrete_powder","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","mcwwindows:light_blue_mosaic_glass","silentgear:fine_silk","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","deepresonance:radiation_monitor","railcraft:steel_gear","aether:diamond_shovel_repairing","sophisticatedstorage:crafting_upgrade","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","mcwroofs:green_concrete_top_roof","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","mcwbiomesoplenty:dead_plank_window2","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","immersiveengineering:crafting/conveyor_splitter","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","pneumaticcraft:wall_lamp_light_gray","mcwfurnitures:stripped_spruce_triple_drawer","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","alltheores:bronze_rod","mcwroofs:light_gray_concrete_lower_roof","immersiveengineering:crafting/stick_iron","productivebees:expansion_boxes/expansion_box_cherry_canvas","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","handcrafted:golden_medium_pot","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","aquaculture:heavy_hook","mcwroofs:red_sandstone_steep_roof","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","mcwwindows:acacia_window","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","mcwroofs:orange_concrete_lower_roof","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","productivebees:hives/advanced_birch_canvas_hive","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","mcwroofs:blue_concrete_attic_roof","mcwpaths:red_sandstone_running_bond_stairs","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","aether:iron_boots_repairing","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwdoors:spruce_modern_door","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","minecraft:gray_stained_glass","cfm:magenta_trampoline","utilitarian:utility/spruce_logs_to_boats","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","productivebees:stonecutter/concrete_canvas_hive","mcwpaths:red_sandstone_honeycomb_paving","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","mcwtrpdoors:spruce_tropical_trapdoor","securitycraft:reinforced_gray_stained_glass","mcwtrpdoors:print_cottage","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_2","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:gutter_base_pink","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","cfm:stripped_spruce_table","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","croptopia:flour","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","domum_ornamentum:cactus_extra","cfm:cyan_cooler","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwpaths:red_sandstone_crystal_floor_stairs","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","immersiveengineering:crafting/conveyor_extract","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","travelersbackpack:redstone","immersiveengineering:crafting/shovel_steel","mcwwindows:iron_shutter","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","minecraft:note_block","mcwpaths:sandstone_flagstone","immersiveengineering:crafting/sawblade","mcwwindows:brown_mosaic_glass_pane","mcwpaths:stone_windmill_weave_stairs","railcraft:steel_shovel","mcwfurnitures:stripped_spruce_large_drawer","minecraft:smooth_sandstone","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","productivebees:stonecutter/cherry_canvas_hive","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","minecraft:spruce_planks","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwfurnitures:spruce_triple_drawer","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","mcwwindows:dark_oak_log_parapet","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","mcwfurnitures:spruce_large_drawer","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","securitycraft:reinforced_warped_fence_gate","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","mcwwindows:spruce_plank_four_window","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","pneumaticcraft:thermostat_module","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwwindows:green_mosaic_glass_pane","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","railcraft:animal_detector","alltheores:diamond_rod","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","reliquary:infernal_claw","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwroofs:spruce_upper_lower_roof","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwlights:white_paper_lamp","cfm:spruce_chair","minecraft:oak_trapdoor","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwtrpdoors:cherry_ranch_trapdoor","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","immersiveengineering:crafting/sorter","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","minecraft:lapis_lazuli","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","minecraft:coal","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","aether:iron_axe_repairing","supplementaries:sack","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","pneumaticcraft:wall_lamp_brown","cfm:stripped_oak_mail_box","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","productivebees:hives/advanced_bamboo_canvas_hive","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","cfm:lime_cooler","minecraft:red_sandstone_slab","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","minecraft:smooth_red_sandstone_stairs_from_smooth_red_sandstone_stonecutting","utilitarian:utility/spruce_logs_to_doors","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","mcwpaths:red_sandstone_flagstone_path","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","deepresonance:machine_frame","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","xnet:netcable_red_dye","mcwfurnitures:spruce_modern_chair","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","alltheores:gold_rod","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","biomesoplenty:magic_boat","minecraft:chiseled_red_sandstone","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","productivebees:expansion_boxes/expansion_box_bamboo_canvas","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","mcwfurnitures:stripped_oak_table","rftoolspower:power_core1","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:magic_window2","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","cfm:spruce_blinds","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","mcwdoors:spruce_tropical_door","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","mcwtrpdoors:jungle_mystic_trapdoor","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","pneumaticcraft:wall_lamp_inverted_green","mcwfurnitures:spruce_chair","productivebees:expansion_boxes/expansion_box_spruce_canvas","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","supplementaries:slingshot","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","productivebees:expansion_boxes/expansion_box_mangrove_canvas","everythingcopper:copper_rail","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aquaculture:neptunium_ingot_from_nuggets","minecraft:iron_pickaxe","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","deepresonance:resonating_plate","mcwfurnitures:stripped_spruce_table","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","minecraft:birch_boat","mcwfences:spruce_highley_gate","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","mcwdoors:jungle_barn_door","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","cfm:orange_cooler","minecraft:lime_stained_glass","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwbiomesoplenty:stripped_umbran_log_four_window","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwpaths:stone_crystal_floor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","chimes:copper_chimes","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwtrpdoors:mangrove_bark_trapdoor","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","cfm:spatula","pneumaticcraft:charging_upgrade","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwroofs:purple_concrete_attic_roof","xnet:connector_red_dye","minecolonies:shapetool","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","additionallanterns:red_sandstone_chain","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:bamboo_chest_raft","reliquary:mob_charm_fragments/slime","cfm:stripped_spruce_desk","productivebees:stonecutter/willow_canvas_expansion_box","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:spruce_roof","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","mcwbiomesoplenty:stripped_fir_log_four_window","immersiveengineering:crafting/survey_tools","create:crafting/logistics/powered_latch","productivebees:stonecutter/warped_canvas_expansion_box","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","sophisticatedstorage:stonecutter_upgrade","mcwfences:vintage_metal_fence","aquaculture:double_hook","handcrafted:oak_bench","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","dyenamics:spring_green_concrete_powder","cfm:jungle_cabinet","minecraft:iron_leggings","mcwlights:tavern_lantern","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","productivebees:hives/advanced_mangrove_canvas_hive","dyenamics:bed/navy_bed","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","ironfurnaces:furnaces/iron_furnace","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","railcraft:world_spike","mcwdoors:garage_gray_door","mcwfurnitures:stripped_oak_counter","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","minecraft:oak_pressure_plate","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwwindows:quartz_window","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","reliquary:mob_charm_fragments/creeper","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:clock","mcwroofs:red_concrete_top_roof","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","twilightforest:transformation_chest_boat","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","dyenamics:bed/conifer_bed","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwtrpdoors:spruce_whispering_trapdoor","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","cfm:stripped_spruce_crate","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","mcwwindows:andesite_four_window","domum_ornamentum:light_blue_brick_extra","mcwlights:golden_low_candle_holder","mcwpaths:red_sandstone_crystal_floor_path","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","mcwlights:festive_lantern","securitycraft:block_change_detector","twigs:rocky_dirt","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","mcwpaths:red_sandstone_flagstone_slab","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","pneumaticcraft:elevator_frame","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","pneumaticcraft:wall_lamp_cyan","create:crafting/kinetics/fluid_pipe","pneumaticcraft:pressure_chamber_glass_x4","mcwpaths:red_sand_path_block","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","undergarden:gloom_o_lantern","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","twigs:oak_table","pneumaticcraft:heat_pipe","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","mcwfurnitures:oak_lower_triple_drawer","mcwpaths:red_sandstone_flagstone","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","mcwdoors:jungle_japanese_door","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","cfm:purple_kitchen_drawer","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","mcwroofs:red_sandstone_top_roof","farmersdelight:diamond_knife","minecraft:oak_sign","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","minecraft:red_sandstone_stairs_from_red_sandstone_stonecutting","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:stripped_spruce_counter","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","mcwroofs:spruce_lower_roof","mcwwindows:cyan_mosaic_glass_pane","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","mcwdoors:spruce_whispering_door","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","minecraft:purple_concrete_powder","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","immersiveengineering:crafting/light_engineering","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwpaths:red_sandstone_square_paving","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","mcwroofs:oak_planks_attic_roof","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","railcraft:radio_circuit","minecraft:smooth_red_sandstone_stairs","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","mcwtrpdoors:spruce_classic_trapdoor","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwroofs:red_sandstone_upper_lower_roof","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","handcrafted:red_sandstone_corner_trim","mcwroofs:lime_concrete_attic_roof","pneumaticcraft:manometer","securitycraft:alarm","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","pneumaticcraft:wall_lamp_inverted_magenta","mcwroofs:oak_top_roof","mcwpaths:sandstone_honeycomb_paving","additionallanterns:amethyst_lantern","utilitix:crude_furnace","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwwindows:stone_window","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","productivebees:stonecutter/yucca_canvas_hive","sophisticatedstorage:jukebox_upgrade","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","productivebees:expansion_boxes/expansion_box_warped_canvas","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwdoors:oak_japanese2_door","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","mcwpaths:red_sandstone_strewn_rocky_path","utilitarian:utility/oak_logs_to_slabs","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","productivebees:stonecutter/yucca_canvas_expansion_box","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","immersiveengineering:crafting/conveyor_redstone","mcwbiomesoplenty:magic_pyramid_gate","mcwfurnitures:spruce_table","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","minecraft:red_sandstone_wall","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","minecraft:iron_nugget_from_smelting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","xnet:advanced_connector_blue","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwroofs:orange_concrete_upper_lower_roof","mcwtrpdoors:spruce_barn_trapdoor","mcwfurnitures:spruce_modern_wardrobe","productivebees:stonecutter/hellbark_canvas_hive","mcwwindows:prismarine_window","securitycraft:laser_block","mcwpaths:red_sandstone_flagstone_stairs","mcwfences:modern_andesite_wall","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","productivebees:stonecutter/crimson_canvas_expansion_box","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","cfm:lime_kitchen_drawer","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:spruce_drawer_counter","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","deeperdarker:echo_chest_boat","eccentrictome:tome","securitycraft:reinforced_warped_fence","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","sophisticatedstorage:chipped/tinkering_table_upgrade","mcwpaths:red_sandstone_crystal_floor_slab","buildinggadgets2:gadget_copy_paste","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwfurnitures:spruce_double_drawer","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwfurnitures:stripped_spruce_drawer_counter","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","mcwfurnitures:stripped_spruce_modern_wardrobe","productivebees:expansion_boxes/expansion_box_oak_canvas","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","mcwdoors:jungle_paper_door","mcwpaths:red_sandstone_clover_paving","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:red_sandstone_running_bond_path","immersiveengineering:crafting/conveyor_vertical","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","minecraft:yellow_stained_glass","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:wheat_dough_from_eggs","cfm:dark_oak_kitchen_sink_light","mcwbiomesoplenty:willow_pane_window","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","immersiveengineering:crafting/strip_curtain","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","mcwfurnitures:spruce_counter","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","dyenamics:amber_stained_glass","securitycraft:blacklist_module","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","productivebees:hives/advanced_acacia_canvas_hive","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","immersiveengineering:crafting/item_batcher","sophisticatedstorage:chipped/botanist_workbench_upgrade","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mcwbiomesoplenty:magic_plank_window2","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","domum_ornamentum:green_cactus_extra","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","pneumaticcraft:air_canister","mcwwindows:prismarine_pane_window","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:slice_map","mcwpaths:red_sandstone_windmill_weave_stairs","productivebees:expansion_boxes/expansion_box_jungle_canvas","appflux:insulating_resin","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","cfm:stripped_spruce_desk_cabinet","mcwfences:jungle_wired_fence","pneumaticcraft:kerosene_lamp","mcwfences:end_brick_pillar_wall","cfm:gray_grill","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","pneumaticcraft:wall_lamp_inverted_cyan","mcwroofs:spruce_steep_roof","mcwfurnitures:jungle_modern_chair","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","railcraft:steel_helmet","mcwroofs:red_sandstone_roof","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","cfm:stripped_spruce_mail_box","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","immersiveengineering:crafting/windmill_sail","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","minecraft:chiseled_red_sandstone_from_red_sandstone_stonecutting","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","mcwfurnitures:stripped_spruce_bookshelf_drawer","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","forbidden_arcanus:processed_obsidian_block_from_obsidian_ingot","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","pneumaticcraft:pressure_chamber_valve_x1","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","minecraft:spruce_wood","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","botania:runic_altar","domum_ornamentum:purple_brick_extra","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","minecraft:smooth_red_sandstone_slab","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","productivebees:stonecutter/maple_canvas_hive","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","handcrafted:jungle_table","supplementaries:planter_rich","botania:abstruse_platform","mcwfences:crimson_stockade_fence","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","productivebees:expansion_boxes/expansion_box_birch_canvas","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwroofs:magenta_concrete_upper_lower_roof","immersiveengineering:crafting/cushion","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","mcwwindows:white_mosaic_glass_pane","cfm:stripped_spruce_coffee_table","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:fishing_rod","xnet:connector_yellow_dye","mcwwindows:acacia_blinds","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","minecraft:cut_red_sandstone","blue_skies:lunar_bookshelf","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","productivebees:stonecutter/redwood_canvas_hive","mcwlights:double_street_lamp","minecraft:tnt","minecolonies:chainmailboots","mcwdoors:spruce_stable_door","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","cfm:spruce_upgraded_gate","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwfurnitures:spruce_cupboard_counter","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","ae2:network/cells/item_cell_housing","minecraft:cobblestone_wall_from_cobblestone_stonecutting","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","minecraft:brown_concrete_powder","productivebees:expansion_boxes/expansion_box_snake_block_canvas","securitycraft:reinforced_lime_stained_glass_pane_from_dye","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","domum_ornamentum:brown_brick_extra","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","securitycraft:protecto","immersiveengineering:crafting/conveyor_basic_covered","utilitix:jungle_shulker_boat","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_spruce_cupboard_counter","cfm:orange_kitchen_drawer","silentgear:stone_rod","railcraft:tank_detector","handcrafted:sandstone_corner_trim","immersiveengineering:crafting/balloon","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","mcwlights:pink_paper_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","mcwpaths:red_sandstone_running_bond_slab","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","aether:iron_gloves","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","mcwbiomesoplenty:mahogany_highley_gate","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","aether:book_of_lore","minecraft:brush","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","sfm:cable","minecraft:hay_block","mcwroofs:deepslate_upper_steep_roof","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","pneumaticcraft:compressed_iron_boots","mcwwindows:red_sandstone_window2","botania:phantom_ink","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","domum_ornamentum:brick_extra","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/generator","immersiveengineering:crafting/cokebrick_from_slab","mcwwindows:warped_stem_four_window","securitycraft:sentry","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwwindows:blue_mosaic_glass_pane","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","silentgear:fine_silk_cloth","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","utilitarian:utility/spruce_logs_to_slabs","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwpaths:red_sandstone_windmill_weave_slab","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","domum_ornamentum:white_brick_extra","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwroofs:red_sandstone_upper_steep_roof","domum_ornamentum:pink_floating_carpet","securitycraft:reinforced_andesite","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","handcrafted:oven","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","productivebees:stonecutter/comb_canvas_hive","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","minecraft:red_sandstone_wall_from_red_sandstone_stonecutting","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","silentgear:upgrade_base","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","handcrafted:pufferfish_trophy","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","minecraft:oak_chest_boat","mcwdoors:spruce_waffle_door","simplylight:illuminant_purple_block_on_toggle","mcwpaths:red_sandstone_windmill_weave","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","pneumaticcraft:logistics_core","everythingcopper:copper_helmet","mcwroofs:red_concrete_upper_lower_roof","modularrouters:blank_upgrade","mcwdoors:spruce_swamp_door","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","cfm:stripped_spruce_cabinet","minecraft:mangrove_boat","minecraft:bread","mcwdoors:spruce_barn_glass_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","mcwdoors:garage_silver_door","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwfurnitures:spruce_coffee_table","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","minecraft:iron_shovel","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","mcwroofs:jungle_planks_lower_roof","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","pneumaticcraft:camo_applicator","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","supplementaries:gold_trapdoor","cfm:fridge_light","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","mcwfurnitures:stripped_spruce_covered_desk","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbridges:red_sandstone_bridge_pier","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","mcwpaths:red_sandstone_dumble_paving","mcwbiomesoplenty:magic_highley_gate","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","mcwbridges:balustrade_orange_sandstone_bridge","mcwfences:oak_curved_gate","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","supplementaries:wrench","deepresonance:radiation_suit_boots","minecraft:bow","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","cfm:purple_kitchen_sink","mcwpaths:red_sandstone_basket_weave_paving","cfm:black_cooler","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:spruce_glass_table","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwfurnitures:spruce_covered_desk","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","mcwwindows:stripped_spruce_log_four_window","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","mcwdoors:spruce_mystic_door","mcwtrpdoors:spruce_beach_trapdoor","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","travelersbackpack:blaze","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","mcwwindows:deepslate_pane_window","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwpaths:sandstone_flagstone_path","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwdoors:jungle_tropical_door","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","crafting_on_a_stick:crafting_table","mcwdoors:oak_barn_door","botania:alchemy_catalyst","mcwfurnitures:spruce_double_drawer_counter","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","immersiveengineering:crafting/ersatz_leather","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","mcwfurnitures:stripped_spruce_coffee_table","minecraft:ender_eye","mcwfurnitures:spruce_double_wardrobe","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","productivebees:stonecutter/mangrove_canvas_expansion_box","railcraft:receiver_circuit","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","immersiveengineering:crafting/conveyor_dropper","sophisticatedstorage:feeding_upgrade","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","additionallanterns:diamond_chain","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","mcwroofs:jungle_lower_roof","mcwbridges:orange_sandstone_bridge","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:age_detector","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","productivebees:hives/advanced_dark_oak_canvas_hive","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwwindows:birch_plank_parapet","mcwpaths:red_sandstone_running_bond","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","domum_ornamentum:black_brick_extra","railcraft:steel_chestplate","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwdoors:spruce_japanese2_door","enderio:fluid_tank","mcwroofs:oak_planks_roof","ironfurnaces:furnaces/gold_furnace","bigreactors:energizer/casing","mcwpaths:stone_flagstone","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","botania:mana_tablet","cfm:birch_mail_box","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwpaths:red_sandstone_windmill_weave_path","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:logistics_configurator","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"],toBeDisplayed:["mcwfurnitures:stripped_spruce_bookshelf","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","botania:ender_hand","littlelogistics:seater_barge","handcrafted:spider_trophy","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","mcwfurnitures:spruce_modern_desk","mcwfurnitures:spruce_drawer","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","supplementaries:candle_holders/candle_holder_green","aether:skyroot_fletching_table","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","dyenamics:banner/icy_blue_banner","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:bubble_blower","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:sandstone_slab_from_sandstone_stonecutting","productivebees:stonecutter/fir_canvas_expansion_box","mcwbiomesoplenty:mahogany_plank_pane_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwtrpdoors:spruce_barred_trapdoor","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","dyenamics:aquamarine_concrete_powder","nethersdelight:golden_machete","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","productivebees:hives/advanced_cherry_canvas_hive","mcwlights:chain_lantern","securitycraft:camera_monitor","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:oak_kitchen_counter","minecraft:fire_charge","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","cfm:stripped_spruce_bedside_cabinet","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","biomesoplenty:palm_chest_boat","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:stripped_dead_log_window2","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","mcwpaths:sandstone_running_bond_stairs","mcwdoors:spruce_four_panel_door","domum_ornamentum:blue_brick_extra","mcwbiomesoplenty:mahogany_window","cfm:stripped_spruce_chair","simplylight:illuminant_light_blue_block_toggle","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","productivebees:expansion_boxes/expansion_box_acacia_canvas","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwbridges:spruce_bridge_pier","productivebees:stonecutter/oak_canvas_hive","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","securitycraft:whitelist_module","cfm:spruce_bedside_cabinet","mcwbiomesoplenty:pine_plank_window2","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","mcwwindows:warped_louvered_shutter","xnet:connector_routing","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","mcwpaths:red_sandstone_diamond_paving","productivebees:stonecutter/palm_canvas_expansion_box","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","utilitix:directional_highspeed_rail","handcrafted:red_sandstone_pillar_trim","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","securitycraft:reinforced_purple_stained_glass","mcwfurnitures:stripped_spruce_double_drawer","mcwpaths:oak_planks_path","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","cfm:green_grill","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","cfm:orange_grill","minecraft:cut_red_sandstone_from_red_sandstone_stonecutting","mcwwindows:dark_oak_shutter","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:mangrove_chest_boat","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","mcwfences:nether_brick_pillar_wall","mcwroofs:thatch_roof","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","minecraft:jungle_trapdoor","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","mcwbiomesoplenty:maple_curved_gate","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","cfm:green_kitchen_sink","pneumaticcraft:refinery","pneumaticcraft:thermal_compressor","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","productivebees:hives/advanced_oak_canvas_hive","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","botania:tiny_planet_block","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","mcwlights:golden_chandelier","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwwindows:birch_plank_pane_window","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwroofs:spruce_top_roof","supplementaries:bomb","dyenamics:navy_stained_glass","mcwroofs:gutter_middle_pink","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwpaths:sandstone_flagstone_slab","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwdoors:oak_cottage_door","minecraft:red_sandstone_slab_from_red_sandstone_stonecutting","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","cfm:pink_cooler","twilightforest:twilight_oak_chest_boat","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwroofs:red_sandstone_attic_roof","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwfurnitures:spruce_wardrobe","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","connectedglass:borderless_glass1","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwdoors:jungle_japanese2_door","minecraft:smooth_red_sandstone","securitycraft:reinforced_andesite_with_vanilla_cobblestone","pneumaticcraft:wall_lamp_pink","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwfurnitures:spruce_stool_chair","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","mcwfurnitures:oak_double_wardrobe","mcwdoors:spruce_stable_head_door","minecraft:iron_nugget_from_blasting","productivebees:stonecutter/aspen_canvas_expansion_box","pneumaticcraft:liquid_hopper","twilightforest:mining_boat","minecraft:cut_red_sandstone_slab_from_red_sandstone_stonecutting","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","xnet:advanced_connector_yellow","cfm:stripped_oak_chair","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","immersiveengineering:crafting/axe_steel","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","productivebees:stonecutter/jungle_canvas_hive","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_base_green","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","securitycraft:reinforced_spruce_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:cut_red_sandstone_slab_from_cut_red_sandstone_stonecutting","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","handcrafted:stackable_book","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwtrpdoors:spruce_mystic_trapdoor","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","supplementaries:candle_holders/candle_holder_red","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwbiomesoplenty:fir_horse_fence","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","productivebees:stonecutter/oak_canvas_expansion_box","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","utilitarian:utility/spruce_logs_to_pressure_plates","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","productivebees:expansion_boxes/expansion_box_crimson_canvas","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","mcwfurnitures:jungle_stool_chair","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","mcwroofs:brown_concrete_roof","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwfences:bamboo_highley_gate","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","mcwroofs:jungle_roof","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","supplementaries:daub_frame","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwwindows:oak_plank_four_window","mcwroofs:sandstone_lower_roof","handcrafted:jungle_dining_bench","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","aether:golden_pendant","mcwpaths:sandstone_basket_weave_paving","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_1","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","mcwfences:mangrove_curved_gate","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","immersiveengineering:crafting/cokebrick_to_slab","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwfurnitures:jungle_striped_chair","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","additionallanterns:diamond_lantern","railcraft:steel_shears","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","itemcollectors:basic_collector","minecraft:loom","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","mcwroofs:light_gray_concrete_steep_roof","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mcwroofs:sandstone_attic_roof","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","minecraft:red_sandstone_stairs","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","utilitarian:utility/spruce_logs_to_stairs","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","reliquary:uncrafting/slime_ball","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","cfm:brown_cooler","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","productivebees:hives/advanced_snake_block_canvas_hive","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwroofs:red_sandstone_lower_roof","paraglider:paraglider","utilitix:anvil_cart","mcwfurnitures:spruce_desk","pneumaticcraft:wall_lamp_red","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwtrpdoors:spruce_swamp_trapdoor","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","minecraft:jukebox","mcwwindows:jungle_curtain_rod","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","minecraft:gold_ingot_from_gold_block","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","mcwroofs:lime_concrete_lower_roof","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","mcwfurnitures:stripped_spruce_double_drawer_counter","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","mcwfurnitures:stripped_spruce_stool_chair","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwwindows:blackstone_pane_window","mcwdoors:jungle_whispering_door","mcwlights:bamboo_tiki_torch","mcwpaths:red_sandstone_crystal_floor","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:stripped_redwood_log_window","productivebees:stonecutter/maple_canvas_expansion_box","cfm:jungle_blinds","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","sophisticatedstorage:decoration_table","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","immersiveengineering:crafting/glider","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","mcwfences:warped_curved_gate","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","cfm:spruce_kitchen_counter","cfm:oak_upgraded_gate","minecraft:copper_ingot_from_blasting_raw_copper","minecraft:cut_red_sandstone_slab","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","mcwdoors:spruce_nether_door","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","mcwfurnitures:oak_bookshelf","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","supplementaries:notice_board","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:flower_pot","minecraft:diamond_pickaxe","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","productivebees:hives/advanced_jungle_canvas_hive","pneumaticcraft:reinforced_brick_wall","mcwtrpdoors:spruce_ranch_trapdoor","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","pneumaticcraft:wall_lamp_inverted_blue","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","mcwwindows:stripped_oak_log_window","minecraft:golden_boots","securitycraft:harming_module","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:smooth_red_sandstone_slab_from_smooth_red_sandstone_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","alltheores:bronze_plate","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","pneumaticcraft:pressure_chamber_wall","minecraft:glass","allthecompressed:compress/gold_block_1x","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","mcwdoors:spruce_paper_door","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:light_blue_concrete_powder","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","mcwwindows:light_blue_mosaic_glass","silentgear:fine_silk","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","deepresonance:radiation_monitor","railcraft:steel_gear","aether:diamond_shovel_repairing","sophisticatedstorage:crafting_upgrade","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","mcwroofs:green_concrete_top_roof","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","mcwbiomesoplenty:dead_plank_window2","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","immersiveengineering:crafting/conveyor_splitter","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","pneumaticcraft:wall_lamp_light_gray","mcwfurnitures:stripped_spruce_triple_drawer","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","alltheores:bronze_rod","mcwroofs:light_gray_concrete_lower_roof","immersiveengineering:crafting/stick_iron","productivebees:expansion_boxes/expansion_box_cherry_canvas","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","handcrafted:golden_medium_pot","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","sfm:fancy_to_cable","aquaculture:heavy_hook","mcwroofs:red_sandstone_steep_roof","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","mcwwindows:acacia_window","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","mcwroofs:orange_concrete_lower_roof","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","productivebees:hives/advanced_birch_canvas_hive","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","mcwroofs:blue_concrete_attic_roof","mcwpaths:red_sandstone_running_bond_stairs","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","aether:iron_boots_repairing","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwdoors:spruce_modern_door","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","minecraft:gray_stained_glass","cfm:magenta_trampoline","utilitarian:utility/spruce_logs_to_boats","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","productivebees:stonecutter/concrete_canvas_hive","mcwpaths:red_sandstone_honeycomb_paving","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","mcwtrpdoors:spruce_tropical_trapdoor","securitycraft:reinforced_gray_stained_glass","mcwtrpdoors:print_cottage","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_2","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:gutter_base_pink","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","cfm:stripped_spruce_table","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","croptopia:flour","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","domum_ornamentum:cactus_extra","cfm:cyan_cooler","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","mcwpaths:red_sandstone_crystal_floor_stairs","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","immersiveengineering:crafting/conveyor_extract","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","travelersbackpack:redstone","immersiveengineering:crafting/shovel_steel","mcwwindows:iron_shutter","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","minecraft:note_block","mcwpaths:sandstone_flagstone","immersiveengineering:crafting/sawblade","mcwwindows:brown_mosaic_glass_pane","mcwpaths:stone_windmill_weave_stairs","railcraft:steel_shovel","mcwfurnitures:stripped_spruce_large_drawer","minecraft:smooth_sandstone","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","productivebees:stonecutter/cherry_canvas_hive","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","minecraft:spruce_planks","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwfurnitures:spruce_triple_drawer","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","mcwwindows:dark_oak_log_parapet","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","mcwfurnitures:spruce_large_drawer","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","securitycraft:reinforced_warped_fence_gate","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","mcwwindows:spruce_plank_four_window","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","pneumaticcraft:thermostat_module","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwwindows:green_mosaic_glass_pane","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","railcraft:animal_detector","alltheores:diamond_rod","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","reliquary:infernal_claw","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwroofs:spruce_upper_lower_roof","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","dyenamics:ultramarine_stained_glass","terralith:dropper_alt","mcwlights:white_paper_lamp","cfm:spruce_chair","minecraft:oak_trapdoor","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwtrpdoors:cherry_ranch_trapdoor","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","immersiveengineering:crafting/sorter","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","minecraft:lapis_lazuli","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","twigs:bone_meal_from_seashells","mcwroofs:lime_concrete_top_roof","minecraft:coal","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","aether:iron_axe_repairing","supplementaries:sack","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","pneumaticcraft:wall_lamp_brown","cfm:stripped_oak_mail_box","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","productivebees:hives/advanced_bamboo_canvas_hive","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","cfm:lime_cooler","minecraft:red_sandstone_slab","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","minecraft:smooth_red_sandstone_stairs_from_smooth_red_sandstone_stonecutting","utilitarian:utility/spruce_logs_to_doors","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","mcwpaths:red_sandstone_flagstone_path","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","deepresonance:machine_frame","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","xnet:netcable_red_dye","mcwfurnitures:spruce_modern_chair","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","alltheores:gold_rod","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","biomesoplenty:magic_boat","minecraft:chiseled_red_sandstone","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","productivebees:expansion_boxes/expansion_box_bamboo_canvas","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","mcwfurnitures:stripped_oak_table","rftoolspower:power_core1","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:magic_window2","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","cfm:spruce_blinds","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","mcwdoors:spruce_tropical_door","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","mcwtrpdoors:jungle_mystic_trapdoor","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","pneumaticcraft:wall_lamp_inverted_green","mcwfurnitures:spruce_chair","productivebees:expansion_boxes/expansion_box_spruce_canvas","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","supplementaries:slingshot","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","productivebees:expansion_boxes/expansion_box_mangrove_canvas","everythingcopper:copper_rail","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aquaculture:neptunium_ingot_from_nuggets","minecraft:iron_pickaxe","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","deepresonance:resonating_plate","mcwfurnitures:stripped_spruce_table","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","minecraft:birch_boat","mcwfences:spruce_highley_gate","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","mcwdoors:jungle_barn_door","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","cfm:orange_cooler","minecraft:lime_stained_glass","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwbiomesoplenty:stripped_umbran_log_four_window","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwpaths:stone_crystal_floor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","chimes:copper_chimes","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwtrpdoors:mangrove_bark_trapdoor","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","cfm:spatula","pneumaticcraft:charging_upgrade","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwroofs:purple_concrete_attic_roof","xnet:connector_red_dye","minecolonies:shapetool","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","additionallanterns:red_sandstone_chain","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:bamboo_chest_raft","reliquary:mob_charm_fragments/slime","cfm:stripped_spruce_desk","productivebees:stonecutter/willow_canvas_expansion_box","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:spruce_roof","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","mcwbiomesoplenty:stripped_fir_log_four_window","immersiveengineering:crafting/survey_tools","create:crafting/logistics/powered_latch","productivebees:stonecutter/warped_canvas_expansion_box","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","sophisticatedstorage:stonecutter_upgrade","mcwfences:vintage_metal_fence","aquaculture:double_hook","handcrafted:oak_bench","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","immersiveengineering:crafting/wire_steel","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","dyenamics:spring_green_concrete_powder","cfm:jungle_cabinet","minecraft:iron_leggings","mcwlights:tavern_lantern","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","productivebees:hives/advanced_mangrove_canvas_hive","dyenamics:bed/navy_bed","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","ironfurnaces:furnaces/iron_furnace","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","railcraft:world_spike","mcwdoors:garage_gray_door","mcwfurnitures:stripped_oak_counter","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","minecraft:oak_pressure_plate","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwwindows:quartz_window","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","reliquary:mob_charm_fragments/creeper","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:clock","mcwroofs:red_concrete_top_roof","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","twilightforest:transformation_chest_boat","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","dyenamics:bed/conifer_bed","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwtrpdoors:spruce_whispering_trapdoor","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","cfm:stripped_spruce_crate","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","mcwwindows:andesite_four_window","domum_ornamentum:light_blue_brick_extra","mcwlights:golden_low_candle_holder","mcwpaths:red_sandstone_crystal_floor_path","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","mcwlights:festive_lantern","securitycraft:block_change_detector","twigs:rocky_dirt","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","mcwpaths:red_sandstone_flagstone_slab","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","pneumaticcraft:elevator_frame","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","pneumaticcraft:wall_lamp_cyan","create:crafting/kinetics/fluid_pipe","pneumaticcraft:pressure_chamber_glass_x4","mcwpaths:red_sand_path_block","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","undergarden:gloom_o_lantern","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","twigs:oak_table","pneumaticcraft:heat_pipe","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","mcwfurnitures:oak_lower_triple_drawer","mcwpaths:red_sandstone_flagstone","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","mcwdoors:jungle_japanese_door","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","cfm:purple_kitchen_drawer","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","mcwroofs:red_sandstone_top_roof","farmersdelight:diamond_knife","minecraft:oak_sign","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","minecraft:red_sandstone_stairs_from_red_sandstone_stonecutting","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:stripped_spruce_counter","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","mcwroofs:spruce_lower_roof","mcwwindows:cyan_mosaic_glass_pane","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","mcwdoors:spruce_whispering_door","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","deeperdarker:bloom_boat","utilitix:linked_repeater","dyenamics:rose_stained_glass","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","minecraft:purple_concrete_powder","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","immersiveengineering:crafting/light_engineering","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwpaths:red_sandstone_square_paving","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","mcwroofs:oak_planks_attic_roof","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","railcraft:radio_circuit","minecraft:smooth_red_sandstone_stairs","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","mcwtrpdoors:spruce_classic_trapdoor","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwroofs:red_sandstone_upper_lower_roof","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","handcrafted:red_sandstone_corner_trim","mcwroofs:lime_concrete_attic_roof","pneumaticcraft:manometer","securitycraft:alarm","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","pneumaticcraft:wall_lamp_inverted_magenta","mcwroofs:oak_top_roof","mcwpaths:sandstone_honeycomb_paving","additionallanterns:amethyst_lantern","utilitix:crude_furnace","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwwindows:stone_window","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","productivebees:stonecutter/yucca_canvas_hive","sophisticatedstorage:jukebox_upgrade","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","productivebees:expansion_boxes/expansion_box_warped_canvas","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwdoors:oak_japanese2_door","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","mcwpaths:red_sandstone_strewn_rocky_path","utilitarian:utility/oak_logs_to_slabs","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","productivebees:stonecutter/yucca_canvas_expansion_box","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","immersiveengineering:crafting/conveyor_redstone","mcwbiomesoplenty:magic_pyramid_gate","mcwfurnitures:spruce_table","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","minecraft:red_sandstone_wall","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","minecraft:iron_nugget_from_smelting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","xnet:advanced_connector_blue","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwroofs:orange_concrete_upper_lower_roof","mcwtrpdoors:spruce_barn_trapdoor","mcwfurnitures:spruce_modern_wardrobe","productivebees:stonecutter/hellbark_canvas_hive","mcwwindows:prismarine_window","securitycraft:laser_block","mcwpaths:red_sandstone_flagstone_stairs","mcwfences:modern_andesite_wall","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","productivebees:stonecutter/crimson_canvas_expansion_box","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","cfm:lime_kitchen_drawer","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:spruce_drawer_counter","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","deeperdarker:echo_chest_boat","eccentrictome:tome","securitycraft:reinforced_warped_fence","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","sophisticatedstorage:chipped/tinkering_table_upgrade","mcwpaths:red_sandstone_crystal_floor_slab","buildinggadgets2:gadget_copy_paste","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwfurnitures:spruce_double_drawer","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwfurnitures:stripped_spruce_drawer_counter","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","mcwfurnitures:stripped_spruce_modern_wardrobe","productivebees:expansion_boxes/expansion_box_oak_canvas","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","mcwdoors:jungle_paper_door","mcwpaths:red_sandstone_clover_paving","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:red_sandstone_running_bond_path","immersiveengineering:crafting/conveyor_vertical","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","minecraft:yellow_stained_glass","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:wheat_dough_from_eggs","cfm:dark_oak_kitchen_sink_light","mcwbiomesoplenty:willow_pane_window","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","immersiveengineering:crafting/strip_curtain","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","mcwfurnitures:spruce_counter","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","dyenamics:amber_stained_glass","securitycraft:blacklist_module","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","productivebees:hives/advanced_acacia_canvas_hive","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","immersiveengineering:crafting/item_batcher","sophisticatedstorage:chipped/botanist_workbench_upgrade","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mcwbiomesoplenty:magic_plank_window2","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","domum_ornamentum:green_cactus_extra","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","pneumaticcraft:air_canister","mcwwindows:prismarine_pane_window","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:slice_map","mcwpaths:red_sandstone_windmill_weave_stairs","productivebees:expansion_boxes/expansion_box_jungle_canvas","appflux:insulating_resin","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","cfm:stripped_spruce_desk_cabinet","mcwfences:jungle_wired_fence","pneumaticcraft:kerosene_lamp","mcwfences:end_brick_pillar_wall","cfm:gray_grill","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","pneumaticcraft:wall_lamp_inverted_cyan","mcwroofs:spruce_steep_roof","mcwfurnitures:jungle_modern_chair","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","railcraft:steel_helmet","mcwroofs:red_sandstone_roof","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","cfm:stripped_spruce_mail_box","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","immersiveengineering:crafting/windmill_sail","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","minecraft:chiseled_red_sandstone_from_red_sandstone_stonecutting","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","mcwfurnitures:stripped_spruce_bookshelf_drawer","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","forbidden_arcanus:processed_obsidian_block_from_obsidian_ingot","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","pneumaticcraft:pressure_chamber_valve_x1","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","minecraft:spruce_wood","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","botania:runic_altar","domum_ornamentum:purple_brick_extra","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","minecraft:smooth_red_sandstone_slab","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","productivebees:stonecutter/maple_canvas_hive","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","handcrafted:jungle_table","supplementaries:planter_rich","botania:abstruse_platform","mcwfences:crimson_stockade_fence","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","productivebees:expansion_boxes/expansion_box_birch_canvas","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwroofs:magenta_concrete_upper_lower_roof","immersiveengineering:crafting/cushion","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","mcwwindows:white_mosaic_glass_pane","cfm:stripped_spruce_coffee_table","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:fishing_rod","xnet:connector_yellow_dye","mcwwindows:acacia_blinds","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","minecraft:cut_red_sandstone","blue_skies:lunar_bookshelf","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","productivebees:stonecutter/redwood_canvas_hive","mcwlights:double_street_lamp","minecraft:tnt","minecolonies:chainmailboots","mcwdoors:spruce_stable_door","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","cfm:spruce_upgraded_gate","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwfurnitures:spruce_cupboard_counter","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","ae2:network/cells/item_cell_housing","minecraft:cobblestone_wall_from_cobblestone_stonecutting","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","minecraft:brown_concrete_powder","productivebees:expansion_boxes/expansion_box_snake_block_canvas","securitycraft:reinforced_lime_stained_glass_pane_from_dye","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","domum_ornamentum:brown_brick_extra","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","securitycraft:protecto","immersiveengineering:crafting/conveyor_basic_covered","utilitix:jungle_shulker_boat","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_spruce_cupboard_counter","cfm:orange_kitchen_drawer","silentgear:stone_rod","railcraft:tank_detector","handcrafted:sandstone_corner_trim","immersiveengineering:crafting/balloon","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","mcwlights:pink_paper_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","mcwpaths:red_sandstone_running_bond_slab","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","aether:iron_gloves","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","mcwbiomesoplenty:mahogany_highley_gate","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","aether:book_of_lore","minecraft:brush","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","sfm:cable","minecraft:hay_block","mcwroofs:deepslate_upper_steep_roof","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","pneumaticcraft:compressed_iron_boots","mcwwindows:red_sandstone_window2","botania:phantom_ink","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","domum_ornamentum:brick_extra","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/generator","immersiveengineering:crafting/cokebrick_from_slab","mcwwindows:warped_stem_four_window","securitycraft:sentry","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwwindows:blue_mosaic_glass_pane","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","silentgear:fine_silk_cloth","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","utilitarian:utility/spruce_logs_to_slabs","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwpaths:red_sandstone_windmill_weave_slab","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","domum_ornamentum:white_brick_extra","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwroofs:red_sandstone_upper_steep_roof","domum_ornamentum:pink_floating_carpet","securitycraft:reinforced_andesite","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","handcrafted:oven","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwlights:jungle_tiki_torch","delightful:knives/bronze_knife","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","productivebees:stonecutter/comb_canvas_hive","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","minecraft:red_sandstone_wall_from_red_sandstone_stonecutting","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","silentgear:upgrade_base","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","handcrafted:pufferfish_trophy","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","minecraft:oak_chest_boat","mcwdoors:spruce_waffle_door","simplylight:illuminant_purple_block_on_toggle","mcwpaths:red_sandstone_windmill_weave","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","pneumaticcraft:logistics_core","everythingcopper:copper_helmet","mcwroofs:red_concrete_upper_lower_roof","modularrouters:blank_upgrade","mcwdoors:spruce_swamp_door","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","cfm:stripped_spruce_cabinet","minecraft:mangrove_boat","minecraft:bread","mcwdoors:spruce_barn_glass_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","mcwdoors:garage_silver_door","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwfurnitures:spruce_coffee_table","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","minecraft:iron_shovel","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","mcwroofs:jungle_planks_lower_roof","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","pneumaticcraft:camo_applicator","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","supplementaries:gold_trapdoor","cfm:fridge_light","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","mcwfurnitures:stripped_spruce_covered_desk","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbridges:red_sandstone_bridge_pier","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","mcwpaths:red_sandstone_dumble_paving","mcwbiomesoplenty:magic_highley_gate","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","mcwbridges:balustrade_orange_sandstone_bridge","mcwfences:oak_curved_gate","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","supplementaries:wrench","deepresonance:radiation_suit_boots","minecraft:bow","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","cfm:purple_kitchen_sink","mcwpaths:red_sandstone_basket_weave_paving","cfm:black_cooler","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:spruce_glass_table","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwfurnitures:spruce_covered_desk","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","mcwwindows:stripped_spruce_log_four_window","immersiveengineering:crafting/coil_hv","mcwroofs:blackstone_upper_lower_roof","mcwdoors:spruce_mystic_door","mcwtrpdoors:spruce_beach_trapdoor","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","travelersbackpack:blaze","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","mcwwindows:deepslate_pane_window","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwpaths:sandstone_flagstone_path","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwdoors:jungle_tropical_door","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","crafting_on_a_stick:crafting_table","mcwdoors:oak_barn_door","botania:alchemy_catalyst","mcwfurnitures:spruce_double_drawer_counter","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","immersiveengineering:crafting/ersatz_leather","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","mcwfurnitures:stripped_spruce_coffee_table","minecraft:ender_eye","mcwfurnitures:spruce_double_wardrobe","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","productivebees:stonecutter/mangrove_canvas_expansion_box","railcraft:receiver_circuit","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","immersiveengineering:crafting/conveyor_dropper","sophisticatedstorage:feeding_upgrade","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","additionallanterns:diamond_chain","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","securitycraft:trophy_system","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","mcwroofs:jungle_lower_roof","mcwbridges:orange_sandstone_bridge","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:age_detector","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","productivebees:hives/advanced_dark_oak_canvas_hive","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwwindows:birch_plank_parapet","mcwpaths:red_sandstone_running_bond","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","domum_ornamentum:black_brick_extra","railcraft:steel_chestplate","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwdoors:spruce_japanese2_door","enderio:fluid_tank","mcwroofs:oak_planks_roof","ironfurnaces:furnaces/gold_furnace","bigreactors:energizer/casing","mcwpaths:stone_flagstone","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","botania:mana_tablet","cfm:birch_mail_box","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwpaths:red_sandstone_windmill_weave_path","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:logistics_configurator","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:3612,warning_level:0}}