{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:96,Id:45,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:vitality"},{Ambient:1b,Amplifier:5b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:348,HiddenEffect:{Ambient:0b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:0,Id:10,ShowIcon:1b,ShowParticles:1b,"forge:id":"minecraft:regeneration"},Id:10,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:regeneration"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:348,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:348,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],Air:300s,Attributes:[{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:1.0d,Name:"irons_spellbooks:cast_time_reduction"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:20.0d,Modifiers:[{Amount:4.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.05d,Name:"attributeslib:crit_chance"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"eidolon:magic_power"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Modifiers:[{Amount:0.4d,Name:"effect.attributeslib.vitality 1",Operation:0,UUID:[I;-**********,-**********,-1081819870,222680728]}],Name:"attributeslib:healing_received"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:1.5d,Name:"attributeslib:crit_damage"},{Base:1.0d,Name:"attributeslib:arrow_velocity"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"attributeslib:life_steal"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:1.0d,Name:"attributeslib:arrow_damage"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-**********,-684178100,-**********,-55231777],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"ad_astra_giselle_addon:netherite_oxygen_can",tag:{BotariumData:{StoredFluids:[{Amount:1000L,Fluid:"ad_astra:oxygen"}]},oxygencharger:{chargemode:"ad_astra_giselle_addon:none"}}}],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,ForgeCaps:{Parent:{energy:{energy:575975366},module_host:{modules:[{anim:1.0f,boost:0.0d,boost_time:0,cap:4020,cooldwn:0,env_cdwn:0b,hit:-7.4505806E-8f,id:"draconicevolution:chaotic_shield_control",max_boost:0.0d,points:4020.0d,properties:{shield_mod.always_visible:{hud:1b,uni_name:[I;1780224921,-329692540,-1310131564,478134416]},shield_mod.enabled:{hud:1b,uni_name:[I;1897934853,-1811333058,-1482027620,-2101859754]}},visible:1b,x:6b,y:0b},{id:"draconicevolution:chaotic_large_shield_capacity",x:4b,y:0b},{id:"draconicevolution:chaotic_energy",x:0b,y:3b},{id:"draconicevolution:chaotic_energy",x:1b,y:3b},{id:"draconicevolution:chaotic_energy",x:2b,y:3b},{id:"draconicevolution:chaotic_energy",x:3b,y:3b},{id:"draconicevolution:chaotic_energy",x:4b,y:3b},{id:"draconicevolution:chaotic_shield_recovery",x:4b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:5b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:0b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:1b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:2b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:3b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:6b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:7b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:8b,y:6b},{id:"draconicevolution:chaotic_shield_recovery",x:9b,y:6b},{id:"draconicevolution:chaotic_shield_capacity",x:0b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:1b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:2b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:3b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:4b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:5b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:6b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:7b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:8b,y:7b},{id:"draconicevolution:chaotic_shield_capacity",x:9b,y:7b},{id:"draconicevolution:chaotic_shield_recovery",x:0b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:1b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:2b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:3b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:4b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:5b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:6b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:7b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:8b,y:5b},{id:"draconicevolution:chaotic_shield_recovery",x:9b,y:5b},{id:"draconicevolution:chaotic_shield_capacity",x:0b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:1b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:2b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:3b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:4b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:5b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:6b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:7b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:8b,y:2b},{id:"draconicevolution:chaotic_shield_capacity",x:9b,y:2b},{id:"draconicevolution:chaotic_shield_recovery",x:0b,y:4b},{id:"draconicevolution:chaotic_shield_recovery",x:1b,y:4b},{id:"draconicevolution:chaotic_shield_recovery",x:2b,y:4b},{id:"draconicevolution:chaotic_shield_recovery",x:3b,y:4b},{id:"draconicevolution:chaotic_shield_recovery",x:4b,y:4b},{id:"draconicevolution:chaotic_shield_recovery",x:7b,y:4b},{id:"draconicevolution:chaotic_shield_recovery",x:8b,y:4b},{id:"draconicevolution:chaotic_shield_recovery",x:9b,y:4b},{id:"draconicevolution:chaotic_energy",x:0b,y:0b},{id:"draconicevolution:chaotic_energy",x:1b,y:0b},{id:"draconicevolution:chaotic_energy",x:2b,y:0b},{id:"draconicevolution:chaotic_energy",x:3b,y:0b},{id:"draconicevolution:chaotic_shield_capacity",x:0b,y:1b},{id:"draconicevolution:chaotic_shield_capacity",x:1b,y:1b},{id:"draconicevolution:chaotic_shield_capacity",x:2b,y:1b},{id:"draconicevolution:chaotic_shield_capacity",x:3b,y:1b},{id:"draconicevolution:chaotic_shield_recovery",x:9b,y:3b},{id:"draconicevolution:chaotic_large_shield_capacity",x:8b,y:0b},{id:"draconicevolution:chaotic_shield_recovery",x:7b,y:3b},{id:"draconicevolution:chaotic_shield_recovery",x:8b,y:3b},{charge:900,id:"draconicevolution:chaotic_undying",invul:0,x:5b,y:3b}],properties:{},provider_id:[I;-**********,-**********,-**********,-**********]}}},Slot:0,id:"draconicevolution:chaotic_chestpiece",tag:{}}],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"apotheosis:potion_charm",tag:{Damage:0,Potion:"apotheosis:strong_vitality",Unbreakable:1b,charm_enabled:1b}}],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:fire_gauntlet"},{Count:1b,Slot:1,id:"botania:odin_ring",tag:{baubleUUID:[I;-1156812661,-911062143,-1080491291,-2139281856],soulbindUUID:"c30aada0-3e52-4fbf-8a83-a977fcba83df"}}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:backpack",tag:{contentsUuid:[I;1509880617,-2030547166,-1099296764,939670819],inventorySlots:27,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{}}]},upgradeSlots:1}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:vampiric_glove"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"allthewizardgear:unobtainium_spell_book",tag:{ISB_Spells:{data:[{id:"irons_spellbooks:heartstop",index:0,level:10,locked:0b},{id:"irons_spellbooks:lightning_bolt",index:1,level:7,locked:0b}],maxSpells:15,mustEquip:1b,spellWheel:1b}}}],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:63,wirelessNetwork:5},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:1},"irons_spellbooks:player_magic":{castingEquipmentSlot:"spellbook",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:170,spellSelection:{index:1,lastIndex:0,lastSlot:"spellbook",slot:"spellbook"}},"l2library:conditionals":{data:{},tickSinceDeath:1184956},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:50},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE1:"diamond",MAHOUTSUKAI_LAST_RECIPE2:"diamond",MAHOUTSUKAI_LAST_RECIPE3:"iron",MAHOUTSUKAI_LAST_RECIPE_CLOTH:1b,MAHOUTSUKAI_MANA_FULL:0b,MAHOUTSUKAI_MANA_UP_COUNTER:50,MAHOUTSUKAI_PAGE:36,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:1b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"gtceu:shaped/block_compress_neodymium",ItemStack:{Count:1b,id:"gtceu:neodymium_block"}}],SelectedRecipe:"gtceu:shaped/block_compress_neodymium"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:165,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:0.21f,width:0.0f},"solcarrot:food":{foodList:["minecraft:apple","delightful:pumpkin_pie_slice","twilightforest:meef_stroganoff","minecraft:cooked_beef","artifacts:eternal_steak","croptopia:buttered_toast","minecraft:enchanted_golden_apple","minecraft:melon_slice"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:90s,knowledge:38,perks:[{id:10s,level:2b}]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:3b,PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:-6047314239421L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;1213950643,-1430042181,-1140969420,*********]},{FromDim:"minecraft:overworld",FromPos:-824633905086L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:549755797565L,UID:[I;1498657153,1869040310,-1654360962,1977210704]},{FromDim:"minecraft:the_nether",FromPos:4398046584841L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-1774407316,708723214,-1161571934,1833674158]},{FromDim:"minecraft:the_nether",FromPos:824633704509L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;1520428032,-982826190,-1862358385,-568735719]},{FromDim:"minecraft:overworld",FromPos:96207268925505L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;-113798171,1569542092,-1340447096,-1595736434]},{FromDim:"minecraft:the_nether",FromPos:12919261778018L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-1205869345,1135953431,-1887209950,-777346365]},{FromDim:"minecraft:overworld",FromPos:-9070971117498L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;48745036,1360675510,-1199668616,849806477]},{FromDim:"minecraft:the_nether",FromPos:14293651308643L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-1282316245,-1610332727,-1548084462,-1881353825]},{FromDim:"minecraft:overworld",FromPos:479387055955948L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;1070160097,556942760,-1163945765,872681043]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{WarpStoneCooldownUntilUnix:1753675221849L,Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","70e61f07-a350-437a-ae01-15e3ef9a1bab","4f5f98b0-b346-45b6-83f1-59fad95c9199","3c0ccabf-fdf5-42f9-8d79-************","6c8881a8-dbdf-465b-bb3e-0c97f47d45a4","d1811b83-cc8c-4821-80c6-3e5ce576ae62","42d85d81-dfae-42b5-ab86-9ebce283d4c6","44305267-ff27-4e8f-8df6-4521174251c8","db9fa2f7-bedb-4c7b-91c7-5ef71c247526","3701bcdb-916d-42ae-b046-b2e1a1f94a25","36d86fd7-e43f-48b5-a2b9-6969a1ba515c","9ba0302b-f845-40f5-9335-3ed5ac7759bb","491241f8-9fc1-4e12-aa7a-dab068db44c3","7ed2b2d1-c6ac-4c62-8950-c88f832a3e35","8e2f3e86-835b-4333-8d31-421884ba727e","35565ae9-d211-4465-9a1e-dcc336c86a23","2968051a-1dc8-457d-97c2-8af1ed99da77","8736efdf-35ba-4252-bdd1-7c81e39a90da","44d772cc-bc38-4546-9510-9f013cbf1934","ca28c95d-ca7a-44e2-823a-0af2e1199d4d","d9c17d31-d3d7-4854-bd5f-5da34a1b174c","f178d140-58e5-4f1a-9f57-dffe871e0a1c","cca8d749-2936-4d9a-a535-4335d9016efe","44953abf-804d-48a9-bf97-74d84d506fb0","93436da3-28fb-49a1-964c-5bc926b4199c","79476455-ced0-4846-878e-07487bf7d53a"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_end_conquered_effects:[{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:370,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:370,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-31,tb_last_ground_location_y:253,tb_last_ground_location_z:-192,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},"aae$downkey":0b,"aae$nokey":1b,"aae$upkey":0b,"apoth.affix_cooldown.apotheosis:armor/mob_effect/blinding":12381947L,"apoth.affix_cooldown.apotheosis:sword/mob_effect/elusive":13252021L,apoth_reforge_seed:837810295,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedStorageSettings:{}},Health:68.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"ae2wtlib:wireless_universal_terminal",tag:{accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},blankPattern:[{Count:48b,Slot:0,id:"ae2:blank_pattern"}],crafting:1b,currentTerminal:"pattern_encoding",encodedInputs:[{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"},{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"},{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"},{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"},{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"},{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"},{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"},{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"},{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_ingot"}],encodedOutputs:[{"#":1L,"#c":"ae2:i",id:"gtceu:neodymium_block"}],filter_type:"ALL",internalCurrentPower:4800000.0d,internalMaxPower:4800000.0d,mode:"CRAFTING",pattern_access:1b,pattern_encoding:1b,show_pattern_providers:"VISIBLE",sort_by:"AMOUNT",sort_direction:"ASCENDING",substitute:1b,substituteFluids:1b,view_mode:"ALL"}},{Count:58b,Slot:1b,id:"fluxnetworks:flux_point"},{Count:1b,Slot:2b,id:"gtceu:diamond_screwdriver",tag:{DisallowContainerItem:0,GT.Behaviours:{},GT.Tool:{AttackDamage:6.0f,AttackSpeed:3.1f,Damage:89,HarvestLevel:3,LastCraftingUse:53880927,MaxDamage:767},HideFlags:2}},{Count:1b,Slot:3b,id:"mekanism:configurator",tag:{GT.Behaviours:{},mekData:{EnergyContainers:[{Container:0b,stored:"60000"}],state:10}}},{Count:2b,Slot:4b,id:"gtceu:uhv_energy_input_hatch_16a"},{Count:1b,Slot:5b,id:"gtceu:creative_energy"},{Count:1b,Slot:7b,id:"gtceu:terminal"},{Count:1b,Slot:8b,id:"mekanism:meka_tool",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"16000000"}],modules:{"mekanism:excavation_escalation_unit":{amount:4,enabled:1b,excavation_mode:5,handleModeChange:1b,renderHUD:1b}}}}},{Count:1b,Slot:9b,id:"gtceu:item_filter",tag:{isBlackList:0b,matchNbt:0b,matches:[{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:coal"},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,**********,-**********,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}]}},{Count:16b,Slot:10b,id:"minecraft:egg"},{Count:1b,Slot:12b,id:"fluxnetworks:flux_point",tag:{FluxData:{buffer:0L,customName:"",limit:800000L,networkID:19,priority:0}}},{Count:1b,Slot:16b,id:"expatternprovider:wireless_tool"},{Count:1b,Slot:17b,id:"gtceu:raw_neodymium_block"},{Count:1b,Slot:22b,id:"waystones:warp_stone",tag:{Damage:0}},{Count:1b,Slot:23b,id:"ae2:memory_card",tag:{Config:"block.ae2.interface",Data:{config:[{},{},{},{},{},{},{},{"#":64L,"#c":"ae2:i",id:"minecraft:acacia_log"},{"#":4000L,"#c":"ae2:f",id:"gtceu:nitrogen"}],fuzzy_mode:"IGNORE_ALL",priority:0,upgrades:{}}}},{Count:1b,Slot:24b,id:"gtceu:uv_parallel_hatch"},{Count:57b,Slot:33b,id:"ae2:fluix_covered_cable"},{Count:1b,Slot:100b,id:"mekanism:mekasuit_boots",tag:{affix_data:{affixes:{"apotheosis:armor/attribute/blessed":1.0f,"apotheosis:armor/attribute/fortunate":0.5019871f,"apotheosis:armor/attribute/steel_touched":0.76294816f,"apotheosis:armor/dmg_reduction/feathery":0.7066602f,"apotheosis:armor/mob_effect/nimble":0.8469725f,"eidolon:wand/attribute/magic_power":0.9517962f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.eidolon:wand/attribute/magic_power"},"",{"translate":"affix.apotheosis:armor/mob_effect/nimble.suffix"}]}',rarity:"apotheosis:mythic",sockets:1,uuids:[[I;322719130,-1459207742,-1105899110,-298431713]]},mekData:{EnergyContainers:[{Container:0b,stored:"4095999999.3143"}],modules:{"ad_astra_giselle_addon:gravity_normalizing_unit":{},"mekanism:energy_unit":{amount:8,enabled:1b},"mekanism:laser_dissipation_unit":{},"mekanism:radiation_shielding_unit":{}}}}},{Count:1b,Slot:101b,id:"mekanism:mekasuit_pants",tag:{affix_data:{affixes:{"apotheosis:armor/attribute/spiritual":1.0f,"apotheosis:armor/dmg_reduction/blast_forged":0.5839635f,"apotheosis:armor/dmg_reduction/blockading":0.6121013f,"apotheosis:armor/dmg_reduction/runed":1.0f,"irons_spellbooks:armor/attribute/cooldown":0.68051815f,"irons_spellbooks:armor/attribute/mana":0.67244786f,"irons_spellbooks:armor/attribute/spell_power":0.39639252f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/cooldown"},"",{"translate":"affix.apotheosis:armor/attribute/fortunate.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-2010724544,-1823456870,-1220872979,271911091]]},mekData:{Enchantments:[{id:"minecraft:swift_sneak",lvl:5s}],EnergyContainers:[{Container:0b,stored:"4095999999.3143"}],modules:{"mekanism:energy_unit":{amount:8,enabled:1b},"mekanism:gyroscopic_stabilization_unit":{},"mekanism:laser_dissipation_unit":{},"mekanism:locomotive_boosting_unit":{amount:4,enabled:1b,handleModeChange:1b,sprint_boost:4},"mekanism:motorized_servo_unit":{amount:5,enabled:1b},"mekanism:radiation_shielding_unit":{}}}}},{Count:1b,Slot:102b,id:"mekanism:mekasuit_bodyarmor",tag:{affix_data:{affixes:{"apotheosis:armor/attribute/blessed":1.0f,"apotheosis:armor/attribute/spiritual":1.0f,"apotheosis:armor/dmg_reduction/blockading":0.6717196f,"apotheosis:armor/mob_effect/bursting":1.0f,"irons_spellbooks:armor/attribute/cooldown":1.0f,"irons_spellbooks:armor/attribute/spell_power":1.0f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:mythic"},gem:"apotheosis:the_nether/blood_lord",uuids:[[I;144440742,-1632418366,-1115996713,-245623711],[I;489892519,1384860552,-1713263274,-2005940834]]}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}}],name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/cooldown"},"",{"translate":"affix.apotheosis:armor/attribute/winged.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-413046228,2121747341,-2114217422,285784426]]},mekData:{EnergyContainers:[{Container:0b,stored:"4095998999.3143"}],GasTanks:[{Tank:0b,stored:{amount:48000L,gasName:"mekanism:hydrogen"}}],modules:{"gravitationalmodulatingunittweaks:gravitational_modulating_additional_unit":{amount:1,enabled:1b,fix_fov:1b,fly_always:0b,stop_immediately:0b,vertical_speed:4},"mekanism:charge_distribution_unit":{},"mekanism:elytra_unit":{},"mekanism:energy_unit":{amount:8,enabled:1b},"mekanism:geiger_unit":{},"mekanism:gravitational_modulating_unit":{amount:1,enabled:1b,handleModeChange:0b,renderHUD:1b,speed_boost:2},"mekanism:jetpack_unit":{amount:1,enabled:0b,handleModeChange:1b,jetpack_mode:0,renderHUD:1b},"mekanism:laser_dissipation_unit":{},"mekanism:radiation_shielding_unit":{}}}}},{Count:1b,Slot:103b,id:"mekanism:mekasuit_helmet",tag:{affix_data:{affixes:{"apotheosis:armor/attribute/blessed":1.0f,"apotheosis:armor/attribute/ironforged":1.0f,"apotheosis:armor/dmg_reduction/runed":0.82511765f,"apotheosis:armor/mob_effect/blinding":0.9877145f,"irons_spellbooks:armor/attribute/cooldown":1.0f,"irons_spellbooks:armor/attribute/spell_power":1.0f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/fortunate"},"",{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-463227018,1370573536,-1585040237,-996224749]]},mekData:{EnergyContainers:[{Container:0b,stored:"4096000000"}],FluidTanks:[{Tank:0b,stored:{Amount:89650,FluidName:"mekanism:nutritional_paste"}}],modules:{"ad_astra_giselle_addon:space_breathing_unit":{},"mekanism:electrolytic_breathing_unit":{amount:4,enabled:1b,fill_held:1b},"mekanism:energy_unit":{amount:8,enabled:1b},"mekanism:inhalation_purification_unit":{},"mekanism:laser_dissipation_unit":{},"mekanism:nutritional_injection_unit":{},"mekanism:radiation_shielding_unit":{},"mekanismgenerators:solar_recharging_unit":{amount:8,enabled:1b}}}}}],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:mining",pos:[I;-58,257,-91]},Motion:[0.0d,0.0d,0.0d],OnGround:0b,PortalCooldown:0,Pos:[-34.5273130836659d,267.4655879291772d,-187.8269738391935d],Railways_DataVersion:2,Rotation:[162.75171f,10.349938f],Score:405035,SelectedItemSlot:0,SleepTimer:0s,SpawnAngle:77.55017f,SpawnDimension:"allthemodium:mining",SpawnForced:0b,SpawnX:-60,SpawnY:253,SpawnZ:-102,Spigot.ticksLived:1184949,UUID:[I;-1022710368,1045581759,-1971082889,-54885409],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-10170483207925L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:104,XpP:0.5823387f,XpSeed:-1296793128,XpTotal:27117,abilities:{flySpeed:0.05f,flying:1b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752059582303L,keepLevel:0b,lastKnownName:"qingniao-0910",lastPlayed:1753683607702L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:2.6532812f,foodLevel:20,foodSaturationLevel:1.6f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","croptopia:mead","mcwfurnitures:stripped_jungle_desk","mcwfurnitures:stripped_acacia_counter","mcwwindows:birch_four_window","biomesoplenty:fir_boat","mcwdoors:bamboo_stable_door","pneumaticcraft:chunkloader_upgrade","refinedstorage:coloring_recipes/red_detector","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","refinedstorage:coloring_recipes/pink_security_manager","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","mcwpaths:dark_prismarine_dumble_paving","alltheores:gold_dust_from_hammer_crushing","minecraft:bone_block","refinedstorage:coloring_recipes/pink_controller","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwbiomesoplenty:willow_waffle_door","mcwroofs:lime_terracotta_lower_roof","refinedstorage:coloring_recipes/red_pattern_grid","minecraft:melon","ae2:network/cables/dense_smart_green","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","sophisticatedstorage:backpack_crafting_upgrade_from_storage_crafting_upgrade","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","mcwtrpdoors:acacia_barrel_trapdoor","cfm:jungle_coffee_table","xnet:connector_green","advanced_ae:smallappupgrade","create:oak_window","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","sophisticatedstorage:backpack_advanced_magnet_upgrade_from_storage_advanced_magnet_upgrade","mcwfurnitures:stripped_acacia_bookshelf_cupboard","mythicbotany:central_rune_holder","sophisticatedbackpacks:void_upgrade","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","dyenamics:maroon_candle","mcwroofs:acacia_upper_steep_roof","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","refinedstorage:coloring_recipes/gray_grid","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","supplementaries:forbidden_arcanus/sign_post_edelwood","appbot:mana_storage_cell_16k","mcwtrpdoors:acacia_barn_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","alltheores:brass_plate","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","railcraft:controller_circuit","twigs:polished_rhyolite_stonecutting","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","utilitix:oak_shulker_boat_with_shell","create:crafting/kinetics/gearbox","create:crafting/logistics/brass_tunnel","mcwbiomesoplenty:umbran_modern_door","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","rftoolsutility:screen_link","minecraft:leather_helmet","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","mcwbiomesoplenty:redwood_nether_door","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:bubble_blower","refinedstorage:coloring_recipes/white_disk_manipulator","dyenamics:conifer_stained_glass_pane_from_glass_pane","supplementaries:crank","minecraft:recovery_compass","securitycraft:secret_bamboo_sign_item","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","cfm:acacia_cabinet","mcwbiomesoplenty:willow_bark_glass_door","minecraft:pink_terracotta","refinedstorage:coloring_recipes/magenta_security_manager","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","refinedstorage:coloring_recipes/blue_crafting_grid","mcwbiomesoplenty:empyreal_classic_door","twigs:bamboo_thatch","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","refinedstorage:coloring_recipes/fluid_grid","nethersdelight:golden_machete","ae2:network/cables/smart_fluix_clean","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","allthecompressed:compress/quartz_block_1x","railcraft:standard_rail_from_rail","refinedstorage:coloring_recipes/brown_security_manager","twilightdelight:torchberry_pie","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","refinedstorage:coloring_recipes/white_grid","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","railcraft:train_detector","silentgear:crimson_steel_nugget","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","minecraft:golden_hoe","cfm:oak_kitchen_counter","mcwfurnitures:acacia_lower_triple_drawer","minecraft:fire_charge","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","twilightdelight:glacier_ice_tea","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:pink_petal_block","alltheores:platinum_dust_from_hammer_crushing","railcraft:any_detector","mcwpaths:dark_prismarine_clover_paving","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","pneumaticcraft:reinforced_chest_kit","mcwroofs:stone_bricks_upper_steep_roof","biomesoplenty:palm_chest_boat","minecraft:stone_button","mcwtrpdoors:bamboo_mystic_trapdoor","mcwbiomesoplenty:redwood_bark_glass_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","chemlib:strontium_ingot_to_block","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","pneumaticcraft:regulator_tube_module","mcwwindows:birch_plank_window","minecraft:sandstone","allthemodium:vibranium_ingot_from_raw_blasting","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:no_soliciting/soliciting_carpets/gray_trapped_soliciting_carpet","pneumaticcraft:logistics_frame_active_provider_self","minecraft:lodestone","mcwbiomesoplenty:umbran_japanese2_door","handcrafted:birch_counter","mcwlights:black_lamp","minecraft:purpur_block","railcraft:brass_ingot_crafted_with_ingots","minecraft:cut_copper_stairs_from_copper_block_stonecutting","minecraft:polished_blackstone_slab","mcwbiomesoplenty:mahogany_window","silentgear:azure_electrum_nugget","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:quartz","refinedstorage:coloring_recipes/brown_pattern_grid","refinedstorage:coloring_recipes/yellow_fluid_grid","pneumaticcraft:transfer_gadget","constructionwand:core_destruction","refinedstorage:coloring_recipes/purple_crafting_grid","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","allthemodium:unobtainium_dust_from_ore_crushing","mcwdoors:jungle_bark_glass_door","refinedstorage:coloring_recipes/pink_network_receiver","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","minecraft:redstone_from_blasting_redstone_ore","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","refinedstorage:coloring_recipes/white_detector","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","enderio:resetting_lever_five","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:hellbark_beach_door","twilightdelight:aurora_pie","chemlib:manganese_nugget_to_ingot","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","refinedstorage:coloring_recipes/red_grid","mcwbiomesoplenty:umbran_classic_door","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","ae2:network/parts/terminals","securitycraft:keypad_frame","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","twilightforest:equipment/fiery_fiery_helmet","bigreactors:turbine/basic/shaft","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:hellbark_bark_glass_door","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","pneumaticcraft:vortex_tube","refinedstorage:coloring_recipes/orange_wireless_transmitter","xnet:connector_routing","mcwwindows:warped_louvered_shutter","undergarden:wigglewood_chest_boat","blue_skies:glowing_nature_stone","handcrafted:birch_nightstand","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:palm_plank_window2","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","bloodmagic:ritual_diviner_1","bloodmagic:ritual_diviner_0","megacells:network/mega_interface","mcwroofs:green_terracotta_attic_roof","minecraft:birch_stairs","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:palm_bark_glass_door","immersiveengineering:crafting/shield","mcwbiomesoplenty:umbran_cottage_door","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","refinedstorage:coloring_recipes/magenta_network_receiver","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","sophisticatedstorage:birch_barrel","refinedstorage:coloring_recipes/brown_network_receiver","dyenamics:ultramarine_candle","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","ae2:network/crafting/cpu_crafting_monitor","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","mcwbiomesoplenty:pine_stable_head_door","refinedstorage:coloring_recipes/light_gray_fluid_grid","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","refinedstorage:coloring_recipes/purple_crafter_manager","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","cfm:orange_grill","dyenamics:fluorescent_candle","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","cfm:stripped_acacia_park_bench","ae2:network/blocks/storage_chest","securitycraft:secret_bamboo_hanging_sign","mcwroofs:oak_roof","cfm:jungle_desk","refinedstorage:coloring_recipes/yellow_crafter","aether:diamond_boots_repairing","minecraft:mangrove_chest_boat","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwroofs:light_gray_roof_slab","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","mcwroofs:purple_striped_awning","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwbiomesoplenty:palm_japanese2_door","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","ae2:network/wireless_booster","dyenamics:lavender_candle","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","refinedstorage:coloring_recipes/blue_network_transmitter","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","croptopia:crab_legs","mcwtrpdoors:bamboo_tropical_trapdoor","expatternprovider:ingredient_buffer","sophisticatedbackpacks:filter_upgrade","mcwbiomesoplenty:maple_japanese_door","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","ae2:network/cables/dense_covered_purple","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwfurnitures:acacia_stool_chair","alltheores:nickel_ingot_from_ore_blasting","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","bigreactors:crafting/blutonium_storage_to_component","minecraft:target","chemlib:beryllium_ingot_from_blasting_beryllium_dust","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","supplementaries:evilcraft/sign_post_undead","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","mcwfurnitures:stripped_acacia_modern_chair","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","chemlib:sodium_block_to_ingot","cfm:stripped_birch_kitchen_drawer","mcwfurnitures:stripped_acacia_lower_triple_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","bigreactors:turbine/insanite_block","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","cfm:birch_bedside_cabinet","mcwroofs:blue_terracotta_roof","xnet:netcable_yellow","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","mcwfurnitures:jungle_wardrobe","croptopia:cheese","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","refinedstorage:coloring_recipes/black_detector","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","sophisticatedstorage:backpack_stack_upgrade_tier_4_from_storage_stack_upgrade_tier_5","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwbiomesoplenty:willow_beach_door","mcwpaths:andesite_running_bond_slab","twilightforest:wood/dark_planks","minecraft:chiseled_stone_bricks_stone_from_stonecutting","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","chemlib:sodium_ingot_from_blasting_sodium_dust","minecraft:netherite_sword_smithing","croptopia:shaped_toast_sandwich","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","botania:manasteel_axe","mcwwindows:oak_blinds","mcwtrpdoors:acacia_cottage_trapdoor","create:crafting/kinetics/mechanical_crafter","mcwfences:birch_wired_fence","mcwbiomesoplenty:empyreal_beach_door","mcwroofs:red_concrete_lower_roof","cfm:dye_orange_picket_fence","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","delightful:food/baklava","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","arseng:source_storage_cell_16k","railcraft:signal_circuit","twilightforest:charm_of_life_2","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","aether:golden_aercloud_enchanting","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","mcwbiomesoplenty:mahogany_swamp_door","connectedglass:borderless_glass_pane1","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","cfm:stripped_acacia_kitchen_counter","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","refinedstorage:coloring_recipes/magenta_controller","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","refinedstorage:coloring_recipes/red_relay","cfm:red_grill","ae2:network/cells/fluid_storage_cell_16k_storage","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","refinedstorage:coloring_recipes/red_crafter","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","mcwbiomesoplenty:maple_modern_door","mcwpaths:cobbled_deepslate_running_bond","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","utilitarian:no_soliciting/soliciting_carpets/lime_trapped_soliciting_carpet","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","croptopia:chicken_and_dumplings","silentgear:tyrian_steel_block","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","botania:gray_petal_block","twilightforest:wood/aurora_slab_slab","mcwbiomesoplenty:empyreal_cottage_door","utilitix:tiny_charcoal_to_tiny","mcwtrpdoors:jungle_paper_trapdoor","refinedstorage:coloring_recipes/magenta_crafting_grid","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","pneumaticcraft:uv_light_box","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","nethersdelight:hoglin_sirloin_from_campfire_cooking","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","supplementaries:sign_post_oak","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","refinedstorage:coloring_recipes/black_grid","refinedstorage:coloring_recipes/brown_crafter_manager","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","pneumaticcraft:air_cannon","alltheores:netherite_dust_from_hammer_crushing","twilightdelight:hydra_burger","refinedstorage:coloring_recipes/controller","chemlib:gallium_ingot_from_blasting_gallium_dust","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:dark_prismarine_windmill_weave_slab","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","chemlib:manganese_ingot_to_nugget","railcraft:blast_furnace_bricks","ae2:network/cables/dense_covered_pink","dyenamics:amber_stained_glass_pane_from_glass_pane","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","bloodmagic:bloodstonebrick","ae2:tools/portable_item_cell_256k","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","securitycraft:secret_crimson_sign_item","mcwroofs:purple_terracotta_upper_lower_roof","ae2:network/cells/item_storage_cell_16k_storage","mcwbiomesoplenty:jacaranda_nether_door","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","mcwfurnitures:acacia_counter","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","blue_skies:cake_compat","mcwlights:reinforced_torch","utilitarian:utility/acacia_logs_to_doors","cfm:stripped_acacia_chair","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","pneumaticcraft:liquid_hopper","gtceu:smelting/smelt_dust_iron_to_ingot","refinedstorage:coloring_recipes/pink_fluid_grid","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","bloodmagic:path/path_stonetile","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","ad_astra:ti_69","mcwroofs:acacia_attic_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:fried_chicken","bloodmagic:blood_rune_orb_2","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","mcwbiomesoplenty:mahogany_nether_door","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:fir_bark_glass_door","mcwlights:oak_ceiling_fan_light","bloodmagic:experience_tome","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","evilcraft:crafting/undead_planks","xnet:advanced_connector_yellow","cfm:stripped_oak_chair","xnet:netcable_blue","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwwindows:oak_shutter","mcwroofs:lime_concrete_steep_roof","cfm:rock_path","connectedglass:scratched_glass_black2","botania:mana_spreader","connectedglass:scratched_glass_black1","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","minecraft:quartz_pillar","mysticalagriculture:imperium_block_combine","mcwbiomesoplenty:redwood_waffle_door","chemlib:indium_nugget_to_ingot","refinedstorage:coloring_recipes/light_gray_network_transmitter","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","ad_astra:black_industrial_lamp","refinedstorage:coloring_recipes/yellow_network_receiver","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","mcwbiomesoplenty:willow_barn_door","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwbridges:blackstone_bridge","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","allthemodium:unobtainium_ingot_from_dust_blasting","aether:obsidian_from_bucket_freezing","minecraft:blue_dye","railcraft:steel_leggings","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","mcwbiomesoplenty:umbran_nether_door","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","minecraft:piston","mcwwindows:crimson_planks_four_window","ad_astra:large_gas_tank","refinedstorage:coloring_recipes/orange_grid","mcwlights:white_lamp","mcwfurnitures:jungle_coffee_table","mcwbiomesoplenty:maple_waffle_door","appmek:chemical_storage_cell_4k","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","allthemodium:allthemodium_ingot_from_raw_smelting","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","ae2:network/cells/item_storage_components_cell_16k_part","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","mcwbiomesoplenty:hellbark_japanese2_door","refinedstorage:coloring_recipes/magenta_network_transmitter","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwdoors:bamboo_four_panel_door","mcwtrpdoors:oak_glass_trapdoor","advanced_ae:quantum_storage_component","refinedstorage:coloring_recipes/magenta_crafting_monitor","railcraft:villager_detector","mcwbiomesoplenty:willow_four_window","mcwbiomesoplenty:fir_stable_door","mcwbiomesoplenty:dead_modern_door","mcwroofs:gray_upper_steep_roof","botania:third_eye","mcwroofs:birch_planks_steep_roof","mcwwindows:metal_curtain_rod","mcwbiomesoplenty:palm_classic_door","mcwdoors:acacia_cottage_door","croptopia:apple_sapling","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","refinedstorage:coloring_recipes/magenta_relay","mcwroofs:oak_steep_roof","chemlib:strontium_ingot_from_blasting_strontium_dust","aether:skyroot_tripwire_hook","chemlib:potassium_ingot_to_block","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","minecraft:trapped_chest","mcwfences:spruce_horse_fence","croptopia:beef_jerky","handcrafted:oak_shelf","mcwbiomesoplenty:magic_modern_door","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","forbidden_arcanus:deorum_trapdoor","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","ad_astra:space_helmet","supplementaries:flags/flag_magenta","allthecompressed:compress/soul_soil_1x","minecraft:diamond_axe","mcwbridges:end_stone_bricks_bridge_pier","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","refinedstorage:coloring_recipes/lime_crafting_grid","refinedstorage:coloring_recipes/brown_crafting_monitor","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","forbidden_arcanus:stellarite_piece_from_stellarite_block","mcwroofs:lime_striped_awning","mcwbiomesoplenty:palm_tropical_door","additionallanterns:bricks_lantern","handcrafted:creeper_trophy","travelersbackpack:skeleton","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","mcwbiomesoplenty:palm_cottage_door","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","chemlib:indium_ingot_to_nugget","farmersdelight:stove","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","handcrafted:birch_side_table","packingtape:tape","refinedstorage:coloring_recipes/lime_detector","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","botania:petal_gray","occultism:blasting/iesnium_ingot_from_raw","cfm:birch_kitchen_counter","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","botania:petal_pink_double","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","xnet:antenna","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","dyenamics:persimmon_candle","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","refinedstorage:coloring_recipes/white_crafter_manager","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","refinedstorage:coloring_recipes/pink_crafting_monitor","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","forbidden_arcanus:smelting/rune_from_smelting","refinedstorage:coloring_recipes/pink_crafter_manager","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","mcwtrpdoors:jungle_ranch_trapdoor","securitycraft:secret_mangrove_hanging_sign","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","mcwfurnitures:acacia_double_wardrobe","pylons:expulsion_pylon","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","mythicbotany:yggdrasil_branch","mcwpaths:dark_prismarine_windmill_weave","alltheores:brass_rod","mcwroofs:red_terracotta_attic_roof","bloodmagic:arc","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","chemlib:strontium_nugget_to_ingot","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","utilitarian:no_soliciting/soliciting_carpets/light_blue_trapped_soliciting_carpet","create:crafting/kinetics/sequenced_gearshift","silentgear:tyrian_steel_dust","minecraft:torch","minecraft:polished_granite_stairs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","refinedstorage:coloring_recipes/blue_wireless_transmitter","create:crafting/kinetics/black_seat","refinedstorage:coloring_recipes/wireless_transmitter","cfm:oak_desk","allthecompressed:decompress/atm_star_block_2x","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwbiomesoplenty:dead_bark_glass_door","mcwbiomesoplenty:pine_mystic_door","mcwbiomesoplenty:fir_paper_door","chemlib:sodium_ingot_from_smelting_sodium_dust","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","minecraft:crafting_table","dyenamics:lavender_stained_glass","dyenamics:cherenkov_dye","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","cfm:dye_black_picket_gate","refinedstorage:coloring_recipes/red_controller","mcwbiomesoplenty:fir_japanese_door","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","create:crafting/logistics/stockpile_switch","mcwbridges:dry_bamboo_bridge_pier","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwfurnitures:stripped_acacia_bookshelf_drawer","ae2:network/cables/smart_gray","ae2:network/cables/dense_covered_white","refinedstorage:coloring_recipes/green_crafting_monitor","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","domum_ornamentum:brown_bricks","mcwwindows:oak_plank_four_window","cfm:red_cooler","forbidden_arcanus:edelwood_boat","securitycraft:reinforced_green_stained_glass_pane_from_dye","refinedstorage:coloring_recipes/white_fluid_grid","bigreactors:turbine/basic/blade","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","mekanismtools:osmium/tools/pickaxe","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","enderio:resetting_lever_five_from_inv","sophisticatedstorage:stack_upgrade_tier_1","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","cfm:acacia_desk_cabinet","ae2:network/cells/item_storage_cell_16k","ae2:decorative/quartz_fixture","mcwbiomesoplenty:dead_nether_door","cfm:acacia_blinds","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","mcwbiomesoplenty:dead_paper_door","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","occultism:crafting/raw_iesnium_block","littlelogistics:automatic_switch_rail","allthemodium:unobtainium_ingot_from_raw_smelting","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","forbidden_arcanus:clibano_combustion/coal_from_clibano_combusting","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","refinedstorage:coloring_recipes/light_gray_crafting_grid","nethersdelight:diamond_machete","utilitix:advanced_brewery","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_acacia_glass_table","bigreactors:reactor/reinforced/casing_recycle_glass","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwfurnitures:acacia_chair","mcwbiomesoplenty:palm_nether_door","expatternprovider:cobblestone_cell","refinedstorage:coloring_recipes/red_disk_manipulator","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:fir_western_door","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/yellow_grid","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","pneumaticcraft:tube_junction","bloodmagic:smelting/blasting_ingot_from_raw_hellforged","bambooeverything:bamboo_torch","dyenamics:honey_wool","mcwroofs:acacia_top_roof","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","mcwfurnitures:acacia_glass_table","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bloodmagic:alchemy_table","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","enderio:void_chassis","minecraft:compass","cfm:stripped_acacia_table","refinedstorage:coloring_recipes/cyan_grid","bloodmagic:blood_rune_speed","mcwbiomesoplenty:jacaranda_plank_pane_window","bloodmagic:path/path_wornstone","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","mcwbiomesoplenty:maple_mystic_door","immersiveengineering:crafting/armor_steel_helmet","mekanism:processing/osmium/raw/from_raw_block","expatternprovider:silicon_block","minecraft:item_frame","immersiveengineering:crafting/toolupgrade_railgun_scope","itemcollectors:basic_collector","minecraft:loom","enderio:resetting_lever_three_hundred_inv","ad_astra:steel_engine","minecraft:polished_granite_stairs_from_polished_granite_stonecutting","bloodmagic:blood_rune_capacity","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","sophisticatedstorage:stack_downgrade_tier_3","minecraft:arrow","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwbiomesoplenty:mahogany_stable_head_door","botania:red_string_comparator","cfm:pink_picket_fence","mcwroofs:pink_terracotta_steep_roof","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting","mcwbiomesoplenty:empyreal_stockade_fence","expatternprovider:ei_part","mcwroofs:gray_concrete_attic_roof","refinedstorage:coloring_recipes/black_crafting_grid","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","mekanismtools:osmium/armor/chestplate","refinedstorage:coloring_recipes/white_wireless_transmitter","computercraft:computer_advanced","minecraft:polished_blackstone","advanced_ae:quantumaccel","refinedstorage:coloring_recipes/blue_relay","bloodmagic:smelting/ingot_from_raw_hellforged","chemlib:potassium_ingot_to_nugget","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","railways:crafting/smokestack_woodburner","pneumaticcraft:logistics_frame_storage","mcwpaths:dark_prismarine_diamond_paving","refinedstorage:coloring_recipes/yellow_wireless_transmitter","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwpaths:dark_prismarine_flagstone","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","mcwroofs:stone_top_roof","bigreactors:turbine/reinforced/passivefluidport_forge","travelersbackpack:horse","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","cfm:brown_cooler","xnet:netcable_red","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","dyenamics:amber_candle","bigreactors:reprocessor/wasteinjector","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","mcwfurnitures:jungle_end_table","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwpaths:dark_prismarine_honeycomb_paving","minecraft:respawn_anchor","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","botania:petal_gray_double","dyenamics:persimmon_stained_glass_pane_from_glass_pane","mcwpaths:dark_prismarine_flagstone_path","expatternprovider:ebus_in","handcrafted:birch_corner_trim","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","sophisticatedstorage:shulker_box","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","refinedstorage:coloring_recipes/red_crafter_manager","refinedstorage:coloring_recipes/crafter","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","mcwbiomesoplenty:mahogany_modern_door","immersiveengineering:crafting/component_steel","refinedstorage:coloring_recipes/crafter_manager","utilitarian:utility/acacia_logs_to_boats","minecraft:dark_prismarine_stairs","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","refinedstorage:coloring_recipes/green_fluid_grid","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","sophisticatedbackpacks:compacting_upgrade","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cells/item_storage_cell_64k_storage","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","ae2:network/cables/covered_light_blue","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","ae2:decorative/quartz_vibrant_glass","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:jukebox","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_barn_glass_door","cfm:acacia_chair","handcrafted:birch_dining_bench","mcwwindows:oak_window","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","mcwbiomesoplenty:stripped_magic_pane_window","chemlib:lead_ingot_from_blasting_lead_dust","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","appmek:chemical_storage_cell_16k","ae2:network/cables/smart_brown","create:crafting/kinetics/white_seat","chemlib:gallium_ingot_to_block","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","ae2:network/cables/covered_orange","mcwroofs:stone_bricks_steep_roof","mcwwindows:red_curtain","mcwtrpdoors:oak_mystic_trapdoor","create:crafting/logistics/brass_funnel","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","refinedstorage:coloring_recipes/white_network_transmitter","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","refinedstorage:coloring_recipes/black_disk_manipulator","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","utilitarian:utility/oak_logs_to_doors","railways:crafting/smokestack_diesel","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","mcwdoors:jungle_whispering_door","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","refinedstorage:coloring_recipes/cyan_crafter","mcwlights:bamboo_tiki_torch","silentgear:azure_electrum_dust","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","create:crafting/logistics/display_link","ad_astra:airlock","mcwroofs:grass_steep_roof","cfm:pink_picket_gate","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","chemlib:magnesium_ingot_from_blasting_magnesium_dust","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","croptopia:egg_roll","mcwbiomesoplenty:dead_waffle_door","twigs:bamboo_mat","connectedglass:borderless_glass_black2","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","minecraft:red_stained_glass_pane_from_glass_pane","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","ae2:network/cells/item_storage_cell_1k_storage","mcwfurnitures:stripped_acacia_drawer_counter","alltheores:lead_ingot_from_ore_blasting","expatternprovider:oversize_interface_alt","twigs:silt_brick","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","refinedstorage:coloring_recipes/black_network_transmitter","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","mcwbiomesoplenty:pine_bamboo_door","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","minecraft:orange_candle","supplementaries:flags/flag_purple","create:framed_glass_from_glass_colorless_stonecutting","botania:quartz_sunny","mcwbiomesoplenty:stripped_redwood_log_window","mcwfurnitures:stripped_acacia_cupboard_counter","mcwbiomesoplenty:dead_swamp_door","handcrafted:birch_chair","chemlib:lead_ingot_from_smelting_lead_dust","enderio:infinity_rod","railcraft:signal_lamp","refinedstorage:coloring_recipes/orange_crafter_manager","cfm:jungle_blinds","tombstone:bone_needle","minecraft:stone_brick_wall","forbidden_arcanus:deorum_pressure_plate","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","refinedstorage:coloring_recipes/pink_relay","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:decoration_table","mcwbiomesoplenty:redwood_classic_door","forbidden_arcanus:deorum_chain","chemlib:manganese_ingot_from_blasting_manganese_dust","mcwbiomesoplenty:stripped_redwood_pane_window","chemlib:barium_ingot_to_block","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","delightful:knives/draco_arcanus_knife","pneumaticcraft:paper_from_tag_filter","refinedstorage:coloring_recipes/black_wireless_transmitter","utilitarian:utility/glow_ink_sac","mcwwindows:acacia_window2","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwroofs:white_top_roof","mcwwindows:blackstone_window2","mcwbiomesoplenty:magic_waffle_door","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","botania:floating_pure_daisy","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","mcwbiomesoplenty:redwood_cottage_door","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","ae2:network/cables/dense_smart_cyan","alltheores:silver_ingot_from_ore_blasting","cfm:magenta_picket_fence","travelersbackpack:dye_orange_sleeping_bag","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","cfm:acacia_bedside_cabinet","securitycraft:secret_mangrove_sign_item","mcwdoors:oak_stable_door","refinedstorage:coloring_recipes/orange_disk_manipulator","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","twilightforest:minoshroom_banner_pattern","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwroofs:light_gray_terracotta_roof","refinedstorage:coloring_recipes/red_fluid_grid","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","forbidden_arcanus:golden_orchid_seeds","dyenamics:fluorescent_concrete_powder","botania:incense_plate","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwpaths:dark_prismarine_square_paving","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:lime_terracotta_steep_roof","twilightforest:canopy_chest_boat","mcwlights:golden_small_chandelier","bambooeverything:dry_bamboo","mcwbiomesoplenty:willow_paper_door","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","refinedstorage:coloring_recipes/pink_crafter","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwpaths:dark_prismarine_running_bond","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","ae2:network/parts/panels_semi_dark_monitor","botania:lime_petal_block","allthemodium:unobtainium_rod","bloodmagic:blood_rune_capacity_2","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","refinedstorage:coloring_recipes/cyan_crafting_grid","mcwbiomesoplenty:redwood_swamp_door","farmersdelight:cooking/baked_cod_stew","immersiveengineering:crafting/wirecoil_structure_rope","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","alltheores:lead_rod","mcwbiomesoplenty:jacaranda_western_door","mcwwindows:oak_plank_window","refinedstorage:coloring_recipes/blue_controller","nethersdelight:hoglin_sirloin","mcwwindows:oak_log_parapet","mcwbiomesoplenty:hellbark_modern_door","refinedstorage:coloring_recipes/yellow_crafting_monitor","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","mcwfurnitures:stripped_acacia_modern_wardrobe","travelersbackpack:spider_smithing","utilitarian:no_soliciting/soliciting_carpets/yellow_trapped_soliciting_carpet","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","minecraft:iron_block","refinedstorage:coloring_recipes/security_manager","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","ae2:network/cells/item_storage_components_cell_64k_part","mcwdoors:acacia_beach_door","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwroofs:black_terracotta_steep_roof","mcwbridges:bamboo_bridge_pier","minecraft:golden_apple","minecraft:diamond_pickaxe","domum_ornamentum:blockbarreldeco_onside","create:crafting/schematics/empty_schematic","silentgear:tyrian_steel_ingot_from_block","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","mcwroofs:orange_terracotta_upper_steep_roof","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","aether:iron_helmet_repairing","ae2:network/cables/smart_purple","farmersdelight:steak_and_potatoes","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","utilitarian:utility/acacia_logs_to_stairs","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","cfm:acacia_coffee_table","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","refinedstorage:coloring_recipes/magenta_crafter_manager","allthecompressed:compress/honey_block_1x","allthemodium:vibranium_plate","securitycraft:codebreaker","rftoolsutility:energy_module","supplementaries:flute","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","cfm:light_blue_picket_gate","securitycraft:reinforced_glass_pane","constructionwand:infinity_wand","farmersdelight:golden_knife","minecraft:orange_stained_glass","bloodmagic:blood_rune_speed_2","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","minecraft:redstone_from_smelting_redstone_ore","mcwroofs:black_roof","securitycraft:reinforced_gray_stained_glass_pane_from_glass","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","utilitarian:no_soliciting/soliciting_carpets/brown_trapped_soliciting_carpet","cfm:blue_kitchen_counter","mcwbiomesoplenty:maple_four_window","dyenamics:spring_green_stained_glass_pane_from_glass_pane","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","chemlib:magnesium_nugget_to_ingot","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwbridges:end_stone_bricks_bridge_stair","mcwroofs:base_top_roof","mcwdoors:bamboo_classic_door","ae2:network/cables/smart_magenta","securitycraft:harming_module","minecraft:golden_boots","silentgear:bort_block","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","refinedstorage:coloring_recipes/green_security_manager","mythicbotany:mana_collector","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","mcwpaths:dark_prismarine_crystal_floor_path","supplementaries:statue","immersiveengineering:crafting/tinted_glass_lead_wire","mcwbiomesoplenty:mahogany_tropical_door","create:crafting/kinetics/clockwork_bearing","cfm:pink_grill","minecraft:polished_granite_slab","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","forbidden_arcanus:arcane_edelwood_planks","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","securitycraft:secret_jungle_sign_item","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","ae2:network/crafting/cpu_crafting_accelerator","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","refinedstorage:coloring_recipes/black_crafter","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","dimstorage:dimensional_tablet","ad_astra:small_orange_industrial_lamp","rftoolsbase:machine_base","mcwbiomesoplenty:maple_stable_head_door","rftoolsutility:counterplus_module","mcwroofs:white_terracotta_upper_steep_roof","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_piston","cfm:purple_kitchen_counter","securitycraft:reinforced_yellow_stained_glass","minecraft:stick_from_bamboo_item","botania:swap_ring","mcwwindows:jungle_log_parapet","refinedstorage:coloring_recipes/pink_pattern_grid","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","mcwfurnitures:acacia_double_drawer","aquaculture:tin_can_to_iron_nugget","alltheores:enderium_plate","mcwwindows:yellow_curtain","connectedglass:clear_glass_orange_pane2","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","refinedstorage:coloring_recipes/gray_wireless_transmitter","allthecompressed:compress/acacia_log_1x","forbidden_arcanus:edelwood_fence","pneumaticcraft:pressure_chamber_wall","modularrouters:player_module","minecraft:glass","xnet:connector_yellow","mcwbiomesoplenty:pine_classic_door","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:end_stone_brick_stairs","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","alltheores:raw_iridium_block","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwbiomesoplenty:pine_cottage_door","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","ae2additions:components/super/64k","ae2:network/cables/smart_red","create:crafting/kinetics/nixie_tube","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","minecraft:light_blue_concrete_powder","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","refinedstorage:coloring_recipes/cyan_crafting_monitor","mcwwindows:light_blue_mosaic_glass","appmek:chemical_storage_cell_64k","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","refinedstorage:coloring_recipes/gray_crafting_monitor","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","chimes:bamboo_chimes","securitycraft:secret_spruce_sign_item","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwbiomesoplenty:pine_nether_door","cfm:oak_upgraded_fence","mob_grinding_utils:recipe_mob_swab","mcwbiomesoplenty:hellbark_barn_glass_door","deepresonance:radiation_monitor","railcraft:steel_gear","immersiveengineering:crafting/treated_wood_horizontal","allthemodium:piglich_heart_block","sophisticatedstorage:crafting_upgrade","bigreactors:smelting/graphite_from_charcoal","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","mcwroofs:green_concrete_top_roof","refinedstorage:coloring_recipes/orange_crafter","connectedglass:tinted_borderless_glass_orange2","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","minecraft:dye_orange_wool","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","utilitarian:utility/acacia_logs_to_slabs","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","mcwbiomesoplenty:jacaranda_bamboo_door","botania:quartz_lavender","occultism:blasting/iesnium_ingot","chemlib:beryllium_ingot_to_block","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","reliquary:glowing_water_from_potion_vial","cfm:yellow_kitchen_drawer","twilightforest:dark_boat","biomesoplenty:umbran_boat","minecraft:gunpowder","travelersbackpack:bookshelf","bloodmagic:blood_rune_acceleration_2","immersiveengineering:crafting/wire_lead","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","alltheores:tin_ingot_from_ore_blasting","mcwbiomesoplenty:redwood_four_panel_door","twilightforest:alpha_yeti_banner_pattern","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","refinedstorage:coloring_recipes/orange_crafting_grid","pylons:potion_filter","enderio:resetting_lever_thirty","minecraft:glistering_melon_slice","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","refinedstorage:coloring_recipes/green_network_receiver","refinedstorage:coloring_recipes/red_wireless_transmitter","handcrafted:quartz_corner_trim","ad_astra:steel_factory_block","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","refinedstorage:coloring_recipes/light_gray_crafter_manager","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","mcwroofs:light_gray_concrete_lower_roof","supplementaries:candle_holders/candle_holder_black_dye","twigs:rhyolite_slab","occultism:crafting/spirit_torch","chemlib:potassium_nugget_to_ingot","nethersdelight:soul_compost_from_hoglin_hide","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:reinforced_bricks_from_tile","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwfurnitures:acacia_table","mcwroofs:lime_terracotta_upper_steep_roof","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","mcwwindows:cyan_curtain","ae2:network/cables/smart_yellow","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","allthecompressed:compress/tuff_1x","mcwtrpdoors:bamboo_blossom_trapdoor","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","mcwbiomesoplenty:fir_tropical_door","securitycraft:secret_sign_item","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","aquaculture:heavy_hook","handcrafted:birch_fancy_bed","bloodmagic:hellforged_block_to_ingot","mcwbiomesoplenty:palm_western_door","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","mcwwindows:acacia_window","refinedstorage:coloring_recipes/green_controller","croptopia:food_press","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","mcwwindows:stripped_crimson_stem_four_window","utilitix:spruce_shulker_boat_with_shell","immersiveengineering:crafting/powerpack","advanced_ae:import_export_bus","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","mcwpaths:dark_prismarine_running_bond_stairs","ae2:network/cables/glass_green","mcwbiomesoplenty:willow_classic_door","ae2:tools/fluix_hoe","mcwroofs:orange_concrete_lower_roof","farmersdelight:nether_salad","cfm:magenta_kitchen_counter","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","mcwlights:orange_lamp","pneumaticcraft:reinforced_brick_pillar","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","mcwwindows:dark_oak_pane_window","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","create:tuff_pillar_from_stone_types_tuff_stonecutting","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","refinedstorage:coloring_recipes/white_controller","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","mcwdoors:acacia_swamp_door","minecraft:dark_prismarine_slab_from_dark_prismarine_stonecutting","aquaculture:fishing_line","forbidden_arcanus:obsidian_skull","cfm:stripped_jungle_kitchen_sink_dark","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","dyenamics:wine_concrete_powder","railcraft:charge_terminal","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:end_stone_brick_slab","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","minecraft:cauldron","botania:glimmering_livingwood_from_log","delightful:smelting/roasted_acorn","mcwpaths:dark_prismarine_crystal_floor_stairs","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","silentgear:blueprint_paper","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","mcwdoors:bamboo_beach_door","ae2:block_cutter/walls/smooth_sky_stone_wall","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","cfm:cyan_kitchen_sink","dyenamics:peach_concrete_powder","mcwbiomesoplenty:dead_beach_door","sophisticatedbackpacks:refill_upgrade","ae2additions:components/super/4k","supplementaries:faucet","chemlib:gallium_ingot_from_smelting_gallium_dust","utilitix:directional_rail","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","mcwfurnitures:stripped_acacia_covered_desk","minecraft:popped_chorus_fruit","mcwbiomesoplenty:fir_barn_glass_door","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","alltheores:enderium_rod","corail_woodcutter:cherry_woodcutter","handcrafted:birch_bench","refinedstorage:coloring_recipes/blue_security_manager","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwroofs:stone_bricks_top_roof","mcwdoors:bamboo_bark_glass_door","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","cfm:magenta_trampoline","supplementaries:pulley","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","chemlib:zirconium_ingot_from_blasting_zirconium_dust","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","refinedstorage:coloring_recipes/gray_crafting_grid","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","silentgear:azure_repair_kit","refinedstorage:coloring_recipes/lime_security_manager","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","farmersdelight:cooking/mushroom_rice","refinedstorage:coloring_recipes/gray_crafter","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","ae2:network/cells/item_storage_cell_4k","mcwroofs:brown_terracotta_top_roof","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","dyenamics:navy_dye","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwbiomesoplenty:hellbark_stable_head_door","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:jacaranda_swamp_door","mcwroofs:grass_lower_roof","refinedstorage:coloring_recipes/gray_network_transmitter","sfm:fancy_cable","refinedstorage:coloring_recipes/magenta_pattern_grid","mcwroofs:gutter_base_red","mcwtrpdoors:acacia_glass_trapdoor","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","mcwdoors:print_bamboo","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","refinedstorage:coloring_recipes/yellow_crafting_grid","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","bigreactors:crafting/blutonium_ingot_to_nugget","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","mcwfurnitures:acacia_modern_desk","sophisticatedbackpacks:chipped/carpenters_table_upgrade","minecraft:copper_ingot_from_smelting_raw_copper","create:crafting/kinetics/mechanical_arm","mcwbiomesoplenty:stripped_maple_log_window2","advanced_ae:stock_export_bus","cfm:dye_black_picket_fence","chemlib:zirconium_ingot_to_block","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","minecraft:netherite_ingot","naturalist:bug_net","minecraft:birch_sign","botania:dye_lime","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_3","mcwbiomesoplenty:jacaranda_mystic_door","sophisticatedstorage:oak_limited_barrel_4","dyenamics:navy_terracotta","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","mcwfurnitures:acacia_end_table","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","refinedstorage:coloring_recipes/green_disk_manipulator","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","railcraft:bushing_gear_brass","refinedstorage:coloring_recipes/lime_grid","ae2:tools/portable_item_cell_16k","enderio:basic_capacitor","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","railcraft:steel_tank_valve","cfm:cyan_cooler","ae2:network/parts/formation_plane","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwdoors:acacia_modern_door","ad_astra:steel_rod","mcwbiomesoplenty:mahogany_mystic_door","mcwwindows:prismarine_four_window","occultism:smelting/iesnium_ingot","mcwroofs:light_blue_concrete_top_roof","refinedstorage:coloring_recipes/magenta_wireless_transmitter","mcwroofs:light_gray_roof","securitycraft:secret_jungle_hanging_sign","mcwbiomesoplenty:umbran_beach_door","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","dyenamics:bubblegum_candle","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:umbran_bark_glass_door","twilightforest:wood/dark_wood","mcwdoors:bamboo_tropical_door","connectedglass:scratched_glass_orange2","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","travelersbackpack:redstone","mcwfurnitures:stripped_acacia_end_table","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","minecraft:slime_block","minecraft:acacia_planks","twilightforest:magic_map_focus","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","dyenamics:lavender_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:fir_nether_door","chimes:glass_bells","mcwwindows:iron_shutter","mcwroofs:birch_planks_top_roof","refinedstorage:coloring_recipes/purple_grid","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","aether:diamond_chestplate_repairing","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","bigreactors:turbine/basic/casing","travelersbackpack:emerald","farmersdelight:cutting_board","croptopia:trail_mix","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","bloodmagic:smelting/ingot_netherite_scrap","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","bloodmagic:blood_rune_sac_2","ad_astra:nasa_workbench","cfm:birch_crate","ad_astra:etrionic_capacitor","mcwbiomesoplenty:empyreal_japanese2_door","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwbridges:blackstone_bridge_pier","modularrouters:flinger_module","mcwbiomesoplenty:empyreal_swamp_door","ae2:tools/nether_quartz_axe","mcwroofs:birch_planks_lower_roof","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/pink_trapped_soliciting_carpet","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","enderio:resetting_lever_sixty_inv","railcraft:steel_shovel","mcwpaths:blackstone_windmill_weave_path","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","minecraft:magenta_stained_glass_pane_from_glass_pane","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:magenta_terracotta_attic_roof","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","mcwfurnitures:stripped_acacia_coffee_table","alltheores:copper_rod","minecraft:shulker_box","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","ae2:network/cables/covered_blue","dyenamics:maroon_dye","utilitix:filter_rail","aether:diamond_gloves","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","enderio:resetting_lever_sixty_from_inv","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","mcwbiomesoplenty:dead_stable_door","minecraft:cracked_stone_bricks","securitycraft:secret_birch_sign_item","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","allthecompressed:compress/vibranium_block_1x","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","arseng:source_storage_cell_4k","refinedstorage:coloring_recipes/black_pattern_grid","ae2:network/cells/fluid_storage_cell_4k","minecraft:magenta_stained_glass","minecraft:ender_chest","refinedstorage:coloring_recipes/lime_crafter_manager","mcwbiomesoplenty:willow_western_door","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","ae2:network/cables/dense_covered_yellow","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","mcwpaths:cobbled_deepslate_running_bond_path","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","modularrouters:void_module","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","immersiveengineering:crafting/redstone_acid","dankstorage:2_to_3","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","enderio:vibrant_capacitor_bank","refinedstorage:coloring_recipes/blue_detector","allthemodium:vibranium_ingot_from_raw_smelting","refinedstorage:coloring_recipes/lime_controller","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","chemlib:manganese_ingot_to_block","biomesoplenty:maple_boat","bigreactors:reactor/reinforced/chargingfe","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","aether:golden_boots_repairing","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","travelersbackpack:nether","mcwbiomesoplenty:redwood_plank_pane_window","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","securitycraft:reinforced_warped_fence_gate","mcwfurnitures:acacia_bookshelf","refinedstorage:coloring_recipes/cyan_fluid_grid","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","refinedstorage:coloring_recipes/yellow_network_transmitter","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","caupona:clay_cistern","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","forbidden_arcanus:edelwood_pressure_plate","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","deepresonance:lens","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","refinedstorage:coloring_recipes/green_crafter_manager","refinedstorage:coloring_recipes/light_gray_pattern_grid","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","occultism:smelting/iesnium_ingot_from_dust","bigreactors:turbine/reinforced/controller","minecraft:redstone_lamp","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/yellow_pattern_grid","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","cfm:stripped_acacia_crate","mcwbiomesoplenty:dead_barn_glass_door","allthecompressed:compress/cobbled_deepslate_1x","twilightdelight:berry_stick","handcrafted:terracotta_bowl","silentgear:stone_torch","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","ae2:tools/fluix_axe","cfm:black_kitchen_sink","mcwwindows:red_mosaic_glass","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwfurnitures:stripped_acacia_double_wardrobe","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwbiomesoplenty:pine_western_door","utilitarian:utility/acacia_logs_to_trapdoors","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","modularrouters:energy_output_module","mcwbiomesoplenty:hellbark_japanese_door","mcwlights:glowstone_slab","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","appbot:mana_storage_cell_64k","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","aether:cold_parachute","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","minecraft:polished_blackstone_pressure_plate","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","securitycraft:secret_dark_oak_hanging_sign","alltheores:diamond_rod","mcwbiomesoplenty:palm_stable_door","minecraft:stone_slab_from_stone_stonecutting","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","refinedstorage:coloring_recipes/lime_pattern_grid","mcwwindows:bamboo_shutter","chemlib:sodium_nugget_to_ingot","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","mcwbiomesoplenty:palm_four_panel_door","ae2:network/crafting/64k_cpu_crafting_storage","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","create:crafting/kinetics/placard","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:birch_button","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","refinedstorage:coloring_recipes/green_relay","ae2:network/cables/dense_smart_brown","forbidden_arcanus:blasting/arcane_crystal_from_blasting","mcwbiomesoplenty:dead_hedge","botania:petal_pink","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","minecraft:honey_bottle","immersiveengineering:crafting/chute_copper","pneumaticcraft:charging_station","mcwfences:prismarine_grass_topped_wall","occultism:crafting/book_of_calling_foliot_transport_items","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwdoors:bamboo_barn_glass_door","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","chemlib:indium_ingot_from_smelting_indium_dust","mcwbiomesoplenty:fir_glass_door","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","alltheores:zinc_ingot_from_ore_blasting","create:crafting/kinetics/crafter_slot_cover","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","minecraft:netherite_machete_smithing","mcwbridges:balustrade_blackstone_bridge","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","refinedstorage:coloring_recipes/purple_relay","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","refinedstorage:coloring_recipes/light_gray_relay","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","supplementaries:sign_post_birch","securitycraft:secret_acacia_hanging_sign","minecraft:quartz_block","alltheores:iron_dust_from_hammer_crushing","minecraft:brick","immersiveengineering:crafting/toolupgrade_railgun_capacitors","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","mcwroofs:lime_concrete_top_roof","mcwroofs:purple_terracotta_attic_roof","bloodmagic:blood_rune_charging_2","utilitarian:no_soliciting/soliciting_carpets/magenta_trapped_soliciting_carpet","mcwroofs:orange_terracotta_lower_roof","refinedstorage:coloring_recipes/cyan_network_receiver","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwbiomesoplenty:mahogany_bamboo_door","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwroofs:brown_terracotta_upper_steep_roof","mcwdoors:oak_swamp_door","enderio:resetting_lever_sixty_from_prev","aether:iron_axe_repairing","mcwwindows:mud_brick_arrow_slit","botania:dye_gray","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","alltheores:electrum_ingot_from_dust_blasting","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","refinedstorage:coloring_recipes/cyan_crafter_manager","refinedstorage:coloring_recipes/gray_controller","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","mcwbiomesoplenty:willow_mystic_door","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","mcwbiomesoplenty:pine_modern_door","handcrafted:wither_skeleton_trophy","aether:wooden_hoe_repairing","mcwroofs:blue_terracotta_top_roof","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","minecraft:brown_dye","mcwtrpdoors:acacia_mystic_trapdoor","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","handcrafted:terracotta_medium_pot","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:umbran_paper_door","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","refinedstorage:coloring_recipes/gray_disk_manipulator","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","mcwroofs:gray_concrete_upper_steep_roof","botania:fel_pumpkin","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwbiomesoplenty:empyreal_stable_door","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","ae2:network/cells/fluid_storage_cell_64k_storage","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","refinedstorage:coloring_recipes/purple_security_manager","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","ae2:network/cells/fluid_storage_cell_16k","mcwroofs:pink_concrete_lower_roof","railcraft:diamond_spike_maul","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","allthetweaks:greg_star_from_gregstar_block","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwroofs:gray_roof_block","mcwbiomesoplenty:fir_plank_four_window","botania:mushroom_11","cfm:white_kitchen_drawer","ae2:network/crafting/1k_cpu_crafting_storage","botania:mushroom_10","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","ae2:network/cables/smart_pink","mcwbiomesoplenty:pine_horse_fence","refinedstorage:coloring_recipes/green_detector","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","create:smelting/zinc_ingot_from_ore","mcwwindows:acacia_four_window","chemlib:potassium_ingot_from_blasting_potassium_dust","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","mcwdoors:bamboo_nether_door","mcwbiomesoplenty:hellbark_window","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","refinedstorage:coloring_recipes/light_gray_security_manager","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","reliquary:witherless_rose","dyenamics:honey_stained_glass","cfm:stripped_acacia_cabinet","create:crafting/kinetics/elevator_pulley","mcwfences:sandstone_grass_topped_wall","alltheores:invar_rod","allthemodium:vibranium_ingot_from_dust_smelting","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","twilightforest:compressed_blocks/carminite_block","mcwfurnitures:oak_bookshelf_cupboard","travelersbackpack:pumpkin","create:crafting/kinetics/attribute_filter","supplementaries:key","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","allthemodium:unobtainium_ingot_from_dust_smelting","blue_skies:maple_bookshelf","rftoolspower:power_core1","pneumaticcraft:logistics_frame_default_storage","ae2:network/blocks/io_port","mcwpaths:blackstone_windmill_weave_stairs","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","refinedstorage:coloring_recipes/light_gray_detector","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","mcwbiomesoplenty:redwood_window2","refinedstorage:coloring_recipes/purple_detector","create:crafting/kinetics/purple_seat","twilightforest:equipment/fiery_iron_sword","mcwfurnitures:stripped_acacia_stool_chair","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","botania:mana_pylon","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","silentgear:crimson_repair_kit","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","chemlib:zirconium_ingot_to_nugget","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","mekanismtools:osmium/armor/boots","simplylight:illuminant_red_block_on_toggle","cfm:acacia_table","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwtrpdoors:bamboo_barn_trapdoor","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","alltheores:platinum_ingot_from_ore","refinedstorage:coloring_recipes/brown_disk_manipulator","mcwtrpdoors:jungle_mystic_trapdoor","mcwbiomesoplenty:dead_barn_door","refinedstorage:coloring_recipes/network_transmitter","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","mcwtrpdoors:jungle_glass_trapdoor","chemlib:indium_ingot_from_blasting_indium_dust","securitycraft:reinforced_oak_fence","railcraft:feed_station","minecraft:black_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","arseng:source_storage_cell_64k","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","immersiveengineering:crafting/plate_uranium_hammering","refinedstorage:coloring_recipes/cyan_disk_manipulator","mcwdoors:bamboo_paper_door","handcrafted:oak_nightstand","railcraft:personal_world_spike","immersiveengineering:crafting/armor_steel_chestplate","refinedstorage:coloring_recipes/light_gray_crafter","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","refinedstorage:coloring_recipes/green_pattern_grid","mysticalagriculture:prudentium_block_combine","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","mcwfurnitures:acacia_modern_wardrobe","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","expatternprovider:water_cell","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","refinedstorage:coloring_recipes/magenta_fluid_grid","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","minecraft:dark_prismarine_stairs_from_dark_prismarine_stonecutting","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:fir_four_panel_door","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","handcrafted:birch_couch","farmersdelight:cooking/fish_stew","supplementaries:slingshot","allthemodium:allthemodium_plate","advanced_ae:quantumunit","mcwdoors:acacia_japanese2_door","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","travelersbackpack:dye_black_sleeping_bag","minecraft:activator_rail","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","everythingcopper:copper_rail","refinedstorage:coloring_recipes/brown_crafting_grid","cfm:black_grill","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","cfm:birch_desk_cabinet","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_four_panel_door","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","cfm:crimson_kitchen_drawer","travelersbackpack:end","chemlib:strontium_block_to_ingot","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","dyenamics:rose_candle","aquaculture:neptunium_ingot_from_nuggets","minecraft:iron_pickaxe","aether:shield_repairing","refinedstorage:coloring_recipes/light_gray_wireless_transmitter","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","bigreactors:crafting/cyanite_storage_to_component","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","ae2:network/cables/glass_orange","deepresonance:resonating_plate","minecraft:vibranium_mage_chestplate_smithing","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","reliquary:angelheart_vial","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","mcwbiomesoplenty:redwood_stable_head_door","dyenamics:aquamarine_candle","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwtrpdoors:bamboo_classic_trapdoor","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwbiomesoplenty:empyreal_western_door","mcwroofs:black_concrete_roof","occultism:crafting/lenses","cfm:orange_cooler","chemlib:strontium_ingot_to_nugget","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwtrpdoors:bamboo_barred_trapdoor","mcwbiomesoplenty:empyreal_japanese_door","mcwroofs:acacia_lower_roof","dyenamics:mint_candle","mcwbridges:balustrade_mossy_stone_bricks_bridge","expatternprovider:epp_part","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","refinedstorage:coloring_recipes/brown_wireless_transmitter","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:hellbark_paper_door","cfm:white_kitchen_counter","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","mcwfurnitures:acacia_coffee_table","alltheores:enderium_ingot_from_dust","ae2:network/cables/covered_fluix_clean","mcwbiomesoplenty:umbran_stable_door","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwpaths:dark_prismarine_running_bond_slab","mcwbiomesoplenty:willow_stable_head_door","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","trashcans:energy_trash_can","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","pneumaticcraft:air_compressor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","megacells:crafting/decompression_module","mcwdoors:bamboo_modern_door","minecolonies:baked_salmon","cfm:fridge_dark","chimes:copper_chimes","handcrafted:birch_desk","minecraft:birch_fence","mcwbiomesoplenty:willow_window2","silentgear:azure_electrum_ingot_from_nugget","mcwbiomesoplenty:stripped_mahogany_log_four_window","create:crafting/kinetics/brass_door","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","chemlib:sodium_ingot_to_block","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","twigs:weeping_polished_blackstone_bricks","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","expatternprovider:wireless_connector","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","alltheores:steel_dust_from_alloy_blending","forbidden_arcanus:clibano_combustion/arcane_crystal_from_clibano_combusting","mcwroofs:black_terracotta_lower_roof","immersiveengineering:crafting/radiator","minecraft:white_stained_glass_pane_from_glass_pane","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","ae2:network/cables/dense_covered_fluix_clean","littlelogistics:switch_rail","minecraft:scaffolding","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwbiomesoplenty:fir_cottage_door","nethersdelight:blackstone_furnace","mcwroofs:purple_concrete_attic_roof","constructionwand:core_angel","mcwfurnitures:acacia_bookshelf_cupboard","enderio:resetting_lever_thirty_from_prev","sophisticatedstorage:birch_chest","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","utilitarian:no_soliciting/soliciting_carpets/light_gray_trapped_soliciting_carpet","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwbiomesoplenty:willow_glass_door","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","delightful:knives/tin_knife","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwlights:copper_candle_holder","minecraft:mossy_stone_brick_slab","mcwtrpdoors:bamboo_swamp_trapdoor","mcwfurnitures:acacia_covered_desk","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:bamboo_chest_raft","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","minecraft:honey_block","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","farmersdelight:melon_juice","sophisticatedbackpacks:crafting_upgrade","twilightdelight:cooking/fiery_snakes_block","aquaculture:birch_fish_mount","bigreactors:crafting/cyanite_component_to_storage","mcwwindows:stripped_mangrove_log_window","securitycraft:secret_crimson_hanging_sign","pneumaticcraft:standby_upgrade","buildinggadgets2:template_manager","occultism:crafting/iesnium_ingot_from_block","mcwwindows:jungle_window","minecraft:cobblestone_stairs","refinedstorage:coloring_recipes/purple_fluid_grid","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","cfm:birch_table","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","cfm:stripped_acacia_coffee_table","mcwbiomesoplenty:empyreal_barn_glass_door","tombstone:ankh_of_prayer","cfm:white_sofa","refinedstorage:coloring_recipes/red_crafting_monitor","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","utilitix:dark_oak_shulker_boat_with_shell","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","allthecompressed:compress/uranium_block_1x","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","refinedstorage:coloring_recipes/blue_grid","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","connectedglass:tinted_borderless_glass_black2","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","allthecompressed:compress/melon_1x","pneumaticcraft:heat_sink","mcwbiomesoplenty:stripped_fir_log_four_window","mcwlights:magenta_lamp","nethersdelight:hoglin_sirloin_from_smoking","chemlib:gallium_ingot_to_nugget","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","mcwbiomesoplenty:maple_swamp_door","refinedstorage:coloring_recipes/blue_crafter_manager","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","sophisticatedstorage:stonecutter_upgrade","utilitarian:utility/oak_logs_to_pressure_plates","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","handcrafted:oak_bench","mcwbiomesoplenty:maple_bark_glass_door","bigreactors:turbine/basic/controller","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwpaths:dark_prismarine_flagstone_stairs","refinedstorage:coloring_recipes/yellow_detector","mcwwindows:birch_louvered_shutter","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","minecraft:red_concrete_powder","mcwbiomesoplenty:maple_stable_door","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","chemlib:magnesium_ingot_to_block","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwpaths:blackstone_crystal_floor_path","mcwbiomesoplenty:empyreal_glass_door","cfm:jungle_cabinet","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwtrpdoors:acacia_beach_trapdoor","mcwroofs:base_roof_block","handcrafted:birch_pillar_trim","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","twigs:blackstone_column","mcwbiomesoplenty:palm_window","mcwfurnitures:acacia_lower_bookshelf_drawer","botania:conversions/pink_petal_block_deconstruct","mcwbiomesoplenty:fir_waffle_door","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:navy_candle","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","minecraft:polished_granite_slab_from_polished_granite_stonecutting","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","securitycraft:reinforced_orange_stained_glass_pane_from_dye","cfm:jungle_chair","chemlib:barium_block_to_ingot","mcwlights:birch_tiki_torch","twilightforest:equipment/fiery_iron_pickaxe","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","mcwbiomesoplenty:pine_beach_door","twilightforest:time_chest_boat","create:crafting/logistics/content_observer","refinedstorage:coloring_recipes/gray_network_receiver","refinedstorage:coloring_recipes/green_wireless_transmitter","sophisticatedbackpacks:chipped/glassblower_upgrade","utilitix:acacia_shulker_boat_with_shell","allthecompressed:compress/atm_star_block_3x","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:nether_bricks","enderio:resetting_lever_three_hundred_inv_from_prev","allthecompressed:compress/birch_planks_1x","refinedstorage:coloring_recipes/white_network_receiver","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","twilightforest:naga_banner_pattern","croptopia:tuna_roll","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","mcwpaths:blackstone_square_paving","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","mcwdoors:garage_gray_door","railcraft:world_spike","handcrafted:witch_trophy","mcwfurnitures:stripped_oak_counter","ae2:network/cells/item_storage_components_cell_256k_part","minecraft:polished_andesite_from_andesite_stonecutting","allthearcanistgear:vibranium_robes_smithing","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","refinedstorage:coloring_recipes/orange_fluid_grid","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","minecraft:oak_pressure_plate","botania:mushroom_5","ae2:misc/chests_smooth_sky_stone","botania:mushroom_4","minecraft:stone_brick_stairs","aether:skyroot_beehive","bigreactors:turbine/basic/activefluidport_forge","botania:mushroom_3","refinedstorage:coloring_recipes/red_network_transmitter","bigreactors:reprocessor/outputport","cfm:brown_grill","mcwroofs:gray_roof","mcwwindows:quartz_window","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","supplementaries:present","bloodmagic:blood_rune_orb","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","refinedstorage:coloring_recipes/purple_wireless_transmitter","mcwroofs:gutter_base_brown","cfm:acacia_crate","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","botania:glimmering_livingwood_log","mcwbiomesoplenty:redwood_tropical_door","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","dyenamics:peach_candle","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","securitycraft:reinforced_chiseled_bookshelf","utilitix:mangrove_shulker_boat_with_shell","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","refinedstorage:coloring_recipes/magenta_crafter","minecraft:clock","mcwroofs:red_concrete_top_roof","mcwroofs:birch_planks_attic_roof","securitycraft:portable_tune_player","utilitarian:no_soliciting/soliciting_carpets/black_trapped_soliciting_carpet","enderio:entity_filter","mcwroofs:birch_planks_upper_steep_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","railcraft:lead_gear","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","allthetweaks:greg_star_block","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","silentgear:diamond_shard","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","cfm:lime_picket_fence","aether:leather_leggings_repairing","dyenamics:bed/conifer_bed","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:peach_stained_glass","dyenamics:bed/amber_bed","mcwdoors:acacia_western_door","minecraft:diamond_leggings","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwbiomesoplenty:maple_paper_door","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","silentgear:leather_scrap","mcwroofs:purple_terracotta_roof","alltheores:tin_rod","connectedglass:scratched_glass_orange_pane2","mcwbridges:rope_birch_bridge","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","aether:stone_hoe_repairing","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","minecraft:dye_black_bed","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","mcwbiomesoplenty:magic_western_door","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","biomesoplenty:empyreal_chest_boat","twigs:rhyolite","railcraft:brass_gear","pylons:infusion_pylon","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","mcwbiomesoplenty:pine_swamp_door","expatternprovider:assembler_matrix_speed","utilitix:mob_yoinker","mcwwindows:andesite_four_window","chemlib:manganese_ingot_from_smelting_manganese_dust","mcwlights:golden_low_candle_holder","supplementaries:feather_block","enderio:advanced_item_filter","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/cyan_trapped_soliciting_carpet","utilitarian:no_soliciting/soliciting_carpets/red_trapped_soliciting_carpet","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","refinedstorage:coloring_recipes/purple_network_transmitter","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","dyenamics:honey_candle","mcwbiomesoplenty:magic_tropical_door","botania:conversions/gray_petal_block_deconstruct","securitycraft:block_change_detector","croptopia:fruit_salad","supplementaries:pancake_fd","twigs:rocky_dirt","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","modularrouters:placer_module","minecraft:redstone_from_blasting_deepslate_redstone_ore","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","minecraft:netherite_scrap_from_blasting","mcwbiomesoplenty:dead_bamboo_door","enderio:resetting_lever_three_hundred_from_prev","utilitarian:no_soliciting/soliciting_carpets/purple_trapped_soliciting_carpet","mcwroofs:pink_terracotta_lower_roof","mcwtrpdoors:bamboo_four_panel_trapdoor","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","silentgear:azure_electrum_dust_smelting","refinedstorage:coloring_recipes/orange_security_manager","mcwbridges:bridge_torch","refinedstorage:coloring_recipes/orange_detector","botania:white_shiny_flower","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","undergarden:wigglewood_boat","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","refinedstorage:coloring_recipes/orange_crafting_monitor","createoreexcavation:vein_atlas","additionallanterns:quartz_chain","twilightforest:sorting_chest_boat","refinedstorage:coloring_recipes/magenta_disk_manipulator","mcwwindows:birch_curtain_rod","forbidden_arcanus:blasting/rune_from_blasting","aether:diamond_axe_repairing","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","pneumaticcraft:pressure_chamber_glass_x4","refinedstorage:coloring_recipes/lime_crafting_monitor","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","minecraft:redstone_from_smelting_deepslate_redstone_ore","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","chemlib:barium_nugget_to_ingot","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","mcwbiomesoplenty:pine_bark_glass_door","domum_ornamentum:sand_bricks","twilightdelight:cooking/tear_drink","securitycraft:secret_dark_oak_sign_item","ae2:network/cables/dense_covered_cyan","minecraft:gold_ingot_from_smelting_raw_gold","sophisticatedbackpacks:smithing_upgrade","mcwbiomesoplenty:palm_beach_door","mcwfences:mud_brick_railing_gate","twigs:oak_table","pneumaticcraft:heat_pipe","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","mysticalagriculture:essence/minecraft/amethyst","mcwbiomesoplenty:magic_bamboo_door","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwfurnitures:stripped_acacia_double_drawer","utilitix:bamboo_shulker_raft_with_shell","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","mcwroofs:green_concrete_steep_roof","enderio:staff_of_travelling","botania:conversions/lime_petal_block_deconstruct","mcwfences:mangrove_wired_fence","refinedstorage:coloring_recipes/gray_security_manager","minecraft:red_terracotta","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwpaths:blackstone_dumble_paving","mcwroofs:white_roof_block","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwbiomesoplenty:mahogany_stable_door","travelersbackpack:melon","securitycraft:block_pocket_manager","refinedstorage:coloring_recipes/orange_pattern_grid","mcwlights:soul_cherry_tiki_torch","expatternprovider:ei","minecraft:end_stone_brick_wall","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","alltheores:copper_ore_hammer","enderio:resetting_lever_sixty_inv_from_prev","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","mcwlights:red_lamp","bloodmagic:blood_altar","alltheores:lumium_dust_from_alloy_blending","refinedstorage:coloring_recipes/gray_crafter_manager","bloodmagic:path/path_stone","cfm:purple_kitchen_drawer","botania:flower_bag","ad_astra:steel_plateblock","alltheores:brass_gear","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","twilightforest:berry_torch","mcwfences:red_sandstone_railing_gate","enderio:primitive_alloy_smelter","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:acacia_triple_drawer","minecraft:netherite_chestplate_smithing","cfm:jungle_kitchen_counter","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","mcwfurnitures:stripped_acacia_double_drawer_counter","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","refinedstorage:coloring_recipes/green_crafting_grid","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwlights:light_blue_lamp","sophisticatedstorage:blasting_upgrade","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","mcwpaths:dark_prismarine_crystal_floor","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","cfm:acacia_desk","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwbiomesoplenty:pine_waffle_door","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","mcwbiomesoplenty:magic_stable_head_door","minecraft:birch_pressure_plate","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwfurnitures:stripped_acacia_drawer","mcwwindows:golden_curtain_rod","refinedstorage:coloring_recipes/pink_crafting_grid","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:palm_stable_head_door","refinedstorage:coloring_recipes/white_crafter","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","refinedstorage:coloring_recipes/black_crafting_monitor","sfm:manager","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwbiomesoplenty:willow_bamboo_door","travelersbackpack:fox","silentgear:tyrian_steel_nugget","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","littlelogistics:tee_junction_rail","botania:livingwood_twig","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","deeperdarker:bloom_boat","mcwtrpdoors:birch_blossom_trapdoor","refinedstorage:coloring_recipes/cyan_pattern_grid","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","mcwroofs:pink_terracotta_upper_steep_roof","dyenamics:rose_stained_glass","mcwpaths:blackstone_diamond_paving","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","croptopia:steamed_crab","refinedstorage:coloring_recipes/black_security_manager","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","mcwroofs:acacia_steep_roof","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","allthecompressed:compress/copper_block_1x","minecraft:spectral_arrow","alltheores:copper_dust_from_hammer_ingot_crushing","mcwroofs:light_blue_concrete_upper_steep_roof","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","refinedstorage:coloring_recipes/purple_pattern_grid","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","supplementaries:candle_holders/candle_holder_light_gray","chemlib:barium_ingot_from_blasting_barium_dust","mcwwindows:warped_planks_four_window","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","refinedstorage:coloring_recipes/black_fluid_grid","bloodmagic:enhanced_teleposer_focus","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","expatternprovider:pre_bus","mcwdoors:bamboo_waffle_door","mcwfurnitures:stripped_acacia_desk","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","allthearcanistgear:vibranium_hat_smithing","ae2:network/cables/dense_smart_fluix_clean","minecraft:dye_orange_bed","refinedstorage:coloring_recipes/brown_grid","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","rftoolsutility:moduleplus_template","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","handcrafted:birch_drawer","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","chemlib:argon_lamp_block","refinedstorage:coloring_recipes/lime_wireless_transmitter","littlelogistics:vessel_charger","mcwbiomesoplenty:dead_tropical_door","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","bigreactors:reprocessor/powerport","mcwbiomesoplenty:jacaranda_paper_door","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","refinedstorage:coloring_recipes/relay","cfm:green_picket_gate","railcraft:radio_circuit","aether:netherite_pickaxe_repairing","pneumaticcraft:manual_compressor","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","refinedstorage:coloring_recipes/lime_crafter","minecraft:dispenser","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","ae2:decorative/sky_stone_brick_from_stonecutting","securitycraft:secret_acacia_sign_item","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","refinedstorage:coloring_recipes/magenta_detector","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","refinedstorage:coloring_recipes/purple_controller","mcwwindows:cherry_plank_four_window","mcwtrpdoors:acacia_swamp_trapdoor","refinedstorage:coloring_recipes/orange_controller","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","mcwbiomesoplenty:fir_modern_door","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","minecraft:cooked_cod","deepresonance:radiation_suit_helmet","refinedstorage:coloring_recipes/detector","minecraft:iron_helmet","ad_astra:steel_trapdoor","voidtotem:totem_of_void_undying","securitycraft:reinforced_pink_stained_glass_pane_from_dye","mcwbiomesoplenty:mahogany_barn_glass_door","forbidden_arcanus:edelwood_slab","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","pneumaticcraft:fluid_mixer","minecraft:cartography_table","securitycraft:alarm","pneumaticcraft:manometer","allthemodium:vibranium_ingot_from_dust_blasting","mcwbiomesoplenty:dead_japanese2_door","ad_astra:launch_pad","cfm:magenta_picket_gate","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","cfm:stripped_acacia_desk_cabinet","ae2:tools/fluix_shovel","forbidden_arcanus:edelwood_fence_gate","pneumaticcraft:wall_lamp_inverted_magenta","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","botania:petal_lime_double","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","pneumaticcraft:smart_chest","mcwpaths:dark_prismarine_crystal_floor_slab","refinedstorage:coloring_recipes/cyan_security_manager","refinedstorage:coloring_recipes/cyan_relay","additionallanterns:amethyst_lantern","utilitix:crude_furnace","dyenamics:peach_dye","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","mysticalagriculture:essence/minecraft/netherite_ingot","mcwfurnitures:stripped_acacia_striped_chair","refinedstorage:coloring_recipes/blue_crafter","mcwroofs:gray_steep_roof","create:copper_shingles_from_ingots_copper_stonecutting","reliquary:mob_charm_belt","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","refinedstorage:coloring_recipes/lime_network_transmitter","mcwroofs:birch_planks_roof","croptopia:pumpkin_bars","sophisticatedstorage:jukebox_upgrade","allthemodium:raw_unobtainium_block","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","dankstorage:dank_3","mcwroofs:yellow_terracotta_upper_lower_roof","ae2:network/cables/glass_black","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","railcraft:invar_gear","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","chemlib:beryllium_nugget_to_ingot","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","bloodmagic:blood_rune_aug_capacity_2","cfm:black_trampoline","mcwfurnitures:acacia_cupboard_counter","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","minecraft:polished_blackstone_stairs","mcwbiomesoplenty:magic_mystic_door","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","minecraft:dropper","bloodmagic:path/path_wornstonetile","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","farmersdelight:cooking/stuffed_pumpkin_block","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","expatternprovider:oversize_interface_part","mcwwindows:oak_window2","mcwfurnitures:stripped_acacia_bookshelf","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","create:crafting/kinetics/brass_hand","mcwfences:mangrove_horse_fence","bigreactors:reprocessor/collector","create:crafting/kinetics/lime_seat","mcwbiomesoplenty:empyreal_bamboo_door","minecraft:golden_helmet","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","mcwdoors:acacia_bamboo_door","ae2:network/crafting/4k_cpu_crafting_storage","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","cfm:stripped_dark_oak_kitchen_sink_light","forbidden_arcanus:edelwood_bucket","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","mcwwindows:quartz_pane_window","refinedstorage:coloring_recipes/green_network_transmitter","bloodmagic:raw_hellforged_block","refinedstorage:coloring_recipes/orange_network_receiver","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:fancy_painting","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","pneumaticcraft:air_grate_module","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","twilightforest:equipment/twilight_scepter","mcwbridges:balustrade_end_stone_bricks_bridge","twigs:polished_rhyolite","minecraft:quartz_bricks","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","chemlib:magnesium_ingot_from_smelting_magnesium_dust","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwpaths:stone_running_bond_stairs","refinedstorage:coloring_recipes/yellow_disk_manipulator","rftoolsbuilder:red_shield_template_block","refinedstorage:coloring_recipes/white_crafting_grid","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","allthetweaks:atm_star_block","mcwbiomesoplenty:dead_mystic_door","chemlib:strontium_ingot_from_smelting_strontium_dust","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","minecraft:birch_door","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","bloodmagic:soul_forge","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","pneumaticcraft:pressure_chamber_valve","delightful:food/cooking/jam_jar","pneumaticcraft:logistics_frame_passive_provider","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","twigs:polished_tuff_stonecutting","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","mcwbiomesoplenty:pine_japanese_door","cfm:green_picket_fence","mcwroofs:white_terracotta_top_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","cfm:dye_orange_picket_gate","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","mcwbiomesoplenty:redwood_glass_door","refinedstorage:coloring_recipes/red_security_manager","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:birch_fence_gate","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwroofs:orange_concrete_upper_lower_roof","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","expatternprovider:assembler_matrix_pattern","mcwfences:modern_andesite_wall","mcwbridges:bamboo_bridge","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","chemlib:barium_ingot_to_nugget","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","mcwbiomesoplenty:maple_beach_door","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","mcwbiomesoplenty:magic_four_panel_door","expatternprovider:caner","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwfurnitures:acacia_drawer_counter","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:hellbark_four_panel_door","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","connectedglass:borderless_glass_orange_pane2","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","chemlib:gallium_nugget_to_ingot","minecraft:honeycomb_block","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","minecraft:yellow_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwfurnitures:acacia_striped_chair","croptopia:steamed_clams","mcwpaths:dark_prismarine_running_bond_path","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","minecolonies:apple_pie","mcwtrpdoors:bamboo_beach_trapdoor","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","pneumaticcraft:smart_chest_kit","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwbiomesoplenty:palm_barn_door","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","twilightdelight:cooking/torchberry_juice","domum_ornamentum:orange_floating_carpet","mcwbiomesoplenty:palm_mystic_door","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","mcwbiomesoplenty:maple_japanese2_door","farmersdelight:sweet_berry_cookie","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","mcwlights:pink_lamp","refinedstorage:coloring_recipes/gray_detector","railcraft:steel_boots","ad_astra:zip_gun","croptopia:nougat","ae2:network/cables/covered_magenta","refinedstorage:coloring_recipes/white_crafting_monitor","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","chemlib:indium_block_to_ingot","refinedstorage:coloring_recipes/black_controller","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","expatternprovider:pattern_modifier","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","silentgear:crimson_steel_dust_blasting","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwdoors:acacia_whispering_door","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwbiomesoplenty:jacaranda_cottage_door","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","ae2:network/cables/covered_gray","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","silentgear:tyrian_steel_dust_smelting","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","allthecompressed:compress/atm_star_block_1x","cfm:birch_coffee_table","mcwtrpdoors:acacia_whispering_trapdoor","travelersbackpack:magma_cube","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","refinedstorage:coloring_recipes/white_security_manager","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","allthecompressed:compress/iridium_block_1x","forbidden_arcanus:deorum_lantern","mcwbiomesoplenty:hellbark_glass_door","minecraft:brewing_stand","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","occultism:crafting/spirit_attuned_crystal","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwroofs:acacia_roof","mcwdoors:jungle_paper_door","chemlib:potassium_block_to_ingot","pneumaticcraft:spawner_agitator","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","ae2:network/cables/smart_light_gray","mcwbiomesoplenty:stripped_palm_log_four_window","mcwbiomesoplenty:empyreal_mystic_door","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","create:crafting/kinetics/orange_seat","refinedstorage:coloring_recipes/white_relay","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","dyenamics:icy_blue_terracotta","dyenamics:cherenkov_stained_glass","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","chemlib:sodium_ingot_to_nugget","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","refinedstorage:coloring_recipes/brown_network_transmitter","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","refinedstorage:coloring_recipes/gray_relay","bigreactors:blasting/graphite_from_coal","minecraft:pumpkin_seeds","mcwpaths:dark_prismarine_windmill_weave_stairs","sophisticatedbackpacks:stack_upgrade_tier_1","bigreactors:crafting/blutonium_component_to_storage","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_brown_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","farmersdelight:horse_feed","railways:crafting/smokestack_long","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","refinedstorage:coloring_recipes/brown_fluid_grid","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","ae2:tools/certus_quartz_cutting_knife","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:light_gray_terracotta","dyenamics:amber_stained_glass","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","bloodmagic:primitive_furnace_cell","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","dyenamics:wine_terracotta","refinedstorage:coloring_recipes/cyan_wireless_transmitter","mcwroofs:light_gray_attic_roof","mcwpaths:birch_planks_path","refinedstorage:coloring_recipes/pattern_grid","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","twilightforest:hydra_banner_pattern","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","occultism:crafting/book_of_calling_foliot_cleaner","merequester:requester_terminal","mcwfurnitures:stripped_jungle_covered_desk","ae2:shaped/stairs/smooth_sky_stone_block","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:acacia_paper_trapdoor","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","mcwbiomesoplenty:pine_paper_door","botania:manasteel_shears","minecraft:pumpkin_pie","ae2:network/cells/item_storage_cell_64k","ae2:network/cables/smart_green","mcwfurnitures:stripped_jungle_large_drawer","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mekanismtools:refined_obsidian/tools/pickaxe","cfm:acacia_upgraded_gate","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","refinedstorage:coloring_recipes/black_network_receiver","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","minecraft:soul_campfire","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","alltheores:platinum_ingot_from_ore_blasting","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","refinedstorage:coloring_recipes/red_network_receiver","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","refinedstorage:coloring_recipes/pink_grid","twilightdelight:obsidian","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","pneumaticcraft:air_canister","mcwwindows:prismarine_pane_window","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","refinedstorage:coloring_recipes/gray_pattern_grid","supplementaries:slice_map","littlelogistics:automatic_tee_junction_rail","mcwdoors:acacia_mystic_door","alltheores:aluminum_dust_from_hammer_ingot_crushing","refinedstorage:coloring_recipes/pink_detector","mcwroofs:cobblestone_lower_roof","alltheores:invar_plate","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","silentgear:azure_electrum_block","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","botania:lens_normal","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","enderio:reinforced_obsidian_block","mcwroofs:light_gray_upper_lower_roof","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","mcwfurnitures:jungle_modern_chair","ae2:network/cells/item_storage_cell_4k_storage","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","handcrafted:phantom_trophy","minecraft:quartz_slab","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","create:brass_bars_from_ingots_brass_stonecutting","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","ae2:block_cutter/stairs/smooth_sky_stone_stairs","dyenamics:amber_wool","forbidden_arcanus:edelwood_button","farmersdelight:iron_knife","minecraft:quartz_pillar_from_quartz_block_stonecutting","railcraft:steel_helmet","mcwfurnitures:stripped_jungle_double_drawer","mcwbiomesoplenty:palm_bamboo_door","ae2:network/cables/smart_black","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","biomesoplenty:hellbark_chest_boat","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwtrpdoors:acacia_bark_trapdoor","mcwbiomesoplenty:hellbark_waffle_door","alltheores:uranium_ingot_from_ore_blasting","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","mcwpaths:dark_prismarine_strewn_rocky_path","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","minecraft:bucket","handcrafted:salmon_trophy","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","farmersdelight:chicken_sandwich","minecraft:polished_andesite","mcwbiomesoplenty:palm_swamp_door","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","chemlib:helium_lamp_block","enderio:iron_gear","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwdoors:bamboo_stable_head_door","immersiveengineering:crafting/firework","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","ae2:network/cables/smart_orange","silentgear:crimson_steel_ingot_from_nugget","cfm:white_picket_fence","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:acacia_drawer","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","twigs:polished_tuff_bricks_from_tuff_stonecutting","handcrafted:kitchen_hood","appbot:mana_storage_cell_4k","mcwdoors:jungle_bamboo_door","sophisticatedbackpacks:stonecutter_upgrade","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","chemlib:gallium_block_to_ingot","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:fir_swamp_door","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwbridges:birch_log_bridge_middle","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwbiomesoplenty:dead_stable_head_door","immersiveengineering:crafting/treated_fence","silentgear:azure_electrum_ingot_from_block","mcwwindows:diorite_louvered_shutter","minecraft:gray_dye","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","securitycraft:secret_birch_hanging_sign","allthemodium:allthemodium_ingot_from_dust_blasting","botania:travel_belt","bigreactors:reprocessor/fluidinjector","bloodmagic:blood_rune_acceleration","allthemodium:unobtainium_ingot_from_raw_blasting","mcwbiomesoplenty:fir_highley_gate","refinedstorage:coloring_recipes/lime_network_receiver","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwroofs:acacia_upper_lower_roof","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","utilitarian:no_soliciting/soliciting_carpets/white_trapped_soliciting_carpet","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","allthecompressed:compress/pumpkin_1x","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","aether:blue_aercloud_freezing","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwbiomesoplenty:redwood_bamboo_door","mcwwindows:oak_plank_parapet","immersiveengineering:crafting/hempcrete","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:iron_sword","aquaculture:sushi","forbidden_arcanus:stellarite_block_from_stellarite_piece","ae2:network/blocks/interfaces_interface_alt","mcwfurnitures:jungle_table","refinedstorage:coloring_recipes/purple_network_receiver","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","chemlib:indium_ingot_to_block","minecraft:dark_prismarine_slab","railways:crafting/smokestack_caboosestyle","additionallanterns:blackstone_chain","mcwbiomesoplenty:magic_japanese2_door","mcwwindows:one_way_glass_pane","mcwbiomesoplenty:hellbark_stable_door","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfurnitures:stripped_acacia_table","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","ae2:tools/fluix_upgrade_smithing_template","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","mcwlights:jungle_ceiling_fan_light","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","minecraft:birch_trapdoor","ae2:network/cables/covered_light_gray","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","everythingcopper:copper_anvil","mcwlights:striped_lantern","refinedstorage:coloring_recipes/cyan_controller","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwdoors:acacia_barn_glass_door","mcwroofs:light_blue_terracotta_top_roof","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","create:birch_window","cfm:birch_park_bench","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","deeperdarker:bloom_chest_boat","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","mcwbiomesoplenty:empyreal_barn_door","minecraft:powered_rail","botania:dye_pink","mcwwindows:diorite_pane_window","dyenamics:wine_stained_glass_pane_from_glass_pane","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","twigs:birch_table","silentgear:crimson_steel_ingot_from_block","enderio:resetting_lever_ten_from_prev","mcwtrpdoors:acacia_classic_trapdoor","mcwbridges:end_stone_bricks_bridge","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:magenta_concrete_upper_lower_roof","sophisticatedstorage:storage_stack_upgrade_tier_5_from_backpack_stack_upgrade_tier_4","mcwroofs:light_gray_terracotta_steep_roof","mcwbiomesoplenty:hellbark_nether_door","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","twilightforest:equipment/fiery_ingot_crafting","sophisticatedstorage:void_upgrade","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","mcwbiomesoplenty:empyreal_paper_door","minecraft:gray_terracotta","mcwfurnitures:acacia_modern_chair","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","mcwdoors:bamboo_mystic_door","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","twilightforest:equipment/fiery_fiery_chestplate","mcwbiomesoplenty:magic_bark_glass_door","dyenamics:peach_terracotta","minecraft:fishing_rod","xnet:connector_yellow_dye","rftoolspower:powercell_card","minecraft:terracotta","mcwwindows:acacia_blinds","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwdoors:acacia_waffle_door","mcwlights:yellow_lamp","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","refinedstorage:coloring_recipes/blue_crafting_monitor","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","twigs:copper_pillar_stonecutting","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","bloodmagic:largebloodstonebrick","forbidden_arcanus:dark_rune_from_dark_rune_block","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","ae2:network/cells/fluid_storage_cell_4k_storage","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:crafting/kinetics/vertical_gearbox","occultism:blasting/iesnium_ingot_from_dust","mcwlights:double_street_lamp","minecraft:beacon","railcraft:tin_gear","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","railways:crafting/smokestack_streamlined","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","bloodmagic:smelting/ingot_hellforged","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","chemlib:barium_ingot_from_smelting_barium_dust","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","twilightforest:snow_queen_banner_pattern","create:crafting/kinetics/blue_seat","allthemodium:vibranium_dust_from_ore_crushing","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:umbran_bamboo_door","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","bloodmagic:blood_rune_displacement","ae2:shaped/walls/smooth_sky_stone_block","allthetweaks:atm_star_from_atmstar_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","rftoolsbase:tablet","ae2:network/crafting/256k_cpu_crafting_storage","mcwroofs:magenta_terracotta_lower_roof","ad_astra:vent","refinedstorage:coloring_recipes/light_gray_disk_manipulator","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","twilightforest:equipment/fiery_fiery_boots","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:birch_planks_upper_lower_roof","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","securitycraft:reinforced_nether_brick_fence","handcrafted:terracotta_plate","supplementaries:stone_tile","mcwroofs:light_blue_terracotta_roof","mcwbiomesoplenty:willow_barn_glass_door","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","supplementaries:flags/flag_white","silentgear:coating_smithing_template","mcwpaths:dark_prismarine_basket_weave_paving","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","handcrafted:berry_jam_jar","mcwroofs:andesite_top_roof","mcwbridges:birch_rail_bridge","minecraft:glass_pane","supplementaries:timber_brace","refinedstorage:coloring_recipes/lime_relay","alltheores:iridium_ingot_from_raw_blasting","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwbiomesoplenty:maple_barn_glass_door","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","farmersdelight:netherite_knife_smithing","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","handcrafted:birch_cupboard","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","railways:crafting/smokestack_oilburner","securitycraft:sonic_security_system","minecraft:clay","mcwbiomesoplenty:pine_pane_window","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","securitycraft:protecto","bloodmagic:smelting/blasting_ingot_from_demonite","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","ae2:network/blocks/interfaces_interface_part","dyenamics:cherenkov_terracotta","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","refinedstorage:coloring_recipes/brown_controller","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","mcwbiomesoplenty:mahogany_paper_door","ae2:materials/annihilationcore","forbidden_arcanus:clibano_combustion/rune_from_clibano_combusting","croptopia:chicken_and_noodles","ae2:network/cables/dense_smart_from_smart","forbidden_arcanus:clibano_combustion/lapis_lazuli_from_clibano_combusting","silentgear:stone_rod","ae2:network/cables/dense_covered_green","ae2:network/cables/covered_green","minecraft:leather_boots","railcraft:steel_spike_maul","refinedstorage:coloring_recipes/gray_fluid_grid","pneumaticcraft:classify_filter","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","mcwwindows:gray_mosaic_glass","chemlib:zirconium_ingot_from_smelting_zirconium_dust","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwtrpdoors:acacia_barred_trapdoor","mcwlights:pink_paper_lamp","mcwbiomesoplenty:magic_nether_door","pneumaticcraft:wall_lamp_inverted_light_blue","occultism:crafting/divination_rod","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","cfm:yellow_kitchen_sink","alltheores:enderium_ingot_from_dust_blasting","silentgear:diamond_from_shards","minecolonies:supplychestdeployer","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:dye_black_carpet","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","mcwfurnitures:acacia_bookshelf_drawer","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","expatternprovider:ebus_out","ae2:network/cables/covered_purple","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","alltheores:iron_rod","mcwroofs:cyan_striped_awning","silentgear:azure_electrum_dust_blasting","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwpaths:dark_prismarine_flagstone_slab","minecraft:magma_block","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","cfm:acacia_park_bench","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","connectedglass:clear_glass_black_pane2","connectedglass:clear_glass_black_pane3","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","littlelogistics:transmitter_component","minecraft:brush","mcwfurnitures:stripped_acacia_triple_drawer","aether:book_of_lore","securitycraft:secret_spruce_hanging_sign","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","mcwfurnitures:acacia_wardrobe","sfm:cable","mcwbiomesoplenty:palm_japanese_door","minecraft:hay_block","twilightforest:equipment/fiery_fiery_leggings","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","mcwroofs:white_upper_steep_roof","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","delightful:knives/invar_knife","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","pneumaticcraft:compressed_iron_boots","mcwwindows:red_sandstone_window2","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbiomesoplenty:jacaranda_barn_glass_door","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","railways:crafting/smokestack_coalburner","mcwfences:modern_granite_wall","bloodmagic:primitive_hydration_cell","pneumaticcraft:etching_tank","enderio:xp_obelisk","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","bloodmagic:ritual_stone_master","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:fir_barn_door","bambooeverything:bamboo_ladder","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","refinedstorage:coloring_recipes/light_gray_network_receiver","bigreactors:blasting/graphite_from_charcoal","refinedstorage:coloring_recipes/pink_wireless_transmitter","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","create:crafting/kinetics/rotation_speed_controller","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","littlelogistics:fluid_hopper","mcwwindows:bricks_window","twigs:paper_lantern","railcraft:goggles","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","mcwbiomesoplenty:willow_japanese2_door","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","ae2:network/cables/covered_black","botania:petal_lime","refinedstorage:coloring_recipes/green_grid","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","ae2:network/cables/dense_smart_purple","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","bloodmagic:blood_rune_aug_capacity","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwtrpdoors:bamboo_barrel_trapdoor","mcwbiomesoplenty:empyreal_window2","mcwbiomesoplenty:pine_barn_glass_door","refinedstorage:coloring_recipes/black_relay","mcwpaths:stone_flagstone_slab","ae2:decorative/sky_stone_small_brick_from_stonecutting","silentgear:crimson_steel_dust_smelting","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","enderio:basic_item_filter","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","securitycraft:sentry","minecraft:netherite_block","immersiveengineering:crafting/blueprint_bullets","alltheores:uranium_rod","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","refinedstorage:coloring_recipes/purple_disk_manipulator","refinedstorage:coloring_recipes/cyan_detector","utilitarian:no_soliciting/soliciting_carpets/blue_trapped_soliciting_carpet","mcwroofs:pink_terracotta_top_roof","create:crafting/logistics/redstone_contact","mcwfurnitures:stripped_acacia_lower_bookshelf_drawer","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","mcwfurnitures:stripped_oak_modern_chair","minecraft:calibrated_sculk_sensor","pneumaticcraft:coordinate_tracker_upgrade","bloodmagic:smelting/ingot_from_demonite","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwbiomesoplenty:fir_mystic_door","refinedstorage:coloring_recipes/white_pattern_grid","mcwfences:dark_oak_horse_fence","securitycraft:secret_warped_hanging_sign","ae2:tools/certus_quartz_spade","mcwbiomesoplenty:fir_beach_door","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","twilightforest:carminite_builder","alltheores:constantan_dust_from_alloy_blending","mcwroofs:black_concrete_upper_lower_roof","ae2:network/cables/dense_covered_black","forbidden_arcanus:edelwood_stairs","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","expatternprovider:epp_alt","occultism:crafting/spirit_campfire","utilitix:piston_controller_rail","croptopia:roasted_smoking","create:crafting/curiosities/peculiar_bell","mcwbiomesoplenty:hellbark_curved_gate","itemcollectors:advanced_collector","minecraft:green_terracotta","delightful:knives/brass_knife","minecraft:white_stained_glass","mcwbiomesoplenty:umbran_mystic_door","expatternprovider:assembler_matrix_frame","securitycraft:secret_warped_sign_item","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","refinedstorage:coloring_recipes/yellow_controller","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","allthemodium:vibranium_nugget_from_ingot","croptopia:pumpkin_soup","ae2:network/cells/fluid_storage_cell_64k","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","refinedstorage:coloring_recipes/crafting_monitor","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","mcwfurnitures:acacia_double_drawer_counter","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","ad_astra:fan","chemlib:zirconium_nugget_to_ingot","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","sfm:labelgun","mcwfences:jungle_hedge","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mekanismtools:osmium/tools/sword","refinedstorage:coloring_recipes/yellow_relay","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","occultism:crafting/book_of_calling_foliot_lumberjack","twilightforest:carminite_reactor","domum_ornamentum:pink_floating_carpet","botania:virus_nullodermal","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","ae2:network/cables/dense_smart_gray","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","allthecompressed:compress/stone_1x","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","refinedstorage:coloring_recipes/yellow_security_manager","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","chemlib:magnesium_ingot_to_nugget","botania:manasteel_shovel","minecraft:quartz_from_blasting","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","minecraft:light_gray_stained_glass_pane_from_glass_pane","mcwpaths:cobbled_deepslate_crystal_floor_slab","silentgear:leather_from_scraps","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwbiomesoplenty:magic_stable_door","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","cfm:lime_picket_gate","mcwpaths:blackstone_crystal_floor","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwbiomesoplenty:mahogany_classic_door","supplementaries:candle_holders/candle_holder_orange_dye","sophisticatedbackpacks:tool_swapper_upgrade","mcwwindows:stripped_spruce_pane_window","additionallanterns:cobbled_deepslate_chain","allthecompressed:compress/sand_1x","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","minecraft:lime_terracotta","mcwwindows:stripped_dark_oak_log_window","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","occultism:crafting/spirit_attuned_pickaxe_head","minecraft:gray_stained_glass_pane_from_glass_pane","mekanism:processing/osmium/ingot/from_raw_smelting","allthecompressed:compress/andesite_1x","mcwbiomesoplenty:willow_nether_door","gravitationalmodulatingunittweaks:module_gravitational_modulating_additional_unit","cfm:birch_cabinet","minecraft:stone_slab","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwbiomesoplenty:magic_classic_door","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","mcwbiomesoplenty:maple_four_panel_door","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","allthemodium:vibranium_ingot_from_block","travelersbackpack:backpack_tank","refinedstorage:coloring_recipes/blue_disk_manipulator","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbridges:glass_bridge","refinedstorage:coloring_recipes/black_crafter_manager","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","utilitarian:no_soliciting/soliciting_carpets/green_trapped_soliciting_carpet","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","minecraft:light_blue_stained_glass_pane_from_glass_pane","refinedstorage:coloring_recipes/purple_crafter","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","croptopia:campfire_molasses","mcwdoors:acacia_stable_door","ae2:network/crafting/16k_cpu_crafting_storage","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwbiomesoplenty:pine_four_panel_door","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","minecraft:blue_stained_glass_pane_from_glass_pane","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","mcwbiomesoplenty:hellbark_cottage_door","railcraft:water_tank_siding","expatternprovider:assembler_matrix_crafter","pneumaticcraft:wall_lamp_inverted_light_gray","refinedstorage:coloring_recipes/green_crafter","mcwtrpdoors:bamboo_bark_trapdoor","cfm:jungle_kitchen_sink_dark","twigs:crimson_roots_paper_lantern","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","securitycraft:secret_cherry_hanging_sign","croptopia:melon_juice","refinedstorage:coloring_recipes/crafting_grid","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","ae2:network/cables/smart_cyan","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","mcwbiomesoplenty:jacaranda_glass_door","pneumaticcraft:compressed_stone_slab","mcwfurnitures:stripped_acacia_chair","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","mcwwindows:one_way_glass","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","minecraft:oak_chest_boat","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","mcwbridges:acacia_bridge_pier","pneumaticcraft:empty_pcb_from_failed_pcb","allthecompressed:compress/grass_block_1x","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwbiomesoplenty:hellbark_classic_door","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","ae2additions:components/super/16k","mcwbiomesoplenty:dead_glass_door","connectedglass:borderless_glass_orange2","chemlib:beryllium_block_to_ingot","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","twigs:silt_from_silt_balls","everythingcopper:copper_helmet","pneumaticcraft:logistics_core","create:crafting/kinetics/yellow_seat","mcwbiomesoplenty:willow_modern_door","mcwroofs:red_concrete_upper_lower_roof","croptopia:anchovy_pizza","mcwpaths:andesite_windmill_weave_path","mcwbiomesoplenty:palm_glass_door","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","ae2:shaped/walls/sky_stone_block","cfm:birch_chair","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","mcwdoors:bamboo_whispering_door","minecraft:mangrove_boat","minecraft:bread","mcwroofs:green_terracotta_steep_roof","mcwbiomesoplenty:redwood_western_door","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","ad_astra:orange_industrial_lamp","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","cfm:birch_desk","immersiveengineering:crafting/wirecoil_structure_steel","mcwbiomesoplenty:dead_classic_door","mcwwindows:dark_oak_curtain_rod","botania:water_rod","botania:horn_grass","securitycraft:secret_cherry_sign_item","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwroofs:white_concrete_steep_roof","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","refinedstorage:coloring_recipes/light_gray_grid","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwbiomesoplenty:umbran_swamp_door","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","mcwfurnitures:acacia_desk","refinedstorage:coloring_recipes/cyan_network_transmitter","refinedstorage:coloring_recipes/yellow_crafter_manager","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwwindows:stone_brick_arrow_slit","mcwpaths:cobbled_deepslate_windmill_weave_stairs","chimes:amethyst_chimes","ae2:smelting/smooth_sky_stone_block","mcwfurnitures:jungle_desk","mcwbiomesoplenty:mahogany_cottage_door","domum_ornamentum:white_floating_carpet","mcwbiomesoplenty:umbran_waffle_door","twilightforest:iron_ladder","croptopia:ham_sandwich","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","ad_astra:space_suit","securitycraft:disguise_module","mcwbiomesoplenty:willow_swamp_door","mcwwindows:deepslate_four_window","mcwdoors:acacia_glass_door","pneumaticcraft:camo_applicator","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:fir_stable_head_door","cfm:light_blue_picket_fence","mcwbiomesoplenty:redwood_barn_door","mcwtrpdoors:acacia_four_panel_trapdoor","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","enderio:electromagnet","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","supplementaries:gold_trapdoor","forbidden_arcanus:deorum_block_from_deorum_ingot","croptopia:deep_fried_shrimp","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","mcwbiomesoplenty:palm_modern_door","enderio:basic_fluid_filter","handcrafted:birch_table","rftoolsutility:counter_module","cfm:stripped_jungle_crate","minecraft:amethyst_block","minecraft:oak_door","biomesoplenty:jacaranda_boat","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwdoors:bamboo_barn_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","mcwbiomesoplenty:maple_cottage_door","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwbiomesoplenty:empyreal_modern_door","mcwroofs:black_roof_block","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","bloodmagic:blood_rune_self_sac_2","advancedperipherals:peripheral_casing","immersiveengineering:crafting/wooden_grip","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","reliquary:fertile_lily_pad","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwfurnitures:stripped_acacia_large_drawer","mcwroofs:red_striped_awning","silentgear:tyrian_steel_dust_blasting","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","mcwroofs:lime_concrete_upper_lower_roof","modularrouters:dropper_module","twilightforest:lich_banner_pattern","mcwbiomesoplenty:magic_highley_gate","twilightforest:ur_ghast_banner_pattern","utilitarian:no_soliciting/soliciting_carpets/orange_trapped_soliciting_carpet","ae2:tools/fluix_sword","simplemagnets:basic_demagnetization_coil","dyenamics:mint_terracotta","mcwwindows:pink_curtain","mcwroofs:yellow_concrete_upper_lower_roof","sophisticatedstorage:birch_limited_barrel_1","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","sophisticatedstorage:birch_limited_barrel_4","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","sophisticatedstorage:birch_limited_barrel_2","delightful:knives/refined_obsidian_knife","expatternprovider:ei_alt","sophisticatedstorage:birch_limited_barrel_3","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","refinedstorage:coloring_recipes/blue_network_receiver","cfm:light_gray_kitchen_counter","minecraft:candle","twilightforest:knight_phantom_banner_pattern","framedblocks:framed_cube","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","immersiveengineering:crafting/connector_structural","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","bloodmagic:smelting/blasting_ingot_hellforged","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","advanced_ae:throughput_monitor_configurator","mcwdoors:bamboo_swamp_door","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/brown_relay","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","mcwroofs:blue_terracotta_upper_steep_roof","deepresonance:radiation_suit_boots","supplementaries:wrench","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","minecraft:bow","cfm:acacia_kitchen_counter","dyenamics:lavender_wool","aether:skyroot_smithing_table","refinedstorage:coloring_recipes/blue_fluid_grid","mekanismtools:osmium/armor/leggings","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","chemlib:periodic_table","botania:mushroom_stew","minecraft:andesite_stairs","forbidden_arcanus:deorum_nugget_from_deorum_ingot","bigreactors:energizer/chargingport_fe","refinedstorage:coloring_recipes/purple_crafting_monitor","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","dyenamics:amber_dye","mcwdoors:bamboo_japanese_door","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","refinedstorage:coloring_recipes/brown_crafter","supplementaries:sconce","silentgear:rough_rod","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","dyenamics:maroon_stained_glass_pane_from_glass_pane","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","chemlib:potassium_ingot_from_smelting_potassium_dust","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:acacia_large_drawer","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","bigreactors:reprocessor/glass","forbidden_arcanus:deorum_door","sophisticatedbackpacks:feeding_upgrade","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:pine_stable_door","minecraft:granite","minecraft:melon_seeds","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","cfm:stripped_acacia_desk","botania:ghost_rail","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","refinedstorage:coloring_recipes/network_receiver","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","silentgear:crimson_steel_block","nethersdelight:soul_compost_from_warped_roots","mcwbiomesoplenty:magic_barn_glass_door","sophisticatedstorage:backpack_advanced_void_upgrade_from_storage_advanced_void_upgrade","mcwbiomesoplenty:redwood_mystic_door","bloodmagic:smelting/blasting_ingot_netherite_scrap","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","chemlib:beryllium_ingot_from_smelting_beryllium_dust","pneumaticcraft:wall_lamp_inverted_brown","mcwbiomesoplenty:fir_bamboo_door","mcwroofs:andesite_roof","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","rftoolsbase:infused_enderpearl","supplementaries:pedestal","travelersbackpack:blaze","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","chemlib:zirconium_block_to_ingot","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:network/cables/glass_yellow","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","alltheores:ruby_dust_from_hammer_crushing","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwfurnitures:stripped_acacia_wardrobe","chemlib:manganese_block_to_ingot","dyenamics:rose_terracotta","farmersdelight:cooking/apple_cider","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","bloodmagic:blood_rune_charging","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","expatternprovider:oversize_interface","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","mcwfurnitures:stripped_acacia_modern_desk","dyenamics:bed/lavender_bed","minecraft:blackstone_stairs","refinedstorage:coloring_recipes/magenta_grid","cfm:lime_kitchen_counter","minecraft:dye_orange_carpet","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","alltheores:electrum_ingot_from_dust","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","refinedstorage:coloring_recipes/brown_detector","mcwroofs:brown_terracotta_steep_roof","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwdoors:oak_barn_door","botania:alchemy_catalyst","refinedstorage:coloring_recipes/blue_pattern_grid","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","refinedstorage:coloring_recipes/light_gray_crafting_monitor","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","forbidden_arcanus:smelting/arcane_crystal_from_smelting","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:acacia_upgraded_fence","cfm:oak_bedside_cabinet","ae2:network/cables/dense_smart_light_gray","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","mcwroofs:white_concrete_attic_roof","twilightdelight:torchberries_crate","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","additionallanterns:obsidian_chain","minecraft:ender_eye","supplementaries:blackstone_lamp","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","minecraft:birch_slab","refinedstorage:coloring_recipes/disk_manipulator","mcwtrpdoors:acacia_ranch_trapdoor","mcwlights:cross_lantern","mcwlights:brown_lamp","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","mcwwindows:bricks_four_window","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_packed_mud","immersiveengineering:crafting/hemp_fabric","chemlib:magnesium_block_to_ingot","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","ae2:network/parts/terminals_pattern_access","additionallanterns:diamond_chain","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","ae2:network/blocks/io_condenser","mekanismtools:osmium/armor/helmet","sophisticatedbackpacks:smoking_upgrade","supplementaries:stonecutting/stone_tile","ae2:network/cables/covered_pink","railcraft:steel_tank_wall","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","handcrafted:birch_shelf","dyenamics:conifer_concrete_powder","minecraft:dye_black_wool","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","twilightforest:firefly_jar","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","twilightdelight:torchberry_cookie","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","advanced_ae:quantumstorage128","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","refinedstorage:coloring_recipes/light_gray_controller","bloodmagic:blood_rune_displacement_2","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","allthemodium:raw_vibranium_block","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","refinedstorage:coloring_recipes/grid","refinedstorage:coloring_recipes/orange_network_transmitter","mcwpaths:dark_prismarine_windmill_weave_path","mcwwindows:jungle_plank_window2","ad_astra:space_pants","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","occultism:smelting/iesnium_ingot_from_raw","refinedstorage:coloring_recipes/lime_fluid_grid","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","additionallanterns:dark_prismarine_chain","refinedstorage:coloring_recipes/red_crafting_grid","mcwbiomesoplenty:mahogany_japanese_door","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","utilitarian:utility/acacia_logs_to_pressure_plates","nethersdelight:blackstone_stove","mcwbiomesoplenty:umbran_four_panel_door","enderio:resetting_lever_five_inv","silentgear:crimson_steel_dust","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","silentgear:tyrian_steel_ingot_from_nugget","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","bigreactors:turbine/ridiculite_block","mcwbiomesoplenty:redwood_beach_door","ae2:network/cables/smart_blue","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","bloodmagic:ritual_reader","botania:turntable","ae2:network/cells/item_storage_components_cell_1k_part","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","mcwroofs:oak_planks_top_roof","biomesoplenty:hellbark_boat","refinedstorage:coloring_recipes/orange_relay","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","minecraft:netherite_scrap","alltheores:zinc_dust_from_hammer_crushing","supplementaries:daub","railcraft:steel_chestplate","mcwfurnitures:stripped_jungle_end_table","securitycraft:secret_oak_hanging_sign","mcwfences:oak_hedge","bigreactors:reactor/reinforced/casing_recycle","enderio:fluid_tank","mcwroofs:oak_planks_roof","alltheores:tin_dust_from_hammer_ingot_crushing","botania:petal_white","ae2:decorative/quartz_block","forbidden_arcanus:edelwood_trapdoor","mcwbiomesoplenty:magic_cottage_door","ae2:network/cables/dense_smart_orange","chemlib:beryllium_ingot_to_nugget","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","alltheores:raw_iridium_from_block","ae2:block_cutter/slabs/smooth_sky_stone_slab","mcwbiomesoplenty:hellbark_highley_gate","create:crafting/logistics/redstone_link","immersiveengineering:crafting/treated_scaffold","minecraft:enchanting_table","mcwpaths:cobbled_deepslate_square_paving","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","farmersdelight:roast_chicken_block","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deeperdarker:reinforced_echo_shard","deepresonance:radiation_suit_chestplate","refinedstorage:coloring_recipes/pink_disk_manipulator","refinedstorage:coloring_recipes/lime_disk_manipulator","mcwlights:brown_paper_lamp","minecraft:campfire","allthearcanistgear:unobtainium_boots_smithing","dyenamics:peach_stained_glass_pane_from_glass_pane","travelersbackpack:standard_no_tanks","cfm:white_kitchen_sink","securitycraft:reinforced_birch_fence","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","refinedstorage:coloring_recipes/pink_network_transmitter","forbidden_arcanus:edelwood_door","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","minecraft:acacia_wood","mcwbiomesoplenty:maple_tropical_door","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","pneumaticcraft:logistics_configurator","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:mahogany_beach_door","mcwtrpdoors:print_tropical","pneumaticcraft:logistics_frame_default_storage_self","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","croptopia:mead","mcwfurnitures:stripped_jungle_desk","mcwfurnitures:stripped_acacia_counter","mcwwindows:birch_four_window","biomesoplenty:fir_boat","mcwdoors:bamboo_stable_door","pneumaticcraft:chunkloader_upgrade","refinedstorage:coloring_recipes/red_detector","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","refinedstorage:coloring_recipes/pink_security_manager","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","mcwpaths:dark_prismarine_dumble_paving","alltheores:gold_dust_from_hammer_crushing","minecraft:bone_block","refinedstorage:coloring_recipes/pink_controller","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwbiomesoplenty:willow_waffle_door","mcwroofs:lime_terracotta_lower_roof","refinedstorage:coloring_recipes/red_pattern_grid","minecraft:melon","ae2:network/cables/dense_smart_green","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","sophisticatedstorage:backpack_crafting_upgrade_from_storage_crafting_upgrade","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","mcwtrpdoors:acacia_barrel_trapdoor","cfm:jungle_coffee_table","xnet:connector_green","advanced_ae:smallappupgrade","create:oak_window","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","sophisticatedstorage:backpack_advanced_magnet_upgrade_from_storage_advanced_magnet_upgrade","mcwfurnitures:stripped_acacia_bookshelf_cupboard","mythicbotany:central_rune_holder","sophisticatedbackpacks:void_upgrade","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","dyenamics:maroon_candle","mcwroofs:acacia_upper_steep_roof","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","refinedstorage:coloring_recipes/gray_grid","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","supplementaries:forbidden_arcanus/sign_post_edelwood","appbot:mana_storage_cell_16k","mcwtrpdoors:acacia_barn_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","alltheores:brass_plate","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","railcraft:controller_circuit","twigs:polished_rhyolite_stonecutting","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","utilitix:oak_shulker_boat_with_shell","create:crafting/kinetics/gearbox","create:crafting/logistics/brass_tunnel","mcwbiomesoplenty:umbran_modern_door","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","rftoolsutility:screen_link","minecraft:leather_helmet","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","mcwbiomesoplenty:redwood_nether_door","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:bubble_blower","refinedstorage:coloring_recipes/white_disk_manipulator","dyenamics:conifer_stained_glass_pane_from_glass_pane","supplementaries:crank","minecraft:recovery_compass","securitycraft:secret_bamboo_sign_item","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","cfm:acacia_cabinet","mcwbiomesoplenty:willow_bark_glass_door","minecraft:pink_terracotta","refinedstorage:coloring_recipes/magenta_security_manager","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","refinedstorage:coloring_recipes/blue_crafting_grid","mcwbiomesoplenty:empyreal_classic_door","twigs:bamboo_thatch","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","refinedstorage:coloring_recipes/fluid_grid","nethersdelight:golden_machete","ae2:network/cables/smart_fluix_clean","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","allthecompressed:compress/quartz_block_1x","railcraft:standard_rail_from_rail","refinedstorage:coloring_recipes/brown_security_manager","twilightdelight:torchberry_pie","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","refinedstorage:coloring_recipes/white_grid","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","railcraft:train_detector","silentgear:crimson_steel_nugget","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","minecraft:golden_hoe","cfm:oak_kitchen_counter","mcwfurnitures:acacia_lower_triple_drawer","minecraft:fire_charge","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","twilightdelight:glacier_ice_tea","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:pink_petal_block","alltheores:platinum_dust_from_hammer_crushing","railcraft:any_detector","mcwpaths:dark_prismarine_clover_paving","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","pneumaticcraft:reinforced_chest_kit","mcwroofs:stone_bricks_upper_steep_roof","biomesoplenty:palm_chest_boat","minecraft:stone_button","mcwtrpdoors:bamboo_mystic_trapdoor","mcwbiomesoplenty:redwood_bark_glass_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","chemlib:strontium_ingot_to_block","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","pneumaticcraft:regulator_tube_module","mcwwindows:birch_plank_window","minecraft:sandstone","allthemodium:vibranium_ingot_from_raw_blasting","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:no_soliciting/soliciting_carpets/gray_trapped_soliciting_carpet","pneumaticcraft:logistics_frame_active_provider_self","minecraft:lodestone","mcwbiomesoplenty:umbran_japanese2_door","handcrafted:birch_counter","mcwlights:black_lamp","minecraft:purpur_block","railcraft:brass_ingot_crafted_with_ingots","minecraft:cut_copper_stairs_from_copper_block_stonecutting","minecraft:polished_blackstone_slab","mcwbiomesoplenty:mahogany_window","silentgear:azure_electrum_nugget","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:quartz","refinedstorage:coloring_recipes/brown_pattern_grid","refinedstorage:coloring_recipes/yellow_fluid_grid","pneumaticcraft:transfer_gadget","constructionwand:core_destruction","refinedstorage:coloring_recipes/purple_crafting_grid","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","allthemodium:unobtainium_dust_from_ore_crushing","mcwdoors:jungle_bark_glass_door","refinedstorage:coloring_recipes/pink_network_receiver","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","minecraft:redstone_from_blasting_redstone_ore","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","refinedstorage:coloring_recipes/white_detector","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","enderio:resetting_lever_five","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:hellbark_beach_door","twilightdelight:aurora_pie","chemlib:manganese_nugget_to_ingot","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","refinedstorage:coloring_recipes/red_grid","mcwbiomesoplenty:umbran_classic_door","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","ae2:network/parts/terminals","securitycraft:keypad_frame","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","twilightforest:equipment/fiery_fiery_helmet","bigreactors:turbine/basic/shaft","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:hellbark_bark_glass_door","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","pneumaticcraft:vortex_tube","refinedstorage:coloring_recipes/orange_wireless_transmitter","xnet:connector_routing","mcwwindows:warped_louvered_shutter","undergarden:wigglewood_chest_boat","blue_skies:glowing_nature_stone","handcrafted:birch_nightstand","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:palm_plank_window2","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","bloodmagic:ritual_diviner_1","bloodmagic:ritual_diviner_0","megacells:network/mega_interface","mcwroofs:green_terracotta_attic_roof","minecraft:birch_stairs","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:palm_bark_glass_door","immersiveengineering:crafting/shield","mcwbiomesoplenty:umbran_cottage_door","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","refinedstorage:coloring_recipes/magenta_network_receiver","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","sophisticatedstorage:birch_barrel","refinedstorage:coloring_recipes/brown_network_receiver","dyenamics:ultramarine_candle","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","ae2:network/crafting/cpu_crafting_monitor","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","mcwbiomesoplenty:pine_stable_head_door","refinedstorage:coloring_recipes/light_gray_fluid_grid","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","refinedstorage:coloring_recipes/purple_crafter_manager","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","cfm:orange_grill","dyenamics:fluorescent_candle","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","cfm:stripped_acacia_park_bench","ae2:network/blocks/storage_chest","securitycraft:secret_bamboo_hanging_sign","mcwroofs:oak_roof","cfm:jungle_desk","refinedstorage:coloring_recipes/yellow_crafter","aether:diamond_boots_repairing","minecraft:mangrove_chest_boat","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwroofs:light_gray_roof_slab","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","mcwroofs:purple_striped_awning","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwbiomesoplenty:palm_japanese2_door","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","ae2:network/wireless_booster","dyenamics:lavender_candle","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","refinedstorage:coloring_recipes/blue_network_transmitter","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","croptopia:crab_legs","mcwtrpdoors:bamboo_tropical_trapdoor","expatternprovider:ingredient_buffer","sophisticatedbackpacks:filter_upgrade","mcwbiomesoplenty:maple_japanese_door","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","ae2:network/cables/dense_covered_purple","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwfurnitures:acacia_stool_chair","alltheores:nickel_ingot_from_ore_blasting","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","bigreactors:crafting/blutonium_storage_to_component","minecraft:target","chemlib:beryllium_ingot_from_blasting_beryllium_dust","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","supplementaries:evilcraft/sign_post_undead","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","mcwfurnitures:stripped_acacia_modern_chair","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","chemlib:sodium_block_to_ingot","cfm:stripped_birch_kitchen_drawer","mcwfurnitures:stripped_acacia_lower_triple_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","bigreactors:turbine/insanite_block","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","cfm:birch_bedside_cabinet","mcwroofs:blue_terracotta_roof","xnet:netcable_yellow","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","mcwfurnitures:jungle_wardrobe","croptopia:cheese","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","refinedstorage:coloring_recipes/black_detector","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","sophisticatedstorage:backpack_stack_upgrade_tier_4_from_storage_stack_upgrade_tier_5","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwbiomesoplenty:willow_beach_door","mcwpaths:andesite_running_bond_slab","twilightforest:wood/dark_planks","minecraft:chiseled_stone_bricks_stone_from_stonecutting","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","chemlib:sodium_ingot_from_blasting_sodium_dust","minecraft:netherite_sword_smithing","croptopia:shaped_toast_sandwich","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","botania:manasteel_axe","mcwwindows:oak_blinds","mcwtrpdoors:acacia_cottage_trapdoor","create:crafting/kinetics/mechanical_crafter","mcwfences:birch_wired_fence","mcwbiomesoplenty:empyreal_beach_door","mcwroofs:red_concrete_lower_roof","cfm:dye_orange_picket_fence","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","delightful:food/baklava","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","arseng:source_storage_cell_16k","railcraft:signal_circuit","twilightforest:charm_of_life_2","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","aether:golden_aercloud_enchanting","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","mcwbiomesoplenty:mahogany_swamp_door","connectedglass:borderless_glass_pane1","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","cfm:stripped_acacia_kitchen_counter","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","refinedstorage:coloring_recipes/magenta_controller","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","refinedstorage:coloring_recipes/red_relay","cfm:red_grill","ae2:network/cells/fluid_storage_cell_16k_storage","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","refinedstorage:coloring_recipes/red_crafter","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","mcwbiomesoplenty:maple_modern_door","mcwpaths:cobbled_deepslate_running_bond","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","utilitarian:no_soliciting/soliciting_carpets/lime_trapped_soliciting_carpet","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","croptopia:chicken_and_dumplings","silentgear:tyrian_steel_block","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","botania:gray_petal_block","twilightforest:wood/aurora_slab_slab","mcwbiomesoplenty:empyreal_cottage_door","utilitix:tiny_charcoal_to_tiny","mcwtrpdoors:jungle_paper_trapdoor","refinedstorage:coloring_recipes/magenta_crafting_grid","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","pneumaticcraft:uv_light_box","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","nethersdelight:hoglin_sirloin_from_campfire_cooking","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","supplementaries:sign_post_oak","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","refinedstorage:coloring_recipes/black_grid","refinedstorage:coloring_recipes/brown_crafter_manager","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","pneumaticcraft:air_cannon","alltheores:netherite_dust_from_hammer_crushing","twilightdelight:hydra_burger","refinedstorage:coloring_recipes/controller","chemlib:gallium_ingot_from_blasting_gallium_dust","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:dark_prismarine_windmill_weave_slab","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","chemlib:manganese_ingot_to_nugget","railcraft:blast_furnace_bricks","ae2:network/cables/dense_covered_pink","dyenamics:amber_stained_glass_pane_from_glass_pane","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","bloodmagic:bloodstonebrick","ae2:tools/portable_item_cell_256k","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","securitycraft:secret_crimson_sign_item","mcwroofs:purple_terracotta_upper_lower_roof","ae2:network/cells/item_storage_cell_16k_storage","mcwbiomesoplenty:jacaranda_nether_door","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","mcwfurnitures:acacia_counter","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","blue_skies:cake_compat","mcwlights:reinforced_torch","utilitarian:utility/acacia_logs_to_doors","cfm:stripped_acacia_chair","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","pneumaticcraft:liquid_hopper","gtceu:smelting/smelt_dust_iron_to_ingot","refinedstorage:coloring_recipes/pink_fluid_grid","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","bloodmagic:path/path_stonetile","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","ad_astra:ti_69","mcwroofs:acacia_attic_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:fried_chicken","bloodmagic:blood_rune_orb_2","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","mcwbiomesoplenty:mahogany_nether_door","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:fir_bark_glass_door","mcwlights:oak_ceiling_fan_light","bloodmagic:experience_tome","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","evilcraft:crafting/undead_planks","xnet:advanced_connector_yellow","cfm:stripped_oak_chair","xnet:netcable_blue","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwwindows:oak_shutter","mcwroofs:lime_concrete_steep_roof","cfm:rock_path","connectedglass:scratched_glass_black2","botania:mana_spreader","connectedglass:scratched_glass_black1","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","minecraft:quartz_pillar","mysticalagriculture:imperium_block_combine","mcwbiomesoplenty:redwood_waffle_door","chemlib:indium_nugget_to_ingot","refinedstorage:coloring_recipes/light_gray_network_transmitter","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","ad_astra:black_industrial_lamp","refinedstorage:coloring_recipes/yellow_network_receiver","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","mcwbiomesoplenty:willow_barn_door","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwbridges:blackstone_bridge","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","allthemodium:unobtainium_ingot_from_dust_blasting","aether:obsidian_from_bucket_freezing","minecraft:blue_dye","railcraft:steel_leggings","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","mcwbiomesoplenty:umbran_nether_door","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","minecraft:piston","mcwwindows:crimson_planks_four_window","ad_astra:large_gas_tank","refinedstorage:coloring_recipes/orange_grid","mcwlights:white_lamp","mcwfurnitures:jungle_coffee_table","mcwbiomesoplenty:maple_waffle_door","appmek:chemical_storage_cell_4k","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","allthemodium:allthemodium_ingot_from_raw_smelting","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","ae2:network/cells/item_storage_components_cell_16k_part","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","mcwbiomesoplenty:hellbark_japanese2_door","refinedstorage:coloring_recipes/magenta_network_transmitter","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwdoors:bamboo_four_panel_door","mcwtrpdoors:oak_glass_trapdoor","advanced_ae:quantum_storage_component","refinedstorage:coloring_recipes/magenta_crafting_monitor","railcraft:villager_detector","mcwbiomesoplenty:willow_four_window","mcwbiomesoplenty:fir_stable_door","mcwbiomesoplenty:dead_modern_door","mcwroofs:gray_upper_steep_roof","botania:third_eye","mcwroofs:birch_planks_steep_roof","mcwwindows:metal_curtain_rod","mcwbiomesoplenty:palm_classic_door","mcwdoors:acacia_cottage_door","croptopia:apple_sapling","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","refinedstorage:coloring_recipes/magenta_relay","mcwroofs:oak_steep_roof","chemlib:strontium_ingot_from_blasting_strontium_dust","aether:skyroot_tripwire_hook","chemlib:potassium_ingot_to_block","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","minecraft:trapped_chest","mcwfences:spruce_horse_fence","croptopia:beef_jerky","handcrafted:oak_shelf","mcwbiomesoplenty:magic_modern_door","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","forbidden_arcanus:deorum_trapdoor","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","ad_astra:space_helmet","supplementaries:flags/flag_magenta","allthecompressed:compress/soul_soil_1x","minecraft:diamond_axe","mcwbridges:end_stone_bricks_bridge_pier","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","refinedstorage:coloring_recipes/lime_crafting_grid","refinedstorage:coloring_recipes/brown_crafting_monitor","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","forbidden_arcanus:stellarite_piece_from_stellarite_block","mcwroofs:lime_striped_awning","mcwbiomesoplenty:palm_tropical_door","additionallanterns:bricks_lantern","handcrafted:creeper_trophy","travelersbackpack:skeleton","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","mcwbiomesoplenty:palm_cottage_door","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","chemlib:indium_ingot_to_nugget","farmersdelight:stove","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","handcrafted:birch_side_table","packingtape:tape","refinedstorage:coloring_recipes/lime_detector","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","botania:petal_gray","occultism:blasting/iesnium_ingot_from_raw","cfm:birch_kitchen_counter","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","botania:petal_pink_double","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","xnet:antenna","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","dyenamics:persimmon_candle","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","refinedstorage:coloring_recipes/white_crafter_manager","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","refinedstorage:coloring_recipes/pink_crafting_monitor","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","forbidden_arcanus:smelting/rune_from_smelting","refinedstorage:coloring_recipes/pink_crafter_manager","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","mcwtrpdoors:jungle_ranch_trapdoor","securitycraft:secret_mangrove_hanging_sign","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","mcwfurnitures:acacia_double_wardrobe","pylons:expulsion_pylon","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","mythicbotany:yggdrasil_branch","mcwpaths:dark_prismarine_windmill_weave","alltheores:brass_rod","mcwroofs:red_terracotta_attic_roof","bloodmagic:arc","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","chemlib:strontium_nugget_to_ingot","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","utilitarian:no_soliciting/soliciting_carpets/light_blue_trapped_soliciting_carpet","create:crafting/kinetics/sequenced_gearshift","silentgear:tyrian_steel_dust","minecraft:torch","minecraft:polished_granite_stairs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","refinedstorage:coloring_recipes/blue_wireless_transmitter","create:crafting/kinetics/black_seat","refinedstorage:coloring_recipes/wireless_transmitter","cfm:oak_desk","allthecompressed:decompress/atm_star_block_2x","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwbiomesoplenty:dead_bark_glass_door","mcwbiomesoplenty:pine_mystic_door","mcwbiomesoplenty:fir_paper_door","chemlib:sodium_ingot_from_smelting_sodium_dust","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","minecraft:crafting_table","dyenamics:lavender_stained_glass","dyenamics:cherenkov_dye","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","cfm:dye_black_picket_gate","refinedstorage:coloring_recipes/red_controller","mcwbiomesoplenty:fir_japanese_door","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","create:crafting/logistics/stockpile_switch","mcwbridges:dry_bamboo_bridge_pier","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwfurnitures:stripped_acacia_bookshelf_drawer","ae2:network/cables/smart_gray","ae2:network/cables/dense_covered_white","refinedstorage:coloring_recipes/green_crafting_monitor","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","domum_ornamentum:brown_bricks","mcwwindows:oak_plank_four_window","cfm:red_cooler","forbidden_arcanus:edelwood_boat","securitycraft:reinforced_green_stained_glass_pane_from_dye","refinedstorage:coloring_recipes/white_fluid_grid","bigreactors:turbine/basic/blade","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","mekanismtools:osmium/tools/pickaxe","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","enderio:resetting_lever_five_from_inv","sophisticatedstorage:stack_upgrade_tier_1","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","cfm:acacia_desk_cabinet","ae2:network/cells/item_storage_cell_16k","ae2:decorative/quartz_fixture","mcwbiomesoplenty:dead_nether_door","cfm:acacia_blinds","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","mcwbiomesoplenty:dead_paper_door","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","occultism:crafting/raw_iesnium_block","littlelogistics:automatic_switch_rail","allthemodium:unobtainium_ingot_from_raw_smelting","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","forbidden_arcanus:clibano_combustion/coal_from_clibano_combusting","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","refinedstorage:coloring_recipes/light_gray_crafting_grid","nethersdelight:diamond_machete","utilitix:advanced_brewery","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_acacia_glass_table","bigreactors:reactor/reinforced/casing_recycle_glass","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwfurnitures:acacia_chair","mcwbiomesoplenty:palm_nether_door","expatternprovider:cobblestone_cell","refinedstorage:coloring_recipes/red_disk_manipulator","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:fir_western_door","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/yellow_grid","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","pneumaticcraft:tube_junction","bloodmagic:smelting/blasting_ingot_from_raw_hellforged","bambooeverything:bamboo_torch","dyenamics:honey_wool","mcwroofs:acacia_top_roof","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","mcwfurnitures:acacia_glass_table","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bloodmagic:alchemy_table","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","enderio:void_chassis","minecraft:compass","cfm:stripped_acacia_table","refinedstorage:coloring_recipes/cyan_grid","bloodmagic:blood_rune_speed","mcwbiomesoplenty:jacaranda_plank_pane_window","bloodmagic:path/path_wornstone","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","mcwbiomesoplenty:maple_mystic_door","immersiveengineering:crafting/armor_steel_helmet","mekanism:processing/osmium/raw/from_raw_block","expatternprovider:silicon_block","minecraft:item_frame","immersiveengineering:crafting/toolupgrade_railgun_scope","itemcollectors:basic_collector","minecraft:loom","enderio:resetting_lever_three_hundred_inv","ad_astra:steel_engine","minecraft:polished_granite_stairs_from_polished_granite_stonecutting","bloodmagic:blood_rune_capacity","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","sophisticatedstorage:stack_downgrade_tier_3","minecraft:arrow","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwbiomesoplenty:mahogany_stable_head_door","botania:red_string_comparator","cfm:pink_picket_fence","mcwroofs:pink_terracotta_steep_roof","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting","mcwbiomesoplenty:empyreal_stockade_fence","expatternprovider:ei_part","mcwroofs:gray_concrete_attic_roof","refinedstorage:coloring_recipes/black_crafting_grid","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","mekanismtools:osmium/armor/chestplate","refinedstorage:coloring_recipes/white_wireless_transmitter","computercraft:computer_advanced","minecraft:polished_blackstone","advanced_ae:quantumaccel","refinedstorage:coloring_recipes/blue_relay","bloodmagic:smelting/ingot_from_raw_hellforged","chemlib:potassium_ingot_to_nugget","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","railways:crafting/smokestack_woodburner","pneumaticcraft:logistics_frame_storage","mcwpaths:dark_prismarine_diamond_paving","refinedstorage:coloring_recipes/yellow_wireless_transmitter","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwpaths:dark_prismarine_flagstone","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","mcwroofs:stone_top_roof","bigreactors:turbine/reinforced/passivefluidport_forge","travelersbackpack:horse","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","cfm:brown_cooler","xnet:netcable_red","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","dyenamics:amber_candle","bigreactors:reprocessor/wasteinjector","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","mcwfurnitures:jungle_end_table","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwpaths:dark_prismarine_honeycomb_paving","minecraft:respawn_anchor","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","botania:petal_gray_double","dyenamics:persimmon_stained_glass_pane_from_glass_pane","mcwpaths:dark_prismarine_flagstone_path","expatternprovider:ebus_in","handcrafted:birch_corner_trim","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","sophisticatedstorage:shulker_box","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","refinedstorage:coloring_recipes/red_crafter_manager","refinedstorage:coloring_recipes/crafter","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","mcwbiomesoplenty:mahogany_modern_door","immersiveengineering:crafting/component_steel","refinedstorage:coloring_recipes/crafter_manager","utilitarian:utility/acacia_logs_to_boats","minecraft:dark_prismarine_stairs","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","refinedstorage:coloring_recipes/green_fluid_grid","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","sophisticatedbackpacks:compacting_upgrade","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cells/item_storage_cell_64k_storage","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","ae2:network/cables/covered_light_blue","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","ae2:decorative/quartz_vibrant_glass","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:jukebox","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_barn_glass_door","cfm:acacia_chair","handcrafted:birch_dining_bench","mcwwindows:oak_window","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","mcwbiomesoplenty:stripped_magic_pane_window","chemlib:lead_ingot_from_blasting_lead_dust","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","appmek:chemical_storage_cell_16k","ae2:network/cables/smart_brown","create:crafting/kinetics/white_seat","chemlib:gallium_ingot_to_block","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","ae2:network/cables/covered_orange","mcwroofs:stone_bricks_steep_roof","mcwwindows:red_curtain","mcwtrpdoors:oak_mystic_trapdoor","create:crafting/logistics/brass_funnel","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","refinedstorage:coloring_recipes/white_network_transmitter","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","refinedstorage:coloring_recipes/black_disk_manipulator","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","utilitarian:utility/oak_logs_to_doors","railways:crafting/smokestack_diesel","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","mcwdoors:jungle_whispering_door","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","refinedstorage:coloring_recipes/cyan_crafter","mcwlights:bamboo_tiki_torch","silentgear:azure_electrum_dust","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","create:crafting/logistics/display_link","ad_astra:airlock","mcwroofs:grass_steep_roof","cfm:pink_picket_gate","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","chemlib:magnesium_ingot_from_blasting_magnesium_dust","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","croptopia:egg_roll","mcwbiomesoplenty:dead_waffle_door","twigs:bamboo_mat","connectedglass:borderless_glass_black2","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","minecraft:red_stained_glass_pane_from_glass_pane","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","ae2:network/cells/item_storage_cell_1k_storage","mcwfurnitures:stripped_acacia_drawer_counter","alltheores:lead_ingot_from_ore_blasting","expatternprovider:oversize_interface_alt","twigs:silt_brick","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","refinedstorage:coloring_recipes/black_network_transmitter","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","mcwbiomesoplenty:pine_bamboo_door","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","minecraft:orange_candle","supplementaries:flags/flag_purple","create:framed_glass_from_glass_colorless_stonecutting","botania:quartz_sunny","mcwbiomesoplenty:stripped_redwood_log_window","mcwfurnitures:stripped_acacia_cupboard_counter","mcwbiomesoplenty:dead_swamp_door","handcrafted:birch_chair","chemlib:lead_ingot_from_smelting_lead_dust","enderio:infinity_rod","railcraft:signal_lamp","refinedstorage:coloring_recipes/orange_crafter_manager","cfm:jungle_blinds","tombstone:bone_needle","minecraft:stone_brick_wall","forbidden_arcanus:deorum_pressure_plate","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","refinedstorage:coloring_recipes/pink_relay","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:decoration_table","mcwbiomesoplenty:redwood_classic_door","forbidden_arcanus:deorum_chain","chemlib:manganese_ingot_from_blasting_manganese_dust","mcwbiomesoplenty:stripped_redwood_pane_window","chemlib:barium_ingot_to_block","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","delightful:knives/draco_arcanus_knife","pneumaticcraft:paper_from_tag_filter","refinedstorage:coloring_recipes/black_wireless_transmitter","utilitarian:utility/glow_ink_sac","mcwwindows:acacia_window2","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwroofs:white_top_roof","mcwwindows:blackstone_window2","mcwbiomesoplenty:magic_waffle_door","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","botania:floating_pure_daisy","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","mcwbiomesoplenty:redwood_cottage_door","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","ae2:network/cables/dense_smart_cyan","alltheores:silver_ingot_from_ore_blasting","cfm:magenta_picket_fence","travelersbackpack:dye_orange_sleeping_bag","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","cfm:acacia_bedside_cabinet","securitycraft:secret_mangrove_sign_item","mcwdoors:oak_stable_door","refinedstorage:coloring_recipes/orange_disk_manipulator","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","twilightforest:minoshroom_banner_pattern","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwroofs:light_gray_terracotta_roof","refinedstorage:coloring_recipes/red_fluid_grid","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","forbidden_arcanus:golden_orchid_seeds","dyenamics:fluorescent_concrete_powder","botania:incense_plate","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwpaths:dark_prismarine_square_paving","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:lime_terracotta_steep_roof","twilightforest:canopy_chest_boat","mcwlights:golden_small_chandelier","bambooeverything:dry_bamboo","mcwbiomesoplenty:willow_paper_door","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","refinedstorage:coloring_recipes/pink_crafter","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwpaths:dark_prismarine_running_bond","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","ae2:network/parts/panels_semi_dark_monitor","botania:lime_petal_block","allthemodium:unobtainium_rod","bloodmagic:blood_rune_capacity_2","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","refinedstorage:coloring_recipes/cyan_crafting_grid","mcwbiomesoplenty:redwood_swamp_door","farmersdelight:cooking/baked_cod_stew","immersiveengineering:crafting/wirecoil_structure_rope","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","alltheores:lead_rod","mcwbiomesoplenty:jacaranda_western_door","mcwwindows:oak_plank_window","refinedstorage:coloring_recipes/blue_controller","nethersdelight:hoglin_sirloin","mcwwindows:oak_log_parapet","mcwbiomesoplenty:hellbark_modern_door","refinedstorage:coloring_recipes/yellow_crafting_monitor","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","mcwfurnitures:stripped_acacia_modern_wardrobe","travelersbackpack:spider_smithing","utilitarian:no_soliciting/soliciting_carpets/yellow_trapped_soliciting_carpet","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","minecraft:iron_block","refinedstorage:coloring_recipes/security_manager","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","ae2:network/cells/item_storage_components_cell_64k_part","mcwdoors:acacia_beach_door","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwroofs:black_terracotta_steep_roof","mcwbridges:bamboo_bridge_pier","minecraft:golden_apple","minecraft:diamond_pickaxe","domum_ornamentum:blockbarreldeco_onside","create:crafting/schematics/empty_schematic","silentgear:tyrian_steel_ingot_from_block","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","mcwroofs:orange_terracotta_upper_steep_roof","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","aether:iron_helmet_repairing","ae2:network/cables/smart_purple","farmersdelight:steak_and_potatoes","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","utilitarian:utility/acacia_logs_to_stairs","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","cfm:acacia_coffee_table","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","refinedstorage:coloring_recipes/magenta_crafter_manager","allthecompressed:compress/honey_block_1x","allthemodium:vibranium_plate","securitycraft:codebreaker","rftoolsutility:energy_module","supplementaries:flute","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","cfm:light_blue_picket_gate","securitycraft:reinforced_glass_pane","constructionwand:infinity_wand","farmersdelight:golden_knife","minecraft:orange_stained_glass","bloodmagic:blood_rune_speed_2","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","minecraft:redstone_from_smelting_redstone_ore","mcwroofs:black_roof","securitycraft:reinforced_gray_stained_glass_pane_from_glass","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","utilitarian:no_soliciting/soliciting_carpets/brown_trapped_soliciting_carpet","cfm:blue_kitchen_counter","mcwbiomesoplenty:maple_four_window","dyenamics:spring_green_stained_glass_pane_from_glass_pane","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","chemlib:magnesium_nugget_to_ingot","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwbridges:end_stone_bricks_bridge_stair","mcwroofs:base_top_roof","mcwdoors:bamboo_classic_door","ae2:network/cables/smart_magenta","securitycraft:harming_module","minecraft:golden_boots","silentgear:bort_block","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","refinedstorage:coloring_recipes/green_security_manager","mythicbotany:mana_collector","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","mcwpaths:dark_prismarine_crystal_floor_path","supplementaries:statue","immersiveengineering:crafting/tinted_glass_lead_wire","mcwbiomesoplenty:mahogany_tropical_door","create:crafting/kinetics/clockwork_bearing","cfm:pink_grill","minecraft:polished_granite_slab","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","forbidden_arcanus:arcane_edelwood_planks","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","securitycraft:secret_jungle_sign_item","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","ae2:network/crafting/cpu_crafting_accelerator","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","refinedstorage:coloring_recipes/black_crafter","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","dimstorage:dimensional_tablet","ad_astra:small_orange_industrial_lamp","rftoolsbase:machine_base","mcwbiomesoplenty:maple_stable_head_door","rftoolsutility:counterplus_module","mcwroofs:white_terracotta_upper_steep_roof","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_piston","cfm:purple_kitchen_counter","securitycraft:reinforced_yellow_stained_glass","minecraft:stick_from_bamboo_item","botania:swap_ring","mcwwindows:jungle_log_parapet","refinedstorage:coloring_recipes/pink_pattern_grid","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","mcwfurnitures:acacia_double_drawer","aquaculture:tin_can_to_iron_nugget","alltheores:enderium_plate","mcwwindows:yellow_curtain","connectedglass:clear_glass_orange_pane2","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","refinedstorage:coloring_recipes/gray_wireless_transmitter","allthecompressed:compress/acacia_log_1x","forbidden_arcanus:edelwood_fence","pneumaticcraft:pressure_chamber_wall","modularrouters:player_module","minecraft:glass","xnet:connector_yellow","mcwbiomesoplenty:pine_classic_door","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:end_stone_brick_stairs","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","alltheores:raw_iridium_block","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwbiomesoplenty:pine_cottage_door","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","ae2additions:components/super/64k","ae2:network/cables/smart_red","create:crafting/kinetics/nixie_tube","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","minecraft:light_blue_concrete_powder","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","refinedstorage:coloring_recipes/cyan_crafting_monitor","mcwwindows:light_blue_mosaic_glass","appmek:chemical_storage_cell_64k","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","refinedstorage:coloring_recipes/gray_crafting_monitor","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","chimes:bamboo_chimes","securitycraft:secret_spruce_sign_item","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwbiomesoplenty:pine_nether_door","cfm:oak_upgraded_fence","mob_grinding_utils:recipe_mob_swab","mcwbiomesoplenty:hellbark_barn_glass_door","deepresonance:radiation_monitor","railcraft:steel_gear","immersiveengineering:crafting/treated_wood_horizontal","allthemodium:piglich_heart_block","sophisticatedstorage:crafting_upgrade","bigreactors:smelting/graphite_from_charcoal","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","mcwroofs:green_concrete_top_roof","refinedstorage:coloring_recipes/orange_crafter","connectedglass:tinted_borderless_glass_orange2","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","minecraft:dye_orange_wool","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","utilitarian:utility/acacia_logs_to_slabs","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","mcwbiomesoplenty:jacaranda_bamboo_door","botania:quartz_lavender","occultism:blasting/iesnium_ingot","chemlib:beryllium_ingot_to_block","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","reliquary:glowing_water_from_potion_vial","cfm:yellow_kitchen_drawer","twilightforest:dark_boat","biomesoplenty:umbran_boat","minecraft:gunpowder","travelersbackpack:bookshelf","bloodmagic:blood_rune_acceleration_2","immersiveengineering:crafting/wire_lead","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","alltheores:tin_ingot_from_ore_blasting","mcwbiomesoplenty:redwood_four_panel_door","twilightforest:alpha_yeti_banner_pattern","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","refinedstorage:coloring_recipes/orange_crafting_grid","pylons:potion_filter","enderio:resetting_lever_thirty","minecraft:glistering_melon_slice","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","refinedstorage:coloring_recipes/green_network_receiver","refinedstorage:coloring_recipes/red_wireless_transmitter","handcrafted:quartz_corner_trim","ad_astra:steel_factory_block","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","refinedstorage:coloring_recipes/light_gray_crafter_manager","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","mcwroofs:light_gray_concrete_lower_roof","supplementaries:candle_holders/candle_holder_black_dye","twigs:rhyolite_slab","occultism:crafting/spirit_torch","chemlib:potassium_nugget_to_ingot","nethersdelight:soul_compost_from_hoglin_hide","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:reinforced_bricks_from_tile","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwfurnitures:acacia_table","mcwroofs:lime_terracotta_upper_steep_roof","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","mcwwindows:cyan_curtain","ae2:network/cables/smart_yellow","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","allthecompressed:compress/tuff_1x","mcwtrpdoors:bamboo_blossom_trapdoor","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","mcwbiomesoplenty:fir_tropical_door","securitycraft:secret_sign_item","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","aquaculture:heavy_hook","handcrafted:birch_fancy_bed","bloodmagic:hellforged_block_to_ingot","mcwbiomesoplenty:palm_western_door","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","mcwwindows:acacia_window","refinedstorage:coloring_recipes/green_controller","croptopia:food_press","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","mcwwindows:stripped_crimson_stem_four_window","utilitix:spruce_shulker_boat_with_shell","immersiveengineering:crafting/powerpack","advanced_ae:import_export_bus","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","mcwpaths:dark_prismarine_running_bond_stairs","ae2:network/cables/glass_green","mcwbiomesoplenty:willow_classic_door","ae2:tools/fluix_hoe","mcwroofs:orange_concrete_lower_roof","farmersdelight:nether_salad","cfm:magenta_kitchen_counter","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","mcwlights:orange_lamp","pneumaticcraft:reinforced_brick_pillar","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","mcwwindows:dark_oak_pane_window","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","supplementaries:boat_jar","create:tuff_pillar_from_stone_types_tuff_stonecutting","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","refinedstorage:coloring_recipes/white_controller","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","mcwdoors:acacia_swamp_door","minecraft:dark_prismarine_slab_from_dark_prismarine_stonecutting","aquaculture:fishing_line","forbidden_arcanus:obsidian_skull","cfm:stripped_jungle_kitchen_sink_dark","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","dyenamics:wine_concrete_powder","railcraft:charge_terminal","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:end_stone_brick_slab","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","minecraft:cauldron","botania:glimmering_livingwood_from_log","delightful:smelting/roasted_acorn","mcwpaths:dark_prismarine_crystal_floor_stairs","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","silentgear:blueprint_paper","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","mcwdoors:bamboo_beach_door","ae2:block_cutter/walls/smooth_sky_stone_wall","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","cfm:cyan_kitchen_sink","dyenamics:peach_concrete_powder","mcwbiomesoplenty:dead_beach_door","sophisticatedbackpacks:refill_upgrade","ae2additions:components/super/4k","supplementaries:faucet","chemlib:gallium_ingot_from_smelting_gallium_dust","utilitix:directional_rail","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","mcwfurnitures:stripped_acacia_covered_desk","minecraft:popped_chorus_fruit","mcwbiomesoplenty:fir_barn_glass_door","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","alltheores:enderium_rod","corail_woodcutter:cherry_woodcutter","handcrafted:birch_bench","refinedstorage:coloring_recipes/blue_security_manager","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwroofs:stone_bricks_top_roof","mcwdoors:bamboo_bark_glass_door","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","cfm:magenta_trampoline","supplementaries:pulley","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","chemlib:zirconium_ingot_from_blasting_zirconium_dust","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","refinedstorage:coloring_recipes/gray_crafting_grid","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","silentgear:azure_repair_kit","refinedstorage:coloring_recipes/lime_security_manager","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","farmersdelight:cooking/mushroom_rice","refinedstorage:coloring_recipes/gray_crafter","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","ae2:network/cells/item_storage_cell_4k","mcwroofs:brown_terracotta_top_roof","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","dyenamics:navy_dye","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwbiomesoplenty:hellbark_stable_head_door","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:jacaranda_swamp_door","mcwroofs:grass_lower_roof","refinedstorage:coloring_recipes/gray_network_transmitter","sfm:fancy_cable","refinedstorage:coloring_recipes/magenta_pattern_grid","mcwroofs:gutter_base_red","mcwtrpdoors:acacia_glass_trapdoor","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","mcwdoors:print_bamboo","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","refinedstorage:coloring_recipes/yellow_crafting_grid","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","bigreactors:crafting/blutonium_ingot_to_nugget","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","mcwfurnitures:acacia_modern_desk","sophisticatedbackpacks:chipped/carpenters_table_upgrade","minecraft:copper_ingot_from_smelting_raw_copper","create:crafting/kinetics/mechanical_arm","mcwbiomesoplenty:stripped_maple_log_window2","advanced_ae:stock_export_bus","cfm:dye_black_picket_fence","chemlib:zirconium_ingot_to_block","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","minecraft:netherite_ingot","naturalist:bug_net","minecraft:birch_sign","botania:dye_lime","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_3","mcwbiomesoplenty:jacaranda_mystic_door","sophisticatedstorage:oak_limited_barrel_4","dyenamics:navy_terracotta","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","mcwfurnitures:acacia_end_table","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","refinedstorage:coloring_recipes/green_disk_manipulator","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","railcraft:bushing_gear_brass","refinedstorage:coloring_recipes/lime_grid","ae2:tools/portable_item_cell_16k","enderio:basic_capacitor","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","railcraft:steel_tank_valve","cfm:cyan_cooler","ae2:network/parts/formation_plane","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwdoors:acacia_modern_door","ad_astra:steel_rod","mcwbiomesoplenty:mahogany_mystic_door","mcwwindows:prismarine_four_window","occultism:smelting/iesnium_ingot","mcwroofs:light_blue_concrete_top_roof","refinedstorage:coloring_recipes/magenta_wireless_transmitter","mcwroofs:light_gray_roof","securitycraft:secret_jungle_hanging_sign","mcwbiomesoplenty:umbran_beach_door","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","dyenamics:bubblegum_candle","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:umbran_bark_glass_door","twilightforest:wood/dark_wood","mcwdoors:bamboo_tropical_door","connectedglass:scratched_glass_orange2","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","travelersbackpack:redstone","mcwfurnitures:stripped_acacia_end_table","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","minecraft:slime_block","minecraft:acacia_planks","twilightforest:magic_map_focus","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","dyenamics:lavender_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:fir_nether_door","chimes:glass_bells","mcwwindows:iron_shutter","mcwroofs:birch_planks_top_roof","refinedstorage:coloring_recipes/purple_grid","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","aether:diamond_chestplate_repairing","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","bigreactors:turbine/basic/casing","travelersbackpack:emerald","farmersdelight:cutting_board","croptopia:trail_mix","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","bloodmagic:smelting/ingot_netherite_scrap","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","bloodmagic:blood_rune_sac_2","ad_astra:nasa_workbench","cfm:birch_crate","ad_astra:etrionic_capacitor","mcwbiomesoplenty:empyreal_japanese2_door","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwbridges:blackstone_bridge_pier","modularrouters:flinger_module","mcwbiomesoplenty:empyreal_swamp_door","ae2:tools/nether_quartz_axe","mcwroofs:birch_planks_lower_roof","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/pink_trapped_soliciting_carpet","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","enderio:resetting_lever_sixty_inv","railcraft:steel_shovel","mcwpaths:blackstone_windmill_weave_path","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","minecraft:magenta_stained_glass_pane_from_glass_pane","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:magenta_terracotta_attic_roof","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","mcwfurnitures:stripped_acacia_coffee_table","alltheores:copper_rod","minecraft:shulker_box","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","ae2:network/cables/covered_blue","dyenamics:maroon_dye","utilitix:filter_rail","aether:diamond_gloves","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","enderio:resetting_lever_sixty_from_inv","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","mcwbiomesoplenty:dead_stable_door","minecraft:cracked_stone_bricks","securitycraft:secret_birch_sign_item","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","allthecompressed:compress/vibranium_block_1x","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","arseng:source_storage_cell_4k","refinedstorage:coloring_recipes/black_pattern_grid","ae2:network/cells/fluid_storage_cell_4k","minecraft:magenta_stained_glass","minecraft:ender_chest","refinedstorage:coloring_recipes/lime_crafter_manager","mcwbiomesoplenty:willow_western_door","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","ae2:network/cables/dense_covered_yellow","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","mcwpaths:cobbled_deepslate_running_bond_path","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","modularrouters:void_module","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","immersiveengineering:crafting/redstone_acid","dankstorage:2_to_3","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","enderio:vibrant_capacitor_bank","refinedstorage:coloring_recipes/blue_detector","allthemodium:vibranium_ingot_from_raw_smelting","refinedstorage:coloring_recipes/lime_controller","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","chemlib:manganese_ingot_to_block","biomesoplenty:maple_boat","bigreactors:reactor/reinforced/chargingfe","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","aether:golden_boots_repairing","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","travelersbackpack:nether","mcwbiomesoplenty:redwood_plank_pane_window","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","securitycraft:reinforced_warped_fence_gate","mcwfurnitures:acacia_bookshelf","refinedstorage:coloring_recipes/cyan_fluid_grid","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","refinedstorage:coloring_recipes/yellow_network_transmitter","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","caupona:clay_cistern","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","forbidden_arcanus:edelwood_pressure_plate","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","deepresonance:lens","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","refinedstorage:coloring_recipes/green_crafter_manager","refinedstorage:coloring_recipes/light_gray_pattern_grid","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","occultism:smelting/iesnium_ingot_from_dust","bigreactors:turbine/reinforced/controller","minecraft:redstone_lamp","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/yellow_pattern_grid","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","cfm:stripped_acacia_crate","mcwbiomesoplenty:dead_barn_glass_door","allthecompressed:compress/cobbled_deepslate_1x","twilightdelight:berry_stick","handcrafted:terracotta_bowl","silentgear:stone_torch","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","ae2:tools/fluix_axe","cfm:black_kitchen_sink","mcwwindows:red_mosaic_glass","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwfurnitures:stripped_acacia_double_wardrobe","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwbiomesoplenty:pine_western_door","utilitarian:utility/acacia_logs_to_trapdoors","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","modularrouters:energy_output_module","mcwbiomesoplenty:hellbark_japanese_door","mcwlights:glowstone_slab","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","appbot:mana_storage_cell_64k","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","aether:cold_parachute","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","minecraft:polished_blackstone_pressure_plate","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","securitycraft:secret_dark_oak_hanging_sign","alltheores:diamond_rod","mcwbiomesoplenty:palm_stable_door","minecraft:stone_slab_from_stone_stonecutting","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","refinedstorage:coloring_recipes/lime_pattern_grid","mcwwindows:bamboo_shutter","chemlib:sodium_nugget_to_ingot","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","mcwbiomesoplenty:palm_four_panel_door","ae2:network/crafting/64k_cpu_crafting_storage","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","create:crafting/kinetics/placard","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:birch_button","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","refinedstorage:coloring_recipes/green_relay","ae2:network/cables/dense_smart_brown","forbidden_arcanus:blasting/arcane_crystal_from_blasting","mcwbiomesoplenty:dead_hedge","botania:petal_pink","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","minecraft:honey_bottle","immersiveengineering:crafting/chute_copper","pneumaticcraft:charging_station","mcwfences:prismarine_grass_topped_wall","occultism:crafting/book_of_calling_foliot_transport_items","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwdoors:bamboo_barn_glass_door","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","chemlib:indium_ingot_from_smelting_indium_dust","mcwbiomesoplenty:fir_glass_door","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","alltheores:zinc_ingot_from_ore_blasting","create:crafting/kinetics/crafter_slot_cover","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","minecraft:netherite_machete_smithing","mcwbridges:balustrade_blackstone_bridge","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","refinedstorage:coloring_recipes/purple_relay","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","refinedstorage:coloring_recipes/light_gray_relay","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","supplementaries:sign_post_birch","securitycraft:secret_acacia_hanging_sign","minecraft:quartz_block","alltheores:iron_dust_from_hammer_crushing","minecraft:brick","immersiveengineering:crafting/toolupgrade_railgun_capacitors","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","mcwroofs:lime_concrete_top_roof","mcwroofs:purple_terracotta_attic_roof","bloodmagic:blood_rune_charging_2","utilitarian:no_soliciting/soliciting_carpets/magenta_trapped_soliciting_carpet","mcwroofs:orange_terracotta_lower_roof","refinedstorage:coloring_recipes/cyan_network_receiver","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwbiomesoplenty:mahogany_bamboo_door","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwroofs:brown_terracotta_upper_steep_roof","mcwdoors:oak_swamp_door","enderio:resetting_lever_sixty_from_prev","aether:iron_axe_repairing","mcwwindows:mud_brick_arrow_slit","botania:dye_gray","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","alltheores:electrum_ingot_from_dust_blasting","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","refinedstorage:coloring_recipes/cyan_crafter_manager","refinedstorage:coloring_recipes/gray_controller","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","mcwbiomesoplenty:willow_mystic_door","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","mcwbiomesoplenty:pine_modern_door","handcrafted:wither_skeleton_trophy","aether:wooden_hoe_repairing","mcwroofs:blue_terracotta_top_roof","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","minecraft:brown_dye","mcwtrpdoors:acacia_mystic_trapdoor","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","handcrafted:terracotta_medium_pot","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:umbran_paper_door","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","refinedstorage:coloring_recipes/gray_disk_manipulator","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","mcwroofs:gray_concrete_upper_steep_roof","botania:fel_pumpkin","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwbiomesoplenty:empyreal_stable_door","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","ae2:network/cells/fluid_storage_cell_64k_storage","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","refinedstorage:coloring_recipes/purple_security_manager","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","ae2:network/cells/fluid_storage_cell_16k","mcwroofs:pink_concrete_lower_roof","railcraft:diamond_spike_maul","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","allthetweaks:greg_star_from_gregstar_block","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwroofs:gray_roof_block","mcwbiomesoplenty:fir_plank_four_window","botania:mushroom_11","cfm:white_kitchen_drawer","ae2:network/crafting/1k_cpu_crafting_storage","botania:mushroom_10","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","ae2:network/cables/smart_pink","mcwbiomesoplenty:pine_horse_fence","refinedstorage:coloring_recipes/green_detector","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","create:smelting/zinc_ingot_from_ore","mcwwindows:acacia_four_window","chemlib:potassium_ingot_from_blasting_potassium_dust","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","mcwdoors:bamboo_nether_door","mcwbiomesoplenty:hellbark_window","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","refinedstorage:coloring_recipes/light_gray_security_manager","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","reliquary:witherless_rose","dyenamics:honey_stained_glass","cfm:stripped_acacia_cabinet","create:crafting/kinetics/elevator_pulley","mcwfences:sandstone_grass_topped_wall","alltheores:invar_rod","allthemodium:vibranium_ingot_from_dust_smelting","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","twilightforest:compressed_blocks/carminite_block","mcwfurnitures:oak_bookshelf_cupboard","travelersbackpack:pumpkin","create:crafting/kinetics/attribute_filter","supplementaries:key","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfurnitures:stripped_oak_table","allthemodium:unobtainium_ingot_from_dust_smelting","blue_skies:maple_bookshelf","rftoolspower:power_core1","pneumaticcraft:logistics_frame_default_storage","ae2:network/blocks/io_port","mcwpaths:blackstone_windmill_weave_stairs","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","refinedstorage:coloring_recipes/light_gray_detector","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","mcwbiomesoplenty:redwood_window2","refinedstorage:coloring_recipes/purple_detector","create:crafting/kinetics/purple_seat","twilightforest:equipment/fiery_iron_sword","mcwfurnitures:stripped_acacia_stool_chair","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","botania:mana_pylon","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","silentgear:crimson_repair_kit","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","chemlib:zirconium_ingot_to_nugget","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","mekanismtools:osmium/armor/boots","simplylight:illuminant_red_block_on_toggle","cfm:acacia_table","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwtrpdoors:bamboo_barn_trapdoor","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","alltheores:platinum_ingot_from_ore","refinedstorage:coloring_recipes/brown_disk_manipulator","mcwtrpdoors:jungle_mystic_trapdoor","mcwbiomesoplenty:dead_barn_door","refinedstorage:coloring_recipes/network_transmitter","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","mcwtrpdoors:jungle_glass_trapdoor","chemlib:indium_ingot_from_blasting_indium_dust","securitycraft:reinforced_oak_fence","railcraft:feed_station","minecraft:black_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","arseng:source_storage_cell_64k","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","immersiveengineering:crafting/plate_uranium_hammering","refinedstorage:coloring_recipes/cyan_disk_manipulator","mcwdoors:bamboo_paper_door","handcrafted:oak_nightstand","railcraft:personal_world_spike","immersiveengineering:crafting/armor_steel_chestplate","refinedstorage:coloring_recipes/light_gray_crafter","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","refinedstorage:coloring_recipes/green_pattern_grid","mysticalagriculture:prudentium_block_combine","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","mcwfurnitures:acacia_modern_wardrobe","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","expatternprovider:water_cell","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","refinedstorage:coloring_recipes/magenta_fluid_grid","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","minecraft:dark_prismarine_stairs_from_dark_prismarine_stonecutting","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:fir_four_panel_door","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","handcrafted:birch_couch","farmersdelight:cooking/fish_stew","supplementaries:slingshot","allthemodium:allthemodium_plate","advanced_ae:quantumunit","mcwdoors:acacia_japanese2_door","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","travelersbackpack:dye_black_sleeping_bag","minecraft:activator_rail","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","everythingcopper:copper_rail","refinedstorage:coloring_recipes/brown_crafting_grid","cfm:black_grill","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","cfm:birch_desk_cabinet","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_four_panel_door","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","cfm:crimson_kitchen_drawer","travelersbackpack:end","chemlib:strontium_block_to_ingot","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","dyenamics:rose_candle","aquaculture:neptunium_ingot_from_nuggets","minecraft:iron_pickaxe","aether:shield_repairing","refinedstorage:coloring_recipes/light_gray_wireless_transmitter","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","bigreactors:crafting/cyanite_storage_to_component","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","ae2:network/cables/glass_orange","deepresonance:resonating_plate","minecraft:vibranium_mage_chestplate_smithing","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","reliquary:angelheart_vial","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","mcwbiomesoplenty:redwood_stable_head_door","dyenamics:aquamarine_candle","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwtrpdoors:bamboo_classic_trapdoor","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwbiomesoplenty:empyreal_western_door","mcwroofs:black_concrete_roof","occultism:crafting/lenses","cfm:orange_cooler","chemlib:strontium_ingot_to_nugget","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwtrpdoors:bamboo_barred_trapdoor","mcwbiomesoplenty:empyreal_japanese_door","mcwroofs:acacia_lower_roof","dyenamics:mint_candle","mcwbridges:balustrade_mossy_stone_bricks_bridge","expatternprovider:epp_part","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","refinedstorage:coloring_recipes/brown_wireless_transmitter","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:hellbark_paper_door","cfm:white_kitchen_counter","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","mcwfurnitures:acacia_coffee_table","alltheores:enderium_ingot_from_dust","ae2:network/cables/covered_fluix_clean","mcwbiomesoplenty:umbran_stable_door","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwpaths:dark_prismarine_running_bond_slab","mcwbiomesoplenty:willow_stable_head_door","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","trashcans:energy_trash_can","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","pneumaticcraft:air_compressor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","megacells:crafting/decompression_module","mcwdoors:bamboo_modern_door","minecolonies:baked_salmon","cfm:fridge_dark","chimes:copper_chimes","handcrafted:birch_desk","minecraft:birch_fence","mcwbiomesoplenty:willow_window2","silentgear:azure_electrum_ingot_from_nugget","mcwbiomesoplenty:stripped_mahogany_log_four_window","create:crafting/kinetics/brass_door","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","chemlib:sodium_ingot_to_block","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","twigs:weeping_polished_blackstone_bricks","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","expatternprovider:wireless_connector","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","alltheores:steel_dust_from_alloy_blending","forbidden_arcanus:clibano_combustion/arcane_crystal_from_clibano_combusting","mcwroofs:black_terracotta_lower_roof","immersiveengineering:crafting/radiator","minecraft:white_stained_glass_pane_from_glass_pane","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","ae2:network/cables/dense_covered_fluix_clean","littlelogistics:switch_rail","minecraft:scaffolding","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwbiomesoplenty:fir_cottage_door","nethersdelight:blackstone_furnace","mcwroofs:purple_concrete_attic_roof","constructionwand:core_angel","mcwfurnitures:acacia_bookshelf_cupboard","enderio:resetting_lever_thirty_from_prev","sophisticatedstorage:birch_chest","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","utilitarian:no_soliciting/soliciting_carpets/light_gray_trapped_soliciting_carpet","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwbiomesoplenty:willow_glass_door","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","delightful:knives/tin_knife","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwlights:copper_candle_holder","minecraft:mossy_stone_brick_slab","mcwtrpdoors:bamboo_swamp_trapdoor","mcwfurnitures:acacia_covered_desk","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:bamboo_chest_raft","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","minecraft:honey_block","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","farmersdelight:melon_juice","sophisticatedbackpacks:crafting_upgrade","twilightdelight:cooking/fiery_snakes_block","aquaculture:birch_fish_mount","bigreactors:crafting/cyanite_component_to_storage","mcwwindows:stripped_mangrove_log_window","securitycraft:secret_crimson_hanging_sign","pneumaticcraft:standby_upgrade","buildinggadgets2:template_manager","occultism:crafting/iesnium_ingot_from_block","mcwwindows:jungle_window","minecraft:cobblestone_stairs","refinedstorage:coloring_recipes/purple_fluid_grid","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","cfm:birch_table","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","cfm:stripped_acacia_coffee_table","mcwbiomesoplenty:empyreal_barn_glass_door","tombstone:ankh_of_prayer","cfm:white_sofa","refinedstorage:coloring_recipes/red_crafting_monitor","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","utilitix:dark_oak_shulker_boat_with_shell","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","allthecompressed:compress/uranium_block_1x","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","refinedstorage:coloring_recipes/blue_grid","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","connectedglass:tinted_borderless_glass_black2","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","allthecompressed:compress/melon_1x","pneumaticcraft:heat_sink","mcwbiomesoplenty:stripped_fir_log_four_window","mcwlights:magenta_lamp","nethersdelight:hoglin_sirloin_from_smoking","chemlib:gallium_ingot_to_nugget","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","mcwbiomesoplenty:maple_swamp_door","refinedstorage:coloring_recipes/blue_crafter_manager","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","sophisticatedstorage:stonecutter_upgrade","utilitarian:utility/oak_logs_to_pressure_plates","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","handcrafted:oak_bench","mcwbiomesoplenty:maple_bark_glass_door","bigreactors:turbine/basic/controller","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwpaths:dark_prismarine_flagstone_stairs","refinedstorage:coloring_recipes/yellow_detector","mcwwindows:birch_louvered_shutter","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","minecraft:red_concrete_powder","mcwbiomesoplenty:maple_stable_door","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","chemlib:magnesium_ingot_to_block","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwpaths:blackstone_crystal_floor_path","mcwbiomesoplenty:empyreal_glass_door","cfm:jungle_cabinet","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwtrpdoors:acacia_beach_trapdoor","mcwroofs:base_roof_block","handcrafted:birch_pillar_trim","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","twigs:blackstone_column","mcwbiomesoplenty:palm_window","mcwfurnitures:acacia_lower_bookshelf_drawer","botania:conversions/pink_petal_block_deconstruct","mcwbiomesoplenty:fir_waffle_door","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:navy_candle","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","minecraft:polished_granite_slab_from_polished_granite_stonecutting","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","securitycraft:reinforced_orange_stained_glass_pane_from_dye","cfm:jungle_chair","chemlib:barium_block_to_ingot","mcwlights:birch_tiki_torch","twilightforest:equipment/fiery_iron_pickaxe","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","mcwbiomesoplenty:pine_beach_door","twilightforest:time_chest_boat","create:crafting/logistics/content_observer","refinedstorage:coloring_recipes/gray_network_receiver","refinedstorage:coloring_recipes/green_wireless_transmitter","sophisticatedbackpacks:chipped/glassblower_upgrade","utilitix:acacia_shulker_boat_with_shell","allthecompressed:compress/atm_star_block_3x","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:nether_bricks","enderio:resetting_lever_three_hundred_inv_from_prev","allthecompressed:compress/birch_planks_1x","refinedstorage:coloring_recipes/white_network_receiver","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","twilightforest:naga_banner_pattern","croptopia:tuna_roll","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","mcwpaths:blackstone_square_paving","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","mcwdoors:garage_gray_door","railcraft:world_spike","handcrafted:witch_trophy","mcwfurnitures:stripped_oak_counter","ae2:network/cells/item_storage_components_cell_256k_part","minecraft:polished_andesite_from_andesite_stonecutting","allthearcanistgear:vibranium_robes_smithing","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","refinedstorage:coloring_recipes/orange_fluid_grid","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","minecraft:oak_pressure_plate","botania:mushroom_5","ae2:misc/chests_smooth_sky_stone","botania:mushroom_4","minecraft:stone_brick_stairs","aether:skyroot_beehive","bigreactors:turbine/basic/activefluidport_forge","botania:mushroom_3","refinedstorage:coloring_recipes/red_network_transmitter","bigreactors:reprocessor/outputport","cfm:brown_grill","mcwroofs:gray_roof","mcwwindows:quartz_window","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","supplementaries:present","bloodmagic:blood_rune_orb","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","refinedstorage:coloring_recipes/purple_wireless_transmitter","mcwroofs:gutter_base_brown","cfm:acacia_crate","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","botania:glimmering_livingwood_log","mcwbiomesoplenty:redwood_tropical_door","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","dyenamics:peach_candle","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","securitycraft:reinforced_chiseled_bookshelf","utilitix:mangrove_shulker_boat_with_shell","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","refinedstorage:coloring_recipes/magenta_crafter","minecraft:clock","mcwroofs:red_concrete_top_roof","mcwroofs:birch_planks_attic_roof","securitycraft:portable_tune_player","utilitarian:no_soliciting/soliciting_carpets/black_trapped_soliciting_carpet","enderio:entity_filter","mcwroofs:birch_planks_upper_steep_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","railcraft:lead_gear","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","allthetweaks:greg_star_block","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","silentgear:diamond_shard","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","cfm:lime_picket_fence","aether:leather_leggings_repairing","dyenamics:bed/conifer_bed","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:peach_stained_glass","dyenamics:bed/amber_bed","mcwdoors:acacia_western_door","minecraft:diamond_leggings","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwbiomesoplenty:maple_paper_door","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","silentgear:leather_scrap","mcwroofs:purple_terracotta_roof","alltheores:tin_rod","connectedglass:scratched_glass_orange_pane2","mcwbridges:rope_birch_bridge","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","aether:stone_hoe_repairing","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","minecraft:dye_black_bed","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","mcwbiomesoplenty:magic_western_door","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","biomesoplenty:empyreal_chest_boat","twigs:rhyolite","railcraft:brass_gear","pylons:infusion_pylon","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","mcwbiomesoplenty:pine_swamp_door","expatternprovider:assembler_matrix_speed","utilitix:mob_yoinker","mcwwindows:andesite_four_window","chemlib:manganese_ingot_from_smelting_manganese_dust","mcwlights:golden_low_candle_holder","supplementaries:feather_block","enderio:advanced_item_filter","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/cyan_trapped_soliciting_carpet","utilitarian:no_soliciting/soliciting_carpets/red_trapped_soliciting_carpet","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","refinedstorage:coloring_recipes/purple_network_transmitter","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","dyenamics:honey_candle","mcwbiomesoplenty:magic_tropical_door","botania:conversions/gray_petal_block_deconstruct","securitycraft:block_change_detector","croptopia:fruit_salad","supplementaries:pancake_fd","twigs:rocky_dirt","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","modularrouters:placer_module","minecraft:redstone_from_blasting_deepslate_redstone_ore","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","minecraft:netherite_scrap_from_blasting","mcwbiomesoplenty:dead_bamboo_door","enderio:resetting_lever_three_hundred_from_prev","utilitarian:no_soliciting/soliciting_carpets/purple_trapped_soliciting_carpet","mcwroofs:pink_terracotta_lower_roof","mcwtrpdoors:bamboo_four_panel_trapdoor","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","silentgear:azure_electrum_dust_smelting","refinedstorage:coloring_recipes/orange_security_manager","mcwbridges:bridge_torch","refinedstorage:coloring_recipes/orange_detector","botania:white_shiny_flower","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","undergarden:wigglewood_boat","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","refinedstorage:coloring_recipes/orange_crafting_monitor","createoreexcavation:vein_atlas","additionallanterns:quartz_chain","twilightforest:sorting_chest_boat","refinedstorage:coloring_recipes/magenta_disk_manipulator","mcwwindows:birch_curtain_rod","forbidden_arcanus:blasting/rune_from_blasting","aether:diamond_axe_repairing","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","pneumaticcraft:pressure_chamber_glass_x4","refinedstorage:coloring_recipes/lime_crafting_monitor","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","minecraft:redstone_from_smelting_deepslate_redstone_ore","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","chemlib:barium_nugget_to_ingot","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","mcwbiomesoplenty:pine_bark_glass_door","domum_ornamentum:sand_bricks","twilightdelight:cooking/tear_drink","securitycraft:secret_dark_oak_sign_item","ae2:network/cables/dense_covered_cyan","minecraft:gold_ingot_from_smelting_raw_gold","sophisticatedbackpacks:smithing_upgrade","mcwbiomesoplenty:palm_beach_door","mcwfences:mud_brick_railing_gate","twigs:oak_table","pneumaticcraft:heat_pipe","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","mysticalagriculture:essence/minecraft/amethyst","mcwbiomesoplenty:magic_bamboo_door","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwfurnitures:stripped_acacia_double_drawer","utilitix:bamboo_shulker_raft_with_shell","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","mcwroofs:green_concrete_steep_roof","enderio:staff_of_travelling","botania:conversions/lime_petal_block_deconstruct","mcwfences:mangrove_wired_fence","refinedstorage:coloring_recipes/gray_security_manager","minecraft:red_terracotta","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwpaths:blackstone_dumble_paving","mcwroofs:white_roof_block","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwbiomesoplenty:mahogany_stable_door","travelersbackpack:melon","securitycraft:block_pocket_manager","refinedstorage:coloring_recipes/orange_pattern_grid","mcwlights:soul_cherry_tiki_torch","expatternprovider:ei","minecraft:end_stone_brick_wall","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","alltheores:copper_ore_hammer","enderio:resetting_lever_sixty_inv_from_prev","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","mcwlights:red_lamp","bloodmagic:blood_altar","alltheores:lumium_dust_from_alloy_blending","refinedstorage:coloring_recipes/gray_crafter_manager","bloodmagic:path/path_stone","cfm:purple_kitchen_drawer","botania:flower_bag","ad_astra:steel_plateblock","alltheores:brass_gear","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","twilightforest:berry_torch","mcwfences:red_sandstone_railing_gate","enderio:primitive_alloy_smelter","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:acacia_triple_drawer","minecraft:netherite_chestplate_smithing","cfm:jungle_kitchen_counter","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","mcwfurnitures:stripped_acacia_double_drawer_counter","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","refinedstorage:coloring_recipes/green_crafting_grid","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwlights:light_blue_lamp","sophisticatedstorage:blasting_upgrade","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","mcwpaths:dark_prismarine_crystal_floor","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","cfm:acacia_desk","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwbiomesoplenty:pine_waffle_door","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","mcwbiomesoplenty:magic_stable_head_door","minecraft:birch_pressure_plate","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwfurnitures:stripped_acacia_drawer","mcwwindows:golden_curtain_rod","refinedstorage:coloring_recipes/pink_crafting_grid","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:palm_stable_head_door","refinedstorage:coloring_recipes/white_crafter","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","refinedstorage:coloring_recipes/black_crafting_monitor","sfm:manager","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwbiomesoplenty:willow_bamboo_door","travelersbackpack:fox","silentgear:tyrian_steel_nugget","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","littlelogistics:tee_junction_rail","botania:livingwood_twig","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","deeperdarker:bloom_boat","mcwtrpdoors:birch_blossom_trapdoor","refinedstorage:coloring_recipes/cyan_pattern_grid","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","mcwroofs:pink_terracotta_upper_steep_roof","dyenamics:rose_stained_glass","mcwpaths:blackstone_diamond_paving","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","croptopia:steamed_crab","refinedstorage:coloring_recipes/black_security_manager","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","mcwroofs:acacia_steep_roof","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","allthecompressed:compress/copper_block_1x","minecraft:spectral_arrow","alltheores:copper_dust_from_hammer_ingot_crushing","mcwroofs:light_blue_concrete_upper_steep_roof","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","refinedstorage:coloring_recipes/purple_pattern_grid","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","supplementaries:candle_holders/candle_holder_light_gray","chemlib:barium_ingot_from_blasting_barium_dust","mcwwindows:warped_planks_four_window","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","refinedstorage:coloring_recipes/black_fluid_grid","bloodmagic:enhanced_teleposer_focus","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","expatternprovider:pre_bus","mcwdoors:bamboo_waffle_door","mcwfurnitures:stripped_acacia_desk","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","allthearcanistgear:vibranium_hat_smithing","ae2:network/cables/dense_smart_fluix_clean","minecraft:dye_orange_bed","refinedstorage:coloring_recipes/brown_grid","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","rftoolsutility:moduleplus_template","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","handcrafted:birch_drawer","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","chemlib:argon_lamp_block","refinedstorage:coloring_recipes/lime_wireless_transmitter","littlelogistics:vessel_charger","mcwbiomesoplenty:dead_tropical_door","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","bigreactors:reprocessor/powerport","mcwbiomesoplenty:jacaranda_paper_door","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","refinedstorage:coloring_recipes/relay","cfm:green_picket_gate","railcraft:radio_circuit","aether:netherite_pickaxe_repairing","pneumaticcraft:manual_compressor","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","refinedstorage:coloring_recipes/lime_crafter","minecraft:dispenser","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","ae2:decorative/sky_stone_brick_from_stonecutting","securitycraft:secret_acacia_sign_item","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","refinedstorage:coloring_recipes/magenta_detector","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","refinedstorage:coloring_recipes/purple_controller","mcwwindows:cherry_plank_four_window","mcwtrpdoors:acacia_swamp_trapdoor","refinedstorage:coloring_recipes/orange_controller","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","mcwbiomesoplenty:fir_modern_door","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","minecraft:cooked_cod","deepresonance:radiation_suit_helmet","refinedstorage:coloring_recipes/detector","minecraft:iron_helmet","ad_astra:steel_trapdoor","voidtotem:totem_of_void_undying","securitycraft:reinforced_pink_stained_glass_pane_from_dye","mcwbiomesoplenty:mahogany_barn_glass_door","forbidden_arcanus:edelwood_slab","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","pneumaticcraft:fluid_mixer","minecraft:cartography_table","securitycraft:alarm","pneumaticcraft:manometer","allthemodium:vibranium_ingot_from_dust_blasting","mcwbiomesoplenty:dead_japanese2_door","ad_astra:launch_pad","cfm:magenta_picket_gate","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","cfm:stripped_acacia_desk_cabinet","ae2:tools/fluix_shovel","forbidden_arcanus:edelwood_fence_gate","pneumaticcraft:wall_lamp_inverted_magenta","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","botania:petal_lime_double","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","pneumaticcraft:smart_chest","mcwpaths:dark_prismarine_crystal_floor_slab","refinedstorage:coloring_recipes/cyan_security_manager","refinedstorage:coloring_recipes/cyan_relay","additionallanterns:amethyst_lantern","utilitix:crude_furnace","dyenamics:peach_dye","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","mysticalagriculture:essence/minecraft/netherite_ingot","mcwfurnitures:stripped_acacia_striped_chair","refinedstorage:coloring_recipes/blue_crafter","mcwroofs:gray_steep_roof","create:copper_shingles_from_ingots_copper_stonecutting","reliquary:mob_charm_belt","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","refinedstorage:coloring_recipes/lime_network_transmitter","mcwroofs:birch_planks_roof","croptopia:pumpkin_bars","sophisticatedstorage:jukebox_upgrade","allthemodium:raw_unobtainium_block","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","dankstorage:dank_3","mcwroofs:yellow_terracotta_upper_lower_roof","ae2:network/cables/glass_black","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","railcraft:invar_gear","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","chemlib:beryllium_nugget_to_ingot","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","bloodmagic:blood_rune_aug_capacity_2","cfm:black_trampoline","mcwfurnitures:acacia_cupboard_counter","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","minecraft:polished_blackstone_stairs","mcwbiomesoplenty:magic_mystic_door","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","minecraft:dropper","bloodmagic:path/path_wornstonetile","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","farmersdelight:cooking/stuffed_pumpkin_block","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","expatternprovider:oversize_interface_part","mcwwindows:oak_window2","mcwfurnitures:stripped_acacia_bookshelf","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","create:crafting/kinetics/brass_hand","mcwfences:mangrove_horse_fence","bigreactors:reprocessor/collector","create:crafting/kinetics/lime_seat","mcwbiomesoplenty:empyreal_bamboo_door","minecraft:golden_helmet","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","mcwdoors:acacia_bamboo_door","ae2:network/crafting/4k_cpu_crafting_storage","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","cfm:stripped_dark_oak_kitchen_sink_light","forbidden_arcanus:edelwood_bucket","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","mcwwindows:quartz_pane_window","refinedstorage:coloring_recipes/green_network_transmitter","bloodmagic:raw_hellforged_block","refinedstorage:coloring_recipes/orange_network_receiver","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:fancy_painting","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","pneumaticcraft:air_grate_module","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","twilightforest:equipment/twilight_scepter","mcwbridges:balustrade_end_stone_bricks_bridge","twigs:polished_rhyolite","minecraft:quartz_bricks","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","chemlib:magnesium_ingot_from_smelting_magnesium_dust","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwpaths:stone_running_bond_stairs","refinedstorage:coloring_recipes/yellow_disk_manipulator","rftoolsbuilder:red_shield_template_block","refinedstorage:coloring_recipes/white_crafting_grid","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","allthetweaks:atm_star_block","mcwbiomesoplenty:dead_mystic_door","chemlib:strontium_ingot_from_smelting_strontium_dust","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","minecraft:birch_door","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","bloodmagic:soul_forge","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","pneumaticcraft:pressure_chamber_valve","delightful:food/cooking/jam_jar","pneumaticcraft:logistics_frame_passive_provider","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","twigs:polished_tuff_stonecutting","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","mcwbiomesoplenty:pine_japanese_door","cfm:green_picket_fence","mcwroofs:white_terracotta_top_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","cfm:dye_orange_picket_gate","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","mcwbiomesoplenty:redwood_glass_door","refinedstorage:coloring_recipes/red_security_manager","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:birch_fence_gate","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwroofs:orange_concrete_upper_lower_roof","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","expatternprovider:assembler_matrix_pattern","mcwfences:modern_andesite_wall","mcwbridges:bamboo_bridge","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","chemlib:barium_ingot_to_nugget","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","mcwbiomesoplenty:maple_beach_door","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","mcwbiomesoplenty:magic_four_panel_door","expatternprovider:caner","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwfurnitures:acacia_drawer_counter","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:hellbark_four_panel_door","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","connectedglass:borderless_glass_orange_pane2","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","chemlib:gallium_nugget_to_ingot","minecraft:honeycomb_block","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","minecraft:yellow_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwfurnitures:acacia_striped_chair","croptopia:steamed_clams","mcwpaths:dark_prismarine_running_bond_path","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","minecolonies:apple_pie","mcwtrpdoors:bamboo_beach_trapdoor","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","pneumaticcraft:smart_chest_kit","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwbiomesoplenty:palm_barn_door","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","twilightdelight:cooking/torchberry_juice","domum_ornamentum:orange_floating_carpet","mcwbiomesoplenty:palm_mystic_door","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","mcwbiomesoplenty:maple_japanese2_door","farmersdelight:sweet_berry_cookie","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","mcwlights:pink_lamp","refinedstorage:coloring_recipes/gray_detector","railcraft:steel_boots","ad_astra:zip_gun","croptopia:nougat","ae2:network/cables/covered_magenta","refinedstorage:coloring_recipes/white_crafting_monitor","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","chemlib:indium_block_to_ingot","refinedstorage:coloring_recipes/black_controller","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","expatternprovider:pattern_modifier","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","silentgear:crimson_steel_dust_blasting","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwdoors:acacia_whispering_door","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwbiomesoplenty:jacaranda_cottage_door","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","ae2:network/cables/covered_gray","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","silentgear:tyrian_steel_dust_smelting","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","allthecompressed:compress/atm_star_block_1x","cfm:birch_coffee_table","mcwtrpdoors:acacia_whispering_trapdoor","travelersbackpack:magma_cube","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","refinedstorage:coloring_recipes/white_security_manager","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","allthecompressed:compress/iridium_block_1x","forbidden_arcanus:deorum_lantern","mcwbiomesoplenty:hellbark_glass_door","minecraft:brewing_stand","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","occultism:crafting/spirit_attuned_crystal","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwroofs:acacia_roof","mcwdoors:jungle_paper_door","chemlib:potassium_block_to_ingot","pneumaticcraft:spawner_agitator","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","ae2:network/cables/smart_light_gray","mcwbiomesoplenty:stripped_palm_log_four_window","mcwbiomesoplenty:empyreal_mystic_door","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","create:crafting/kinetics/orange_seat","refinedstorage:coloring_recipes/white_relay","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","dyenamics:icy_blue_terracotta","dyenamics:cherenkov_stained_glass","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","chemlib:sodium_ingot_to_nugget","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","refinedstorage:coloring_recipes/brown_network_transmitter","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","refinedstorage:coloring_recipes/gray_relay","bigreactors:blasting/graphite_from_coal","minecraft:pumpkin_seeds","mcwpaths:dark_prismarine_windmill_weave_stairs","sophisticatedbackpacks:stack_upgrade_tier_1","bigreactors:crafting/blutonium_component_to_storage","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_brown_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","farmersdelight:horse_feed","railways:crafting/smokestack_long","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","refinedstorage:coloring_recipes/brown_fluid_grid","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","ae2:tools/certus_quartz_cutting_knife","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:light_gray_terracotta","dyenamics:amber_stained_glass","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","bloodmagic:primitive_furnace_cell","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","dyenamics:wine_terracotta","refinedstorage:coloring_recipes/cyan_wireless_transmitter","mcwroofs:light_gray_attic_roof","mcwpaths:birch_planks_path","refinedstorage:coloring_recipes/pattern_grid","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","twilightforest:hydra_banner_pattern","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","occultism:crafting/book_of_calling_foliot_cleaner","merequester:requester_terminal","mcwfurnitures:stripped_jungle_covered_desk","ae2:shaped/stairs/smooth_sky_stone_block","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:acacia_paper_trapdoor","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","mcwbiomesoplenty:pine_paper_door","botania:manasteel_shears","minecraft:pumpkin_pie","ae2:network/cells/item_storage_cell_64k","ae2:network/cables/smart_green","mcwfurnitures:stripped_jungle_large_drawer","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mekanismtools:refined_obsidian/tools/pickaxe","cfm:acacia_upgraded_gate","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","refinedstorage:coloring_recipes/black_network_receiver","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","minecraft:soul_campfire","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","alltheores:platinum_ingot_from_ore_blasting","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","refinedstorage:coloring_recipes/red_network_receiver","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","refinedstorage:coloring_recipes/pink_grid","twilightdelight:obsidian","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","pneumaticcraft:air_canister","mcwwindows:prismarine_pane_window","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","refinedstorage:coloring_recipes/gray_pattern_grid","supplementaries:slice_map","littlelogistics:automatic_tee_junction_rail","mcwdoors:acacia_mystic_door","alltheores:aluminum_dust_from_hammer_ingot_crushing","refinedstorage:coloring_recipes/pink_detector","mcwroofs:cobblestone_lower_roof","alltheores:invar_plate","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","silentgear:azure_electrum_block","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","botania:lens_normal","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","enderio:reinforced_obsidian_block","mcwroofs:light_gray_upper_lower_roof","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","mcwfurnitures:jungle_modern_chair","ae2:network/cells/item_storage_cell_4k_storage","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","handcrafted:phantom_trophy","minecraft:quartz_slab","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","create:brass_bars_from_ingots_brass_stonecutting","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","ae2:block_cutter/stairs/smooth_sky_stone_stairs","dyenamics:amber_wool","forbidden_arcanus:edelwood_button","farmersdelight:iron_knife","minecraft:quartz_pillar_from_quartz_block_stonecutting","railcraft:steel_helmet","mcwfurnitures:stripped_jungle_double_drawer","mcwbiomesoplenty:palm_bamboo_door","ae2:network/cables/smart_black","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","biomesoplenty:hellbark_chest_boat","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwtrpdoors:acacia_bark_trapdoor","mcwbiomesoplenty:hellbark_waffle_door","alltheores:uranium_ingot_from_ore_blasting","oceansdelight:stuffed_cod","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","mcwpaths:dark_prismarine_strewn_rocky_path","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","minecraft:bucket","handcrafted:salmon_trophy","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","farmersdelight:chicken_sandwich","minecraft:polished_andesite","mcwbiomesoplenty:palm_swamp_door","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","chemlib:helium_lamp_block","enderio:iron_gear","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwdoors:bamboo_stable_head_door","immersiveengineering:crafting/firework","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","ae2:network/cables/smart_orange","silentgear:crimson_steel_ingot_from_nugget","cfm:white_picket_fence","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:acacia_drawer","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","twigs:polished_tuff_bricks_from_tuff_stonecutting","handcrafted:kitchen_hood","appbot:mana_storage_cell_4k","mcwdoors:jungle_bamboo_door","sophisticatedbackpacks:stonecutter_upgrade","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","chemlib:gallium_block_to_ingot","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:fir_swamp_door","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwbridges:birch_log_bridge_middle","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwbiomesoplenty:dead_stable_head_door","immersiveengineering:crafting/treated_fence","silentgear:azure_electrum_ingot_from_block","mcwwindows:diorite_louvered_shutter","minecraft:gray_dye","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","securitycraft:secret_birch_hanging_sign","allthemodium:allthemodium_ingot_from_dust_blasting","botania:travel_belt","bigreactors:reprocessor/fluidinjector","bloodmagic:blood_rune_acceleration","allthemodium:unobtainium_ingot_from_raw_blasting","mcwbiomesoplenty:fir_highley_gate","refinedstorage:coloring_recipes/lime_network_receiver","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwroofs:acacia_upper_lower_roof","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","utilitarian:no_soliciting/soliciting_carpets/white_trapped_soliciting_carpet","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","allthecompressed:compress/pumpkin_1x","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","aether:blue_aercloud_freezing","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwbiomesoplenty:redwood_bamboo_door","mcwwindows:oak_plank_parapet","immersiveengineering:crafting/hempcrete","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:iron_sword","aquaculture:sushi","forbidden_arcanus:stellarite_block_from_stellarite_piece","ae2:network/blocks/interfaces_interface_alt","mcwfurnitures:jungle_table","refinedstorage:coloring_recipes/purple_network_receiver","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","chemlib:indium_ingot_to_block","minecraft:dark_prismarine_slab","railways:crafting/smokestack_caboosestyle","additionallanterns:blackstone_chain","mcwbiomesoplenty:magic_japanese2_door","mcwwindows:one_way_glass_pane","mcwbiomesoplenty:hellbark_stable_door","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfurnitures:stripped_acacia_table","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","ae2:tools/fluix_upgrade_smithing_template","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","mcwlights:jungle_ceiling_fan_light","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","minecraft:birch_trapdoor","ae2:network/cables/covered_light_gray","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","everythingcopper:copper_anvil","mcwlights:striped_lantern","refinedstorage:coloring_recipes/cyan_controller","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwdoors:acacia_barn_glass_door","mcwroofs:light_blue_terracotta_top_roof","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","create:birch_window","cfm:birch_park_bench","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","deeperdarker:bloom_chest_boat","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","mcwbiomesoplenty:empyreal_barn_door","minecraft:powered_rail","botania:dye_pink","mcwwindows:diorite_pane_window","dyenamics:wine_stained_glass_pane_from_glass_pane","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","twigs:birch_table","silentgear:crimson_steel_ingot_from_block","enderio:resetting_lever_ten_from_prev","mcwtrpdoors:acacia_classic_trapdoor","mcwbridges:end_stone_bricks_bridge","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:magenta_concrete_upper_lower_roof","sophisticatedstorage:storage_stack_upgrade_tier_5_from_backpack_stack_upgrade_tier_4","mcwroofs:light_gray_terracotta_steep_roof","mcwbiomesoplenty:hellbark_nether_door","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","twilightforest:equipment/fiery_ingot_crafting","sophisticatedstorage:void_upgrade","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","mcwbiomesoplenty:empyreal_paper_door","minecraft:gray_terracotta","mcwfurnitures:acacia_modern_chair","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","mcwdoors:bamboo_mystic_door","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","twilightforest:equipment/fiery_fiery_chestplate","mcwbiomesoplenty:magic_bark_glass_door","dyenamics:peach_terracotta","minecraft:fishing_rod","xnet:connector_yellow_dye","rftoolspower:powercell_card","minecraft:terracotta","mcwwindows:acacia_blinds","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwdoors:acacia_waffle_door","mcwlights:yellow_lamp","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","refinedstorage:coloring_recipes/blue_crafting_monitor","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","twigs:copper_pillar_stonecutting","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","bloodmagic:largebloodstonebrick","forbidden_arcanus:dark_rune_from_dark_rune_block","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","ae2:network/cells/fluid_storage_cell_4k_storage","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:crafting/kinetics/vertical_gearbox","occultism:blasting/iesnium_ingot_from_dust","mcwlights:double_street_lamp","minecraft:beacon","railcraft:tin_gear","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","railways:crafting/smokestack_streamlined","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","bloodmagic:smelting/ingot_hellforged","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","chemlib:barium_ingot_from_smelting_barium_dust","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","twilightforest:snow_queen_banner_pattern","create:crafting/kinetics/blue_seat","allthemodium:vibranium_dust_from_ore_crushing","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:umbran_bamboo_door","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","bloodmagic:blood_rune_displacement","ae2:shaped/walls/smooth_sky_stone_block","allthetweaks:atm_star_from_atmstar_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","rftoolsbase:tablet","ae2:network/crafting/256k_cpu_crafting_storage","mcwroofs:magenta_terracotta_lower_roof","ad_astra:vent","refinedstorage:coloring_recipes/light_gray_disk_manipulator","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","twilightforest:equipment/fiery_fiery_boots","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:birch_planks_upper_lower_roof","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","securitycraft:reinforced_nether_brick_fence","handcrafted:terracotta_plate","supplementaries:stone_tile","mcwroofs:light_blue_terracotta_roof","mcwbiomesoplenty:willow_barn_glass_door","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","supplementaries:flags/flag_white","silentgear:coating_smithing_template","mcwpaths:dark_prismarine_basket_weave_paving","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","handcrafted:berry_jam_jar","mcwroofs:andesite_top_roof","mcwbridges:birch_rail_bridge","minecraft:glass_pane","supplementaries:timber_brace","refinedstorage:coloring_recipes/lime_relay","alltheores:iridium_ingot_from_raw_blasting","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwbiomesoplenty:maple_barn_glass_door","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","farmersdelight:netherite_knife_smithing","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","handcrafted:birch_cupboard","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","railways:crafting/smokestack_oilburner","securitycraft:sonic_security_system","minecraft:clay","mcwbiomesoplenty:pine_pane_window","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","securitycraft:protecto","bloodmagic:smelting/blasting_ingot_from_demonite","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","ae2:network/blocks/interfaces_interface_part","dyenamics:cherenkov_terracotta","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","refinedstorage:coloring_recipes/brown_controller","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","mcwbiomesoplenty:mahogany_paper_door","ae2:materials/annihilationcore","forbidden_arcanus:clibano_combustion/rune_from_clibano_combusting","croptopia:chicken_and_noodles","ae2:network/cables/dense_smart_from_smart","forbidden_arcanus:clibano_combustion/lapis_lazuli_from_clibano_combusting","silentgear:stone_rod","ae2:network/cables/dense_covered_green","ae2:network/cables/covered_green","minecraft:leather_boots","railcraft:steel_spike_maul","refinedstorage:coloring_recipes/gray_fluid_grid","pneumaticcraft:classify_filter","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","mcwwindows:gray_mosaic_glass","chemlib:zirconium_ingot_from_smelting_zirconium_dust","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwtrpdoors:acacia_barred_trapdoor","mcwlights:pink_paper_lamp","mcwbiomesoplenty:magic_nether_door","pneumaticcraft:wall_lamp_inverted_light_blue","occultism:crafting/divination_rod","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","cfm:yellow_kitchen_sink","alltheores:enderium_ingot_from_dust_blasting","silentgear:diamond_from_shards","minecolonies:supplychestdeployer","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:dye_black_carpet","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","mcwfurnitures:acacia_bookshelf_drawer","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","expatternprovider:ebus_out","ae2:network/cables/covered_purple","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","alltheores:iron_rod","mcwroofs:cyan_striped_awning","silentgear:azure_electrum_dust_blasting","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwpaths:dark_prismarine_flagstone_slab","minecraft:magma_block","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","cfm:acacia_park_bench","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","connectedglass:clear_glass_black_pane2","connectedglass:clear_glass_black_pane3","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","littlelogistics:transmitter_component","minecraft:brush","mcwfurnitures:stripped_acacia_triple_drawer","aether:book_of_lore","securitycraft:secret_spruce_hanging_sign","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","mcwfurnitures:acacia_wardrobe","sfm:cable","mcwbiomesoplenty:palm_japanese_door","minecraft:hay_block","twilightforest:equipment/fiery_fiery_leggings","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","mcwroofs:white_upper_steep_roof","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","delightful:knives/invar_knife","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","pneumaticcraft:compressed_iron_boots","mcwwindows:red_sandstone_window2","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbiomesoplenty:jacaranda_barn_glass_door","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","railways:crafting/smokestack_coalburner","mcwfences:modern_granite_wall","bloodmagic:primitive_hydration_cell","pneumaticcraft:etching_tank","enderio:xp_obelisk","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","bloodmagic:ritual_stone_master","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:fir_barn_door","bambooeverything:bamboo_ladder","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","refinedstorage:coloring_recipes/light_gray_network_receiver","bigreactors:blasting/graphite_from_charcoal","refinedstorage:coloring_recipes/pink_wireless_transmitter","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","create:crafting/kinetics/rotation_speed_controller","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","littlelogistics:fluid_hopper","mcwwindows:bricks_window","twigs:paper_lantern","railcraft:goggles","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","mcwbiomesoplenty:willow_japanese2_door","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","ae2:network/cables/covered_black","botania:petal_lime","refinedstorage:coloring_recipes/green_grid","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","ae2:network/cables/dense_smart_purple","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","bloodmagic:blood_rune_aug_capacity","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwtrpdoors:bamboo_barrel_trapdoor","mcwbiomesoplenty:empyreal_window2","mcwbiomesoplenty:pine_barn_glass_door","refinedstorage:coloring_recipes/black_relay","mcwpaths:stone_flagstone_slab","ae2:decorative/sky_stone_small_brick_from_stonecutting","silentgear:crimson_steel_dust_smelting","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","enderio:basic_item_filter","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","securitycraft:sentry","minecraft:netherite_block","immersiveengineering:crafting/blueprint_bullets","alltheores:uranium_rod","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","refinedstorage:coloring_recipes/purple_disk_manipulator","refinedstorage:coloring_recipes/cyan_detector","utilitarian:no_soliciting/soliciting_carpets/blue_trapped_soliciting_carpet","mcwroofs:pink_terracotta_top_roof","create:crafting/logistics/redstone_contact","mcwfurnitures:stripped_acacia_lower_bookshelf_drawer","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","mcwfurnitures:stripped_oak_modern_chair","minecraft:calibrated_sculk_sensor","pneumaticcraft:coordinate_tracker_upgrade","bloodmagic:smelting/ingot_from_demonite","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwbiomesoplenty:fir_mystic_door","refinedstorage:coloring_recipes/white_pattern_grid","mcwfences:dark_oak_horse_fence","securitycraft:secret_warped_hanging_sign","ae2:tools/certus_quartz_spade","mcwbiomesoplenty:fir_beach_door","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","twilightforest:carminite_builder","alltheores:constantan_dust_from_alloy_blending","mcwroofs:black_concrete_upper_lower_roof","ae2:network/cables/dense_covered_black","forbidden_arcanus:edelwood_stairs","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","expatternprovider:epp_alt","occultism:crafting/spirit_campfire","utilitix:piston_controller_rail","croptopia:roasted_smoking","create:crafting/curiosities/peculiar_bell","mcwbiomesoplenty:hellbark_curved_gate","itemcollectors:advanced_collector","minecraft:green_terracotta","delightful:knives/brass_knife","minecraft:white_stained_glass","mcwbiomesoplenty:umbran_mystic_door","expatternprovider:assembler_matrix_frame","securitycraft:secret_warped_sign_item","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","refinedstorage:coloring_recipes/yellow_controller","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","allthemodium:vibranium_nugget_from_ingot","croptopia:pumpkin_soup","ae2:network/cells/fluid_storage_cell_64k","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","refinedstorage:coloring_recipes/crafting_monitor","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","mcwfurnitures:acacia_double_drawer_counter","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","ad_astra:fan","chemlib:zirconium_nugget_to_ingot","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","sfm:labelgun","mcwfences:jungle_hedge","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mekanismtools:osmium/tools/sword","refinedstorage:coloring_recipes/yellow_relay","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","occultism:crafting/book_of_calling_foliot_lumberjack","twilightforest:carminite_reactor","domum_ornamentum:pink_floating_carpet","botania:virus_nullodermal","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","ae2:network/cables/dense_smart_gray","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","allthecompressed:compress/stone_1x","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","refinedstorage:coloring_recipes/yellow_security_manager","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","chemlib:magnesium_ingot_to_nugget","botania:manasteel_shovel","minecraft:quartz_from_blasting","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","minecraft:light_gray_stained_glass_pane_from_glass_pane","mcwpaths:cobbled_deepslate_crystal_floor_slab","silentgear:leather_from_scraps","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwbiomesoplenty:magic_stable_door","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","cfm:lime_picket_gate","mcwpaths:blackstone_crystal_floor","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwbiomesoplenty:mahogany_classic_door","supplementaries:candle_holders/candle_holder_orange_dye","sophisticatedbackpacks:tool_swapper_upgrade","mcwwindows:stripped_spruce_pane_window","additionallanterns:cobbled_deepslate_chain","allthecompressed:compress/sand_1x","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","minecraft:lime_terracotta","mcwwindows:stripped_dark_oak_log_window","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","occultism:crafting/spirit_attuned_pickaxe_head","minecraft:gray_stained_glass_pane_from_glass_pane","mekanism:processing/osmium/ingot/from_raw_smelting","allthecompressed:compress/andesite_1x","mcwbiomesoplenty:willow_nether_door","gravitationalmodulatingunittweaks:module_gravitational_modulating_additional_unit","cfm:birch_cabinet","minecraft:stone_slab","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwbiomesoplenty:magic_classic_door","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","mcwbiomesoplenty:maple_four_panel_door","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","allthemodium:vibranium_ingot_from_block","travelersbackpack:backpack_tank","refinedstorage:coloring_recipes/blue_disk_manipulator","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbridges:glass_bridge","refinedstorage:coloring_recipes/black_crafter_manager","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","utilitarian:no_soliciting/soliciting_carpets/green_trapped_soliciting_carpet","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","minecraft:light_blue_stained_glass_pane_from_glass_pane","refinedstorage:coloring_recipes/purple_crafter","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","croptopia:campfire_molasses","mcwdoors:acacia_stable_door","ae2:network/crafting/16k_cpu_crafting_storage","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwbiomesoplenty:pine_four_panel_door","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","minecraft:blue_stained_glass_pane_from_glass_pane","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","mcwbiomesoplenty:hellbark_cottage_door","railcraft:water_tank_siding","expatternprovider:assembler_matrix_crafter","pneumaticcraft:wall_lamp_inverted_light_gray","refinedstorage:coloring_recipes/green_crafter","mcwtrpdoors:bamboo_bark_trapdoor","cfm:jungle_kitchen_sink_dark","twigs:crimson_roots_paper_lantern","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","securitycraft:secret_cherry_hanging_sign","croptopia:melon_juice","refinedstorage:coloring_recipes/crafting_grid","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","ae2:network/cables/smart_cyan","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","mcwbiomesoplenty:jacaranda_glass_door","pneumaticcraft:compressed_stone_slab","mcwfurnitures:stripped_acacia_chair","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","mcwwindows:one_way_glass","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","minecraft:oak_chest_boat","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","mcwbridges:acacia_bridge_pier","pneumaticcraft:empty_pcb_from_failed_pcb","allthecompressed:compress/grass_block_1x","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwbiomesoplenty:hellbark_classic_door","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","ae2additions:components/super/16k","mcwbiomesoplenty:dead_glass_door","connectedglass:borderless_glass_orange2","chemlib:beryllium_block_to_ingot","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","twigs:silt_from_silt_balls","everythingcopper:copper_helmet","pneumaticcraft:logistics_core","create:crafting/kinetics/yellow_seat","mcwbiomesoplenty:willow_modern_door","mcwroofs:red_concrete_upper_lower_roof","croptopia:anchovy_pizza","mcwpaths:andesite_windmill_weave_path","mcwbiomesoplenty:palm_glass_door","modularrouters:blank_upgrade","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","ae2:shaped/walls/sky_stone_block","cfm:birch_chair","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","mcwdoors:bamboo_whispering_door","minecraft:mangrove_boat","minecraft:bread","mcwroofs:green_terracotta_steep_roof","mcwbiomesoplenty:redwood_western_door","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","ad_astra:orange_industrial_lamp","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","cfm:birch_desk","immersiveengineering:crafting/wirecoil_structure_steel","mcwbiomesoplenty:dead_classic_door","mcwwindows:dark_oak_curtain_rod","botania:water_rod","botania:horn_grass","securitycraft:secret_cherry_sign_item","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwroofs:white_concrete_steep_roof","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","refinedstorage:coloring_recipes/light_gray_grid","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwbiomesoplenty:umbran_swamp_door","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","mcwfurnitures:acacia_desk","refinedstorage:coloring_recipes/cyan_network_transmitter","refinedstorage:coloring_recipes/yellow_crafter_manager","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwwindows:stone_brick_arrow_slit","mcwpaths:cobbled_deepslate_windmill_weave_stairs","chimes:amethyst_chimes","ae2:smelting/smooth_sky_stone_block","mcwfurnitures:jungle_desk","mcwbiomesoplenty:mahogany_cottage_door","domum_ornamentum:white_floating_carpet","mcwbiomesoplenty:umbran_waffle_door","twilightforest:iron_ladder","croptopia:ham_sandwich","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","ad_astra:space_suit","securitycraft:disguise_module","mcwbiomesoplenty:willow_swamp_door","mcwwindows:deepslate_four_window","mcwdoors:acacia_glass_door","pneumaticcraft:camo_applicator","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:fir_stable_head_door","cfm:light_blue_picket_fence","mcwbiomesoplenty:redwood_barn_door","mcwtrpdoors:acacia_four_panel_trapdoor","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","enderio:electromagnet","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","supplementaries:gold_trapdoor","forbidden_arcanus:deorum_block_from_deorum_ingot","croptopia:deep_fried_shrimp","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","mcwbiomesoplenty:palm_modern_door","enderio:basic_fluid_filter","handcrafted:birch_table","rftoolsutility:counter_module","cfm:stripped_jungle_crate","minecraft:amethyst_block","minecraft:oak_door","biomesoplenty:jacaranda_boat","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwdoors:bamboo_barn_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","mcwbiomesoplenty:maple_cottage_door","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwbiomesoplenty:empyreal_modern_door","mcwroofs:black_roof_block","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","bloodmagic:blood_rune_self_sac_2","advancedperipherals:peripheral_casing","immersiveengineering:crafting/wooden_grip","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","reliquary:fertile_lily_pad","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwfurnitures:stripped_acacia_large_drawer","mcwroofs:red_striped_awning","silentgear:tyrian_steel_dust_blasting","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","mcwroofs:lime_concrete_upper_lower_roof","modularrouters:dropper_module","twilightforest:lich_banner_pattern","mcwbiomesoplenty:magic_highley_gate","twilightforest:ur_ghast_banner_pattern","utilitarian:no_soliciting/soliciting_carpets/orange_trapped_soliciting_carpet","ae2:tools/fluix_sword","simplemagnets:basic_demagnetization_coil","dyenamics:mint_terracotta","mcwwindows:pink_curtain","mcwroofs:yellow_concrete_upper_lower_roof","sophisticatedstorage:birch_limited_barrel_1","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","sophisticatedstorage:birch_limited_barrel_4","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","sophisticatedstorage:birch_limited_barrel_2","delightful:knives/refined_obsidian_knife","expatternprovider:ei_alt","sophisticatedstorage:birch_limited_barrel_3","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","refinedstorage:coloring_recipes/blue_network_receiver","cfm:light_gray_kitchen_counter","minecraft:candle","twilightforest:knight_phantom_banner_pattern","framedblocks:framed_cube","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","immersiveengineering:crafting/connector_structural","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","bloodmagic:smelting/blasting_ingot_hellforged","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","biomesoplenty:fir_chest_boat","advanced_ae:throughput_monitor_configurator","mcwdoors:bamboo_swamp_door","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/brown_relay","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","mcwroofs:blue_terracotta_upper_steep_roof","deepresonance:radiation_suit_boots","supplementaries:wrench","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","minecraft:bow","cfm:acacia_kitchen_counter","dyenamics:lavender_wool","aether:skyroot_smithing_table","refinedstorage:coloring_recipes/blue_fluid_grid","mekanismtools:osmium/armor/leggings","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","chemlib:periodic_table","botania:mushroom_stew","minecraft:andesite_stairs","forbidden_arcanus:deorum_nugget_from_deorum_ingot","bigreactors:energizer/chargingport_fe","refinedstorage:coloring_recipes/purple_crafting_monitor","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","dyenamics:amber_dye","mcwdoors:bamboo_japanese_door","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","refinedstorage:coloring_recipes/brown_crafter","supplementaries:sconce","silentgear:rough_rod","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","dyenamics:maroon_stained_glass_pane_from_glass_pane","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","chemlib:potassium_ingot_from_smelting_potassium_dust","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:acacia_large_drawer","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","bigreactors:reprocessor/glass","forbidden_arcanus:deorum_door","sophisticatedbackpacks:feeding_upgrade","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:pine_stable_door","minecraft:granite","minecraft:melon_seeds","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","cfm:stripped_acacia_desk","botania:ghost_rail","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","refinedstorage:coloring_recipes/network_receiver","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","silentgear:crimson_steel_block","nethersdelight:soul_compost_from_warped_roots","mcwbiomesoplenty:magic_barn_glass_door","sophisticatedstorage:backpack_advanced_void_upgrade_from_storage_advanced_void_upgrade","mcwbiomesoplenty:redwood_mystic_door","bloodmagic:smelting/blasting_ingot_netherite_scrap","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","chemlib:beryllium_ingot_from_smelting_beryllium_dust","pneumaticcraft:wall_lamp_inverted_brown","mcwbiomesoplenty:fir_bamboo_door","mcwroofs:andesite_roof","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","rftoolsbase:infused_enderpearl","supplementaries:pedestal","travelersbackpack:blaze","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","chemlib:zirconium_block_to_ingot","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:network/cables/glass_yellow","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","alltheores:ruby_dust_from_hammer_crushing","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwfurnitures:stripped_acacia_wardrobe","chemlib:manganese_block_to_ingot","dyenamics:rose_terracotta","farmersdelight:cooking/apple_cider","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","bloodmagic:blood_rune_charging","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","expatternprovider:oversize_interface","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","mcwfurnitures:stripped_acacia_modern_desk","dyenamics:bed/lavender_bed","minecraft:blackstone_stairs","refinedstorage:coloring_recipes/magenta_grid","cfm:lime_kitchen_counter","minecraft:dye_orange_carpet","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","alltheores:electrum_ingot_from_dust","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","refinedstorage:coloring_recipes/brown_detector","mcwroofs:brown_terracotta_steep_roof","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwdoors:oak_barn_door","botania:alchemy_catalyst","refinedstorage:coloring_recipes/blue_pattern_grid","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","refinedstorage:coloring_recipes/light_gray_crafting_monitor","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","forbidden_arcanus:smelting/arcane_crystal_from_smelting","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:acacia_upgraded_fence","cfm:oak_bedside_cabinet","ae2:network/cables/dense_smart_light_gray","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","mcwroofs:white_concrete_attic_roof","twilightdelight:torchberries_crate","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","additionallanterns:obsidian_chain","minecraft:ender_eye","supplementaries:blackstone_lamp","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","minecraft:birch_slab","refinedstorage:coloring_recipes/disk_manipulator","mcwtrpdoors:acacia_ranch_trapdoor","mcwlights:cross_lantern","mcwlights:brown_lamp","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","mcwwindows:bricks_four_window","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_packed_mud","immersiveengineering:crafting/hemp_fabric","chemlib:magnesium_block_to_ingot","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","ae2:network/parts/terminals_pattern_access","additionallanterns:diamond_chain","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","ae2:network/blocks/io_condenser","mekanismtools:osmium/armor/helmet","sophisticatedbackpacks:smoking_upgrade","supplementaries:stonecutting/stone_tile","ae2:network/cables/covered_pink","railcraft:steel_tank_wall","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","handcrafted:birch_shelf","dyenamics:conifer_concrete_powder","minecraft:dye_black_wool","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","twilightforest:firefly_jar","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","twilightdelight:torchberry_cookie","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","advanced_ae:quantumstorage128","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","refinedstorage:coloring_recipes/light_gray_controller","bloodmagic:blood_rune_displacement_2","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","allthemodium:raw_vibranium_block","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","refinedstorage:coloring_recipes/grid","refinedstorage:coloring_recipes/orange_network_transmitter","mcwpaths:dark_prismarine_windmill_weave_path","mcwwindows:jungle_plank_window2","ad_astra:space_pants","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","occultism:smelting/iesnium_ingot_from_raw","refinedstorage:coloring_recipes/lime_fluid_grid","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","additionallanterns:dark_prismarine_chain","refinedstorage:coloring_recipes/red_crafting_grid","mcwbiomesoplenty:mahogany_japanese_door","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","utilitarian:utility/acacia_logs_to_pressure_plates","nethersdelight:blackstone_stove","mcwbiomesoplenty:umbran_four_panel_door","enderio:resetting_lever_five_inv","silentgear:crimson_steel_dust","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","silentgear:tyrian_steel_ingot_from_nugget","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","bigreactors:turbine/ridiculite_block","mcwbiomesoplenty:redwood_beach_door","ae2:network/cables/smart_blue","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","bloodmagic:ritual_reader","botania:turntable","ae2:network/cells/item_storage_components_cell_1k_part","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","mcwroofs:oak_planks_top_roof","biomesoplenty:hellbark_boat","refinedstorage:coloring_recipes/orange_relay","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","minecraft:netherite_scrap","alltheores:zinc_dust_from_hammer_crushing","supplementaries:daub","railcraft:steel_chestplate","mcwfurnitures:stripped_jungle_end_table","securitycraft:secret_oak_hanging_sign","mcwfences:oak_hedge","bigreactors:reactor/reinforced/casing_recycle","enderio:fluid_tank","mcwroofs:oak_planks_roof","alltheores:tin_dust_from_hammer_ingot_crushing","botania:petal_white","ae2:decorative/quartz_block","forbidden_arcanus:edelwood_trapdoor","mcwbiomesoplenty:magic_cottage_door","ae2:network/cables/dense_smart_orange","chemlib:beryllium_ingot_to_nugget","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","alltheores:raw_iridium_from_block","ae2:block_cutter/slabs/smooth_sky_stone_slab","mcwbiomesoplenty:hellbark_highley_gate","create:crafting/logistics/redstone_link","immersiveengineering:crafting/treated_scaffold","minecraft:enchanting_table","mcwpaths:cobbled_deepslate_square_paving","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","farmersdelight:roast_chicken_block","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deeperdarker:reinforced_echo_shard","deepresonance:radiation_suit_chestplate","refinedstorage:coloring_recipes/pink_disk_manipulator","refinedstorage:coloring_recipes/lime_disk_manipulator","mcwlights:brown_paper_lamp","minecraft:campfire","allthearcanistgear:unobtainium_boots_smithing","dyenamics:peach_stained_glass_pane_from_glass_pane","travelersbackpack:standard_no_tanks","cfm:white_kitchen_sink","securitycraft:reinforced_birch_fence","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","refinedstorage:coloring_recipes/pink_network_transmitter","forbidden_arcanus:edelwood_door","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","minecraft:acacia_wood","mcwbiomesoplenty:maple_tropical_door","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","pneumaticcraft:logistics_configurator","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:mahogany_beach_door","mcwtrpdoors:print_tropical","pneumaticcraft:logistics_frame_default_storage_self","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:1b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:6825,warning_level:0}}