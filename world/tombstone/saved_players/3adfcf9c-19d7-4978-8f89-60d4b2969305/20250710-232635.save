{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:0.6d,Name:"forge:step_height_addition"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.08d,Name:"forge:entity_gravity"}],BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-*********,*********,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-*********,*********,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-*********,*********,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-*********,*********,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-*********,*********,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:9027},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:wooden_pickaxe",ItemStack:{Count:1b,id:"minecraft:wooden_pickaxe",tag:{Damage:0}}}],SelectedRecipe:"minecraft:wooden_pickaxe"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:154,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:[]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-*********,*********,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],twilightforest_banished:1b},"quark:trying_crawl":0b,simplylight_unlocked:2},Health:20.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:1b,id:"crafting_on_a_stick:crafting_table"},{Count:1b,Slot:2b,id:"minecraft:wooden_pickaxe",tag:{Damage:0}},{Count:1b,Slot:9b,id:"minecraft:sugar_cane"},{Count:1b,Slot:10b,id:"productivebees:sugar_cane_nest",tag:{BlockEntityTag:{BeeList:{Inhabitants:[{EntityData:{Age:0,AgeLocked:0b,AngerTime:0,BalmData:{},Bukkit.Aware:1b,Bukkit.updateLevel:2,CanUpdate:1b,FlowerPos:{X:-191,Y:63,Z:-270},ForcedAge:0,ForgeCaps:{"aether:mob_accessory":{DropChances:{aether_gloves:0.085f,aether_pendant:0.085f,hands:0.085f,necklace:0.085f}},"blueflame:blue_flame_on":{isOnFire:0b},"botania:loonium_drop":{},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-266630896,**********,-**********,-823358447],hasTentacle:0b},"curios:inventory":{Curios:[]},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mekanism:radiation":{radiation:1.0E-7d},"pneumaticcraft:hacking":{},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{"naturesaura:time_alive":36000},HasConverted:0b,HasNectar:1b,HasStung:0b,Health:15.0f,Invulnerable:0b,MaxHealth:15.0f,PersistenceRequired:0b,Spigot.ticksLived:177034,"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-52226803425217L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bee_behavior:0,bee_endurance:2,bee_productivity:1,bee_temper:1,bee_type:"solitary",bee_weather_tolerance:0,id:"productivebees:chocolate_mining_bee"},FlowerPos:{X:-191,Y:63,Z:-270},MinOccupationTicks:4800,Name:"Chocolate Mining Bee",TicksInHive:3104}]}}}},{Count:1b,Slot:11b,id:"minecraft:jungle_planks"},{Count:5b,Slot:14b,id:"minecraft:stick"},{Count:8b,Slot:15b,id:"minecraft:jungle_planks"},{Count:42b,Slot:25b,id:"minecraft:jungle_log"},{Count:1b,Slot:28b,id:"minecraft:bow",tag:{Damage:0}},{Count:8b,Slot:29b,id:"minecraft:cooked_beef"}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-187.17213586954074d,63.0d,-265.3399821740985d],Railways_DataVersion:2,Rotation:[-146.84851f,90.0f],Score:10,SelectedItemSlot:2,SleepTimer:0s,Spigot.ticksLived:9027,UUID:[I;987746204,433539448,-1886822188,-1298754811],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-51402169688001L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:1,XpP:0.33333337f,XpSeed:0,XpTotal:10,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752160741952L,keepLevel:0b,lastKnownName:"Yamada_nemui",lastPlayed:1752161195939L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:2.7103076f,foodLevel:13,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","minecraft:charcoal","mcwroofs:jungle_planks_attic_roof","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","dyenamics:banner/lavender_banner","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","handcrafted:spider_trophy","mcwfurnitures:jungle_bookshelf_drawer","mcwtrpdoors:jungle_cottage_trapdoor","simplylight:illuminant_block_on_toggle","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","cfm:jungle_coffee_table","mcwpaths:jungle_planks_path","create:crafting/appliances/crafting_blueprint","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","utilitix:reinforced_rail","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/jungle_logs_to_doors","mcwbiomesoplenty:hellbark_picket_fence","mcwdoors:jungle_nether_door","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","cfm:stripped_jungle_kitchen_counter","aquaculture:dark_oak_fish_mount","mcwfences:acacia_wired_fence","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwfurnitures:jungle_modern_desk","supplementaries:flags/flag_brown","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","securitycraft:reinforced_cherry_fence_gate","mcwfences:acacia_hedge","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","dyenamics:banner/icy_blue_banner","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","energymeter:meter","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","minecolonies:shapetool","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","aquaculture:sushi","mcwfurnitures:jungle_table","mcwtrpdoors:jungle_blossom_trapdoor","mcwfences:flowering_azalea_hedge","mcwfences:acacia_stockade_fence","aquaculture:birch_fish_mount","aquaculture:wooden_fillet_knife","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","simplylight:illuminant_red_block_toggle","sophisticatedstorage:generic_limited_barrel_4","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","undergarden:torch_ditchbulb_paste","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:jungle_kitchen_sink_light","mcwroofs:jungle_attic_roof","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:wooden_pickaxe","supplementaries:clock_block","biomesoplenty:mahogany_boat","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","solcarrot:food_book","mcwbridges:jungle_rail_bridge","utilitarian:utility/charcoal_from_campfire","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","aquaculture:double_hook","mcwbiomesoplenty:willow_stockade_fence","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","aquaculture:tin_can_to_iron_nugget","simplylight:illuminant_orange_block_on_toggle","mcwfences:andesite_railing_gate","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","cfm:stripped_jungle_park_bench","dyenamics:bed/navy_bed","ae2wtlib:magnet_card","securitycraft:reinforced_dark_oak_fence_gate","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwdoors:jungle_bark_glass_door","supplementaries:timber_frame","mcwfences:deepslate_railing_gate","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","supplementaries:daub_cross_brace","mcwwindows:jungle_four_window","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","handcrafted:jungle_bench","dyenamics:banner/honey_banner","mcwfences:stone_grass_topped_wall","mcwfences:mud_brick_grass_topped_wall","minecraft:jungle_door","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","cfm:stripped_jungle_bedside_cabinet","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwbiomesoplenty:jacaranda_wired_fence","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","utilitix:directional_highspeed_rail","mcwbiomesoplenty:magic_picket_fence","twilightforest:dark_boat","biomesoplenty:umbran_boat","handcrafted:jungle_drawer","securitycraft:reinforced_dark_oak_fence","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","cfm:stripped_jungle_cabinet","mcwbiomesoplenty:pine_hedge","cfm:jungle_desk","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwfences:nether_brick_pillar_wall","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","simplylight:illuminant_blue_block_dyed","mcwfurnitures:jungle_modern_wardrobe","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","minecraft:jungle_trapdoor","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwbiomesoplenty:maple_curved_gate","simplylight:illuminant_yellow_block_toggle","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","minecraft:bamboo_raft","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfurnitures:stripped_jungle_chair","mcwroofs:jungle_upper_steep_roof","cfm:stripped_jungle_chair","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwtrpdoors:jungle_swamp_trapdoor","minecraft:cherry_boat","simplylight:illuminant_light_gray_block_on_dyed","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwbiomesoplenty:redwood_pyramid_gate","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwfences:railing_prismarine_wall","mcwbiomesoplenty:hellbark_wired_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwfurnitures:jungle_wardrobe","dyenamics:bed/rose_bed","mcwfences:end_brick_railing_gate","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","simplylight:illuminant_light_gray_block_toggle","utilitix:hand_bell","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","undergarden:wigglewood_boat","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:fir_hedge","mcwfences:deepslate_brick_pillar_wall","handcrafted:jungle_pillar_trim","corail_woodcutter:oak_woodcutter","mcwfences:birch_highley_gate","securitycraft:reinforced_crimson_fence","minecraft:wooden_shovel","mcwfences:railing_diorite_wall","mcwfences:birch_wired_fence","mcwwindows:birch_curtain_rod","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwdoors:jungle_stable_door","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","mcwfences:jungle_picket_fence","mcwdoors:print_jungle","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","utilitarian:utility/jungle_logs_to_stairs","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:birch_curved_gate","mcwfences:mud_brick_railing_gate","supplementaries:faucet","simplylight:illuminant_purple_block_toggle","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","create:jungle_window","mcwfences:railing_blackstone_wall","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","supplementaries:flags/flag_light_blue","minecraft:chest","supplementaries:pulley","mcwbridges:jungle_log_bridge_middle","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:birch_horse_fence","mcwfences:modern_sandstone_wall","dyenamics:banner/peach_banner","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","dyenamics:bed/bubblegum_bed","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","securitycraft:reinforced_mangrove_fence","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","simplylight:illuminant_brown_block_toggle","mcwfences:red_sandstone_railing_gate","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","simplylight:illuminant_black_block_on_toggle","mcwfurnitures:stripped_jungle_drawer_counter","mcwdoors:jungle_japanese2_door","mcwwindows:crimson_curtain_rod","mcwwindows:jungle_plank_pane_window","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:red_maple_hedge","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","mcwfences:dark_oak_horse_fence","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwbiomesoplenty:dead_picket_fence","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwfences:spruce_curved_gate","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","minecraft:wooden_hoe","mcwbiomesoplenty:maple_picket_fence","mcwwindows:mangrove_curtain_rod","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","farmersdelight:cutting_board","mcwbiomesoplenty:magic_stockade_fence","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","mcwdoors:jungle_classic_door","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwfurnitures:jungle_coffee_table","naturescompass:natures_compass","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","minecraft:dispenser","minecraft:wooden_axe","aquaculture:cooked_fish_fillet_from_smoking","utilitarian:utility/jungle_logs_to_slabs","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","supplementaries:sign_post_jungle","dyenamics:bed/spring_green_bed","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","mcwdoors:jungle_four_panel_door","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","supplementaries:daub_brace","simplylight:illuminant_light_blue_block_on_dyed","simplylight:lamp_post","mcwfences:spruce_horse_fence","mcwwindows:jungle_plank_four_window","supplementaries:flags/flag_lime","mcwfences:bamboo_wired_fence","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","mcwfences:crimson_curved_gate","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","handcrafted:jungle_desk","corail_woodcutter:bamboo_woodcutter","supplementaries:flags/flag_magenta","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","minecraft:jungle_wood","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","mcwfences:andesite_pillar_wall","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","dyenamics:banner/bubblegum_banner","mcwfences:majestic_metal_fence_gate","handcrafted:jungle_chair","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","farmersdelight:flint_knife","biomesoplenty:maple_boat","croptopia:campfire_molasses","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","cfm:jungle_kitchen_sink_dark","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","silentgear:upgrade_base","mcwbiomesoplenty:rainbow_birch_hedge","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","mcwfences:expanded_mesh_metal_fence","mcwroofs:jungle_steep_roof","mcwfences:crimson_picket_fence","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwdoors:jungle_cottage_door","mcwfurnitures:jungle_chair","mcwbiomesoplenty:maple_stockade_fence","simplylight:illuminant_purple_block_on_toggle","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_orange_block_dyed","simplylight:illuminant_gray_block_dyed","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwtrpdoors:print_beach","cfm:jungle_upgraded_fence","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","terralith:dispenser_alt","dyenamics:bed/maroon_bed","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","securitycraft:sc_manual","mcwfences:bamboo_highley_gate","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_dispenser","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","mcwroofs:jungle_roof","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwfences:jungle_curved_gate","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","securitycraft:reinforced_spruce_fence_gate","mcwwindows:dark_oak_curtain_rod","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","supplementaries:daub_frame","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","aquaculture:iron_fishing_rod","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","mcwfurnitures:jungle_desk","minecraft:crafting_table","twilightforest:time_boat","mcwroofs:jungle_planks_lower_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwfences:spruce_wired_fence","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","mcwfences:birch_picket_fence","mcwfences:double_curved_metal_fence","handcrafted:jungle_dining_bench","mcwbiomesoplenty:magic_wired_fence","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwfences:oak_picket_fence","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","simplylight:illuminant_purple_block_on_dyed","framedblocks:framed_cube","aquaculture:redstone_hook","supplementaries:flags/flag_blue","mcwbiomesoplenty:flowering_oak_hedge","mcwroofs:jungle_planks_top_roof","utilitarian:utility/logs_to_bowls","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","aquaculture:bobber","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","simplylight:illuminant_brown_block_on_dyed","handcrafted:bear_trophy","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","minecraft:jungle_slab","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","silentgear:rough_rod","mcwfurnitures:stripped_jungle_cupboard_counter","minecraft:dark_oak_boat","rftoolsbase:crafting_card","aquaculture:gold_hook","mcwbiomesoplenty:palm_wired_fence","supplementaries:turn_table","supplementaries:flags/flag_pink","handcrafted:wither_skeleton_trophy","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwbiomesoplenty:hellbark_stockade_fence","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","minecraft:stick","securitycraft:reinforced_acacia_fence_gate","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","minecraft:jungle_pressure_plate","mcwfences:andesite_grass_topped_wall","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:silverfish_trophy","minecraft:jungle_button","mcwdoors:jungle_tropical_door","securitycraft:reinforced_birch_fence_gate","dyenamics:bed/lavender_bed","mcwdoors:jungle_glass_door","simplylight:illuminant_cyan_block_dyed","simplylight:illuminant_pink_block_dyed","mcwfurnitures:jungle_triple_drawer","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","crafting_on_a_stick:crafting_table","mcwfurnitures:jungle_end_table","undergarden:shard_torch","mcwfences:bamboo_picket_fence","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:jungle_lower_bookshelf_drawer","handcrafted:goat_trophy","mcwwindows:stripped_jungle_log_window2","paraglider:paraglider","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","supplementaries:timber_cross_brace","simplylight:illuminant_lime_block_on_dyed","simplylight:illuminant_orange_block_on_dyed","mcwbridges:jungle_bridge_pier","mcwfences:bamboo_pyramid_gate","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","handcrafted:jungle_fancy_bed","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:snowblossom_hedge","minecraft:oak_boat","sophisticatedstorage:jungle_chest","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","simplylight:illuminant_blue_block_on_toggle","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbiomesoplenty:redwood_horse_fence","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwwindows:jungle_window2","minecraft:jungle_fence","mcwtrpdoors:jungle_mystic_trapdoor","mcwfences:mesh_metal_fence","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:empyreal_picket_fence","mcwbiomesoplenty:pine_curved_gate","aether:bow_repairing","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","dyenamics:banner/conifer_banner","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","dyenamics:bed/cherenkov_bed","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","cfm:stripped_jungle_kitchen_drawer","mcwdoors:jungle_stable_head_door","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwwindows:jungle_plank_window2","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","dyenamics:banner/maroon_banner","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","mcwfences:stone_pillar_wall","simplylight:illuminant_gray_block_toggle","cfm:jungle_table","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","utilitix:armed_stand","botania:lexicon","mcwdoors:jungle_modern_door","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:palm_hedge","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwfences:oak_hedge","mcwfurnitures:stripped_jungle_coffee_table","mcwfences:railing_sandstone_wall","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","handcrafted:skeleton_horse_trophy","mcwbiomesoplenty:hellbark_highley_gate","mcwfurnitures:stripped_jungle_double_drawer","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:spruce_boat","minecraft:birch_boat","handcrafted:jungle_counter","dyenamics:banner/navy_banner","mcwdoors:jungle_barn_door","minecraft:paper","minecraft:campfire","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","handcrafted:salmon_trophy","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence"],toBeDisplayed:["corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","minecraft:charcoal","mcwroofs:jungle_planks_attic_roof","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","dyenamics:banner/lavender_banner","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","handcrafted:spider_trophy","mcwfurnitures:jungle_bookshelf_drawer","mcwtrpdoors:jungle_cottage_trapdoor","simplylight:illuminant_block_on_toggle","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","cfm:jungle_coffee_table","mcwpaths:jungle_planks_path","create:crafting/appliances/crafting_blueprint","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","utilitix:reinforced_rail","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/jungle_logs_to_doors","mcwbiomesoplenty:hellbark_picket_fence","mcwdoors:jungle_nether_door","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","cfm:stripped_jungle_kitchen_counter","aquaculture:dark_oak_fish_mount","mcwfences:acacia_wired_fence","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwfurnitures:jungle_modern_desk","supplementaries:flags/flag_brown","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","securitycraft:reinforced_cherry_fence_gate","mcwfences:acacia_hedge","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","dyenamics:banner/icy_blue_banner","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","energymeter:meter","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","minecolonies:shapetool","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","aquaculture:sushi","mcwfurnitures:jungle_table","mcwtrpdoors:jungle_blossom_trapdoor","mcwfences:flowering_azalea_hedge","mcwfences:acacia_stockade_fence","aquaculture:birch_fish_mount","aquaculture:wooden_fillet_knife","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","simplylight:illuminant_red_block_toggle","sophisticatedstorage:generic_limited_barrel_4","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","undergarden:torch_ditchbulb_paste","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:jungle_kitchen_sink_light","mcwroofs:jungle_attic_roof","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:wooden_pickaxe","supplementaries:clock_block","biomesoplenty:mahogany_boat","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","solcarrot:food_book","mcwbridges:jungle_rail_bridge","utilitarian:utility/charcoal_from_campfire","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","aquaculture:double_hook","mcwbiomesoplenty:willow_stockade_fence","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","aquaculture:tin_can_to_iron_nugget","simplylight:illuminant_orange_block_on_toggle","mcwfences:andesite_railing_gate","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","cfm:stripped_jungle_park_bench","dyenamics:bed/navy_bed","ae2wtlib:magnet_card","securitycraft:reinforced_dark_oak_fence_gate","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwdoors:jungle_bark_glass_door","supplementaries:timber_frame","mcwfences:deepslate_railing_gate","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","supplementaries:daub_cross_brace","mcwwindows:jungle_four_window","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","handcrafted:jungle_bench","dyenamics:banner/honey_banner","mcwfences:stone_grass_topped_wall","mcwfences:mud_brick_grass_topped_wall","minecraft:jungle_door","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","cfm:stripped_jungle_bedside_cabinet","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwbiomesoplenty:jacaranda_wired_fence","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","utilitix:directional_highspeed_rail","mcwbiomesoplenty:magic_picket_fence","twilightforest:dark_boat","biomesoplenty:umbran_boat","handcrafted:jungle_drawer","securitycraft:reinforced_dark_oak_fence","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","cfm:stripped_jungle_cabinet","mcwbiomesoplenty:pine_hedge","cfm:jungle_desk","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwfences:nether_brick_pillar_wall","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","simplylight:illuminant_blue_block_dyed","mcwfurnitures:jungle_modern_wardrobe","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","minecraft:jungle_trapdoor","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwbiomesoplenty:maple_curved_gate","simplylight:illuminant_yellow_block_toggle","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","minecraft:bamboo_raft","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfurnitures:stripped_jungle_chair","mcwroofs:jungle_upper_steep_roof","cfm:stripped_jungle_chair","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwtrpdoors:jungle_swamp_trapdoor","minecraft:cherry_boat","simplylight:illuminant_light_gray_block_on_dyed","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwbiomesoplenty:redwood_pyramid_gate","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwfences:railing_prismarine_wall","mcwbiomesoplenty:hellbark_wired_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwfurnitures:jungle_wardrobe","dyenamics:bed/rose_bed","mcwfences:end_brick_railing_gate","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","simplylight:illuminant_light_gray_block_toggle","utilitix:hand_bell","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","undergarden:wigglewood_boat","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:fir_hedge","mcwfences:deepslate_brick_pillar_wall","handcrafted:jungle_pillar_trim","corail_woodcutter:oak_woodcutter","mcwfences:birch_highley_gate","securitycraft:reinforced_crimson_fence","minecraft:wooden_shovel","mcwfences:railing_diorite_wall","mcwfences:birch_wired_fence","mcwwindows:birch_curtain_rod","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwdoors:jungle_stable_door","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","mcwfences:jungle_picket_fence","mcwdoors:print_jungle","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","utilitarian:utility/jungle_logs_to_stairs","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:birch_curved_gate","mcwfences:mud_brick_railing_gate","supplementaries:faucet","simplylight:illuminant_purple_block_toggle","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","create:jungle_window","mcwfences:railing_blackstone_wall","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","supplementaries:flags/flag_light_blue","minecraft:chest","supplementaries:pulley","mcwbridges:jungle_log_bridge_middle","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:birch_horse_fence","mcwfences:modern_sandstone_wall","dyenamics:banner/peach_banner","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","dyenamics:bed/bubblegum_bed","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","securitycraft:reinforced_mangrove_fence","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","simplylight:illuminant_brown_block_toggle","mcwfences:red_sandstone_railing_gate","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","simplylight:illuminant_black_block_on_toggle","mcwfurnitures:stripped_jungle_drawer_counter","mcwdoors:jungle_japanese2_door","mcwwindows:crimson_curtain_rod","mcwwindows:jungle_plank_pane_window","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:red_maple_hedge","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","mcwfences:dark_oak_horse_fence","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwbiomesoplenty:dead_picket_fence","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwfences:spruce_curved_gate","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","minecraft:wooden_hoe","mcwbiomesoplenty:maple_picket_fence","mcwwindows:mangrove_curtain_rod","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","farmersdelight:cutting_board","mcwbiomesoplenty:magic_stockade_fence","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","mcwdoors:jungle_classic_door","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwfurnitures:jungle_coffee_table","naturescompass:natures_compass","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","minecraft:dispenser","minecraft:wooden_axe","aquaculture:cooked_fish_fillet_from_smoking","utilitarian:utility/jungle_logs_to_slabs","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","supplementaries:sign_post_jungle","dyenamics:bed/spring_green_bed","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","mcwdoors:jungle_four_panel_door","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","supplementaries:daub_brace","simplylight:illuminant_light_blue_block_on_dyed","simplylight:lamp_post","mcwfences:spruce_horse_fence","mcwwindows:jungle_plank_four_window","supplementaries:flags/flag_lime","mcwfences:bamboo_wired_fence","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","mcwfences:crimson_curved_gate","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","handcrafted:jungle_desk","corail_woodcutter:bamboo_woodcutter","supplementaries:flags/flag_magenta","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","minecraft:jungle_wood","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","mcwfences:andesite_pillar_wall","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","dyenamics:banner/bubblegum_banner","mcwfences:majestic_metal_fence_gate","handcrafted:jungle_chair","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","farmersdelight:flint_knife","biomesoplenty:maple_boat","croptopia:campfire_molasses","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","cfm:jungle_kitchen_sink_dark","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","silentgear:upgrade_base","mcwbiomesoplenty:rainbow_birch_hedge","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","mcwfences:expanded_mesh_metal_fence","mcwroofs:jungle_steep_roof","mcwfences:crimson_picket_fence","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwdoors:jungle_cottage_door","mcwfurnitures:jungle_chair","mcwbiomesoplenty:maple_stockade_fence","simplylight:illuminant_purple_block_on_toggle","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_orange_block_dyed","simplylight:illuminant_gray_block_dyed","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwtrpdoors:print_beach","cfm:jungle_upgraded_fence","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","terralith:dispenser_alt","dyenamics:bed/maroon_bed","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","securitycraft:sc_manual","mcwfences:bamboo_highley_gate","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_dispenser","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","mcwroofs:jungle_roof","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwfences:jungle_curved_gate","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","securitycraft:reinforced_spruce_fence_gate","mcwwindows:dark_oak_curtain_rod","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","supplementaries:daub_frame","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","aquaculture:iron_fishing_rod","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","mcwfurnitures:jungle_desk","minecraft:crafting_table","twilightforest:time_boat","mcwroofs:jungle_planks_lower_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwfences:spruce_wired_fence","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","mcwfences:birch_picket_fence","mcwfences:double_curved_metal_fence","handcrafted:jungle_dining_bench","mcwbiomesoplenty:magic_wired_fence","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwfences:oak_picket_fence","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","simplylight:illuminant_purple_block_on_dyed","framedblocks:framed_cube","aquaculture:redstone_hook","supplementaries:flags/flag_blue","mcwbiomesoplenty:flowering_oak_hedge","mcwroofs:jungle_planks_top_roof","utilitarian:utility/logs_to_bowls","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","aquaculture:bobber","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","simplylight:illuminant_brown_block_on_dyed","handcrafted:bear_trophy","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","minecraft:jungle_slab","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","silentgear:rough_rod","mcwfurnitures:stripped_jungle_cupboard_counter","minecraft:dark_oak_boat","rftoolsbase:crafting_card","aquaculture:gold_hook","mcwbiomesoplenty:palm_wired_fence","supplementaries:turn_table","supplementaries:flags/flag_pink","handcrafted:wither_skeleton_trophy","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwbiomesoplenty:hellbark_stockade_fence","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","minecraft:stick","securitycraft:reinforced_acacia_fence_gate","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","minecraft:jungle_pressure_plate","mcwfences:andesite_grass_topped_wall","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:silverfish_trophy","minecraft:jungle_button","mcwdoors:jungle_tropical_door","securitycraft:reinforced_birch_fence_gate","dyenamics:bed/lavender_bed","mcwdoors:jungle_glass_door","simplylight:illuminant_cyan_block_dyed","simplylight:illuminant_pink_block_dyed","mcwfurnitures:jungle_triple_drawer","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","crafting_on_a_stick:crafting_table","mcwfurnitures:jungle_end_table","undergarden:shard_torch","mcwfences:bamboo_picket_fence","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:jungle_lower_bookshelf_drawer","handcrafted:goat_trophy","mcwwindows:stripped_jungle_log_window2","paraglider:paraglider","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","supplementaries:timber_cross_brace","simplylight:illuminant_lime_block_on_dyed","simplylight:illuminant_orange_block_on_dyed","mcwbridges:jungle_bridge_pier","mcwfences:bamboo_pyramid_gate","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","handcrafted:jungle_fancy_bed","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:snowblossom_hedge","minecraft:oak_boat","sophisticatedstorage:jungle_chest","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","simplylight:illuminant_blue_block_on_toggle","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbiomesoplenty:redwood_horse_fence","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwwindows:jungle_window2","minecraft:jungle_fence","mcwtrpdoors:jungle_mystic_trapdoor","mcwfences:mesh_metal_fence","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:empyreal_picket_fence","mcwbiomesoplenty:pine_curved_gate","aether:bow_repairing","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","dyenamics:banner/conifer_banner","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","dyenamics:bed/cherenkov_bed","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","cfm:stripped_jungle_kitchen_drawer","mcwdoors:jungle_stable_head_door","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwwindows:jungle_plank_window2","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","dyenamics:banner/maroon_banner","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","mcwfences:stone_pillar_wall","simplylight:illuminant_gray_block_toggle","cfm:jungle_table","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","utilitix:armed_stand","botania:lexicon","mcwdoors:jungle_modern_door","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:palm_hedge","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwfences:oak_hedge","mcwfurnitures:stripped_jungle_coffee_table","mcwfences:railing_sandstone_wall","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","handcrafted:skeleton_horse_trophy","mcwbiomesoplenty:hellbark_highley_gate","mcwfurnitures:stripped_jungle_double_drawer","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:spruce_boat","minecraft:birch_boat","handcrafted:jungle_counter","dyenamics:banner/navy_banner","mcwdoors:jungle_barn_door","minecraft:paper","minecraft:campfire","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","handcrafted:salmon_trophy","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:9027,warning_level:0}}