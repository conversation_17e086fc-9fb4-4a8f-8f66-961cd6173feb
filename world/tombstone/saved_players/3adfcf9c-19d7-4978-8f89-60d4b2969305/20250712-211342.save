{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:4.5d,Name:"forge:block_reach"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"attributeslib:creative_flight"}],BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:-29137058447294L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-50852413845517L,UID:[I;-*********,*********,-**********,-**********]},{FromDim:"minecraft:overworld",FromPos:219902050677334083L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:27488065572302977L,UID:[I;-**********,-**********,-**********,**********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-610038287,666059998,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","fe5c8ef6-4d05-416c-b01d-4453f78742fd"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-610038287,666059998,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-610038287,666059998,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-610038287,666059998,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;-610038287,666059998,-**********,-**********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-**********,-721924235,-**********,-**********],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:73840},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:7},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:chest",ItemStack:{Count:1b,id:"minecraft:chest"}}]},"rftoolsutility:properties":{allowFlying:0b,buffTicks:141,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:bread","minecraft:cooked_beef"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:1,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:-29137058447294L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-50852413845517L,UID:[I;-*********,*********,-**********,-**********]},{FromDim:"minecraft:overworld",FromPos:219902050677334083L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:27488065572302977L,UID:[I;-**********,-**********,-**********,**********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","fe5c8ef6-4d05-416c-b01d-4453f78742fd"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:800548,tb_last_ground_location_y:59,tb_last_ground_location_z:-83,twilightforest_banished:1b},"quark:trying_crawl":0b,simplylight_unlocked:2},Health:20.0f,HurtByTimestamp:32590,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"minecraft:stone_axe",tag:{Damage:89}},{Count:2b,Slot:1b,id:"minecraft:cobblestone"},{Count:1b,Slot:2b,id:"minecraft:diamond_pickaxe",tag:{Damage:133}},{Count:1b,Slot:3b,id:"ars_nouveau:caster_tome",tag:{"ars_nouveau:caster":{current_slot:0,flavor:"Fire at a body of water to create a Ice bubble in the depths.",hidden_recipe:"",is_hidden:0b,spell_count:1,spells:{spell0:{name:"Poseidon's Refuge",recipe:{part0:"ars_nouveau:glyph_projectile",part1:"ars_nouveau:glyph_sensitive",part2:"ars_nouveau:glyph_light",part3:"ars_nouveau:burst",part4:"ars_nouveau:glyph_aoe",part5:"ars_nouveau:glyph_aoe",part6:"ars_nouveau:glyph_sensitive",part7:"ars_nouveau:glyph_freeze",part8:"ars_nouveau:glyph_break",part9:"ars_nouveau:glyph_freeze",size:10},sound:{pitch:1.0f,soundTag:{id:"ars_nouveau:tempestry_family"},volume:1.0f},spellColor:{b:255,g:1,r:1,type:"ars_nouveau:constant"}}}},display:{Name:'{"italic":true,"color":"dark_purple","text":"Poseidon\'s Refuge"}'}}},{Count:1b,Slot:4b,id:"waystones:mossy_waystone"},{Count:1b,Slot:7b,id:"minecraft:water_bucket"},{Count:1b,Slot:8b,id:"minecraft:flint_and_steel",tag:{Damage:2}}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[800548.3703283003d,59.0d,-82.69999998807907d],Railways_DataVersion:2,Rotation:[-135.43188f,-72.14999f],Score:245,SelectedItemSlot:1,SleepTimer:0s,Spigot.ticksLived:73835,UUID:[I;987746204,433539448,-1886822188,-1298754811],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":220053233525772347L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:12,XpP:0.9354836f,XpSeed:-1110195761,XpTotal:245,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752160741952L,keepLevel:0b,lastKnownName:"Yamada_nemui",lastPlayed:1752326022174L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:2.646331f,foodLevel:14,foodSaturationLevel:9.8f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","trashcans:energy_trash_can","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","mcwfurnitures:stripped_cherry_modern_desk","handcrafted:spider_trophy","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","croptopia:shaped_beef_stew","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mcwfurnitures:cherry_chair","mcwbiomesoplenty:empyreal_hedge","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","supplementaries:flint_block","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwdoors:jungle_nether_door","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","minecraft:leather_chestplate","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","immersiveengineering:crafting/radiator","mcwfurnitures:cherry_modern_desk","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","aether:skyroot_fletching_table","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","sliceanddice:slicer","xnet:connector_red_dye","minecolonies:shapetool","supplementaries:bubble_blower","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","supplementaries:crank","mcwroofs:gutter_base_light_gray","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","minecraft:mossy_stone_brick_slab","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwdoors:cherry_bamboo_door","mcwtrpdoors:cherry_whispering_trapdoor","aquaculture:birch_fish_mount","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwpaths:cobblestone_dumble_paving","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","dyenamics:aquamarine_concrete_powder","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","handcrafted:jungle_cupboard","tombstone:ankh_of_prayer","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwdoors:cherry_whispering_door","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","utilitarian:utility/charcoal_from_campfire","cfm:acacia_kitchen_drawer","minecraft:orange_dye_from_torchflower","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfurnitures:stripped_cherry_cupboard_counter","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","farmersdelight:cooking/pasta_with_meatballs","mcwfurnitures:cherry_cupboard_counter","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","minecraft:jungle_fence_gate","utilitarian:angel_block","minecraft:red_concrete_powder","simplylight:illuminant_orange_block_on_toggle","dyenamics:spring_green_concrete_powder","mcwpaths:sandstone_running_bond_stairs","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","biomesoplenty:magenta_dye_from_wildflower","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwtrpdoors:cherry_glass_trapdoor","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","mcwdoors:cherry_japanese_door","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwtrpdoors:cherry_barred_trapdoor","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwwindows:jungle_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","croptopia:hamburger","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","minecraft:jungle_door","xnet:connector_routing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","utilitix:directional_highspeed_rail","mcwdoors:garage_gray_door","mcwbiomesoplenty:magic_picket_fence","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","securitycraft:reinforced_diorite","cfm:green_grill","handcrafted:jungle_drawer","minecraft:stone_brick_stairs","cfm:orange_grill","cfm:brown_grill","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","cfm:jungle_desk","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","cfm:gray_kitchen_drawer","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","utilitarian:utility/cherry_logs_to_stairs","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twigs:silt","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","cfm:mangrove_kitchen_sink_dark","silentgear:diamond_shard","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","mcwroofs:cherry_attic_roof","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","mcwfurnitures:stripped_cherry_table","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:bamboo_raft","cfm:green_kitchen_sink","mcwfurnitures:cherry_counter","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","cfm:acacia_kitchen_sink_dark","cfm:stripped_jungle_chair","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","minecraft:mossy_stone_bricks_from_moss_block","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","forbidden_arcanus:diamond_blacksmith_gavel","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwfurnitures:stripped_cherry_lower_bookshelf_drawer","twigs:rocky_dirt","mcwfences:end_brick_railing_gate","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","simplylight:illuminant_light_gray_block_toggle","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwpaths:sandstone_flagstone_slab","botania:white_shiny_flower","mcwbridges:cherry_bridge_pier","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","securitycraft:reinforced_crimson_fence","minecraft:wooden_shovel","aether:stone_axe_repairing","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwfurnitures:cherry_double_wardrobe","mcwfurnitures:cherry_double_drawer","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwfurnitures:stripped_cherry_bookshelf_drawer","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","cfm:light_blue_cooler","railcraft:signal_circuit","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","sophisticatedstorage:oak_chest_from_vanilla_chest","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","cfm:pink_cooler","railcraft:signal_block_surveyor","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","mcwpaths:stone_windmill_weave","cfm:red_grill","mcwfences:railing_blackstone_wall","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","mcwpaths:sandstone_windmill_weave_stairs","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","cfm:light_blue_grill","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","cfm:light_blue_kitchen_drawer","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwtrpdoors:cherry_tropical_trapdoor","securitycraft:reinforced_mangrove_fence","cfm:stripped_warped_mail_box","additionallanterns:normal_sandstone_chain","mcwpaths:sandstone_windmill_weave","cfm:light_gray_trampoline","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","cfm:purple_kitchen_drawer","botania:flower_bag","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfurnitures:cherry_striped_chair","tombstone:green_marble","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwfurnitures:cherry_large_drawer","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:red_maple_hedge","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","securitycraft:reinforced_redstone_lamp","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","mcwroofs:gutter_middle_blue","mcwdoors:cherry_glass_door","sfm:manager","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","cfm:purple_grill","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","littlelogistics:tug_route","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","minecraft:purple_concrete_powder","minecraft:sugar_from_sugar_cane","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","mcwlights:oak_ceiling_fan_light","mcwfurnitures:stripped_cherry_modern_wardrobe","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","xnet:advanced_connector_yellow","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","cfm:orange_trampoline","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","minecraft:wooden_hoe","cfm:light_blue_kitchen_sink","minecraft:cooked_beef_from_campfire_cooking","createoreexcavation:diamond_drill","mcwbiomesoplenty:maple_picket_fence","mcwfurnitures:cherry_glass_table","mcwfurnitures:stripped_cherry_desk","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwdoors:cherry_four_panel_door","mcwroofs:gutter_base_green","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","littlelogistics:vessel_charger","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","mcwlights:soul_dark_oak_tiki_torch","mcwfurnitures:cherry_desk","mcwbridges:stone_bridge_pier","naturescompass:natures_compass","railcraft:radio_circuit","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","minecraft:wooden_axe","mcwpaths:cobbled_deepslate_clover_paving","mcwfences:panelled_metal_fence_gate","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:cherry_coffee_table","cfm:birch_kitchen_sink_dark","supplementaries:daub_brace","mcwtrpdoors:cherry_four_panel_trapdoor","simplylight:illuminant_light_blue_block_on_dyed","simplylight:lamp_post","mcwfences:spruce_horse_fence","minecraft:hopper","mcwfurnitures:stripped_cherry_double_wardrobe","croptopia:beef_jerky","mcwwindows:jungle_plank_four_window","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","silentgear:sinew_fiber","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","minecraft:iron_bars","corail_woodcutter:mangrove_woodcutter","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","cfm:oak_kitchen_drawer","supplementaries:flags/flag_magenta","minecraft:iron_helmet","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","securitycraft:alarm","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","quark:tweaks/crafting/utility/tools/stone_pickaxe","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","mcwfurnitures:stripped_cherry_double_drawer_counter","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwpaths:sandstone_honeycomb_paving","mcwfurnitures:stripped_cherry_coffee_table","mcwdoors:print_waffle","twigs:torchflower_paper_lantern","utilitix:crude_furnace","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwfurnitures:cherry_stool_chair","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwpaths:cobbled_deepslate_flagstone_path","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","mcwfences:crimson_picket_fence","mcwfurnitures:cherry_wardrobe","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","mcwroofs:cobblestone_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","sfm:water_tank","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwfences:dark_oak_picket_fence","alchemistry:compactor","travelersbackpack:sandstone","cfm:jungle_upgraded_fence","minecraft:dropper","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","minecraft:lime_dye_from_smelting","mcwfurnitures:cherry_bookshelf","mcwfences:bamboo_highley_gate","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","bigreactors:reactor/basic/passivetap_fe","mcwfurnitures:stripped_cherry_bookshelf_cupboard","securitycraft:reinforced_dispenser","mcwdoors:cherry_stable_head_door","mcwroofs:jungle_roof","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwfences:jungle_curved_gate","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_spruce_fence_gate","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwtrpdoors:cherry_mystic_trapdoor","mcwtrpdoors:metal_trapdoor","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","supplementaries:daub_frame","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","mcwpaths:sandstone_running_bond_slab","minecraft:crafting_table","twilightforest:time_boat","minecraft:sandstone_wall","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwbridges:balustrade_sandstone_bridge","utilitarian:utility/cherry_logs_to_boats","securitycraft:briefcase","mcwfences:spruce_wired_fence","cfm:white_grill","minecraft:iron_nugget_from_smelting","mcwlights:iron_low_candle_holder","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","mcwroofs:sandstone_lower_roof","travelersbackpack:hose_nozzle","securitycraft:claymore","handcrafted:jungle_dining_bench","cfm:red_cooler","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","create:crafting/logistics/pulse_extender","twigs:polished_tuff_stonecutting","supplementaries:lock_block","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfurnitures:stripped_cherry_large_drawer","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwpaths:sandstone_basket_weave_paving","mcwdoors:cherry_mystic_door","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwroofs:cherry_steep_roof","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwfurnitures:stripped_cherry_end_table","mcwroofs:orange_concrete_upper_lower_roof","mcwtrpdoors:cherry_paper_trapdoor","mcwwindows:cherry_plank_window2","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","quark:tweaks/crafting/utility/tools/stone_axe","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","cfm:acacia_mail_box","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:raw_iron_block","mcwroofs:cherry_upper_lower_roof","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","minecraft:redstone_block","minecraft:shears","croptopia:fruit_smoothie","supplementaries:flags/flag_blue","mcwtrpdoors:metal_warning_trapdoor","mcwroofs:jungle_planks_top_roof","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","mcwfurnitures:jungle_striped_chair","xnet:advanced_connector_yellow_dye","allthecompressed:compress/cobblestone_1x","twigs:mossy_bricks_from_moss_block","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","aquaculture:bobber","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","mcwpaths:cobblestone_honeycomb_paving","utilitarian:utility/cherry_logs_to_doors","simplylight:edge_light_top","mcwfences:granite_pillar_wall","mcwfurnitures:cherry_bookshelf_cupboard","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwroofs:cherry_upper_steep_roof","simplylight:illuminant_brown_block_on_dyed","securitycraft:reinforced_warped_fence","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","minecraft:item_frame","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","minecraft:arrow","mcwroofs:gutter_middle_light_blue","supplementaries:candle_holders/candle_holder_gray","mcwroofs:light_gray_concrete_steep_roof","supplementaries:turn_table","supplementaries:flags/flag_pink","mcwfurnitures:stripped_cherry_glass_table","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwroofs:cherry_top_roof","mcwroofs:sandstone_attic_roof","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfurnitures:cherry_modern_wardrobe","mcwfences:modern_stone_brick_wall","mcwfurnitures:stripped_cherry_chair","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","utilitarian:utility/cherry_logs_to_slabs","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","biomesoplenty:white_dye_from_tall_white_lavender","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","mcwfurnitures:stripped_cherry_triple_drawer","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","mcwpaths:stone_windmill_weave_path","mcwfurnitures:cherry_drawer_counter","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","mcwdoors:cherry_beach_door","mcwroofs:orange_concrete_steep_roof","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","securitycraft:reinforced_birch_fence_gate","mcwroofs:gutter_base_light_blue","minecraft:diamond_block","mcwroofs:deepslate_roof","bigreactors:turbine/reinforced/passivefluidport_forge","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","cfm:brown_cooler","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","rftoolsutility:inventoryplus_module","mcwdoors:cherry_western_door","mcwfurnitures:jungle_end_table","undergarden:shard_torch","mcwfences:bamboo_picket_fence","cfm:green_cooler","handcrafted:goat_trophy","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","cfm:stripped_crimson_kitchen_sink_light","simplylight:illuminant_lime_block_on_dyed","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","cfm:dark_oak_kitchen_sink_light","mcwbridges:jungle_bridge_pier","buildinggadgets2:gadget_exchanging","aether:flint_and_steel_repairing","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","utilitarian:snad/snad","minecraft:sandstone_stairs","mcwfurnitures:stripped_cherry_modern_chair","minecraft:jukebox","mcwwindows:jungle_curtain_rod","securitycraft:blacklist_module","mcwbiomesoplenty:snowblossom_hedge","mcwfurnitures:stripped_cherry_drawer","mcwfurnitures:cherry_modern_chair","minecraft:coarse_dirt","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","minecraft:red_dye_from_rose_bush","simplylight:illuminant_blue_block_on_toggle","securitycraft:smart_module","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","minecraft:jungle_fence","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","minecraft:iron_ingot_from_smelting_raw_iron","tombstone:blue_marble","mcwfences:mesh_metal_fence","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwfences:quartz_railing_gate","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alchemistry:combiner","mcwdoors:jungle_stable_head_door","mcwtrpdoors:cherry_barrel_trapdoor","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:candle_holders/candle_holder_yellow","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","simplylight:illuminant_slab_from_panel","cfm:gray_grill","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","tombstone:bone_needle","minecraft:stone_brick_wall","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_cherry_covered_desk","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","mcwdoors:cherry_bark_glass_door","cfm:stripped_mangrove_mail_box","mcwtrpdoors:cherry_cottage_trapdoor","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","farmersdelight:iron_knife","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:spruce_boat","handcrafted:jungle_counter","dyenamics:banner/navy_banner","cfm:gray_kitchen_sink","minecraft:paper","minecraft:cherry_wood","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","cfm:dark_oak_kitchen_sink_dark","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","minecraft:copper_ingot_from_blasting_raw_copper","handcrafted:salmon_trophy","minecraft:bucket","supplementaries:speaker_block","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbridges:sandstone_bridge","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwroofs:jungle_planks_attic_roof","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","supplementaries:bellows","cfm:crimson_mail_box","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","simplylight:illuminant_block_on_toggle","minecraft:iron_nugget","twigs:polished_tuff_bricks_from_tuff_stonecutting","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwlights:cherry_ceiling_fan_light","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","mcwpaths:cobbled_deepslate_flagstone_slab","minecraft:diamond_pickaxe","dyenamics:banner/cherenkov_banner","mcwpaths:sandstone_dumble_paving","mcwlights:black_paper_lamp","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","create:tuff_from_stone_types_tuff_stonecutting","domum_ornamentum:blue_cobblestone_extra","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwdoors:cherry_tropical_door","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwpaths:sandstone_strewn_rocky_path","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","utilitarian:redstone_clock","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","securitycraft:harming_module","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","cfm:pink_grill","mcwbridges:iron_bridge_pier","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwroofs:sandstone_roof","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","utilitix:diamond_shears","minecraft:wooden_pickaxe","minecraft:cherry_planks","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwbiomesoplenty:fir_stockade_fence","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsutility:counterplus_module","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_piston","aether:stone_pickaxe_repairing","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwpaths:sandstone_running_bond","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","xnet:connector_yellow","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","aether:leather_chestplate_repairing","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","mcwdoors:cherry_swamp_door","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","aether:diamond_pickaxe_repairing","mcwfurnitures:cherry_covered_desk","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","rftoolsstorage:storage_control_module","mcwdoors:cherry_japanese2_door","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","twigs:mossy_cobblestone_bricks","minecraft:flint_and_steel","mcwdoors:cherry_nether_door","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","mcwfurnitures:stripped_cherry_drawer_counter","xnet:advanced_connector_blue_dye","cfm:spruce_kitchen_sink_light","aether:leather_gloves","mcwroofs:cyan_concrete_roof","xnet:netcable_green_dye","mcwdoors:cherry_barn_door","mcwwindows:cherry_plank_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","xnet:advanced_connector_red_dye","mcwdoors:cherry_modern_door","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","mcwfurnitures:cherry_triple_drawer","mcwroofs:red_concrete_roof","minecraft:yellow_dye_from_sunflower","mcwtrpdoors:cherry_barn_trapdoor","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","rftoolscontrol:card_base","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwlights:wall_lantern","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","supplementaries:stone_tile","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","simplylight:illuminant_yellow_block_toggle","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","immersiveengineering:crafting/component_iron","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","sfm:fancy_to_cable","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","cfm:orange_kitchen_drawer","minecraft:cherry_boat","silentgear:stone_rod","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","minecraft:leather_boots","aquaculture:cooked_fish_fillet_from_campfire","immersiveengineering:crafting/powerpack","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:spruce_mail_box","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwlights:pink_paper_lamp","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","utilitix:hand_bell","xnet:connector_green_dye","mcwfurnitures:cherry_lower_triple_drawer","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","aether:iron_gloves","mcwfurnitures:stripped_jungle_drawer","minecraft:chiseled_stone_bricks","mcwfences:deepslate_brick_pillar_wall","tombstone:white_marble","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","cfm:yellow_grill","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","minecraft:diamond_shovel","sfm:cable","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","tombstone:impregnated_diamond","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","mcwfences:mangrove_highley_gate","mcwfences:birch_horse_fence","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","dyenamics:banner/peach_banner","mcwlights:crimson_ceiling_fan_light","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","cfm:stripped_acacia_kitchen_drawer","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","simplylight:illuminant_black_block_on_toggle","minecraft:copper_ingot_from_smelting_raw_copper","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","securitycraft:universal_key_changer","mcwwindows:cherry_four_window","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","cfm:magenta_cooler","securitycraft:sentry","domum_ornamentum:beige_bricks","mcwroofs:gutter_base_pink","mcwroofs:magenta_concrete_lower_roof","mcwlights:iron_framed_torch","minecraft:deepslate","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","supplementaries:checker","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","minecraft:crossbow","cfm:cyan_cooler","mcwpaths:sandstone_crystal_floor_path","mcwtrpdoors:cherry_beach_trapdoor","mcwbridges:cobblestone_bridge_pier","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwroofs:gutter_base_purple","mcwtrpdoors:cherry_classic_trapdoor","mcwlights:iron_triple_candle_holder","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","xnet:advanced_connector_green_dye","securitycraft:taser","supplementaries:soap","mcwbridges:cobblestone_bridge","mcwdoors:cherry_paper_door","rftoolsutility:redstone_information","cfm:dark_oak_kitchen_drawer","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwpaths:stone_windmill_weave_stairs","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","securitycraft:reinforced_andesite","supplementaries:altimeter","utilitarian:angel_block_rot","mcwroofs:lime_concrete_roof","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","cfm:red_kitchen_drawer","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","mcwpaths:cobblestone_square_paving","dyenamics:bed/spring_green_bed","aether:diamond_gloves","cfm:stripped_dark_oak_mail_box","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","minecraft:mossy_cobblestone_from_moss_block","minecraft:cracked_stone_bricks","mcwdoors:cherry_classic_door","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","utilitarian:utility/cherry_logs_to_pressure_plates","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwfurnitures:stripped_cherry_lower_triple_drawer","croptopia:campfire_molasses","mcwlights:acacia_tiki_torch","supplementaries:candle_holders/candle_holder_white","corail_woodcutter:warped_woodcutter","railcraft:water_tank_siding","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","cfm:jungle_kitchen_sink_dark","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","silentgear:upgrade_base","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:fletching_table","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfurnitures:cherry_table","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfurnitures:stripped_cherry_double_drawer","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:gutter_middle_cyan","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","mcwfurnitures:cherry_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","handcrafted:wolf_trophy","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mythicbotany:rune_holder","dyenamics:banner/wine_banner","allthecompressed:compress/cobbled_deepslate_1x","mcwfurnitures:stripped_cherry_stool_chair","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","croptopia:cheeseburger","minecraft:mangrove_boat","mcwdoors:garage_silver_door","cfm:black_kitchen_sink","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","minecraft:lever","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwroofs:cherry_roof","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","terralith:dropper_alt","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","enderio:basic_fluid_filter","rftoolsutility:counter_module","littlelogistics:locomotive_route","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:dead_hedge","mcwfurnitures:cherry_drawer","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:cherry_end_table","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","cfm:oak_kitchen_sink_dark","mcwbiomesoplenty:magic_highley_gate","utilitarian:utility/cherry_logs_to_trapdoors","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwfurnitures:stripped_cherry_counter","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","mcwfurnitures:stripped_cherry_striped_chair","mcwdoors:cherry_cottage_door","mcwroofs:lime_concrete_top_roof","mcwfences:oak_curved_gate","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwroofs:cherry_lower_roof","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","handcrafted:bear_trophy","cfm:purple_kitchen_sink","mcwwindows:cherry_window2","cfm:black_cooler","mcwdoors:cherry_stable_door","minecraft:jungle_slab","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","silentgear:rough_rod","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","handcrafted:wither_skeleton_trophy","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwroofs:blackstone_upper_lower_roof","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","croptopia:roasted_sunflower_seeds","mcwwindows:sandstone_window","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","supplementaries:pedestal","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","mcwwindows:deepslate_pane_window","mcwdoors:cherry_barn_glass_door","mcwlights:iron_chandelier","rftoolsbase:smartwrench","mcwfences:andesite_grass_topped_wall","mcwpaths:sandstone_flagstone_path","cfm:white_kitchen_drawer","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","securitycraft:floor_trap","xnet:connector_red","mcwdoors:jungle_tropical_door","minecraft:stone","dyenamics:bed/lavender_bed","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","deepresonance:machine_frame","mcwdoors:jungle_glass_door","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","xnet:netcable_red_dye","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","crafting_on_a_stick:crafting_table","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwfurnitures:stripped_cherry_bookshelf","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","mcwfurnitures:cherry_double_drawer_counter","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","rftoolspower:power_core1","minecraft:iron_ingot_from_blasting_raw_iron","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","immersiveengineering:crafting/conveyor_basic","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","mcwroofs:blue_concrete_lower_roof","handcrafted:jungle_fancy_bed","additionallanterns:obsidian_chain","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwlights:cross_lantern","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","railcraft:receiver_circuit","mcwwindows:deepslate_window2","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","mcwfurnitures:cherry_bookshelf_drawer","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","ae2additions:super_cell_housing","cfm:brown_trampoline","supplementaries:stonecutting/stone_tile","mcwwindows:jungle_window2","cfm:gray_cooler","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","cfm:green_kitchen_drawer","minecraft:cooked_beef","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","mcwfurnitures:stripped_cherry_wardrobe","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwlights:warped_tiki_torch","twigs:gravel_bricks","cfm:light_blue_trampoline","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","mcwlights:lava_lamp","toolbelt:pouch","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwwindows:cherry_pane_window","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","mcwdoors:garage_white_door","utilitix:armed_stand","cfm:black_grill","botania:lexicon","mcwdoors:jungle_modern_door","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:gutter_middle_white","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","botania:petal_white","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","xnet:connector_blue","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/coil_lv","cfm:post_box","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:packed_mud","mcwbiomesoplenty:maple_highley_gate","cfm:orange_cooler","mcwbridges:balustrade_mossy_stone_bricks_bridge","aether:skyroot_note_block","mcwfences:railing_quartz_wall","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwlights:bell_wall_lantern"],toBeDisplayed:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","trashcans:energy_trash_can","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","mcwfurnitures:stripped_cherry_modern_desk","handcrafted:spider_trophy","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","croptopia:shaped_beef_stew","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mcwfurnitures:cherry_chair","mcwbiomesoplenty:empyreal_hedge","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","supplementaries:flint_block","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwdoors:jungle_nether_door","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","minecraft:leather_chestplate","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","immersiveengineering:crafting/radiator","mcwfurnitures:cherry_modern_desk","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_green","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","aether:skyroot_fletching_table","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","sliceanddice:slicer","xnet:connector_red_dye","minecolonies:shapetool","supplementaries:bubble_blower","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","supplementaries:crank","mcwroofs:gutter_base_light_gray","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","mcwtrpdoors:jungle_blossom_trapdoor","alltheores:iron_plate","minecraft:mossy_stone_brick_slab","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwdoors:cherry_bamboo_door","mcwtrpdoors:cherry_whispering_trapdoor","aquaculture:birch_fish_mount","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwpaths:cobblestone_dumble_paving","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","dyenamics:aquamarine_concrete_powder","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","handcrafted:jungle_cupboard","tombstone:ankh_of_prayer","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwdoors:cherry_whispering_door","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","utilitarian:utility/charcoal_from_campfire","cfm:acacia_kitchen_drawer","minecraft:orange_dye_from_torchflower","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfurnitures:stripped_cherry_cupboard_counter","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","farmersdelight:cooking/pasta_with_meatballs","mcwfurnitures:cherry_cupboard_counter","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","minecraft:jungle_fence_gate","utilitarian:angel_block","minecraft:red_concrete_powder","simplylight:illuminant_orange_block_on_toggle","dyenamics:spring_green_concrete_powder","mcwpaths:sandstone_running_bond_stairs","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","biomesoplenty:magenta_dye_from_wildflower","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwtrpdoors:cherry_glass_trapdoor","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","mcwdoors:cherry_japanese_door","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwtrpdoors:cherry_barred_trapdoor","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwwindows:jungle_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","croptopia:hamburger","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","minecraft:jungle_door","xnet:connector_routing","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","utilitix:directional_highspeed_rail","mcwdoors:garage_gray_door","mcwbiomesoplenty:magic_picket_fence","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","securitycraft:reinforced_diorite","cfm:green_grill","handcrafted:jungle_drawer","minecraft:stone_brick_stairs","cfm:orange_grill","cfm:brown_grill","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","cfm:jungle_desk","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","cfm:gray_kitchen_drawer","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","utilitarian:utility/cherry_logs_to_stairs","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twigs:silt","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","cfm:mangrove_kitchen_sink_dark","silentgear:diamond_shard","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","mcwroofs:cherry_attic_roof","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","mcwfurnitures:stripped_cherry_table","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:bamboo_raft","cfm:green_kitchen_sink","mcwfurnitures:cherry_counter","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","cfm:acacia_kitchen_sink_dark","cfm:stripped_jungle_chair","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","minecraft:mossy_stone_bricks_from_moss_block","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","forbidden_arcanus:diamond_blacksmith_gavel","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwfurnitures:stripped_cherry_lower_bookshelf_drawer","twigs:rocky_dirt","mcwfences:end_brick_railing_gate","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","simplylight:illuminant_light_gray_block_toggle","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwpaths:sandstone_flagstone_slab","botania:white_shiny_flower","mcwbridges:cherry_bridge_pier","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","securitycraft:reinforced_crimson_fence","minecraft:wooden_shovel","aether:stone_axe_repairing","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwfurnitures:cherry_double_wardrobe","mcwfurnitures:cherry_double_drawer","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwfurnitures:stripped_cherry_bookshelf_drawer","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","cfm:light_blue_cooler","railcraft:signal_circuit","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","sophisticatedstorage:oak_chest_from_vanilla_chest","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","cfm:pink_cooler","railcraft:signal_block_surveyor","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","mcwpaths:stone_windmill_weave","cfm:red_grill","mcwfences:railing_blackstone_wall","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","mcwpaths:sandstone_windmill_weave_stairs","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","cfm:light_blue_grill","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","cfm:light_blue_kitchen_drawer","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwtrpdoors:cherry_tropical_trapdoor","securitycraft:reinforced_mangrove_fence","cfm:stripped_warped_mail_box","additionallanterns:normal_sandstone_chain","mcwpaths:sandstone_windmill_weave","cfm:light_gray_trampoline","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","cfm:purple_kitchen_drawer","botania:flower_bag","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfurnitures:cherry_striped_chair","tombstone:green_marble","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwfurnitures:cherry_large_drawer","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:red_maple_hedge","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","securitycraft:reinforced_redstone_lamp","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","mcwroofs:gutter_middle_blue","mcwdoors:cherry_glass_door","sfm:manager","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","cfm:purple_grill","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","littlelogistics:tug_route","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","minecraft:purple_concrete_powder","minecraft:sugar_from_sugar_cane","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","mcwlights:oak_ceiling_fan_light","mcwfurnitures:stripped_cherry_modern_wardrobe","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","xnet:advanced_connector_yellow","cfm:stripped_spruce_kitchen_drawer","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","cfm:orange_trampoline","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","minecraft:wooden_hoe","cfm:light_blue_kitchen_sink","minecraft:cooked_beef_from_campfire_cooking","createoreexcavation:diamond_drill","mcwbiomesoplenty:maple_picket_fence","mcwfurnitures:cherry_glass_table","mcwfurnitures:stripped_cherry_desk","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwdoors:cherry_four_panel_door","mcwroofs:gutter_base_green","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","littlelogistics:vessel_charger","mcwfurnitures:jungle_coffee_table","utilitix:minecart_tinkerer","mcwlights:soul_dark_oak_tiki_torch","mcwfurnitures:cherry_desk","mcwbridges:stone_bridge_pier","naturescompass:natures_compass","railcraft:radio_circuit","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","minecraft:wooden_axe","mcwpaths:cobbled_deepslate_clover_paving","mcwfences:panelled_metal_fence_gate","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:cherry_coffee_table","cfm:birch_kitchen_sink_dark","supplementaries:daub_brace","mcwtrpdoors:cherry_four_panel_trapdoor","simplylight:illuminant_light_blue_block_on_dyed","simplylight:lamp_post","mcwfences:spruce_horse_fence","minecraft:hopper","mcwfurnitures:stripped_cherry_double_wardrobe","croptopia:beef_jerky","mcwwindows:jungle_plank_four_window","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","silentgear:sinew_fiber","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","minecraft:iron_bars","corail_woodcutter:mangrove_woodcutter","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","cfm:oak_kitchen_drawer","supplementaries:flags/flag_magenta","minecraft:iron_helmet","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","securitycraft:alarm","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","quark:tweaks/crafting/utility/tools/stone_pickaxe","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","mcwfurnitures:stripped_cherry_double_drawer_counter","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwpaths:sandstone_honeycomb_paving","mcwfurnitures:stripped_cherry_coffee_table","mcwdoors:print_waffle","twigs:torchflower_paper_lantern","utilitix:crude_furnace","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwfurnitures:cherry_stool_chair","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwpaths:cobbled_deepslate_flagstone_path","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","mcwfences:crimson_picket_fence","mcwfurnitures:cherry_wardrobe","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","mcwroofs:cobblestone_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","sfm:water_tank","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwfences:dark_oak_picket_fence","alchemistry:compactor","travelersbackpack:sandstone","cfm:jungle_upgraded_fence","minecraft:dropper","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","minecraft:lime_dye_from_smelting","mcwfurnitures:cherry_bookshelf","mcwfences:bamboo_highley_gate","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","bigreactors:reactor/basic/passivetap_fe","mcwfurnitures:stripped_cherry_bookshelf_cupboard","securitycraft:reinforced_dispenser","mcwdoors:cherry_stable_head_door","mcwroofs:jungle_roof","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwfences:jungle_curved_gate","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_spruce_fence_gate","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwtrpdoors:cherry_mystic_trapdoor","mcwtrpdoors:metal_trapdoor","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","supplementaries:daub_frame","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","mcwpaths:sandstone_running_bond_slab","minecraft:crafting_table","twilightforest:time_boat","minecraft:sandstone_wall","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwbridges:balustrade_sandstone_bridge","utilitarian:utility/cherry_logs_to_boats","securitycraft:briefcase","mcwfences:spruce_wired_fence","cfm:white_grill","minecraft:iron_nugget_from_smelting","mcwlights:iron_low_candle_holder","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","mcwroofs:sandstone_lower_roof","travelersbackpack:hose_nozzle","securitycraft:claymore","handcrafted:jungle_dining_bench","cfm:red_cooler","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","create:crafting/logistics/pulse_extender","twigs:polished_tuff_stonecutting","supplementaries:lock_block","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfurnitures:stripped_cherry_large_drawer","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwpaths:sandstone_basket_weave_paving","mcwdoors:cherry_mystic_door","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwroofs:cherry_steep_roof","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwfurnitures:stripped_cherry_end_table","mcwroofs:orange_concrete_upper_lower_roof","mcwtrpdoors:cherry_paper_trapdoor","mcwwindows:cherry_plank_window2","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","quark:tweaks/crafting/utility/tools/stone_axe","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","cfm:acacia_mail_box","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:raw_iron_block","mcwroofs:cherry_upper_lower_roof","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","minecraft:redstone_block","minecraft:shears","croptopia:fruit_smoothie","supplementaries:flags/flag_blue","mcwtrpdoors:metal_warning_trapdoor","mcwroofs:jungle_planks_top_roof","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","mcwfurnitures:jungle_striped_chair","xnet:advanced_connector_yellow_dye","allthecompressed:compress/cobblestone_1x","twigs:mossy_bricks_from_moss_block","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","aquaculture:bobber","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","mcwpaths:cobblestone_honeycomb_paving","utilitarian:utility/cherry_logs_to_doors","simplylight:edge_light_top","mcwfences:granite_pillar_wall","mcwfurnitures:cherry_bookshelf_cupboard","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwroofs:cherry_upper_steep_roof","simplylight:illuminant_brown_block_on_dyed","securitycraft:reinforced_warped_fence","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","minecraft:item_frame","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","minecraft:arrow","mcwroofs:gutter_middle_light_blue","supplementaries:candle_holders/candle_holder_gray","mcwroofs:light_gray_concrete_steep_roof","supplementaries:turn_table","supplementaries:flags/flag_pink","mcwfurnitures:stripped_cherry_glass_table","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwroofs:cherry_top_roof","mcwroofs:sandstone_attic_roof","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfurnitures:cherry_modern_wardrobe","mcwfences:modern_stone_brick_wall","mcwfurnitures:stripped_cherry_chair","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","utilitarian:utility/cherry_logs_to_slabs","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","biomesoplenty:white_dye_from_tall_white_lavender","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","mcwfurnitures:stripped_cherry_triple_drawer","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","mcwpaths:stone_windmill_weave_path","mcwfurnitures:cherry_drawer_counter","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","mcwdoors:cherry_beach_door","mcwroofs:orange_concrete_steep_roof","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","securitycraft:reinforced_birch_fence_gate","mcwroofs:gutter_base_light_blue","minecraft:diamond_block","mcwroofs:deepslate_roof","bigreactors:turbine/reinforced/passivefluidport_forge","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","cfm:brown_cooler","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","rftoolsutility:inventoryplus_module","mcwdoors:cherry_western_door","mcwfurnitures:jungle_end_table","undergarden:shard_torch","mcwfences:bamboo_picket_fence","cfm:green_cooler","handcrafted:goat_trophy","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","cfm:stripped_crimson_kitchen_sink_light","simplylight:illuminant_lime_block_on_dyed","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","cfm:dark_oak_kitchen_sink_light","mcwbridges:jungle_bridge_pier","buildinggadgets2:gadget_exchanging","aether:flint_and_steel_repairing","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","utilitarian:snad/snad","minecraft:sandstone_stairs","mcwfurnitures:stripped_cherry_modern_chair","minecraft:jukebox","mcwwindows:jungle_curtain_rod","securitycraft:blacklist_module","mcwbiomesoplenty:snowblossom_hedge","mcwfurnitures:stripped_cherry_drawer","mcwfurnitures:cherry_modern_chair","minecraft:coarse_dirt","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","minecraft:red_dye_from_rose_bush","simplylight:illuminant_blue_block_on_toggle","securitycraft:smart_module","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","minecraft:jungle_fence","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","minecraft:iron_ingot_from_smelting_raw_iron","tombstone:blue_marble","mcwfences:mesh_metal_fence","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwfences:quartz_railing_gate","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alchemistry:combiner","mcwdoors:jungle_stable_head_door","mcwtrpdoors:cherry_barrel_trapdoor","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:candle_holders/candle_holder_yellow","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","simplylight:illuminant_slab_from_panel","cfm:gray_grill","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","tombstone:bone_needle","minecraft:stone_brick_wall","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_cherry_covered_desk","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","cfm:cyan_grill","mcwdoors:cherry_bark_glass_door","cfm:stripped_mangrove_mail_box","mcwtrpdoors:cherry_cottage_trapdoor","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","farmersdelight:iron_knife","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:spruce_boat","handcrafted:jungle_counter","dyenamics:banner/navy_banner","cfm:gray_kitchen_sink","minecraft:paper","minecraft:cherry_wood","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","cfm:dark_oak_kitchen_sink_dark","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","minecraft:copper_ingot_from_blasting_raw_copper","handcrafted:salmon_trophy","minecraft:bucket","supplementaries:speaker_block","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbridges:sandstone_bridge","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwroofs:jungle_planks_attic_roof","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","supplementaries:bellows","cfm:crimson_mail_box","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","simplylight:illuminant_block_on_toggle","minecraft:iron_nugget","twigs:polished_tuff_bricks_from_tuff_stonecutting","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwlights:cherry_ceiling_fan_light","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","mcwpaths:cobbled_deepslate_flagstone_slab","minecraft:diamond_pickaxe","dyenamics:banner/cherenkov_banner","mcwpaths:sandstone_dumble_paving","mcwlights:black_paper_lamp","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","create:tuff_from_stone_types_tuff_stonecutting","domum_ornamentum:blue_cobblestone_extra","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwdoors:cherry_tropical_door","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwpaths:sandstone_strewn_rocky_path","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","utilitarian:redstone_clock","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","securitycraft:harming_module","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","cfm:pink_grill","mcwbridges:iron_bridge_pier","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwroofs:sandstone_roof","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","utilitix:diamond_shears","minecraft:wooden_pickaxe","minecraft:cherry_planks","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwbiomesoplenty:fir_stockade_fence","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsutility:counterplus_module","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_piston","aether:stone_pickaxe_repairing","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwpaths:sandstone_running_bond","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","xnet:connector_yellow","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","aether:leather_chestplate_repairing","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwroofs:roofing_hammer","mcwdoors:cherry_swamp_door","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","aether:diamond_pickaxe_repairing","mcwfurnitures:cherry_covered_desk","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","rftoolsstorage:storage_control_module","mcwdoors:cherry_japanese2_door","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","twigs:mossy_cobblestone_bricks","minecraft:flint_and_steel","mcwdoors:cherry_nether_door","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","mcwfurnitures:stripped_cherry_drawer_counter","xnet:advanced_connector_blue_dye","cfm:spruce_kitchen_sink_light","aether:leather_gloves","mcwroofs:cyan_concrete_roof","xnet:netcable_green_dye","mcwdoors:cherry_barn_door","mcwwindows:cherry_plank_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","xnet:advanced_connector_red_dye","mcwdoors:cherry_modern_door","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","mcwfurnitures:cherry_triple_drawer","mcwroofs:red_concrete_roof","minecraft:yellow_dye_from_sunflower","mcwtrpdoors:cherry_barn_trapdoor","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","rftoolscontrol:card_base","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwlights:wall_lantern","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","supplementaries:stone_tile","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","immersiveengineering:crafting/stick_iron","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","simplylight:illuminant_yellow_block_toggle","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","immersiveengineering:crafting/component_iron","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","sfm:fancy_to_cable","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","cfm:orange_kitchen_drawer","minecraft:cherry_boat","silentgear:stone_rod","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","minecraft:leather_boots","aquaculture:cooked_fish_fillet_from_campfire","immersiveengineering:crafting/powerpack","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:spruce_mail_box","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwlights:pink_paper_lamp","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","utilitix:hand_bell","xnet:connector_green_dye","mcwfurnitures:cherry_lower_triple_drawer","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","aether:iron_gloves","mcwfurnitures:stripped_jungle_drawer","minecraft:chiseled_stone_bricks","mcwfences:deepslate_brick_pillar_wall","tombstone:white_marble","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","cfm:yellow_grill","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","minecraft:diamond_shovel","sfm:cable","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","tombstone:impregnated_diamond","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","create:jungle_window","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","bigreactors:reprocessor/casing","mcwfences:mangrove_highley_gate","mcwfences:birch_horse_fence","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","dyenamics:banner/peach_banner","mcwlights:crimson_ceiling_fan_light","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","travelersbackpack:standard","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","cfm:stripped_acacia_kitchen_drawer","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","simplylight:illuminant_black_block_on_toggle","minecraft:copper_ingot_from_smelting_raw_copper","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","securitycraft:universal_key_changer","mcwwindows:cherry_four_window","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","cfm:magenta_cooler","securitycraft:sentry","domum_ornamentum:beige_bricks","mcwroofs:gutter_base_pink","mcwroofs:magenta_concrete_lower_roof","mcwlights:iron_framed_torch","minecraft:deepslate","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","supplementaries:checker","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","minecraft:crossbow","cfm:cyan_cooler","mcwpaths:sandstone_crystal_floor_path","mcwtrpdoors:cherry_beach_trapdoor","mcwbridges:cobblestone_bridge_pier","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwroofs:gutter_base_purple","mcwtrpdoors:cherry_classic_trapdoor","mcwlights:iron_triple_candle_holder","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","xnet:advanced_connector_green_dye","securitycraft:taser","supplementaries:soap","mcwbridges:cobblestone_bridge","mcwdoors:cherry_paper_door","rftoolsutility:redstone_information","cfm:dark_oak_kitchen_drawer","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwpaths:stone_windmill_weave_stairs","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","securitycraft:reinforced_andesite","supplementaries:altimeter","utilitarian:angel_block_rot","mcwroofs:lime_concrete_roof","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","cfm:red_kitchen_drawer","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","mcwpaths:cobblestone_square_paving","dyenamics:bed/spring_green_bed","aether:diamond_gloves","cfm:stripped_dark_oak_mail_box","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","minecraft:mossy_cobblestone_from_moss_block","minecraft:cracked_stone_bricks","mcwdoors:cherry_classic_door","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","utilitarian:utility/cherry_logs_to_pressure_plates","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwfurnitures:stripped_cherry_lower_triple_drawer","croptopia:campfire_molasses","mcwlights:acacia_tiki_torch","supplementaries:candle_holders/candle_holder_white","corail_woodcutter:warped_woodcutter","railcraft:water_tank_siding","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","cfm:jungle_kitchen_sink_dark","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","silentgear:upgrade_base","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:fletching_table","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfurnitures:cherry_table","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfurnitures:stripped_cherry_double_drawer","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:gutter_middle_cyan","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","mcwfurnitures:cherry_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","handcrafted:wolf_trophy","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mythicbotany:rune_holder","dyenamics:banner/wine_banner","allthecompressed:compress/cobbled_deepslate_1x","mcwfurnitures:stripped_cherry_stool_chair","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","croptopia:cheeseburger","minecraft:mangrove_boat","mcwdoors:garage_silver_door","cfm:black_kitchen_sink","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","minecraft:lever","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwroofs:cherry_roof","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","terralith:dropper_alt","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","enderio:basic_fluid_filter","rftoolsutility:counter_module","littlelogistics:locomotive_route","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:dead_hedge","mcwfurnitures:cherry_drawer","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:cyan_trampoline","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:cherry_end_table","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","cfm:oak_kitchen_sink_dark","mcwbiomesoplenty:magic_highley_gate","utilitarian:utility/cherry_logs_to_trapdoors","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwfurnitures:stripped_cherry_counter","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","mcwfurnitures:stripped_cherry_striped_chair","mcwdoors:cherry_cottage_door","mcwroofs:lime_concrete_top_roof","mcwfences:oak_curved_gate","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwroofs:cherry_lower_roof","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","handcrafted:bear_trophy","cfm:purple_kitchen_sink","mcwwindows:cherry_window2","cfm:black_cooler","mcwdoors:cherry_stable_door","minecraft:jungle_slab","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","silentgear:rough_rod","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","handcrafted:wither_skeleton_trophy","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwroofs:blackstone_upper_lower_roof","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","croptopia:roasted_sunflower_seeds","mcwwindows:sandstone_window","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","supplementaries:pedestal","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","mcwwindows:deepslate_pane_window","mcwdoors:cherry_barn_glass_door","mcwlights:iron_chandelier","rftoolsbase:smartwrench","mcwfences:andesite_grass_topped_wall","mcwpaths:sandstone_flagstone_path","cfm:white_kitchen_drawer","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","securitycraft:floor_trap","xnet:connector_red","mcwdoors:jungle_tropical_door","minecraft:stone","dyenamics:bed/lavender_bed","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","deepresonance:machine_frame","mcwdoors:jungle_glass_door","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","xnet:netcable_red_dye","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","crafting_on_a_stick:crafting_table","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwfurnitures:stripped_cherry_bookshelf","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","mcwfurnitures:cherry_double_drawer_counter","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","rftoolspower:power_core1","minecraft:iron_ingot_from_blasting_raw_iron","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","immersiveengineering:crafting/conveyor_basic","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","mcwroofs:blue_concrete_lower_roof","handcrafted:jungle_fancy_bed","additionallanterns:obsidian_chain","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwlights:cross_lantern","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","railcraft:receiver_circuit","mcwwindows:deepslate_window2","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","mcwfurnitures:cherry_bookshelf_drawer","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","ae2additions:super_cell_housing","cfm:brown_trampoline","supplementaries:stonecutting/stone_tile","mcwwindows:jungle_window2","cfm:gray_cooler","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","cfm:green_kitchen_drawer","minecraft:cooked_beef","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","mcwfurnitures:stripped_cherry_wardrobe","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwlights:warped_tiki_torch","twigs:gravel_bricks","cfm:light_blue_trampoline","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","mcwlights:lava_lamp","toolbelt:pouch","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwwindows:cherry_pane_window","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","mcwdoors:garage_white_door","utilitix:armed_stand","cfm:black_grill","botania:lexicon","mcwdoors:jungle_modern_door","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:gutter_middle_white","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","botania:petal_white","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","xnet:connector_blue","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/coil_lv","cfm:post_box","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:packed_mud","mcwbiomesoplenty:maple_highley_gate","cfm:orange_cooler","mcwbridges:balustrade_mossy_stone_bricks_bridge","aether:skyroot_note_block","mcwfences:railing_quartz_wall","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwlights:bell_wall_lantern"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:1829,warning_level:0}}