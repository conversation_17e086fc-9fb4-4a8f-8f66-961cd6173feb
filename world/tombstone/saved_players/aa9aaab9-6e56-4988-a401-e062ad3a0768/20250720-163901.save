{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:291,Id:8,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:jump_boost"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:362,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:362,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],Air:300s,Attributes:[{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"attributeslib:prot_pierce"},{Base:0.0d,Name:"minecraft:generic.attack_knockback"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.05d,Name:"attributeslib:crit_chance"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"attributeslib:prot_shred"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"attributeslib:armor_pierce"},{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:1.0d,Name:"attributeslib:healing_received"},{Base:0.0d,Name:"attributeslib:overheal"},{Base:1.5d,Name:"attributeslib:crit_damage"},{Base:1.0d,Name:"attributeslib:arrow_velocity"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"attributeslib:cold_damage"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"attributeslib:arrow_damage"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:0.0d,Name:"attributeslib:armor_shred"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"irons_spellbooks:cast_time_reduction"},{Base:20.0d,Modifiers:[{Amount:4.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"attributeslib:current_hp_damage"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"attributeslib:ghost_health"},{Base:0.0d,Name:"attributeslib:fire_damage"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"irons_spellbooks:ender_spell_power"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Name:"eidolon:magic_power"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"attributeslib:life_steal"},{Base:0.0d,Name:"attributeslib:dodge_chance"},{Base:0.10000000149011612d,Modifiers:[{Amount:0.1d,Name:"fox_movement_speed",Operation:2,UUID:[I;555098007,-**********,-**********,**********]}],Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:375.0d,glyph:0,max:375},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"blue_skies:dusk_arc",tag:{ArcLevel:1}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:1b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:1b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:1b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;**********,234373974,-**********,-151215789],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:obsidian_skull"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"irons_spellbooks:concentration_amulet"}],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[{Count:1b,Slot:0,id:"irons_spellbooks:frostward_ring"},{Count:1b,Slot:1,id:"irons_spellbooks:cast_time_ring"},{Count:1b,Slot:2,id:"irons_spellbooks:cast_time_ring"},{Count:1b,Slot:3,id:"irons_spellbooks:cast_time_ring"}],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"travelersbackpack:fox",tag:{Color:-*********,Inventory:{Items:[{Count:1b,Slot:0,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:4,id:"constructionwand:diamond_wand",tag:{Damage:445,wand_options:{}}},{Count:1b,Slot:5,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:6,id:"botania:king_key",tag:{charging:0b,soulbindUUID:"aa9aaab9-6e56-4988-a401-e062ad3a0768",weaponsSpawned:0}},{Count:1b,Slot:7,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:8,id:"botania:lexicon",tag:{"botania:elven_unlock":1b}},{Count:1b,Slot:9,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:10,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:11,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:12,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:13,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:14,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:15,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:16,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:17,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:18,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:19,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:20,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:21,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:22,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:23,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:24,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:25,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:26,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],Size:27},RenderInfo:{},SleepingBagColor:5,StorageSlots:27,Tier:0,ToolSlots:2,UpgradeSlots:2}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"cataclysm:blazing_grips"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"ae2wtlib:wireless_universal_terminal",tag:{accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},crafting:1b,craftingGrid:[{Count:1b,Slot:0,id:"forbidden_arcanus:darkstone"},{Count:1b,Slot:1,id:"bloodmagic:blankslate"},{Count:1b,Slot:2,id:"forbidden_arcanus:darkstone"},{Count:1b,Slot:3,id:"forbidden_arcanus:darkstone"},{Count:1b,Slot:4,id:"bloodmagic:weakbloodorb"},{Count:1b,Slot:5,id:"forbidden_arcanus:darkstone"},{Count:1b,Slot:6,id:"forbidden_arcanus:darkstone"},{Count:1b,Slot:7,id:"forbidden_arcanus:darkstone"},{Count:1b,Slot:8,id:"forbidden_arcanus:darkstone"}],currentTerminal:"crafting",internalCurrentPower:3200000.0d,internalMaxPower:3200000.0d,pattern_access:1b}}],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"allthewizardgear:unobtainium_spell_book",tag:{ISBUpgrades:[{id:"irons_spellbooks:ender_power",slot:"spellbook",upgrades:3}],ISB_Spells:{data:[{id:"irons_spellbooks:black_hole",index:0,level:6,locked:0b},{id:"irons_spellbooks:sculk_tentacles",index:1,level:4,locked:0b},{id:"irons_spellbooks:eldritch_blast",index:2,level:5,locked:0b},{id:"irons_spellbooks:fireball",index:3,level:3,locked:0b},{id:"irons_spellbooks:thunderstorm",index:4,level:8,locked:0b},{id:"irons_spellbooks:charge",index:5,level:3,locked:0b},{id:"irons_spellbooks:heat_surge",index:6,level:8,locked:0b},{id:"irons_spellbooks:starfall",index:7,level:9,locked:0b},{id:"irons_spellbooks:oakskin",index:8,level:8,locked:0b},{id:"irons_spellbooks:summon_vex",index:9,level:4,locked:0b},{id:"irons_spellbooks:acupuncture",index:10,level:8,locked:0b},{id:"irons_spellbooks:echoing_strikes",index:11,level:4,locked:0b},{id:"irons_spellbooks:heartstop",index:12,level:10,locked:0b},{id:"irons_spellbooks:haste",index:13,level:3,locked:0b},{id:"irons_spellbooks:raise_dead",index:14,level:6,locked:0b}],maxSpells:15,mustEquip:1b,spellWheel:1b}}}],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:57,wirelessNetwork:10},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"spellbook",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,learnedSpells:["irons_spellbooks:sculk_tentacles","irons_spellbooks:sonic_boom","irons_spellbooks:eldritch_blast","irons_spellbooks:telekinesis"],mana:2030,spellSelection:{index:0,lastIndex:14,lastSlot:"spellbook",slot:"spellbook"}},"l2library:conditionals":{data:{},tickSinceDeath:64889},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:259},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"bloodmagic:blood_rune_blank",ItemStack:{Count:1b,id:"bloodmagic:blankrune"}}]},"rftoolsutility:properties":{allowFlying:0b,buffTicks:92,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:cooked_beef","artifacts:eternal_steak","occultism:datura","minecraft:cooked_chicken","minecraft:baked_potato","minecraft:bread"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:300s,knowledge:25,perks:[{id:3s,level:2b}]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:3b,MythicBotanyPlayerInfo:{},PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:overworld",FromPos:-5222680522499L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;-1263220910,-*********,-1362548706,*********]},{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-*********,-1310244532,-1391404219,*********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b}},WaystonesData:{Waystones:["2ed386e9-5730-4aff-816d-209261d26e15","4f5f98b0-b346-45b6-83f1-59fad95c9199","70e61f07-a350-437a-ae01-15e3ef9a1bab","2968051a-1dc8-457d-97c2-8af1ed99da77","d1811b83-cc8c-4821-80c6-3e5ce576ae62","e6353391-3a89-405f-94a8-c274c384abcd","42d85d81-dfae-42b5-ab86-9ebce283d4c6","2fa16f9f-0adb-438b-af4e-01e2e395f48a","92662b7f-21bf-4acf-ac24-7d7c296f7f6d","050381f4-135a-46ce-bced-6ac10daaad20","0266a389-60af-4e6d-b7d4-eb665d5e9ce1","99deccfe-3919-4212-bb6d-6a5a479f0181","88320f8d-ea59-4cc6-896d-79cc52d4aa80"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-58,tb_last_ground_location_y:257,tb_last_ground_location_z:-90,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},"aae$downkey":0b,"aae$nokey":0b,"aae$upkey":0b,"apoth.affix_cooldown.apotheosis:armor/mob_effect/nimble":11437087L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/revitalizing":11437087L,"apoth.affix_cooldown.apotheosis:berserkers_fury":11437087L,"apoth.affix_cooldown.apotheosis:sword/mob_effect/sophisticated":6425408L,apoth_reforge_seed:**********,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedBackpackSettings:{},sophisticatedStorageSettings:{}},Health:58.65f,HurtByTimestamp:54885,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"tiab:time_in_a_bottle",tag:{storedTime:72840,totalAccumulatedTime:1756020}},{Count:1b,Slot:1b,id:"bloodmagic:soulsword",tag:{Damage:4,activated:1b,demonWillType:"default",soulSwordActiveDrain:0.1d,soulSwordAttackSpeed:-2.4d,soulSwordDamage:6.5d,soulSwordDrop:4.0d,soulSwordHealth:0.0d,soulSwordSpeed:0.0d,soulSwordStaticDrop:1.0d}},{Count:8b,Slot:2b,id:"mysticalagriculture:inferium_essence"},{Count:1b,Slot:5b,id:"occultism:infused_pickaxe",tag:{Damage:5,Enchantments:[{id:"minecraft:fortune",lvl:6s},{id:"evilcraft:unusing",lvl:1s},{id:"minecraft:mending",lvl:1s},{id:"minecraft:unbreaking",lvl:8s}],affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":0.96745163f,"apotheosis:breaker/attribute/experienced":0.19324034f,"apotheosis:breaker/attribute/lengthy":0.2160542f,"apotheosis:breaker/attribute/lucky":0.13214684f,"apotheosis:breaker/special/enlightened":0.9889457f,"apotheosis:breaker/special/omnetic":0.5221134f,"apotheosis:durable":0.29999998f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/attribute/lengthy"},"",{"translate":"affix.apotheosis:breaker/attribute/experienced.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;837272175,-**********,-**********,776587600]]},spiritName:"Narmaracfyr"}},{Count:1b,Slot:7b,id:"allthemodium:alloy_sword",tag:{affix_data:{affixes:{"apotheosis:durable":0.73f,"apotheosis:socket":2.0f,"apotheosis:sword/attribute/elongated":0.85489064f,"apotheosis:sword/attribute/glacial":0.14216435f,"apotheosis:sword/attribute/intricate":0.8090417f,"apotheosis:sword/attribute/lacerating":0.98741335f,"apotheosis:sword/attribute/piercing":0.95980805f,"apotheosis:sword/mob_effect/sophisticated":0.33794326f,"apotheosis:sword/mob_effect/weakening":0.6003846f,"apotheosis:sword/special/festive":0.05302018f,"apotheosis:sword/special/thunderstruck":0.014283717f},name:'{"color":"rainbow","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/attribute/intricate"},"",{"translate":"affix.apotheosis:sword/attribute/glacial.suffix"}]}',rarity:"ancient",uuids:[[I;-2031333617,480725750,-1840465454,888090631]]}}},{Count:13b,Slot:8b,id:"bloodmagic:soulsnare"},{Count:1b,Slot:15b,id:"bloodmagic:basemonstersoul",tag:{souls:4.0d}},{Count:1b,Slot:25b,id:"bloodmagic:basemonstersoul",tag:{souls:4.400000000000002d}},{Count:1b,Slot:33b,id:"sophisticatedbackpacks:diamond_backpack",tag:{contentsUuid:[I;-330093708,-1481618294,-1730118871,737190324],inventorySlots:108,renderInfo:{upgradeItems:[{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_tier_3"},{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_tier_3"},{Count:1b,id:"sophisticatedbackpacks:advanced_feeding_upgrade"},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,2019182445,-1688876346,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,2019182445,-1688876346,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}]},upgradeSlots:5}},{Count:1b,Slot:34b,id:"bloodmagic:soulgemlesser",tag:{souls:44.0d}},{Count:1b,Slot:35b,id:"bloodmagic:basemonstersoul",tag:{souls:4.0d}},{Count:1b,Slot:100b,id:"allthewizardgear:unobtainium_mage_boots",tag:{Enchantments:[{id:"minecraft:projectile_protection",lvl:7s},{id:"enderio:repellent",lvl:7s},{id:"minecraft:feather_falling",lvl:6s},{id:"tombstone:soulbound",lvl:1s},{id:"minecraft:depth_strider",lvl:4s}],ISBUpgrades:[{id:"irons_spellbooks:ender_power",slot:"feet",upgrades:3}],ISB_Spells:{data:[],maxSpells:0,mustEquip:1b,spellWheel:0b},affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.7587331f,"apotheosis:armor/attribute/windswept":0.32378644f,"apotheosis:armor/dmg_reduction/feathery":0.31280112f,"apotheosis:armor/mob_effect/nimble":0.2952571f,"apotheosis:durable":0.38f,"irons_spellbooks:armor/attribute/mana":0.05286795f,"irons_spellbooks:armor/attribute/spell_resist":0.33677256f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",{"translate":"affix.apotheosis:armor/attribute/fortunate.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;-1328182020,-1612624372,-1807771077,1171321938]]}}},{Count:1b,Slot:101b,id:"allthewizardgear:unobtainium_mage_leggings",tag:{Enchantments:[{id:"minecraft:fire_protection",lvl:7s},{id:"ensorcellation:soulbound",lvl:1s},{id:"enderio:repellent",lvl:8s},{id:"apotheosis:rebounding",lvl:4s},{id:"ars_nouveau:mana_boost",lvl:5s}],ISBUpgrades:[{id:"irons_spellbooks:ender_power",slot:"legs",upgrades:3}],ISB_Spells:{data:[],maxSpells:0,mustEquip:1b,spellWheel:0b},affix_data:{affixes:{"apotheosis:armor/attribute/ironforged":0.3862238f,"apotheosis:armor/attribute/steel_touched":0.9016755f,"apotheosis:armor/dmg_reduction/blast_forged":0.66981876f,"apotheosis:armor/dmg_reduction/blockading":0.30002147f,"apotheosis:durable":0.29f,"irons_spellbooks:armor/attribute/cooldown":0.05416888f,"irons_spellbooks:armor/attribute/spell_power":0.33049476f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/dmg_reduction/blast_forged"},"",{"translate":"affix.apotheosis:armor/dmg_reduction/blockading.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;1219108245,-1738125419,-1732603587,713681548]]}}},{Count:1b,Slot:102b,id:"allthewizardgear:unobtainium_mage_chestplate",tag:{Enchantments:[{id:"tombstone:soulbound",lvl:1s},{id:"ensorcellation:vitality",lvl:6s},{id:"minecraft:projectile_protection",lvl:8s},{id:"ars_nouveau:mana_regen",lvl:6s},{id:"apotheosis:berserkers_fury",lvl:1s},{id:"minecraft:thorns",lvl:4s},{id:"ensorcellation:reach",lvl:6s}],ISBUpgrades:[{id:"irons_spellbooks:ender_power",slot:"chest",upgrades:3}],ISB_Spells:{data:[],maxSpells:0,mustEquip:1b,spellWheel:1b},affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.034717143f,"apotheosis:armor/attribute/spiritual":0.27845323f,"apotheosis:armor/dmg_reduction/dwarven":0.43716007f,"apotheosis:armor/mob_effect/revitalizing":0.19935262f,"apotheosis:durable":0.37f,"irons_spellbooks:armor/attribute/spell_power":0.30776304f,"irons_spellbooks:armor/attribute/spell_resist":0.50929666f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist"},"",{"translate":"affix.apotheosis:armor/mob_effect/revitalizing.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-1056421595,-1677639540,-1521630380,1752109299]]}}},{Count:1b,Slot:103b,id:"allthewizardgear:unobtainium_mage_helmet",tag:{Enchantments:[{id:"ensorcellation:air_affinity",lvl:1s},{id:"tombstone:spectral_conjurer",lvl:11s},{id:"minecraft:protection",lvl:6s},{id:"ars_nouveau:mana_boost",lvl:6s},{id:"ensorcellation:soulbound",lvl:1s},{id:"minecraft:projectile_protection",lvl:8s},{id:"minecraft:respiration",lvl:6s}],ISBUpgrades:[{id:"irons_spellbooks:ender_power",slot:"head",upgrades:3}],ISB_Spells:{data:[],maxSpells:0,mustEquip:1b,spellWheel:0b},affix_data:{affixes:{"apotheosis:armor/attribute/ironforged":0.76783496f,"apotheosis:armor/dmg_reduction/runed":0.5725159f,"apotheosis:armor/mob_effect/blinding":0.20261812f,"apotheosis:durable":0.37f,"eidolon:wand/attribute/magic_power":0.75887185f,"irons_spellbooks:armor/attribute/mana":0.4469341f,"irons_spellbooks:armor/attribute/spell_power":0.33516437f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:ancient"},gem:"apotheosis:overworld/royalty",uuids:[[I;-2143410175,1809926663,-1259495569,-722620071],[I;433715062,1801404481,-1522649545,359579086],[I;-556583142,2042315067,-1159655531,-139307841]]}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,UUID:[I;255950004,2019182445,-1688876346,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/ironforged"},"",{"translate":"affix.irons_spellbooks:armor/attribute/mana.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;-556214173,1815234359,-1232054232,-127539406]]}}}],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:mining",pos:[I;-58,257,-90]},Motion:[0.0d,0.0d,0.0d],OnGround:0b,PortalCooldown:0,Pos:[-40.371621662244536d,255.50357011250824d,-81.30085180162065d],Railways_DataVersion:2,Rotation:[-52.962677f,47.550056f],Score:99165,SelectedItemSlot:3,SleepTimer:0s,SpawnAngle:-67.63269f,SpawnDimension:"allthemodium:mining",SpawnForced:0b,SpawnX:-81,SpawnY:253,SpawnZ:-103,Spigot.ticksLived:64888,UUID:[I;-1432704327,1851148680,-1543380894,-1388705944],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-13194139856643L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:58,XpP:0.90934026f,XpSeed:44065635,XpTotal:8264,abilities:{flySpeed:0.05f,flying:1b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752063538166L,keepLevel:0b,lastKnownName:"Chizuihaodedami",lastPlayed:1753000741916L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:1.882998f,foodLevel:20,foodSaturationLevel:10.8f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["botania:star_sword","mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","comforts:hammock_to_cyan","cfm:yellow_cooler","croptopia:mead","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwbiomesoplenty:jacaranda_window2","minecraft:stonecutter","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","alltheores:gold_dust_from_hammer_crushing","minecraft:bone_block","mcwbiomesoplenty:willow_waffle_door","mcwpaths:cobbled_deepslate_crystal_floor_path","croptopia:shaped_beef_stew","minecraft:melon","pneumaticcraft:wall_lamp_white","mcwroofs:red_terracotta_lower_roof","ad_astra:cyan_industrial_lamp","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","create:oak_window","minecraft:gold_nugget_from_blasting","minecolonies:chainmailchestplate","mekanism:chemical_tank/basic","sophisticatedbackpacks:void_upgrade","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","minecraft:cobblestone_wall","dyenamics:maroon_candle","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","mcwfurnitures:spruce_modern_desk","alltheores:brass_plate","railcraft:controller_circuit","minecraft:spruce_sign","mcwroofs:gray_top_roof","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","create:crafting/logistics/brass_tunnel","sophisticatedstorage:copper_to_iron_tier_upgrade","securitycraft:reinforced_cherry_fence_gate","twigs:polished_schist","rftoolsutility:screen_link","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwbiomesoplenty:redwood_nether_door","mcwroofs:gray_terracotta_roof","mcwlights:covered_lantern","minecraft:copper_block","botania:floating_endoflame","sliceanddice:slicer","pneumaticcraft:crop_support","botania:elementium_sword","supplementaries:crank","connectedglass:clear_glass_gray_pane2","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","modularrouters:bulk_item_filter","minecraft:unobtainium_mage_chestplate_smithing","aiotbotania:livingrock_axe","mcwbiomesoplenty:empyreal_classic_door","supplementaries:gtceu/sign_post_rubber","biomesoplenty:mossy_black_sand","mcwbiomesoplenty:willow_highley_gate","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwwindows:dark_oak_blinds","mcwpaths:cobblestone_dumble_paving","dyenamics:aquamarine_concrete_powder","supplementaries:candle_holders/candle_holder_white_dye","handcrafted:jungle_cupboard","mekanism:energy_tablet","mcwroofs:green_terracotta_top_roof","productivebees:hives/advanced_cherry_canvas_hive","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","dyenamics:spring_green_wool","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","travelersbackpack:diamond_tier_upgrade","additionallanterns:mossy_cobblestone_chain","advanced_ae:advpartenc","botania:bauble_box","botania:vial","railcraft:train_detector","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","cfm:oak_kitchen_counter","minecraft:golden_hoe","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","minecraft:fire_charge","mekanism:factory/basic/infusing","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","botania:pink_petal_block","minecraft:dye_green_bed","railcraft:any_detector","ad_astra:gray_industrial_lamp","alchemistry:reactor_energy","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","minecraft:stone_button","mcwroofs:cyan_concrete_lower_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","aether:golden_pickaxe_repairing","pneumaticcraft:regulator_tube_module","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:umbran_japanese2_door","occultism:crafting/magic_lamp_empty","minecraft:purpur_block","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","domum_ornamentum:blue_brick_extra","cfm:stripped_spruce_chair","simplylight:illuminant_light_blue_block_toggle","aether:red_cape","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","minecraft:gray_concrete_powder","productivebees:expansion_boxes/expansion_box_acacia_canvas","dyenamics:lavender_dye","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:crafting/kinetics/mechanical_roller","botania:open_bucket","blue_skies:glowing_blinding_stone","botania:fabulous_pool","botania:livingrock_slate","handcrafted:jungle_side_table","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","chemlib:manganese_nugget_to_ingot","mcwbiomesoplenty:empyreal_wired_fence","handcrafted:jungle_bench","mcwbiomesoplenty:umbran_classic_door","cfm:purple_cooler","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","littlelogistics:barrel_barge","botania:terrasteel_leggings","mcwroofs:yellow_terracotta_steep_roof","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:hellbark_bark_glass_door","cfm:spruce_bedside_cabinet","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","rftoolsutility:crafter3","rftoolsutility:crafter2","supplementaries:candle_holders/candle_holder_red_dye","blue_skies:glowing_nature_stone","twilightforest:wood/jungle_banister","megacells:network/mega_interface","mcwbiomesoplenty:mahogany_drawer_counter","productivebees:stonecutter/palm_canvas_expansion_box","mcwbiomesoplenty:umbran_cottage_door","mcwbiomesoplenty:mahogany_classic_trapdoor","aether:blue_cape_cyan_wool","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_inverted_white","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","dyenamics:fluorescent_candle","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","rftoolsutility:crafter1","farmersdelight:milk_bottle","mcwroofs:oak_roof","minecraft:iron_ingot_from_iron_block","handcrafted:blue_cushion","mcwroofs:light_gray_roof_slab","mcwfurnitures:jungle_drawer","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwroofs:thatch_roof","mcwbridges:rope_jungle_bridge","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","dyenamics:lavender_candle","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","sophisticatedstorage:storage_output","connectedglass:clear_glass_cyan2","connectedglass:tinted_borderless_glass_gray2","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","minecraft:iron_ingot_from_nuggets","mcwpaths:mossy_stone_running_bond_slab","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_bookshelf","productivebees:stonecutter/mangrove_canvas_hive","mcwpaths:cobblestone_basket_weave_paving","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","supplementaries:evilcraft/sign_post_undead","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","botania:tiny_planet_block","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","mcwlights:golden_chandelier","cfm:blue_grill","mcwpaths:stone_flagstone_stairs","chemlib:sodium_block_to_ingot","cfm:stripped_birch_kitchen_drawer","botania:petal_green_double","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","twigs:chiseled_bricks","enderio:stone_gear","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwbiomesoplenty:fir_pyramid_gate","mcwlights:tavern_wall_lantern","croptopia:cheese","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","mcwfences:railing_end_brick_wall","reliquary:void_tear","mcwfurnitures:stripped_oak_lower_triple_drawer","mekanism:factory/advanced/infusing","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","handcrafted:jungle_pillar_trim","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","mcwbiomesoplenty:mahogany_large_drawer","botania:manasteel_axe","mcwbiomesoplenty:empyreal_beach_door","mcwroofs:red_concrete_lower_roof","handcrafted:spruce_counter","mcwfences:warped_pyramid_gate","mcwdoors:jungle_stable_door","mcwbiomesoplenty:pine_window2","evilcraft:special/vengeance_pickaxe","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","mcwbiomesoplenty:mahogany_swamp_door","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","rftoolsbuilder:vehicle_builder","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","mcwbiomesoplenty:stripped_mahogany_modern_wardrobe","mcwbiomesoplenty:dead_plank_pane_window","mcwbiomesoplenty:stripped_palm_log_window2","mcwwindows:blackstone_brick_gothic","mcwlights:soul_double_street_lamp","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","minecraft:diamond_sword","botania:gray_petal_block","mcwbiomesoplenty:empyreal_cottage_door","modularrouters:blank_module","mcwbiomesoplenty:mahogany_lower_triple_drawer","sgjourney:sandstone_with_lapis","mcwlights:upgraded_torch","cfm:jungle_park_bench","cfm:light_blue_kitchen_drawer","botania:petal_cyan_double","mcwpaths:sand_path_block","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","botania:livingrock_slab","immersiveengineering:crafting/toolbox","sophisticatedstorage:spruce_limited_barrel_3","botania:terrasteel_boots","sophisticatedstorage:spruce_limited_barrel_2","sophisticatedstorage:spruce_limited_barrel_1","forbidden_arcanus:clibano_core","sophisticatedstorage:spruce_limited_barrel_4","alltheores:netherite_dust_from_hammer_crushing","chemlib:gallium_ingot_from_blasting_gallium_dust","mcwpaths:brick_running_bond","mcwwindows:dark_oak_window","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_bricks_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","chemlib:manganese_ingot_to_nugget","dyenamics:amber_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_pink","handcrafted:terracotta_wide_pot","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","botania:runic_altar_alt","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:jacaranda_nether_door","mcwwindows:lime_curtain","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","cfm:stripped_acacia_chair","ae2:network/blocks/crystal_processing_growth_accelerator","mcwfurnitures:oak_double_wardrobe","mcwdoors:spruce_stable_head_door","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","botania:green_pavement","twilightforest:mining_boat","aether:golden_dart","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","croptopia:fried_chicken","minecraft:magenta_dye_from_blue_red_pink","alltheores:smelting_dust/uranium_ingot","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:fir_bark_glass_door","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","biomesoplenty:maple_chest_boat","evilcraft:crafting/undead_planks","botania:petal_brown","ae2:network/parts/terminals_crafting","xnet:netcable_blue","cfm:stripped_oak_chair","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwwindows:oak_shutter","cfm:rock_path","connectedglass:scratched_glass_black2","connectedglass:clear_glass_yellow2","mekanismgenerators:generator/heat","minecraft:cooked_beef_from_campfire_cooking","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","minecraft:dye_red_wool","productivebees:stonecutter/jungle_canvas_hive","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","ad_astra:black_industrial_lamp","mcwroofs:cyan_concrete_top_roof","mcwbiomesoplenty:willow_barn_door","aquaculture:stone_fillet_knife","handcrafted:jungle_couch","mcwbiomesoplenty:magic_stockade_fence","bloodmagic:lava_crystal","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","mcwwindows:crimson_planks_four_window","ad_astra:large_gas_tank","mcwlights:white_lamp","mcwbiomesoplenty:maple_waffle_door","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:mahogany_blossom_trapdoor","mcwbiomesoplenty:umbran_plank_pane_window","botania:virus_necrodermal","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","cfm:stripped_oak_desk","connectedglass:borderless_glass_light_gray2","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","mcwbiomesoplenty:fir_stable_door","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","mcwbiomesoplenty:palm_classic_door","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","botania:mana_diamond_block","supplementaries:daub_brace","delightful:food/cooking/rock_candy","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","ad_astra:small_white_industrial_lamp","mcwwindows:mangrove_louvered_shutter","ad_astra:space_helmet","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:palm_tropical_door","handcrafted:creeper_trophy","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","minecraft:dye_blue_wool","aiotbotania:livingwood_pickaxe","mcwbiomesoplenty:palm_cottage_door","mcwfurnitures:spruce_lower_triple_drawer","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","rftoolsbuilder:vehicle_control_module","botania:petal_gray","occultism:blasting/iesnium_ingot_from_raw","utilitarian:utility/spruce_logs_to_pressure_plates","silentgear:crimson_iron_dust","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","mcwfences:granite_grass_topped_wall","botania:fire_rod","botania:petal_pink_double","securitycraft:reinforced_crimson_fence_gate","pneumaticcraft:spawner_core_shell","alltheores:smelting_dust/aluminum_ingot","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","mcwroofs:light_gray_steep_roof","dyenamics:persimmon_candle","botania:elementium_shears","minecraft:red_banner","botania:bifrost_pane","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","mcwfences:oak_stockade_fence","mcwfences:dark_oak_picket_fence","croptopia:shaped_water_bottle","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","pylons:expulsion_pylon","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","alltheores:brass_rod","botania:livingwood_stairs","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","mcwroofs:jungle_roof","industrialforegoing:fluid_extractor","mcwpaths:andesite_running_bond","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","create:crafting/kinetics/black_seat","mcwpaths:brick_crystal_floor_path","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","railcraft:signal_tuner","cfm:pink_kitchen_counter","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwbiomesoplenty:dead_bark_glass_door","botania:spark_upgrade_dispersive","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","botania:dreamwood_wand","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:cherenkov_dye","twilightforest:time_boat","cfm:dye_black_picket_gate","mcwbiomesoplenty:fir_japanese_door","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","alchemistry:fusion_chamber_controller","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","connectedglass:scratched_glass_red2","minecraft:stripped_acacia_wood","botania:red_string_dispenser","handcrafted:jungle_dining_bench","botania:dreamwood_wall","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","mcwwindows:stripped_acacia_log_four_window","mcwbiomesoplenty:dead_paper_door","domum_ornamentum:green_floating_carpet","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","occultism:crafting/raw_iesnium_block","rftoolspower:blazing_agitator","mcwwindows:spruce_curtain_rod","create:crafting/kinetics/andesite_door","minecraft:brick_wall","nethersdelight:diamond_machete","utilitix:advanced_brewery","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:stripped_willow_log_window","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwbiomesoplenty:palm_nether_door","simplylight:illuminant_purple_block_on_dyed","cfm:blue_sofa","mcwbiomesoplenty:fir_western_door","pneumaticcraft:compressed_brick_stairs","mcwtrpdoors:metal_warning_trapdoor","productivetrees:planks/brown_amber_planks","mcwfurnitures:jungle_striped_chair","dyenamics:honey_wool","additionallanterns:diamond_lantern","railcraft:steel_shears","minecraft:mossy_cobblestone_slab_from_mossy_cobblestone_stonecutting","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","railways:crafting/track_switch_andesite","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","minecraft:compass","bigreactors:energizer/computerport","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","minecraft:loom","supplementaries:sign_post_spruce","ad_astra:steel_engine","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","domum_ornamentum:red_brick_extra","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","dyenamics:wine_wool","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","botania:green_shiny_flower","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","sophisticatedbackpacks:everlasting_upgrade","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_white_stained_glass_pane_from_glass","handcrafted:blue_crockery_combo","utilitarian:utility/spruce_logs_to_stairs","sophisticatedstorage:storage_advanced_void_upgrade_from_backpack_advanced_void_upgrade","chemlib:potassium_ingot_to_nugget","dyenamics:bed/maroon_bed_frm_white_bed","travelersbackpack:dye_red_sleeping_bag","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","botania:gaia_spreader","mcwroofs:black_upper_lower_roof","undergarden:depthrock_brick_stairs_stonecutting","mcwfences:warped_wired_fence","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwroofs:stone_top_roof","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","forbidden_arcanus:blacksmith_gavel_head","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","mcwfurnitures:jungle_end_table","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfurnitures:stripped_oak_chair","paraglider:paraglider","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","botania:petal_gray_double","botania:petal_yellow","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","mcwfurnitures:spruce_desk","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","aether:golden_helmet_repairing","alchemistry:dissolver","mcwfences:mangrove_hedge","sophisticatedbackpacks:compacting_upgrade","chemlib:tantalum_ingot_from_smelting_tantalum_dust","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","handcrafted:spruce_cupboard","mcwbiomesoplenty:mahogany_chair","botania:dye_red","mcwbiomesoplenty:mahogany_planks_upper_steep_roof","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_barn_glass_door","aether:aether_gold_nugget_from_blasting","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","mcwfurnitures:stripped_spruce_lower_triple_drawer","mcwbiomesoplenty:mahogany_glass_table","ae2:network/cables/smart_brown","mcwbiomesoplenty:mahogany_top_roof","botania:world_seed","ae2:network/cables/covered_orange","mcwwindows:red_curtain","create:crafting/logistics/brass_funnel","chemlib:titanium_ingot_to_block","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwroofs:red_terracotta_top_roof","aether:golden_sword_repairing","matc:crystals/inferium","mcwroofs:lime_concrete_lower_roof","occultism:crafting/demons_dream_essence_from_seeds","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","mcwroofs:gray_terracotta_attic_roof","mcwroofs:gutter_base_orange","mythicbotany:alfsteel_ingot_decompress","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","ad_astra:cyan_flag","cfm:stripped_spruce_kitchen_sink_light","mcwroofs:bricks_attic_roof","occultism:crafting/otherstone_slab","enderio:resetting_lever_three_hundred_from_inv","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","simplylight:illuminant_yellow_block_dyed","occultism:crafting/brush","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","mcwbiomesoplenty:dead_waffle_door","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","botania:lens_firework","ae2:network/cells/item_storage_cell_1k_storage","mcwroofs:yellow_concrete_steep_roof","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwlights:mangrove_tiki_torch","securitycraft:redstone_module","travelersbackpack:redstone_smithing","botania:dye_magenta","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","create:framed_glass_from_glass_colorless_stonecutting","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:stripped_redwood_log_window","mcwbiomesoplenty:dead_swamp_door","railcraft:signal_lamp","cfm:jungle_blinds","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","xnet:netcable_green","mcwbiomesoplenty:redwood_classic_door","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","sophisticatedstorage:jungle_limited_barrel_3","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","pneumaticcraft:paper_from_tag_filter","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwwindows:acacia_window2","minecraft:orange_terracotta","mcwroofs:white_top_roof","botania:light_blue_petal_block","mcwbiomesoplenty:magic_waffle_door","aquaculture:gold_fillet_knife","mcwbiomesoplenty:redwood_cottage_door","create:copper_scaffolding_from_ingots_copper_stonecutting","mythicbotany:wither_aconite_floating","minecraft:paper","ae2:network/cables/dense_smart_cyan","chemlib:tantalum_ingot_to_block","pneumaticcraft:compressed_bricks_from_tile","botania:light_gray_shiny_flower","utilitix:stone_wall_stonecutting","botania:conversions/terrasteel_block_deconstruct","cfm:spruce_kitchen_counter","travelersbackpack:warden","supplementaries:candle_holders/candle_holder_gray_dye","dyenamics:fluorescent_concrete_powder","botania:incense_plate","securitycraft:reinforced_cherry_fence","additionallanterns:copper_lantern","comforts:sleeping_bag_light_blue","mcwroofs:lime_terracotta_steep_roof","mcwbiomesoplenty:willow_paper_door","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","cfm:crimson_mail_box","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","botania:conversions/black_petal_block_deconstruct","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwfences:acacia_curved_gate","mysticalagriculture:infusion_pedestal","rftoolsutility:screen_controller","mcwwindows:diorite_window2","minecraft:cooked_rabbit","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwbiomesoplenty:redwood_swamp_door","alltheores:lead_rod","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","minecraft:iron_block","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwroofs:gutter_base","mcwdoors:acacia_beach_door","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwpaths:mossy_cobblestone_diamond_paving","connectedglass:borderless_glass_gray_pane2","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","advanced_ae:advpatpropart","pneumaticcraft:reinforced_brick_wall","fluxnetworks:wipe_fluxpoint","ae2:network/cables/smart_purple","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","pneumaticcraft:wall_lamp_inverted_blue","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","biomesoplenty:mahogany_trapdoor","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","pneumaticcraft:logistics_frame_requester","mcwbiomesoplenty:maple_four_window","chemlib:magnesium_nugget_to_ingot","mcwwindows:stripped_oak_log_window","minecraft:golden_boots","minecraft:pink_dye_from_red_white_dye","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","rftoolspower:blazing_generator","ad_astra:steel_door","supplementaries:flags/flag_cyan","immersiveengineering:crafting/tinted_glass_lead_wire","reliquary:wraith_node","botania:diva_charm","cfm:pink_grill","mcwroofs:gutter_middle_orange","croptopia:scrambled_eggs","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwroofs:jungle_attic_roof","blue_skies:aquite_helmet","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","dimstorage:dimensional_tablet","mcwbiomesoplenty:maple_stable_head_door","botania:aura_ring_greater","minecraft:polished_blackstone_brick_stairs","mcwroofs:white_terracotta_upper_steep_roof","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","create:crafting/kinetics/mechanical_saw","mcwpaths:brick_crystal_floor_slab","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","modularrouters:player_module","minecraft:glass","mcwbiomesoplenty:pine_classic_door","allthecompressed:compress/gold_block_1x","mcwdoors:spruce_paper_door","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:brown_concrete_attic_roof","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwbiomesoplenty:pine_cottage_door","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","ae2:network/cables/smart_red","dyenamics:persimmon_concrete_powder","botania:light_relay","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","mcwwindows:light_blue_mosaic_glass","undergarden:depthrock_tile_slab_stonecutting","ad_astra:yellow_industrial_lamp","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","chemlib:tantalum_ingot_from_blasting_tantalum_dust","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","railcraft:steel_gear","aether:diamond_shovel_repairing","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","ae2:tools/network_color_applicator","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","mcwbiomesoplenty:maple_plank_window2","biomesoplenty:mahogany_fence","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:jacaranda_bamboo_door","mcwbiomesoplenty:dead_plank_window2","botania:quartz_lavender","mysticalagriculture:prosperity_gemstone","travelersbackpack:cyan_sleeping_bag","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","mcwroofs:red_concrete_roof","botania:dye_light_gray","minecraft:cyan_banner","ae2:network/cables/smart_fluix","enderio:resetting_lever_thirty","minecraft:glistering_melon_slice","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","pneumaticcraft:wall_lamp_light_gray","minecraft:dye_yellow_wool","ad_astra:steel_factory_block","dyenamics:amber_concrete_powder","handcrafted:spruce_chair","bigreactors:crafting/raw_yellorium_component_to_storage","mcwroofs:light_gray_concrete_lower_roof","supplementaries:candle_holders/candle_holder_black_dye","occultism:crafting/spirit_torch","undergarden:depthrock_brick_wall_stonecutting","chemlib:potassium_nugget_to_ingot","productivebees:expansion_boxes/expansion_box_cherry_canvas","farmersdelight:cooking/vegetable_noodles","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwbiomesoplenty:hellbark_plank_window2","mcwroofs:lime_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","minecraft:smooth_stone_slab","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","botania:azulejo_0","mcwbiomesoplenty:fir_tropical_door","occultism:crafting/demons_dream_essence_from_fruit","connectedglass:borderless_glass_white2","aquaculture:heavy_hook","mcwwindows:acacia_window","croptopia:food_press","railcraft:mob_detector","blue_skies:frostbright_bookshelf","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","mcwfurnitures:oak_chair","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","advanced_ae:import_export_bus","botania:cyan_shiny_flower","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","croptopia:shaped_milk_bottle","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","expatternprovider:active_formation_plane","mcwlights:orange_lamp","pneumaticcraft:reinforced_brick_pillar","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","gtceu:shapeless/decompress_tin_from_ore_block","mcwpaths:brick_crystal_floor","botania:dodge_ring","biomesoplenty:mahogany_sign","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","comforts:hammock_to_red","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","botania:cyan_petal_block","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","railcraft:charge_terminal","dyenamics:conifer_wool","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","mcwdoors:spruce_barn_door","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","domum_ornamentum:red_floating_carpet","immersiveengineering:crafting/alu_fence","minecraft:diamond_shovel","mcwwindows:end_brick_gothic","botania:petal_white_double","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:jungle_planks_upper_lower_roof","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","dyenamics:mint_wool","connectedglass:borderless_glass_green2","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","alltheores:diamond_plate","utilitarian:utility/spruce_logs_to_boats","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","productivebees:stonecutter/rosewood_canvas_hive","additionallanterns:end_stone_chain","farmersdelight:cooking/mushroom_rice","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","mcwbiomesoplenty:hellbark_stable_head_door","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","blue_skies:aquite_block","mcwroofs:gutter_base_red","botania:holy_cloak","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:receiver_component","create:crafting/kinetics/fluid_pipe_vertical","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","minecraft:diamond_chestplate","domum_ornamentum:mossy_cobblestone_extra","securitycraft:reinforced_gray_stained_glass","aether:iron_hoe_repairing","cfm:dye_black_picket_fence","mcwbiomesoplenty:stripped_maple_log_window2","advanced_ae:stock_export_bus","aether:wooden_sword_repairing","mcwlights:wall_lamp","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","mcwbiomesoplenty:jacaranda_mystic_door","supplementaries:hourglass","everythingcopper:copper_leggings","pneumaticcraft:reinforced_brick_tile","mcwbridges:andesite_bridge_stair","cfm:light_blue_sofa","mcwroofs:lime_terracotta_attic_roof","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","minecraft:orange_dye_from_red_yellow","simplylight:illuminant_blue_block_on_dyed","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","railcraft:bushing_gear_brass","mcwwindows:crimson_planks_window","botania:conversions/light_blue_petal_block_deconstruct","railcraft:steel_tank_valve","mob_grinding_utils:recipe_entity_conveyor","mcwdoors:acacia_modern_door","ad_astra:steel_rod","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","enderio:redstone_alloy_grinding_ball","mcwwindows:stripped_jungle_log_four_window","dyenamics:bubblegum_candle","create:crafting/kinetics/weighted_ejector","mcwlights:iron_triple_candle_holder","mcwroofs:pink_terracotta_roof","travelersbackpack:redstone","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","minecraft:acacia_planks","immersiveengineering:crafting/shovel_steel","mcwbiomesoplenty:fir_nether_door","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","travelersbackpack:emerald","farmersdelight:cutting_board","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:mossy_cobblestone_square_paving","ad_astra:nasa_workbench","sophisticatedstorage:storage_stack_upgrade_tier_2_from_backpack_stack_upgrade_tier_1","ad_astra:etrionic_capacitor","mcwbiomesoplenty:empyreal_japanese2_door","botania:apothecary_deepslate","cfm:dye_yellow_picket_gate","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","rftoolspower:coalgenerator","mcwfurnitures:stripped_spruce_large_drawer","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","allthecompressed:compress/nether_star_block_2x","minecraft:white_carpet","ae2:network/cables/covered_blue","dyenamics:maroon_dye","utilitix:filter_rail","aether:diamond_gloves","minecraft:mossy_cobblestone_stairs_from_mossy_cobblestone_stonecutting","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwbiomesoplenty:dead_stable_door","minecraft:cracked_stone_bricks","mcwfurnitures:spruce_triple_drawer","railcraft:steel_tunnel_bore_head","gtceu:smelting/smelt_dust_electrum_to_ingot","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","mcwbiomesoplenty:willow_plank_pane_window","travelersbackpack:dye_green_sleeping_bag","securitycraft:crystal_quartz_item","dimstorage:dim_wall","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","allthecompressed:decompress/nether_star_block_1x","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","modularrouters:void_module","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","mcwwindows:mangrove_plank_window2","botania:knockback_belt","mcwroofs:spruce_planks_steep_roof","mysticalagriculture:imperium_essence_uncraft","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","botania:shimmerrock_slab","mcwfurnitures:spruce_large_drawer","botania:conversions/blazeblock_deconstruct","handcrafted:spruce_shelf","mcwlights:acacia_tiki_torch","biomesoplenty:mahogany_door","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","securitycraft:reinforced_warped_fence_gate","botania:skydirt_rod","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/gearshift","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","minecraft:light_gray_dye_from_gray_white_dye","mcwfences:blackstone_pillar_wall","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","blue_skies:aquite_leggings","deepresonance:lens","additionallanterns:normal_lantern_black","croptopia:doughnut","mcwlights:square_wall_lamp","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","botania:blue_shiny_flower","mcwbiomesoplenty:mahogany_double_drawer_counter","minecraft:cut_copper_from_copper_block_stonecutting","alltheores:lumium_ingot_from_dust","aether:skyroot_bookshelf","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","cfm:stripped_acacia_crate","allthecompressed:compress/cobbled_deepslate_1x","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","ae2:tools/fluix_axe","cfm:black_kitchen_sink","connectedglass:borderless_glass_pink2","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","cfm:dye_cyan_picket_gate","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwroofs:pink_concrete_top_roof","travelersbackpack:blue_sleeping_bag","forbidden_arcanus:sanity_meter","botania:diluted_pool","mcwbiomesoplenty:hellbark_japanese_door","modularrouters:energy_output_module","chemlib:titanium_ingot_to_nugget","mcwlights:glowstone_slab","mcwbiomesoplenty:stripped_mahogany_stool_chair","mcwbiomesoplenty:mahogany_log_bridge_middle","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alltheores:diamond_rod","minecraft:stone_slab_from_stone_stonecutting","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","reliquary:infernal_claw","chemlib:sodium_nugget_to_ingot","silentgear:crimson_iron_dust_blasting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwroofs:spruce_upper_lower_roof","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","cfm:spruce_chair","littlelogistics:locomotive_route","connectedglass:clear_glass_white2","securitycraft:universal_block_remover","mcwbiomesoplenty:stripped_mahogany_coffee_table","securitycraft:reinforced_bookshelf","minecraft:netherite_leggings_smithing","enderio:resetting_lever_sixty","mcwfences:quartz_grass_topped_wall","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","matc:crystals/prudentium","mcwtrpdoors:cherry_ranch_trapdoor","minecraft:stone_brick_stairs_from_stone_stonecutting","comforts:sleeping_bag_to_light_blue","mcwroofs:brown_concrete_top_roof","chemlib:calcium_nugget_to_ingot","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","botania:petal_pink","handcrafted:wood_bowl","pneumaticcraft:charging_station","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","pneumaticcraft:compressed_stone_from_slab","cfm:cyan_trampoline","mcwroofs:white_roof","mcwbiomesoplenty:fir_glass_door","connectedglass:scratched_glass_yellow_pane2","ae2:tools/portable_item_cell_1k","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","ae2:network/cables/glass_blue","cfm:stripped_warped_kitchen_sink_dark","minecraft:lapis_lazuli","mcwroofs:light_gray_concrete_upper_steep_roof","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","mcwpaths:mossy_stone_strewn_rocky_path","mcwwindows:stripped_mangrove_log_window2","minecraft:quartz_block","alltheores:iron_dust_from_hammer_crushing","botania:glimmering_dreamwood_log","rftoolsbuilder:yellow_shield_template_block","domum_ornamentum:brown_floating_carpet","mcwroofs:lime_concrete_top_roof","mcwwindows:quartz_four_window","occultism:crafting/chalk_white_impure","biomesoplenty:palm_boat","reliquary:uncrafting/bone","botania:dye_gray","utilitarian:no_soliciting/restraining_order","mcwbiomesoplenty:stripped_mahogany_bookshelf_cupboard","pneumaticcraft:wall_lamp_brown","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","productivebees:hives/advanced_bamboo_canvas_hive","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","bloodmagic:incense_altar","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_paper_door","sophisticatedstorage:storage_pickup_upgrade_from_backpack_pickup_upgrade","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","blue_skies:aquite_shovel","sophisticatedstorage:backpack_magnet_upgrade_from_storage_magnet_upgrade","rftoolsbuilder:shape_card_quarry_clear_fortune","mcwpaths:andesite_crystal_floor","mcwbiomesoplenty:empyreal_stable_door","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwroofs:oak_planks_lower_roof","mcwwindows:sandstone_window","sophisticatedstorage:storage_magnet_upgrade_from_backpack_magnet_upgrade","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","alchemistry:reactor_input","railcraft:diamond_spike_maul","additionallanterns:granite_lantern","minecraft:white_candle","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_bricks_stonecutting","mcwwindows:spruce_plank_window2","mcwwindows:lime_mosaic_glass_pane","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","comforts:hammock_to_white","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","create:crafting/kinetics/mechanical_bearing","mcwbiomesoplenty:stripped_mahogany_bookshelf","minecraft:golden_carrot","ae2:network/cells/fluid_cell_housing","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","productivebees:stonecutter/magic_canvas_hive","croptopia:dough","mcwbiomesoplenty:stripped_empyreal_log_window2","blue_skies:aquite_axe","dyenamics:honey_stained_glass","create:crafting/kinetics/elevator_pulley","cfm:stripped_acacia_cabinet","mcwtrpdoors:spruce_four_panel_trapdoor","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","minecraft:magma_cream","botania:slime_bottle","create:crafting/kinetics/attribute_filter","handcrafted:white_bowl","supplementaries:key","ae2:block_cutter/walls/sky_stone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","rftoolspower:power_core1","gtceu:shaped/sticky_piston_resin","botania:gaia_ingot","ae2:network/blocks/io_port","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","botania:petal_black_double","additionallanterns:stone_chain","pneumaticcraft:compressed_brick_from_slab","dyenamics:banner/ultramarine_banner","aether:skyroot_chest_boat","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","botania:mana_pylon","mcwfurnitures:stripped_oak_striped_chair","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","mcwpaths:brick_windmill_weave_stairs","additionallanterns:normal_sandstone_lantern","undergarden:polished_depthrock_stonecutting","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","botania:apothecary_livingrock","advanced_ae:smalladvpatpro","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","mcwbiomesoplenty:dead_barn_door","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","railcraft:feed_station","minecraft:black_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_inverted_green","mcwfurnitures:spruce_chair","twigs:gravel_bricks","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","mcwroofs:oak_planks_upper_lower_roof","immersiveengineering:crafting/armor_steel_chestplate","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","comforts:sleeping_bag_to_cyan","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","immersiveengineering:crafting/stick_aluminum","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","dyenamics:bubblegum_concrete_powder","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","ae2:tools/matter_cannon","mcwbiomesoplenty:fir_four_panel_door","supplementaries:slingshot","mcwdoors:acacia_japanese2_door","twigs:cobblestone_from_pebble","everythingcopper:copper_rail","cfm:black_grill","pneumaticcraft:wall_lamp_inverted_black","simplylight:illuminant_block","evilcraft:crafting/blood_infuser","blue_skies:glowing_nature_stonebrick_from_glowstone","dyenamics:dye_ultramarine_carpet","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_four_panel_door","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","dyenamics:rose_candle","aether:aether_iron_nugget_from_blasting","aquaculture:neptunium_ingot_from_nuggets","botania:terraform_rod","minecraft:iron_pickaxe","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","minecraft:spruce_button","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","rftoolsutility:matter_booster","mcwwindows:granite_window","ae2:network/cables/glass_orange","minecraft:vibranium_mage_chestplate_smithing","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","simplylight:illuminant_black_block_on_dyed","mcwbiomesoplenty:mahogany_glass_trapdoor","mcwfences:spruce_highley_gate","mcwbiomesoplenty:redwood_stable_head_door","dyenamics:aquamarine_candle","mcwdoors:jungle_barn_door","handcrafted:oak_drawer","mcwbiomesoplenty:mahogany_planks_roof","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","sophisticatedbackpacks:inception_upgrade","mcwbiomesoplenty:empyreal_japanese_door","minecraft:lime_stained_glass","dyenamics:mint_candle","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwbiomesoplenty:mahogany_steep_roof","minecraft:stone_brick_walls_from_stone_stonecutting","securitycraft:keypad_item","mcwbiomesoplenty:hellbark_paper_door","cfm:light_blue_kitchen_counter","additionallanterns:normal_lantern_lime","ae2:network/cables/covered_fluix_clean","botania:elementium_block","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","biomesoplenty:mahogany_slab","handcrafted:cyan_cushion","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","botania:aura_ring","botania:dreamwood_twig","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","cfm:fridge_dark","chimes:copper_chimes","simplylight:illuminant_magenta_block_on_dyed","mcwtrpdoors:mangrove_bark_trapdoor","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","chemlib:sodium_ingot_to_block","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","expatternprovider:wireless_connector","mcwwindows:black_mosaic_glass","mcwroofs:black_terracotta_lower_roof","mcwroofs:gray_concrete_top_roof","blue_skies:ventium_shears","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","mcwwindows:prismarine_brick_gothic","cfm:spatula","ae2:network/cables/dense_covered_fluix_clean","dyenamics:dye_honey_carpet","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwpaths:mossy_cobblestone_clover_paving","mcwbiomesoplenty:fir_cottage_door","constructionwand:core_angel","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","mcwwindows:stripped_oak_log_four_window","mcwroofs:gutter_base_light_gray","mcwbiomesoplenty:willow_glass_door","mcwroofs:lime_terracotta_top_roof","mcwroofs:orange_terracotta_upper_lower_roof","sophisticatedstorage:storage_stack_upgrade_tier_1_plus_from_backpack_stack_upgrade_starter_tier","securitycraft:reinforced_andesite_with_vanilla_diorite","gateways:basic/slime","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","reliquary:mob_charm_fragments/slime","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","sophisticatedbackpacks:crafting_upgrade","cfm:stripped_spruce_desk","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","botania:white_petal_block","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","botania:mana_quartz_pillar","tombstone:ankh_of_prayer","cfm:white_sofa","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwpaths:mossy_stone_flagstone_slab","mcwfences:acacia_highley_gate","mcwroofs:andesite_steep_roof","mcwroofs:pink_concrete_roof","connectedglass:tinted_borderless_glass_black2","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","mcwlights:magenta_lamp","chemlib:gallium_ingot_to_nugget","productivebees:stonecutter/warped_canvas_expansion_box","ae2:network/cables/dense_covered_lime","botania:conversions/magenta_petal_block_deconstruct","mcwbiomesoplenty:maple_swamp_door","botania:mana_fluxfield","mcwwindows:orange_mosaic_glass","mcwwindows:warped_planks_window2","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","ae2:shaped/slabs/sky_stone_block","handcrafted:oak_bench","mcwbiomesoplenty:maple_bark_glass_door","minecraft:yellow_candle","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","minecraft:repeater","mcwroofs:spruce_planks_upper_steep_roof","minecraft:red_concrete_powder","rftoolsbuilder:shape_card_quarry_silk_dirt","botania:conversions/manasteel_block_deconstruct","chemlib:magnesium_ingot_to_block","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwbiomesoplenty:empyreal_glass_door","additionallanterns:normal_lantern_white","minecraft:iron_leggings","ad_astra:steel_cable","create:rose_quartz_block_from_rose_quartz_stonecutting","botania:livingwood_slab","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","productivebees:hives/advanced_mangrove_canvas_hive","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:fir_waffle_door","mcwbridges:balustrade_mossy_cobblestone_bridge","cfm:pink_kitchen_sink","blue_skies:aquite_chestplate","dyenamics:banner/amber_banner","mcwwindows:spruce_blinds","mcwbiomesoplenty:mahogany_lower_bookshelf_drawer","mcwwindows:red_sandstone_window","immersiveengineering:crafting/gunpart_hammer","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","securitycraft:reinforced_orange_stained_glass_pane_from_dye","handcrafted:spruce_drawer","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","ad_astra:red_industrial_lamp","domum_ornamentum:light_blue_floating_carpet","minecraft:dye_white_carpet","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","mcwbiomesoplenty:pine_beach_door","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","additionallanterns:normal_lantern_blue","minecraft:nether_bricks","enderio:resetting_lever_three_hundred_inv_from_prev","mekanism:factory/elite/enriching","minecraft:iron_chestplate","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","mcwdoors:garage_gray_door","botania:petal_green","handcrafted:witch_trophy","minecraft:polished_andesite_from_andesite_stonecutting","littlelogistics:seater_car","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","botania:mushroom_5","minecraft:stone_brick_stairs","botania:mushroom_4","botania:mushroom_3","bigreactors:reprocessor/outputport","mcwwindows:quartz_window","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","botania:dye_brown","blue_skies:coarse_lunar_dirt","botania:glimmering_livingwood_log","dyenamics:peach_candle","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","botania:goddess_charm","minecraft:clock","dyenamics:conifer_dye","occultism:crafting/otherstone_pedestal","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","dyenamics:dye_peach_carpet","mcwlights:soul_birch_tiki_torch","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","biomesoplenty:mahogany_button","travelersbackpack:coal","dyenamics:bed/conifer_bed","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","minecraft:diamond_leggings","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_paper_door","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","alltheores:tin_rod","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","minecraft:polished_andesite_slab_from_andesite_stonecutting","minecraft:dye_black_bed","securitycraft:reinforced_red_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_western_door","handcrafted:jungle_corner_trim","biomesoplenty:empyreal_chest_boat","chemlib:calcium_ingot_from_blasting_calcium_dust","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","mcwbiomesoplenty:pine_swamp_door","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","dyenamics:honey_candle","twigs:rocky_dirt","supplementaries:pancake_fd","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","botania:petal_black","botania:lens_warp","mcwbiomesoplenty:dead_bamboo_door","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","create:crafting/kinetics/mechanical_piston","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","botania:white_shiny_flower","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","botania:petal_red_double","mcwroofs:white_concrete_upper_lower_roof","chemlib:titanium_block_to_ingot","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","twilightforest:sorting_chest_boat","aether:diamond_axe_repairing","pneumaticcraft:wall_lamp_cyan","create:crafting/kinetics/fluid_pipe","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbridges:brick_bridge","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","mcwbiomesoplenty:stripped_mahogany_lower_triple_drawer","supplementaries:flags/flag_yellow","mcwbiomesoplenty:pine_bark_glass_door","sophisticatedbackpacks:smithing_upgrade","minecraft:gold_ingot_from_smelting_raw_gold","mekanism:tier_installer/ultimate","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","chemlib:calcium_ingot_to_nugget","mcwfurnitures:oak_lower_triple_drawer","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","mcwbridges:jungle_log_bridge_middle","sophisticatedbackpacks:anvil_upgrade","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwwindows:jungle_blinds","occultism:crafting/book_of_binding_afrit","mcwlights:oak_tiki_torch","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","travelersbackpack:iron_golem_smithing","alltheores:lumium_dust_from_alloy_blending","mcwfurnitures:spruce_bookshelf_cupboard","cfm:purple_kitchen_drawer","ad_astra:steel_plateblock","alltheores:brass_gear","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","dyenamics:aquamarine_stained_glass","minecraft:netherite_chestplate_smithing","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","terralith:observer_alt","minecraft:oak_sign","mcwwindows:stone_pane_window","botania:light_gray_petal_block","domum_ornamentum:white_paper_extra","connectedglass:borderless_glass_blue2","dyenamics:fluorescent_dye","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","mcwwindows:dark_oak_plank_parapet","mcwbiomesoplenty:pine_waffle_door","mcwwindows:spruce_log_parapet","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","mekanism:osmium_compressor","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","sfm:manager","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:willow_bamboo_door","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","mysticalagriculture:infusion_altar","mcwroofs:spruce_lower_roof","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","comforts:sleeping_bag_to_red","connectedglass:tinted_borderless_glass_cyan2","mcwdoors:spruce_whispering_door","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","ae2:tools/network_tool","deeperdarker:bloom_boat","utilitix:linked_repeater","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","additionallanterns:normal_lantern_green","connectedglass:clear_glass_yellow_pane2","advanced_ae:smalladvpatpro2","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:spruce_curved_gate","mcwdoors:jungle_swamp_door","aiotbotania:livingwood_sword","botania:conversions/terrasteel_to_nugget","mcwpaths:andesite_flagstone_stairs","botania:dye_light_blue","domum_ornamentum:green_brick_extra","mcwwindows:warped_planks_four_window","mcwwindows:stripped_crimson_stem_window2","mcwpaths:mossy_stone_crystal_floor_path","croptopia:meringue","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","ae2:network/cables/dense_smart_fluix_clean","rftoolsutility:moduleplus_template","evilcraft:crafting/blood_infusion_core","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","mcwbiomesoplenty:dead_tropical_door","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwbiomesoplenty:jacaranda_paper_door","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","railcraft:radio_circuit","pneumaticcraft:manual_compressor","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","chemlib:tantalum_nugget_to_ingot","mcwpaths:cobbled_deepslate_clover_paving","mcwfurnitures:oak_modern_wardrobe","handcrafted:yellow_plate","mcwlights:golden_double_candle_holder","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","simplylight:illuminant_light_blue_block_on_dyed","mcwroofs:yellow_terracotta_upper_steep_roof","supplementaries:flags/flag_lime","create:crafting/appliances/copper_backtank","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","mcwwindows:cherry_plank_four_window","mcwbiomesoplenty:fir_modern_door","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","create:crafting/kinetics/portable_storage_interface","ad_astra:steel_trapdoor","securitycraft:reinforced_pink_stained_glass_pane_from_dye","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","pneumaticcraft:manometer","securitycraft:alarm","mcwbiomesoplenty:dead_japanese2_door","mcwfurnitures:oak_counter","corail_woodcutter:spruce_woodcutter","dyenamics:icy_blue_dye","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","botania:elementium_leggings","mcwroofs:oak_top_roof","botania:mana_ring","eidolon:decompress_raw_silver_block","create:crafting/appliances/netherite_backtank_from_netherite","comforts:sleeping_bag_red","utilitix:crude_furnace","dyenamics:peach_dye","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","integrateddynamics:crafting/drying_basin","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","aether:blue_cape_blue_wool","twigs:mossy_cobblestone_bricks_cobblestone","ae2:network/cables/glass_black","mcwfurnitures:stripped_oak_bookshelf","botania:polished_livingrock","mcwbiomesoplenty:fir_curved_gate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","botania:brown_shiny_flower","additionallanterns:gold_lantern","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","productivebees:expansion_boxes/expansion_box_warped_canvas","mcwbiomesoplenty:mahogany_planks_lower_roof","matc:prudentium_essence","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","alchemistry:compactor","minecraft:dropper","dyenamics:bed/maroon_bed","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","productivebees:stonecutter/spruce_canvas_hive","create:crafting/kinetics/brass_hand","mcwfences:mangrove_horse_fence","mcwbiomesoplenty:empyreal_bamboo_door","create:crafting/kinetics/lime_seat","mcwbiomesoplenty:stripped_mahogany_double_drawer_counter","mcwdoors:oak_japanese2_door","simplylight:illuminant_black_block_dyed","mcwbiomesoplenty:mahogany_mystic_trapdoor","mcwdoors:acacia_bamboo_door","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","productivebees:stonecutter/yucca_canvas_expansion_box","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:red_cushion","mcwfurnitures:spruce_table","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","mcwwindows:stripped_cherry_log_window","dyenamics:dye_rose_carpet","botania:temperance_stone","immersiveengineering:crafting/wirecoil_redstone","mcwbiomesoplenty:mahogany_tropical_trapdoor","cfm:blue_kitchen_drawer","chemlib:magnesium_ingot_from_smelting_magnesium_dust","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_bricks_bridge","supplementaries:candle_holders/candle_holder_blue_dye","botania:crystal_bow","rftoolsbuilder:red_shield_template_block","mcwfurnitures:stripped_oak_large_drawer","mcwbiomesoplenty:dead_mystic_door","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","undergarden:polished_depthrock_wall_stonecutting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","bloodmagic:soul_forge","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","pneumaticcraft:wall_lamp_magenta","mcwroofs:jungle_planks_steep_roof","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","cfm:dye_red_picket_gate","travelersbackpack:hose_nozzle","botania:elementium_helmet","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","ae2:network/parts/formation_plane_alt","enderio:black_paper","twigs:polished_tuff_stonecutting","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","mcwbiomesoplenty:pine_japanese_door","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","allthemodium:allthemodium_ingot","sophisticatedbackpacks:pickup_upgrade","blue_skies:ventium_nugget_from_ingot","undergarden:smogstem_chest_boat","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","blue_skies:aquite_pickaxe","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","securitycraft:laser_block","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","botania:terra_pick","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","botania:blood_pendant","rftoolsutility:matter_receiver","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:hellbark_four_panel_door","rftoolsutility:clock_module","botania:terra_plate","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_3","modularrouters:sender_module_2","modularrouters:sender_module_1","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","supplementaries:flags/flag_blue","minecraft:honeycomb_block","undergarden:polished_depthrock_stairs_stonecutting","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","occultism:crafting/chalk_purple_impure","ae2:network/cables/glass_light_blue","twigs:mossy_bricks_from_moss_block","minecraft:andesite_stairs_from_andesite_stonecutting","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","create:vertical_framed_glass_from_glass_colorless_stonecutting","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","create:crafting/materials/electron_tube","mcwbiomesoplenty:maple_japanese2_door","farmersdelight:wheat_dough_from_water","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","croptopia:nougat","dyenamics:icy_blue_concrete_powder","rftoolsbase:crafting_card","mcwroofs:cyan_terracotta_attic_roof","mcwlights:soul_bamboo_tiki_torch","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","rftoolsutility:matter_transmitter","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","simplylight:illuminant_pink_block_on_toggle","bigreactors:turbine/reinforced/passivetap_fe","cfm:stripped_warped_kitchen_sink_light","aiotbotania:livingwood_shovel","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwbiomesoplenty:mahogany_end_table","mcwfurnitures:spruce_double_drawer","handcrafted:oak_couch","sophisticatedbackpacks:stack_upgrade_tier_4","allthetweaks:ender_pearl_block","chemlib:copper_ingot_from_smelting_copper_dust","mythicbotany:mana_infuser","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","mcwbiomesoplenty:hellbark_glass_door","everythingcopper:copper_door","travelersbackpack:dragon","farmersdelight:cooking/glow_berry_custard","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","cfm:orange_kitchen_counter","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","chemlib:potassium_block_to_ingot","pneumaticcraft:spawner_agitator","dyenamics:bed/persimmon_bed","mcwbiomesoplenty:mahogany_counter","mcwroofs:jungle_upper_lower_roof","minecraft:polished_blackstone_brick_wall","mcwbiomesoplenty:empyreal_mystic_door","ae2:network/wireless_crafting_terminal","mcwdoors:metal_door","mcwroofs:jungle_planks_upper_steep_roof","modularrouters:vacuum_module","rftoolsutility:spawner","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","mekanism:factory/basic/enriching","sophisticatedbackpacks:stack_upgrade_tier_2","sophisticatedbackpacks:stack_upgrade_tier_3","botania:astrolabe","sophisticatedbackpacks:stack_upgrade_tier_1","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","ae2:network/cables/dense_covered_light_blue","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","railways:crafting/smokestack_long","botania:dye_cyan","connectedglass:tinted_borderless_glass_red2","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","connectedglass:scratched_glass_gray2","immersiveengineering:crafting/strip_curtain","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","alltheores:platinum_ingot_from_raw","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","computercraft:turtle_normal_overlays/turtle_trans_overlay","ae2:tools/certus_quartz_cutting_knife","mcwfurnitures:spruce_counter","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:white_bed","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:spruce_slab","dyenamics:wine_terracotta","cfm:dye_green_picket_gate","minecraft:cobblestone_slab_from_cobblestone_stonecutting","sophisticatedstorage:jungle_chest","botania:corporea_spark","productivebees:hives/advanced_acacia_canvas_hive","occultism:smelting/burnt_otherstone","mcwbiomesoplenty:palm_highley_gate","bigreactors:blasting/yellorium_from_raw","mekanism:electrolytic_separator","mcwroofs:black_attic_roof","mcwfurnitures:stripped_jungle_large_drawer","mcwfences:mesh_metal_fence","supplementaries:soap/piston","sophisticatedbackpacks:chipped/tinkering_table_upgrade","botania:red_string_interceptor","botania:clip","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","undergarden:depthrock_wall_stonecutting","botania:speed_up_belt","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","twilightdelight:obsidian","mcwwindows:prismarine_pane_window","mcwpaths:brick_windmill_weave_path","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","twigs:cracked_bricks","productivebees:expansion_boxes/expansion_box_jungle_canvas","supplementaries:slice_map","appflux:insulating_resin","connectedglass:borderless_glass_green_pane2","mcwdoors:acacia_mystic_door","alltheores:aluminum_dust_from_hammer_ingot_crushing","travelersbackpack:fox_smithing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","blue_skies:trough","botania:lens_normal","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","mcwroofs:bricks_steep_roof","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","minecraft:cooked_chicken_from_campfire_cooking","railcraft:overalls","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","blue_skies:anvil_compat","create:brass_bars_from_ingots_brass_stonecutting","railcraft:steel_helmet","botania:conversions/yellow_petal_block_deconstruct","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","minecraft:observer","minecraft:mossy_cobblestone_wall_from_mossy_cobblestone_stonecutting","cfm:gray_kitchen_sink","mcwfences:bamboo_stockade_fence","mcwbiomesoplenty:hellbark_waffle_door","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","immersiveengineering:crafting/hoe_steel","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","connectedglass:clear_glass_white_pane2","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","cfm:white_picket_fence","productivebees:stonecutter/dark_oak_canvas_hive","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","botania:light_blue_shiny_flower","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:red_terracotta_steep_roof","utilitix:oak_shulker_boat","cfm:white_trampoline","handcrafted:oak_desk","xnet:connector_blue_dye","rftoolsbuilder:shape_card_liquid","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwbiomesoplenty:dead_stable_head_door","mcwfurnitures:stripped_oak_desk","minecraft:gray_dye","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","croptopia:candied_nuts","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","botania:travel_belt","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwwindows:warped_pane_window","minecraft:emerald_block","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","mcwwindows:stripped_acacia_log_window2","blue_skies:aquite_sword","aiotbotania:livingrock_pickaxe","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","handcrafted:spruce_desk","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","occultism:crafting/golden_sacrificial_bowl","ae2:network/blocks/interfaces_interface_alt","mcwfurnitures:jungle_table","domum_ornamentum:blue_cobblestone_extra","botania:runic_altar","railways:crafting/smokestack_caboosestyle","additionallanterns:normal_lantern_red","mcwbiomesoplenty:stripped_mahogany_wardrobe","domum_ornamentum:purple_brick_extra","mcwwindows:one_way_glass_pane","mcwbiomesoplenty:hellbark_stable_door","comforts:hammock_red","mcwpaths:andesite_dumble_paving","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","productivebees:stonecutter/maple_canvas_hive","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwdoors:acacia_barn_glass_door","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","botania:green_petal_block","supplementaries:candle_holders/candle_holder_green_dye","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","supplementaries:planter_rich","minecolonies:blockconstructiontape","create:crafting/kinetics/millstone","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwfences:railing_red_sandstone_wall","sophisticatedstorage:spruce_chest","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:cyan_sofa","productivebees:expansion_boxes/expansion_box_birch_canvas","travelersbackpack:dye_white_sleeping_bag","dyenamics:wine_stained_glass_pane_from_glass_pane","mcwroofs:gutter_base_white","mcwbridges:jungle_rail_bridge","enderio:resetting_lever_ten_from_prev","mcwroofs:magenta_concrete_upper_lower_roof","botania:lens_flare","sophisticatedstorage:storage_stack_upgrade_tier_5_from_backpack_stack_upgrade_tier_4","reliquary:crimson_cloth","mcwroofs:cyan_concrete_steep_roof","pneumaticcraft:gilded_upgrade","cfm:stripped_acacia_upgraded_gate","mcwroofs:spruce_planks_upper_lower_roof","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","cfm:stripped_spruce_coffee_table","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwroofs:red_concrete_steep_roof","mcwbiomesoplenty:magic_bark_glass_door","aiotbotania:livingwood_axe","xnet:connector_yellow_dye","rftoolspower:powercell_card","reliquary:alkahestry_altar","fluxnetworks:wipe_fluxplug","mcwwindows:crimson_stem_window","mcwdoors:acacia_waffle_door","mcwlights:yellow_lamp","botania:blue_pavement","mcwbiomesoplenty:mahogany_bridge_pier","supplementaries:timber_frame","sophisticatedstorage:packing_tape","enderio:resetting_lever_thirty_from_inv","mekanism:control_circuit/ultimate","mcwlights:soul_oak_tiki_torch","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","railcraft:tin_gear","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","ad_astra:white_flag","travelersbackpack:melon_smithing","mcwtrpdoors:oak_swamp_trapdoor","cfm:spruce_upgraded_gate","aether:fishing_rod_repairing","biomesoplenty:pine_boat","mcwfurnitures:jungle_drawer_counter","mcwroofs:stone_steep_roof","mekanism:steel_casing","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","aiotbotania:terra_shovel","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","occultism:crafting/book_of_binding_foliot","domum_ornamentum:light_gray_brick_extra","mcwroofs:light_gray_roof_block","mcwroofs:magenta_terracotta_lower_roof","ad_astra:vent","utilitarian:fluid_hopper","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:stone_attic_roof","immersiveengineering:crafting/concrete","rftoolsbuilder:blue_shield_template_block","botania:conversions/dragonstone_block_deconstruct","rftoolsbuilder:space_chamber","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwwindows:metal_window","mcwbiomesoplenty:mahogany_swamp_trapdoor","minecraft:glass_pane","mcwbiomesoplenty:maple_barn_glass_door","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","simplylight:illuminant_yellow_block_toggle","minecraft:clay","ae2:network/crafting/cpu_crafting_unit","mcwbiomesoplenty:pine_pane_window","ad_astra:blue_flag","mcwroofs:magenta_concrete_roof","create:crafting/kinetics/mechanical_plough","enderio:wood_gear","travelersbackpack:light_blue_sleeping_bag","mcwtrpdoors:spruce_bark_trapdoor","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","ae2:network/blocks/interfaces_interface_part","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwbiomesoplenty:mahogany_paper_door","ae2:materials/annihilationcore","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","minecraft:leather_boots","railcraft:tank_detector","railcraft:steel_spike_maul","pneumaticcraft:classify_filter","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","mcwbridges:mossy_stone_brick_bridge","dyenamics:persimmon_dye","productivebees:stonecutter/birch_canvas_expansion_box","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwbiomesoplenty:magic_nether_door","pneumaticcraft:wall_lamp_inverted_light_blue","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","minecolonies:supplychestdeployer","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","minecraft:dye_black_carpet","tombstone:white_marble","ae2:network/cables/covered_purple","expatternprovider:ebus_out","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","enderio:resetting_lever_thirty_inv","botania:petal_blue","mcwbiomesoplenty:pine_pyramid_gate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","aether:aether_iron_nugget_from_smelting","mcwbiomesoplenty:mahogany_planks_top_roof","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbridges:mossy_cobblestone_bridge_stair","mcwfences:modern_granite_wall","productivebees:stonecutter/grimwood_canvas_hive","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_modern_wardrobe","mcwbiomesoplenty:fir_barn_door","simplylight:illuminant_block_on","botania:petal_light_gray_double","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:mahogany_coffee_table","biomesoplenty:dead_boat","mysticalagriculture:inferium_gemstone","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","mcwbiomesoplenty:willow_japanese2_door","mcwwindows:andesite_window","domum_ornamentum:brick_extra","modularrouters:activator_module","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","ae2:network/cables/dense_smart_purple","minecraft:blaze_powder","cfm:brown_kitchen_drawer","minecraft:chain","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","mcwfences:crimson_highley_gate","mcwbiomesoplenty:pine_barn_glass_door","mcwpaths:mossy_stone_running_bond","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","immersiveengineering:crafting/blueprint_bullets","handcrafted:white_crockery_combo","mcwroofs:magenta_concrete_lower_roof","supplementaries:dispenser_minecart","buildinggadgets2:gadget_destruction","comforts:hammock_light_blue","minecraft:deepslate","mcwroofs:stone_roof","travelersbackpack:wither","mcwdoors:jungle_mystic_door","mcwroofs:pink_terracotta_top_roof","handcrafted:light_blue_cushion","cfm:green_kitchen_counter","minecraft:calibrated_sculk_sensor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:fir_mystic_door","railcraft:bronze_gear","mcwbiomesoplenty:fir_beach_door","ae2:tools/certus_quartz_spade","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","ae2:network/cables/dense_covered_black","undergarden:depthrock_stairs_stonecutting","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","occultism:crafting/spirit_campfire","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","mcwpaths:mossy_cobblestone_basket_weave_paving","minecraft:green_terracotta","delightful:knives/brass_knife","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","supplementaries:biomesoplenty/sign_post_mahogany","domum_ornamentum:white_brick_extra","supplementaries:gold_door","ae2:misc/fluixpearl","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","sfm:labelgun","mcwbiomesoplenty:mahogany_lower_roof","mcwfences:jungle_hedge","reliquary:angelic_feather","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","occultism:crafting/book_of_calling_foliot_lumberjack","immersiveengineering:crafting/clinker_brick_quoin","aether:aether_tune_enchanting","supplementaries:altimeter","gtceu:smelting/smelt_dust_bronze_to_ingot","ae2:network/cables/dense_smart_gray","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwbridges:rope_spruce_bridge","supplementaries:end_stone_lamp","allthecompressed:compress/stone_1x","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","cfm:stripped_jungle_desk","botania:manasteel_shovel","handcrafted:oven","minecraft:quartz_from_blasting","mcwbiomesoplenty:stripped_mahogany_table","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","thermal_extra:smelting/soul_infused_ingot_from_dust_smelting","supplementaries:candle_holders/candle_holder_blue","rftoolsbuilder:shape_card_quarry_fortune","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","immersiveengineering:crafting/plate_aluminum_hammering","delightful:knives/bronze_knife","travelersbackpack:quartz","mcwbiomesoplenty:mahogany_classic_door","additionallanterns:cobbled_deepslate_chain","allthecompressed:compress/sand_1x","minecraft:gold_nugget","bigreactors:energizer/controller","mcwbiomesoplenty:stripped_maple_log_four_window","botania:super_travel_belt","mcwwindows:stripped_dark_oak_log_window","mcwbiomesoplenty:stripped_mahogany_double_drawer","occultism:crafting/spirit_attuned_pickaxe_head","mekanism:processing/osmium/ingot/from_raw_smelting","allthecompressed:compress/andesite_1x","sophisticatedbackpacks:stack_upgrade_tier_1_from_starter","mcwbiomesoplenty:willow_nether_door","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","mcwpaths:brick_flagstone","mekanism:tier_installer/basic","minecraft:blast_furnace","pneumaticcraft:reinforced_stone_from_slab","additionallanterns:smooth_stone_lantern","additionallanterns:normal_lantern_light_blue","mcwbiomesoplenty:pine_plank_four_window","securitycraft:electrified_iron_fence","minecraft:light_blue_stained_glass_pane_from_glass_pane","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","ae2:network/parts/import_bus","minecraft:dye_red_bed","mcwdoors:acacia_stable_door","mcwbiomesoplenty:mahogany_bookshelf_cupboard","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","minecraft:blue_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_cupboard_counter","silentgear:crimson_iron_ingot_from_nugget","mcwbiomesoplenty:hellbark_cottage_door","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","silentgear:upgrade_base","ae2:network/cables/glass_pink","ae2:network/cables/smart_cyan","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","rftoolsbuilder:space_chamber_controller","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","minecraft:mossy_cobblestone_wall","mcwroofs:light_gray_lower_roof","mcwwindows:one_way_glass","minecraft:spyglass","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mekanism:electrolytic_core","minecraft:pink_stained_glass","cfm:mangrove_mail_box","minecraft:oak_chest_boat","mcwpaths:mossy_stone_flagstone_path","travelersbackpack:ghast","handcrafted:oak_dining_bench","twigs:tuff_stairs","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","botania:conversions/brown_petal_block_deconstruct","mcwbiomesoplenty:hellbark_classic_door","mcwroofs:spruce_planks_lower_roof","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:dead_glass_door","connectedglass:borderless_glass_orange2","ad_astra:fuel_refinery","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","pneumaticcraft:logistics_core","mcwbiomesoplenty:willow_modern_door","comforts:hammock_blue","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","mcwdoors:spruce_swamp_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","minecraft:bread","mcwdoors:spruce_barn_glass_door","mcwbiomesoplenty:redwood_western_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","mcwfurnitures:spruce_coffee_table","mcwbiomesoplenty:dead_classic_door","botania:horn_grass","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","dyenamics:peach_wool","mcwbiomesoplenty:umbran_swamp_door","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","allthemodium:allthemodium_ingot_from_block","minecraft:dark_oak_chest_boat","additionallanterns:normal_lantern_brown","minecraft:iron_shovel","tombstone:dark_marble","ironfurnaces:furnaces/diamond_furnace","cfm:lime_kitchen_sink","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","chimes:amethyst_chimes","ae2:smelting/smooth_sky_stone_block","mcwbiomesoplenty:mahogany_cottage_door","productivebees:stonecutter/willow_canvas_hive","domum_ornamentum:white_floating_carpet","securitycraft:track_mine","ad_astra:space_suit","mcwbiomesoplenty:willow_swamp_door","mcwdoors:acacia_glass_door","pneumaticcraft:camo_applicator","mcwbiomesoplenty:fir_stable_head_door","alltheores:osmium_dust_from_hammer_ingot_crushing","cfm:light_blue_picket_fence","railcraft:polished_quarried_stone_from_quarried_stone_in_stonecutter","deeperdarker:echo_boat","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","twigs:smooth_basalt_brick_wall_from_smooth_basalt_stonecutting","mcwroofs:andesite_upper_lower_roof","mythicbotany:ice_ring","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","minecraft:oak_door","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwbiomesoplenty:maple_cottage_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","mcwbiomesoplenty:empyreal_modern_door","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","handcrafted:spruce_dining_bench","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","ad_astra:coal_generator","mcwroofs:spruce_planks_roof","mcwbiomesoplenty:stripped_hellbark_log_four_window","botania:petal_purple","minecraft:spruce_door","sophisticatedbackpacks:smelting_upgrade","botania:mana_quartz_stairs","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","modularrouters:dropper_module","mcwbiomesoplenty:magic_highley_gate","botania:balance_cloak","mcwroofs:yellow_concrete_upper_lower_roof","dyenamics:mint_terracotta","ae2:network/cables/glass_magenta","supplementaries:candle_holders/candle_holder","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","biomesoplenty:fir_chest_boat","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","supplementaries:wrench","aether:skyroot_smithing_table","securitycraft:reinforced_oak_fence_gate","delightful:knives/aluminum_knife","bigreactors:energizer/chargingport_fe","mcwbiomesoplenty:stripped_redwood_log_four_window","cfm:purple_kitchen_sink","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","mcwpaths:mossy_stone_crystal_floor_slab","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","botania:tiny_planet","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","connectedglass:borderless_glass_brown2","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:pine_stable_door","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:granite","minecraft:melon_seeds","cfm:dye_cyan_picket_fence","minecraft:black_dye_from_wither_rose","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwbiomesoplenty:magic_barn_glass_door","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwbiomesoplenty:fir_bamboo_door","mcwroofs:andesite_roof","rftoolsbase:infused_enderpearl","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwwindows:deepslate_pane_window","minecraft:dye_yellow_carpet","ae2:network/cables/glass_yellow","minecraft:dye_white_wool","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","chemlib:manganese_block_to_ingot","dyenamics:rose_terracotta","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","handcrafted:silverfish_trophy","cfm:yellow_kitchen_counter","alltheores:signalum_plate","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","cfm:lime_kitchen_counter","mcwbridges:mossy_cobblestone_bridge","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","forbidden_arcanus:deorum_soul_lantern","securitycraft:speed_module","mcwdoors:garage_black_door","botania:mana_ring_greater","blue_skies:glowing_poison_stone","mcwdoors:oak_barn_door","botania:alchemy_catalyst","mcwbiomesoplenty:mahogany_barred_trapdoor","mcwbiomesoplenty:stripped_mahogany_bookshelf_drawer","cfm:cyan_kitchen_drawer","mcwfurnitures:spruce_double_drawer_counter","mcwpaths:stone_flagstone_path","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","minecraft:cobbled_deepslate_wall","supplementaries:timber_cross_brace","botania:lava_pendant","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","undergarden:depthrock_slab","minecraft:smooth_stone","twigs:schist_slab","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","botania:conversions/elementium_block_deconstruct","rftoolsutility:environmental_controller","mcwroofs:cyan_concrete_attic_roof","silentgear:crimson_iron_block","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","mcwbiomesoplenty:stripped_mahogany_desk","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","mcwbiomesoplenty:stripped_mahogany_large_drawer","mcwroofs:white_concrete_attic_roof","xnet:controller","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","botania:hourglass","alltheores:lead_plate","reliquary:uncrafting/ender_pearl","additionallanterns:obsidian_chain","minecraft:ender_eye","mcwfurnitures:stripped_spruce_coffee_table","mcwfurnitures:spruce_double_wardrobe","gtceu:shaped/hammer_steel","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","rftoolsbase:manual","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","ae2:network/blocks/io_condenser","sophisticatedbackpacks:smoking_upgrade","additionallanterns:normal_lantern_purple","mcwpaths:brick_clover_paving","minecraft:dye_black_wool","securitycraft:trophy_system","botania:elementium_axe","ad_astra:small_green_industrial_lamp","minecraft:cooked_beef","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","cfm:green_trampoline","mcwlights:yellow_paper_lamp","allthemodium:allthemodium_ingot_from_raw_blasting","forbidden_arcanus:arcane_chiseled_darkstone","twigs:dandelion_paper_lantern","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","dyenamics:wine_stained_glass","minecraft:gold_nugget_from_smelting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","ad_astra:space_pants","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","occultism:smelting/iesnium_ingot_from_raw","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwroofs:pink_striped_awning","mcwbiomesoplenty:mahogany_japanese_door","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:umbran_four_panel_door","mcwdoors:garage_white_door","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","ae2:network/cells/item_storage_components_cell_1k_part","mcwroofs:oak_planks_top_roof","evilcraft:crafting/dark_tank","mcwdoors:spruce_japanese2_door","mcwroofs:oak_planks_roof","enderio:fluid_tank","botania:petal_white","bigreactors:energizer/casing","supplementaries:blackstone_tile","minecraft:enchanting_table","mcwbiomesoplenty:magic_japanese_door","botania:mana_tablet","cfm:birch_mail_box","farmersdelight:roast_chicken_block","mcwbiomesoplenty:mahogany_striped_chair","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","deeperdarker:reinforced_echo_shard","mcwroofs:blue_terracotta_lower_roof","deepresonance:radiation_suit_chestplate","ad_astra:small_blue_industrial_lamp","allthearcanistgear:unobtainium_boots_smithing","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","mcwpaths:mossy_stone_running_bond_path","aether:skyroot_note_block","mcwbiomesoplenty:maple_tropical_door","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","pneumaticcraft:logistics_configurator","bigreactors:smelting/yellorium_from_raw","rftoolsutility:dialing_device","sophisticatedbackpacks:chipped/mason_table_upgrade","botania:dreamwood_planks","allthecompressed:compress/diamond_block_1x","sfm:disk","botania:alfheim_portal","dyenamics:banner/lavender_banner","botania:ender_hand","modularrouters:energy_distributor_module","handcrafted:spider_trophy","mcwroofs:lime_terracotta_lower_roof","ae2:network/cables/dense_smart_green","mcwlights:chain_wall_lantern","cfm:jungle_coffee_table","xnet:connector_green","ad_astra:light_blue_flag","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","securitycraft:reinforced_white_stained_glass","sophisticatedstorage:backpack_advanced_magnet_upgrade_from_storage_advanced_magnet_upgrade","mythicbotany:central_rune_holder","botania:missile_rod","create:crafting/kinetics/light_gray_seat","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","productivebees:stonecutter/dark_oak_canvas_expansion_box","mcwpaths:cobbled_deepslate_running_bond_slab","mcwbiomesoplenty:stripped_mahogany_chair","productivetrees:wood/soul_tree_wood","buildinggadgets2:gadget_cut_paste","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","rftoolsbuilder:mover_status","mcwfurnitures:spruce_drawer","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","handcrafted:cyan_sheet","twigs:polished_rhyolite_stonecutting","supplementaries:candle_holders/candle_holder_green","aether:skyroot_fletching_table","create:crafting/kinetics/gearbox","botania:terrasteel_block","mcwbiomesoplenty:umbran_modern_door","handcrafted:spruce_side_table","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","minecraft:light_blue_bed","mcwroofs:white_roof_slab","energymeter:meter","mcwroofs:purple_concrete_upper_lower_roof","comforts:sleeping_bag_to_blue","supplementaries:bubble_blower","botania:dye_black","dyenamics:conifer_stained_glass_pane_from_glass_pane","minecraft:recovery_compass","occultism:crafting/book_of_binding_marid","minecraft:magenta_dye_from_blue_red_white_dye","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","productivebees:stonecutter/fir_canvas_expansion_box","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","mcwbiomesoplenty:willow_bark_glass_door","aiotbotania:livingwood_hoe","minecraft:pink_terracotta","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","rftoolsutility:destination_analyzer","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:mahogany_curved_gate","mcwtrpdoors:spruce_barred_trapdoor","cfm:spruce_upgraded_fence","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/cables/smart_fluix_clean","ae2:network/parts/annihilation_plane_alt","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","securitycraft:camera_monitor","immersiveengineering:crafting/clinker_brick_sill","mcwbiomesoplenty:umbran_curved_gate","mcwroofs:black_top_roof","mcwfences:modern_nether_brick_wall","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","cfm:jungle_kitchen_sink_light","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","cfm:stripped_spruce_bedside_cabinet","connectedglass:borderless_glass_purple2","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","sophisticatedstorage:controller","biomesoplenty:palm_chest_boat","mcwbiomesoplenty:redwood_bark_glass_door","simplylight:illuminant_cyan_block_on_dyed","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwroofs:magenta_concrete_upper_steep_roof","minecraft:tinted_glass","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:lodestone","mcwlights:black_lamp","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwdoors:spruce_four_panel_door","mcwbiomesoplenty:mahogany_window","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:quartz","connectedglass:borderless_glass_red2","constructionwand:core_destruction","mcwfences:oak_pyramid_gate","handcrafted:terracotta_thick_pot","mcwbiomesoplenty:mahogany_modern_chair","enderio:extraction_speed_upgrade_1","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","mcwbiomesoplenty:hellbark_beach_door","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","undergarden:depthrock_button","ae2:network/parts/terminals","securitycraft:keypad_frame","enderio:resetting_lever_thirty_inv_from_prev","securitycraft:whitelist_module","ae2:network/blocks/energy_energy_acceptor","pneumaticcraft:vortex_tube","mcwwindows:warped_louvered_shutter","undergarden:wigglewood_chest_boat","xnet:connector_routing","minecraft:blue_carpet","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","comforts:sleeping_bag_cyan","mcwroofs:green_terracotta_attic_roof","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:palm_bark_glass_door","securitycraft:reinforced_black_stained_glass_pane_from_glass","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","dyenamics:ultramarine_candle","mcwfurnitures:stripped_spruce_double_drawer","mcwroofs:cyan_terracotta_steep_roof","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwbiomesoplenty:pine_stable_head_door","minecraft:cooked_chicken_from_smoking","pneumaticcraft:search_upgrade","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","cfm:stripped_acacia_park_bench","ae2:network/blocks/storage_chest","cfm:jungle_desk","minecraft:mangrove_chest_boat","mcwbiomesoplenty:stripped_mahogany_drawer","minecraft:iron_hoe","mekanismgenerators:generator/wind","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","mcwbiomesoplenty:palm_japanese2_door","mcwbiomesoplenty:mahogany_bark_trapdoor","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","minecraft:jungle_trapdoor","croptopia:mashed_potatoes","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","sophisticatedbackpacks:filter_upgrade","mcwbiomesoplenty:maple_japanese_door","aether:skyroot_barrel","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","ae2:network/cables/dense_covered_purple","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","allthetweaks:nether_star_block","undergarden:depthrock_bricks_stonecutting","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","blue_skies:ventium_block","productivebees:hives/advanced_oak_canvas_hive","minecraft:target","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwroofs:green_concrete_upper_lower_roof","allthearcanistgear:unobtainium_hat_smithing","handcrafted:light_blue_sheet","mcwtrpdoors:jungle_swamp_trapdoor","xnet:wireless_router","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","mcwwindows:dark_prismarine_brick_gothic","additionallanterns:normal_lantern_pink","mcwroofs:black_roof_slab","botania:mana_detector","mcwfurnitures:oak_double_drawer_counter","mcwroofs:orange_terracotta_top_roof","pneumaticcraft:compressed_bricks_from_tile_stonecutting","travelersbackpack:gold_tier_upgrade","mcwfences:mesh_metal_fence_gate","xnet:netcable_yellow","mcwroofs:blue_terracotta_roof","mcwfurnitures:jungle_wardrobe","cfm:red_sofa","dyenamics:navy_wool","mcwtrpdoors:metal_full_trapdoor","mcwroofs:spruce_top_roof","supplementaries:bomb","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","rftoolscontrol:tank","additionallanterns:normal_lantern_orange","utilitix:mob_bell","mcwbiomesoplenty:willow_beach_door","mcwroofs:green_terracotta_upper_lower_roof","mcwpaths:andesite_running_bond_slab","appbot:mana_cell_housing","minecraft:chiseled_stone_bricks_stone_from_stonecutting","utilitarian:utility/oak_logs_to_boats","mcwbiomesoplenty:fir_hedge","chemlib:sodium_ingot_from_blasting_sodium_dust","minecraft:netherite_sword_smithing","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","allthecompressed:compress/allthemodium_block_1x","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwdoors:metal_hospital_door","railcraft:signal_circuit","twilightforest:charm_of_life_2","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:dye_conifer_carpet","dyenamics:bubblegum_stained_glass","sophisticatedstorage:oak_chest_from_vanilla_chest","immersiveengineering:crafting/gunpart_barrel","immersiveengineering:crafting/screwdriver","connectedglass:borderless_glass_pane3","connectedglass:borderless_glass_pane1","cfm:pink_cooler","cfm:stripped_acacia_kitchen_counter","mcwfurnitures:oak_double_drawer","mcwfences:railing_blackstone_wall","minecraft:stone_bricks_from_stone_stonecutting","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","ad_astra:blue_industrial_lamp","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","mcwbiomesoplenty:maple_modern_door","cfm:birch_kitchen_sink_light","mcwbiomesoplenty:mahogany_planks_upper_lower_roof","croptopia:chicken_and_dumplings","mcwfences:modern_sandstone_wall","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","utilitix:tiny_charcoal_to_tiny","railcraft:polished_quarried_stone_from_quarried_stone","connectedglass:borderless_glass_light_blue2","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","chemlib:calcium_block_to_ingot","mcwroofs:blue_concrete_roof","alltheores:smelting_dust/osmium_ingot","travelersbackpack:bee","mcwbiomesoplenty:fir_plank_window","minecraft:dye_gray_bed","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","mcwbridges:spruce_rail_bridge","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwwindows:crimson_shutter","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","pneumaticcraft:air_cannon","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","ad_astra:engine_frame","mcwpaths:cobblestone_clover_paving","minecraft:blue_candle","ae2:network/cables/dense_covered_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","botania:red_string_relay","ae2:tools/portable_item_cell_256k","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwroofs:purple_terracotta_upper_lower_roof","mcwfurnitures:spruce_stool_chair","mcwroofs:gray_striped_awning","biomesoplenty:orange_dye_from_burning_blossom","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","blue_skies:cake_compat","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_bricks_stonecutting","mcwpaths:mossy_stone_windmill_weave_path","minecraft:gold_ingot_from_blasting_raw_gold","productivebees:stonecutter/aspen_canvas_expansion_box","mythicbotany:kvasir_mead","gtceu:smelting/smelt_dust_iron_to_ingot","handcrafted:wood_cup","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","ad_astra:ti_69","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","botania:shimmerwood_planks","mcwbiomesoplenty:mahogany_nether_door","mcwbiomesoplenty:hellbark_window2","rftoolsbuilder:shape_card_quarry","mcwbiomesoplenty:magic_hedge","mcwbiomesoplenty:mahogany_barrel_trapdoor","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime2","cfm:orange_trampoline","mcwroofs:lime_concrete_steep_roof","botania:mana_spreader","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:redwood_waffle_door","handcrafted:white_cup","botania:slingshot","ae2:network/cables/glass_gray","botania:dye_green","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","botania:lens_tripwire","mcwlights:light_gray_lamp","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwfences:deepslate_brick_grass_topped_wall","mcwwindows:andesite_window2","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","mcwfurnitures:stripped_spruce_double_wardrobe","railcraft:steel_leggings","mekanism:energized_smelter","mcwbiomesoplenty:umbran_nether_door","securitycraft:reinforced_spruce_fence","minecraft:piston","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","allthemodium:allthemodium_ingot_from_raw_smelting","mcwbiomesoplenty:dead_plank_four_window","undergarden:regalium_block","connectedglass:borderless_glass_red_pane2","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","botania:mana_bomb","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwbiomesoplenty:dead_modern_door","botania:third_eye","productivetrees:wood/brown_amber_wood","mcwdoors:acacia_cottage_door","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","chemlib:potassium_ingot_to_block","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwbiomesoplenty:magic_modern_door","handcrafted:stackable_book","mcwroofs:andesite_attic_roof","securitycraft:reinforced_green_stained_glass","mcwtrpdoors:spruce_mystic_trapdoor","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","productivebees:stonecutter/jacaranda_canvas_expansion_box","mcwbiomesoplenty:stripped_mahogany_glass_table","mcwroofs:red_terracotta_upper_lower_roof","aether:blue_cape_light_blue_wool","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","blue_skies:aquite_hoe","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","travelersbackpack:skeleton","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","productivebees:stonecutter/oak_canvas_expansion_box","farmersdelight:stove","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","minecraft:light_blue_carpet","mcwwindows:birch_shutter","mcwfurnitures:stripped_jungle_wardrobe","undergarden:smelt_gloomper_leg","mcwbiomesoplenty:rainbow_birch_hedge","botania:spawner_mover","productivebees:expansion_boxes/expansion_box_crimson_canvas","immersiveengineering:crafting/pickaxe_steel","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","travelersbackpack:diamond","mcwpaths:mossy_stone_crystal_floor_stairs","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","sfm:water_tank","mcwfences:crimson_wired_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","botania:magenta_shiny_flower","mythicbotany:yggdrasil_branch","mcwroofs:red_terracotta_attic_roof","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","computercraft:pocket_computer_advanced","dyenamics:bed/ultramarine_bed_frm_white_bed","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","cfm:light_gray_grill","cfm:oak_desk","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:hellbark_hedge","supplementaries:daub_frame","botania:manasteel_sword","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:pine_mystic_door","mcwbiomesoplenty:fir_paper_door","chemlib:sodium_ingot_from_smelting_sodium_dust","mcwfurnitures:jungle_double_drawer","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","minecraft:green_stained_glass_pane_from_glass_pane","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","mcwwindows:cobblestone_arrow_slit","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","botania:brown_petal_block","ae2:network/cables/smart_gray","ae2:network/cables/dense_covered_white","mcwpaths:andesite_crystal_floor_stairs","modularrouters:distributor_module","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","create:crafting/logistics/pulse_extender","aether:golden_pendant","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","rftoolsutility:matter_beamer","ae2:decorative/quartz_fixture","mcwbiomesoplenty:dead_nether_door","mcwwindows:cherry_plank_window2","botania:sextant","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:stripped_mahogany_triple_drawer","cfm:dye_blue_picket_gate","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","mcwlights:copper_double_candle_holder","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","twigs:polished_schist_bricks_from_schist_stonecutting","mcwfences:diorite_grass_topped_wall","croptopia:shaped_fish_and_chips","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","pneumaticcraft:tube_junction","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","allthemodium:unobtainium_gear","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","alltheores:constantan_ingot_from_dust","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bloodmagic:alchemy_table","simplylight:illuminant_brown_block_dyed","cfm:stripped_acacia_table","bloodmagic:blood_rune_speed","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","mcwroofs:lime_terracotta_upper_lower_roof","mcwbiomesoplenty:maple_mystic_door","mekanism:processing/osmium/raw/from_raw_block","mcwpaths:mossy_stone_running_bond_stairs","expatternprovider:silicon_block","minecraft:item_frame","mekanism:tier_installer/elite","itemcollectors:basic_collector","enderio:resetting_lever_three_hundred_inv","mcwtrpdoors:oak_bark_trapdoor","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","travelersbackpack:cow","alltheores:brass_dust_from_alloy_blending","handcrafted:skeleton_trophy","handcrafted:golden_wide_pot","rftoolsutility:tank","aether:netherite_gloves_smithing","mcwbiomesoplenty:mahogany_stable_head_door","mcwpaths:brick_flagstone_slab","utilitarian:utility/spruce_logs_to_trapdoors","supplementaries:flags/flag_green","undergarden:smogstem_boat","computercraft:computer_advanced","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","reliquary:uncrafting/slime_ball","enderio:redstone_alloy_nugget_to_ingot","quark:building/crafting/furnaces/cobblestone_furnace","sophisticatedstorage:generic_chest","railways:crafting/smokestack_woodburner","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","handcrafted:wood_plate","securitycraft:reinforced_birch_fence_gate","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","cfm:dye_red_picket_fence","travelersbackpack:horse","cfm:brown_cooler","xnet:netcable_red","delightful:knives/terra_knife","mcwfences:diorite_pillar_wall","mcwwindows:dark_prismarine_parapet","productivebees:hives/advanced_snake_block_canvas_hive","rftoolsutility:inventoryplus_module","minecraft:spruce_trapdoor","bigreactors:reprocessor/wasteinjector","dyenamics:amber_candle","undergarden:shard_torch","mcwfences:bamboo_picket_fence","handcrafted:goat_trophy","mcwpaths:cobbled_deepslate_flagstone","dyenamics:persimmon_stained_glass_pane_from_glass_pane","expatternprovider:ebus_in","mcwroofs:blue_striped_awning","pneumaticcraft:wall_lamp_red","botania:livingwood_wall","mcwbiomesoplenty:mahogany_modern_door","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","botania:yellow_shiny_flower","botania:fabulous_pool_upgrade","buildinggadgets2:gadget_exchanging","comforts:hammock_to_blue","mcwbiomesoplenty:jacaranda_picket_fence","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwtrpdoors:spruce_swamp_trapdoor","undergarden:catalyst","ae2:network/cables/covered_light_blue","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","minecraft:composter","aether:diamond_sword_repairing","undergarden:polished_depthrock","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","minecraft:coarse_dirt","mcwwindows:spruce_plank_pane_window","alltheores:signalum_rod","create:crafting/kinetics/white_seat","chemlib:gallium_ingot_to_block","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","mcwwindows:acacia_louvered_shutter","mcwdoors:print_whispering","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","tombstone:blue_marble","create:crafting/appliances/dough","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwlights:spruce_ceiling_fan_light","dyenamics:dye_spring_green_carpet","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","railways:crafting/smokestack_diesel","securitycraft:reinforced_light_gray_stained_glass","mcwfurnitures:stripped_spruce_stool_chair","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","connectedglass:borderless_glass_yellow2","mcwdoors:jungle_whispering_door","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","mcwlights:bamboo_tiki_torch","handcrafted:hammer","ad_astra:airlock","mcwroofs:grass_steep_roof","alchemistry:combiner","ae2:network/cables/glass_white","chemlib:magnesium_ingot_from_blasting_magnesium_dust","croptopia:egg_roll","connectedglass:borderless_glass_black2","mcwtrpdoors:oak_whispering_trapdoor","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","minecraft:lime_dye","mcwbiomesoplenty:pine_bamboo_door","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:rope_mahogany_bridge","supplementaries:flags/flag_purple","botania:quartz_sunny","productivebees:stonecutter/maple_canvas_expansion_box","botania:flighttiara_5","botania:flighttiara_4","botania:terrasteel_helmet","botania:flighttiara_7","botania:flighttiara_6","botania:flighttiara_1","botania:flighttiara_0","botania:flighttiara_3","botania:flighttiara_2","botania:flighttiara_8","mcwpaths:mossy_stone_windmill_weave_slab","tombstone:bone_needle","minecraft:stone_brick_wall","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","chemlib:manganese_ingot_from_blasting_manganese_dust","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","minecraft:cyan_dye","evilcraft:crafting/dark_stick","connectedglass:scratched_glass_white_pane2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","enderio:stone_gear_upgrade","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:gold_block","minecraft:spruce_boat","botania:floating_pure_daisy","mcwbiomesoplenty:maple_plank_pane_window","dyenamics:banner/navy_banner","handcrafted:jungle_counter","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","connectedglass:scratched_glass_cyan2","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","botania:magenta_petal_block","minecraft:copper_ingot_from_blasting_raw_copper","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","pneumaticcraft:pneumatic_wrench","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","twilightforest:canopy_chest_boat","mcwroofs:jungle_planks_attic_roof","mcwpaths:spruce_planks_path","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","mcwdoors:spruce_nether_door","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","allthemodium:unobtainium_rod","blue_skies:thwarted_armor_trim_smithing_template_smithing_trim","twigs:soul_lamp","mcwbiomesoplenty:dead_pane_window","utilitix:reinforced_rail","immersiveengineering:crafting/wirecoil_structure_rope","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","mcwbiomesoplenty:jacaranda_western_door","cfm:dye_green_picket_fence","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","littlelogistics:spring","allthecompressed:compress/jungle_planks_1x","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","mcwbiomesoplenty:mahogany_planks_path","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_middle_purple","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","supplementaries:candle_holders/candle_holder_yellow_dye","allthecompressed:compress/spruce_planks_1x","deeperdarker:warden_upgrade_smithing_template","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","productivebees:stonecutter/dead_canvas_expansion_box","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","productivebees:hives/advanced_jungle_canvas_hive","mcwroofs:orange_terracotta_upper_steep_roof","allthecompressed:compress/niotic_crystal_block_1x","farmersdelight:steak_and_potatoes","mcwtrpdoors:spruce_ranch_trapdoor","create:tuff_from_stone_types_tuff_stonecutting","mcwroofs:green_striped_awning","gtceu:shapeless/decompress_aluminium_from_ore_block","allthemodium:vibranium_plate","securitycraft:codebreaker","rftoolsutility:energy_module","cfm:light_blue_picket_gate","constructionwand:infinity_wand","botania:rainbow_rod","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","botania:redstone_spreader","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","alltheores:gold_plate","cfm:blue_kitchen_counter","dyenamics:spring_green_stained_glass_pane_from_glass_pane","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","mcwroofs:base_top_roof","ae2:network/cables/smart_magenta","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","occultism:crafting/book_of_binding_djinni","immersiveengineering:crafting/grit_sand","create:crafting/kinetics/item_vault","mythicbotany:mana_collector","mcwfences:warped_highley_gate","pneumaticcraft:minigun","megacells:network/cell_dock","supplementaries:statue","mcwbiomesoplenty:mahogany_tropical_door","minecraft:dye_green_carpet","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","mcwbiomesoplenty:fir_stockade_fence","twigs:schist_stairs","botania:conversions/terrasteel_from_nugget","ae2:network/blocks/pattern_providers_interface","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","alltheores:bronze_plate","botania:swap_ring","create:crafting/kinetics/depot","pneumaticcraft:wall_lamp_yellow","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","mcwbiomesoplenty:mahogany_rail_bridge","pneumaticcraft:pressure_chamber_wall","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","minecraft:cyan_carpet","alltheores:raw_iridium_block","domum_ornamentum:gray_floating_carpet","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","ad_astra:green_industrial_lamp","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","mcwbiomesoplenty:pine_nether_door","mcwbiomesoplenty:mahogany_barn_trapdoor","mcwbiomesoplenty:hellbark_barn_glass_door","deepresonance:radiation_monitor","botania:placeholder","botania:super_cloud_pendant","allthemodium:piglich_heart_block","bigreactors:smelting/graphite_from_charcoal","mcwroofs:green_concrete_top_roof","connectedglass:borderless_glass_cyan2","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","handcrafted:bricks_pillar_trim","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","botania:petal_yellow_double","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","immersiveengineering:crafting/wire_lead","botania:mana_quartz_slab","pneumaticcraft:compressed_brick_slab","mcwbiomesoplenty:redwood_four_panel_door","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","immersiveengineering:crafting/alu_wallmount","pylons:potion_filter","create:crafting/kinetics/magenta_seat","handcrafted:spruce_fancy_bed","botania:conversions/light_gray_petal_block_deconstruct","create:crafting/kinetics/brown_seat","littlelogistics:energy_tug","mcwwindows:acacia_log_parapet","mcwfurnitures:stripped_spruce_triple_drawer","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:fluorescent_stained_glass","alltheores:bronze_rod","twigs:rhyolite_slab","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","twigs:rhyolite_stairs","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","alltheores:aluminum_gear","pneumaticcraft:compressed_bricks_from_stone_stonecutting","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwwindows:cyan_curtain","ae2:network/cables/smart_yellow","handcrafted:golden_medium_pot","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","allthecompressed:compress/tuff_1x","mcwroofs:gray_terracotta_upper_steep_roof","botania:mana_glass_pane","sfm:fancy_to_cable","mcwbiomesoplenty:palm_western_door","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","xnet:router","mcwfurnitures:stripped_jungle_modern_chair","mcwbiomesoplenty:willow_classic_door","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","botania:terrasteel_chestplate","mcwroofs:orange_concrete_lower_roof","aether:golden_gloves_repairing","cfm:magenta_kitchen_counter","minecraft:barrel","utilitix:tiny_coal_to_tiny","supplementaries:boat_jar","create:tuff_pillar_from_stone_types_tuff_stonecutting","ae2:materials/basiccard","handcrafted:oak_table","handcrafted:white_plate","botania:laputa_shard","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwdoors:acacia_swamp_door","pneumaticcraft:safety_tube_module","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","productivebees:hives/advanced_birch_canvas_hive","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","mcwroofs:blue_concrete_attic_roof","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","minecraft:dye_gray_carpet","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","mekanism:control_circuit/elite","botania:petal_magenta_double","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:cauldron","botania:glimmering_livingwood_from_log","mcwwindows:stripped_mangrove_pane_window","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","mcwfurnitures:oak_kitchen_cabinet","ae2:network/cables/dense_covered_magenta","biomesoplenty:mahogany_fence_gate","mcwdoors:spruce_modern_door","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","tombstone:impregnated_diamond","dimstorage:dim_core","dyenamics:bed/navy_bed_frm_white_bed","bigreactors:smelting/graphite_from_dust","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwbiomesoplenty:dead_beach_door","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","chemlib:gallium_ingot_from_smelting_gallium_dust","utilitix:directional_rail","minecraft:popped_chorus_fruit","sophisticatedstorage:storage_void_upgrade_from_backpack_void_upgrade","mcwbiomesoplenty:fir_barn_glass_door","mekanism:factory/elite/infusing","mcwbiomesoplenty:umbran_window2","mcwfurnitures:oak_double_kitchen_cabinet","mythicbotany:gaia_pylon","undergarden:depthrock_brick_slab_stonecutting","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","minecraft:red_bed","botania:conversions/cyan_petal_block_deconstruct","minecraft:dye_gray_wool","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","dyenamics:dye_persimmon_carpet","mcwroofs:brown_terracotta_top_roof","utilitix:stone_wall","dyenamics:navy_dye","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwbiomesoplenty:jacaranda_swamp_door","mcwbiomesoplenty:mahogany_stool_chair","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","productivebees:stonecutter/concrete_canvas_hive","createoreexcavation:vein_finder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","aether:aether_gold_nugget_from_smelting","mcwwindows:light_blue_mosaic_glass_pane","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","mcwtrpdoors:spruce_tropical_trapdoor","mcwtrpdoors:print_cottage","botania:spark_upgrade_recessive","sophisticatedbackpacks:chipped/carpenters_table_upgrade","minecraft:copper_ingot_from_smelting_raw_copper","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","sophisticatedstorage:oak_limited_barrel_1","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","additionallanterns:normal_lantern_gray","domum_ornamentum:beige_bricks","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","railcraft:diamond_tunnel_bore_head","ae2:tools/nether_quartz_hoe","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","botania:spark_upgrade_isolated","mekanism:metallurgic_infuser","productivebees:stonecutter/aspen_canvas_hive","mcwlights:iron_framed_torch","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","alltheores:aluminum_rod","cfm:stripped_spruce_table","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","mcwwindows:spruce_plank_window","minecraft:spruce_stairs","minecraft:jungle_sign","minecraft:cyan_candle","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","expatternprovider:precise_storage_bus","occultism:crafting/book_of_calling_djinni_manage_machine","domum_ornamentum:cactus_extra","mekanism:factory/advanced/enriching","cfm:cyan_cooler","ae2:network/parts/formation_plane","railcraft:silver_gear","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwbiomesoplenty:mahogany_mystic_door","create:crafting/kinetics/mechanical_drill","mcwroofs:light_gray_roof","mcwbiomesoplenty:umbran_beach_door","mcwroofs:gutter_base_purple","mcwbiomesoplenty:umbran_bark_glass_door","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","mcwtrpdoors:mangrove_ranch_trapdoor","minecraft:slime_block","mcwwindows:blue_curtain","dyenamics:lavender_stained_glass_pane_from_glass_pane","chimes:glass_bells","mcwwindows:iron_shutter","mcwpaths:mossy_stone_crystal_floor","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","blue_skies:aquite_boots","immersiveengineering:crafting/sawblade","mcwbiomesoplenty:empyreal_swamp_door","modularrouters:flinger_module","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","sophisticatedstorage:basic_to_gold_tier_upgrade","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","minecraft:magenta_stained_glass_pane_from_glass_pane","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","alltheores:copper_rod","productivebees:stonecutter/cherry_canvas_hive","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwfurnitures:stripped_oak_double_drawer","botania:dragonstone_block","minecraft:spruce_planks","aquaculture:cooked_fish_fillet","cfm:orange_kitchen_sink","mcwbiomesoplenty:mahogany_attic_roof","croptopia:pork_jerky","enderio:resetting_lever_sixty_from_inv","minecraft:mossy_cobblestone_from_moss_block","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","botania:unholy_cloak","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","mcwbiomesoplenty:willow_western_door","ae2:network/cables/dense_covered_yellow","mcwpaths:cobbled_deepslate_running_bond_path","handcrafted:stone_corner_trim","delightful:knives/bone_knife","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","botania:ender_dagger","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","sophisticatedstorage:spruce_barrel","mcwwindows:mangrove_pane_window","productivebees:stonecutter/river_canvas_expansion_box","farmersdelight:flint_knife","chemlib:manganese_ingot_to_block","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwfurnitures:stripped_spruce_drawer","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","immersiveengineering:smoking/clinker_brick","handcrafted:white_sheet","ae2:network/cables/glass_purple","create:crafting/kinetics/steam_whistle","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","immersiveengineering:crafting/plate_steel_hammering","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","alltheores:aluminum_plate","aquaculture:gold_nugget_from_blasting","bloodmagic:synthetic_point","ad_astra:small_yellow_industrial_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","minecraft:redstone_lamp","mcwbiomesoplenty:stripped_umbran_pane_window","supplementaries:candle_holders/candle_holder_cyan_dye","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwbiomesoplenty:dead_barn_glass_door","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","botania:black_hole_talisman","botania:spark_upgrade_dominant","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwwindows:green_mosaic_glass_pane","mcwbiomesoplenty:pine_western_door","minecraft:dye_blue_bed","botania:auto_crafting_halo","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:mahogany_upper_lower_roof","dyenamics:banner/fluorescent_banner","croptopia:tortilla","undergarden:chiseled_depthrock_bricks_stonecutting","securitycraft:bouncing_betty","minecraft:painting","botania:avatar","connectedglass:tinted_borderless_glass_blue2","connectedglass:tinted_borderless_glass_white2","sophisticatedstorage:storage_stack_upgrade_tier_4_from_backpack_stack_upgrade_tier_3","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","alchemistry:fission_chamber_controller","railcraft:animal_detector","mcwbiomesoplenty:palm_stable_door","rftoolsbuilder:shield_block2","rftoolsbuilder:shield_block1","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwbiomesoplenty:palm_four_panel_door","aether:ice_from_bucket_freezing","productivebees:stonecutter/grimwood_canvas_expansion_box","pneumaticcraft:speed_upgrade","terralith:dropper_alt","mcwlights:white_paper_lamp","occultism:crafting/book_of_binding_empty","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","mcwwindows:mangrove_plank_pane_window","botania:dreamwood","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","create:crafting/kinetics/placard","chimes:iron_chimes","securitycraft:storage_module","alltheores:tin_plate","botania:dye_white","ae2:network/cables/dense_smart_brown","botania:conversions/red_petal_block_deconstruct","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","immersiveengineering:crafting/chute_copper","minecraft:cyan_bed","mcwfences:prismarine_grass_topped_wall","occultism:crafting/book_of_calling_foliot_transport_items","ad_astra:small_gray_industrial_lamp","mcwbiomesoplenty:mahogany_cottage_trapdoor","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","create:crafting/kinetics/crafter_slot_cover","twilightforest:mangrove_chest_boat","simplylight:illuminant_green_block_on_toggle","allthecompressed:compress/nitro_crystal_block_1x","travelersbackpack:wolf","productivebees:stonecutter/mahogany_canvas_expansion_box","forbidden_arcanus:stone_blacksmith_gavel","minecraft:brick","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","minecraft:music_disc_5","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:coal","mcwbiomesoplenty:mahogany_bamboo_door","minecraft:unobtainium_mage_boots_smithing","mcwroofs:blue_terracotta_upper_lower_roof","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","supplementaries:sack","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","mcwwindows:nether_brick_gothic","mcwbiomesoplenty:willow_mystic_door","allthemodium:unobtainium_ingot","mcwbiomesoplenty:pine_modern_door","minecraft:brown_dye","alltheores:silver_plate","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","cfm:lime_cooler","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","alltheores:copper_ingot_from_block","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","dyenamics:mint_dye","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","productivebees:stonecutter/cherry_canvas_expansion_box","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","mcwlights:iron_chandelier","mcwpaths:mossy_cobblestone_honeycomb_paving","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:prismarine_window2","utilitarian:tps_meter","utilitarian:utility/spruce_logs_to_doors","cfm:stripped_acacia_upgraded_fence","ae2:network/cables/smart_pink","mcwbiomesoplenty:pine_horse_fence","mcwpaths:mossy_stone_windmill_weave","mcwwindows:acacia_four_window","chemlib:potassium_ingot_from_blasting_potassium_dust","mcwwindows:warped_shutter","domum_ornamentum:yellow_floating_carpet","enderio:redstone_alloy_nugget","minecraft:blue_banner","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","botania:petal_light_blue","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","utilitarian:utility/green_dye","mcwfurnitures:spruce_modern_chair","mcwwindows:pink_mosaic_glass_pane","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","alltheores:gold_rod","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","farmersdelight:fried_egg","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","productivebees:expansion_boxes/expansion_box_bamboo_canvas","botania:dirt_rod","mcwfurnitures:stripped_oak_table","connectedglass:clear_glass_red2","pneumaticcraft:logistics_frame_default_storage","mcwbiomesoplenty:mahogany_table","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwdoors:jail_door","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","reliquary:mob_charm_fragments/enderman","pneumaticcraft:wall_lamp_inverted_gray","handcrafted:spruce_table","mcwpaths:brick_running_bond_path","rftoolsbuilder:shape_card_quarry_dirt","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:birch_blinds","cfm:spruce_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","mcwroofs:magenta_terracotta_upper_lower_roof","mcwdoors:spruce_tropical_door","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","minecraft:dye_cyan_wool","mcwbiomesoplenty:stripped_mahogany_counter","mcwbridges:bridge_lantern","mcwroofs:blackstone_top_roof","cfm:brown_trampoline","mcwdoors:oak_whispering_door","alltheores:signalum_ingot_from_dust","mcwwindows:jungle_window2","cfm:gray_cooler","everythingcopper:copper_axe","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","connectedglass:scratched_glass_green_pane2","mcwroofs:stone_upper_steep_roof","connectedglass:scratched_glass_white2","productivebees:expansion_boxes/expansion_box_spruce_canvas","simplylight:edge_light","mcwfences:railing_granite_wall","immersiveengineering:crafting/plate_uranium_hammering","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","mcwbiomesoplenty:stripped_pine_log_window","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","cfm:magenta_kitchen_drawer","xnet:redstoneproxy_update","cfm:stripped_acacia_mail_box","mcwroofs:spruce_planks_attic_roof","handcrafted:andesite_pillar_trim","delightful:food/cooking/ender_nectar","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","silentgear:crimson_steel_ingot","allthemodium:allthemodium_plate","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","productivebees:expansion_boxes/expansion_box_mangrove_canvas","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","travelersbackpack:end","croptopia:campfire_toast","mcwroofs:bricks_upper_steep_roof","mcwbiomesoplenty:willow_plank_four_window","reliquary:fortune_coin","deepresonance:resonating_plate","mcwfurnitures:stripped_spruce_table","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwroofs:black_concrete_upper_steep_roof","allthemodium:unobtainium_nugget_from_ingot","mcwpaths:mossy_stone_windmill_weave_stairs","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","minecraft:birch_boat","aether:leather_boots_repairing","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwwindows:brown_curtain","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwbiomesoplenty:empyreal_western_door","occultism:crafting/lenses","cfm:orange_cooler","dyenamics:dye_wine_carpet","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","pneumaticcraft:wall_lamp_light_blue","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwroofs:gray_terracotta_top_roof","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwpaths:mossy_stone_flagstone","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","modularrouters:puller_module_1","mcwbiomesoplenty:umbran_stable_door","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwbiomesoplenty:willow_stable_head_door","mcwdoors:spruce_bamboo_door","minecraft:gray_candle","trashcans:energy_trash_can","mcwpaths:stone_crystal_floor","pneumaticcraft:air_compressor","minecolonies:baked_salmon","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","croptopia:burrito","pneumaticcraft:security_upgrade","mcwbiomesoplenty:umbran_horse_fence","dyenamics:dye_icy_blue_carpet","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwbiomesoplenty:stripped_mahogany_covered_desk","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","botania:dye_yellow","mcwroofs:grass_attic_roof","minecraft:spruce_fence_gate","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","immersiveengineering:crafting/radiator","minecraft:white_stained_glass_pane_from_glass_pane","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","cfm:spruce_crate","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","pneumaticcraft:charging_upgrade","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","twigs:schist","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","delightful:knives/tin_knife","mcwfurnitures:stripped_jungle_counter","mekanism:purification_chamber","cfm:brown_kitchen_sink","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","minecraft:andesite_slab","minecraft:bamboo_chest_raft","minecraft:honey_block","additionallanterns:normal_lantern_yellow","farmersdelight:melon_juice","dyenamics:bed/bubblegum_bed_frm_white_bed","productivebees:stonecutter/willow_canvas_expansion_box","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","croptopia:oatmeal","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","twigs:schist_wall","cfm:stripped_acacia_coffee_table","mcwbiomesoplenty:empyreal_barn_glass_door","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwfences:ornate_metal_fence","rftoolsbuilder:mover_control2","cfm:stripped_acacia_blinds","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","aiotbotania:livingrock_sword","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","allthemods:easy_sticks","botania:obedience_stick","mcwwindows:bricks_pane_window","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:spruce_roof","create:crafting/kinetics/gantry_carriage","mcwroofs:base_upper_steep_roof","mcwbiomesoplenty:stripped_fir_log_four_window","immersiveengineering:crafting/survey_tools","ae2:network/blocks/cell_workbench","create:crafting/logistics/powered_latch","solcarrot:food_book","utilitarian:utility/charcoal_from_campfire","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","farmersdelight:cooking/pasta_with_meatballs","minecraft:jungle_fence_gate","mcwwindows:birch_louvered_shutter","mcwbiomesoplenty:maple_stable_door","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","travelersbackpack:dye_yellow_sleeping_bag","botania:elementium_boots","undergarden:depthrock_slab_stonecutting","cfm:jungle_cabinet","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:base_roof_block","cfm:oak_blinds","mcwbiomesoplenty:palm_window","botania:conversions/pink_petal_block_deconstruct","mcwbiomesoplenty:mahogany_beach_trapdoor","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:navy_candle","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwfences:deepslate_pillar_wall","cfm:jungle_chair","mcwlights:birch_tiki_torch","botania:terra_axe","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","mcwpaths:brick_square_paving","minecraft:red_candle","sushigocrafting:seaweed_on_a_stick","enderio:silent_light_weighted_pressure_plate","mcwbiomesoplenty:stripped_mahogany_end_table","sophisticatedbackpacks:chipped/glassblower_upgrade","mcwbiomesoplenty:hellbark_four_window","mcwpaths:mossy_cobblestone_dumble_paving","aether:chainmail_gloves_repairing","cfm:stripped_jungle_bedside_cabinet","mcwtrpdoors:spruce_blossom_trapdoor","mcwlights:iron_small_chandelier","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","botania:petal_brown_double","botania:divining_rod","minecraft:light_gray_stained_glass","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","dyenamics:bed/fluorescent_bed_frm_white_bed","allthearcanistgear:vibranium_robes_smithing","botania:ender_eye_block","minecraft:decorated_pot_simple","botania:reach_ring","mcwbiomesoplenty:stripped_dead_pane_window","mcwroofs:magenta_terracotta_steep_roof","biomesoplenty:mahogany_stairs","minecraft:oak_pressure_plate","gtceu:shapeless/rubber_planks","bigreactors:turbine/basic/activefluidport_forge","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","minecraft:diorite","botania:orange_shiny_flower","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","cfm:stripped_jungle_cabinet","mcwbiomesoplenty:redwood_tropical_door","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","railcraft:lead_gear","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","additionallanterns:normal_lantern_light_gray","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","dyenamics:rose_wool","productivebees:stonecutter/driftwood_canvas_hive","mcwbridges:cobblestone_bridge_stair","mcwbiomesoplenty:mahogany_bookshelf_drawer","minecraft:red_nether_bricks","dyenamics:bed/amber_bed","mcwdoors:acacia_western_door","dyenamics:peach_stained_glass","mcwtrpdoors:spruce_whispering_trapdoor","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","botania:conversions/green_petal_block_deconstruct","botania:magnet_ring","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","botania:livingwood_bow","modularrouters:fluid_module","mcwlights:purple_lamp","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","mcwroofs:purple_terracotta_roof","mcwfurnitures:stripped_jungle_chair","botania:spark_changer","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","minecraft:mossy_cobblestone_slab","rftoolsbase:machine_frame","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","cfm:stripped_spruce_crate","railcraft:brass_gear","forbidden_arcanus:diamond_blacksmith_gavel","domum_ornamentum:light_blue_brick_extra","chemlib:manganese_ingot_from_smelting_manganese_dust","mcwpaths:stone_windmill_weave_slab","mcwroofs:thatch_top_roof","supplementaries:cog_block","twigs:polished_schist_stonecutting","pneumaticcraft:armor_upgrade","minecraft:lime_concrete_powder","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","mcwlights:festive_lantern","mcwbiomesoplenty:magic_tropical_door","botania:conversions/gray_petal_block_deconstruct","securitycraft:block_change_detector","advanced_ae:advpatpro2","minecraft:netherite_scrap_from_blasting","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","undergarden:wigglewood_boat","minecraft:book","handcrafted:blue_sheet","mcwfurnitures:oak_drawer_counter","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","travelersbackpack:standard_smithing","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","mekanism:processing/gold/ingot/from_dust_smelting","connectedglass:scratched_glass_cyan_pane2","pneumaticcraft:pressure_chamber_glass_x4","botania:dreamwood_slab","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","mcwfences:acacia_picket_fence","domum_ornamentum:sand_bricks","allthecompressed:compress/spruce_log_1x","ae2:network/cables/dense_covered_cyan","mcwbiomesoplenty:palm_beach_door","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","minecraft:cracked_polished_blackstone_bricks","matc:crystals/tertium","pneumaticcraft:wall_lamp_inverted_purple","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","mcwbiomesoplenty:magic_bamboo_door","ae2:misc/tiny_tnt","botania:shimmerrock","comforts:hammock_to_light_blue","ae2:decorative/quartz_glass","minecraft:red_terracotta","mcwdoors:jungle_japanese_door","handcrafted:spruce_couch","connectedglass:clear_glass1","mcwbiomesoplenty:mahogany_ranch_trapdoor","mcwroofs:white_roof_block","mcwbiomesoplenty:mahogany_stable_door","travelersbackpack:melon","securitycraft:block_pocket_manager","create:crafting/kinetics/encased_chain_drive","occultism:crafting/chalk_red_impure","mcwlights:soul_cherry_tiki_torch","botania:red_pavement","securitycraft:reinforced_mangrove_fence","alltheores:copper_ore_hammer","dyenamics:conifer_stained_glass","productivebees:stonecutter/crimson_canvas_hive","cfm:light_gray_trampoline","mcwlights:red_lamp","bloodmagic:blood_altar","botania:flower_bag","botania:black_shiny_flower","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","quark:tweaks/crafting/utility/bent/paper","railcraft:bag_of_cement","mcwlights:light_blue_lamp","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwbiomesoplenty:fir_plank_window2","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","create:spruce_window","pneumaticcraft:block_tracker_upgrade","connectedglass:clear_glass_gray2","mcwfurnitures:stripped_spruce_counter","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","biomesoplenty:mahogany_wood","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","botania:livingwood_twig","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:bowl","mcwwindows:ender_brick_arrow_slit","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","minecraft:purple_concrete_powder","mcwfences:bastion_metal_fence","mcwbiomesoplenty:mahogany_wardrobe","mcwwindows:dark_oak_plank_window2","allthecompressed:compress/copper_block_1x","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","minecraft:red_carpet","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","supplementaries:candle_holders/candle_holder_light_gray","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:blue_terracotta_steep_roof","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","expatternprovider:pre_bus","allthearcanistgear:vibranium_hat_smithing","mcwroofs:oak_planks_attic_roof","bigreactors:crafting/graphite_component_to_storage","dyenamics:amber_terracotta","mcwdoors:spruce_beach_door","alchemistry:reactor_output","sophisticatedstorage:basic_to_copper_tier_upgrade","twigs:smooth_basalt_bricks","aether:white_cape","aether:netherite_pickaxe_repairing","botania:elementium_hoe","mcwbiomesoplenty:mahogany_cupboard_counter","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwfences:panelled_metal_fence_gate","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwtrpdoors:spruce_classic_trapdoor","aquaculture:planks_from_driftwood","mcwfurnitures:oak_drawer","botania:bifrost_perm","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","minecraft:hopper","mcwroofs:base_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","corail_woodcutter:mangrove_woodcutter","mcwpaths:brick_dumble_paving","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","minecraft:iron_helmet","mcwbiomesoplenty:mahogany_barn_glass_door","create:crafting/kinetics/cyan_seat","mcwroofs:lime_concrete_attic_roof","railcraft:manual_rolling_machine","minecraft:cartography_table","ad_astra:launch_pad","rftoolsutility:charged_porter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","pneumaticcraft:compressed_brick_tile","cfm:stripped_acacia_desk_cabinet","ae2:tools/fluix_shovel","reliquary:magicbane","pneumaticcraft:wall_lamp_inverted_magenta","ae2:block_cutter/stairs/sky_stone_stairs","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwwindows:green_curtain","botania:petal_light_gray","mcwwindows:stone_window","mcwroofs:gray_steep_roof","rftoolspower:endergenic","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","productivebees:stonecutter/yucca_canvas_hive","mcwtrpdoors:jungle_classic_trapdoor","additionallanterns:normal_lantern_cyan","minecraft:light_blue_banner","connectedglass:scratched_glass_gray_pane2","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","mcwroofs:yellow_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_flagstone_path","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","rftoolsbuilder:shape_card_pump_dirt","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:magic_mystic_door","botania:petal_red","minecraft:netherite_pickaxe_smithing","farmersdelight:cooking/fried_rice","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","enderio:resetting_lever_ten","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","botania:conversions/white_petal_block_deconstruct","farmersdelight:honey_cookie","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","botania:thunder_sword","minecraft:polished_deepslate","connectedglass:borderless_glass_yellow_pane2","simplylight:illuminant_gray_block_on_toggle","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","travelersbackpack:netherite","mcwwindows:quartz_pane_window","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","botania:gray_shiny_flower","mcwbiomesoplenty:magic_pyramid_gate","cfm:spruce_desk_cabinet","pneumaticcraft:air_grate_module","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","supplementaries:stonecutting/blackstone_tile","minecraft:dye_red_carpet","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","twigs:polished_rhyolite","productivebees:nests/gravel_nest_clear","undergarden:polished_depthrock_slab_stonecutting","additionallanterns:stone_bricks_chain","mcwbridges:mossy_cobblestone_bridge_pier","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","botania:petal_orange","botania:black_petal_block","mcwfences:spruce_wired_fence","handcrafted:andesite_corner_trim","chemlib:calcium_ingot_to_block","mcwroofs:magenta_terracotta_top_roof","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","mcwbiomesoplenty:stripped_mahogany_double_wardrobe","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","securitycraft:claymore","railcraft:golden_ticket","ae2:network/cables/dense_smart_fluix","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwbiomesoplenty:redwood_glass_door","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","productivebees:stonecutter/hellbark_canvas_hive","mcwfurnitures:spruce_modern_wardrobe","mcwtrpdoors:spruce_barn_trapdoor","ae2:network/blocks/pattern_providers_interface_part","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","botania:manasteel_boots","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","mcwbiomesoplenty:maple_beach_door","enderio:resetting_lever_ten_from_inv","mcwbiomesoplenty:magic_four_panel_door","botania:monocle","mcwroofs:deepslate_lower_roof","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","minecraft:cookie","aquaculture:spruce_fish_mount","minecraft:lightning_rod","mysticalagriculture:tertium_essence_uncraft","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","minecraft:shears","dyenamics:lavender_terracotta","chemlib:gallium_nugget_to_ingot","mcwroofs:jungle_planks_top_roof","bloodmagic:blood_rune_blank","ae2:tools/certus_quartz_hoe","minecraft:yellow_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:spruce_drawer_counter","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","create:crafting/appliances/filter_clear","mcwbiomesoplenty:palm_barn_door","cfm:spruce_desk","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","mcwbiomesoplenty:palm_mystic_door","deeperdarker:echo_chest_boat","cfm:stripped_spruce_park_bench","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","ad_astra:zip_gun","ae2:network/cables/covered_magenta","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","botania:chiseled_mana_quartz","supplementaries:turn_table","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwdoors:acacia_whispering_door","twilightforest:sorting_boat","mcwbiomesoplenty:jacaranda_cottage_door","mythicbotany:alfsteel_nugget_compress","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","ae2:network/cables/covered_gray","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","occultism:crafting/goggles","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","mcwfurnitures:stripped_spruce_drawer_counter","mcwpaths:stone_windmill_weave_path","mcwwindows:gray_curtain","undergarden:depthrock_tiles_stonecutting","minecraft:brewing_stand","allthecompressed:compress/oak_planks_1x","enderio:redstone_filter_base","twigs:bloodstone","productivebees:expansion_boxes/expansion_box_oak_canvas","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","occultism:crafting/spirit_attuned_crystal","supplementaries:bamboo_spikes","rftoolsbuilder:vehicle_status_module","ae2:network/cables/smart_light_gray","mcwbiomesoplenty:stripped_palm_log_four_window","handcrafted:spruce_bench","twigs:spruce_table","mcwroofs:gutter_base_light_blue","create:crafting/kinetics/gearboxfrom_conversion","mcwpaths:andesite_crystal_floor_path","create:crafting/kinetics/orange_seat","rftoolsutility:syringe","minecraft:diamond_block","mcwroofs:deepslate_roof","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","chemlib:sodium_ingot_to_nugget","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","bigreactors:fluidizer/outputport","botania:gravity_rod","additionallanterns:normal_lantern_magenta","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","sophisticatedbackpacks:restock_upgrade","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","croptopia:sweet_crepes","minecraft:lapis_block","aether:skyroot_chest","deepresonance:filter_material","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","rftoolsstorage:modular_storage","handcrafted:bricks_corner_trim","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","mcwroofs:pink_terracotta_attic_roof","dyenamics:spring_green_dye","mcwroofs:light_gray_attic_roof","minecraft:oak_boat","occultism:crafting/book_of_calling_foliot_cleaner","rftoolsbuilder:shape_card_pump","mcwfurnitures:stripped_jungle_covered_desk","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","immersiveengineering:crafting/plate_lead_hammering","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwbiomesoplenty:pine_paper_door","botania:manasteel_shears","ae2:network/cables/smart_green","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","botania:elementium_pickaxe","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","minecraft:glass_bottle","travelersbackpack:dye_gray_sleeping_bag","minecraft:iron_ingot_from_smelting_raw_iron","littlelogistics:fishing_barge","mcwbiomesoplenty:magic_plank_window2","undergarden:depthrock_wall","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","sophisticatedbackpacks:stack_downgrade_tier_2","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","pneumaticcraft:air_canister","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","mcwbridges:brick_bridge_pier","silentgear:crimson_iron_ingot_from_block","cfm:stripped_spruce_desk_cabinet","mekanism:control_circuit/advanced","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","silentgear:crimson_iron_nugget","mcwfences:end_brick_pillar_wall","dyenamics:ultramarine_dye","dyenamics:honey_dye","mcwwindows:prismarine_brick_arrow_slit","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","delightful:knives/elementium_knife","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:purple_dye","mcwroofs:spruce_steep_roof","mcwfurnitures:jungle_modern_chair","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","handcrafted:phantom_trophy","mcwroofs:cyan_terracotta_upper_steep_roof","mcwroofs:stone_bricks_roof","cfm:cyan_grill","thermal_extra:smelting/twinite_ingot_from_dust_smelting","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","mcwbiomesoplenty:palm_bamboo_door","ae2:network/cables/smart_black","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","mcwbiomesoplenty:stripped_mahogany_drawer_counter","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","botania:pink_shiny_flower","mcwbridges:spruce_log_bridge_middle","undergarden:stonecutter_from_depthrock","cfm:dark_oak_mail_box","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","farmersdelight:chicken_sandwich","mcwbiomesoplenty:palm_swamp_door","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","chemlib:helium_lamp_block","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","handcrafted:blue_bowl","mcwfurnitures:stripped_oak_double_wardrobe","minecraft:polished_blackstone_brick_slab","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","ae2:network/cables/smart_orange","dyenamics:dye_amber_carpet","chemlib:tantalum_block_to_ingot","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","mcwbiomesoplenty:magic_pane_window","mythicbotany:alfsteel_ingot_compress","simplylight:illuminant_block_on_toggle","twigs:polished_tuff_bricks_from_tuff_stonecutting","supplementaries:fodder","sophisticatedbackpacks:stonecutter_upgrade","chemlib:gallium_block_to_ingot","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:fir_swamp_door","mcwfurnitures:stripped_oak_glass_table","mcwroofs:blackstone_attic_roof","utilitarian:utility/jungle_logs_to_doors","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","reliquary:ender_staff","mcwwindows:diorite_louvered_shutter","mcwfurnitures:oak_end_table","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","mcwfurnitures:stripped_spruce_bookshelf_drawer","mcwbiomesoplenty:mahogany_roof","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwfurnitures:jungle_modern_desk","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","ae2:materials/advancedcard","mcwlights:gray_lamp","dyenamics:maroon_stained_glass","minecraft:oak_wood","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwbiomesoplenty:redwood_bamboo_door","immersiveengineering:crafting/hempcrete","minecraft:spruce_wood","botania:elementium_chestplate","minecraft:spruce_fence","aquaculture:sushi","mcwdoors:metal_reinforced_door","silentgear:crimson_iron_dust_smelting","mcwbiomesoplenty:magic_japanese2_door","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","ae2:tools/fluix_upgrade_smithing_template","botania:petal_light_blue_double","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","mcwlights:jungle_ceiling_fan_light","bigreactors:turbine/basic/activetap_fe","ae2:network/cables/covered_light_gray","mcwroofs:gutter_base_lime","everythingcopper:copper_anvil","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","mcwbiomesoplenty:palm_picket_fence","handcrafted:jungle_table","minecraft:andesite_wall_from_andesite_stonecutting","botania:abstruse_platform","mcwfences:crimson_stockade_fence","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","deeperdarker:bloom_chest_boat","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","mcwbiomesoplenty:empyreal_barn_door","botania:dye_pink","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","additionallanterns:stone_lantern","supplementaries:flags/flag_red","cfm:dye_blue_picket_fence","minecraft:dye_white_bed","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:light_gray_terracotta_steep_roof","mcwbiomesoplenty:hellbark_nether_door","mcwfurnitures:oak_desk","minecraft:blue_terracotta","mcwroofs:purple_terracotta_lower_roof","supplementaries:stone_lamp","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","chemlib:titanium_nugget_to_ingot","mcwbiomesoplenty:empyreal_paper_door","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","minecraft:gray_terracotta","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","mcwbiomesoplenty:mahogany_triple_drawer","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","dyenamics:peach_terracotta","botania:livingrock_wall","aiotbotania:livingwood_shears","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","minecraft:terracotta","delightful:knives/silver_knife","mcwwindows:acacia_blinds","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwwindows:dark_oak_window2","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","connectedglass:scratched_glass_yellow2","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","twigs:copper_pillar_stonecutting","blue_skies:lunar_bookshelf","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","productivebees:stonecutter/redwood_canvas_hive","minecraft:beacon","minecraft:tnt","mcwdoors:spruce_stable_door","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:dye_yellow_bed","minecraft:flint_and_steel","railways:crafting/smokestack_streamlined","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","botania:petal_purple_double","mcwfurnitures:stripped_jungle_double_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","xnet:advanced_connector_blue_dye","mcwfurnitures:spruce_cupboard_counter","mcwbiomesoplenty:umbran_bamboo_door","mcwbiomesoplenty:empyreal_plank_pane_window","botania:purple_shiny_flower","mcwdoors:oak_bamboo_door","travelersbackpack:hay","xnet:advanced_connector_red_dye","mcwroofs:brown_striped_awning","mcwbiomesoplenty:mahogany_whispering_trapdoor","minecraft:brown_concrete_powder","productivebees:expansion_boxes/expansion_box_snake_block_canvas","mcwbiomesoplenty:stripped_mahogany_modern_desk","securitycraft:reinforced_lime_stained_glass_pane_from_dye","supplementaries:globe_sepia","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","simplylight:bulb","travelersbackpack:cactus","mcwwindows:granite_four_window","cfm:door_mat","mcwroofs:light_blue_concrete_steep_roof","securitycraft:reinforced_nether_brick_fence","supplementaries:stone_tile","supplementaries:item_shelf","handcrafted:terracotta_plate","mcwbiomesoplenty:willow_barn_glass_door","mcwroofs:light_blue_terracotta_roof","allthemodium:vibranium_gear","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:willow_plank_window","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","supplementaries:timber_brace","alltheores:iridium_ingot_from_raw_blasting","handcrafted:jungle_nightstand","domum_ornamentum:gray_brick_extra","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","railways:crafting/smokestack_oilburner","securitycraft:sonic_security_system","supplementaries:bed_from_feather_block","mcwbiomesoplenty:stripped_mahogany_striped_chair","connectedglass:borderless_glass_magenta2","connectedglass:borderless_glass_gray2","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","mcwlights:copper_chain","additionallanterns:normal_lantern_colorless","blue_skies:tool_box","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","cfm:orange_kitchen_drawer","mcwfurnitures:stripped_spruce_cupboard_counter","mcwroofs:spruce_planks_top_roof","botania:glimmering_livingwood","ae2:network/cables/dense_smart_from_smart","ae2:network/cables/covered_green","handcrafted:spruce_pillar_trim","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","megacells:network/mega_pattern_provider","twigs:smooth_basalt_bricks_stonecutting","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","dyenamics:conifer_candle","gtceu:shapeless/red_alloy_cable_1","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwlights:pink_paper_lamp","ad_astra:white_industrial_lamp","immersiveengineering:crafting/wire_aluminum","occultism:crafting/divination_rod","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","dyenamics:bubblegum_terracotta","utilitix:hand_bell","rftoolscontrol:workbench","ad_astra:steel_plating","minecraft:chiseled_stone_bricks","cfm:dye_gray_picket_fence","mcwroofs:stone_upper_lower_roof","bigreactors:turbine/basic/bearing","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","mcwroofs:cyan_striped_awning","dyenamics:dye_mint_carpet","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","undergarden:depthrock_pebble_stonecutting","minecraft:spruce_pressure_plate","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","connectedglass:clear_glass_black_pane2","mcwfences:railing_stone_brick_wall","railcraft:steel_pickaxe","mcwroofs:jungle_planks_roof","cfm:dye_gray_picket_gate","handcrafted:spruce_nightstand","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","sfm:cable","mcwbiomesoplenty:palm_japanese_door","minecraft:hay_block","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:jacaranda_barn_glass_door","mcwwindows:red_sandstone_window2","botania:phantom_ink","mcwbiomesoplenty:palm_pyramid_gate","railways:crafting/smokestack_coalburner","create:jungle_window","twigs:mossy_cobblestone_brick_slab_from_mossy_cobblestone_stonecutting","mekanism:crusher","chemlib:calcium_ingot_from_smelting_calcium_dust","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_planks_attic_roof","mcwroofs:blue_concrete_top_roof","evilcraft:smelting/hardened_blood_shard","aquaculture:jungle_fish_mount","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","mcwbiomesoplenty:mahogany_paper_trapdoor","mcwdoors:oak_waffle_door","bigreactors:blasting/graphite_from_charcoal","botania:red_petal_block","minecraft:cut_copper","supplementaries:flags/flag_orange","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","comforts:hammock_cyan","ae2:network/cables/covered_black","modularrouters:augment_core","mcwbiomesoplenty:stripped_mahogany_modern_chair","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","pneumaticcraft:liquid_compressor","ad_astra:reinforced_door","connectedglass:clear_glass_red_pane2","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","mcwroofs:black_concrete_top_roof","enderio:basic_item_filter","ae2:network/blocks/inscribers","mcwwindows:granite_louvered_shutter","ad_astra:red_flag","ad_astra:small_red_industrial_lamp","securitycraft:sentry","botania:tornado_rod","minecraft:netherite_block","alltheores:uranium_rod","cfm:cyan_kitchen_counter","mcwwindows:blue_mosaic_glass_pane","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","connectedglass:tinted_borderless_glass_yellow2","mcwbiomesoplenty:mahogany_desk","ae2:network/cables/dense_covered_gray","minecraft:crossbow","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","occultism:crafting/otherstone_frame","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","expatternprovider:epp_alt","handcrafted:yellow_crockery_combo","create:crafting/curiosities/peculiar_bell","mcwbiomesoplenty:umbran_mystic_door","securitycraft:reinforced_granite","immersiveengineering:crafting/metal_ladder_none","alltheores:aluminum_dust_from_hammer_crushing","allthemodium:vibranium_nugget_from_ingot","mcwroofs:stone_bricks_lower_roof","chemlib:tantalum_ingot_to_nugget","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","aiotbotania:livingrock_hoe","connectedglass:scratched_glass_red_pane2","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:network/cables/glass_light_gray","minecraft:mossy_cobblestone_stairs","mcwbiomesoplenty:mahogany_covered_desk","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","twigs:mixed_bricks_stonecutting","minecraft:acacia_hanging_sign","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","connectedglass:scratched_glass_blue2","minecraft:blue_bed","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","create:crafting/kinetics/mechanical_harvester","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","chemlib:magnesium_ingot_to_nugget","minecraft:red_stained_glass","minecraft:light_gray_stained_glass_pane_from_glass_pane","mcwpaths:cobbled_deepslate_crystal_floor_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","mcwpaths:cobbled_deepslate_crystal_floor","mcwbiomesoplenty:magic_stable_door","connectedglass:borderless_glass_blue_pane2","mcwbiomesoplenty:mahogany_modern_desk","botania:yellow_petal_block","mcwfurnitures:cabinet_door","sophisticatedbackpacks:tool_swapper_upgrade","mcwwindows:stripped_spruce_pane_window","twigs:mossy_cobblestone_bricks_stonecutting","twigs:smooth_basalt_brick_slab_from_smooth_basalt_stonecutting","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","delightful:food/marshmallow_stick","minecraft:lime_terracotta","mcwbiomesoplenty:mahogany_drawer","mcwpaths:cobbled_deepslate_strewn_rocky_path","cfm:stripped_jungle_desk_cabinet","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","minecraft:gray_stained_glass_pane_from_glass_pane","gravitationalmodulatingunittweaks:module_gravitational_modulating_additional_unit","minecraft:stone_slab","mcwbiomesoplenty:magic_classic_door","productivebees:stonecutter/comb_canvas_hive","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","botania:petal_cyan","mcwbiomesoplenty:maple_four_panel_door","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","travelersbackpack:backpack_tank","minecraft:jungle_stairs","mcwbridges:glass_bridge","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","enderio:resetting_lever_ten_inv_from_base","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","dyenamics:bed/mint_bed_frm_white_bed","croptopia:campfire_molasses","mcwbiomesoplenty:pine_four_panel_door","ae2:network/upgrade_wireless_crafting_terminal","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","utilitix:weak_redstone_torch","mcwbiomesoplenty:mahogany_planks_steep_roof","mcwroofs:blackstone_roof","botania:red_shiny_flower","croptopia:melon_juice","occultism:crafting/chalk_gold_impure","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","industrialforegoing:machine_frame_pity","securitycraft:mine","mcwbiomesoplenty:jacaranda_glass_door","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","gtceu:shaped/rubber_boat","mcwbiomesoplenty:maple_stockade_fence","botania:starfield","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","mekanism:tier_installer/advanced","mcwdoors:spruce_waffle_door","simplylight:illuminant_purple_block_on_toggle","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","botania:yellow_pavement","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","create:crafting/kinetics/yellow_seat","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","mcwpaths:andesite_windmill_weave_path","mcwbiomesoplenty:palm_glass_door","mcwdoors:spruce_western_door","silentgear:sturdy_repair_kit","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","cfm:stripped_spruce_cabinet","minecraft:mangrove_boat","mcwbiomesoplenty:mahogany_upper_steep_roof","mcwroofs:green_terracotta_steep_roof","allthecompressed:compress/hay_block_1x","mcwdoors:garage_silver_door","comforts:hammock_white","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwfurnitures:stripped_oak_cupboard_counter","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","botania:water_rod","mcwroofs:gray_concrete_lower_roof","botania:spectral_platform","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","undergarden:smoke_gloomper_leg","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","handcrafted:red_sheet","sophisticatedstorage:paintbrush","biomesoplenty:willow_boat","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwwindows:stone_brick_arrow_slit","mcwfurnitures:jungle_desk","mcwbiomesoplenty:umbran_waffle_door","mcwroofs:jungle_planks_lower_roof","croptopia:ham_sandwich","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwwindows:dark_oak_louvered_shutter","mcwfurnitures:oak_covered_desk","mcwbiomesoplenty:redwood_barn_door","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwbiomesoplenty:palm_modern_door","rftoolsutility:counter_module","botania:lens_redirect","cfm:stripped_jungle_crate","minecraft:amethyst_block","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","pneumaticcraft:logistics_frame_passive_provider_self","mcwroofs:black_roof_block","botania:elementium_shovel","connectedglass:clear_glass_cyan_pane2","mythicbotany:fire_ring","mcwlights:warped_ceiling_fan_light","botania:prism","mcwwindows:window_centre_bar_base","modularrouters:sender_module_2_x4","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","handcrafted:oak_corner_trim","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","ae2:tools/fluix_sword","simplemagnets:basic_demagnetization_coil","rftoolspower:pearl_injector","mcwwindows:pink_curtain","mcwlights:soul_jungle_tiki_torch","productivebees:stonecutter/rosewood_canvas_expansion_box","mcwwindows:light_gray_curtain","securitycraft:limited_use_keycard","delightful:knives/refined_obsidian_knife","twigs:cobblestone_bricks_stonecutting","pylons:harvester_pylon","minecraft:wheat","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","mcwbiomesoplenty:mahogany_four_panel_trapdoor","ae2:network/cables/glass_fluix_clean","minecraft:bow","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","deepresonance:radiation_suit_boots","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","crafting_on_a_stick:smithing_table","undergarden:depthrock_stairs","chemlib:periodic_table","minecraft:andesite_stairs","botania:super_lava_pendant","handcrafted:bear_trophy","cfm:black_cooler","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","botania:spark","minecraft:dark_oak_boat","dyenamics:maroon_stained_glass_pane_from_glass_pane","chemlib:potassium_ingot_from_smelting_potassium_dust","mcwbiomesoplenty:palm_wired_fence","occultism:crafting/sacrificial_bowl","mcwfurnitures:spruce_glass_table","dyenamics:bed/aquamarine_bed_frm_white_bed","sophisticatedbackpacks:feeding_upgrade","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","create:crafting/kinetics/clutch","mcwfurnitures:spruce_covered_desk","cfm:stripped_acacia_desk","minecraft:firework_rocket_simple","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","mcwdoors:spruce_mystic_door","sophisticatedstorage:backpack_advanced_void_upgrade_from_storage_advanced_void_upgrade","biomesoplenty:mahogany_planks","mcwtrpdoors:spruce_beach_trapdoor","mcwbiomesoplenty:redwood_mystic_door","enderio:enderios","botania:black_pavement","supplementaries:pedestal","travelersbackpack:blaze","cfm:lime_trampoline","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","twigs:smooth_basalt_brick_stairs_from_smooth_basalt_stonecutting","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","alltheores:ruby_dust_from_hammer_crushing","aiotbotania:livingrock_shears","forbidden_arcanus:wooden_blacksmith_gavel","allthemodium:smithing/allthemodium_pickaxe_smithing","mcwdoors:metal_windowed_door","twigs:lamp","minecraft:stone","mcwroofs:spruce_upper_steep_roof","travelersbackpack:red_sleeping_bag","comforts:sleeping_bag_blue","cfm:stripped_oak_kitchen_sink_dark","minecraft:dye_cyan_bed","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","mcwroofs:brown_terracotta_steep_roof","create:crafting/appliances/tree_fertilizer","rftoolsutility:text_module","dyenamics:dye_bubblegum_carpet","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","dyenamics:dye_aquamarine_carpet","connectedglass:clear_glass_black2","mcwbiomesoplenty:mahogany_double_wardrobe","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","undergarden:depthrock_pressure_plate","sfm:printing_press","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","ae2:network/cables/dense_smart_light_gray","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","minecraft:birch_chest_boat","mcwbridges:mossy_stone_bridge_pier","cfm:stripped_oak_bedside_cabinet","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","littlelogistics:tug","create:crafting/kinetics/mechanical_press","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","mcwpaths:brick_strewn_rocky_path","enderio:resetting_lever_three_hundred","mcwlights:cross_lantern","mcwlights:brown_lamp","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","immersiveengineering:crafting/hemp_fabric","minecraft:green_candle","botania:shimmerrock_stairs","chemlib:magnesium_block_to_ingot","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","pneumaticcraft:refinery_output","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","ae2:network/parts/terminals_pattern_access","additionallanterns:diamond_chain","handcrafted:yellow_bowl","supplementaries:stonecutting/stone_tile","ae2:network/cables/covered_pink","railcraft:steel_tank_wall","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","rftoolsbuilder:shape_card_quarry_silk","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","undergarden:depthrock_tile_stairs_stonecutting","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwpaths:mossy_stone_flagstone_stairs","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","cfm:stripped_jungle_kitchen_drawer","railcraft:age_detector","pneumaticcraft:wall_lamp_orange","mcwwindows:black_curtain","productivebees:stonecutter/wisteria_canvas_expansion_box","botania:pixie_ring","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwbiomesoplenty:mahogany_double_drawer","blue_skies:ventium_bucket","botania:drum_gathering","sophisticatedstorage:storage_stack_upgrade_tier_3_from_backpack_stack_upgrade_tier_2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","productivebees:hives/advanced_dark_oak_canvas_hive","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","nethersdelight:blackstone_stove","enderio:resetting_lever_five_inv","handcrafted:spruce_corner_trim","utilitix:armed_stand","botania:lexicon","handcrafted:yellow_cup","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwbiomesoplenty:redwood_beach_door","ae2:network/cables/smart_blue","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","pneumaticcraft:pressure_gauge_module","botania:turntable","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","railcraft:steel_chestplate","supplementaries:daub","alltheores:zinc_dust_from_hammer_crushing","minecraft:netherite_scrap","mcwfurnitures:stripped_jungle_end_table","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","alltheores:tin_dust_from_hammer_ingot_crushing","ae2:decorative/quartz_block","mcwbiomesoplenty:magic_cottage_door","ae2:network/cables/dense_smart_orange","mcwpaths:stone_flagstone","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","immersiveengineering:crafting/coil_lv","biomesoplenty:mahogany_pressure_plate","mcwlights:brown_paper_lamp","connectedglass:borderless_glass_white_pane2","minecraft:campfire","dyenamics:peach_stained_glass_pane_from_glass_pane","cfm:white_kitchen_sink","mcwbiomesoplenty:stripped_mahogany_lower_bookshelf_drawer","cfm:dye_yellow_picket_fence","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","blue_skies:bluebright_bookshelf","mcwbiomesoplenty:mahogany_beach_door","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","mysticalagriculture:unattuned_augment","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["botania:star_sword","mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","comforts:hammock_to_cyan","cfm:yellow_cooler","croptopia:mead","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwbiomesoplenty:jacaranda_window2","minecraft:stonecutter","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","alltheores:gold_dust_from_hammer_crushing","minecraft:bone_block","mcwbiomesoplenty:willow_waffle_door","mcwpaths:cobbled_deepslate_crystal_floor_path","croptopia:shaped_beef_stew","minecraft:melon","pneumaticcraft:wall_lamp_white","mcwroofs:red_terracotta_lower_roof","ad_astra:cyan_industrial_lamp","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","create:oak_window","minecraft:gold_nugget_from_blasting","minecolonies:chainmailchestplate","mekanism:chemical_tank/basic","sophisticatedbackpacks:void_upgrade","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","create:brass_ladder_from_ingots_brass_stonecutting","minecraft:cobblestone_wall","dyenamics:maroon_candle","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","mcwfurnitures:spruce_modern_desk","alltheores:brass_plate","railcraft:controller_circuit","minecraft:spruce_sign","mcwroofs:gray_top_roof","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","create:crafting/logistics/brass_tunnel","sophisticatedstorage:copper_to_iron_tier_upgrade","securitycraft:reinforced_cherry_fence_gate","twigs:polished_schist","rftoolsutility:screen_link","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwbiomesoplenty:redwood_nether_door","mcwroofs:gray_terracotta_roof","mcwlights:covered_lantern","minecraft:copper_block","botania:floating_endoflame","sliceanddice:slicer","pneumaticcraft:crop_support","botania:elementium_sword","supplementaries:crank","connectedglass:clear_glass_gray_pane2","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","modularrouters:bulk_item_filter","minecraft:unobtainium_mage_chestplate_smithing","aiotbotania:livingrock_axe","mcwbiomesoplenty:empyreal_classic_door","supplementaries:gtceu/sign_post_rubber","biomesoplenty:mossy_black_sand","mcwbiomesoplenty:willow_highley_gate","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwwindows:dark_oak_blinds","mcwpaths:cobblestone_dumble_paving","dyenamics:aquamarine_concrete_powder","supplementaries:candle_holders/candle_holder_white_dye","handcrafted:jungle_cupboard","mekanism:energy_tablet","mcwroofs:green_terracotta_top_roof","productivebees:hives/advanced_cherry_canvas_hive","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","dyenamics:spring_green_wool","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","travelersbackpack:diamond_tier_upgrade","additionallanterns:mossy_cobblestone_chain","advanced_ae:advpartenc","botania:bauble_box","botania:vial","railcraft:train_detector","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","cfm:oak_kitchen_counter","minecraft:golden_hoe","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","minecraft:fire_charge","mekanism:factory/basic/infusing","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","botania:pink_petal_block","minecraft:dye_green_bed","railcraft:any_detector","ad_astra:gray_industrial_lamp","alchemistry:reactor_energy","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","minecraft:stone_button","mcwroofs:cyan_concrete_lower_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","aether:golden_pickaxe_repairing","pneumaticcraft:regulator_tube_module","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:umbran_japanese2_door","occultism:crafting/magic_lamp_empty","minecraft:purpur_block","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","domum_ornamentum:blue_brick_extra","cfm:stripped_spruce_chair","simplylight:illuminant_light_blue_block_toggle","aether:red_cape","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","minecraft:gray_concrete_powder","productivebees:expansion_boxes/expansion_box_acacia_canvas","dyenamics:lavender_dye","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:crafting/kinetics/mechanical_roller","botania:open_bucket","blue_skies:glowing_blinding_stone","botania:fabulous_pool","botania:livingrock_slate","handcrafted:jungle_side_table","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","chemlib:manganese_nugget_to_ingot","mcwbiomesoplenty:empyreal_wired_fence","handcrafted:jungle_bench","mcwbiomesoplenty:umbran_classic_door","cfm:purple_cooler","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","littlelogistics:barrel_barge","botania:terrasteel_leggings","mcwroofs:yellow_terracotta_steep_roof","alltheores:nickel_dust_from_hammer_crushing","mcwbiomesoplenty:hellbark_bark_glass_door","cfm:spruce_bedside_cabinet","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","rftoolsutility:crafter3","rftoolsutility:crafter2","supplementaries:candle_holders/candle_holder_red_dye","blue_skies:glowing_nature_stone","twilightforest:wood/jungle_banister","megacells:network/mega_interface","mcwbiomesoplenty:mahogany_drawer_counter","productivebees:stonecutter/palm_canvas_expansion_box","mcwbiomesoplenty:umbran_cottage_door","mcwbiomesoplenty:mahogany_classic_trapdoor","aether:blue_cape_cyan_wool","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_inverted_white","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","aether:iron_sword_repairing","create:brass_scaffolding_from_ingots_brass_stonecutting","dyenamics:fluorescent_candle","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","rftoolsutility:crafter1","farmersdelight:milk_bottle","mcwroofs:oak_roof","minecraft:iron_ingot_from_iron_block","handcrafted:blue_cushion","mcwroofs:light_gray_roof_slab","mcwfurnitures:jungle_drawer","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwroofs:thatch_roof","mcwbridges:rope_jungle_bridge","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","dyenamics:lavender_candle","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","sophisticatedstorage:storage_output","connectedglass:clear_glass_cyan2","connectedglass:tinted_borderless_glass_gray2","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","minecraft:iron_ingot_from_nuggets","mcwpaths:mossy_stone_running_bond_slab","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_bookshelf","productivebees:stonecutter/mangrove_canvas_hive","mcwpaths:cobblestone_basket_weave_paving","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","supplementaries:evilcraft/sign_post_undead","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","botania:tiny_planet_block","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","mcwlights:golden_chandelier","cfm:blue_grill","mcwpaths:stone_flagstone_stairs","chemlib:sodium_block_to_ingot","cfm:stripped_birch_kitchen_drawer","botania:petal_green_double","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","twigs:chiseled_bricks","enderio:stone_gear","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwbiomesoplenty:fir_pyramid_gate","mcwlights:tavern_wall_lantern","croptopia:cheese","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","mcwfences:railing_end_brick_wall","reliquary:void_tear","mcwfurnitures:stripped_oak_lower_triple_drawer","mekanism:factory/advanced/infusing","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","handcrafted:jungle_pillar_trim","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","mcwbiomesoplenty:mahogany_large_drawer","botania:manasteel_axe","mcwbiomesoplenty:empyreal_beach_door","mcwroofs:red_concrete_lower_roof","handcrafted:spruce_counter","mcwfences:warped_pyramid_gate","mcwdoors:jungle_stable_door","mcwbiomesoplenty:pine_window2","evilcraft:special/vengeance_pickaxe","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","mcwbiomesoplenty:mahogany_swamp_door","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","rftoolsbuilder:vehicle_builder","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","mcwbiomesoplenty:stripped_mahogany_modern_wardrobe","mcwbiomesoplenty:dead_plank_pane_window","mcwbiomesoplenty:stripped_palm_log_window2","mcwwindows:blackstone_brick_gothic","mcwlights:soul_double_street_lamp","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","minecraft:diamond_sword","botania:gray_petal_block","mcwbiomesoplenty:empyreal_cottage_door","modularrouters:blank_module","mcwbiomesoplenty:mahogany_lower_triple_drawer","sgjourney:sandstone_with_lapis","mcwlights:upgraded_torch","cfm:jungle_park_bench","cfm:light_blue_kitchen_drawer","botania:petal_cyan_double","mcwpaths:sand_path_block","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","botania:livingrock_slab","immersiveengineering:crafting/toolbox","sophisticatedstorage:spruce_limited_barrel_3","botania:terrasteel_boots","sophisticatedstorage:spruce_limited_barrel_2","sophisticatedstorage:spruce_limited_barrel_1","forbidden_arcanus:clibano_core","sophisticatedstorage:spruce_limited_barrel_4","alltheores:netherite_dust_from_hammer_crushing","chemlib:gallium_ingot_from_blasting_gallium_dust","mcwpaths:brick_running_bond","mcwwindows:dark_oak_window","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_bricks_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","chemlib:manganese_ingot_to_nugget","dyenamics:amber_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_pink","handcrafted:terracotta_wide_pot","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","botania:runic_altar_alt","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:jacaranda_nether_door","mcwwindows:lime_curtain","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","cfm:stripped_acacia_chair","ae2:network/blocks/crystal_processing_growth_accelerator","mcwfurnitures:oak_double_wardrobe","mcwdoors:spruce_stable_head_door","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","botania:green_pavement","twilightforest:mining_boat","aether:golden_dart","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","croptopia:fried_chicken","minecraft:magenta_dye_from_blue_red_pink","alltheores:smelting_dust/uranium_ingot","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:fir_bark_glass_door","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","biomesoplenty:maple_chest_boat","evilcraft:crafting/undead_planks","botania:petal_brown","ae2:network/parts/terminals_crafting","xnet:netcable_blue","cfm:stripped_oak_chair","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwwindows:oak_shutter","cfm:rock_path","connectedglass:scratched_glass_black2","connectedglass:clear_glass_yellow2","mekanismgenerators:generator/heat","minecraft:cooked_beef_from_campfire_cooking","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","minecraft:dye_red_wool","productivebees:stonecutter/jungle_canvas_hive","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","ad_astra:black_industrial_lamp","mcwroofs:cyan_concrete_top_roof","mcwbiomesoplenty:willow_barn_door","aquaculture:stone_fillet_knife","handcrafted:jungle_couch","mcwbiomesoplenty:magic_stockade_fence","bloodmagic:lava_crystal","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","mcwwindows:crimson_planks_four_window","ad_astra:large_gas_tank","mcwlights:white_lamp","mcwbiomesoplenty:maple_waffle_door","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:mahogany_blossom_trapdoor","mcwbiomesoplenty:umbran_plank_pane_window","botania:virus_necrodermal","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","cfm:stripped_oak_desk","connectedglass:borderless_glass_light_gray2","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","mcwbiomesoplenty:fir_stable_door","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","mcwbiomesoplenty:palm_classic_door","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","botania:mana_diamond_block","supplementaries:daub_brace","delightful:food/cooking/rock_candy","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","ad_astra:small_white_industrial_lamp","mcwwindows:mangrove_louvered_shutter","ad_astra:space_helmet","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:palm_tropical_door","handcrafted:creeper_trophy","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","minecraft:dye_blue_wool","aiotbotania:livingwood_pickaxe","mcwbiomesoplenty:palm_cottage_door","mcwfurnitures:spruce_lower_triple_drawer","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","rftoolsbuilder:vehicle_control_module","botania:petal_gray","occultism:blasting/iesnium_ingot_from_raw","utilitarian:utility/spruce_logs_to_pressure_plates","silentgear:crimson_iron_dust","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","mcwfences:granite_grass_topped_wall","botania:fire_rod","botania:petal_pink_double","securitycraft:reinforced_crimson_fence_gate","pneumaticcraft:spawner_core_shell","alltheores:smelting_dust/aluminum_ingot","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","mcwroofs:light_gray_steep_roof","dyenamics:persimmon_candle","botania:elementium_shears","minecraft:red_banner","botania:bifrost_pane","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","mcwfences:oak_stockade_fence","mcwfences:dark_oak_picket_fence","croptopia:shaped_water_bottle","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","pylons:expulsion_pylon","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","alltheores:brass_rod","botania:livingwood_stairs","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","mcwroofs:jungle_roof","industrialforegoing:fluid_extractor","mcwpaths:andesite_running_bond","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","create:crafting/kinetics/black_seat","mcwpaths:brick_crystal_floor_path","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","railcraft:signal_tuner","cfm:pink_kitchen_counter","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwbiomesoplenty:dead_bark_glass_door","botania:spark_upgrade_dispersive","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","botania:dreamwood_wand","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:cherenkov_dye","twilightforest:time_boat","cfm:dye_black_picket_gate","mcwbiomesoplenty:fir_japanese_door","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","alchemistry:fusion_chamber_controller","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","connectedglass:scratched_glass_red2","minecraft:stripped_acacia_wood","botania:red_string_dispenser","handcrafted:jungle_dining_bench","botania:dreamwood_wall","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","mcwwindows:stripped_acacia_log_four_window","mcwbiomesoplenty:dead_paper_door","domum_ornamentum:green_floating_carpet","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","occultism:crafting/raw_iesnium_block","rftoolspower:blazing_agitator","mcwwindows:spruce_curtain_rod","create:crafting/kinetics/andesite_door","minecraft:brick_wall","nethersdelight:diamond_machete","utilitix:advanced_brewery","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:stripped_willow_log_window","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwbiomesoplenty:palm_nether_door","simplylight:illuminant_purple_block_on_dyed","cfm:blue_sofa","mcwbiomesoplenty:fir_western_door","pneumaticcraft:compressed_brick_stairs","mcwtrpdoors:metal_warning_trapdoor","productivetrees:planks/brown_amber_planks","mcwfurnitures:jungle_striped_chair","dyenamics:honey_wool","additionallanterns:diamond_lantern","railcraft:steel_shears","minecraft:mossy_cobblestone_slab_from_mossy_cobblestone_stonecutting","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","railways:crafting/track_switch_andesite","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","minecraft:compass","bigreactors:energizer/computerport","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","minecraft:loom","supplementaries:sign_post_spruce","ad_astra:steel_engine","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","domum_ornamentum:red_brick_extra","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","dyenamics:wine_wool","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","botania:green_shiny_flower","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","sophisticatedbackpacks:everlasting_upgrade","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_white_stained_glass_pane_from_glass","handcrafted:blue_crockery_combo","utilitarian:utility/spruce_logs_to_stairs","sophisticatedstorage:storage_advanced_void_upgrade_from_backpack_advanced_void_upgrade","chemlib:potassium_ingot_to_nugget","dyenamics:bed/maroon_bed_frm_white_bed","travelersbackpack:dye_red_sleeping_bag","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","botania:gaia_spreader","mcwroofs:black_upper_lower_roof","undergarden:depthrock_brick_stairs_stonecutting","mcwfences:warped_wired_fence","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwroofs:stone_top_roof","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","forbidden_arcanus:blacksmith_gavel_head","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","mcwfurnitures:jungle_end_table","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfurnitures:stripped_oak_chair","paraglider:paraglider","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","botania:petal_gray_double","botania:petal_yellow","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","mcwfurnitures:spruce_desk","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","aether:golden_helmet_repairing","alchemistry:dissolver","mcwfences:mangrove_hedge","sophisticatedbackpacks:compacting_upgrade","chemlib:tantalum_ingot_from_smelting_tantalum_dust","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","handcrafted:spruce_cupboard","mcwbiomesoplenty:mahogany_chair","botania:dye_red","mcwbiomesoplenty:mahogany_planks_upper_steep_roof","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_barn_glass_door","aether:aether_gold_nugget_from_blasting","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","mcwfurnitures:stripped_spruce_lower_triple_drawer","mcwbiomesoplenty:mahogany_glass_table","ae2:network/cables/smart_brown","mcwbiomesoplenty:mahogany_top_roof","botania:world_seed","ae2:network/cables/covered_orange","mcwwindows:red_curtain","create:crafting/logistics/brass_funnel","chemlib:titanium_ingot_to_block","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwroofs:red_terracotta_top_roof","aether:golden_sword_repairing","matc:crystals/inferium","mcwroofs:lime_concrete_lower_roof","occultism:crafting/demons_dream_essence_from_seeds","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","mcwroofs:gray_terracotta_attic_roof","mcwroofs:gutter_base_orange","mythicbotany:alfsteel_ingot_decompress","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","ad_astra:cyan_flag","cfm:stripped_spruce_kitchen_sink_light","mcwroofs:bricks_attic_roof","occultism:crafting/otherstone_slab","enderio:resetting_lever_three_hundred_from_inv","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","simplylight:illuminant_yellow_block_dyed","occultism:crafting/brush","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","mcwbiomesoplenty:dead_waffle_door","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","botania:lens_firework","ae2:network/cells/item_storage_cell_1k_storage","mcwroofs:yellow_concrete_steep_roof","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwlights:mangrove_tiki_torch","securitycraft:redstone_module","travelersbackpack:redstone_smithing","botania:dye_magenta","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","create:framed_glass_from_glass_colorless_stonecutting","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:stripped_redwood_log_window","mcwbiomesoplenty:dead_swamp_door","railcraft:signal_lamp","cfm:jungle_blinds","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","xnet:netcable_green","mcwbiomesoplenty:redwood_classic_door","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","sophisticatedstorage:jungle_limited_barrel_3","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","pneumaticcraft:paper_from_tag_filter","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwwindows:acacia_window2","minecraft:orange_terracotta","mcwroofs:white_top_roof","botania:light_blue_petal_block","mcwbiomesoplenty:magic_waffle_door","aquaculture:gold_fillet_knife","mcwbiomesoplenty:redwood_cottage_door","create:copper_scaffolding_from_ingots_copper_stonecutting","mythicbotany:wither_aconite_floating","minecraft:paper","ae2:network/cables/dense_smart_cyan","chemlib:tantalum_ingot_to_block","pneumaticcraft:compressed_bricks_from_tile","botania:light_gray_shiny_flower","utilitix:stone_wall_stonecutting","botania:conversions/terrasteel_block_deconstruct","cfm:spruce_kitchen_counter","travelersbackpack:warden","supplementaries:candle_holders/candle_holder_gray_dye","dyenamics:fluorescent_concrete_powder","botania:incense_plate","securitycraft:reinforced_cherry_fence","additionallanterns:copper_lantern","comforts:sleeping_bag_light_blue","mcwroofs:lime_terracotta_steep_roof","mcwbiomesoplenty:willow_paper_door","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","cfm:crimson_mail_box","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","botania:conversions/black_petal_block_deconstruct","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwfences:acacia_curved_gate","mysticalagriculture:infusion_pedestal","rftoolsutility:screen_controller","mcwwindows:diorite_window2","minecraft:cooked_rabbit","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwbiomesoplenty:redwood_swamp_door","alltheores:lead_rod","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","minecraft:iron_block","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwroofs:gutter_base","mcwdoors:acacia_beach_door","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwpaths:mossy_cobblestone_diamond_paving","connectedglass:borderless_glass_gray_pane2","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","advanced_ae:advpatpropart","pneumaticcraft:reinforced_brick_wall","fluxnetworks:wipe_fluxpoint","ae2:network/cables/smart_purple","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","pneumaticcraft:wall_lamp_inverted_blue","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","biomesoplenty:mahogany_trapdoor","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","pneumaticcraft:logistics_frame_requester","mcwbiomesoplenty:maple_four_window","chemlib:magnesium_nugget_to_ingot","mcwwindows:stripped_oak_log_window","minecraft:golden_boots","minecraft:pink_dye_from_red_white_dye","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","rftoolspower:blazing_generator","ad_astra:steel_door","supplementaries:flags/flag_cyan","immersiveengineering:crafting/tinted_glass_lead_wire","reliquary:wraith_node","botania:diva_charm","cfm:pink_grill","mcwroofs:gutter_middle_orange","croptopia:scrambled_eggs","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwroofs:jungle_attic_roof","blue_skies:aquite_helmet","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","dimstorage:dimensional_tablet","mcwbiomesoplenty:maple_stable_head_door","botania:aura_ring_greater","minecraft:polished_blackstone_brick_stairs","mcwroofs:white_terracotta_upper_steep_roof","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","create:crafting/kinetics/mechanical_saw","mcwpaths:brick_crystal_floor_slab","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","modularrouters:player_module","minecraft:glass","mcwbiomesoplenty:pine_classic_door","allthecompressed:compress/gold_block_1x","mcwdoors:spruce_paper_door","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:brown_concrete_attic_roof","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwbiomesoplenty:pine_cottage_door","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","ae2:network/cables/smart_red","dyenamics:persimmon_concrete_powder","botania:light_relay","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","mcwwindows:light_blue_mosaic_glass","undergarden:depthrock_tile_slab_stonecutting","ad_astra:yellow_industrial_lamp","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","chemlib:tantalum_ingot_from_blasting_tantalum_dust","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","railcraft:steel_gear","aether:diamond_shovel_repairing","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","ae2:tools/network_color_applicator","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","mcwbiomesoplenty:maple_plank_window2","biomesoplenty:mahogany_fence","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:jacaranda_bamboo_door","mcwbiomesoplenty:dead_plank_window2","botania:quartz_lavender","mysticalagriculture:prosperity_gemstone","travelersbackpack:cyan_sleeping_bag","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","mcwroofs:red_concrete_roof","botania:dye_light_gray","minecraft:cyan_banner","ae2:network/cables/smart_fluix","enderio:resetting_lever_thirty","minecraft:glistering_melon_slice","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","farmersdelight:cooking/pumpkin_soup","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","pneumaticcraft:wall_lamp_light_gray","minecraft:dye_yellow_wool","ad_astra:steel_factory_block","dyenamics:amber_concrete_powder","handcrafted:spruce_chair","bigreactors:crafting/raw_yellorium_component_to_storage","mcwroofs:light_gray_concrete_lower_roof","supplementaries:candle_holders/candle_holder_black_dye","occultism:crafting/spirit_torch","undergarden:depthrock_brick_wall_stonecutting","chemlib:potassium_nugget_to_ingot","productivebees:expansion_boxes/expansion_box_cherry_canvas","farmersdelight:cooking/vegetable_noodles","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwbiomesoplenty:hellbark_plank_window2","mcwroofs:lime_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","minecraft:smooth_stone_slab","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","botania:azulejo_0","mcwbiomesoplenty:fir_tropical_door","occultism:crafting/demons_dream_essence_from_fruit","connectedglass:borderless_glass_white2","aquaculture:heavy_hook","mcwwindows:acacia_window","croptopia:food_press","railcraft:mob_detector","blue_skies:frostbright_bookshelf","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","mcwfurnitures:oak_chair","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","advanced_ae:import_export_bus","botania:cyan_shiny_flower","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","croptopia:shaped_milk_bottle","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","expatternprovider:active_formation_plane","mcwlights:orange_lamp","pneumaticcraft:reinforced_brick_pillar","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","gtceu:shapeless/decompress_tin_from_ore_block","mcwpaths:brick_crystal_floor","botania:dodge_ring","biomesoplenty:mahogany_sign","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","comforts:hammock_to_red","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","botania:cyan_petal_block","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","railcraft:charge_terminal","dyenamics:conifer_wool","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","mcwdoors:spruce_barn_door","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","domum_ornamentum:red_floating_carpet","immersiveengineering:crafting/alu_fence","minecraft:diamond_shovel","mcwwindows:end_brick_gothic","botania:petal_white_double","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:jungle_planks_upper_lower_roof","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","dyenamics:mint_wool","connectedglass:borderless_glass_green2","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","alltheores:diamond_plate","utilitarian:utility/spruce_logs_to_boats","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","productivebees:stonecutter/rosewood_canvas_hive","additionallanterns:end_stone_chain","farmersdelight:cooking/mushroom_rice","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","mcwbiomesoplenty:hellbark_stable_head_door","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","blue_skies:aquite_block","mcwroofs:gutter_base_red","botania:holy_cloak","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:receiver_component","create:crafting/kinetics/fluid_pipe_vertical","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","minecraft:diamond_chestplate","domum_ornamentum:mossy_cobblestone_extra","securitycraft:reinforced_gray_stained_glass","aether:iron_hoe_repairing","cfm:dye_black_picket_fence","mcwbiomesoplenty:stripped_maple_log_window2","advanced_ae:stock_export_bus","aether:wooden_sword_repairing","mcwlights:wall_lamp","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","mcwbiomesoplenty:jacaranda_mystic_door","supplementaries:hourglass","everythingcopper:copper_leggings","pneumaticcraft:reinforced_brick_tile","mcwbridges:andesite_bridge_stair","cfm:light_blue_sofa","mcwroofs:lime_terracotta_attic_roof","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","minecraft:orange_dye_from_red_yellow","simplylight:illuminant_blue_block_on_dyed","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","railcraft:bushing_gear_brass","mcwwindows:crimson_planks_window","botania:conversions/light_blue_petal_block_deconstruct","railcraft:steel_tank_valve","mob_grinding_utils:recipe_entity_conveyor","mcwdoors:acacia_modern_door","ad_astra:steel_rod","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","enderio:redstone_alloy_grinding_ball","mcwwindows:stripped_jungle_log_four_window","dyenamics:bubblegum_candle","create:crafting/kinetics/weighted_ejector","mcwlights:iron_triple_candle_holder","mcwroofs:pink_terracotta_roof","travelersbackpack:redstone","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","minecraft:acacia_planks","immersiveengineering:crafting/shovel_steel","mcwbiomesoplenty:fir_nether_door","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","travelersbackpack:emerald","farmersdelight:cutting_board","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:mossy_cobblestone_square_paving","ad_astra:nasa_workbench","sophisticatedstorage:storage_stack_upgrade_tier_2_from_backpack_stack_upgrade_tier_1","ad_astra:etrionic_capacitor","mcwbiomesoplenty:empyreal_japanese2_door","botania:apothecary_deepslate","cfm:dye_yellow_picket_gate","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","rftoolspower:coalgenerator","mcwfurnitures:stripped_spruce_large_drawer","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","allthecompressed:compress/nether_star_block_2x","minecraft:white_carpet","ae2:network/cables/covered_blue","dyenamics:maroon_dye","utilitix:filter_rail","aether:diamond_gloves","minecraft:mossy_cobblestone_stairs_from_mossy_cobblestone_stonecutting","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwbiomesoplenty:dead_stable_door","minecraft:cracked_stone_bricks","mcwfurnitures:spruce_triple_drawer","railcraft:steel_tunnel_bore_head","gtceu:smelting/smelt_dust_electrum_to_ingot","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","mcwbiomesoplenty:willow_plank_pane_window","travelersbackpack:dye_green_sleeping_bag","securitycraft:crystal_quartz_item","dimstorage:dim_wall","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","allthecompressed:decompress/nether_star_block_1x","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","modularrouters:void_module","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","mcwwindows:mangrove_plank_window2","botania:knockback_belt","mcwroofs:spruce_planks_steep_roof","mysticalagriculture:imperium_essence_uncraft","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","botania:shimmerrock_slab","mcwfurnitures:spruce_large_drawer","botania:conversions/blazeblock_deconstruct","handcrafted:spruce_shelf","mcwlights:acacia_tiki_torch","biomesoplenty:mahogany_door","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","securitycraft:reinforced_warped_fence_gate","botania:skydirt_rod","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/gearshift","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","minecraft:light_gray_dye_from_gray_white_dye","mcwfences:blackstone_pillar_wall","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","blue_skies:aquite_leggings","deepresonance:lens","additionallanterns:normal_lantern_black","croptopia:doughnut","mcwlights:square_wall_lamp","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","botania:blue_shiny_flower","mcwbiomesoplenty:mahogany_double_drawer_counter","minecraft:cut_copper_from_copper_block_stonecutting","alltheores:lumium_ingot_from_dust","aether:skyroot_bookshelf","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","cfm:stripped_acacia_crate","allthecompressed:compress/cobbled_deepslate_1x","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","ae2:tools/fluix_axe","cfm:black_kitchen_sink","connectedglass:borderless_glass_pink2","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","cfm:dye_cyan_picket_gate","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwroofs:pink_concrete_top_roof","travelersbackpack:blue_sleeping_bag","forbidden_arcanus:sanity_meter","botania:diluted_pool","mcwbiomesoplenty:hellbark_japanese_door","modularrouters:energy_output_module","chemlib:titanium_ingot_to_nugget","mcwlights:glowstone_slab","mcwbiomesoplenty:stripped_mahogany_stool_chair","mcwbiomesoplenty:mahogany_log_bridge_middle","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alltheores:diamond_rod","minecraft:stone_slab_from_stone_stonecutting","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","reliquary:infernal_claw","chemlib:sodium_nugget_to_ingot","silentgear:crimson_iron_dust_blasting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwroofs:spruce_upper_lower_roof","minecraft:gold_ingot_from_nuggets","dyenamics:ultramarine_stained_glass","cfm:spruce_chair","littlelogistics:locomotive_route","connectedglass:clear_glass_white2","securitycraft:universal_block_remover","mcwbiomesoplenty:stripped_mahogany_coffee_table","securitycraft:reinforced_bookshelf","minecraft:netherite_leggings_smithing","enderio:resetting_lever_sixty","mcwfences:quartz_grass_topped_wall","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","matc:crystals/prudentium","mcwtrpdoors:cherry_ranch_trapdoor","minecraft:stone_brick_stairs_from_stone_stonecutting","comforts:sleeping_bag_to_light_blue","mcwroofs:brown_concrete_top_roof","chemlib:calcium_nugget_to_ingot","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","botania:petal_pink","handcrafted:wood_bowl","pneumaticcraft:charging_station","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","pneumaticcraft:compressed_stone_from_slab","cfm:cyan_trampoline","mcwroofs:white_roof","mcwbiomesoplenty:fir_glass_door","connectedglass:scratched_glass_yellow_pane2","ae2:tools/portable_item_cell_1k","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","ae2:network/cables/glass_blue","cfm:stripped_warped_kitchen_sink_dark","minecraft:lapis_lazuli","mcwroofs:light_gray_concrete_upper_steep_roof","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","mcwpaths:mossy_stone_strewn_rocky_path","mcwwindows:stripped_mangrove_log_window2","minecraft:quartz_block","alltheores:iron_dust_from_hammer_crushing","botania:glimmering_dreamwood_log","rftoolsbuilder:yellow_shield_template_block","domum_ornamentum:brown_floating_carpet","mcwroofs:lime_concrete_top_roof","mcwwindows:quartz_four_window","occultism:crafting/chalk_white_impure","biomesoplenty:palm_boat","reliquary:uncrafting/bone","botania:dye_gray","utilitarian:no_soliciting/restraining_order","mcwbiomesoplenty:stripped_mahogany_bookshelf_cupboard","pneumaticcraft:wall_lamp_brown","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","productivebees:hives/advanced_bamboo_canvas_hive","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","bloodmagic:incense_altar","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_paper_door","sophisticatedstorage:storage_pickup_upgrade_from_backpack_pickup_upgrade","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","blue_skies:aquite_shovel","sophisticatedstorage:backpack_magnet_upgrade_from_storage_magnet_upgrade","rftoolsbuilder:shape_card_quarry_clear_fortune","mcwpaths:andesite_crystal_floor","mcwbiomesoplenty:empyreal_stable_door","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwroofs:oak_planks_lower_roof","mcwwindows:sandstone_window","sophisticatedstorage:storage_magnet_upgrade_from_backpack_magnet_upgrade","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","alchemistry:reactor_input","railcraft:diamond_spike_maul","additionallanterns:granite_lantern","minecraft:white_candle","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_bricks_stonecutting","mcwwindows:spruce_plank_window2","mcwwindows:lime_mosaic_glass_pane","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","comforts:hammock_to_white","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","create:crafting/kinetics/mechanical_bearing","mcwbiomesoplenty:stripped_mahogany_bookshelf","minecraft:golden_carrot","ae2:network/cells/fluid_cell_housing","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","productivebees:stonecutter/magic_canvas_hive","croptopia:dough","mcwbiomesoplenty:stripped_empyreal_log_window2","blue_skies:aquite_axe","dyenamics:honey_stained_glass","create:crafting/kinetics/elevator_pulley","cfm:stripped_acacia_cabinet","mcwtrpdoors:spruce_four_panel_trapdoor","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","minecraft:magma_cream","botania:slime_bottle","create:crafting/kinetics/attribute_filter","handcrafted:white_bowl","supplementaries:key","ae2:block_cutter/walls/sky_stone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","rftoolspower:power_core1","gtceu:shaped/sticky_piston_resin","botania:gaia_ingot","ae2:network/blocks/io_port","mcwroofs:white_concrete_upper_steep_roof","additionallanterns:cobbled_deepslate_lantern","botania:petal_black_double","additionallanterns:stone_chain","pneumaticcraft:compressed_brick_from_slab","dyenamics:banner/ultramarine_banner","aether:skyroot_chest_boat","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","botania:mana_pylon","mcwfurnitures:stripped_oak_striped_chair","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","mcwpaths:brick_windmill_weave_stairs","additionallanterns:normal_sandstone_lantern","undergarden:polished_depthrock_stonecutting","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","botania:apothecary_livingrock","advanced_ae:smalladvpatpro","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","mcwbiomesoplenty:dead_barn_door","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","railcraft:feed_station","minecraft:black_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_inverted_green","mcwfurnitures:spruce_chair","twigs:gravel_bricks","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","mcwroofs:oak_planks_upper_lower_roof","immersiveengineering:crafting/armor_steel_chestplate","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","comforts:sleeping_bag_to_cyan","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","immersiveengineering:crafting/stick_aluminum","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","dyenamics:bubblegum_concrete_powder","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","ae2:tools/matter_cannon","mcwbiomesoplenty:fir_four_panel_door","supplementaries:slingshot","mcwdoors:acacia_japanese2_door","twigs:cobblestone_from_pebble","everythingcopper:copper_rail","cfm:black_grill","pneumaticcraft:wall_lamp_inverted_black","simplylight:illuminant_block","evilcraft:crafting/blood_infuser","blue_skies:glowing_nature_stonebrick_from_glowstone","dyenamics:dye_ultramarine_carpet","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_four_panel_door","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","dyenamics:rose_candle","aether:aether_iron_nugget_from_blasting","aquaculture:neptunium_ingot_from_nuggets","botania:terraform_rod","minecraft:iron_pickaxe","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","minecraft:spruce_button","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","rftoolsutility:matter_booster","mcwwindows:granite_window","ae2:network/cables/glass_orange","minecraft:vibranium_mage_chestplate_smithing","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","simplylight:illuminant_black_block_on_dyed","mcwbiomesoplenty:mahogany_glass_trapdoor","mcwfences:spruce_highley_gate","mcwbiomesoplenty:redwood_stable_head_door","dyenamics:aquamarine_candle","mcwdoors:jungle_barn_door","handcrafted:oak_drawer","mcwbiomesoplenty:mahogany_planks_roof","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","sophisticatedbackpacks:inception_upgrade","mcwbiomesoplenty:empyreal_japanese_door","minecraft:lime_stained_glass","dyenamics:mint_candle","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwbiomesoplenty:mahogany_steep_roof","minecraft:stone_brick_walls_from_stone_stonecutting","securitycraft:keypad_item","mcwbiomesoplenty:hellbark_paper_door","cfm:light_blue_kitchen_counter","additionallanterns:normal_lantern_lime","ae2:network/cables/covered_fluix_clean","botania:elementium_block","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","biomesoplenty:mahogany_slab","handcrafted:cyan_cushion","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","botania:aura_ring","botania:dreamwood_twig","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","cfm:fridge_dark","chimes:copper_chimes","simplylight:illuminant_magenta_block_on_dyed","mcwtrpdoors:mangrove_bark_trapdoor","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","chemlib:sodium_ingot_to_block","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","expatternprovider:wireless_connector","mcwwindows:black_mosaic_glass","mcwroofs:black_terracotta_lower_roof","mcwroofs:gray_concrete_top_roof","blue_skies:ventium_shears","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","mcwwindows:prismarine_brick_gothic","cfm:spatula","ae2:network/cables/dense_covered_fluix_clean","dyenamics:dye_honey_carpet","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwpaths:mossy_cobblestone_clover_paving","mcwbiomesoplenty:fir_cottage_door","constructionwand:core_angel","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","mcwwindows:stripped_oak_log_four_window","mcwroofs:gutter_base_light_gray","mcwbiomesoplenty:willow_glass_door","mcwroofs:lime_terracotta_top_roof","mcwroofs:orange_terracotta_upper_lower_roof","sophisticatedstorage:storage_stack_upgrade_tier_1_plus_from_backpack_stack_upgrade_starter_tier","securitycraft:reinforced_andesite_with_vanilla_diorite","gateways:basic/slime","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","reliquary:mob_charm_fragments/slime","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","sophisticatedbackpacks:crafting_upgrade","cfm:stripped_spruce_desk","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","botania:white_petal_block","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","botania:mana_quartz_pillar","tombstone:ankh_of_prayer","cfm:white_sofa","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwpaths:mossy_stone_flagstone_slab","mcwfences:acacia_highley_gate","mcwroofs:andesite_steep_roof","mcwroofs:pink_concrete_roof","connectedglass:tinted_borderless_glass_black2","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","mcwlights:magenta_lamp","chemlib:gallium_ingot_to_nugget","productivebees:stonecutter/warped_canvas_expansion_box","ae2:network/cables/dense_covered_lime","botania:conversions/magenta_petal_block_deconstruct","mcwbiomesoplenty:maple_swamp_door","botania:mana_fluxfield","mcwwindows:orange_mosaic_glass","mcwwindows:warped_planks_window2","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","ae2:shaped/slabs/sky_stone_block","handcrafted:oak_bench","mcwbiomesoplenty:maple_bark_glass_door","minecraft:yellow_candle","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","minecraft:repeater","mcwroofs:spruce_planks_upper_steep_roof","minecraft:red_concrete_powder","rftoolsbuilder:shape_card_quarry_silk_dirt","botania:conversions/manasteel_block_deconstruct","chemlib:magnesium_ingot_to_block","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwbiomesoplenty:empyreal_glass_door","additionallanterns:normal_lantern_white","minecraft:iron_leggings","ad_astra:steel_cable","create:rose_quartz_block_from_rose_quartz_stonecutting","botania:livingwood_slab","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","productivebees:hives/advanced_mangrove_canvas_hive","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:fir_waffle_door","mcwbridges:balustrade_mossy_cobblestone_bridge","cfm:pink_kitchen_sink","blue_skies:aquite_chestplate","dyenamics:banner/amber_banner","mcwwindows:spruce_blinds","mcwbiomesoplenty:mahogany_lower_bookshelf_drawer","mcwwindows:red_sandstone_window","immersiveengineering:crafting/gunpart_hammer","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","securitycraft:reinforced_orange_stained_glass_pane_from_dye","handcrafted:spruce_drawer","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","ad_astra:red_industrial_lamp","domum_ornamentum:light_blue_floating_carpet","minecraft:dye_white_carpet","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","mcwbiomesoplenty:pine_beach_door","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","additionallanterns:normal_lantern_blue","minecraft:nether_bricks","enderio:resetting_lever_three_hundred_inv_from_prev","mekanism:factory/elite/enriching","minecraft:iron_chestplate","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","mcwdoors:garage_gray_door","botania:petal_green","handcrafted:witch_trophy","minecraft:polished_andesite_from_andesite_stonecutting","littlelogistics:seater_car","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","botania:mushroom_5","minecraft:stone_brick_stairs","botania:mushroom_4","botania:mushroom_3","bigreactors:reprocessor/outputport","mcwwindows:quartz_window","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","botania:dye_brown","blue_skies:coarse_lunar_dirt","botania:glimmering_livingwood_log","dyenamics:peach_candle","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","botania:goddess_charm","minecraft:clock","dyenamics:conifer_dye","occultism:crafting/otherstone_pedestal","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","dyenamics:dye_peach_carpet","mcwlights:soul_birch_tiki_torch","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","biomesoplenty:mahogany_button","travelersbackpack:coal","dyenamics:bed/conifer_bed","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","minecraft:diamond_leggings","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_paper_door","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","alltheores:tin_rod","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","minecraft:polished_andesite_slab_from_andesite_stonecutting","minecraft:dye_black_bed","securitycraft:reinforced_red_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_western_door","handcrafted:jungle_corner_trim","biomesoplenty:empyreal_chest_boat","chemlib:calcium_ingot_from_blasting_calcium_dust","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","mcwbiomesoplenty:pine_swamp_door","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","dyenamics:honey_candle","twigs:rocky_dirt","supplementaries:pancake_fd","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","botania:petal_black","botania:lens_warp","mcwbiomesoplenty:dead_bamboo_door","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","create:crafting/kinetics/mechanical_piston","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","botania:white_shiny_flower","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","botania:petal_red_double","mcwroofs:white_concrete_upper_lower_roof","chemlib:titanium_block_to_ingot","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","twilightforest:sorting_chest_boat","aether:diamond_axe_repairing","pneumaticcraft:wall_lamp_cyan","create:crafting/kinetics/fluid_pipe","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbridges:brick_bridge","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","mcwbiomesoplenty:stripped_mahogany_lower_triple_drawer","supplementaries:flags/flag_yellow","mcwbiomesoplenty:pine_bark_glass_door","sophisticatedbackpacks:smithing_upgrade","minecraft:gold_ingot_from_smelting_raw_gold","mekanism:tier_installer/ultimate","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","chemlib:calcium_ingot_to_nugget","mcwfurnitures:oak_lower_triple_drawer","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","mcwbridges:jungle_log_bridge_middle","sophisticatedbackpacks:anvil_upgrade","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwwindows:jungle_blinds","occultism:crafting/book_of_binding_afrit","mcwlights:oak_tiki_torch","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","travelersbackpack:iron_golem_smithing","alltheores:lumium_dust_from_alloy_blending","mcwfurnitures:spruce_bookshelf_cupboard","cfm:purple_kitchen_drawer","ad_astra:steel_plateblock","alltheores:brass_gear","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","dyenamics:aquamarine_stained_glass","minecraft:netherite_chestplate_smithing","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","terralith:observer_alt","minecraft:oak_sign","mcwwindows:stone_pane_window","botania:light_gray_petal_block","domum_ornamentum:white_paper_extra","connectedglass:borderless_glass_blue2","dyenamics:fluorescent_dye","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","mcwwindows:dark_oak_plank_parapet","mcwbiomesoplenty:pine_waffle_door","mcwwindows:spruce_log_parapet","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","mekanism:osmium_compressor","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","sfm:manager","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:willow_bamboo_door","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","mysticalagriculture:infusion_altar","mcwroofs:spruce_lower_roof","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","comforts:sleeping_bag_to_red","connectedglass:tinted_borderless_glass_cyan2","mcwdoors:spruce_whispering_door","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","ae2:tools/network_tool","deeperdarker:bloom_boat","utilitix:linked_repeater","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","additionallanterns:normal_lantern_green","connectedglass:clear_glass_yellow_pane2","advanced_ae:smalladvpatpro2","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:spruce_curved_gate","mcwdoors:jungle_swamp_door","aiotbotania:livingwood_sword","botania:conversions/terrasteel_to_nugget","mcwpaths:andesite_flagstone_stairs","botania:dye_light_blue","domum_ornamentum:green_brick_extra","mcwwindows:warped_planks_four_window","mcwwindows:stripped_crimson_stem_window2","mcwpaths:mossy_stone_crystal_floor_path","croptopia:meringue","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","ae2:network/cables/dense_smart_fluix_clean","rftoolsutility:moduleplus_template","evilcraft:crafting/blood_infusion_core","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","mcwbiomesoplenty:dead_tropical_door","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwbiomesoplenty:jacaranda_paper_door","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","railcraft:radio_circuit","pneumaticcraft:manual_compressor","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","chemlib:tantalum_nugget_to_ingot","mcwpaths:cobbled_deepslate_clover_paving","mcwfurnitures:oak_modern_wardrobe","handcrafted:yellow_plate","mcwlights:golden_double_candle_holder","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","simplylight:illuminant_light_blue_block_on_dyed","mcwroofs:yellow_terracotta_upper_steep_roof","supplementaries:flags/flag_lime","create:crafting/appliances/copper_backtank","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","mcwwindows:cherry_plank_four_window","mcwbiomesoplenty:fir_modern_door","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","create:crafting/kinetics/portable_storage_interface","ad_astra:steel_trapdoor","securitycraft:reinforced_pink_stained_glass_pane_from_dye","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","pneumaticcraft:manometer","securitycraft:alarm","mcwbiomesoplenty:dead_japanese2_door","mcwfurnitures:oak_counter","corail_woodcutter:spruce_woodcutter","dyenamics:icy_blue_dye","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","botania:elementium_leggings","mcwroofs:oak_top_roof","botania:mana_ring","eidolon:decompress_raw_silver_block","create:crafting/appliances/netherite_backtank_from_netherite","comforts:sleeping_bag_red","utilitix:crude_furnace","dyenamics:peach_dye","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","integrateddynamics:crafting/drying_basin","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","aether:blue_cape_blue_wool","twigs:mossy_cobblestone_bricks_cobblestone","ae2:network/cables/glass_black","mcwfurnitures:stripped_oak_bookshelf","botania:polished_livingrock","mcwbiomesoplenty:fir_curved_gate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","botania:brown_shiny_flower","additionallanterns:gold_lantern","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","productivebees:expansion_boxes/expansion_box_warped_canvas","mcwbiomesoplenty:mahogany_planks_lower_roof","matc:prudentium_essence","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","alchemistry:compactor","minecraft:dropper","dyenamics:bed/maroon_bed","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","productivebees:stonecutter/spruce_canvas_hive","create:crafting/kinetics/brass_hand","mcwfences:mangrove_horse_fence","mcwbiomesoplenty:empyreal_bamboo_door","create:crafting/kinetics/lime_seat","mcwbiomesoplenty:stripped_mahogany_double_drawer_counter","mcwdoors:oak_japanese2_door","simplylight:illuminant_black_block_dyed","mcwbiomesoplenty:mahogany_mystic_trapdoor","mcwdoors:acacia_bamboo_door","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","productivebees:stonecutter/yucca_canvas_expansion_box","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:red_cushion","mcwfurnitures:spruce_table","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","mcwwindows:stripped_cherry_log_window","dyenamics:dye_rose_carpet","botania:temperance_stone","immersiveengineering:crafting/wirecoil_redstone","mcwbiomesoplenty:mahogany_tropical_trapdoor","cfm:blue_kitchen_drawer","chemlib:magnesium_ingot_from_smelting_magnesium_dust","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_bricks_bridge","supplementaries:candle_holders/candle_holder_blue_dye","botania:crystal_bow","rftoolsbuilder:red_shield_template_block","mcwfurnitures:stripped_oak_large_drawer","mcwbiomesoplenty:dead_mystic_door","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","undergarden:polished_depthrock_wall_stonecutting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","bloodmagic:soul_forge","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","pneumaticcraft:wall_lamp_magenta","mcwroofs:jungle_planks_steep_roof","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","cfm:dye_red_picket_gate","travelersbackpack:hose_nozzle","botania:elementium_helmet","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","ae2:network/parts/formation_plane_alt","enderio:black_paper","twigs:polished_tuff_stonecutting","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","mcwbiomesoplenty:pine_japanese_door","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","allthemodium:allthemodium_ingot","sophisticatedbackpacks:pickup_upgrade","blue_skies:ventium_nugget_from_ingot","undergarden:smogstem_chest_boat","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","blue_skies:aquite_pickaxe","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","securitycraft:laser_block","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","botania:terra_pick","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","botania:blood_pendant","rftoolsutility:matter_receiver","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:hellbark_four_panel_door","rftoolsutility:clock_module","botania:terra_plate","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_3","modularrouters:sender_module_2","modularrouters:sender_module_1","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","supplementaries:flags/flag_blue","minecraft:honeycomb_block","undergarden:polished_depthrock_stairs_stonecutting","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","occultism:crafting/chalk_purple_impure","ae2:network/cables/glass_light_blue","twigs:mossy_bricks_from_moss_block","minecraft:andesite_stairs_from_andesite_stonecutting","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","create:vertical_framed_glass_from_glass_colorless_stonecutting","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","create:crafting/materials/electron_tube","mcwbiomesoplenty:maple_japanese2_door","farmersdelight:wheat_dough_from_water","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","croptopia:nougat","dyenamics:icy_blue_concrete_powder","rftoolsbase:crafting_card","mcwroofs:cyan_terracotta_attic_roof","mcwlights:soul_bamboo_tiki_torch","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","rftoolsutility:matter_transmitter","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","simplylight:illuminant_pink_block_on_toggle","bigreactors:turbine/reinforced/passivetap_fe","cfm:stripped_warped_kitchen_sink_light","aiotbotania:livingwood_shovel","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwbiomesoplenty:mahogany_end_table","mcwfurnitures:spruce_double_drawer","handcrafted:oak_couch","sophisticatedbackpacks:stack_upgrade_tier_4","allthetweaks:ender_pearl_block","chemlib:copper_ingot_from_smelting_copper_dust","mythicbotany:mana_infuser","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","mcwbiomesoplenty:hellbark_glass_door","everythingcopper:copper_door","travelersbackpack:dragon","farmersdelight:cooking/glow_berry_custard","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","cfm:orange_kitchen_counter","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","chemlib:potassium_block_to_ingot","pneumaticcraft:spawner_agitator","dyenamics:bed/persimmon_bed","mcwbiomesoplenty:mahogany_counter","mcwroofs:jungle_upper_lower_roof","minecraft:polished_blackstone_brick_wall","mcwbiomesoplenty:empyreal_mystic_door","ae2:network/wireless_crafting_terminal","mcwdoors:metal_door","mcwroofs:jungle_planks_upper_steep_roof","modularrouters:vacuum_module","rftoolsutility:spawner","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","mekanism:factory/basic/enriching","sophisticatedbackpacks:stack_upgrade_tier_2","sophisticatedbackpacks:stack_upgrade_tier_3","botania:astrolabe","sophisticatedbackpacks:stack_upgrade_tier_1","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","ae2:network/cables/dense_covered_light_blue","securitycraft:reinforced_white_stained_glass_pane_from_dye","securitycraft:reinforced_brown_stained_glass_pane_from_dye","railways:crafting/smokestack_long","botania:dye_cyan","connectedglass:tinted_borderless_glass_red2","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","connectedglass:scratched_glass_gray2","immersiveengineering:crafting/strip_curtain","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","alltheores:platinum_ingot_from_raw","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","computercraft:turtle_normal_overlays/turtle_trans_overlay","ae2:tools/certus_quartz_cutting_knife","mcwfurnitures:spruce_counter","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:white_bed","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:spruce_slab","dyenamics:wine_terracotta","cfm:dye_green_picket_gate","minecraft:cobblestone_slab_from_cobblestone_stonecutting","sophisticatedstorage:jungle_chest","botania:corporea_spark","productivebees:hives/advanced_acacia_canvas_hive","occultism:smelting/burnt_otherstone","mcwbiomesoplenty:palm_highley_gate","bigreactors:blasting/yellorium_from_raw","mekanism:electrolytic_separator","mcwroofs:black_attic_roof","mcwfurnitures:stripped_jungle_large_drawer","mcwfences:mesh_metal_fence","supplementaries:soap/piston","sophisticatedbackpacks:chipped/tinkering_table_upgrade","botania:red_string_interceptor","botania:clip","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","undergarden:depthrock_wall_stonecutting","botania:speed_up_belt","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","twilightdelight:obsidian","mcwwindows:prismarine_pane_window","mcwpaths:brick_windmill_weave_path","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","dyenamics:bed/amber_bed_frm_white_bed","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","twigs:cracked_bricks","productivebees:expansion_boxes/expansion_box_jungle_canvas","supplementaries:slice_map","appflux:insulating_resin","connectedglass:borderless_glass_green_pane2","mcwdoors:acacia_mystic_door","alltheores:aluminum_dust_from_hammer_ingot_crushing","travelersbackpack:fox_smithing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","blue_skies:trough","botania:lens_normal","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","mcwroofs:bricks_steep_roof","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","minecraft:cooked_chicken_from_campfire_cooking","railcraft:overalls","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","blue_skies:anvil_compat","create:brass_bars_from_ingots_brass_stonecutting","railcraft:steel_helmet","botania:conversions/yellow_petal_block_deconstruct","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","minecraft:observer","minecraft:mossy_cobblestone_wall_from_mossy_cobblestone_stonecutting","cfm:gray_kitchen_sink","mcwfences:bamboo_stockade_fence","mcwbiomesoplenty:hellbark_waffle_door","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","immersiveengineering:crafting/hoe_steel","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","connectedglass:clear_glass_white_pane2","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","cfm:white_picket_fence","productivebees:stonecutter/dark_oak_canvas_hive","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","botania:light_blue_shiny_flower","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:red_terracotta_steep_roof","utilitix:oak_shulker_boat","cfm:white_trampoline","handcrafted:oak_desk","xnet:connector_blue_dye","rftoolsbuilder:shape_card_liquid","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwbiomesoplenty:dead_stable_head_door","mcwfurnitures:stripped_oak_desk","minecraft:gray_dye","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","croptopia:candied_nuts","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","botania:travel_belt","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwwindows:warped_pane_window","minecraft:emerald_block","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","mcwwindows:stripped_acacia_log_window2","blue_skies:aquite_sword","aiotbotania:livingrock_pickaxe","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","handcrafted:spruce_desk","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","occultism:crafting/golden_sacrificial_bowl","ae2:network/blocks/interfaces_interface_alt","mcwfurnitures:jungle_table","domum_ornamentum:blue_cobblestone_extra","botania:runic_altar","railways:crafting/smokestack_caboosestyle","additionallanterns:normal_lantern_red","mcwbiomesoplenty:stripped_mahogany_wardrobe","domum_ornamentum:purple_brick_extra","mcwwindows:one_way_glass_pane","mcwbiomesoplenty:hellbark_stable_door","comforts:hammock_red","mcwpaths:andesite_dumble_paving","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","productivebees:stonecutter/maple_canvas_hive","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwdoors:acacia_barn_glass_door","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","botania:green_petal_block","supplementaries:candle_holders/candle_holder_green_dye","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","supplementaries:planter_rich","minecolonies:blockconstructiontape","create:crafting/kinetics/millstone","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwfences:railing_red_sandstone_wall","sophisticatedstorage:spruce_chest","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:cyan_sofa","productivebees:expansion_boxes/expansion_box_birch_canvas","travelersbackpack:dye_white_sleeping_bag","dyenamics:wine_stained_glass_pane_from_glass_pane","mcwroofs:gutter_base_white","mcwbridges:jungle_rail_bridge","enderio:resetting_lever_ten_from_prev","mcwroofs:magenta_concrete_upper_lower_roof","botania:lens_flare","sophisticatedstorage:storage_stack_upgrade_tier_5_from_backpack_stack_upgrade_tier_4","reliquary:crimson_cloth","mcwroofs:cyan_concrete_steep_roof","pneumaticcraft:gilded_upgrade","cfm:stripped_acacia_upgraded_gate","mcwroofs:spruce_planks_upper_lower_roof","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","cfm:stripped_spruce_coffee_table","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwroofs:red_concrete_steep_roof","mcwbiomesoplenty:magic_bark_glass_door","aiotbotania:livingwood_axe","xnet:connector_yellow_dye","rftoolspower:powercell_card","reliquary:alkahestry_altar","fluxnetworks:wipe_fluxplug","mcwwindows:crimson_stem_window","mcwdoors:acacia_waffle_door","mcwlights:yellow_lamp","botania:blue_pavement","mcwbiomesoplenty:mahogany_bridge_pier","supplementaries:timber_frame","sophisticatedstorage:packing_tape","enderio:resetting_lever_thirty_from_inv","mekanism:control_circuit/ultimate","mcwlights:soul_oak_tiki_torch","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","railcraft:tin_gear","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","ad_astra:white_flag","travelersbackpack:melon_smithing","mcwtrpdoors:oak_swamp_trapdoor","cfm:spruce_upgraded_gate","aether:fishing_rod_repairing","biomesoplenty:pine_boat","mcwfurnitures:jungle_drawer_counter","mcwroofs:stone_steep_roof","mekanism:steel_casing","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","aiotbotania:terra_shovel","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","occultism:crafting/book_of_binding_foliot","domum_ornamentum:light_gray_brick_extra","mcwroofs:light_gray_roof_block","mcwroofs:magenta_terracotta_lower_roof","ad_astra:vent","utilitarian:fluid_hopper","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:stone_attic_roof","immersiveengineering:crafting/concrete","rftoolsbuilder:blue_shield_template_block","botania:conversions/dragonstone_block_deconstruct","rftoolsbuilder:space_chamber","supplementaries:flags/flag_white","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwwindows:metal_window","mcwbiomesoplenty:mahogany_swamp_trapdoor","minecraft:glass_pane","mcwbiomesoplenty:maple_barn_glass_door","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","simplylight:illuminant_yellow_block_toggle","minecraft:clay","ae2:network/crafting/cpu_crafting_unit","mcwbiomesoplenty:pine_pane_window","ad_astra:blue_flag","mcwroofs:magenta_concrete_roof","create:crafting/kinetics/mechanical_plough","enderio:wood_gear","travelersbackpack:light_blue_sleeping_bag","mcwtrpdoors:spruce_bark_trapdoor","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","ae2:network/blocks/interfaces_interface_part","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwbiomesoplenty:mahogany_paper_door","ae2:materials/annihilationcore","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","minecraft:leather_boots","railcraft:tank_detector","railcraft:steel_spike_maul","pneumaticcraft:classify_filter","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","mcwbridges:mossy_stone_brick_bridge","dyenamics:persimmon_dye","productivebees:stonecutter/birch_canvas_expansion_box","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwbiomesoplenty:magic_nether_door","pneumaticcraft:wall_lamp_inverted_light_blue","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","minecolonies:supplychestdeployer","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","minecraft:dye_black_carpet","tombstone:white_marble","ae2:network/cables/covered_purple","expatternprovider:ebus_out","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","enderio:resetting_lever_thirty_inv","botania:petal_blue","mcwbiomesoplenty:pine_pyramid_gate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","aether:aether_iron_nugget_from_smelting","mcwbiomesoplenty:mahogany_planks_top_roof","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbridges:mossy_cobblestone_bridge_stair","mcwfences:modern_granite_wall","productivebees:stonecutter/grimwood_canvas_hive","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_modern_wardrobe","mcwbiomesoplenty:fir_barn_door","simplylight:illuminant_block_on","botania:petal_light_gray_double","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:mahogany_coffee_table","biomesoplenty:dead_boat","mysticalagriculture:inferium_gemstone","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","mcwbiomesoplenty:willow_japanese2_door","mcwwindows:andesite_window","domum_ornamentum:brick_extra","modularrouters:activator_module","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","ae2:network/cables/dense_smart_purple","minecraft:blaze_powder","cfm:brown_kitchen_drawer","minecraft:chain","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","mcwfences:crimson_highley_gate","mcwbiomesoplenty:pine_barn_glass_door","mcwpaths:mossy_stone_running_bond","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","immersiveengineering:crafting/blueprint_bullets","handcrafted:white_crockery_combo","mcwroofs:magenta_concrete_lower_roof","supplementaries:dispenser_minecart","buildinggadgets2:gadget_destruction","comforts:hammock_light_blue","minecraft:deepslate","mcwroofs:stone_roof","travelersbackpack:wither","mcwdoors:jungle_mystic_door","mcwroofs:pink_terracotta_top_roof","handcrafted:light_blue_cushion","cfm:green_kitchen_counter","minecraft:calibrated_sculk_sensor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:fir_mystic_door","railcraft:bronze_gear","mcwbiomesoplenty:fir_beach_door","ae2:tools/certus_quartz_spade","farmersdelight:skillet","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","ae2:network/cables/dense_covered_black","undergarden:depthrock_stairs_stonecutting","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","occultism:crafting/spirit_campfire","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","mcwpaths:mossy_cobblestone_basket_weave_paving","minecraft:green_terracotta","delightful:knives/brass_knife","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","supplementaries:biomesoplenty/sign_post_mahogany","domum_ornamentum:white_brick_extra","supplementaries:gold_door","ae2:misc/fluixpearl","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","sfm:labelgun","mcwbiomesoplenty:mahogany_lower_roof","mcwfences:jungle_hedge","reliquary:angelic_feather","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","occultism:crafting/book_of_calling_foliot_lumberjack","immersiveengineering:crafting/clinker_brick_quoin","aether:aether_tune_enchanting","supplementaries:altimeter","gtceu:smelting/smelt_dust_bronze_to_ingot","ae2:network/cables/dense_smart_gray","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwbridges:rope_spruce_bridge","supplementaries:end_stone_lamp","allthecompressed:compress/stone_1x","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","cfm:stripped_jungle_desk","botania:manasteel_shovel","handcrafted:oven","minecraft:quartz_from_blasting","mcwbiomesoplenty:stripped_mahogany_table","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","thermal_extra:smelting/soul_infused_ingot_from_dust_smelting","supplementaries:candle_holders/candle_holder_blue","rftoolsbuilder:shape_card_quarry_fortune","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","immersiveengineering:crafting/plate_aluminum_hammering","delightful:knives/bronze_knife","travelersbackpack:quartz","mcwbiomesoplenty:mahogany_classic_door","additionallanterns:cobbled_deepslate_chain","allthecompressed:compress/sand_1x","minecraft:gold_nugget","bigreactors:energizer/controller","mcwbiomesoplenty:stripped_maple_log_four_window","botania:super_travel_belt","mcwwindows:stripped_dark_oak_log_window","mcwbiomesoplenty:stripped_mahogany_double_drawer","occultism:crafting/spirit_attuned_pickaxe_head","mekanism:processing/osmium/ingot/from_raw_smelting","allthecompressed:compress/andesite_1x","sophisticatedbackpacks:stack_upgrade_tier_1_from_starter","mcwbiomesoplenty:willow_nether_door","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","mcwpaths:brick_flagstone","mekanism:tier_installer/basic","minecraft:blast_furnace","pneumaticcraft:reinforced_stone_from_slab","additionallanterns:smooth_stone_lantern","additionallanterns:normal_lantern_light_blue","mcwbiomesoplenty:pine_plank_four_window","securitycraft:electrified_iron_fence","minecraft:light_blue_stained_glass_pane_from_glass_pane","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","ae2:network/parts/import_bus","minecraft:dye_red_bed","mcwdoors:acacia_stable_door","mcwbiomesoplenty:mahogany_bookshelf_cupboard","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","minecraft:blue_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_cupboard_counter","silentgear:crimson_iron_ingot_from_nugget","mcwbiomesoplenty:hellbark_cottage_door","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","silentgear:upgrade_base","ae2:network/cables/glass_pink","ae2:network/cables/smart_cyan","minecraft:fletching_table","mcwbiomesoplenty:fir_picket_fence","rftoolsbuilder:space_chamber_controller","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","minecraft:mossy_cobblestone_wall","mcwroofs:light_gray_lower_roof","mcwwindows:one_way_glass","minecraft:spyglass","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mekanism:electrolytic_core","minecraft:pink_stained_glass","cfm:mangrove_mail_box","minecraft:oak_chest_boat","mcwpaths:mossy_stone_flagstone_path","travelersbackpack:ghast","handcrafted:oak_dining_bench","twigs:tuff_stairs","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","botania:conversions/brown_petal_block_deconstruct","mcwbiomesoplenty:hellbark_classic_door","mcwroofs:spruce_planks_lower_roof","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:dead_glass_door","connectedglass:borderless_glass_orange2","ad_astra:fuel_refinery","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","pneumaticcraft:logistics_core","mcwbiomesoplenty:willow_modern_door","comforts:hammock_blue","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","mcwdoors:spruce_swamp_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","minecraft:bread","mcwdoors:spruce_barn_glass_door","mcwbiomesoplenty:redwood_western_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","mcwfurnitures:spruce_coffee_table","mcwbiomesoplenty:dead_classic_door","botania:horn_grass","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","dyenamics:peach_wool","mcwbiomesoplenty:umbran_swamp_door","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","allthemodium:allthemodium_ingot_from_block","minecraft:dark_oak_chest_boat","additionallanterns:normal_lantern_brown","minecraft:iron_shovel","tombstone:dark_marble","ironfurnaces:furnaces/diamond_furnace","cfm:lime_kitchen_sink","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","chimes:amethyst_chimes","ae2:smelting/smooth_sky_stone_block","mcwbiomesoplenty:mahogany_cottage_door","productivebees:stonecutter/willow_canvas_hive","domum_ornamentum:white_floating_carpet","securitycraft:track_mine","ad_astra:space_suit","mcwbiomesoplenty:willow_swamp_door","mcwdoors:acacia_glass_door","pneumaticcraft:camo_applicator","mcwbiomesoplenty:fir_stable_head_door","alltheores:osmium_dust_from_hammer_ingot_crushing","cfm:light_blue_picket_fence","railcraft:polished_quarried_stone_from_quarried_stone_in_stonecutter","deeperdarker:echo_boat","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","twigs:smooth_basalt_brick_wall_from_smooth_basalt_stonecutting","mcwroofs:andesite_upper_lower_roof","mythicbotany:ice_ring","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","minecraft:oak_door","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwbiomesoplenty:maple_cottage_door","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","mcwbiomesoplenty:empyreal_modern_door","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","handcrafted:spruce_dining_bench","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","ad_astra:coal_generator","mcwroofs:spruce_planks_roof","mcwbiomesoplenty:stripped_hellbark_log_four_window","botania:petal_purple","minecraft:spruce_door","sophisticatedbackpacks:smelting_upgrade","botania:mana_quartz_stairs","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","modularrouters:dropper_module","mcwbiomesoplenty:magic_highley_gate","botania:balance_cloak","mcwroofs:yellow_concrete_upper_lower_roof","dyenamics:mint_terracotta","ae2:network/cables/glass_magenta","supplementaries:candle_holders/candle_holder","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","biomesoplenty:fir_chest_boat","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","supplementaries:wrench","aether:skyroot_smithing_table","securitycraft:reinforced_oak_fence_gate","delightful:knives/aluminum_knife","bigreactors:energizer/chargingport_fe","mcwbiomesoplenty:stripped_redwood_log_four_window","cfm:purple_kitchen_sink","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","mcwpaths:mossy_stone_crystal_floor_slab","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","botania:tiny_planet","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","connectedglass:borderless_glass_brown2","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:pine_stable_door","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:granite","minecraft:melon_seeds","cfm:dye_cyan_picket_fence","minecraft:black_dye_from_wither_rose","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwbiomesoplenty:magic_barn_glass_door","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwbiomesoplenty:fir_bamboo_door","mcwroofs:andesite_roof","rftoolsbase:infused_enderpearl","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwwindows:deepslate_pane_window","minecraft:dye_yellow_carpet","ae2:network/cables/glass_yellow","minecraft:dye_white_wool","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","chemlib:manganese_block_to_ingot","dyenamics:rose_terracotta","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","handcrafted:silverfish_trophy","cfm:yellow_kitchen_counter","alltheores:signalum_plate","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","cfm:lime_kitchen_counter","mcwbridges:mossy_cobblestone_bridge","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","forbidden_arcanus:deorum_soul_lantern","securitycraft:speed_module","mcwdoors:garage_black_door","botania:mana_ring_greater","blue_skies:glowing_poison_stone","mcwdoors:oak_barn_door","botania:alchemy_catalyst","mcwbiomesoplenty:mahogany_barred_trapdoor","mcwbiomesoplenty:stripped_mahogany_bookshelf_drawer","cfm:cyan_kitchen_drawer","mcwfurnitures:spruce_double_drawer_counter","mcwpaths:stone_flagstone_path","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","minecraft:cobbled_deepslate_wall","supplementaries:timber_cross_brace","botania:lava_pendant","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","undergarden:depthrock_slab","minecraft:smooth_stone","twigs:schist_slab","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","botania:conversions/elementium_block_deconstruct","rftoolsutility:environmental_controller","mcwroofs:cyan_concrete_attic_roof","silentgear:crimson_iron_block","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","mcwbiomesoplenty:stripped_mahogany_desk","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","mcwbiomesoplenty:stripped_mahogany_large_drawer","mcwroofs:white_concrete_attic_roof","xnet:controller","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","botania:hourglass","alltheores:lead_plate","reliquary:uncrafting/ender_pearl","additionallanterns:obsidian_chain","minecraft:ender_eye","mcwfurnitures:stripped_spruce_coffee_table","mcwfurnitures:spruce_double_wardrobe","gtceu:shaped/hammer_steel","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","rftoolsbase:manual","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","ae2:network/blocks/io_condenser","sophisticatedbackpacks:smoking_upgrade","additionallanterns:normal_lantern_purple","mcwpaths:brick_clover_paving","minecraft:dye_black_wool","securitycraft:trophy_system","botania:elementium_axe","ad_astra:small_green_industrial_lamp","minecraft:cooked_beef","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","cfm:green_trampoline","mcwlights:yellow_paper_lamp","allthemodium:allthemodium_ingot_from_raw_blasting","forbidden_arcanus:arcane_chiseled_darkstone","twigs:dandelion_paper_lantern","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","dyenamics:wine_stained_glass","minecraft:gold_nugget_from_smelting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","ad_astra:space_pants","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","occultism:smelting/iesnium_ingot_from_raw","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwroofs:pink_striped_awning","mcwbiomesoplenty:mahogany_japanese_door","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:umbran_four_panel_door","mcwdoors:garage_white_door","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","ae2:network/cells/item_storage_components_cell_1k_part","mcwroofs:oak_planks_top_roof","evilcraft:crafting/dark_tank","mcwdoors:spruce_japanese2_door","mcwroofs:oak_planks_roof","enderio:fluid_tank","botania:petal_white","bigreactors:energizer/casing","supplementaries:blackstone_tile","minecraft:enchanting_table","mcwbiomesoplenty:magic_japanese_door","botania:mana_tablet","cfm:birch_mail_box","farmersdelight:roast_chicken_block","mcwbiomesoplenty:mahogany_striped_chair","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","deeperdarker:reinforced_echo_shard","mcwroofs:blue_terracotta_lower_roof","deepresonance:radiation_suit_chestplate","ad_astra:small_blue_industrial_lamp","allthearcanistgear:unobtainium_boots_smithing","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","mcwpaths:mossy_stone_running_bond_path","aether:skyroot_note_block","mcwbiomesoplenty:maple_tropical_door","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","pneumaticcraft:logistics_configurator","bigreactors:smelting/yellorium_from_raw","rftoolsutility:dialing_device","sophisticatedbackpacks:chipped/mason_table_upgrade","botania:dreamwood_planks","allthecompressed:compress/diamond_block_1x","sfm:disk","botania:alfheim_portal","dyenamics:banner/lavender_banner","botania:ender_hand","modularrouters:energy_distributor_module","handcrafted:spider_trophy","mcwroofs:lime_terracotta_lower_roof","ae2:network/cables/dense_smart_green","mcwlights:chain_wall_lantern","cfm:jungle_coffee_table","xnet:connector_green","ad_astra:light_blue_flag","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","securitycraft:reinforced_white_stained_glass","sophisticatedstorage:backpack_advanced_magnet_upgrade_from_storage_advanced_magnet_upgrade","mythicbotany:central_rune_holder","botania:missile_rod","create:crafting/kinetics/light_gray_seat","mcwbiomesoplenty:hellbark_picket_fence","railcraft:bushing_gear_bronze","productivebees:stonecutter/dark_oak_canvas_expansion_box","mcwpaths:cobbled_deepslate_running_bond_slab","mcwbiomesoplenty:stripped_mahogany_chair","productivetrees:wood/soul_tree_wood","buildinggadgets2:gadget_cut_paste","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","rftoolsbuilder:mover_status","mcwfurnitures:spruce_drawer","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","handcrafted:cyan_sheet","twigs:polished_rhyolite_stonecutting","supplementaries:candle_holders/candle_holder_green","aether:skyroot_fletching_table","create:crafting/kinetics/gearbox","botania:terrasteel_block","mcwbiomesoplenty:umbran_modern_door","handcrafted:spruce_side_table","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","minecraft:light_blue_bed","mcwroofs:white_roof_slab","energymeter:meter","mcwroofs:purple_concrete_upper_lower_roof","comforts:sleeping_bag_to_blue","supplementaries:bubble_blower","botania:dye_black","dyenamics:conifer_stained_glass_pane_from_glass_pane","minecraft:recovery_compass","occultism:crafting/book_of_binding_marid","minecraft:magenta_dye_from_blue_red_white_dye","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","productivebees:stonecutter/fir_canvas_expansion_box","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","mcwbiomesoplenty:willow_bark_glass_door","aiotbotania:livingwood_hoe","minecraft:pink_terracotta","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","rftoolsutility:destination_analyzer","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:mahogany_curved_gate","mcwtrpdoors:spruce_barred_trapdoor","cfm:spruce_upgraded_fence","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/cables/smart_fluix_clean","ae2:network/parts/annihilation_plane_alt","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","securitycraft:camera_monitor","immersiveengineering:crafting/clinker_brick_sill","mcwbiomesoplenty:umbran_curved_gate","mcwroofs:black_top_roof","mcwfences:modern_nether_brick_wall","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","cfm:jungle_kitchen_sink_light","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","cfm:stripped_spruce_bedside_cabinet","connectedglass:borderless_glass_purple2","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","sophisticatedstorage:controller","biomesoplenty:palm_chest_boat","mcwbiomesoplenty:redwood_bark_glass_door","simplylight:illuminant_cyan_block_on_dyed","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwroofs:magenta_concrete_upper_steep_roof","minecraft:tinted_glass","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:lodestone","mcwlights:black_lamp","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwdoors:spruce_four_panel_door","mcwbiomesoplenty:mahogany_window","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:quartz","connectedglass:borderless_glass_red2","constructionwand:core_destruction","mcwfences:oak_pyramid_gate","handcrafted:terracotta_thick_pot","mcwbiomesoplenty:mahogany_modern_chair","enderio:extraction_speed_upgrade_1","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","mcwbiomesoplenty:hellbark_beach_door","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","undergarden:depthrock_button","ae2:network/parts/terminals","securitycraft:keypad_frame","enderio:resetting_lever_thirty_inv_from_prev","securitycraft:whitelist_module","ae2:network/blocks/energy_energy_acceptor","pneumaticcraft:vortex_tube","mcwwindows:warped_louvered_shutter","undergarden:wigglewood_chest_boat","xnet:connector_routing","minecraft:blue_carpet","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","comforts:sleeping_bag_cyan","mcwroofs:green_terracotta_attic_roof","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:palm_bark_glass_door","securitycraft:reinforced_black_stained_glass_pane_from_glass","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","dyenamics:ultramarine_candle","mcwfurnitures:stripped_spruce_double_drawer","mcwroofs:cyan_terracotta_steep_roof","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwbiomesoplenty:pine_stable_head_door","minecraft:cooked_chicken_from_smoking","pneumaticcraft:search_upgrade","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","cfm:stripped_acacia_park_bench","ae2:network/blocks/storage_chest","cfm:jungle_desk","minecraft:mangrove_chest_boat","mcwbiomesoplenty:stripped_mahogany_drawer","minecraft:iron_hoe","mekanismgenerators:generator/wind","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","mcwbiomesoplenty:palm_japanese2_door","mcwbiomesoplenty:mahogany_bark_trapdoor","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","minecraft:jungle_trapdoor","croptopia:mashed_potatoes","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","sophisticatedbackpacks:filter_upgrade","mcwbiomesoplenty:maple_japanese_door","aether:skyroot_barrel","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","ae2:network/cables/dense_covered_purple","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","allthetweaks:nether_star_block","undergarden:depthrock_bricks_stonecutting","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","blue_skies:ventium_block","productivebees:hives/advanced_oak_canvas_hive","minecraft:target","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwroofs:green_concrete_upper_lower_roof","allthearcanistgear:unobtainium_hat_smithing","handcrafted:light_blue_sheet","mcwtrpdoors:jungle_swamp_trapdoor","xnet:wireless_router","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","mcwwindows:dark_prismarine_brick_gothic","additionallanterns:normal_lantern_pink","mcwroofs:black_roof_slab","botania:mana_detector","mcwfurnitures:oak_double_drawer_counter","mcwroofs:orange_terracotta_top_roof","pneumaticcraft:compressed_bricks_from_tile_stonecutting","travelersbackpack:gold_tier_upgrade","mcwfences:mesh_metal_fence_gate","xnet:netcable_yellow","mcwroofs:blue_terracotta_roof","mcwfurnitures:jungle_wardrobe","cfm:red_sofa","dyenamics:navy_wool","mcwtrpdoors:metal_full_trapdoor","mcwroofs:spruce_top_roof","supplementaries:bomb","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","rftoolscontrol:tank","additionallanterns:normal_lantern_orange","utilitix:mob_bell","mcwbiomesoplenty:willow_beach_door","mcwroofs:green_terracotta_upper_lower_roof","mcwpaths:andesite_running_bond_slab","appbot:mana_cell_housing","minecraft:chiseled_stone_bricks_stone_from_stonecutting","utilitarian:utility/oak_logs_to_boats","mcwbiomesoplenty:fir_hedge","chemlib:sodium_ingot_from_blasting_sodium_dust","minecraft:netherite_sword_smithing","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","allthecompressed:compress/allthemodium_block_1x","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwdoors:metal_hospital_door","railcraft:signal_circuit","twilightforest:charm_of_life_2","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:dye_conifer_carpet","dyenamics:bubblegum_stained_glass","sophisticatedstorage:oak_chest_from_vanilla_chest","immersiveengineering:crafting/gunpart_barrel","immersiveengineering:crafting/screwdriver","connectedglass:borderless_glass_pane3","connectedglass:borderless_glass_pane1","cfm:pink_cooler","cfm:stripped_acacia_kitchen_counter","mcwfurnitures:oak_double_drawer","mcwfences:railing_blackstone_wall","minecraft:stone_bricks_from_stone_stonecutting","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","ad_astra:blue_industrial_lamp","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","mcwbiomesoplenty:maple_modern_door","cfm:birch_kitchen_sink_light","mcwbiomesoplenty:mahogany_planks_upper_lower_roof","croptopia:chicken_and_dumplings","mcwfences:modern_sandstone_wall","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","utilitix:tiny_charcoal_to_tiny","railcraft:polished_quarried_stone_from_quarried_stone","connectedglass:borderless_glass_light_blue2","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","chemlib:calcium_block_to_ingot","mcwroofs:blue_concrete_roof","alltheores:smelting_dust/osmium_ingot","travelersbackpack:bee","mcwbiomesoplenty:fir_plank_window","minecraft:dye_gray_bed","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","mcwbridges:spruce_rail_bridge","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwwindows:crimson_shutter","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","pneumaticcraft:air_cannon","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","ad_astra:engine_frame","mcwpaths:cobblestone_clover_paving","minecraft:blue_candle","ae2:network/cables/dense_covered_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","botania:red_string_relay","ae2:tools/portable_item_cell_256k","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwroofs:purple_terracotta_upper_lower_roof","mcwfurnitures:spruce_stool_chair","mcwroofs:gray_striped_awning","biomesoplenty:orange_dye_from_burning_blossom","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","blue_skies:cake_compat","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_bricks_stonecutting","mcwpaths:mossy_stone_windmill_weave_path","minecraft:gold_ingot_from_blasting_raw_gold","productivebees:stonecutter/aspen_canvas_expansion_box","mythicbotany:kvasir_mead","gtceu:smelting/smelt_dust_iron_to_ingot","handcrafted:wood_cup","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","ad_astra:ti_69","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","botania:shimmerwood_planks","mcwbiomesoplenty:mahogany_nether_door","mcwbiomesoplenty:hellbark_window2","rftoolsbuilder:shape_card_quarry","mcwbiomesoplenty:magic_hedge","mcwbiomesoplenty:mahogany_barrel_trapdoor","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime2","cfm:orange_trampoline","mcwroofs:lime_concrete_steep_roof","botania:mana_spreader","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:redwood_waffle_door","handcrafted:white_cup","botania:slingshot","ae2:network/cables/glass_gray","botania:dye_green","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","botania:lens_tripwire","mcwlights:light_gray_lamp","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwfences:deepslate_brick_grass_topped_wall","mcwwindows:andesite_window2","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","mcwfurnitures:stripped_spruce_double_wardrobe","railcraft:steel_leggings","mekanism:energized_smelter","mcwbiomesoplenty:umbran_nether_door","securitycraft:reinforced_spruce_fence","minecraft:piston","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","allthemodium:allthemodium_ingot_from_raw_smelting","mcwbiomesoplenty:dead_plank_four_window","undergarden:regalium_block","connectedglass:borderless_glass_red_pane2","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","botania:mana_bomb","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwbiomesoplenty:dead_modern_door","botania:third_eye","productivetrees:wood/brown_amber_wood","mcwdoors:acacia_cottage_door","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","chemlib:potassium_ingot_to_block","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwbiomesoplenty:magic_modern_door","handcrafted:stackable_book","mcwroofs:andesite_attic_roof","securitycraft:reinforced_green_stained_glass","mcwtrpdoors:spruce_mystic_trapdoor","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","productivebees:stonecutter/jacaranda_canvas_expansion_box","mcwbiomesoplenty:stripped_mahogany_glass_table","mcwroofs:red_terracotta_upper_lower_roof","aether:blue_cape_light_blue_wool","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","blue_skies:aquite_hoe","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","travelersbackpack:skeleton","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","productivebees:stonecutter/oak_canvas_expansion_box","farmersdelight:stove","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","minecraft:light_blue_carpet","mcwwindows:birch_shutter","mcwfurnitures:stripped_jungle_wardrobe","undergarden:smelt_gloomper_leg","mcwbiomesoplenty:rainbow_birch_hedge","botania:spawner_mover","productivebees:expansion_boxes/expansion_box_crimson_canvas","immersiveengineering:crafting/pickaxe_steel","mcwfurnitures:jungle_stool_chair","ad_astra:small_black_industrial_lamp","travelersbackpack:diamond","mcwpaths:mossy_stone_crystal_floor_stairs","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","sfm:water_tank","mcwfences:crimson_wired_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","cfm:jungle_upgraded_fence","connectedglass:borderless_glass_black_pane2","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","botania:magenta_shiny_flower","mythicbotany:yggdrasil_branch","mcwroofs:red_terracotta_attic_roof","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","computercraft:pocket_computer_advanced","dyenamics:bed/ultramarine_bed_frm_white_bed","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","cfm:light_gray_grill","cfm:oak_desk","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:hellbark_hedge","supplementaries:daub_frame","botania:manasteel_sword","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:pine_mystic_door","mcwbiomesoplenty:fir_paper_door","chemlib:sodium_ingot_from_smelting_sodium_dust","mcwfurnitures:jungle_double_drawer","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","minecraft:green_stained_glass_pane_from_glass_pane","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","mcwwindows:cobblestone_arrow_slit","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","botania:brown_petal_block","ae2:network/cables/smart_gray","ae2:network/cables/dense_covered_white","mcwpaths:andesite_crystal_floor_stairs","modularrouters:distributor_module","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","create:crafting/logistics/pulse_extender","aether:golden_pendant","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","rftoolsutility:matter_beamer","ae2:decorative/quartz_fixture","mcwbiomesoplenty:dead_nether_door","mcwwindows:cherry_plank_window2","botania:sextant","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:stripped_mahogany_triple_drawer","cfm:dye_blue_picket_gate","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","mcwlights:copper_double_candle_holder","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","twigs:polished_schist_bricks_from_schist_stonecutting","mcwfences:diorite_grass_topped_wall","croptopia:shaped_fish_and_chips","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","pneumaticcraft:tube_junction","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","allthemodium:unobtainium_gear","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","alltheores:constantan_ingot_from_dust","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bloodmagic:alchemy_table","simplylight:illuminant_brown_block_dyed","cfm:stripped_acacia_table","bloodmagic:blood_rune_speed","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","mcwroofs:lime_terracotta_upper_lower_roof","mcwbiomesoplenty:maple_mystic_door","mekanism:processing/osmium/raw/from_raw_block","mcwpaths:mossy_stone_running_bond_stairs","expatternprovider:silicon_block","minecraft:item_frame","mekanism:tier_installer/elite","itemcollectors:basic_collector","enderio:resetting_lever_three_hundred_inv","mcwtrpdoors:oak_bark_trapdoor","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","travelersbackpack:cow","alltheores:brass_dust_from_alloy_blending","handcrafted:skeleton_trophy","handcrafted:golden_wide_pot","rftoolsutility:tank","aether:netherite_gloves_smithing","mcwbiomesoplenty:mahogany_stable_head_door","mcwpaths:brick_flagstone_slab","utilitarian:utility/spruce_logs_to_trapdoors","supplementaries:flags/flag_green","undergarden:smogstem_boat","computercraft:computer_advanced","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","reliquary:uncrafting/slime_ball","enderio:redstone_alloy_nugget_to_ingot","quark:building/crafting/furnaces/cobblestone_furnace","sophisticatedstorage:generic_chest","railways:crafting/smokestack_woodburner","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","handcrafted:wood_plate","securitycraft:reinforced_birch_fence_gate","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","cfm:dye_red_picket_fence","travelersbackpack:horse","cfm:brown_cooler","xnet:netcable_red","delightful:knives/terra_knife","mcwfences:diorite_pillar_wall","mcwwindows:dark_prismarine_parapet","productivebees:hives/advanced_snake_block_canvas_hive","rftoolsutility:inventoryplus_module","minecraft:spruce_trapdoor","bigreactors:reprocessor/wasteinjector","dyenamics:amber_candle","undergarden:shard_torch","mcwfences:bamboo_picket_fence","handcrafted:goat_trophy","mcwpaths:cobbled_deepslate_flagstone","dyenamics:persimmon_stained_glass_pane_from_glass_pane","expatternprovider:ebus_in","mcwroofs:blue_striped_awning","pneumaticcraft:wall_lamp_red","botania:livingwood_wall","mcwbiomesoplenty:mahogany_modern_door","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","botania:yellow_shiny_flower","botania:fabulous_pool_upgrade","buildinggadgets2:gadget_exchanging","comforts:hammock_to_blue","mcwbiomesoplenty:jacaranda_picket_fence","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwtrpdoors:spruce_swamp_trapdoor","undergarden:catalyst","ae2:network/cables/covered_light_blue","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","minecraft:composter","aether:diamond_sword_repairing","undergarden:polished_depthrock","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","minecraft:coarse_dirt","mcwwindows:spruce_plank_pane_window","alltheores:signalum_rod","create:crafting/kinetics/white_seat","chemlib:gallium_ingot_to_block","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","mcwwindows:acacia_louvered_shutter","mcwdoors:print_whispering","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","tombstone:blue_marble","create:crafting/appliances/dough","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwlights:spruce_ceiling_fan_light","dyenamics:dye_spring_green_carpet","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","railways:crafting/smokestack_diesel","securitycraft:reinforced_light_gray_stained_glass","mcwfurnitures:stripped_spruce_stool_chair","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","connectedglass:borderless_glass_yellow2","mcwdoors:jungle_whispering_door","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","mcwlights:bamboo_tiki_torch","handcrafted:hammer","ad_astra:airlock","mcwroofs:grass_steep_roof","alchemistry:combiner","ae2:network/cables/glass_white","chemlib:magnesium_ingot_from_blasting_magnesium_dust","croptopia:egg_roll","connectedglass:borderless_glass_black2","mcwtrpdoors:oak_whispering_trapdoor","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","minecraft:lime_dye","mcwbiomesoplenty:pine_bamboo_door","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:rope_mahogany_bridge","supplementaries:flags/flag_purple","botania:quartz_sunny","productivebees:stonecutter/maple_canvas_expansion_box","botania:flighttiara_5","botania:flighttiara_4","botania:terrasteel_helmet","botania:flighttiara_7","botania:flighttiara_6","botania:flighttiara_1","botania:flighttiara_0","botania:flighttiara_3","botania:flighttiara_2","botania:flighttiara_8","mcwpaths:mossy_stone_windmill_weave_slab","tombstone:bone_needle","minecraft:stone_brick_wall","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","chemlib:manganese_ingot_from_blasting_manganese_dust","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","minecraft:cyan_dye","evilcraft:crafting/dark_stick","connectedglass:scratched_glass_white_pane2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","enderio:stone_gear_upgrade","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:gold_block","minecraft:spruce_boat","botania:floating_pure_daisy","mcwbiomesoplenty:maple_plank_pane_window","dyenamics:banner/navy_banner","handcrafted:jungle_counter","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","connectedglass:scratched_glass_cyan2","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","botania:magenta_petal_block","minecraft:copper_ingot_from_blasting_raw_copper","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","pneumaticcraft:pneumatic_wrench","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","twilightforest:canopy_chest_boat","mcwroofs:jungle_planks_attic_roof","mcwpaths:spruce_planks_path","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","mcwdoors:spruce_nether_door","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","allthemodium:unobtainium_rod","blue_skies:thwarted_armor_trim_smithing_template_smithing_trim","twigs:soul_lamp","mcwbiomesoplenty:dead_pane_window","utilitix:reinforced_rail","immersiveengineering:crafting/wirecoil_structure_rope","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","mcwbiomesoplenty:jacaranda_western_door","cfm:dye_green_picket_fence","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","littlelogistics:spring","allthecompressed:compress/jungle_planks_1x","simplylight:illuminant_lime_block_toggle","farmersdelight:melon_popsicle","mcwbiomesoplenty:mahogany_planks_path","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_middle_purple","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","supplementaries:candle_holders/candle_holder_yellow_dye","allthecompressed:compress/spruce_planks_1x","deeperdarker:warden_upgrade_smithing_template","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","productivebees:stonecutter/dead_canvas_expansion_box","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","productivebees:hives/advanced_jungle_canvas_hive","mcwroofs:orange_terracotta_upper_steep_roof","allthecompressed:compress/niotic_crystal_block_1x","farmersdelight:steak_and_potatoes","mcwtrpdoors:spruce_ranch_trapdoor","create:tuff_from_stone_types_tuff_stonecutting","mcwroofs:green_striped_awning","gtceu:shapeless/decompress_aluminium_from_ore_block","allthemodium:vibranium_plate","securitycraft:codebreaker","rftoolsutility:energy_module","cfm:light_blue_picket_gate","constructionwand:infinity_wand","botania:rainbow_rod","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","botania:redstone_spreader","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","alltheores:gold_plate","cfm:blue_kitchen_counter","dyenamics:spring_green_stained_glass_pane_from_glass_pane","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","mcwroofs:base_top_roof","ae2:network/cables/smart_magenta","securitycraft:harming_module","gtceu:shapeless/decompress_platinum_from_ore_block","occultism:crafting/book_of_binding_djinni","immersiveengineering:crafting/grit_sand","create:crafting/kinetics/item_vault","mythicbotany:mana_collector","mcwfences:warped_highley_gate","pneumaticcraft:minigun","megacells:network/cell_dock","supplementaries:statue","mcwbiomesoplenty:mahogany_tropical_door","minecraft:dye_green_carpet","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","mcwbiomesoplenty:palm_plank_four_window","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","mcwbiomesoplenty:fir_stockade_fence","twigs:schist_stairs","botania:conversions/terrasteel_from_nugget","ae2:network/blocks/pattern_providers_interface","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","alltheores:bronze_plate","botania:swap_ring","create:crafting/kinetics/depot","pneumaticcraft:wall_lamp_yellow","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","mcwbiomesoplenty:mahogany_rail_bridge","pneumaticcraft:pressure_chamber_wall","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","minecraft:cyan_carpet","alltheores:raw_iridium_block","domum_ornamentum:gray_floating_carpet","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","ad_astra:green_industrial_lamp","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","mcwbiomesoplenty:pine_nether_door","mcwbiomesoplenty:mahogany_barn_trapdoor","mcwbiomesoplenty:hellbark_barn_glass_door","deepresonance:radiation_monitor","botania:placeholder","botania:super_cloud_pendant","allthemodium:piglich_heart_block","bigreactors:smelting/graphite_from_charcoal","mcwroofs:green_concrete_top_roof","connectedglass:borderless_glass_cyan2","simplemagnets:advancedmagnet","mcwbiomesoplenty:jacaranda_wired_fence","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","handcrafted:bricks_pillar_trim","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","botania:petal_yellow_double","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","immersiveengineering:crafting/wire_lead","botania:mana_quartz_slab","pneumaticcraft:compressed_brick_slab","mcwbiomesoplenty:redwood_four_panel_door","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","immersiveengineering:crafting/alu_wallmount","pylons:potion_filter","create:crafting/kinetics/magenta_seat","handcrafted:spruce_fancy_bed","botania:conversions/light_gray_petal_block_deconstruct","create:crafting/kinetics/brown_seat","littlelogistics:energy_tug","mcwwindows:acacia_log_parapet","mcwfurnitures:stripped_spruce_triple_drawer","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:fluorescent_stained_glass","alltheores:bronze_rod","twigs:rhyolite_slab","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","twigs:rhyolite_stairs","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","alltheores:aluminum_gear","pneumaticcraft:compressed_bricks_from_stone_stonecutting","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwwindows:cyan_curtain","ae2:network/cables/smart_yellow","handcrafted:golden_medium_pot","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","allthecompressed:compress/tuff_1x","mcwroofs:gray_terracotta_upper_steep_roof","botania:mana_glass_pane","sfm:fancy_to_cable","mcwbiomesoplenty:palm_western_door","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","xnet:router","mcwfurnitures:stripped_jungle_modern_chair","mcwbiomesoplenty:willow_classic_door","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","botania:terrasteel_chestplate","mcwroofs:orange_concrete_lower_roof","aether:golden_gloves_repairing","cfm:magenta_kitchen_counter","minecraft:barrel","utilitix:tiny_coal_to_tiny","supplementaries:boat_jar","create:tuff_pillar_from_stone_types_tuff_stonecutting","ae2:materials/basiccard","handcrafted:oak_table","handcrafted:white_plate","botania:laputa_shard","aquaculture:golden_fishing_rod","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwdoors:acacia_swamp_door","pneumaticcraft:safety_tube_module","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","productivebees:hives/advanced_birch_canvas_hive","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","mcwroofs:blue_concrete_attic_roof","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","minecraft:dye_gray_carpet","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","mekanism:control_circuit/elite","botania:petal_magenta_double","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:cauldron","botania:glimmering_livingwood_from_log","mcwwindows:stripped_mangrove_pane_window","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","mcwfurnitures:oak_kitchen_cabinet","ae2:network/cables/dense_covered_magenta","biomesoplenty:mahogany_fence_gate","mcwdoors:spruce_modern_door","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","tombstone:impregnated_diamond","dimstorage:dim_core","dyenamics:bed/navy_bed_frm_white_bed","bigreactors:smelting/graphite_from_dust","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwbiomesoplenty:dead_beach_door","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","chemlib:gallium_ingot_from_smelting_gallium_dust","utilitix:directional_rail","minecraft:popped_chorus_fruit","sophisticatedstorage:storage_void_upgrade_from_backpack_void_upgrade","mcwbiomesoplenty:fir_barn_glass_door","mekanism:factory/elite/infusing","mcwbiomesoplenty:umbran_window2","mcwfurnitures:oak_double_kitchen_cabinet","mythicbotany:gaia_pylon","undergarden:depthrock_brick_slab_stonecutting","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","minecraft:red_bed","botania:conversions/cyan_petal_block_deconstruct","minecraft:dye_gray_wool","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","dyenamics:dye_persimmon_carpet","mcwroofs:brown_terracotta_top_roof","utilitix:stone_wall","dyenamics:navy_dye","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwbiomesoplenty:jacaranda_swamp_door","mcwbiomesoplenty:mahogany_stool_chair","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","productivebees:stonecutter/concrete_canvas_hive","createoreexcavation:vein_finder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","aether:aether_gold_nugget_from_smelting","mcwwindows:light_blue_mosaic_glass_pane","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","mcwtrpdoors:spruce_tropical_trapdoor","mcwtrpdoors:print_cottage","botania:spark_upgrade_recessive","sophisticatedbackpacks:chipped/carpenters_table_upgrade","minecraft:copper_ingot_from_smelting_raw_copper","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","sophisticatedstorage:oak_limited_barrel_1","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","additionallanterns:normal_lantern_gray","domum_ornamentum:beige_bricks","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","railcraft:diamond_tunnel_bore_head","ae2:tools/nether_quartz_hoe","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","botania:spark_upgrade_isolated","mekanism:metallurgic_infuser","productivebees:stonecutter/aspen_canvas_hive","mcwlights:iron_framed_torch","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","alltheores:aluminum_rod","cfm:stripped_spruce_table","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","mcwwindows:spruce_plank_window","minecraft:spruce_stairs","minecraft:jungle_sign","minecraft:cyan_candle","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","expatternprovider:precise_storage_bus","occultism:crafting/book_of_calling_djinni_manage_machine","domum_ornamentum:cactus_extra","mekanism:factory/advanced/enriching","cfm:cyan_cooler","ae2:network/parts/formation_plane","railcraft:silver_gear","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwbiomesoplenty:mahogany_mystic_door","create:crafting/kinetics/mechanical_drill","mcwroofs:light_gray_roof","mcwbiomesoplenty:umbran_beach_door","mcwroofs:gutter_base_purple","mcwbiomesoplenty:umbran_bark_glass_door","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","minecraft:light_gray_dye_from_black_white_dye","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","mcwtrpdoors:mangrove_ranch_trapdoor","minecraft:slime_block","mcwwindows:blue_curtain","dyenamics:lavender_stained_glass_pane_from_glass_pane","chimes:glass_bells","mcwwindows:iron_shutter","mcwpaths:mossy_stone_crystal_floor","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","blue_skies:aquite_boots","immersiveengineering:crafting/sawblade","mcwbiomesoplenty:empyreal_swamp_door","modularrouters:flinger_module","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","sophisticatedstorage:basic_to_gold_tier_upgrade","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","minecraft:magenta_stained_glass_pane_from_glass_pane","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","alltheores:copper_rod","productivebees:stonecutter/cherry_canvas_hive","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwfurnitures:stripped_oak_double_drawer","botania:dragonstone_block","minecraft:spruce_planks","aquaculture:cooked_fish_fillet","cfm:orange_kitchen_sink","mcwbiomesoplenty:mahogany_attic_roof","croptopia:pork_jerky","enderio:resetting_lever_sixty_from_inv","minecraft:mossy_cobblestone_from_moss_block","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","botania:unholy_cloak","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwwindows:jungle_pane_window","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","mcwbiomesoplenty:willow_western_door","ae2:network/cables/dense_covered_yellow","mcwpaths:cobbled_deepslate_running_bond_path","handcrafted:stone_corner_trim","delightful:knives/bone_knife","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","botania:ender_dagger","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","sophisticatedstorage:spruce_barrel","mcwwindows:mangrove_pane_window","productivebees:stonecutter/river_canvas_expansion_box","farmersdelight:flint_knife","chemlib:manganese_ingot_to_block","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwfurnitures:stripped_spruce_drawer","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","immersiveengineering:smoking/clinker_brick","handcrafted:white_sheet","ae2:network/cables/glass_purple","create:crafting/kinetics/steam_whistle","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","immersiveengineering:crafting/plate_steel_hammering","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","alltheores:aluminum_plate","aquaculture:gold_nugget_from_blasting","bloodmagic:synthetic_point","ad_astra:small_yellow_industrial_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","minecraft:redstone_lamp","mcwbiomesoplenty:stripped_umbran_pane_window","supplementaries:candle_holders/candle_holder_cyan_dye","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwbiomesoplenty:dead_barn_glass_door","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","botania:black_hole_talisman","botania:spark_upgrade_dominant","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwwindows:green_mosaic_glass_pane","mcwbiomesoplenty:pine_western_door","minecraft:dye_blue_bed","botania:auto_crafting_halo","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:mahogany_upper_lower_roof","dyenamics:banner/fluorescent_banner","croptopia:tortilla","undergarden:chiseled_depthrock_bricks_stonecutting","securitycraft:bouncing_betty","minecraft:painting","botania:avatar","connectedglass:tinted_borderless_glass_blue2","connectedglass:tinted_borderless_glass_white2","sophisticatedstorage:storage_stack_upgrade_tier_4_from_backpack_stack_upgrade_tier_3","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","alchemistry:fission_chamber_controller","railcraft:animal_detector","mcwbiomesoplenty:palm_stable_door","rftoolsbuilder:shield_block2","rftoolsbuilder:shield_block1","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwbiomesoplenty:palm_four_panel_door","aether:ice_from_bucket_freezing","productivebees:stonecutter/grimwood_canvas_expansion_box","pneumaticcraft:speed_upgrade","terralith:dropper_alt","mcwlights:white_paper_lamp","occultism:crafting/book_of_binding_empty","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","mcwwindows:mangrove_plank_pane_window","botania:dreamwood","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","create:crafting/kinetics/placard","chimes:iron_chimes","securitycraft:storage_module","alltheores:tin_plate","botania:dye_white","ae2:network/cables/dense_smart_brown","botania:conversions/red_petal_block_deconstruct","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","immersiveengineering:crafting/chute_copper","minecraft:cyan_bed","mcwfences:prismarine_grass_topped_wall","occultism:crafting/book_of_calling_foliot_transport_items","ad_astra:small_gray_industrial_lamp","mcwbiomesoplenty:mahogany_cottage_trapdoor","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","create:crafting/kinetics/crafter_slot_cover","twilightforest:mangrove_chest_boat","simplylight:illuminant_green_block_on_toggle","allthecompressed:compress/nitro_crystal_block_1x","travelersbackpack:wolf","productivebees:stonecutter/mahogany_canvas_expansion_box","forbidden_arcanus:stone_blacksmith_gavel","minecraft:brick","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","minecraft:music_disc_5","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:coal","mcwbiomesoplenty:mahogany_bamboo_door","minecraft:unobtainium_mage_boots_smithing","mcwroofs:blue_terracotta_upper_lower_roof","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","supplementaries:sack","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","mcwwindows:nether_brick_gothic","mcwbiomesoplenty:willow_mystic_door","allthemodium:unobtainium_ingot","mcwbiomesoplenty:pine_modern_door","minecraft:brown_dye","alltheores:silver_plate","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","cfm:lime_cooler","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","alltheores:copper_ingot_from_block","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","dyenamics:mint_dye","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","productivebees:stonecutter/cherry_canvas_expansion_box","mcwroofs:pink_concrete_lower_roof","minecraft:jungle_pressure_plate","mcwlights:iron_chandelier","mcwpaths:mossy_cobblestone_honeycomb_paving","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:prismarine_window2","utilitarian:tps_meter","utilitarian:utility/spruce_logs_to_doors","cfm:stripped_acacia_upgraded_fence","ae2:network/cables/smart_pink","mcwbiomesoplenty:pine_horse_fence","mcwpaths:mossy_stone_windmill_weave","mcwwindows:acacia_four_window","chemlib:potassium_ingot_from_blasting_potassium_dust","mcwwindows:warped_shutter","domum_ornamentum:yellow_floating_carpet","enderio:redstone_alloy_nugget","minecraft:blue_banner","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","botania:petal_light_blue","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","utilitarian:utility/green_dye","mcwfurnitures:spruce_modern_chair","mcwwindows:pink_mosaic_glass_pane","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","alltheores:gold_rod","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","farmersdelight:fried_egg","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","productivebees:expansion_boxes/expansion_box_bamboo_canvas","botania:dirt_rod","mcwfurnitures:stripped_oak_table","connectedglass:clear_glass_red2","pneumaticcraft:logistics_frame_default_storage","mcwbiomesoplenty:mahogany_table","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwdoors:jail_door","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","reliquary:mob_charm_fragments/enderman","pneumaticcraft:wall_lamp_inverted_gray","handcrafted:spruce_table","mcwpaths:brick_running_bond_path","rftoolsbuilder:shape_card_quarry_dirt","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:birch_blinds","cfm:spruce_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","mcwroofs:magenta_terracotta_upper_lower_roof","mcwdoors:spruce_tropical_door","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","minecraft:dye_cyan_wool","mcwbiomesoplenty:stripped_mahogany_counter","mcwbridges:bridge_lantern","mcwroofs:blackstone_top_roof","cfm:brown_trampoline","mcwdoors:oak_whispering_door","alltheores:signalum_ingot_from_dust","mcwwindows:jungle_window2","cfm:gray_cooler","everythingcopper:copper_axe","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","connectedglass:scratched_glass_green_pane2","mcwroofs:stone_upper_steep_roof","connectedglass:scratched_glass_white2","productivebees:expansion_boxes/expansion_box_spruce_canvas","simplylight:edge_light","mcwfences:railing_granite_wall","immersiveengineering:crafting/plate_uranium_hammering","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","mcwbiomesoplenty:stripped_pine_log_window","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","cfm:magenta_kitchen_drawer","xnet:redstoneproxy_update","cfm:stripped_acacia_mail_box","mcwroofs:spruce_planks_attic_roof","handcrafted:andesite_pillar_trim","delightful:food/cooking/ender_nectar","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","silentgear:crimson_steel_ingot","allthemodium:allthemodium_plate","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","productivebees:expansion_boxes/expansion_box_mangrove_canvas","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","travelersbackpack:end","croptopia:campfire_toast","mcwroofs:bricks_upper_steep_roof","mcwbiomesoplenty:willow_plank_four_window","reliquary:fortune_coin","deepresonance:resonating_plate","mcwfurnitures:stripped_spruce_table","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwroofs:black_concrete_upper_steep_roof","allthemodium:unobtainium_nugget_from_ingot","mcwpaths:mossy_stone_windmill_weave_stairs","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","minecraft:birch_boat","aether:leather_boots_repairing","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwwindows:brown_curtain","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwbiomesoplenty:empyreal_western_door","occultism:crafting/lenses","cfm:orange_cooler","dyenamics:dye_wine_carpet","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","pneumaticcraft:wall_lamp_light_blue","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwroofs:gray_terracotta_top_roof","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwpaths:mossy_stone_flagstone","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","modularrouters:puller_module_1","mcwbiomesoplenty:umbran_stable_door","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwbiomesoplenty:willow_stable_head_door","mcwdoors:spruce_bamboo_door","minecraft:gray_candle","trashcans:energy_trash_can","mcwpaths:stone_crystal_floor","pneumaticcraft:air_compressor","minecolonies:baked_salmon","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","croptopia:burrito","pneumaticcraft:security_upgrade","mcwbiomesoplenty:umbran_horse_fence","dyenamics:dye_icy_blue_carpet","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","mcwbiomesoplenty:stripped_mahogany_covered_desk","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","botania:dye_yellow","mcwroofs:grass_attic_roof","minecraft:spruce_fence_gate","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","immersiveengineering:crafting/radiator","minecraft:white_stained_glass_pane_from_glass_pane","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","cfm:spruce_crate","biomesoplenty:mahogany_chest_boat","immersiveengineering:crafting/hammer","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","pneumaticcraft:charging_upgrade","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","twigs:schist","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","delightful:knives/tin_knife","mcwfurnitures:stripped_jungle_counter","mekanism:purification_chamber","cfm:brown_kitchen_sink","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","minecraft:andesite_slab","minecraft:bamboo_chest_raft","minecraft:honey_block","additionallanterns:normal_lantern_yellow","farmersdelight:melon_juice","dyenamics:bed/bubblegum_bed_frm_white_bed","productivebees:stonecutter/willow_canvas_expansion_box","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","croptopia:oatmeal","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","twigs:schist_wall","cfm:stripped_acacia_coffee_table","mcwbiomesoplenty:empyreal_barn_glass_door","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwfences:ornate_metal_fence","rftoolsbuilder:mover_control2","cfm:stripped_acacia_blinds","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","aiotbotania:livingrock_sword","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","allthemods:easy_sticks","botania:obedience_stick","mcwwindows:bricks_pane_window","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:spruce_roof","create:crafting/kinetics/gantry_carriage","mcwroofs:base_upper_steep_roof","mcwbiomesoplenty:stripped_fir_log_four_window","immersiveengineering:crafting/survey_tools","ae2:network/blocks/cell_workbench","create:crafting/logistics/powered_latch","solcarrot:food_book","utilitarian:utility/charcoal_from_campfire","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","farmersdelight:cooking/pasta_with_meatballs","minecraft:jungle_fence_gate","mcwwindows:birch_louvered_shutter","mcwbiomesoplenty:maple_stable_door","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","travelersbackpack:dye_yellow_sleeping_bag","botania:elementium_boots","undergarden:depthrock_slab_stonecutting","cfm:jungle_cabinet","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:base_roof_block","cfm:oak_blinds","mcwbiomesoplenty:palm_window","botania:conversions/pink_petal_block_deconstruct","mcwbiomesoplenty:mahogany_beach_trapdoor","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:navy_candle","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwfences:deepslate_pillar_wall","cfm:jungle_chair","mcwlights:birch_tiki_torch","botania:terra_axe","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","mcwpaths:brick_square_paving","minecraft:red_candle","sushigocrafting:seaweed_on_a_stick","enderio:silent_light_weighted_pressure_plate","mcwbiomesoplenty:stripped_mahogany_end_table","sophisticatedbackpacks:chipped/glassblower_upgrade","mcwbiomesoplenty:hellbark_four_window","mcwpaths:mossy_cobblestone_dumble_paving","aether:chainmail_gloves_repairing","cfm:stripped_jungle_bedside_cabinet","mcwtrpdoors:spruce_blossom_trapdoor","mcwlights:iron_small_chandelier","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","botania:petal_brown_double","botania:divining_rod","minecraft:light_gray_stained_glass","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","dyenamics:bed/fluorescent_bed_frm_white_bed","allthearcanistgear:vibranium_robes_smithing","botania:ender_eye_block","minecraft:decorated_pot_simple","botania:reach_ring","mcwbiomesoplenty:stripped_dead_pane_window","mcwroofs:magenta_terracotta_steep_roof","biomesoplenty:mahogany_stairs","minecraft:oak_pressure_plate","gtceu:shapeless/rubber_planks","bigreactors:turbine/basic/activefluidport_forge","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","minecraft:diorite","botania:orange_shiny_flower","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","cfm:stripped_jungle_cabinet","mcwbiomesoplenty:redwood_tropical_door","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","railcraft:lead_gear","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","additionallanterns:normal_lantern_light_gray","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","dyenamics:rose_wool","productivebees:stonecutter/driftwood_canvas_hive","mcwbridges:cobblestone_bridge_stair","mcwbiomesoplenty:mahogany_bookshelf_drawer","minecraft:red_nether_bricks","dyenamics:bed/amber_bed","mcwdoors:acacia_western_door","dyenamics:peach_stained_glass","mcwtrpdoors:spruce_whispering_trapdoor","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","botania:conversions/green_petal_block_deconstruct","botania:magnet_ring","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","botania:livingwood_bow","modularrouters:fluid_module","mcwlights:purple_lamp","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","mcwroofs:purple_terracotta_roof","mcwfurnitures:stripped_jungle_chair","botania:spark_changer","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","minecraft:mossy_cobblestone_slab","rftoolsbase:machine_frame","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","cfm:stripped_spruce_crate","railcraft:brass_gear","forbidden_arcanus:diamond_blacksmith_gavel","domum_ornamentum:light_blue_brick_extra","chemlib:manganese_ingot_from_smelting_manganese_dust","mcwpaths:stone_windmill_weave_slab","mcwroofs:thatch_top_roof","supplementaries:cog_block","twigs:polished_schist_stonecutting","pneumaticcraft:armor_upgrade","minecraft:lime_concrete_powder","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","mcwlights:festive_lantern","mcwbiomesoplenty:magic_tropical_door","botania:conversions/gray_petal_block_deconstruct","securitycraft:block_change_detector","advanced_ae:advpatpro2","minecraft:netherite_scrap_from_blasting","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","undergarden:wigglewood_boat","minecraft:book","handcrafted:blue_sheet","mcwfurnitures:oak_drawer_counter","create:crafting/kinetics/flywheel","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","travelersbackpack:standard_smithing","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","mekanism:processing/gold/ingot/from_dust_smelting","connectedglass:scratched_glass_cyan_pane2","pneumaticcraft:pressure_chamber_glass_x4","botania:dreamwood_slab","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","mcwfences:acacia_picket_fence","domum_ornamentum:sand_bricks","allthecompressed:compress/spruce_log_1x","ae2:network/cables/dense_covered_cyan","mcwbiomesoplenty:palm_beach_door","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","minecraft:cracked_polished_blackstone_bricks","matc:crystals/tertium","pneumaticcraft:wall_lamp_inverted_purple","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","mcwbiomesoplenty:magic_bamboo_door","ae2:misc/tiny_tnt","botania:shimmerrock","comforts:hammock_to_light_blue","ae2:decorative/quartz_glass","minecraft:red_terracotta","mcwdoors:jungle_japanese_door","handcrafted:spruce_couch","connectedglass:clear_glass1","mcwbiomesoplenty:mahogany_ranch_trapdoor","mcwroofs:white_roof_block","mcwbiomesoplenty:mahogany_stable_door","travelersbackpack:melon","securitycraft:block_pocket_manager","create:crafting/kinetics/encased_chain_drive","occultism:crafting/chalk_red_impure","mcwlights:soul_cherry_tiki_torch","botania:red_pavement","securitycraft:reinforced_mangrove_fence","alltheores:copper_ore_hammer","dyenamics:conifer_stained_glass","productivebees:stonecutter/crimson_canvas_hive","cfm:light_gray_trampoline","mcwlights:red_lamp","bloodmagic:blood_altar","botania:flower_bag","botania:black_shiny_flower","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","quark:tweaks/crafting/utility/bent/paper","railcraft:bag_of_cement","mcwlights:light_blue_lamp","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwbiomesoplenty:fir_plank_window2","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","create:spruce_window","pneumaticcraft:block_tracker_upgrade","connectedglass:clear_glass_gray2","mcwfurnitures:stripped_spruce_counter","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","biomesoplenty:mahogany_wood","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","botania:livingwood_twig","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:bowl","mcwwindows:ender_brick_arrow_slit","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","minecraft:purple_concrete_powder","mcwfences:bastion_metal_fence","mcwbiomesoplenty:mahogany_wardrobe","mcwwindows:dark_oak_plank_window2","allthecompressed:compress/copper_block_1x","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","minecraft:red_carpet","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","supplementaries:candle_holders/candle_holder_light_gray","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:blue_terracotta_steep_roof","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","expatternprovider:pre_bus","allthearcanistgear:vibranium_hat_smithing","mcwroofs:oak_planks_attic_roof","bigreactors:crafting/graphite_component_to_storage","dyenamics:amber_terracotta","mcwdoors:spruce_beach_door","alchemistry:reactor_output","sophisticatedstorage:basic_to_copper_tier_upgrade","twigs:smooth_basalt_bricks","aether:white_cape","aether:netherite_pickaxe_repairing","botania:elementium_hoe","mcwbiomesoplenty:mahogany_cupboard_counter","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwfences:panelled_metal_fence_gate","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwtrpdoors:spruce_classic_trapdoor","aquaculture:planks_from_driftwood","mcwfurnitures:oak_drawer","botania:bifrost_perm","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","minecraft:hopper","mcwroofs:base_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","corail_woodcutter:mangrove_woodcutter","mcwpaths:brick_dumble_paving","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","minecraft:iron_helmet","mcwbiomesoplenty:mahogany_barn_glass_door","create:crafting/kinetics/cyan_seat","mcwroofs:lime_concrete_attic_roof","railcraft:manual_rolling_machine","minecraft:cartography_table","ad_astra:launch_pad","rftoolsutility:charged_porter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","pneumaticcraft:compressed_brick_tile","cfm:stripped_acacia_desk_cabinet","ae2:tools/fluix_shovel","reliquary:magicbane","pneumaticcraft:wall_lamp_inverted_magenta","ae2:block_cutter/stairs/sky_stone_stairs","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwwindows:green_curtain","botania:petal_light_gray","mcwwindows:stone_window","mcwroofs:gray_steep_roof","rftoolspower:endergenic","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","productivebees:stonecutter/yucca_canvas_hive","mcwtrpdoors:jungle_classic_trapdoor","additionallanterns:normal_lantern_cyan","minecraft:light_blue_banner","connectedglass:scratched_glass_gray_pane2","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","mcwroofs:yellow_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_flagstone_path","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","rftoolsbuilder:shape_card_pump_dirt","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:magic_mystic_door","botania:petal_red","minecraft:netherite_pickaxe_smithing","farmersdelight:cooking/fried_rice","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","enderio:resetting_lever_ten","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","botania:conversions/white_petal_block_deconstruct","farmersdelight:honey_cookie","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","botania:thunder_sword","minecraft:polished_deepslate","connectedglass:borderless_glass_yellow_pane2","simplylight:illuminant_gray_block_on_toggle","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","travelersbackpack:netherite","mcwwindows:quartz_pane_window","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","botania:gray_shiny_flower","mcwbiomesoplenty:magic_pyramid_gate","cfm:spruce_desk_cabinet","pneumaticcraft:air_grate_module","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","supplementaries:stonecutting/blackstone_tile","minecraft:dye_red_carpet","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","twigs:polished_rhyolite","productivebees:nests/gravel_nest_clear","undergarden:polished_depthrock_slab_stonecutting","additionallanterns:stone_bricks_chain","mcwbridges:mossy_cobblestone_bridge_pier","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","botania:petal_orange","botania:black_petal_block","mcwfences:spruce_wired_fence","handcrafted:andesite_corner_trim","chemlib:calcium_ingot_to_block","mcwroofs:magenta_terracotta_top_roof","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","mcwbiomesoplenty:stripped_mahogany_double_wardrobe","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","securitycraft:claymore","railcraft:golden_ticket","ae2:network/cables/dense_smart_fluix","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwbiomesoplenty:redwood_glass_door","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","productivebees:stonecutter/hellbark_canvas_hive","mcwfurnitures:spruce_modern_wardrobe","mcwtrpdoors:spruce_barn_trapdoor","ae2:network/blocks/pattern_providers_interface_part","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","botania:manasteel_boots","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","mcwbiomesoplenty:maple_beach_door","enderio:resetting_lever_ten_from_inv","mcwbiomesoplenty:magic_four_panel_door","botania:monocle","mcwroofs:deepslate_lower_roof","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","minecraft:cookie","aquaculture:spruce_fish_mount","minecraft:lightning_rod","mysticalagriculture:tertium_essence_uncraft","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","minecraft:shears","dyenamics:lavender_terracotta","chemlib:gallium_nugget_to_ingot","mcwroofs:jungle_planks_top_roof","bloodmagic:blood_rune_blank","ae2:tools/certus_quartz_hoe","minecraft:yellow_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:spruce_drawer_counter","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","create:crafting/appliances/filter_clear","mcwbiomesoplenty:palm_barn_door","cfm:spruce_desk","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","mcwbiomesoplenty:palm_mystic_door","deeperdarker:echo_chest_boat","cfm:stripped_spruce_park_bench","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","ad_astra:zip_gun","ae2:network/cables/covered_magenta","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","botania:chiseled_mana_quartz","supplementaries:turn_table","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwdoors:acacia_whispering_door","twilightforest:sorting_boat","mcwbiomesoplenty:jacaranda_cottage_door","mythicbotany:alfsteel_nugget_compress","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","ae2:network/cables/covered_gray","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","occultism:crafting/goggles","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","mcwfurnitures:stripped_spruce_drawer_counter","mcwpaths:stone_windmill_weave_path","mcwwindows:gray_curtain","undergarden:depthrock_tiles_stonecutting","minecraft:brewing_stand","allthecompressed:compress/oak_planks_1x","enderio:redstone_filter_base","twigs:bloodstone","productivebees:expansion_boxes/expansion_box_oak_canvas","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","occultism:crafting/spirit_attuned_crystal","supplementaries:bamboo_spikes","rftoolsbuilder:vehicle_status_module","ae2:network/cables/smart_light_gray","mcwbiomesoplenty:stripped_palm_log_four_window","handcrafted:spruce_bench","twigs:spruce_table","mcwroofs:gutter_base_light_blue","create:crafting/kinetics/gearboxfrom_conversion","mcwpaths:andesite_crystal_floor_path","create:crafting/kinetics/orange_seat","rftoolsutility:syringe","minecraft:diamond_block","mcwroofs:deepslate_roof","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","chemlib:sodium_ingot_to_nugget","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","bigreactors:fluidizer/outputport","botania:gravity_rod","additionallanterns:normal_lantern_magenta","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","sophisticatedbackpacks:restock_upgrade","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","croptopia:sweet_crepes","minecraft:lapis_block","aether:skyroot_chest","deepresonance:filter_material","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","rftoolsstorage:modular_storage","handcrafted:bricks_corner_trim","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","mcwroofs:pink_terracotta_attic_roof","dyenamics:spring_green_dye","mcwroofs:light_gray_attic_roof","minecraft:oak_boat","occultism:crafting/book_of_calling_foliot_cleaner","rftoolsbuilder:shape_card_pump","mcwfurnitures:stripped_jungle_covered_desk","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","immersiveengineering:crafting/plate_lead_hammering","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwbiomesoplenty:pine_paper_door","botania:manasteel_shears","ae2:network/cables/smart_green","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","botania:elementium_pickaxe","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","minecraft:glass_bottle","travelersbackpack:dye_gray_sleeping_bag","minecraft:iron_ingot_from_smelting_raw_iron","littlelogistics:fishing_barge","mcwbiomesoplenty:magic_plank_window2","undergarden:depthrock_wall","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","sophisticatedbackpacks:stack_downgrade_tier_2","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","pneumaticcraft:air_canister","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","mcwbridges:brick_bridge_pier","silentgear:crimson_iron_ingot_from_block","cfm:stripped_spruce_desk_cabinet","mekanism:control_circuit/advanced","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","silentgear:crimson_iron_nugget","mcwfences:end_brick_pillar_wall","dyenamics:ultramarine_dye","dyenamics:honey_dye","mcwwindows:prismarine_brick_arrow_slit","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","delightful:knives/elementium_knife","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:purple_dye","mcwroofs:spruce_steep_roof","mcwfurnitures:jungle_modern_chair","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","handcrafted:phantom_trophy","mcwroofs:cyan_terracotta_upper_steep_roof","mcwroofs:stone_bricks_roof","cfm:cyan_grill","thermal_extra:smelting/twinite_ingot_from_dust_smelting","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","mcwbiomesoplenty:palm_bamboo_door","ae2:network/cables/smart_black","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","mcwbiomesoplenty:stripped_mahogany_drawer_counter","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","botania:pink_shiny_flower","mcwbridges:spruce_log_bridge_middle","undergarden:stonecutter_from_depthrock","cfm:dark_oak_mail_box","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","farmersdelight:chicken_sandwich","mcwbiomesoplenty:palm_swamp_door","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","chemlib:helium_lamp_block","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","handcrafted:blue_bowl","mcwfurnitures:stripped_oak_double_wardrobe","minecraft:polished_blackstone_brick_slab","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","ae2:network/cables/smart_orange","dyenamics:dye_amber_carpet","chemlib:tantalum_block_to_ingot","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","mcwbiomesoplenty:magic_pane_window","mythicbotany:alfsteel_ingot_compress","simplylight:illuminant_block_on_toggle","twigs:polished_tuff_bricks_from_tuff_stonecutting","supplementaries:fodder","sophisticatedbackpacks:stonecutter_upgrade","chemlib:gallium_block_to_ingot","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:fir_swamp_door","mcwfurnitures:stripped_oak_glass_table","mcwroofs:blackstone_attic_roof","utilitarian:utility/jungle_logs_to_doors","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","reliquary:ender_staff","mcwwindows:diorite_louvered_shutter","mcwfurnitures:oak_end_table","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","mcwfurnitures:stripped_spruce_bookshelf_drawer","mcwbiomesoplenty:mahogany_roof","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwfurnitures:jungle_modern_desk","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","ae2:materials/advancedcard","mcwlights:gray_lamp","dyenamics:maroon_stained_glass","minecraft:oak_wood","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwbiomesoplenty:redwood_bamboo_door","immersiveengineering:crafting/hempcrete","minecraft:spruce_wood","botania:elementium_chestplate","minecraft:spruce_fence","aquaculture:sushi","mcwdoors:metal_reinforced_door","silentgear:crimson_iron_dust_smelting","mcwbiomesoplenty:magic_japanese2_door","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","ae2:tools/fluix_upgrade_smithing_template","botania:petal_light_blue_double","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","mcwlights:jungle_ceiling_fan_light","bigreactors:turbine/basic/activetap_fe","ae2:network/cables/covered_light_gray","mcwroofs:gutter_base_lime","everythingcopper:copper_anvil","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","mcwbiomesoplenty:palm_picket_fence","handcrafted:jungle_table","minecraft:andesite_wall_from_andesite_stonecutting","botania:abstruse_platform","mcwfences:crimson_stockade_fence","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","deeperdarker:bloom_chest_boat","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","mcwbiomesoplenty:empyreal_barn_door","botania:dye_pink","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","additionallanterns:stone_lantern","supplementaries:flags/flag_red","cfm:dye_blue_picket_fence","minecraft:dye_white_bed","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:light_gray_terracotta_steep_roof","mcwbiomesoplenty:hellbark_nether_door","mcwfurnitures:oak_desk","minecraft:blue_terracotta","mcwroofs:purple_terracotta_lower_roof","supplementaries:stone_lamp","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","chemlib:titanium_nugget_to_ingot","mcwbiomesoplenty:empyreal_paper_door","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","minecraft:gray_terracotta","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","mcwbiomesoplenty:mahogany_triple_drawer","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","dyenamics:peach_terracotta","botania:livingrock_wall","aiotbotania:livingwood_shears","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","minecraft:terracotta","delightful:knives/silver_knife","mcwwindows:acacia_blinds","productivebees:stonecutter/snake_block_canvas_expansion_box","mcwwindows:dark_oak_window2","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","connectedglass:scratched_glass_yellow2","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","twigs:copper_pillar_stonecutting","blue_skies:lunar_bookshelf","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","productivebees:stonecutter/redwood_canvas_hive","minecraft:beacon","minecraft:tnt","mcwdoors:spruce_stable_door","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:dye_yellow_bed","minecraft:flint_and_steel","railways:crafting/smokestack_streamlined","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","botania:petal_purple_double","mcwfurnitures:stripped_jungle_double_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","xnet:advanced_connector_blue_dye","mcwfurnitures:spruce_cupboard_counter","mcwbiomesoplenty:umbran_bamboo_door","mcwbiomesoplenty:empyreal_plank_pane_window","botania:purple_shiny_flower","mcwdoors:oak_bamboo_door","travelersbackpack:hay","xnet:advanced_connector_red_dye","mcwroofs:brown_striped_awning","mcwbiomesoplenty:mahogany_whispering_trapdoor","minecraft:brown_concrete_powder","productivebees:expansion_boxes/expansion_box_snake_block_canvas","mcwbiomesoplenty:stripped_mahogany_modern_desk","securitycraft:reinforced_lime_stained_glass_pane_from_dye","supplementaries:globe_sepia","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","simplylight:bulb","travelersbackpack:cactus","mcwwindows:granite_four_window","cfm:door_mat","mcwroofs:light_blue_concrete_steep_roof","securitycraft:reinforced_nether_brick_fence","supplementaries:stone_tile","supplementaries:item_shelf","handcrafted:terracotta_plate","mcwbiomesoplenty:willow_barn_glass_door","mcwroofs:light_blue_terracotta_roof","allthemodium:vibranium_gear","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:willow_plank_window","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","supplementaries:timber_brace","alltheores:iridium_ingot_from_raw_blasting","handcrafted:jungle_nightstand","domum_ornamentum:gray_brick_extra","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","railways:crafting/smokestack_oilburner","securitycraft:sonic_security_system","supplementaries:bed_from_feather_block","mcwbiomesoplenty:stripped_mahogany_striped_chair","connectedglass:borderless_glass_magenta2","connectedglass:borderless_glass_gray2","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","mcwlights:copper_chain","additionallanterns:normal_lantern_colorless","blue_skies:tool_box","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","cfm:orange_kitchen_drawer","mcwfurnitures:stripped_spruce_cupboard_counter","mcwroofs:spruce_planks_top_roof","botania:glimmering_livingwood","ae2:network/cables/dense_smart_from_smart","ae2:network/cables/covered_green","handcrafted:spruce_pillar_trim","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","megacells:network/mega_pattern_provider","twigs:smooth_basalt_bricks_stonecutting","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","dyenamics:conifer_candle","gtceu:shapeless/red_alloy_cable_1","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwlights:pink_paper_lamp","ad_astra:white_industrial_lamp","immersiveengineering:crafting/wire_aluminum","occultism:crafting/divination_rod","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","dyenamics:bubblegum_terracotta","utilitix:hand_bell","rftoolscontrol:workbench","ad_astra:steel_plating","minecraft:chiseled_stone_bricks","cfm:dye_gray_picket_fence","mcwroofs:stone_upper_lower_roof","bigreactors:turbine/basic/bearing","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","mcwroofs:cyan_striped_awning","dyenamics:dye_mint_carpet","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","undergarden:depthrock_pebble_stonecutting","minecraft:spruce_pressure_plate","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","connectedglass:clear_glass_black_pane2","mcwfences:railing_stone_brick_wall","railcraft:steel_pickaxe","mcwroofs:jungle_planks_roof","cfm:dye_gray_picket_gate","handcrafted:spruce_nightstand","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","sfm:cable","mcwbiomesoplenty:palm_japanese_door","minecraft:hay_block","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:jacaranda_barn_glass_door","mcwwindows:red_sandstone_window2","botania:phantom_ink","mcwbiomesoplenty:palm_pyramid_gate","railways:crafting/smokestack_coalburner","create:jungle_window","twigs:mossy_cobblestone_brick_slab_from_mossy_cobblestone_stonecutting","mekanism:crusher","chemlib:calcium_ingot_from_smelting_calcium_dust","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_planks_attic_roof","mcwroofs:blue_concrete_top_roof","evilcraft:smelting/hardened_blood_shard","aquaculture:jungle_fish_mount","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","mcwbiomesoplenty:mahogany_paper_trapdoor","mcwdoors:oak_waffle_door","bigreactors:blasting/graphite_from_charcoal","botania:red_petal_block","minecraft:cut_copper","supplementaries:flags/flag_orange","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","comforts:hammock_cyan","ae2:network/cables/covered_black","modularrouters:augment_core","mcwbiomesoplenty:stripped_mahogany_modern_chair","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","pneumaticcraft:liquid_compressor","ad_astra:reinforced_door","connectedglass:clear_glass_red_pane2","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","mcwroofs:black_concrete_top_roof","enderio:basic_item_filter","ae2:network/blocks/inscribers","mcwwindows:granite_louvered_shutter","ad_astra:red_flag","ad_astra:small_red_industrial_lamp","securitycraft:sentry","botania:tornado_rod","minecraft:netherite_block","alltheores:uranium_rod","cfm:cyan_kitchen_counter","mcwwindows:blue_mosaic_glass_pane","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","connectedglass:tinted_borderless_glass_yellow2","mcwbiomesoplenty:mahogany_desk","ae2:network/cables/dense_covered_gray","minecraft:crossbow","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","occultism:crafting/otherstone_frame","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwfences:dark_oak_horse_fence","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","expatternprovider:epp_alt","handcrafted:yellow_crockery_combo","create:crafting/curiosities/peculiar_bell","mcwbiomesoplenty:umbran_mystic_door","securitycraft:reinforced_granite","immersiveengineering:crafting/metal_ladder_none","alltheores:aluminum_dust_from_hammer_crushing","allthemodium:vibranium_nugget_from_ingot","mcwroofs:stone_bricks_lower_roof","chemlib:tantalum_ingot_to_nugget","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","aiotbotania:livingrock_hoe","connectedglass:scratched_glass_red_pane2","rftoolsutility:redstone_information","blue_skies:dusk_bookshelf","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:network/cables/glass_light_gray","minecraft:mossy_cobblestone_stairs","mcwbiomesoplenty:mahogany_covered_desk","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","twigs:mixed_bricks_stonecutting","minecraft:acacia_hanging_sign","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","connectedglass:scratched_glass_blue2","minecraft:blue_bed","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","create:crafting/kinetics/mechanical_harvester","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","chemlib:magnesium_ingot_to_nugget","minecraft:red_stained_glass","minecraft:light_gray_stained_glass_pane_from_glass_pane","mcwpaths:cobbled_deepslate_crystal_floor_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","mcwpaths:cobbled_deepslate_crystal_floor","mcwbiomesoplenty:magic_stable_door","connectedglass:borderless_glass_blue_pane2","mcwbiomesoplenty:mahogany_modern_desk","botania:yellow_petal_block","mcwfurnitures:cabinet_door","sophisticatedbackpacks:tool_swapper_upgrade","mcwwindows:stripped_spruce_pane_window","twigs:mossy_cobblestone_bricks_stonecutting","twigs:smooth_basalt_brick_slab_from_smooth_basalt_stonecutting","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","delightful:food/marshmallow_stick","minecraft:lime_terracotta","mcwbiomesoplenty:mahogany_drawer","mcwpaths:cobbled_deepslate_strewn_rocky_path","cfm:stripped_jungle_desk_cabinet","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","dyenamics:wine_dye","handcrafted:bench","minecraft:gray_stained_glass_pane_from_glass_pane","gravitationalmodulatingunittweaks:module_gravitational_modulating_additional_unit","minecraft:stone_slab","mcwbiomesoplenty:magic_classic_door","productivebees:stonecutter/comb_canvas_hive","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","botania:petal_cyan","mcwbiomesoplenty:maple_four_panel_door","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","travelersbackpack:backpack_tank","minecraft:jungle_stairs","mcwbridges:glass_bridge","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","enderio:resetting_lever_ten_inv_from_base","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","dyenamics:bed/mint_bed_frm_white_bed","croptopia:campfire_molasses","mcwbiomesoplenty:pine_four_panel_door","ae2:network/upgrade_wireless_crafting_terminal","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","utilitix:weak_redstone_torch","mcwbiomesoplenty:mahogany_planks_steep_roof","mcwroofs:blackstone_roof","botania:red_shiny_flower","croptopia:melon_juice","occultism:crafting/chalk_gold_impure","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","industrialforegoing:machine_frame_pity","securitycraft:mine","mcwbiomesoplenty:jacaranda_glass_door","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","gtceu:shaped/rubber_boat","mcwbiomesoplenty:maple_stockade_fence","botania:starfield","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","mekanism:tier_installer/advanced","mcwdoors:spruce_waffle_door","simplylight:illuminant_purple_block_on_toggle","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","botania:yellow_pavement","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","create:crafting/kinetics/yellow_seat","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","mcwpaths:andesite_windmill_weave_path","mcwbiomesoplenty:palm_glass_door","mcwdoors:spruce_western_door","silentgear:sturdy_repair_kit","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","cfm:stripped_spruce_cabinet","minecraft:mangrove_boat","mcwbiomesoplenty:mahogany_upper_steep_roof","mcwroofs:green_terracotta_steep_roof","allthecompressed:compress/hay_block_1x","mcwdoors:garage_silver_door","comforts:hammock_white","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwfurnitures:stripped_oak_cupboard_counter","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","botania:water_rod","mcwroofs:gray_concrete_lower_roof","botania:spectral_platform","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","undergarden:smoke_gloomper_leg","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","handcrafted:red_sheet","sophisticatedstorage:paintbrush","biomesoplenty:willow_boat","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwwindows:stone_brick_arrow_slit","mcwfurnitures:jungle_desk","mcwbiomesoplenty:umbran_waffle_door","mcwroofs:jungle_planks_lower_roof","croptopia:ham_sandwich","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwwindows:dark_oak_louvered_shutter","mcwfurnitures:oak_covered_desk","mcwbiomesoplenty:redwood_barn_door","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwbiomesoplenty:palm_modern_door","rftoolsutility:counter_module","botania:lens_redirect","cfm:stripped_jungle_crate","minecraft:amethyst_block","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","pneumaticcraft:logistics_frame_passive_provider_self","mcwroofs:black_roof_block","botania:elementium_shovel","connectedglass:clear_glass_cyan_pane2","mythicbotany:fire_ring","mcwlights:warped_ceiling_fan_light","botania:prism","mcwwindows:window_centre_bar_base","modularrouters:sender_module_2_x4","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","handcrafted:oak_corner_trim","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","ae2:tools/fluix_sword","simplemagnets:basic_demagnetization_coil","rftoolspower:pearl_injector","mcwwindows:pink_curtain","mcwlights:soul_jungle_tiki_torch","productivebees:stonecutter/rosewood_canvas_expansion_box","mcwwindows:light_gray_curtain","securitycraft:limited_use_keycard","delightful:knives/refined_obsidian_knife","twigs:cobblestone_bricks_stonecutting","pylons:harvester_pylon","minecraft:wheat","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","mcwbiomesoplenty:mahogany_four_panel_trapdoor","ae2:network/cables/glass_fluix_clean","minecraft:bow","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","deepresonance:radiation_suit_boots","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","crafting_on_a_stick:smithing_table","undergarden:depthrock_stairs","chemlib:periodic_table","minecraft:andesite_stairs","botania:super_lava_pendant","handcrafted:bear_trophy","cfm:black_cooler","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","botania:spark","minecraft:dark_oak_boat","dyenamics:maroon_stained_glass_pane_from_glass_pane","chemlib:potassium_ingot_from_smelting_potassium_dust","mcwbiomesoplenty:palm_wired_fence","occultism:crafting/sacrificial_bowl","mcwfurnitures:spruce_glass_table","dyenamics:bed/aquamarine_bed_frm_white_bed","sophisticatedbackpacks:feeding_upgrade","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","create:crafting/kinetics/clutch","mcwfurnitures:spruce_covered_desk","cfm:stripped_acacia_desk","minecraft:firework_rocket_simple","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","mcwdoors:spruce_mystic_door","sophisticatedstorage:backpack_advanced_void_upgrade_from_storage_advanced_void_upgrade","biomesoplenty:mahogany_planks","mcwtrpdoors:spruce_beach_trapdoor","mcwbiomesoplenty:redwood_mystic_door","enderio:enderios","botania:black_pavement","supplementaries:pedestal","travelersbackpack:blaze","cfm:lime_trampoline","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","twigs:smooth_basalt_brick_stairs_from_smooth_basalt_stonecutting","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","alltheores:ruby_dust_from_hammer_crushing","aiotbotania:livingrock_shears","forbidden_arcanus:wooden_blacksmith_gavel","allthemodium:smithing/allthemodium_pickaxe_smithing","mcwdoors:metal_windowed_door","twigs:lamp","minecraft:stone","mcwroofs:spruce_upper_steep_roof","travelersbackpack:red_sleeping_bag","comforts:sleeping_bag_blue","cfm:stripped_oak_kitchen_sink_dark","minecraft:dye_cyan_bed","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","mcwroofs:brown_terracotta_steep_roof","create:crafting/appliances/tree_fertilizer","rftoolsutility:text_module","dyenamics:dye_bubblegum_carpet","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","dyenamics:dye_aquamarine_carpet","connectedglass:clear_glass_black2","mcwbiomesoplenty:mahogany_double_wardrobe","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","undergarden:depthrock_pressure_plate","sfm:printing_press","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","ae2:network/cables/dense_smart_light_gray","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","minecraft:birch_chest_boat","mcwbridges:mossy_stone_bridge_pier","cfm:stripped_oak_bedside_cabinet","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","littlelogistics:tug","create:crafting/kinetics/mechanical_press","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","mcwpaths:brick_strewn_rocky_path","enderio:resetting_lever_three_hundred","mcwlights:cross_lantern","mcwlights:brown_lamp","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","immersiveengineering:crafting/hemp_fabric","minecraft:green_candle","botania:shimmerrock_stairs","chemlib:magnesium_block_to_ingot","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","pneumaticcraft:refinery_output","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","ae2:network/parts/terminals_pattern_access","additionallanterns:diamond_chain","handcrafted:yellow_bowl","supplementaries:stonecutting/stone_tile","ae2:network/cables/covered_pink","railcraft:steel_tank_wall","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","rftoolsbuilder:shape_card_quarry_silk","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","undergarden:depthrock_tile_stairs_stonecutting","immersiveengineering:crafting/coil_mv","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwpaths:mossy_stone_flagstone_stairs","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","cfm:stripped_jungle_kitchen_drawer","railcraft:age_detector","pneumaticcraft:wall_lamp_orange","mcwwindows:black_curtain","productivebees:stonecutter/wisteria_canvas_expansion_box","botania:pixie_ring","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwbiomesoplenty:mahogany_double_drawer","blue_skies:ventium_bucket","botania:drum_gathering","sophisticatedstorage:storage_stack_upgrade_tier_3_from_backpack_stack_upgrade_tier_2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","cfm:magenta_grill","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","productivebees:hives/advanced_dark_oak_canvas_hive","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","nethersdelight:blackstone_stove","enderio:resetting_lever_five_inv","handcrafted:spruce_corner_trim","utilitix:armed_stand","botania:lexicon","handcrafted:yellow_cup","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwbiomesoplenty:redwood_beach_door","ae2:network/cables/smart_blue","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","pneumaticcraft:pressure_gauge_module","botania:turntable","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","railcraft:steel_chestplate","supplementaries:daub","alltheores:zinc_dust_from_hammer_crushing","minecraft:netherite_scrap","mcwfurnitures:stripped_jungle_end_table","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","alltheores:tin_dust_from_hammer_ingot_crushing","ae2:decorative/quartz_block","mcwbiomesoplenty:magic_cottage_door","ae2:network/cables/dense_smart_orange","mcwpaths:stone_flagstone","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","immersiveengineering:crafting/coil_lv","biomesoplenty:mahogany_pressure_plate","mcwlights:brown_paper_lamp","connectedglass:borderless_glass_white_pane2","minecraft:campfire","dyenamics:peach_stained_glass_pane_from_glass_pane","cfm:white_kitchen_sink","mcwbiomesoplenty:stripped_mahogany_lower_bookshelf_drawer","cfm:dye_yellow_picket_fence","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","blue_skies:bluebright_bookshelf","mcwbiomesoplenty:mahogany_beach_door","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","mysticalagriculture:unattuned_augment","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:9692,warning_level:0}}