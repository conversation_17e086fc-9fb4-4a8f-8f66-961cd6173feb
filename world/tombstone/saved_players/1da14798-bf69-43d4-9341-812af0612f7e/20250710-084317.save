{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:4.5d,Name:"forge:block_reach"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:3.0d,Name:"forge:entity_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"attributeslib:creative_flight"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:11968},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:0b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"pneumaticcraft:logistics_frame_requester",ItemStack:{Count:8b,id:"pneumaticcraft:logistics_frame_requester"}}],SelectedRecipe:"pneumaticcraft:logistics_frame_requester"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:93,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["farmersdelight:hamburger"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:1,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},apoth_reforge_seed:0,"quark:trying_crawl":0b},Health:20.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"tombstone:gift",tag:{Items:[{Count:1b,id:"tombstone:grave_dust"}]}},{Count:1b,Slot:1b,id:"patchouli:guide_book",tag:{"patchouli:book":"allthemodium:allthemodium_book"}},{Count:6b,Slot:2b,id:"minecraft:ladder"},{Count:3b,Slot:3b,id:"minecraft:prismarine_bricks"},{Count:5b,Slot:4b,id:"minecraft:prismarine"},{Count:1b,Slot:5b,id:"pneumaticcraft:logistics_frame_requester",tag:{EntityTag:{filters:{Items:[],Size:27},fluidFilters:{filters:[{Amount:0,FluidName:"minecraft:empty"},{Amount:0,FluidName:"minecraft:empty"},{Amount:0,FluidName:"minecraft:empty"},{Amount:0,FluidName:"minecraft:empty"},{Amount:0,FluidName:"minecraft:empty"},{Amount:0,FluidName:"minecraft:empty"},{Amount:0,FluidName:"minecraft:empty"},{Amount:0,FluidName:"minecraft:empty"},{Amount:0,FluidName:"minecraft:empty"}],size:9},fluidWhitelist:1b,minFluid:1,minItems:1,side:3,whitelist:1b}}},{Count:1b,Slot:6b,id:"sophisticatedbackpacks:diamond_backpack",tag:{contentsUuid:[I;1112404195,660490516,-1944875632,-424770120],inventorySlots:108,openTabId:0,renderInfo:{upgradeItems:[{Count:1b,id:"sophisticatedbackpacks:advanced_feeding_upgrade"},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b}}}]},upgradeSlots:5}},{Count:1b,Slot:7b,id:"minecraft:blue_dye"},{Count:8b,Slot:9b,id:"minecraft:torch"},{Count:1b,Slot:10b,id:"minecraft:dark_prismarine"},{Count:1b,Slot:11b,id:"sophisticatedbackpacks:magnet_upgrade"},{Count:5b,Slot:12b,id:"minecraft:string"},{Count:1b,Slot:13b,id:"minecraft:porkchop"},{Count:2b,Slot:14b,id:"enderio:dark_steel_ingot"},{Count:8b,Slot:15b,id:"minecraft:cooked_beef"},{Count:64b,Slot:16b,id:"minecraft:oak_planks"},{Count:7b,Slot:17b,id:"pneumaticcraft:logistics_frame_requester"},{Count:3b,Slot:18b,id:"deepresonance:resonating_ore_deepslate"},{Count:1b,Slot:19b,id:"gtceu:rubber_log"},{Count:6b,Slot:20b,id:"minecraft:coal"},{Count:2b,Slot:21b,id:"minecraft:mutton"},{Count:1b,Slot:22b,id:"minecraft:spruce_wood"},{Count:16b,Slot:23b,id:"minecraft:gold_ingot"},{Count:47b,Slot:24b,id:"minecraft:oak_planks"},{Count:19b,Slot:25b,id:"minecraft:stick"},{Count:3b,Slot:26b,id:"gtceu:rubber_sapling"},{Count:8b,Slot:27b,id:"minecraft:slime_ball"},{Count:3b,Slot:28b,id:"minecraft:chest"},{Count:1b,Slot:29b,id:"minecraft:map"},{Count:14b,Slot:30b,id:"gtceu:sticky_resin"},{Count:10b,Slot:31b,id:"minecraft:glass"}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:overworld",pos:[I;80,33,2]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-1143.133749826725d,253.0d,-772.6590217806272d],Railways_DataVersion:2,Rotation:[-171.30273f,11.999886f],Score:300,SelectedItemSlot:6,SleepTimer:0s,Spigot.ticksLived:11966,UUID:[I;497108888,-1083620396,-1824423638,-262066306],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-314185450802947L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:14,XpP:0.57142806f,XpSeed:0,XpTotal:300,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752107000096L,keepLevel:0b,lastKnownName:"Muaia",lastPlayed:1752108197098L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:1.8668115f,foodLevel:20,foodSaturationLevel:11.6f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwwindows:birch_log_parapet","cfm:yellow_cooler","mcwfences:granite_railing_gate","minecraft:charcoal","additionallanterns:copper_lantern","handcrafted:blue_bowl","mcwpaths:dark_prismarine_running_bond_slab","mcwlights:golden_small_chandelier","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","dyenamics:banner/lavender_banner","mcwpaths:dark_prismarine_running_bond","mcwbiomesoplenty:redwood_highley_gate","handcrafted:spider_trophy","mcwpaths:dark_prismarine_dumble_paving","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","domum_ornamentum:purple_floating_carpet","cfm:fridge_dark","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","xnet:connector_green","mcwwindows:crimson_blinds","create:oak_window","create:crafting/appliances/crafting_blueprint","dyenamics:banner/aquamarine_banner","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","utilitix:reinforced_rail","handcrafted:oak_desk","mcwbiomesoplenty:empyreal_hedge","xnet:connector_blue_dye","mcwbiomesoplenty:hellbark_picket_fence","mcwwindows:oak_log_parapet","mcwwindows:diorite_louvered_shutter","corail_woodcutter:crimson_woodcutter","mcwwindows:crimson_plank_parapet","supplementaries:notice_board","minecraft:coal_block","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","simplylight:illuminant_light_blue_block_dyed","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","mcwwindows:black_mosaic_glass","simplylight:illuminant_lime_block_toggle","additionallanterns:gold_chain","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","minecraft:cooked_porkchop","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","securitycraft:reinforced_cherry_fence_gate","dyenamics:maroon_stained_glass","mcwfences:acacia_hedge","securitycraft:reinforced_acacia_fence","minecraft:golden_apple","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","energymeter:meter","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwlights:covered_lantern","simplylight:rodlamp","sophisticatedstorage:oak_chest","xnet:connector_red_dye","minecolonies:shapetool","mcwwindows:dark_prismarine_pane_window","cfm:blue_cooler","supplementaries:crank","mcwwindows:oak_plank_parapet","aquaculture:iron_nugget_from_smelting","minecraft:magenta_dye_from_blue_red_white_dye","minecraft:green_stained_glass","aquaculture:sushi","mcwwindows:andesite_louvered_shutter","domum_ornamentum:blue_cobblestone_extra","rftoolsbuilder:green_shield_template_block","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:dark_prismarine_slab","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","aquaculture:birch_fish_mount","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","mcwfences:prismarine_railing_gate","pneumaticcraft:logistics_module","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","mcwwindows:crimson_louvered_shutter","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","handcrafted:blue_plate","simplylight:illuminant_red_block_toggle","sophisticatedstorage:generic_limited_barrel_4","securitycraft:reinforced_gray_stained_glass_pane_from_glass","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwwindows:dark_oak_blinds","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","dyenamics:mint_stained_glass","nethersdelight:golden_machete","minecraft:prismarine_brick_stairs_from_prismarine_stonecutting","mcwwindows:acacia_curtain_rod","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwlights:chain_lantern","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","minecraft:golden_boots","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","cfm:black_kitchen_drawer","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwbiomesoplenty:umbran_curved_gate","corail_woodcutter:dark_oak_woodcutter","mcwfences:modern_nether_brick_wall","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwpaths:dark_prismarine_crystal_floor_path","minecraft:prismarine_stairs","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwwindows:mangrove_blinds","minecraft:golden_hoe","cfm:oak_kitchen_counter","enderio:dark_steel_grinding_ball","minecraft:sticky_piston","mcwfences:railing_red_sandstone_wall","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","additionallanterns:prismarine_lantern","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","cfm:oak_kitchen_sink_light","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","minecraft:wooden_pickaxe","mcwpaths:dark_prismarine_clover_paving","supplementaries:clock_block","biomesoplenty:mahogany_boat","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","mcwbridges:asian_red_bridge_pier","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwwindows:orange_mosaic_glass","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","additionallanterns:stone_lantern","utilitarian:utility/charcoal_from_campfire","supplementaries:flags/flag_red","cfm:acacia_kitchen_drawer","cfm:dye_blue_picket_fence","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","minecraft:brown_stained_glass","aquaculture:double_hook","mcwroofs:prismarine_brick_steep_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","handcrafted:oak_bench","securitycraft:reinforced_yellow_stained_glass","mcwwindows:jungle_log_parapet","mcwfences:nether_brick_grass_topped_wall","mcwwindows:prismarine_parapet","utilitarian:angel_block","mcwpaths:dark_prismarine_flagstone_stairs","aquaculture:tin_can_to_iron_nugget","mcwwindows:birch_louvered_shutter","mcwwindows:white_mosaic_glass_pane","mcwwindows:purple_mosaic_glass","simplylight:illuminant_orange_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:prismarine_brick_lower_roof","xnet:connector_yellow","domum_ornamentum:blue_brick_extra","simplylight:illuminant_light_blue_block_toggle","minecraft:fishing_rod","xnet:connector_yellow_dye","mcwlights:tavern_lantern","mcwroofs:dark_prismarine_upper_lower_roof","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwwindows:acacia_blinds","simplylight:edge_light_bottom_from_top","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","dyenamics:bed/navy_bed","domum_ornamentum:gray_floating_carpet","ae2wtlib:magnet_card","securitycraft:reinforced_dark_oak_fence_gate","mcwfences:oak_pyramid_gate","supplementaries:timber_frame","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","mcwroofs:dark_prismarine_upper_steep_roof","mcwwindows:spruce_blinds","supplementaries:daub_cross_brace","enderio:dark_steel_ladder","cfm:stripped_crimson_mail_box","mcwfences:deepslate_pillar_wall","sophisticatedstorage:packing_tape","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:soul_oak_tiki_torch","mcwlights:birch_tiki_torch","dyenamics:banner/honey_banner","domum_ornamentum:light_blue_floating_carpet","mcwlights:double_street_lamp","cfm:purple_cooler","connectedglass:scratched_glass_blue_pane2","ae2:network/cables/dense_covered_blue","additionallanterns:quartz_lantern","mcwfences:stone_grass_topped_wall","mcwfences:mud_brick_grass_topped_wall","minecraft:prismarine_brick_slab","xnet:connector_routing","mcwwindows:warped_louvered_shutter","biomesoplenty:pine_boat","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","additionallanterns:normal_nether_bricks_lantern","mcwbridges:oak_rail_bridge","mcwbridges:rope_oak_bridge","mcwwindows:spruce_plank_parapet","dyenamics:bed/mint_bed","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","xnet:advanced_connector_blue_dye","cfm:oak_coffee_table","minecraft:white_wool_from_string","minecraft:light_gray_stained_glass","xnet:netcable_green_dye","securitycraft:reinforced_blue_stained_glass","mcwwindows:yellow_mosaic_glass_pane","mcwlights:golden_chain","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:magic_picket_fence","xnet:advanced_connector_red_dye","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","minecraft:golden_axe","mcwroofs:prismarine_brick_top_roof","cfm:warped_mail_box","cfm:yellow_kitchen_drawer","twilightforest:dark_boat","biomesoplenty:umbran_boat","mcwwindows:dark_prismarine_window2","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","minecraft:oak_pressure_plate","pneumaticcraft:logistics_frame_requester_self","mcwwindows:dark_oak_shutter","securitycraft:reinforced_dark_oak_fence","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","create:tiled_glass_from_glass_colorless_stonecutting","cfm:gray_kitchen_drawer","mcwlights:wall_lantern","farmersdelight:cooking/pumpkin_soup","mcwfences:nether_brick_pillar_wall","mcwwindows:warped_curtain_rod","rftoolsbuilder:blue_shield_template_block","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwtrpdoors:oak_blossom_trapdoor","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwwindows:acacia_log_parapet","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","mcwbridges:balustrade_prismarine_bricks_bridge","supplementaries:flags/flag_white","dyenamics:fluorescent_stained_glass","additionallanterns:end_stone_lantern","mcwpaths:dark_prismarine_basket_weave_paving","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","travelersbackpack:coal","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","minecraft:glass_pane","dyenamics:peach_stained_glass","handcrafted:oak_side_table","supplementaries:timber_brace","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","minecraft:purple_stained_glass","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","minecraft:golden_pickaxe","minecraft:bamboo_raft","ae2:network/cables/dense_smart_blue","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","botania:azulejo_0","mcwwindows:white_mosaic_glass","sfm:fancy_to_cable","mcwwindows:oak_louvered_shutter","mcwfences:railing_andesite_wall","mcwbiomesoplenty:mahogany_hedge","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","cfm:orange_kitchen_drawer","additionallanterns:andesite_lantern","enderio:cold_fire_igniter","mcwbridges:prismarine_bricks_bridge","mcwlights:golden_low_candle_holder","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","simplylight:illuminant_light_gray_block_on_dyed","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwlights:golden_chandelier","enderio:glider_wing","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","mcwbiomesoplenty:empyreal_highley_gate","pneumaticcraft:classify_filter","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","mcwbridges:oak_log_bridge_middle","mcwpaths:dark_prismarine_running_bond_stairs","mcwwindows:gray_mosaic_glass","mcwbiomesoplenty:hellbark_wired_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","cfm:spruce_mail_box","mcwbiomesoplenty:fir_pyramid_gate","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwlights:pink_paper_lamp","bloodmagic:sacrificial_dagger","mcwwindows:magenta_mosaic_glass_pane","mcwfences:end_brick_railing_gate","handcrafted:oak_table","supplementaries:bomb","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","aquaculture:golden_fishing_rod","mcwwindows:gray_mosaic_glass_pane","mcwfences:oak_highley_gate","simplylight:illuminant_light_gray_block_toggle","xnet:connector_green_dye","utilitix:hand_bell","minecraft:dark_prismarine_slab_from_dark_prismarine_stonecutting","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","handcrafted:blue_cup","undergarden:wigglewood_boat","mcwbiomesoplenty:fir_hedge","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","corail_woodcutter:oak_woodcutter","mcwfences:birch_highley_gate","cfm:oak_table","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwfences:railing_diorite_wall","mcwwindows:oak_blinds","mcwwindows:mangrove_plank_parapet","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwpaths:dark_prismarine_flagstone_slab","mcwwindows:birch_curtain_rod","minecolonies:mutton_dinner","mcwwindows:crimson_stem_parapet","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbiomesoplenty:mahogany_highley_gate","immersiveengineering:crafting/torch","botania:petal_blue","mcwbridges:glass_bridge_pier","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","mcwfences:warped_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","mcwpaths:dark_prismarine_crystal_floor_stairs","domum_ornamentum:architectscutter","cfm:light_blue_cooler","mcwfences:jungle_picket_fence","domum_ornamentum:red_floating_carpet","dyenamics:bubblegum_stained_glass","minecraft:prismarine_brick_slab_from_prismarine_stonecutting","undergarden:gloom_o_lantern","sfm:cable","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwlights:dark_oak_tiki_torch","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","twigs:oak_table","supplementaries:faucet","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","mcwfences:railing_blackstone_wall","mcwlights:soul_crimson_tiki_torch","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","ad_astra:blue_industrial_lamp","minecraft:chest","supplementaries:flags/flag_light_blue","supplementaries:pulley","minecraft:gray_stained_glass","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:birch_horse_fence","mcwfences:modern_sandstone_wall","allthecompressed:compress/glass_1x","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","minecraft:golden_shovel","additionallanterns:mossy_cobblestone_lantern","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","biomesoplenty:dead_boat","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","supplementaries:flags/flag_orange","additionallanterns:prismarine_chain","travelersbackpack:standard","cfm:light_blue_kitchen_drawer","dyenamics:bed/bubblegum_bed","dyenamics:navy_dye","mcwfences:mangrove_stockade_fence","mcwlights:soul_cherry_tiki_torch","mcwfences:mud_brick_pillar_wall","mcwroofs:dark_prismarine_steep_roof","cfm:stripped_warped_mail_box","securitycraft:reinforced_mangrove_fence","cfm:jungle_mail_box","dyenamics:conifer_stained_glass","aquaculture:iron_hook","connectedglass:borderless_glass1","aquaculture:diamond_fishing_rod","sfm:fancy_cable","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","bloodmagic:blood_altar","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","cfm:purple_kitchen_drawer","botania:flower_bag","cfm:stripped_acacia_kitchen_drawer","mcwpaths:dark_prismarine_windmill_weave_slab","domum_ornamentum:blue_floating_carpet","minecraft:blue_stained_glass","simplylight:illuminant_brown_block_toggle","minecraft:oak_fence","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:prismarine_brick_stairs","dyenamics:aquamarine_stained_glass","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:golden_chestplate","handcrafted:fox_trophy","securitycraft:reinforced_gray_stained_glass","simplylight:illuminant_black_block_on_toggle","mcwlights:golden_candle_holder","minecraft:prismarine_stairs_from_prismarine_stonecutting","minecraft:oak_sign","mcwwindows:crimson_curtain_rod","minecraft:blue_candle","mcwwindows:granite_louvered_shutter","mcwpaths:dark_prismarine_crystal_floor","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","connectedglass:borderless_glass_blue2","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","mcwwindows:dark_oak_plank_parapet","mcwwindows:spruce_log_parapet","additionallanterns:emerald_lantern","handcrafted:oak_counter","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwwindows:golden_curtain_rod","mcwlights:iron_framed_torch","mcwbiomesoplenty:red_maple_hedge","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","sfm:manager","mcwfences:railing_mud_brick_wall","botania:red_string_container","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","cfm:cyan_cooler","minecraft:crossbow","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","cfm:oak_crate","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwwindows:prismarine_four_window","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","minecraft:magenta_dye_from_blue_red_pink","dyenamics:rose_stained_glass","mcwlights:orange_paper_lamp","mcwbiomesoplenty:dead_picket_fence","minecraft:prismarine_slab_from_prismarine_stonecutting","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","xnet:advanced_connector_yellow","mcwwindows:orange_mosaic_glass_pane","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwfences:spruce_curved_gate","mcwwindows:oak_shutter","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","mcwroofs:dark_prismarine_attic_roof","minecraft:slime_block","minecraft:wooden_hoe","mcwbiomesoplenty:maple_picket_fence","supplementaries:soap","railcraft:gold_gear","mcwwindows:iron_shutter","mcwwindows:mangrove_curtain_rod","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","mcwfences:deepslate_brick_grass_topped_wall","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","mcwbiomesoplenty:magic_stockade_fence","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","botania:dye_blue","farmersdelight:cooking/noodle_soup","mcwroofs:oak_planks_attic_roof","mcwfences:spruce_hedge","securitycraft:reinforced_pink_stained_glass","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","connectedglass:scratched_glass_blue2","mcwwindows:brown_mosaic_glass_pane","domum_ornamentum:pink_floating_carpet","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","utilitarian:angel_block_rot","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","minecraft:wooden_axe","aquaculture:cooked_fish_fillet_from_smoking","cfm:red_kitchen_drawer","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:spruce_planks","minecraft:red_stained_glass","mcwlights:golden_double_candle_holder","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","croptopia:pork_jerky","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","aether:skyroot_tripwire_hook","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","minecraft:tripwire_hook","simplylight:illuminant_cyan_block_on_toggle","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwlights:jungle_tiki_torch","connectedglass:borderless_glass_blue_pane2","handcrafted:oak_shelf","supplementaries:flags/flag_lime","mcwfences:bamboo_wired_fence","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","minecraft:magenta_stained_glass","minecraft:gold_nugget","securitycraft:reinforced_green_stained_glass","littlelogistics:fluid_barge","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:mangrove_louvered_shutter","minecolonies:supplycampdeployer","mcwwindows:warped_blinds","supplementaries:flags/flag_magenta","aquaculture:jellyfish_to_slimeball","securitycraft:reinforced_pink_stained_glass_pane_from_dye","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","minecraft:dye_blue_wool","mcwlights:covered_wall_lantern","minecraft:cooked_porkchop_from_smoking","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","dyenamics:banner/bubblegum_banner","mcwfences:majestic_metal_fence_gate","mcwpaths:dark_prismarine_crystal_floor_slab","packingtape:tape","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","additionallanterns:amethyst_lantern","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","minecraft:blue_stained_glass_pane_from_glass_pane","enderio:dark_steel_nugget_to_ingot","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","railcraft:water_tank_siding","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","securitycraft:reinforced_crimson_fence_gate","silentgear:upgrade_base","mcwbiomesoplenty:rainbow_birch_hedge","xnet:advanced_connector_green","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwroofs:prismarine_brick_upper_lower_roof","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","cfm:stripped_warped_kitchen_drawer","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","mcwroofs:prismarine_brick_upper_steep_roof","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","minecraft:golden_leggings","additionallanterns:gold_lantern","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","handcrafted:oak_dining_bench","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","simplylight:illuminant_pink_block_toggle","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","dyenamics:bed/maroon_bed","botania:blue_shiny_flower","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","mcwfences:bamboo_highley_gate","securitycraft:sc_manual","mcwpaths:dark_prismarine_windmill_weave","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","dyenamics:aquamarine_dye","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","silentgear:stone_torch","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwfences:jungle_curved_gate","securitycraft:reinforced_red_stained_glass_pane_from_glass","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","mcwwindows:dark_oak_curtain_rod","cfm:oak_desk","mcwwindows:green_mosaic_glass_pane","minecraft:dye_blue_bed","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","croptopia:carnitas","forbidden_arcanus:sanity_meter","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","sophisticatedstorage:generic_barrel","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","minecraft:cooked_porkchop_from_campfire_cooking","supplementaries:daub_frame","mcwfences:iron_cheval_de_frise","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","mcwlights:purple_paper_lamp","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","aquaculture:iron_fishing_rod","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","connectedglass:tinted_borderless_glass_blue2","domum_ornamentum:white_floating_carpet","minecraft:crafting_table","dyenamics:cherenkov_dye","dyenamics:lavender_stained_glass","twilightforest:time_boat","mcwlights:golden_wall_candle_holder","cfm:blue_kitchen_drawer","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","ae2:network/blocks/crank","mcwwindows:dark_oak_louvered_shutter","mcwwindows:green_mosaic_glass","mcwbridges:prismarine_bricks_bridge_stair","supplementaries:candle_holders/candle_holder_blue_dye","domum_ornamentum:blockbarreldeco_standing","rftoolsbuilder:red_shield_template_block","mcwwindows:bamboo_shutter","mcwfences:spruce_wired_fence","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","supplementaries:jar","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","dyenamics:ultramarine_stained_glass","pneumaticcraft:logistics_frame_passive_provider","securitycraft:motion_activated_light","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","mcwfences:double_curved_metal_fence","minecraft:oak_trapdoor","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","biomesoplenty:jacaranda_boat","minecraft:oak_door","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","aether:golden_pendant","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","mythicbotany:alfsteel_template","mcwwindows:prismarine_window","domum_ornamentum:green_floating_carpet","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","mcwwindows:spruce_curtain_rod","cfm:dye_blue_picket_gate","mcwbridges:asian_red_bridge","aether:skyroot_loom","utilitix:advanced_brewery","mcwfences:bamboo_horse_fence","cfm:oak_cabinet","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","minecraft:prismarine_wall_from_prismarine_stonecutting","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","ae2:network/cables/glass_blue","aquaculture:spruce_fish_mount","enderio:dark_steel_nugget","minecraft:candle","minecraft:oak_button","mcwroofs:prismarine_brick_attic_roof","simplylight:illuminant_purple_block_on_dyed","framedblocks:framed_cube","aquaculture:redstone_hook","supplementaries:flags/flag_blue","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","rftoolsbuilder:yellow_shield_template_block","xnet:netcable_blue_dye","xnet:advanced_connector_yellow_dye","mcwpaths:dark_prismarine_running_bond_path","domum_ornamentum:brown_floating_carpet","mcwlights:golden_triple_candle_holder","additionallanterns:diamond_lantern","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","securitycraft:reinforced_purple_stained_glass_pane_from_dye","minecraft:bow","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aquaculture:bobber","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","securitycraft:reinforced_lime_stained_glass_pane_from_glass","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwwindows:light_gray_mosaic_glass_pane","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_dyed","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","simplylight:illuminant_brown_block_on_dyed","bigreactors:energizer/computerport","handcrafted:bear_trophy","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","supplementaries:sack","cfm:black_cooler","minecraft:loom","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","supplementaries:sconce","cfm:stripped_oak_mail_box","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","rftoolsbase:crafting_card","aquaculture:gold_hook","forbidden_arcanus:deorum_glass","minecraft:oak_fence_gate","mcwroofs:prismarine_brick_roof","mcwbiomesoplenty:palm_wired_fence","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","rftoolsbuilder:space_chamber_card","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwbiomesoplenty:hellbark_stockade_fence","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwwindows:mangrove_shutter","mcwbiomesoplenty:umbran_pyramid_gate","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","cfm:lime_cooler","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:oak_planks_lower_roof","aquaculture:oak_fish_mount","sophisticatedstorage:storage_magnet_upgrade_from_backpack_magnet_upgrade","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","supplementaries:flags/flag_green","cfm:stripped_jungle_mail_box","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","handcrafted:oak_couch","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","handcrafted:blue_crockery_combo","minecraft:stick","securitycraft:reinforced_acacia_fence_gate","mcwbridges:prismarine_bricks_bridge_pier","mcwfences:red_sandstone_grass_topped_wall","mcwlights:light_blue_paper_lamp","forbidden_arcanus:deorum_lantern","additionallanterns:granite_lantern","allthecompressed:compress/oak_planks_1x","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","cfm:white_kitchen_drawer","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:silverfish_trophy","pneumaticcraft:logistics_frame_storage","mcwpaths:dark_prismarine_diamond_paving","mcwwindows:warped_shutter","xnet:connector_red","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","twigs:lamp","dyenamics:bed/lavender_bed","domum_ornamentum:yellow_floating_carpet","additionallanterns:diorite_lantern","mcwpaths:dark_prismarine_flagstone","simplylight:illuminant_cyan_block_dyed","minecraft:prismarine_wall","xnet:netcable_red_dye","cfm:oak_chair","simplylight:illuminant_pink_block_dyed","cfm:brown_cooler","utilitarian:utility/green_dye","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwwindows:dark_prismarine_parapet","mcwwindows:pink_mosaic_glass_pane","minecraft:yellow_stained_glass","dyenamics:honey_stained_glass","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","cfm:green_cooler","alltheores:gold_rod","handcrafted:goat_trophy","mcwpaths:dark_prismarine_honeycomb_paving","bigreactors:blasting/graphite_from_coal","mcwlights:soul_warped_tiki_torch","mcwpaths:dark_prismarine_windmill_weave_stairs","paraglider:paraglider","mcwpaths:dark_prismarine_flagstone_path","biomesoplenty:magic_boat","mcwwindows:dark_prismarine_four_window","supplementaries:timber_cross_brace","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","minecraft:cooked_mutton","securitycraft:reinforced_brown_stained_glass_pane_from_dye","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfences:bamboo_pyramid_gate","pneumaticcraft:logistics_frame_default_storage","cfm:oak_bedside_cabinet","minecraft:dark_prismarine_stairs","cfm:oak_park_bench","croptopia:pork_and_beans","mcwbiomesoplenty:jacaranda_picket_fence","additionallanterns:cobbled_deepslate_lantern","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","aether:golden_ring","travelersbackpack:pig","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","undergarden:catalyst","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","cfm:pink_kitchen_drawer","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwroofs:dark_prismarine_roof","mcwwindows:blue_mosaic_glass","croptopia:shaped_bacon","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:snowblossom_hedge","mcwlights:cross_lantern","domum_ornamentum:cyan_floating_carpet","minecraft:oak_boat","mcwwindows:birch_blinds","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwwindows:diorite_parapet","mcwbiomesoplenty:palm_highley_gate","mcwroofs:dark_prismarine_top_roof","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","minecraft:prismarine_slab","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","supplementaries:flags/flag_black","mcwwindows:acacia_louvered_shutter","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions","minecraft:cooked_mutton_from_campfire_cooking","minecraft:glass_bottle","enderio:dark_steel_trapdoor","mcwfences:mesh_metal_fence","cfm:light_gray_cooler","cfm:green_kitchen_drawer","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwbiomesoplenty:pine_curved_gate","minecraft:cooked_mutton_from_smoking","farmersdelight:cooking/pasta_with_mutton_chop","supplementaries:flags/flag_gray","securitycraft:reinforced_light_gray_stained_glass","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwroofs:dark_prismarine_lower_roof","mcwbiomesoplenty:palm_horse_fence","twilightforest:transformation_boat","botania:speed_up_belt","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","dyenamics:bed/cherenkov_bed","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","handcrafted:oak_cupboard","simplylight:edge_light","mcwlights:bamboo_tiki_torch","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","mcwroofs:oak_planks_upper_lower_roof","cfm:stripped_jungle_kitchen_drawer","handcrafted:oak_nightstand","dyenamics:wine_stained_glass","mcwwindows:prismarine_pane_window","domum_ornamentum:light_gray_floating_carpet","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","aether:skyroot_boat","cfm:magenta_kitchen_drawer","mcwpaths:dark_prismarine_windmill_weave_path","supplementaries:slice_map","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","computercraft:turtle_advanced_overlays/turtle_trans_overlay","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:stone_pillar_wall","mcwfences:birch_hedge","securitycraft:reinforced_light_blue_stained_glass","simplylight:illuminant_gray_block_toggle","additionallanterns:dark_prismarine_chain","utilitix:gilding_crystal","minecraft:dark_prismarine_stairs_from_dark_prismarine_stonecutting","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwlights:mangrove_tiki_torch","securitycraft:reinforced_sticky_piston","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","utilitix:armed_stand","botania:lexicon","dyenamics:ultramarine_dye","additionallanterns:purpur_lantern","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","supplementaries:flags/flag_purple","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:palm_hedge","ae2:network/cables/smart_blue","mcwlights:green_paper_lamp","mcwwindows:birch_plank_parapet","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","minecraft:purple_dye","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","mcwfences:oak_hedge","mcwwindows:jungle_shutter","mcwroofs:oak_planks_roof","mcwfences:railing_sandstone_wall","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","deepresonance:resonating_plate","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwfences:modern_diorite_wall","mcwwindows:granite_parapet","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:cyan_dye","dyenamics:bed/wine_bed","handcrafted:skeleton_horse_trophy","mcwbiomesoplenty:hellbark_highley_gate","additionallanterns:warped_lantern","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","cfm:stripped_spruce_mail_box","minecraft:lead","mcwfences:spruce_highley_gate","minecraft:spruce_boat","minecraft:birch_boat","minecraft:gold_block","aquaculture:gold_fillet_knife","dyenamics:banner/navy_banner","travelersbackpack:gold","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","minecraft:campfire","handcrafted:oak_drawer","mcwfences:warped_curved_gate","ad_astra:small_blue_industrial_lamp","mcwfences:bamboo_stockade_fence","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","connectedglass:clear_glass_blue_pane2","mcwwindows:pink_mosaic_glass","mcwwindows:warped_plank_parapet","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","mcwwindows:jungle_louvered_shutter","cfm:orange_cooler","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","mcwpaths:dark_prismarine_strewn_rocky_path","minecraft:lime_stained_glass","mcwfences:railing_quartz_wall","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","bigreactors:smelting/graphite_from_coal","handcrafted:salmon_trophy","mcwlights:bell_wall_lantern","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","minecraft:black_stained_glass","cfm:oak_desk_cabinet","mcwpaths:dark_prismarine_square_paving"],toBeDisplayed:["securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwwindows:birch_log_parapet","cfm:yellow_cooler","mcwfences:granite_railing_gate","minecraft:charcoal","additionallanterns:copper_lantern","handcrafted:blue_bowl","mcwpaths:dark_prismarine_running_bond_slab","mcwlights:golden_small_chandelier","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","dyenamics:banner/lavender_banner","mcwpaths:dark_prismarine_running_bond","mcwbiomesoplenty:redwood_highley_gate","handcrafted:spider_trophy","mcwpaths:dark_prismarine_dumble_paving","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","domum_ornamentum:purple_floating_carpet","cfm:fridge_dark","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","xnet:connector_green","mcwwindows:crimson_blinds","create:oak_window","create:crafting/appliances/crafting_blueprint","dyenamics:banner/aquamarine_banner","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","utilitix:reinforced_rail","handcrafted:oak_desk","mcwbiomesoplenty:empyreal_hedge","xnet:connector_blue_dye","mcwbiomesoplenty:hellbark_picket_fence","mcwwindows:oak_log_parapet","mcwwindows:diorite_louvered_shutter","corail_woodcutter:crimson_woodcutter","mcwwindows:crimson_plank_parapet","supplementaries:notice_board","minecraft:coal_block","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","simplylight:illuminant_light_blue_block_dyed","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","mcwwindows:black_mosaic_glass","simplylight:illuminant_lime_block_toggle","additionallanterns:gold_chain","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","minecraft:cooked_porkchop","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","securitycraft:reinforced_cherry_fence_gate","dyenamics:maroon_stained_glass","mcwfences:acacia_hedge","securitycraft:reinforced_acacia_fence","minecraft:golden_apple","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","energymeter:meter","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwlights:covered_lantern","simplylight:rodlamp","sophisticatedstorage:oak_chest","xnet:connector_red_dye","minecolonies:shapetool","mcwwindows:dark_prismarine_pane_window","cfm:blue_cooler","supplementaries:crank","mcwwindows:oak_plank_parapet","aquaculture:iron_nugget_from_smelting","minecraft:magenta_dye_from_blue_red_white_dye","minecraft:green_stained_glass","aquaculture:sushi","mcwwindows:andesite_louvered_shutter","domum_ornamentum:blue_cobblestone_extra","rftoolsbuilder:green_shield_template_block","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:dark_prismarine_slab","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","aquaculture:birch_fish_mount","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","mcwfences:prismarine_railing_gate","pneumaticcraft:logistics_module","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","mcwwindows:crimson_louvered_shutter","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","handcrafted:blue_plate","simplylight:illuminant_red_block_toggle","sophisticatedstorage:generic_limited_barrel_4","securitycraft:reinforced_gray_stained_glass_pane_from_glass","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwwindows:dark_oak_blinds","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","dyenamics:mint_stained_glass","nethersdelight:golden_machete","minecraft:prismarine_brick_stairs_from_prismarine_stonecutting","mcwwindows:acacia_curtain_rod","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwlights:chain_lantern","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","minecraft:golden_boots","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","cfm:black_kitchen_drawer","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwbiomesoplenty:umbran_curved_gate","corail_woodcutter:dark_oak_woodcutter","mcwfences:modern_nether_brick_wall","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwpaths:dark_prismarine_crystal_floor_path","minecraft:prismarine_stairs","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwwindows:mangrove_blinds","minecraft:golden_hoe","cfm:oak_kitchen_counter","enderio:dark_steel_grinding_ball","minecraft:sticky_piston","mcwfences:railing_red_sandstone_wall","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","additionallanterns:prismarine_lantern","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","cfm:oak_kitchen_sink_light","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","minecraft:wooden_pickaxe","mcwpaths:dark_prismarine_clover_paving","supplementaries:clock_block","biomesoplenty:mahogany_boat","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","mcwbridges:asian_red_bridge_pier","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwwindows:orange_mosaic_glass","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","additionallanterns:stone_lantern","utilitarian:utility/charcoal_from_campfire","supplementaries:flags/flag_red","cfm:acacia_kitchen_drawer","cfm:dye_blue_picket_fence","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","minecraft:brown_stained_glass","aquaculture:double_hook","mcwroofs:prismarine_brick_steep_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","handcrafted:oak_bench","securitycraft:reinforced_yellow_stained_glass","mcwwindows:jungle_log_parapet","mcwfences:nether_brick_grass_topped_wall","mcwwindows:prismarine_parapet","utilitarian:angel_block","mcwpaths:dark_prismarine_flagstone_stairs","aquaculture:tin_can_to_iron_nugget","mcwwindows:birch_louvered_shutter","mcwwindows:white_mosaic_glass_pane","mcwwindows:purple_mosaic_glass","simplylight:illuminant_orange_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:prismarine_brick_lower_roof","xnet:connector_yellow","domum_ornamentum:blue_brick_extra","simplylight:illuminant_light_blue_block_toggle","minecraft:fishing_rod","xnet:connector_yellow_dye","mcwlights:tavern_lantern","mcwroofs:dark_prismarine_upper_lower_roof","mcwbiomesoplenty:umbran_wired_fence","xnet:netcable_yellow_dye","mcwwindows:acacia_blinds","simplylight:edge_light_bottom_from_top","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","dyenamics:bed/navy_bed","domum_ornamentum:gray_floating_carpet","ae2wtlib:magnet_card","securitycraft:reinforced_dark_oak_fence_gate","mcwfences:oak_pyramid_gate","supplementaries:timber_frame","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","mcwroofs:dark_prismarine_upper_steep_roof","mcwwindows:spruce_blinds","supplementaries:daub_cross_brace","enderio:dark_steel_ladder","cfm:stripped_crimson_mail_box","mcwfences:deepslate_pillar_wall","sophisticatedstorage:packing_tape","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:soul_oak_tiki_torch","mcwlights:birch_tiki_torch","dyenamics:banner/honey_banner","domum_ornamentum:light_blue_floating_carpet","mcwlights:double_street_lamp","cfm:purple_cooler","connectedglass:scratched_glass_blue_pane2","ae2:network/cables/dense_covered_blue","additionallanterns:quartz_lantern","mcwfences:stone_grass_topped_wall","mcwfences:mud_brick_grass_topped_wall","minecraft:prismarine_brick_slab","xnet:connector_routing","mcwwindows:warped_louvered_shutter","biomesoplenty:pine_boat","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","additionallanterns:normal_nether_bricks_lantern","mcwbridges:oak_rail_bridge","mcwbridges:rope_oak_bridge","mcwwindows:spruce_plank_parapet","dyenamics:bed/mint_bed","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","xnet:advanced_connector_blue_dye","cfm:oak_coffee_table","minecraft:white_wool_from_string","minecraft:light_gray_stained_glass","xnet:netcable_green_dye","securitycraft:reinforced_blue_stained_glass","mcwwindows:yellow_mosaic_glass_pane","mcwlights:golden_chain","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:magic_picket_fence","xnet:advanced_connector_red_dye","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","minecraft:golden_axe","mcwroofs:prismarine_brick_top_roof","cfm:warped_mail_box","cfm:yellow_kitchen_drawer","twilightforest:dark_boat","biomesoplenty:umbran_boat","mcwwindows:dark_prismarine_window2","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","minecraft:oak_pressure_plate","pneumaticcraft:logistics_frame_requester_self","mcwwindows:dark_oak_shutter","securitycraft:reinforced_dark_oak_fence","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","create:tiled_glass_from_glass_colorless_stonecutting","cfm:gray_kitchen_drawer","mcwlights:wall_lantern","farmersdelight:cooking/pumpkin_soup","mcwfences:nether_brick_pillar_wall","mcwwindows:warped_curtain_rod","rftoolsbuilder:blue_shield_template_block","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwtrpdoors:oak_blossom_trapdoor","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwwindows:acacia_log_parapet","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","mcwbridges:balustrade_prismarine_bricks_bridge","supplementaries:flags/flag_white","dyenamics:fluorescent_stained_glass","additionallanterns:end_stone_lantern","mcwpaths:dark_prismarine_basket_weave_paving","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","travelersbackpack:coal","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","minecraft:glass_pane","dyenamics:peach_stained_glass","handcrafted:oak_side_table","supplementaries:timber_brace","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","minecraft:purple_stained_glass","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","minecraft:golden_pickaxe","minecraft:bamboo_raft","ae2:network/cables/dense_smart_blue","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","botania:azulejo_0","mcwwindows:white_mosaic_glass","sfm:fancy_to_cable","mcwwindows:oak_louvered_shutter","mcwfences:railing_andesite_wall","mcwbiomesoplenty:mahogany_hedge","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","cfm:orange_kitchen_drawer","additionallanterns:andesite_lantern","enderio:cold_fire_igniter","mcwbridges:prismarine_bricks_bridge","mcwlights:golden_low_candle_holder","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","simplylight:illuminant_light_gray_block_on_dyed","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwlights:golden_chandelier","enderio:glider_wing","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","mcwbiomesoplenty:empyreal_highley_gate","pneumaticcraft:classify_filter","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","mcwbridges:oak_log_bridge_middle","mcwpaths:dark_prismarine_running_bond_stairs","mcwwindows:gray_mosaic_glass","mcwbiomesoplenty:hellbark_wired_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","cfm:spruce_mail_box","mcwbiomesoplenty:fir_pyramid_gate","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwlights:pink_paper_lamp","bloodmagic:sacrificial_dagger","mcwwindows:magenta_mosaic_glass_pane","mcwfences:end_brick_railing_gate","handcrafted:oak_table","supplementaries:bomb","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","aquaculture:golden_fishing_rod","mcwwindows:gray_mosaic_glass_pane","mcwfences:oak_highley_gate","simplylight:illuminant_light_gray_block_toggle","xnet:connector_green_dye","utilitix:hand_bell","minecraft:dark_prismarine_slab_from_dark_prismarine_stonecutting","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","handcrafted:blue_cup","undergarden:wigglewood_boat","mcwbiomesoplenty:fir_hedge","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","corail_woodcutter:oak_woodcutter","mcwfences:birch_highley_gate","cfm:oak_table","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwfences:railing_diorite_wall","mcwwindows:oak_blinds","mcwwindows:mangrove_plank_parapet","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwpaths:dark_prismarine_flagstone_slab","mcwwindows:birch_curtain_rod","minecolonies:mutton_dinner","mcwwindows:crimson_stem_parapet","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbiomesoplenty:mahogany_highley_gate","immersiveengineering:crafting/torch","botania:petal_blue","mcwbridges:glass_bridge_pier","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","mcwfences:warped_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","mcwpaths:dark_prismarine_crystal_floor_stairs","domum_ornamentum:architectscutter","cfm:light_blue_cooler","mcwfences:jungle_picket_fence","domum_ornamentum:red_floating_carpet","dyenamics:bubblegum_stained_glass","minecraft:prismarine_brick_slab_from_prismarine_stonecutting","undergarden:gloom_o_lantern","sfm:cable","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwlights:dark_oak_tiki_torch","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","twigs:oak_table","supplementaries:faucet","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","mcwfences:railing_blackstone_wall","mcwlights:soul_crimson_tiki_torch","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","ad_astra:blue_industrial_lamp","minecraft:chest","supplementaries:flags/flag_light_blue","supplementaries:pulley","minecraft:gray_stained_glass","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:birch_horse_fence","mcwfences:modern_sandstone_wall","allthecompressed:compress/glass_1x","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","minecraft:golden_shovel","additionallanterns:mossy_cobblestone_lantern","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","biomesoplenty:dead_boat","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","supplementaries:flags/flag_orange","additionallanterns:prismarine_chain","travelersbackpack:standard","cfm:light_blue_kitchen_drawer","dyenamics:bed/bubblegum_bed","dyenamics:navy_dye","mcwfences:mangrove_stockade_fence","mcwlights:soul_cherry_tiki_torch","mcwfences:mud_brick_pillar_wall","mcwroofs:dark_prismarine_steep_roof","cfm:stripped_warped_mail_box","securitycraft:reinforced_mangrove_fence","cfm:jungle_mail_box","dyenamics:conifer_stained_glass","aquaculture:iron_hook","connectedglass:borderless_glass1","aquaculture:diamond_fishing_rod","sfm:fancy_cable","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","bloodmagic:blood_altar","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","cfm:purple_kitchen_drawer","botania:flower_bag","cfm:stripped_acacia_kitchen_drawer","mcwpaths:dark_prismarine_windmill_weave_slab","domum_ornamentum:blue_floating_carpet","minecraft:blue_stained_glass","simplylight:illuminant_brown_block_toggle","minecraft:oak_fence","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:prismarine_brick_stairs","dyenamics:aquamarine_stained_glass","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:golden_chestplate","handcrafted:fox_trophy","securitycraft:reinforced_gray_stained_glass","simplylight:illuminant_black_block_on_toggle","mcwlights:golden_candle_holder","minecraft:prismarine_stairs_from_prismarine_stonecutting","minecraft:oak_sign","mcwwindows:crimson_curtain_rod","minecraft:blue_candle","mcwwindows:granite_louvered_shutter","mcwpaths:dark_prismarine_crystal_floor","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","connectedglass:borderless_glass_blue2","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","mcwwindows:dark_oak_plank_parapet","mcwwindows:spruce_log_parapet","additionallanterns:emerald_lantern","handcrafted:oak_counter","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwwindows:golden_curtain_rod","mcwlights:iron_framed_torch","mcwbiomesoplenty:red_maple_hedge","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","sfm:manager","mcwfences:railing_mud_brick_wall","botania:red_string_container","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","cfm:cyan_cooler","minecraft:crossbow","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","cfm:oak_crate","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwwindows:prismarine_four_window","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","minecraft:magenta_dye_from_blue_red_pink","dyenamics:rose_stained_glass","mcwlights:orange_paper_lamp","mcwbiomesoplenty:dead_picket_fence","minecraft:prismarine_slab_from_prismarine_stonecutting","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","xnet:advanced_connector_yellow","mcwwindows:orange_mosaic_glass_pane","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwfences:spruce_curved_gate","mcwwindows:oak_shutter","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","mcwroofs:dark_prismarine_attic_roof","minecraft:slime_block","minecraft:wooden_hoe","mcwbiomesoplenty:maple_picket_fence","supplementaries:soap","railcraft:gold_gear","mcwwindows:iron_shutter","mcwwindows:mangrove_curtain_rod","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","mcwfences:deepslate_brick_grass_topped_wall","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","mcwbiomesoplenty:magic_stockade_fence","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","botania:dye_blue","farmersdelight:cooking/noodle_soup","mcwroofs:oak_planks_attic_roof","mcwfences:spruce_hedge","securitycraft:reinforced_pink_stained_glass","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","connectedglass:scratched_glass_blue2","mcwwindows:brown_mosaic_glass_pane","domum_ornamentum:pink_floating_carpet","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","utilitarian:angel_block_rot","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","minecraft:wooden_axe","aquaculture:cooked_fish_fillet_from_smoking","cfm:red_kitchen_drawer","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:spruce_planks","minecraft:red_stained_glass","mcwlights:golden_double_candle_holder","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","croptopia:pork_jerky","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","aether:skyroot_tripwire_hook","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","minecraft:tripwire_hook","simplylight:illuminant_cyan_block_on_toggle","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwlights:jungle_tiki_torch","connectedglass:borderless_glass_blue_pane2","handcrafted:oak_shelf","supplementaries:flags/flag_lime","mcwfences:bamboo_wired_fence","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","minecraft:magenta_stained_glass","minecraft:gold_nugget","securitycraft:reinforced_green_stained_glass","littlelogistics:fluid_barge","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:mangrove_louvered_shutter","minecolonies:supplycampdeployer","mcwwindows:warped_blinds","supplementaries:flags/flag_magenta","aquaculture:jellyfish_to_slimeball","securitycraft:reinforced_pink_stained_glass_pane_from_dye","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","additionallanterns:bricks_lantern","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","minecraft:dye_blue_wool","mcwlights:covered_wall_lantern","minecraft:cooked_porkchop_from_smoking","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","dyenamics:banner/bubblegum_banner","mcwfences:majestic_metal_fence_gate","mcwpaths:dark_prismarine_crystal_floor_slab","packingtape:tape","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","additionallanterns:amethyst_lantern","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","securitycraft:reinforced_blue_stained_glass_pane_from_dye","minecraft:blue_stained_glass_pane_from_glass_pane","enderio:dark_steel_nugget_to_ingot","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","railcraft:water_tank_siding","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","securitycraft:reinforced_crimson_fence_gate","silentgear:upgrade_base","mcwbiomesoplenty:rainbow_birch_hedge","xnet:advanced_connector_green","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwroofs:prismarine_brick_upper_lower_roof","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","cfm:stripped_warped_kitchen_drawer","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","mcwroofs:prismarine_brick_upper_steep_roof","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","minecraft:golden_leggings","additionallanterns:gold_lantern","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","handcrafted:oak_dining_bench","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","simplylight:illuminant_pink_block_toggle","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","dyenamics:bed/maroon_bed","botania:blue_shiny_flower","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","mcwfences:bamboo_highley_gate","securitycraft:sc_manual","mcwpaths:dark_prismarine_windmill_weave","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","dyenamics:aquamarine_dye","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","silentgear:stone_torch","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwfences:jungle_curved_gate","securitycraft:reinforced_red_stained_glass_pane_from_glass","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","mcwwindows:dark_oak_curtain_rod","cfm:oak_desk","mcwwindows:green_mosaic_glass_pane","minecraft:dye_blue_bed","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","croptopia:carnitas","forbidden_arcanus:sanity_meter","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","sophisticatedstorage:generic_barrel","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","minecraft:cooked_porkchop_from_campfire_cooking","supplementaries:daub_frame","mcwfences:iron_cheval_de_frise","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","mcwlights:purple_paper_lamp","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","aquaculture:iron_fishing_rod","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","connectedglass:tinted_borderless_glass_blue2","domum_ornamentum:white_floating_carpet","minecraft:crafting_table","dyenamics:cherenkov_dye","dyenamics:lavender_stained_glass","twilightforest:time_boat","mcwlights:golden_wall_candle_holder","cfm:blue_kitchen_drawer","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","ae2:network/blocks/crank","mcwwindows:dark_oak_louvered_shutter","mcwwindows:green_mosaic_glass","mcwbridges:prismarine_bricks_bridge_stair","supplementaries:candle_holders/candle_holder_blue_dye","domum_ornamentum:blockbarreldeco_standing","rftoolsbuilder:red_shield_template_block","mcwwindows:bamboo_shutter","mcwfences:spruce_wired_fence","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","supplementaries:jar","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","dyenamics:ultramarine_stained_glass","pneumaticcraft:logistics_frame_passive_provider","securitycraft:motion_activated_light","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","mcwfences:double_curved_metal_fence","minecraft:oak_trapdoor","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","biomesoplenty:jacaranda_boat","minecraft:oak_door","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","aether:golden_pendant","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","mythicbotany:alfsteel_template","mcwwindows:prismarine_window","domum_ornamentum:green_floating_carpet","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","mcwwindows:spruce_curtain_rod","cfm:dye_blue_picket_gate","mcwbridges:asian_red_bridge","aether:skyroot_loom","utilitix:advanced_brewery","mcwfences:bamboo_horse_fence","cfm:oak_cabinet","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","minecraft:prismarine_wall_from_prismarine_stonecutting","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","ae2:network/cables/glass_blue","aquaculture:spruce_fish_mount","enderio:dark_steel_nugget","minecraft:candle","minecraft:oak_button","mcwroofs:prismarine_brick_attic_roof","simplylight:illuminant_purple_block_on_dyed","framedblocks:framed_cube","aquaculture:redstone_hook","supplementaries:flags/flag_blue","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","rftoolsbuilder:yellow_shield_template_block","xnet:netcable_blue_dye","xnet:advanced_connector_yellow_dye","mcwpaths:dark_prismarine_running_bond_path","domum_ornamentum:brown_floating_carpet","mcwlights:golden_triple_candle_holder","additionallanterns:diamond_lantern","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","securitycraft:reinforced_purple_stained_glass_pane_from_dye","minecraft:bow","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aquaculture:bobber","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","securitycraft:reinforced_lime_stained_glass_pane_from_glass","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwwindows:light_gray_mosaic_glass_pane","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_dyed","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","simplylight:illuminant_brown_block_on_dyed","bigreactors:energizer/computerport","handcrafted:bear_trophy","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","supplementaries:sack","cfm:black_cooler","minecraft:loom","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","supplementaries:sconce","cfm:stripped_oak_mail_box","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","rftoolsbase:crafting_card","aquaculture:gold_hook","forbidden_arcanus:deorum_glass","minecraft:oak_fence_gate","mcwroofs:prismarine_brick_roof","mcwbiomesoplenty:palm_wired_fence","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","rftoolsbuilder:space_chamber_card","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwbiomesoplenty:hellbark_stockade_fence","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwwindows:mangrove_shutter","mcwbiomesoplenty:umbran_pyramid_gate","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","cfm:lime_cooler","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:oak_planks_lower_roof","aquaculture:oak_fish_mount","sophisticatedstorage:storage_magnet_upgrade_from_backpack_magnet_upgrade","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","supplementaries:flags/flag_green","cfm:stripped_jungle_mail_box","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","handcrafted:oak_couch","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","handcrafted:blue_crockery_combo","minecraft:stick","securitycraft:reinforced_acacia_fence_gate","mcwbridges:prismarine_bricks_bridge_pier","mcwfences:red_sandstone_grass_topped_wall","mcwlights:light_blue_paper_lamp","forbidden_arcanus:deorum_lantern","additionallanterns:granite_lantern","allthecompressed:compress/oak_planks_1x","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","cfm:white_kitchen_drawer","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:silverfish_trophy","pneumaticcraft:logistics_frame_storage","mcwpaths:dark_prismarine_diamond_paving","mcwwindows:warped_shutter","xnet:connector_red","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","twigs:lamp","dyenamics:bed/lavender_bed","domum_ornamentum:yellow_floating_carpet","additionallanterns:diorite_lantern","mcwpaths:dark_prismarine_flagstone","simplylight:illuminant_cyan_block_dyed","minecraft:prismarine_wall","xnet:netcable_red_dye","cfm:oak_chair","simplylight:illuminant_pink_block_dyed","cfm:brown_cooler","utilitarian:utility/green_dye","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","mcwwindows:dark_prismarine_parapet","mcwwindows:pink_mosaic_glass_pane","minecraft:yellow_stained_glass","dyenamics:honey_stained_glass","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","cfm:green_cooler","alltheores:gold_rod","handcrafted:goat_trophy","mcwpaths:dark_prismarine_honeycomb_paving","bigreactors:blasting/graphite_from_coal","mcwlights:soul_warped_tiki_torch","mcwpaths:dark_prismarine_windmill_weave_stairs","paraglider:paraglider","mcwpaths:dark_prismarine_flagstone_path","biomesoplenty:magic_boat","mcwwindows:dark_prismarine_four_window","supplementaries:timber_cross_brace","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","simplylight:illuminant_orange_block_on_dyed","supplementaries:key","minecraft:cooked_mutton","securitycraft:reinforced_brown_stained_glass_pane_from_dye","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfences:bamboo_pyramid_gate","pneumaticcraft:logistics_frame_default_storage","cfm:oak_bedside_cabinet","minecraft:dark_prismarine_stairs","cfm:oak_park_bench","croptopia:pork_and_beans","mcwbiomesoplenty:jacaranda_picket_fence","additionallanterns:cobbled_deepslate_lantern","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","aether:golden_ring","travelersbackpack:pig","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","undergarden:catalyst","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","cfm:pink_kitchen_drawer","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwroofs:dark_prismarine_roof","mcwwindows:blue_mosaic_glass","croptopia:shaped_bacon","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:snowblossom_hedge","mcwlights:cross_lantern","domum_ornamentum:cyan_floating_carpet","minecraft:oak_boat","mcwwindows:birch_blinds","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwwindows:diorite_parapet","mcwbiomesoplenty:palm_highley_gate","mcwroofs:dark_prismarine_top_roof","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","minecraft:prismarine_slab","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","supplementaries:flags/flag_black","mcwwindows:acacia_louvered_shutter","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions","minecraft:cooked_mutton_from_campfire_cooking","minecraft:glass_bottle","enderio:dark_steel_trapdoor","mcwfences:mesh_metal_fence","cfm:light_gray_cooler","cfm:green_kitchen_drawer","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwbiomesoplenty:pine_curved_gate","minecraft:cooked_mutton_from_smoking","farmersdelight:cooking/pasta_with_mutton_chop","supplementaries:flags/flag_gray","securitycraft:reinforced_light_gray_stained_glass","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwroofs:dark_prismarine_lower_roof","mcwbiomesoplenty:palm_horse_fence","twilightforest:transformation_boat","botania:speed_up_belt","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","dyenamics:bed/cherenkov_bed","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","handcrafted:oak_cupboard","simplylight:edge_light","mcwlights:bamboo_tiki_torch","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","mcwroofs:oak_planks_upper_lower_roof","cfm:stripped_jungle_kitchen_drawer","handcrafted:oak_nightstand","dyenamics:wine_stained_glass","mcwwindows:prismarine_pane_window","domum_ornamentum:light_gray_floating_carpet","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","aether:skyroot_boat","cfm:magenta_kitchen_drawer","mcwpaths:dark_prismarine_windmill_weave_path","supplementaries:slice_map","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","computercraft:turtle_advanced_overlays/turtle_trans_overlay","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:stone_pillar_wall","mcwfences:birch_hedge","securitycraft:reinforced_light_blue_stained_glass","simplylight:illuminant_gray_block_toggle","additionallanterns:dark_prismarine_chain","utilitix:gilding_crystal","minecraft:dark_prismarine_stairs_from_dark_prismarine_stonecutting","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwlights:mangrove_tiki_torch","securitycraft:reinforced_sticky_piston","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","utilitix:armed_stand","botania:lexicon","dyenamics:ultramarine_dye","additionallanterns:purpur_lantern","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","supplementaries:flags/flag_purple","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:palm_hedge","ae2:network/cables/smart_blue","mcwlights:green_paper_lamp","mcwwindows:birch_plank_parapet","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","minecraft:purple_dye","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","mcwfences:oak_hedge","mcwwindows:jungle_shutter","mcwroofs:oak_planks_roof","mcwfences:railing_sandstone_wall","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","deepresonance:resonating_plate","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwfences:modern_diorite_wall","mcwwindows:granite_parapet","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:cyan_dye","dyenamics:bed/wine_bed","handcrafted:skeleton_horse_trophy","mcwbiomesoplenty:hellbark_highley_gate","additionallanterns:warped_lantern","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","cfm:stripped_spruce_mail_box","minecraft:lead","mcwfences:spruce_highley_gate","minecraft:spruce_boat","minecraft:birch_boat","minecraft:gold_block","aquaculture:gold_fillet_knife","dyenamics:banner/navy_banner","travelersbackpack:gold","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","minecraft:campfire","handcrafted:oak_drawer","mcwfences:warped_curved_gate","ad_astra:small_blue_industrial_lamp","mcwfences:bamboo_stockade_fence","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","connectedglass:clear_glass_blue_pane2","mcwwindows:pink_mosaic_glass","mcwwindows:warped_plank_parapet","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","mcwwindows:jungle_louvered_shutter","cfm:orange_cooler","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","mcwpaths:dark_prismarine_strewn_rocky_path","minecraft:lime_stained_glass","mcwfences:railing_quartz_wall","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","bigreactors:smelting/graphite_from_coal","handcrafted:salmon_trophy","mcwlights:bell_wall_lantern","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","minecraft:black_stained_glass","cfm:oak_desk_cabinet","mcwpaths:dark_prismarine_square_paving"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:11928,warning_level:0}}