{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:4.5d,Name:"forge:block_reach"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"attributeslib:creative_flight"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;**********,**********,-**********,-814586354],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"crafting_on_a_stick:crafting_table"}],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:10069},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"crafting_on_a_stick:crafting_table",ItemStack:{Count:1b,id:"crafting_on_a_stick:crafting_table"}}],SelectedRecipe:"crafting_on_a_stick:crafting_table"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:12,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:cooked_beef"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:1,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-612,tb_last_ground_location_y:253,tb_last_ground_location_z:-102,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},apoth_reforge_seed:0,"quark:trying_crawl":0b,simplylight_unlocked:2},Health:12.0f,HurtByTimestamp:9286,HurtTime:0s,Inventory:[{Count:6b,Slot:0b,id:"minecraft:dirt"},{Count:1b,Slot:2b,id:"minecraft:jungle_log"},{Count:31b,Slot:9b,id:"minecraft:oak_log"},{Count:2b,Slot:10b,id:"minecraft:jungle_planks"},{Count:9b,Slot:11b,id:"minecraft:sand"},{Count:2b,Slot:12b,id:"minecraft:oak_sapling"},{Count:1b,Slot:13b,id:"minecraft:poppy"},{Count:1b,Slot:14b,id:"minecraft:rose_bush"},{Count:7b,Slot:15b,id:"minecraft:torch"},{Count:1b,Slot:16b,id:"minecraft:map"},{Count:1b,Slot:17b,id:"minecraft:egg"},{Count:5b,Slot:18b,id:"minecraft:cooked_beef"},{Count:3b,Slot:19b,id:"minecraft:stick"},{Count:1b,Slot:20b,id:"occultism:datura_seeds"},{Count:5b,Slot:21b,id:"twigs:twig"},{Count:1b,Slot:22b,id:"twigs:opaline_seashell"},{Count:1b,Slot:23b,id:"patchouli:guide_book",tag:{"patchouli:book":"allthemodium:allthemodium_book"}},{Count:1b,Slot:24b,id:"tombstone:gift",tag:{Items:[{Count:1b,id:"tombstone:grave_dust"}]}}],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:mining",pos:[I;481,-63,-17]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-219.27210414715958d,70.0d,-261.36742721068515d],Railways_DataVersion:2,Rotation:[-122.33417f,22.051428f],Score:190,SelectedItemSlot:4,SleepTimer:0s,Spigot.ticksLived:10069,UUID:[I;-1112824060,-416202245,-1452827023,746447673],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-60198262693818L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:10,XpP:0.40740725f,XpSeed:1002425290,XpTotal:171,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752505464699L,keepLevel:0b,lastKnownName:"Yu565",lastPlayed:1752508430476L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:1.6186198f,foodLevel:14,foodSaturationLevel:8.8f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:stripped_palm_pane_window","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","handcrafted:spider_trophy","mcwtrpdoors:jungle_cottage_trapdoor","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","mcwbiomesoplenty:palm_plank_window","mcwpaths:jungle_planks_path","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","mcwdoors:jungle_nether_door","mcwwindows:oak_plank_pane_window","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","securitycraft:reinforced_cherry_fence_gate","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecolonies:shapetool","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","mcwtrpdoors:jungle_blossom_trapdoor","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","dyenamics:aquamarine_concrete_powder","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","cfm:oak_kitchen_counter","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","additionallanterns:stone_bricks_lantern","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwwindows:birch_plank_window","minecraft:jungle_fence_gate","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:mahogany_window","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwlights:tavern_lantern","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","dyenamics:bed/navy_bed","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","mcwdoors:jungle_bark_glass_door","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","mcwwindows:red_sandstone_window","supplementaries:daub_cross_brace","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwbiomesoplenty:umbran_plank_four_window","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","handcrafted:jungle_bench","mcwfences:stone_grass_topped_wall","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:pine_plank_window2","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","mcwbiomesoplenty:palm_plank_window2","cfm:stripped_jungle_bedside_cabinet","travelersbackpack:cake","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","mcwwindows:granite_pane_window","mcwbiomesoplenty:stripped_dead_pane_window","handcrafted:jungle_drawer","mcwwindows:dark_prismarine_window2","mcwwindows:quartz_window","securitycraft:reinforced_dark_oak_fence","cfm:stripped_jungle_cabinet","mcwroofs:oak_roof","cfm:jungle_desk","mcwbiomesoplenty:mahogany_horse_fence","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_blue_concrete_lower_roof","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwbiomesoplenty:willow_curved_gate","mcwlights:soul_birch_tiki_torch","mcwbridges:rope_jungle_bridge","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","mcwdoors:oak_four_panel_door","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","mcwwindows:stripped_cherry_log_window2","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:green_concrete_roof","mcwroofs:jungle_upper_steep_roof","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","cfm:stripped_jungle_chair","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwwindows:spruce_window2","mcwwindows:andesite_four_window","mcwtrpdoors:jungle_swamp_trapdoor","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwbiomesoplenty:pine_stockade_fence","mcwfurnitures:oak_double_drawer_counter","mcwbiomesoplenty:redwood_pyramid_gate","mcwwindows:birch_plank_pane_window","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","mcwlights:tavern_wall_lantern","mcwfurnitures:jungle_wardrobe","dyenamics:bed/rose_bed","supplementaries:pancake_fd","mcwfences:end_brick_railing_gate","additionallanterns:iron_lantern","simplylight:illuminant_light_gray_block_toggle","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwfurnitures:stripped_oak_lower_triple_drawer","utilitarian:utility/oak_logs_to_boats","undergarden:wigglewood_boat","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwdoors:oak_cottage_door","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwpaths:red_sand_path_block","mcwdoors:jungle_stable_door","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","mcwbiomesoplenty:pine_window2","undergarden:gloom_o_lantern","mcwwindows:birch_window","mcwwindows:blackstone_four_window","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","mcwfurnitures:oak_wardrobe","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwwindows:stripped_dark_oak_log_window2","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","mcwdoors:jungle_japanese2_door","mcwwindows:stone_pane_window","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:stripped_hellbark_log_window","blue_skies:cake_compat","mcwbiomesoplenty:maple_pyramid_gate","mcwfurnitures:oak_double_wardrobe","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwwindows:stripped_dark_oak_pane_window","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","cfm:oak_crate","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:purple_concrete_powder","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","cfm:stripped_oak_chair","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:jungle_counter","mcwroofs:lime_concrete_steep_roof","mcwdoors:jungle_swamp_door","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwwindows:warped_planks_four_window","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","croptopia:meringue","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","mcwwindows:crimson_planks_four_window","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwwindows:diorite_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","mcwbiomesoplenty:mahogany_plank_window2","cfm:stripped_oak_desk","mcwfurnitures:oak_modern_wardrobe","mcwtrpdoors:oak_glass_trapdoor","mcwfences:panelled_metal_fence_gate","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","mcwfences:mangrove_picket_fence","farmersdelight:fried_egg_from_campfire_cooking","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwwindows:jungle_plank_four_window","supplementaries:flags/flag_lime","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","supplementaries:flags/flag_magenta","mcwroofs:lime_concrete_attic_roof","mcwfurnitures:oak_counter","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","additionallanterns:bricks_lantern","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","mcwroofs:oak_top_roof","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","additionallanterns:amethyst_lantern","mcwwindows:stone_window","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","mcwfurnitures:jungle_stool_chair","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwfurnitures:stripped_oak_bookshelf","mcwfences:crimson_picket_fence","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","mcwfurnitures:jungle_chair","mcwroofs:red_concrete_upper_steep_roof","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_orange_block_dyed","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","mcwdoors:oak_japanese2_door","mcwroofs:jungle_roof","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","mcwwindows:window_base","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","mcwwindows:mangrove_window","mcwwindows:quartz_pane_window","cfm:oak_desk","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","supplementaries:daub_frame","mcwbiomesoplenty:jacaranda_highley_gate","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","minecraft:crafting_table","twilightforest:time_boat","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:stripped_oak_large_drawer","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","mcwbiomesoplenty:magic_wired_fence","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwwindows:stripped_acacia_log_four_window","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","aquaculture:spruce_fish_mount","mcwwindows:stone_window2","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","mcwbiomesoplenty:maple_window","supplementaries:flags/flag_blue","mcwbiomesoplenty:stripped_pine_pane_window","mcwroofs:jungle_planks_top_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","additionallanterns:diamond_lantern","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","aquaculture:bobber","mcwwindows:quartz_window2","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","mcwtrpdoors:oak_bark_trapdoor","rftoolsbase:crafting_card","mcwroofs:light_gray_concrete_steep_roof","supplementaries:turn_table","supplementaries:flags/flag_pink","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","undergarden:smogstem_boat","mcwbiomesoplenty:redwood_wired_fence","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","mcwfences:warped_wired_fence","mcwbiomesoplenty:stripped_palm_log_four_window","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_four_window","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwfurnitures:jungle_end_table","mcwwindows:red_sandstone_pane_window","undergarden:shard_torch","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","paraglider:paraglider","mcwwindows:dark_prismarine_four_window","simplylight:illuminant_lime_block_on_dyed","mcwbridges:jungle_bridge_pier","mcwbiomesoplenty:willow_pane_window","mcwtrpdoors:oak_barrel_trapdoor","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwwindows:spruce_plank_pane_window","minecraft:oak_boat","sophisticatedstorage:jungle_chest","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","mcwtrpdoors:oak_mystic_trapdoor","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwdoors:print_whispering","minecraft:jungle_fence","mcwroofs:lime_concrete_lower_roof","mcwfences:mesh_metal_fence","mcwroofs:oak_upper_steep_roof","occultism:crafting/demons_dream_essence_from_seeds","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwbiomesoplenty:magic_plank_window2","dyenamics:banner/conifer_banner","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwwindows:spruce_four_window","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","botania:speed_up_belt","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","mcwroofs:grass_steep_roof","mcwwindows:prismarine_pane_window","mcwdoors:jungle_stable_head_door","croptopia:egg_roll","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:slice_map","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","mcwfences:end_brick_pillar_wall","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_jungle_coffee_table","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","handcrafted:jungle_counter","dyenamics:banner/navy_banner","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwdoors:oak_stable_door","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","cfm:oak_upgraded_gate","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","handcrafted:salmon_trophy","mcwroofs:grass_upper_lower_roof","dyenamics:fluorescent_concrete_powder","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","cfm:oak_desk_cabinet","corail_woodcutter:birch_woodcutter","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","mcwroofs:black_concrete_attic_roof","minecraft:charcoal","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","mcwbiomesoplenty:magic_pane_window","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","utilitix:reinforced_rail","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","utilitarian:utility/jungle_logs_to_doors","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","mcwfurnitures:oak_end_table","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","mcwwindows:stripped_acacia_log_window2","mcwwindows:dark_prismarine_pane_window","mcwfurnitures:oak_modern_desk","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","mcwfurnitures:jungle_table","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","mcwfences:acacia_stockade_fence","aquaculture:wooden_fillet_knife","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:maple_plank_window","twilightforest:canopy_boat","mcwfences:warped_horse_fence","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwbiomesoplenty:maple_four_window","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwbiomesoplenty:jacaranda_four_window","undergarden:torch_ditchbulb_paste","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:dark_oak_plank_window","mcwwindows:granite_window2","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","mcwroofs:magenta_concrete_upper_lower_roof","mcwbiomesoplenty:dead_plank_window","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","aquaculture:tin_can_to_iron_nugget","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","mcwbiomesoplenty:hellbark_plank_pane_window","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:brown_concrete_attic_roof","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:light_blue_concrete_powder","supplementaries:timber_frame","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","mcwroofs:red_concrete_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","mcwwindows:granite_four_window","mcwlights:wall_lantern","mcwroofs:light_blue_concrete_steep_roof","mcwbiomesoplenty:willow_plank_window","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwroofs:orange_concrete_roof","mcwroofs:lime_concrete_upper_steep_roof","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","minecraft:white_concrete_powder","mcwwindows:acacia_window","minecraft:cherry_boat","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwwindows:dark_oak_pane_window","mcwlights:pink_paper_lamp","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","aquaculture:fishing_line","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","mcwroofs:jungle_planks_roof","farmersdelight:fried_egg_from_smoking","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","dyenamics:ultramarine_concrete_powder","mcwfurnitures:oak_kitchen_cabinet","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","utilitarian:utility/jungle_logs_to_stairs","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","dyenamics:peach_concrete_powder","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:birch_curved_gate","supplementaries:faucet","mcwbiomesoplenty:mahogany_window2","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","create:jungle_window","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwfences:dark_oak_curved_gate","mcwwindows:crimson_plank_pane_window","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","minecraft:chest","mcwroofs:blue_concrete_top_roof","supplementaries:pulley","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","additionallanterns:basalt_lantern","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","mcwbiomesoplenty:empyreal_window2","mcwfences:sandstone_pillar_wall","cfm:stripped_oak_park_bench","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","mcwroofs:magenta_concrete_lower_roof","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","mcwfurnitures:stripped_oak_modern_chair","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwwindows:oak_plank_window2","mcwbiomesoplenty:pine_plank_window","farmersdelight:cutting_board","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwbiomesoplenty:willow_plank_window2","mcwdoors:jungle_classic_door","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:lime_concrete_roof","allthecompressed:compress/oak_log_1x","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","simplylight:illuminant_light_gray_block_on_toggle","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","allthecompressed:compress/sand_1x","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","aquaculture:jellyfish_to_slimeball","mcwwindows:stripped_dark_oak_log_window","cfm:stripped_jungle_desk_cabinet","minecraft:jungle_wood","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwroofs:green_concrete_attic_roof","mcwwindows:diorite_four_window","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","mcwroofs:light_gray_concrete_top_roof","mcwwindows:mangrove_plank_window2","mcwfurnitures:jungle_bookshelf_cupboard","cfm:jungle_desk_cabinet","mcwwindows:mangrove_pane_window","minecraft:jungle_stairs","additionallanterns:smooth_stone_lantern","mcwbiomesoplenty:pine_plank_four_window","handcrafted:jungle_chair","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwwindows:stripped_birch_log_four_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","cfm:jungle_kitchen_sink_dark","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","mcwlights:gray_paper_lamp","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:stripped_umbran_pane_window","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwwindows:spruce_plank_four_window","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","mcwfences:guardian_metal_fence","minecraft:mangrove_boat","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwfurnitures:stripped_oak_cupboard_counter","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwwindows:stripped_warped_stem_four_window","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","biomesoplenty:willow_boat","mcwfences:modern_red_sandstone_wall","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwwindows:crimson_planks_window2","mcwwindows:deepslate_four_window","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwfurnitures:oak_covered_desk","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","mcwwindows:mangrove_plank_pane_window","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","mcwfences:quartz_grass_topped_wall","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","mcwroofs:brown_concrete_top_roof","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","mcwroofs:lime_concrete_upper_lower_roof","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","mcwwindows:stripped_mangrove_log_window2","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","mcwwindows:stripped_cherry_log_four_window","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","twigs:bone_meal_from_seashells","mcwfences:oak_curved_gate","aquaculture:brown_mushroom_from_fish","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","mcwdoors:oak_swamp_door","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","mcwwindows:cherry_window2","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","handcrafted:wither_skeleton_trophy","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwwindows:dark_oak_plank_pane_window","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:stripped_spruce_log_four_window","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwfurnitures:oak_coffee_table","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwwindows:spruce_plank_window2","mcwwindows:spruce_window","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwwindows:acacia_four_window","mcwdoors:jungle_tropical_door","twigs:lamp","dyenamics:bed/lavender_bed","mcwroofs:brown_concrete_lower_roof","mcwbiomesoplenty:hellbark_window","mcwdoors:jungle_glass_door","simplylight:illuminant_cyan_block_dyed","mcwfurnitures:jungle_triple_drawer","crafting_on_a_stick:crafting_table","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:jungle_lower_bookshelf_drawer","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","farmersdelight:fried_egg","supplementaries:timber_cross_brace","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwroofs:purple_concrete_top_roof","simplylight:illuminant_orange_block_on_dyed","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","mcwroofs:white_concrete_attic_roof","mcwroofs:blue_concrete_lower_roof","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:magic_window2","mcwlights:cross_lantern","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","mcwbiomesoplenty:redwood_plank_four_window","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","mcwbiomesoplenty:fir_four_window","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwlights:warped_tiki_torch","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","cfm:stripped_jungle_kitchen_drawer","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","mcwfences:stone_pillar_wall","mcwfences:spruce_stockade_fence","cfm:jungle_table","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","utilitix:armed_stand","mcwwindows:mangrove_window2","botania:lexicon","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwlights:green_paper_lamp","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","simplylight:illuminant_black_block_on_dyed","minecraft:birch_boat","mcwfences:spruce_highley_gate","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","mcwlights:bell_wall_lantern","mcwbiomesoplenty:stripped_umbran_log_four_window"],toBeDisplayed:["mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:stripped_palm_pane_window","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","handcrafted:spider_trophy","mcwtrpdoors:jungle_cottage_trapdoor","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","mcwbiomesoplenty:palm_plank_window","mcwpaths:jungle_planks_path","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","mcwdoors:jungle_nether_door","mcwwindows:oak_plank_pane_window","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","securitycraft:reinforced_cherry_fence_gate","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecolonies:shapetool","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","mcwtrpdoors:jungle_blossom_trapdoor","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","dyenamics:aquamarine_concrete_powder","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","cfm:oak_kitchen_counter","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","additionallanterns:stone_bricks_lantern","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwwindows:birch_plank_window","minecraft:jungle_fence_gate","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:mahogany_window","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwlights:tavern_lantern","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","dyenamics:bed/navy_bed","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","mcwdoors:jungle_bark_glass_door","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","mcwwindows:red_sandstone_window","supplementaries:daub_cross_brace","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwbiomesoplenty:umbran_plank_four_window","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","handcrafted:jungle_bench","mcwfences:stone_grass_topped_wall","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:pine_plank_window2","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","mcwbiomesoplenty:palm_plank_window2","cfm:stripped_jungle_bedside_cabinet","travelersbackpack:cake","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","mcwwindows:granite_pane_window","mcwbiomesoplenty:stripped_dead_pane_window","handcrafted:jungle_drawer","mcwwindows:dark_prismarine_window2","mcwwindows:quartz_window","securitycraft:reinforced_dark_oak_fence","cfm:stripped_jungle_cabinet","mcwroofs:oak_roof","cfm:jungle_desk","mcwbiomesoplenty:mahogany_horse_fence","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_blue_concrete_lower_roof","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwbiomesoplenty:willow_curved_gate","mcwlights:soul_birch_tiki_torch","mcwbridges:rope_jungle_bridge","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","mcwdoors:oak_four_panel_door","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","mcwwindows:stripped_cherry_log_window2","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:green_concrete_roof","mcwroofs:jungle_upper_steep_roof","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","cfm:stripped_jungle_chair","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwwindows:spruce_window2","mcwwindows:andesite_four_window","mcwtrpdoors:jungle_swamp_trapdoor","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwbiomesoplenty:pine_stockade_fence","mcwfurnitures:oak_double_drawer_counter","mcwbiomesoplenty:redwood_pyramid_gate","mcwwindows:birch_plank_pane_window","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","mcwlights:tavern_wall_lantern","mcwfurnitures:jungle_wardrobe","dyenamics:bed/rose_bed","supplementaries:pancake_fd","mcwfences:end_brick_railing_gate","additionallanterns:iron_lantern","simplylight:illuminant_light_gray_block_toggle","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwfurnitures:stripped_oak_lower_triple_drawer","utilitarian:utility/oak_logs_to_boats","undergarden:wigglewood_boat","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwdoors:oak_cottage_door","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","mcwpaths:red_sand_path_block","mcwdoors:jungle_stable_door","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","mcwbiomesoplenty:pine_window2","undergarden:gloom_o_lantern","mcwwindows:birch_window","mcwwindows:blackstone_four_window","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","mcwfurnitures:oak_wardrobe","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwwindows:stripped_dark_oak_log_window2","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","mcwdoors:jungle_japanese2_door","mcwwindows:stone_pane_window","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:stripped_hellbark_log_window","blue_skies:cake_compat","mcwbiomesoplenty:maple_pyramid_gate","mcwfurnitures:oak_double_wardrobe","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwwindows:stripped_dark_oak_pane_window","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","dyenamics:lavender_concrete_powder","cfm:oak_crate","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:purple_concrete_powder","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","cfm:stripped_oak_chair","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:jungle_counter","mcwroofs:lime_concrete_steep_roof","mcwdoors:jungle_swamp_door","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwwindows:warped_planks_four_window","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","croptopia:meringue","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","mcwwindows:crimson_planks_four_window","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwwindows:diorite_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","mcwbiomesoplenty:mahogany_plank_window2","cfm:stripped_oak_desk","mcwfurnitures:oak_modern_wardrobe","mcwtrpdoors:oak_glass_trapdoor","mcwfences:panelled_metal_fence_gate","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","mcwfences:mangrove_picket_fence","farmersdelight:fried_egg_from_campfire_cooking","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwwindows:jungle_plank_four_window","supplementaries:flags/flag_lime","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","supplementaries:flags/flag_magenta","mcwroofs:lime_concrete_attic_roof","mcwfurnitures:oak_counter","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","additionallanterns:bricks_lantern","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","mcwroofs:oak_top_roof","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","additionallanterns:amethyst_lantern","mcwwindows:stone_window","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","mcwfurnitures:jungle_stool_chair","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwfurnitures:stripped_oak_bookshelf","mcwfences:crimson_picket_fence","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","mcwfurnitures:jungle_chair","mcwroofs:red_concrete_upper_steep_roof","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_orange_block_dyed","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","mcwdoors:oak_japanese2_door","mcwroofs:jungle_roof","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","mcwwindows:window_base","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","mcwwindows:mangrove_window","mcwwindows:quartz_pane_window","cfm:oak_desk","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","supplementaries:daub_frame","mcwbiomesoplenty:jacaranda_highley_gate","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","minecraft:crafting_table","twilightforest:time_boat","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:stripped_oak_large_drawer","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","mcwbiomesoplenty:magic_wired_fence","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwwindows:stripped_acacia_log_four_window","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","aquaculture:spruce_fish_mount","mcwwindows:stone_window2","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","mcwbiomesoplenty:maple_window","supplementaries:flags/flag_blue","mcwbiomesoplenty:stripped_pine_pane_window","mcwroofs:jungle_planks_top_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","additionallanterns:diamond_lantern","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","aquaculture:bobber","mcwwindows:quartz_window2","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","mcwtrpdoors:oak_bark_trapdoor","rftoolsbase:crafting_card","mcwroofs:light_gray_concrete_steep_roof","supplementaries:turn_table","supplementaries:flags/flag_pink","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","undergarden:smogstem_boat","mcwbiomesoplenty:redwood_wired_fence","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","mcwfences:warped_wired_fence","mcwbiomesoplenty:stripped_palm_log_four_window","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_four_window","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwfurnitures:jungle_end_table","mcwwindows:red_sandstone_pane_window","undergarden:shard_torch","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","paraglider:paraglider","mcwwindows:dark_prismarine_four_window","simplylight:illuminant_lime_block_on_dyed","mcwbridges:jungle_bridge_pier","mcwbiomesoplenty:willow_pane_window","mcwtrpdoors:oak_barrel_trapdoor","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwwindows:spruce_plank_pane_window","minecraft:oak_boat","sophisticatedstorage:jungle_chest","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","mcwtrpdoors:oak_mystic_trapdoor","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwdoors:print_whispering","minecraft:jungle_fence","mcwroofs:lime_concrete_lower_roof","mcwfences:mesh_metal_fence","mcwroofs:oak_upper_steep_roof","occultism:crafting/demons_dream_essence_from_seeds","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwbiomesoplenty:magic_plank_window2","dyenamics:banner/conifer_banner","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwwindows:spruce_four_window","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","botania:speed_up_belt","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","mcwroofs:grass_steep_roof","mcwwindows:prismarine_pane_window","mcwdoors:jungle_stable_head_door","croptopia:egg_roll","mcwlights:spruce_tiki_torch","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:slice_map","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","mcwfences:end_brick_pillar_wall","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_jungle_coffee_table","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","simplylight:illuminant_purple_block_dyed","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","handcrafted:jungle_counter","dyenamics:banner/navy_banner","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwdoors:oak_stable_door","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","cfm:oak_upgraded_gate","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","handcrafted:salmon_trophy","mcwroofs:grass_upper_lower_roof","dyenamics:fluorescent_concrete_powder","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","cfm:oak_desk_cabinet","corail_woodcutter:birch_woodcutter","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","mcwroofs:black_concrete_attic_roof","minecraft:charcoal","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","mcwbiomesoplenty:magic_pane_window","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","utilitix:reinforced_rail","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","utilitarian:utility/jungle_logs_to_doors","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","mcwfurnitures:oak_end_table","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","mcwwindows:stripped_acacia_log_window2","mcwwindows:dark_prismarine_pane_window","mcwfurnitures:oak_modern_desk","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","mcwfurnitures:jungle_table","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","mcwfences:acacia_stockade_fence","aquaculture:wooden_fillet_knife","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:maple_plank_window","twilightforest:canopy_boat","mcwfences:warped_horse_fence","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwbiomesoplenty:maple_four_window","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwbiomesoplenty:jacaranda_four_window","undergarden:torch_ditchbulb_paste","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:dark_oak_plank_window","mcwwindows:granite_window2","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","mcwroofs:magenta_concrete_upper_lower_roof","mcwbiomesoplenty:dead_plank_window","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","aquaculture:tin_can_to_iron_nugget","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","mcwbiomesoplenty:hellbark_plank_pane_window","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:brown_concrete_attic_roof","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:light_blue_concrete_powder","supplementaries:timber_frame","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","dyenamics:banner/honey_banner","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","mcwroofs:red_concrete_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","mcwwindows:granite_four_window","mcwlights:wall_lantern","mcwroofs:light_blue_concrete_steep_roof","mcwbiomesoplenty:willow_plank_window","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwroofs:orange_concrete_roof","mcwroofs:lime_concrete_upper_steep_roof","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","minecraft:white_concrete_powder","mcwwindows:acacia_window","minecraft:cherry_boat","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwwindows:dark_oak_pane_window","mcwlights:pink_paper_lamp","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","aquaculture:fishing_line","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","mcwroofs:jungle_planks_roof","farmersdelight:fried_egg_from_smoking","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","dyenamics:ultramarine_concrete_powder","mcwfurnitures:oak_kitchen_cabinet","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","utilitarian:utility/jungle_logs_to_stairs","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","dyenamics:peach_concrete_powder","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:birch_curved_gate","supplementaries:faucet","mcwbiomesoplenty:mahogany_window2","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","create:jungle_window","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwfences:dark_oak_curved_gate","mcwwindows:crimson_plank_pane_window","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","minecraft:chest","mcwroofs:blue_concrete_top_roof","supplementaries:pulley","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","additionallanterns:basalt_lantern","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","mcwbiomesoplenty:empyreal_window2","mcwfences:sandstone_pillar_wall","cfm:stripped_oak_park_bench","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","mcwroofs:magenta_concrete_lower_roof","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","mcwfurnitures:stripped_oak_modern_chair","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwwindows:oak_plank_window2","mcwbiomesoplenty:pine_plank_window","farmersdelight:cutting_board","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwbiomesoplenty:willow_plank_window2","mcwdoors:jungle_classic_door","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:lime_concrete_roof","allthecompressed:compress/oak_log_1x","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","simplylight:illuminant_light_gray_block_on_toggle","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","allthecompressed:compress/sand_1x","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","aquaculture:jellyfish_to_slimeball","mcwwindows:stripped_dark_oak_log_window","cfm:stripped_jungle_desk_cabinet","minecraft:jungle_wood","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwroofs:green_concrete_attic_roof","mcwwindows:diorite_four_window","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","mcwroofs:light_gray_concrete_top_roof","mcwwindows:mangrove_plank_window2","mcwfurnitures:jungle_bookshelf_cupboard","cfm:jungle_desk_cabinet","mcwwindows:mangrove_pane_window","minecraft:jungle_stairs","additionallanterns:smooth_stone_lantern","mcwbiomesoplenty:pine_plank_four_window","handcrafted:jungle_chair","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwwindows:stripped_birch_log_four_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","cfm:jungle_kitchen_sink_dark","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","mcwlights:gray_paper_lamp","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:stripped_umbran_pane_window","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwwindows:spruce_plank_four_window","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","mcwfences:guardian_metal_fence","minecraft:mangrove_boat","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwfurnitures:stripped_oak_cupboard_counter","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwwindows:stripped_warped_stem_four_window","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","biomesoplenty:willow_boat","mcwfences:modern_red_sandstone_wall","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwwindows:crimson_planks_window2","mcwwindows:deepslate_four_window","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwfurnitures:oak_covered_desk","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","mcwwindows:mangrove_plank_pane_window","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","mcwfences:quartz_grass_topped_wall","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","mcwroofs:brown_concrete_top_roof","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","mcwroofs:lime_concrete_upper_lower_roof","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","mcwwindows:stripped_mangrove_log_window2","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","mcwwindows:stripped_cherry_log_four_window","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","twigs:bone_meal_from_seashells","mcwfences:oak_curved_gate","aquaculture:brown_mushroom_from_fish","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","mcwdoors:oak_swamp_door","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","mcwwindows:cherry_window2","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","handcrafted:wither_skeleton_trophy","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwwindows:dark_oak_plank_pane_window","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:stripped_spruce_log_four_window","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwfurnitures:oak_coffee_table","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwwindows:spruce_plank_window2","mcwwindows:spruce_window","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwwindows:acacia_four_window","mcwdoors:jungle_tropical_door","twigs:lamp","dyenamics:bed/lavender_bed","mcwroofs:brown_concrete_lower_roof","mcwbiomesoplenty:hellbark_window","mcwdoors:jungle_glass_door","simplylight:illuminant_cyan_block_dyed","mcwfurnitures:jungle_triple_drawer","crafting_on_a_stick:crafting_table","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:jungle_lower_bookshelf_drawer","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","farmersdelight:fried_egg","supplementaries:timber_cross_brace","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwroofs:purple_concrete_top_roof","simplylight:illuminant_orange_block_on_dyed","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","mcwroofs:white_concrete_attic_roof","mcwroofs:blue_concrete_lower_roof","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:magic_window2","mcwlights:cross_lantern","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","mcwbiomesoplenty:redwood_plank_four_window","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","mcwbiomesoplenty:fir_four_window","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwlights:warped_tiki_torch","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","cfm:stripped_jungle_kitchen_drawer","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","mcwfences:stone_pillar_wall","mcwfences:spruce_stockade_fence","cfm:jungle_table","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","supplementaries:slingshot","simplylight:illuminant_green_block_toggle","utilitix:armed_stand","mcwwindows:mangrove_window2","botania:lexicon","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwlights:green_paper_lamp","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","simplylight:illuminant_black_block_on_dyed","minecraft:birch_boat","mcwfences:spruce_highley_gate","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","mcwlights:bell_wall_lantern","mcwbiomesoplenty:stripped_umbran_log_four_window"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:62,warning_level:0}}