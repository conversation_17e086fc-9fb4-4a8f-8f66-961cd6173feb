{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:4.5d,Name:"forge:block_reach"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"attributeslib:creative_flight"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:1116},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:0b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{},"rftoolsutility:properties":{allowFlying:0b,buffTicks:145,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["minecraft:cooked_beef"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:1,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,**********,-**********,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},apoth_reforge_seed:0},Health:20.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:9b,id:"tombstone:gift",tag:{Items:[{Count:1b,id:"tombstone:grave_dust"}]}},{Count:1b,Slot:10b,id:"twigs:opaline_seashell"},{Count:1b,Slot:11b,id:"minecraft:map"},{Count:6b,Slot:12b,id:"minecraft:cooked_beef"},{Count:7b,Slot:13b,id:"minecraft:torch"},{Count:1b,Slot:14b,id:"patchouli:guide_book",tag:{"patchouli:book":"allthemodium:allthemodium_book"}},{Count:21b,Slot:15b,id:"minecraft:oak_log"},{Count:9b,Slot:16b,id:"minecraft:sand"},{Count:1b,Slot:17b,id:"minecraft:egg"}],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:mining",pos:[I;481,-63,-17]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-521.1111539199939d,253.0d,-148.07051200428674d],Railways_DataVersion:2,Rotation:[92.45239f,28.199749f],Score:20,SelectedItemSlot:8,SleepTimer:0s,Spigot.ticksLived:1116,UUID:[I;-**********,-416202245,-**********,746447673],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-143211390127875L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:0,XpP:0.14285715f,XpSeed:0,XpTotal:1,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752505464699L,keepLevel:0b,lastKnownName:"Yu565",lastPlayed:1752507230367L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:3.3110006f,foodLevel:20,foodSaturationLevel:3.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["corail_woodcutter:birch_woodcutter","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","mcwfences:granite_railing_gate","mcwroofs:black_concrete_attic_roof","mcwroofs:magenta_concrete_top_roof","minecraft:charcoal","additionallanterns:copper_lantern","mcwfurnitures:stripped_oak_double_wardrobe","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwfences:railing_deepslate_brick_wall","mcwbiomesoplenty:stripped_palm_pane_window","mcwbiomesoplenty:jacaranda_window2","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:hellbark_plank_window","mcwbiomesoplenty:magic_pane_window","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","mcwfences:dark_oak_stockade_fence","mcwbiomesoplenty:empyreal_hedge","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","corail_woodcutter:crimson_woodcutter","mcwfurnitures:oak_end_table","mcwwindows:oak_plank_pane_window","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","mcwfences:acacia_wired_fence","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","mcwlights:soul_mangrove_tiki_torch","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwlights:black_paper_lamp","simplylight:rodlamp","mcwroofs:purple_concrete_upper_lower_roof","mcwwindows:stripped_acacia_log_window2","mcwwindows:dark_prismarine_pane_window","mcwwindows:stripped_oak_log_four_window","mcwwindows:stripped_birch_log_window2","mcwfurnitures:oak_modern_desk","aquaculture:iron_nugget_from_smelting","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:maple_plank_window","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","twilightforest:canopy_boat","mcwfences:warped_horse_fence","mcwbiomesoplenty:mahogany_curved_gate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","dyenamics:aquamarine_concrete_powder","mcwbiomesoplenty:maple_four_window","mcwwindows:acacia_curtain_rod","mcwlights:striped_lantern","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwlights:chain_lantern","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwbiomesoplenty:jacaranda_four_window","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:dark_oak_plank_window","mcwwindows:granite_window2","cfm:oak_kitchen_counter","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","biomesoplenty:mahogany_boat","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwwindows:diorite_pane_window","solcarrot:food_book","additionallanterns:stone_bricks_lantern","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","additionallanterns:stone_lantern","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","mcwroofs:magenta_concrete_upper_lower_roof","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","mcwbiomesoplenty:dead_plank_window","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwroofs:cyan_concrete_steep_roof","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","mcwfurnitures:oak_desk","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwwindows:birch_plank_window","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwbiomesoplenty:stripped_dead_log_window2","aquaculture:tin_can_to_iron_nugget","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","dyenamics:spring_green_concrete_powder","mcwroofs:red_concrete_steep_roof","minecraft:glass","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","mcwbiomesoplenty:hellbark_plank_pane_window","mcwlights:tavern_lantern","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","mcwroofs:brown_concrete_attic_roof","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:festive_wall_lantern","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","minecraft:light_blue_concrete_powder","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwbiomesoplenty:umbran_plank_four_window","mcwfences:deepslate_pillar_wall","mcwbiomesoplenty:empyreal_wired_fence","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","mcwlights:birch_tiki_torch","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","mcwfences:stone_grass_topped_wall","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:pine_plank_window2","mcwbiomesoplenty:empyreal_plank_four_window","biomesoplenty:pine_boat","mcwbiomesoplenty:palm_plank_window2","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","travelersbackpack:cake","mcwbiomesoplenty:jacaranda_wired_fence","additionallanterns:normal_nether_bricks_lantern","mcwbiomesoplenty:jacaranda_horse_fence","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","mcwwindows:granite_pane_window","mcwbiomesoplenty:stripped_dead_pane_window","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","mcwwindows:dark_prismarine_window2","mcwroofs:red_concrete_roof","mcwwindows:quartz_window","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","mcwroofs:oak_roof","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","mcwwindows:granite_four_window","mcwtrpdoors:oak_barred_trapdoor","mcwlights:wall_lantern","mcwroofs:light_blue_concrete_lower_roof","mcwfences:nether_brick_pillar_wall","mcwroofs:light_blue_concrete_steep_roof","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwbiomesoplenty:willow_curved_gate","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwbiomesoplenty:willow_plank_window","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:blue_concrete_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","mcwfurnitures:stripped_oak_double_drawer_counter","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwbiomesoplenty:maple_curved_gate","mcwroofs:orange_concrete_roof","mcwroofs:lime_concrete_upper_steep_roof","mcwbiomesoplenty:fir_window","mcwlights:blue_paper_lamp","simplylight:illuminant_yellow_block_toggle","mcwbiomesoplenty:hellbark_plank_window2","mcwwindows:stripped_cherry_log_window2","mcwbiomesoplenty:pine_pane_window","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwroofs:magenta_concrete_roof","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwroofs:green_concrete_roof","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwwindows:sandstone_window2","mcwbiomesoplenty:willow_hedge","minecraft:white_concrete_powder","mcwwindows:acacia_window","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwwindows:spruce_window2","mcwwindows:andesite_four_window","minecraft:cherry_boat","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","mcwfurnitures:oak_double_drawer_counter","mcwbiomesoplenty:redwood_pyramid_gate","mcwbiomesoplenty:empyreal_highley_gate","mcwwindows:birch_plank_pane_window","mcwbridges:oak_bridge_pier","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","mcwfences:jungle_stockade_fence","mcwlights:tavern_wall_lantern","supplementaries:pancake_fd","mcwwindows:dark_oak_pane_window","mcwlights:pink_paper_lamp","mcwfences:end_brick_railing_gate","mcwwindows:cherry_window","additionallanterns:iron_lantern","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","simplylight:illuminant_light_gray_block_toggle","mcwbiomesoplenty:palm_window2","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_oak_lower_triple_drawer","utilitarian:utility/oak_logs_to_boats","undergarden:wigglewood_boat","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_palm_log_window","mcwfurnitures:oak_drawer_counter","mcwfences:deepslate_brick_pillar_wall","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfurnitures:oak_glass_table","mcwfences:birch_highley_gate","cfm:oak_table","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwdoors:oak_cottage_door","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","farmersdelight:fried_egg_from_smoking","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","dyenamics:ultramarine_concrete_powder","minecraft:magenta_concrete_powder","mcwfurnitures:oak_kitchen_cabinet","mcwbiomesoplenty:pine_window2","undergarden:gloom_o_lantern","mcwwindows:birch_window","mcwwindows:blackstone_four_window","mcwlights:dark_oak_tiki_torch","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","utilitarian:utility/oak_logs_to_trapdoors","domum_ornamentum:sand_bricks","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","dyenamics:peach_concrete_powder","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","simplylight:illuminant_purple_block_toggle","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwwindows:crimson_plank_pane_window","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwroofs:blue_concrete_top_roof","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwroofs:oak_upper_lower_roof","mcwroofs:green_concrete_steep_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","mcwfurnitures:oak_wardrobe","mcwdoors:oak_waffle_door","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","biomesoplenty:dead_boat","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","mcwbiomesoplenty:fir_plank_window","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","mcwlights:soul_cherry_tiki_torch","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwwindows:stripped_acacia_log_window","mcwwindows:stripped_dark_oak_log_window2","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","simplylight:illuminant_lime_block_dyed","additionallanterns:basalt_lantern","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","simplylight:illuminant_brown_block_toggle","mcwwindows:dark_oak_window","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","mcwbiomesoplenty:empyreal_window2","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","mcwwindows:hammer","cfm:stripped_oak_park_bench","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwwindows:stone_pane_window","mcwroofs:gray_concrete_roof","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwwindows:warped_stem_four_window","mcwroofs:yellow_concrete_lower_roof","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","mcwroofs:magenta_concrete_lower_roof","mcwbiomesoplenty:red_maple_hedge","simplylight:illuminant_blue_block_on_dyed","mcwbiomesoplenty:stripped_hellbark_log_window","blue_skies:cake_compat","mcwwindows:spruce_plank_window","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","mcwfurnitures:oak_double_wardrobe","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","mcwwindows:stripped_dark_oak_pane_window","twilightforest:mining_boat","dyenamics:lavender_concrete_powder","cfm:oak_crate","mcwroofs:white_concrete_roof","mcwfurnitures:stripped_oak_modern_chair","mcwwindows:window_half_bar_base","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","mcwfences:dark_oak_horse_fence","mcwwindows:prismarine_four_window","mcwfurnitures:oak_modern_chair","mcwroofs:light_blue_concrete_top_roof","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwbiomesoplenty:hellbark_window2","mcwroofs:black_concrete_upper_lower_roof","minecraft:purple_concrete_powder","mcwroofs:gray_concrete_upper_lower_roof","mcwbiomesoplenty:magic_hedge","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwfences:spruce_curved_gate","mcwbiomesoplenty:hellbark_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","cfm:stripped_oak_chair","mcwwindows:stripped_cherry_pane_window","mcwroofs:lime_concrete_steep_roof","mcwbiomesoplenty:magic_plank_pane_window","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwwindows:warped_planks_four_window","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","mcwwindows:oak_plank_window2","croptopia:meringue","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:pine_plank_window","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","mcwbiomesoplenty:magic_horse_fence","mcwwindows:crimson_planks_four_window","mcwbiomesoplenty:willow_plank_window2","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:diorite_window","mcwwindows:stripped_mangrove_log_four_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwroofs:lime_concrete_roof","allthecompressed:compress/oak_log_1x","simplylight:illuminant_yellow_block_on_dyed","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwwindows:spruce_pane_window","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","aquaculture:cooked_fish_fillet_from_smoking","mcwbiomesoplenty:mahogany_plank_window2","mcwwindows:sandstone_pane_window","cfm:stripped_oak_desk","mcwfurnitures:oak_modern_wardrobe","mcwtrpdoors:oak_glass_trapdoor","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","mcwbiomesoplenty:willow_four_window","mcwfurnitures:stripped_oak_double_drawer","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","mcwfences:mangrove_picket_fence","farmersdelight:fried_egg_from_campfire_cooking","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwlights:jungle_tiki_torch","mcwroofs:oak_lower_roof","mcwwindows:jungle_plank_four_window","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfences:crimson_curved_gate","mcwfurnitures:cabinet_door","additionallanterns:red_sandstone_lantern","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","allthecompressed:compress/sand_1x","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","aquaculture:jellyfish_to_slimeball","mcwwindows:stripped_dark_oak_log_window","mcwroofs:lime_concrete_attic_roof","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwfurnitures:oak_counter","mcwbiomesoplenty:fir_horse_fence","mcwroofs:green_concrete_attic_roof","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","mcwwindows:diorite_four_window","additionallanterns:dark_prismarine_lantern","additionallanterns:bricks_lantern","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:light_gray_concrete_top_roof","mcwwindows:mangrove_plank_window2","mcwlights:covered_wall_lantern","mcwwindows:mangrove_pane_window","additionallanterns:smooth_stone_lantern","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:oak_top_roof","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","additionallanterns:amethyst_lantern","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stone_window","mcwwindows:stripped_warped_stem_window","mcwwindows:stripped_birch_log_four_window","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:redwood_plank_pane_window","mcwbiomesoplenty:stripped_fir_log_window","mcwbiomesoplenty:rainbow_birch_hedge","mcwbiomesoplenty:stripped_jacaranda_pane_window","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","mcwbiomesoplenty:stripped_umbran_log_window","mcwfurnitures:stripped_oak_modern_wardrobe","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","dyenamics:mint_concrete_powder","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","mcwfurnitures:stripped_oak_bookshelf","mcwfences:crimson_picket_fence","mcwfences:blackstone_pillar_wall","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwroofs:brown_concrete_roof","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:red_concrete_upper_steep_roof","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_purple_block_on_toggle","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","farmersdelight:cooking/fried_rice","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:stripped_umbran_pane_window","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:pine_wired_fence","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","mcwwindows:spruce_plank_four_window","mcwfences:end_brick_grass_topped_wall","mcwbiomesoplenty:jacaranda_pane_window","mcwdoors:oak_japanese2_door","corail_woodcutter:acacia_woodcutter","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","utilitarian:utility/oak_logs_to_slabs","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwlights:striped_wall_lantern","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwwindows:window_base","mcwfurnitures:stripped_oak_cupboard_counter","additionallanterns:obsidian_lantern","dyenamics:cherenkov_concrete_powder","mcwwindows:mangrove_window","mcwbiomesoplenty:dead_four_window","mcwwindows:stripped_warped_stem_four_window","mcwwindows:quartz_pane_window","mcwwindows:dark_oak_curtain_rod","cfm:oak_desk","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwwindows:birch_pane_window","mcwroofs:gray_concrete_steep_roof","mcwbiomesoplenty:willow_pyramid_gate","mcwwindows:oak_pane_window","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","mcwlights:purple_paper_lamp","mcwwindows:stripped_cherry_log_window","mcwroofs:blue_concrete_upper_lower_roof","biomesoplenty:willow_boat","mcwfences:modern_red_sandstone_wall","aquaculture:iron_fishing_rod","mcwfences:deepslate_grass_topped_wall","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","twilightforest:time_boat","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","mcwwindows:crimson_planks_window2","mcwbiomesoplenty:stripped_mahogany_log_window","mcwwindows:deepslate_four_window","mcwroofs:brown_concrete_steep_roof","mcwfurnitures:oak_covered_desk","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwwindows:stripped_jungle_log_window","mcwfurnitures:stripped_oak_large_drawer","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","mcwbiomesoplenty:magic_wired_fence","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","mcwwindows:mangrove_plank_pane_window","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","mcwwindows:stripped_acacia_log_four_window","mcwbiomesoplenty:hellbark_pane_window","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","mcwroofs:brown_concrete_top_roof","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwwindows:spruce_curtain_rod","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","mcwroofs:blue_concrete_upper_steep_roof","cfm:oak_cabinet","mcwbiomesoplenty:stripped_willow_log_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwroofs:lime_concrete_upper_lower_roof","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwfences:diorite_grass_topped_wall","mcwbiomesoplenty:stripped_umbran_log_window2","mcwroofs:white_concrete_top_roof","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","mcwwindows:stone_window2","mcwwindows:metal_four_window","mcwroofs:light_gray_concrete_upper_steep_roof","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","aquaculture:redstone_hook","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:flowering_oak_hedge","mcwbiomesoplenty:stripped_pine_pane_window","mcwwindows:stripped_mangrove_log_window2","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","simplylight:illuminant_yellow_block_on_toggle","mcwbiomesoplenty:redwood_pane_window","mcwwindows:stripped_cherry_log_four_window","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","additionallanterns:diamond_lantern","twigs:bone_meal_from_seashells","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","aquaculture:bobber","mcwwindows:quartz_window2","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","mcwwindows:quartz_four_window","simplylight:illuminant_brown_block_dyed","biomesoplenty:palm_boat","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","mcwdoors:oak_swamp_door","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwbiomesoplenty:redwood_hedge","mcwwindows:cherry_window2","ae2wtlib:quantum_bridge_card","supplementaries:sconce","dyenamics:icy_blue_concrete_powder","mcwwindows:stripped_oak_pane_window","mcwtrpdoors:oak_bark_trapdoor","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwroofs:light_gray_concrete_steep_roof","mcwbiomesoplenty:palm_wired_fence","mcwfurnitures:cabinet_drawer","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwwindows:dark_oak_plank_pane_window","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","mcwwindows:stripped_spruce_log_four_window","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwbiomesoplenty:cypress_hedge","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwbiomesoplenty:stripped_jacaranda_log_window2","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","mcwwindows:sandstone_window","mcwfurnitures:oak_coffee_table","aquaculture:oak_fish_mount","undergarden:smogstem_boat","mcwbiomesoplenty:redwood_wired_fence","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","mcwfences:red_sandstone_grass_topped_wall","mcwlights:light_blue_paper_lamp","forbidden_arcanus:deorum_lantern","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwwindows:acacia_plank_four_window","mcwwindows:spruce_plank_window2","mcwwindows:spruce_window","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","mcwbiomesoplenty:stripped_palm_log_four_window","mcwwindows:acacia_four_window","mcwtrpdoors:oak_cottage_trapdoor","twigs:lamp","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwroofs:brown_concrete_lower_roof","mcwbiomesoplenty:hellbark_window","simplylight:illuminant_cyan_block_dyed","mcwbiomesoplenty:stripped_maple_log_window","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","mcwbiomesoplenty:mahogany_plank_four_window","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","mcwfences:sandstone_grass_topped_wall","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","mcwwindows:dark_prismarine_four_window","farmersdelight:fried_egg","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","simplylight:illuminant_lime_block_on_dyed","mcwroofs:purple_concrete_top_roof","simplylight:illuminant_orange_block_on_dyed","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwbiomesoplenty:willow_pane_window","mcwfurnitures:oak_bookshelf_drawer","mcwtrpdoors:oak_barrel_trapdoor","cfm:oak_park_bench","mcwroofs:white_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_picket_fence","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","mcwfences:mangrove_hedge","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","mcwroofs:white_concrete_attic_roof","mcwbiomesoplenty:redwood_plank_window","mcwroofs:blue_concrete_lower_roof","utilitarian:snad/snad","mcwbiomesoplenty:redwood_window2","mcwbiomesoplenty:empyreal_plank_window2","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:magic_window2","mcwlights:cross_lantern","mcwbiomesoplenty:stripped_magic_pane_window","mcwwindows:spruce_plank_pane_window","minecraft:oak_boat","mcwfurnitures:stripped_oak_striped_chair","mcwfences:dark_oak_highley_gate","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","mcwbiomesoplenty:palm_highley_gate","mcwbiomesoplenty:redwood_plank_four_window","mcwfurnitures:stripped_oak_kitchen_cabinet","simplylight:illuminant_red_block_on_toggle","mcwtrpdoors:oak_mystic_trapdoor","additionallanterns:normal_sandstone_lantern","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","mcwdoors:print_whispering","mcwroofs:lime_concrete_lower_roof","dyenamics:conifer_concrete_powder","mcwfences:mesh_metal_fence","mcwroofs:oak_upper_steep_roof","mcwbiomesoplenty:fir_four_window","mcwwindows:birch_plank_window2","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:magic_plank_window2","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwbiomesoplenty:palm_horse_fence","mcwwindows:spruce_four_window","mcwroofs:green_concrete_lower_roof","twilightforest:transformation_boat","botania:speed_up_belt","mcwlights:warped_tiki_torch","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","simplylight:edge_light","mcwlights:bamboo_tiki_torch","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","mcwwindows:prismarine_pane_window","croptopia:egg_roll","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","mcwtrpdoors:oak_whispering_trapdoor","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","supplementaries:slice_map","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:yellow_concrete_steep_roof","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:stone_pillar_wall","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwwindows:stripped_birch_pane_window","mcwfences:spruce_stockade_fence","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwlights:mangrove_tiki_torch","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","simplylight:illuminant_block","mcwbiomesoplenty:stripped_redwood_log_window","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwlights:green_paper_lamp","mcwtrpdoors:oak_barn_trapdoor","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","mcwbiomesoplenty:stripped_redwood_pane_window","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwwindows:acacia_window2","mcwbiomesoplenty:hellbark_highley_gate","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","simplylight:illuminant_black_block_on_dyed","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:birch_boat","minecraft:spruce_boat","mcwfences:spruce_highley_gate","mcwbiomesoplenty:maple_plank_pane_window","mcwlights:brown_paper_lamp","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","mcwroofs:black_concrete_roof","mcwdoors:oak_stable_door","mcwbiomesoplenty:maple_highley_gate","mcwfences:deepslate_brick_railing_gate","mcwbiomesoplenty:magic_plank_four_window","cfm:oak_upgraded_gate","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","mcwlights:bell_wall_lantern","dyenamics:fluorescent_concrete_powder","mcwbiomesoplenty:stripped_umbran_log_four_window","mcwfences:modern_blackstone_wall","cfm:oak_desk_cabinet"],toBeDisplayed:["corail_woodcutter:birch_woodcutter","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","mcwfences:granite_railing_gate","mcwroofs:black_concrete_attic_roof","mcwroofs:magenta_concrete_top_roof","minecraft:charcoal","additionallanterns:copper_lantern","mcwfurnitures:stripped_oak_double_wardrobe","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwfences:railing_deepslate_brick_wall","mcwbiomesoplenty:stripped_palm_pane_window","mcwbiomesoplenty:jacaranda_window2","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:hellbark_plank_window","mcwbiomesoplenty:magic_pane_window","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","mcwfences:dark_oak_stockade_fence","mcwbiomesoplenty:empyreal_hedge","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","corail_woodcutter:crimson_woodcutter","mcwfurnitures:oak_end_table","mcwwindows:oak_plank_pane_window","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","mcwfences:acacia_wired_fence","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","mcwlights:soul_mangrove_tiki_torch","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwroofs:purple_concrete_attic_roof","energymeter:meter","mcwlights:covered_lantern","mcwlights:black_paper_lamp","simplylight:rodlamp","mcwroofs:purple_concrete_upper_lower_roof","mcwwindows:stripped_acacia_log_window2","mcwwindows:dark_prismarine_pane_window","mcwwindows:stripped_oak_log_four_window","mcwwindows:stripped_birch_log_window2","mcwfurnitures:oak_modern_desk","aquaculture:iron_nugget_from_smelting","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:maple_plank_window","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","twilightforest:canopy_boat","mcwfences:warped_horse_fence","mcwbiomesoplenty:mahogany_curved_gate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","dyenamics:aquamarine_concrete_powder","mcwbiomesoplenty:maple_four_window","mcwwindows:acacia_curtain_rod","mcwlights:striped_lantern","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwlights:chain_lantern","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwbiomesoplenty:jacaranda_four_window","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:dark_oak_plank_window","mcwwindows:granite_window2","cfm:oak_kitchen_counter","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","biomesoplenty:mahogany_boat","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwwindows:diorite_pane_window","solcarrot:food_book","additionallanterns:stone_bricks_lantern","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","additionallanterns:stone_lantern","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","mcwroofs:magenta_concrete_upper_lower_roof","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","mcwbiomesoplenty:dead_plank_window","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwroofs:cyan_concrete_steep_roof","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","mcwfurnitures:oak_desk","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwwindows:birch_plank_window","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwbiomesoplenty:stripped_dead_log_window2","aquaculture:tin_can_to_iron_nugget","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","dyenamics:spring_green_concrete_powder","mcwroofs:red_concrete_steep_roof","minecraft:glass","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","mcwbiomesoplenty:hellbark_plank_pane_window","mcwlights:tavern_lantern","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","mcwroofs:brown_concrete_attic_roof","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:festive_wall_lantern","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","minecraft:light_blue_concrete_powder","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwbiomesoplenty:umbran_plank_four_window","mcwfences:deepslate_pillar_wall","mcwbiomesoplenty:empyreal_wired_fence","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","mcwlights:birch_tiki_torch","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","mcwfences:stone_grass_topped_wall","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:pine_plank_window2","mcwbiomesoplenty:empyreal_plank_four_window","biomesoplenty:pine_boat","mcwbiomesoplenty:palm_plank_window2","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","travelersbackpack:cake","mcwbiomesoplenty:jacaranda_wired_fence","additionallanterns:normal_nether_bricks_lantern","mcwbiomesoplenty:jacaranda_horse_fence","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","mcwwindows:granite_pane_window","mcwbiomesoplenty:stripped_dead_pane_window","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","mcwwindows:dark_prismarine_window2","mcwroofs:red_concrete_roof","mcwwindows:quartz_window","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","mcwroofs:oak_roof","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","mcwwindows:granite_four_window","mcwtrpdoors:oak_barred_trapdoor","mcwlights:wall_lantern","mcwroofs:light_blue_concrete_lower_roof","mcwfences:nether_brick_pillar_wall","mcwroofs:light_blue_concrete_steep_roof","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwbiomesoplenty:willow_curved_gate","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwbiomesoplenty:willow_plank_window","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:blue_concrete_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","mcwfurnitures:stripped_oak_double_drawer_counter","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwbiomesoplenty:maple_curved_gate","mcwroofs:orange_concrete_roof","mcwroofs:lime_concrete_upper_steep_roof","mcwbiomesoplenty:fir_window","mcwlights:blue_paper_lamp","simplylight:illuminant_yellow_block_toggle","mcwbiomesoplenty:hellbark_plank_window2","mcwwindows:stripped_cherry_log_window2","mcwbiomesoplenty:pine_pane_window","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwroofs:magenta_concrete_roof","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwroofs:green_concrete_roof","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwwindows:sandstone_window2","mcwbiomesoplenty:willow_hedge","minecraft:white_concrete_powder","mcwwindows:acacia_window","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwwindows:spruce_window2","mcwwindows:andesite_four_window","minecraft:cherry_boat","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","mcwfurnitures:oak_double_drawer_counter","mcwbiomesoplenty:redwood_pyramid_gate","mcwbiomesoplenty:empyreal_highley_gate","mcwwindows:birch_plank_pane_window","mcwbridges:oak_bridge_pier","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","mcwfences:jungle_stockade_fence","mcwlights:tavern_wall_lantern","supplementaries:pancake_fd","mcwwindows:dark_oak_pane_window","mcwlights:pink_paper_lamp","mcwfences:end_brick_railing_gate","mcwwindows:cherry_window","additionallanterns:iron_lantern","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","simplylight:illuminant_light_gray_block_toggle","mcwbiomesoplenty:palm_window2","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_oak_lower_triple_drawer","utilitarian:utility/oak_logs_to_boats","undergarden:wigglewood_boat","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_palm_log_window","mcwfurnitures:oak_drawer_counter","mcwfences:deepslate_brick_pillar_wall","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfurnitures:oak_glass_table","mcwfences:birch_highley_gate","cfm:oak_table","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwdoors:oak_cottage_door","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","farmersdelight:fried_egg_from_smoking","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","dyenamics:ultramarine_concrete_powder","minecraft:magenta_concrete_powder","mcwfurnitures:oak_kitchen_cabinet","mcwbiomesoplenty:pine_window2","undergarden:gloom_o_lantern","mcwwindows:birch_window","mcwwindows:blackstone_four_window","mcwlights:dark_oak_tiki_torch","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","utilitarian:utility/oak_logs_to_trapdoors","domum_ornamentum:sand_bricks","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","dyenamics:peach_concrete_powder","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","simplylight:illuminant_purple_block_toggle","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwwindows:crimson_plank_pane_window","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwroofs:blue_concrete_top_roof","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","mcwroofs:oak_upper_lower_roof","mcwroofs:green_concrete_steep_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","mcwfurnitures:oak_wardrobe","mcwdoors:oak_waffle_door","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","biomesoplenty:dead_boat","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","mcwbiomesoplenty:fir_plank_window","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","mcwlights:soul_cherry_tiki_torch","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwwindows:stripped_acacia_log_window","mcwwindows:stripped_dark_oak_log_window2","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","simplylight:illuminant_lime_block_dyed","additionallanterns:basalt_lantern","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","simplylight:illuminant_brown_block_toggle","mcwwindows:dark_oak_window","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","mcwbiomesoplenty:empyreal_window2","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","mcwwindows:hammer","cfm:stripped_oak_park_bench","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwwindows:stone_pane_window","mcwroofs:gray_concrete_roof","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwwindows:warped_stem_four_window","mcwroofs:yellow_concrete_lower_roof","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","mcwroofs:magenta_concrete_lower_roof","mcwbiomesoplenty:red_maple_hedge","simplylight:illuminant_blue_block_on_dyed","mcwbiomesoplenty:stripped_hellbark_log_window","blue_skies:cake_compat","mcwwindows:spruce_plank_window","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","mcwfurnitures:oak_double_wardrobe","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","mcwwindows:stripped_dark_oak_pane_window","twilightforest:mining_boat","dyenamics:lavender_concrete_powder","cfm:oak_crate","mcwroofs:white_concrete_roof","mcwfurnitures:stripped_oak_modern_chair","mcwwindows:window_half_bar_base","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","mcwfences:dark_oak_horse_fence","mcwwindows:prismarine_four_window","mcwfurnitures:oak_modern_chair","mcwroofs:light_blue_concrete_top_roof","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwbiomesoplenty:hellbark_window2","mcwroofs:black_concrete_upper_lower_roof","minecraft:purple_concrete_powder","mcwroofs:gray_concrete_upper_lower_roof","mcwbiomesoplenty:magic_hedge","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwfences:spruce_curved_gate","mcwbiomesoplenty:hellbark_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","cfm:stripped_oak_chair","mcwwindows:stripped_cherry_pane_window","mcwroofs:lime_concrete_steep_roof","mcwbiomesoplenty:magic_plank_pane_window","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwwindows:warped_planks_four_window","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","mcwwindows:oak_plank_window2","croptopia:meringue","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:pine_plank_window","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","mcwbiomesoplenty:magic_horse_fence","mcwwindows:crimson_planks_four_window","mcwbiomesoplenty:willow_plank_window2","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:diorite_window","mcwwindows:stripped_mangrove_log_four_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwroofs:lime_concrete_roof","allthecompressed:compress/oak_log_1x","simplylight:illuminant_yellow_block_on_dyed","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwwindows:spruce_pane_window","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","aquaculture:cooked_fish_fillet_from_smoking","mcwbiomesoplenty:mahogany_plank_window2","mcwwindows:sandstone_pane_window","cfm:stripped_oak_desk","mcwfurnitures:oak_modern_wardrobe","mcwtrpdoors:oak_glass_trapdoor","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","mcwbiomesoplenty:willow_four_window","mcwfurnitures:stripped_oak_double_drawer","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","mcwfences:mangrove_picket_fence","farmersdelight:fried_egg_from_campfire_cooking","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwlights:jungle_tiki_torch","mcwroofs:oak_lower_roof","mcwwindows:jungle_plank_four_window","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfences:crimson_curved_gate","mcwfurnitures:cabinet_door","additionallanterns:red_sandstone_lantern","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","allthecompressed:compress/sand_1x","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","aquaculture:jellyfish_to_slimeball","mcwwindows:stripped_dark_oak_log_window","mcwroofs:lime_concrete_attic_roof","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwfurnitures:oak_counter","mcwbiomesoplenty:fir_horse_fence","mcwroofs:green_concrete_attic_roof","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","mcwwindows:diorite_four_window","additionallanterns:dark_prismarine_lantern","additionallanterns:bricks_lantern","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:light_gray_concrete_top_roof","mcwwindows:mangrove_plank_window2","mcwlights:covered_wall_lantern","mcwwindows:mangrove_pane_window","additionallanterns:smooth_stone_lantern","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:oak_top_roof","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","additionallanterns:amethyst_lantern","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stone_window","mcwwindows:stripped_warped_stem_window","mcwwindows:stripped_birch_log_four_window","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:redwood_plank_pane_window","mcwbiomesoplenty:stripped_fir_log_window","mcwbiomesoplenty:rainbow_birch_hedge","mcwbiomesoplenty:stripped_jacaranda_pane_window","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","mcwbiomesoplenty:stripped_umbran_log_window","mcwfurnitures:stripped_oak_modern_wardrobe","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","dyenamics:mint_concrete_powder","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","mcwfurnitures:stripped_oak_bookshelf","mcwfences:crimson_picket_fence","mcwfences:blackstone_pillar_wall","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwroofs:brown_concrete_roof","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:red_concrete_upper_steep_roof","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_purple_block_on_toggle","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","farmersdelight:cooking/fried_rice","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:stripped_umbran_pane_window","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:pine_wired_fence","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","mcwwindows:spruce_plank_four_window","mcwfences:end_brick_grass_topped_wall","mcwbiomesoplenty:jacaranda_pane_window","mcwdoors:oak_japanese2_door","corail_woodcutter:acacia_woodcutter","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","utilitarian:utility/oak_logs_to_slabs","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwlights:striped_wall_lantern","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwwindows:window_base","mcwfurnitures:stripped_oak_cupboard_counter","additionallanterns:obsidian_lantern","dyenamics:cherenkov_concrete_powder","mcwwindows:mangrove_window","mcwbiomesoplenty:dead_four_window","mcwwindows:stripped_warped_stem_four_window","mcwwindows:quartz_pane_window","mcwwindows:dark_oak_curtain_rod","cfm:oak_desk","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","mcwwindows:birch_pane_window","mcwroofs:gray_concrete_steep_roof","mcwbiomesoplenty:willow_pyramid_gate","mcwwindows:oak_pane_window","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","mcwlights:purple_paper_lamp","mcwwindows:stripped_cherry_log_window","mcwroofs:blue_concrete_upper_lower_roof","biomesoplenty:willow_boat","mcwfences:modern_red_sandstone_wall","aquaculture:iron_fishing_rod","mcwfences:deepslate_grass_topped_wall","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","twilightforest:time_boat","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","mcwwindows:crimson_planks_window2","mcwbiomesoplenty:stripped_mahogany_log_window","mcwwindows:deepslate_four_window","mcwroofs:brown_concrete_steep_roof","mcwfurnitures:oak_covered_desk","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwwindows:stripped_jungle_log_window","mcwfurnitures:stripped_oak_large_drawer","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","mcwbiomesoplenty:magic_wired_fence","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","mcwwindows:mangrove_plank_pane_window","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","mcwwindows:stripped_acacia_log_four_window","mcwbiomesoplenty:hellbark_pane_window","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","mcwroofs:brown_concrete_top_roof","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwwindows:spruce_curtain_rod","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","mcwroofs:blue_concrete_upper_steep_roof","cfm:oak_cabinet","mcwbiomesoplenty:stripped_willow_log_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwroofs:lime_concrete_upper_lower_roof","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwfences:diorite_grass_topped_wall","mcwbiomesoplenty:stripped_umbran_log_window2","mcwroofs:white_concrete_top_roof","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","mcwwindows:stone_window2","mcwwindows:metal_four_window","mcwroofs:light_gray_concrete_upper_steep_roof","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","aquaculture:redstone_hook","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:flowering_oak_hedge","mcwbiomesoplenty:stripped_pine_pane_window","mcwwindows:stripped_mangrove_log_window2","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","simplylight:illuminant_yellow_block_on_toggle","mcwbiomesoplenty:redwood_pane_window","mcwwindows:stripped_cherry_log_four_window","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","additionallanterns:diamond_lantern","twigs:bone_meal_from_seashells","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","aquaculture:bobber","mcwwindows:quartz_window2","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","mcwwindows:quartz_four_window","simplylight:illuminant_brown_block_dyed","biomesoplenty:palm_boat","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","mcwdoors:oak_swamp_door","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwbiomesoplenty:redwood_hedge","mcwwindows:cherry_window2","ae2wtlib:quantum_bridge_card","supplementaries:sconce","dyenamics:icy_blue_concrete_powder","mcwwindows:stripped_oak_pane_window","mcwtrpdoors:oak_bark_trapdoor","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwroofs:light_gray_concrete_steep_roof","mcwbiomesoplenty:palm_wired_fence","mcwfurnitures:cabinet_drawer","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwwindows:dark_oak_plank_pane_window","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","mcwwindows:stripped_spruce_log_four_window","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwbiomesoplenty:cypress_hedge","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwbiomesoplenty:stripped_jacaranda_log_window2","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","mcwwindows:sandstone_window","mcwfurnitures:oak_coffee_table","aquaculture:oak_fish_mount","undergarden:smogstem_boat","mcwbiomesoplenty:redwood_wired_fence","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","mcwfences:red_sandstone_grass_topped_wall","mcwlights:light_blue_paper_lamp","forbidden_arcanus:deorum_lantern","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwwindows:acacia_plank_four_window","mcwwindows:spruce_plank_window2","mcwwindows:spruce_window","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","mcwbiomesoplenty:stripped_palm_log_four_window","mcwwindows:acacia_four_window","mcwtrpdoors:oak_cottage_trapdoor","twigs:lamp","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwroofs:brown_concrete_lower_roof","mcwbiomesoplenty:hellbark_window","simplylight:illuminant_cyan_block_dyed","mcwbiomesoplenty:stripped_maple_log_window","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","mcwbiomesoplenty:mahogany_plank_four_window","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","mcwfences:sandstone_grass_topped_wall","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","mcwwindows:dark_prismarine_four_window","farmersdelight:fried_egg","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","simplylight:illuminant_lime_block_on_dyed","mcwroofs:purple_concrete_top_roof","simplylight:illuminant_orange_block_on_dyed","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwbiomesoplenty:willow_pane_window","mcwfurnitures:oak_bookshelf_drawer","mcwtrpdoors:oak_barrel_trapdoor","cfm:oak_park_bench","mcwroofs:white_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_picket_fence","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","mcwfences:mangrove_hedge","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","mcwroofs:white_concrete_attic_roof","mcwbiomesoplenty:redwood_plank_window","mcwroofs:blue_concrete_lower_roof","utilitarian:snad/snad","mcwbiomesoplenty:redwood_window2","mcwbiomesoplenty:empyreal_plank_window2","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:magic_window2","mcwlights:cross_lantern","mcwbiomesoplenty:stripped_magic_pane_window","mcwwindows:spruce_plank_pane_window","minecraft:oak_boat","mcwfurnitures:stripped_oak_striped_chair","mcwfences:dark_oak_highley_gate","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","mcwbiomesoplenty:palm_highley_gate","mcwbiomesoplenty:redwood_plank_four_window","mcwfurnitures:stripped_oak_kitchen_cabinet","simplylight:illuminant_red_block_on_toggle","mcwtrpdoors:oak_mystic_trapdoor","additionallanterns:normal_sandstone_lantern","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","mcwdoors:print_whispering","mcwroofs:lime_concrete_lower_roof","dyenamics:conifer_concrete_powder","mcwfences:mesh_metal_fence","mcwroofs:oak_upper_steep_roof","mcwbiomesoplenty:fir_four_window","mcwwindows:birch_plank_window2","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:magic_plank_window2","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwbiomesoplenty:palm_horse_fence","mcwwindows:spruce_four_window","mcwroofs:green_concrete_lower_roof","twilightforest:transformation_boat","botania:speed_up_belt","mcwlights:warped_tiki_torch","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","simplylight:edge_light","mcwlights:bamboo_tiki_torch","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","mcwwindows:prismarine_pane_window","croptopia:egg_roll","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","mcwtrpdoors:oak_whispering_trapdoor","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","supplementaries:slice_map","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:yellow_concrete_steep_roof","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:stone_pillar_wall","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwwindows:stripped_birch_pane_window","mcwfences:spruce_stockade_fence","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwlights:mangrove_tiki_torch","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","simplylight:illuminant_block","mcwbiomesoplenty:stripped_redwood_log_window","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwlights:green_paper_lamp","mcwtrpdoors:oak_barn_trapdoor","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","mcwbiomesoplenty:stripped_redwood_pane_window","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwwindows:acacia_window2","mcwbiomesoplenty:hellbark_highley_gate","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","simplylight:illuminant_black_block_on_dyed","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:birch_boat","minecraft:spruce_boat","mcwfences:spruce_highley_gate","mcwbiomesoplenty:maple_plank_pane_window","mcwlights:brown_paper_lamp","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","mcwroofs:black_concrete_roof","mcwdoors:oak_stable_door","mcwbiomesoplenty:maple_highley_gate","mcwfences:deepslate_brick_railing_gate","mcwbiomesoplenty:magic_plank_four_window","cfm:oak_upgraded_gate","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","mcwlights:bell_wall_lantern","dyenamics:fluorescent_concrete_powder","mcwbiomesoplenty:stripped_umbran_log_four_window","mcwfences:modern_blackstone_wall","cfm:oak_desk_cabinet"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:3110,warning_level:0}}