{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:385,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:100,Id:241,ShowIcon:1b,ShowParticles:1b,"forge:id":"mahoutsukai:bleeding"}],Air:300s,Attributes:[{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:gold_backpack",tag:{contentsUuid:[I;-405603898,-402371022,-1551198811,-755448216],inventorySlots:81,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}}]},upgradeSlots:3}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"ae2:wireless_crafting_terminal",tag:{accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},craftingGrid:[{Count:1b,Slot:3,id:"mysticalagriculture:wood_essence"},{Count:1b,Slot:4,id:"mysticalagriculture:wood_essence"},{Count:1b,Slot:5,id:"mysticalagriculture:wood_essence"}],internalCurrentPower:1480850.0d}}],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"ae2wtlib:wireless_pattern_encoding_terminal",tag:{internalCurrentPower:1600000.0d}}],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:28,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:278},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:0b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_LAST_ARROW:[I;-1544541626,1042107800,-1312689262,2118939251],MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:10,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{},"rftoolsutility:properties":{allowFlying:0b,buffTicks:83,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["minecraft:baked_potato","allthemodium:allthemodium_carrot","minecraft:bread","minecraft:cooked_beef"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:-10s,knowledge:6,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:-6322192146365L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-1101971672,-*********,-2055536216,1953084018]},{FromDim:"minecraft:the_nether",FromPos:824633704509L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-*********,1220167097,-2137937586,1127182546]},{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-1960633629,-*********,-2097850597,1329766536]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;255950004,2019182445,-1688876346,-439704507],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","70e61f07-a350-437a-ae01-15e3ef9a1bab","d1811b83-cc8c-4821-80c6-3e5ce576ae62","42d85d81-dfae-42b5-ab86-9ebce283d4c6","6c8881a8-dbdf-465b-bb3e-0c97f47d45a4","3c0ccabf-fdf5-42f9-8d79-************","db9fa2f7-bedb-4c7b-91c7-5ef71c247526"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_end_conquered_effects:[{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:353,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-19,tb_last_ground_location_y:253,tb_last_ground_location_z:-18,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},apoth_reforge_seed:-487502010,"quark:locked_once":1b,sophisticatedStorageSettings:{}},Health:1.3931141f,HurtByTimestamp:268,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"mysticalagriculture:inferium_pickaxe",tag:{Damage:374,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":0.65022194f,"apotheosis:breaker/attribute/experienced":0.12915665f,"apotheosis:breaker/attribute/lengthy":0.6935076f,"apotheosis:breaker/attribute/lucky":0.24180782f,"apotheosis:breaker/mob_effect/swift":0.07110709f,"apotheosis:breaker/special/enlightened":0.37519318f,"apotheosis:durable":0.28f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/attribute/lengthy"},"",{"translate":"affix.apotheosis:breaker/mob_effect/swift.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;954793996,**********,-**********,664176785]]}}},{Count:64b,Slot:1b,id:"minecraft:ender_eye"},{Count:1b,Slot:2b,id:"minecraft:diamond_pickaxe",tag:{Damage:383}},{Count:63b,Slot:3b,id:"allthemodium:allthemodium_carrot"},{Count:1b,Slot:5b,id:"immersiveengineering:hammer",tag:{Damage:0}},{Count:56b,Slot:6b,id:"allthemodium:allthemodium_carrot"},{Count:1b,Slot:7b,id:"minecraft:beehive"},{Count:33b,Slot:8b,id:"immersiveengineering:cokebrick"},{Count:1b,Slot:10b,id:"ad_astra:space_suit",tag:{BotariumData:{StoredFluids:[{Amount:134L,Fluid:"ad_astra:oxygen"}]},Damage:76}},{Count:64b,Slot:11b,id:"create:wheat_flour"},{Count:29b,Slot:12b,id:"mysticalagriculture:nether_seeds"},{Count:7b,Slot:13b,id:"minecraft:wheat_seeds"},{Count:6b,Slot:32b,id:"minecraft:acacia_log"},{Count:1b,Slot:100b,id:"ad_astra:space_boots",tag:{Damage:137}},{Count:1b,Slot:101b,id:"ad_astra:space_pants",tag:{Damage:137}},{Count:1b,Slot:102b,id:"allthemodium:allthemodium_chestplate"},{Count:1b,Slot:103b,id:"ad_astra:space_helmet",tag:{Damage:138}}],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:mining",pos:[I;-57,270,-65]},Motion:[-0.0571335499366602d,0.009814342986900787d,-0.08565558514243518d],OnGround:0b,PortalCooldown:0,Pos:[-55.967954601153984d,269.97034235798986d,-63.95232824872011d],Railways_DataVersion:2,Rotation:[22.550467f,15.149984f],Score:290891,SelectedItemSlot:4,SleepTimer:0s,Spigot.ticksLived:278,UUID:[I;-661307686,1094798779,-1904238911,1722469380],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-14018773860099L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:0,XpP:0.42857143f,XpSeed:203453686,XpTotal:3,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752055003504L,keepLevel:0b,lastKnownName:"Epsilon_Llbra",lastPlayed:1752496274895L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:5.51f,foodLevel:20,foodSaturationLevel:3.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["botania:star_sword","botania:dreamwood_planks","mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","croptopia:mead","botania:alfheim_portal","mcwfurnitures:stripped_jungle_desk","mcwfurnitures:stripped_acacia_counter","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwdoors:bamboo_stable_door","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","botania:ender_hand","minecraft:stonecutter","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwbiomesoplenty:willow_waffle_door","mcwroofs:lime_terracotta_lower_roof","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","mcwtrpdoors:acacia_barrel_trapdoor","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mcwfurnitures:stripped_acacia_bookshelf_cupboard","mythicbotany:central_rune_holder","sophisticatedbackpacks:void_upgrade","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","railcraft:bushing_gear_bronze","dyenamics:maroon_candle","mcwroofs:acacia_upper_steep_roof","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","buildinggadgets2:gadget_cut_paste","mcwtrpdoors:acacia_barn_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","mcwfurnitures:spruce_modern_desk","mcwfurnitures:spruce_drawer","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","handcrafted:deepslate_corner_trim","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","create:crafting/kinetics/gearbox","botania:terrasteel_block","mcwbiomesoplenty:umbran_modern_door","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","arseng:portable_source_cell_64k","rftoolsutility:screen_link","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","mcwbiomesoplenty:redwood_nether_door","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","comforts:sleeping_bag_to_blue","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:bubble_blower","naturalist:glow_goop","supplementaries:crank","minecraft:recovery_compass","ad_astra:calorite_panel","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","supplementaries:netherite_trapdoor","productivebees:stonecutter/fir_canvas_expansion_box","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","cfm:acacia_cabinet","mcwbiomesoplenty:willow_bark_glass_door","minecraft:pink_terracotta","aiotbotania:livingrock_axe","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","mcwbiomesoplenty:empyreal_classic_door","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwtrpdoors:spruce_barred_trapdoor","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","dyenamics:aquamarine_concrete_powder","allthecompressed:compress/energized_steel_block_1x","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mekanism:energy_tablet","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","productivebees:hives/advanced_cherry_canvas_hive","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","botania:vial","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:golden_hoe","cfm:oak_kitchen_counter","minecraft:fire_charge","mcwfurnitures:acacia_lower_triple_drawer","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","cfm:stripped_spruce_bedside_cabinet","railcraft:any_detector","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","mcwtrpdoors:bamboo_mystic_trapdoor","minecraft:stone_button","enderio:dark_steel_door","mcwbiomesoplenty:redwood_bark_glass_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","minecraft:sandstone","mcwwindows:birch_plank_window","ae2:network/cells/spatial_storage_cell_2_cubed_storage","mcwbiomesoplenty:stripped_dead_log_window2","pneumaticcraft:logistics_frame_active_provider_self","minecraft:lodestone","mcwbiomesoplenty:umbran_japanese2_door","appbot:portable_mana_storage_cell_4k","minecraft:purpur_block","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwpaths:sandstone_running_bond_stairs","mcwdoors:spruce_four_panel_door","create:crafting/kinetics/speedometer","domum_ornamentum:blue_brick_extra","cfm:stripped_spruce_chair","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:dried_kelp_block","minecraft:quartz","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","productivebees:expansion_boxes/expansion_box_acacia_canvas","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:crafting/kinetics/mechanical_roller","productivetrees:sawdust_to_paper_water_bottle","minecraft:redstone_from_blasting_redstone_ore","create:industrial_iron_block_from_ingots_iron_stonecutting","quark:building/crafting/furnaces/deepslate_furnace","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","cfm:stripped_crimson_mail_box","tombstone:grave_plate","handcrafted:jungle_side_table","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:hellbark_beach_door","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","mcwbiomesoplenty:umbran_classic_door","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","ae2:network/parts/terminals","securitycraft:keypad_frame","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","botania:terrasteel_leggings","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","cfm:spruce_bedside_cabinet","mcwbiomesoplenty:hellbark_bark_glass_door","botania:incense_stick","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","ae2:network/blocks/energy_energy_acceptor","mcwbiomesoplenty:pine_plank_window2","pneumaticcraft:vortex_tube","xnet:connector_routing","mcwwindows:warped_louvered_shutter","blue_skies:glowing_nature_stone","minecraft:blue_carpet","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:palm_plank_window2","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","mcwroofs:green_terracotta_attic_roof","productivebees:stonecutter/palm_canvas_expansion_box","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:palm_bark_glass_door","mcwbiomesoplenty:umbran_cottage_door","utilitix:directional_highspeed_rail","mekanism:robit","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","dyenamics:ultramarine_candle","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","mcwfurnitures:stripped_spruce_double_drawer","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","mcwbiomesoplenty:pine_stable_head_door","create:crafting/kinetics/chute","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","dyenamics:fluorescent_candle","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","cfm:stripped_acacia_park_bench","ae2:network/blocks/storage_chest","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:iron_hoe","aether:crossbow_repairing","minecraft:iron_ingot_from_iron_block","handcrafted:blue_cushion","mcwroofs:light_gray_roof_slab","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwbiomesoplenty:palm_japanese2_door","mcwroofs:thatch_roof","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","dyenamics:lavender_candle","dyenamics:fluorescent_wool","ae2:tools/portable_fluid_cell_256k","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","mcwtrpdoors:bamboo_tropical_trapdoor","sophisticatedbackpacks:filter_upgrade","pneumaticcraft:gun_ammo_freezing","mcwbiomesoplenty:maple_japanese_door","ad_astra:ostrum_plateblock","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","minecraft:iron_ingot_from_nuggets","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","ad_astra:smelting/cheese_from_smelting_moon_cheese_ore","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwfurnitures:acacia_stool_chair","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","productivebees:hives/advanced_oak_canvas_hive","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","minecraft:gray_bed","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","mcwfurnitures:stripped_acacia_modern_chair","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","cfm:stripped_birch_kitchen_drawer","mcwfurnitures:stripped_acacia_lower_triple_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","twigs:chiseled_bricks","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","allthecompressed:decompress/end_stone_5x","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwroofs:spruce_top_roof","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwbiomesoplenty:willow_beach_door","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","croptopia:potato_chips","minecraft:chiseled_stone_bricks_stone_from_stonecutting","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","minecraft:netherite_sword_smithing","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwtrpdoors:acacia_cottage_trapdoor","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwbiomesoplenty:empyreal_beach_door","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/gunpart_barrel","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","mcwbiomesoplenty:mahogany_swamp_door","ad_astra:energizer","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","minecraft:gray_banner","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","mcwfurnitures:stripped_spruce_glass_table","cfm:stripped_acacia_kitchen_counter","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","supplementaries:flags/flag_light_blue","mcwbiomesoplenty:maple_modern_door","ad_astra:smelting/desh_ingot_from_smelting_raw_desh","mcwpaths:cobbled_deepslate_running_bond","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","mcwbiomesoplenty:empyreal_cottage_door","utilitix:tiny_charcoal_to_tiny","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","appflux:smelting/harden_resin","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","botania:terrasteel_boots","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:polished_blackstone_from_blackstone_stonecutting","mcwpaths:brick_running_bond","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:engine_frame","mcwwindows:hammer","ad_astra:iron_panel","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwroofs:gray_attic_roof","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","ae2:tools/portable_item_cell_256k","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwroofs:purple_terracotta_upper_lower_roof","mcwfurnitures:spruce_stool_chair","mcwbiomesoplenty:jacaranda_nether_door","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","mcwfurnitures:acacia_counter","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","blue_skies:cake_compat","utilitarian:utility/acacia_logs_to_doors","cfm:stripped_acacia_chair","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwdoors:spruce_stable_head_door","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","productivebees:stonecutter/aspen_canvas_expansion_box","railcraft:iron_tank_wall","pneumaticcraft:liquid_hopper","botania:green_pavement","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","ad_astra:ti_69","mcwroofs:acacia_attic_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","mcwbiomesoplenty:mahogany_nether_door","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:fir_bark_glass_door","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","botania:petal_brown","create:deepslate_from_stone_types_deepslate_stonecutting","cfm:stripped_oak_chair","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","cfm:rock_path","minecraft:wooden_hoe","mekanismgenerators:generator/heat","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","mcwbiomesoplenty:redwood_waffle_door","botania:slingshot","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","botanypots:botanypots/crafting/terracotta_hopper_botany_pot","productivebees:stonecutter/jungle_canvas_hive","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","mcwbiomesoplenty:willow_barn_door","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_base_green","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","mcwbiomesoplenty:umbran_nether_door","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","minecraft:piston","mcwwindows:crimson_planks_four_window","ad_astra:large_gas_tank","mcwfurnitures:jungle_coffee_table","mcwbiomesoplenty:maple_waffle_door","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","undergarden:regalium_block","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwdoors:bamboo_four_panel_door","mcwtrpdoors:oak_glass_trapdoor","railcraft:villager_detector","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwbiomesoplenty:fir_stable_door","mcwbiomesoplenty:dead_modern_door","mcwroofs:gray_upper_steep_roof","botania:third_eye","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","mcwbiomesoplenty:palm_classic_door","mcwdoors:acacia_cottage_door","croptopia:apple_sapling","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","handcrafted:oak_shelf","handcrafted:stackable_book","mcwbiomesoplenty:magic_modern_door","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","silentgear:sinew_fiber","handcrafted:gray_cushion","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","mcwtrpdoors:spruce_mystic_trapdoor","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","create:crafting/appliances/clipboard","ad_astra:space_helmet","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","ad_astra:desh_cable","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","mcwbiomesoplenty:palm_tropical_door","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","mcwbiomesoplenty:palm_cottage_door","productivebees:stonecutter/oak_canvas_expansion_box","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","utilitarian:utility/spruce_logs_to_pressure_plates","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","enderio:dark_steel_nugget_to_ingot","create:crafting/curiosities/minecart_coupling","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","productivebees:expansion_boxes/expansion_box_crimson_canvas","ad_astra:netherite_space_suit","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","xnet:antenna","mcwfurnitures:jungle_stool_chair","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","dyenamics:persimmon_candle","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","ad_astra:netherite_space_helmet","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","botania:petal_orange_double","mcwbiomesoplenty:umbran_barn_glass_door","pylons:expulsion_pylon","mcwfurnitures:acacia_double_wardrobe","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","mcwroofs:red_terracotta_attic_roof","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","minecraft:torch","ad_astra:gray_flag","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","create:crafting/kinetics/black_seat","cfm:oak_desk","ad_astra:encased_ostrum_block","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwbiomesoplenty:dead_bark_glass_door","mcwbiomesoplenty:fir_paper_door","mcwbiomesoplenty:pine_mystic_door","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","travelersbackpack:gray_sleeping_bag","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:fir_japanese_door","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbridges:dry_bamboo_bridge_pier","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","ad_astra:wrench","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","minecraft:stripped_acacia_wood","mcwbiomesoplenty:mahogany_stockade_fence","botania:brown_petal_block","mcwfurnitures:stripped_acacia_bookshelf_drawer","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","mcwroofs:sandstone_lower_roof","domum_ornamentum:brown_bricks","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","cfm:red_cooler","botania:dreamwood_wall","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","cfm:acacia_desk_cabinet","ae2:decorative/quartz_fixture","mcwbiomesoplenty:dead_nether_door","cfm:acacia_blinds","domum_ornamentum:green_floating_carpet","mcwwindows:cherry_plank_window2","mcwbiomesoplenty:dead_paper_door","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","botania:dye_orange","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","forbidden_arcanus:clibano_combustion/coal_from_clibano_combusting","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","create:crafting/kinetics/andesite_door","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_acacia_glass_table","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","immersiveengineering:crafting/cokebrick_to_slab","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","croptopia:shaped_fish_and_chips","mcwfurnitures:acacia_chair","mcwbiomesoplenty:palm_nether_door","enderio:dark_steel_nugget","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","cfm:blue_sofa","railcraft:whistle_tuner","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:fir_western_door","botania:blaze_block","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwfurnitures:jungle_striped_chair","bambooeverything:bamboo_torch","dyenamics:honey_wool","mcwroofs:acacia_top_roof","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","mcwfurnitures:acacia_glass_table","securitycraft:reinforced_purple_stained_glass_pane_from_dye","railways:crafting/track_switch_andesite","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","cfm:stripped_acacia_table","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","mcwbiomesoplenty:maple_mystic_door","mekanism:processing/osmium/raw/from_raw_block","expatternprovider:silicon_block","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","itemcollectors:basic_collector","minecraft:loom","ad_astra:steel_engine","enderio:resetting_lever_three_hundred_inv","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwbiomesoplenty:mahogany_stable_head_door","appbot:portable_mana_storage_cell_16k","mcwroofs:pink_terracotta_steep_roof","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","ad_astra:water_pump","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","minecraft:polished_blackstone","utilitarian:utility/spruce_logs_to_stairs","advanced_ae:quantumaccel","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","ad_astra:fluid_pipe_duct","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","travelersbackpack:horse","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","cfm:brown_cooler","delightful:knives/terra_knife","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","appbot:portable_mana_storage_cell_1k","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","productivebees:hives/advanced_snake_block_canvas_hive","dyenamics:amber_candle","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","comforts:sleeping_bag_to_gray","allthemodium:ancient_stone_brick_slabs","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","minecraft:respawn_anchor","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","mcwfurnitures:spruce_desk","pneumaticcraft:wall_lamp_red","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","mcwbiomesoplenty:mahogany_modern_door","immersiveengineering:crafting/component_steel","utilitarian:utility/acacia_logs_to_boats","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","comforts:hammock_to_blue","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","sophisticatedbackpacks:compacting_upgrade","mcwtrpdoors:spruce_swamp_trapdoor","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","farmersdelight:kelp_roll","mcwpaths:blackstone_flagstone","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","ae2:decorative/quartz_vibrant_glass","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:sandstone_stairs","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","allthemodium:ancient_stone_brick_wall","mcwbiomesoplenty:palm_barn_glass_door","cfm:acacia_chair","mcwwindows:oak_window","megacells:cells/standard/fluid_storage_cell_256m","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","domum_ornamentum:cyan_floating_carpet","minecraft:coarse_dirt","mcwwindows:spruce_plank_pane_window","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","botania:world_seed","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","minecraft:gold_ingot_from_gold_block","mcwwindows:acacia_louvered_shutter","mcwdoors:print_whispering","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","create:crafting/appliances/dough","cfm:light_gray_cooler","occultism:crafting/demons_dream_essence_from_seeds","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gray_terracotta_attic_roof","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","mcwfurnitures:stripped_spruce_stool_chair","enderio:silent_oak_pressure_plate","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwroofs:bricks_attic_roof","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","botania:orange_petal_block","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","ad_astra:airlock","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","croptopia:egg_roll","mcwbiomesoplenty:dead_waffle_door","twigs:bamboo_mat","railcraft:iron_gear","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","mcwfurnitures:stripped_acacia_drawer_counter","twigs:silt_brick","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","create:crafting/kinetics/mechanical_mixer","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","mcwbiomesoplenty:pine_bamboo_door","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","botania:quartz_sunny","mcwbiomesoplenty:stripped_redwood_log_window","productivebees:stonecutter/maple_canvas_expansion_box","mcwfurnitures:stripped_acacia_cupboard_counter","botania:terrasteel_helmet","mcwbiomesoplenty:dead_swamp_door","railcraft:signal_lamp","megacells:cells/standard/fluid_storage_cell_256m_with_housing","ad_astra:desh_panel","ad_astra:smelting/calorite_ingot_from_smelting_raw_calorite","cfm:jungle_blinds","tombstone:bone_needle","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:redwood_classic_door","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwroofs:white_top_roof","mcwwindows:blackstone_window2","botania:light_blue_petal_block","mcwbiomesoplenty:magic_waffle_door","additionallanterns:warped_lantern","ad_astra:calorite_plating","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","botania:floating_pure_daisy","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","mcwbiomesoplenty:redwood_cottage_door","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","alltheores:silver_ingot_from_ore_blasting","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","cfm:acacia_bedside_cabinet","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","botania:conversions/terrasteel_block_deconstruct","cfm:spruce_kitchen_counter","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwroofs:light_gray_terracotta_roof","ad_astra:gas_tank","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","bambooeverything:dry_bamboo","mcwlights:golden_small_chandelier","mcwbiomesoplenty:willow_paper_door","mcwwindows:lime_mosaic_glass","railways:stonecutting/riveted_locometal","securitycraft:reinforced_observer","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","cfm:crimson_mail_box","mcwdoors:spruce_nether_door","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","mcwfences:acacia_curved_gate","ae2:network/parts/panels_semi_dark_monitor","allthemodium:unobtainium_rod","supplementaries:deepslate_lamp","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","mcwbiomesoplenty:redwood_swamp_door","farmersdelight:cooking/baked_cod_stew","immersiveengineering:crafting/wirecoil_structure_rope","mcwfurnitures:oak_bookshelf","alltheores:lead_rod","mcwbiomesoplenty:jacaranda_western_door","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","mcwfurnitures:stripped_acacia_modern_wardrobe","ad_astra:iron_plateblock","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwdoors:acacia_beach_door","ad_astra:calorite_engine","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwbridges:bamboo_bridge_pier","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","productivebees:hives/advanced_jungle_canvas_hive","ad_astra:oxygen_sensor","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","farmersdelight:steak_and_potatoes","mcwtrpdoors:spruce_ranch_trapdoor","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","botania:itemfinder","mcwbiomesoplenty:umbran_plank_window2","utilitarian:utility/acacia_logs_to_stairs","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","cfm:acacia_coffee_table","mcwroofs:green_striped_awning","mcwpaths:sandstone_crystal_floor_stairs","pneumaticcraft:wall_lamp_inverted_blue","allthecompressed:compress/honey_block_1x","securitycraft:codebreaker","rftoolsutility:energy_module","supplementaries:flute","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","constructionwand:infinity_wand","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","botania:rainbow_rod","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","comforts:hammock_to_gray","minecraft:redstone_from_smelting_redstone_ore","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","cfm:blue_kitchen_counter","ae2:network/blocks/interfaces_interface","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","mcwwindows:stripped_oak_log_window","mcwdoors:bamboo_classic_door","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","supplementaries:statue","mcwbiomesoplenty:mahogany_tropical_door","cfm:pink_grill","ad_astra:jet_suit_boots","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","ad_astra:raw_desh_block","enderio:dark_steel_grinding_ball","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","botania:aura_ring_greater","mcwbiomesoplenty:maple_stable_head_door","rftoolsutility:counterplus_module","mcwroofs:white_terracotta_upper_steep_roof","mcwbiomesoplenty:dead_plank_window","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","ae2:network/cells/spatial_components_0","create:crafting/kinetics/mechanical_saw","minecraft:stick_from_bamboo_item","alltheores:bronze_plate","create:crafting/logistics/andesite_funnel","mcwpaths:brick_crystal_floor_slab","botania:swap_ring","mcwwindows:jungle_log_parapet","create:crafting/kinetics/depot","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","mcwfurnitures:acacia_double_drawer","aquaculture:tin_can_to_iron_nugget","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","mcwpaths:blackstone_basket_weave_paving","allthecompressed:compress/acacia_log_1x","pneumaticcraft:pressure_chamber_wall","modularrouters:player_module","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:pine_classic_door","mcwdoors:spruce_paper_door","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","ae2:tools/portable_fluid_cell_64k","mcwroofs:brown_concrete_attic_roof","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwbiomesoplenty:pine_cottage_door","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","dyenamics:persimmon_concrete_powder","botania:light_relay","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","minecraft:light_blue_concrete_powder","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","enderio:dark_steel_ladder","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","mcwbiomesoplenty:stripped_maple_pane_window","botania:spawner_claw","rftoolsstorage:storage_control_module","chimes:bamboo_chimes","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwbiomesoplenty:pine_nether_door","cfm:oak_upgraded_fence","mcwbiomesoplenty:hellbark_barn_glass_door","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","botania:placeholder","railcraft:steel_gear","delightful:knives/refined_glowstone_knife","ad_astra:encased_iron_block","allthemodium:piglich_heart_block","bigreactors:smelting/graphite_from_charcoal","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","xnet:netcable_green_dye","utilitarian:utility/acacia_logs_to_slabs","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","mcwpaths:andesite_flagstone","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","mcwbiomesoplenty:jacaranda_bamboo_door","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","alltheores:tin_ingot_from_ore_blasting","mcwbiomesoplenty:redwood_four_panel_door","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","pylons:potion_filter","enderio:resetting_lever_thirty","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","littlelogistics:energy_tug","pneumaticcraft:wall_lamp_light_gray","create:crafting/kinetics/contraption_controls","ad_astra:steel_factory_block","mcwwindows:acacia_log_parapet","mcwfurnitures:stripped_spruce_triple_drawer","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","occultism:crafting/spirit_torch","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","productivebees:expansion_boxes/expansion_box_cherry_canvas","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwfurnitures:acacia_table","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","allthecompressed:compress/tuff_1x","create:crafting/kinetics/sail_framefrom_conversion","mcwtrpdoors:bamboo_blossom_trapdoor","mcwroofs:gray_terracotta_upper_steep_roof","botania:mana_glass_pane","mcwbiomesoplenty:fir_tropical_door","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","aquaculture:heavy_hook","aether:netherite_sword_repairing","mcwbiomesoplenty:palm_western_door","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","mcwwindows:acacia_window","croptopia:food_press","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","ad_astra:photovoltaic_etrium_cell","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwfurnitures:oak_chair","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","ae2:network/cables/glass_green","mcwbiomesoplenty:willow_classic_door","ae2:tools/fluix_hoe","botania:terrasteel_chestplate","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","ad_astra:blasting/cheese_from_blasting_moon_cheese_ore","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","pneumaticcraft:reinforced_brick_pillar","mcwwindows:dark_oak_pane_window","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","mcwpaths:brick_crystal_floor","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","mcwdoors:acacia_swamp_door","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","mcwpaths:cobbled_deepslate_honeycomb_paving","productivebees:hives/advanced_birch_canvas_hive","megacells:crafting/bulk_cell_component","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","ae2:network/cables/glass_fluix","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","allthecompressed:compress/moss_block_1x","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","mcwdoors:bamboo_beach_door","mcwwindows:end_brick_gothic","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwdoors:spruce_modern_door","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwbiomesoplenty:dead_beach_door","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","mcwfurnitures:stripped_acacia_covered_desk","minecraft:popped_chorus_fruit","mcwbiomesoplenty:fir_barn_glass_door","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","botanypots:botanypots/crafting/terracotta_botany_pot","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwdoors:bamboo_bark_glass_door","mcwroofs:stone_bricks_top_roof","mcwwindows:crimson_plank_pane_window","ad_astra:calorite_sliding_door","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","utilitarian:utility/spruce_logs_to_boats","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwbiomesoplenty:hellbark_stable_head_door","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:jacaranda_swamp_door","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","mcwroofs:gutter_base_red","mcwtrpdoors:acacia_glass_trapdoor","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","productivebees:stonecutter/concrete_canvas_hive","mcwdoors:print_bamboo","createoreexcavation:vein_finder","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","mcwtrpdoors:spruce_tropical_trapdoor","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","mcwtrpdoors:print_cottage","mcwfurnitures:acacia_modern_desk","minecraft:copper_ingot_from_smelting_raw_copper","sophisticatedbackpacks:chipped/carpenters_table_upgrade","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","minecraft:netherite_ingot","naturalist:bug_net","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","mcwbiomesoplenty:jacaranda_mystic_door","sophisticatedstorage:oak_limited_barrel_4","domum_ornamentum:beige_bricks","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","supplementaries:hourglass","mcwfurnitures:acacia_end_table","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","mekanism:metallurgic_infuser","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","cfm:stripped_spruce_table","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","minecraft:jungle_sign","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","botania:conversions/light_blue_petal_block_deconstruct","railcraft:steel_tank_valve","cfm:cyan_cooler","ae2:network/parts/formation_plane","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","ad_astra:steel_rod","mcwdoors:acacia_modern_door","mcwbiomesoplenty:mahogany_mystic_door","create:crafting/kinetics/whisk","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","create:crafting/kinetics/mechanical_drill","mcwroofs:light_gray_roof","mcwbiomesoplenty:umbran_beach_door","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","dyenamics:bubblegum_candle","create:crafting/kinetics/weighted_ejector","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:umbran_bark_glass_door","mcwdoors:bamboo_tropical_door","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","mcwtrpdoors:mangrove_ranch_trapdoor","travelersbackpack:redstone","mcwfurnitures:stripped_acacia_end_table","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","minecraft:slime_block","minecraft:acacia_planks","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwbiomesoplenty:fir_nether_door","chimes:glass_bells","mcwwindows:iron_shutter","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","aether:diamond_chestplate_repairing","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","farmersdelight:cutting_board","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","appbot:portable_mana_storage_cell_64k","ad_astra:nasa_workbench","mcwpaths:sandstone_flagstone","ad_astra:etrionic_capacitor","mcwbiomesoplenty:empyreal_japanese2_door","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","mcwbridges:blackstone_bridge_pier","mcwbiomesoplenty:empyreal_swamp_door","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","mcwfurnitures:stripped_spruce_large_drawer","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","appmek:portable_chemical_storage_cell_16k","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:magenta_terracotta_attic_roof","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","mcwfurnitures:stripped_acacia_coffee_table","alltheores:copper_rod","productivebees:stonecutter/cherry_canvas_hive","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","botania:dragonstone_block","minecraft:white_carpet","utilitix:filter_rail","aether:diamond_gloves","minecraft:spruce_planks","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","enderio:resetting_lever_sixty_from_inv","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwbiomesoplenty:dead_stable_door","minecraft:mossy_cobblestone_from_moss_block","mcwfurnitures:spruce_triple_drawer","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","gtceu:smelting/smelt_dust_electrum_to_ingot","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","minecraft:magenta_stained_glass","minecraft:ender_chest","mcwbiomesoplenty:willow_western_door","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","alltheores:gold_dust_from_hammer_ingot_crushing","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","create:crafting/kinetics/shaft","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","modularrouters:void_module","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","mysticalagriculture:imperium_essence_uncraft","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","allthecompressed:compress/obsidian_6x","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","mcwfurnitures:spruce_large_drawer","biomesoplenty:maple_boat","appbot:portable_mana_storage_cell_256k","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","ad_astra:desh_nugget","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","securitycraft:reinforced_warped_fence_gate","pneumaticcraft:gun_ammo_weighted","mcwfurnitures:acacia_bookshelf","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","expatternprovider:epp","create:crafting/kinetics/gearshift","simplylight:walllamp","mcwwindows:dark_oak_four_window","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","bloodmagic:synthetic_point","ad_astra:ostrum_panel","croptopia:doughnut","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","alltheores:lumium_ingot_from_dust","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","cfm:stripped_acacia_crate","mcwbiomesoplenty:dead_barn_glass_door","allthecompressed:compress/cobbled_deepslate_1x","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","handcrafted:terracotta_bowl","littlecontraptions:contraption_barge","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwfurnitures:stripped_acacia_double_wardrobe","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwbiomesoplenty:pine_western_door","utilitarian:utility/acacia_logs_to_trapdoors","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","travelersbackpack:blue_sleeping_bag","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","botania:diluted_pool","modularrouters:energy_output_module","mcwbiomesoplenty:hellbark_japanese_door","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","croptopia:tortilla","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","ae2:network/cables/glass_lime","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","railcraft:animal_detector","mcwbiomesoplenty:palm_stable_door","minecraft:stone_slab_from_stone_stonecutting","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","ad_astra:desh_block","mcwwindows:stripped_jungle_log_window","minecraft:stone_brick_slab_from_stone_stonecutting","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","mcwroofs:spruce_upper_lower_roof","mcwbiomesoplenty:palm_four_panel_door","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","create:crafting/materials/andesite_alloy_block","minecraft:oak_trapdoor","cfm:spruce_chair","mcwbiomesoplenty:mahogany_japanese2_door","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwtrpdoors:cherry_ranch_trapdoor","create:crafting/kinetics/sticky_mechanical_piston","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","ad_astra:desh_ingot_from_desh_block","mcwbiomesoplenty:dead_hedge","minecraft:honey_bottle","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwdoors:bamboo_barn_glass_door","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","mcwbiomesoplenty:fir_glass_door","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","alltheores:zinc_ingot_from_ore_blasting","create:crafting/kinetics/gantry_shaft","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","mcwbridges:balustrade_blackstone_bridge","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","ae2:network/cables/glass_blue","alltheores:lumium_rod","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","arseng:portable_source_cell_16k","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwbiomesoplenty:mahogany_bamboo_door","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","mcwroofs:brown_terracotta_upper_steep_roof","mcwdoors:oak_swamp_door","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwwindows:nether_brick_gothic","botania:forest_eye","productivetrees:planks/soul_tree_planks","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","mcwbiomesoplenty:willow_mystic_door","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","alltheores:diamond_dust_from_hammer_crushing","mcwbiomesoplenty:pine_modern_door","handcrafted:wither_skeleton_trophy","create:andesite_ladder_from_andesite_alloy_stonecutting","productivebees:hives/advanced_bamboo_canvas_hive","mcwroofs:blue_terracotta_top_roof","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","mcwtrpdoors:acacia_mystic_trapdoor","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:umbran_paper_door","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwbiomesoplenty:empyreal_stable_door","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","railcraft:diamond_spike_maul","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwroofs:gray_roof_block","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","allthecompressed:compress/blaze_block_1x","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","utilitarian:utility/spruce_logs_to_doors","cfm:stripped_acacia_upgraded_fence","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","comforts:hammock_to_white","create:smelting/zinc_ingot_from_ore","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","create:crafting/kinetics/mechanical_bearing","domum_ornamentum:yellow_floating_carpet","minecraft:blue_banner","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwdoors:jungle_glass_door","mcwdoors:bamboo_nether_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","ae2:decorative/cut_quartz_block","mcwbiomesoplenty:dead_japanese_door","botania:petal_light_blue","mcwbiomesoplenty:jacaranda_tropical_door","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwfurnitures:spruce_modern_chair","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","croptopia:dough","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","cfm:stripped_acacia_cabinet","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","alltheores:invar_rod","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","productivebees:expansion_boxes/expansion_box_bamboo_canvas","botania:dirt_rod","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","mcwfurnitures:stripped_oak_table","rftoolspower:power_core1","pneumaticcraft:logistics_frame_default_storage","aether:wooden_axe_repairing","mcwroofs:white_concrete_upper_steep_roof","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","create:crafting/kinetics/belt_connector","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","simplylight:illuminant_lime_block_on_toggle","ae2:misc/deconstruction_certus_quartz_block","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","mcwpaths:brick_running_bond_path","mcwfurnitures:stripped_acacia_stool_chair","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","botania:mana_pylon","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:birch_blinds","mcwfurnitures:stripped_oak_striped_chair","cfm:spruce_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","cfm:acacia_table","mcwpaths:brick_windmill_weave_stairs","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","mcwdoors:spruce_tropical_door","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","advanced_ae:smalladvpatpro","botania:apothecary_livingrock","mcwdoors:oak_whispering_door","alltheores:signalum_ingot_from_dust","mcwwindows:jungle_window2","mcwtrpdoors:bamboo_barn_trapdoor","railcraft:iron_spike_maul","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","alltheores:platinum_ingot_from_ore","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","mcwbiomesoplenty:dead_barn_door","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","railcraft:feed_station","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","mcwfurnitures:spruce_chair","productivebees:expansion_boxes/expansion_box_spruce_canvas","twigs:gravel_bricks","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","immersiveengineering:crafting/plate_uranium_hammering","railcraft:personal_world_spike","mcwdoors:bamboo_paper_door","handcrafted:oak_nightstand","immersiveengineering:crafting/armor_steel_chestplate","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","mcwroofs:thatch_steep_roof","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","mcwfurnitures:acacia_modern_wardrobe","dyenamics:bubblegum_concrete_powder","ad_astra:encased_calorite_block","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","delightful:food/cooking/ender_nectar","merequester:requester","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","ad_astra:ostrum_factory_block","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:fir_four_panel_door","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","supplementaries:slingshot","advanced_ae:quantumunit","allthemodium:allthemodium_plate","mcwdoors:acacia_japanese2_door","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","productivebees:expansion_boxes/expansion_box_mangrove_canvas","everythingcopper:copper_rail","cfm:black_grill","handcrafted:deepslate_pillar_trim","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_four_panel_door","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","ad_astra:desh_sliding_door","dyenamics:rose_candle","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwroofs:bricks_upper_steep_roof","botania:terraform_rod","minecraft:iron_pickaxe","ae2:shaped/not_so_mysterious_cube","mcwwindows:jungle_shutter","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","ae2:network/cables/glass_orange","mcwwindows:granite_window","deepresonance:resonating_plate","mcwfurnitures:stripped_spruce_table","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","create:crafting/kinetics/cart_assembler","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","minecraft:birch_boat","mcwfences:spruce_highley_gate","handcrafted:gray_sheet","cfm:post_box","mcwbiomesoplenty:redwood_stable_head_door","dyenamics:aquamarine_candle","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwtrpdoors:bamboo_classic_trapdoor","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mekanism:teleportation_core","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","mcwbiomesoplenty:empyreal_western_door","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwtrpdoors:bamboo_barred_trapdoor","dyenamics:mint_candle","mcwroofs:acacia_lower_roof","mcwbiomesoplenty:empyreal_japanese_door","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:hellbark_paper_door","cfm:white_kitchen_counter","create:crafting/kinetics/filter","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","mcwfurnitures:acacia_coffee_table","mcwbiomesoplenty:umbran_stable_door","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwbiomesoplenty:willow_stable_head_door","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","botania:dreamwood_twig","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","ad_astra:smelting/ice_shard_from_smelting_mars_ice_shard_ore","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","mcwdoors:bamboo_modern_door","cfm:fridge_dark","chimes:copper_chimes","ad_astra:cryo_freezer","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwtrpdoors:mangrove_bark_trapdoor","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mekanism:digital_miner","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","alltheores:steel_dust_from_alloy_blending","immersiveengineering:crafting/earmuffs","mcwroofs:black_terracotta_lower_roof","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","mcwfurnitures:stripped_jungle_double_wardrobe","minecraft:scaffolding","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwbiomesoplenty:fir_cottage_door","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","constructionwand:core_angel","mcwfurnitures:acacia_bookshelf_cupboard","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwbiomesoplenty:willow_glass_door","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","delightful:knives/tin_knife","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","allthemodium:ancient_cracked_stone_bricks_from_crushing","mcwlights:copper_candle_holder","mcwtrpdoors:bamboo_swamp_trapdoor","mcwfurnitures:acacia_covered_desk","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:honey_block","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","create:crafting/kinetics/large_cogwheel","sophisticatedbackpacks:crafting_upgrade","cfm:stripped_spruce_desk","productivebees:stonecutter/willow_canvas_expansion_box","aquaculture:birch_fish_mount","ae2:network/cells/fluid_storage_cell_1k_storage","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","create:crafting/kinetics/metal_bracket","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","cfm:stripped_acacia_coffee_table","mcwbiomesoplenty:empyreal_barn_glass_door","cfm:white_sofa","tombstone:ankh_of_prayer","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwfences:ornate_metal_fence","ad_astra:jet_suit_helmet","cfm:stripped_acacia_blinds","mcwfences:acacia_highley_gate","aiotbotania:livingrock_sword","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:spruce_roof","create:crafting/kinetics/gantry_carriage","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","mcwbiomesoplenty:stripped_fir_log_four_window","immersiveengineering:crafting/survey_tools","create:crafting/logistics/powered_latch","productivebees:stonecutter/warped_canvas_expansion_box","mcwbiomesoplenty:maple_swamp_door","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","ad_astra:desh_plateblock","aquaculture:double_hook","ae2:shaped/slabs/sky_stone_block","allthemodium:allthemodium_rod","handcrafted:oak_bench","mcwbiomesoplenty:maple_bark_glass_door","create:crafting/materials/andesite_alloy_from_block","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","create:crafting/kinetics/water_wheel","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:maple_stable_door","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwpaths:blackstone_crystal_floor_path","mcwbiomesoplenty:empyreal_glass_door","cfm:jungle_cabinet","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwtrpdoors:acacia_beach_trapdoor","mcwroofs:base_roof_block","productivebees:hives/advanced_mangrove_canvas_hive","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","twigs:blackstone_column","mcwfurnitures:acacia_lower_bookshelf_drawer","mcwbiomesoplenty:fir_waffle_door","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","littlecontraptions:barge_assembler","dyenamics:navy_candle","mcwwindows:spruce_blinds","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","create:crafting/kinetics/analog_lever","create:andesite_scaffolding_from_andesite_alloy_stonecutting","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","botania:terra_axe","domum_ornamentum:light_blue_floating_carpet","megacells:cells/standard/item_storage_cell_256m_with_housing","create:crafting/kinetics/sticker","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","ad_astra:ostrum_plating","mcwbiomesoplenty:pine_beach_door","enderio:silent_light_weighted_pressure_plate","ae2:block_cutter/slabs/quartz_slab","appmek:portable_chemical_storage_cell_256k","sophisticatedbackpacks:chipped/glassblower_upgrade","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","botania:petal_brown_double","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","handcrafted:witch_trophy","mcwfurnitures:stripped_oak_counter","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","handcrafted:jungle_drawer","minecraft:oak_pressure_plate","megacells:cells/standard/item_storage_cell_256m","minecraft:stone_brick_stairs","bigreactors:turbine/basic/activefluidport_forge","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","mcwwindows:quartz_window","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","mcwroofs:gutter_base_brown","cfm:acacia_crate","alltheores:uranium_dust_from_hammer_crushing","botania:dye_brown","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","mcwbiomesoplenty:redwood_tropical_door","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","dyenamics:peach_candle","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","minecraft:clock","mcwroofs:red_concrete_top_roof","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","railcraft:lead_gear","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwtrpdoors:spruce_whispering_trapdoor","mcwdoors:acacia_western_door","minecraft:diamond_leggings","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","ad_astra:iron_plating","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwbiomesoplenty:maple_paper_door","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","ad_astra:blasting/ice_shard_from_blasting_mars_ice_shard_ore","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","mcwroofs:purple_terracotta_roof","alltheores:tin_rod","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","cfm:acacia_kitchen_sink_dark","botania:keep_ivy","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","mcwbiomesoplenty:magic_western_door","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","cfm:stripped_spruce_crate","twigs:rhyolite","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","create:crafting/kinetics/linear_chassis","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","mcwbiomesoplenty:pine_swamp_door","utilitix:mob_yoinker","domum_ornamentum:light_blue_brick_extra","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","mcwroofs:thatch_top_roof","ad_astra:smelting/ostrum_ingot_from_smelting_raw_ostrum","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","minecraft:lime_concrete_powder","ae2:network/blocks/energy_dense_energy_cell","minecraft:sandstone_wall_from_sandstone_stonecutting","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","mcwlights:festive_lantern","dyenamics:honey_candle","mcwbiomesoplenty:magic_tropical_door","securitycraft:block_change_detector","croptopia:fruit_salad","twigs:rocky_dirt","supplementaries:pancake_fd","advanced_ae:advpatpro2","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","minecraft:netherite_scrap_from_blasting","mcwbiomesoplenty:dead_bamboo_door","allthemodium:ancient_stone_brick_stairs","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","create:crafting/kinetics/mechanical_piston","mcwtrpdoors:bamboo_four_panel_trapdoor","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","handcrafted:blue_sheet","mcwfurnitures:oak_drawer_counter","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","botania:dreamwood_slab","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","minecraft:sandstone_slab","mcwwindows:birch_window","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","allthecompressed:compress/spruce_log_1x","mcwbiomesoplenty:pine_bark_glass_door","minecraft:gold_ingot_from_smelting_raw_gold","sophisticatedbackpacks:smithing_upgrade","mcwbiomesoplenty:palm_beach_door","mcwfences:mud_brick_railing_gate","twigs:oak_table","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","pneumaticcraft:heat_pipe","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","comforts:hammock_gray","mcwbiomesoplenty:magic_bamboo_door","mcwfurnitures:oak_lower_triple_drawer","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwfurnitures:stripped_acacia_double_drawer","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","mcwfences:mangrove_wired_fence","minecraft:red_terracotta","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","create:crafting/kinetics/basin","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwpaths:blackstone_dumble_paving","mcwroofs:white_roof_block","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwbiomesoplenty:mahogany_stable_door","securitycraft:block_pocket_manager","create:crafting/kinetics/encased_chain_drive","mcwlights:soul_cherry_tiki_torch","botania:red_pavement","securitycraft:reinforced_mangrove_fence","alltheores:copper_ore_hammer","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","bloodmagic:blood_altar","alltheores:lumium_dust_from_alloy_blending","mcwfurnitures:spruce_bookshelf_cupboard","cfm:purple_kitchen_drawer","ad_astra:steel_plateblock","ad_astra:blasting/calorite_ingot_from_blasting_raw_calorite","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:acacia_triple_drawer","minecraft:netherite_chestplate_smithing","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","mcwfurnitures:stripped_acacia_double_drawer_counter","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","railcraft:bag_of_cement","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","cfm:acacia_desk","mcwwindows:dark_oak_plank_parapet","mcwbiomesoplenty:pine_waffle_door","mcwwindows:spruce_log_parapet","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwfurnitures:stripped_acacia_drawer","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","mekanism:osmium_compressor","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:stripped_spruce_counter","ad_astra:oxygen_distributor","mcwroofs:purple_concrete_roof","botania:apothecary_mossy","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwbiomesoplenty:willow_bamboo_door","eidolon:smooth_stone_masonry_stonecutter_0","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwroofs:spruce_lower_roof","croptopia:apple_pie","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","mcwdoors:spruce_whispering_door","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","deeperdarker:bloom_boat","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwpaths:blackstone_diamond_paving","pneumaticcraft:vortex_cannon","ad_astra:ostrum_engine","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","botania:red_string","mcwroofs:pink_concrete_upper_lower_roof","mcwroofs:acacia_steep_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","allthecompressed:compress/copper_block_1x","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","botania:dye_light_blue","mcwpaths:andesite_flagstone_stairs","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","ad_astra:netherite_space_pants","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwdoors:bamboo_waffle_door","mcwfurnitures:stripped_acacia_desk","ad_astra:calorite_plateblock","farmersdelight:cooking/noodle_soup","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","rftoolsutility:moduleplus_template","mcwbiomesoplenty:willow_tropical_door","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","create:crafting/kinetics/white_sail","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","mcwbiomesoplenty:dead_tropical_door","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwbiomesoplenty:jacaranda_paper_door","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","ae2:tools/misctools_entropy_manipulator","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","create:crafting/kinetics/hand_crank","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwtrpdoors:spruce_classic_trapdoor","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","mcwtrpdoors:acacia_swamp_trapdoor","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","mcwbiomesoplenty:fir_modern_door","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","create:crafting/kinetics/portable_storage_interface","minecraft:iron_helmet","ad_astra:steel_trapdoor","botania:red_string_alt","securitycraft:reinforced_pink_stained_glass_pane_from_dye","mcwbiomesoplenty:mahogany_barn_glass_door","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","railcraft:manual_rolling_machine","minecraft:cartography_table","securitycraft:alarm","pneumaticcraft:manometer","ad_astra:launch_pad","mcwbiomesoplenty:dead_japanese2_door","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","cfm:stripped_acacia_desk_cabinet","ae2:tools/fluix_shovel","pneumaticcraft:wall_lamp_inverted_magenta","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","utilitix:crude_furnace","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","mcwfurnitures:stripped_acacia_striped_chair","mcwroofs:gray_steep_roof","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","mekanism:personal_chest","alltheores:iron_ore_hammer","productivebees:stonecutter/yucca_canvas_hive","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","aether:blue_cape_blue_wool","mcwfences:railing_nether_brick_wall","mcwroofs:yellow_terracotta_upper_lower_roof","ae2:network/cables/glass_black","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","botania:polished_livingrock","mcwwindows:warped_stem_window","ad_astra:tier_1_rover","railcraft:invar_gear","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","mcwfurnitures:acacia_cupboard_counter","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","productivebees:expansion_boxes/expansion_box_warped_canvas","ae2:tools/portable_fluid_cell_16k","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwdoors:print_spruce","mcwbiomesoplenty:magic_mystic_door","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","create:crafting/kinetics/piston_extension_pole","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","mcwwindows:oak_window2","mcwfurnitures:stripped_acacia_bookshelf","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","bigreactors:reprocessor/collector","create:crafting/kinetics/lime_seat","mcwbiomesoplenty:empyreal_bamboo_door","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","botania:thunder_sword","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","mcwdoors:acacia_bamboo_door","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","productivebees:stonecutter/yucca_canvas_expansion_box","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","mcwfurnitures:spruce_table","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","botania:temperance_stone","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","botania:petal_orange","mcwbridges:balustrade_bricks_bridge","pneumaticcraft:logistics_frame_storage_self","botania:crystal_bow","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","mcwbiomesoplenty:dead_mystic_door","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","pneumaticcraft:pressure_chamber_valve","delightful:food/cooking/jam_jar","pneumaticcraft:logistics_frame_passive_provider","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","enderio:black_paper","twigs:polished_tuff_stonecutting","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","ae2:network/cables/dense_smart_fluix","mcwbiomesoplenty:pine_japanese_door","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","allthemodium:allthemodium_ingot","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","mcwbiomesoplenty:redwood_glass_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwfurnitures:spruce_modern_wardrobe","mcwtrpdoors:spruce_barn_trapdoor","ae2:network/blocks/pattern_providers_interface_part","productivebees:stonecutter/hellbark_canvas_hive","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwbridges:bamboo_bridge","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","botania:terra_pick","botania:brewery","mcwbiomesoplenty:maple_beach_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","mcwbiomesoplenty:magic_four_panel_door","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","mcwfurnitures:jungle_large_drawer","mcwfurnitures:acacia_drawer_counter","botania:monocle","mcwroofs:deepslate_lower_roof","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","mcwbiomesoplenty:hellbark_four_panel_door","ad_astra:desh_plating","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","ae2:network/cables/glass_light_blue","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:acacia_striped_chair","mcwfurnitures:spruce_drawer_counter","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","mcwtrpdoors:bamboo_beach_trapdoor","minecolonies:apple_pie","mcwpaths:stone_running_bond_path","twigs:mossy_bricks_from_moss_block","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","minecraft:andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","create:crafting/appliances/filter_clear","mcwbiomesoplenty:palm_barn_door","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","alltheores:copper_dust_from_hammer_crushing","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","industrialforegoing:plastic","domum_ornamentum:orange_floating_carpet","mcwbiomesoplenty:palm_mystic_door","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","create:crafting/kinetics/goggles","alltheores:osmium_dust_from_hammer_crushing","pneumaticcraft:gun_ammo_ap","mcwbiomesoplenty:maple_japanese2_door","aether:aether_saddle","create:crafting/kinetics/rope_pulley","farmersdelight:wheat_dough_from_water","create:crafting/kinetics/cuckoo_clock","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","ad_astra:zip_gun","croptopia:nougat","rftoolsstorage:storage_module0","immersiveengineering:crafting/fluid_placer","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwdoors:acacia_whispering_door","mcwwindows:stripped_spruce_log_window","ad_astra:desh_tank","mcwfurnitures:stripped_jungle_table","mcwbiomesoplenty:jacaranda_cottage_door","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","allthecompressed:decompress/obsidian_5x","additionallanterns:deepslate_bricks_lantern","minecraft:gray_carpet","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","cfm:stripped_jungle_mail_box","mcwfurnitures:spruce_double_drawer","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwtrpdoors:acacia_whispering_trapdoor","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwfurnitures:stripped_spruce_drawer_counter","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","ad_astra:desh_engine","forbidden_arcanus:deorum_lantern","mcwbiomesoplenty:hellbark_glass_door","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","farmersdelight:cooking/glow_berry_custard","twigs:bloodstone","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","productivebees:expansion_boxes/expansion_box_oak_canvas","mcwroofs:orange_concrete_steep_roof","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwwindows:warped_planks_window","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwroofs:acacia_roof","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","arseng:portable_source_cell_256k","mcwbiomesoplenty:empyreal_mystic_door","mekanism:logistical_sorter","mcwroofs:gutter_base_light_blue","create:crafting/kinetics/gearboxfrom_conversion","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","create:crafting/kinetics/orange_seat","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","ad_astra:raw_ostrum_block","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","ad_astra:gravity_normalizer","botania:gravity_rod","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","bigreactors:blasting/graphite_from_coal","ad_astra:jet_suit","sophisticatedbackpacks:stack_upgrade_tier_1","allthecompressed:compress/end_stone_6x","appmek:portable_chemical_storage_cell_1k","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","sophisticatedbackpacks:restock_upgrade","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:wheat_dough_from_eggs","mcwbiomesoplenty:willow_pane_window","croptopia:sweet_crepes","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","deepresonance:filter_material","immersiveengineering:crafting/strip_curtain","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","ae2:tools/certus_quartz_cutting_knife","mcwfurnitures:spruce_counter","ad_astra:blasting/desh_ingot_from_blasting_raw_desh","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","botania:corporea_spark","productivebees:hives/advanced_acacia_canvas_hive","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","appmek:portable_chemical_storage_cell_4k","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:acacia_paper_trapdoor","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","mcwbiomesoplenty:pine_paper_door","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","cfm:acacia_upgraded_gate","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","botania:clip","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","alltheores:platinum_ingot_from_ore_blasting","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","pneumaticcraft:air_canister","mcwwindows:prismarine_pane_window","mcwpaths:brick_windmill_weave_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","twigs:cracked_bricks","supplementaries:slice_map","productivebees:expansion_boxes/expansion_box_jungle_canvas","mcwbridges:brick_bridge_pier","mcwdoors:acacia_mystic_door","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","alltheores:invar_plate","occultism:crafting/butcher_knife","cfm:stripped_spruce_desk_cabinet","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mekanism:control_circuit/advanced","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","botania:lens_normal","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","pneumaticcraft:wall_lamp_inverted_cyan","mcwroofs:spruce_steep_roof","mcwfurnitures:jungle_modern_chair","create:crafting/kinetics/radial_chassis","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","mcwroofs:stone_bricks_roof","cfm:cyan_grill","thermal_extra:smelting/twinite_ingot_from_dust_smelting","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","mcwbiomesoplenty:palm_bamboo_door","ad_astra:raw_calorite_block","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwtrpdoors:acacia_bark_trapdoor","alltheores:uranium_ingot_from_ore_blasting","mcwbiomesoplenty:hellbark_waffle_door","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","minecraft:polished_andesite","mcwbiomesoplenty:palm_swamp_door","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","ad_astra:iron_factory_block","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwdoors:bamboo_stable_head_door","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","cfm:white_picket_fence","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:acacia_drawer","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","twigs:polished_tuff_bricks_from_tuff_stonecutting","ad_astra:steel_ingot","handcrafted:kitchen_hood","mcwdoors:jungle_bamboo_door","sophisticatedbackpacks:stonecutter_upgrade","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:fir_swamp_door","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","create:crafting/kinetics/super_glue","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:black_lower_roof","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwbiomesoplenty:dead_stable_head_door","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","pneumaticcraft:volume_upgrade","mcwfurnitures:stripped_spruce_bookshelf_drawer","mcwtrpdoors:oak_four_panel_trapdoor","travelersbackpack:netherite_tier_upgrade","bigreactors:reprocessor/fluidinjector","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwroofs:acacia_upper_lower_roof","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwroofs:andesite_lower_roof","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","mcwbridges:andesite_bridge","mcwwindows:dark_prismarine_pane_window","pneumaticcraft:pressure_chamber_valve_x1","mcwbiomesoplenty:redwood_bamboo_door","mcwwindows:oak_plank_parapet","immersiveengineering:crafting/hempcrete","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","minecraft:spruce_wood","mcwpaths:sandstone_clover_paving","ad_astra:ostrum_block","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","ae2:tools/portable_fluid_cell_4k","domum_ornamentum:blue_cobblestone_extra","ad_astra:netherite_space_boots","botania:runic_altar","additionallanterns:blackstone_chain","mcwbiomesoplenty:magic_japanese2_door","domum_ornamentum:purple_brick_extra","mcwbiomesoplenty:hellbark_stable_door","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfurnitures:stripped_acacia_table","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","ae2:tools/fluix_upgrade_smithing_template","botania:petal_light_blue_double","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","productivebees:stonecutter/maple_canvas_hive","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwdoors:acacia_barn_glass_door","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","handcrafted:jungle_table","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","ad_astra:encased_desh_block","botania:abstruse_platform","create:crafting/kinetics/metal_girder","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","productivebees:stonecutter/dead_canvas_hive","create:crafting/kinetics/millstone","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","ae2:tools/portable_fluid_cell_1k","mcwbiomesoplenty:empyreal_barn_door","productivebees:expansion_boxes/expansion_box_birch_canvas","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","enderio:resetting_lever_ten_from_prev","mcwtrpdoors:acacia_classic_trapdoor","mcwroofs:magenta_concrete_upper_lower_roof","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","pneumaticcraft:gun_ammo_explosive","mcwbiomesoplenty:hellbark_nether_door","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","forbidden_arcanus:clibano_combustion/diamond_from_clibano_combusting","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","cfm:stripped_acacia_upgraded_gate","mcwpaths:sandstone_running_bond","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwbiomesoplenty:empyreal_paper_door","minecraft:gray_terracotta","mcwfurnitures:acacia_modern_chair","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","mcwdoors:bamboo_mystic_door","cfm:stripped_spruce_coffee_table","mcwroofs:pink_concrete_attic_roof","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","mcwbiomesoplenty:magic_bark_glass_door","dyenamics:peach_terracotta","botania:livingrock_wall","minecraft:fishing_rod","xnet:connector_yellow_dye","minecraft:terracotta","mcwwindows:acacia_blinds","productivebees:stonecutter/snake_block_canvas_expansion_box","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwdoors:acacia_waffle_door","botania:blue_pavement","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","comforts:sleeping_bag_gray","twigs:copper_pillar_stonecutting","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","productivebees:stonecutter/redwood_canvas_hive","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","minecraft:beacon","railcraft:tin_gear","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwdoors:spruce_stable_door","aquaculture:nether_star_hook","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","cfm:spruce_upgraded_gate","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","mekanism:steel_casing","alltheores:invar_dust_from_alloy_blending","ad_astra:solar_panel","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","aiotbotania:terra_shovel","mcwfurnitures:spruce_cupboard_counter","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:umbran_bamboo_door","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:hay","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","productivebees:expansion_boxes/expansion_box_snake_block_canvas","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","mcwroofs:magenta_terracotta_lower_roof","create:crafting/kinetics/windmill_bearing","ad_astra:vent","create:crafting/kinetics/turntable","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","ad_astra:ostrum_sliding_door","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","handcrafted:terracotta_plate","mcwroofs:light_blue_terracotta_roof","mcwbiomesoplenty:willow_barn_glass_door","rftoolsbuilder:blue_shield_template_block","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","botania:conversions/dragonstone_block_deconstruct","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","silentgear:coating_smithing_template","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwbiomesoplenty:maple_barn_glass_door","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","ad_astra:blue_flag","create:crafting/kinetics/mechanical_plough","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","alltheores:lumium_plate","create:crafting/kinetics/cogwheel","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","mcwfurnitures:stripped_spruce_cupboard_counter","mcwbiomesoplenty:mahogany_paper_door","ae2:materials/annihilationcore","enderio:cold_fire_igniter","ad_astra:blasting/ostrum_ingot_from_blasting_raw_ostrum","silentgear:stone_rod","minecraft:leather_boots","railcraft:tank_detector","railcraft:steel_spike_maul","handcrafted:sandstone_corner_trim","ad_astra:etrionic_blast_furnace","create:crafting/logistics/andesite_tunnel","pneumaticcraft:classify_filter","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwbiomesoplenty:redwood_japanese_door","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","ad_astra:ostrum_nugget","mcwtrpdoors:acacia_barred_trapdoor","mcwlights:pink_paper_lamp","mcwbiomesoplenty:magic_nether_door","pneumaticcraft:gun_ammo_incendiary","pneumaticcraft:wall_lamp_inverted_light_blue","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","bigreactors:turbine/basic/bearing","tombstone:white_marble","mcwfurnitures:acacia_bookshelf_drawer","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","cfm:acacia_park_bench","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","mcwfurnitures:stripped_acacia_triple_drawer","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","mcwfurnitures:acacia_wardrobe","sfm:cable","minecraft:hay_block","mcwbiomesoplenty:palm_japanese_door","mcwroofs:deepslate_upper_steep_roof","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","mcwroofs:white_upper_steep_roof","alltheores:ruby_from_hammer_crushing","delightful:knives/invar_knife","create:crafting/kinetics/vertical_gearboxfrom_conversion","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:jacaranda_barn_glass_door","botania:phantom_ink","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwbiomesoplenty:fir_barn_door","mcwroofs:blue_concrete_top_roof","bambooeverything:bamboo_ladder","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","mcwdoors:oak_waffle_door","bigreactors:blasting/graphite_from_charcoal","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","ad_astra:calorite_factory_block","railcraft:goggles","twigs:paper_lantern","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","mcwbiomesoplenty:willow_japanese2_door","domum_ornamentum:brick_extra","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwtrpdoors:bamboo_barrel_trapdoor","mcwbiomesoplenty:empyreal_window2","mcwbiomesoplenty:pine_barn_glass_door","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","enderio:basic_item_filter","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","ad_astra:ostrum_tank","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","immersiveengineering:crafting/fertilizer","ae2:network/cells/spatial_storage_cell_2_cubed","mcwroofs:magenta_terracotta_upper_steep_roof","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/cokebrick_from_slab","mcwwindows:warped_stem_four_window","securitycraft:sentry","botania:tornado_rod","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","alltheores:uranium_rod","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","alltheores:aluminum_ingot_from_ore_blasting","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","mcwroofs:pink_terracotta_top_roof","mcwfurnitures:stripped_acacia_lower_bookshelf_drawer","cfm:green_kitchen_counter","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwbiomesoplenty:fir_mystic_door","mekanism:processing/refined_obsidian/ingot/from_block","ad_astra:desh_factory_block","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","ae2:tools/certus_quartz_spade","mcwbiomesoplenty:fir_beach_door","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","farmersdelight:skillet","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","megacells:cells/portable/portable_fluid_cell_256m","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","occultism:crafting/spirit_campfire","expatternprovider:epp_alt","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","minecraft:white_stained_glass","expatternprovider:assembler_matrix_frame","mcwbiomesoplenty:umbran_mystic_door","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","immersiveengineering:crafting/metal_ladder_none","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","domum_ornamentum:white_brick_extra","aiotbotania:livingrock_hoe","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","mcwfurnitures:acacia_double_drawer_counter","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","immersiveengineering:crafting/chute_iron","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","minecraft:acacia_hanging_sign","mcwfences:jungle_hedge","ae2:network/parts/toggle_bus","mcwdoors:jungle_classic_door","create:crafting/kinetics/large_cogwheel_from_little","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","minecraft:blue_bed","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","create:crafting/kinetics/mechanical_harvester","gtceu:smelting/smelt_dust_bronze_to_ingot","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","allthecompressed:compress/deepslate_1x","allthecompressed:compress/stone_1x","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","botania:manasteel_shovel","handcrafted:oven","minecraft:quartz_from_blasting","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","thermal_extra:smelting/soul_infused_ingot_from_dust_smelting","mcwpaths:cobbled_deepslate_crystal_floor_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwbiomesoplenty:magic_stable_door","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","delightful:knives/bronze_knife","mcwpaths:blackstone_crystal_floor","mekanismgenerators:generator/bio","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwbiomesoplenty:mahogany_classic_door","sophisticatedbackpacks:tool_swapper_upgrade","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","botania:conversions/orange_petal_block_deconstruct","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","arseng:portable_source_cell_4k","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","handcrafted:bench","mekanism:processing/osmium/ingot/from_raw_smelting","allthecompressed:compress/andesite_1x","mcwbiomesoplenty:willow_nether_door","minecraft:stone_slab","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","cfm:gray_sofa","mcwbiomesoplenty:magic_classic_door","productivebees:stonecutter/comb_canvas_hive","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","mcwbiomesoplenty:maple_four_panel_door","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","aether:netherite_chestplate_repairing","mcwfurnitures:stripped_oak_wardrobe","ae2:network/parts/import_bus","croptopia:campfire_molasses","mcwdoors:acacia_stable_door","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwbiomesoplenty:pine_four_panel_door","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","mcwbiomesoplenty:hellbark_cottage_door","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwtrpdoors:bamboo_bark_trapdoor","mcwpaths:brick_windmill_weave","twigs:crimson_roots_paper_lantern","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:fletching_table","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","mcwbiomesoplenty:jacaranda_glass_door","pneumaticcraft:compressed_stone_slab","mcwfurnitures:stripped_acacia_chair","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","mcwdoors:spruce_waffle_door","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","botania:conversions/brown_petal_block_deconstruct","mcwbridges:acacia_bridge_pier","allthecompressed:compress/grass_block_1x","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwbiomesoplenty:hellbark_classic_door","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","botania:yellow_pavement","mcwbiomesoplenty:dead_glass_door","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","twigs:silt_from_silt_balls","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","pneumaticcraft:logistics_core","create:crafting/kinetics/yellow_seat","mcwbiomesoplenty:willow_modern_door","mcwpaths:andesite_windmill_weave_path","comforts:hammock_blue","mcwbiomesoplenty:palm_glass_door","modularrouters:blank_upgrade","mcwdoors:spruce_swamp_door","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","mcwbiomesoplenty:jacaranda_pane_window","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","cfm:stripped_spruce_cabinet","minecraft:mangrove_boat","mcwdoors:bamboo_whispering_door","minecraft:bread","mcwroofs:green_terracotta_steep_roof","mcwdoors:spruce_barn_glass_door","mcwbiomesoplenty:redwood_western_door","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwfurnitures:spruce_coffee_table","mcwbiomesoplenty:dead_classic_door","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwbiomesoplenty:jacaranda_plank_four_window","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwbiomesoplenty:umbran_swamp_door","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","mcwfurnitures:acacia_desk","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","ae2:smelting/smooth_sky_stone_block","mcwwindows:stone_brick_arrow_slit","mcwfurnitures:jungle_desk","mcwbiomesoplenty:mahogany_cottage_door","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","mcwbiomesoplenty:umbran_waffle_door","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","ad_astra:space_suit","securitycraft:disguise_module","mcwbiomesoplenty:willow_swamp_door","mcwwindows:deepslate_four_window","mcwdoors:acacia_glass_door","pneumaticcraft:camo_applicator","mcwwindows:dark_oak_louvered_shutter","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwfurnitures:oak_covered_desk","mcwbiomesoplenty:fir_stable_head_door","mcwbiomesoplenty:redwood_barn_door","mcwtrpdoors:acacia_four_panel_trapdoor","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mcwfences:birch_picket_fence","mcwbiomesoplenty:palm_modern_door","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwdoors:bamboo_barn_door","mcwbiomesoplenty:maple_cottage_door","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","mcwbiomesoplenty:empyreal_modern_door","pneumaticcraft:logistics_frame_passive_provider_self","mcwdoors:jungle_waffle_door","mcwroofs:black_roof_block","mcwbiomesoplenty:hellbark_pane_window","create:crafting/kinetics/wrench","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","create:crafting/kinetics/propeller","minecraft:allthemodium_mage_leggings_smithing","mcwfurnitures:stripped_acacia_large_drawer","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","modularrouters:dropper_module","immersiveengineering:crafting/fluid_pipe","mcwbiomesoplenty:magic_highley_gate","ae2:tools/fluix_sword","ad_astra:ostrum_fluid_pipe","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","dyenamics:mint_terracotta","mcwwindows:pink_curtain","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","delightful:knives/refined_obsidian_knife","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwdoors:bamboo_swamp_door","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","minecraft:bow","supplementaries:wrench","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","mcwroofs:blue_terracotta_upper_steep_roof","deepresonance:radiation_suit_boots","cfm:acacia_kitchen_counter","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","minecraft:andesite_stairs","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwdoors:bamboo_japanese_door","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:spruce_glass_table","mcwfurnitures:acacia_large_drawer","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","sophisticatedbackpacks:feeding_upgrade","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:pine_stable_door","minecraft:granite","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","create:crafting/kinetics/clutch","mcwfurnitures:spruce_covered_desk","cfm:stripped_acacia_desk","minecraft:firework_rocket_simple","create:crafting/kinetics/encased_fan","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","mcwbiomesoplenty:magic_barn_glass_door","mcwdoors:spruce_mystic_door","mcwtrpdoors:spruce_beach_trapdoor","mcwbiomesoplenty:redwood_mystic_door","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwbiomesoplenty:fir_bamboo_door","mcwroofs:andesite_roof","botania:black_pavement","rftoolsbase:infused_enderpearl","travelersbackpack:blaze","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","ae2:shaped/stairs/sky_stone_block","ae2:network/cables/glass_yellow","botania:quartz_blaze","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","mcwpaths:sandstone_flagstone_path","mcwfurnitures:stripped_acacia_wardrobe","dyenamics:rose_terracotta","farmersdelight:cooking/apple_cider","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","ad_astra:ostrum_ingot_from_ostrum_block","minecraft:stone","twigs:lamp","mcwwindows:orange_curtain","mcwfurnitures:stripped_acacia_modern_desk","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","railcraft:iron_tank_valve","minecraft:blackstone_stairs","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","ad_astra:calorite_tank","dyenamics:icy_blue_wool","securitycraft:speed_module","comforts:sleeping_bag_blue","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","cfm:stripped_oak_kitchen_sink_dark","botania:mana_ring_greater","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","mcwroofs:brown_terracotta_steep_roof","tombstone:carmin_marble","botania:elf_quartz","create:andesite_bars_from_andesite_alloy_stonecutting","mcwdoors:oak_barn_door","botania:alchemy_catalyst","cfm:cyan_kitchen_drawer","mcwfurnitures:spruce_double_drawer_counter","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","create:crafting/kinetics/display_board","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","mcwpaths:sandstone_windmill_weave_slab","minecraft:smooth_stone","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","cfm:acacia_upgraded_fence","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","create:crafting/kinetics/mechanical_press","botania:hourglass","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","alltheores:lead_plate","additionallanterns:obsidian_chain","minecraft:ender_eye","mcwfurnitures:stripped_spruce_coffee_table","supplementaries:blackstone_lamp","mcwfurnitures:spruce_double_wardrobe","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","mcwtrpdoors:acacia_ranch_trapdoor","mcwlights:cross_lantern","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","immersiveengineering:crafting/hemp_fabric","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","ae2:network/parts/terminals_pattern_access","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","ae2:network/blocks/io_condenser","ae2:block_cutter/walls/quartz_wall","sophisticatedbackpacks:smoking_upgrade","railcraft:steel_tank_wall","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","enderio:dark_steel_trapdoor","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","advanced_ae:quantumstorage128","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","mcwroofs:jungle_lower_roof","railcraft:iron_tank_gauge","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:age_detector","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","botania:drum_gathering","mcwwindows:jungle_plank_window2","ad_astra:space_pants","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","productivebees:hives/advanced_dark_oak_canvas_hive","mcwbiomesoplenty:mahogany_japanese_door","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","utilitarian:utility/acacia_logs_to_pressure_plates","mcwbiomesoplenty:umbran_four_panel_door","enderio:resetting_lever_five_inv","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:redwood_beach_door","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","alltheores:osmium_ingot_from_ore_blasting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","appmek:portable_chemical_storage_cell_64k","ae2:network/cells/item_storage_components_cell_1k_part","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","railcraft:steel_chestplate","supplementaries:daub","minecraft:netherite_scrap","alltheores:zinc_dust_from_hammer_crushing","mcwfurnitures:stripped_jungle_end_table","create:crafting/kinetics/wooden_bracket","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwdoors:spruce_japanese2_door","ad_astra:jet_suit_pants","enderio:fluid_tank","mcwroofs:oak_planks_roof","alltheores:tin_dust_from_hammer_ingot_crushing","arseng:portable_source_cell_1k","ad_astra:iron_rod","ae2:decorative/quartz_block","mcwbiomesoplenty:magic_cottage_door","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","botania:mana_tablet","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","ad_astra:desh_fluid_pipe","allthearcanistgear:unobtainium_boots_smithing","travelersbackpack:standard_no_tanks","cfm:white_kitchen_sink","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","megacells:cells/portable/portable_item_cell_256m","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","minecraft:acacia_wood","mcwbiomesoplenty:maple_tropical_door","create:cut_tuff_from_stone_types_tuff_stonecutting","pneumaticcraft:logistics_configurator","mcwbiomesoplenty:mahogany_beach_door","mcwtrpdoors:print_tropical","pneumaticcraft:logistics_frame_default_storage_self","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["botania:star_sword","botania:dreamwood_planks","mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","croptopia:mead","botania:alfheim_portal","mcwfurnitures:stripped_jungle_desk","mcwfurnitures:stripped_acacia_counter","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwdoors:bamboo_stable_door","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","botania:ender_hand","minecraft:stonecutter","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwbiomesoplenty:willow_waffle_door","mcwroofs:lime_terracotta_lower_roof","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","mcwtrpdoors:acacia_barrel_trapdoor","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mcwfurnitures:stripped_acacia_bookshelf_cupboard","mythicbotany:central_rune_holder","sophisticatedbackpacks:void_upgrade","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","railcraft:bushing_gear_bronze","dyenamics:maroon_candle","mcwroofs:acacia_upper_steep_roof","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","buildinggadgets2:gadget_cut_paste","mcwtrpdoors:acacia_barn_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","mcwfurnitures:spruce_modern_desk","mcwfurnitures:spruce_drawer","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","handcrafted:deepslate_corner_trim","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","create:crafting/kinetics/gearbox","botania:terrasteel_block","mcwbiomesoplenty:umbran_modern_door","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","arseng:portable_source_cell_64k","rftoolsutility:screen_link","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","mcwbiomesoplenty:redwood_nether_door","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","comforts:sleeping_bag_to_blue","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:bubble_blower","naturalist:glow_goop","supplementaries:crank","minecraft:recovery_compass","ad_astra:calorite_panel","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","supplementaries:netherite_trapdoor","productivebees:stonecutter/fir_canvas_expansion_box","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","cfm:acacia_cabinet","mcwbiomesoplenty:willow_bark_glass_door","minecraft:pink_terracotta","aiotbotania:livingrock_axe","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","mcwbiomesoplenty:empyreal_classic_door","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwtrpdoors:spruce_barred_trapdoor","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","dyenamics:aquamarine_concrete_powder","allthecompressed:compress/energized_steel_block_1x","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mekanism:energy_tablet","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","productivebees:hives/advanced_cherry_canvas_hive","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","botania:vial","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:golden_hoe","cfm:oak_kitchen_counter","minecraft:fire_charge","mcwfurnitures:acacia_lower_triple_drawer","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","cfm:stripped_spruce_bedside_cabinet","railcraft:any_detector","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","mcwtrpdoors:bamboo_mystic_trapdoor","minecraft:stone_button","enderio:dark_steel_door","mcwbiomesoplenty:redwood_bark_glass_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","minecraft:sandstone","mcwwindows:birch_plank_window","ae2:network/cells/spatial_storage_cell_2_cubed_storage","mcwbiomesoplenty:stripped_dead_log_window2","pneumaticcraft:logistics_frame_active_provider_self","minecraft:lodestone","mcwbiomesoplenty:umbran_japanese2_door","appbot:portable_mana_storage_cell_4k","minecraft:purpur_block","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwpaths:sandstone_running_bond_stairs","mcwdoors:spruce_four_panel_door","create:crafting/kinetics/speedometer","domum_ornamentum:blue_brick_extra","cfm:stripped_spruce_chair","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:dried_kelp_block","minecraft:quartz","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","productivebees:expansion_boxes/expansion_box_acacia_canvas","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:crafting/kinetics/mechanical_roller","productivetrees:sawdust_to_paper_water_bottle","minecraft:redstone_from_blasting_redstone_ore","create:industrial_iron_block_from_ingots_iron_stonecutting","quark:building/crafting/furnaces/deepslate_furnace","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","cfm:stripped_crimson_mail_box","tombstone:grave_plate","handcrafted:jungle_side_table","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:hellbark_beach_door","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","mcwbiomesoplenty:umbran_classic_door","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","ae2:network/parts/terminals","securitycraft:keypad_frame","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","botania:terrasteel_leggings","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","cfm:spruce_bedside_cabinet","mcwbiomesoplenty:hellbark_bark_glass_door","botania:incense_stick","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","ae2:network/blocks/energy_energy_acceptor","mcwbiomesoplenty:pine_plank_window2","pneumaticcraft:vortex_tube","xnet:connector_routing","mcwwindows:warped_louvered_shutter","blue_skies:glowing_nature_stone","minecraft:blue_carpet","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:palm_plank_window2","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","mcwroofs:green_terracotta_attic_roof","productivebees:stonecutter/palm_canvas_expansion_box","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:palm_bark_glass_door","mcwbiomesoplenty:umbran_cottage_door","utilitix:directional_highspeed_rail","mekanism:robit","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","immersiveengineering:crafting/blastbrick","dyenamics:ultramarine_candle","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","mcwfurnitures:stripped_spruce_double_drawer","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","mcwbiomesoplenty:pine_stable_head_door","create:crafting/kinetics/chute","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","dyenamics:fluorescent_candle","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","cfm:stripped_acacia_park_bench","ae2:network/blocks/storage_chest","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:iron_hoe","aether:crossbow_repairing","minecraft:iron_ingot_from_iron_block","handcrafted:blue_cushion","mcwroofs:light_gray_roof_slab","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwbiomesoplenty:palm_japanese2_door","mcwroofs:thatch_roof","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","dyenamics:lavender_candle","dyenamics:fluorescent_wool","ae2:tools/portable_fluid_cell_256k","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","mcwtrpdoors:bamboo_tropical_trapdoor","sophisticatedbackpacks:filter_upgrade","pneumaticcraft:gun_ammo_freezing","mcwbiomesoplenty:maple_japanese_door","ad_astra:ostrum_plateblock","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","minecraft:iron_ingot_from_nuggets","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","ad_astra:smelting/cheese_from_smelting_moon_cheese_ore","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","mcwfurnitures:acacia_stool_chair","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","productivebees:hives/advanced_oak_canvas_hive","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","minecraft:gray_bed","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","mcwfurnitures:stripped_acacia_modern_chair","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","cfm:stripped_birch_kitchen_drawer","mcwfurnitures:stripped_acacia_lower_triple_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","twigs:chiseled_bricks","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","allthecompressed:decompress/end_stone_5x","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwroofs:spruce_top_roof","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwbiomesoplenty:willow_beach_door","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","croptopia:potato_chips","minecraft:chiseled_stone_bricks_stone_from_stonecutting","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","minecraft:netherite_sword_smithing","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwtrpdoors:acacia_cottage_trapdoor","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwbiomesoplenty:empyreal_beach_door","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/gunpart_barrel","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","mcwbiomesoplenty:mahogany_swamp_door","ad_astra:energizer","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","minecraft:gray_banner","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","mcwfurnitures:stripped_spruce_glass_table","cfm:stripped_acacia_kitchen_counter","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","supplementaries:flags/flag_light_blue","mcwbiomesoplenty:maple_modern_door","ad_astra:smelting/desh_ingot_from_smelting_raw_desh","mcwpaths:cobbled_deepslate_running_bond","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","mcwbiomesoplenty:empyreal_cottage_door","utilitix:tiny_charcoal_to_tiny","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","appflux:smelting/harden_resin","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","botania:terrasteel_boots","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","minecraft:polished_blackstone_from_blackstone_stonecutting","mcwpaths:brick_running_bond","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:engine_frame","mcwwindows:hammer","ad_astra:iron_panel","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwroofs:gray_attic_roof","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","ae2:tools/portable_item_cell_256k","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwroofs:purple_terracotta_upper_lower_roof","mcwfurnitures:spruce_stool_chair","mcwbiomesoplenty:jacaranda_nether_door","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","mcwfurnitures:acacia_counter","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","blue_skies:cake_compat","utilitarian:utility/acacia_logs_to_doors","cfm:stripped_acacia_chair","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwdoors:spruce_stable_head_door","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","productivebees:stonecutter/aspen_canvas_expansion_box","railcraft:iron_tank_wall","pneumaticcraft:liquid_hopper","botania:green_pavement","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","immersiveengineering:crafting/string","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","ad_astra:ti_69","mcwroofs:acacia_attic_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","mcwbiomesoplenty:mahogany_nether_door","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:fir_bark_glass_door","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","botania:petal_brown","create:deepslate_from_stone_types_deepslate_stonecutting","cfm:stripped_oak_chair","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","cfm:rock_path","minecraft:wooden_hoe","mekanismgenerators:generator/heat","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","mcwbiomesoplenty:redwood_waffle_door","botania:slingshot","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","botanypots:botanypots/crafting/terracotta_hopper_botany_pot","productivebees:stonecutter/jungle_canvas_hive","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","mcwbiomesoplenty:willow_barn_door","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_base_green","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","mcwbiomesoplenty:umbran_nether_door","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","minecraft:piston","mcwwindows:crimson_planks_four_window","ad_astra:large_gas_tank","mcwfurnitures:jungle_coffee_table","mcwbiomesoplenty:maple_waffle_door","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","undergarden:regalium_block","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwdoors:bamboo_four_panel_door","mcwtrpdoors:oak_glass_trapdoor","railcraft:villager_detector","mcwbiomesoplenty:willow_four_window","supplementaries:sign_post_jungle","mcwbiomesoplenty:fir_stable_door","mcwbiomesoplenty:dead_modern_door","mcwroofs:gray_upper_steep_roof","botania:third_eye","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","mcwbiomesoplenty:palm_classic_door","mcwdoors:acacia_cottage_door","croptopia:apple_sapling","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","handcrafted:oak_shelf","handcrafted:stackable_book","mcwbiomesoplenty:magic_modern_door","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","silentgear:sinew_fiber","handcrafted:gray_cushion","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","mcwtrpdoors:spruce_mystic_trapdoor","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","create:crafting/appliances/clipboard","ad_astra:space_helmet","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","ad_astra:desh_cable","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","mcwbiomesoplenty:palm_tropical_door","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwlights:red_paper_lamp","mcwbiomesoplenty:palm_four_window","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","mcwbiomesoplenty:palm_cottage_door","productivebees:stonecutter/oak_canvas_expansion_box","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","utilitarian:utility/spruce_logs_to_pressure_plates","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","enderio:dark_steel_nugget_to_ingot","create:crafting/curiosities/minecart_coupling","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","productivebees:expansion_boxes/expansion_box_crimson_canvas","ad_astra:netherite_space_suit","pneumaticcraft:pressure_chamber_interface","mcwbiomesoplenty:stripped_umbran_log_window","xnet:antenna","mcwfurnitures:jungle_stool_chair","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","dyenamics:persimmon_candle","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","ad_astra:netherite_space_helmet","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","botania:petal_orange_double","mcwbiomesoplenty:umbran_barn_glass_door","pylons:expulsion_pylon","mcwfurnitures:acacia_double_wardrobe","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","mcwroofs:red_terracotta_attic_roof","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwroofs:jungle_roof","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","minecraft:torch","ad_astra:gray_flag","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","create:crafting/kinetics/black_seat","cfm:oak_desk","ad_astra:encased_ostrum_block","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwbiomesoplenty:dead_bark_glass_door","mcwbiomesoplenty:fir_paper_door","mcwbiomesoplenty:pine_mystic_door","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","travelersbackpack:gray_sleeping_bag","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:fir_japanese_door","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbridges:dry_bamboo_bridge_pier","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","ad_astra:wrench","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","minecraft:stripped_acacia_wood","mcwbiomesoplenty:mahogany_stockade_fence","botania:brown_petal_block","mcwfurnitures:stripped_acacia_bookshelf_drawer","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","mcwroofs:sandstone_lower_roof","domum_ornamentum:brown_bricks","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","cfm:red_cooler","botania:dreamwood_wall","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","cfm:acacia_desk_cabinet","ae2:decorative/quartz_fixture","mcwbiomesoplenty:dead_nether_door","cfm:acacia_blinds","domum_ornamentum:green_floating_carpet","mcwwindows:cherry_plank_window2","mcwbiomesoplenty:dead_paper_door","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","botania:dye_orange","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","forbidden_arcanus:clibano_combustion/coal_from_clibano_combusting","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","create:crafting/kinetics/andesite_door","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_acacia_glass_table","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","immersiveengineering:crafting/cokebrick_to_slab","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","croptopia:shaped_fish_and_chips","mcwfurnitures:acacia_chair","mcwbiomesoplenty:palm_nether_door","enderio:dark_steel_nugget","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","cfm:blue_sofa","railcraft:whistle_tuner","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:fir_western_door","botania:blaze_block","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwfurnitures:jungle_striped_chair","bambooeverything:bamboo_torch","dyenamics:honey_wool","mcwroofs:acacia_top_roof","xnet:advanced_connector_yellow_dye","productivebees:stonecutter/magic_canvas_expansion_box","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","mcwfurnitures:acacia_glass_table","securitycraft:reinforced_purple_stained_glass_pane_from_dye","railways:crafting/track_switch_andesite","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","cfm:stripped_acacia_table","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","mcwbiomesoplenty:maple_mystic_door","mekanism:processing/osmium/raw/from_raw_block","expatternprovider:silicon_block","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","itemcollectors:basic_collector","minecraft:loom","ad_astra:steel_engine","enderio:resetting_lever_three_hundred_inv","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwbiomesoplenty:mahogany_stable_head_door","appbot:portable_mana_storage_cell_16k","mcwroofs:pink_terracotta_steep_roof","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","ad_astra:water_pump","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","minecraft:polished_blackstone","utilitarian:utility/spruce_logs_to_stairs","advanced_ae:quantumaccel","botania:crafting_halo","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","ad_astra:fluid_pipe_duct","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:dry_bamboo_bridge","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","travelersbackpack:horse","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","cfm:brown_cooler","delightful:knives/terra_knife","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","appbot:portable_mana_storage_cell_1k","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","productivebees:hives/advanced_snake_block_canvas_hive","dyenamics:amber_candle","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","comforts:sleeping_bag_to_gray","allthemodium:ancient_stone_brick_slabs","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","minecraft:respawn_anchor","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","mcwfurnitures:spruce_desk","pneumaticcraft:wall_lamp_red","mcwbridges:jungle_bridge_pier","alltheores:signalum_dust_from_alloy_blending","mcwbiomesoplenty:mahogany_modern_door","immersiveengineering:crafting/component_steel","utilitarian:utility/acacia_logs_to_boats","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","comforts:hammock_to_blue","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","sophisticatedbackpacks:compacting_upgrade","mcwtrpdoors:spruce_swamp_trapdoor","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","farmersdelight:kelp_roll","mcwpaths:blackstone_flagstone","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","ae2:decorative/quartz_vibrant_glass","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:sandstone_stairs","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","allthemodium:ancient_stone_brick_wall","mcwbiomesoplenty:palm_barn_glass_door","cfm:acacia_chair","mcwwindows:oak_window","megacells:cells/standard/fluid_storage_cell_256m","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","domum_ornamentum:cyan_floating_carpet","minecraft:coarse_dirt","mcwwindows:spruce_plank_pane_window","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","botania:world_seed","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","minecraft:gold_ingot_from_gold_block","mcwwindows:acacia_louvered_shutter","mcwdoors:print_whispering","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","create:crafting/appliances/dough","cfm:light_gray_cooler","occultism:crafting/demons_dream_essence_from_seeds","mcwroofs:oak_upper_steep_roof","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gray_terracotta_attic_roof","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","mcwfurnitures:stripped_spruce_stool_chair","enderio:silent_oak_pressure_plate","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwroofs:bricks_attic_roof","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","botania:orange_petal_block","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","ad_astra:airlock","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","croptopia:egg_roll","mcwbiomesoplenty:dead_waffle_door","twigs:bamboo_mat","railcraft:iron_gear","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","mcwfurnitures:stripped_acacia_drawer_counter","twigs:silt_brick","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","create:crafting/kinetics/mechanical_mixer","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","mcwbiomesoplenty:pine_bamboo_door","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","create:framed_glass_from_glass_colorless_stonecutting","botania:quartz_sunny","mcwbiomesoplenty:stripped_redwood_log_window","productivebees:stonecutter/maple_canvas_expansion_box","mcwfurnitures:stripped_acacia_cupboard_counter","botania:terrasteel_helmet","mcwbiomesoplenty:dead_swamp_door","railcraft:signal_lamp","megacells:cells/standard/fluid_storage_cell_256m_with_housing","ad_astra:desh_panel","ad_astra:smelting/calorite_ingot_from_smelting_raw_calorite","cfm:jungle_blinds","tombstone:bone_needle","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:redwood_classic_door","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwroofs:white_top_roof","mcwwindows:blackstone_window2","botania:light_blue_petal_block","mcwbiomesoplenty:magic_waffle_door","additionallanterns:warped_lantern","ad_astra:calorite_plating","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","botania:floating_pure_daisy","handcrafted:jungle_counter","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","mcwbiomesoplenty:redwood_cottage_door","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","alltheores:silver_ingot_from_ore_blasting","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","cfm:acacia_bedside_cabinet","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","botania:conversions/terrasteel_block_deconstruct","cfm:spruce_kitchen_counter","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwroofs:light_gray_terracotta_roof","ad_astra:gas_tank","minecraft:copper_ingot_from_blasting_raw_copper","mcwdoors:bamboo_western_door","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","bambooeverything:dry_bamboo","mcwlights:golden_small_chandelier","mcwbiomesoplenty:willow_paper_door","mcwwindows:lime_mosaic_glass","railways:stonecutting/riveted_locometal","securitycraft:reinforced_observer","sophisticatedstorage:oak_barrel","productivebees:stonecutter/warped_canvas_hive","cfm:crimson_mail_box","mcwdoors:spruce_nether_door","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","mcwfences:acacia_curved_gate","ae2:network/parts/panels_semi_dark_monitor","allthemodium:unobtainium_rod","supplementaries:deepslate_lamp","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","mcwbiomesoplenty:redwood_swamp_door","farmersdelight:cooking/baked_cod_stew","immersiveengineering:crafting/wirecoil_structure_rope","mcwfurnitures:oak_bookshelf","alltheores:lead_rod","mcwbiomesoplenty:jacaranda_western_door","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","mcwfurnitures:stripped_acacia_modern_wardrobe","ad_astra:iron_plateblock","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","alltheores:platinum_dust_from_hammer_ingot_crushing","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwdoors:acacia_beach_door","ad_astra:calorite_engine","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwbridges:bamboo_bridge_pier","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","productivebees:hives/advanced_jungle_canvas_hive","ad_astra:oxygen_sensor","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","farmersdelight:steak_and_potatoes","mcwtrpdoors:spruce_ranch_trapdoor","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","botania:itemfinder","mcwbiomesoplenty:umbran_plank_window2","utilitarian:utility/acacia_logs_to_stairs","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","cfm:acacia_coffee_table","mcwroofs:green_striped_awning","mcwpaths:sandstone_crystal_floor_stairs","pneumaticcraft:wall_lamp_inverted_blue","allthecompressed:compress/honey_block_1x","securitycraft:codebreaker","rftoolsutility:energy_module","supplementaries:flute","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","constructionwand:infinity_wand","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","botania:rainbow_rod","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","comforts:hammock_to_gray","minecraft:redstone_from_smelting_redstone_ore","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","cfm:blue_kitchen_counter","ae2:network/blocks/interfaces_interface","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","mcwwindows:stripped_oak_log_window","mcwdoors:bamboo_classic_door","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwdoors:bamboo_cottage_door","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","supplementaries:statue","mcwbiomesoplenty:mahogany_tropical_door","cfm:pink_grill","ad_astra:jet_suit_boots","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","ad_astra:raw_desh_block","enderio:dark_steel_grinding_ball","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","botania:aura_ring_greater","mcwbiomesoplenty:maple_stable_head_door","rftoolsutility:counterplus_module","mcwroofs:white_terracotta_upper_steep_roof","mcwbiomesoplenty:dead_plank_window","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","ae2:network/cells/spatial_components_0","create:crafting/kinetics/mechanical_saw","minecraft:stick_from_bamboo_item","alltheores:bronze_plate","create:crafting/logistics/andesite_funnel","mcwpaths:brick_crystal_floor_slab","botania:swap_ring","mcwwindows:jungle_log_parapet","create:crafting/kinetics/depot","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","mcwfurnitures:acacia_double_drawer","aquaculture:tin_can_to_iron_nugget","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","productivebees:stonecutter/umbran_canvas_expansion_box","mcwpaths:blackstone_basket_weave_paving","allthecompressed:compress/acacia_log_1x","pneumaticcraft:pressure_chamber_wall","modularrouters:player_module","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:pine_classic_door","mcwdoors:spruce_paper_door","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","ae2:tools/portable_fluid_cell_64k","mcwroofs:brown_concrete_attic_roof","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwbiomesoplenty:pine_cottage_door","alltheores:tin_dust_from_hammer_crushing","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","dyenamics:persimmon_concrete_powder","botania:light_relay","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","minecraft:light_blue_concrete_powder","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","enderio:dark_steel_ladder","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","mcwbiomesoplenty:stripped_maple_pane_window","botania:spawner_claw","rftoolsstorage:storage_control_module","chimes:bamboo_chimes","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwbiomesoplenty:pine_nether_door","cfm:oak_upgraded_fence","mcwbiomesoplenty:hellbark_barn_glass_door","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","botania:placeholder","railcraft:steel_gear","delightful:knives/refined_glowstone_knife","ad_astra:encased_iron_block","allthemodium:piglich_heart_block","bigreactors:smelting/graphite_from_charcoal","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","xnet:netcable_green_dye","utilitarian:utility/acacia_logs_to_slabs","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","mcwpaths:andesite_flagstone","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","mcwbiomesoplenty:jacaranda_bamboo_door","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","alltheores:tin_ingot_from_ore_blasting","mcwbiomesoplenty:redwood_four_panel_door","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","pylons:potion_filter","enderio:resetting_lever_thirty","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","littlelogistics:energy_tug","pneumaticcraft:wall_lamp_light_gray","create:crafting/kinetics/contraption_controls","ad_astra:steel_factory_block","mcwwindows:acacia_log_parapet","mcwfurnitures:stripped_spruce_triple_drawer","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwroofs:light_gray_concrete_lower_roof","alltheores:bronze_rod","occultism:crafting/spirit_torch","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","productivebees:expansion_boxes/expansion_box_cherry_canvas","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","mcwfurnitures:acacia_table","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","allthecompressed:compress/tuff_1x","create:crafting/kinetics/sail_framefrom_conversion","mcwtrpdoors:bamboo_blossom_trapdoor","mcwroofs:gray_terracotta_upper_steep_roof","botania:mana_glass_pane","mcwbiomesoplenty:fir_tropical_door","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","aquaculture:heavy_hook","aether:netherite_sword_repairing","mcwbiomesoplenty:palm_western_door","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","mcwwindows:acacia_window","croptopia:food_press","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","ad_astra:photovoltaic_etrium_cell","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwfurnitures:oak_chair","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","ae2:network/cables/glass_green","mcwbiomesoplenty:willow_classic_door","ae2:tools/fluix_hoe","botania:terrasteel_chestplate","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","ad_astra:blasting/cheese_from_blasting_moon_cheese_ore","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","pneumaticcraft:reinforced_brick_pillar","mcwwindows:dark_oak_pane_window","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","mcwpaths:brick_crystal_floor","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","mcwdoors:acacia_swamp_door","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","mcwpaths:cobbled_deepslate_honeycomb_paving","productivebees:hives/advanced_birch_canvas_hive","megacells:crafting/bulk_cell_component","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","ae2:network/cables/glass_fluix","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","allthecompressed:compress/moss_block_1x","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","mcwdoors:bamboo_beach_door","mcwwindows:end_brick_gothic","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwdoors:spruce_modern_door","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:iron_ring","rftoolsutility:module_template","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwbiomesoplenty:dead_beach_door","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","mcwfurnitures:stripped_acacia_covered_desk","minecraft:popped_chorus_fruit","mcwbiomesoplenty:fir_barn_glass_door","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","botanypots:botanypots/crafting/terracotta_botany_pot","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwdoors:bamboo_bark_glass_door","mcwroofs:stone_bricks_top_roof","mcwwindows:crimson_plank_pane_window","ad_astra:calorite_sliding_door","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","utilitarian:utility/spruce_logs_to_boats","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwbiomesoplenty:hellbark_stable_head_door","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:jacaranda_swamp_door","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","mcwroofs:gutter_base_red","mcwtrpdoors:acacia_glass_trapdoor","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","productivebees:stonecutter/concrete_canvas_hive","mcwdoors:print_bamboo","createoreexcavation:vein_finder","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","mcwtrpdoors:spruce_tropical_trapdoor","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","mcwtrpdoors:print_cottage","mcwfurnitures:acacia_modern_desk","minecraft:copper_ingot_from_smelting_raw_copper","sophisticatedbackpacks:chipped/carpenters_table_upgrade","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","minecraft:netherite_ingot","naturalist:bug_net","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","mcwbiomesoplenty:jacaranda_mystic_door","sophisticatedstorage:oak_limited_barrel_4","domum_ornamentum:beige_bricks","securitycraft:reinforced_black_stained_glass","dyenamics:navy_terracotta","supplementaries:hourglass","mcwfurnitures:acacia_end_table","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","mekanism:metallurgic_infuser","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","cfm:stripped_spruce_table","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","minecraft:jungle_sign","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","botania:conversions/light_blue_petal_block_deconstruct","railcraft:steel_tank_valve","cfm:cyan_cooler","ae2:network/parts/formation_plane","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","ad_astra:steel_rod","mcwdoors:acacia_modern_door","mcwbiomesoplenty:mahogany_mystic_door","create:crafting/kinetics/whisk","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","create:crafting/kinetics/mechanical_drill","mcwroofs:light_gray_roof","mcwbiomesoplenty:umbran_beach_door","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","dyenamics:bubblegum_candle","create:crafting/kinetics/weighted_ejector","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:umbran_bark_glass_door","mcwdoors:bamboo_tropical_door","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","mcwtrpdoors:mangrove_ranch_trapdoor","travelersbackpack:redstone","mcwfurnitures:stripped_acacia_end_table","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","minecraft:slime_block","minecraft:acacia_planks","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwbiomesoplenty:fir_nether_door","chimes:glass_bells","mcwwindows:iron_shutter","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","aether:diamond_chestplate_repairing","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","farmersdelight:cutting_board","bigreactors:reactor/reinforced/controller_ingots_uranium","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","appbot:portable_mana_storage_cell_64k","ad_astra:nasa_workbench","mcwpaths:sandstone_flagstone","ad_astra:etrionic_capacitor","mcwbiomesoplenty:empyreal_japanese2_door","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","mcwbridges:blackstone_bridge_pier","mcwbiomesoplenty:empyreal_swamp_door","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","mcwfurnitures:stripped_spruce_large_drawer","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","appmek:portable_chemical_storage_cell_16k","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:magenta_terracotta_attic_roof","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","mcwfurnitures:stripped_acacia_coffee_table","alltheores:copper_rod","productivebees:stonecutter/cherry_canvas_hive","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","botania:dragonstone_block","minecraft:white_carpet","utilitix:filter_rail","aether:diamond_gloves","minecraft:spruce_planks","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","enderio:resetting_lever_sixty_from_inv","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwbiomesoplenty:dead_stable_door","minecraft:mossy_cobblestone_from_moss_block","mcwfurnitures:spruce_triple_drawer","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","gtceu:smelting/smelt_dust_electrum_to_ingot","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","minecraft:magenta_stained_glass","minecraft:ender_chest","mcwbiomesoplenty:willow_western_door","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","alltheores:gold_dust_from_hammer_ingot_crushing","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","create:crafting/kinetics/shaft","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","modularrouters:void_module","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","mysticalagriculture:imperium_essence_uncraft","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","allthecompressed:compress/obsidian_6x","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","mcwfurnitures:spruce_large_drawer","biomesoplenty:maple_boat","appbot:portable_mana_storage_cell_256k","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","ad_astra:desh_nugget","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","securitycraft:reinforced_warped_fence_gate","pneumaticcraft:gun_ammo_weighted","mcwfurnitures:acacia_bookshelf","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","expatternprovider:epp","create:crafting/kinetics/gearshift","simplylight:walllamp","mcwwindows:dark_oak_four_window","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","bloodmagic:synthetic_point","ad_astra:ostrum_panel","croptopia:doughnut","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","alltheores:lumium_ingot_from_dust","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","cfm:stripped_acacia_crate","mcwbiomesoplenty:dead_barn_glass_door","allthecompressed:compress/cobbled_deepslate_1x","productivebees:stonecutter/acacia_canvas_expansion_box","silentgear:stone_torch","handcrafted:terracotta_bowl","littlecontraptions:contraption_barge","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwfurnitures:stripped_acacia_double_wardrobe","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwbiomesoplenty:pine_western_door","utilitarian:utility/acacia_logs_to_trapdoors","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","travelersbackpack:blue_sleeping_bag","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","botania:diluted_pool","modularrouters:energy_output_module","mcwbiomesoplenty:hellbark_japanese_door","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","croptopia:tortilla","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","ae2:network/cables/glass_lime","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","railcraft:animal_detector","mcwbiomesoplenty:palm_stable_door","minecraft:stone_slab_from_stone_stonecutting","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","ad_astra:desh_block","mcwwindows:stripped_jungle_log_window","minecraft:stone_brick_slab_from_stone_stonecutting","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","mcwroofs:spruce_upper_lower_roof","mcwbiomesoplenty:palm_four_panel_door","pneumaticcraft:speed_upgrade","productivebees:stonecutter/grimwood_canvas_expansion_box","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","create:crafting/materials/andesite_alloy_block","minecraft:oak_trapdoor","cfm:spruce_chair","mcwbiomesoplenty:mahogany_japanese2_door","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwtrpdoors:cherry_ranch_trapdoor","create:crafting/kinetics/sticky_mechanical_piston","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","ad_astra:desh_ingot_from_desh_block","mcwbiomesoplenty:dead_hedge","minecraft:honey_bottle","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwdoors:bamboo_barn_glass_door","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","mcwbiomesoplenty:fir_glass_door","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","alltheores:zinc_ingot_from_ore_blasting","create:crafting/kinetics/gantry_shaft","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","mcwbridges:balustrade_blackstone_bridge","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","ae2:network/cables/glass_blue","alltheores:lumium_rod","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","arseng:portable_source_cell_16k","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwbiomesoplenty:mahogany_bamboo_door","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","mcwroofs:brown_terracotta_upper_steep_roof","mcwdoors:oak_swamp_door","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwwindows:nether_brick_gothic","botania:forest_eye","productivetrees:planks/soul_tree_planks","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","mcwbiomesoplenty:willow_mystic_door","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","alltheores:diamond_dust_from_hammer_crushing","mcwbiomesoplenty:pine_modern_door","handcrafted:wither_skeleton_trophy","create:andesite_ladder_from_andesite_alloy_stonecutting","productivebees:hives/advanced_bamboo_canvas_hive","mcwroofs:blue_terracotta_top_roof","mcwtrpdoors:bamboo_whispering_trapdoor","pneumaticcraft:omnidirectional_hopper","mcwtrpdoors:acacia_mystic_trapdoor","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:umbran_paper_door","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwbiomesoplenty:empyreal_stable_door","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","railcraft:diamond_spike_maul","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwroofs:gray_roof_block","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","allthecompressed:compress/blaze_block_1x","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","utilitarian:utility/spruce_logs_to_doors","cfm:stripped_acacia_upgraded_fence","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","comforts:hammock_to_white","create:smelting/zinc_ingot_from_ore","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","create:crafting/kinetics/mechanical_bearing","domum_ornamentum:yellow_floating_carpet","minecraft:blue_banner","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwdoors:jungle_glass_door","mcwdoors:bamboo_nether_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","ae2:decorative/cut_quartz_block","mcwbiomesoplenty:dead_japanese_door","botania:petal_light_blue","mcwbiomesoplenty:jacaranda_tropical_door","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwfurnitures:spruce_modern_chair","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","croptopia:dough","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","cfm:stripped_acacia_cabinet","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","alltheores:invar_rod","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","productivebees:expansion_boxes/expansion_box_bamboo_canvas","botania:dirt_rod","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","mcwfurnitures:stripped_oak_table","rftoolspower:power_core1","pneumaticcraft:logistics_frame_default_storage","aether:wooden_axe_repairing","mcwroofs:white_concrete_upper_steep_roof","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","create:crafting/kinetics/belt_connector","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","simplylight:illuminant_lime_block_on_toggle","ae2:misc/deconstruction_certus_quartz_block","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:thatch_attic_roof","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","mcwpaths:brick_running_bond_path","mcwfurnitures:stripped_acacia_stool_chair","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","botania:mana_pylon","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwwindows:birch_blinds","mcwfurnitures:stripped_oak_striped_chair","cfm:spruce_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","pneumaticcraft:thermopneumatic_processing_plant","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","cfm:acacia_table","mcwpaths:brick_windmill_weave_stairs","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","mcwdoors:spruce_tropical_door","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","advanced_ae:smalladvpatpro","botania:apothecary_livingrock","mcwdoors:oak_whispering_door","alltheores:signalum_ingot_from_dust","mcwwindows:jungle_window2","mcwtrpdoors:bamboo_barn_trapdoor","railcraft:iron_spike_maul","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","alltheores:platinum_ingot_from_ore","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","mcwbiomesoplenty:dead_barn_door","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","railcraft:feed_station","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","mcwfurnitures:spruce_chair","productivebees:expansion_boxes/expansion_box_spruce_canvas","twigs:gravel_bricks","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","domum_ornamentum:roan_stone_bricks","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","immersiveengineering:crafting/plate_uranium_hammering","railcraft:personal_world_spike","mcwdoors:bamboo_paper_door","handcrafted:oak_nightstand","immersiveengineering:crafting/armor_steel_chestplate","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","mcwroofs:thatch_steep_roof","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","mcwfurnitures:acacia_modern_wardrobe","dyenamics:bubblegum_concrete_powder","ad_astra:encased_calorite_block","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","delightful:food/cooking/ender_nectar","merequester:requester","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","ad_astra:ostrum_factory_block","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:fir_four_panel_door","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","supplementaries:slingshot","advanced_ae:quantumunit","allthemodium:allthemodium_plate","mcwdoors:acacia_japanese2_door","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","productivebees:expansion_boxes/expansion_box_mangrove_canvas","everythingcopper:copper_rail","cfm:black_grill","handcrafted:deepslate_pillar_trim","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_four_panel_door","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","ad_astra:desh_sliding_door","dyenamics:rose_candle","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwroofs:bricks_upper_steep_roof","botania:terraform_rod","minecraft:iron_pickaxe","ae2:shaped/not_so_mysterious_cube","mcwwindows:jungle_shutter","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","ae2:network/cables/glass_orange","mcwwindows:granite_window","deepresonance:resonating_plate","mcwfurnitures:stripped_spruce_table","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","create:crafting/kinetics/cart_assembler","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwtrpdoors:bamboo_paper_trapdoor","minecraft:birch_boat","mcwfences:spruce_highley_gate","handcrafted:gray_sheet","cfm:post_box","mcwbiomesoplenty:redwood_stable_head_door","dyenamics:aquamarine_candle","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwtrpdoors:bamboo_classic_trapdoor","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mekanism:teleportation_core","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","mcwbiomesoplenty:empyreal_western_door","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwtrpdoors:bamboo_barred_trapdoor","dyenamics:mint_candle","mcwroofs:acacia_lower_roof","mcwbiomesoplenty:empyreal_japanese_door","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:hellbark_paper_door","cfm:white_kitchen_counter","create:crafting/kinetics/filter","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","mcwfurnitures:acacia_coffee_table","mcwbiomesoplenty:umbran_stable_door","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwbiomesoplenty:willow_stable_head_door","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","botania:dreamwood_twig","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","ad_astra:smelting/ice_shard_from_smelting_mars_ice_shard_ore","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","mcwdoors:bamboo_modern_door","cfm:fridge_dark","chimes:copper_chimes","ad_astra:cryo_freezer","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwtrpdoors:mangrove_bark_trapdoor","mcwbiomesoplenty:palm_plank_window","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mekanism:digital_miner","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","alltheores:steel_dust_from_alloy_blending","immersiveengineering:crafting/earmuffs","mcwroofs:black_terracotta_lower_roof","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","mcwfurnitures:stripped_jungle_double_wardrobe","minecraft:scaffolding","mcwlights:soul_mangrove_tiki_torch","alltheores:sapphire_from_hammer_crushing","mcwbiomesoplenty:fir_cottage_door","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","constructionwand:core_angel","mcwfurnitures:acacia_bookshelf_cupboard","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwbiomesoplenty:willow_glass_door","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","delightful:knives/tin_knife","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","allthemodium:ancient_cracked_stone_bricks_from_crushing","mcwlights:copper_candle_holder","mcwtrpdoors:bamboo_swamp_trapdoor","mcwfurnitures:acacia_covered_desk","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:honey_block","ae2:tools/nether_quartz_spade","mcwbiomesoplenty:mahogany_barn_door","create:crafting/kinetics/large_cogwheel","sophisticatedbackpacks:crafting_upgrade","cfm:stripped_spruce_desk","productivebees:stonecutter/willow_canvas_expansion_box","aquaculture:birch_fish_mount","ae2:network/cells/fluid_storage_cell_1k_storage","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","create:crafting/kinetics/metal_bracket","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","cfm:stripped_acacia_coffee_table","mcwbiomesoplenty:empyreal_barn_glass_door","cfm:white_sofa","tombstone:ankh_of_prayer","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwfences:ornate_metal_fence","ad_astra:jet_suit_helmet","cfm:stripped_acacia_blinds","mcwfences:acacia_highley_gate","aiotbotania:livingrock_sword","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:spruce_roof","create:crafting/kinetics/gantry_carriage","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","pneumaticcraft:heat_sink","mcwbiomesoplenty:stripped_fir_log_four_window","immersiveengineering:crafting/survey_tools","create:crafting/logistics/powered_latch","productivebees:stonecutter/warped_canvas_expansion_box","mcwbiomesoplenty:maple_swamp_door","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","ad_astra:desh_plateblock","aquaculture:double_hook","ae2:shaped/slabs/sky_stone_block","allthemodium:allthemodium_rod","handcrafted:oak_bench","mcwbiomesoplenty:maple_bark_glass_door","create:crafting/materials/andesite_alloy_from_block","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","create:crafting/kinetics/water_wheel","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:maple_stable_door","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwbiomesoplenty:fir_classic_door","mcwpaths:blackstone_crystal_floor_path","mcwbiomesoplenty:empyreal_glass_door","cfm:jungle_cabinet","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwtrpdoors:acacia_beach_trapdoor","mcwroofs:base_roof_block","productivebees:hives/advanced_mangrove_canvas_hive","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","twigs:blackstone_column","mcwfurnitures:acacia_lower_bookshelf_drawer","mcwbiomesoplenty:fir_waffle_door","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","littlecontraptions:barge_assembler","dyenamics:navy_candle","mcwwindows:spruce_blinds","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","create:crafting/kinetics/analog_lever","create:andesite_scaffolding_from_andesite_alloy_stonecutting","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","botania:terra_axe","domum_ornamentum:light_blue_floating_carpet","megacells:cells/standard/item_storage_cell_256m_with_housing","create:crafting/kinetics/sticker","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","ad_astra:ostrum_plating","mcwbiomesoplenty:pine_beach_door","enderio:silent_light_weighted_pressure_plate","ae2:block_cutter/slabs/quartz_slab","appmek:portable_chemical_storage_cell_256k","sophisticatedbackpacks:chipped/glassblower_upgrade","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","botania:petal_brown_double","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","handcrafted:witch_trophy","mcwfurnitures:stripped_oak_counter","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","handcrafted:jungle_drawer","minecraft:oak_pressure_plate","megacells:cells/standard/item_storage_cell_256m","minecraft:stone_brick_stairs","bigreactors:turbine/basic/activefluidport_forge","aether:skyroot_beehive","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","mcwwindows:quartz_window","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","mcwroofs:gutter_base_brown","cfm:acacia_crate","alltheores:uranium_dust_from_hammer_crushing","botania:dye_brown","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","mcwbiomesoplenty:redwood_tropical_door","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","dyenamics:peach_candle","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","minecraft:clock","mcwroofs:red_concrete_top_roof","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","railcraft:lead_gear","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","botania:twig_wand","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwtrpdoors:spruce_whispering_trapdoor","mcwdoors:acacia_western_door","minecraft:diamond_leggings","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","ad_astra:iron_plating","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwbiomesoplenty:maple_paper_door","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","ad_astra:blasting/ice_shard_from_blasting_mars_ice_shard_ore","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","mcwroofs:purple_terracotta_roof","alltheores:tin_rod","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","cfm:acacia_kitchen_sink_dark","botania:keep_ivy","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","mcwbiomesoplenty:magic_western_door","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","cfm:stripped_spruce_crate","twigs:rhyolite","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","create:crafting/kinetics/linear_chassis","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","mcwbiomesoplenty:pine_swamp_door","utilitix:mob_yoinker","domum_ornamentum:light_blue_brick_extra","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","mcwroofs:thatch_top_roof","ad_astra:smelting/ostrum_ingot_from_smelting_raw_ostrum","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","minecraft:lime_concrete_powder","ae2:network/blocks/energy_dense_energy_cell","minecraft:sandstone_wall_from_sandstone_stonecutting","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","mcwlights:festive_lantern","dyenamics:honey_candle","mcwbiomesoplenty:magic_tropical_door","securitycraft:block_change_detector","croptopia:fruit_salad","twigs:rocky_dirt","supplementaries:pancake_fd","advanced_ae:advpatpro2","bloodmagic:sacrificial_dagger","modularrouters:placer_module","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","minecraft:netherite_scrap_from_blasting","mcwbiomesoplenty:dead_bamboo_door","allthemodium:ancient_stone_brick_stairs","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","create:crafting/kinetics/mechanical_piston","mcwtrpdoors:bamboo_four_panel_trapdoor","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwroofs:white_concrete_upper_lower_roof","handcrafted:blue_sheet","mcwfurnitures:oak_drawer_counter","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","botania:dreamwood_slab","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","minecraft:sandstone_slab","mcwwindows:birch_window","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","allthecompressed:compress/spruce_log_1x","mcwbiomesoplenty:pine_bark_glass_door","minecraft:gold_ingot_from_smelting_raw_gold","sophisticatedbackpacks:smithing_upgrade","mcwbiomesoplenty:palm_beach_door","mcwfences:mud_brick_railing_gate","twigs:oak_table","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","pneumaticcraft:heat_pipe","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","comforts:hammock_gray","mcwbiomesoplenty:magic_bamboo_door","mcwfurnitures:oak_lower_triple_drawer","alltheores:uranium_gear","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwfurnitures:stripped_acacia_double_drawer","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","mcwfences:mangrove_wired_fence","minecraft:red_terracotta","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","create:crafting/kinetics/basin","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwpaths:blackstone_dumble_paving","mcwroofs:white_roof_block","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwbiomesoplenty:mahogany_stable_door","securitycraft:block_pocket_manager","create:crafting/kinetics/encased_chain_drive","mcwlights:soul_cherry_tiki_torch","botania:red_pavement","securitycraft:reinforced_mangrove_fence","alltheores:copper_ore_hammer","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","bloodmagic:blood_altar","alltheores:lumium_dust_from_alloy_blending","mcwfurnitures:spruce_bookshelf_cupboard","cfm:purple_kitchen_drawer","ad_astra:steel_plateblock","ad_astra:blasting/calorite_ingot_from_blasting_raw_calorite","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","mcwfurnitures:acacia_triple_drawer","minecraft:netherite_chestplate_smithing","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","mcwfurnitures:stripped_acacia_double_drawer_counter","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","railcraft:bag_of_cement","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","cfm:acacia_desk","mcwwindows:dark_oak_plank_parapet","mcwbiomesoplenty:pine_waffle_door","mcwwindows:spruce_log_parapet","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwfurnitures:stripped_acacia_drawer","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","mekanism:osmium_compressor","immersiveengineering:crafting/alloybrick","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:stripped_spruce_counter","ad_astra:oxygen_distributor","mcwroofs:purple_concrete_roof","botania:apothecary_mossy","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwbiomesoplenty:willow_bamboo_door","eidolon:smooth_stone_masonry_stonecutter_0","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwroofs:spruce_lower_roof","croptopia:apple_pie","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwbiomesoplenty:magic_glass_door","mcwbiomesoplenty:hellbark_tropical_door","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","mcwdoors:spruce_whispering_door","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","deeperdarker:bloom_boat","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwpaths:blackstone_diamond_paving","pneumaticcraft:vortex_cannon","ad_astra:ostrum_engine","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","botania:red_string","mcwroofs:pink_concrete_upper_lower_roof","mcwroofs:acacia_steep_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","allthecompressed:compress/copper_block_1x","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","botania:dye_light_blue","mcwpaths:andesite_flagstone_stairs","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","ad_astra:netherite_space_pants","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","minecraft:bamboo_block","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwdoors:bamboo_waffle_door","mcwfurnitures:stripped_acacia_desk","ad_astra:calorite_plateblock","farmersdelight:cooking/noodle_soup","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","rftoolsutility:moduleplus_template","mcwbiomesoplenty:willow_tropical_door","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","create:crafting/kinetics/white_sail","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","mcwbiomesoplenty:dead_tropical_door","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwbiomesoplenty:jacaranda_paper_door","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","ae2:tools/misctools_entropy_manipulator","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","create:crafting/kinetics/hand_crank","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwtrpdoors:spruce_classic_trapdoor","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","handcrafted:jungle_desk","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","mcwtrpdoors:acacia_swamp_trapdoor","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","mcwbiomesoplenty:fir_modern_door","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","create:crafting/kinetics/portable_storage_interface","minecraft:iron_helmet","ad_astra:steel_trapdoor","botania:red_string_alt","securitycraft:reinforced_pink_stained_glass_pane_from_dye","mcwbiomesoplenty:mahogany_barn_glass_door","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","railcraft:manual_rolling_machine","minecraft:cartography_table","securitycraft:alarm","pneumaticcraft:manometer","ad_astra:launch_pad","mcwbiomesoplenty:dead_japanese2_door","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","cfm:stripped_acacia_desk_cabinet","ae2:tools/fluix_shovel","pneumaticcraft:wall_lamp_inverted_magenta","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","utilitix:crude_furnace","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","mcwfurnitures:stripped_acacia_striped_chair","mcwroofs:gray_steep_roof","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","mekanism:personal_chest","alltheores:iron_ore_hammer","productivebees:stonecutter/yucca_canvas_hive","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","aether:blue_cape_blue_wool","mcwfences:railing_nether_brick_wall","mcwroofs:yellow_terracotta_upper_lower_roof","ae2:network/cables/glass_black","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","botania:polished_livingrock","mcwwindows:warped_stem_window","ad_astra:tier_1_rover","railcraft:invar_gear","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","mcwfurnitures:acacia_cupboard_counter","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","productivebees:expansion_boxes/expansion_box_warped_canvas","ae2:tools/portable_fluid_cell_16k","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwdoors:print_spruce","mcwbiomesoplenty:magic_mystic_door","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","create:crafting/kinetics/piston_extension_pole","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","mcwwindows:oak_window2","mcwfurnitures:stripped_acacia_bookshelf","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","bigreactors:reprocessor/collector","create:crafting/kinetics/lime_seat","mcwbiomesoplenty:empyreal_bamboo_door","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","botania:thunder_sword","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","mcwdoors:acacia_bamboo_door","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","productivebees:stonecutter/yucca_canvas_expansion_box","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","mcwfurnitures:spruce_table","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","botania:temperance_stone","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","botania:petal_orange","mcwbridges:balustrade_bricks_bridge","pneumaticcraft:logistics_frame_storage_self","botania:crystal_bow","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","mcwbiomesoplenty:dead_mystic_door","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","pneumaticcraft:pressure_chamber_valve","delightful:food/cooking/jam_jar","pneumaticcraft:logistics_frame_passive_provider","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","enderio:black_paper","twigs:polished_tuff_stonecutting","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","ae2:network/cables/dense_smart_fluix","mcwbiomesoplenty:pine_japanese_door","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","allthemodium:allthemodium_ingot","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","mcwbiomesoplenty:redwood_glass_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","pneumaticcraft:compressed_iron_leggings","mcwbiomesoplenty:stripped_magic_log_window2","mcwfurnitures:spruce_modern_wardrobe","mcwtrpdoors:spruce_barn_trapdoor","ae2:network/blocks/pattern_providers_interface_part","productivebees:stonecutter/hellbark_canvas_hive","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwbridges:bamboo_bridge","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:umbran_glass_door","botania:terra_pick","botania:brewery","mcwbiomesoplenty:maple_beach_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","mcwbiomesoplenty:magic_four_panel_door","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","mcwfurnitures:jungle_large_drawer","mcwfurnitures:acacia_drawer_counter","botania:monocle","mcwroofs:deepslate_lower_roof","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","mcwbiomesoplenty:hellbark_four_panel_door","ad_astra:desh_plating","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","pneumaticcraft:wall_lamp_lime","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","ae2:network/cables/glass_light_blue","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:acacia_striped_chair","mcwfurnitures:spruce_drawer_counter","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","mcwtrpdoors:bamboo_beach_trapdoor","minecolonies:apple_pie","mcwpaths:stone_running_bond_path","twigs:mossy_bricks_from_moss_block","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","minecraft:andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","create:crafting/appliances/filter_clear","mcwbiomesoplenty:palm_barn_door","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","create:vertical_framed_glass_from_glass_colorless_stonecutting","alltheores:copper_dust_from_hammer_crushing","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","industrialforegoing:plastic","domum_ornamentum:orange_floating_carpet","mcwbiomesoplenty:palm_mystic_door","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","create:crafting/kinetics/goggles","alltheores:osmium_dust_from_hammer_crushing","pneumaticcraft:gun_ammo_ap","mcwbiomesoplenty:maple_japanese2_door","aether:aether_saddle","create:crafting/kinetics/rope_pulley","farmersdelight:wheat_dough_from_water","create:crafting/kinetics/cuckoo_clock","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","ad_astra:zip_gun","croptopia:nougat","rftoolsstorage:storage_module0","immersiveengineering:crafting/fluid_placer","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","alltheores:silver_dust_from_hammer_crushing","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwdoors:acacia_whispering_door","mcwwindows:stripped_spruce_log_window","ad_astra:desh_tank","mcwfurnitures:stripped_jungle_table","mcwbiomesoplenty:jacaranda_cottage_door","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","allthecompressed:decompress/obsidian_5x","additionallanterns:deepslate_bricks_lantern","minecraft:gray_carpet","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","cfm:stripped_jungle_mail_box","mcwfurnitures:spruce_double_drawer","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwtrpdoors:acacia_whispering_trapdoor","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwfurnitures:stripped_spruce_drawer_counter","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","ad_astra:desh_engine","forbidden_arcanus:deorum_lantern","mcwbiomesoplenty:hellbark_glass_door","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","farmersdelight:cooking/glow_berry_custard","twigs:bloodstone","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","productivebees:expansion_boxes/expansion_box_oak_canvas","mcwroofs:orange_concrete_steep_roof","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwwindows:warped_planks_window","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwroofs:acacia_roof","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","arseng:portable_source_cell_256k","mcwbiomesoplenty:empyreal_mystic_door","mekanism:logistical_sorter","mcwroofs:gutter_base_light_blue","create:crafting/kinetics/gearboxfrom_conversion","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","create:crafting/kinetics/orange_seat","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","ad_astra:raw_ostrum_block","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","ad_astra:gravity_normalizer","botania:gravity_rod","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","bigreactors:blasting/graphite_from_coal","ad_astra:jet_suit","sophisticatedbackpacks:stack_upgrade_tier_1","allthecompressed:compress/end_stone_6x","appmek:portable_chemical_storage_cell_1k","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","sophisticatedbackpacks:restock_upgrade","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:wheat_dough_from_eggs","mcwbiomesoplenty:willow_pane_window","croptopia:sweet_crepes","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","deepresonance:filter_material","immersiveengineering:crafting/strip_curtain","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","ae2:tools/certus_quartz_cutting_knife","mcwfurnitures:spruce_counter","ad_astra:blasting/desh_ingot_from_blasting_raw_desh","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwdoors:bamboo_glass_door","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","botania:corporea_spark","productivebees:hives/advanced_acacia_canvas_hive","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","appmek:portable_chemical_storage_cell_4k","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:acacia_paper_trapdoor","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","mcwbiomesoplenty:pine_paper_door","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","cfm:acacia_upgraded_gate","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","botania:clip","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","alltheores:platinum_ingot_from_ore_blasting","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","pneumaticcraft:air_canister","mcwwindows:prismarine_pane_window","mcwpaths:brick_windmill_weave_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","mcwtrpdoors:jungle_barred_trapdoor","twigs:cracked_bricks","supplementaries:slice_map","productivebees:expansion_boxes/expansion_box_jungle_canvas","mcwbridges:brick_bridge_pier","mcwdoors:acacia_mystic_door","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","alltheores:invar_plate","occultism:crafting/butcher_knife","cfm:stripped_spruce_desk_cabinet","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mekanism:control_circuit/advanced","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","botania:lens_normal","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","pneumaticcraft:wall_lamp_inverted_cyan","mcwroofs:spruce_steep_roof","mcwfurnitures:jungle_modern_chair","create:crafting/kinetics/radial_chassis","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","mcwroofs:stone_bricks_roof","cfm:cyan_grill","thermal_extra:smelting/twinite_ingot_from_dust_smelting","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","mcwbiomesoplenty:palm_bamboo_door","ad_astra:raw_calorite_block","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","mcwdoors:acacia_japanese_door","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwdoors:bamboo_japanese2_door","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwtrpdoors:acacia_bark_trapdoor","alltheores:uranium_ingot_from_ore_blasting","mcwbiomesoplenty:hellbark_waffle_door","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","minecraft:polished_andesite","mcwbiomesoplenty:palm_swamp_door","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","ad_astra:iron_factory_block","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwdoors:bamboo_stable_head_door","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","cfm:white_picket_fence","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:acacia_drawer","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","twigs:polished_tuff_bricks_from_tuff_stonecutting","ad_astra:steel_ingot","handcrafted:kitchen_hood","mcwdoors:jungle_bamboo_door","sophisticatedbackpacks:stonecutter_upgrade","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:fir_swamp_door","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","create:crafting/kinetics/super_glue","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:black_lower_roof","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwbiomesoplenty:dead_stable_head_door","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","pneumaticcraft:volume_upgrade","mcwfurnitures:stripped_spruce_bookshelf_drawer","mcwtrpdoors:oak_four_panel_trapdoor","travelersbackpack:netherite_tier_upgrade","bigreactors:reprocessor/fluidinjector","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwroofs:acacia_upper_lower_roof","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwroofs:andesite_lower_roof","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","mcwbridges:andesite_bridge","mcwwindows:dark_prismarine_pane_window","pneumaticcraft:pressure_chamber_valve_x1","mcwbiomesoplenty:redwood_bamboo_door","mcwwindows:oak_plank_parapet","immersiveengineering:crafting/hempcrete","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","minecraft:spruce_wood","mcwpaths:sandstone_clover_paving","ad_astra:ostrum_block","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","ae2:tools/portable_fluid_cell_4k","domum_ornamentum:blue_cobblestone_extra","ad_astra:netherite_space_boots","botania:runic_altar","additionallanterns:blackstone_chain","mcwbiomesoplenty:magic_japanese2_door","domum_ornamentum:purple_brick_extra","mcwbiomesoplenty:hellbark_stable_door","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfurnitures:stripped_acacia_table","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","ae2:tools/fluix_upgrade_smithing_template","botania:petal_light_blue_double","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","mcwtrpdoors:bamboo_trapdoor","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","productivebees:stonecutter/maple_canvas_hive","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwdoors:acacia_barn_glass_door","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","railcraft:coke_oven_bricks","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","handcrafted:jungle_table","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","ad_astra:encased_desh_block","botania:abstruse_platform","create:crafting/kinetics/metal_girder","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","productivebees:stonecutter/dead_canvas_hive","create:crafting/kinetics/millstone","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","ae2:tools/portable_fluid_cell_1k","mcwbiomesoplenty:empyreal_barn_door","productivebees:expansion_boxes/expansion_box_birch_canvas","mcwwindows:diorite_pane_window","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","enderio:resetting_lever_ten_from_prev","mcwtrpdoors:acacia_classic_trapdoor","mcwroofs:magenta_concrete_upper_lower_roof","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","pneumaticcraft:gun_ammo_explosive","mcwbiomesoplenty:hellbark_nether_door","aether:stone_pickaxe_repairing","mcwfurnitures:oak_desk","forbidden_arcanus:clibano_combustion/diamond_from_clibano_combusting","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","cfm:stripped_acacia_upgraded_gate","mcwpaths:sandstone_running_bond","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwbiomesoplenty:empyreal_paper_door","minecraft:gray_terracotta","mcwfurnitures:acacia_modern_chair","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","mcwdoors:bamboo_mystic_door","cfm:stripped_spruce_coffee_table","mcwroofs:pink_concrete_attic_roof","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","mcwbiomesoplenty:magic_bark_glass_door","dyenamics:peach_terracotta","botania:livingrock_wall","minecraft:fishing_rod","xnet:connector_yellow_dye","minecraft:terracotta","mcwwindows:acacia_blinds","productivebees:stonecutter/snake_block_canvas_expansion_box","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwdoors:acacia_waffle_door","botania:blue_pavement","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","comforts:sleeping_bag_gray","twigs:copper_pillar_stonecutting","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwlights:soul_oak_tiki_torch","mcwwindows:andesite_pane_window","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","productivebees:stonecutter/redwood_canvas_hive","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","minecraft:beacon","railcraft:tin_gear","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwdoors:spruce_stable_door","aquaculture:nether_star_hook","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","cfm:spruce_upgraded_gate","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","mekanism:steel_casing","alltheores:invar_dust_from_alloy_blending","ad_astra:solar_panel","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","aiotbotania:terra_shovel","mcwfurnitures:spruce_cupboard_counter","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:umbran_bamboo_door","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:hay","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","handcrafted:sandstone_pillar_trim","domum_ornamentum:light_gray_brick_extra","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","productivebees:expansion_boxes/expansion_box_snake_block_canvas","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","mcwroofs:magenta_terracotta_lower_roof","create:crafting/kinetics/windmill_bearing","ad_astra:vent","create:crafting/kinetics/turntable","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","ad_astra:ostrum_sliding_door","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwwindows:granite_four_window","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","handcrafted:terracotta_plate","mcwroofs:light_blue_terracotta_roof","mcwbiomesoplenty:willow_barn_glass_door","rftoolsbuilder:blue_shield_template_block","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","botania:conversions/dragonstone_block_deconstruct","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","silentgear:coating_smithing_template","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwbiomesoplenty:maple_barn_glass_door","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","ad_astra:blue_flag","create:crafting/kinetics/mechanical_plough","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","alltheores:lumium_plate","create:crafting/kinetics/cogwheel","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","mcwfurnitures:stripped_spruce_cupboard_counter","mcwbiomesoplenty:mahogany_paper_door","ae2:materials/annihilationcore","enderio:cold_fire_igniter","ad_astra:blasting/ostrum_ingot_from_blasting_raw_ostrum","silentgear:stone_rod","minecraft:leather_boots","railcraft:tank_detector","railcraft:steel_spike_maul","handcrafted:sandstone_corner_trim","ad_astra:etrionic_blast_furnace","create:crafting/logistics/andesite_tunnel","pneumaticcraft:classify_filter","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwbiomesoplenty:redwood_japanese_door","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","ad_astra:ostrum_nugget","mcwtrpdoors:acacia_barred_trapdoor","mcwlights:pink_paper_lamp","mcwbiomesoplenty:magic_nether_door","pneumaticcraft:gun_ammo_incendiary","pneumaticcraft:wall_lamp_inverted_light_blue","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","bigreactors:turbine/basic/bearing","tombstone:white_marble","mcwfurnitures:acacia_bookshelf_drawer","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","cfm:acacia_park_bench","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","mcwfurnitures:stripped_acacia_triple_drawer","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","mcwfurnitures:acacia_wardrobe","sfm:cable","minecraft:hay_block","mcwbiomesoplenty:palm_japanese_door","mcwroofs:deepslate_upper_steep_roof","twilightforest:mangrove_boat","mcwbiomesoplenty:palm_stockade_fence","mcwroofs:white_upper_steep_roof","alltheores:ruby_from_hammer_crushing","delightful:knives/invar_knife","create:crafting/kinetics/vertical_gearboxfrom_conversion","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:jacaranda_barn_glass_door","botania:phantom_ink","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwbiomesoplenty:fir_barn_door","mcwroofs:blue_concrete_top_roof","bambooeverything:bamboo_ladder","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwwindows:andesite_parapet","mcwdoors:oak_waffle_door","bigreactors:blasting/graphite_from_charcoal","mythicbotany:alfsteel_pylon","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","ad_astra:calorite_factory_block","railcraft:goggles","twigs:paper_lantern","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","mcwbiomesoplenty:willow_japanese2_door","domum_ornamentum:brick_extra","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwtrpdoors:bamboo_barrel_trapdoor","mcwbiomesoplenty:empyreal_window2","mcwbiomesoplenty:pine_barn_glass_door","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","enderio:basic_item_filter","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","ad_astra:ostrum_tank","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","immersiveengineering:crafting/fertilizer","ae2:network/cells/spatial_storage_cell_2_cubed","mcwroofs:magenta_terracotta_upper_steep_roof","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","immersiveengineering:crafting/cokebrick_from_slab","mcwwindows:warped_stem_four_window","securitycraft:sentry","botania:tornado_rod","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","alltheores:uranium_rod","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","alltheores:aluminum_ingot_from_ore_blasting","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","mcwroofs:pink_terracotta_top_roof","mcwfurnitures:stripped_acacia_lower_bookshelf_drawer","cfm:green_kitchen_counter","minecraft:crossbow","mcwtrpdoors:bamboo_cottage_trapdoor","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwlights:classic_street_lamp","mcwbiomesoplenty:maple_plank_four_window","mcwbiomesoplenty:fir_mystic_door","mekanism:processing/refined_obsidian/ingot/from_block","ad_astra:desh_factory_block","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","ae2:tools/certus_quartz_spade","mcwbiomesoplenty:fir_beach_door","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","farmersdelight:skillet","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","megacells:cells/portable/portable_fluid_cell_256m","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","occultism:crafting/spirit_campfire","expatternprovider:epp_alt","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","minecraft:white_stained_glass","expatternprovider:assembler_matrix_frame","mcwbiomesoplenty:umbran_mystic_door","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","immersiveengineering:crafting/metal_ladder_none","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","domum_ornamentum:white_brick_extra","aiotbotania:livingrock_hoe","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","mcwfurnitures:acacia_double_drawer_counter","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","immersiveengineering:crafting/chute_iron","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","minecraft:acacia_hanging_sign","mcwfences:jungle_hedge","ae2:network/parts/toggle_bus","mcwdoors:jungle_classic_door","create:crafting/kinetics/large_cogwheel_from_little","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","minecraft:blue_bed","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","securitycraft:reinforced_andesite","mcwtrpdoors:bamboo_glass_trapdoor","supplementaries:altimeter","create:crafting/kinetics/mechanical_harvester","gtceu:smelting/smelt_dust_bronze_to_ingot","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","allthecompressed:compress/deepslate_1x","allthecompressed:compress/stone_1x","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","botania:manasteel_shovel","handcrafted:oven","minecraft:quartz_from_blasting","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","thermal_extra:smelting/soul_infused_ingot_from_dust_smelting","mcwpaths:cobbled_deepslate_crystal_floor_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwbiomesoplenty:magic_stable_door","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","delightful:knives/bronze_knife","mcwpaths:blackstone_crystal_floor","mekanismgenerators:generator/bio","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwbiomesoplenty:mahogany_classic_door","sophisticatedbackpacks:tool_swapper_upgrade","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","botania:conversions/orange_petal_block_deconstruct","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","arseng:portable_source_cell_4k","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","cfm:stripped_jungle_desk_cabinet","mcwpaths:cobbled_deepslate_strewn_rocky_path","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","handcrafted:bench","mekanism:processing/osmium/ingot/from_raw_smelting","allthecompressed:compress/andesite_1x","mcwbiomesoplenty:willow_nether_door","minecraft:stone_slab","mcwwindows:diorite_four_window","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","cfm:gray_sofa","mcwbiomesoplenty:magic_classic_door","productivebees:stonecutter/comb_canvas_hive","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","mcwbiomesoplenty:maple_four_panel_door","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","aether:netherite_chestplate_repairing","mcwfurnitures:stripped_oak_wardrobe","ae2:network/parts/import_bus","croptopia:campfire_molasses","mcwdoors:acacia_stable_door","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwbiomesoplenty:pine_four_panel_door","pneumaticcraft:wall_lamp_green","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","additionallanterns:netherite_chain","mcwbiomesoplenty:hellbark_cottage_door","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwtrpdoors:bamboo_bark_trapdoor","mcwpaths:brick_windmill_weave","twigs:crimson_roots_paper_lantern","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:fletching_table","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","mcwbiomesoplenty:jacaranda_glass_door","pneumaticcraft:compressed_stone_slab","mcwfurnitures:stripped_acacia_chair","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","mcwdoors:spruce_waffle_door","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","botania:conversions/brown_petal_block_deconstruct","mcwbridges:acacia_bridge_pier","allthecompressed:compress/grass_block_1x","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwbiomesoplenty:hellbark_classic_door","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","botania:yellow_pavement","mcwbiomesoplenty:dead_glass_door","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","twigs:silt_from_silt_balls","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","pneumaticcraft:logistics_core","create:crafting/kinetics/yellow_seat","mcwbiomesoplenty:willow_modern_door","mcwpaths:andesite_windmill_weave_path","comforts:hammock_blue","mcwbiomesoplenty:palm_glass_door","modularrouters:blank_upgrade","mcwdoors:spruce_swamp_door","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","mcwbiomesoplenty:jacaranda_pane_window","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","cfm:stripped_spruce_cabinet","minecraft:mangrove_boat","mcwdoors:bamboo_whispering_door","minecraft:bread","mcwroofs:green_terracotta_steep_roof","mcwdoors:spruce_barn_glass_door","mcwbiomesoplenty:redwood_western_door","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwfurnitures:spruce_coffee_table","mcwbiomesoplenty:dead_classic_door","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwbiomesoplenty:jacaranda_plank_four_window","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwbiomesoplenty:umbran_swamp_door","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","mcwfurnitures:acacia_desk","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","ae2:smelting/smooth_sky_stone_block","mcwwindows:stone_brick_arrow_slit","mcwfurnitures:jungle_desk","mcwbiomesoplenty:mahogany_cottage_door","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","mcwbiomesoplenty:umbran_waffle_door","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","ad_astra:space_suit","securitycraft:disguise_module","mcwbiomesoplenty:willow_swamp_door","mcwwindows:deepslate_four_window","mcwdoors:acacia_glass_door","pneumaticcraft:camo_applicator","mcwwindows:dark_oak_louvered_shutter","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwfurnitures:oak_covered_desk","mcwbiomesoplenty:fir_stable_head_door","mcwbiomesoplenty:redwood_barn_door","mcwtrpdoors:acacia_four_panel_trapdoor","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mcwfences:birch_picket_fence","mcwbiomesoplenty:palm_modern_door","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwdoors:bamboo_barn_door","mcwbiomesoplenty:maple_cottage_door","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","mcwbiomesoplenty:empyreal_modern_door","pneumaticcraft:logistics_frame_passive_provider_self","mcwdoors:jungle_waffle_door","mcwroofs:black_roof_block","mcwbiomesoplenty:hellbark_pane_window","create:crafting/kinetics/wrench","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","create:crafting/kinetics/propeller","minecraft:allthemodium_mage_leggings_smithing","mcwfurnitures:stripped_acacia_large_drawer","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","modularrouters:dropper_module","immersiveengineering:crafting/fluid_pipe","mcwbiomesoplenty:magic_highley_gate","ae2:tools/fluix_sword","ad_astra:ostrum_fluid_pipe","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","dyenamics:mint_terracotta","mcwwindows:pink_curtain","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","delightful:knives/refined_obsidian_knife","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwdoors:bamboo_swamp_door","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","minecraft:bow","supplementaries:wrench","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","mcwroofs:blue_terracotta_upper_steep_roof","deepresonance:radiation_suit_boots","cfm:acacia_kitchen_counter","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","minecraft:andesite_stairs","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwdoors:bamboo_japanese_door","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","minecraft:jungle_slab","supplementaries:sconce","silentgear:rough_rod","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:spruce_glass_table","mcwfurnitures:acacia_large_drawer","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","sophisticatedbackpacks:feeding_upgrade","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:pine_stable_door","minecraft:granite","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","create:crafting/kinetics/clutch","mcwfurnitures:spruce_covered_desk","cfm:stripped_acacia_desk","minecraft:firework_rocket_simple","create:crafting/kinetics/encased_fan","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","mcwbiomesoplenty:magic_barn_glass_door","mcwdoors:spruce_mystic_door","mcwtrpdoors:spruce_beach_trapdoor","mcwbiomesoplenty:redwood_mystic_door","pneumaticcraft:wall_lamp_inverted_orange","enderio:enderios","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwbiomesoplenty:fir_bamboo_door","mcwroofs:andesite_roof","botania:black_pavement","rftoolsbase:infused_enderpearl","travelersbackpack:blaze","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","ae2:shaped/stairs/sky_stone_block","ae2:network/cables/glass_yellow","botania:quartz_blaze","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","mcwpaths:sandstone_flagstone_path","mcwfurnitures:stripped_acacia_wardrobe","dyenamics:rose_terracotta","farmersdelight:cooking/apple_cider","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwbiomesoplenty:dead_cottage_door","mcwdoors:jungle_tropical_door","ad_astra:ostrum_ingot_from_ostrum_block","minecraft:stone","twigs:lamp","mcwwindows:orange_curtain","mcwfurnitures:stripped_acacia_modern_desk","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","railcraft:iron_tank_valve","minecraft:blackstone_stairs","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","ad_astra:calorite_tank","dyenamics:icy_blue_wool","securitycraft:speed_module","comforts:sleeping_bag_blue","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","cfm:stripped_oak_kitchen_sink_dark","botania:mana_ring_greater","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","mcwroofs:brown_terracotta_steep_roof","tombstone:carmin_marble","botania:elf_quartz","create:andesite_bars_from_andesite_alloy_stonecutting","mcwdoors:oak_barn_door","botania:alchemy_catalyst","cfm:cyan_kitchen_drawer","mcwfurnitures:spruce_double_drawer_counter","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","create:crafting/kinetics/display_board","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","mcwpaths:sandstone_windmill_weave_slab","minecraft:smooth_stone","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","cfm:acacia_upgraded_fence","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","create:crafting/kinetics/mechanical_press","botania:hourglass","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","alltheores:lead_plate","additionallanterns:obsidian_chain","minecraft:ender_eye","mcwfurnitures:stripped_spruce_coffee_table","supplementaries:blackstone_lamp","mcwfurnitures:spruce_double_wardrobe","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","mcwtrpdoors:acacia_ranch_trapdoor","mcwlights:cross_lantern","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwbiomesoplenty:pine_barn_door","railcraft:receiver_circuit","mcwwindows:bricks_four_window","securitycraft:reinforced_packed_mud","immersiveengineering:crafting/hemp_fabric","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","ae2:network/parts/terminals_pattern_access","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","ae2:network/blocks/io_condenser","ae2:block_cutter/walls/quartz_wall","sophisticatedbackpacks:smoking_upgrade","railcraft:steel_tank_wall","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","enderio:dark_steel_trapdoor","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","advanced_ae:quantumstorage128","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","mcwroofs:jungle_lower_roof","railcraft:iron_tank_gauge","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:age_detector","pneumaticcraft:wall_lamp_orange","dyenamics:wine_stained_glass","mcwwindows:black_curtain","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","botania:drum_gathering","mcwwindows:jungle_plank_window2","ad_astra:space_pants","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","productivebees:hives/advanced_dark_oak_canvas_hive","mcwbiomesoplenty:mahogany_japanese_door","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","utilitarian:utility/acacia_logs_to_pressure_plates","mcwbiomesoplenty:umbran_four_panel_door","enderio:resetting_lever_five_inv","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:redwood_beach_door","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","alltheores:osmium_ingot_from_ore_blasting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","appmek:portable_chemical_storage_cell_64k","ae2:network/cells/item_storage_components_cell_1k_part","dyenamics:spring_green_candle","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","ad_astra:steel_tank","domum_ornamentum:black_brick_extra","railcraft:steel_chestplate","supplementaries:daub","minecraft:netherite_scrap","alltheores:zinc_dust_from_hammer_crushing","mcwfurnitures:stripped_jungle_end_table","create:crafting/kinetics/wooden_bracket","productivebees:stonecutter/acacia_canvas_hive","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwdoors:spruce_japanese2_door","ad_astra:jet_suit_pants","enderio:fluid_tank","mcwroofs:oak_planks_roof","alltheores:tin_dust_from_hammer_ingot_crushing","arseng:portable_source_cell_1k","ad_astra:iron_rod","ae2:decorative/quartz_block","mcwbiomesoplenty:magic_cottage_door","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","botania:mana_tablet","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","ad_astra:desh_fluid_pipe","allthearcanistgear:unobtainium_boots_smithing","travelersbackpack:standard_no_tanks","cfm:white_kitchen_sink","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","megacells:cells/portable/portable_item_cell_256m","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","minecraft:acacia_wood","mcwbiomesoplenty:maple_tropical_door","create:cut_tuff_from_stone_types_tuff_stonecutting","pneumaticcraft:logistics_configurator","mcwbiomesoplenty:mahogany_beach_door","mcwtrpdoors:print_tropical","pneumaticcraft:logistics_frame_default_storage_self","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:1b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:6096,warning_level:0}}