{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:395,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],Air:300s,Attributes:[{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:1.0d,Name:"eidolon:magic_power"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:20.0d,Modifiers:[{Amount:8.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"attributeslib:healing_received"},{Base:0.0d,Name:"attributeslib:cold_damage"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.0d,Name:"attributeslib:fire_damage"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"attributeslib:life_steal"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.0d,Name:"minecraft:generic.luck"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:universal_attractor"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:1,id:"tempad:he_who_remains_tempad"}],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:netherite_gloves",tag:{Damage:65,Enchantments:[{id:"minecraft:unbreaking",lvl:4s}]}},{Count:1b,Slot:1,id:"artifacts:night_vision_goggles"}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:villager_hat"}],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:diamond_backpack",tag:{contentsUuid:[I;546499169,-2001255739,-1798843988,2011460880],inventorySlots:108,renderInfo:{upgradeItems:[{Count:1b,id:"sophisticatedbackpacks:deposit_upgrade"},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}]},upgradeSlots:5}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:vampiric_glove"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:rooted_boots"}],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:netherite_gloves",tag:{Damage:328,Enchantments:[{id:"naturesaura:aura_mending",lvl:1s}]}}],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:155,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:227568},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:0b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:7},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_LAST_ARROW:[I;1950010978,-1769517923,-1120535797,1741149766],MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"sophisticatedstorage:stack_upgrade_tier_4",ItemStack:{Count:1b,id:"sophisticatedstorage:stack_upgrade_tier_4"}}],SelectedRecipe:"sophisticatedstorage:stack_upgrade_tier_4"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:133,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["minecraft:baked_potato","minecraft:cooked_chicken","farmersdelight:hamburger","minecraft:cooked_porkchop","minecraft:enchanted_golden_apple","minecraft:bread","minecraft:rotten_flesh","minecraft:chorus_fruit","minecraft:golden_apple","minecraft:cooked_beef","botania:mana_bottle","minecraft:mushroom_stew","maidensmerrymaking:chocolate_bunny"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:3400s,knowledge:52,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:2b,PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:12919261778018L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;1755974416,-1313521370,-2054355241,-84837602]},{FromDim:"minecraft:the_nether",FromPos:14293651308643L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;1148295264,-1646705090,-1556208382,-1605006239]}],TrashSlot:{Count:1b,id:"evilcraft:flesh_humanoid",tag:{Id:[I;-1882397333,-2136128001,-1485221170,-1356946660],Name:"RP_bad",Properties:{textures:[{Signature:"pkbGOcm2ZvWdDt5uGcjYEFub1nkC1SFHL/IzBEvda+jsNAGEPle63LmvQ+5il42s/kTjuDUf+Q0lWe5kivqekaSLiIMKm++NXqEJ0xefAusnuCAkOsJ86nWGM653r+dUVDdXUvMYPU6tHSfVCZlVOKKnL8iRefWbmlP/kjQQ5KVKm3AbDGr+sHKFLVsA4zrEaxR3fFeewLJh0VwSEz8EeEUhrLA6Fv6sUUujAqqpfhkwT+IabwgPajPBEgbFkt4nAMFsEgSkDx50Nj3dAWEDLGp5iqFtGs9KJCO0XLnpH+Nd0IxqAwWYR6tVDzki3cepQIijTcdVDxgFrPSvQQkRhw3xFSITRzgRkg+2/L+66klPX0BoBxv/TMuuSiNNOF3cKgUkHW3pxfatm2GiDbb20WGIbTUrG7K2Cq/YbFACEpn6AL6N04SSysw2gKeBxFWD3YpxAQCkLJIqcVVUH6CykgSDI+F2/f+6kCYwbiPF2F/FOdVnA8UzmVRJtSCc8vzeiduHhOO7OM4s2Gd+e7n/ZwwzEU3++nJ+YTtOV/VigxrMoZEC9mAC1Dx/fv8hA9xTL6T8DJ9EnWLBK9pNlYbqHfS31wHwi1FdR496iQkDh8jfDOHurRakY4kjwUYjM0r2gGlUOC9kWQthg3s2SnOi31LS2LquKC9QLmNfCSvEL9s=",Value:"eyJ0aW1lc3RhbXAiOjE3NTIzNjc2NjcsInByb2ZpbGVJZCI6IjhmY2NlNTZiODBhZDQ1ZmZhNzc5NTJjZWFmMWVhMzFjIiwicHJvZmlsZU5hbWUiOiJSUF9iYWQiLCJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHBzOi8vbGl0dGxlc2tpbi5jbi90ZXh0dXJlcy9mMDg2YjM3NGJlMWNiNmMzNmMwYTY0ZGQyZmVhOGIwNGMzODhjNzdjNTlmZmFiZDNhYzlhZDBkMGQxZWQyMDVlIn19fQ=="}]}}},WaystonesData:{Waystones:["70e61f07-a350-437a-ae01-15e3ef9a1bab","88320f8d-ea59-4cc6-896d-79cc52d4aa80","92662b7f-21bf-4acf-ac24-7d7c296f7f6d","44305267-ff27-4e8f-8df6-4521174251c8","db9fa2f7-bedb-4c7b-91c7-5ef71c247526","36d86fd7-e43f-48b5-a2b9-6969a1ba515c","557ffa77-b0ca-4b50-98bb-2a30fadb9587","b0983766-8943-4738-a1fa-681aa5a959e5","3701bcdb-916d-42ae-b046-b2e1a1f94a25"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_end_conquered_effects:[{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:37,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:389,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-42,tb_last_ground_location_y:258,tb_last_ground_location_z:180,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},"apoth.affix_cooldown.apotheosis:armor/mob_effect/bursting":4328430L,"apoth.affix_cooldown.apotheosis:breaker/mob_effect/swift":4327891L,apoth_reforge_seed:**********,"quark:locked_once":1b,"quark:trying_crawl":0b,sophisticatedBackpackSettings:{},sophisticatedStorageSettings:{}},Health:34.0f,HurtByTimestamp:5292,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"allthemodium:vibranium_sword",tag:{Damage:44,Enchantments:[{id:"apotheosis:bane_of_illagers",lvl:7s},{id:"ensorcellation:vorpal",lvl:5s},{id:"tombstone:soulbound",lvl:1s},{id:"tombstone:incurable_wounds",lvl:9s},{id:"minecraft:looting",lvl:4s}],RepairCost:1,affix_data:{affixes:{"apotheosis:durable":0.35f,"apotheosis:sword/attribute/glacial":0.096070945f,"apotheosis:sword/attribute/infernal":0.40308654f,"apotheosis:sword/attribute/vampiric":0.56254655f,"apotheosis:sword/mob_effect/weakening":0.81581384f,"apotheosis:sword/special/thunderstruck":0.8757709f,"irons_spellbooks:sword/attribute/mana_regen":0.6215898f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/attribute/infernal"},"",{"translate":"affix.apotheosis:sword/attribute/glacial.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-**********,-736475506,-**********,582842593]]}}},{Count:1b,Slot:1b,id:"allthemodium:vibranium_pickaxe",tag:{Damage:591,Enchantments:[{id:"minecraft:fortune",lvl:6s}],RepairCost:3,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":0.88584554f,"apotheosis:breaker/attribute/experienced":0.52096045f,"apotheosis:breaker/attribute/lengthy":0.18446553f,"apotheosis:breaker/attribute/lucky":0.7878533f,"apotheosis:breaker/mob_effect/swift":0.17236996f,"apotheosis:durable":0.37f,"apotheosis:telepathic":0.66143525f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/attribute/lucky"},"",{"translate":"affix.apotheosis:breaker/mob_effect/swift.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;124000683,-534232201,-1595512534,207886694]]},apoth_rspawn:1b}},{Count:44b,Slot:2b,id:"allthemodium:unobtainium_ingot"},{Count:3b,Slot:3b,id:"endermanoverhaul:corrupted_pearl"},{Count:1b,Slot:4b,id:"apotheosis:potion_charm",tag:{Damage:3,Potion:"potionsmaster:unobtainium_sight",charm_enabled:0b}},{Count:39b,Slot:5b,id:"minecraft:glass"},{Count:15b,Slot:6b,id:"darkutils:vector_plate_extreme"},{Count:1b,Slot:7b,id:"sophisticatedbackpacks:diamond_backpack",tag:{contentsUuid:[I;1835949170,1786069169,-1942583115,-2021342309],inventorySlots:108,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}]},upgradeSlots:5}},{Count:64b,Slot:9b,id:"minecraft:glass"},{Count:64b,Slot:10b,id:"minecraft:glass"},{Count:7b,Slot:11b,id:"minecraft:glass"},{Count:1b,Slot:12b,id:"minecraft:netherite_shovel",tag:{Damage:169,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":0.59368795f,"apotheosis:breaker/attribute/experienced":0.81087536f,"apotheosis:breaker/attribute/lengthy":0.69455075f,"apotheosis:breaker/attribute/lucky":0.6184776f,"apotheosis:breaker/mob_effect/spelunkers":0.6963558f,"apotheosis:durable":0.35999998f,"apotheosis:telepathic":0.11873716f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/attribute/lengthy"},"",{"translate":"affix.apotheosis:telepathic.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;-2106276506,1647855175,-1175830422,-115335692]]},apoth_rspawn:1b}},{Count:5b,Slot:13b,id:"minecraft:ender_pearl"},{Count:1b,Slot:14b,id:"constructionwand:diamond_wand",tag:{Damage:897,wand_options:{}}},{Count:1b,Slot:15b,id:"minecraft:trident",tag:{Damage:0,Enchantments:[{id:"naturesaura:aura_mending",lvl:1s},{id:"minecraft:loyalty",lvl:6s},{id:"enderio:xp_boost",lvl:5s}],RepairCost:1,affix_data:{affixes:{"apotheosis:durable":0.35999998f,"apotheosis:ranged/attribute/elven":0.27853638f,"apotheosis:ranged/attribute/windswept":0.77404445f,"apotheosis:ranged/mob_effect/ivy_laced":0.6699431f,"apotheosis:sword/attribute/graceful":0.49516064f,"apotheosis:sword/attribute/violent":0.35362554f,"apotheosis:sword/mob_effect/elusive":0.6607209f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/attribute/violent"},"",{"translate":"affix.apotheosis:ranged/attribute/windswept.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-1359701020,676872638,-1510286874,-1365879609]]},apoth_rspawn:1b}},{Count:14b,Slot:17b,id:"darkutils:vector_plate"},{Count:1b,Slot:19b,id:"buildinggadgets2:gadget_copy_paste",tag:{bind:0b,binddirection:5,bound:{blockpos:{X:-44,Y:271,Z:185},level:"allthemodium:mining"},copyend:{X:-80,Y:252,Z:223},copystart:{X:-1,Y:252,Z:176},copyuuid:[I;-738974828,-446151428,-1282483423,1785594798],energy:632250,mode:"buildinggadgets2:paste",relativepaste:{X:0,Y:-1,Z:16},undolist:[{uuid:[I;57220707,515459145,-1631470394,473676145]},{uuid:[I;2129681363,-1130280301,-1804086020,487581392]},{uuid:[I;-1514201784,-461356879,-1957767134,1811676728]},{uuid:[I;-903369230,1107707418,-1903165380,1448905634]},{uuid:[I;196778851,1038895162,-1948630720,1608373867]},{uuid:[I;-1984866253,820070961,-1919182642,-993318194]},{uuid:[I;-1781842973,1833192181,-1922043569,1350630480]},{uuid:[I;436464427,1595886374,-2003726877,1734761136]},{uuid:[I;-1844831910,1391084450,-1531476460,575039624]},{uuid:[I;744896374,192364714,-1259797756,-811738673]}],uuid:[I;325232691,-1018935719,-1418392972,2011780795]}},{Count:1b,Slot:100b,id:"minecraft:diamond_boots",tag:{Damage:347,Enchantments:[{id:"minecraft:protection",lvl:5s},{id:"ars_nouveau:mana_regen",lvl:4s},{id:"minecraft:blast_protection",lvl:5s}],affix_data:{affixes:{"apotheosis:armor/attribute/steel_touched":0.1609391f,"apotheosis:armor/dmg_reduction/feathery":0.5863567f,"apotheosis:durable":0.36f,"irons_spellbooks:armor/attribute/cooldown":0.4690948f,"irons_spellbooks:armor/attribute/spell_resist":0.5844888f},name:'{"italic":false,"color":"#BB00BB","translate":"misc.apotheosis.affix_name.four","with":["Kassandra\'s",{"translate":"affix.irons_spellbooks:armor/attribute/cooldown"},"",{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist.suffix"}]}',rarity:"apotheosis:epic",sockets:2,uuids:[[I;-1388256359,-282505292,-1361354604,-1806879778]]},apoth_boss:1b,display:{Name:'{"extra":[{"text":"Boots"}],"text":""}'}}},{Count:1b,Slot:101b,id:"minecraft:netherite_leggings",tag:{Damage:0,Enchantments:[{id:"minecraft:projectile_protection",lvl:6s},{id:"minecraft:unbreaking",lvl:4s},{id:"minecraft:mending",lvl:1s}],affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.70296735f,"apotheosis:armor/dmg_reduction/runed":0.47144198f,"apotheosis:armor/mob_effect/bursting":0.14644927f,"apotheosis:durable":0.28f,"eidolon:wand/attribute/magic_power":0.54480094f,"irons_spellbooks:armor/attribute/mana":0.010502934f},name:'{"color":"#BB00BB","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/mana"},"",{"translate":"affix.apotheosis:armor/mob_effect/bursting.suffix"}]}',rarity:"apotheosis:epic",sockets:2,uuids:[[I;578755771,2046709005,-1893739106,-945153642]]}}},{Count:1b,Slot:102b,id:"minecraft:netherite_chestplate",tag:{Damage:0,Enchantments:[{id:"ars_nouveau:mana_regen",lvl:4s},{id:"tombstone:soulbound",lvl:1s},{id:"minecraft:mending",lvl:1s},{id:"ensorcellation:reach",lvl:4s}],affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.6836853f,"apotheosis:armor/attribute/fortunate":0.69824576f,"apotheosis:armor/dmg_reduction/runed":0.77086383f,"apotheosis:durable":0.34f,"eidolon:wand/attribute/magic_power":0.35695958f,"irons_spellbooks:armor/attribute/spell_power":0.82299745f},name:'{"color":"#BB00BB","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/fortunate"},"",{"translate":"affix.apotheosis:armor/attribute/blessed.suffix"}]}',rarity:"apotheosis:epic",sockets:3,uuids:[[I;2057322565,636833648,-1403221482,680999067]]}}},{Count:1b,Slot:103b,id:"minecraft:netherite_helmet",tag:{Damage:121,Enchantments:[{id:"ars_nouveau:mana_regen",lvl:4s},{id:"tombstone:spectral_conjurer",lvl:6s},{id:"enderio:shimmer",lvl:1s}],affix_data:{affixes:{"apotheosis:armor/attribute/fortunate":0.17082214f,"apotheosis:armor/attribute/ironforged":0.5513301f,"apotheosis:armor/attribute/stalwart":0.27773064f,"apotheosis:armor/dmg_reduction/runed":0.81341434f,"apotheosis:armor/mob_effect/blinding":0.9386601f,"apotheosis:durable":0.28f,"irons_spellbooks:armor/attribute/spell_power":0.4563486f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_power"},"",{"translate":"affix.apotheosis:armor/attribute/fortunate.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;404629403,-1890367831,-1129059462,-1719428210]]}}}],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:mining",pos:[I;-45,253,182]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-38.69999998807907d,260.0d,164.30000001192093d],Railways_DataVersion:2,Rotation:[7.980652f,90.0f],Score:116788,SelectedItemSlot:5,SleepTimer:0s,SpawnAngle:13.661743f,SpawnDimension:"allthemodium:mining",SpawnForced:0b,SpawnX:-48,SpawnY:256,SpawnZ:189,Spigot.ticksLived:227568,UUID:[I;-1882397333,-2136128001,-1485221170,-1356946660],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-11819749297912L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:107,XpP:0.24969146f,XpSeed:-827683960,XpTotal:36554,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752136614332L,keepLevel:0b,lastKnownName:"RP_bad",lastPlayed:1752406425743L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:3.4686024f,foodLevel:20,foodSaturationLevel:2.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","comforts:hammock_to_cyan","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","mcwroofs:dark_oak_planks_roof","mcwwindows:birch_four_window","biomesoplenty:fir_boat","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwdoors:dark_oak_swamp_door","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","mcwroofs:blue_terracotta_attic_roof","handcrafted:spider_trophy","allthecompressed:compress/calcite_1x","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","croptopia:shaped_beef_stew","ae2:network/cables/dense_smart_green","pneumaticcraft:wall_lamp_white","mcwroofs:red_terracotta_lower_roof","sophisticatedstorage:backpack_crafting_upgrade_from_storage_crafting_upgrade","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","xnet:connector_green","advanced_ae:smallappupgrade","minecraft:gold_nugget_from_blasting","create:oak_window","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","mcwpaths:cobbled_deepslate_running_bond_slab","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","appbot:mana_storage_cell_16k","cfm:gray_picket_fence","buildinggadgets2:gadget_cut_paste","reliquary:mob_charm_fragments/cave_spider","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","mcwfurnitures:spruce_modern_desk","rftoolsbuilder:mover_status","mcwfurnitures:spruce_drawer","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","handcrafted:cyan_sheet","railcraft:controller_circuit","railcraft:world_spike_minecart","twigs:polished_rhyolite_stonecutting","handcrafted:deepslate_corner_trim","minecraft:spruce_sign","mcwroofs:gray_top_roof","cfm:stripped_dark_oak_bedside_cabinet","supplementaries:candle_holders/candle_holder_green","handcrafted:dark_oak_shelf","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","utilitix:oak_shulker_boat_with_shell","sophisticatedstorage:copper_to_iron_tier_upgrade","handcrafted:spruce_side_table","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","minecraft:chiseled_nether_bricks","mekanism:fluid_tank/basic","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","minecraft:nether_brick_stairs","pneumaticcraft:crop_support","mcwfurnitures:dark_oak_stool_chair","supplementaries:bubble_blower","naturalist:glow_goop","dyenamics:conifer_stained_glass_pane_from_glass_pane","supplementaries:crank","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","minecraft:magenta_dye_from_blue_red_white_dye","alltheores:iron_plate","cfm:stripped_birch_park_bench","allthearcanistgear:vibranium_leggings_smithing","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","allthecompressed:compress/ancient_stone_1x","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","minecraft:pink_terracotta","mcwbiomesoplenty:mahogany_plank_pane_window","rftoolsutility:destination_analyzer","mcwfences:prismarine_railing_gate","mcwfurnitures:stripped_dark_oak_double_drawer_counter","mcwbiomesoplenty:willow_highley_gate","ae2:materials/cardfuzzy","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwtrpdoors:spruce_barred_trapdoor","ae2:network/cables/dense_smart_light_blue","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/cables/smart_fluix_clean","ae2:network/parts/annihilation_plane_alt","mekanism:energy_tablet","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","railcraft:standard_rail_from_rail","allthecompressed:decompress/gravel_2x","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","additionallanterns:mossy_cobblestone_chain","advanced_ae:advpartenc","mcwroofs:black_top_roof","mcwfences:modern_nether_brick_wall","minecraft:prismarine_stairs","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","cfm:oak_kitchen_counter","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","minecraft:golden_hoe","minecraft:fire_charge","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","deeperdarker:gold_ingot_from_smelting_gloomslate_gold_ore","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","cfm:stripped_spruce_bedside_cabinet","railcraft:any_detector","biomesoplenty:mahogany_boat","quark:tweaks/crafting/utility/misc/charcoal_to_black_dye","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","sophisticatedstorage:controller","mcwroofs:stone_bricks_upper_steep_roof","advanced_ae:reactionchamber","biomesoplenty:palm_chest_boat","minecraft:stone_button","mcwfurnitures:birch_desk","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","cfm:purple_picket_fence","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","aether:golden_pickaxe_repairing","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:no_soliciting/soliciting_carpets/gray_trapped_soliciting_carpet","minecraft:lodestone","occultism:crafting/magic_lamp_empty","handcrafted:birch_counter","mcwlights:black_lamp","minecraft:purpur_block","croptopia:vanilla_ice_cream","railcraft:brass_ingot_crafted_with_ingots","megacells:crafting/greater_energy_card","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwdoors:spruce_four_panel_door","mcwpaths:sandstone_running_bond_stairs","minecraft:polished_blackstone_slab","domum_ornamentum:blue_brick_extra","mcwbiomesoplenty:mahogany_window","cfm:stripped_spruce_chair","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:quartz","minecraft:nether_brick_wall","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","silentgear:blaze_gold_block","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","allthemodium:unobtainium_dust_from_ore_crushing","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","minecraft:redstone_from_blasting_redstone_ore","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","ae2:tools/paintballs_light_blue","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","ae2:network/parts/terminals","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","enderio:resetting_lever_thirty_inv_from_prev","croptopia:fried_calamari","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","cfm:spruce_bedside_cabinet","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","utilitix:birch_shulker_boat","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","xnet:connector_routing","mcwwindows:warped_louvered_shutter","blue_skies:glowing_nature_stone","handcrafted:brown_sheet","handcrafted:birch_nightstand","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","handcrafted:dark_oak_drawer","travelersbackpack:cake","comforts:sleeping_bag_cyan","megacells:network/mega_interface","minecraft:birch_stairs","mcwroofs:green_terracotta_attic_roof","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","immersiveengineering:crafting/shield","mcwfurnitures:dark_oak_glass_table","aether:blue_cape_cyan_wool","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","mcwdoors:birch_stable_head_door","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","sophisticatedstorage:birch_barrel","immersiveengineering:crafting/blastbrick","mcwfurnitures:stripped_spruce_double_drawer","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","ae2:network/crafting/cpu_crafting_monitor","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","mcwroofs:prismarine_brick_top_roof","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","rftoolsutility:crafter1","ae2:network/blocks/storage_chest","mcwroofs:oak_roof","aether:diamond_boots_repairing","minecraft:mangrove_chest_boat","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","xnet:netcable_routing","mekanism:processing/osmium/ingot/from_ore_smelting","mcwfences:nether_brick_pillar_wall","mcwfurnitures:birch_striped_chair","alltheores:smelting_dust/tin_ingot","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","mcwbridges:balustrade_prismarine_bricks_bridge","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwfurnitures:dark_oak_coffee_table","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","ae2:tools/paintballs_orange","mcwfences:blackstone_brick_railing_gate","mcwroofs:dark_oak_planks_upper_lower_roof","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","minecraft:iron_ingot_from_nuggets","mcwpaths:mossy_stone_running_bond_slab","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","mcwdoors:birch_glass_door","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","minecraft:gray_bed","cfm:dark_oak_coffee_table","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","xnet:wireless_router","cfm:stripped_dark_oak_kitchen_counter","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwwindows:dark_prismarine_brick_gothic","minecraft:white_terracotta","additionallanterns:normal_lantern_pink","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","mcwfurnitures:oak_double_drawer_counter","cfm:blue_grill","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","travelersbackpack:brown_sleeping_bag","pneumaticcraft:compressed_bricks_from_tile_stonecutting","twigs:chiseled_bricks","minecraft:blue_dye_from_cornflower","silentgear:blaze_gold_dust_blasting","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","ae2:network/parts/panels_monitor","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","mcwroofs:nether_bricks_upper_lower_roof","mcwbiomesoplenty:fir_pyramid_gate","cfm:birch_bedside_cabinet","minecraft:brown_banner","xnet:netcable_yellow","mcwroofs:blue_terracotta_roof","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","alltheores:platinum_gear","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwroofs:spruce_top_roof","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","rftoolscontrol:tank","mcwfurnitures:stripped_birch_drawer","mcwwindows:gray_mosaic_glass_pane","additionallanterns:normal_lantern_orange","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","create:cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","reliquary:void_tear","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","mcwbridges:dark_oak_bridge_pier","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","minecraft:chiseled_stone_bricks_stone_from_stonecutting","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","mcwfurnitures:stripped_birch_double_drawer_counter","mcwbiomesoplenty:fir_hedge","minecraft:netherite_sword_smithing","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","utilitarian:utility/dark_oak_logs_to_pressure_plates","mcwbridges:dark_oak_rail_bridge","cfm:dye_orange_picket_fence","mcwdoors:oak_cottage_door","ae2:block_cutter/slabs/fluix_slab","handcrafted:spruce_counter","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:metal_hospital_door","arseng:source_storage_cell_16k","railcraft:signal_circuit","ae2:tools/paintballs_red","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","dyenamics:dye_conifer_carpet","mcwbiomesoplenty:pine_window2","minecraft:prismarine_brick_slab_from_prismarine_stonecutting","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","connectedglass:borderless_glass_pane1","mcwtrpdoors:birch_mystic_trapdoor","mcwfurnitures:birch_bookshelf_cupboard","croptopia:buttered_toast","minecraft:gray_banner","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","cfm:pink_cooler","rftoolsbuilder:vehicle_builder","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","cfm:red_grill","create:small_calcite_brick_slab_from_stone_types_calcite_stonecutting","ae2:network/cells/fluid_storage_cell_16k_storage","ae2:tools/paintballs_gray","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","ad_astra:blue_industrial_lamp","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","allthecompressed:decompress/gravel_1x","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","utilitarian:no_soliciting/soliciting_carpets/lime_trapped_soliciting_carpet","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","croptopia:chicken_and_dumplings","mekanism:transmitter/mechanical_pipe/basic","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","utilitix:tiny_charcoal_to_tiny","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","minecraft:purpur_stairs_from_purpur_block_stonecutting","mcwtrpdoors:dark_oak_barrel_trapdoor","mcwroofs:blue_concrete_roof","alltheores:smelting_dust/osmium_ingot","additionallanterns:prismarine_chain","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwroofs:dark_prismarine_steep_roof","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","mcwbridges:spruce_rail_bridge","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","sophisticatedstorage:spruce_limited_barrel_3","sophisticatedstorage:spruce_limited_barrel_2","sophisticatedstorage:spruce_limited_barrel_1","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","pneumaticcraft:air_cannon","sophisticatedstorage:spruce_limited_barrel_4","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","minecraft:polished_blackstone_from_blackstone_stonecutting","twigs:warped_table","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwfurnitures:stripped_dark_oak_desk","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_bricks_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","twilightforest:wood/twilight_oak_planks","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwfurnitures:dark_oak_modern_desk","minecraft:blue_candle","railcraft:blast_furnace_bricks","mekanismtools:netherite_paxel","dyenamics:amber_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_pink","handcrafted:terracotta_wide_pot","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","reliquary:uncrafting/glass_bottle","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","cfm:gray_picket_gate","mcwfurnitures:spruce_stool_chair","mcwroofs:purple_terracotta_upper_lower_roof","ae2:network/cells/item_storage_cell_16k_storage","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","biomesoplenty:orange_dye_from_burning_blossom","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","blue_skies:cake_compat","minecraft:mangrove_planks","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_bricks_stonecutting","mcwpaths:mossy_stone_windmill_weave_path","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","mcwdoors:spruce_stable_head_door","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","gtceu:smelting/smelt_dust_iron_to_ingot","twigs:bloodstone_stairs","handcrafted:wood_cup","reliquary:phoenix_down","twilightforest:mining_boat","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","additionallanterns:normal_nether_bricks_chain","mcwroofs:dark_oak_planks_top_roof","mcwwindows:window_half_bar_base","utilitarian:utility/dark_oak_logs_to_boats","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwdoors:birch_modern_door","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:fried_chicken","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","cfm:birch_upgraded_gate","rftoolsbuilder:shape_card_quarry","gtceu:smelting/smelt_apatite_ore_to_ingot","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","ae2:network/parts/terminals_crafting","create:deepslate_from_stone_types_deepslate_stonecutting","twigs:calcite_wall","mcwfurnitures:stripped_dark_oak_lower_bookshelf_drawer","cfm:stripped_oak_chair","xnet:netcable_blue","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwwindows:oak_shutter","mcwroofs:lime_concrete_steep_roof","connectedglass:scratched_glass_black2","cfm:rock_path","minecraft:wooden_hoe","mekanismgenerators:generator/heat","minecraft:cooked_beef_from_campfire_cooking","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","ironfurnaces:augments/augment_generator","mcwroofs:nether_bricks_top_roof","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","mcwroofs:nether_bricks_lower_roof","ad_astra:black_industrial_lamp","twilightforest:wood/spruce_banister","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwroofs:dark_oak_steep_roof","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","allthecompressed:compress/birch_log_1x","securitycraft:panic_button","mcwtrpdoors:birch_bark_trapdoor","mcwfurnitures:birch_modern_chair","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","allthemodium:unobtainium_ingot_from_dust_blasting","biomesoplenty:brimstone_bud","mcwfurnitures:stripped_spruce_double_wardrobe","aether:obsidian_from_bucket_freezing","minecraft:blue_dye","mcwfurnitures:stripped_dark_oak_large_drawer","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","mcwwindows:crimson_planks_four_window","minecraft:piston","mcwlights:white_lamp","appmek:chemical_storage_cell_4k","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","allthemodium:allthemodium_ingot_from_raw_smelting","ae2:network/cells/item_storage_components_cell_16k_part","mcwbiomesoplenty:umbran_plank_pane_window","botania:virus_necrodermal","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwfurnitures:dark_oak_lower_triple_drawer","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","mcwroofs:gray_upper_steep_roof","mcwroofs:birch_planks_steep_roof","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","minecraft:trapped_chest","mcwfences:spruce_horse_fence","croptopia:beef_jerky","handcrafted:oak_shelf","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","handcrafted:gray_cushion","mcwfences:crimson_curved_gate","cfm:stripped_birch_kitchen_counter","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","mcwtrpdoors:spruce_mystic_trapdoor","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","silentgear:raw_crimson_iron_block","supplementaries:flags/flag_magenta","allthecompressed:compress/soul_soil_1x","minecraft:diamond_axe","mcwbridges:end_stone_bricks_bridge_pier","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","mcwfurnitures:birch_covered_desk","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","appmek:chemical_storage_cell_1k","mcwfurnitures:stripped_dark_oak_modern_chair","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","travelersbackpack:skeleton","quark:tweaks/crafting/utility/tools/stone_pickaxe","handcrafted:creeper_trophy","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","minecraft:dye_blue_wool","minecraft:dark_oak_pressure_plate","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","handcrafted:birch_side_table","packingtape:tape","mcwpaths:blackstone_flagstone_path","mcwtrpdoors:dark_oak_cottage_trapdoor","rftoolsbuilder:vehicle_control_module","utilitarian:utility/spruce_logs_to_pressure_plates","cfm:birch_kitchen_counter","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","enderio:dark_steel_nugget_to_ingot","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","mcwdoors:birch_mystic_door","securitycraft:reinforced_crimson_fence_gate","mcwbiomesoplenty:rainbow_birch_hedge","handcrafted:dark_oak_dining_bench","utilitarian:utility/birch_logs_to_stairs","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","mcwfurnitures:birch_double_drawer","enderio:fluid_conduit","ad_astra:small_black_industrial_lamp","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","mcwpaths:mossy_stone_crystal_floor_stairs","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:prismarine_brick_upper_steep_roof","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","create:polished_cut_calcite_from_stone_types_calcite_stonecutting","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","mcwfurnitures:stripped_birch_double_drawer","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfurnitures:stripped_dark_oak_covered_desk","mcwfences:dark_oak_picket_fence","connectedglass:borderless_glass_black_pane2","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwfurnitures:stripped_birch_glass_table","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","pylons:expulsion_pylon","create:dark_oak_window","mcwfences:bamboo_highley_gate","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","eidolon:smelt_lead_dust","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","ae2:tools/paintballs_blue","dyenamics:bed/ultramarine_bed_frm_white_bed","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_spruce_wardrobe","ae2:tools/paintballs_lime","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","utilitarian:no_soliciting/soliciting_carpets/light_blue_trapped_soliciting_carpet","mcwwindows:white_curtain","mcwroofs:white_terracotta_roof","minecraft:torch","ad_astra:gray_flag","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","cfm:light_gray_grill","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","productivetrees:planks/firecracker_planks","mcwfurnitures:birch_large_drawer","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:dark_oak_covered_desk","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","everythingcopper:copper_hopper","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","twilightforest:wood/twilight_oak_wood","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","travelersbackpack:gray_sleeping_bag","minecraft:crafting_table","dyenamics:lavender_stained_glass","dyenamics:cherenkov_dye","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","cfm:dye_black_picket_gate","minecraft:sandstone_wall","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/smart_gray","comforts:sleeping_bag_brown","mcwpaths:andesite_crystal_floor_stairs","botania:red_string_dispenser","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","mcwroofs:sandstone_lower_roof","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","create:crafting/logistics/pulse_extender","mcwroofs:red_nether_bricks_upper_steep_roof","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","enderio:resetting_lever_five_from_inv","mcwpaths:sandstone_basket_weave_paving","sophisticatedstorage:stack_upgrade_tier_1","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","sophisticatedstorage:stack_upgrade_tier_3","mcwwindows:stripped_acacia_log_four_window","silentgear:crimson_iron_raw_ore_blasting","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","sophisticatedstorage:stack_upgrade_tier_4","rftoolsutility:matter_beamer","ae2:decorative/quartz_fixture","sophisticatedstorage:stack_upgrade_tier_5","ae2:network/cells/item_storage_cell_16k","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","mcwfences:mangrove_curved_gate","littlelogistics:automatic_switch_rail","minecraft:carrot_on_a_stick","quark:tweaks/crafting/utility/tools/stone_axe","rftoolspower:blazing_agitator","railcraft:signal_capacitor_box","allthemodium:unobtainium_ingot_from_raw_smelting","mcwwindows:spruce_curtain_rod","cfm:dye_blue_picket_gate","forbidden_arcanus:clibano_combustion/coal_from_clibano_combusting","ae2:network/blocks/quantum_ring","aether:skyroot_loom","minecraft:brick_wall","nethersdelight:diamond_machete","utilitix:advanced_brewery","aether:netherite_helmet_repairing","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwroofs:red_nether_bricks_top_roof","enderio:dark_steel_nugget","expatternprovider:cobblestone_cell","minecraft:raw_iron_block","minecraft:dark_oak_planks","mcwwindows:stone_window2","minecraft:oak_button","allthecompressed:decompress/sand_1x","mcwroofs:prismarine_brick_attic_roof","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","croptopia:fruit_smoothie","mcwdoors:dark_oak_paper_door","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","pneumaticcraft:compressed_brick_stairs","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwtrpdoors:birch_barred_trapdoor","mcwdoors:dark_oak_western_door","pneumaticcraft:tube_junction","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","mcwdoors:birch_waffle_door","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","minecraft:mossy_cobblestone_slab_from_mossy_cobblestone_stonecutting","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","comforts:hammock_brown","securitycraft:reinforced_purple_stained_glass_pane_from_dye","mcwroofs:dark_oak_planks_steep_roof","aquaculture:bobber","mcwwindows:quartz_window2","mcwdoors:print_dark_oak","mcwpaths:cobblestone_honeycomb_paving","minecraft:dark_oak_fence_gate","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","simplylight:illuminant_brown_block_dyed","enderio:void_chassis","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","bigreactors:energizer/computerport","littlelogistics:vacuum_barge","allthemodium:allthemodium_apple","mcwroofs:lime_terracotta_upper_lower_roof","mcwpaths:mossy_stone_running_bond_stairs","expatternprovider:silicon_block","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mekanism:tier_installer/elite","itemcollectors:basic_collector","handcrafted:dark_oak_fancy_bed","minecraft:loom","supplementaries:sign_post_spruce","enderio:resetting_lever_three_hundred_inv","croptopia:banana_cream_pie","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","mcwroofs:light_gray_concrete_steep_roof","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","botania:green_shiny_flower","mcwbiomesoplenty:origin_hedge","rftoolsutility:tank","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","handcrafted:dark_oak_counter","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","cfm:pink_picket_fence","mcwroofs:pink_terracotta_steep_roof","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","mcwdoors:dark_oak_beach_door","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","minecraft:polished_blackstone","utilitarian:utility/spruce_logs_to_stairs","handcrafted:blue_crockery_combo","croptopia:shaped_scones","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","dyenamics:bed/maroon_bed_frm_white_bed","reliquary:uncrafting/slime_ball","rftoolsbase:dimensionalshard","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","mcwpaths:dark_oak_planks_path","cfm:brown_cooler","xnet:netcable_red","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","minecraft:spruce_trapdoor","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","bigreactors:reprocessor/wasteinjector","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","comforts:sleeping_bag_to_gray","allthemodium:ancient_stone_brick_slabs","undergarden:shard_torch","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","cfm:birch_upgraded_fence","minecraft:respawn_anchor","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","botania:petal_yellow","dyenamics:persimmon_stained_glass_pane_from_glass_pane","minecraft:deepslate_brick_slab","expatternprovider:ebus_in","handcrafted:birch_corner_trim","dankstorage:dock","mcwfurnitures:stripped_birch_lower_triple_drawer","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","sophisticatedstorage:shulker_box","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwfurnitures:spruce_desk","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","botania:yellow_shiny_flower","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","ae2:tools/paintballs_purple","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwtrpdoors:spruce_swamp_trapdoor","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","minecraft:snow_block","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cells/item_storage_cell_64k_storage","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","ae2:network/cables/covered_light_blue","handcrafted:spruce_cupboard","minecraft:iron_axe","mcwroofs:dark_prismarine_roof","create:crafting/kinetics/fluid_valve","ae2:decorative/quartz_vibrant_glass","enderio:resetting_lever_thirty_inv_from_base","supplementaries:crystal_display","minecraft:composter","minecraft:sandstone_stairs","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","allthemodium:ancient_stone_brick_wall","aether:aether_gold_nugget_from_blasting","handcrafted:birch_dining_bench","mcwroofs:dark_oak_upper_steep_roof","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","ae2:network/cables/smart_brown","appmek:chemical_storage_cell_16k","create:crafting/kinetics/white_seat","paraglider:cosmetic/paraglider","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","minecraft:prismarine_slab","ae2:network/cables/covered_orange","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","travelersbackpack:ghast_smithing","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:dark_oak_drawer_counter","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwdoors:birch_nether_door","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","cfm:light_gray_picket_fence","minecraft:deepslate_bricks_from_polished_deepslate_stonecutting","mcwroofs:red_terracotta_top_roof","mcwdoors:dark_oak_barn_door","quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","dyenamics:dye_spring_green_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","mcwfurnitures:stripped_spruce_stool_chair","securitycraft:reinforced_light_gray_stained_glass","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","ad_astra:cyan_flag","allthemodium:allthemodium_dust_from_ore_crushing","mcwroofs:bricks_attic_roof","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","reliquary:uncrafting/spider_eye","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","cfm:pink_picket_gate","alchemistry:combiner","mcwroofs:birch_steep_roof","occultism:crafting/brush","ae2:network/cables/glass_white","minecraft:end_stone_bricks","croptopia:egg_roll","connectedglass:borderless_glass_black2","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","sophisticatedstorage:chipped/loom_table_upgrade","railcraft:solid_fueled_firebox","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","ae2:network/cells/item_storage_cell_1k_storage","minecraft:smooth_quartz_stairs","dyenamics:bed/lavender_bed_frm_white_bed","twigs:silt_brick","twigs:smooth_stone_brick_stairs_from_smooth_stone_brick_stonecutting","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","minecraft:orange_candle","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","botania:quartz_sunny","create:framed_glass_from_glass_colorless_stonecutting","biomesoplenty:brimstone_fumarole","handcrafted:birch_chair","enderio:infinity_rod","railcraft:signal_lamp","handcrafted:dark_oak_chair","reliquary:uncrafting/sugar","mcwpaths:mossy_stone_windmill_weave_slab","tombstone:bone_needle","minecraft:stone_brick_wall","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","mcwroofs:dark_oak_roof","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:decoration_table","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","delightful:knives/draco_arcanus_knife","utilitarian:utility/glow_ink_sac","minecraft:cyan_dye","mcwwindows:acacia_window2","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","cfm:brown_sofa","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","enderio:stone_gear_upgrade","botania:light_blue_petal_block","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","minecraft:gold_block","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","ae2:network/cables/dense_smart_cyan","travelersbackpack:dye_orange_sleeping_bag","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","ironfurnaces:augments/augment_factory","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","mcwfurnitures:stripped_birch_striped_chair","cfm:spruce_kitchen_counter","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwfurnitures:stripped_dark_oak_bookshelf","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","cfm:stripped_dark_oak_desk","railcraft:routing_detector","minecraft:copper_ingot_from_blasting_raw_copper","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","gtceu:smelting/smelt_deepslate_iron_ore_to_ingot","additionallanterns:copper_lantern","twilightforest:canopy_chest_boat","mcwroofs:lime_terracotta_steep_roof","minecraft:dark_oak_button","mcwpaths:spruce_planks_path","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","mcwdoors:spruce_nether_door","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","domum_ornamentum:beige_stone_bricks","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","ae2:network/parts/panels_semi_dark_monitor","allthemodium:unobtainium_rod","supplementaries:deepslate_lamp","mcwbiomesoplenty:dead_pane_window","croptopia:vanilla_seeds","rftoolsutility:screen_controller","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:stripped_birch_desk","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","mcwfurnitures:dark_oak_double_wardrobe","mcwwindows:oak_plank_window","mcwbridges:dark_oak_log_bridge_middle","mcwwindows:oak_log_parapet","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","utilitarian:no_soliciting/soliciting_carpets/yellow_trapped_soliciting_carpet","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","farmersdelight:spruce_cabinet","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","ae2:network/cells/item_storage_components_cell_64k_part","minecraft:dark_oak_door","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwpaths:mossy_cobblestone_diamond_paving","cfm:dark_oak_cabinet","mcwfurnitures:stripped_oak_double_kitchen_cabinet","cfm:birch_blinds","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","allthecompressed:compress/spruce_planks_1x","mcwroofs:black_terracotta_steep_roof","additionallanterns:purpur_chain","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","alltheores:platinum_plate","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","farmersdelight:steak_and_potatoes","ae2:network/cables/smart_purple","mcwtrpdoors:spruce_ranch_trapdoor","cfm:dark_oak_kitchen_counter","mcwtrpdoors:dark_oak_tropical_trapdoor","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","mcwpaths:sandstone_crystal_floor_stairs","allthemodium:vibranium_plate","securitycraft:codebreaker","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","constructionwand:infinity_wand","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","farmersdelight:cooking/squid_ink_pasta","comforts:hammock_to_gray","minecraft:redstone_from_smelting_redstone_ore","cfm:dark_oak_desk","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","create:crafting/kinetics/controller_rail","utilitarian:no_soliciting/soliciting_carpets/brown_trapped_soliciting_carpet","mcwbiomesoplenty:maple_four_window","dyenamics:spring_green_stained_glass_pane_from_glass_pane","cfm:blue_kitchen_counter","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","mcwwindows:stripped_oak_log_window","mcwbridges:end_stone_bricks_bridge_stair","mcwroofs:base_top_roof","ae2:network/cables/smart_magenta","securitycraft:harming_module","minecraft:golden_boots","sfm:xp_goop","mcwroofs:birch_roof","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","rftoolspower:blazing_generator","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","megacells:network/cell_dock","supplementaries:statue","immersiveengineering:crafting/tinted_glass_lead_wire","reliquary:wraith_node","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","enderio:dark_steel_grinding_ball","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","mcwtrpdoors:birch_barrel_trapdoor","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","aether:chainmail_chestplate_repairing","ae2:network/crafting/cpu_crafting_accelerator","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","enderio:energy_conduit","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","mcwfurnitures:stripped_birch_bookshelf_cupboard","mcwdoors:birch_cottage_door","ad_astra:small_orange_industrial_lamp","rftoolsbase:machine_base","rftoolsutility:counterplus_module","minecraft:polished_blackstone_brick_stairs","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","croptopia:whipping_cream","mcwpaths:brick_crystal_floor_slab","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","mcwwindows:yellow_curtain","connectedglass:clear_glass_orange_pane2","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","pneumaticcraft:pressure_chamber_wall","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","mcwdoors:spruce_paper_door","handcrafted:dark_oak_nightstand","minecraft:end_stone_brick_stairs","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwroofs:dark_prismarine_upper_lower_roof","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","minecraft:cyan_carpet","mcwfurnitures:birch_lower_bookshelf_drawer","minecraft:polished_deepslate_stairs_from_polished_deepslate_stonecutting","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","domum_ornamentum:gray_floating_carpet","ae2:network/cables/smart_red","ae2additions:components/super/64k","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","mcwfurnitures:birch_bookshelf_drawer","dyenamics:bed/peach_bed_frm_white_bed","mcwfurnitures:stripped_birch_bookshelf_drawer","minecraft:light_blue_concrete_powder","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","twigs:smooth_stone_brick_wall_from_smooth_stone_brick_stonecutting","enderio:dark_steel_ladder","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","appmek:chemical_storage_cell_64k","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","mcwfurnitures:birch_coffee_table","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","railcraft:steel_gear","ironfurnaces:furnaces/obsidian_furnace","enderio:conduit_binder_from_smelting","aether:diamond_shovel_repairing","enderio:pulsating_alloy_nugget","bigreactors:smelting/graphite_from_charcoal","sophisticatedstorage:crafting_upgrade","minecraft:prismarine_bricks","ars_nouveau:spike_to_dye","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","mcwroofs:green_concrete_top_roof","minecraft:stone_bricks","connectedglass:tinted_borderless_glass_orange2","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","ae2:tools/paintballs_cyan","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","minecraft:dye_orange_wool","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","botania:quartz_lavender","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","travelersbackpack:cyan_sleeping_bag","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","mcwfurnitures:stripped_dark_oak_wardrobe","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","minecraft:cyan_banner","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","enderio:resetting_lever_thirty","pylons:potion_filter","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","handcrafted:spruce_fancy_bed","minecraft:snow","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","mcwfurnitures:stripped_spruce_triple_drawer","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","mcwroofs:dark_oak_upper_lower_roof","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","handcrafted:spruce_chair","mcwroofs:light_gray_concrete_lower_roof","supplementaries:candle_holders/candle_holder_black_dye","twigs:rhyolite_slab","nethersdelight:soul_compost_from_hoglin_hide","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","pylons:interdiction_pylon","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:reinforced_bricks_from_tile","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","mcwpaths:stone_crystal_floor_path","twigs:stone_column","pneumaticcraft:compressed_bricks_from_stone_stonecutting","minecraft:lectern","sophisticatedstorage:dark_oak_limited_barrel_1","allthecompressed:compress/dark_oak_planks_1x","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","sophisticatedstorage:dark_oak_limited_barrel_4","sophisticatedstorage:dark_oak_limited_barrel_3","sophisticatedstorage:dark_oak_limited_barrel_2","mcwbiomesoplenty:hellbark_plank_window2","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","ae2:network/cables/smart_yellow","minecraft:smooth_stone_slab","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","silentgear:crimson_iron_raw_ore_smelting","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","allthecompressed:compress/dark_oak_log_1x","allthecompressed:compress/tuff_1x","botania:azulejo_0","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","sfm:fancy_to_cable","ae2additions:components/super/1k","aquaculture:heavy_hook","handcrafted:birch_fancy_bed","aether:netherite_sword_repairing","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwwindows:acacia_window","mcwroofs:green_terracotta_roof","croptopia:food_press","mcwbridges:prismarine_bricks_bridge","railcraft:mob_detector","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","cfm:black_picket_fence","mcwfurnitures:oak_chair","mcwpaths:brick_honeycomb_paving","dyenamics:honey_terracotta","mcwwindows:stripped_crimson_stem_four_window","utilitix:spruce_shulker_boat_with_shell","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","silentgear:material_grader","xnet:router","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","mcwbridges:birch_bridge_pier","farmersdelight:nether_salad","aether:golden_gloves_repairing","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","reliquary:uncrafting/ink_sac","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","easy_villagers:trader","minecraft:barrel","mcwlights:orange_lamp","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","create:cut_calcite_brick_stairs_from_stone_types_calcite_stonecutting","supplementaries:boat_jar","gtceu:shapeless/decompress_tin_from_ore_block","immersiveengineering:crafting/connector_hv","mcwdoors:birch_classic_door","mcwpaths:brick_crystal_floor","create:tuff_pillar_from_stone_types_tuff_stonecutting","ae2:materials/basiccard","cfm:dark_oak_bedside_cabinet","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","forbidden_arcanus:obsidian_skull","cfm:stripped_jungle_kitchen_sink_dark","minecraft:deepslate_brick_slab_from_polished_deepslate_stonecutting","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","dyenamics:wine_concrete_powder","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:end_stone_brick_slab","mcwfurnitures:stripped_dark_oak_end_table","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","minecraft:deepslate_tile_slab_from_polished_deepslate_stonecutting","mcwpaths:gravel_path_block","mekanism:control_circuit/elite","enderio:silent_spruce_pressure_plate","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:cauldron","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","mcwbridges:nether_bricks_bridge","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","aether:iron_boots_repairing","ae2:block_cutter/walls/smooth_sky_stone_wall","mcwwindows:end_brick_gothic","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwdoors:spruce_modern_door","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:netherite_gloves_repairing","aether:iron_ring","tombstone:impregnated_diamond","dimstorage:dim_core","dyenamics:bed/navy_bed_frm_white_bed","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","cfm:cyan_kitchen_sink","dyenamics:peach_concrete_powder","ae2additions:components/super/4k","supplementaries:faucet","twigs:polished_calcite_bricks_from_calcite_stonecutting","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","sophisticatedstorage:storage_void_upgrade_from_backpack_void_upgrade","railcraft:copper_gear","littlelogistics:guide_rail_tug","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","handcrafted:birch_bench","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","dyenamics:bed/persimmon_bed_frm_white_bed","mcwlights:sea_lantern_slab","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","utilitarian:utility/spruce_logs_to_boats","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwroofs:oak_upper_lower_roof","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","mcwfurnitures:stripped_birch_cupboard_counter","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","twigs:bloodstone_slab_stonecutting","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","farmersdelight:cooking/mushroom_rice","minecraft:golden_shovel","allthecompressed:compress/podzol_1x","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","ae2:network/cells/item_storage_cell_4k","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","dyenamics:dye_persimmon_carpet","travelersbackpack:standard","mcwfurnitures:stripped_birch_counter","pneumaticcraft:reinforced_stone_slab","handcrafted:dark_oak_desk","utilitix:stone_wall","dyenamics:navy_dye","mcwfences:mangrove_stockade_fence","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","ae2:materials/cardspeed","thermal:smelting/apatite_from_smelting","mcwfurnitures:stripped_spruce_modern_chair","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","sophisticatedstorage:dark_oak_barrel","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","crafting_on_a_stick:uncraft_crafting_table","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","aether:aether_gold_nugget_from_smelting","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","mcwdoors:birch_barn_door","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","mcwtrpdoors:spruce_tropical_trapdoor","minecraft:golden_chestplate","domum_ornamentum:mossy_cobblestone_extra","mcwtrpdoors:print_cottage","securitycraft:reinforced_gray_stained_glass","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","cfm:dye_black_picket_fence","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","minecraft:birch_sign","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","additionallanterns:normal_lantern_gray","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","dyenamics:navy_terracotta","supplementaries:hourglass","ae2:tools/paintballs_light_gray","everythingcopper:copper_leggings","ae2:network/cells/item_storage_cell_1k","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_stair","mcwdoors:dark_oak_bark_glass_door","mekanism:metallurgic_infuser","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","sophisticatedstorage:backpack_stack_upgrade_tier_3_from_storage_stack_upgrade_tier_4","cfm:stripped_spruce_table","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","mcwroofs:yellow_striped_awning","minecraft:light_blue_terracotta","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","minecraft:spruce_stairs","supplementaries:checker","mcwdoors:birch_japanese_door","enderio:basic_capacitor","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","botania:conversions/light_blue_petal_block_deconstruct","domum_ornamentum:cactus_extra","cfm:cyan_cooler","allthecompressed:compress/amethyst_block_1x","mcwfurnitures:dark_oak_chair","ae2:network/parts/formation_plane","railcraft:silver_gear","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","ars_nouveau:wing_to_leather","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","enderio:conduit_binder_from_blasting","mcwroofs:light_gray_roof","create:small_calcite_brick_stairs_from_stone_types_calcite_stonecutting","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","connectedglass:scratched_glass_orange2","dyenamics:fluorescent_terracotta","minecraft:light_gray_dye_from_black_white_dye","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","mcwdoors:dark_oak_modern_door","travelersbackpack:redstone","biomesoplenty:brimstone_brick_wall_from_brimstone_stonecutting","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","dyenamics:lavender_stained_glass_pane_from_glass_pane","create:cut_calcite_from_stone_types_calcite_stonecutting","chimes:glass_bells","mcwroofs:birch_planks_top_roof","mcwwindows:iron_shutter","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","biomesoplenty:chiseled_brimstone_bricks_from_brimstone_stonecutting","mcwpaths:mossy_stone_crystal_floor","travelersbackpack:emerald","farmersdelight:cutting_board","twigs:bloodstone_wall_stonecutting","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","gtceu:smelting/smelt_raw_nether_quartz_ore_to_ingot","create:smelting/scoria","mcwpaths:mossy_cobblestone_square_paving","sophisticatedstorage:storage_stack_upgrade_tier_2_from_backpack_stack_upgrade_tier_1","mcwpaths:sandstone_flagstone","cfm:birch_crate","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwbridges:blackstone_bridge_pier","mcwroofs:birch_planks_lower_roof","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/pink_trapped_soliciting_carpet","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","mcwfurnitures:stripped_spruce_large_drawer","mcwpaths:blackstone_windmill_weave_path","minecraft:magenta_stained_glass_pane_from_glass_pane","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","alltheores:copper_rod","minecraft:shulker_box","mcwfurnitures:stripped_birch_modern_chair","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","ae2:network/cables/covered_blue","dyenamics:maroon_dye","utilitix:filter_rail","handcrafted:brown_cushion","minecraft:spruce_planks","aether:diamond_gloves","minecraft:mossy_cobblestone_stairs_from_mossy_cobblestone_stonecutting","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","enderio:resetting_lever_sixty_from_inv","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwdoors:dark_oak_barn_glass_door","minecraft:polished_blackstone_bricks","mcwfurnitures:spruce_triple_drawer","minecraft:cracked_stone_bricks","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwfurnitures:birch_double_wardrobe","mcwfurnitures:stripped_birch_desk","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","croptopia:toast_with_jam","mcwtrpdoors:birch_glass_trapdoor","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","minecraft:lantern","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","arseng:source_storage_cell_4k","cfm:spruce_park_bench","ae2:network/cells/fluid_storage_cell_4k","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","minecraft:deepslate_brick_wall","pneumaticcraft:thermal_lagging","mcwbridges:nether_bricks_bridge_pier","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","handcrafted:stone_corner_trim","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","twigs:polished_amethyst","cfm:spruce_cabinet","mcwwindows:mangrove_plank_window2","sophisticatedstorage:spruce_barrel","mcwwindows:mangrove_pane_window","mcwroofs:spruce_planks_steep_roof","minecraft:deepslate_brick_stairs_from_deepslate_bricks_stonecutting","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","mcwfurnitures:spruce_large_drawer","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","handcrafted:spruce_shelf","mcwlights:acacia_tiki_torch","aether:golden_boots_repairing","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","securitycraft:reinforced_warped_fence_gate","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwroofs:prismarine_brick_upper_lower_roof","mcwfences:oak_horse_fence","caupona:clay_cistern","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","utilitarian:utility/dark_oak_logs_to_doors","mcwfences:blackstone_pillar_wall","mcwfurnitures:birch_wardrobe","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","gtceu:smelting/smelt_raw_quartzite_ore_to_ingot","minecraft:purpur_pillar","bloodmagic:synthetic_point","mcwtrpdoors:dark_oak_ranch_trapdoor","deepresonance:lens","additionallanterns:normal_lantern_black","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","minecraft:redstone_lamp","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","handcrafted:dark_oak_side_table","alltheores:lumium_ingot_from_dust","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","mcwfurnitures:stripped_birch_large_drawer","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","immersiveengineering:crafting/plate_silver_hammering","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","mcwfurnitures:stripped_dark_oak_modern_desk","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","minecraft:dye_blue_bed","allthemodium:vibranium_ingot","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","mcwlights:glowstone_slab","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","appbot:mana_storage_cell_64k","mcwdoors:birch_western_door","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","reliquary:uncrafting/string","connectedglass:tinted_borderless_glass_blue2","minecraft:polished_blackstone_pressure_plate","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","minecraft:dark_oak_wood","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alltheores:diamond_rod","railcraft:animal_detector","rftoolsbuilder:shield_block2","minecraft:stone_slab_from_stone_stonecutting","rftoolsbuilder:shield_block1","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwroofs:light_blue_terracotta_upper_lower_roof","reliquary:infernal_claw","mcwdoors:print_birch","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","mcwroofs:spruce_upper_lower_roof","mcwtrpdoors:print_four_panel","pneumaticcraft:speed_upgrade","ae2:network/crafting/64k_cpu_crafting_storage","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","cfm:spruce_chair","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwwindows:mangrove_plank_pane_window","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","minecraft:netherite_leggings_smithing","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:birch_button","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","mcwtrpdoors:cherry_ranch_trapdoor","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","ae2:network/cables/dense_smart_brown","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","handcrafted:wood_bowl","minecraft:cyan_bed","pneumaticcraft:charging_station","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","pneumaticcraft:compressed_stone_from_slab","cfm:cyan_trampoline","mcwroofs:white_roof","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","minecraft:netherite_machete_smithing","mcwbridges:balustrade_blackstone_bridge","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","alltheores:lumium_rod","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","mcwdoors:dark_oak_stable_head_door","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","mcwpaths:mossy_stone_strewn_rocky_path","supplementaries:sign_post_birch","handcrafted:dark_oak_table","minecraft:quartz_block","alltheores:iron_dust_from_hammer_crushing","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","mcwroofs:lime_concrete_top_roof","botania:water_ring","twigs:bone_meal_from_seashells","mcwroofs:purple_terracotta_attic_roof","arseng:source_storage_cell_1k","utilitarian:no_soliciting/soliciting_carpets/magenta_trapped_soliciting_carpet","sophisticatedstorage:stack_upgrade_tier_1_plus","mcwroofs:orange_terracotta_lower_roof","ae2:network/cells/fluid_storage_cell_1k","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","gtceu:smelting/smelt_copper_ore_to_ingot","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","mcwroofs:dark_oak_lower_roof","aether:iron_axe_repairing","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","mcwfurnitures:stripped_birch_stool_chair","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","pneumaticcraft:wall_lamp_brown","cfm:stripped_oak_mail_box","mcwwindows:nether_brick_gothic","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","minecraft:dune_armor_trim_smithing_template","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","cfm:dark_oak_upgraded_fence","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","mcwfurnitures:stripped_dark_oak_double_wardrobe","minecraft:brown_dye","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","expatternprovider:ex_drive","cfm:lime_cooler","rftoolsbuilder:shape_card_quarry_clear_fortune","mcwpaths:andesite_crystal_floor","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","ae2:network/cells/fluid_storage_cell_64k_storage","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","minecraft:polished_deepslate_stairs","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwtrpdoors:dark_oak_barred_trapdoor","mcwbiomesoplenty:hellbark_plank_four_window","ae2:network/cells/fluid_storage_cell_16k","mcwroofs:pink_concrete_lower_roof","railcraft:diamond_spike_maul","minecraft:dark_oak_stairs","additionallanterns:granite_lantern","mcwlights:iron_chandelier","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_bricks_stonecutting","mcwwindows:spruce_plank_window2","mcwpaths:mossy_cobblestone_honeycomb_paving","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","ae2:network/crafting/1k_cpu_crafting_storage","utilitarian:tps_meter","mcwdoors:birch_four_panel_door","utilitarian:utility/spruce_logs_to_doors","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","ae2:network/cables/smart_pink","mcwbiomesoplenty:pine_horse_fence","mcwpaths:mossy_stone_windmill_weave","comforts:hammock_to_white","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:nether_bricks_upper_steep_roof","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","ae2:decorative/cut_quartz_block","minecraft:prismarine_wall","megacells:crafting/sky_steel_ingot_from_sky_steel_block","botania:petal_light_blue","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwfurnitures:spruce_modern_chair","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","ad_astra:brown_flag","alltheores:gold_rod","additionallanterns:smooth_stone_chain","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","mcwdoors:birch_stable_door","biomesoplenty:jacaranda_chest_boat","enderio:item_conduit","minecraft:red_nether_brick_wall","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","allthemodium:unobtainium_ingot_from_dust_smelting","rftoolspower:power_core1","aether:wooden_axe_repairing","ae2:network/blocks/io_port","mcwroofs:white_concrete_upper_steep_roof","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","allthemodium:smithing/vibranium_pickaxe_smithing","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","ae2:misc/deconstruction_certus_quartz_block","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","reliquary:mob_charm_fragments/enderman","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","handcrafted:spruce_table","dyenamics:bed/cherenkov_bed_frm_white_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","supplementaries:soap/map","mcwpaths:brick_running_bond_path","supplementaries:sugar_cube","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","rftoolsbuilder:shape_card_quarry_dirt","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","mcwtrpdoors:birch_classic_trapdoor","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","cfm:spruce_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","mcwroofs:dark_prismarine_top_roof","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","mcwpaths:brick_windmill_weave_stairs","mcwfurnitures:birch_double_drawer_counter","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","mcwdoors:spruce_tropical_door","create:polished_cut_calcite_stairs_from_stone_types_calcite_stonecutting","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","advanced_ae:smalladvpatpro","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","railcraft:iron_spike_maul","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","ae2:tools/paintballs_green","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwbiomesoplenty:fir_four_window","everythingcopper:copper_axe","cfm:green_kitchen_drawer","securitycraft:reinforced_oak_fence","minecraft:black_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","mcwroofs:dark_prismarine_lower_roof","mcwfurnitures:spruce_chair","create:cut_calcite_brick_slab_from_stone_types_calcite_stonecutting","biomesoplenty:brimstone_cluster_from_brimstone_stonecutting","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","domum_ornamentum:roan_stone_bricks","arseng:source_storage_cell_64k","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","cfm:dark_oak_chair","domum_ornamentum:light_gray_floating_carpet","comforts:sleeping_bag_to_cyan","bigreactors:energizer/powerport_fe","megacells:crafting/greater_energy_card_upgraded","cfm:purple_picket_gate","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","dyenamics:bubblegum_concrete_powder","xnet:redstoneproxy_update","mcwroofs:spruce_planks_attic_roof","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwroofs:nether_bricks_attic_roof","expatternprovider:water_cell","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","merequester:requester","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","minecraft:cracked_nether_bricks","utilitix:gilding_crystal","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","handcrafted:birch_couch","charginggadgets:charging_station","minecraft:purpur_pillar_from_purpur_block_stonecutting","supplementaries:slingshot","allthemodium:allthemodium_plate","reliquary:kraken_shell_fragment","twigs:cobblestone_from_pebble","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","mcwfurnitures:birch_cupboard_counter","everythingcopper:copper_rail","ae2:network/cells/item_storage_components_cell_4k_part","cfm:black_grill","handcrafted:deepslate_pillar_trim","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","create:small_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","cfm:birch_desk_cabinet","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","dyenamics:dye_ultramarine_carpet","mcwdoors:dark_oak_japanese_door","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","cfm:crimson_kitchen_drawer","mcwroofs:nether_bricks_steep_roof","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","cfm:dark_oak_upgraded_gate","aquaculture:neptunium_ingot_from_nuggets","mcwroofs:bricks_upper_steep_roof","minecraft:iron_pickaxe","aether:shield_repairing","ae2:shaped/not_so_mysterious_cube","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","reliquary:fortune_coin","mcwfences:railing_sandstone_wall","minecraft:spruce_button","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","rftoolsutility:matter_booster","ae2:network/cables/glass_orange","mcwfurnitures:stripped_spruce_table","deepresonance:resonating_plate","minecraft:vibranium_mage_chestplate_smithing","ae2:materials/cardinverter","mcwroofs:deepslate_top_roof","mcwdoors:birch_swamp_door","immersiveengineering:crafting/steel_wallmount","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","pipez:wrench","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwpaths:mossy_stone_windmill_weave_stairs","rftoolsutility:fluid_module","minecraft:deepslate_tiles","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","aether:leather_boots_repairing","handcrafted:gray_sheet","mcwtrpdoors:dark_oak_bark_trapdoor","cfm:post_box","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwwindows:brown_curtain","minecraft:dark_oak_slab","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","mcwfurnitures:birch_drawer","cfm:orange_cooler","minecraft:lime_stained_glass","mcwbridges:balustrade_mossy_stone_bricks_bridge","dyenamics:dye_wine_carpet","ae2:tools/paintballs_black","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwfurnitures:dark_oak_desk","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","create:layered_calcite_from_stone_types_calcite_stonecutting","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","utilitarian:utility/birch_logs_to_slabs","minecraft:stone_brick_walls_from_stone_stonecutting","mcwroofs:gray_terracotta_top_roof","securitycraft:keypad_item","ae2:tools/nether_quartz_wrench","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","mcwpaths:mossy_stone_flagstone","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","cfm:stripped_dark_oak_crate","additionallanterns:normal_lantern_lime","ae2:network/cables/covered_fluix_clean","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","mcwroofs:magenta_concrete_top_roof","handcrafted:cyan_cushion","pneumaticcraft:spawner_extractor","aether:packed_ice_freezing","mcwfurnitures:stripped_dark_oak_counter","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","silentgear:blaze_gold_ingot_from_nugget","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","pneumaticcraft:air_compressor","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","handcrafted:birch_desk","cfm:fridge_dark","chimes:copper_chimes","minecraft:birch_fence","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwfurnitures:stripped_dark_oak_table","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","mcwtrpdoors:mangrove_bark_trapdoor","pneumaticcraft:security_upgrade","minecraft:red_nether_brick_slab","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","ae2:tools/paintballs_yellow","mcwbiomesoplenty:empyreal_hedge","mcwdoors:dark_oak_mystic_door","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","dyenamics:dye_icy_blue_carpet","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","twigs:weeping_polished_blackstone_bricks","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:spruce_fence_gate","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","expatternprovider:wireless_connector","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","minecraft:white_stained_glass_pane_from_glass_pane","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","biomesoplenty:mahogany_chest_boat","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","dimstorage:solid_dim_core","mcwpaths:cobbled_deepslate_running_bond_stairs","minecraft:birch_wood","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","littlelogistics:switch_rail","dyenamics:dye_honey_carpet","mcwlights:soul_mangrove_tiki_torch","mcwpaths:mossy_cobblestone_clover_paving","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","mcwtrpdoors:birch_ranch_trapdoor","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","sophisticatedstorage:birch_chest","xnet:connector_red_dye","ae2:network/cables/covered_fluix","utilitarian:no_soliciting/soliciting_carpets/light_gray_trapped_soliciting_carpet","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","delightful:knives/tin_knife","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwfurnitures:dark_oak_wardrobe","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwfurnitures:birch_modern_wardrobe","allthemodium:ancient_cracked_stone_bricks_from_crushing","mcwlights:copper_candle_holder","wstweaks:wither_skeleton_skull","minecraft:mossy_stone_brick_slab","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:bamboo_chest_raft","mcwtrpdoors:birch_four_panel_trapdoor","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","reliquary:mob_charm_fragments/slime","allthecompressed:compress/gravel_3x","additionallanterns:normal_lantern_yellow","allthecompressed:compress/gravel_2x","dyenamics:bed/bubblegum_bed_frm_white_bed","cfm:stripped_spruce_desk","aquaculture:birch_fish_mount","ae2:network/cells/fluid_storage_cell_1k_storage","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","cfm:dark_oak_park_bench","buildinggadgets2:template_manager","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","utilitarian:utility/dark_oak_logs_to_slabs","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","cfm:birch_table","blue_skies:comet_bookshelf","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","reliquary:uncrafting/glowstone_dust","cfm:white_sofa","tombstone:ankh_of_prayer","utilitarian:utility/birch_logs_to_boats","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","ae2:shaped/slabs/fluix_block","mcwpaths:mossy_stone_flagstone_slab","rftoolsbuilder:mover_control2","mcwfences:acacia_highley_gate","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","utilitix:dark_oak_shulker_boat_with_shell","cfm:black_kitchen_drawer","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","allthemods:easy_sticks","mcwwindows:bricks_pane_window","mcwtrpdoors:birch_cottage_trapdoor","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","connectedglass:tinted_borderless_glass_black2","travelersbackpack:squid","mcwroofs:spruce_roof","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","ae2:network/blocks/cell_workbench","minecraft:polished_andesite_slab","mcwlights:magenta_lamp","create:crafting/logistics/powered_latch","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","minecraft:cracked_deepslate_bricks","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","sophisticatedstorage:stonecutter_upgrade","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","mcwroofs:prismarine_brick_steep_roof","handcrafted:oak_bench","farmersdelight:cooking/pasta_with_meatballs","silentgear:blaze_gold_dust_smelting","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwroofs:spruce_planks_upper_steep_roof","mcwwindows:birch_louvered_shutter","minecraft:repeater","minecraft:red_concrete_powder","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","rftoolsbuilder:shape_card_quarry_silk_dirt","dyenamics:spring_green_concrete_powder","mcwpaths:blackstone_crystal_floor_path","additionallanterns:normal_lantern_white","minecraft:iron_leggings","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwroofs:base_roof_block","handcrafted:birch_pillar_trim","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","alltheores:smelting_dust/platinum_ingot","ae2:tools/paintballs_white","mcwbiomesoplenty:palm_window","twigs:blackstone_column","additionallanterns:deepslate_bricks_chain","mcwbridges:balustrade_mossy_cobblestone_bridge","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwroofs:dark_prismarine_upper_steep_roof","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","handcrafted:spruce_drawer","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","minecraft:red_nether_brick_wall_from_red_nether_bricks_stonecutting","domum_ornamentum:light_blue_floating_carpet","mcwtrpdoors:dark_oak_glass_trapdoor","ae2:shaped/stairs/fluix_block","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","ae2:block_cutter/slabs/quartz_slab","utilitix:acacia_shulker_boat_with_shell","minecraft:brown_carpet","biomesoplenty:brimstone_bricks","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","additionallanterns:normal_lantern_blue","sophisticatedstorage:backpack_stack_upgrade_tier_2_from_storage_stack_upgrade_tier_3","minecraft:prismarine_brick_slab","mcwpaths:mossy_cobblestone_dumble_paving","allthecompressed:compress/birch_planks_1x","enderio:resetting_lever_three_hundred_inv_from_prev","mcwfurnitures:dark_oak_bookshelf_cupboard","minecraft:iron_chestplate","mcwtrpdoors:spruce_blossom_trapdoor","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","supplementaries:sign_post_dark_oak","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","alltheores:platinum_rod","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","railcraft:world_spike","mcwdoors:garage_gray_door","mcwfurnitures:dark_oak_table","mysticalagriculture:prosperity_shard_smelted","mcwfurnitures:stripped_oak_counter","botania:petal_green","handcrafted:witch_trophy","mcwroofs:dark_oak_planks_attic_roof","ae2:network/cells/item_storage_components_cell_256k_part","dyenamics:bed/fluorescent_bed_frm_white_bed","minecraft:polished_andesite_from_andesite_stonecutting","allthearcanistgear:vibranium_robes_smithing","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","mcwbiomesoplenty:stripped_dead_pane_window","ae2:shaped/walls/fluix_block","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","minecraft:oak_pressure_plate","botania:mushroom_5","botania:mushroom_4","minecraft:stone_brick_stairs","ae2:misc/chests_smooth_sky_stone","bigreactors:turbine/basic/activefluidport_forge","botania:mushroom_3","aether:netherite_leggings_repairing","bigreactors:reprocessor/outputport","mcwwindows:quartz_window","immersiveengineering:crafting/steel_scaffolding_standard","cfm:brown_grill","mcwroofs:gray_roof","minecraft:diorite","mcwfurnitures:dark_oak_drawer","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","blue_skies:coarse_lunar_dirt","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","create:cut_calcite_bricks_from_stone_types_calcite_stonecutting","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","securitycraft:reinforced_chiseled_bookshelf","utilitix:mangrove_shulker_boat_with_shell","cfm:gray_kitchen_drawer","minecraft:netherite_axe_smithing","minecraft:clock","mcwroofs:red_concrete_top_roof","create:crafting/appliances/netherite_diving_helmet_from_netherite","mcwroofs:birch_planks_attic_roof","domum_ornamentum:cyan_brick_extra","utilitarian:no_soliciting/soliciting_carpets/black_trapped_soliciting_carpet","securitycraft:portable_tune_player","mcwroofs:birch_planks_upper_steep_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","dyenamics:dye_peach_carpet","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","megacells:crafting/compression_card","mcwfences:oak_wired_fence","biomesoplenty:brimstone_brick_slab_from_brimstone_stonecutting","botania:twig_wand","twilightforest:transformation_chest_boat","additionallanterns:normal_lantern_light_gray","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","aquaculture:iron_fillet_knife","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","minecraft:polished_deepslate_slab_from_polished_deepslate_stonecutting","additionallanterns:red_nether_bricks_chain","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwtrpdoors:spruce_whispering_trapdoor","dyenamics:peach_stained_glass","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","minecraft:prismarine","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","cfm:dark_oak_blinds","mcwroofs:purple_terracotta_roof","alltheores:tin_rod","connectedglass:scratched_glass_orange_pane2","mcwbridges:rope_birch_bridge","mcwbridges:nether_bricks_bridge_stair","twigs:dark_oak_table","mcwwindows:white_mosaic_glass","cfm:stripped_dark_oak_desk_cabinet","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","cfm:acacia_kitchen_sink_dark","handcrafted:dark_oak_cupboard","minecraft:mossy_cobblestone_slab","pneumaticcraft:remote","rftoolsbase:machine_frame","minecraft:polished_andesite_slab_from_andesite_stonecutting","minecraft:dye_black_bed","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","cfm:stripped_spruce_crate","mcwfurnitures:stripped_dark_oak_drawer","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","mcwwindows:spruce_window2","mcwfurnitures:stripped_birch_table","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","expatternprovider:assembler_matrix_speed","utilitix:mob_yoinker","mcwwindows:andesite_four_window","domum_ornamentum:light_blue_brick_extra","minecraft:dark_oak_sign","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/cyan_trapped_soliciting_carpet","utilitarian:no_soliciting/soliciting_carpets/red_trapped_soliciting_carpet","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","mcwtrpdoors:dark_oak_whispering_trapdoor","minecraft:lime_concrete_powder","minecraft:leather_leggings","minecraft:sandstone_wall_from_sandstone_stonecutting","twigs:polished_bloodstone_bricks_from_bloodstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","securitycraft:block_change_detector","megacells:crafting/sky_steel_block","twigs:rocky_dirt","supplementaries:pancake_fd","advanced_ae:advpatpro2","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","minecraft:netherite_scrap_from_blasting","enderio:resetting_lever_three_hundred_from_prev","allthemodium:ancient_stone_brick_stairs","utilitarian:no_soliciting/soliciting_carpets/purple_trapped_soliciting_carpet","mcwroofs:pink_terracotta_lower_roof","additionallanterns:amethyst_chain","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","biomesoplenty:brimstone_brick_stairs_from_brimstone_stonecutting","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","securitycraft:reinforced_crimson_fence","minecraft:wooden_shovel","mcwtrpdoors:birch_beach_trapdoor","mcwdoors:dark_oak_bamboo_door","createoreexcavation:vein_atlas","aether:stone_axe_repairing","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","pneumaticcraft:wall_lamp_cyan","create:crafting/kinetics/fluid_pipe","mekanism:processing/gold/ingot/from_dust_smelting","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","enderio:pulsating_alloy_grinding_ball","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","minecraft:red_nether_brick_stairs_from_red_nether_bricks_stonecutting","mcwpaths:red_sand_path_block","pneumaticcraft:pressure_chamber_glass_x1","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","mcwroofs:lime_terracotta_roof","create:copper_bars_from_ingots_copper_stonecutting","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","mcwroofs:red_nether_bricks_lower_roof","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","mcwtrpdoors:dark_oak_mystic_trapdoor","minecraft:gold_ingot_from_smelting_raw_gold","alltheores:invar_ingot_from_dust_blasting","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","minecraft:cracked_polished_blackstone_bricks","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","quark:tweaks/crafting/utility/misc/repeater","mcwdoors:birch_whispering_door","comforts:hammock_gray","mcwfurnitures:oak_lower_triple_drawer","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","railcraft:logbook","ae2:misc/tiny_tnt","utilitix:bamboo_shulker_raft_with_shell","mcwroofs:green_concrete_steep_roof","ae2:decorative/quartz_glass","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","naturalist:glow_goop_from_campfire_cooking","cfm:light_gray_picket_gate","handcrafted:spruce_couch","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwpaths:blackstone_dumble_paving","mcwwindows:jungle_blinds","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","securitycraft:block_pocket_manager","enderio:pulsating_alloy_nugget_to_ingot","mcwlights:soul_cherry_tiki_torch","minecraft:end_stone_brick_wall","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","alltheores:copper_ore_hammer","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","mcwlights:red_lamp","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","alltheores:lumium_dust_from_alloy_blending","botania:flower_bag","cfm:purple_kitchen_drawer","ironfurnaces:furnaces/emerald_furnace","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","enderio:primitive_alloy_smelter","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","aether:stone_sword_repairing","minecraft:netherite_chestplate_smithing","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:prismarine_stairs_from_prismarine_stonecutting","minecraft:oak_sign","quark:tweaks/crafting/utility/bent/paper","mcwroofs:birch_lower_roof","railcraft:bag_of_cement","bigreactors:fluidizer/powerport","mcwlights:light_blue_lamp","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","domum_ornamentum:white_paper_extra","connectedglass:borderless_glass_blue2","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:maple_horse_fence","mcwbridges:deepslate_brick_bridge_pier","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","twigs:bloodstone_stairs_stonecutting","mcwbiomesoplenty:fir_plank_window2","minecraft:birch_pressure_plate","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","create:spruce_window","utilitarian:utility/dark_oak_logs_to_trapdoors","immersiveengineering:crafting/alloybrick","biomesoplenty:brimstone_bud_from_brimstone_stonecutting","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:stripped_spruce_counter","mcwfurnitures:stripped_dark_oak_modern_wardrobe","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwroofs:spruce_lower_roof","mcwwindows:cyan_mosaic_glass_pane","cfm:stripped_dark_oak_cabinet","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","littlelogistics:tee_junction_rail","allthemodium:smithing/allthemodium_sword_smithing","mcwdoors:spruce_whispering_door","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","rftoolsutility:fluidplus_module","ae2:tools/network_tool","minecraft:bowl","enderio:redstone_conduit","deeperdarker:bloom_boat","mcwtrpdoors:birch_blossom_trapdoor","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","mcwtrpdoors:birch_barn_trapdoor","dyenamics:rose_stained_glass","sophisticatedstorage:stack_upgrade_tier_2_from_tier_1_plus","mcwroofs:pink_terracotta_upper_steep_roof","mcwfurnitures:birch_chair","mcwpaths:blackstone_diamond_paving","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","additionallanterns:normal_lantern_green","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","securitycraft:reinforced_jungle_fence","minecraft:birch_planks","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","allthecompressed:compress/copper_block_1x","minecraft:spectral_arrow","alltheores:copper_dust_from_hammer_ingot_crushing","mcwroofs:light_blue_concrete_upper_steep_roof","allthemodium:ancient_stone_stairs","mcwwindows:stripped_cherry_pane_window","mcwtrpdoors:print_paper","mcwroofs:dark_prismarine_attic_roof","cfm:stripped_birch_desk_cabinet","cfm:light_blue_kitchen_sink","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","immersiveengineering:crafting/light_engineering","botania:dye_light_blue","mcwpaths:andesite_flagstone_stairs","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","reliquary:mob_charm_fragments/zombified_piglin","create:crafting/materials/red_sand_paper","mcwwindows:stripped_crimson_stem_window2","mcwpaths:mossy_stone_crystal_floor_path","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","alltheores:lead_dust_from_hammer_crushing","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","allthearcanistgear:vibranium_hat_smithing","ae2:network/cables/dense_smart_fluix_clean","minecraft:dye_orange_bed","mcwdoors:birch_barn_glass_door","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","allthecompressed:compress/netherite_block_1x","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","handcrafted:birch_drawer","mcwbiomesoplenty:magic_horse_fence","sophisticatedstorage:basic_to_copper_tier_upgrade","minecraft:brown_bed","mcwtrpdoors:birch_whispering_trapdoor","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","bigreactors:reprocessor/powerport","cfm:stripped_dark_oak_table","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","ae2:tools/misctools_entropy_manipulator","aether:netherite_pickaxe_repairing","pneumaticcraft:manual_compressor","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","minecraft:light_blue_glazed_terracotta","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","ae2:decorative/sky_stone_brick_from_stonecutting","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","mcwtrpdoors:spruce_classic_trapdoor","cfm:light_gray_kitchen_sink","allthemodium:ancient_stone_wall","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:illuminant_light_blue_block_on_dyed","simplylight:lamp_post","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","twilightforest:wood/crimson_banister","create:cut_calcite_stairs_from_stone_types_calcite_stonecutting","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","ae2:network/crafting/patterns_blank","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","mcwtrpdoors:dark_oak_swamp_trapdoor","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","voidtotem:totem_of_void_undying","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","pneumaticcraft:manometer","securitycraft:alarm","mcwfurnitures:oak_counter","rftoolsutility:charged_porter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","comforts:hammock_to_brown","pneumaticcraft:compressed_brick_tile","ae2:network/blocks/energy_vibration_chamber","mcwfurnitures:birch_stool_chair","ae2:tools/fluix_shovel","mcwdoors:birch_japanese2_door","reliquary:magicbane","mcwfurnitures:stripped_dark_oak_triple_drawer","pneumaticcraft:wall_lamp_inverted_magenta","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","dyenamics:peach_dye","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","mcwwindows:stone_window","utilitix:comparator_redirector_down","mcwroofs:gray_steep_roof","utilitarian:utility/birch_logs_to_pressure_plates","rftoolspower:endergenic","create:copper_shingles_from_ingots_copper_stonecutting","reliquary:mob_charm_belt","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","mcwroofs:birch_planks_roof","cfm:stripped_birch_crate","sophisticatedstorage:jukebox_upgrade","allthemodium:raw_unobtainium_block","additionallanterns:normal_lantern_cyan","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","twigs:mossy_cobblestone_bricks_cobblestone","ae2:network/cables/glass_black","mcwroofs:yellow_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","minecraft:unobtainium_mage_leggings_smithing","mcwroofs:birch_attic_roof","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","rftoolsbuilder:shape_card_pump_dirt","cfm:black_trampoline","cfm:stripped_oak_crate","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","mcwdoors:oak_bark_glass_door","allthemodium:smithing/vibranium_sword_smithing","mcwbiomesoplenty:stripped_willow_log_four_window","minecraft:deepslate_tiles_from_polished_deepslate_stonecutting","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","create:layered_tuff_from_stone_types_tuff_stonecutting","minecraft:polished_blackstone_stairs","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","minecraft:deepslate_brick_stairs","travelersbackpack:sandstone","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","potionsmaster:calcinatedunobtainium_powder","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","mcwdoors:dark_oak_tropical_door","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","allthecompressed:compress/cobblestone_2x","ae2:network/crafting/4k_cpu_crafting_storage","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","mcwbridges:balustrade_deepslate_bricks_bridge","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","mcwfurnitures:birch_glass_table","mcwwindows:quartz_pane_window","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","farmersdelight:cooking/mushroom_stew","mcwroofs:base_roof_slab","mcwfurnitures:spruce_table","bigreactors:energizer/energycore","cfm:spruce_desk_cabinet","minecraft:shield","allthecompressed:compress/cobblestone_3x","mcwroofs:gray_concrete_steep_roof","pneumaticcraft:air_grate_module","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:cyan_stained_glass_pane_from_glass_pane","create:cut_calcite_wall_from_stone_types_calcite_stonecutting","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","supplementaries:stonecutting/blackstone_tile","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwbridges:balustrade_end_stone_bricks_bridge","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","dyenamics:dye_rose_carpet","mcwbridges:mossy_cobblestone_bridge_pier","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","mcwbridges:balustrade_bricks_bridge","mcwbridges:prismarine_bricks_bridge_stair","supplementaries:candle_holders/candle_holder_blue_dye","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","mcwfurnitures:stripped_dark_oak_drawer_counter","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","minecraft:birch_door","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwpaths:cobbled_deepslate_windmill_weave","mcwtrpdoors:dark_oak_paper_trapdoor","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","mcwfences:double_curved_metal_fence","minecraft:smooth_quartz_stairs_from_smooth_quartz_stonecutting","securitycraft:claymore","travelersbackpack:hose_nozzle","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","twigs:polished_tuff_stonecutting","cfm:dark_oak_table","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","ae2:network/cables/dense_smart_fluix","mcwroofs:white_terracotta_top_roof","mcwroofs:sandstone_steep_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","cfm:dye_orange_picket_gate","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","biomesoplenty:redwood_chest_boat","undergarden:smogstem_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","mekanismtools:diamond_paxel","minecraft:birch_fence_gate","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","mcwroofs:orange_concrete_upper_lower_roof","mcwfurnitures:spruce_modern_wardrobe","mcwtrpdoors:spruce_barn_trapdoor","appbot:fluix_mana_pool","ae2:network/blocks/pattern_providers_interface_part","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","minecraft:netherite_drill_smithing","mcwfurnitures:dark_oak_large_drawer","mcwroofs:dark_oak_planks_lower_roof","expatternprovider:assembler_matrix_pattern","mcwfences:modern_andesite_wall","minecraft:smooth_quartz_slab","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","ae2:network/cells/view_cell","botania:brewery","mcwwindows:metal_pane_window","ironfurnaces:furnaces/allthemodium_furnace","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","rftoolsutility:matter_receiver","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","mcwroofs:deepslate_lower_roof","minecraft:prismarine_wall_from_prismarine_stonecutting","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","connectedglass:borderless_glass_orange_pane2","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","twigs:smooth_stone_brick_slab","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","minecraft:yellow_stained_glass_pane_from_glass_pane","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwfurnitures:spruce_drawer_counter","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","cfm:stripped_birch_coffee_table","simplylight:illuminant_green_block_dyed","silentgear:blaze_gold_dust","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","cfm:stripped_spruce_park_bench","eccentrictome:tome","mcwroofs:dark_oak_top_roof","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","ae2:network/cables/covered_magenta","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","dyenamics:icy_blue_concrete_powder","rftoolsbase:crafting_card","mcwroofs:dark_oak_planks_upper_steep_roof","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","deeperdarker:soul_elytra","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","rftoolsutility:matter_transmitter","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","ae2:network/cables/covered_gray","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","alltheores:platinum_ore_hammer","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","minecraft:gray_carpet","mysticalagriculture:inferium_essence_smelted","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwfurnitures:birch_lower_triple_drawer","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","mcwfurnitures:spruce_double_drawer","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","cfm:birch_coffee_table","travelersbackpack:magma_cube","mcwfurnitures:dark_oak_double_drawer_counter","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwfurnitures:stripped_birch_chair","mcwwindows:mangrove_plank_four_window","mcwfurnitures:stripped_spruce_drawer_counter","chemlib:copper_ingot_from_smelting_copper_dust","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwbridges:prismarine_bricks_bridge_pier","mcwfences:red_sandstone_grass_topped_wall","minecraft:red_nether_brick_stairs","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","farmersdelight:cooking/glow_berry_custard","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwpaths:brick_flagstone_stairs","pneumaticcraft:spawner_agitator","dyenamics:bed/persimmon_bed","rftoolsbuilder:vehicle_status_module","ae2:network/cables/smart_light_gray","minecraft:polished_blackstone_brick_wall","mcwbiomesoplenty:stripped_palm_log_four_window","handcrafted:spruce_bench","twigs:spruce_table","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","ae2:network/wireless_crafting_terminal","create:crafting/kinetics/orange_seat","mcwroofs:birch_upper_steep_roof","rftoolsutility:syringe","minecraft:diamond_block","mekanism:configurator","mcwroofs:deepslate_roof","minecraft:deepslate_tile_stairs_from_deepslate_bricks_stonecutting","biomesoplenty:willow_chest_boat","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","rftoolsutility:spawner","bigreactors:fluidizer/outputport","cfm:green_cooler","alltheores:steel_rod","additionallanterns:normal_lantern_magenta","twigs:bloodstone_wall","paraglider:cosmetic/deku_leaf","bigreactors:blasting/graphite_from_coal","sophisticatedbackpacks:stack_upgrade_tier_2","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","twilightforest:wood/warped_banister","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:horse_feed","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","bigreactors:energizer/energizercell","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwfurnitures:birch_counter","mcwfurnitures:spruce_counter","utilitarian:snad/snad","ae2:tools/certus_quartz_cutting_knife","rftoolsstorage:modular_storage","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:spruce_slab","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","mcwpaths:birch_planks_path","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","rftoolsbuilder:shape_card_pump","merequester:requester_terminal","ae2:shaped/stairs/smooth_sky_stone_block","travelersbackpack:snow","cfm:black_picket_gate","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","allthecompressed:compress/platinum_block_1x","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","ae2:network/cables/smart_green","ae2:network/cells/item_storage_cell_64k","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","ae2:block_cutter/walls/fluix_wall","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","supplementaries:soap/piston","minecraft:deepslate_brick_slab_from_deepslate_bricks_stonecutting","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:soul_campfire","botania:red_string_interceptor","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwfurnitures:stripped_dark_oak_glass_table","domum_ornamentum:green_cobblestone_extra","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwlights:spruce_tiki_torch","dyenamics:bed/amber_bed_frm_white_bed","simplylight:illuminant_block_dyed","ae2:tools/paintballs_magenta","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","twigs:cracked_bricks","supplementaries:slice_map","appflux:insulating_resin","littlelogistics:automatic_tee_junction_rail","mcwbridges:brick_bridge_pier","ae2:misc/deconstruction_fluix_block","alltheores:aluminum_dust_from_hammer_ingot_crushing","ironfurnaces:furnaces/netherite_furnace","mcwdoors:dark_oak_nether_door","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","cfm:stripped_spruce_desk_cabinet","mcwfences:jungle_wired_fence","mekanism:control_circuit/advanced","mekanism:upgrade/speed","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","blue_skies:trough","mcwfences:end_brick_pillar_wall","dyenamics:ultramarine_dye","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","enderio:reinforced_obsidian_block","mcwroofs:light_gray_upper_lower_roof","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","mcwroofs:spruce_steep_roof","minecraft:purple_dye","minecraft:deepslate_tile_wall_from_polished_deepslate_stonecutting","ae2:network/cells/item_storage_cell_4k_storage","create:cut_calcite_brick_wall_from_stone_types_calcite_stonecutting","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","minecraft:quartz_slab","handcrafted:phantom_trophy","railcraft:overalls","modularrouters:modular_router","mcwfurnitures:dark_oak_modern_chair","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","ae2:block_cutter/stairs/smooth_sky_stone_stairs","dyenamics:amber_wool","railcraft:steel_helmet","farmersdelight:iron_knife","allthecompressed:decompress/cobblestone_1x","mcwroofs:spruce_attic_roof","mcwroofs:nether_bricks_roof","ae2:network/cables/smart_black","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:mossy_cobblestone_wall_from_mossy_cobblestone_stonecutting","minecraft:observer","cfm:gray_kitchen_sink","minecraft:polished_deepslate_wall","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","allthecompressed:decompress/cobblestone_2x","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","mcwbridges:spruce_log_bridge_middle","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","ae2:block_cutter/stairs/fluix_stairs","farmersdelight:chicken_sandwich","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","enderio:iron_gear","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","railcraft:switch_track_lever","mcwpaths:andesite_honeycomb_paving","mcwwindows:birch_log_parapet","mcwbridges:sandstone_bridge","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","handcrafted:blue_bowl","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","minecraft:polished_blackstone_brick_slab","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","ae2:network/cables/smart_orange","minecraft:unobtainium_mage_helmet_smithing","dyenamics:dye_amber_carpet","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","twigs:polished_tuff_bricks_from_tuff_stonecutting","handcrafted:kitchen_hood","appbot:mana_storage_cell_4k","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","biomesoplenty:brimstone_cluster","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwbridges:birch_log_bridge_middle","xnet:connector_blue_dye","rftoolsbuilder:shape_card_liquid","create:polished_cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwroofs:black_lower_roof","mcwfurnitures:stripped_oak_desk","mcwroofs:gutter_base_blue","twigs:cut_amethyst_from_amethyst_block_stonecutting","reliquary:ender_staff","mcwwindows:diorite_louvered_shutter","minecraft:gray_dye","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","twigs:polished_amethyst_stonecutting","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","mcwfurnitures:stripped_spruce_bookshelf_drawer","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","cfm:stripped_dark_oak_park_bench","allthemodium:unobtainium_ingot_from_raw_blasting","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","ae2:network/parts/energy_acceptor","additionallanterns:gold_chain","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","ae2:materials/advancedcard","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","silentgear:blaze_gold_nugget","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","utilitarian:no_soliciting/soliciting_carpets/white_trapped_soliciting_carpet","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","mcwfurnitures:stripped_dark_oak_striped_chair","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwroofs:andesite_lower_roof","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","create:calcite_from_stone_types_calcite_stonecutting","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","mcwfurnitures:stripped_birch_modern_wardrobe","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","handcrafted:spruce_desk","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:spruce_wood","minecraft:iron_sword","mcwtrpdoors:birch_swamp_trapdoor","minecraft:spruce_fence","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwdoors:metal_reinforced_door","utilitarian:utility/birch_logs_to_trapdoors","domum_ornamentum:blue_cobblestone_extra","additionallanterns:blackstone_chain","additionallanterns:normal_lantern_red","domum_ornamentum:purple_brick_extra","mcwwindows:one_way_glass_pane","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","ae2:tools/fluix_upgrade_smithing_template","minecraft:chiseled_sandstone_from_sandstone_stonecutting","botania:petal_light_blue_double","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","gtceu:shaped/block_compress_platinum","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","appbot:mana_storage_cell_1k","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","minecraft:birch_trapdoor","mcwpaths:sandstone_flagstone_stairs","bigreactors:turbine/basic/activetap_fe","ae2:network/cables/covered_light_gray","mcwroofs:gutter_base_lime","everythingcopper:copper_anvil","deepresonance:tank","minecraft:prismarine_brick_stairs_from_prismarine_stonecutting","mcwlights:striped_lantern","minecraft:deepslate_brick_wall_from_deepslate_bricks_stonecutting","mcwfences:jungle_highley_gate","mcwtrpdoors:dark_oak_classic_trapdoor","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","create:birch_window","railcraft:coke_oven_bricks","cfm:birch_park_bench","mcwroofs:gutter_middle_red","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","minecraft:conduit","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","sophisticatedstorage:spruce_chest","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:cyan_sofa","mcwroofs:cyan_concrete_upper_lower_roof","cfm:stripped_dark_oak_chair","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","mcwfurnitures:dark_oak_counter","minecraft:powered_rail","mcwdoors:dark_oak_stable_door","mcwdoors:birch_bamboo_door","mcwwindows:diorite_pane_window","dyenamics:wine_stained_glass_pane_from_glass_pane","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","twigs:birch_table","cfm:dye_blue_picket_fence","enderio:resetting_lever_ten_from_prev","mcwbridges:end_stone_bricks_bridge","mcwroofs:magenta_concrete_upper_lower_roof","reliquary:crimson_cloth","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","aether:stone_pickaxe_repairing","forbidden_arcanus:clibano_combustion/diamond_from_clibano_combusting","minecraft:blue_terracotta","mcwroofs:purple_terracotta_lower_roof","supplementaries:stone_lamp","sophisticatedstorage:void_upgrade","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","mcwroofs:spruce_planks_upper_lower_roof","minecraft:gray_terracotta","minecraft:comparator","handcrafted:dark_oak_corner_trim","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","minecraft:netherite_boots_smithing","mob_grinding_utils:recipe_tintedglass","mcwfurnitures:stripped_birch_drawer_counter","cfm:stripped_spruce_coffee_table","mcwroofs:pink_concrete_attic_roof","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","mcwroofs:prismarine_brick_lower_roof","dyenamics:peach_terracotta","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","rftoolspower:powercell_card","mcwdoors:dark_oak_japanese2_door","minecraft:terracotta","delightful:knives/silver_knife","reliquary:alkahestry_altar","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:yellow_lamp","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","dimstorage:dimensional_tank","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","comforts:sleeping_bag_gray","twigs:copper_pillar_stonecutting","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","minecraft:red_dye_from_tulip","ae2:network/cells/fluid_storage_cell_4k_storage","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwlights:double_street_lamp","minecraft:beacon","railcraft:tin_gear","minecraft:tnt","mcwfurnitures:stripped_birch_bookshelf","minecraft:andesite_slab_from_andesite_stonecutting","mcwdoors:spruce_stable_door","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","cfm:spruce_upgraded_gate","minecraft:black_dye","biomesoplenty:pine_boat","cfm:stripped_birch_chair","minecraft:deepslate_brick_stairs_from_polished_deepslate_stonecutting","croptopia:shaped_sticky_toffee_pudding","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_birch_cabinet","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","minecraft:dune_armor_trim_smithing_template_smithing_trim","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","mekanism:steel_casing","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwfurnitures:spruce_cupboard_counter","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwdoors:dark_oak_waffle_door","domum_ornamentum:light_gray_brick_extra","ironfurnaces:augments/augment_speed","handcrafted:sandstone_pillar_trim","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","ae2:shaped/walls/smooth_sky_stone_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","ae2:network/crafting/256k_cpu_crafting_storage","mcwroofs:magenta_terracotta_lower_roof","railcraft:routing_table_book","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","sophisticatedstorage:storage_link","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:birch_planks_upper_lower_roof","mcwroofs:stone_attic_roof","mcwbridges:rope_dark_oak_bridge","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:item_shelf","handcrafted:terracotta_plate","supplementaries:stone_tile","handcrafted:calcite_corner_trim","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","rftoolsbuilder:space_chamber","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","supplementaries:flags/flag_white","silentgear:coating_smithing_template","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","cfm:dark_oak_desk_cabinet","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","mcwbridges:birch_rail_bridge","mcwfurnitures:dark_oak_striped_chair","minecraft:glass_pane","supplementaries:timber_brace","allthearcanistgear:unobtainium_robes_smithing","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","handcrafted:birch_cupboard","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","ae2:network/crafting/cpu_crafting_unit","mcwbridges:deepslate_brick_bridge","mcwbiomesoplenty:pine_pane_window","comforts:sleeping_bag_to_brown","mcwfurnitures:stripped_birch_lower_bookshelf_drawer","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","mcwfurnitures:stripped_dark_oak_coffee_table","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","mcwfurnitures:stripped_birch_coffee_table","ae2:network/blocks/interfaces_interface_part","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","additionallanterns:normal_lantern_colorless","mcwlights:magenta_paper_lamp","alltheores:lumium_plate","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_spruce_cupboard_counter","cfm:orange_kitchen_drawer","mcwroofs:spruce_planks_top_roof","ae2:materials/annihilationcore","enderio:cold_fire_igniter","croptopia:chicken_and_noodles","ae2:network/cables/dense_smart_from_smart","forbidden_arcanus:clibano_combustion/lapis_lazuli_from_clibano_combusting","silentgear:stone_rod","ae2:network/cables/covered_green","handcrafted:spruce_pillar_trim","minecraft:leather_boots","railcraft:tank_detector","minecraft:netherite_ingot_from_netherite_block","create:crafting/appliances/netherite_diving_boots_from_netherite","railcraft:steel_spike_maul","handcrafted:sandstone_corner_trim","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","mcwbridges:mossy_stone_brick_bridge","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","mcwlights:pink_paper_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","ae2:network/crystal_resonance_generator","mcwfurnitures:stripped_dark_oak_stool_chair","mcwfurnitures:birch_end_table","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","dyenamics:bubblegum_terracotta","minecraft:red_nether_brick_slab_from_red_nether_bricks_stonecutting","mcwwindows:jungle_plank_window","gtceu:smelting/smelt_iron_ore_to_ingot","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","rftoolscontrol:workbench","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","minecraft:dye_black_carpet","draconicevolution:components/draconium_ingot_from_ore","minecraft:chiseled_stone_bricks","mcwroofs:birch_upper_lower_roof","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","ae2:network/cables/covered_purple","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","dyenamics:dye_mint_carpet","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","minecraft:magma_block","mcwwindows:crimson_stem_parapet","minecraft:spruce_pressure_plate","enderio:resetting_lever_thirty_inv","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","mcwdoors:dark_oak_whispering_door","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","connectedglass:clear_glass_black_pane2","nethersdelight:nether_brick_smoker","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","handcrafted:spruce_nightstand","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","minecraft:deepslate_tiles_from_deepslate_bricks_stonecutting","sfm:cable","silentgear:raw_crimson_iron_from_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbridges:mossy_cobblestone_bridge_stair","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","ae2:network/parts/monitors_storage","twigs:mossy_cobblestone_brick_slab_from_mossy_cobblestone_stonecutting","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","minecraft:smooth_quartz_slab_from_smooth_quartz_stonecutting","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","utilitix:spruce_shulker_boat","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","mcwtrpdoors:dark_oak_blossom_trapdoor","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:dark_oak_fence","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","littlelogistics:fluid_hopper","railcraft:goggles","twigs:paper_lantern","mcwtrpdoors:dark_oak_beach_trapdoor","mcwdoors:birch_beach_door","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","twigs:polished_bloodstone_stonecutting","mcwdoors:birch_bark_glass_door","domum_ornamentum:brick_extra","comforts:hammock_cyan","travelersbackpack:iron","ae2:network/cables/covered_black","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","ae2:network/cables/dense_smart_purple","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","mcwfurnitures:birch_modern_desk","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwroofs:dark_oak_attic_roof","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","pneumaticcraft:liquid_compressor","create:polished_cut_calcite_wall_from_stone_types_calcite_stonecutting","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:mossy_stone_running_bond","minecraft:prismarine_brick_stairs","mcwpaths:stone_flagstone_slab","ae2:decorative/sky_stone_small_brick_from_stonecutting","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","reliquary:uncrafting/redstone","mcwwindows:cherry_four_window","handcrafted:dark_oak_bench","mcwwindows:granite_louvered_shutter","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","securitycraft:sentry","mcwfurnitures:dark_oak_triple_drawer","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","alltheores:lumium_ingot_from_dust_blasting","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","supplementaries:dispenser_minecart","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","utilitarian:no_soliciting/soliciting_carpets/blue_trapped_soliciting_carpet","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","twigs:smooth_stone_brick_stairs","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","ae2:tools/certus_quartz_spade","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","farmersdelight:skillet","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","minecraft:prismarine_slab_from_prismarine_stonecutting","alltheores:constantan_dust_from_alloy_blending","mcwroofs:black_concrete_upper_lower_roof","ae2:network/cables/dense_covered_black","mcwfurnitures:dark_oak_bookshelf","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","expatternprovider:epp_alt","utilitix:piston_controller_rail","minecraft:deepslate_tile_wall_from_deepslate_bricks_stonecutting","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","mcwpaths:mossy_cobblestone_basket_weave_paving","minecraft:green_terracotta","minecraft:white_stained_glass","expatternprovider:assembler_matrix_frame","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","allthemodium:vibranium_nugget_from_ingot","ae2:network/cells/fluid_storage_cell_64k","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","ae2:tools/paintballs_pink","minecraft:purpur_stairs","domum_ornamentum:white_brick_extra","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","minecraft:mossy_cobblestone_stairs","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","sfm:labelgun","sophisticatedstorage:dark_oak_chest","mcwfences:jungle_hedge","reliquary:angelic_feather","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","connectedglass:scratched_glass_blue2","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","mcwdoors:birch_tropical_door","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","supplementaries:altimeter","ae2:network/cables/dense_smart_gray","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwbridges:rope_spruce_bridge","twigs:bloodstone_slab","supplementaries:end_stone_lamp","allthecompressed:compress/deepslate_1x","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","mcwfurnitures:stripped_birch_double_wardrobe","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","minecraft:quartz_stairs","handcrafted:oven","minecraft:quartz_from_blasting","handcrafted:calcite_pillar_trim","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","minecraft:red_stained_glass","minecraft:light_gray_stained_glass_pane_from_glass_pane","mcwroofs:white_steep_roof","mcwpaths:cobbled_deepslate_crystal_floor_slab","minecraft:purpur_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","mcwpaths:cobbled_deepslate_crystal_floor","supplementaries:candle_holders/candle_holder_blue","rftoolsbuilder:shape_card_quarry_fortune","mcwfurnitures:stripped_dark_oak_bookshelf_drawer","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","connectedglass:borderless_glass_blue_pane2","mcwfurnitures:stripped_dark_oak_chair","mcwpaths:blackstone_crystal_floor","travelersbackpack:quartz","mcwfurnitures:cabinet_door","supplementaries:candle_holders/candle_holder_orange_dye","mcwwindows:stripped_spruce_pane_window","additionallanterns:cobbled_deepslate_chain","allthecompressed:compress/sand_1x","cfm:dark_oak_crate","twigs:mossy_cobblestone_bricks_stonecutting","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","twigs:polished_bloodstone","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","utilitarian:utility/dark_oak_logs_to_stairs","mcwwindows:stripped_dark_oak_log_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","littlelogistics:guide_rail_corner","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","dyenamics:wine_dye","handcrafted:bench","minecraft:gray_stained_glass_pane_from_glass_pane","mekanism:processing/osmium/ingot/from_raw_smelting","allthecompressed:compress/andesite_1x","cfm:birch_cabinet","allthecompressed:compress/sand_2x","mcwwindows:diorite_four_window","minecraft:stone_slab","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","cfm:gray_sofa","mcwroofs:red_nether_bricks_upper_lower_roof","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","silentgear:blaze_gold_ingot_from_block","create:small_calcite_brick_wall_from_stone_types_calcite_stonecutting","mekanism:tier_installer/basic","handcrafted:dark_oak_pillar_trim","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","travelersbackpack:backpack_tank","minecraft:blast_furnace","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbridges:glass_bridge","additionallanterns:normal_lantern_light_blue","mcwbiomesoplenty:pine_plank_four_window","ae2:materials/cardenergy","mcwroofs:blackstone_steep_roof","utilitarian:no_soliciting/soliciting_carpets/green_trapped_soliciting_carpet","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","minecraft:light_blue_stained_glass_pane_from_glass_pane","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","dyenamics:bed/mint_bed_frm_white_bed","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","ae2:materials/carddistribution","ae2:network/parts/import_bus","mcwfurnitures:dark_oak_double_drawer","croptopia:campfire_molasses","ae2:network/crafting/16k_cpu_crafting_storage","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","ae2:network/upgrade_wireless_crafting_terminal","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","minecraft:blue_stained_glass_pane_from_glass_pane","additionallanterns:netherite_chain","railcraft:water_tank_siding","expatternprovider:assembler_matrix_crafter","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","ae2:network/cables/smart_cyan","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","minecraft:fletching_table","handcrafted:pufferfish_trophy","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","rftoolsbuilder:space_chamber_controller","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","minecraft:mossy_cobblestone_wall","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","mcwwindows:one_way_glass","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwroofs:bricks_top_roof","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","mekanism:tier_installer/advanced","cfm:mangrove_mail_box","biomesoplenty:brimstone_bricks_from_brimstone_stonecutting","sophisticatedstorage:smelting_upgrade","mekanism:upgrade/energy","minecraft:oak_chest_boat","mcwpaths:mossy_stone_flagstone_path","mcwdoors:spruce_waffle_door","simplylight:illuminant_purple_block_on_toggle","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","allthecompressed:compress/grass_block_1x","minecraft:stone_pressure_plate","mcwroofs:spruce_planks_lower_roof","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","ae2additions:components/super/16k","connectedglass:borderless_glass_orange2","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","twigs:silt_from_silt_balls","pneumaticcraft:logistics_core","everythingcopper:copper_helmet","mcwroofs:red_concrete_upper_lower_roof","create:crafting/kinetics/yellow_seat","mcwfurnitures:dark_oak_modern_wardrobe","mcwpaths:andesite_windmill_weave_path","dyenamics:aquamarine_dye","mcwdoors:spruce_swamp_door","modularrouters:blank_upgrade","mcwdoors:spruce_western_door","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","ae2:shaped/walls/sky_stone_block","cfm:birch_chair","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","croptopia:cheeseburger","cfm:stripped_spruce_cabinet","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwroofs:green_terracotta_steep_roof","mcwdoors:spruce_barn_glass_door","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","travelersbackpack:diamond_smithing","mcwfurnitures:stripped_oak_cupboard_counter","ad_astra:orange_industrial_lamp","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","cfm:birch_desk","immersiveengineering:crafting/wirecoil_structure_steel","mcwfurnitures:spruce_coffee_table","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","farmersdelight:cooking/vegetable_soup","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","minecraft:dark_oak_trapdoor","mcwfences:iron_cheval_de_frise","additionallanterns:normal_lantern_brown","minecraft:iron_shovel","tombstone:dark_marble","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","ironfurnaces:furnaces/diamond_furnace","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwwindows:stone_brick_arrow_slit","ae2:smelting/smooth_sky_stone_block","chimes:amethyst_chimes","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","croptopia:ham_sandwich","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","minecraft:polished_deepslate_slab","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","quark:tweaks/crafting/clear_glass","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwwindows:dark_oak_louvered_shutter","mcwdoors:dark_oak_glass_door","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","minecraft:deepslate_bricks","create:calcite_pillar_from_stone_types_calcite_stonecutting","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","handcrafted:birch_table","enderio:basic_fluid_filter","rftoolsutility:counter_module","aether:netherite_boots_repairing","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","minecraft:oak_door","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:black_roof_block","mcwfurnitures:stripped_birch_wardrobe","handcrafted:spruce_dining_bench","advancedperipherals:peripheral_casing","mcwfurnitures:birch_table","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwroofs:spruce_planks_roof","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","minecraft:spruce_door","mcwfurnitures:stripped_birch_covered_desk","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:dark_oak_bookshelf_drawer","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","mcwroofs:lime_concrete_upper_lower_roof","mcwfurnitures:dark_oak_lower_bookshelf_drawer","mcwbiomesoplenty:magic_highley_gate","utilitarian:utility/birch_logs_to_doors","minecraft:dark_prismarine","utilitarian:no_soliciting/soliciting_carpets/orange_trapped_soliciting_carpet","ae2:tools/fluix_sword","sophisticatedstorage:birch_limited_barrel_1","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","mcwwindows:pink_curtain","rftoolspower:pearl_injector","dyenamics:mint_terracotta","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","sophisticatedstorage:birch_limited_barrel_4","sophisticatedstorage:birch_limited_barrel_2","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","sophisticatedstorage:birch_limited_barrel_3","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","biomesoplenty:fir_chest_boat","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","minecraft:bow","supplementaries:wrench","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","deepresonance:radiation_suit_boots","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","expatternprovider:silicon_block_disassembler","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","botania:mushroom_stew","minecraft:andesite_stairs","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","dyenamics:amber_dye","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","mcwdoors:dark_oak_classic_door","cfm:black_cooler","twigs:polished_calcite_stonecutting","supplementaries:sconce","mcwfurnitures:stripped_dark_oak_double_drawer","silentgear:rough_rod","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","mcwpaths:mossy_stone_crystal_floor_slab","dyenamics:maroon_stained_glass_pane_from_glass_pane","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:birch_top_roof","aether:skyroot_bed","mcwroofs:prismarine_brick_roof","mcwbiomesoplenty:palm_wired_fence","mcwfurnitures:dark_oak_cupboard_counter","mcwroofs:red_nether_bricks_attic_roof","railcraft:iron_tunnel_bore_head","mcwfurnitures:spruce_glass_table","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","dyenamics:bed/aquamarine_bed_frm_white_bed","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwfurnitures:spruce_covered_desk","botania:ghost_rail","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","minecraft:black_dye_from_wither_rose","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","nethersdelight:soul_compost_from_warped_roots","mcwdoors:spruce_mystic_door","sophisticatedstorage:backpack_advanced_void_upgrade_from_storage_advanced_void_upgrade","mcwtrpdoors:spruce_beach_trapdoor","aether:netherite_shovel_repairing","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","mcwbridges:balustrade_nether_bricks_bridge","pneumaticcraft:wall_lamp_inverted_brown","cfm:stripped_birch_bedside_cabinet","mcwroofs:andesite_roof","supplementaries:pedestal","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","ae2:network/cables/glass_yellow","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","alltheores:ruby_dust_from_hammer_crushing","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwpaths:sandstone_flagstone_path","dyenamics:rose_terracotta","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","twigs:crimson_table","mcwfurnitures:stripped_birch_triple_drawer","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","minecraft:blackstone_stairs","handcrafted:dark_oak_couch","cfm:lime_kitchen_counter","minecraft:dye_orange_carpet","mcwbridges:mossy_cobblestone_bridge","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","mcwroofs:red_nether_bricks_steep_roof","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","minecraft:deepslate_tile_slab_from_deepslate_bricks_stonecutting","securitycraft:speed_module","cfm:stripped_oak_kitchen_sink_dark","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","blue_skies:glowing_poison_stone","tombstone:carmin_marble","mcwroofs:brown_terracotta_steep_roof","crafting_on_a_stick:crafting_table","mcwdoors:oak_barn_door","mcwdoors:dark_oak_cottage_door","botania:alchemy_catalyst","allthemodium:ancient_stone_bricks","mcwfurnitures:spruce_double_drawer_counter","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","dyenamics:dye_bubblegum_carpet","botania:redstone_root","ae2:tools/paintballs_brown","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","dyenamics:dye_aquamarine_carpet","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","minecraft:purpur_slab_from_purpur_block_stonecutting","twigs:smooth_stone_brick_slab_from_smooth_stone_brick_stonecutting","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","supplementaries:timber_cross_brace","mcwfurnitures:stripped_dark_oak_cupboard_counter","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","reliquary:fertile_essence","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","ae2:network/cables/dense_smart_light_gray","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","rftoolsutility:environmental_controller","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","mcwbridges:mossy_stone_bridge_pier","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","mcwroofs:white_concrete_attic_roof","xnet:controller","cookingforblockheads:sink","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwfurnitures:dark_oak_end_table","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","reliquary:uncrafting/ender_pearl","additionallanterns:obsidian_chain","mcwfurnitures:stripped_spruce_coffee_table","minecraft:ender_eye","supplementaries:blackstone_lamp","minecraft:polished_deepslate_wall_from_polished_deepslate_stonecutting","mcwfurnitures:spruce_double_wardrobe","mcwpaths:brick_strewn_rocky_path","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","minecraft:birch_slab","enderio:resetting_lever_three_hundred","mcwlights:cross_lantern","mcwlights:brown_lamp","mcwwindows:bricks_four_window","railcraft:receiver_circuit","cfm:stripped_dark_oak_coffee_table","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","mcwtrpdoors:birch_tropical_trapdoor","minecraft:pink_stained_glass_pane_from_glass_pane","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","ae2:network/parts/terminals_pattern_access","thermal:smelting/niter_from_smelting","additionallanterns:diamond_chain","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","mcwfurnitures:birch_triple_drawer","ae2:network/blocks/io_condenser","ae2:block_cutter/walls/quartz_wall","supplementaries:stonecutting/stone_tile","ae2:network/cables/covered_pink","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","handcrafted:birch_shelf","additionallanterns:normal_lantern_purple","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","minecraft:dye_black_wool","enderio:dark_steel_trapdoor","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","rftoolsbuilder:shape_card_quarry_silk","pneumaticcraft:reinforced_brick_from_slab","minecraft:cooked_beef","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","mcwpaths:mossy_stone_flagstone_stairs","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","allthemodium:allthemodium_ingot_from_raw_blasting","mcwroofs:red_nether_bricks_roof","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","forbidden_arcanus:arcane_chiseled_darkstone","mcwfurnitures:stripped_dark_oak_bookshelf_cupboard","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","railcraft:age_detector","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","pneumaticcraft:wall_lamp_orange","minecraft:gold_nugget_from_smelting","dyenamics:wine_stained_glass","mcwwindows:black_curtain","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","ae2:network/cables/dense_smart_black","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","twigs:smooth_stone_brick_wall","mcwfurnitures:stripped_dark_oak_lower_triple_drawer","mcwfurnitures:stripped_birch_end_table","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","enderio:resetting_lever_five_inv","twigs:calcite_stairs","handcrafted:spruce_corner_trim","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","mcwpaths:stone_running_bond","minecraft:deepslate_brick_wall_from_polished_deepslate_stonecutting","mcwfurnitures:stripped_birch_modern_desk","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","ae2:network/cables/smart_blue","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","botania:turntable","cfm:stripped_birch_table","ae2:network/cells/item_storage_components_cell_1k_part","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","domum_ornamentum:black_brick_extra","supplementaries:daub","railcraft:steel_chestplate","alltheores:zinc_dust_from_hammer_crushing","minecraft:netherite_scrap","mcwfences:oak_hedge","mcwdoors:spruce_japanese2_door","minecraft:cut_sandstone","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","ironfurnaces:furnaces/gold_furnace","ae2:decorative/quartz_block","ae2:network/cables/dense_smart_orange","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","aether:chainmail_boots_repairing","mcwfurnitures:birch_drawer_counter","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:nether_brick_slab","mcwfurnitures:birch_bookshelf","ae2:block_cutter/slabs/smooth_sky_stone_slab","supplementaries:blackstone_tile","mcwbiomesoplenty:hellbark_highley_gate","minecraft:deepslate_tile_stairs_from_polished_deepslate_stonecutting","minecraft:enchanting_table","mcwpaths:cobbled_deepslate_square_paving","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","mcwtrpdoors:dark_oak_barn_trapdoor","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","dyenamics:peach_stained_glass_pane_from_glass_pane","ad_astra:small_blue_industrial_lamp","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","mcwpaths:mossy_stone_running_bond_path","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:logistics_configurator","create:cut_tuff_from_stone_types_tuff_stonecutting","rftoolsutility:dialing_device","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","comforts:hammock_to_cyan","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","mcwroofs:dark_oak_planks_roof","mcwwindows:birch_four_window","biomesoplenty:fir_boat","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwdoors:dark_oak_swamp_door","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","mcwroofs:blue_terracotta_attic_roof","handcrafted:spider_trophy","allthecompressed:compress/calcite_1x","alltheores:gold_dust_from_hammer_crushing","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","croptopia:shaped_beef_stew","ae2:network/cables/dense_smart_green","pneumaticcraft:wall_lamp_white","mcwroofs:red_terracotta_lower_roof","sophisticatedstorage:backpack_crafting_upgrade_from_storage_crafting_upgrade","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","xnet:connector_green","advanced_ae:smallappupgrade","minecraft:gold_nugget_from_blasting","create:oak_window","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","mcwpaths:cobbled_deepslate_running_bond_slab","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","appbot:mana_storage_cell_16k","cfm:gray_picket_fence","buildinggadgets2:gadget_cut_paste","reliquary:mob_charm_fragments/cave_spider","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","mcwfurnitures:spruce_modern_desk","rftoolsbuilder:mover_status","mcwfurnitures:spruce_drawer","simplylight:illuminant_light_blue_block_dyed","dyenamics:fluorescent_stained_glass_pane_from_glass_pane","handcrafted:cyan_sheet","railcraft:controller_circuit","railcraft:world_spike_minecart","twigs:polished_rhyolite_stonecutting","handcrafted:deepslate_corner_trim","minecraft:spruce_sign","mcwroofs:gray_top_roof","cfm:stripped_dark_oak_bedside_cabinet","supplementaries:candle_holders/candle_holder_green","handcrafted:dark_oak_shelf","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","utilitix:oak_shulker_boat_with_shell","sophisticatedstorage:copper_to_iron_tier_upgrade","handcrafted:spruce_side_table","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","minecraft:leather_helmet","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","minecraft:chiseled_nether_bricks","mekanism:fluid_tank/basic","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","minecraft:nether_brick_stairs","pneumaticcraft:crop_support","mcwfurnitures:dark_oak_stool_chair","supplementaries:bubble_blower","naturalist:glow_goop","dyenamics:conifer_stained_glass_pane_from_glass_pane","supplementaries:crank","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","minecraft:magenta_dye_from_blue_red_white_dye","alltheores:iron_plate","cfm:stripped_birch_park_bench","allthearcanistgear:vibranium_leggings_smithing","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","allthecompressed:compress/ancient_stone_1x","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","minecraft:pink_terracotta","mcwbiomesoplenty:mahogany_plank_pane_window","rftoolsutility:destination_analyzer","mcwfences:prismarine_railing_gate","mcwfurnitures:stripped_dark_oak_double_drawer_counter","mcwbiomesoplenty:willow_highley_gate","ae2:materials/cardfuzzy","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwtrpdoors:spruce_barred_trapdoor","ae2:network/cables/dense_smart_light_blue","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","cfm:spruce_upgraded_fence","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","ae2:network/cables/smart_fluix_clean","ae2:network/parts/annihilation_plane_alt","mekanism:energy_tablet","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","railcraft:standard_rail_from_rail","allthecompressed:decompress/gravel_2x","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","additionallanterns:mossy_cobblestone_chain","advanced_ae:advpartenc","mcwroofs:black_top_roof","mcwfences:modern_nether_brick_wall","minecraft:prismarine_stairs","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","cfm:oak_kitchen_counter","pneumaticcraft:compressed_brick_stairs_from_bricks_stonecutting","minecraft:golden_hoe","minecraft:fire_charge","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","deeperdarker:gold_ingot_from_smelting_gloomslate_gold_ore","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","alltheores:platinum_dust_from_hammer_crushing","cfm:stripped_spruce_bedside_cabinet","railcraft:any_detector","biomesoplenty:mahogany_boat","quark:tweaks/crafting/utility/misc/charcoal_to_black_dye","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","sophisticatedstorage:controller","mcwroofs:stone_bricks_upper_steep_roof","advanced_ae:reactionchamber","biomesoplenty:palm_chest_boat","minecraft:stone_button","mcwfurnitures:birch_desk","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","cfm:purple_picket_fence","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","aether:golden_pickaxe_repairing","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:no_soliciting/soliciting_carpets/gray_trapped_soliciting_carpet","minecraft:lodestone","occultism:crafting/magic_lamp_empty","handcrafted:birch_counter","mcwlights:black_lamp","minecraft:purpur_block","croptopia:vanilla_ice_cream","railcraft:brass_ingot_crafted_with_ingots","megacells:crafting/greater_energy_card","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwdoors:spruce_four_panel_door","mcwpaths:sandstone_running_bond_stairs","minecraft:polished_blackstone_slab","domum_ornamentum:blue_brick_extra","mcwbiomesoplenty:mahogany_window","cfm:stripped_spruce_chair","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:quartz","minecraft:nether_brick_wall","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","silentgear:blaze_gold_block","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","allthemodium:unobtainium_dust_from_ore_crushing","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","minecraft:redstone_from_blasting_redstone_ore","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","ae2:tools/paintballs_light_blue","mcwbiomesoplenty:umbran_plank_four_window","cfm:stripped_crimson_mail_box","tombstone:grave_plate","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","connectedglass:scratched_glass_blue_pane2","ae2:network/parts/terminals","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","croptopia:hamburger","enderio:resetting_lever_thirty_inv_from_prev","croptopia:fried_calamari","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","cfm:spruce_bedside_cabinet","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","utilitix:birch_shulker_boat","pneumaticcraft:vortex_tube","undergarden:wigglewood_chest_boat","xnet:connector_routing","mcwwindows:warped_louvered_shutter","blue_skies:glowing_nature_stone","handcrafted:brown_sheet","handcrafted:birch_nightstand","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","handcrafted:dark_oak_drawer","travelersbackpack:cake","comforts:sleeping_bag_cyan","megacells:network/mega_interface","minecraft:birch_stairs","mcwroofs:green_terracotta_attic_roof","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","immersiveengineering:crafting/shield","mcwfurnitures:dark_oak_glass_table","aether:blue_cape_cyan_wool","utilitix:directional_highspeed_rail","dyenamics:aquamarine_stained_glass_pane_from_glass_pane","mcwdoors:birch_stable_head_door","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","sophisticatedstorage:birch_barrel","immersiveengineering:crafting/blastbrick","mcwfurnitures:stripped_spruce_double_drawer","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","ae2:network/crafting/cpu_crafting_monitor","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","mcwroofs:prismarine_brick_top_roof","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","rftoolsutility:crafter1","ae2:network/blocks/storage_chest","mcwroofs:oak_roof","aether:diamond_boots_repairing","minecraft:mangrove_chest_boat","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","xnet:netcable_routing","mekanism:processing/osmium/ingot/from_ore_smelting","mcwfences:nether_brick_pillar_wall","mcwfurnitures:birch_striped_chair","alltheores:smelting_dust/tin_ingot","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","mcwbridges:balustrade_prismarine_bricks_bridge","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwfurnitures:dark_oak_coffee_table","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","ae2:tools/paintballs_orange","mcwfences:blackstone_brick_railing_gate","mcwroofs:dark_oak_planks_upper_lower_roof","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","minecraft:iron_ingot_from_nuggets","mcwpaths:mossy_stone_running_bond_slab","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","mcwdoors:birch_glass_door","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","minecraft:gray_bed","cfm:dark_oak_coffee_table","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","xnet:wireless_router","cfm:stripped_dark_oak_kitchen_counter","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwwindows:dark_prismarine_brick_gothic","minecraft:white_terracotta","additionallanterns:normal_lantern_pink","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","mcwfurnitures:oak_double_drawer_counter","cfm:blue_grill","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","travelersbackpack:brown_sleeping_bag","pneumaticcraft:compressed_bricks_from_tile_stonecutting","twigs:chiseled_bricks","minecraft:blue_dye_from_cornflower","silentgear:blaze_gold_dust_blasting","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","ae2:network/parts/panels_monitor","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","mcwroofs:nether_bricks_upper_lower_roof","mcwbiomesoplenty:fir_pyramid_gate","cfm:birch_bedside_cabinet","minecraft:brown_banner","xnet:netcable_yellow","mcwroofs:blue_terracotta_roof","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","dyenamics:navy_wool","alltheores:platinum_gear","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","mcwroofs:spruce_top_roof","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","rftoolscontrol:tank","mcwfurnitures:stripped_birch_drawer","mcwwindows:gray_mosaic_glass_pane","additionallanterns:normal_lantern_orange","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","create:cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","reliquary:void_tear","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","mcwbridges:dark_oak_bridge_pier","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","minecraft:chiseled_stone_bricks_stone_from_stonecutting","handcrafted:blue_cup","pneumaticcraft:compressed_brick_pillar","mcwfurnitures:stripped_birch_double_drawer_counter","mcwbiomesoplenty:fir_hedge","minecraft:netherite_sword_smithing","pneumaticcraft:compressed_brick_pillar_from_bricks_stonecutting","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","utilitarian:utility/dark_oak_logs_to_pressure_plates","mcwbridges:dark_oak_rail_bridge","cfm:dye_orange_picket_fence","mcwdoors:oak_cottage_door","ae2:block_cutter/slabs/fluix_slab","handcrafted:spruce_counter","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:metal_hospital_door","arseng:source_storage_cell_16k","railcraft:signal_circuit","ae2:tools/paintballs_red","minecraft:red_dye_from_poppy","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","dyenamics:dye_conifer_carpet","mcwbiomesoplenty:pine_window2","minecraft:prismarine_brick_slab_from_prismarine_stonecutting","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","connectedglass:borderless_glass_pane1","mcwtrpdoors:birch_mystic_trapdoor","mcwfurnitures:birch_bookshelf_cupboard","croptopia:buttered_toast","minecraft:gray_banner","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","cfm:pink_cooler","rftoolsbuilder:vehicle_builder","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","cfm:red_grill","create:small_calcite_brick_slab_from_stone_types_calcite_stonecutting","ae2:network/cells/fluid_storage_cell_16k_storage","ae2:tools/paintballs_gray","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","ad_astra:blue_industrial_lamp","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","allthecompressed:decompress/gravel_1x","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","utilitarian:no_soliciting/soliciting_carpets/lime_trapped_soliciting_carpet","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","croptopia:chicken_and_dumplings","mekanism:transmitter/mechanical_pipe/basic","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","utilitix:tiny_charcoal_to_tiny","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","allthecompressed:compress/redstone_block_1x","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","minecraft:purpur_stairs_from_purpur_block_stonecutting","mcwtrpdoors:dark_oak_barrel_trapdoor","mcwroofs:blue_concrete_roof","alltheores:smelting_dust/osmium_ingot","additionallanterns:prismarine_chain","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwroofs:dark_prismarine_steep_roof","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","mcwbridges:spruce_rail_bridge","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","sophisticatedstorage:spruce_limited_barrel_3","sophisticatedstorage:spruce_limited_barrel_2","sophisticatedstorage:spruce_limited_barrel_1","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","pneumaticcraft:air_cannon","sophisticatedstorage:spruce_limited_barrel_4","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","minecraft:polished_blackstone_from_blackstone_stonecutting","twigs:warped_table","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwfurnitures:stripped_dark_oak_desk","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_bricks_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","twilightforest:wood/twilight_oak_planks","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwfurnitures:dark_oak_modern_desk","minecraft:blue_candle","railcraft:blast_furnace_bricks","mekanismtools:netherite_paxel","dyenamics:amber_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_pink","handcrafted:terracotta_wide_pot","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","reliquary:uncrafting/glass_bottle","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","cfm:gray_picket_gate","mcwfurnitures:spruce_stool_chair","mcwroofs:purple_terracotta_upper_lower_roof","ae2:network/cells/item_storage_cell_16k_storage","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","biomesoplenty:orange_dye_from_burning_blossom","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","blue_skies:cake_compat","minecraft:mangrove_planks","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_bricks_stonecutting","mcwpaths:mossy_stone_windmill_weave_path","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","mcwdoors:spruce_stable_head_door","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","gtceu:smelting/smelt_dust_iron_to_ingot","twigs:bloodstone_stairs","handcrafted:wood_cup","reliquary:phoenix_down","twilightforest:mining_boat","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","additionallanterns:normal_nether_bricks_chain","mcwroofs:dark_oak_planks_top_roof","mcwwindows:window_half_bar_base","utilitarian:utility/dark_oak_logs_to_boats","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwdoors:birch_modern_door","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:fried_chicken","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","cfm:birch_upgraded_gate","rftoolsbuilder:shape_card_quarry","gtceu:smelting/smelt_apatite_ore_to_ingot","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","rftoolsbuilder:mover_control","xnet:advanced_connector_yellow","ae2:network/parts/terminals_crafting","create:deepslate_from_stone_types_deepslate_stonecutting","twigs:calcite_wall","mcwfurnitures:stripped_dark_oak_lower_bookshelf_drawer","cfm:stripped_oak_chair","xnet:netcable_blue","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","mcwwindows:oak_shutter","mcwroofs:lime_concrete_steep_roof","connectedglass:scratched_glass_black2","cfm:rock_path","minecraft:wooden_hoe","mekanismgenerators:generator/heat","minecraft:cooked_beef_from_campfire_cooking","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","ironfurnaces:augments/augment_generator","mcwroofs:nether_bricks_top_roof","ae2:network/parts/energy_level_emitter","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","mcwroofs:nether_bricks_lower_roof","ad_astra:black_industrial_lamp","twilightforest:wood/spruce_banister","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwroofs:dark_oak_steep_roof","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","allthecompressed:compress/birch_log_1x","securitycraft:panic_button","mcwtrpdoors:birch_bark_trapdoor","mcwfurnitures:birch_modern_chair","mcwtrpdoors:spruce_glass_trapdoor","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","allthemodium:unobtainium_ingot_from_dust_blasting","biomesoplenty:brimstone_bud","mcwfurnitures:stripped_spruce_double_wardrobe","aether:obsidian_from_bucket_freezing","minecraft:blue_dye","mcwfurnitures:stripped_dark_oak_large_drawer","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","mcwwindows:crimson_planks_four_window","minecraft:piston","mcwlights:white_lamp","appmek:chemical_storage_cell_4k","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","dyenamics:ultramarine_stained_glass_pane_from_glass_pane","allthemodium:allthemodium_ingot_from_raw_smelting","ae2:network/cells/item_storage_components_cell_16k_part","mcwbiomesoplenty:umbran_plank_pane_window","botania:virus_necrodermal","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwfurnitures:dark_oak_lower_triple_drawer","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","mcwroofs:gray_upper_steep_roof","mcwroofs:birch_planks_steep_roof","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","minecraft:trapped_chest","mcwfences:spruce_horse_fence","croptopia:beef_jerky","handcrafted:oak_shelf","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","handcrafted:gray_cushion","mcwfences:crimson_curved_gate","cfm:stripped_birch_kitchen_counter","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","mcwtrpdoors:spruce_mystic_trapdoor","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","silentgear:raw_crimson_iron_block","supplementaries:flags/flag_magenta","allthecompressed:compress/soul_soil_1x","minecraft:diamond_axe","mcwbridges:end_stone_bricks_bridge_pier","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","mcwfurnitures:birch_covered_desk","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","appmek:chemical_storage_cell_1k","mcwfurnitures:stripped_dark_oak_modern_chair","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","travelersbackpack:skeleton","quark:tweaks/crafting/utility/tools/stone_pickaxe","handcrafted:creeper_trophy","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","minecraft:dye_blue_wool","minecraft:dark_oak_pressure_plate","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","handcrafted:birch_side_table","packingtape:tape","mcwpaths:blackstone_flagstone_path","mcwtrpdoors:dark_oak_cottage_trapdoor","rftoolsbuilder:vehicle_control_module","utilitarian:utility/spruce_logs_to_pressure_plates","cfm:birch_kitchen_counter","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","enderio:dark_steel_nugget_to_ingot","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","mcwdoors:birch_mystic_door","securitycraft:reinforced_crimson_fence_gate","mcwbiomesoplenty:rainbow_birch_hedge","handcrafted:dark_oak_dining_bench","utilitarian:utility/birch_logs_to_stairs","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","mcwfurnitures:birch_double_drawer","enderio:fluid_conduit","ad_astra:small_black_industrial_lamp","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","mcwpaths:mossy_stone_crystal_floor_stairs","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:prismarine_brick_upper_steep_roof","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","create:polished_cut_calcite_from_stone_types_calcite_stonecutting","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","mcwfurnitures:stripped_birch_double_drawer","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfurnitures:stripped_dark_oak_covered_desk","mcwfences:dark_oak_picket_fence","connectedglass:borderless_glass_black_pane2","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwfurnitures:stripped_birch_glass_table","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","pylons:expulsion_pylon","create:dark_oak_window","mcwfences:bamboo_highley_gate","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","eidolon:smelt_lead_dust","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","ae2:tools/paintballs_blue","dyenamics:bed/ultramarine_bed_frm_white_bed","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_spruce_wardrobe","ae2:tools/paintballs_lime","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","utilitarian:no_soliciting/soliciting_carpets/light_blue_trapped_soliciting_carpet","mcwwindows:white_curtain","mcwroofs:white_terracotta_roof","minecraft:torch","ad_astra:gray_flag","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","cfm:light_gray_grill","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","productivetrees:planks/firecracker_planks","mcwfurnitures:birch_large_drawer","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:dark_oak_covered_desk","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","everythingcopper:copper_hopper","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","twilightforest:wood/twilight_oak_wood","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","travelersbackpack:gray_sleeping_bag","minecraft:crafting_table","dyenamics:lavender_stained_glass","dyenamics:cherenkov_dye","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","cfm:dye_black_picket_gate","minecraft:sandstone_wall","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","cfm:pink_trampoline","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","pneumaticcraft:compressed_brick_slab_from_bricks_stonecutting","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/smart_gray","comforts:sleeping_bag_brown","mcwpaths:andesite_crystal_floor_stairs","botania:red_string_dispenser","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","mcwroofs:sandstone_lower_roof","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","create:crafting/logistics/pulse_extender","mcwroofs:red_nether_bricks_upper_steep_roof","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","enderio:resetting_lever_five_from_inv","mcwpaths:sandstone_basket_weave_paving","sophisticatedstorage:stack_upgrade_tier_1","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","sophisticatedstorage:stack_upgrade_tier_3","mcwwindows:stripped_acacia_log_four_window","silentgear:crimson_iron_raw_ore_blasting","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","sophisticatedstorage:stack_upgrade_tier_4","rftoolsutility:matter_beamer","ae2:decorative/quartz_fixture","sophisticatedstorage:stack_upgrade_tier_5","ae2:network/cells/item_storage_cell_16k","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","mcwfences:mangrove_curved_gate","littlelogistics:automatic_switch_rail","minecraft:carrot_on_a_stick","quark:tweaks/crafting/utility/tools/stone_axe","rftoolspower:blazing_agitator","railcraft:signal_capacitor_box","allthemodium:unobtainium_ingot_from_raw_smelting","mcwwindows:spruce_curtain_rod","cfm:dye_blue_picket_gate","forbidden_arcanus:clibano_combustion/coal_from_clibano_combusting","ae2:network/blocks/quantum_ring","aether:skyroot_loom","minecraft:brick_wall","nethersdelight:diamond_machete","utilitix:advanced_brewery","aether:netherite_helmet_repairing","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwroofs:red_nether_bricks_top_roof","enderio:dark_steel_nugget","expatternprovider:cobblestone_cell","minecraft:raw_iron_block","minecraft:dark_oak_planks","mcwwindows:stone_window2","minecraft:oak_button","allthecompressed:decompress/sand_1x","mcwroofs:prismarine_brick_attic_roof","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","croptopia:fruit_smoothie","mcwdoors:dark_oak_paper_door","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","pneumaticcraft:compressed_brick_stairs","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwtrpdoors:birch_barred_trapdoor","mcwdoors:dark_oak_western_door","pneumaticcraft:tube_junction","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","mcwdoors:birch_waffle_door","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","minecraft:mossy_cobblestone_slab_from_mossy_cobblestone_stonecutting","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","comforts:hammock_brown","securitycraft:reinforced_purple_stained_glass_pane_from_dye","mcwroofs:dark_oak_planks_steep_roof","aquaculture:bobber","mcwwindows:quartz_window2","mcwdoors:print_dark_oak","mcwpaths:cobblestone_honeycomb_paving","minecraft:dark_oak_fence_gate","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","simplylight:illuminant_brown_block_dyed","enderio:void_chassis","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","bigreactors:energizer/computerport","littlelogistics:vacuum_barge","allthemodium:allthemodium_apple","mcwroofs:lime_terracotta_upper_lower_roof","mcwpaths:mossy_stone_running_bond_stairs","expatternprovider:silicon_block","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mekanism:tier_installer/elite","itemcollectors:basic_collector","handcrafted:dark_oak_fancy_bed","minecraft:loom","supplementaries:sign_post_spruce","enderio:resetting_lever_three_hundred_inv","croptopia:banana_cream_pie","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","domum_ornamentum:red_brick_extra","minecraft:arrow","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","mcwroofs:light_gray_concrete_steep_roof","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","botania:green_shiny_flower","mcwbiomesoplenty:origin_hedge","rftoolsutility:tank","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","handcrafted:dark_oak_counter","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","cfm:pink_picket_fence","mcwroofs:pink_terracotta_steep_roof","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","mcwdoors:dark_oak_beach_door","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","minecraft:polished_blackstone","utilitarian:utility/spruce_logs_to_stairs","handcrafted:blue_crockery_combo","croptopia:shaped_scones","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","dyenamics:bed/maroon_bed_frm_white_bed","reliquary:uncrafting/slime_ball","rftoolsbase:dimensionalshard","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","mcwpaths:dark_oak_planks_path","cfm:brown_cooler","xnet:netcable_red","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","minecraft:spruce_trapdoor","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","bigreactors:reprocessor/wasteinjector","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","comforts:sleeping_bag_to_gray","allthemodium:ancient_stone_brick_slabs","undergarden:shard_torch","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","cfm:birch_upgraded_fence","minecraft:respawn_anchor","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","botania:petal_yellow","dyenamics:persimmon_stained_glass_pane_from_glass_pane","minecraft:deepslate_brick_slab","expatternprovider:ebus_in","handcrafted:birch_corner_trim","dankstorage:dock","mcwfurnitures:stripped_birch_lower_triple_drawer","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","sophisticatedstorage:shulker_box","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwfurnitures:spruce_desk","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","alltheores:signalum_dust_from_alloy_blending","immersiveengineering:crafting/component_steel","botania:yellow_shiny_flower","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","ae2:tools/paintballs_purple","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwtrpdoors:spruce_swamp_trapdoor","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","minecraft:snow_block","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cells/item_storage_cell_64k_storage","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","ae2:network/cables/covered_light_blue","handcrafted:spruce_cupboard","minecraft:iron_axe","mcwroofs:dark_prismarine_roof","create:crafting/kinetics/fluid_valve","ae2:decorative/quartz_vibrant_glass","enderio:resetting_lever_thirty_inv_from_base","supplementaries:crystal_display","minecraft:composter","minecraft:sandstone_stairs","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","allthemodium:ancient_stone_brick_wall","aether:aether_gold_nugget_from_blasting","handcrafted:birch_dining_bench","mcwroofs:dark_oak_upper_steep_roof","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","ae2:network/cables/smart_brown","appmek:chemical_storage_cell_16k","create:crafting/kinetics/white_seat","paraglider:cosmetic/paraglider","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","dyenamics:mint_stained_glass_pane_from_glass_pane","minecraft:prismarine_slab","ae2:network/cables/covered_orange","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","travelersbackpack:ghast_smithing","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:dark_oak_drawer_counter","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwdoors:birch_nether_door","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","cfm:light_gray_picket_fence","minecraft:deepslate_bricks_from_polished_deepslate_stonecutting","mcwroofs:red_terracotta_top_roof","mcwdoors:dark_oak_barn_door","quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions","minecraft:cobbled_deepslate_slab","minecraft:kjs/rftoolsbuilder_builder","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","dyenamics:dye_spring_green_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","mcwfurnitures:stripped_spruce_stool_chair","securitycraft:reinforced_light_gray_stained_glass","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","ad_astra:cyan_flag","allthemodium:allthemodium_dust_from_ore_crushing","mcwroofs:bricks_attic_roof","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","reliquary:uncrafting/spider_eye","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","cfm:pink_picket_gate","alchemistry:combiner","mcwroofs:birch_steep_roof","occultism:crafting/brush","ae2:network/cables/glass_white","minecraft:end_stone_bricks","croptopia:egg_roll","connectedglass:borderless_glass_black2","railcraft:iron_gear","minecraft:red_stained_glass_pane_from_glass_pane","sophisticatedstorage:chipped/loom_table_upgrade","railcraft:solid_fueled_firebox","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","minecraft:black_candle","ae2:network/cells/item_storage_cell_1k_storage","minecraft:smooth_quartz_stairs","dyenamics:bed/lavender_bed_frm_white_bed","twigs:silt_brick","twigs:smooth_stone_brick_stairs_from_smooth_stone_brick_stonecutting","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","minecraft:orange_candle","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","botania:quartz_sunny","create:framed_glass_from_glass_colorless_stonecutting","biomesoplenty:brimstone_fumarole","handcrafted:birch_chair","enderio:infinity_rod","railcraft:signal_lamp","handcrafted:dark_oak_chair","reliquary:uncrafting/sugar","mcwpaths:mossy_stone_windmill_weave_slab","tombstone:bone_needle","minecraft:stone_brick_wall","utilitix:reinforced_filter_rail","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","mcwroofs:dark_oak_roof","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:decoration_table","pneumaticcraft:compressed_brick_wall_from_bricks_stonecutting","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","minecraft:light_blue_dye_from_blue_white_dye","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","delightful:knives/draco_arcanus_knife","utilitarian:utility/glow_ink_sac","minecraft:cyan_dye","mcwwindows:acacia_window2","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","cfm:brown_sofa","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","enderio:stone_gear_upgrade","botania:light_blue_petal_block","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","minecraft:gold_block","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","ae2:network/cables/dense_smart_cyan","travelersbackpack:dye_orange_sleeping_bag","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","ironfurnaces:augments/augment_factory","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","mcwfurnitures:stripped_birch_striped_chair","cfm:spruce_kitchen_counter","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwfurnitures:stripped_dark_oak_bookshelf","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","cfm:stripped_dark_oak_desk","railcraft:routing_detector","minecraft:copper_ingot_from_blasting_raw_copper","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","gtceu:smelting/smelt_deepslate_iron_ore_to_ingot","additionallanterns:copper_lantern","twilightforest:canopy_chest_boat","mcwroofs:lime_terracotta_steep_roof","minecraft:dark_oak_button","mcwpaths:spruce_planks_path","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","mcwdoors:spruce_nether_door","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","domum_ornamentum:beige_stone_bricks","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","ae2:network/parts/panels_semi_dark_monitor","allthemodium:unobtainium_rod","supplementaries:deepslate_lamp","mcwbiomesoplenty:dead_pane_window","croptopia:vanilla_seeds","rftoolsutility:screen_controller","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:stripped_birch_desk","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","mcwfurnitures:dark_oak_double_wardrobe","mcwwindows:oak_plank_window","mcwbridges:dark_oak_log_bridge_middle","mcwwindows:oak_log_parapet","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","dyenamics:honey_stained_glass_pane_from_glass_pane","utilitarian:no_soliciting/soliciting_carpets/yellow_trapped_soliciting_carpet","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","farmersdelight:spruce_cabinet","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","ae2:network/cells/item_storage_components_cell_64k_part","minecraft:dark_oak_door","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwpaths:mossy_cobblestone_diamond_paving","cfm:dark_oak_cabinet","mcwfurnitures:stripped_oak_double_kitchen_cabinet","cfm:birch_blinds","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","allthecompressed:compress/spruce_planks_1x","mcwroofs:black_terracotta_steep_roof","additionallanterns:purpur_chain","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:flower_pot","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","alltheores:platinum_plate","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","farmersdelight:steak_and_potatoes","ae2:network/cables/smart_purple","mcwtrpdoors:spruce_ranch_trapdoor","cfm:dark_oak_kitchen_counter","mcwtrpdoors:dark_oak_tropical_trapdoor","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","mcwpaths:sandstone_crystal_floor_stairs","allthemodium:vibranium_plate","securitycraft:codebreaker","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","constructionwand:infinity_wand","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","farmersdelight:cooking/squid_ink_pasta","comforts:hammock_to_gray","minecraft:redstone_from_smelting_redstone_ore","cfm:dark_oak_desk","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","create:crafting/kinetics/controller_rail","utilitarian:no_soliciting/soliciting_carpets/brown_trapped_soliciting_carpet","mcwbiomesoplenty:maple_four_window","dyenamics:spring_green_stained_glass_pane_from_glass_pane","cfm:blue_kitchen_counter","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","mcwwindows:stripped_oak_log_window","mcwbridges:end_stone_bricks_bridge_stair","mcwroofs:base_top_roof","ae2:network/cables/smart_magenta","securitycraft:harming_module","minecraft:golden_boots","sfm:xp_goop","mcwroofs:birch_roof","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","rftoolspower:blazing_generator","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","megacells:network/cell_dock","supplementaries:statue","immersiveengineering:crafting/tinted_glass_lead_wire","reliquary:wraith_node","cfm:pink_grill","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwwindows:granite_window2","enderio:dark_steel_grinding_ball","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","mcwtrpdoors:birch_barrel_trapdoor","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","aether:chainmail_chestplate_repairing","ae2:network/crafting/cpu_crafting_accelerator","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","enderio:energy_conduit","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","mcwfurnitures:stripped_birch_bookshelf_cupboard","mcwdoors:birch_cottage_door","ad_astra:small_orange_industrial_lamp","rftoolsbase:machine_base","rftoolsutility:counterplus_module","minecraft:polished_blackstone_brick_stairs","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","mcwfurnitures:spruce_end_table","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","croptopia:whipping_cream","mcwpaths:brick_crystal_floor_slab","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","rftoolsbuilder:shape_card_quarry_fortune_dirt","aquaculture:tin_can_to_iron_nugget","mcwwindows:yellow_curtain","connectedglass:clear_glass_orange_pane2","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","pneumaticcraft:pressure_chamber_wall","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","mcwdoors:spruce_paper_door","handcrafted:dark_oak_nightstand","minecraft:end_stone_brick_stairs","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwroofs:dark_prismarine_upper_lower_roof","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","minecraft:cyan_carpet","mcwfurnitures:birch_lower_bookshelf_drawer","minecraft:polished_deepslate_stairs_from_polished_deepslate_stonecutting","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","domum_ornamentum:gray_floating_carpet","ae2:network/cables/smart_red","ae2additions:components/super/64k","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","mcwfurnitures:birch_bookshelf_drawer","dyenamics:bed/peach_bed_frm_white_bed","mcwfurnitures:stripped_birch_bookshelf_drawer","minecraft:light_blue_concrete_powder","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","twigs:smooth_stone_brick_wall_from_smooth_stone_brick_stonecutting","enderio:dark_steel_ladder","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","appmek:chemical_storage_cell_64k","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","mcwfurnitures:birch_coffee_table","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","twigs:mossy_cobblestone_bricks","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","railcraft:steel_gear","ironfurnaces:furnaces/obsidian_furnace","enderio:conduit_binder_from_smelting","aether:diamond_shovel_repairing","enderio:pulsating_alloy_nugget","bigreactors:smelting/graphite_from_charcoal","sophisticatedstorage:crafting_upgrade","minecraft:prismarine_bricks","ars_nouveau:spike_to_dye","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","mcwroofs:green_concrete_top_roof","minecraft:stone_bricks","connectedglass:tinted_borderless_glass_orange2","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","ae2:tools/paintballs_cyan","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","minecraft:dye_orange_wool","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","botania:quartz_lavender","rftoolsbuilder:shape_card_pump_clear","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","travelersbackpack:cyan_sleeping_bag","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","mcwfurnitures:stripped_dark_oak_wardrobe","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","minecraft:cyan_banner","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","enderio:resetting_lever_thirty","pylons:potion_filter","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","handcrafted:spruce_fancy_bed","minecraft:snow","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","mcwfurnitures:stripped_spruce_triple_drawer","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","mcwroofs:dark_oak_upper_lower_roof","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","handcrafted:spruce_chair","mcwroofs:light_gray_concrete_lower_roof","supplementaries:candle_holders/candle_holder_black_dye","twigs:rhyolite_slab","nethersdelight:soul_compost_from_hoglin_hide","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","pylons:interdiction_pylon","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:reinforced_bricks_from_tile","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","mcwpaths:stone_crystal_floor_path","twigs:stone_column","pneumaticcraft:compressed_bricks_from_stone_stonecutting","minecraft:lectern","sophisticatedstorage:dark_oak_limited_barrel_1","allthecompressed:compress/dark_oak_planks_1x","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","sophisticatedstorage:dark_oak_limited_barrel_4","sophisticatedstorage:dark_oak_limited_barrel_3","sophisticatedstorage:dark_oak_limited_barrel_2","mcwbiomesoplenty:hellbark_plank_window2","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass","mcwwindows:cyan_curtain","handcrafted:golden_medium_pot","ae2:network/cables/smart_yellow","minecraft:smooth_stone_slab","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","silentgear:crimson_iron_raw_ore_smelting","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","allthecompressed:compress/dark_oak_log_1x","allthecompressed:compress/tuff_1x","botania:azulejo_0","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","sfm:fancy_to_cable","ae2additions:components/super/1k","aquaculture:heavy_hook","handcrafted:birch_fancy_bed","aether:netherite_sword_repairing","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwwindows:acacia_window","mcwroofs:green_terracotta_roof","croptopia:food_press","mcwbridges:prismarine_bricks_bridge","railcraft:mob_detector","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","cfm:black_picket_fence","mcwfurnitures:oak_chair","mcwpaths:brick_honeycomb_paving","dyenamics:honey_terracotta","mcwwindows:stripped_crimson_stem_four_window","utilitix:spruce_shulker_boat_with_shell","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","silentgear:material_grader","xnet:router","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","mcwbridges:birch_bridge_pier","farmersdelight:nether_salad","aether:golden_gloves_repairing","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","reliquary:uncrafting/ink_sac","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","easy_villagers:trader","minecraft:barrel","mcwlights:orange_lamp","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","pneumaticcraft:reinforced_brick_pillar","utilitix:tiny_coal_to_tiny","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","create:cut_calcite_brick_stairs_from_stone_types_calcite_stonecutting","supplementaries:boat_jar","gtceu:shapeless/decompress_tin_from_ore_block","immersiveengineering:crafting/connector_hv","mcwdoors:birch_classic_door","mcwpaths:brick_crystal_floor","create:tuff_pillar_from_stone_types_tuff_stonecutting","ae2:materials/basiccard","cfm:dark_oak_bedside_cabinet","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","forbidden_arcanus:obsidian_skull","cfm:stripped_jungle_kitchen_sink_dark","minecraft:deepslate_brick_slab_from_polished_deepslate_stonecutting","mcwpaths:cobbled_deepslate_honeycomb_paving","allthearcanistgear:vibranium_boots_smithing","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","dyenamics:wine_concrete_powder","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:end_stone_brick_slab","mcwfurnitures:stripped_dark_oak_end_table","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","minecraft:deepslate_tile_slab_from_polished_deepslate_stonecutting","mcwpaths:gravel_path_block","mekanism:control_circuit/elite","enderio:silent_spruce_pressure_plate","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:cauldron","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","mcwbridges:nether_bricks_bridge","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","aether:iron_boots_repairing","ae2:block_cutter/walls/smooth_sky_stone_wall","mcwwindows:end_brick_gothic","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwdoors:spruce_modern_door","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:netherite_gloves_repairing","aether:iron_ring","tombstone:impregnated_diamond","dimstorage:dim_core","dyenamics:bed/navy_bed_frm_white_bed","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","cfm:cyan_kitchen_sink","dyenamics:peach_concrete_powder","ae2additions:components/super/4k","supplementaries:faucet","twigs:polished_calcite_bricks_from_calcite_stonecutting","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","sophisticatedstorage:storage_void_upgrade_from_backpack_void_upgrade","railcraft:copper_gear","littlelogistics:guide_rail_tug","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","handcrafted:birch_bench","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","dyenamics:bed/persimmon_bed_frm_white_bed","mcwlights:sea_lantern_slab","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwroofs:stone_bricks_top_roof","rftoolsbuilder:mover_controller","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","utilitarian:utility/spruce_logs_to_boats","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwroofs:oak_upper_lower_roof","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","mcwfurnitures:stripped_birch_cupboard_counter","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","twigs:bloodstone_slab_stonecutting","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","farmersdelight:cooking/mushroom_rice","minecraft:golden_shovel","allthecompressed:compress/podzol_1x","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","ae2:network/cells/item_storage_cell_4k","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","dyenamics:dye_persimmon_carpet","travelersbackpack:standard","mcwfurnitures:stripped_birch_counter","pneumaticcraft:reinforced_stone_slab","handcrafted:dark_oak_desk","utilitix:stone_wall","dyenamics:navy_dye","mcwfences:mangrove_stockade_fence","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","ae2:materials/cardspeed","thermal:smelting/apatite_from_smelting","mcwfurnitures:stripped_spruce_modern_chair","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","sophisticatedstorage:dark_oak_barrel","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","crafting_on_a_stick:uncraft_crafting_table","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","aether:aether_gold_nugget_from_smelting","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","mcwdoors:birch_barn_door","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","mcwtrpdoors:spruce_tropical_trapdoor","minecraft:golden_chestplate","domum_ornamentum:mossy_cobblestone_extra","mcwtrpdoors:print_cottage","securitycraft:reinforced_gray_stained_glass","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","cfm:dye_black_picket_fence","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","minecraft:birch_sign","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","additionallanterns:normal_lantern_gray","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","dyenamics:navy_terracotta","supplementaries:hourglass","ae2:tools/paintballs_light_gray","everythingcopper:copper_leggings","ae2:network/cells/item_storage_cell_1k","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_stair","mcwdoors:dark_oak_bark_glass_door","mekanism:metallurgic_infuser","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","sophisticatedstorage:backpack_stack_upgrade_tier_3_from_storage_stack_upgrade_tier_4","cfm:stripped_spruce_table","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","mcwroofs:yellow_striped_awning","minecraft:light_blue_terracotta","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","minecraft:spruce_stairs","supplementaries:checker","mcwdoors:birch_japanese_door","enderio:basic_capacitor","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","botania:conversions/light_blue_petal_block_deconstruct","domum_ornamentum:cactus_extra","cfm:cyan_cooler","allthecompressed:compress/amethyst_block_1x","mcwfurnitures:dark_oak_chair","ae2:network/parts/formation_plane","railcraft:silver_gear","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","ars_nouveau:wing_to_leather","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","enderio:conduit_binder_from_blasting","mcwroofs:light_gray_roof","create:small_calcite_brick_stairs_from_stone_types_calcite_stonecutting","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","connectedglass:scratched_glass_orange2","dyenamics:fluorescent_terracotta","minecraft:light_gray_dye_from_black_white_dye","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","mcwdoors:dark_oak_modern_door","travelersbackpack:redstone","biomesoplenty:brimstone_brick_wall_from_brimstone_stonecutting","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","dyenamics:lavender_stained_glass_pane_from_glass_pane","create:cut_calcite_from_stone_types_calcite_stonecutting","chimes:glass_bells","mcwroofs:birch_planks_top_roof","mcwwindows:iron_shutter","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","biomesoplenty:chiseled_brimstone_bricks_from_brimstone_stonecutting","mcwpaths:mossy_stone_crystal_floor","travelersbackpack:emerald","farmersdelight:cutting_board","twigs:bloodstone_wall_stonecutting","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","gtceu:smelting/smelt_raw_nether_quartz_ore_to_ingot","create:smelting/scoria","mcwpaths:mossy_cobblestone_square_paving","sophisticatedstorage:storage_stack_upgrade_tier_2_from_backpack_stack_upgrade_tier_1","mcwpaths:sandstone_flagstone","cfm:birch_crate","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwbridges:blackstone_bridge_pier","mcwroofs:birch_planks_lower_roof","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/pink_trapped_soliciting_carpet","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","mcwfurnitures:stripped_spruce_large_drawer","mcwpaths:blackstone_windmill_weave_path","minecraft:magenta_stained_glass_pane_from_glass_pane","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","alltheores:copper_rod","minecraft:shulker_box","mcwfurnitures:stripped_birch_modern_chair","rftoolsstorage:storage_scanner","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","ae2:network/cables/covered_blue","dyenamics:maroon_dye","utilitix:filter_rail","handcrafted:brown_cushion","minecraft:spruce_planks","aether:diamond_gloves","minecraft:mossy_cobblestone_stairs_from_mossy_cobblestone_stonecutting","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","enderio:resetting_lever_sixty_from_inv","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwdoors:dark_oak_barn_glass_door","minecraft:polished_blackstone_bricks","mcwfurnitures:spruce_triple_drawer","minecraft:cracked_stone_bricks","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwfurnitures:birch_double_wardrobe","mcwfurnitures:stripped_birch_desk","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","croptopia:toast_with_jam","mcwtrpdoors:birch_glass_trapdoor","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","minecraft:lantern","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","arseng:source_storage_cell_4k","cfm:spruce_park_bench","ae2:network/cells/fluid_storage_cell_4k","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","minecraft:deepslate_brick_wall","pneumaticcraft:thermal_lagging","mcwbridges:nether_bricks_bridge_pier","alltheores:gold_dust_from_hammer_ingot_crushing","minecolonies:supplycampdeployer","mcwpaths:cobbled_deepslate_running_bond_path","handcrafted:stone_corner_trim","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","mcwwindows:dark_oak_log_parapet","ae2:network/blocks/storage_drive","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","twigs:polished_amethyst","cfm:spruce_cabinet","mcwwindows:mangrove_plank_window2","sophisticatedstorage:spruce_barrel","mcwwindows:mangrove_pane_window","mcwroofs:spruce_planks_steep_roof","minecraft:deepslate_brick_stairs_from_deepslate_bricks_stonecutting","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","mcwfurnitures:spruce_large_drawer","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","handcrafted:spruce_shelf","mcwlights:acacia_tiki_torch","aether:golden_boots_repairing","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","securitycraft:reinforced_warped_fence_gate","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwroofs:prismarine_brick_upper_lower_roof","mcwfences:oak_horse_fence","caupona:clay_cistern","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","utilitarian:utility/dark_oak_logs_to_doors","mcwfences:blackstone_pillar_wall","mcwfurnitures:birch_wardrobe","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","gtceu:smelting/smelt_raw_quartzite_ore_to_ingot","minecraft:purpur_pillar","bloodmagic:synthetic_point","mcwtrpdoors:dark_oak_ranch_trapdoor","deepresonance:lens","additionallanterns:normal_lantern_black","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","minecraft:redstone_lamp","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","handcrafted:dark_oak_side_table","alltheores:lumium_ingot_from_dust","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","mcwfurnitures:stripped_birch_large_drawer","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","immersiveengineering:crafting/plate_silver_hammering","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","mcwfurnitures:stripped_dark_oak_modern_desk","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","minecraft:dye_blue_bed","allthemodium:vibranium_ingot","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","mcwlights:glowstone_slab","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","appbot:mana_storage_cell_64k","mcwdoors:birch_western_door","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","reliquary:uncrafting/string","connectedglass:tinted_borderless_glass_blue2","minecraft:polished_blackstone_pressure_plate","rftoolsbuilder:shield_block4","rftoolsbuilder:shield_block3","rftoolsbuilder:shape_card_quarry_clear_silk","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","minecraft:dark_oak_wood","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alltheores:diamond_rod","railcraft:animal_detector","rftoolsbuilder:shield_block2","minecraft:stone_slab_from_stone_stonecutting","rftoolsbuilder:shield_block1","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwroofs:light_blue_terracotta_upper_lower_roof","reliquary:infernal_claw","mcwdoors:print_birch","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","minecraft:stone_brick_slab_from_stone_stonecutting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","mcwroofs:spruce_upper_lower_roof","mcwtrpdoors:print_four_panel","pneumaticcraft:speed_upgrade","ae2:network/crafting/64k_cpu_crafting_storage","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","cfm:spruce_chair","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwwindows:mangrove_plank_pane_window","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","minecraft:netherite_leggings_smithing","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:birch_button","pneumaticcraft:compressed_stone_slab_from_stone_stonecutting","mcwtrpdoors:cherry_ranch_trapdoor","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","ae2:network/cables/dense_smart_brown","rftoolsbuilder:shape_card_quarry_clear","mcwbiomesoplenty:dead_hedge","handcrafted:wood_bowl","minecraft:cyan_bed","pneumaticcraft:charging_station","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","pneumaticcraft:compressed_stone_from_slab","cfm:cyan_trampoline","mcwroofs:white_roof","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","minecraft:netherite_machete_smithing","mcwbridges:balustrade_blackstone_bridge","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","alltheores:lumium_rod","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","mcwdoors:dark_oak_stable_head_door","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","mcwpaths:mossy_stone_strewn_rocky_path","supplementaries:sign_post_birch","handcrafted:dark_oak_table","minecraft:quartz_block","alltheores:iron_dust_from_hammer_crushing","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","mcwroofs:lime_concrete_top_roof","botania:water_ring","twigs:bone_meal_from_seashells","mcwroofs:purple_terracotta_attic_roof","arseng:source_storage_cell_1k","utilitarian:no_soliciting/soliciting_carpets/magenta_trapped_soliciting_carpet","sophisticatedstorage:stack_upgrade_tier_1_plus","mcwroofs:orange_terracotta_lower_roof","ae2:network/cells/fluid_storage_cell_1k","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","gtceu:smelting/smelt_copper_ore_to_ingot","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","mcwroofs:dark_oak_lower_roof","aether:iron_axe_repairing","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","mcwfurnitures:stripped_birch_stool_chair","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","pneumaticcraft:wall_lamp_brown","cfm:stripped_oak_mail_box","mcwwindows:nether_brick_gothic","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","minecraft:dune_armor_trim_smithing_template","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","cfm:dark_oak_upgraded_fence","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","mcwfurnitures:stripped_dark_oak_double_wardrobe","minecraft:brown_dye","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","expatternprovider:ex_drive","cfm:lime_cooler","rftoolsbuilder:shape_card_quarry_clear_fortune","mcwpaths:andesite_crystal_floor","minecraft:bricks","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","ae2:network/cells/fluid_storage_cell_64k_storage","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","silentgear:emerald_from_shards","supplementaries:candy","minecraft:polished_deepslate_stairs","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwtrpdoors:dark_oak_barred_trapdoor","mcwbiomesoplenty:hellbark_plank_four_window","ae2:network/cells/fluid_storage_cell_16k","mcwroofs:pink_concrete_lower_roof","railcraft:diamond_spike_maul","minecraft:dark_oak_stairs","additionallanterns:granite_lantern","mcwlights:iron_chandelier","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_bricks_stonecutting","mcwwindows:spruce_plank_window2","mcwpaths:mossy_cobblestone_honeycomb_paving","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","cfm:white_kitchen_drawer","botania:mushroom_10","ae2:network/crafting/1k_cpu_crafting_storage","utilitarian:tps_meter","mcwdoors:birch_four_panel_door","utilitarian:utility/spruce_logs_to_doors","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","ae2:network/cables/smart_pink","mcwbiomesoplenty:pine_horse_fence","mcwpaths:mossy_stone_windmill_weave","comforts:hammock_to_white","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:nether_bricks_upper_steep_roof","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","ae2:decorative/cut_quartz_block","minecraft:prismarine_wall","megacells:crafting/sky_steel_ingot_from_sky_steel_block","botania:petal_light_blue","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwfurnitures:spruce_modern_chair","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","ad_astra:brown_flag","alltheores:gold_rod","additionallanterns:smooth_stone_chain","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","mcwdoors:birch_stable_door","biomesoplenty:jacaranda_chest_boat","enderio:item_conduit","minecraft:red_nether_brick_wall","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","allthemodium:unobtainium_ingot_from_dust_smelting","rftoolspower:power_core1","aether:wooden_axe_repairing","ae2:network/blocks/io_port","mcwroofs:white_concrete_upper_steep_roof","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","allthemodium:smithing/vibranium_pickaxe_smithing","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","minecraft:dried_kelp_from_smelting","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","ae2:misc/deconstruction_certus_quartz_block","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","reliquary:mob_charm_fragments/enderman","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","handcrafted:spruce_table","dyenamics:bed/cherenkov_bed_frm_white_bed","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","supplementaries:soap/map","mcwpaths:brick_running_bond_path","supplementaries:sugar_cube","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","rftoolsbuilder:shape_card_quarry_dirt","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","mcwtrpdoors:birch_classic_trapdoor","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","cfm:spruce_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","mcwroofs:dark_prismarine_top_roof","minecraft:dye_blue_carpet","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","mcwpaths:brick_windmill_weave_stairs","mcwfurnitures:birch_double_drawer_counter","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","mcwdoors:spruce_tropical_door","create:polished_cut_calcite_stairs_from_stone_types_calcite_stonecutting","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","advanced_ae:smalladvpatpro","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","railcraft:iron_spike_maul","cfm:gray_cooler","minecraft:light_weighted_pressure_plate","ae2:tools/paintballs_green","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwbiomesoplenty:fir_four_window","everythingcopper:copper_axe","cfm:green_kitchen_drawer","securitycraft:reinforced_oak_fence","minecraft:black_stained_glass_pane_from_glass_pane","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","mcwroofs:dark_prismarine_lower_roof","mcwfurnitures:spruce_chair","create:cut_calcite_brick_slab_from_stone_types_calcite_stonecutting","biomesoplenty:brimstone_cluster_from_brimstone_stonecutting","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","domum_ornamentum:roan_stone_bricks","arseng:source_storage_cell_64k","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","railcraft:personal_world_spike","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","cfm:dark_oak_chair","domum_ornamentum:light_gray_floating_carpet","comforts:sleeping_bag_to_cyan","bigreactors:energizer/powerport_fe","megacells:crafting/greater_energy_card_upgraded","cfm:purple_picket_gate","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","dyenamics:bubblegum_concrete_powder","xnet:redstoneproxy_update","mcwroofs:spruce_planks_attic_roof","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwroofs:nether_bricks_attic_roof","expatternprovider:water_cell","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","merequester:requester","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","minecraft:cracked_nether_bricks","utilitix:gilding_crystal","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","handcrafted:birch_couch","charginggadgets:charging_station","minecraft:purpur_pillar_from_purpur_block_stonecutting","supplementaries:slingshot","allthemodium:allthemodium_plate","reliquary:kraken_shell_fragment","twigs:cobblestone_from_pebble","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","travelersbackpack:dye_black_sleeping_bag","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","mcwfurnitures:birch_cupboard_counter","everythingcopper:copper_rail","ae2:network/cells/item_storage_components_cell_4k_part","cfm:black_grill","handcrafted:deepslate_pillar_trim","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","create:small_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","cfm:birch_desk_cabinet","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","dyenamics:dye_ultramarine_carpet","mcwdoors:dark_oak_japanese_door","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","cfm:crimson_kitchen_drawer","mcwroofs:nether_bricks_steep_roof","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","cfm:dark_oak_upgraded_gate","aquaculture:neptunium_ingot_from_nuggets","mcwroofs:bricks_upper_steep_roof","minecraft:iron_pickaxe","aether:shield_repairing","ae2:shaped/not_so_mysterious_cube","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","reliquary:fortune_coin","mcwfences:railing_sandstone_wall","minecraft:spruce_button","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","rftoolsutility:matter_booster","ae2:network/cables/glass_orange","mcwfurnitures:stripped_spruce_table","deepresonance:resonating_plate","minecraft:vibranium_mage_chestplate_smithing","ae2:materials/cardinverter","mcwroofs:deepslate_top_roof","mcwdoors:birch_swamp_door","immersiveengineering:crafting/steel_wallmount","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","pipez:wrench","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwpaths:mossy_stone_windmill_weave_stairs","rftoolsutility:fluid_module","minecraft:deepslate_tiles","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","aether:leather_boots_repairing","handcrafted:gray_sheet","mcwtrpdoors:dark_oak_bark_trapdoor","cfm:post_box","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwwindows:brown_curtain","minecraft:dark_oak_slab","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","mcwfurnitures:birch_drawer","cfm:orange_cooler","minecraft:lime_stained_glass","mcwbridges:balustrade_mossy_stone_bricks_bridge","dyenamics:dye_wine_carpet","ae2:tools/paintballs_black","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwfurnitures:dark_oak_desk","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","create:layered_calcite_from_stone_types_calcite_stonecutting","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","utilitarian:utility/birch_logs_to_slabs","minecraft:stone_brick_walls_from_stone_stonecutting","mcwroofs:gray_terracotta_top_roof","securitycraft:keypad_item","ae2:tools/nether_quartz_wrench","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","mcwpaths:mossy_stone_flagstone","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","cfm:stripped_dark_oak_crate","additionallanterns:normal_lantern_lime","ae2:network/cables/covered_fluix_clean","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","mcwroofs:magenta_concrete_top_roof","handcrafted:cyan_cushion","pneumaticcraft:spawner_extractor","aether:packed_ice_freezing","mcwfurnitures:stripped_dark_oak_counter","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","silentgear:blaze_gold_ingot_from_nugget","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","pneumaticcraft:air_compressor","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","handcrafted:birch_desk","cfm:fridge_dark","chimes:copper_chimes","minecraft:birch_fence","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwfurnitures:stripped_dark_oak_table","farmersdelight:cooking_pot","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","mcwtrpdoors:mangrove_bark_trapdoor","pneumaticcraft:security_upgrade","minecraft:red_nether_brick_slab","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","ae2:tools/paintballs_yellow","mcwbiomesoplenty:empyreal_hedge","mcwdoors:dark_oak_mystic_door","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","dyenamics:dye_icy_blue_carpet","supplementaries:flint_block","aether:diamond_leggings_repairing","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","twigs:weeping_polished_blackstone_bricks","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:spruce_fence_gate","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","expatternprovider:wireless_connector","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","minecraft:white_stained_glass_pane_from_glass_pane","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","biomesoplenty:mahogany_chest_boat","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","dimstorage:solid_dim_core","mcwpaths:cobbled_deepslate_running_bond_stairs","minecraft:birch_wood","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","littlelogistics:switch_rail","dyenamics:dye_honey_carpet","mcwlights:soul_mangrove_tiki_torch","mcwpaths:mossy_cobblestone_clover_paving","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","mcwtrpdoors:birch_ranch_trapdoor","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","sophisticatedstorage:birch_chest","xnet:connector_red_dye","ae2:network/cables/covered_fluix","utilitarian:no_soliciting/soliciting_carpets/light_gray_trapped_soliciting_carpet","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","delightful:knives/tin_knife","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwfurnitures:dark_oak_wardrobe","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwfurnitures:birch_modern_wardrobe","allthemodium:ancient_cracked_stone_bricks_from_crushing","mcwlights:copper_candle_holder","wstweaks:wither_skeleton_skull","minecraft:mossy_stone_brick_slab","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:bamboo_chest_raft","mcwtrpdoors:birch_four_panel_trapdoor","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","reliquary:mob_charm_fragments/slime","allthecompressed:compress/gravel_3x","additionallanterns:normal_lantern_yellow","allthecompressed:compress/gravel_2x","dyenamics:bed/bubblegum_bed_frm_white_bed","cfm:stripped_spruce_desk","aquaculture:birch_fish_mount","ae2:network/cells/fluid_storage_cell_1k_storage","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","cfm:dark_oak_park_bench","buildinggadgets2:template_manager","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","rftoolscontrol:programmer","handcrafted:blue_plate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","utilitarian:utility/dark_oak_logs_to_slabs","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","cfm:birch_table","blue_skies:comet_bookshelf","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","reliquary:uncrafting/glowstone_dust","cfm:white_sofa","tombstone:ankh_of_prayer","utilitarian:utility/birch_logs_to_boats","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","ae2:shaped/slabs/fluix_block","mcwpaths:mossy_stone_flagstone_slab","rftoolsbuilder:mover_control2","mcwfences:acacia_highley_gate","rftoolsbuilder:mover_control3","rftoolsbuilder:mover_control4","utilitix:dark_oak_shulker_boat_with_shell","cfm:black_kitchen_drawer","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","allthemods:easy_sticks","mcwwindows:bricks_pane_window","mcwtrpdoors:birch_cottage_trapdoor","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","connectedglass:tinted_borderless_glass_black2","travelersbackpack:squid","mcwroofs:spruce_roof","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","ae2:network/blocks/cell_workbench","minecraft:polished_andesite_slab","mcwlights:magenta_lamp","create:crafting/logistics/powered_latch","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","minecraft:cracked_deepslate_bricks","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","sophisticatedstorage:stonecutter_upgrade","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","mcwroofs:prismarine_brick_steep_roof","handcrafted:oak_bench","farmersdelight:cooking/pasta_with_meatballs","silentgear:blaze_gold_dust_smelting","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwroofs:spruce_planks_upper_steep_roof","mcwwindows:birch_louvered_shutter","minecraft:repeater","minecraft:red_concrete_powder","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","rftoolsbuilder:shape_card_quarry_silk_dirt","dyenamics:spring_green_concrete_powder","mcwpaths:blackstone_crystal_floor_path","additionallanterns:normal_lantern_white","minecraft:iron_leggings","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","mcwroofs:base_roof_block","handcrafted:birch_pillar_trim","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","alltheores:smelting_dust/platinum_ingot","ae2:tools/paintballs_white","mcwbiomesoplenty:palm_window","twigs:blackstone_column","additionallanterns:deepslate_bricks_chain","mcwbridges:balustrade_mossy_cobblestone_bridge","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwroofs:dark_prismarine_upper_steep_roof","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","handcrafted:spruce_drawer","pneumaticcraft:compressed_brick_tile_from_bricks_stonecutting","minecraft:red_nether_brick_wall_from_red_nether_bricks_stonecutting","domum_ornamentum:light_blue_floating_carpet","mcwtrpdoors:dark_oak_glass_trapdoor","ae2:shaped/stairs/fluix_block","pneumaticcraft:compressed_iron_chestplate","ae2:decorative/fluix_block","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","ae2:block_cutter/slabs/quartz_slab","utilitix:acacia_shulker_boat_with_shell","minecraft:brown_carpet","biomesoplenty:brimstone_bricks","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","additionallanterns:normal_lantern_blue","sophisticatedstorage:backpack_stack_upgrade_tier_2_from_storage_stack_upgrade_tier_3","minecraft:prismarine_brick_slab","mcwpaths:mossy_cobblestone_dumble_paving","allthecompressed:compress/birch_planks_1x","enderio:resetting_lever_three_hundred_inv_from_prev","mcwfurnitures:dark_oak_bookshelf_cupboard","minecraft:iron_chestplate","mcwtrpdoors:spruce_blossom_trapdoor","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","supplementaries:sign_post_dark_oak","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","alltheores:platinum_rod","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","railcraft:world_spike","mcwdoors:garage_gray_door","mcwfurnitures:dark_oak_table","mysticalagriculture:prosperity_shard_smelted","mcwfurnitures:stripped_oak_counter","botania:petal_green","handcrafted:witch_trophy","mcwroofs:dark_oak_planks_attic_roof","ae2:network/cells/item_storage_components_cell_256k_part","dyenamics:bed/fluorescent_bed_frm_white_bed","minecraft:polished_andesite_from_andesite_stonecutting","allthearcanistgear:vibranium_robes_smithing","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","minecraft:decorated_pot_simple","mcwbiomesoplenty:stripped_dead_pane_window","ae2:shaped/walls/fluix_block","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","botania:mushroom_9","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","minecraft:oak_pressure_plate","botania:mushroom_5","botania:mushroom_4","minecraft:stone_brick_stairs","ae2:misc/chests_smooth_sky_stone","bigreactors:turbine/basic/activefluidport_forge","botania:mushroom_3","aether:netherite_leggings_repairing","bigreactors:reprocessor/outputport","mcwwindows:quartz_window","immersiveengineering:crafting/steel_scaffolding_standard","cfm:brown_grill","mcwroofs:gray_roof","minecraft:diorite","mcwfurnitures:dark_oak_drawer","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","alltheores:uranium_dust_from_hammer_crushing","blue_skies:coarse_lunar_dirt","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","create:cut_calcite_bricks_from_stone_types_calcite_stonecutting","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","securitycraft:reinforced_chiseled_bookshelf","utilitix:mangrove_shulker_boat_with_shell","cfm:gray_kitchen_drawer","minecraft:netherite_axe_smithing","minecraft:clock","mcwroofs:red_concrete_top_roof","create:crafting/appliances/netherite_diving_helmet_from_netherite","mcwroofs:birch_planks_attic_roof","domum_ornamentum:cyan_brick_extra","utilitarian:no_soliciting/soliciting_carpets/black_trapped_soliciting_carpet","securitycraft:portable_tune_player","mcwroofs:birch_planks_upper_steep_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","dyenamics:dye_peach_carpet","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","megacells:crafting/compression_card","mcwfences:oak_wired_fence","biomesoplenty:brimstone_brick_slab_from_brimstone_stonecutting","botania:twig_wand","twilightforest:transformation_chest_boat","additionallanterns:normal_lantern_light_gray","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","aquaculture:iron_fillet_knife","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","minecraft:polished_deepslate_slab_from_polished_deepslate_stonecutting","additionallanterns:red_nether_bricks_chain","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwtrpdoors:spruce_whispering_trapdoor","dyenamics:peach_stained_glass","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","minecraft:prismarine","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","cfm:dark_oak_blinds","mcwroofs:purple_terracotta_roof","alltheores:tin_rod","connectedglass:scratched_glass_orange_pane2","mcwbridges:rope_birch_bridge","mcwbridges:nether_bricks_bridge_stair","twigs:dark_oak_table","mcwwindows:white_mosaic_glass","cfm:stripped_dark_oak_desk_cabinet","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","cfm:acacia_kitchen_sink_dark","handcrafted:dark_oak_cupboard","minecraft:mossy_cobblestone_slab","pneumaticcraft:remote","rftoolsbase:machine_frame","minecraft:polished_andesite_slab_from_andesite_stonecutting","minecraft:dye_black_bed","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","cfm:stripped_spruce_crate","mcwfurnitures:stripped_dark_oak_drawer","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","mcwwindows:spruce_window2","mcwfurnitures:stripped_birch_table","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","expatternprovider:assembler_matrix_speed","utilitix:mob_yoinker","mcwwindows:andesite_four_window","domum_ornamentum:light_blue_brick_extra","minecraft:dark_oak_sign","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/cyan_trapped_soliciting_carpet","utilitarian:no_soliciting/soliciting_carpets/red_trapped_soliciting_carpet","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","mcwtrpdoors:dark_oak_whispering_trapdoor","minecraft:lime_concrete_powder","minecraft:leather_leggings","minecraft:sandstone_wall_from_sandstone_stonecutting","twigs:polished_bloodstone_bricks_from_bloodstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","securitycraft:block_change_detector","megacells:crafting/sky_steel_block","twigs:rocky_dirt","supplementaries:pancake_fd","advanced_ae:advpatpro2","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","minecraft:netherite_scrap_from_blasting","enderio:resetting_lever_three_hundred_from_prev","allthemodium:ancient_stone_brick_stairs","utilitarian:no_soliciting/soliciting_carpets/purple_trapped_soliciting_carpet","mcwroofs:pink_terracotta_lower_roof","additionallanterns:amethyst_chain","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","biomesoplenty:brimstone_brick_stairs_from_brimstone_stonecutting","mcwbridges:bridge_torch","domum_ornamentum:magenta_brick_extra","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","securitycraft:reinforced_crimson_fence","minecraft:wooden_shovel","mcwtrpdoors:birch_beach_trapdoor","mcwdoors:dark_oak_bamboo_door","createoreexcavation:vein_atlas","aether:stone_axe_repairing","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","pneumaticcraft:wall_lamp_cyan","create:crafting/kinetics/fluid_pipe","mekanism:processing/gold/ingot/from_dust_smelting","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","enderio:pulsating_alloy_grinding_ball","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","minecraft:red_nether_brick_stairs_from_red_nether_bricks_stonecutting","mcwpaths:red_sand_path_block","pneumaticcraft:pressure_chamber_glass_x1","minecraft:waxed_copper_block_from_honeycomb","cfm:light_blue_cooler","mcwroofs:lime_terracotta_roof","create:copper_bars_from_ingots_copper_stonecutting","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","mcwroofs:red_nether_bricks_lower_roof","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","mcwtrpdoors:dark_oak_mystic_trapdoor","minecraft:gold_ingot_from_smelting_raw_gold","alltheores:invar_ingot_from_dust_blasting","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","minecraft:cracked_polished_blackstone_bricks","pneumaticcraft:wall_lamp_inverted_purple","railcraft:signal_block_surveyor","rftoolsbuilder:mover","mcwpaths:stone_windmill_weave","quark:tweaks/crafting/utility/misc/repeater","mcwdoors:birch_whispering_door","comforts:hammock_gray","mcwfurnitures:oak_lower_triple_drawer","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","railcraft:logbook","ae2:misc/tiny_tnt","utilitix:bamboo_shulker_raft_with_shell","mcwroofs:green_concrete_steep_roof","ae2:decorative/quartz_glass","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","naturalist:glow_goop_from_campfire_cooking","cfm:light_gray_picket_gate","handcrafted:spruce_couch","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwpaths:blackstone_dumble_paving","mcwwindows:jungle_blinds","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","securitycraft:block_pocket_manager","enderio:pulsating_alloy_nugget_to_ingot","mcwlights:soul_cherry_tiki_torch","minecraft:end_stone_brick_wall","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","alltheores:copper_ore_hammer","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","mcwlights:red_lamp","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","alltheores:lumium_dust_from_alloy_blending","botania:flower_bag","cfm:purple_kitchen_drawer","ironfurnaces:furnaces/emerald_furnace","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","enderio:primitive_alloy_smelter","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","aether:stone_sword_repairing","minecraft:netherite_chestplate_smithing","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:prismarine_stairs_from_prismarine_stonecutting","minecraft:oak_sign","quark:tweaks/crafting/utility/bent/paper","mcwroofs:birch_lower_roof","railcraft:bag_of_cement","bigreactors:fluidizer/powerport","mcwlights:light_blue_lamp","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","domum_ornamentum:white_paper_extra","connectedglass:borderless_glass_blue2","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","dyenamics:bed/wine_bed_frm_white_bed","mcwbiomesoplenty:maple_horse_fence","mcwbridges:deepslate_brick_bridge_pier","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","twigs:bloodstone_stairs_stonecutting","mcwbiomesoplenty:fir_plank_window2","minecraft:birch_pressure_plate","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","dyenamics:rose_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","create:spruce_window","utilitarian:utility/dark_oak_logs_to_trapdoors","immersiveengineering:crafting/alloybrick","biomesoplenty:brimstone_bud_from_brimstone_stonecutting","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:stripped_spruce_counter","mcwfurnitures:stripped_dark_oak_modern_wardrobe","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwroofs:spruce_lower_roof","mcwwindows:cyan_mosaic_glass_pane","cfm:stripped_dark_oak_cabinet","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","littlelogistics:tee_junction_rail","allthemodium:smithing/allthemodium_sword_smithing","mcwdoors:spruce_whispering_door","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","rftoolsutility:fluidplus_module","ae2:tools/network_tool","minecraft:bowl","enderio:redstone_conduit","deeperdarker:bloom_boat","mcwtrpdoors:birch_blossom_trapdoor","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","mcwtrpdoors:birch_barn_trapdoor","dyenamics:rose_stained_glass","sophisticatedstorage:stack_upgrade_tier_2_from_tier_1_plus","mcwroofs:pink_terracotta_upper_steep_roof","mcwfurnitures:birch_chair","mcwpaths:blackstone_diamond_paving","farmersdelight:cooking/hot_cocoa","pneumaticcraft:vortex_cannon","sophisticatedstorage:chipped/carpenters_table_upgrade","additionallanterns:normal_lantern_green","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","securitycraft:reinforced_jungle_fence","minecraft:birch_planks","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","allthecompressed:compress/copper_block_1x","minecraft:spectral_arrow","alltheores:copper_dust_from_hammer_ingot_crushing","mcwroofs:light_blue_concrete_upper_steep_roof","allthemodium:ancient_stone_stairs","mcwwindows:stripped_cherry_pane_window","mcwtrpdoors:print_paper","mcwroofs:dark_prismarine_attic_roof","cfm:stripped_birch_desk_cabinet","cfm:light_blue_kitchen_sink","dyenamics:bubblegum_stained_glass_pane_from_glass_pane","immersiveengineering:crafting/light_engineering","botania:dye_light_blue","mcwpaths:andesite_flagstone_stairs","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","reliquary:mob_charm_fragments/zombified_piglin","create:crafting/materials/red_sand_paper","mcwwindows:stripped_crimson_stem_window2","mcwpaths:mossy_stone_crystal_floor_path","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","alltheores:lead_dust_from_hammer_crushing","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","allthearcanistgear:vibranium_hat_smithing","ae2:network/cables/dense_smart_fluix_clean","minecraft:dye_orange_bed","mcwdoors:birch_barn_glass_door","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","allthecompressed:compress/netherite_block_1x","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","handcrafted:birch_drawer","mcwbiomesoplenty:magic_horse_fence","sophisticatedstorage:basic_to_copper_tier_upgrade","minecraft:brown_bed","mcwtrpdoors:birch_whispering_trapdoor","littlelogistics:vessel_charger","ae2:network/crafting/molecular_assembler","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","bigreactors:reprocessor/powerport","cfm:stripped_dark_oak_table","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","ae2:tools/misctools_entropy_manipulator","aether:netherite_pickaxe_repairing","pneumaticcraft:manual_compressor","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","minecraft:light_blue_glazed_terracotta","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","ae2:decorative/sky_stone_brick_from_stonecutting","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","mcwtrpdoors:spruce_classic_trapdoor","cfm:light_gray_kitchen_sink","allthemodium:ancient_stone_wall","mcwfences:mangrove_picket_fence","rftoolsbuilder:shape_card_void","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:illuminant_light_blue_block_on_dyed","simplylight:lamp_post","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","twilightforest:wood/crimson_banister","create:cut_calcite_stairs_from_stone_types_calcite_stonecutting","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","ae2:network/crafting/patterns_blank","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","mcwtrpdoors:dark_oak_swamp_trapdoor","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","voidtotem:totem_of_void_undying","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","pneumaticcraft:manometer","securitycraft:alarm","mcwfurnitures:oak_counter","rftoolsutility:charged_porter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","comforts:hammock_to_brown","pneumaticcraft:compressed_brick_tile","ae2:network/blocks/energy_vibration_chamber","mcwfurnitures:birch_stool_chair","ae2:tools/fluix_shovel","mcwdoors:birch_japanese2_door","reliquary:magicbane","mcwfurnitures:stripped_dark_oak_triple_drawer","pneumaticcraft:wall_lamp_inverted_magenta","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","create:crafting/appliances/netherite_backtank_from_netherite","mcwpaths:sandstone_honeycomb_paving","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","dyenamics:peach_dye","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","mcwwindows:stone_window","utilitix:comparator_redirector_down","mcwroofs:gray_steep_roof","utilitarian:utility/birch_logs_to_pressure_plates","rftoolspower:endergenic","create:copper_shingles_from_ingots_copper_stonecutting","reliquary:mob_charm_belt","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","mcwroofs:birch_planks_roof","cfm:stripped_birch_crate","sophisticatedstorage:jukebox_upgrade","allthemodium:raw_unobtainium_block","additionallanterns:normal_lantern_cyan","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","twigs:mossy_cobblestone_bricks_cobblestone","ae2:network/cables/glass_black","mcwroofs:yellow_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","minecraft:unobtainium_mage_leggings_smithing","mcwroofs:birch_attic_roof","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","rftoolsbuilder:shape_card_pump_dirt","cfm:black_trampoline","cfm:stripped_oak_crate","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","mcwdoors:oak_bark_glass_door","allthemodium:smithing/vibranium_sword_smithing","mcwbiomesoplenty:stripped_willow_log_four_window","minecraft:deepslate_tiles_from_polished_deepslate_stonecutting","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","create:layered_tuff_from_stone_types_tuff_stonecutting","minecraft:polished_blackstone_stairs","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","minecraft:deepslate_brick_stairs","travelersbackpack:sandstone","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","potionsmaster:calcinatedunobtainium_powder","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","mcwdoors:dark_oak_tropical_door","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","allthecompressed:compress/cobblestone_2x","ae2:network/crafting/4k_cpu_crafting_storage","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","mcwbridges:balustrade_deepslate_bricks_bridge","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","mcwfurnitures:birch_glass_table","mcwwindows:quartz_pane_window","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","farmersdelight:cooking/mushroom_stew","mcwroofs:base_roof_slab","mcwfurnitures:spruce_table","bigreactors:energizer/energycore","cfm:spruce_desk_cabinet","minecraft:shield","allthecompressed:compress/cobblestone_3x","mcwroofs:gray_concrete_steep_roof","pneumaticcraft:air_grate_module","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:cyan_stained_glass_pane_from_glass_pane","create:cut_calcite_wall_from_stone_types_calcite_stonecutting","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","supplementaries:stonecutting/blackstone_tile","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwbridges:balustrade_end_stone_bricks_bridge","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","dyenamics:dye_rose_carpet","mcwbridges:mossy_cobblestone_bridge_pier","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","mcwbridges:balustrade_bricks_bridge","mcwbridges:prismarine_bricks_bridge_stair","supplementaries:candle_holders/candle_holder_blue_dye","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","mcwfurnitures:stripped_dark_oak_drawer_counter","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","minecraft:birch_door","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwpaths:cobbled_deepslate_windmill_weave","mcwtrpdoors:dark_oak_paper_trapdoor","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","computercraft:pocket_computer_normal","alltheores:peridot_dust_from_hammer_crushing","mcwfences:double_curved_metal_fence","minecraft:smooth_quartz_stairs_from_smooth_quartz_stonecutting","securitycraft:claymore","travelersbackpack:hose_nozzle","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","twigs:polished_tuff_stonecutting","cfm:dark_oak_table","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","ae2:network/cables/dense_smart_fluix","mcwroofs:white_terracotta_top_roof","mcwroofs:sandstone_steep_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","cfm:dye_orange_picket_gate","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","biomesoplenty:redwood_chest_boat","undergarden:smogstem_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","mekanismtools:diamond_paxel","minecraft:birch_fence_gate","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","mcwroofs:orange_concrete_upper_lower_roof","mcwfurnitures:spruce_modern_wardrobe","mcwtrpdoors:spruce_barn_trapdoor","appbot:fluix_mana_pool","ae2:network/blocks/pattern_providers_interface_part","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","minecraft:netherite_drill_smithing","mcwfurnitures:dark_oak_large_drawer","mcwroofs:dark_oak_planks_lower_roof","expatternprovider:assembler_matrix_pattern","mcwfences:modern_andesite_wall","minecraft:smooth_quartz_slab","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","ae2:network/cells/view_cell","botania:brewery","mcwwindows:metal_pane_window","ironfurnaces:furnaces/allthemodium_furnace","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","rftoolsutility:matter_receiver","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","mcwroofs:deepslate_lower_roof","minecraft:prismarine_wall_from_prismarine_stonecutting","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:purple_stained_glass_pane_from_glass_pane","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","connectedglass:borderless_glass_orange_pane2","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","twigs:smooth_stone_brick_slab","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","minecraft:yellow_stained_glass_pane_from_glass_pane","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwfurnitures:spruce_drawer_counter","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","cfm:stripped_birch_coffee_table","simplylight:illuminant_green_block_dyed","silentgear:blaze_gold_dust","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","cfm:stripped_spruce_park_bench","eccentrictome:tome","mcwroofs:dark_oak_top_roof","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","ae2:network/cables/covered_magenta","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","dyenamics:icy_blue_concrete_powder","rftoolsbase:crafting_card","mcwroofs:dark_oak_planks_upper_steep_roof","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","deeperdarker:soul_elytra","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","rftoolsutility:matter_transmitter","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","ae2:network/cables/covered_gray","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","alltheores:platinum_ore_hammer","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","minecraft:gray_carpet","mysticalagriculture:inferium_essence_smelted","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwfurnitures:birch_lower_triple_drawer","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","mcwfurnitures:spruce_double_drawer","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","cfm:birch_coffee_table","travelersbackpack:magma_cube","mcwfurnitures:dark_oak_double_drawer_counter","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwfurnitures:stripped_birch_chair","mcwwindows:mangrove_plank_four_window","mcwfurnitures:stripped_spruce_drawer_counter","chemlib:copper_ingot_from_smelting_copper_dust","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwbridges:prismarine_bricks_bridge_pier","mcwfences:red_sandstone_grass_topped_wall","minecraft:red_nether_brick_stairs","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","farmersdelight:cooking/glow_berry_custard","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","mcwpaths:brick_flagstone_stairs","pneumaticcraft:spawner_agitator","dyenamics:bed/persimmon_bed","rftoolsbuilder:vehicle_status_module","ae2:network/cables/smart_light_gray","minecraft:polished_blackstone_brick_wall","mcwbiomesoplenty:stripped_palm_log_four_window","handcrafted:spruce_bench","twigs:spruce_table","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","ae2:network/wireless_crafting_terminal","create:crafting/kinetics/orange_seat","mcwroofs:birch_upper_steep_roof","rftoolsutility:syringe","minecraft:diamond_block","mekanism:configurator","mcwroofs:deepslate_roof","minecraft:deepslate_tile_stairs_from_deepslate_bricks_stonecutting","biomesoplenty:willow_chest_boat","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","rftoolsutility:spawner","bigreactors:fluidizer/outputport","cfm:green_cooler","alltheores:steel_rod","additionallanterns:normal_lantern_magenta","twigs:bloodstone_wall","paraglider:cosmetic/deku_leaf","bigreactors:blasting/graphite_from_coal","sophisticatedbackpacks:stack_upgrade_tier_2","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","twilightforest:wood/warped_banister","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","farmersdelight:horse_feed","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","bigreactors:energizer/energizercell","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwfurnitures:birch_counter","mcwfurnitures:spruce_counter","utilitarian:snad/snad","ae2:tools/certus_quartz_cutting_knife","rftoolsstorage:modular_storage","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:spruce_slab","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","mcwpaths:birch_planks_path","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","rftoolsbuilder:shape_card_pump","merequester:requester_terminal","ae2:shaped/stairs/smooth_sky_stone_block","travelersbackpack:snow","cfm:black_picket_gate","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","minecraft:red_dye_from_rose_bush","allthecompressed:compress/platinum_block_1x","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","ae2:network/cables/smart_green","ae2:network/cells/item_storage_cell_64k","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","ae2:block_cutter/walls/fluix_wall","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","supplementaries:soap/piston","minecraft:deepslate_brick_slab_from_deepslate_bricks_stonecutting","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:soul_campfire","botania:red_string_interceptor","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwfurnitures:stripped_dark_oak_glass_table","domum_ornamentum:green_cobblestone_extra","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwlights:spruce_tiki_torch","dyenamics:bed/amber_bed_frm_white_bed","simplylight:illuminant_block_dyed","ae2:tools/paintballs_magenta","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","twigs:cracked_bricks","supplementaries:slice_map","appflux:insulating_resin","littlelogistics:automatic_tee_junction_rail","mcwbridges:brick_bridge_pier","ae2:misc/deconstruction_fluix_block","alltheores:aluminum_dust_from_hammer_ingot_crushing","ironfurnaces:furnaces/netherite_furnace","mcwdoors:dark_oak_nether_door","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","cfm:stripped_spruce_desk_cabinet","mcwfences:jungle_wired_fence","mekanism:control_circuit/advanced","mekanism:upgrade/speed","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","blue_skies:trough","mcwfences:end_brick_pillar_wall","dyenamics:ultramarine_dye","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","enderio:reinforced_obsidian_block","mcwroofs:light_gray_upper_lower_roof","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","mcwroofs:spruce_steep_roof","minecraft:purple_dye","minecraft:deepslate_tile_wall_from_polished_deepslate_stonecutting","ae2:network/cells/item_storage_cell_4k_storage","create:cut_calcite_brick_wall_from_stone_types_calcite_stonecutting","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","minecraft:quartz_slab","handcrafted:phantom_trophy","railcraft:overalls","modularrouters:modular_router","mcwfurnitures:dark_oak_modern_chair","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","ae2:block_cutter/stairs/smooth_sky_stone_stairs","dyenamics:amber_wool","railcraft:steel_helmet","farmersdelight:iron_knife","allthecompressed:decompress/cobblestone_1x","mcwroofs:spruce_attic_roof","mcwroofs:nether_bricks_roof","ae2:network/cables/smart_black","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:mossy_cobblestone_wall_from_mossy_cobblestone_stonecutting","minecraft:observer","cfm:gray_kitchen_sink","minecraft:polished_deepslate_wall","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","allthecompressed:decompress/cobblestone_2x","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","mcwbridges:spruce_log_bridge_middle","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","ae2:block_cutter/stairs/fluix_stairs","farmersdelight:chicken_sandwich","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","enderio:iron_gear","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","railcraft:switch_track_lever","mcwpaths:andesite_honeycomb_paving","mcwwindows:birch_log_parapet","mcwbridges:sandstone_bridge","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","handcrafted:blue_bowl","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","minecraft:polished_blackstone_brick_slab","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","ae2:network/cables/smart_orange","minecraft:unobtainium_mage_helmet_smithing","dyenamics:dye_amber_carpet","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","twigs:polished_tuff_bricks_from_tuff_stonecutting","handcrafted:kitchen_hood","appbot:mana_storage_cell_4k","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","biomesoplenty:brimstone_cluster","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwbridges:birch_log_bridge_middle","xnet:connector_blue_dye","rftoolsbuilder:shape_card_liquid","create:polished_cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwroofs:black_lower_roof","mcwfurnitures:stripped_oak_desk","mcwroofs:gutter_base_blue","twigs:cut_amethyst_from_amethyst_block_stonecutting","reliquary:ender_staff","mcwwindows:diorite_louvered_shutter","minecraft:gray_dye","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","twigs:polished_amethyst_stonecutting","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","mcwfurnitures:stripped_spruce_bookshelf_drawer","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","cfm:stripped_dark_oak_park_bench","allthemodium:unobtainium_ingot_from_raw_blasting","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","ae2:network/parts/energy_acceptor","additionallanterns:gold_chain","dyenamics:cherenkov_stained_glass_pane_from_glass_pane","ae2:materials/advancedcard","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","silentgear:blaze_gold_nugget","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","utilitarian:no_soliciting/soliciting_carpets/white_trapped_soliciting_carpet","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","mcwfurnitures:stripped_dark_oak_striped_chair","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","mcwroofs:andesite_lower_roof","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","create:calcite_from_stone_types_calcite_stonecutting","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","mcwfurnitures:stripped_birch_modern_wardrobe","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","handcrafted:spruce_desk","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:spruce_wood","minecraft:iron_sword","mcwtrpdoors:birch_swamp_trapdoor","minecraft:spruce_fence","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwdoors:metal_reinforced_door","utilitarian:utility/birch_logs_to_trapdoors","domum_ornamentum:blue_cobblestone_extra","additionallanterns:blackstone_chain","additionallanterns:normal_lantern_red","domum_ornamentum:purple_brick_extra","mcwwindows:one_way_glass_pane","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","ae2:tools/fluix_upgrade_smithing_template","minecraft:chiseled_sandstone_from_sandstone_stonecutting","botania:petal_light_blue_double","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","gtceu:shaped/block_compress_platinum","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","appbot:mana_storage_cell_1k","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","minecraft:birch_trapdoor","mcwpaths:sandstone_flagstone_stairs","bigreactors:turbine/basic/activetap_fe","ae2:network/cables/covered_light_gray","mcwroofs:gutter_base_lime","everythingcopper:copper_anvil","deepresonance:tank","minecraft:prismarine_brick_stairs_from_prismarine_stonecutting","mcwlights:striped_lantern","minecraft:deepslate_brick_wall_from_deepslate_bricks_stonecutting","mcwfences:jungle_highley_gate","mcwtrpdoors:dark_oak_classic_trapdoor","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","create:birch_window","railcraft:coke_oven_bricks","cfm:birch_park_bench","mcwroofs:gutter_middle_red","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","minecraft:conduit","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","deeperdarker:bloom_chest_boat","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","sophisticatedstorage:spruce_chest","mcwpaths:cobbled_deepslate_diamond_paving","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:cyan_sofa","mcwroofs:cyan_concrete_upper_lower_roof","cfm:stripped_dark_oak_chair","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","mcwfurnitures:dark_oak_counter","minecraft:powered_rail","mcwdoors:dark_oak_stable_door","mcwdoors:birch_bamboo_door","mcwwindows:diorite_pane_window","dyenamics:wine_stained_glass_pane_from_glass_pane","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","twigs:birch_table","cfm:dye_blue_picket_fence","enderio:resetting_lever_ten_from_prev","mcwbridges:end_stone_bricks_bridge","mcwroofs:magenta_concrete_upper_lower_roof","reliquary:crimson_cloth","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","aether:stone_pickaxe_repairing","forbidden_arcanus:clibano_combustion/diamond_from_clibano_combusting","minecraft:blue_terracotta","mcwroofs:purple_terracotta_lower_roof","supplementaries:stone_lamp","sophisticatedstorage:void_upgrade","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","mcwroofs:spruce_planks_upper_lower_roof","minecraft:gray_terracotta","minecraft:comparator","handcrafted:dark_oak_corner_trim","mcwwindows:white_mosaic_glass_pane","xnet:redstone_proxy","minecraft:netherite_boots_smithing","mob_grinding_utils:recipe_tintedglass","mcwfurnitures:stripped_birch_drawer_counter","cfm:stripped_spruce_coffee_table","mcwroofs:pink_concrete_attic_roof","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","mcwroofs:prismarine_brick_lower_roof","dyenamics:peach_terracotta","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","rftoolspower:powercell_card","mcwdoors:dark_oak_japanese2_door","minecraft:terracotta","delightful:knives/silver_knife","reliquary:alkahestry_altar","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:yellow_lamp","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","dimstorage:dimensional_tank","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","comforts:sleeping_bag_gray","twigs:copper_pillar_stonecutting","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","minecraft:red_dye_from_tulip","ae2:network/cells/fluid_storage_cell_4k_storage","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwlights:double_street_lamp","minecraft:beacon","railcraft:tin_gear","minecraft:tnt","mcwfurnitures:stripped_birch_bookshelf","minecraft:andesite_slab_from_andesite_stonecutting","mcwdoors:spruce_stable_door","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","cfm:spruce_upgraded_gate","minecraft:black_dye","biomesoplenty:pine_boat","cfm:stripped_birch_chair","minecraft:deepslate_brick_stairs_from_polished_deepslate_stonecutting","croptopia:shaped_sticky_toffee_pudding","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_birch_cabinet","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","minecraft:dune_armor_trim_smithing_template_smithing_trim","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","mekanism:steel_casing","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwfurnitures:spruce_cupboard_counter","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwdoors:dark_oak_waffle_door","domum_ornamentum:light_gray_brick_extra","ironfurnaces:augments/augment_speed","handcrafted:sandstone_pillar_trim","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","ae2:shaped/walls/smooth_sky_stone_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","ae2:network/crafting/256k_cpu_crafting_storage","mcwroofs:magenta_terracotta_lower_roof","railcraft:routing_table_book","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","sophisticatedstorage:storage_link","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:birch_planks_upper_lower_roof","mcwroofs:stone_attic_roof","mcwbridges:rope_dark_oak_bridge","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:item_shelf","handcrafted:terracotta_plate","supplementaries:stone_tile","handcrafted:calcite_corner_trim","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","rftoolsbuilder:space_chamber","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","supplementaries:flags/flag_white","silentgear:coating_smithing_template","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","cfm:dark_oak_desk_cabinet","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","mcwbridges:birch_rail_bridge","mcwfurnitures:dark_oak_striped_chair","minecraft:glass_pane","supplementaries:timber_brace","allthearcanistgear:unobtainium_robes_smithing","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","mcwroofs:orange_concrete_roof","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","handcrafted:birch_cupboard","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","ae2:network/crafting/cpu_crafting_unit","mcwbridges:deepslate_brick_bridge","mcwbiomesoplenty:pine_pane_window","comforts:sleeping_bag_to_brown","mcwfurnitures:stripped_birch_lower_bookshelf_drawer","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","mcwfurnitures:stripped_dark_oak_coffee_table","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","mcwfurnitures:stripped_birch_coffee_table","ae2:network/blocks/interfaces_interface_part","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","additionallanterns:normal_lantern_colorless","mcwlights:magenta_paper_lamp","alltheores:lumium_plate","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_spruce_cupboard_counter","cfm:orange_kitchen_drawer","mcwroofs:spruce_planks_top_roof","ae2:materials/annihilationcore","enderio:cold_fire_igniter","croptopia:chicken_and_noodles","ae2:network/cables/dense_smart_from_smart","forbidden_arcanus:clibano_combustion/lapis_lazuli_from_clibano_combusting","silentgear:stone_rod","ae2:network/cables/covered_green","handcrafted:spruce_pillar_trim","minecraft:leather_boots","railcraft:tank_detector","minecraft:netherite_ingot_from_netherite_block","create:crafting/appliances/netherite_diving_boots_from_netherite","railcraft:steel_spike_maul","handcrafted:sandstone_corner_trim","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","mcwbridges:mossy_stone_brick_bridge","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","megacells:network/mega_pattern_provider","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","mcwlights:pink_paper_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","ae2:network/crystal_resonance_generator","mcwfurnitures:stripped_dark_oak_stool_chair","mcwfurnitures:birch_end_table","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","dyenamics:bubblegum_terracotta","minecraft:red_nether_brick_slab_from_red_nether_bricks_stonecutting","mcwwindows:jungle_plank_window","gtceu:smelting/smelt_iron_ore_to_ingot","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","rftoolscontrol:workbench","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","minecraft:dye_black_carpet","draconicevolution:components/draconium_ingot_from_ore","minecraft:chiseled_stone_bricks","mcwroofs:birch_upper_lower_roof","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","ae2:network/cables/covered_purple","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","dyenamics:dye_mint_carpet","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","minecraft:magma_block","mcwwindows:crimson_stem_parapet","minecraft:spruce_pressure_plate","enderio:resetting_lever_thirty_inv","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","mcwdoors:dark_oak_whispering_door","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","connectedglass:clear_glass_black_pane2","nethersdelight:nether_brick_smoker","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","handcrafted:spruce_nightstand","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","minecraft:deepslate_tiles_from_deepslate_bricks_stonecutting","sfm:cable","silentgear:raw_crimson_iron_from_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbridges:mossy_cobblestone_bridge_stair","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","ae2:network/parts/monitors_storage","twigs:mossy_cobblestone_brick_slab_from_mossy_cobblestone_stonecutting","mekanism:crusher","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","minecraft:smooth_quartz_slab_from_smooth_quartz_stonecutting","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","utilitix:spruce_shulker_boat","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","mcwtrpdoors:dark_oak_blossom_trapdoor","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:dark_oak_fence","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","littlelogistics:fluid_hopper","railcraft:goggles","twigs:paper_lantern","mcwtrpdoors:dark_oak_beach_trapdoor","mcwdoors:birch_beach_door","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","twigs:polished_bloodstone_stonecutting","mcwdoors:birch_bark_glass_door","domum_ornamentum:brick_extra","comforts:hammock_cyan","travelersbackpack:iron","ae2:network/cables/covered_black","mcwroofs:oak_planks_upper_steep_roof","ae2:tools/certus_quartz_wrench","ae2:network/cables/dense_smart_purple","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","mcwfurnitures:birch_modern_desk","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwroofs:dark_oak_attic_roof","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","alltheores:peridot_from_hammer_crushing","minecraft:oak_fence","pneumaticcraft:liquid_compressor","create:polished_cut_calcite_wall_from_stone_types_calcite_stonecutting","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:mossy_stone_running_bond","minecraft:prismarine_brick_stairs","mcwpaths:stone_flagstone_slab","ae2:decorative/sky_stone_small_brick_from_stonecutting","mcwroofs:black_terracotta_upper_lower_roof","supplementaries:sconce_soul","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","reliquary:uncrafting/redstone","mcwwindows:cherry_four_window","handcrafted:dark_oak_bench","mcwwindows:granite_louvered_shutter","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","securitycraft:sentry","mcwfurnitures:dark_oak_triple_drawer","immersiveengineering:crafting/blueprint_bullets","minecraft:netherite_block","alltheores:lumium_ingot_from_dust_blasting","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","supplementaries:dispenser_minecart","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","utilitarian:no_soliciting/soliciting_carpets/blue_trapped_soliciting_carpet","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","twigs:smooth_stone_brick_stairs","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","ae2:tools/certus_quartz_spade","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","farmersdelight:skillet","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","minecraft:prismarine_slab_from_prismarine_stonecutting","alltheores:constantan_dust_from_alloy_blending","mcwroofs:black_concrete_upper_lower_roof","ae2:network/cables/dense_covered_black","mcwfurnitures:dark_oak_bookshelf","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","expatternprovider:epp_alt","utilitix:piston_controller_rail","minecraft:deepslate_tile_wall_from_deepslate_bricks_stonecutting","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","mcwpaths:mossy_cobblestone_basket_weave_paving","minecraft:green_terracotta","minecraft:white_stained_glass","expatternprovider:assembler_matrix_frame","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","allthemodium:vibranium_nugget_from_ingot","ae2:network/cells/fluid_storage_cell_64k","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","ae2:tools/paintballs_pink","minecraft:purpur_stairs","domum_ornamentum:white_brick_extra","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","minecraft:mossy_cobblestone_stairs","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","sfm:labelgun","sophisticatedstorage:dark_oak_chest","mcwfences:jungle_hedge","reliquary:angelic_feather","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","connectedglass:scratched_glass_blue2","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","mcwdoors:birch_tropical_door","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","supplementaries:altimeter","ae2:network/cables/dense_smart_gray","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwbridges:rope_spruce_bridge","twigs:bloodstone_slab","supplementaries:end_stone_lamp","allthecompressed:compress/deepslate_1x","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","mcwfurnitures:stripped_birch_double_wardrobe","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","minecraft:quartz_stairs","handcrafted:oven","minecraft:quartz_from_blasting","handcrafted:calcite_pillar_trim","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","ae2:network/parts/terminals_pattern_encoding","minecraft:red_stained_glass","minecraft:light_gray_stained_glass_pane_from_glass_pane","mcwroofs:white_steep_roof","mcwpaths:cobbled_deepslate_crystal_floor_slab","minecraft:purpur_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","mcwpaths:cobbled_deepslate_crystal_floor","supplementaries:candle_holders/candle_holder_blue","rftoolsbuilder:shape_card_quarry_fortune","mcwfurnitures:stripped_dark_oak_bookshelf_drawer","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","connectedglass:borderless_glass_blue_pane2","mcwfurnitures:stripped_dark_oak_chair","mcwpaths:blackstone_crystal_floor","travelersbackpack:quartz","mcwfurnitures:cabinet_door","supplementaries:candle_holders/candle_holder_orange_dye","mcwwindows:stripped_spruce_pane_window","additionallanterns:cobbled_deepslate_chain","allthecompressed:compress/sand_1x","cfm:dark_oak_crate","twigs:mossy_cobblestone_bricks_stonecutting","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","twigs:polished_bloodstone","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","utilitarian:utility/dark_oak_logs_to_stairs","mcwwindows:stripped_dark_oak_log_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","littlelogistics:guide_rail_corner","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","dyenamics:wine_dye","handcrafted:bench","minecraft:gray_stained_glass_pane_from_glass_pane","mekanism:processing/osmium/ingot/from_raw_smelting","allthecompressed:compress/andesite_1x","cfm:birch_cabinet","allthecompressed:compress/sand_2x","mcwwindows:diorite_four_window","minecraft:stone_slab","pneumaticcraft:compressed_bricks","mcwfurnitures:stripped_spruce_desk","cfm:gray_sofa","mcwroofs:red_nether_bricks_upper_lower_roof","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","silentgear:blaze_gold_ingot_from_block","create:small_calcite_brick_wall_from_stone_types_calcite_stonecutting","mekanism:tier_installer/basic","handcrafted:dark_oak_pillar_trim","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","travelersbackpack:backpack_tank","minecraft:blast_furnace","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbridges:glass_bridge","additionallanterns:normal_lantern_light_blue","mcwbiomesoplenty:pine_plank_four_window","ae2:materials/cardenergy","mcwroofs:blackstone_steep_roof","utilitarian:no_soliciting/soliciting_carpets/green_trapped_soliciting_carpet","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","minecraft:light_blue_stained_glass_pane_from_glass_pane","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","dyenamics:bed/mint_bed_frm_white_bed","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","ae2:materials/carddistribution","ae2:network/parts/import_bus","mcwfurnitures:dark_oak_double_drawer","croptopia:campfire_molasses","ae2:network/crafting/16k_cpu_crafting_storage","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","ae2:network/upgrade_wireless_crafting_terminal","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","minecraft:blue_stained_glass_pane_from_glass_pane","additionallanterns:netherite_chain","railcraft:water_tank_siding","expatternprovider:assembler_matrix_crafter","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","xnet:advanced_connector_green","ae2:network/cables/glass_pink","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","ae2:network/cables/smart_cyan","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","minecraft:fletching_table","handcrafted:pufferfish_trophy","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","rftoolsbuilder:space_chamber_controller","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","minecraft:mossy_cobblestone_wall","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","travelersbackpack:dye_blue_sleeping_bag","dyenamics:bed/peach_bed","mcwwindows:one_way_glass","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwroofs:bricks_top_roof","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","mekanism:tier_installer/advanced","cfm:mangrove_mail_box","biomesoplenty:brimstone_bricks_from_brimstone_stonecutting","sophisticatedstorage:smelting_upgrade","mekanism:upgrade/energy","minecraft:oak_chest_boat","mcwpaths:mossy_stone_flagstone_path","mcwdoors:spruce_waffle_door","simplylight:illuminant_purple_block_on_toggle","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","allthecompressed:compress/grass_block_1x","minecraft:stone_pressure_plate","mcwroofs:spruce_planks_lower_roof","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","ae2additions:components/super/16k","connectedglass:borderless_glass_orange2","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","connectedglass:scratched_glass_black_pane2","securitycraft:sc_manual","twigs:silt_from_silt_balls","pneumaticcraft:logistics_core","everythingcopper:copper_helmet","mcwroofs:red_concrete_upper_lower_roof","create:crafting/kinetics/yellow_seat","mcwfurnitures:dark_oak_modern_wardrobe","mcwpaths:andesite_windmill_weave_path","dyenamics:aquamarine_dye","mcwdoors:spruce_swamp_door","modularrouters:blank_upgrade","mcwdoors:spruce_western_door","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","dyenamics:icy_blue_stained_glass_pane_from_glass_pane","ae2:shaped/walls/sky_stone_block","cfm:birch_chair","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","croptopia:cheeseburger","cfm:stripped_spruce_cabinet","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwroofs:green_terracotta_steep_roof","mcwdoors:spruce_barn_glass_door","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","comforts:hammock_white","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","travelersbackpack:diamond_smithing","mcwfurnitures:stripped_oak_cupboard_counter","ad_astra:orange_industrial_lamp","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","cfm:birch_desk","immersiveengineering:crafting/wirecoil_structure_steel","mcwfurnitures:spruce_coffee_table","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","farmersdelight:cooking/vegetable_soup","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","minecraft:dark_oak_trapdoor","mcwfences:iron_cheval_de_frise","additionallanterns:normal_lantern_brown","minecraft:iron_shovel","tombstone:dark_marble","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","ironfurnaces:furnaces/diamond_furnace","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwwindows:stone_brick_arrow_slit","ae2:smelting/smooth_sky_stone_block","chimes:amethyst_chimes","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","croptopia:ham_sandwich","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","minecraft:polished_deepslate_slab","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","quark:tweaks/crafting/clear_glass","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","alltheores:osmium_dust_from_hammer_ingot_crushing","mcwwindows:dark_oak_louvered_shutter","mcwdoors:dark_oak_glass_door","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","minecraft:deepslate_bricks","create:calcite_pillar_from_stone_types_calcite_stonecutting","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","handcrafted:birch_table","enderio:basic_fluid_filter","rftoolsutility:counter_module","aether:netherite_boots_repairing","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","minecraft:oak_door","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:black_roof_block","mcwfurnitures:stripped_birch_wardrobe","handcrafted:spruce_dining_bench","advancedperipherals:peripheral_casing","mcwfurnitures:birch_table","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwroofs:spruce_planks_roof","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","minecraft:spruce_door","mcwfurnitures:stripped_birch_covered_desk","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwfurnitures:dark_oak_bookshelf_drawer","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","mcwroofs:lime_concrete_upper_lower_roof","mcwfurnitures:dark_oak_lower_bookshelf_drawer","mcwbiomesoplenty:magic_highley_gate","utilitarian:utility/birch_logs_to_doors","minecraft:dark_prismarine","utilitarian:no_soliciting/soliciting_carpets/orange_trapped_soliciting_carpet","ae2:tools/fluix_sword","sophisticatedstorage:birch_limited_barrel_1","simplemagnets:basic_demagnetization_coil","mcwroofs:yellow_concrete_upper_lower_roof","mcwwindows:pink_curtain","rftoolspower:pearl_injector","dyenamics:mint_terracotta","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","sophisticatedstorage:birch_limited_barrel_4","sophisticatedstorage:birch_limited_barrel_2","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","sophisticatedstorage:birch_limited_barrel_3","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","dyenamics:navy_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","biomesoplenty:fir_chest_boat","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","minecraft:bow","supplementaries:wrench","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","deepresonance:radiation_suit_boots","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","expatternprovider:silicon_block_disassembler","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","botania:mushroom_stew","minecraft:andesite_stairs","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","dyenamics:amber_dye","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","mcwdoors:dark_oak_classic_door","cfm:black_cooler","twigs:polished_calcite_stonecutting","supplementaries:sconce","mcwfurnitures:stripped_dark_oak_double_drawer","silentgear:rough_rod","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","mcwpaths:mossy_stone_crystal_floor_slab","dyenamics:maroon_stained_glass_pane_from_glass_pane","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:birch_top_roof","aether:skyroot_bed","mcwroofs:prismarine_brick_roof","mcwbiomesoplenty:palm_wired_fence","mcwfurnitures:dark_oak_cupboard_counter","mcwroofs:red_nether_bricks_attic_roof","railcraft:iron_tunnel_bore_head","mcwfurnitures:spruce_glass_table","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","dyenamics:bed/aquamarine_bed_frm_white_bed","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwfurnitures:spruce_covered_desk","botania:ghost_rail","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","minecraft:black_dye_from_wither_rose","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","nethersdelight:soul_compost_from_warped_roots","mcwdoors:spruce_mystic_door","sophisticatedstorage:backpack_advanced_void_upgrade_from_storage_advanced_void_upgrade","mcwtrpdoors:spruce_beach_trapdoor","aether:netherite_shovel_repairing","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","mcwbridges:balustrade_nether_bricks_bridge","pneumaticcraft:wall_lamp_inverted_brown","cfm:stripped_birch_bedside_cabinet","mcwroofs:andesite_roof","supplementaries:pedestal","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:shaped/stairs/sky_stone_block","ae2:network/cables/glass_yellow","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","alltheores:ruby_dust_from_hammer_crushing","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwpaths:sandstone_flagstone_path","dyenamics:rose_terracotta","rftoolsbuilder:mover_control_back","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","twigs:crimson_table","mcwfurnitures:stripped_birch_triple_drawer","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","minecraft:blackstone_stairs","handcrafted:dark_oak_couch","cfm:lime_kitchen_counter","minecraft:dye_orange_carpet","mcwbridges:mossy_cobblestone_bridge","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","mcwroofs:red_nether_bricks_steep_roof","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","minecraft:deepslate_tile_slab_from_deepslate_bricks_stonecutting","securitycraft:speed_module","cfm:stripped_oak_kitchen_sink_dark","mcwdoors:garage_black_door","domum_ornamentum:cream_stone_bricks","blue_skies:glowing_poison_stone","tombstone:carmin_marble","mcwroofs:brown_terracotta_steep_roof","crafting_on_a_stick:crafting_table","mcwdoors:oak_barn_door","mcwdoors:dark_oak_cottage_door","botania:alchemy_catalyst","allthemodium:ancient_stone_bricks","mcwfurnitures:spruce_double_drawer_counter","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","dyenamics:dye_bubblegum_carpet","botania:redstone_root","ae2:tools/paintballs_brown","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","dyenamics:dye_aquamarine_carpet","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","minecraft:purpur_slab_from_purpur_block_stonecutting","twigs:smooth_stone_brick_slab_from_smooth_stone_brick_stonecutting","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","supplementaries:timber_cross_brace","mcwfurnitures:stripped_dark_oak_cupboard_counter","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","reliquary:fertile_essence","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","ae2:network/cables/dense_smart_light_gray","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","rftoolsutility:environmental_controller","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","mcwbridges:mossy_stone_bridge_pier","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","mcwroofs:white_concrete_attic_roof","xnet:controller","cookingforblockheads:sink","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwfurnitures:dark_oak_end_table","mcwroofs:blue_concrete_lower_roof","create:ornate_iron_window","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","reliquary:uncrafting/ender_pearl","additionallanterns:obsidian_chain","mcwfurnitures:stripped_spruce_coffee_table","minecraft:ender_eye","supplementaries:blackstone_lamp","minecraft:polished_deepslate_wall_from_polished_deepslate_stonecutting","mcwfurnitures:spruce_double_wardrobe","mcwpaths:brick_strewn_rocky_path","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","minecraft:birch_slab","enderio:resetting_lever_three_hundred","mcwlights:cross_lantern","mcwlights:brown_lamp","mcwwindows:bricks_four_window","railcraft:receiver_circuit","cfm:stripped_dark_oak_coffee_table","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","mcwtrpdoors:birch_tropical_trapdoor","minecraft:pink_stained_glass_pane_from_glass_pane","rftoolspower:blazing_infuser","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","ae2:network/parts/terminals_pattern_access","thermal:smelting/niter_from_smelting","additionallanterns:diamond_chain","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","mcwfurnitures:birch_triple_drawer","ae2:network/blocks/io_condenser","ae2:block_cutter/walls/quartz_wall","supplementaries:stonecutting/stone_tile","ae2:network/cables/covered_pink","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","handcrafted:birch_shelf","additionallanterns:normal_lantern_purple","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","minecraft:dye_black_wool","enderio:dark_steel_trapdoor","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","rftoolsbuilder:shape_card_quarry_silk","pneumaticcraft:reinforced_brick_from_slab","minecraft:cooked_beef","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","mcwpaths:mossy_stone_flagstone_stairs","mcwlights:yellow_paper_lamp","domum_ornamentum:cream_bricks","allthemodium:allthemodium_ingot_from_raw_blasting","mcwroofs:red_nether_bricks_roof","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","forbidden_arcanus:arcane_chiseled_darkstone","mcwfurnitures:stripped_dark_oak_bookshelf_cupboard","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","railcraft:age_detector","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","pneumaticcraft:wall_lamp_orange","minecraft:gold_nugget_from_smelting","dyenamics:wine_stained_glass","mcwwindows:black_curtain","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","ae2:network/cables/dense_smart_black","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","twigs:smooth_stone_brick_wall","mcwfurnitures:stripped_dark_oak_lower_triple_drawer","mcwfurnitures:stripped_birch_end_table","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","enderio:resetting_lever_five_inv","twigs:calcite_stairs","handcrafted:spruce_corner_trim","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","mcwpaths:stone_running_bond","minecraft:deepslate_brick_wall_from_polished_deepslate_stonecutting","mcwfurnitures:stripped_birch_modern_desk","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","ae2:network/cables/smart_blue","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","botania:turntable","cfm:stripped_birch_table","ae2:network/cells/item_storage_components_cell_1k_part","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","domum_ornamentum:black_brick_extra","supplementaries:daub","railcraft:steel_chestplate","alltheores:zinc_dust_from_hammer_crushing","minecraft:netherite_scrap","mcwfences:oak_hedge","mcwdoors:spruce_japanese2_door","minecraft:cut_sandstone","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","mcwroofs:oak_planks_roof","ironfurnaces:furnaces/gold_furnace","ae2:decorative/quartz_block","ae2:network/cables/dense_smart_orange","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","aether:chainmail_boots_repairing","mcwfurnitures:birch_drawer_counter","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:nether_brick_slab","mcwfurnitures:birch_bookshelf","ae2:block_cutter/slabs/smooth_sky_stone_slab","supplementaries:blackstone_tile","mcwbiomesoplenty:hellbark_highley_gate","minecraft:deepslate_tile_stairs_from_polished_deepslate_stonecutting","minecraft:enchanting_table","mcwpaths:cobbled_deepslate_square_paving","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","mcwtrpdoors:dark_oak_barn_trapdoor","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","dyenamics:peach_stained_glass_pane_from_glass_pane","ad_astra:small_blue_industrial_lamp","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","connectedglass:clear_glass_blue_pane2","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","mcwpaths:mossy_stone_running_bond_path","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:logistics_configurator","create:cut_tuff_from_stone_types_tuff_stonecutting","rftoolsutility:dialing_device","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:1b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:8125,warning_level:0}}