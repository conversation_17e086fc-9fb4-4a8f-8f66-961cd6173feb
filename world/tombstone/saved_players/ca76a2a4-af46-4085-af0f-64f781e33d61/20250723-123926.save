{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:4.5d,Name:"forge:block_reach"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"}],BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:[]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:36375},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{},"rftoolsutility:properties":{allowFlying:0b,buffTicks:166,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:[]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],twilightforest_banished:1b},"quark:trying_crawl":0b,simplylight_unlocked:2},Health:20.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:9b,id:"minecraft:player_head",tag:{SkullOwner:{Id:[I;**********,**********,-**********,**********],Name:"ZephyrWindSpirit",Properties:{textures:[{Signature:"bszJX3BvT6r4197un7fCTLWwbmYvucp9bjel1xHRqbEgw6BUuXWSjvUi2rSORpzP9fiT1fXL1AbDsu9L1/JkTVsqhBdsOZjlTEQDjTLE4WPy1nUobhYGT+mlNJjcQV9mQtOm8TYVr/lRPIu/uKjJPk1Ot/1W7oY0s9wEUxjDCzdBjDfDybaH9x8VQZR2cSgTPo8NXB/zNV+DG/6ylgh3z6FxvP8m7PwFUveouivcMZZGB8mV0Z3cRms4vymPrD+TLGQNLwQ4C4RYgMka4SKDKT2E2JLC3JXXUY/94zG9vsU92FwQ6kfz1h7p9pPQSJdVbdji0qVnAR8tX0KswksRYdY4vnwIFPzpMXfNEm6SroEFodp9FXHZKvPnlDgyVixQU/oIkp+UALXByvSPnFod7TCqhLMW8nSn+96gOTm/TnV95YvY/d7dRIAuNS/hu0dh/ITasE9M0HJ2/OaPH23vVENs4YVh9vdjl9kM6E0LlxgzvTa3H9uCvzt0zh67I8B/hQqyQrGgrTMIE0+B7tYI+e9ZZIfeRJx/NlB6i11CtNZ3iSaycUQ0uUOmZnqhkyvr6tUf0sDTnXvf9aUfVaV4a8Mz4YruQDGmHbJQIwGZQxpt8yp3qqjHx2u+Uh6JdpwXfi4P+qccovq360nfeMVHa3Omy8f8QJuffSIPzIZrdg0=",Value:"ewogICJ0aW1lc3RhbXAiIDogMTY1Nzk0MjM3Mzc5MiwKICAicHJvZmlsZUlkIiA6ICI0OGU2NjBiZjU0NDk0ZDlkYmRkZTNiODczY2JkNjlmYSIsCiAgInByb2ZpbGVOYW1lIiA6ICJaZXBoeXJXaW5kU3Bpcml0IiwKICAic2lnbmF0dXJlUmVxdWlyZWQiIDogdHJ1ZSwKICAidGV4dHVyZXMiIDogewogICAgIlNLSU4iIDogewogICAgICAidXJsIiA6ICJodHRwOi8vdGV4dHVyZXMubWluZWNyYWZ0Lm5ldC90ZXh0dXJlLzRmNzYyNTNjMTVjMTg1ZTg1NzVjY2FiMDgwN2Y3MWNiZmFlMWJhZGZlOWMwOTM2MGUwNDgxODc2MzZkZGNjNGQiCiAgICB9LAogICAgIkNBUEUiIDogewogICAgICAidXJsIiA6ICJodHRwOi8vdGV4dHVyZXMubWluZWNyYWZ0Lm5ldC90ZXh0dXJlLzIzNDBjMGUwM2RkMjRhMTFiMTVhOGIzM2MyYTdlOWUzMmFiYjIwNTFiMjQ4MWQwYmE3ZGVmZDYzNWNhN2E5MzMiCiAgICB9CiAgfQp9"}]}}}},{Count:1b,Slot:11b,id:"patchouli:guide_book",tag:{"patchouli:book":"allthemodium:allthemodium_book"}}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-14.543094941389198d,72.0d,-55.235669472063776d],Railways_DataVersion:2,Rotation:[-170.24188f,40.49991f],Score:280,SelectedItemSlot:0,SleepTimer:0s,Spigot.ticksLived:36374,UUID:[I;-898194780,-1354350459,-1357945609,-2115814047],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-3848290926520L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:13,XpP:0.9999997f,XpSeed:0,XpTotal:280,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1753238489855L,keepLevel:0b,lastKnownName:"junmiamiao",lastPlayed:1753245566908L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:3.0230043f,foodLevel:20,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwbiomesoplenty:magic_horse_fence","corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","mcwfences:railing_deepslate_brick_wall","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","mcwbiomesoplenty:redwood_highley_gate","aquaculture:cooked_fish_fillet_from_smoking","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","simplylight:illuminant_block_on_toggle","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","aquaculture:cooked_fish_fillet","mcwfences:mangrove_picket_fence","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","aquaculture:planks_from_driftwood","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwbiomesoplenty:umbran_horse_fence","mcwfences:birch_stockade_fence","mcwfences:dark_oak_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwbiomesoplenty:empyreal_hedge","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:hellbark_picket_fence","mcwfences:crimson_curved_gate","corail_woodcutter:crimson_woodcutter","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","corail_woodcutter:bamboo_woodcutter","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","aquaculture:jellyfish_to_slimeball","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:andesite_pillar_wall","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwfences:majestic_metal_fence_gate","energymeter:meter","simplylight:rodlamp","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","aquaculture:iron_nugget_from_smelting","aquaculture:sushi","mcwbiomesoplenty:rainbow_birch_hedge","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:flowering_azalea_hedge","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","mcwfences:expanded_mesh_metal_fence","mcwfences:acacia_stockade_fence","mcwfences:crimson_picket_fence","aquaculture:birch_fish_mount","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","mcwbiomesoplenty:fir_curved_gate","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwfences:warped_horse_fence","mcwbiomesoplenty:mahogany_curved_gate","mcwbiomesoplenty:maple_stockade_fence","simplylight:illuminant_red_block_toggle","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwfences:jungle_highley_gate","mcwbiomesoplenty:pine_highley_gate","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","mcwfences:dark_oak_hedge","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:warped_picket_fence","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwbiomesoplenty:pine_wired_fence","mcwfences:bamboo_highley_gate","mcwfences:modern_nether_brick_wall","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","mcwfences:mangrove_horse_fence","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","mcwfences:end_brick_grass_topped_wall","mcwfences:railing_red_sandstone_wall","corail_woodcutter:acacia_woodcutter","mcwbiomesoplenty:jacaranda_stockade_fence","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","aquaculture:gold_nugget_from_smelting","mcwfences:jungle_curved_gate","simplylight:illuminant_blue_block_toggle","mcwfences:acacia_pyramid_gate","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","solcarrot:food_book","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwfences:twilight_metal_fence","aquaculture:double_hook","mcwfences:curved_metal_fence","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:willow_stockade_fence","mcwbiomesoplenty:hellbark_hedge","mcwfences:nether_brick_grass_topped_wall","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","aquaculture:tin_can_to_iron_nugget","mcwfences:modern_red_sandstone_wall","simplylight:illuminant_orange_block_on_toggle","aquaculture:iron_fishing_rod","mcwfences:deepslate_grass_topped_wall","mcwfences:andesite_railing_gate","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","simplylight:illuminant_light_blue_block_toggle","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","mcwfences:spruce_wired_fence","ae2wtlib:magnet_card","mcwfences:gothic_metal_fence","mcwfences:oak_pyramid_gate","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","mcwfences:deepslate_railing_gate","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwfences:birch_picket_fence","mcwfences:double_curved_metal_fence","mcwfences:deepslate_pillar_wall","mcwbiomesoplenty:magic_wired_fence","mcwbiomesoplenty:empyreal_wired_fence","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","simplylight:illuminant_pink_block_on_dyed","mcwfences:stone_grass_topped_wall","mcwfences:oak_picket_fence","mcwfences:mud_brick_grass_topped_wall","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwbiomesoplenty:jacaranda_wired_fence","mcwfences:bamboo_horse_fence","mcwbiomesoplenty:jacaranda_horse_fence","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","mcwbiomesoplenty:magic_picket_fence","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","simplylight:illuminant_purple_block_on_dyed","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","simplylight:illuminant_yellow_block_on_toggle","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","mcwbiomesoplenty:pine_hedge","simplylight:bulb","simplylight:illuminant_green_block_dyed","simplylight:illuminant_magenta_block_toggle","mcwfences:oak_curved_gate","mcwbiomesoplenty:mahogany_horse_fence","aquaculture:bobber","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfences:nether_brick_pillar_wall","mcwfences:granite_pillar_wall","simplylight:illuminant_brown_block_dyed","simplylight:illuminant_brown_block_on_dyed","mcwbiomesoplenty:willow_curved_gate","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","mcwbiomesoplenty:redwood_hedge","mcwbiomesoplenty:redwood_stockade_fence","ae2wtlib:quantum_bridge_card","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","mcwbiomesoplenty:maple_curved_gate","aquaculture:gold_hook","mcwbiomesoplenty:palm_wired_fence","simplylight:illuminant_yellow_block_toggle","mcwfences:diorite_railing_gate","mcwbiomesoplenty:hellbark_stockade_fence","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","mcwbiomesoplenty:cypress_hedge","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwbiomesoplenty:empyreal_stockade_fence","aquaculture:oak_fish_mount","mcwbiomesoplenty:redwood_wired_fence","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","simplylight:illuminant_light_gray_block_on_dyed","mcwfences:red_sandstone_grass_topped_wall","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwbiomesoplenty:redwood_pyramid_gate","mcwfences:andesite_grass_topped_wall","mcwbiomesoplenty:empyreal_highley_gate","mcwfences:railing_prismarine_wall","mcwbiomesoplenty:hellbark_wired_fence","mcwbiomesoplenty:pine_horse_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:warped_wired_fence","mcwfences:bamboo_fence","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfences:jungle_stockade_fence","mcwfences:end_brick_railing_gate","simplylight:illuminant_cyan_block_dyed","aquaculture:golden_fishing_rod","simplylight:illuminant_pink_block_dyed","mcwfences:oak_highley_gate","simplylight:illuminant_light_gray_block_toggle","mcwfences:diorite_pillar_wall","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwfences:bamboo_picket_fence","mcwfences:sandstone_grass_topped_wall","mcwbiomesoplenty:fir_hedge","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","mcwfences:birch_highley_gate","mcwfences:railing_diorite_wall","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","simplylight:illuminant_lime_block_on_dyed","simplylight:illuminant_orange_block_on_dyed","mcwbiomesoplenty:mahogany_highley_gate","mcwfences:bamboo_pyramid_gate","mcwfences:warped_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:jungle_picket_fence","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","mcwfences:acacia_picket_fence","mcwbiomesoplenty:palm_stockade_fence","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","simplylight:illuminant_purple_block_toggle","supplementaries:soap/map","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:snowblossom_hedge","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","mcwfences:railing_blackstone_wall","mcwfences:dark_oak_highley_gate","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwbiomesoplenty:palm_highley_gate","mcwbiomesoplenty:dead_wired_fence","simplylight:illuminant_red_block_on_toggle","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","simplylight:illuminant_blue_block_on_toggle","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","aquaculture:gold_nugget_from_gold_fish","mcwfences:modern_sandstone_wall","mcwbiomesoplenty:umbran_stockade_fence","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:magic_curved_gate","mcwfences:mesh_metal_fence","mcwbiomesoplenty:empyreal_picket_fence","mcwbiomesoplenty:pine_curved_gate","mcwfences:mangrove_stockade_fence","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","simplylight:illuminant_lime_block_dyed","botania:speed_up_belt","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","simplylight:edge_light","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","simplylight:illuminant_block_dyed","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:sandstone_pillar_wall","mcwfences:prismarine_pillar_wall","mcwbiomesoplenty:orange_maple_hedge","simplylight:illuminant_black_block_on_toggle","supplementaries:slice_map","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwbiomesoplenty:maple_horse_fence","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","mcwfences:stone_pillar_wall","simplylight:illuminant_gray_block_toggle","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:empyreal_pyramid_gate","simplylight:illuminant_green_block_toggle","mcwbiomesoplenty:red_maple_hedge","simplylight:illuminant_blue_block_on_dyed","mcwfences:end_brick_pillar_wall","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","mcwbiomesoplenty:palm_hedge","mcwfences:railing_mud_brick_wall","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","mcwfences:dark_oak_horse_fence","mcwfences:oak_hedge","mcwfences:railing_sandstone_wall","mcwbiomesoplenty:willow_wired_fence","mcwbiomesoplenty:dead_picket_fence","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwbiomesoplenty:magic_hedge","mcwbiomesoplenty:hellbark_highley_gate","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwfences:spruce_curved_gate","mcwbiomesoplenty:hellbark_curved_gate","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","mcwbiomesoplenty:maple_picket_fence","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","mcwbiomesoplenty:maple_highley_gate","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwfences:deepslate_brick_railing_gate","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:modern_blackstone_wall","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge"],toBeDisplayed:["mcwbiomesoplenty:magic_horse_fence","corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","mcwfences:railing_deepslate_brick_wall","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","mcwbiomesoplenty:redwood_highley_gate","aquaculture:cooked_fish_fillet_from_smoking","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","simplylight:illuminant_block_on_toggle","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","aquaculture:cooked_fish_fillet","mcwfences:mangrove_picket_fence","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","aquaculture:planks_from_driftwood","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwbiomesoplenty:umbran_horse_fence","mcwfences:birch_stockade_fence","mcwfences:dark_oak_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwbiomesoplenty:empyreal_hedge","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:hellbark_picket_fence","mcwfences:crimson_curved_gate","corail_woodcutter:crimson_woodcutter","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","corail_woodcutter:bamboo_woodcutter","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","aquaculture:jellyfish_to_slimeball","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:andesite_pillar_wall","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwfences:majestic_metal_fence_gate","energymeter:meter","simplylight:rodlamp","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","aquaculture:iron_nugget_from_smelting","aquaculture:sushi","mcwbiomesoplenty:rainbow_birch_hedge","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:flowering_azalea_hedge","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","mcwfences:expanded_mesh_metal_fence","mcwfences:acacia_stockade_fence","mcwfences:crimson_picket_fence","aquaculture:birch_fish_mount","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","mcwbiomesoplenty:fir_curved_gate","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwfences:warped_horse_fence","mcwbiomesoplenty:mahogany_curved_gate","mcwbiomesoplenty:maple_stockade_fence","simplylight:illuminant_red_block_toggle","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwfences:jungle_highley_gate","mcwbiomesoplenty:pine_highley_gate","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","mcwfences:dark_oak_hedge","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:warped_picket_fence","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwbiomesoplenty:pine_wired_fence","mcwfences:bamboo_highley_gate","mcwfences:modern_nether_brick_wall","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","mcwfences:mangrove_horse_fence","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","mcwfences:end_brick_grass_topped_wall","mcwfences:railing_red_sandstone_wall","corail_woodcutter:acacia_woodcutter","mcwbiomesoplenty:jacaranda_stockade_fence","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","aquaculture:gold_nugget_from_smelting","mcwfences:jungle_curved_gate","simplylight:illuminant_blue_block_toggle","mcwfences:acacia_pyramid_gate","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","solcarrot:food_book","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwfences:twilight_metal_fence","aquaculture:double_hook","mcwfences:curved_metal_fence","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:willow_stockade_fence","mcwbiomesoplenty:hellbark_hedge","mcwfences:nether_brick_grass_topped_wall","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","aquaculture:tin_can_to_iron_nugget","mcwfences:modern_red_sandstone_wall","simplylight:illuminant_orange_block_on_toggle","aquaculture:iron_fishing_rod","mcwfences:deepslate_grass_topped_wall","mcwfences:andesite_railing_gate","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","simplylight:illuminant_light_blue_block_toggle","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","mcwfences:spruce_wired_fence","ae2wtlib:magnet_card","mcwfences:gothic_metal_fence","mcwfences:oak_pyramid_gate","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","mcwfences:deepslate_railing_gate","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwfences:birch_picket_fence","mcwfences:double_curved_metal_fence","mcwfences:deepslate_pillar_wall","mcwbiomesoplenty:magic_wired_fence","mcwbiomesoplenty:empyreal_wired_fence","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","simplylight:illuminant_pink_block_on_dyed","mcwfences:stone_grass_topped_wall","mcwfences:oak_picket_fence","mcwfences:mud_brick_grass_topped_wall","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwbiomesoplenty:jacaranda_wired_fence","mcwfences:bamboo_horse_fence","mcwbiomesoplenty:jacaranda_horse_fence","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","mcwbiomesoplenty:magic_picket_fence","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","simplylight:illuminant_purple_block_on_dyed","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","simplylight:illuminant_yellow_block_on_toggle","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","mcwbiomesoplenty:pine_hedge","simplylight:bulb","simplylight:illuminant_green_block_dyed","simplylight:illuminant_magenta_block_toggle","mcwfences:oak_curved_gate","mcwbiomesoplenty:mahogany_horse_fence","aquaculture:bobber","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfences:nether_brick_pillar_wall","mcwfences:granite_pillar_wall","simplylight:illuminant_brown_block_dyed","simplylight:illuminant_brown_block_on_dyed","mcwbiomesoplenty:willow_curved_gate","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","mcwbiomesoplenty:redwood_hedge","mcwbiomesoplenty:redwood_stockade_fence","ae2wtlib:quantum_bridge_card","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","mcwbiomesoplenty:maple_curved_gate","aquaculture:gold_hook","mcwbiomesoplenty:palm_wired_fence","simplylight:illuminant_yellow_block_toggle","mcwfences:diorite_railing_gate","mcwbiomesoplenty:hellbark_stockade_fence","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","mcwbiomesoplenty:cypress_hedge","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwbiomesoplenty:empyreal_stockade_fence","aquaculture:oak_fish_mount","mcwbiomesoplenty:redwood_wired_fence","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","simplylight:illuminant_light_gray_block_on_dyed","mcwfences:red_sandstone_grass_topped_wall","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwbiomesoplenty:redwood_pyramid_gate","mcwfences:andesite_grass_topped_wall","mcwbiomesoplenty:empyreal_highley_gate","mcwfences:railing_prismarine_wall","mcwbiomesoplenty:hellbark_wired_fence","mcwbiomesoplenty:pine_horse_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:warped_wired_fence","mcwfences:bamboo_fence","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfences:jungle_stockade_fence","mcwfences:end_brick_railing_gate","simplylight:illuminant_cyan_block_dyed","aquaculture:golden_fishing_rod","simplylight:illuminant_pink_block_dyed","mcwfences:oak_highley_gate","simplylight:illuminant_light_gray_block_toggle","mcwfences:diorite_pillar_wall","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwfences:bamboo_picket_fence","mcwfences:sandstone_grass_topped_wall","mcwbiomesoplenty:fir_hedge","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","mcwfences:birch_highley_gate","mcwfences:railing_diorite_wall","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","simplylight:illuminant_lime_block_on_dyed","simplylight:illuminant_orange_block_on_dyed","mcwbiomesoplenty:mahogany_highley_gate","mcwfences:bamboo_pyramid_gate","mcwfences:warped_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:jungle_picket_fence","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","mcwfences:acacia_picket_fence","mcwbiomesoplenty:palm_stockade_fence","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","simplylight:illuminant_purple_block_toggle","supplementaries:soap/map","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:snowblossom_hedge","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","mcwfences:railing_blackstone_wall","mcwfences:dark_oak_highley_gate","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwbiomesoplenty:palm_highley_gate","mcwbiomesoplenty:dead_wired_fence","simplylight:illuminant_red_block_on_toggle","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","aquaculture:jungle_fish_mount","simplylight:illuminant_blue_block_on_toggle","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","aquaculture:gold_nugget_from_gold_fish","mcwfences:modern_sandstone_wall","mcwbiomesoplenty:umbran_stockade_fence","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:magic_curved_gate","mcwfences:mesh_metal_fence","mcwbiomesoplenty:empyreal_picket_fence","mcwbiomesoplenty:pine_curved_gate","mcwfences:mangrove_stockade_fence","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","simplylight:illuminant_lime_block_dyed","botania:speed_up_belt","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","simplylight:edge_light","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","simplylight:illuminant_block_dyed","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:sandstone_pillar_wall","mcwfences:prismarine_pillar_wall","mcwbiomesoplenty:orange_maple_hedge","simplylight:illuminant_black_block_on_toggle","supplementaries:slice_map","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwbiomesoplenty:maple_horse_fence","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","mcwfences:stone_pillar_wall","simplylight:illuminant_gray_block_toggle","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:empyreal_pyramid_gate","simplylight:illuminant_green_block_toggle","mcwbiomesoplenty:red_maple_hedge","simplylight:illuminant_blue_block_on_dyed","mcwfences:end_brick_pillar_wall","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","mcwbiomesoplenty:palm_hedge","mcwfences:railing_mud_brick_wall","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","mcwfences:dark_oak_horse_fence","mcwfences:oak_hedge","mcwfences:railing_sandstone_wall","mcwbiomesoplenty:willow_wired_fence","mcwbiomesoplenty:dead_picket_fence","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwbiomesoplenty:magic_hedge","mcwbiomesoplenty:hellbark_highley_gate","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwfences:spruce_curved_gate","mcwbiomesoplenty:hellbark_curved_gate","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","mcwbiomesoplenty:maple_picket_fence","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","mcwbiomesoplenty:maple_highley_gate","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwfences:deepslate_brick_railing_gate","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:modern_blackstone_wall","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:371,warning_level:0}}