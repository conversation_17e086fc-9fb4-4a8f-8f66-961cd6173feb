{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:2b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:341,Id:1,ShowIcon:1b,ShowParticles:1b,"forge:id":"minecraft:speed"}],Air:300s,Attributes:[{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.spell_damage"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Modifiers:[{Amount:0.6000000089406967d,Name:"effect.minecraft.speed 2",Operation:2,UUID:[I;-**********,929776792,-**********,**********]}],Name:"minecraft:generic.movement_speed"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;**********,354042026,-**********,428632013],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:21873},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{},"rftoolsutility:properties":{allowFlying:0b,buffTicks:88,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["allthemodium:allthemodium_apple"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:1,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:0b,PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["2ab8d773-2456-45f2-806b-084e4b94d6a1","2d293ddf-66fb-4b60-862a-67f687e6668b","88320f8d-ea59-4cc6-896d-79cc52d4aa80","cca8d749-2936-4d9a-a535-4335d9016efe","6c8881a8-dbdf-465b-bb3e-0c97f47d45a4"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-1227,tb_last_ground_location_y:253,tb_last_ground_location_z:2106,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},"aae$nokey":0b,"aae$upkey":1b,apoth_reforge_seed:0,"quark:trying_crawl":0b,simplylight_unlocked:2},Health:20.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:the_beyond",pos:[I;10060,30,10137]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-354.8577091568252d,253.0d,-61.293714501424574d],Railways_DataVersion:2,Rotation:[-130.69568f,41.24994f],Score:280,SelectedItemSlot:4,SleepTimer:0s,SpawnAngle:55.502686f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:-20,SpawnY:66,SpawnZ:-66,Spigot.ticksLived:21870,UUID:[I;-898194780,-**********,-**********,-**********],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-97306779311875L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:1,XpP:0.6666666f,XpSeed:-393473513,XpTotal:13,abilities:{flySpeed:0.05f,flying:0b,instabuild:1b,invulnerable:1b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1753238489855L,keepLevel:0b,lastKnownName:"junmiamiao",lastPlayed:1753262368009L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:3.2740047f,foodLevel:20,foodSaturationLevel:15.0f,foodTickTimer:0,playerGameType:1,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwbiomesoplenty:magic_horse_fence","corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","minecraft:charcoal","naturescompass:natures_compass","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","cfm:white_picket_fence","mcwbiomesoplenty:dead_pyramid_gate","supplementaries:bellows","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","mcwbiomesoplenty:redwood_highley_gate","handcrafted:spider_trophy","aquaculture:cooked_fish_fillet_from_smoking","cfm:red_kitchen_drawer","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","simplylight:illuminant_block_on_toggle","cfm:red_kitchen_sink","dyenamics:bed/spring_green_bed","minecraft:spruce_planks","ae2:network/parts/terminals_pattern_encoding","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","aquaculture:cooked_fish_fillet","cfm:light_gray_kitchen_sink","cfm:orange_kitchen_sink","twilightforest:wood/canopy_trapdoor","mcwfences:mangrove_picket_fence","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","aquaculture:planks_from_driftwood","twilightforest:wood/mangrove_fence","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwbiomesoplenty:umbran_horse_fence","mcwfences:birch_stockade_fence","mcwfences:dark_oak_stockade_fence","twilightforest:wood/twilight_oak_trapdoor","simplylight:illuminant_cyan_block_on_toggle","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","mcwfences:bamboo_wired_fence","twilightforest:wood/twilight_oak_sign","mcwbiomesoplenty:hellbark_picket_fence","mcwfences:crimson_curved_gate","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","ae2:network/crafting/patterns_blank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","aquaculture:jellyfish_to_slimeball","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","cfm:brown_kitchen_counter","simplylight:illuminant_lime_block_toggle","mcwfences:panelled_metal_fence","twilightforest:wood/mangrove_stairs","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:andesite_pillar_wall","mcwfences:curved_metal_fence_gate","minecraft:blast_furnace","mcwfences:acacia_hedge","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwfences:majestic_metal_fence_gate","domum_ornamentum:blockbarreldeco_onside","energymeter:meter","simplylight:rodlamp","biomesoplenty:maple_boat","sliceanddice:slicer","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","twilightforest:wood/canopy_button","mcwfences:granite_grass_topped_wall","cfm:brown_kitchen_sink","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","aquaculture:iron_nugget_from_smelting","aquaculture:sushi","silentgear:upgrade_base","mcwbiomesoplenty:rainbow_birch_hedge","handcrafted:pufferfish_trophy","cfm:white_picket_gate","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:flowering_azalea_hedge","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","cfm:light_blue_picket_gate","mcwfences:expanded_mesh_metal_fence","cfm:mangrove_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfences:acacia_stockade_fence","mcwfences:crimson_picket_fence","dyenamics:bed/peach_bed","aquaculture:birch_fish_mount","mcwfences:blackstone_pillar_wall","botania:petal_light_blue_double","aquaculture:gold_nugget_from_blasting","mcwbiomesoplenty:fir_curved_gate","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwfences:warped_horse_fence","mcwbiomesoplenty:mahogany_curved_gate","mcwbiomesoplenty:maple_stockade_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","supplementaries:twilightforest/sign_post_canopy","simplylight:illuminant_red_block_toggle","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","twilightforest:wood/mangrove_sign","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","twilightforest:wood/twilight_oak_stairs","twilightforest:wood/mining_banister","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","allthecompressed:compress/grass_block_1x","cfm:blue_kitchen_counter","mcwfences:jungle_highley_gate","mcwbiomesoplenty:pine_highley_gate","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfences:bamboo_curved_gate","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","dyenamics:bed/maroon_bed","mcwfences:acacia_highley_gate","mcwfences:dark_oak_hedge","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:warped_picket_fence","cfm:black_kitchen_drawer","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","mcwfences:bamboo_highley_gate","mcwfences:modern_nether_brick_wall","securitycraft:sc_manual","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","mcwfences:mangrove_horse_fence","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","twilightforest:wood/canopy_fence","mcwfences:end_brick_grass_topped_wall","mcwfences:railing_red_sandstone_wall","corail_woodcutter:acacia_woodcutter","mcwbiomesoplenty:jacaranda_stockade_fence","mcwfences:guardian_metal_fence","cfm:jungle_kitchen_sink_light","simplylight:illuminant_black_block_dyed","mcwpaths:dirt_path_block","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","cfm:oak_kitchen_sink_light","aquaculture:gold_nugget_from_smelting","mcwfences:jungle_curved_gate","supplementaries:clock_block","cfm:black_kitchen_sink","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","biomesoplenty:mahogany_boat","mcwfences:acacia_pyramid_gate","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","solcarrot:food_book","mcwroofs:grass_top_roof","cfm:stripped_dark_oak_kitchen_sink_light","twilightforest:reappearing_block","utilitarian:utility/charcoal_from_campfire","twilightforest:wood/canopy_slab","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwfences:twilight_metal_fence","aquaculture:double_hook","mcwfences:curved_metal_fence","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:willow_stockade_fence","cfm:purple_kitchen_counter","mcwbiomesoplenty:hellbark_hedge","ae2:network/wireless_terminal","mcwfences:nether_brick_grass_topped_wall","sophisticatedstorage:generic_barrel","mcwbiomesoplenty:willow_pyramid_gate","cfm:pink_kitchen_counter","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","aquaculture:tin_can_to_iron_nugget","cfm:lime_kitchen_sink","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","simplylight:illuminant_orange_block_on_toggle","aquaculture:iron_fishing_rod","cfm:black_kitchen_counter","mcwfences:deepslate_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","twilightforest:wood/mangrove_slab","twilightforest:time_boat","simplylight:illuminant_light_blue_block_toggle","cfm:blue_kitchen_drawer","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","cfm:light_blue_picket_fence","domum_ornamentum:blockbarreldeco_standing","mcwfences:spruce_wired_fence","dyenamics:bed/navy_bed","ae2wtlib:magnet_card","mcwfences:gothic_metal_fence","mcwfences:oak_pyramid_gate","deeperdarker:echo_boat","supplementaries:jar","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","cfm:pink_kitchen_sink","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_stockade_fence","mcwfences:deepslate_railing_gate","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwfences:birch_picket_fence","mcwfences:double_curved_metal_fence","mcwfences:deepslate_pillar_wall","mcwbiomesoplenty:magic_wired_fence","biomesoplenty:jacaranda_boat","mcwbiomesoplenty:empyreal_wired_fence","supplementaries:twilightforest/sign_post_twilight_oak","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","domum_ornamentum:roan_bricks","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","mcwfences:stone_grass_topped_wall","mcwfences:oak_picket_fence","mcwfences:mud_brick_grass_topped_wall","cfm:blue_kitchen_sink","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","botania:petal_purple","botania:petal_pink","mcwfences:modern_andesite_wall","biomesoplenty:pine_boat","handcrafted:wood_bowl","twilightforest:naga_banner_pattern","cfm:magenta_kitchen_sink","mcwfences:prismarine_grass_topped_wall","ae2:network/cells/view_cell","aquaculture:tin_can_to_iron_nugget_from_blasting","twilightforest:snow_queen_banner_pattern","mcwbiomesoplenty:jacaranda_wired_fence","utilitix:advanced_brewery","dyenamics:bed/mint_bed","mcwfences:bamboo_horse_fence","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:jacaranda_horse_fence","cfm:stripped_oak_kitchen_sink_light","supplementaries:cage","cfm:spruce_kitchen_sink_light","twilightforest:lich_banner_pattern","mcwbiomesoplenty:magic_highley_gate","twilightforest:ur_ghast_banner_pattern","mcwfences:diorite_grass_topped_wall","mcwbiomesoplenty:magic_picket_fence","simplylight:illuminant_green_block_on_toggle","twilightforest:wood/mangrove_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","botania:purple_shiny_flower","aquaculture:spruce_fish_mount","cfm:light_gray_kitchen_counter","twilightforest:dark_boat","twilightforest:knight_phantom_banner_pattern","cfm:yellow_kitchen_drawer","simplylight:illuminant_purple_block_on_dyed","biomesoplenty:umbran_boat","framedblocks:framed_cube","aquaculture:redstone_hook","twilightforest:wood/canopy_planks","allthetweaks:atm_star_from_atmstar_block","mcwbiomesoplenty:flowering_oak_hedge","twilightforest:vanishing_block","twilightforest:wood/time_planks","utilitarian:utility/logs_to_bowls","twilightforest:alpha_yeti_banner_pattern","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","twilightforest:wood/canopy_door","ae2:network/blocks/storage_chest","mcwbiomesoplenty:pine_hedge","simplylight:bulb","simplylight:illuminant_green_block_dyed","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","simplylight:illuminant_magenta_block_toggle","mcwfences:oak_curved_gate","mcwbiomesoplenty:mahogany_horse_fence","twilightforest:wood/mangrove_gate","aquaculture:bobber","cfm:gray_kitchen_drawer","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfences:nether_brick_pillar_wall","cfm:stripped_mangrove_kitchen_sink_light","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","simplylight:illuminant_brown_block_dyed","supplementaries:item_shelf","biomesoplenty:palm_boat","simplylight:illuminant_brown_block_on_dyed","twilightforest:wood/time_wood","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","handcrafted:bear_trophy","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","cfm:purple_kitchen_sink","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","mcwroofs:grass_upper_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","pylons:interdiction_pylon","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","aether:skyroot_barrel","minecraft:dark_oak_boat","mcwbiomesoplenty:maple_curved_gate","aquaculture:gold_hook","twilightforest:wood/mangrove_plate","mcwbiomesoplenty:palm_wired_fence","simplylight:illuminant_yellow_block_toggle","supplementaries:turn_table","handcrafted:wither_skeleton_trophy","minecraft:bamboo_raft","twilightforest:sorting_boat","cfm:green_kitchen_sink","supplementaries:bed_from_feather_block","minecraft:smooth_stone_slab","twilightforest:wood/twilight_oak_door","mcwfences:diorite_railing_gate","mcwbiomesoplenty:hellbark_stockade_fence","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","cfm:red_kitchen_counter","simplylight:illuminant_pink_block_on_toggle","twilightforest:canopy_bookshelf","simplylight:illuminant_panel","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","twilightforest:wood/canopy_stairs","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:willow_hedge","mcwbiomesoplenty:empyreal_stockade_fence","twilightforest:wood/canopy_chest","aquaculture:oak_fish_mount","cfm:orange_kitchen_drawer","twilightdelight:mining_cabinet","twilightforest:wood/mangrove_button","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","allthecompressed:compress/atm_star_block_1x","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","minecraft:cherry_boat","minecraft:stick","simplylight:illuminant_light_gray_block_on_dyed","mcwfences:red_sandstone_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","minecraft:netherite_ingot_from_netherite_block","twilightforest:wood/twilight_oak_plate","mcwbiomesoplenty:redwood_pyramid_gate","mcwfences:andesite_grass_topped_wall","mcwbiomesoplenty:empyreal_highley_gate","mcwfences:railing_prismarine_wall","cfm:orange_kitchen_counter","cfm:white_kitchen_drawer","supplementaries:bamboo_spikes","dyenamics:bed/persimmon_bed","mcwbiomesoplenty:hellbark_wired_fence","forbidden_arcanus:wooden_blacksmith_gavel","twilightforest:wood/canopy_plate","mcwbiomesoplenty:pine_horse_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:warped_wired_fence","mcwfences:bamboo_fence","sophisticatedstorage:generic_chest","handcrafted:silverfish_trophy","cfm:magenta_kitchen_counter","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","handcrafted:wood_plate","mcwfences:jungle_stockade_fence","cfm:yellow_kitchen_counter","minecraft:barrel","dyenamics:bed/rose_bed","dyenamics:bed/lavender_bed","twilightforest:wood/canopy_gate","mcwfences:end_brick_railing_gate","cfm:lime_kitchen_counter","ae2:network/wireless_crafting_terminal","botania:petal_black","simplylight:illuminant_cyan_block_dyed","twilightforest:empty_canopy_bookshelf","aquaculture:golden_fishing_rod","simplylight:illuminant_pink_block_dyed","mcwfences:oak_highley_gate","simplylight:illuminant_light_gray_block_toggle","mcwfences:diorite_pillar_wall","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","cfm:yellow_kitchen_sink","mcwfences:bamboo_picket_fence","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","undergarden:wigglewood_boat","mcwbiomesoplenty:fir_hedge","additionallanterns:smooth_stone_chain","handcrafted:goat_trophy","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","mcwfences:birch_highley_gate","mcwfences:railing_diorite_wall","biomesoplenty:magic_boat","cfm:stripped_crimson_kitchen_sink_light","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwpaths:gravel_path_block","simplylight:illuminant_lime_block_on_dyed","simplylight:illuminant_orange_block_on_dyed","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbiomesoplenty:mahogany_highley_gate","cfm:dark_oak_kitchen_sink_light","mcwfences:bamboo_pyramid_gate","mcwfences:warped_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","mcwpaths:red_sand_path_block","aether:wooden_axe_repairing","domum_ornamentum:architectscutter","twilightforest:wood/twilight_oak_slab","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","twilightforest:wood/twilight_oak_chest","twilightdelight:canopy_cabinet","supplementaries:blackboard","twilightforest:wood/mangrove_door","mcwfences:jungle_picket_fence","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_birch_kitchen_sink_light","cfm:gray_kitchen_counter","simplylight:illuminant_lime_block_on_toggle","mcwfences:acacia_picket_fence","supplementaries:flower_box","mcwroofs:grass_roof","cfm:pink_kitchen_drawer","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","cfm:cyan_kitchen_sink","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","minecraft:composter","simplylight:illuminant_purple_block_toggle","supplementaries:soap/map","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:snowblossom_hedge","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","mcwfences:railing_blackstone_wall","minecraft:oak_boat","twilightforest:hydra_banner_pattern","mcwfences:dark_oak_highley_gate","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwbiomesoplenty:palm_highley_gate","mcwbiomesoplenty:dead_wired_fence","simplylight:illuminant_red_block_on_toggle","minecraft:chest","supplementaries:pulley","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","cfm:birch_kitchen_sink_light","aquaculture:light_hook","aquaculture:jungle_fish_mount","dyenamics:bed/icy_blue_bed","simplylight:illuminant_blue_block_on_toggle","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","aquaculture:gold_nugget_from_gold_fish","mcwfences:modern_sandstone_wall","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbiomesoplenty:redwood_horse_fence","twilightforest:wood/canopy_banister","mcwbiomesoplenty:dead_curved_gate","twilightforest:wood/twilight_oak_button","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:magic_curved_gate","biomesoplenty:dead_boat","mcwfences:mesh_metal_fence","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","cfm:green_kitchen_drawer","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:pine_curved_gate","mcwfences:mangrove_stockade_fence","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","cfm:stripped_spruce_kitchen_sink_light","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","simplylight:illuminant_lime_block_dyed","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:bed/cherenkov_bed","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","cfm:purple_kitchen_drawer","botania:flower_bag","simplylight:edge_light","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","mcwroofs:grass_steep_roof","botania:black_shiny_flower","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","twilightforest:wood/twilight_oak_gate","simplylight:illuminant_block_dyed","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:sandstone_pillar_wall","mcwfences:prismarine_pillar_wall","mcwbiomesoplenty:orange_maple_hedge","aether:skyroot_boat","handcrafted:fox_trophy","simplylight:illuminant_black_block_on_toggle","cfm:magenta_kitchen_drawer","supplementaries:slice_map","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwbiomesoplenty:maple_horse_fence","twilightforest:quest_ram_banner_pattern","handcrafted:blaze_trophy","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","mcwfences:stone_pillar_wall","simplylight:illuminant_gray_block_toggle","mcwfences:spruce_stockade_fence","cfm:cyan_kitchen_counter","mcwbiomesoplenty:empyreal_pyramid_gate","simplylight:illuminant_green_block_toggle","mcwbiomesoplenty:red_maple_hedge","blue_skies:trough","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwfences:end_brick_pillar_wall","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","mcwbiomesoplenty:palm_hedge","mcwfences:railing_mud_brick_wall","handcrafted:wood_cup","eidolon:smooth_stone_masonry_stonecutter_0","mcwbiomesoplenty:empyreal_curved_gate","twilightforest:mining_boat","cfm:green_kitchen_counter","corail_woodcutter:jungle_woodcutter","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","twilightforest:wood/canopy_sign","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","twilightforest:twilight_oak_boat","mcwfences:dark_oak_horse_fence","mcwfences:oak_hedge","cfm:warped_kitchen_sink_light","mcwfences:railing_sandstone_wall","deeperdarker:bloom_boat","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:willow_wired_fence","mcwbiomesoplenty:dead_picket_fence","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwbiomesoplenty:magic_hedge","dyenamics:bed/wine_bed","handcrafted:skeleton_horse_trophy","mcwbiomesoplenty:hellbark_highley_gate","mcwfences:bastion_metal_fence","ae2:network/parts/terminals_crafting","mcwfences:spruce_picket_fence","mcwfences:spruce_curved_gate","mcwbiomesoplenty:hellbark_curved_gate","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","minecraft:spruce_boat","cfm:light_blue_kitchen_sink","cfm:gray_kitchen_sink","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwbiomesoplenty:maple_picket_fence","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","cfm:white_kitchen_sink","mcwfences:modern_end_brick_wall","twilightforest:wood/mangrove_chest","twilightforest:wood/twilight_oak_fence","mcwbiomesoplenty:maple_highley_gate","twigs:smooth_stone_bricks","twilightforest:minoshroom_banner_pattern","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwfences:deepslate_brick_railing_gate","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","botania:pink_shiny_flower","mcwbiomesoplenty:magic_stockade_fence","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwbiomesoplenty:umbran_picket_fence","mcwfences:acacia_horse_fence","cfm:stripped_jungle_kitchen_sink_light","handcrafted:salmon_trophy","supplementaries:twilightforest/sign_post_mangrove","mcwroofs:grass_upper_lower_roof","mcwfences:spruce_hedge","supplementaries:speaker_block","allthecompressed:compress/netherite_block_1x","cfm:white_kitchen_counter","mcwfences:modern_blackstone_wall","cfm:light_blue_kitchen_counter","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge"],toBeDisplayed:["mcwbiomesoplenty:magic_horse_fence","corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","minecraft:charcoal","naturescompass:natures_compass","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","cfm:white_picket_fence","mcwbiomesoplenty:dead_pyramid_gate","supplementaries:bellows","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","mcwbiomesoplenty:redwood_highley_gate","handcrafted:spider_trophy","aquaculture:cooked_fish_fillet_from_smoking","cfm:red_kitchen_drawer","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","simplylight:illuminant_block_on_toggle","cfm:red_kitchen_sink","dyenamics:bed/spring_green_bed","minecraft:spruce_planks","ae2:network/parts/terminals_pattern_encoding","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","aquaculture:cooked_fish_fillet","cfm:light_gray_kitchen_sink","cfm:orange_kitchen_sink","twilightforest:wood/canopy_trapdoor","mcwfences:mangrove_picket_fence","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","aquaculture:planks_from_driftwood","twilightforest:wood/mangrove_fence","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwbiomesoplenty:umbran_horse_fence","mcwfences:birch_stockade_fence","mcwfences:dark_oak_stockade_fence","twilightforest:wood/twilight_oak_trapdoor","simplylight:illuminant_cyan_block_on_toggle","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","mcwfences:bamboo_wired_fence","twilightforest:wood/twilight_oak_sign","mcwbiomesoplenty:hellbark_picket_fence","mcwfences:crimson_curved_gate","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","ae2:network/crafting/patterns_blank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","utilitarian:snad/drit","corail_woodcutter:bamboo_woodcutter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","aquaculture:jellyfish_to_slimeball","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","cfm:brown_kitchen_counter","simplylight:illuminant_lime_block_toggle","mcwfences:panelled_metal_fence","twilightforest:wood/mangrove_stairs","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:andesite_pillar_wall","mcwfences:curved_metal_fence_gate","minecraft:blast_furnace","mcwfences:acacia_hedge","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwfences:majestic_metal_fence_gate","domum_ornamentum:blockbarreldeco_onside","energymeter:meter","simplylight:rodlamp","biomesoplenty:maple_boat","sliceanddice:slicer","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","twilightforest:wood/canopy_button","mcwfences:granite_grass_topped_wall","cfm:brown_kitchen_sink","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","aquaculture:iron_nugget_from_smelting","aquaculture:sushi","silentgear:upgrade_base","mcwbiomesoplenty:rainbow_birch_hedge","handcrafted:pufferfish_trophy","cfm:white_picket_gate","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:flowering_azalea_hedge","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","cfm:light_blue_picket_gate","mcwfences:expanded_mesh_metal_fence","cfm:mangrove_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfences:acacia_stockade_fence","mcwfences:crimson_picket_fence","dyenamics:bed/peach_bed","aquaculture:birch_fish_mount","mcwfences:blackstone_pillar_wall","botania:petal_light_blue_double","aquaculture:gold_nugget_from_blasting","mcwbiomesoplenty:fir_curved_gate","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwfences:warped_horse_fence","mcwbiomesoplenty:mahogany_curved_gate","mcwbiomesoplenty:maple_stockade_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","supplementaries:twilightforest/sign_post_canopy","simplylight:illuminant_red_block_toggle","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","twilightforest:wood/mangrove_sign","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","twilightforest:wood/twilight_oak_stairs","twilightforest:wood/mining_banister","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","allthecompressed:compress/grass_block_1x","cfm:blue_kitchen_counter","mcwfences:jungle_highley_gate","mcwbiomesoplenty:pine_highley_gate","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfences:bamboo_curved_gate","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","dyenamics:bed/maroon_bed","mcwfences:acacia_highley_gate","mcwfences:dark_oak_hedge","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:warped_picket_fence","cfm:black_kitchen_drawer","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","mcwfences:bamboo_highley_gate","mcwfences:modern_nether_brick_wall","securitycraft:sc_manual","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","mcwfences:mangrove_horse_fence","mcwfences:sandstone_railing_gate","mcwfences:dark_oak_pyramid_gate","twilightforest:wood/canopy_fence","mcwfences:end_brick_grass_topped_wall","mcwfences:railing_red_sandstone_wall","corail_woodcutter:acacia_woodcutter","mcwbiomesoplenty:jacaranda_stockade_fence","mcwfences:guardian_metal_fence","cfm:jungle_kitchen_sink_light","simplylight:illuminant_black_block_dyed","mcwpaths:dirt_path_block","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","cfm:oak_kitchen_sink_light","aquaculture:gold_nugget_from_smelting","mcwfences:jungle_curved_gate","supplementaries:clock_block","cfm:black_kitchen_sink","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","biomesoplenty:mahogany_boat","mcwfences:acacia_pyramid_gate","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","solcarrot:food_book","mcwroofs:grass_top_roof","cfm:stripped_dark_oak_kitchen_sink_light","twilightforest:reappearing_block","utilitarian:utility/charcoal_from_campfire","twilightforest:wood/canopy_slab","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwfences:twilight_metal_fence","aquaculture:double_hook","mcwfences:curved_metal_fence","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:willow_stockade_fence","cfm:purple_kitchen_counter","mcwbiomesoplenty:hellbark_hedge","ae2:network/wireless_terminal","mcwfences:nether_brick_grass_topped_wall","sophisticatedstorage:generic_barrel","mcwbiomesoplenty:willow_pyramid_gate","cfm:pink_kitchen_counter","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","aquaculture:tin_can_to_iron_nugget","cfm:lime_kitchen_sink","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","simplylight:illuminant_orange_block_on_toggle","aquaculture:iron_fishing_rod","cfm:black_kitchen_counter","mcwfences:deepslate_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","twilightforest:wood/mangrove_slab","twilightforest:time_boat","simplylight:illuminant_light_blue_block_toggle","cfm:blue_kitchen_drawer","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","cfm:light_blue_picket_fence","domum_ornamentum:blockbarreldeco_standing","mcwfences:spruce_wired_fence","dyenamics:bed/navy_bed","ae2wtlib:magnet_card","mcwfences:gothic_metal_fence","mcwfences:oak_pyramid_gate","deeperdarker:echo_boat","supplementaries:jar","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","cfm:pink_kitchen_sink","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_stockade_fence","mcwfences:deepslate_railing_gate","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","mcwfences:birch_picket_fence","mcwfences:double_curved_metal_fence","mcwfences:deepslate_pillar_wall","mcwbiomesoplenty:magic_wired_fence","biomesoplenty:jacaranda_boat","mcwbiomesoplenty:empyreal_wired_fence","supplementaries:twilightforest/sign_post_twilight_oak","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","domum_ornamentum:roan_bricks","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","mcwfences:stone_grass_topped_wall","mcwfences:oak_picket_fence","mcwfences:mud_brick_grass_topped_wall","cfm:blue_kitchen_sink","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","botania:petal_purple","botania:petal_pink","mcwfences:modern_andesite_wall","biomesoplenty:pine_boat","handcrafted:wood_bowl","twilightforest:naga_banner_pattern","cfm:magenta_kitchen_sink","mcwfences:prismarine_grass_topped_wall","ae2:network/cells/view_cell","aquaculture:tin_can_to_iron_nugget_from_blasting","twilightforest:snow_queen_banner_pattern","mcwbiomesoplenty:jacaranda_wired_fence","utilitix:advanced_brewery","dyenamics:bed/mint_bed","mcwfences:bamboo_horse_fence","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:jacaranda_horse_fence","cfm:stripped_oak_kitchen_sink_light","supplementaries:cage","cfm:spruce_kitchen_sink_light","twilightforest:lich_banner_pattern","mcwbiomesoplenty:magic_highley_gate","twilightforest:ur_ghast_banner_pattern","mcwfences:diorite_grass_topped_wall","mcwbiomesoplenty:magic_picket_fence","simplylight:illuminant_green_block_on_toggle","twilightforest:wood/mangrove_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","botania:purple_shiny_flower","aquaculture:spruce_fish_mount","cfm:light_gray_kitchen_counter","twilightforest:dark_boat","twilightforest:knight_phantom_banner_pattern","cfm:yellow_kitchen_drawer","simplylight:illuminant_purple_block_on_dyed","biomesoplenty:umbran_boat","framedblocks:framed_cube","aquaculture:redstone_hook","twilightforest:wood/canopy_planks","allthetweaks:atm_star_from_atmstar_block","mcwbiomesoplenty:flowering_oak_hedge","twilightforest:vanishing_block","twilightforest:wood/time_planks","utilitarian:utility/logs_to_bowls","twilightforest:alpha_yeti_banner_pattern","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","twilightforest:wood/canopy_door","ae2:network/blocks/storage_chest","mcwbiomesoplenty:pine_hedge","simplylight:bulb","simplylight:illuminant_green_block_dyed","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","simplylight:illuminant_magenta_block_toggle","mcwfences:oak_curved_gate","mcwbiomesoplenty:mahogany_horse_fence","twilightforest:wood/mangrove_gate","aquaculture:bobber","cfm:gray_kitchen_drawer","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfences:nether_brick_pillar_wall","cfm:stripped_mangrove_kitchen_sink_light","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","simplylight:illuminant_brown_block_dyed","supplementaries:item_shelf","biomesoplenty:palm_boat","simplylight:illuminant_brown_block_on_dyed","twilightforest:wood/time_wood","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","handcrafted:bear_trophy","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","cfm:purple_kitchen_sink","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","mcwroofs:grass_upper_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","pylons:interdiction_pylon","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","aether:skyroot_barrel","minecraft:dark_oak_boat","mcwbiomesoplenty:maple_curved_gate","aquaculture:gold_hook","twilightforest:wood/mangrove_plate","mcwbiomesoplenty:palm_wired_fence","simplylight:illuminant_yellow_block_toggle","supplementaries:turn_table","handcrafted:wither_skeleton_trophy","minecraft:bamboo_raft","twilightforest:sorting_boat","cfm:green_kitchen_sink","supplementaries:bed_from_feather_block","minecraft:smooth_stone_slab","twilightforest:wood/twilight_oak_door","mcwfences:diorite_railing_gate","mcwbiomesoplenty:hellbark_stockade_fence","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","cfm:red_kitchen_counter","simplylight:illuminant_pink_block_on_toggle","twilightforest:canopy_bookshelf","simplylight:illuminant_panel","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","twilightforest:wood/canopy_stairs","mcwpaths:podzol_path_block","mcwbiomesoplenty:cypress_hedge","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:willow_hedge","mcwbiomesoplenty:empyreal_stockade_fence","twilightforest:wood/canopy_chest","aquaculture:oak_fish_mount","cfm:orange_kitchen_drawer","twilightdelight:mining_cabinet","twilightforest:wood/mangrove_button","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","allthecompressed:compress/atm_star_block_1x","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","minecraft:cherry_boat","minecraft:stick","simplylight:illuminant_light_gray_block_on_dyed","mcwfences:red_sandstone_grass_topped_wall","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","minecraft:netherite_ingot_from_netherite_block","twilightforest:wood/twilight_oak_plate","mcwbiomesoplenty:redwood_pyramid_gate","mcwfences:andesite_grass_topped_wall","mcwbiomesoplenty:empyreal_highley_gate","mcwfences:railing_prismarine_wall","cfm:orange_kitchen_counter","cfm:white_kitchen_drawer","supplementaries:bamboo_spikes","dyenamics:bed/persimmon_bed","mcwbiomesoplenty:hellbark_wired_fence","forbidden_arcanus:wooden_blacksmith_gavel","twilightforest:wood/canopy_plate","mcwbiomesoplenty:pine_horse_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:warped_wired_fence","mcwfences:bamboo_fence","sophisticatedstorage:generic_chest","handcrafted:silverfish_trophy","cfm:magenta_kitchen_counter","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","handcrafted:wood_plate","mcwfences:jungle_stockade_fence","cfm:yellow_kitchen_counter","minecraft:barrel","dyenamics:bed/rose_bed","dyenamics:bed/lavender_bed","twilightforest:wood/canopy_gate","mcwfences:end_brick_railing_gate","cfm:lime_kitchen_counter","ae2:network/wireless_crafting_terminal","botania:petal_black","simplylight:illuminant_cyan_block_dyed","twilightforest:empty_canopy_bookshelf","aquaculture:golden_fishing_rod","simplylight:illuminant_pink_block_dyed","mcwfences:oak_highley_gate","simplylight:illuminant_light_gray_block_toggle","mcwfences:diorite_pillar_wall","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","cfm:yellow_kitchen_sink","mcwfences:bamboo_picket_fence","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","undergarden:wigglewood_boat","mcwbiomesoplenty:fir_hedge","additionallanterns:smooth_stone_chain","handcrafted:goat_trophy","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","mcwfences:birch_highley_gate","mcwfences:railing_diorite_wall","biomesoplenty:magic_boat","cfm:stripped_crimson_kitchen_sink_light","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwpaths:gravel_path_block","simplylight:illuminant_lime_block_on_dyed","simplylight:illuminant_orange_block_on_dyed","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","mcwbiomesoplenty:mahogany_highley_gate","cfm:dark_oak_kitchen_sink_light","mcwfences:bamboo_pyramid_gate","mcwfences:warped_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","mcwpaths:red_sand_path_block","aether:wooden_axe_repairing","domum_ornamentum:architectscutter","twilightforest:wood/twilight_oak_slab","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","twilightforest:wood/twilight_oak_chest","twilightdelight:canopy_cabinet","supplementaries:blackboard","twilightforest:wood/mangrove_door","mcwfences:jungle_picket_fence","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_birch_kitchen_sink_light","cfm:gray_kitchen_counter","simplylight:illuminant_lime_block_on_toggle","mcwfences:acacia_picket_fence","supplementaries:flower_box","mcwroofs:grass_roof","cfm:pink_kitchen_drawer","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","cfm:cyan_kitchen_sink","mcwfences:mud_brick_railing_gate","mcwfences:birch_curved_gate","minecraft:composter","simplylight:illuminant_purple_block_toggle","supplementaries:soap/map","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:snowblossom_hedge","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","mcwfences:railing_blackstone_wall","minecraft:oak_boat","twilightforest:hydra_banner_pattern","mcwfences:dark_oak_highley_gate","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwbiomesoplenty:palm_highley_gate","mcwbiomesoplenty:dead_wired_fence","simplylight:illuminant_red_block_on_toggle","minecraft:chest","supplementaries:pulley","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","cfm:birch_kitchen_sink_light","aquaculture:light_hook","aquaculture:jungle_fish_mount","dyenamics:bed/icy_blue_bed","simplylight:illuminant_blue_block_on_toggle","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","aquaculture:gold_nugget_from_gold_fish","mcwfences:modern_sandstone_wall","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","mcwbiomesoplenty:redwood_horse_fence","twilightforest:wood/canopy_banister","mcwbiomesoplenty:dead_curved_gate","twilightforest:wood/twilight_oak_button","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:magic_curved_gate","biomesoplenty:dead_boat","mcwfences:mesh_metal_fence","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","cfm:green_kitchen_drawer","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:pine_curved_gate","mcwfences:mangrove_stockade_fence","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","cfm:stripped_spruce_kitchen_sink_light","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","simplylight:illuminant_lime_block_dyed","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","dyenamics:bed/cherenkov_bed","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","cfm:purple_kitchen_drawer","botania:flower_bag","simplylight:edge_light","mcwfences:railing_granite_wall","simplylight:illuminant_yellow_block_dyed","mcwroofs:grass_steep_roof","botania:black_shiny_flower","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","twilightforest:wood/twilight_oak_gate","simplylight:illuminant_block_dyed","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:sandstone_pillar_wall","mcwfences:prismarine_pillar_wall","mcwbiomesoplenty:orange_maple_hedge","aether:skyroot_boat","handcrafted:fox_trophy","simplylight:illuminant_black_block_on_toggle","cfm:magenta_kitchen_drawer","supplementaries:slice_map","mcwbiomesoplenty:dead_stockade_fence","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwbiomesoplenty:maple_horse_fence","twilightforest:quest_ram_banner_pattern","handcrafted:blaze_trophy","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","mcwfences:stone_pillar_wall","simplylight:illuminant_gray_block_toggle","mcwfences:spruce_stockade_fence","cfm:cyan_kitchen_counter","mcwbiomesoplenty:empyreal_pyramid_gate","simplylight:illuminant_green_block_toggle","mcwbiomesoplenty:red_maple_hedge","blue_skies:trough","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwfences:end_brick_pillar_wall","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","mcwbiomesoplenty:palm_hedge","mcwfences:railing_mud_brick_wall","handcrafted:wood_cup","eidolon:smooth_stone_masonry_stonecutter_0","mcwbiomesoplenty:empyreal_curved_gate","twilightforest:mining_boat","cfm:green_kitchen_counter","corail_woodcutter:jungle_woodcutter","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","twilightforest:wood/canopy_sign","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","twilightforest:twilight_oak_boat","mcwfences:dark_oak_horse_fence","mcwfences:oak_hedge","cfm:warped_kitchen_sink_light","mcwfences:railing_sandstone_wall","deeperdarker:bloom_boat","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:willow_wired_fence","mcwbiomesoplenty:dead_picket_fence","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwbiomesoplenty:magic_hedge","dyenamics:bed/wine_bed","handcrafted:skeleton_horse_trophy","mcwbiomesoplenty:hellbark_highley_gate","mcwfences:bastion_metal_fence","ae2:network/parts/terminals_crafting","mcwfences:spruce_picket_fence","mcwfences:spruce_curved_gate","mcwbiomesoplenty:hellbark_curved_gate","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","minecraft:spruce_boat","cfm:light_blue_kitchen_sink","cfm:gray_kitchen_sink","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwbiomesoplenty:maple_picket_fence","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","cfm:white_kitchen_sink","mcwfences:modern_end_brick_wall","twilightforest:wood/mangrove_chest","twilightforest:wood/twilight_oak_fence","mcwbiomesoplenty:maple_highley_gate","twigs:smooth_stone_bricks","twilightforest:minoshroom_banner_pattern","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwfences:deepslate_brick_railing_gate","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","botania:pink_shiny_flower","mcwbiomesoplenty:magic_stockade_fence","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwbiomesoplenty:umbran_picket_fence","mcwfences:acacia_horse_fence","cfm:stripped_jungle_kitchen_sink_light","handcrafted:salmon_trophy","supplementaries:twilightforest/sign_post_mangrove","mcwroofs:grass_upper_lower_roof","mcwfences:spruce_hedge","supplementaries:speaker_block","allthecompressed:compress/netherite_block_1x","cfm:white_kitchen_counter","mcwfences:modern_blackstone_wall","cfm:light_blue_kitchen_counter","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:6389,warning_level:0}}