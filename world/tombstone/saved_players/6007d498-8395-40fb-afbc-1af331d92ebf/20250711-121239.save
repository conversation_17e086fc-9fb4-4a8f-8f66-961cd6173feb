{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:30096,Id:46,ShowIcon:1b,ShowParticles:1b,"forge:id":"attributeslib:flying"}],Air:300s,Attributes:[{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:20.0d,Modifiers:[{Amount:24.0d,Name:"Heart Containers",Operation:0,UUID:[I;-**********,-990297069,-**********,-847674717]},{Amount:8.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:the_beyond",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:cross_necklace",tag:{CanApplyBonus:1b}},{Count:1b,Slot:1,id:"artifacts:running_shoes"}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:villager_hat"}],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[{Count:1b,Slot:0,id:"irons_spellbooks:emerald_stoneplate_ring"}],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:iron_backpack",tag:{contentsUuid:[I;183773063,1676100957,-1954879741,2135773033],inventorySlots:54,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:2}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:kitty_slippers"}],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:76216},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:28},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_LAST_ARROW:[I;-2058715453,1116292062,-1756772525,61742329],MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:12,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions",ItemStack:{Count:4b,id:"minecraft:chest"}}],SelectedRecipe:"quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:105,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["minecraft:carrot","minecraft:baked_potato","gtceu:purple_drink","nethersdelight:hoglin_loin","minecraft:cooked_beef","minecraft:glow_berries","maidensmerrymaking:chocolate_bunny","delightful:pumpkin_pie_slice","croptopia:peach","minecraft:bread","minecraft:golden_apple","minecraft:cooked_cod","minecraft:chorus_fruit","minecraft:mushroom_stew","minecraft:cookie","minecraft:apple","minecraft:suspicious_stew","minecraft:chicken","silentgear:nether_banana","minecraft:rotten_flesh"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:17,perks:[{id:0s,level:4b}]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:2b,PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:overworld",FromPos:-6047314239421L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:274877898813L,UID:[I;-20974842,-78295280,-1975580082,-*********]},{FromDim:"minecraft:the_nether",FromPos:549755805757L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-170699181088807L,UID:[I;-*********,-1736881286,-1130302985,-1133054004]},{FromDim:"minecraft:the_nether",FromPos:203684529430628L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-1504039232,1774078639,-1352384613,-1094610412]},{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;737594774,70206902,-1942881838,-818557955]},{FromDim:"minecraft:overworld",FromPos:96207268921409L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;538329221,-204062309,-1949687609,2117983625]},{FromDim:"minecraft:overworld",FromPos:-223475740831582L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-29137058447294L,UID:[I;-856567548,1566852222,-1168863315,1186696347]},{FromDim:"minecraft:the_nether",FromPos:-29137058443198L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-170699181088807L,UID:[I;-1003941690,1468092373,-1415468434,172218467]},{FromDim:"minecraft:overworld",FromPos:-169599669465128L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:-29137058447294L,UID:[I;-1678221233,372983691,-1806177896,-6088762]}],TrashSlot:{Count:3b,id:"minecraft:moss_carpet"},WaystonesData:{Waystones:["ba59ed03-fc91-4f2d-9d54-fa7014c385fa","88320f8d-ea59-4cc6-896d-79cc52d4aa80","21dcb5f4-0da3-4518-b4a0-aa66d6124f0c","79be4d10-254c-4336-9921-b1a4f2b80373"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:1692,tb_last_ground_location_y:-27,tb_last_ground_location_z:1539,tb_last_offhand_item:"mekanismtools:refined_obsidian_shield",twilightforest_banished:1b},apoth_reforge_seed:0,"quark:trying_crawl":0b,sophisticatedBackpackSettings:{}},Health:56.0f,HurtByTimestamp:34212,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"minecraft:brewing_stand"},{Count:1b,Slot:1b,id:"pylons:infusion_pylon"},{Count:3b,Slot:2b,id:"minecraft:cobbled_deepslate"},{Count:4b,Slot:7b,id:"minecraft:bread"},{Count:1b,Slot:8b,id:"mekanismgenerators:heat_generator",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"160000"}],FluidTanks:[],HeatCapacitors:[{Container:0b,heatCapacity:10.0d,stored:2950.185790381246d}],Items:[],componentUpgrade:{Items:[],upgrades:[]},controlType:0,owner:[I;**********,-**********,-**********,836316863],securityMode:0}}},{Count:10b,Slot:9b,id:"productivetrees:sawdust"},{Count:64b,Slot:10b,id:"minecraft:oak_planks"},{Count:1b,Slot:11b,id:"mekanismgenerators:heat_generator",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"160000"}],FluidTanks:[],HeatCapacitors:[{Container:0b,heatCapacity:10.0d,stored:3111.92315431008d}],Items:[],componentUpgrade:{Items:[],upgrades:[]},controlType:0,owner:[I;**********,-**********,-**********,836316863],securityMode:0}}},{Count:8b,Slot:12b,id:"mekanism:basic_universal_cable"},{Count:1b,Slot:13b,id:"alltheores:raw_nickel"},{Count:1b,Slot:14b,id:"mekanismgenerators:heat_generator",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"160000"}],FluidTanks:[],HeatCapacitors:[{Container:0b,heatCapacity:10.0d,stored:3024.3157507620563d}],Items:[],componentUpgrade:{Items:[],upgrades:[]},controlType:0,owner:[I;**********,-**********,-**********,836316863],securityMode:0}}},{Count:1b,Slot:15b,id:"mekanism:metallurgic_infuser",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"20000"}],InfusionTanks:[],Items:[],componentConfig:{config0:{side0:1,side1:1,side2:1,side3:1,side4:1,side5:1},config3:{side0:1,side1:1,side2:4,side3:1,side4:1,side5:1},config6:{side0:1,side1:1,side2:4,side3:8,side4:1,side5:9},eject0:0b,eject3:0b,eject6:0b},componentEjector:{color0:-1,color1:-1,color2:-1,color3:-1,color4:-1,color5:-1,strictInput:0b},componentUpgrade:{Items:[],upgrades:[]},controlType:0,owner:[I;**********,-**********,-**********,836316863],securityMode:0}}},{Count:1b,Slot:16b,id:"mekanism:enrichment_chamber",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"20000"}],Items:[],componentConfig:{config0:{side0:1,side1:1,side2:1,side3:1,side4:1,side5:1},config6:{side0:1,side1:1,side2:4,side3:8,side4:1,side5:1},eject0:0b,eject6:0b},componentEjector:{color0:-1,color1:-1,color2:-1,color3:-1,color4:-1,color5:-1,strictInput:0b},componentUpgrade:{Items:[],upgrades:[]},controlType:0,owner:[I;**********,-**********,-**********,836316863],securityMode:0}}},{Count:3b,Slot:17b,id:"minecraft:terracotta"},{Count:1b,Slot:18b,id:"ironfurnaces:augment_factory"},{Count:1b,Slot:19b,id:"ironfurnaces:diamond_furnace",tag:{BlockEntityTag:{Augment:[I;0,0,1],Jovial:0}}},{Count:1b,Slot:20b,id:"mekanism:osmium_compressor",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"80000"}],GasTanks:[{Tank:0b,stored:{amount:200L,gasName:"mekanism:osmium"}}],Items:[{Item:{Count:23b,id:"alltheores:osmium_ingot"},Slot:1b}],componentConfig:{config0:{side0:1,side1:1,side2:1,side3:1,side4:1,side5:1},config2:{side0:1,side1:1,side2:4,side3:1,side4:1,side5:1},config6:{side0:1,side1:1,side2:4,side3:8,side4:1,side5:9},eject0:0b,eject2:0b,eject6:0b},componentEjector:{color0:-1,color1:-1,color2:-1,color3:-1,color4:-1,color5:-1,strictInput:0b},componentUpgrade:{Items:[],upgrades:[]},controlType:0,owner:[I;**********,-**********,-**********,836316863],securityMode:0}}},{Count:1b,Slot:21b,id:"mekanismtools:refined_obsidian_pickaxe",tag:{Damage:289}},{Count:3b,Slot:22b,id:"minecraft:blaze_powder"},{Count:9b,Slot:23b,id:"minecraft:redstone"},{Count:1b,Slot:24b,id:"minecraft:potion",tag:{Potion:"apotheosis:extra_long_flying"}},{Count:1b,Slot:25b,id:"mekanism:basic_fluid_tank",tag:{mekData:{FluidTanks:[{Tank:0b,stored:{Amount:23000,FluidName:"minecraft:lava"}}],bucketMode:1b,owner:[I;**********,-**********,-**********,836316863]}}},{Count:1b,Slot:100b,id:"minecraft:chainmail_boots",tag:{Damage:111,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.8851845f,"irons_spellbooks:armor/attribute/spell_power":0.917818f},name:'{"color":"#33FF33","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_power"},"",{"translate":"affix.apotheosis:armor/attribute/blessed.suffix"}]}',rarity:"apotheosis:uncommon",sockets:1,uuids:[[I;-1050205019,-2079047143,-1983847708,-2113404653]]},apoth_rchest:1b}},{Count:1b,Slot:101b,id:"mekanismtools:refined_obsidian_leggings",tag:{Damage:239}},{Count:1b,Slot:102b,id:"mekanismtools:refined_obsidian_chestplate",tag:{Damage:239}},{Count:1b,Slot:103b,id:"mekanismtools:refined_obsidian_helmet",tag:{Damage:239}}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:the_nether",pos:[I;58,54,-75]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[1043.2641917414278d,62.0d,620.7354611030257d],Railways_DataVersion:2,Rotation:[-64.349976f,-4.650042f],Score:3485,SelectedItemSlot:7,SleepTimer:0s,Spigot.ticksLived:76216,UUID:[I;**********,-**********,-**********,836316863],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":286697659482174L,WorldUUIDLeast:-7555446939536478170L,WorldUUIDMost:-7727361220654125L,XpLevel:16,XpP:0.90476215f,XpSeed:-444740449,XpTotal:390,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752059970905L,keepLevel:0b,lastKnownName:"a145330",lastPlayed:1752207159825L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:1.2192354f,foodLevel:20,foodSaturationLevel:8.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","mcwroofs:dark_oak_planks_roof","mcwfurnitures:stripped_jungle_desk","mcwwindows:birch_four_window","biomesoplenty:fir_boat","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","handcrafted:spider_trophy","mcwpaths:dark_prismarine_dumble_paving","allthecompressed:compress/calcite_1x","alltheores:gold_dust_from_hammer_crushing","minecraft:bone_block","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","ae2:network/cables/dense_smart_green","pneumaticcraft:wall_lamp_white","mcwroofs:red_terracotta_lower_roof","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","create:oak_window","mcwdoors:mangrove_four_panel_door","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","create:crafting/kinetics/light_gray_seat","create:cut_diorite_from_stone_types_diorite_stonecutting","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","mcwpaths:cobbled_deepslate_running_bond_slab","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","twigs:polished_rhyolite_stonecutting","handcrafted:deepslate_corner_trim","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","handcrafted:dark_oak_shelf","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","sophisticatedstorage:copper_to_iron_tier_upgrade","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","minecraft:cyan_dye_from_pitcher_plant","rftoolsutility:screen_link","minecraft:leather_helmet","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mekanism:fluid_tank/basic","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","minecraft:nether_brick_stairs","pneumaticcraft:crop_support","supplementaries:bubble_blower","naturalist:glow_goop","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwroofs:black_steep_roof","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","minecraft:pink_terracotta","mcwbiomesoplenty:mahogany_plank_pane_window","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwwindows:dark_oak_blinds","mcwpaths:cobblestone_dumble_paving","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","mcwdoors:mangrove_barn_door","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","mcwtrpdoors:mangrove_barred_trapdoor","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","allthecompressed:compress/quartz_block_1x","railcraft:standard_rail_from_rail","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","minecraft:mangrove_wood","additionallanterns:mossy_cobblestone_chain","botania:bauble_box","mcwroofs:black_top_roof","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","cfm:oak_kitchen_counter","minecraft:golden_hoe","minecraft:fire_charge","mcwpaths:diorite_running_bond_stairs","utilitarian:utility/mangrove_logs_to_trapdoors","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","mcwroofs:mangrove_planks_upper_lower_roof","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:dye_green_bed","alltheores:platinum_dust_from_hammer_crushing","mcwpaths:dark_prismarine_clover_paving","railcraft:any_detector","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","mcwroofs:stone_bricks_upper_steep_roof","biomesoplenty:palm_chest_boat","minecraft:stone_button","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","mcwpaths:diorite_running_bond_slab","pneumaticcraft:regulator_tube_module","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:purpur_block","travelersbackpack:dye_lime_sleeping_bag","railcraft:brass_ingot_crafted_with_ingots","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwpaths:sandstone_running_bond_stairs","minecraft:polished_blackstone_slab","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:nether_brick_wall","pneumaticcraft:transfer_gadget","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","quark:building/crafting/furnaces/deepslate_furnace","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","minecraft:lime_candle","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","littlelogistics:barrel_barge","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","pneumaticcraft:vortex_tube","mcwwindows:warped_louvered_shutter","undergarden:wigglewood_chest_boat","xnet:connector_routing","blue_skies:glowing_nature_stone","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","dyenamics:aquamarine_terracotta","handcrafted:dark_oak_drawer","travelersbackpack:cake","create:cut_diorite_wall_from_stone_types_diorite_stonecutting","mcwroofs:green_terracotta_attic_roof","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","immersiveengineering:crafting/shield","utilitix:directional_highspeed_rail","mcwpaths:mangrove_planks_path","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","create:small_diorite_bricks_from_stone_types_diorite_stonecutting","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","cfm:warped_mail_box","xnet:antenna_base","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","allthecompressed:compress/diorite_1x","mcwroofs:oak_roof","cfm:jungle_desk","minecraft:mangrove_chest_boat","minecraft:iron_hoe","aether:crossbow_repairing","minecraft:iron_ingot_from_iron_block","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","mcwroofs:light_blue_concrete_lower_roof","mcwfurnitures:jungle_drawer","mcwroofs:purple_striped_awning","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","minecraft:kjs/buildinggadgets2_gadget_building","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","connectedglass:clear_glass_light_blue2","cfm:stripped_mangrove_desk","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","ad_astra:small_light_blue_industrial_lamp","mcwfences:blackstone_brick_railing_gate","mcwroofs:dark_oak_planks_upper_lower_roof","mcwbiomesoplenty:maple_curved_gate","mcwroofs:black_terracotta_roof","cfm:stripped_mangrove_cabinet","mcwpaths:mossy_stone_running_bond_slab","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","mcwfurnitures:mangrove_lower_triple_drawer","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","mcwdoors:mangrove_cottage_door","additionallanterns:andesite_lantern","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","mcwdoors:mangrove_japanese2_door","cfm:dark_oak_coffee_table","mcwtrpdoors:print_swamp","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwtrpdoors:mangrove_paper_trapdoor","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwwindows:dark_prismarine_brick_gothic","minecraft:white_terracotta","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","mcwroofs:mangrove_planks_lower_roof","mcwfurnitures:oak_double_drawer_counter","cfm:blue_grill","mcwlights:golden_chandelier","minecraft:armor_stand","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","cfm:stripped_birch_kitchen_drawer","botania:petal_green_double","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","minecraft:mangrove_slab","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","mcwroofs:nether_bricks_upper_lower_roof","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","xnet:netcable_yellow","mcwroofs:blue_terracotta_roof","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","mcwfurnitures:jungle_wardrobe","dyenamics:bed/rose_bed","dyenamics:navy_wool","mcwwindows:magenta_mosaic_glass_pane","alltheores:platinum_gear","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","create:cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","croptopia:potato_chips","minecraft:chiseled_stone_bricks_stone_from_stonecutting","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","mekanismtools:refined_obsidian/armor/chestplate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwbridges:dark_oak_rail_bridge","mcwdoors:oak_cottage_door","create:diorite_from_stone_types_diorite_stonecutting","immersiveengineering:crafting/torch","mcwbridges:diorite_bridge_stair","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","delightful:food/baklava","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","create:small_diorite_brick_stairs_from_stone_types_diorite_stonecutting","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","minecraft:light_blue_candle","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","create:small_diorite_brick_wall_from_stone_types_diorite_stonecutting","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","cfm:red_grill","create:small_calcite_brick_slab_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwfurnitures:mangrove_double_wardrobe","mcwbiomesoplenty:stripped_palm_log_window2","mcwroofs:mangrove_upper_steep_roof","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","minecraft:smooth_sandstone_slab","supplementaries:flags/flag_light_blue","minecraft:rail","mcwpaths:cobbled_deepslate_running_bond","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","croptopia:chicken_and_dumplings","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","connectedglass:borderless_glass_light_blue2","mcwtrpdoors:jungle_paper_trapdoor","mcwroofs:diorite_upper_lower_roof","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","mcwlights:upgraded_torch","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","alltheores:smelting_dust/osmium_ingot","cfm:mangrove_upgraded_fence","nethersdelight:hoglin_sirloin_from_campfire_cooking","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","handcrafted:diorite_pillar_trim","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwwindows:crimson_shutter","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:dark_prismarine_windmill_weave_slab","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwdoors:mangrove_classic_door","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","pneumaticcraft:wall_lamp_pink","handcrafted:terracotta_wide_pot","mcwfurnitures:stripped_mangrove_counter","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwroofs:purple_terracotta_upper_lower_roof","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","biomesoplenty:orange_dye_from_burning_blossom","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","blue_skies:cake_compat","minecraft:mangrove_planks","mcwpaths:mossy_stone_windmill_weave_path","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","gtceu:smelting/smelt_dust_iron_to_ingot","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","mcwroofs:dark_oak_planks_top_roof","additionallanterns:normal_nether_bricks_chain","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:fried_chicken","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime2","create:deepslate_from_stone_types_deepslate_stonecutting","twigs:calcite_wall","cfm:stripped_oak_chair","xnet:netcable_blue","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","mcwwindows:oak_shutter","mcwroofs:lime_concrete_steep_roof","cfm:rock_path","minecraft:wooden_hoe","mekanismgenerators:generator/heat","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","botania:dye_green","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwpaths:diorite_honeycomb_paving","mcwroofs:nether_bricks_top_roof","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwroofs:nether_bricks_lower_roof","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","aquaculture:stone_fillet_knife","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","delightful:food/cooking/nut_butter_bottle","cfm:stripped_mangrove_park_bench","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","mcwwindows:crimson_planks_four_window","minecraft:piston","mcwfurnitures:stripped_mangrove_wardrobe","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","allthemodium:allthemodium_ingot_from_raw_smelting","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","croptopia:apple_sapling","mcwtrpdoors:mangrove_cottage_trapdoor","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","minecraft:diorite_stairs_from_diorite_stonecutting","mcwfences:spruce_horse_fence","handcrafted:oak_shelf","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","silentgear:raw_crimson_iron_block","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","minecraft:dark_oak_pressure_plate","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwwindows:birch_plank_four_window","mcwfences:majestic_metal_fence_gate","packingtape:tape","mcwpaths:blackstone_flagstone_path","create:cut_diorite_stairs_from_stone_types_diorite_stonecutting","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","silentgear:crimson_iron_dust","mcwroofs:red_terracotta_upper_steep_roof","enderio:dark_steel_nugget_to_ingot","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","handcrafted:dark_oak_dining_bench","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","mcwfurnitures:jungle_stool_chair","create:polished_cut_diorite_slab_from_stone_types_diorite_stonecutting","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","mcwpaths:mossy_stone_crystal_floor_stairs","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","handcrafted:pillager_trophy","sfm:water_tank","create:polished_cut_calcite_from_stone_types_calcite_stonecutting","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","cfm:dye_lime_picket_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","pylons:expulsion_pylon","botania:manasteel_chestplate","create:dark_oak_window","mcwfences:bamboo_highley_gate","mcwpaths:dark_prismarine_windmill_weave","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","dyenamics:bed/ultramarine_bed_frm_white_bed","mcwroofs:jungle_roof","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mcwwindows:white_curtain","mcwroofs:white_terracotta_roof","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","cfm:light_gray_grill","create:crafting/kinetics/black_seat","cfm:oak_desk","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","connectedglass:clear_glass_lime_pane2","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","mcwfurnitures:mangrove_stool_chair","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","immersiveengineering:crafting/minecart_metalbarrel","mcwpaths:diorite_running_bond_path","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","minecraft:sandstone_wall","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:mangrove_modern_chair","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","mcwtrpdoors:mangrove_four_panel_trapdoor","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwpaths:andesite_crystal_floor_stairs","mcwwindows:oak_plank_four_window","mcwroofs:sandstone_lower_roof","domum_ornamentum:brown_bricks","cfm:red_cooler","handcrafted:jungle_dining_bench","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","mcwroofs:red_nether_bricks_upper_steep_roof","xnet:facade","mekanismtools:osmium/tools/pickaxe","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","mcwpaths:sandstone_basket_weave_paving","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","silentgear:crimson_iron_raw_ore_blasting","undergarden:grongle_boat","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","littlelogistics:automatic_switch_rail","minecraft:carrot_on_a_stick","quark:tweaks/crafting/utility/tools/stone_axe","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","mekanismtools:refined_obsidian/armor/leggings","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwpaths:diorite_basket_weave_paving","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","railcraft:track_undercutter","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:red_nether_bricks_top_roof","mcwroofs:orange_terracotta_steep_roof","enderio:dark_steel_nugget","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","croptopia:fruit_smoothie","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","railcraft:steel_shears","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:mossy_cobblestone_slab_from_mossy_cobblestone_stonecutting","minecraft:diamond_helmet","mcwfurnitures:stripped_mangrove_end_table","securitycraft:reinforced_purple_stained_glass_pane_from_dye","mcwroofs:dark_oak_planks_steep_roof","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","connectedglass:borderless_glass_lime_pane2","minecraft:dark_oak_fence_gate","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","bigreactors:energizer/computerport","allthemodium:allthemodium_apple","mcwroofs:lime_terracotta_upper_lower_roof","mekanism:processing/osmium/raw/from_raw_block","mcwpaths:mossy_stone_running_bond_stairs","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","itemcollectors:basic_collector","handcrafted:dark_oak_fancy_bed","minecraft:loom","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","ad_astra:small_lime_industrial_lamp","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","handcrafted:golden_wide_pot","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","botania:green_shiny_flower","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","handcrafted:dark_oak_counter","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwroofs:pink_terracotta_steep_roof","railcraft:steam_locomotive","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","mekanismtools:osmium/armor/chestplate","utilitarian:utility/mangrove_logs_to_pressure_plates","computercraft:computer_advanced","minecraft:polished_blackstone","mcwpaths:diorite_crystal_floor_slab","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","biomesoplenty:red_dye_from_waterlily","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","dyenamics:bed/maroon_bed_frm_white_bed","reliquary:uncrafting/slime_ball","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwpaths:dark_prismarine_diamond_paving","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwpaths:dark_prismarine_flagstone","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","mcwpaths:dark_oak_planks_path","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","mcwwindows:dark_prismarine_parapet","rftoolsutility:inventoryplus_module","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","mcwfurnitures:jungle_end_table","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwpaths:dark_prismarine_honeycomb_paving","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","mcwpaths:dark_prismarine_flagstone_path","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","alltheores:signalum_dust_from_alloy_blending","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","minecraft:dark_prismarine_stairs","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","create:crafting/kinetics/fluid_valve","supplementaries:crystal_display","minecraft:sandstone_stairs","mcwroofs:mangrove_planks_attic_roof","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","minecraft:chest_minecart","create:crafting/kinetics/white_seat","paraglider:cosmetic/paraglider","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","mcwroofs:white_upper_lower_roof","minecraft:magenta_terracotta","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","aether:golden_sword_repairing","quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","cfm:mangrove_bedside_cabinet","handcrafted:white_cushion","mcwdoors:metal_warning_door","utilitarian:utility/oak_logs_to_doors","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gray_terracotta_attic_roof","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","mcwroofs:mangrove_attic_roof","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","minecraft:end_stone_bricks","mcwdoors:jungle_stable_head_door","croptopia:egg_roll","railcraft:iron_gear","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","minecraft:lime_dye","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","create:framed_glass_from_glass_colorless_stonecutting","botania:quartz_sunny","sophisticatedstorage:jungle_barrel","railcraft:signal_lamp","minecraft:mangrove_door","mcwpaths:diorite_windmill_weave_stairs","mcwpaths:diorite_diamond_paving","handcrafted:dark_oak_chair","cfm:jungle_blinds","tombstone:bone_needle","mcwpaths:mossy_stone_windmill_weave_slab","minecraft:stone_brick_wall","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","minecraft:cyan_dye","mcwwindows:acacia_window2","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","minecraft:gold_block","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","handcrafted:jungle_counter","cfm:stripped_mangrove_desk_cabinet","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","handcrafted:mangrove_desk","mcwfences:warped_curved_gate","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","ironfurnaces:augments/augment_factory","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","mcwpaths:diorite_windmill_weave_path","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","minecraft:copper_ingot_from_blasting_raw_copper","mcwroofs:mangrove_planks_roof","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","mcwpaths:dark_prismarine_square_paving","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","mcwfurnitures:mangrove_drawer_counter","additionallanterns:copper_lantern","twilightforest:canopy_chest_boat","minecraft:dark_oak_button","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwpaths:diorite_flagstone","mcwbiomesoplenty:fir_pane_window","mcwpaths:dark_prismarine_running_bond","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","handcrafted:mangrove_chair","supplementaries:deepslate_lamp","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","utilitix:ender_cart","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","utilitix:stonecutter_cart","cfm:dye_green_picket_fence","mcwwindows:oak_plank_window","nethersdelight:hoglin_sirloin","mcwbridges:dark_oak_log_bridge_middle","mcwwindows:oak_log_parapet","minecraft:mangrove_fence_gate","mcwfurnitures:stripped_mangrove_drawer_counter","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","mcwroofs:mangrove_planks_top_roof","travelersbackpack:spider_smithing","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","allthecompressed:compress/jungle_planks_1x","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","minecraft:dark_oak_door","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwpaths:mossy_cobblestone_diamond_paving","cfm:dark_oak_cabinet","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","deeperdarker:warden_upgrade_smithing_template","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","alltheores:platinum_plate","mcwroofs:orange_terracotta_upper_steep_roof","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","pneumaticcraft:reinforced_brick_wall","farmersdelight:steak_and_potatoes","cfm:dark_oak_kitchen_counter","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","mcwpaths:sandstone_crystal_floor_stairs","mcwfurnitures:stripped_mangrove_triple_drawer","mcwtrpdoors:mangrove_classic_trapdoor","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","cfm:dark_oak_desk","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","mcwfurnitures:stripped_mangrove_modern_wardrobe","mcwbiomesoplenty:maple_four_window","cfm:blue_kitchen_counter","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","silentgear:bort_block","gtceu:shapeless/decompress_platinum_from_ore_block","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","create:crafting/kinetics/item_vault","mcwfences:warped_highley_gate","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","mcwpaths:dark_prismarine_crystal_floor_path","supplementaries:statue","immersiveengineering:crafting/tinted_glass_lead_wire","minecraft:dye_green_carpet","cfm:pink_grill","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwpaths:diorite_running_bond","mcwwindows:granite_window2","enderio:dark_steel_grinding_ball","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwfurnitures:mangrove_counter","minecraft:emerald","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","waystones:waystone","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","pneumaticcraft:pressure_chamber_wall","minecraft:glass","allthecompressed:compress/gold_block_1x","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","handcrafted:dark_oak_nightstand","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","domum_ornamentum:gray_floating_carpet","cfm:stripped_jungle_park_bench","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","minecraft:smooth_sandstone_stairs_from_smooth_sandstone_stonecutting","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","enderio:dark_steel_ladder","minecraft:cyan_terracotta","mcwfurnitures:mangrove_modern_desk","mcwwindows:light_blue_mosaic_glass","ad_astra:green_industrial_lamp","mcwlights:rustic_torch","minecraft:mangrove_fence","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","cfm:oak_upgraded_fence","twigs:mossy_cobblestone_bricks","railcraft:steel_gear","mcwbridges:mangrove_rail_bridge","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","mcwbiomesoplenty:jacaranda_wired_fence","mcwdoors:mangrove_glass_door","mcwdoors:mangrove_bamboo_door","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","mcwpaths:andesite_flagstone","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:dead_plank_window2","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","pylons:potion_filter","mcwroofs:diorite_steep_roof","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","create:crafting/kinetics/brown_seat","cfm:stripped_mangrove_kitchen_sink_light","littlelogistics:energy_tug","pneumaticcraft:wall_lamp_light_gray","handcrafted:quartz_corner_trim","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwroofs:light_gray_concrete_lower_roof","mcwfurnitures:stripped_mangrove_chair","twigs:rhyolite_slab","immersiveengineering:crafting/stick_iron","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","mcwpaths:stone_crystal_floor_path","sophisticatedstorage:mangrove_chest","twigs:stone_column","minecraft:lectern","sophisticatedstorage:dark_oak_limited_barrel_1","allthecompressed:compress/dark_oak_planks_1x","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","sophisticatedstorage:dark_oak_limited_barrel_4","sophisticatedstorage:dark_oak_limited_barrel_3","sophisticatedstorage:dark_oak_limited_barrel_2","silentgear:glittery_dust","mcwbiomesoplenty:hellbark_plank_window2","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","mcwwindows:cyan_curtain","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","silentgear:crimson_iron_raw_ore_smelting","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfurnitures:mangrove_triple_drawer","allthecompressed:compress/tuff_1x","minecraft:dye_lime_wool","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","sfm:fancy_to_cable","aquaculture:heavy_hook","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwwindows:acacia_window","cfm:mangrove_blinds","mcwroofs:green_terracotta_roof","croptopia:food_press","railcraft:mob_detector","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwpaths:dark_prismarine_running_bond_stairs","pneumaticcraft:small_tank","ae2:network/cables/glass_green","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","farmersdelight:nether_salad","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","mcwwindows:dark_oak_pane_window","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","pneumaticcraft:reinforced_brick_pillar","create:cut_calcite_brick_stairs_from_stone_types_calcite_stonecutting","supplementaries:boat_jar","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","botania:dodge_ring","cfm:dark_oak_bedside_cabinet","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwfences:oak_highley_gate","minecraft:dark_prismarine_slab_from_dark_prismarine_stonecutting","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","mcwbridges:nether_bricks_bridge","delightful:smelting/roasted_acorn","mcwfurnitures:mangrove_double_drawer_counter","mcwpaths:dark_prismarine_crystal_floor_stairs","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","aether:iron_boots_repairing","mcwwindows:end_brick_gothic","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","rftoolsutility:module_template","tombstone:impregnated_diamond","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","dyenamics:bed/navy_bed_frm_white_bed","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mekanismtools:refined_obsidian/tools/sword","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwroofs:jungle_planks_upper_lower_roof","supplementaries:faucet","twigs:polished_calcite_bricks_from_calcite_stonecutting","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","supplementaries:candle_holders/candle_holder_lime_dye","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","connectedglass:borderless_glass_green2","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwroofs:stone_bricks_top_roof","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","mcwroofs:oak_upper_lower_roof","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","ad_astra:compressor","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","connectedglass:clear_glass_lime2","farmersdelight:cooking/mushroom_rice","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","handcrafted:dark_oak_desk","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","mcwfurnitures:mangrove_lower_bookshelf_drawer","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","sophisticatedstorage:dark_oak_barrel","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","createoreexcavation:vein_finder","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","mcwdoors:mangrove_tropical_door","securitycraft:reinforced_gray_stained_glass","domum_ornamentum:mossy_cobblestone_extra","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","botania:dye_lime","sophisticatedstorage:oak_limited_barrel_1","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","create:cut_diorite_bricks_from_stone_types_diorite_stonecutting","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","dyenamics:navy_terracotta","supplementaries:hourglass","aether:chainmail_helmet_repairing","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","mekanism:metallurgic_infuser","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","mcwroofs:yellow_striped_awning","minecraft:light_blue_terracotta","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","domum_ornamentum:cactus_extra","cfm:cyan_cooler","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwpaths:diorite_crystal_floor","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwroofs:light_gray_roof","create:small_calcite_brick_stairs_from_stone_types_calcite_stonecutting","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","dyenamics:fluorescent_terracotta","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwfurnitures:mangrove_covered_desk","mcwdoors:mangrove_beach_door","travelersbackpack:redstone","minecraft:tnt_minecart","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","create:cut_calcite_from_stone_types_calcite_stonecutting","chimes:glass_bells","mcwwindows:iron_shutter","connectedglass:borderless_glass_light_blue_pane2","immersiveengineering:crafting/blueprint_molds","handcrafted:kitchen_hood_pipe","minecraft:brown_terracotta","mcwpaths:mossy_stone_crystal_floor","travelersbackpack:emerald","croptopia:trail_mix","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","mcwfurnitures:mangrove_wardrobe","mcwpaths:blackstone_running_bond_path","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","mcwpaths:mossy_cobblestone_square_paving","mcwfurnitures:stripped_mangrove_modern_chair","mcwpaths:sandstone_flagstone","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwbridges:blackstone_bridge_pier","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","minecraft:end_crystal","croptopia:shaped_nether_wart_stew","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwfurnitures:stripped_mangrove_large_drawer","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","alltheores:copper_rod","simplylight:illuminant_slab","twigs:cobblestone_bricks","railcraft:track_layer","mcwwindows:sandstone_pane_window","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","minecraft:diorite_wall","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","utilitix:filter_rail","aether:diamond_gloves","minecraft:mossy_cobblestone_stairs_from_mossy_cobblestone_stonecutting","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","minecraft:mossy_cobblestone_from_moss_block","silentgear:golden_nether_banana","mcwfurnitures:mangrove_bookshelf_drawer","minecraft:cracked_stone_bricks","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","minecraft:lantern","mcwwindows:jungle_pane_window","travelersbackpack:dye_green_sleeping_bag","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","mcwbridges:nether_bricks_bridge_pier","alltheores:gold_dust_from_hammer_ingot_crushing","utilitarian:utility/mangrove_logs_to_slabs","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","aether:golden_boots_repairing","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","securitycraft:reinforced_warped_fence_gate","mcwfurnitures:stripped_mangrove_bookshelf_drawer","mcwroofs:black_terracotta_attic_roof","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","handcrafted:mangrove_dining_bench","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","handcrafted:dark_oak_side_table","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","mcwfurnitures:stripped_mangrove_double_drawer_counter","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwroofs:diorite_attic_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","mcwroofs:pink_concrete_top_roof","mcwfurnitures:mangrove_end_table","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","mcwfurnitures:stripped_mangrove_cupboard_counter","aether:skyroot_lectern","mcwpaths:diorite_crystal_floor_stairs","minecraft:polished_blackstone_pressure_plate","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","handcrafted:mangrove_bench","ae2:network/cables/glass_lime","alltheores:diamond_rod","minecraft:stone_slab_from_stone_stonecutting","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:light_blue_terracotta_upper_lower_roof","reliquary:infernal_claw","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","silentgear:crimson_iron_dust_blasting","minecraft:stone_brick_slab_from_stone_stonecutting","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwwindows:mangrove_plank_pane_window","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","farmersdelight:grilled_salmon","minecraft:stone_brick_stairs_from_stone_stonecutting","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwfurnitures:stripped_mangrove_double_wardrobe","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","minecraft:polished_diorite_from_diorite_stonecutting","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","mcwroofs:white_roof","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","mcwfurnitures:mangrove_bookshelf_cupboard","mcwbridges:balustrade_blackstone_bridge","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","mcwpaths:mossy_stone_strewn_rocky_path","cfm:mangrove_kitchen_counter","handcrafted:dark_oak_table","cfm:mangrove_park_bench","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","pneumaticcraft:wall_lamp_inverted_yellow","mcwpaths:diorite_flagstone_stairs","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","botania:water_ring","connectedglass:scratched_glass_lime2","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","pneumaticcraft:wall_lamp_brown","cfm:stripped_oak_mail_box","cfm:stripped_mangrove_chair","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","mcwbridges:diorite_bridge_pier","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","handcrafted:terracotta_medium_pot","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwpaths:andesite_crystal_floor","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","dyenamics:mint_dye","create:polished_cut_diorite_stairs_from_stone_types_diorite_stonecutting","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","railcraft:diamond_spike_maul","minecraft:dark_oak_stairs","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","mcwpaths:mossy_cobblestone_honeycomb_paving","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","botania:mushroom_10","cfm:white_kitchen_drawer","mcwtrpdoors:mangrove_barn_trapdoor","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwpaths:mossy_stone_windmill_weave","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:nether_bricks_upper_steep_roof","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","mcwdoors:jungle_glass_door","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","minecraft:red_nether_brick_wall","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","reliquary:mercy_cross","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwpaths:diorite_square_paving","supplementaries:key","minecraft:blackstone_wall","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","rftoolspower:power_core1","mcwroofs:white_concrete_upper_steep_roof","mcwpaths:blackstone_windmill_weave_stairs","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:cobbled_deepslate_lantern","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","cfm:stripped_mangrove_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","pneumaticcraft:wall_lamp_inverted_gray","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","minecraft:dye_lime_carpet","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","mcwfurnitures:stripped_mangrove_desk","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","immersiveengineering:crafting/minecart_reinforcedcrate","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","mekanismtools:osmium/armor/boots","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","create:polished_cut_calcite_stairs_from_stone_types_calcite_stonecutting","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","mcwbridges:rope_mangrove_bridge","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwfurnitures:stripped_mangrove_double_drawer","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","railcraft:iron_spike_maul","cfm:mangrove_coffee_table","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","mcwbiomesoplenty:fir_four_window","everythingcopper:copper_axe","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","connectedglass:scratched_glass_green_pane2","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","create:cut_calcite_brick_slab_from_stone_types_calcite_stonecutting","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","handcrafted:oak_nightstand","railcraft:personal_world_spike","mcwlights:lava_lamp","toolbelt:pouch","cfm:dark_oak_chair","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","dyenamics:bubblegum_concrete_powder","xnet:redstoneproxy_update","cfm:mangrove_desk","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwroofs:nether_bricks_attic_roof","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","minecraft:cracked_nether_bricks","utilitix:gilding_crystal","minecraft:dark_prismarine_stairs_from_dark_prismarine_stonecutting","mcwfences:spruce_stockade_fence","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","supplementaries:slingshot","silentgear:crimson_steel_ingot","allthemodium:allthemodium_plate","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","everythingcopper:copper_rail","cfm:black_grill","handcrafted:deepslate_pillar_trim","mcwwindows:mangrove_window2","mcwfurnitures:stripped_mangrove_striped_chair","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","ironfurnaces:furnaces/iron_furnace2","create:small_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","connectedglass:scratched_glass_light_blue_pane2","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","mcwtrpdoors:mangrove_blossom_trapdoor","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:nether_bricks_steep_roof","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","minecraft:iron_pickaxe","aether:shield_repairing","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","mcwpaths:mossy_stone_windmill_weave_stairs","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","aether:leather_boots_repairing","cfm:post_box","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","minecraft:dark_oak_slab","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","minecraft:packed_mud","mcwdoors:mangrove_bark_glass_door","cfm:orange_cooler","minecraft:lime_stained_glass","mcwbridges:balustrade_mossy_stone_bricks_bridge","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","create:layered_calcite_from_stone_types_calcite_stonecutting","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","minecraft:stone_brick_walls_from_stone_stonecutting","mcwroofs:gray_terracotta_top_roof","ae2:tools/nether_quartz_wrench","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","mcwpaths:mossy_stone_flagstone","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwpaths:dark_prismarine_running_bond_slab","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","chimes:copper_chimes","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","minecraft:red_nether_brick_slab","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_mangrove_lower_bookshelf_drawer","mcwfences:dark_oak_stockade_fence","minecraft:cooked_salmon_from_campfire_cooking","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","integrateddynamics:crafting/squeezer","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","mcwdoors:mangrove_nether_door","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:mangrove_button","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","reliquary:mob_charm_fragments/wither_skeleton","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","utilitarian:utility/mangrove_logs_to_doors","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","biomesoplenty:mahogany_chest_boat","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","minecraft:cooked_salmon_from_smoking","mcwwindows:prismarine_brick_gothic","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","cfm:dye_lime_picket_gate","littlelogistics:switch_rail","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwpaths:mossy_cobblestone_clover_paving","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","constructionwand:core_angel","xnet:connector_red_dye","mcwroofs:red_terracotta_roof","minecolonies:shapetool","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwfurnitures:stripped_mangrove_glass_table","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","minecraft:mossy_stone_brick_slab","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:bamboo_chest_raft","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","reliquary:mob_charm_fragments/slime","dyenamics:bed/bubblegum_bed_frm_white_bed","aquaculture:birch_fish_mount","handcrafted:mangrove_couch","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","cfm:dark_oak_park_bench","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","minecraft:cobblestone_stairs","mcwwindows:crimson_louvered_shutter","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","tombstone:ankh_of_prayer","cfm:white_sofa","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwpaths:mossy_stone_flagstone_slab","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","cfm:black_kitchen_drawer","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","nethersdelight:hoglin_sirloin_from_smoking","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","mcwfurnitures:mangrove_striped_chair","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","mcwdoors:mangrove_modern_door","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","handcrafted:oak_bench","railcraft:track_remover","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwpaths:dark_prismarine_flagstone_stairs","mcwroofs:diorite_roof","mcwwindows:birch_louvered_shutter","minecraft:repeater","minecraft:red_concrete_powder","forbidden_arcanus:test_tube","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","mcwdoors:mangrove_whispering_door","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwpaths:blackstone_crystal_floor_path","cfm:jungle_cabinet","mcwdoors:mangrove_paper_door","handcrafted:mangrove_corner_trim","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","create:cut_diorite_slab_from_stone_types_diorite_stonecutting","mcwroofs:base_roof_block","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","alltheores:smelting_dust/platinum_ingot","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","twigs:blackstone_column","mcwbridges:balustrade_mossy_cobblestone_bridge","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfurnitures:stripped_mangrove_lower_triple_drawer","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","securitycraft:reinforced_orange_stained_glass_pane_from_dye","cfm:jungle_chair","mcwlights:birch_tiki_torch","minecraft:red_nether_brick_wall_from_red_nether_bricks_stonecutting","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:jungle_door","mcwpaths:mossy_cobblestone_dumble_paving","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwbiomesoplenty:jacaranda_horse_fence","supplementaries:sign_post_dark_oak","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","alltheores:platinum_rod","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","botania:petal_green","mcwroofs:dark_oak_planks_attic_roof","dyenamics:bed/fluorescent_bed_frm_white_bed","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","botania:mushroom_9","handcrafted:jungle_drawer","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","minecraft:oak_pressure_plate","botania:mushroom_5","botania:mushroom_4","minecraft:stone_brick_stairs","botania:mushroom_3","bigreactors:turbine/basic/activefluidport_forge","mcwwindows:quartz_window","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","allthemods:croptopia/knife","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","create:cut_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:clock","dyenamics:conifer_dye","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwroofs:diorite_lower_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","mcwpaths:diorite_windmill_weave","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","mcwdoors:print_swamp","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","aquaculture:iron_fillet_knife","silentgear:diamond_shard","dyenamics:rose_wool","mcwfurnitures:stripped_mangrove_table","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","railways:benchcart","additionallanterns:red_nether_bricks_chain","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwdoors:oak_modern_door","mcwwindows:birch_window2","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","minecraft:mangrove_stairs","botania:magnet_ring","botania:conversions/green_petal_block_deconstruct","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","cfm:mangrove_crate","mcwroofs:purple_terracotta_roof","twigs:dark_oak_table","mcwfurnitures:stripped_jungle_chair","mcwbridges:nether_bricks_bridge_stair","mcwwindows:white_mosaic_glass","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","minecraft:moss_carpet","cfm:acacia_kitchen_sink_dark","mcwroofs:jungle_upper_steep_roof","handcrafted:dark_oak_cupboard","rftoolsbase:machine_frame","minecraft:mossy_cobblestone_slab","minecraft:polished_andesite_slab_from_andesite_stonecutting","pneumaticcraft:remote","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","minecraft:polished_diorite_slab_from_diorite_stonecutting","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","pylons:infusion_pylon","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","minecraft:dark_oak_sign","domum_ornamentum:light_blue_brick_extra","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","supplementaries:cog_block","connectedglass:scratched_glass_light_blue2","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","minecraft:lime_concrete_powder","minecraft:leather_leggings","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","croptopia:fruit_salad","securitycraft:block_change_detector","supplementaries:pancake_fd","twigs:rocky_dirt","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwfurnitures:stripped_mangrove_covered_desk","mcwroofs:pink_terracotta_lower_roof","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","immersiveengineering:crafting/minecart_woodencrate","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","additionallanterns:quartz_chain","aether:stone_axe_repairing","twilightforest:sorting_chest_boat","travelersbackpack:standard_smithing","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","mekanism:processing/gold/ingot/from_dust_smelting","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","pneumaticcraft:pressure_chamber_glass_x4","minecraft:red_nether_brick_stairs_from_red_nether_bricks_stonecutting","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","minecraft:waxed_copper_block_from_honeycomb","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","mcwroofs:lime_terracotta_roof","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","mcwfurnitures:stripped_mangrove_bookshelf","minecraft:warped_fungus_on_a_stick","undergarden:gloom_o_lantern","mcwroofs:red_nether_bricks_lower_roof","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","reliquary:uncrafting/wither_skeleton_skull","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","handcrafted:mangrove_nightstand","mcwbridges:balustrade_diorite_bridge","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","pneumaticcraft:heat_pipe","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","railcraft:logbook","ae2:misc/tiny_tnt","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwpaths:blackstone_dumble_paving","mcwwindows:jungle_blinds","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","alltheores:copper_ore_hammer","additionallanterns:normal_sandstone_chain","mcwfurnitures:mangrove_desk","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","botania:flower_bag","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","aether:stone_sword_repairing","cfm:jungle_kitchen_counter","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","mcwfurnitures:mangrove_coffee_table","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","mcwpaths:diorite_flagstone_slab","mcwpaths:dark_prismarine_crystal_floor","mcwbiomesoplenty:fir_window2","dyenamics:bed/wine_bed_frm_white_bed","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mekanism:osmium_compressor","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","minecraft:smooth_sandstone_slab_from_smooth_sandstone_stonecutting","mcwroofs:gutter_middle_blue","sfm:manager","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwroofs:purple_concrete_roof","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","mcwfurnitures:mangrove_double_drawer","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","littlelogistics:tee_junction_rail","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","rftoolsutility:fluidplus_module","minecraft:bowl","deeperdarker:bloom_boat","create:small_diorite_brick_slab_from_stone_types_diorite_stonecutting","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwpaths:blackstone_diamond_paving","pneumaticcraft:vortex_cannon","ad_astra:lime_industrial_lamp","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","mcwfurnitures:mangrove_cupboard_counter","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwwindows:dark_oak_plank_window2","mcwfences:spruce_curved_gate","allthecompressed:compress/copper_block_1x","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:stripped_mangrove_stool_chair","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","botania:dye_light_blue","waystones:warp_stone","domum_ornamentum:green_brick_extra","create:diorite_pillar_from_stone_types_diorite_stonecutting","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","twigs:mangrove_table","create:crafting/materials/red_sand_paper","create:polished_cut_diorite_from_stone_types_diorite_stonecutting","mcwwindows:stripped_crimson_stem_window2","mcwpaths:mossy_stone_crystal_floor_path","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwfurnitures:stripped_mangrove_modern_desk","mcwroofs:oak_planks_attic_roof","rftoolsutility:moduleplus_template","dyenamics:amber_terracotta","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","sophisticatedstorage:basic_to_copper_tier_upgrade","mcwdoors:mangrove_waffle_door","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","croptopia:peach_jam","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","create:cut_calcite_stairs_from_stone_types_calcite_stonecutting","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","mcwbiomesoplenty:stripped_magic_log_four_window","corail_woodcutter:mangrove_woodcutter","utilitarian:snad/drit","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","minecraft:cooked_cod","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","securitycraft:alarm","pneumaticcraft:manometer","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","minecraft:polished_diorite","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","dyenamics:icy_blue_dye","pneumaticcraft:wall_lamp_inverted_magenta","mcwroofs:oak_top_roof","mcwpaths:dark_prismarine_crystal_floor_slab","mcwpaths:sandstone_honeycomb_paving","utilitix:crude_furnace","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwwindows:green_curtain","cfm:crimson_kitchen_sink_light","mcwwindows:stone_window","utilitix:comparator_redirector_down","mcwroofs:gray_steep_roof","cfm:stripped_mangrove_kitchen_counter","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","utilitarian:utility/mangrove_logs_to_boats","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","twigs:mossy_cobblestone_bricks_cobblestone","mcwroofs:yellow_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","cfm:stripped_oak_crate","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","minecraft:polished_blackstone_stairs","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","minecraft:golden_helmet","botania:animated_torch","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","mcwpaths:diorite_clover_paving","sophisticatedstorage:mangrove_barrel","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","cfm:mangrove_desk_cabinet","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","farmersdelight:cooking/mushroom_stew","mcwroofs:base_roof_slab","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:dye_light_blue_carpet","create:cut_calcite_wall_from_stone_types_calcite_stonecutting","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","minecraft:quartz_bricks","mcwbridges:mossy_cobblestone_bridge_pier","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","pneumaticcraft:pressure_chamber_valve","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwroofs:jungle_planks_steep_roof","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","supplementaries:candle_holders/candle_holder_light_blue_dye","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","twigs:polished_tuff_stonecutting","cfm:dark_oak_table","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwbiomesoplenty:stripped_magic_log_window2","mcwroofs:orange_concrete_upper_lower_roof","pneumaticcraft:compressed_iron_leggings","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","mcwroofs:dark_oak_planks_lower_roof","mcwfences:modern_andesite_wall","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","cfm:dye_light_blue_picket_gate","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","croptopia:peach_sapling","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","sophisticatedstorage:mangrove_limited_barrel_1","sophisticatedstorage:mangrove_limited_barrel_2","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwroofs:deepslate_lower_roof","sophisticatedstorage:mangrove_limited_barrel_3","sophisticatedstorage:mangrove_limited_barrel_4","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","mcwdoors:mangrove_stable_head_door","minecraft:lightning_rod","mcwwindows:metal_four_window","forbidden_arcanus:dark_rune","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","mcwroofs:jungle_planks_top_roof","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwpaths:dark_prismarine_running_bond_path","minecolonies:apple_pie","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","twigs:mossy_bricks_from_moss_block","simplylight:illuminant_green_block_dyed","handcrafted:mangrove_fancy_bed","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","eccentrictome:tome","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwroofs:mangrove_planks_steep_roof","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","mcwroofs:dark_oak_planks_upper_steep_roof","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","mekanism:cardboard_box","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","alltheores:platinum_ore_hammer","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:mangrove_trapdoor","minecraft:diamond_boots","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","pneumaticcraft:redstone_module","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","croptopia:beetroot_salad","mcwwindows:mangrove_plank_four_window","chemlib:copper_ingot_from_smelting_copper_dust","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","minecraft:red_nether_brick_stairs","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","farmersdelight:cooking/glow_berry_custard","twigs:bloodstone","create:crafting/kinetics/red_seat","connectedglass:tinted_borderless_glass_light_blue2","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","cfm:orange_kitchen_counter","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwdoors:mangrove_mystic_door","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","create:crafting/kinetics/orange_seat","minecraft:diamond_block","handcrafted:diorite_corner_trim","mcwroofs:deepslate_roof","biomesoplenty:willow_chest_boat","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","minecraft:diorite_slab","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","paraglider:cosmetic/deku_leaf","bigreactors:blasting/graphite_from_coal","mcwdoors:mangrove_barn_glass_door","mcwpaths:dark_prismarine_windmill_weave_stairs","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","create:cut_diorite_brick_slab_from_stone_types_diorite_stonecutting","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","cfm:stripped_mangrove_table","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","handcrafted:mangrove_counter","utilitarian:snad/snad","croptopia:shaped_bacon","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:spring_green_dye","dyenamics:wine_terracotta","cfm:dye_green_picket_gate","mcwroofs:light_gray_attic_roof","mcwroofs:mangrove_upper_lower_roof","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","mcwfurnitures:stripped_jungle_covered_desk","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwbiomesoplenty:palm_highley_gate","allthecompressed:compress/platinum_block_1x","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","ae2:network/cables/smart_green","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwwindows:stone_brick_gothic","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mekanismtools:refined_obsidian/tools/pickaxe","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:soul_campfire","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwroofs:diorite_upper_steep_roof","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","connectedglass:clear_glass_light_blue_pane2","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","utilitarian:utility/mangrove_logs_to_stairs","domum_ornamentum:green_cobblestone_extra","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:diorite_flagstone_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwlights:spruce_tiki_torch","dyenamics:bed/amber_bed_frm_white_bed","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","littlelogistics:automatic_tee_junction_rail","appflux:insulating_resin","mcwroofs:mangrove_steep_roof","silentgear:crimson_iron_ingot_from_block","connectedglass:borderless_glass_green_pane2","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mekanism:control_circuit/advanced","mekanismtools:refined_obsidian/armor/helmet","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","mcwfurnitures:mangrove_bookshelf","pneumaticcraft:kerosene_lamp","silentgear:crimson_iron_nugget","botania:lens_normal","mcwdoors:mangrove_western_door","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","cfm:dye_light_blue_picket_fence","cfm:gray_grill","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","create:crafting/kinetics/minecart_from_contraption_cart","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","mcwfurnitures:jungle_modern_chair","create:cut_calcite_brick_wall_from_stone_types_calcite_stonecutting","mekanismtools:refined_obsidian/shield","handcrafted:mangrove_cupboard","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","handcrafted:phantom_trophy","minecraft:quartz_slab","mcwroofs:cyan_terracotta_upper_steep_roof","modularrouters:modular_router","handcrafted:mangrove_side_table","mcwfurnitures:stripped_jungle_coffee_table","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","minecraft:quartz_pillar_from_quartz_block_stonecutting","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:nether_bricks_roof","minecraft:kjs/ender_pearl","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:observer","minecraft:mossy_cobblestone_wall_from_mossy_cobblestone_stonecutting","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","mcwfurnitures:mangrove_drawer","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","mcwpaths:dark_prismarine_strewn_rocky_path","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","delightful:smoking/roasted_acorn","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwpaths:andesite_honeycomb_paving","mcwwindows:birch_log_parapet","mcwbridges:sandstone_bridge","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","minecraft:diorite_wall_from_diorite_stonecutting","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","utilitix:dark_oak_shulker_boat","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","twigs:polished_tuff_bricks_from_tuff_stonecutting","handcrafted:kitchen_hood","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","create:polished_cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwroofs:black_lower_roof","mcwfurnitures:stripped_oak_desk","mcwroofs:gutter_base_blue","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","croptopia:candied_nuts","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","mcwtrpdoors:mangrove_whispering_trapdoor","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwroofs:mangrove_roof","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwroofs:andesite_lower_roof","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","create:calcite_from_stone_types_calcite_stonecutting","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","minecraft:dark_prismarine_slab","additionallanterns:blackstone_chain","silentgear:crimson_iron_dust_smelting","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","connectedglass:scratched_glass_lime_pane2","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","gtceu:shaped/block_compress_platinum","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","productivetrees:sawmill","railcraft:tunnel_bore","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","everythingcopper:copper_anvil","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","utilitix:crossing_rail","botania:green_petal_block","supplementaries:candle_holders/candle_holder_green_dye","railcraft:electric_locomotive","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","minecraft:conduit","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","deeperdarker:bloom_chest_boat","pneumaticcraft:wall_lamp_purple","mcwwindows:dark_oak_plank_window","minecraft:sticky_piston","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","minecraft:powered_rail","sgjourney:sandstone_hieroglyphs","mcwwindows:diorite_pane_window","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwroofs:magenta_concrete_upper_lower_roof","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","aether:stone_pickaxe_repairing","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","ad_astra:light_blue_industrial_lamp","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","minecraft:gray_terracotta","minecraft:comparator","handcrafted:dark_oak_corner_trim","mcwwindows:white_mosaic_glass_pane","mcwroofs:mangrove_planks_upper_steep_roof","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","dyenamics:peach_terracotta","minecraft:fishing_rod","rftoolspower:powercell_card","xnet:connector_yellow_dye","minecraft:terracotta","mcwwindows:acacia_blinds","mcwfurnitures:mangrove_chair","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","minecraft:dye_light_blue_bed","supplementaries:timber_frame","aether:diamond_pickaxe_repairing","pneumaticcraft:seismic_sensor","twigs:copper_pillar_stonecutting","immersiveengineering:crafting/minecart_woodenbarrel","blue_skies:lunar_bookshelf","sophisticatedstorage:packing_tape","supplementaries:sign_post_mangrove","mcwroofs:cyan_terracotta_top_roof","cfm:stripped_mangrove_coffee_table","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwlights:double_street_lamp","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","mcwroofs:diorite_top_roof","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","mekanism:steel_casing","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","minecraft:dye_light_blue_wool","handcrafted:sandstone_pillar_trim","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","supplementaries:globe_sepia","rftoolsbase:tablet","mcwroofs:magenta_terracotta_lower_roof","railcraft:routing_table_book","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwtrpdoors:mangrove_tropical_trapdoor","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","allthecompressed:compress/bone_block_1x","additionallanterns:bone_chain","integrateddynamics:crafting/menril_planks","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:stone_attic_roof","minecraft:mangrove_sign","mcwbridges:rope_dark_oak_bridge","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:stone_tile","handcrafted:terracotta_plate","handcrafted:calcite_corner_trim","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_mangrove_drawer","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","supplementaries:flags/flag_white","travelersbackpack:dye_light_blue_sleeping_bag","handcrafted:jungle_shelf","mcwpaths:dark_prismarine_basket_weave_paving","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","cfm:dark_oak_desk_cabinet","mcwroofs:andesite_top_roof","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","utilitix:jungle_shulker_boat","cfm:red_kitchen_counter","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwbridges:diorite_bridge","minecraft:furnace_minecart","mcwlights:magenta_paper_lamp","allthemods:rechiseled/chisel","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_mangrove_bookshelf_cupboard","cfm:orange_kitchen_drawer","enderio:cold_fire_igniter","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","minecraft:leather_boots","handcrafted:sandstone_corner_trim","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","mcwpaths:diorite_crystal_floor_path","mcwbridges:mossy_stone_brick_bridge","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","mcwlights:pink_paper_lamp","mcwroofs:mangrove_lower_roof","pneumaticcraft:wall_lamp_inverted_light_blue","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","minecraft:red_nether_brick_slab_from_red_nether_bricks_stonecutting","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","mcwfurnitures:mangrove_glass_table","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","mcwpaths:diorite_strewn_rocky_path","pneumaticcraft:stomp_upgrade","mcwpaths:dark_prismarine_flagstone_slab","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","forbidden_arcanus:lens_of_veritatis","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","nethersdelight:nether_brick_smoker","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","botania:pool_minecart","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","railcraft:steel_pickaxe","mcwroofs:jungle_planks_roof","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","sfm:cable","silentgear:raw_crimson_iron_from_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbridges:mossy_cobblestone_bridge_stair","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","create:mangrove_window","mcwfences:modern_granite_wall","create:jungle_window","twigs:mossy_cobblestone_brick_slab_from_mossy_cobblestone_stonecutting","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","mcwtrpdoors:dark_oak_blossom_trapdoor","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","cfm:mangrove_table","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:dark_oak_fence","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","create:polished_cut_diorite_wall_from_stone_types_diorite_stonecutting","travelersbackpack:sandstone_smithing","mcwwindows:bricks_window","cfm:mangrove_cabinet","twigs:paper_lantern","railcraft:goggles","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","travelersbackpack:iron","minecraft:smooth_sandstone_stairs","mcwroofs:oak_planks_upper_steep_roof","mcwbridges:mangrove_bridge_pier","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","pneumaticcraft:liquid_compressor","create:polished_cut_calcite_wall_from_stone_types_calcite_stonecutting","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:mossy_stone_running_bond","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","handcrafted:dark_oak_bench","mcwwindows:granite_louvered_shutter","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","immersiveengineering:crafting/blueprint_bullets","mcwroofs:magenta_concrete_lower_roof","supplementaries:dispenser_minecart","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","sophisticatedbackpacks:backpack","forbidden_arcanus:rune_block_from_rune","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","utilitix:piston_controller_rail","croptopia:roasted_smoking","mcwbiomesoplenty:hellbark_curved_gate","mcwpaths:mossy_cobblestone_basket_weave_paving","minecraft:green_terracotta","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","gtceu:shapeless/blaze_rod_to_powder","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","create:cut_diorite_brick_stairs_from_stone_types_diorite_stonecutting","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","handcrafted:mangrove_drawer","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","minecraft:mossy_cobblestone_stairs","mcwfences:acacia_horse_fence","mcwpaths:diorite_windmill_weave_slab","mcwfences:spruce_hedge","sophisticatedstorage:dark_oak_chest","mcwfences:jungle_hedge","mcwbiomesoplenty:willow_plank_window2","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mekanismtools:osmium/tools/sword","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","securitycraft:reinforced_andesite","aether:aether_tune_enchanting","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","allthecompressed:compress/deepslate_1x","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","botania:manasteel_shovel","handcrafted:calcite_pillar_trim","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwtrpdoors:mangrove_beach_trapdoor","mcwroofs:white_steep_roof","mcwpaths:cobbled_deepslate_crystal_floor_slab","handcrafted:mangrove_table","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","mcwpaths:cobbled_deepslate_crystal_floor","supplementaries:candle_holders/candle_holder_blue","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","mcwpaths:blackstone_crystal_floor","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","cfm:dark_oak_crate","minecraft:gold_nugget","twigs:mossy_cobblestone_bricks_stonecutting","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","mcwwindows:stripped_dark_oak_log_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","cfm:stripped_jungle_desk_cabinet","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","securitycraft:display_case","minecraft:jungle_wood","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","allthecompressed:compress/andesite_1x","mcwwindows:diorite_four_window","minecraft:stone_slab","pneumaticcraft:compressed_bricks","mcwroofs:red_nether_bricks_upper_lower_roof","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","create:small_calcite_brick_wall_from_stone_types_calcite_stonecutting","handcrafted:dark_oak_pillar_trim","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","travelersbackpack:backpack_tank","minecraft:jungle_stairs","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","minecraft:light_blue_stained_glass_pane_from_glass_pane","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","dyenamics:bed/mint_bed_frm_white_bed","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","cfm:mangrove_upgraded_gate","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","silentgear:crimson_iron_ingot_from_nugget","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","twigs:crimson_roots_paper_lantern","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:fletching_table","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","minecraft:dye_lime_bed","securitycraft:mine","pneumaticcraft:compressed_stone_slab","minecraft:cooked_salmon","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","minecraft:mossy_cobblestone_wall","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","minecraft:oak_chest_boat","mcwpaths:mossy_stone_flagstone_path","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfurnitures:mangrove_large_drawer","mcwfences:modern_mud_brick_wall","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","mcwroofs:mangrove_top_roof","minecraft:diorite_slab_from_diorite_stonecutting","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","create:crafting/kinetics/yellow_seat","pneumaticcraft:logistics_core","mcwpaths:andesite_windmill_weave_path","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","minecraft:mangrove_boat","minecraft:bone_meal_from_bone_block","mcwroofs:green_terracotta_steep_roof","mcwdoors:garage_silver_door","comforts:hammock_white","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","cfm:mangrove_chair","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","cfm:stripped_mangrove_crate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","farmersdelight:cooking/vegetable_soup","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwtrpdoors:mangrove_mystic_trapdoor","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","minecraft:dark_oak_trapdoor","mcwfences:iron_cheval_de_frise","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","ironfurnaces:furnaces/diamond_furnace","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","chimes:amethyst_chimes","mcwwindows:stone_brick_arrow_slit","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","allthecompressed:compress/mangrove_planks_1x","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","alltheores:osmium_dust_from_hammer_ingot_crushing","pneumaticcraft:camo_applicator","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","create:calcite_pillar_from_stone_types_calcite_stonecutting","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","rftoolsutility:counter_module","minecraft:amethyst_block","cfm:stripped_jungle_crate","minecraft:oak_door","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","mcwroofs:black_roof_block","connectedglass:tinted_borderless_glass_lime2","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","minecraft:polished_diorite_stairs_from_diorite_stonecutting","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","handcrafted:oak_corner_trim","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","mcwbiomesoplenty:magic_highley_gate","create:cut_diorite_brick_wall_from_stone_types_diorite_stonecutting","mcwfurnitures:stripped_mangrove_coffee_table","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","dyenamics:mint_terracotta","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","create:crafting/kinetics/furnace_minecart_from_contraption_cart","securitycraft:limited_use_keycard","delightful:knives/refined_obsidian_knife","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","biomesoplenty:fir_chest_boat","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","supplementaries:wrench","minecraft:bow","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aether:skyroot_smithing_table","mekanismtools:osmium/armor/leggings","aquaculture:brown_mushroom_from_fish","railways:jukeboxcart","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","botania:mushroom_stew","minecraft:andesite_stairs","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwfurnitures:mangrove_modern_wardrobe","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","mcwpaths:diorite_dumble_paving","minecraft:jungle_slab","twigs:polished_calcite_stonecutting","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","mcwpaths:mossy_stone_crystal_floor_slab","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","handcrafted:mangrove_pillar_trim","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","mcwroofs:red_nether_bricks_attic_roof","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","supplementaries:integrateddynamics/sign_post_menril","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","dyenamics:bed/aquamarine_bed_frm_white_bed","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","botania:ghost_rail","minecraft:firework_rocket_simple","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","mcwbridges:balustrade_nether_bricks_bridge","allthecompressed:compress/mangrove_log_1x","minecraft:andesite","handcrafted:mangrove_shelf","pneumaticcraft:wall_lamp_inverted_brown","mcwroofs:andesite_roof","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","rftoolsbase:infused_enderpearl","supplementaries:pedestal","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","dyenamics:rose_terracotta","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwdoors:jungle_tropical_door","minecraft:stone","twigs:lamp","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","mcwtrpdoors:mangrove_glass_trapdoor","minecraft:blackstone_stairs","handcrafted:dark_oak_couch","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","mcwbridges:mossy_cobblestone_bridge","travelersbackpack:lapis","mcwroofs:red_nether_bricks_steep_roof","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","domum_ornamentum:cream_stone_bricks","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","mcwdoors:oak_barn_door","botania:alchemy_catalyst","mcwbridges:mangrove_log_bridge_middle","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","create:layered_diorite_from_stone_types_diorite_stonecutting","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","mcwroofs:cyan_concrete_attic_roof","silentgear:crimson_iron_block","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbridges:mossy_stone_bridge_pier","cfm:stripped_oak_bedside_cabinet","mcwbiomesoplenty:empyreal_horse_fence","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","gtceu:shaped/mortar_iron","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","additionallanterns:obsidian_chain","minecraft:ender_eye","supplementaries:blackstone_lamp","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","mcwwindows:bricks_four_window","railcraft:receiver_circuit","minecraft:quartz_bricks_from_quartz_block_stonecutting","mcwdoors:mangrove_japanese_door","minecraft:green_candle","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","rftoolsbase:manual","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","mekanismtools:osmium/armor/helmet","supplementaries:stonecutting/stone_tile","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwfurnitures:mangrove_table","dyenamics:conifer_concrete_powder","enderio:dark_steel_trapdoor","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","ad_astra:small_green_industrial_lamp","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","minecraft:hopper_minecart","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","mcwpaths:mossy_stone_flagstone_stairs","mcwlights:yellow_paper_lamp","ironfurnaces:furnaces/copper_furnace","additionallanterns:diorite_chain","domum_ornamentum:cream_bricks","allthemodium:allthemodium_ingot_from_raw_blasting","mcwroofs:red_nether_bricks_roof","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","mcwwindows:black_curtain","dyenamics:wine_stained_glass","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwtrpdoors:mangrove_barrel_trapdoor","mcwpaths:dark_prismarine_windmill_weave_path","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","minecraft:diorite_stairs","minecraft:mangrove_pressure_plate","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","additionallanterns:dark_prismarine_chain","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwwindows:cherry_pane_window","mcwbiomesoplenty:empyreal_pyramid_gate","twigs:calcite_stairs","mcwdoors:garage_white_door","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","mcwroofs:oak_planks_top_roof","biomesoplenty:hellbark_boat","alltheores:zinc_dust_from_hammer_crushing","railcraft:steel_chestplate","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwroofs:oak_planks_roof","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","ironfurnaces:furnaces/gold_furnace","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","aether:chainmail_boots_repairing","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:nether_brick_slab","mcwdoors:mangrove_stable_door","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","railcraft:track_relayer","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","mcwlights:brown_paper_lamp","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwpaths:mossy_stone_running_bond_path","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","create:cut_tuff_from_stone_types_tuff_stonecutting","pneumaticcraft:logistics_configurator","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","mcwroofs:dark_oak_planks_roof","mcwfurnitures:stripped_jungle_desk","mcwwindows:birch_four_window","biomesoplenty:fir_boat","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","handcrafted:spider_trophy","mcwpaths:dark_prismarine_dumble_paving","allthecompressed:compress/calcite_1x","alltheores:gold_dust_from_hammer_crushing","minecraft:bone_block","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","ae2:network/cables/dense_smart_green","pneumaticcraft:wall_lamp_white","mcwroofs:red_terracotta_lower_roof","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","create:oak_window","mcwdoors:mangrove_four_panel_door","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","create:crafting/kinetics/light_gray_seat","create:cut_diorite_from_stone_types_diorite_stonecutting","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","mcwpaths:cobbled_deepslate_running_bond_slab","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","twigs:polished_rhyolite_stonecutting","handcrafted:deepslate_corner_trim","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","handcrafted:dark_oak_shelf","botania:manasteel_leggings","minecraft:cut_copper_slab_from_copper_block_stonecutting","aether:skyroot_fletching_table","sophisticatedstorage:copper_to_iron_tier_upgrade","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","minecraft:cyan_dye_from_pitcher_plant","rftoolsutility:screen_link","minecraft:leather_helmet","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mekanism:fluid_tank/basic","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","minecraft:nether_brick_stairs","pneumaticcraft:crop_support","supplementaries:bubble_blower","naturalist:glow_goop","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwroofs:black_steep_roof","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","minecraft:pink_terracotta","mcwbiomesoplenty:mahogany_plank_pane_window","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwwindows:dark_oak_blinds","mcwpaths:cobblestone_dumble_paving","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","mcwdoors:mangrove_barn_door","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","mcwtrpdoors:mangrove_barred_trapdoor","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","allthecompressed:compress/quartz_block_1x","railcraft:standard_rail_from_rail","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","minecraft:mangrove_wood","additionallanterns:mossy_cobblestone_chain","botania:bauble_box","mcwroofs:black_top_roof","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","minecraft:mojang_banner_pattern","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","cfm:oak_kitchen_counter","minecraft:golden_hoe","minecraft:fire_charge","mcwpaths:diorite_running_bond_stairs","utilitarian:utility/mangrove_logs_to_trapdoors","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","mcwroofs:mangrove_planks_upper_lower_roof","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:dye_green_bed","alltheores:platinum_dust_from_hammer_crushing","mcwpaths:dark_prismarine_clover_paving","railcraft:any_detector","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","mcwroofs:stone_bricks_upper_steep_roof","biomesoplenty:palm_chest_boat","minecraft:stone_button","enderio:dark_steel_door","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","pneumaticcraft:compressed_iron_helmet","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","mcwpaths:diorite_running_bond_slab","pneumaticcraft:regulator_tube_module","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:purpur_block","travelersbackpack:dye_lime_sleeping_bag","railcraft:brass_ingot_crafted_with_ingots","minecraft:cut_copper_stairs_from_copper_block_stonecutting","mcwpaths:sandstone_running_bond_stairs","minecraft:polished_blackstone_slab","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","minecraft:nether_brick_wall","pneumaticcraft:transfer_gadget","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwdoors:jungle_bark_glass_door","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","quark:building/crafting/furnaces/deepslate_furnace","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","minecraft:lime_candle","handcrafted:jungle_bench","minecraft:red_dye_from_beetroot","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","littlelogistics:barrel_barge","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","alltheores:nickel_dust_from_hammer_crushing","create:polished_cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","pneumaticcraft:vortex_tube","mcwwindows:warped_louvered_shutter","undergarden:wigglewood_chest_boat","xnet:connector_routing","blue_skies:glowing_nature_stone","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","dyenamics:aquamarine_terracotta","handcrafted:dark_oak_drawer","travelersbackpack:cake","create:cut_diorite_wall_from_stone_types_diorite_stonecutting","mcwroofs:green_terracotta_attic_roof","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","immersiveengineering:crafting/shield","utilitix:directional_highspeed_rail","mcwpaths:mangrove_planks_path","farmersdelight:stuffed_potato","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","create:small_diorite_bricks_from_stone_types_diorite_stonecutting","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","cfm:warped_mail_box","xnet:antenna_base","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","allthecompressed:compress/diorite_1x","mcwroofs:oak_roof","cfm:jungle_desk","minecraft:mangrove_chest_boat","minecraft:iron_hoe","aether:crossbow_repairing","minecraft:iron_ingot_from_iron_block","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","mcwroofs:light_blue_concrete_lower_roof","mcwfurnitures:jungle_drawer","mcwroofs:purple_striped_awning","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","minecraft:kjs/buildinggadgets2_gadget_building","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","connectedglass:clear_glass_light_blue2","cfm:stripped_mangrove_desk","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","ad_astra:small_light_blue_industrial_lamp","mcwfences:blackstone_brick_railing_gate","mcwroofs:dark_oak_planks_upper_lower_roof","mcwbiomesoplenty:maple_curved_gate","mcwroofs:black_terracotta_roof","cfm:stripped_mangrove_cabinet","mcwpaths:mossy_stone_running_bond_slab","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","mcwfurnitures:mangrove_lower_triple_drawer","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","mcwdoors:mangrove_cottage_door","additionallanterns:andesite_lantern","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","mcwdoors:mangrove_japanese2_door","cfm:dark_oak_coffee_table","mcwtrpdoors:print_swamp","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwtrpdoors:mangrove_paper_trapdoor","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwwindows:dark_prismarine_brick_gothic","minecraft:white_terracotta","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","mcwroofs:mangrove_planks_lower_roof","mcwfurnitures:oak_double_drawer_counter","cfm:blue_grill","mcwlights:golden_chandelier","minecraft:armor_stand","mcwpaths:stone_flagstone_stairs","enderio:glider_wing","cfm:stripped_birch_kitchen_drawer","botania:petal_green_double","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","minecraft:mangrove_slab","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","mcwroofs:nether_bricks_upper_lower_roof","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","xnet:netcable_yellow","mcwroofs:blue_terracotta_roof","mcwlights:tavern_wall_lantern","mcwpaths:blackstone_crystal_floor_stairs","mcwfurnitures:jungle_wardrobe","dyenamics:bed/rose_bed","dyenamics:navy_wool","mcwwindows:magenta_mosaic_glass_pane","alltheores:platinum_gear","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","create:cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","croptopia:potato_chips","minecraft:chiseled_stone_bricks_stone_from_stonecutting","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","mekanismtools:refined_obsidian/armor/chestplate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwbridges:dark_oak_rail_bridge","mcwdoors:oak_cottage_door","create:diorite_from_stone_types_diorite_stonecutting","immersiveengineering:crafting/torch","mcwbridges:diorite_bridge_stair","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","delightful:food/baklava","create:small_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","create:small_diorite_brick_stairs_from_stone_types_diorite_stonecutting","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","minecraft:light_blue_candle","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","create:small_diorite_brick_wall_from_stone_types_diorite_stonecutting","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","littlelogistics:junction_rail","twilightforest:twilight_oak_chest_boat","dyenamics:ultramarine_terracotta","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","cfm:red_grill","create:small_calcite_brick_slab_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwfurnitures:mangrove_double_wardrobe","mcwbiomesoplenty:stripped_palm_log_window2","mcwroofs:mangrove_upper_steep_roof","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","minecraft:smooth_sandstone_slab","supplementaries:flags/flag_light_blue","minecraft:rail","mcwpaths:cobbled_deepslate_running_bond","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","croptopia:chicken_and_dumplings","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","connectedglass:borderless_glass_light_blue2","mcwtrpdoors:jungle_paper_trapdoor","mcwroofs:diorite_upper_lower_roof","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","mcwlights:upgraded_torch","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","alltheores:smelting_dust/osmium_ingot","cfm:mangrove_upgraded_fence","nethersdelight:hoglin_sirloin_from_campfire_cooking","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","handcrafted:diorite_pillar_trim","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwwindows:crimson_shutter","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","alltheores:netherite_dust_from_hammer_crushing","mcwroofs:orange_concrete_top_roof","bigreactors:turbine/basic/passivetap_fe","mcwpaths:dark_prismarine_windmill_weave_slab","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwdoors:mangrove_classic_door","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","pneumaticcraft:wall_lamp_pink","handcrafted:terracotta_wide_pot","mcwfurnitures:stripped_mangrove_counter","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwroofs:purple_terracotta_upper_lower_roof","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","biomesoplenty:orange_dye_from_burning_blossom","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","blue_skies:cake_compat","minecraft:mangrove_planks","mcwpaths:mossy_stone_windmill_weave_path","minecraft:gold_ingot_from_blasting_raw_gold","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","pneumaticcraft:liquid_hopper","gtceu:smelting/smelt_dust_iron_to_ingot","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","mcwroofs:dark_oak_planks_top_roof","additionallanterns:normal_nether_bricks_chain","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwbridges:andesite_bridge_pier","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:fried_chicken","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime2","create:deepslate_from_stone_types_deepslate_stonecutting","twigs:calcite_wall","cfm:stripped_oak_chair","xnet:netcable_blue","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","mcwwindows:oak_shutter","mcwroofs:lime_concrete_steep_roof","cfm:rock_path","minecraft:wooden_hoe","mekanismgenerators:generator/heat","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","botania:dye_green","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwpaths:diorite_honeycomb_paving","mcwroofs:nether_bricks_top_roof","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwroofs:nether_bricks_lower_roof","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","aquaculture:stone_fillet_knife","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","delightful:food/cooking/nut_butter_bottle","cfm:stripped_mangrove_park_bench","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","mcwwindows:crimson_planks_four_window","minecraft:piston","mcwfurnitures:stripped_mangrove_wardrobe","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","allthemodium:allthemodium_ingot_from_raw_smelting","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","enderio:dark_steel_bars","croptopia:apple_sapling","mcwtrpdoors:mangrove_cottage_trapdoor","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","minecraft:diorite_stairs_from_diorite_stonecutting","mcwfences:spruce_horse_fence","handcrafted:oak_shelf","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:polished_andesite_stairs_from_andesite_stonecutting","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","silentgear:raw_crimson_iron_block","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","minecraft:dark_oak_pressure_plate","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwwindows:birch_plank_four_window","mcwfences:majestic_metal_fence_gate","packingtape:tape","mcwpaths:blackstone_flagstone_path","create:cut_diorite_stairs_from_stone_types_diorite_stonecutting","rftoolsbuilder:vehicle_control_module","travelersbackpack:sheep","silentgear:crimson_iron_dust","mcwroofs:red_terracotta_upper_steep_roof","enderio:dark_steel_nugget_to_ingot","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","handcrafted:dark_oak_dining_bench","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","xnet:antenna","mcwfurnitures:jungle_stool_chair","create:polished_cut_diorite_slab_from_stone_types_diorite_stonecutting","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","mcwpaths:mossy_stone_crystal_floor_stairs","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","handcrafted:pillager_trophy","sfm:water_tank","create:polished_cut_calcite_from_stone_types_calcite_stonecutting","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","cfm:dye_lime_picket_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","pneumaticcraft:pressure_gauge","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","pylons:expulsion_pylon","botania:manasteel_chestplate","create:dark_oak_window","mcwfences:bamboo_highley_gate","mcwpaths:dark_prismarine_windmill_weave","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","dyenamics:bed/ultramarine_bed_frm_white_bed","mcwroofs:jungle_roof","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","alltheores:bronze_dust_from_alloy_blending","create:small_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","mcwwindows:white_curtain","mcwroofs:white_terracotta_roof","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","cfm:light_gray_grill","create:crafting/kinetics/black_seat","cfm:oak_desk","minecraft:map","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","connectedglass:clear_glass_lime_pane2","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","mcwfurnitures:mangrove_stool_chair","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","minecolonies:potato_soup","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","create:crafting/kinetics/copper_valve_handle_from_others","botania:dreamwood_wand","immersiveengineering:crafting/minecart_metalbarrel","mcwpaths:diorite_running_bond_path","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","minecraft:sandstone_wall","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:mangrove_modern_chair","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","mcwtrpdoors:mangrove_four_panel_trapdoor","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwpaths:andesite_crystal_floor_stairs","mcwwindows:oak_plank_four_window","mcwroofs:sandstone_lower_roof","domum_ornamentum:brown_bricks","cfm:red_cooler","handcrafted:jungle_dining_bench","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","mcwroofs:red_nether_bricks_upper_steep_roof","xnet:facade","mekanismtools:osmium/tools/pickaxe","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","mcwpaths:sandstone_basket_weave_paving","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","silentgear:crimson_iron_raw_ore_blasting","undergarden:grongle_boat","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","littlelogistics:automatic_switch_rail","minecraft:carrot_on_a_stick","quark:tweaks/crafting/utility/tools/stone_axe","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","mekanismtools:refined_obsidian/armor/leggings","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwpaths:diorite_basket_weave_paving","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","railcraft:track_undercutter","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:red_nether_bricks_top_roof","mcwroofs:orange_terracotta_steep_roof","enderio:dark_steel_nugget","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","croptopia:fruit_smoothie","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","railcraft:steel_shears","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:mossy_cobblestone_slab_from_mossy_cobblestone_stonecutting","minecraft:diamond_helmet","mcwfurnitures:stripped_mangrove_end_table","securitycraft:reinforced_purple_stained_glass_pane_from_dye","mcwroofs:dark_oak_planks_steep_roof","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","connectedglass:borderless_glass_lime_pane2","minecraft:dark_oak_fence_gate","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","bigreactors:energizer/computerport","allthemodium:allthemodium_apple","mcwroofs:lime_terracotta_upper_lower_roof","mekanism:processing/osmium/raw/from_raw_block","mcwpaths:mossy_stone_running_bond_stairs","minecraft:item_frame","create:cut_deepslate_brick_stairs_from_stone_types_deepslate_stonecutting","itemcollectors:basic_collector","handcrafted:dark_oak_fancy_bed","minecraft:loom","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","ad_astra:small_lime_industrial_lamp","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","mekanism:transmitter/universal_cable/basic","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","alltheores:brass_dust_from_alloy_blending","dyenamics:wine_wool","mcwroofs:sandstone_attic_roof","handcrafted:skeleton_trophy","handcrafted:golden_wide_pot","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","botania:green_shiny_flower","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","handcrafted:dark_oak_counter","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwroofs:pink_terracotta_steep_roof","railcraft:steam_locomotive","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","mekanismtools:osmium/armor/chestplate","utilitarian:utility/mangrove_logs_to_pressure_plates","computercraft:computer_advanced","minecraft:polished_blackstone","mcwpaths:diorite_crystal_floor_slab","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","biomesoplenty:red_dye_from_waterlily","mcwlights:copper_chandelier","create:cut_deepslate_slab_from_stone_types_deepslate_stonecutting","dyenamics:bed/maroon_bed_frm_white_bed","reliquary:uncrafting/slime_ball","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwpaths:dark_prismarine_diamond_paving","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwpaths:dark_prismarine_flagstone","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","mcwpaths:dark_oak_planks_path","xnet:netcable_red","cfm:brown_cooler","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","mcwwindows:dark_prismarine_parapet","rftoolsutility:inventoryplus_module","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","mcwfurnitures:jungle_end_table","undergarden:shard_torch","minecraft:light_blue_stained_glass","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","mcwpaths:dark_prismarine_honeycomb_paving","paraglider:paraglider","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","mcwpaths:dark_prismarine_flagstone_path","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","mcwroofs:blue_striped_awning","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","alltheores:signalum_dust_from_alloy_blending","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","minecraft:dark_prismarine_stairs","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","create:crafting/kinetics/fluid_valve","supplementaries:crystal_display","minecraft:sandstone_stairs","mcwroofs:mangrove_planks_attic_roof","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","minecraft:chest_minecart","create:crafting/kinetics/white_seat","paraglider:cosmetic/paraglider","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","mcwroofs:white_upper_lower_roof","minecraft:magenta_terracotta","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","aether:golden_sword_repairing","quark:tweaks/crafting/utility/chests/mixed_chest_wood_but_without_exclusions","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","create:cut_deepslate_bricks_from_stone_types_deepslate_stonecutting","cfm:mangrove_bedside_cabinet","handcrafted:white_cushion","mcwdoors:metal_warning_door","utilitarian:utility/oak_logs_to_doors","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gray_terracotta_attic_roof","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","mcwroofs:mangrove_attic_roof","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","minecraft:end_stone_bricks","mcwdoors:jungle_stable_head_door","croptopia:egg_roll","railcraft:iron_gear","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","dyenamics:bed/lavender_bed_frm_white_bed","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","minecraft:lime_dye","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","create:framed_glass_from_glass_colorless_stonecutting","botania:quartz_sunny","sophisticatedstorage:jungle_barrel","railcraft:signal_lamp","minecraft:mangrove_door","mcwpaths:diorite_windmill_weave_stairs","mcwpaths:diorite_diamond_paving","handcrafted:dark_oak_chair","cfm:jungle_blinds","tombstone:bone_needle","mcwpaths:mossy_stone_windmill_weave_slab","minecraft:stone_brick_wall","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","minecraft:cyan_dye","mcwwindows:acacia_window2","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","minecraft:gold_block","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","handcrafted:jungle_counter","cfm:stripped_mangrove_desk_cabinet","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","handcrafted:mangrove_desk","mcwfences:warped_curved_gate","pneumaticcraft:compressed_bricks_from_tile","mcwdoors:oak_stable_door","ironfurnaces:augments/augment_factory","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","mcwpaths:diorite_windmill_weave_path","cfm:oak_upgraded_gate","dyenamics:bed/honey_bed_frm_white_bed","mcwroofs:light_gray_terracotta_roof","minecraft:copper_ingot_from_blasting_raw_copper","mcwroofs:mangrove_planks_roof","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","allthecompressed:compress/clay_1x","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","mcwpaths:dark_prismarine_square_paving","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","mcwfurnitures:mangrove_drawer_counter","additionallanterns:copper_lantern","twilightforest:canopy_chest_boat","minecraft:dark_oak_button","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwpaths:diorite_flagstone","mcwbiomesoplenty:fir_pane_window","mcwpaths:dark_prismarine_running_bond","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","handcrafted:mangrove_chair","supplementaries:deepslate_lamp","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","utilitix:ender_cart","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","utilitix:stonecutter_cart","cfm:dye_green_picket_fence","mcwwindows:oak_plank_window","nethersdelight:hoglin_sirloin","mcwbridges:dark_oak_log_bridge_middle","mcwwindows:oak_log_parapet","minecraft:mangrove_fence_gate","mcwfurnitures:stripped_mangrove_drawer_counter","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","mcwroofs:mangrove_planks_top_roof","travelersbackpack:spider_smithing","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","allthecompressed:compress/jungle_planks_1x","alltheores:platinum_dust_from_hammer_ingot_crushing","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","minecraft:dark_oak_door","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwpaths:mossy_cobblestone_diamond_paving","cfm:dark_oak_cabinet","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","deeperdarker:warden_upgrade_smithing_template","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","alltheores:platinum_plate","mcwroofs:orange_terracotta_upper_steep_roof","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","pneumaticcraft:reinforced_brick_wall","farmersdelight:steak_and_potatoes","cfm:dark_oak_kitchen_counter","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","mcwpaths:sandstone_crystal_floor_stairs","mcwfurnitures:stripped_mangrove_triple_drawer","mcwtrpdoors:mangrove_classic_trapdoor","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","cfm:dark_oak_desk","securitycraft:reinforced_gray_stained_glass_pane_from_glass","mcwroofs:black_roof","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","mcwfurnitures:stripped_mangrove_modern_wardrobe","mcwbiomesoplenty:maple_four_window","cfm:blue_kitchen_counter","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","silentgear:bort_block","gtceu:shapeless/decompress_platinum_from_ore_block","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","create:crafting/kinetics/item_vault","mcwfences:warped_highley_gate","pneumaticcraft:minigun","supplementaries:flags/flag_cyan","mcwpaths:dark_prismarine_crystal_floor_path","supplementaries:statue","immersiveengineering:crafting/tinted_glass_lead_wire","minecraft:dye_green_carpet","cfm:pink_grill","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwpaths:diorite_running_bond","mcwwindows:granite_window2","enderio:dark_steel_grinding_ball","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwfurnitures:mangrove_counter","minecraft:emerald","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","waystones:waystone","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsbase:machine_base","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","mcwpaths:blackstone_basket_weave_paving","pneumaticcraft:pressure_chamber_wall","minecraft:glass","allthecompressed:compress/gold_block_1x","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","handcrafted:dark_oak_nightstand","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","alltheores:tin_dust_from_hammer_crushing","domum_ornamentum:gray_floating_carpet","cfm:stripped_jungle_park_bench","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","dyenamics:bed/peach_bed_frm_white_bed","minecraft:light_blue_concrete_powder","minecraft:smooth_sandstone_stairs_from_smooth_sandstone_stonecutting","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","enderio:dark_steel_ladder","minecraft:cyan_terracotta","mcwfurnitures:mangrove_modern_desk","mcwwindows:light_blue_mosaic_glass","ad_astra:green_industrial_lamp","mcwlights:rustic_torch","minecraft:mangrove_fence","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","cfm:oak_upgraded_fence","twigs:mossy_cobblestone_bricks","railcraft:steel_gear","mcwbridges:mangrove_rail_bridge","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","minecraft:stone_bricks","mcwbiomesoplenty:jacaranda_wired_fence","mcwdoors:mangrove_glass_door","mcwdoors:mangrove_bamboo_door","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","minecraft:spruce_chest_boat","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","mcwpaths:andesite_flagstone","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:dead_plank_window2","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","create:small_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","pylons:potion_filter","mcwroofs:diorite_steep_roof","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","create:crafting/kinetics/brown_seat","cfm:stripped_mangrove_kitchen_sink_light","littlelogistics:energy_tug","pneumaticcraft:wall_lamp_light_gray","handcrafted:quartz_corner_trim","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwroofs:light_gray_concrete_lower_roof","mcwfurnitures:stripped_mangrove_chair","twigs:rhyolite_slab","immersiveengineering:crafting/stick_iron","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","mcwpaths:stone_crystal_floor_path","sophisticatedstorage:mangrove_chest","twigs:stone_column","minecraft:lectern","sophisticatedstorage:dark_oak_limited_barrel_1","allthecompressed:compress/dark_oak_planks_1x","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","sophisticatedstorage:dark_oak_limited_barrel_4","sophisticatedstorage:dark_oak_limited_barrel_3","sophisticatedstorage:dark_oak_limited_barrel_2","silentgear:glittery_dust","mcwbiomesoplenty:hellbark_plank_window2","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","mcwwindows:cyan_curtain","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:stripped_jungle_glass_table","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","silentgear:crimson_iron_raw_ore_smelting","pneumaticcraft:gas_lift","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfurnitures:mangrove_triple_drawer","allthecompressed:compress/tuff_1x","minecraft:dye_lime_wool","mcwroofs:gray_terracotta_upper_steep_roof","farmersdelight:mixed_salad","sfm:fancy_to_cable","aquaculture:heavy_hook","biomesoplenty:dead_chest_boat","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwwindows:acacia_window","cfm:mangrove_blinds","mcwroofs:green_terracotta_roof","croptopia:food_press","railcraft:mob_detector","blue_skies:frostbright_bookshelf","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwpaths:dark_prismarine_running_bond_stairs","pneumaticcraft:small_tank","ae2:network/cables/glass_green","mcwroofs:orange_concrete_lower_roof","create:cut_deepslate_wall_from_stone_types_deepslate_stonecutting","farmersdelight:nether_salad","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","silentgear:bort_from_block","mcwwindows:dark_oak_pane_window","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","pneumaticcraft:reinforced_brick_pillar","create:cut_calcite_brick_stairs_from_stone_types_calcite_stonecutting","supplementaries:boat_jar","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","botania:dodge_ring","cfm:dark_oak_bedside_cabinet","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","dyenamics:bed/icy_blue_bed_frm_white_bed","mcwfences:oak_highley_gate","minecraft:dark_prismarine_slab_from_dark_prismarine_stonecutting","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","minecraft:pink_concrete_powder","mcwbridges:glass_bridge_pier","minecraft:cauldron","mcwbridges:nether_bricks_bridge","delightful:smelting/roasted_acorn","mcwfurnitures:mangrove_double_drawer_counter","mcwpaths:dark_prismarine_crystal_floor_stairs","allthecompressed:compress/moss_block_1x","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","silentgear:blueprint_paper","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","aether:iron_boots_repairing","mcwwindows:end_brick_gothic","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","rftoolsutility:module_template","tombstone:impregnated_diamond","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","dyenamics:bed/navy_bed_frm_white_bed","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mekanismtools:refined_obsidian/tools/sword","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwroofs:jungle_planks_upper_lower_roof","supplementaries:faucet","twigs:polished_calcite_bricks_from_calcite_stonecutting","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","supplementaries:candle_holders/candle_holder_lime_dye","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","connectedglass:borderless_glass_green2","dyenamics:bed/persimmon_bed_frm_white_bed","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwroofs:stone_bricks_top_roof","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","immersiveengineering:crafting/connector_lv","mcwfences:wooden_cheval_de_frise","mcwroofs:oak_upper_lower_roof","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","ad_astra:compressor","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","connectedglass:clear_glass_lime2","farmersdelight:cooking/mushroom_rice","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","handcrafted:dark_oak_desk","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","mcwfurnitures:mangrove_lower_bookshelf_drawer","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","sophisticatedstorage:dark_oak_barrel","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","createoreexcavation:vein_finder","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","securitycraft:reinforced_tinted_glass","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","mcwdoors:mangrove_tropical_door","securitycraft:reinforced_gray_stained_glass","domum_ornamentum:mossy_cobblestone_extra","minecraft:copper_ingot_from_smelting_raw_copper","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","botania:dye_lime","sophisticatedstorage:oak_limited_barrel_1","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","create:cut_diorite_bricks_from_stone_types_diorite_stonecutting","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","dyenamics:navy_terracotta","supplementaries:hourglass","aether:chainmail_helmet_repairing","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","securitycraft:reinforced_blue_stained_glass_pane_from_glass","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","mekanism:metallurgic_infuser","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","mcwroofs:yellow_striped_awning","minecraft:light_blue_terracotta","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","minecraft:jungle_sign","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","domum_ornamentum:cactus_extra","cfm:cyan_cooler","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwpaths:diorite_crystal_floor","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwroofs:light_gray_roof","create:small_calcite_brick_stairs_from_stone_types_calcite_stonecutting","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","dyenamics:fluorescent_terracotta","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwfurnitures:mangrove_covered_desk","mcwdoors:mangrove_beach_door","travelersbackpack:redstone","minecraft:tnt_minecart","minecraft:slime_block","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","create:cut_calcite_from_stone_types_calcite_stonecutting","chimes:glass_bells","mcwwindows:iron_shutter","connectedglass:borderless_glass_light_blue_pane2","immersiveengineering:crafting/blueprint_molds","handcrafted:kitchen_hood_pipe","minecraft:brown_terracotta","mcwpaths:mossy_stone_crystal_floor","travelersbackpack:emerald","croptopia:trail_mix","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","mcwfurnitures:mangrove_wardrobe","mcwpaths:blackstone_running_bond_path","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","mcwpaths:mossy_cobblestone_square_paving","mcwfurnitures:stripped_mangrove_modern_chair","mcwpaths:sandstone_flagstone","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","mcwbridges:blackstone_bridge_pier","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","mcwpaths:blackstone_windmill_weave_path","minecraft:smooth_sandstone","minecraft:end_crystal","croptopia:shaped_nether_wart_stew","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwfurnitures:stripped_mangrove_large_drawer","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","alltheores:copper_rod","simplylight:illuminant_slab","twigs:cobblestone_bricks","railcraft:track_layer","mcwwindows:sandstone_pane_window","aquaculture:cooked_fish_fillet_from_smoking","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","minecraft:diorite_wall","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","utilitix:filter_rail","aether:diamond_gloves","minecraft:mossy_cobblestone_stairs_from_mossy_cobblestone_stonecutting","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","minecraft:mossy_cobblestone_from_moss_block","silentgear:golden_nether_banana","mcwfurnitures:mangrove_bookshelf_drawer","minecraft:cracked_stone_bricks","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","minecraft:lantern","mcwwindows:jungle_pane_window","travelersbackpack:dye_green_sleeping_bag","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","minecraft:ender_chest","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","mcwbridges:nether_bricks_bridge_pier","alltheores:gold_dust_from_hammer_ingot_crushing","utilitarian:utility/mangrove_logs_to_slabs","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","dyenamics:bed/conifer_bed_frm_white_bed","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","connectedglass:scratched_glass_green2","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","handcrafted:jungle_chair","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","aether:golden_boots_repairing","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","handcrafted:white_sheet","securitycraft:reinforced_warped_fence_gate","mcwfurnitures:stripped_mangrove_bookshelf_drawer","mcwroofs:black_terracotta_attic_roof","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","handcrafted:mangrove_dining_bench","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","minecraft:cut_copper_from_copper_block_stonecutting","handcrafted:dark_oak_side_table","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","mcwfurnitures:stripped_mangrove_double_drawer_counter","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","productivetrees:crates/red_delicious_apple_crate_unpack","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwroofs:diorite_attic_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","mcwroofs:pink_concrete_top_roof","mcwfurnitures:mangrove_end_table","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","mcwfurnitures:stripped_mangrove_cupboard_counter","aether:skyroot_lectern","mcwpaths:diorite_crystal_floor_stairs","minecraft:polished_blackstone_pressure_plate","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","handcrafted:mangrove_bench","ae2:network/cables/glass_lime","alltheores:diamond_rod","minecraft:stone_slab_from_stone_stonecutting","ae2:network/blocks/crank","mcwroofs:brown_concrete_steep_roof","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:light_blue_terracotta_upper_lower_roof","reliquary:infernal_claw","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","silentgear:crimson_iron_dust_blasting","minecraft:stone_brick_slab_from_stone_stonecutting","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwwindows:mangrove_plank_pane_window","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","farmersdelight:grilled_salmon","minecraft:stone_brick_stairs_from_stone_stonecutting","mcwroofs:brown_concrete_top_roof","securitycraft:storage_module","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwfurnitures:stripped_mangrove_double_wardrobe","minecraft:raw_copper_block","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","minecraft:polished_diorite_from_diorite_stonecutting","cfm:cyan_trampoline","pneumaticcraft:compressed_stone_from_slab","mcwroofs:white_roof","alltheores:platinum_ingot_from_raw_blasting","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","mcwfurnitures:mangrove_bookshelf_cupboard","mcwbridges:balustrade_blackstone_bridge","twigs:deepslate_column_stonecutting","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","mcwpaths:mossy_stone_strewn_rocky_path","cfm:mangrove_kitchen_counter","handcrafted:dark_oak_table","cfm:mangrove_park_bench","alltheores:iron_dust_from_hammer_crushing","minecraft:quartz_block","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","pneumaticcraft:wall_lamp_inverted_yellow","mcwpaths:diorite_flagstone_stairs","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","botania:water_ring","connectedglass:scratched_glass_lime2","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","mcwwindows:mud_brick_arrow_slit","reliquary:uncrafting/bone","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","pneumaticcraft:wall_lamp_brown","cfm:stripped_oak_mail_box","cfm:stripped_mangrove_chair","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","mcwbridges:diorite_bridge_pier","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","alltheores:diamond_dust_from_hammer_crushing","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","mcwroofs:green_concrete_upper_steep_roof","handcrafted:terracotta_medium_pot","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwpaths:andesite_crystal_floor","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","alltheores:copper_ingot_from_block","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","dyenamics:mint_dye","create:polished_cut_diorite_stairs_from_stone_types_diorite_stonecutting","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","railcraft:diamond_spike_maul","minecraft:dark_oak_stairs","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","mcwpaths:mossy_cobblestone_honeycomb_paving","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","botania:mushroom_11","botania:mushroom_10","cfm:white_kitchen_drawer","mcwtrpdoors:mangrove_barn_trapdoor","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwpaths:mossy_stone_windmill_weave","comforts:hammock_to_white","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:nether_bricks_upper_steep_roof","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","mcwdoors:jungle_glass_door","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:jacaranda_chest_boat","minecraft:red_nether_brick_wall","biomesoplenty:magic_boat","farmersdelight:cooking/beetroot_soup","reliquary:mercy_cross","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwpaths:diorite_square_paving","supplementaries:key","minecraft:blackstone_wall","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","rftoolspower:power_core1","mcwroofs:white_concrete_upper_steep_roof","mcwpaths:blackstone_windmill_weave_stairs","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:cobbled_deepslate_lantern","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","cfm:stripped_mangrove_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","pneumaticcraft:wall_lamp_inverted_gray","dyenamics:bed/cherenkov_bed_frm_white_bed","handcrafted:jungle_fancy_bed","minecraft:dye_lime_carpet","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","mcwfurnitures:stripped_mangrove_desk","supplementaries:sugar_cube","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","allthemodium:allthemodium_ingot_from_dust_smelting","mcwbiomesoplenty:magic_window2","immersiveengineering:crafting/minecart_reinforcedcrate","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","mekanismtools:osmium/armor/boots","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","create:polished_cut_calcite_stairs_from_stone_types_calcite_stonecutting","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","croptopia:mortar_and_pestle","mcwbridges:rope_mangrove_bridge","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwfurnitures:stripped_mangrove_double_drawer","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","railcraft:iron_spike_maul","cfm:mangrove_coffee_table","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","create:polished_cut_deepslate_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","minecraft:dye_green_wool","mcwbiomesoplenty:fir_four_window","everythingcopper:copper_axe","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","connectedglass:scratched_glass_green_pane2","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","create:cut_calcite_brick_slab_from_stone_types_calcite_stonecutting","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","handcrafted:oak_nightstand","railcraft:personal_world_spike","mcwlights:lava_lamp","toolbelt:pouch","cfm:dark_oak_chair","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","dyenamics:bubblegum_concrete_powder","xnet:redstoneproxy_update","cfm:mangrove_desk","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwroofs:nether_bricks_attic_roof","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","sgjourney:archeology_table","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","minecraft:cracked_nether_bricks","utilitix:gilding_crystal","minecraft:dark_prismarine_stairs_from_dark_prismarine_stonecutting","mcwfences:spruce_stockade_fence","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","supplementaries:slingshot","silentgear:crimson_steel_ingot","allthemodium:allthemodium_plate","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","everythingcopper:copper_rail","cfm:black_grill","handcrafted:deepslate_pillar_trim","mcwwindows:mangrove_window2","mcwfurnitures:stripped_mangrove_striped_chair","additionallanterns:purpur_lantern","mcwdoors:jungle_modern_door","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","ironfurnaces:furnaces/iron_furnace2","create:small_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","connectedglass:scratched_glass_light_blue_pane2","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","mcwtrpdoors:mangrove_blossom_trapdoor","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:nether_bricks_steep_roof","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","minecraft:iron_pickaxe","aether:shield_repairing","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","mcwpaths:mossy_stone_windmill_weave_stairs","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","aether:leather_boots_repairing","cfm:post_box","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","minecraft:dark_oak_slab","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","minecraft:packed_mud","mcwdoors:mangrove_bark_glass_door","cfm:orange_cooler","minecraft:lime_stained_glass","mcwbridges:balustrade_mossy_stone_bricks_bridge","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","create:layered_calcite_from_stone_types_calcite_stonecutting","biomesoplenty:pine_chest_boat","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","minecraft:stone_brick_walls_from_stone_stonecutting","mcwroofs:gray_terracotta_top_roof","ae2:tools/nether_quartz_wrench","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","mcwpaths:mossy_stone_flagstone","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwpaths:dark_prismarine_running_bond_slab","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","create:deepslate_pillar_from_stone_types_deepslate_stonecutting","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:sandstone_upper_lower_roof","cfm:fridge_dark","chimes:copper_chimes","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","minecraft:red_nether_brick_slab","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_mangrove_lower_bookshelf_drawer","mcwfences:dark_oak_stockade_fence","minecraft:cooked_salmon_from_campfire_cooking","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","integrateddynamics:crafting/squeezer","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","mcwdoors:mangrove_nether_door","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","minecraft:mangrove_button","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","reliquary:mob_charm_fragments/wither_skeleton","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","alltheores:steel_dust_from_alloy_blending","mcwwindows:black_mosaic_glass","utilitarian:utility/mangrove_logs_to_doors","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","biomesoplenty:mahogany_chest_boat","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","minecraft:cooked_salmon_from_smoking","mcwwindows:prismarine_brick_gothic","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","cfm:dye_lime_picket_gate","littlelogistics:switch_rail","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwpaths:mossy_cobblestone_clover_paving","alltheores:sapphire_from_hammer_crushing","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","constructionwand:core_angel","xnet:connector_red_dye","mcwroofs:red_terracotta_roof","minecolonies:shapetool","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwfurnitures:stripped_mangrove_glass_table","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","minecraft:mossy_stone_brick_slab","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","minecraft:bamboo_chest_raft","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","reliquary:mob_charm_fragments/slime","dyenamics:bed/bubblegum_bed_frm_white_bed","aquaculture:birch_fish_mount","handcrafted:mangrove_couch","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","cfm:dark_oak_park_bench","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","mcwwindows:jungle_window","minecraft:cobblestone_stairs","mcwwindows:crimson_louvered_shutter","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","tombstone:ankh_of_prayer","cfm:white_sofa","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwpaths:mossy_stone_flagstone_slab","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","cfm:black_kitchen_drawer","botania:obedience_stick","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","nethersdelight:hoglin_sirloin_from_smoking","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","mcwfurnitures:mangrove_striped_chair","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","mcwdoors:mangrove_modern_door","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","handcrafted:oak_bench","railcraft:track_remover","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwpaths:dark_prismarine_flagstone_stairs","mcwroofs:diorite_roof","mcwwindows:birch_louvered_shutter","minecraft:repeater","minecraft:red_concrete_powder","forbidden_arcanus:test_tube","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","mcwdoors:mangrove_whispering_door","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwpaths:blackstone_crystal_floor_path","cfm:jungle_cabinet","mcwdoors:mangrove_paper_door","handcrafted:mangrove_corner_trim","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","alltheores:uranium_dust_from_hammer_ingot_crushing","create:cut_diorite_slab_from_stone_types_diorite_stonecutting","mcwroofs:base_roof_block","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","alltheores:smelting_dust/platinum_ingot","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","twigs:blackstone_column","mcwbridges:balustrade_mossy_cobblestone_bridge","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfurnitures:stripped_mangrove_lower_triple_drawer","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","securitycraft:reinforced_orange_stained_glass_pane_from_dye","cfm:jungle_chair","mcwlights:birch_tiki_torch","minecraft:red_nether_brick_wall_from_red_nether_bricks_stonecutting","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","twilightforest:time_chest_boat","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:jungle_door","mcwpaths:mossy_cobblestone_dumble_paving","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwbiomesoplenty:jacaranda_horse_fence","supplementaries:sign_post_dark_oak","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","alltheores:platinum_rod","minecraft:light_gray_stained_glass","mcwroofs:purple_terracotta_steep_roof","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","botania:petal_green","mcwroofs:dark_oak_planks_attic_roof","dyenamics:bed/fluorescent_bed_frm_white_bed","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","botania:ender_eye_block","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","botania:mushroom_9","handcrafted:jungle_drawer","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","minecraft:oak_pressure_plate","botania:mushroom_5","botania:mushroom_4","minecraft:stone_brick_stairs","botania:mushroom_3","bigreactors:turbine/basic/activefluidport_forge","mcwwindows:quartz_window","cfm:brown_grill","immersiveengineering:crafting/steel_scaffolding_standard","mcwroofs:gray_roof","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","allthemods:croptopia/knife","alltheores:uranium_dust_from_hammer_crushing","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","create:cut_calcite_bricks_from_stone_types_calcite_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:clock","dyenamics:conifer_dye","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwroofs:diorite_lower_roof","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","mcwpaths:diorite_windmill_weave","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","botania:twig_wand","twilightforest:transformation_chest_boat","mcwdoors:print_swamp","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","aquaculture:iron_fillet_knife","silentgear:diamond_shard","dyenamics:rose_wool","mcwfurnitures:stripped_mangrove_table","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","railways:benchcart","additionallanterns:red_nether_bricks_chain","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwdoors:oak_modern_door","mcwwindows:birch_window2","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","minecraft:mangrove_stairs","botania:magnet_ring","botania:conversions/green_petal_block_deconstruct","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","cfm:mangrove_crate","mcwroofs:purple_terracotta_roof","twigs:dark_oak_table","mcwfurnitures:stripped_jungle_chair","mcwbridges:nether_bricks_bridge_stair","mcwwindows:white_mosaic_glass","create:polished_cut_deepslate_slab_from_stone_types_deepslate_stonecutting","minecraft:moss_carpet","cfm:acacia_kitchen_sink_dark","mcwroofs:jungle_upper_steep_roof","handcrafted:dark_oak_cupboard","rftoolsbase:machine_frame","minecraft:mossy_cobblestone_slab","minecraft:polished_andesite_slab_from_andesite_stonecutting","pneumaticcraft:remote","cfm:stripped_jungle_chair","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","minecraft:polished_diorite_slab_from_diorite_stonecutting","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","pylons:infusion_pylon","minecraft:mossy_stone_bricks_from_moss_block","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","minecraft:dark_oak_sign","domum_ornamentum:light_blue_brick_extra","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mekanism:processing/steel/ingot/from_dust_smelting","supplementaries:cog_block","connectedglass:scratched_glass_light_blue2","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","enderio:dark_steel_sword","minecraft:lime_concrete_powder","minecraft:leather_leggings","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","croptopia:fruit_salad","securitycraft:block_change_detector","supplementaries:pancake_fd","twigs:rocky_dirt","minecraft:polished_blackstone_button","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwfurnitures:stripped_mangrove_covered_desk","mcwroofs:pink_terracotta_lower_roof","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","immersiveengineering:crafting/minecart_woodencrate","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","pneumaticcraft:elevator_frame","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","additionallanterns:quartz_chain","aether:stone_axe_repairing","twilightforest:sorting_chest_boat","travelersbackpack:standard_smithing","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","mekanism:processing/gold/ingot/from_dust_smelting","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","pneumaticcraft:pressure_chamber_glass_x4","minecraft:red_nether_brick_stairs_from_red_nether_bricks_stonecutting","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","minecraft:waxed_copper_block_from_honeycomb","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","mcwroofs:lime_terracotta_roof","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","mcwfurnitures:stripped_mangrove_bookshelf","minecraft:warped_fungus_on_a_stick","undergarden:gloom_o_lantern","mcwroofs:red_nether_bricks_lower_roof","mcwwindows:birch_window","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","reliquary:uncrafting/wither_skeleton_skull","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","handcrafted:mangrove_nightstand","mcwbridges:balustrade_diorite_bridge","minecraft:gold_ingot_from_smelting_raw_gold","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","pneumaticcraft:heat_pipe","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","minecraft:cooked_cod_from_smoking","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","railcraft:logbook","ae2:misc/tiny_tnt","mcwbridges:jungle_log_bridge_middle","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","naturalist:glow_goop_from_campfire_cooking","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwpaths:blackstone_dumble_paving","mcwwindows:jungle_blinds","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","alltheores:copper_ore_hammer","additionallanterns:normal_sandstone_chain","mcwfurnitures:mangrove_desk","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","alltheores:lumium_dust_from_alloy_blending","cfm:purple_kitchen_drawer","botania:flower_bag","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","pneumaticcraft:compressed_brick_wall","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","aether:stone_sword_repairing","cfm:jungle_kitchen_counter","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","mcwfurnitures:mangrove_coffee_table","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","mcwpaths:diorite_flagstone_slab","mcwpaths:dark_prismarine_crystal_floor","mcwbiomesoplenty:fir_window2","dyenamics:bed/wine_bed_frm_white_bed","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mekanism:osmium_compressor","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","minecraft:smooth_sandstone_slab_from_smooth_sandstone_stonecutting","mcwroofs:gutter_middle_blue","sfm:manager","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwroofs:purple_concrete_roof","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","mcwfurnitures:mangrove_double_drawer","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","littlelogistics:tee_junction_rail","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","rftoolsutility:fluidplus_module","minecraft:bowl","deeperdarker:bloom_boat","create:small_diorite_brick_slab_from_stone_types_diorite_stonecutting","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","dyenamics:rose_stained_glass","mcwroofs:pink_terracotta_upper_steep_roof","mcwpaths:blackstone_diamond_paving","pneumaticcraft:vortex_cannon","ad_astra:lime_industrial_lamp","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","mcwfurnitures:mangrove_cupboard_counter","securitycraft:reinforced_jungle_fence","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwwindows:dark_oak_plank_window2","mcwfences:spruce_curved_gate","allthecompressed:compress/copper_block_1x","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","alltheores:copper_dust_from_hammer_ingot_crushing","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:stripped_mangrove_stool_chair","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","botania:dye_light_blue","waystones:warp_stone","domum_ornamentum:green_brick_extra","create:diorite_pillar_from_stone_types_diorite_stonecutting","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","twigs:mangrove_table","create:crafting/materials/red_sand_paper","create:polished_cut_diorite_from_stone_types_diorite_stonecutting","mcwwindows:stripped_crimson_stem_window2","mcwpaths:mossy_stone_crystal_floor_path","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","alltheores:lead_dust_from_hammer_crushing","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwfurnitures:stripped_mangrove_modern_desk","mcwroofs:oak_planks_attic_roof","rftoolsutility:moduleplus_template","dyenamics:amber_terracotta","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","sophisticatedstorage:basic_to_copper_tier_upgrade","mcwdoors:mangrove_waffle_door","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","alltheores:sapphire_dust_from_hammer_crushing","mcwbridges:stone_bridge_pier","alltheores:iridium_dust_from_hammer_crushing","aether:white_cape","railcraft:radio_circuit","croptopia:peach_jam","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","create:cut_calcite_stairs_from_stone_types_calcite_stonecutting","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","mcwbiomesoplenty:stripped_magic_log_four_window","corail_woodcutter:mangrove_woodcutter","utilitarian:snad/drit","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","minecraft:cooked_cod","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","securitycraft:alarm","pneumaticcraft:manometer","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","rftoolsutility:charged_porter","minecraft:polished_diorite","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","dyenamics:icy_blue_dye","pneumaticcraft:wall_lamp_inverted_magenta","mcwroofs:oak_top_roof","mcwpaths:dark_prismarine_crystal_floor_slab","mcwpaths:sandstone_honeycomb_paving","utilitix:crude_furnace","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwwindows:green_curtain","cfm:crimson_kitchen_sink_light","mcwwindows:stone_window","utilitix:comparator_redirector_down","mcwroofs:gray_steep_roof","cfm:stripped_mangrove_kitchen_counter","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","alltheores:iron_ore_hammer","utilitarian:utility/mangrove_logs_to_boats","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","twigs:mossy_cobblestone_bricks_cobblestone","mcwroofs:yellow_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","delightful:food/cooking/glow_jam_jar","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","cfm:stripped_oak_crate","immersiveengineering:crafting/wirecutter","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","minecraft:polished_blackstone_stairs","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","minecraft:golden_helmet","botania:animated_torch","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","mcwpaths:diorite_clover_paving","sophisticatedstorage:mangrove_barrel","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","create:crafting/kinetics/green_seat","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","cfm:mangrove_desk_cabinet","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","farmersdelight:cooking/mushroom_stew","mcwroofs:base_roof_slab","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","minecraft:dye_light_blue_carpet","create:cut_calcite_wall_from_stone_types_calcite_stonecutting","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","minecraft:quartz_bricks","mcwbridges:mossy_cobblestone_bridge_pier","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","delightful:food/cooking/jam_jar","pneumaticcraft:pressure_chamber_valve","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","mcwroofs:jungle_planks_steep_roof","alltheores:peridot_dust_from_hammer_crushing","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","supplementaries:candle_holders/candle_holder_light_blue_dye","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","twigs:polished_tuff_stonecutting","cfm:dark_oak_table","mcwpaths:cobbled_deepslate_windmill_weave_slab","reliquary:mob_charm_fragments/skeleton","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwroofs:white_terracotta_top_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwbiomesoplenty:stripped_magic_log_window2","mcwroofs:orange_concrete_upper_lower_roof","pneumaticcraft:compressed_iron_leggings","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","mcwroofs:dark_oak_planks_lower_roof","mcwfences:modern_andesite_wall","mcwroofs:light_gray_terracotta_attic_roof","alltheores:nickel_dust_from_hammer_ingot_crushing","cfm:dye_light_blue_picket_gate","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","croptopia:peach_sapling","mcwroofs:light_gray_concrete_upper_lower_roof","create:cut_deepslate_from_stone_types_deepslate_stonecutting","sophisticatedstorage:mangrove_limited_barrel_1","sophisticatedstorage:mangrove_limited_barrel_2","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwroofs:deepslate_lower_roof","sophisticatedstorage:mangrove_limited_barrel_3","sophisticatedstorage:mangrove_limited_barrel_4","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","mcwdoors:mangrove_stable_head_door","minecraft:lightning_rod","mcwwindows:metal_four_window","forbidden_arcanus:dark_rune","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","mcwroofs:jungle_planks_top_roof","cfm:lime_kitchen_drawer","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwpaths:dark_prismarine_running_bond_path","minecolonies:apple_pie","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","twigs:mossy_bricks_from_moss_block","simplylight:illuminant_green_block_dyed","handcrafted:mangrove_fancy_bed","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","alltheores:copper_dust_from_hammer_crushing","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","eccentrictome:tome","securitycraft:reinforced_warped_fence","alltheores:osmium_dust_from_hammer_crushing","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwroofs:mangrove_planks_steep_roof","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","mcwroofs:dark_oak_planks_upper_steep_roof","alltheores:silver_dust_from_hammer_crushing","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","alltheores:iron_dust_from_hammer_ingot_crushing","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","mekanism:cardboard_box","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","alltheores:platinum_ore_hammer","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:mangrove_trapdoor","minecraft:diamond_boots","create:polished_cut_deepslate_wall_from_stone_types_deepslate_stonecutting","pneumaticcraft:redstone_module","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","croptopia:beetroot_salad","mcwwindows:mangrove_plank_four_window","chemlib:copper_ingot_from_smelting_copper_dust","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","minecraft:red_nether_brick_stairs","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","farmersdelight:cooking/glow_berry_custard","twigs:bloodstone","create:crafting/kinetics/red_seat","connectedglass:tinted_borderless_glass_light_blue2","mcwwindows:warped_planks_window","mcwroofs:orange_concrete_steep_roof","cfm:orange_kitchen_counter","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwdoors:mangrove_mystic_door","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","create:crafting/kinetics/orange_seat","minecraft:diamond_block","handcrafted:diorite_corner_trim","mcwroofs:deepslate_roof","biomesoplenty:willow_chest_boat","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","minecraft:diorite_slab","dyenamics:cherenkov_stained_glass","dyenamics:icy_blue_terracotta","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","paraglider:cosmetic/deku_leaf","bigreactors:blasting/graphite_from_coal","mcwdoors:mangrove_barn_glass_door","mcwpaths:dark_prismarine_windmill_weave_stairs","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","create:cut_diorite_brick_slab_from_stone_types_diorite_stonecutting","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","cfm:stripped_mangrove_table","alltheores:platinum_ingot_from_raw","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","handcrafted:mangrove_counter","utilitarian:snad/snad","croptopia:shaped_bacon","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","minecraft:light_gray_terracotta","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:spring_green_dye","dyenamics:wine_terracotta","cfm:dye_green_picket_gate","mcwroofs:light_gray_attic_roof","mcwroofs:mangrove_upper_lower_roof","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","mcwfurnitures:stripped_jungle_covered_desk","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwbiomesoplenty:palm_highley_gate","allthecompressed:compress/platinum_block_1x","simplylight:illuminant_blue_block_on_toggle","domum_ornamentum:lime_brick_extra","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","botania:manasteel_shears","ae2:network/cables/smart_green","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwwindows:stone_brick_gothic","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mekanismtools:refined_obsidian/tools/pickaxe","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:soul_campfire","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwroofs:diorite_upper_steep_roof","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","connectedglass:clear_glass_light_blue_pane2","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","utilitarian:utility/mangrove_logs_to_stairs","domum_ornamentum:green_cobblestone_extra","create:cut_deepslate_stairs_from_stone_types_deepslate_stonecutting","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:diorite_flagstone_path","create:layered_deepslate_from_stone_types_deepslate_stonecutting","mcwlights:spruce_tiki_torch","dyenamics:bed/amber_bed_frm_white_bed","mcwfurnitures:stripped_jungle_striped_chair","simplylight:illuminant_block_dyed","enderio:dark_steel_pressure_plate","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","littlelogistics:automatic_tee_junction_rail","appflux:insulating_resin","mcwroofs:mangrove_steep_roof","silentgear:crimson_iron_ingot_from_block","connectedglass:borderless_glass_green_pane2","alltheores:aluminum_dust_from_hammer_ingot_crushing","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mekanism:control_circuit/advanced","mekanismtools:refined_obsidian/armor/helmet","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","mcwfurnitures:mangrove_bookshelf","pneumaticcraft:kerosene_lamp","silentgear:crimson_iron_nugget","botania:lens_normal","mcwdoors:mangrove_western_door","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","cfm:dye_light_blue_picket_fence","cfm:gray_grill","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","create:crafting/kinetics/minecart_from_contraption_cart","pneumaticcraft:wall_lamp_inverted_cyan","minecraft:cooked_chicken_from_campfire_cooking","mcwfurnitures:jungle_modern_chair","create:cut_calcite_brick_wall_from_stone_types_calcite_stonecutting","mekanismtools:refined_obsidian/shield","handcrafted:mangrove_cupboard","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","handcrafted:phantom_trophy","minecraft:quartz_slab","mcwroofs:cyan_terracotta_upper_steep_roof","modularrouters:modular_router","handcrafted:mangrove_side_table","mcwfurnitures:stripped_jungle_coffee_table","mcwroofs:stone_bricks_roof","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","minecraft:quartz_pillar_from_quartz_block_stonecutting","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:nether_bricks_roof","minecraft:kjs/ender_pearl","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:observer","minecraft:mossy_cobblestone_wall_from_mossy_cobblestone_stonecutting","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","biomesoplenty:hellbark_chest_boat","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","oceansdelight:stuffed_cod","mcwfurnitures:mangrove_drawer","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","mcwpaths:dark_prismarine_strewn_rocky_path","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","delightful:smoking/roasted_acorn","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwpaths:andesite_honeycomb_paving","mcwwindows:birch_log_parapet","mcwbridges:sandstone_bridge","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","minecraft:diorite_wall_from_diorite_stonecutting","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","utilitix:dark_oak_shulker_boat","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","twigs:polished_tuff_bricks_from_tuff_stonecutting","handcrafted:kitchen_hood","mcwdoors:jungle_bamboo_door","alltheores:silver_dust_from_hammer_ingot_crushing","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","utilitix:oak_shulker_boat","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","create:polished_cut_calcite_slab_from_stone_types_calcite_stonecutting","mcwroofs:black_lower_roof","mcwfurnitures:stripped_oak_desk","mcwroofs:gutter_base_blue","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","croptopia:candied_nuts","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","mcwtrpdoors:mangrove_whispering_trapdoor","allthemodium:allthemodium_ingot_from_dust_blasting","bigreactors:reprocessor/fluidinjector","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwroofs:mangrove_roof","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwroofs:andesite_lower_roof","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","create:calcite_from_stone_types_calcite_stonecutting","pneumaticcraft:pressure_chamber_valve_x4","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","minecraft:dark_prismarine_slab","additionallanterns:blackstone_chain","silentgear:crimson_iron_dust_smelting","minecraft:green_dye","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","connectedglass:scratched_glass_lime_pane2","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","gtceu:shaped/block_compress_platinum","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","productivetrees:sawmill","railcraft:tunnel_bore","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","everythingcopper:copper_anvil","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","utilitix:crossing_rail","botania:green_petal_block","supplementaries:candle_holders/candle_holder_green_dye","railcraft:electric_locomotive","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","minecraft:conduit","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","deeperdarker:bloom_chest_boat","pneumaticcraft:wall_lamp_purple","mcwwindows:dark_oak_plank_window","minecraft:sticky_piston","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:copper_chain","cfm:oak_kitchen_sink_light","minecraft:powered_rail","sgjourney:sandstone_hieroglyphs","mcwwindows:diorite_pane_window","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwroofs:magenta_concrete_upper_lower_roof","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","aether:stone_pickaxe_repairing","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","ad_astra:light_blue_industrial_lamp","mcwpaths:sandstone_running_bond","pneumaticcraft:stone_base","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","minecraft:gray_terracotta","minecraft:comparator","handcrafted:dark_oak_corner_trim","mcwwindows:white_mosaic_glass_pane","mcwroofs:mangrove_planks_upper_steep_roof","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","dyenamics:peach_terracotta","minecraft:fishing_rod","rftoolspower:powercell_card","xnet:connector_yellow_dye","minecraft:terracotta","mcwwindows:acacia_blinds","mcwfurnitures:mangrove_chair","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","minecraft:dye_light_blue_bed","supplementaries:timber_frame","aether:diamond_pickaxe_repairing","pneumaticcraft:seismic_sensor","twigs:copper_pillar_stonecutting","immersiveengineering:crafting/minecart_woodenbarrel","blue_skies:lunar_bookshelf","sophisticatedstorage:packing_tape","supplementaries:sign_post_mangrove","mcwroofs:cyan_terracotta_top_roof","cfm:stripped_mangrove_coffee_table","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","create:small_deepslate_bricks_from_stone_types_deepslate_stonecutting","mcwlights:double_street_lamp","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","ad_astra:white_flag","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","mcwroofs:diorite_top_roof","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","alltheores:invar_dust_from_alloy_blending","mekanism:steel_casing","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","minecraft:dye_light_blue_wool","handcrafted:sandstone_pillar_trim","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","supplementaries:globe_sepia","rftoolsbase:tablet","mcwroofs:magenta_terracotta_lower_roof","railcraft:routing_table_book","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwtrpdoors:mangrove_tropical_trapdoor","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","allthecompressed:compress/bone_block_1x","additionallanterns:bone_chain","integrateddynamics:crafting/menril_planks","simplylight:bulb","travelersbackpack:cactus","mcwwindows:stripped_acacia_pane_window","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:stone_attic_roof","minecraft:mangrove_sign","mcwbridges:rope_dark_oak_bridge","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:stone_tile","handcrafted:terracotta_plate","handcrafted:calcite_corner_trim","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","create:cut_deepslate_brick_wall_from_stone_types_deepslate_stonecutting","mcwfurnitures:stripped_mangrove_drawer","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","supplementaries:flags/flag_white","travelersbackpack:dye_light_blue_sleeping_bag","handcrafted:jungle_shelf","mcwpaths:dark_prismarine_basket_weave_paving","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","cfm:dark_oak_desk_cabinet","mcwroofs:andesite_top_roof","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","minecraft:clay","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","utilitix:jungle_shulker_boat","cfm:red_kitchen_counter","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwbridges:diorite_bridge","minecraft:furnace_minecart","mcwlights:magenta_paper_lamp","allthemods:rechiseled/chisel","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_mangrove_bookshelf_cupboard","cfm:orange_kitchen_drawer","enderio:cold_fire_igniter","croptopia:chicken_and_noodles","silentgear:stone_rod","ae2:network/cables/dense_covered_green","minecraft:leather_boots","handcrafted:sandstone_corner_trim","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","mcwpaths:diorite_crystal_floor_path","mcwbridges:mossy_stone_brick_bridge","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","mcwlights:pink_paper_lamp","mcwroofs:mangrove_lower_roof","pneumaticcraft:wall_lamp_inverted_light_blue","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","minecraft:red_nether_brick_slab_from_red_nether_bricks_stonecutting","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","mcwfurnitures:mangrove_glass_table","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","mcwpaths:diorite_strewn_rocky_path","pneumaticcraft:stomp_upgrade","mcwpaths:dark_prismarine_flagstone_slab","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","forbidden_arcanus:lens_of_veritatis","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","nethersdelight:nether_brick_smoker","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivetrees:crates/red_delicious_apple_crate","botania:pool_minecart","littlelogistics:transmitter_component","minecraft:brush","aether:book_of_lore","railcraft:steel_pickaxe","mcwroofs:jungle_planks_roof","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","sfm:cable","silentgear:raw_crimson_iron_from_block","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","alltheores:ruby_from_hammer_crushing","mcwroofs:white_upper_steep_roof","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbridges:mossy_cobblestone_bridge_stair","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","create:mangrove_window","mcwfences:modern_granite_wall","create:jungle_window","twigs:mossy_cobblestone_brick_slab_from_mossy_cobblestone_stonecutting","forbidden_arcanus:golden_blacksmith_gavel","alltheores:lead_dust_from_hammer_ingot_crushing","mcwbiomesoplenty:dead_wired_fence","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","mcwtrpdoors:dark_oak_blossom_trapdoor","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","cfm:mangrove_table","alltheores:zinc_dust_from_hammer_ingot_crushing","minecraft:dark_oak_fence","minecraft:cut_copper","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","create:polished_cut_diorite_wall_from_stone_types_diorite_stonecutting","travelersbackpack:sandstone_smithing","mcwwindows:bricks_window","cfm:mangrove_cabinet","twigs:paper_lantern","railcraft:goggles","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","travelersbackpack:iron","minecraft:smooth_sandstone_stairs","mcwroofs:oak_planks_upper_steep_roof","mcwbridges:mangrove_bridge_pier","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","minecraft:oak_fence","alltheores:peridot_from_hammer_crushing","pneumaticcraft:liquid_compressor","create:polished_cut_calcite_wall_from_stone_types_calcite_stonecutting","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:mossy_stone_running_bond","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","handcrafted:dark_oak_bench","mcwwindows:granite_louvered_shutter","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","immersiveengineering:crafting/blueprint_bullets","mcwroofs:magenta_concrete_lower_roof","supplementaries:dispenser_minecart","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","sophisticatedbackpacks:backpack","forbidden_arcanus:rune_block_from_rune","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","alltheores:constantan_dust_from_alloy_blending","mcwroofs:gray_concrete_upper_lower_roof","mcwwindows:orange_mosaic_glass_pane","mcwroofs:sandstone_upper_steep_roof","utilitix:piston_controller_rail","croptopia:roasted_smoking","mcwbiomesoplenty:hellbark_curved_gate","mcwpaths:mossy_cobblestone_basket_weave_paving","minecraft:green_terracotta","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","securitycraft:taser","alltheores:aluminum_dust_from_hammer_crushing","gtceu:shapeless/blaze_rod_to_powder","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","create:cut_diorite_brick_stairs_from_stone_types_diorite_stonecutting","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","handcrafted:mangrove_drawer","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","minecraft:mossy_cobblestone_stairs","mcwfences:acacia_horse_fence","mcwpaths:diorite_windmill_weave_slab","mcwfences:spruce_hedge","sophisticatedstorage:dark_oak_chest","mcwfences:jungle_hedge","mcwbiomesoplenty:willow_plank_window2","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mekanismtools:osmium/tools/sword","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","securitycraft:reinforced_andesite","aether:aether_tune_enchanting","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","allthecompressed:compress/deepslate_1x","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","alltheores:electrum_dust_from_alloy_blending","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","botania:manasteel_shovel","handcrafted:calcite_pillar_trim","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwtrpdoors:mangrove_beach_trapdoor","mcwroofs:white_steep_roof","mcwpaths:cobbled_deepslate_crystal_floor_slab","handcrafted:mangrove_table","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","mcwpaths:cobbled_deepslate_crystal_floor","supplementaries:candle_holders/candle_holder_blue","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","mcwpaths:blackstone_crystal_floor","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","cfm:dark_oak_crate","minecraft:gold_nugget","twigs:mossy_cobblestone_bricks_stonecutting","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","mcwwindows:stripped_dark_oak_log_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","cfm:stripped_jungle_desk_cabinet","utilitix:dimmable_redstone_lamp","minecraft:blackstone_slab_from_blackstone_stonecutting","securitycraft:display_case","minecraft:jungle_wood","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","allthecompressed:compress/andesite_1x","mcwwindows:diorite_four_window","minecraft:stone_slab","pneumaticcraft:compressed_bricks","mcwroofs:red_nether_bricks_upper_lower_roof","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","create:small_calcite_brick_wall_from_stone_types_calcite_stonecutting","handcrafted:dark_oak_pillar_trim","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","travelersbackpack:backpack_tank","minecraft:jungle_stairs","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","pneumaticcraft:reinforced_stone_from_slab","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","minecraft:light_blue_stained_glass_pane_from_glass_pane","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","dyenamics:bed/mint_bed_frm_white_bed","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","cfm:mangrove_upgraded_gate","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","silentgear:crimson_iron_ingot_from_nugget","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","twigs:crimson_roots_paper_lantern","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:fletching_table","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","minecraft:dye_lime_bed","securitycraft:mine","pneumaticcraft:compressed_stone_slab","minecraft:cooked_salmon","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","minecraft:mossy_cobblestone_wall","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","minecraft:oak_chest_boat","mcwpaths:mossy_stone_flagstone_path","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","aether:wooden_pickaxe_repairing","twigs:tuff_stairs","mcwfurnitures:mangrove_large_drawer","mcwfences:modern_mud_brick_wall","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","twilightforest:mining_chest_boat","mcwroofs:mangrove_top_roof","minecraft:diorite_slab_from_diorite_stonecutting","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","create:crafting/kinetics/yellow_seat","pneumaticcraft:logistics_core","mcwpaths:andesite_windmill_weave_path","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","pneumaticcraft:flippers_upgrade","croptopia:cheeseburger","minecraft:mangrove_boat","minecraft:bone_meal_from_bone_block","mcwroofs:green_terracotta_steep_roof","mcwdoors:garage_silver_door","comforts:hammock_white","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","cfm:mangrove_chair","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","cfm:stripped_mangrove_crate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","farmersdelight:cooking/vegetable_soup","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwtrpdoors:mangrove_mystic_trapdoor","mcwbiomesoplenty:willow_pyramid_gate","minecraft:dark_oak_chest_boat","minecraft:dark_oak_trapdoor","mcwfences:iron_cheval_de_frise","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","ironfurnaces:furnaces/diamond_furnace","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","chimes:amethyst_chimes","mcwwindows:stone_brick_arrow_slit","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","securitycraft:disguise_module","mcwwindows:deepslate_four_window","allthecompressed:compress/mangrove_planks_1x","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","alltheores:osmium_dust_from_hammer_ingot_crushing","pneumaticcraft:camo_applicator","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","create:calcite_pillar_from_stone_types_calcite_stonecutting","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","rftoolsutility:counter_module","minecraft:amethyst_block","cfm:stripped_jungle_crate","minecraft:oak_door","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","mcwroofs:black_roof_block","connectedglass:tinted_borderless_glass_lime2","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","minecraft:polished_diorite_stairs_from_diorite_stonecutting","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","handcrafted:oak_corner_trim","mcwroofs:lime_concrete_upper_lower_roof","cfm:spruce_kitchen_drawer","mcwbiomesoplenty:magic_highley_gate","create:cut_diorite_brick_wall_from_stone_types_diorite_stonecutting","mcwfurnitures:stripped_mangrove_coffee_table","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","dyenamics:mint_terracotta","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","create:crafting/kinetics/furnace_minecart_from_contraption_cart","securitycraft:limited_use_keycard","delightful:knives/refined_obsidian_knife","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","mcwbiomesoplenty:flowering_oak_hedge","pneumaticcraft:compressed_iron_block_from_ingot","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","biomesoplenty:fir_chest_boat","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","naturalist:glow_goop_from_smoking","supplementaries:wrench","minecraft:bow","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","mcwroofs:blue_terracotta_upper_steep_roof","dyenamics:lavender_wool","aether:skyroot_smithing_table","mekanismtools:osmium/armor/leggings","aquaculture:brown_mushroom_from_fish","railways:jukeboxcart","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","botania:mushroom_stew","minecraft:andesite_stairs","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","croptopia:apple_juice","cfm:purple_kitchen_sink","mcwfurnitures:mangrove_modern_wardrobe","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","mcwpaths:diorite_dumble_paving","minecraft:jungle_slab","twigs:polished_calcite_stonecutting","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","twilightforest:dark_chest_boat","botania:spark","minecraft:dark_oak_boat","mcwpaths:mossy_stone_crystal_floor_slab","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","handcrafted:mangrove_pillar_trim","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","mcwroofs:red_nether_bricks_attic_roof","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","supplementaries:integrateddynamics/sign_post_menril","minecraft:mossy_stone_brick_wall","twigs:stick_from_twig","dyenamics:bed/aquamarine_bed_frm_white_bed","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","botania:ghost_rail","minecraft:firework_rocket_simple","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","dyenamics:bed/rose_bed_frm_white_bed","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","mcwbridges:balustrade_nether_bricks_bridge","allthecompressed:compress/mangrove_log_1x","minecraft:andesite","handcrafted:mangrove_shelf","pneumaticcraft:wall_lamp_inverted_brown","mcwroofs:andesite_roof","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","rftoolsbase:infused_enderpearl","supplementaries:pedestal","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","dyenamics:bubblegum_wool","alltheores:ruby_dust_from_hammer_crushing","create:cut_deepslate_brick_slab_from_stone_types_deepslate_stonecutting","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","mcwpaths:sandstone_flagstone_path","farmersdelight:cooking/apple_cider","dyenamics:rose_terracotta","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwdoors:jungle_tropical_door","minecraft:stone","twigs:lamp","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","mcwtrpdoors:mangrove_glass_trapdoor","minecraft:blackstone_stairs","handcrafted:dark_oak_couch","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","mcwbridges:mossy_cobblestone_bridge","travelersbackpack:lapis","mcwroofs:red_nether_bricks_steep_roof","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","domum_ornamentum:cream_stone_bricks","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","mcwdoors:oak_barn_door","botania:alchemy_catalyst","mcwbridges:mangrove_log_bridge_middle","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","reliquary:fertile_essence","create:layered_diorite_from_stone_types_diorite_stonecutting","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","mcwroofs:cyan_concrete_attic_roof","silentgear:crimson_iron_block","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbridges:mossy_stone_bridge_pier","cfm:stripped_oak_bedside_cabinet","mcwbiomesoplenty:empyreal_horse_fence","mcwroofs:white_concrete_attic_roof","cfm:pink_kitchen_drawer","gtceu:shaped/mortar_iron","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","pneumaticcraft:reinforced_brick_slab","additionallanterns:obsidian_chain","minecraft:ender_eye","supplementaries:blackstone_lamp","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","mcwwindows:bricks_four_window","railcraft:receiver_circuit","minecraft:quartz_bricks_from_quartz_block_stonecutting","mcwdoors:mangrove_japanese_door","minecraft:green_candle","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","rftoolsbase:manual","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","mekanismtools:osmium/armor/helmet","supplementaries:stonecutting/stone_tile","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwfurnitures:mangrove_table","dyenamics:conifer_concrete_powder","enderio:dark_steel_trapdoor","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","ad_astra:small_green_industrial_lamp","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","railcraft:steel_hoe","mcwroofs:brown_terracotta_attic_roof","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","minecraft:hopper_minecart","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","mcwpaths:mossy_stone_flagstone_stairs","mcwlights:yellow_paper_lamp","ironfurnaces:furnaces/copper_furnace","additionallanterns:diorite_chain","domum_ornamentum:cream_bricks","allthemodium:allthemodium_ingot_from_raw_blasting","mcwroofs:red_nether_bricks_roof","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","dyenamics:bed/spring_green_bed_frm_white_bed","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","mcwwindows:black_curtain","dyenamics:wine_stained_glass","minecraft:gold_nugget_from_smelting","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwtrpdoors:mangrove_barrel_trapdoor","mcwpaths:dark_prismarine_windmill_weave_path","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","minecraft:diorite_stairs","minecraft:mangrove_pressure_plate","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","additionallanterns:dark_prismarine_chain","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","cfm:jungle_table","mcwwindows:cherry_pane_window","mcwbiomesoplenty:empyreal_pyramid_gate","twigs:calcite_stairs","mcwdoors:garage_white_door","utilitix:armed_stand","twigs:deepslate_column","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","mcwroofs:oak_planks_top_roof","biomesoplenty:hellbark_boat","alltheores:zinc_dust_from_hammer_crushing","railcraft:steel_chestplate","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwroofs:oak_planks_roof","enderio:fluid_tank","alltheores:tin_dust_from_hammer_ingot_crushing","ironfurnaces:furnaces/gold_furnace","mcwpaths:stone_flagstone","bigreactors:energizer/casing","mcwpaths:blackstone_honeycomb_paving","aether:chainmail_boots_repairing","simplylight:illuminant_block_toggle","xnet:connector_blue","minecraft:nether_brick_slab","mcwdoors:mangrove_stable_door","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","railcraft:track_relayer","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","mcwlights:brown_paper_lamp","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","mekanism:enrichment_chamber","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwpaths:mossy_stone_running_bond_path","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","create:cut_tuff_from_stone_types_tuff_stonecutting","pneumaticcraft:logistics_configurator","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:4780,warning_level:0}}