{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.0d,Name:"attributeslib:creative_flight"}],BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["6c8881a8-dbdf-465b-bb3e-0c97f47d45a4"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-245786444,**********,-**********,**********],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:31048},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"quark:tweaks/crafting/utility/tools/stone_axe",ItemStack:{Count:1b,id:"minecraft:stone_axe",tag:{Damage:0}}}]},"rftoolsutility:properties":{allowFlying:0b,buffTicks:93,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"NONE",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["minecraft:cooked_beef"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},UUID:[I;*********,2019182445,-1688876346,-*********],decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["6c8881a8-dbdf-465b-bb3e-0c97f47d45a4","d1811b83-cc8c-4821-80c6-3e5ce576ae62"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-51,tb_last_ground_location_y:253,tb_last_ground_location_z:-29,twilightforest_banished:1b},"aae$nokey":0b,"aae$upkey":0b,"quark:trying_crawl":0b,simplylight_unlocked:2},Health:20.0f,HurtByTimestamp:11379,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"minecraft:stone_sword",tag:{Damage:0}},{Count:1b,Slot:1b,id:"minecraft:stone_pickaxe",tag:{Damage:5}},{Count:1b,Slot:2b,id:"minecraft:stone_axe",tag:{Damage:1}},{Count:1b,Slot:3b,id:"pylons:potion_filter"},{Count:6b,Slot:7b,id:"minecraft:cooked_beef"},{Count:1b,Slot:8b,id:"minecraft:wooden_pickaxe",tag:{Damage:21}},{Count:1b,Slot:9b,id:"minecraft:crafting_table"},{Count:5b,Slot:10b,id:"minecraft:cobblestone"},{Count:4b,Slot:11b,id:"minecraft:sand"},{Count:6b,Slot:12b,id:"minecraft:cactus"},{Count:5b,Slot:13b,id:"minecraft:sugar_cane"},{Count:8b,Slot:14b,id:"minecraft:torch"},{Count:1b,Slot:16b,id:"farmersdelight:rice"},{Count:21b,Slot:22b,id:"minecraft:stick"},{Count:8b,Slot:23b,id:"minecraft:jungle_planks"},{Count:13b,Slot:28b,id:"minecraft:sandstone"},{Count:27b,Slot:30b,id:"minecraft:jungle_log"}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-34.76741112024118d,253.0d,-152.28705892152837d],Railways_DataVersion:2,Rotation:[84.50104f,-11.099932f],Score:0,SelectedItemSlot:6,SleepTimer:0s,Spigot.ticksLived:31047,UUID:[I;1729708789,-90421561,-2023633974,1057190043],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-9345849462531L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:0,XpP:0.0f,XpSeed:294380829,XpTotal:0,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752217518127L,keepLevel:0b,lastKnownName:"Z603H",lastPlayed:1752653636077L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:2.352295f,foodLevel:20,foodSaturationLevel:11.8f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["corail_woodcutter:birch_woodcutter","mcwbridges:sandstone_bridge","mcwfences:granite_railing_gate","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwroofs:magenta_concrete_top_roof","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","dyenamics:banner/lavender_banner","mcwpaths:stone_crystal_floor","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","handcrafted:spider_trophy","mcwfurnitures:jungle_bookshelf_drawer","mcwtrpdoors:jungle_cottage_trapdoor","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","mcwroofs:sandstone_upper_lower_roof","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","cfm:jungle_coffee_table","mcwroofs:yellow_concrete_roof","mcwpaths:jungle_planks_path","create:crafting/appliances/crafting_blueprint","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:umbran_horse_fence","utilitix:reinforced_rail","mcwfences:dark_oak_stockade_fence","mcwroofs:blackstone_attic_roof","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/jungle_logs_to_doors","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","corail_woodcutter:crimson_woodcutter","mcwdoors:jungle_nether_door","supplementaries:notice_board","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","securitycraft:reinforced_bamboo_fence","allthecompressed:compress/jungle_planks_1x","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwfurnitures:jungle_modern_desk","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","securitycraft:reinforced_cherry_fence_gate","mcwfences:acacia_hedge","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","mcwroofs:purple_concrete_attic_roof","energymeter:meter","dyenamics:banner/cherenkov_banner","mcwlights:covered_lantern","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","simplylight:rodlamp","mcwroofs:purple_concrete_upper_lower_roof","mcwroofs:deepslate_attic_roof","sliceanddice:slicer","minecolonies:shapetool","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","farmersdelight:cooking/cooked_rice","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","mcwtrpdoors:jungle_blossom_trapdoor","domum_ornamentum:blue_cobblestone_extra","securitycraft:reinforced_andesite_with_vanilla_diorite","mcwpaths:sandstone_crystal_floor_stairs","minecraft:sandstone_slab_from_sandstone_stonecutting","minecraft:green_dye","mcwfences:flowering_azalea_hedge","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","aquaculture:birch_fish_mount","aquaculture:wooden_fillet_knife","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","minecraft:cobblestone_stairs","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","simplylight:illuminant_red_block_toggle","sophisticatedstorage:generic_limited_barrel_4","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwpaths:cobblestone_dumble_paving","mcwpaths:sandstone_strewn_rocky_path","dyenamics:aquamarine_concrete_powder","mcwpaths:sandstone_flagstone_stairs","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwlights:striped_lantern","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","mcwlights:chain_lantern","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","undergarden:torch_ditchbulb_paste","mcwfences:dark_oak_pyramid_gate","mcwroofs:sandstone_roof","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","cfm:jungle_kitchen_sink_light","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:prismarine_lantern","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","biomesoplenty:mahogany_boat","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","additionallanterns:stone_bricks_lantern","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","utilitarian:utility/charcoal_from_campfire","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwroofs:magenta_concrete_upper_lower_roof","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwroofs:cyan_concrete_steep_roof","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","aether:stone_pickaxe_repairing","mcwpaths:sandstone_running_bond","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","minecraft:yellow_concrete_powder","minecraft:jungle_fence_gate","aquaculture:tin_can_to_iron_nugget","minecraft:red_concrete_powder","simplylight:illuminant_orange_block_on_toggle","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","dyenamics:spring_green_concrete_powder","mcwroofs:red_concrete_steep_roof","mcwpaths:sandstone_running_bond_stairs","minecraft:glass","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwlights:tavern_lantern","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","simplylight:edge_light_bottom_from_top","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","dyenamics:bed/navy_bed","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwdoors:jungle_bark_glass_door","minecraft:light_blue_concrete_powder","supplementaries:timber_frame","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","supplementaries:daub_cross_brace","mcwwindows:jungle_four_window","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","mcwlights:rustic_torch","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:soul_oak_tiki_torch","mcwlights:birch_tiki_torch","dyenamics:banner/honey_banner","handcrafted:jungle_bench","mcwlights:double_street_lamp","additionallanterns:quartz_lantern","twigs:mossy_cobblestone_bricks","mcwfences:stone_grass_topped_wall","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","minecraft:jungle_door","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","cfm:stripped_jungle_bedside_cabinet","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:magic_picket_fence","minecraft:cobblestone_wall_from_cobblestone_stonecutting","mcwbridges:sandstone_bridge_pier","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","mcwroofs:red_concrete_roof","securitycraft:reinforced_dark_oak_fence","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","cfm:stripped_jungle_cabinet","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","cfm:jungle_desk","simplylight:bulb","travelersbackpack:cactus","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","mcwlights:wall_lantern","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","mcwfences:nether_brick_pillar_wall","mcwroofs:light_blue_concrete_steep_roof","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","mcwbridges:rope_jungle_bridge","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","minecraft:jungle_trapdoor","dyenamics:amber_concrete_powder","twigs:silt","additionallanterns:end_stone_lantern","handcrafted:jungle_shelf","mcwroofs:blue_concrete_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_running_bond_slab","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwbiomesoplenty:maple_curved_gate","mcwroofs:orange_concrete_roof","mcwroofs:lime_concrete_upper_steep_roof","mcwlights:blue_paper_lamp","simplylight:illuminant_yellow_block_toggle","securitycraft:sonic_security_system","mcwfurnitures:stripped_jungle_glass_table","minecraft:bamboo_raft","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfurnitures:stripped_jungle_chair","mcwroofs:green_concrete_roof","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","mcwfences:railing_andesite_wall","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwwindows:sandstone_window2","mcwbiomesoplenty:willow_hedge","minecraft:white_concrete_powder","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","minecraft:mossy_stone_bricks_from_moss_block","mcwpaths:cobblestone_basket_weave_paving","mcwtrpdoors:jungle_swamp_trapdoor","minecraft:cherry_boat","silentgear:stone_rod","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","aquaculture:cooked_fish_fillet_from_campfire","quark:tweaks/crafting/utility/tools/stone_sword","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","mcwroofs:orange_concrete_lower_roof","enderio:stone_gear","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","utilitix:highspeed_rail","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwlights:pink_paper_lamp","mcwfences:end_brick_railing_gate","mcwpaths:sandstone_windmill_weave_path","additionallanterns:iron_lantern","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","simplylight:illuminant_light_gray_block_toggle","utilitix:hand_bell","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwpaths:sandstone_flagstone_slab","undergarden:wigglewood_boat","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfences:deepslate_brick_pillar_wall","handcrafted:jungle_pillar_trim","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","aether:stone_axe_repairing","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","immersiveengineering:crafting/torch","minecraft:pink_concrete_powder","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","allthecompressed:compress/moss_block_1x","mcwdoors:jungle_stable_door","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","mcwfences:jungle_picket_fence","mcwdoors:print_jungle","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","minecraft:magenta_concrete_powder","undergarden:gloom_o_lantern","minecraft:sandstone_slab","mcwlights:dark_oak_tiki_torch","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","dyenamics:peach_concrete_powder","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:mud_brick_railing_gate","supplementaries:faucet","mcwfences:birch_curved_gate","simplylight:illuminant_purple_block_toggle","utilitix:directional_rail","mcwpaths:stone_windmill_weave","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","mcwfences:railing_blackstone_wall","mcwlights:soul_crimson_tiki_torch","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","supplementaries:flags/flag_light_blue","minecraft:chest","mcwroofs:blue_concrete_top_roof","supplementaries:pulley","mcwbridges:jungle_log_bridge_middle","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:jungle_fish_mount","aquaculture:light_hook","mcwroofs:green_concrete_steep_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:modern_sandstone_wall","dyenamics:banner/peach_banner","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","farmersdelight:cooking/mushroom_rice","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","biomesoplenty:dead_boat","croptopia:sushi","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","supplementaries:flags/flag_orange","dyenamics:bed/bubblegum_bed","mcwfences:mangrove_stockade_fence","mcwlights:soul_cherry_tiki_torch","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","securitycraft:reinforced_mangrove_fence","additionallanterns:normal_sandstone_chain","mcwpaths:sandstone_windmill_weave","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","additionallanterns:basalt_lantern","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","mcwfences:red_sandstone_railing_gate","mcwfences:crimson_highley_gate","mcwroofs:cobblestone_steep_roof","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:sandstone_pillar_wall","mcwfences:prismarine_pillar_wall","mcwpaths:stone_flagstone_slab","aether:stone_sword_repairing","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","simplylight:illuminant_black_block_on_toggle","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","mcwwindows:crimson_curtain_rod","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","mcwroofs:gray_concrete_roof","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","mcwroofs:yellow_concrete_lower_roof","handcrafted:blaze_trophy","domum_ornamentum:cobblestone_extra","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwroofs:magenta_concrete_lower_roof","mcwlights:iron_framed_torch","mcwbiomesoplenty:red_maple_hedge","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwlights:reinforced_torch","supplementaries:checker","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","minecraft:jungle_sign","mcwroofs:purple_concrete_roof","mcwfences:railing_mud_brick_wall","domum_ornamentum:cactus_extra","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","mcwpaths:sandstone_crystal_floor_path","mcwbridges:cobblestone_bridge_pier","mcwlights:classic_street_lamp","enderio:conduit_binder_composite","mcwfences:dark_oak_horse_fence","mcwwindows:oak_curtain_rod","mcwroofs:light_blue_concrete_top_roof","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","croptopia:steamed_rice","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","minecraft:purple_concrete_powder","mcwroofs:black_concrete_upper_lower_roof","minecraft:sugar_from_sugar_cane","mcwroofs:gray_concrete_upper_lower_roof","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwroofs:pink_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","minecraft:wooden_hoe","mcwbiomesoplenty:maple_picket_fence","mcwwindows:mangrove_curtain_rod","mcwbridges:cobblestone_bridge","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","farmersdelight:cutting_board","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","farmersdelight:cooking/dog_food","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","mcwdoors:jungle_classic_door","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwfurnitures:jungle_coffee_table","mcwpaths:stone_windmill_weave_stairs","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","securitycraft:reinforced_andesite","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwroofs:lime_concrete_roof","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","minecraft:wooden_axe","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","supplementaries:sign_post_jungle","mcwpaths:cobblestone_square_paving","dyenamics:bed/spring_green_bed","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","minecraft:mossy_cobblestone_from_moss_block","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwlights:jungle_tiki_torch","mcwwindows:jungle_plank_four_window","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","mcwfences:bamboo_wired_fence","mcwwindows:jungle_pane_window","mcwfences:crimson_curved_gate","mcwfurnitures:cabinet_door","additionallanterns:red_sandstone_lantern","mcwroofs:sandstone_top_roof","allthecompressed:compress/sand_1x","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","handcrafted:jungle_desk","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","supplementaries:flags/flag_magenta","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","minecraft:jungle_wood","mcwroofs:lime_concrete_attic_roof","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwbiomesoplenty:fir_horse_fence","mcwroofs:green_concrete_attic_roof","corail_woodcutter:spruce_woodcutter","additionallanterns:dark_prismarine_lantern","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwlights:covered_wall_lantern","minecraft:jungle_stairs","additionallanterns:smooth_stone_lantern","dyenamics:banner/bubblegum_banner","mcwfences:majestic_metal_fence_gate","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","mcwpaths:sandstone_honeycomb_paving","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","farmersdelight:flint_knife","additionallanterns:amethyst_lantern","utilitix:crude_furnace","biomesoplenty:maple_boat","croptopia:campfire_molasses","mcwlights:acacia_tiki_torch","utilitix:comparator_redirector_down","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","corail_woodcutter:warped_woodcutter","cfm:jungle_kitchen_sink_dark","securitycraft:reinforced_crimson_fence_gate","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","mcwfurnitures:stripped_jungle_wardrobe","silentgear:upgrade_base","mcwbiomesoplenty:rainbow_birch_hedge","mcwroofs:black_concrete_steep_roof","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","dyenamics:mint_concrete_powder","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","mcwfences:expanded_mesh_metal_fence","mcwlights:framed_torch","mcwfences:crimson_picket_fence","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","mcwfurnitures:jungle_chair","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:red_concrete_upper_steep_roof","mcwroofs:cobblestone_roof","additionallanterns:gold_lantern","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_orange_block_dyed","simplylight:illuminant_gray_block_dyed","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwtrpdoors:print_beach","travelersbackpack:sandstone","cfm:jungle_upgraded_fence","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","mcwfences:bamboo_highley_gate","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","mcwroofs:jungle_roof","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwfences:jungle_curved_gate","minecraft:torch","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwlights:striped_wall_lantern","mcwfences:acacia_pyramid_gate","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","minecraft:lever","dyenamics:cherenkov_concrete_powder","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","securitycraft:reinforced_bamboo_fence_gate","mcwroofs:pink_concrete_top_roof","mcwbiomesoplenty:magic_pyramid_gate","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","supplementaries:daub_frame","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfences:deepslate_grass_topped_wall","undergarden:slingshot","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","mcwfurnitures:jungle_desk","mcwpaths:sandstone_running_bond_slab","minecraft:crafting_table","twilightforest:time_boat","mcwroofs:jungle_planks_lower_roof","minecraft:sandstone_wall","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","ae2:network/blocks/crank","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","domum_ornamentum:blockbarreldeco_standing","mcwfences:spruce_wired_fence","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","securitycraft:motion_activated_light","mcwroofs:jungle_planks_steep_roof","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","mcwfences:double_curved_metal_fence","mcwroofs:sandstone_lower_roof","handcrafted:jungle_dining_bench","mcwbiomesoplenty:magic_wired_fence","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","mcwpaths:sandstone_basket_weave_paving","mcwroofs:sandstone_steep_roof","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwfences:oak_picket_fence","mcwroofs:brown_concrete_top_roof","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","quark:tweaks/crafting/utility/tools/stone_axe","mcwfences:prismarine_grass_topped_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","mcwroofs:blue_concrete_upper_steep_roof","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwroofs:lime_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","twigs:cobblestone_bricks_stonecutting","mcwroofs:light_gray_concrete_upper_steep_roof","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","framedblocks:framed_cube","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","supplementaries:flags/flag_blue","mcwbiomesoplenty:flowering_oak_hedge","mcwroofs:jungle_planks_top_roof","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","mcwroofs:lime_concrete_top_roof","additionallanterns:diamond_lantern","allthecompressed:compress/cobblestone_1x","twigs:mossy_bricks_from_moss_block","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","aquaculture:bobber","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","securitycraft:reinforced_oak_fence_gate","biomesoplenty:palm_boat","croptopia:chicken_and_rice","simplylight:illuminant_brown_block_on_dyed","securitycraft:reinforced_warped_fence","handcrafted:bear_trophy","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","minecraft:jungle_slab","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","silentgear:rough_rod","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","rftoolsbase:crafting_card","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwroofs:light_gray_concrete_steep_roof","mcwbiomesoplenty:palm_wired_fence","supplementaries:turn_table","supplementaries:flags/flag_pink","handcrafted:wither_skeleton_trophy","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwroofs:sandstone_attic_roof","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","mcwroofs:blackstone_upper_lower_roof","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwbiomesoplenty:cypress_hedge","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","mcwwindows:sandstone_window","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","mcwfences:red_sandstone_grass_topped_wall","mcwlights:light_blue_paper_lamp","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwfences:andesite_grass_topped_wall","mcwroofs:orange_concrete_steep_roof","mcwpaths:sandstone_flagstone_path","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","dyenamics:banner/persimmon_banner","mcwroofs:jungle_upper_lower_roof","forbidden_arcanus:wooden_blacksmith_gavel","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:silverfish_trophy","minecraft:jungle_button","mcwdoors:jungle_tropical_door","securitycraft:reinforced_birch_fence_gate","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","additionallanterns:diorite_lantern","mcwroofs:brown_concrete_lower_roof","mcwdoors:jungle_glass_door","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","mcwroofs:deepslate_roof","mcwroofs:orange_concrete_attic_roof","domum_ornamentum:cream_stone_bricks","simplylight:illuminant_pink_block_dyed","mcwfurnitures:jungle_triple_drawer","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","mcwfurnitures:jungle_end_table","undergarden:shard_torch","mcwfences:sandstone_grass_topped_wall","mcwfences:bamboo_picket_fence","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","handcrafted:goat_trophy","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","paraglider:paraglider","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","supplementaries:timber_cross_brace","simplylight:illuminant_lime_block_on_dyed","mcwroofs:purple_concrete_top_roof","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwbridges:jungle_bridge_pier","mcwfences:bamboo_pyramid_gate","mcwroofs:white_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_picket_fence","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","mcwroofs:white_concrete_attic_roof","domum_ornamentum:purple_cobblestone_extra","mcwroofs:blue_concrete_lower_roof","utilitarian:snad/snad","handcrafted:jungle_fancy_bed","minecraft:sandstone_stairs","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:snowblossom_hedge","mcwlights:cross_lantern","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","minecraft:red_dye_from_rose_bush","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","simplylight:illuminant_blue_block_on_toggle","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwroofs:blackstone_top_roof","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwwindows:jungle_window2","minecraft:jungle_fence","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","mcwroofs:lime_concrete_lower_roof","dyenamics:conifer_concrete_powder","mcwfences:mesh_metal_fence","mcwbiomesoplenty:empyreal_picket_fence","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","domum_ornamentum:cream_bricks","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","domum_ornamentum:green_cactus_extra","mcwlights:warped_tiki_torch","dyenamics:bed/cherenkov_bed","mcwroofs:jungle_lower_roof","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","simplylight:edge_light","mcwlights:bamboo_tiki_torch","mcwfences:railing_granite_wall","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","cfm:stripped_jungle_kitchen_drawer","mcwdoors:jungle_stable_head_door","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","mcwpaths:sandstone_crystal_floor_slab","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","appflux:insulating_resin","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:cobblestone_lower_roof","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","mcwfences:stone_pillar_wall","simplylight:illuminant_gray_block_toggle","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","mcwfences:spruce_stockade_fence","cfm:jungle_table","supplementaries:slingshot","mcwbiomesoplenty:empyreal_pyramid_gate","mcwlights:mangrove_tiki_torch","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","utilitix:armed_stand","botania:lexicon","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","mcwpaths:stone_running_bond","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:palm_hedge","mcwlights:green_paper_lamp","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","undergarden:undergarden_scaffolding","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwfurnitures:stripped_jungle_coffee_table","mcwfences:railing_sandstone_wall","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwroofs:magenta_concrete_attic_roof","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","handcrafted:skeleton_horse_trophy","mcwbiomesoplenty:hellbark_highley_gate","mcwfurnitures:stripped_jungle_double_drawer","additionallanterns:warped_lantern","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","minecraft:spruce_boat","dyenamics:banner/navy_banner","handcrafted:jungle_counter","mcwlights:brown_paper_lamp","minecraft:campfire","mcwdoors:jungle_barn_door","minecraft:paper","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","mcwpaths:stone_strewn_rocky_path","mcwroofs:pink_concrete_upper_steep_roof","farmersdelight:rice_bag","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","mcwlights:bell_wall_lantern","dyenamics:fluorescent_concrete_powder","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence"],toBeDisplayed:["corail_woodcutter:birch_woodcutter","mcwbridges:sandstone_bridge","mcwfences:granite_railing_gate","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwroofs:magenta_concrete_top_roof","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwfences:railing_deepslate_brick_wall","dyenamics:banner/lavender_banner","mcwpaths:stone_crystal_floor","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","handcrafted:spider_trophy","mcwfurnitures:jungle_bookshelf_drawer","mcwtrpdoors:jungle_cottage_trapdoor","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","mcwroofs:sandstone_upper_lower_roof","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","cfm:jungle_coffee_table","mcwroofs:yellow_concrete_roof","mcwpaths:jungle_planks_path","create:crafting/appliances/crafting_blueprint","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:umbran_horse_fence","utilitix:reinforced_rail","mcwfences:dark_oak_stockade_fence","mcwroofs:blackstone_attic_roof","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/jungle_logs_to_doors","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","corail_woodcutter:crimson_woodcutter","mcwdoors:jungle_nether_door","supplementaries:notice_board","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","securitycraft:reinforced_bamboo_fence","allthecompressed:compress/jungle_planks_1x","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwfurnitures:jungle_modern_desk","mcwroofs:gray_concrete_top_roof","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","mcwbiomesoplenty:mahogany_wired_fence","mcwfences:curved_metal_fence_gate","securitycraft:reinforced_cherry_fence_gate","mcwfences:acacia_hedge","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","mcwroofs:purple_concrete_attic_roof","energymeter:meter","dyenamics:banner/cherenkov_banner","mcwlights:covered_lantern","mcwlights:black_paper_lamp","mcwpaths:sandstone_dumble_paving","simplylight:rodlamp","mcwroofs:purple_concrete_upper_lower_roof","mcwroofs:deepslate_attic_roof","sliceanddice:slicer","minecolonies:shapetool","mcwfurnitures:stripped_jungle_bookshelf","supplementaries:crank","farmersdelight:cooking/cooked_rice","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwfurnitures:jungle_table","mcwtrpdoors:jungle_blossom_trapdoor","domum_ornamentum:blue_cobblestone_extra","securitycraft:reinforced_andesite_with_vanilla_diorite","mcwpaths:sandstone_crystal_floor_stairs","minecraft:sandstone_slab_from_sandstone_stonecutting","minecraft:green_dye","mcwfences:flowering_azalea_hedge","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","aquaculture:birch_fish_mount","aquaculture:wooden_fillet_knife","biomesoplenty:mossy_black_sand","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","minecraft:cobblestone_stairs","sophisticatedstorage:generic_limited_barrel_3","mcwpaths:sandstone_running_bond_path","simplylight:illuminant_red_block_toggle","sophisticatedstorage:generic_limited_barrel_4","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwpaths:cobblestone_dumble_paving","mcwpaths:sandstone_strewn_rocky_path","dyenamics:aquamarine_concrete_powder","mcwpaths:sandstone_flagstone_stairs","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwlights:striped_lantern","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","mcwlights:chain_lantern","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/grit_sand","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","mcwfences:warped_highley_gate","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","undergarden:torch_ditchbulb_paste","mcwfences:dark_oak_pyramid_gate","mcwroofs:sandstone_roof","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","cfm:jungle_kitchen_sink_light","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","additionallanterns:prismarine_lantern","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","biomesoplenty:mahogany_boat","mcwbiomesoplenty:fir_stockade_fence","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","additionallanterns:stone_bricks_lantern","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","utilitarian:utility/charcoal_from_campfire","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwroofs:magenta_concrete_upper_lower_roof","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwroofs:cyan_concrete_steep_roof","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","aether:stone_pickaxe_repairing","mcwpaths:sandstone_running_bond","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","minecraft:yellow_concrete_powder","minecraft:jungle_fence_gate","aquaculture:tin_can_to_iron_nugget","minecraft:red_concrete_powder","simplylight:illuminant_orange_block_on_toggle","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","dyenamics:spring_green_concrete_powder","mcwroofs:red_concrete_steep_roof","mcwpaths:sandstone_running_bond_stairs","minecraft:glass","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwlights:tavern_lantern","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","simplylight:edge_light_bottom_from_top","mcwroofs:brown_concrete_attic_roof","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","dyenamics:bed/navy_bed","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwdoors:jungle_bark_glass_door","minecraft:light_blue_concrete_powder","supplementaries:timber_frame","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","supplementaries:daub_cross_brace","mcwwindows:jungle_four_window","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","mcwlights:rustic_torch","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:soul_oak_tiki_torch","mcwlights:birch_tiki_torch","dyenamics:banner/honey_banner","handcrafted:jungle_bench","mcwlights:double_street_lamp","additionallanterns:quartz_lantern","twigs:mossy_cobblestone_bricks","mcwfences:stone_grass_topped_wall","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","minecraft:jungle_door","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","cfm:stripped_jungle_bedside_cabinet","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:magic_picket_fence","minecraft:cobblestone_wall_from_cobblestone_stonecutting","mcwbridges:sandstone_bridge_pier","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","mcwroofs:red_concrete_roof","securitycraft:reinforced_dark_oak_fence","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","cfm:stripped_jungle_cabinet","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","cfm:jungle_desk","simplylight:bulb","travelersbackpack:cactus","simplylight:illuminant_magenta_block_toggle","mcwbiomesoplenty:mahogany_horse_fence","mcwlights:wall_lantern","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","mcwfences:nether_brick_pillar_wall","mcwroofs:light_blue_concrete_steep_roof","mcwroofs:red_concrete_top_roof","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","mcwbridges:rope_jungle_bridge","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","minecraft:jungle_trapdoor","dyenamics:amber_concrete_powder","twigs:silt","additionallanterns:end_stone_lantern","handcrafted:jungle_shelf","mcwroofs:blue_concrete_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwpaths:stone_running_bond_slab","mcwpaths:stone_crystal_floor_path","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwbiomesoplenty:maple_curved_gate","mcwroofs:orange_concrete_roof","mcwroofs:lime_concrete_upper_steep_roof","mcwlights:blue_paper_lamp","simplylight:illuminant_yellow_block_toggle","securitycraft:sonic_security_system","mcwfurnitures:stripped_jungle_glass_table","minecraft:bamboo_raft","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwfences:diorite_railing_gate","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfurnitures:stripped_jungle_chair","mcwroofs:green_concrete_roof","mcwroofs:jungle_upper_steep_roof","minecraft:moss_carpet","mcwfences:railing_andesite_wall","mcwbiomesoplenty:mahogany_hedge","cfm:stripped_jungle_chair","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwwindows:sandstone_window2","mcwbiomesoplenty:willow_hedge","minecraft:white_concrete_powder","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","minecraft:mossy_stone_bricks_from_moss_block","mcwpaths:cobblestone_basket_weave_paving","mcwtrpdoors:jungle_swamp_trapdoor","minecraft:cherry_boat","silentgear:stone_rod","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","aquaculture:cooked_fish_fillet_from_campfire","quark:tweaks/crafting/utility/tools/stone_sword","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","mcwroofs:orange_concrete_lower_roof","enderio:stone_gear","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","utilitix:highspeed_rail","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwlights:pink_paper_lamp","mcwfences:end_brick_railing_gate","mcwpaths:sandstone_windmill_weave_path","additionallanterns:iron_lantern","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","simplylight:illuminant_light_gray_block_toggle","utilitix:hand_bell","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","mcwpaths:sandstone_flagstone_slab","undergarden:wigglewood_boat","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfences:deepslate_brick_pillar_wall","handcrafted:jungle_pillar_trim","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","aether:stone_axe_repairing","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","immersiveengineering:crafting/torch","minecraft:pink_concrete_powder","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","allthecompressed:compress/moss_block_1x","mcwdoors:jungle_stable_door","domum_ornamentum:architectscutter","mcwroofs:jungle_planks_roof","mcwfences:jungle_picket_fence","mcwdoors:print_jungle","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","minecraft:magenta_concrete_powder","undergarden:gloom_o_lantern","minecraft:sandstone_slab","mcwlights:dark_oak_tiki_torch","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","utilitarian:utility/jungle_logs_to_stairs","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","dyenamics:peach_concrete_powder","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:mud_brick_railing_gate","supplementaries:faucet","mcwfences:birch_curved_gate","simplylight:illuminant_purple_block_toggle","utilitix:directional_rail","mcwpaths:stone_windmill_weave","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","mcwfences:railing_blackstone_wall","mcwlights:soul_crimson_tiki_torch","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwbiomesoplenty:dead_wired_fence","supplementaries:flags/flag_light_blue","minecraft:chest","mcwroofs:blue_concrete_top_roof","supplementaries:pulley","mcwbridges:jungle_log_bridge_middle","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:jungle_fish_mount","aquaculture:light_hook","mcwroofs:green_concrete_steep_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:modern_sandstone_wall","dyenamics:banner/peach_banner","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","mcwbiomesoplenty:dead_curved_gate","farmersdelight:cooking/mushroom_rice","mcwpaths:sandstone_windmill_weave_stairs","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","biomesoplenty:dead_boat","croptopia:sushi","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","supplementaries:flags/flag_orange","dyenamics:bed/bubblegum_bed","mcwfences:mangrove_stockade_fence","mcwlights:soul_cherry_tiki_torch","cfm:jungle_upgraded_gate","mcwfences:mud_brick_pillar_wall","securitycraft:reinforced_mangrove_fence","additionallanterns:normal_sandstone_chain","mcwpaths:sandstone_windmill_weave","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","additionallanterns:basalt_lantern","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","mcwroofs:orange_concrete_top_roof","minecraft:blue_concrete_powder","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","mcwfences:red_sandstone_railing_gate","mcwfences:crimson_highley_gate","mcwroofs:cobblestone_steep_roof","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:sandstone_pillar_wall","mcwfences:prismarine_pillar_wall","mcwpaths:stone_flagstone_slab","aether:stone_sword_repairing","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","simplylight:illuminant_black_block_on_toggle","mcwpaths:cobblestone_clover_paving","mcwfurnitures:stripped_jungle_drawer_counter","mcwwindows:crimson_curtain_rod","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","mcwroofs:gray_concrete_roof","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","allthecompressed:compress/jungle_log_1x","mcwroofs:yellow_concrete_lower_roof","handcrafted:blaze_trophy","domum_ornamentum:cobblestone_extra","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwroofs:magenta_concrete_lower_roof","mcwlights:iron_framed_torch","mcwbiomesoplenty:red_maple_hedge","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwlights:reinforced_torch","supplementaries:checker","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","minecraft:jungle_sign","mcwroofs:purple_concrete_roof","mcwfences:railing_mud_brick_wall","domum_ornamentum:cactus_extra","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","mcwpaths:sandstone_crystal_floor_path","mcwbridges:cobblestone_bridge_pier","mcwlights:classic_street_lamp","enderio:conduit_binder_composite","mcwfences:dark_oak_horse_fence","mcwwindows:oak_curtain_rod","mcwroofs:light_blue_concrete_top_roof","deeperdarker:bloom_boat","mcwbiomesoplenty:willow_wired_fence","croptopia:steamed_rice","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","minecraft:purple_concrete_powder","mcwroofs:black_concrete_upper_lower_roof","minecraft:sugar_from_sugar_cane","mcwroofs:gray_concrete_upper_lower_roof","mcwbiomesoplenty:magic_hedge","securitycraft:reinforced_jungle_fence","mcwroofs:pink_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","mcwdoors:jungle_swamp_door","mcwroofs:lime_concrete_steep_roof","minecraft:wooden_hoe","mcwbiomesoplenty:maple_picket_fence","mcwwindows:mangrove_curtain_rod","mcwbridges:cobblestone_bridge","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","farmersdelight:cutting_board","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","farmersdelight:cooking/dog_food","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","mcwdoors:jungle_classic_door","mcwwindows:stripped_jungle_pane_window","twigs:jungle_table","mcwfurnitures:jungle_coffee_table","mcwpaths:stone_windmill_weave_stairs","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","securitycraft:reinforced_andesite","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwroofs:lime_concrete_roof","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","minecraft:wooden_axe","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:panelled_metal_fence_gate","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","supplementaries:sign_post_jungle","mcwpaths:cobblestone_square_paving","dyenamics:bed/spring_green_bed","aquaculture:cooked_fish_fillet","mcwwindows:metal_curtain_rod","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","minecraft:mossy_cobblestone_from_moss_block","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwlights:jungle_tiki_torch","mcwwindows:jungle_plank_four_window","minecraft:cut_sandstone_from_sandstone_stonecutting","supplementaries:flags/flag_lime","mcwfences:bamboo_wired_fence","mcwwindows:jungle_pane_window","mcwfences:crimson_curved_gate","mcwfurnitures:cabinet_door","additionallanterns:red_sandstone_lantern","mcwroofs:sandstone_top_roof","allthecompressed:compress/sand_1x","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","handcrafted:jungle_desk","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","supplementaries:flags/flag_magenta","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","minecraft:jungle_wood","mcwroofs:lime_concrete_attic_roof","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwbiomesoplenty:fir_horse_fence","mcwroofs:green_concrete_attic_roof","corail_woodcutter:spruce_woodcutter","additionallanterns:dark_prismarine_lantern","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwlights:covered_wall_lantern","minecraft:jungle_stairs","additionallanterns:smooth_stone_lantern","dyenamics:banner/bubblegum_banner","mcwfences:majestic_metal_fence_gate","handcrafted:jungle_chair","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","mcwpaths:sandstone_honeycomb_paving","mcwfurnitures:stripped_jungle_triple_drawer","utilitarian:utility/jungle_logs_to_boats","farmersdelight:flint_knife","additionallanterns:amethyst_lantern","utilitix:crude_furnace","biomesoplenty:maple_boat","croptopia:campfire_molasses","mcwlights:acacia_tiki_torch","utilitix:comparator_redirector_down","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","corail_woodcutter:warped_woodcutter","cfm:jungle_kitchen_sink_dark","securitycraft:reinforced_crimson_fence_gate","securitycraft:reinforced_warped_fence_gate","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","mcwfurnitures:stripped_jungle_wardrobe","silentgear:upgrade_base","mcwbiomesoplenty:rainbow_birch_hedge","mcwroofs:black_concrete_steep_roof","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","dyenamics:mint_concrete_powder","mcwfences:oak_horse_fence","mcwfences:railing_nether_brick_wall","mcwfences:expanded_mesh_metal_fence","mcwlights:framed_torch","mcwfences:crimson_picket_fence","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","mcwfurnitures:jungle_chair","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:red_concrete_upper_steep_roof","mcwroofs:cobblestone_roof","additionallanterns:gold_lantern","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","simplylight:illuminant_orange_block_dyed","simplylight:illuminant_gray_block_dyed","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwtrpdoors:print_beach","travelersbackpack:sandstone","cfm:jungle_upgraded_fence","simplylight:illuminant_block_on_dyed","mcwfences:fortress_metal_fence","mcwbiomesoplenty:jacaranda_hedge","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","aquaculture:neptunium_ingot_from_neptunium_block","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","mcwfences:bamboo_highley_gate","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","mcwfences:end_brick_grass_topped_wall","securitycraft:reinforced_mossy_stone_bricks_from_vanilla_moss","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","mcwroofs:jungle_roof","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","minecraft:mangrove_boat","mcwfences:jungle_curved_gate","minecraft:torch","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwlights:striped_wall_lantern","mcwfences:acacia_pyramid_gate","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","minecraft:lever","dyenamics:cherenkov_concrete_powder","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","securitycraft:reinforced_bamboo_fence_gate","mcwroofs:pink_concrete_top_roof","mcwbiomesoplenty:magic_pyramid_gate","utilitarian:utility/jungle_logs_to_pressure_plates","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","supplementaries:daub_frame","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfences:deepslate_grass_topped_wall","undergarden:slingshot","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","mcwfurnitures:jungle_desk","mcwpaths:sandstone_running_bond_slab","minecraft:crafting_table","twilightforest:time_boat","mcwroofs:jungle_planks_lower_roof","minecraft:sandstone_wall","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","ae2:network/blocks/crank","mcwtrpdoors:jungle_whispering_trapdoor","mcwroofs:brown_concrete_steep_roof","domum_ornamentum:blockbarreldeco_standing","mcwfences:spruce_wired_fence","mcwwindows:stripped_jungle_log_window","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","mcwbiomesoplenty:umbran_hedge","aquaculture:acacia_fish_mount","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","securitycraft:motion_activated_light","mcwroofs:jungle_planks_steep_roof","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","mcwfences:double_curved_metal_fence","mcwroofs:sandstone_lower_roof","handcrafted:jungle_dining_bench","mcwbiomesoplenty:magic_wired_fence","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:red_concrete_attic_roof","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","mcwpaths:sandstone_basket_weave_paving","mcwroofs:sandstone_steep_roof","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","mcwfences:oak_picket_fence","mcwroofs:brown_concrete_top_roof","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","quark:tweaks/crafting/utility/tools/stone_axe","mcwfences:prismarine_grass_topped_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","mcwroofs:blue_concrete_upper_steep_roof","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwroofs:lime_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwbiomesoplenty:magic_highley_gate","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","twigs:cobblestone_bricks_stonecutting","mcwroofs:light_gray_concrete_upper_steep_roof","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","framedblocks:framed_cube","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","supplementaries:flags/flag_blue","mcwbiomesoplenty:flowering_oak_hedge","mcwroofs:jungle_planks_top_roof","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","mcwroofs:lime_concrete_top_roof","additionallanterns:diamond_lantern","allthecompressed:compress/cobblestone_1x","twigs:mossy_bricks_from_moss_block","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","mcwfences:oak_curved_gate","aquaculture:bobber","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","aquaculture:brown_mushroom_from_fish","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","securitycraft:reinforced_oak_fence_gate","biomesoplenty:palm_boat","croptopia:chicken_and_rice","simplylight:illuminant_brown_block_on_dyed","securitycraft:reinforced_warped_fence","handcrafted:bear_trophy","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","minecraft:jungle_slab","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","silentgear:rough_rod","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","rftoolsbase:crafting_card","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwroofs:light_gray_concrete_steep_roof","mcwbiomesoplenty:palm_wired_fence","supplementaries:turn_table","supplementaries:flags/flag_pink","handcrafted:wither_skeleton_trophy","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwfurnitures:stripped_jungle_table","mcwroofs:sandstone_attic_roof","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","mcwroofs:blackstone_upper_lower_roof","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwbiomesoplenty:cypress_hedge","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","mcwwindows:sandstone_window","aquaculture:oak_fish_mount","supplementaries:flags/flag_green","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwroofs:pink_concrete_lower_roof","securitycraft:reinforced_acacia_fence_gate","mcwfences:red_sandstone_grass_topped_wall","mcwlights:light_blue_paper_lamp","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","minecraft:jungle_pressure_plate","additionallanterns:granite_lantern","mcwfences:andesite_grass_topped_wall","mcwroofs:orange_concrete_steep_roof","mcwpaths:sandstone_flagstone_path","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","dyenamics:bed/persimmon_bed","dyenamics:banner/persimmon_banner","mcwroofs:jungle_upper_lower_roof","forbidden_arcanus:wooden_blacksmith_gavel","mcwbiomesoplenty:pine_horse_fence","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:silverfish_trophy","minecraft:jungle_button","mcwdoors:jungle_tropical_door","securitycraft:reinforced_birch_fence_gate","twigs:lamp","minecraft:stone","dyenamics:bed/lavender_bed","additionallanterns:diorite_lantern","mcwroofs:brown_concrete_lower_roof","mcwdoors:jungle_glass_door","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","mcwroofs:deepslate_roof","mcwroofs:orange_concrete_attic_roof","domum_ornamentum:cream_stone_bricks","simplylight:illuminant_pink_block_dyed","mcwfurnitures:jungle_triple_drawer","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","mcwfurnitures:jungle_end_table","undergarden:shard_torch","mcwfences:sandstone_grass_topped_wall","mcwfences:bamboo_picket_fence","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","handcrafted:goat_trophy","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","paraglider:paraglider","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","supplementaries:timber_cross_brace","simplylight:illuminant_lime_block_on_dyed","mcwroofs:purple_concrete_top_roof","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwbridges:jungle_bridge_pier","mcwfences:bamboo_pyramid_gate","mcwroofs:white_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_picket_fence","mcwroofs:cyan_concrete_attic_roof","additionallanterns:cobbled_deepslate_lantern","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","mcwroofs:white_concrete_attic_roof","domum_ornamentum:purple_cobblestone_extra","mcwroofs:blue_concrete_lower_roof","utilitarian:snad/snad","handcrafted:jungle_fancy_bed","minecraft:sandstone_stairs","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:snowblossom_hedge","mcwlights:cross_lantern","minecraft:oak_boat","sophisticatedstorage:jungle_chest","minecraft:cobblestone_slab_from_cobblestone_stonecutting","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","minecraft:red_dye_from_rose_bush","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","simplylight:illuminant_blue_block_on_toggle","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwroofs:blackstone_top_roof","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwwindows:jungle_window2","minecraft:jungle_fence","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","mcwroofs:lime_concrete_lower_roof","dyenamics:conifer_concrete_powder","mcwfences:mesh_metal_fence","mcwbiomesoplenty:empyreal_picket_fence","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","mcwlights:yellow_paper_lamp","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","domum_ornamentum:cream_bricks","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","domum_ornamentum:green_cactus_extra","mcwlights:warped_tiki_torch","dyenamics:bed/cherenkov_bed","mcwroofs:jungle_lower_roof","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","simplylight:edge_light","mcwlights:bamboo_tiki_torch","mcwfences:railing_granite_wall","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","cfm:stripped_jungle_kitchen_drawer","mcwdoors:jungle_stable_head_door","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","mcwpaths:sandstone_crystal_floor_slab","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","appflux:insulating_resin","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:cobblestone_lower_roof","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","mcwfences:stone_pillar_wall","simplylight:illuminant_gray_block_toggle","securitycraft:reinforced_mossy_cobblestone_from_vanilla_moss","mcwfences:spruce_stockade_fence","cfm:jungle_table","supplementaries:slingshot","mcwbiomesoplenty:empyreal_pyramid_gate","mcwlights:mangrove_tiki_torch","simplylight:illuminant_green_block_toggle","mcwfences:end_brick_pillar_wall","utilitix:armed_stand","botania:lexicon","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","mcwpaths:stone_running_bond","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:palm_hedge","mcwlights:green_paper_lamp","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwfurnitures:stripped_jungle_end_table","undergarden:undergarden_scaffolding","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwfurnitures:stripped_jungle_coffee_table","mcwfences:railing_sandstone_wall","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwroofs:magenta_concrete_attic_roof","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","handcrafted:skeleton_horse_trophy","mcwbiomesoplenty:hellbark_highley_gate","mcwfurnitures:stripped_jungle_double_drawer","additionallanterns:warped_lantern","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","minecraft:spruce_boat","dyenamics:banner/navy_banner","handcrafted:jungle_counter","mcwlights:brown_paper_lamp","minecraft:campfire","mcwdoors:jungle_barn_door","minecraft:paper","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","mcwpaths:stone_strewn_rocky_path","mcwroofs:pink_concrete_upper_steep_roof","farmersdelight:rice_bag","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","mcwlights:bell_wall_lantern","dyenamics:fluorescent_concrete_powder","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:7045,warning_level:0}}