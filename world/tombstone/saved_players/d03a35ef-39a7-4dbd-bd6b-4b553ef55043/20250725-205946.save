{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:386,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],Air:300s,Attributes:[{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;**********,-847687954,-**********,816511786],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:63,wirelessNetwork:16},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:1,furnaces:{furnace0:{X:9,Y:253,Z:-163}}},"ironfurnaces:show_config":{show:1},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:2229670},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"allthetweaks:greg_star_block",ItemStack:{Count:1b,id:"allthetweaks:greg_star_block"}}],SelectedRecipe:"allthetweaks:greg_star_block"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:171,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:1b,isshrunk:1b,scale:0.4999999f,width:0.0f},"solcarrot:food":{foodList:["allthemodium:allthemodium_carrot"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:9,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:12919261778018L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-1003026431,-1564523672,-2049136714,-2121383950]},{FromDim:"minecraft:overworld",FromPos:96207268921409L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;*********,1808878414,-1537121181,1628954882]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["36d86fd7-e43f-48b5-a2b9-6969a1ba515c","6c8881a8-dbdf-465b-bb3e-0c97f47d45a4","70e61f07-a350-437a-ae01-15e3ef9a1bab","cca8d749-2936-4d9a-a535-4335d9016efe"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-34,tb_last_ground_location_y:265,tb_last_ground_location_z:-201,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},"aae$downkey":1b,"aae$nokey":1b,"aae$upkey":0b,apoth_reforge_seed:0,"quark:locked_once":1b,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedStorageSettings:{}},Health:20.0f,HurtByTimestamp:2065817,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"constructionwand:infinity_wand",tag:{wand_options:{cores:["constructionwand:core_destruction"],cores_sel:0b,lock:"nolock",match:"any"}}},{Count:1b,Slot:1b,id:"mekanism:atomic_disassembler",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"1000000"}],mode:2}}},{Count:1b,Slot:2b,id:"ae2wtlib:wireless_universal_terminal",tag:{accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},blankPattern:[{Count:58b,Slot:0,id:"ae2:blank_pattern"}],crafting:1b,currentTerminal:"crafting",encodedInputs:[{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"},{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"},{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"},{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"},{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"},{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"},{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"},{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"},{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star"}],encodedOutputs:[{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star_block"}],ex_pattern_access:1b,filter_type:"ALL",internalCurrentPower:4800000.0d,internalMaxPower:4800000.0d,mode:"CRAFTING",pattern_encoding:1b,show_pattern_providers:"VISIBLE",sort_by:"NAME",sort_direction:"ASCENDING",substitute:0b,substituteFluids:1b,view_mode:"ALL"}},{Count:1b,Slot:7b,id:"ae2:certus_quartz_wrench",tag:{GT.Behaviours:{}}},{Count:1b,Slot:8b,id:"mekanism:configurator",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"60000"}]}}},{Count:42b,Slot:9b,id:"fluxnetworks:flux_point"},{Count:1b,Slot:10b,id:"draconicevolution:advanced_dislocator",tag:{blink:0b,fuel:20,locations:[{dim:"allthemodium:mining",heading:1b,lock:0b,name:"AE",pitch:-40.350193f,x:-31.206511791921073d,y:253.0d,yaw:0.47607422f,z:-30.020517289479738d},{dim:"allthemodium:mining",heading:1b,lock:0b,name:"gt",pitch:-36.150192f,x:0.2642502295004542d,y:253.0d,yaw:0.3251953f,z:-162.11291741634645d}],selected:0}},{Count:1b,Slot:11b,id:"gtceu:terminal"},{Count:1b,Slot:12b,id:"tiab:time_in_a_bottle",tag:{storedTime:2926340,totalAccumulatedTime:4198940}},{Count:1b,Slot:17b,id:"ae2:processing_pattern",tag:{in:[{"#":1L,"#c":"ae2:i",id:"allthetweaks:greg_star_block"},{"#":8L,"#c":"ae2:i",id:"gtceu:uhv_energy_input_hatch_16a"},{"#":1L,"#c":"ae2:i",id:"kubejs:micro_universe_drill_ship"},{"#":16L,"#c":"ae2:i",id:"kubejs:micro_universe_catalyst"},{"#":12000L,"#c":"ae2:f",id:"gtceu:star_matter_plasma"},{"#":1574640L,"#c":"ae2:f",id:"gtceu:nether_star"},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}],out:[{"#":1L,"#c":"ae2:i",id:"gtceu:max_energy_input_hatch"},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}]}}],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:mining",pos:[I;-16,260,-155]},Motion:[0.0d,0.0d,0.0d],OnGround:0b,PortalCooldown:0,Pos:[7.903121184164225d,263.67928128240857d,-195.1559288925895d],Railways_DataVersion:2,Rotation:[61.464355f,74.400024f],Score:-33358,SelectedItemSlot:2,SleepTimer:0s,Spigot.ticksLived:2229667,UUID:[I;-801491473,967265725,-1117041835,1056264259],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-824634560250L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:26,XpP:0.097826086f,XpSeed:2091961377,XpTotal:1006,abilities:{flySpeed:0.05f,flying:1b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752376894633L,keepLevel:0b,lastKnownName:"Saig_Yuyuko",lastPlayed:1753448386417L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:2.7913747f,foodLevel:20,foodSaturationLevel:5.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["botania:dreamwood_planks","botania:star_sword","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","croptopia:mead","botania:alfheim_portal","mcwfurnitures:stripped_acacia_counter","mcwwindows:birch_four_window","biomesoplenty:fir_boat","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","modularrouters:energy_distributor_module","mcwroofs:blue_terracotta_attic_roof","handcrafted:spider_trophy","mcwbiomesoplenty:willow_waffle_door","mcwroofs:lime_terracotta_lower_roof","ae2:network/cables/dense_smart_green","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","ad_astra:cyan_industrial_lamp","mcwlights:chain_wall_lantern","allthemods:ae2/smart_dense_to_smart_normal","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","mcwtrpdoors:acacia_barrel_trapdoor","xnet:connector_green","advanced_ae:smallappupgrade","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mcwfurnitures:stripped_acacia_bookshelf_cupboard","mythicbotany:central_rune_holder","sophisticatedbackpacks:void_upgrade","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","railcraft:bushing_gear_bronze","mcwroofs:acacia_upper_steep_roof","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","mcwtrpdoors:acacia_barn_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","utilitix:oak_shulker_boat_with_shell","create:crafting/kinetics/gearbox","botania:terrasteel_block","sophisticatedstorage:copper_to_iron_tier_upgrade","mcwbiomesoplenty:umbran_modern_door","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","rftoolsutility:screen_link","minecraft:leather_helmet","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","mcwbiomesoplenty:redwood_nether_door","energymeter:meter","mcwlights:covered_lantern","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","botania:elementium_sword","supplementaries:bubble_blower","supplementaries:crank","securitycraft:secret_bamboo_sign_item","minecraft:recovery_compass","connectedglass:clear_glass_gray_pane2","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","cfm:acacia_cabinet","mcwbiomesoplenty:willow_bark_glass_door","minecraft:pink_terracotta","aiotbotania:livingrock_axe","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","mcwbiomesoplenty:empyreal_classic_door","connectedglass:clear_glass_brown_pane2","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","ae2:network/cables/smart_fluix_clean","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:dye_pink_wool","cfm:oak_kitchen_counter","minecraft:fire_charge","minecraft:golden_hoe","mcwfurnitures:acacia_lower_triple_drawer","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","railcraft:any_detector","ad_astra:gray_industrial_lamp","alchemistry:reactor_energy","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","sophisticatedstorage:controller","aiotbotania:livingrock_shovel","computercraft:printer","mcwroofs:stone_bricks_upper_steep_roof","minecraft:stone_button","mcwbiomesoplenty:redwood_bark_glass_door","simplylight:illuminant_cyan_block_on_dyed","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","mcwbiomesoplenty:umbran_japanese2_door","travelersbackpack:dye_lime_sleeping_bag","railcraft:brass_ingot_crafted_with_ingots","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","pneumaticcraft:transfer_gadget","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","enderio:extraction_speed_upgrade_3","mcwroofs:cobblestone_upper_lower_roof","enderio:extraction_speed_upgrade_4","mcwfences:deepslate_railing_gate","enderio:extraction_speed_upgrade_1","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","botania:open_bucket","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","cfm:stripped_crimson_mail_box","enderio:resetting_lever_five","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:hellbark_beach_door","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","minecraft:lime_candle","mcwbiomesoplenty:umbran_classic_door","ae2:block_cutter/slabs/sky_stone_slab","cfm:purple_cooler","ae2:network/parts/terminals","securitycraft:keypad_frame","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","botania:terrasteel_leggings","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","mcwbiomesoplenty:hellbark_bark_glass_door","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","pneumaticcraft:vortex_tube","blue_skies:glowing_nature_stone","xnet:connector_routing","mcwwindows:warped_louvered_shutter","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","megacells:network/mega_interface","mcwroofs:green_terracotta_attic_roof","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:palm_bark_glass_door","ae2:network/blocks/spatial_anchor","mcwbiomesoplenty:umbran_cottage_door","utilitix:directional_highspeed_rail","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","mcwbiomesoplenty:pine_stable_head_door","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","cfm:stripped_acacia_park_bench","securitycraft:secret_bamboo_hanging_sign","ae2:network/blocks/storage_chest","mcwroofs:oak_roof","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwbiomesoplenty:palm_japanese2_door","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","ae2:network/wireless_booster","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","mcwfences:blackstone_brick_railing_gate","expatternprovider:ingredient_buffer","sophisticatedbackpacks:filter_upgrade","connectedglass:tinted_borderless_glass_gray2","mcwbiomesoplenty:maple_japanese_door","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","ae2:network/cables/dense_covered_purple","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwfurnitures:acacia_stool_chair","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","allthearcanistgear:unobtainium_hat_smithing","create:crafting/materials/andesite_alloy","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","mcwfurnitures:stripped_acacia_modern_chair","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwlights:golden_chandelier","minecraft:armor_stand","mcwpaths:stone_flagstone_stairs","chemlib:sodium_block_to_ingot","cfm:stripped_birch_kitchen_drawer","mcwfurnitures:stripped_acacia_lower_triple_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","twigs:chiseled_bricks","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","mcwlights:tavern_wall_lantern","croptopia:cheese","dyenamics:bed/rose_bed","dyenamics:navy_wool","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","mcwwindows:magenta_mosaic_glass_pane","alltheores:platinum_gear","mcwtrpdoors:metal_full_trapdoor","botania:cosmetic_black_bowtie","connectedglass:borderless_glass_pink_pane2","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwbiomesoplenty:willow_beach_door","mcwpaths:andesite_running_bond_slab","mcwfurnitures:stripped_oak_lower_triple_drawer","utilitarian:utility/oak_logs_to_boats","croptopia:potato_chips","minecraft:chiseled_stone_bricks_stone_from_stonecutting","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","chemlib:sodium_ingot_from_blasting_sodium_dust","alltheores:mek_processing/iridium/ingot/from_dust_blasting","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","botania:manasteel_axe","mcwwindows:oak_blinds","mcwtrpdoors:acacia_cottage_trapdoor","mcwfences:birch_wired_fence","mcwbiomesoplenty:empyreal_beach_door","cfm:dye_orange_picket_fence","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","mcwdoors:metal_hospital_door","railcraft:signal_circuit","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","mcwbiomesoplenty:mahogany_swamp_door","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","cfm:stripped_acacia_kitchen_counter","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","supplementaries:flags/flag_light_blue","mcwbiomesoplenty:maple_modern_door","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","botania:gray_petal_block","mcwbiomesoplenty:empyreal_cottage_door","utilitix:tiny_charcoal_to_tiny","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","allthecompressed:compress/redstone_block_1x","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","supplementaries:sign_post_oak","minecraft:dye_gray_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","botania:terrasteel_boots","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","gtceu:shapeless/data_stick_nbt","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","chemlib:gallium_ingot_from_blasting_gallium_dust","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","ae2:network/cables/dense_covered_pink","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","ae2:tools/portable_item_cell_256k","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","securitycraft:secret_crimson_sign_item","mcwbiomesoplenty:empyreal_window","mcwroofs:purple_terracotta_upper_lower_roof","mcwbiomesoplenty:jacaranda_nether_door","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","ae2:network/parts/cable_anchor","mcwfurnitures:acacia_counter","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","blue_skies:cake_compat","utilitarian:utility/acacia_logs_to_doors","cfm:stripped_acacia_chair","ae2:network/blocks/crystal_processing_growth_accelerator","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","pneumaticcraft:liquid_hopper","botania:green_pavement","twilightforest:mining_boat","aether:golden_dart","enderio:vibrant_alloy_block","handcrafted:terracotta_cup","ad_astra:ti_69","mcwroofs:acacia_attic_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","mcwbiomesoplenty:hellbark_window2","mcwbiomesoplenty:mahogany_nether_door","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:fir_bark_glass_door","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","botania:petal_brown","connectedglass:borderless_glass_lime2","ae2:network/parts/terminals_crafting","cfm:stripped_oak_chair","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","cfm:diving_board","mcwwindows:oak_shutter","cfm:rock_path","botania:mana_spreader","connectedglass:clear_glass_yellow2","connectedglass:scratched_glass_black1","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","mcwbiomesoplenty:redwood_waffle_door","allthecompressed:compress/energetic_alloy_block_1x","botania:slingshot","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","botania:lens_tripwire","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwfences:spruce_pyramid_gate","mcwbiomesoplenty:willow_barn_door","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","allthecompressed:compress/energetic_alloy_block_2x","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","mcwbiomesoplenty:umbran_nether_door","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","mcwwindows:crimson_planks_four_window","minecraft:piston","ad_astra:large_gas_tank","mcwbiomesoplenty:maple_waffle_door","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","undergarden:regalium_block","mcwbiomesoplenty:umbran_plank_pane_window","botania:virus_necrodermal","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","mcwbiomesoplenty:dead_modern_door","mcwbiomesoplenty:fir_stable_door","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","mcwbiomesoplenty:palm_classic_door","mcwdoors:acacia_cottage_door","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","chemlib:potassium_ingot_to_block","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","domum_ornamentum:black_floating_carpet","supplementaries:daub_brace","mcwfences:spruce_horse_fence","croptopia:beef_jerky","handcrafted:stackable_book","handcrafted:oak_shelf","mcwbiomesoplenty:magic_modern_door","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","computercraft:speaker","ad_astra:space_helmet","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","mcwbiomesoplenty:palm_tropical_door","additionallanterns:bricks_lantern","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","mcwbiomesoplenty:palm_cottage_door","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","rftoolsbuilder:vehicle_control_module","botania:petal_gray","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","botania:fire_rod","securitycraft:reinforced_crimson_fence_gate","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","mcwroofs:light_gray_steep_roof","botania:cosmetic_red_ribbons","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","botania:elementium_shears","create:layered_andesite_from_stone_types_andesite_stonecutting","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:dye_lime_picket_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","securitycraft:secret_mangrove_hanging_sign","mcwpaths:andesite_square_paving","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","mcwfurnitures:acacia_double_wardrobe","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","mythicbotany:yggdrasil_branch","mcwroofs:red_terracotta_attic_roof","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","computercraft:pocket_computer_advanced","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","mcwwindows:white_curtain","mcwroofs:white_terracotta_roof","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","connectedglass:clear_glass_lime_pane2","alltheores:iridium_plate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","everythingcopper:copper_hopper","minecolonies:potato_soup","botania:spark_upgrade_dispersive","mcwbiomesoplenty:dead_bark_glass_door","mcwbiomesoplenty:fir_paper_door","mcwbiomesoplenty:pine_mystic_door","chemlib:sodium_ingot_from_smelting_sodium_dust","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:banner/mint_banner","botania:dreamwood_wand","botania:cosmetic_unicorn_horn","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","dyenamics:lavender_stained_glass","twilightforest:time_boat","mcwbiomesoplenty:fir_japanese_door","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","alchemistry:fusion_chamber_controller","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/smart_gray","mcwfurnitures:stripped_acacia_bookshelf_drawer","ae2:network/cables/dense_covered_white","botania:cosmetic_thick_eyebrows","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","cfm:red_cooler","botania:dreamwood_wall","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","create:crafting/logistics/pulse_extender","xnet:facade","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_1","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","cfm:acacia_desk_cabinet","undergarden:grongle_boat","ae2:decorative/quartz_fixture","mcwbiomesoplenty:dead_nether_door","cfm:acacia_blinds","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","mcwbiomesoplenty:dead_paper_door","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","mcwwindows:spruce_curtain_rod","ae2:network/blocks/quantum_ring","aether:skyroot_loom","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwfurnitures:stripped_acacia_glass_table","mcwfences:diorite_grass_topped_wall","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwfurnitures:acacia_chair","mcwbiomesoplenty:palm_nether_door","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:fir_western_door","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","simplylight:illuminant_yellow_block_on_toggle","dyenamics:honey_wool","mcwroofs:acacia_top_roof","xnet:advanced_connector_yellow_dye","enderio:soul_chain","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","minecraft:diamond_helmet","mcwfurnitures:acacia_glass_table","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","connectedglass:borderless_glass_lime_pane2","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bigreactors:smelting/graphite_from_coalblock","handcrafted:tropical_fish_trophy","simplylight:illuminant_brown_block_dyed","minecraft:compass","cfm:stripped_acacia_table","mcwbiomesoplenty:jacaranda_plank_pane_window","bigreactors:energizer/computerport","littlelogistics:vacuum_barge","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","mcwbiomesoplenty:maple_mystic_door","expatternprovider:silicon_block","minecraft:item_frame","itemcollectors:basic_collector","minecraft:loom","ad_astra:steel_engine","enderio:resetting_lever_three_hundred_inv","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","ad_astra:small_lime_industrial_lamp","sophisticatedstorage:stack_downgrade_tier_3","minecraft:arrow","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","dyenamics:wine_wool","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwbiomesoplenty:mahogany_stable_head_door","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwpaths:brick_flagstone_slab","expatternprovider:ei_part","alltheores:nickel_plate","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","advanced_ae:quantumaccel","chemlib:potassium_ingot_to_nugget","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","dyenamics:banner/rose_banner","enderio:redstone_alloy_nugget_to_ingot","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","pneumaticcraft:logistics_frame_storage","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","connectedglass:borderless_glass_brown_pane2","botania:mana_pool","cfm:brown_cooler","delightful:knives/terra_knife","mcwfences:diorite_pillar_wall","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","undergarden:shard_torch","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","paraglider:paraglider","botania:petal_gray_double","expatternprovider:ebus_in","sophisticatedstorage:shulker_box","computercraft:disk_drive","mcwroofs:blue_striped_awning","pneumaticcraft:wall_lamp_red","mcwbiomesoplenty:mahogany_modern_door","immersiveengineering:crafting/component_steel","utilitarian:utility/acacia_logs_to_boats","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","botania:cosmetic_lusitanic_shield","mcwfences:mangrove_hedge","sophisticatedbackpacks:compacting_upgrade","chemlib:tantalum_ingot_from_smelting_tantalum_dust","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","ae2:network/cables/covered_light_blue","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","enderio:resetting_lever_thirty_inv_from_base","botania:cosmetic_botanist_emblem","minecraft:jukebox","mcwwindows:jungle_curtain_rod","ad_astra:small_brown_industrial_lamp","mcwbiomesoplenty:palm_barn_glass_door","cfm:acacia_chair","mcwwindows:oak_window","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","ae2:network/cables/smart_brown","mcwwindows:spruce_plank_pane_window","alltheores:signalum_rod","create:crafting/kinetics/white_seat","chemlib:gallium_ingot_to_block","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","ae2:network/cables/covered_orange","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","railcraft:coal_coke_block_from_coal_coke","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","minecraft:baked_potato_from_campfire_cooking","connectedglass:tinted_borderless_glass_brown2","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","mcwroofs:red_terracotta_top_roof","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gray_terracotta_attic_roof","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","securitycraft:reinforced_light_gray_stained_glass","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwroofs:bricks_attic_roof","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","connectedglass:borderless_glass_yellow2","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","ad_astra:airlock","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","chemlib:magnesium_ingot_from_blasting_magnesium_dust","minecraft:end_stone_bricks","croptopia:egg_roll","mcwbiomesoplenty:dead_waffle_door","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","allthecompressed:compress/vibrant_alloy_block_1x","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","botania:lens_firework","mcwfurnitures:stripped_acacia_drawer_counter","expatternprovider:oversize_interface_alt","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","minecraft:dye_brown_wool","aether:iron_pendant","securitycraft:redstone_module","mcwbiomesoplenty:pine_bamboo_door","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","minecraft:orange_candle","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","botania:quartz_sunny","create:framed_glass_from_glass_colorless_stonecutting","supplementaries:candle_holders/candle_holder_brown_dye","mcwfurnitures:stripped_acacia_cupboard_counter","botania:terrasteel_helmet","mcwbiomesoplenty:dead_swamp_door","railcraft:signal_lamp","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","sophisticatedstorage:decoration_table","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:redwood_classic_door","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","allthecompressed:compress/vibrant_alloy_block_2x","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","mcwbiomesoplenty:magic_waffle_door","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:gold_block","botania:floating_pure_daisy","minecraft:spruce_boat","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","sophisticatedstorage:storage_input","mcwbiomesoplenty:redwood_cottage_door","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","ae2:network/cables/dense_smart_cyan","minecraft:paper","travelersbackpack:dye_orange_sleeping_bag","chemlib:tantalum_ingot_to_block","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","cfm:acacia_bedside_cabinet","connectedglass:scratched_glass_cyan2","securitycraft:secret_mangrove_sign_item","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","botania:conversions/terrasteel_block_deconstruct","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwroofs:light_gray_terracotta_roof","supplementaries:candle_holders/candle_holder_gray_dye","botania:incense_plate","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:lime_terracotta_steep_roof","mcwlights:golden_small_chandelier","mcwbiomesoplenty:willow_paper_door","mcwwindows:lime_mosaic_glass","railways:stonecutting/riveted_locometal","securitycraft:reinforced_observer","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","mcwfences:acacia_curved_gate","allthemodium:unobtainium_rod","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","mcwbiomesoplenty:redwood_swamp_door","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","alltheores:lead_rod","mcwbiomesoplenty:jacaranda_western_door","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","mcwfurnitures:stripped_acacia_modern_wardrobe","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","littlelogistics:spring","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwdoors:acacia_beach_door","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","connectedglass:borderless_glass_gray_pane2","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","supplementaries:candle_holders/candle_holder_yellow_dye","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","minecraft:diamond_pickaxe","domum_ornamentum:blockbarreldeco_onside","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","alltheores:platinum_plate","ad_astra:encased_steel_block","mcwroofs:orange_terracotta_upper_steep_roof","fluxnetworks:wipe_fluxpoint","pneumaticcraft:reinforced_brick_wall","ae2:network/cables/smart_purple","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","utilitarian:utility/acacia_logs_to_stairs","rftoolsbuilder:green_shield_template_block","cfm:acacia_coffee_table","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","securitycraft:codebreaker","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:light_blue_picket_gate","allthearcanistgear:unobtainium_leggings_smithing","constructionwand:infinity_wand","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","botania:cosmetic_hyper_plus","expatternprovider:assembler_matrix_wall","mcwroofs:black_roof","securitycraft:reinforced_gray_stained_glass_pane_from_glass","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","botania:cosmetic_eyepatch","cfm:blue_kitchen_counter","mcwbiomesoplenty:maple_four_window","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","chemlib:magnesium_nugget_to_ingot","mcwwindows:stripped_oak_log_window","ae2:network/cables/smart_magenta","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","botania:cosmetic_questgiver_mark","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mythicbotany:mana_collector","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","supplementaries:statue","mcwbiomesoplenty:mahogany_tropical_door","cfm:pink_grill","mcwroofs:gutter_middle_orange","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","securitycraft:secret_jungle_sign_item","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","ae2:network/blocks/pattern_providers_interface","minecraft:oak_stairs","alchemistry:liquifier","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","ad_astra:small_orange_industrial_lamp","botania:aura_ring_greater","mcwbiomesoplenty:maple_stable_head_door","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","securitycraft:reinforced_piston","cfm:purple_kitchen_counter","securitycraft:reinforced_yellow_stained_glass","alltheores:bronze_plate","expatternprovider:threshold_export_bus","mcwpaths:brick_crystal_floor_slab","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","mcwfurnitures:acacia_double_drawer","aquaculture:tin_can_to_iron_nugget","alltheores:enderium_plate","mcwwindows:yellow_curtain","connectedglass:clear_glass_orange_pane2","mcwwindows:purple_mosaic_glass","allthecompressed:compress/acacia_log_1x","modularrouters:player_module","pneumaticcraft:pressure_chamber_wall","xnet:connector_yellow","mcwbiomesoplenty:pine_classic_door","mcwbiomesoplenty:hellbark_plank_pane_window","allthecompressed:decompress/vibrant_alloy_block_1x","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","alltheores:raw_iridium_block","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwbiomesoplenty:pine_cottage_door","domum_ornamentum:gray_floating_carpet","ae2:network/cables/smart_red","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","ad_astra:yellow_industrial_lamp","cfm:dye_brown_picket_gate","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","chemlib:tantalum_ingot_from_blasting_tantalum_dust","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","securitycraft:secret_spruce_sign_item","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwbiomesoplenty:pine_nether_door","cfm:oak_upgraded_fence","mcwbiomesoplenty:hellbark_barn_glass_door","deepresonance:radiation_monitor","botania:placeholder","railcraft:steel_gear","immersiveengineering:crafting/treated_wood_horizontal","allthemodium:piglich_heart_block","enderio:pulsating_alloy_nugget","sophisticatedstorage:crafting_upgrade","bigreactors:smelting/graphite_from_charcoal","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","minecraft:stone_bricks","securitycraft:reinforced_red_stained_glass","connectedglass:borderless_glass_cyan2","connectedglass:tinted_borderless_glass_orange2","mcwbiomesoplenty:jacaranda_wired_fence","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","minecraft:dye_orange_wool","aether:leather_gloves","xnet:netcable_green_dye","utilitarian:utility/acacia_logs_to_slabs","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","mcwbiomesoplenty:jacaranda_bamboo_door","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","cfm:yellow_kitchen_drawer","twilightforest:dark_boat","minecraft:gunpowder","travelersbackpack:bookshelf","biomesoplenty:umbran_boat","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","botania:dye_light_gray","mcwbiomesoplenty:redwood_four_panel_door","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","immersiveengineering:crafting/alu_wallmount","pylons:potion_filter","enderio:resetting_lever_thirty","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","chemlib:lithium_ingot_to_nugget","create:crafting/kinetics/gray_seat","botania:conversions/light_gray_petal_block_deconstruct","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","minecraft:dye_yellow_wool","ad_astra:steel_factory_block","mcwwindows:acacia_log_parapet","simplylight:illuminant_red_block_on_dyed","dyenamics:fluorescent_stained_glass","alltheores:bronze_rod","occultism:crafting/spirit_torch","chemlib:potassium_nugget_to_ingot","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","alltheores:aluminum_gear","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","cfm:lime_grill","mcwfurnitures:acacia_table","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwroofs:lime_terracotta_upper_steep_roof","mcwwindows:cyan_curtain","securitycraft:reinforced_cyan_stained_glass","ae2:network/cables/smart_yellow","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","botania:cosmetic_groucho_glasses","mcwroofs:gray_terracotta_upper_steep_roof","minecraft:dye_lime_wool","securitycraft:secret_sign_item","mcwbiomesoplenty:fir_tropical_door","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","aquaculture:heavy_hook","mcwbiomesoplenty:palm_western_door","minecraft:yellow_terracotta","mcwwindows:acacia_window","mcwroofs:green_terracotta_roof","croptopia:food_press","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","connectedglass:scratched_glass_pink2","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwfurnitures:oak_chair","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","utilitix:spruce_shulker_boat_with_shell","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","ae2:network/cables/glass_green","mcwbiomesoplenty:willow_classic_door","botania:terrasteel_chestplate","cfm:magenta_kitchen_counter","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","mcwwindows:dark_oak_pane_window","sgjourney:fire_pit","utilitix:tiny_coal_to_tiny","pneumaticcraft:reinforced_brick_pillar","immersiveengineering:crafting/connector_hv","mcwpaths:brick_crystal_floor","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","mcwdoors:acacia_swamp_door","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","botania:cosmetic_witch_pin","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","minecraft:dye_gray_carpet","mcwpaths:gravel_path_block","supplementaries:lapis_bricks","minecraft:cauldron","mcwbridges:glass_bridge_pier","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","immersiveengineering:crafting/alu_fence","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","botania:petal_white_double","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:iron_ring","dimstorage:dim_core","minecraft:cobblestone_slab","cfm:cyan_kitchen_sink","mcwbiomesoplenty:dead_beach_door","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","chemlib:gallium_ingot_from_smelting_gallium_dust","utilitix:directional_rail","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","mcwfurnitures:stripped_acacia_covered_desk","supplementaries:candle_holders/candle_holder_lime_dye","mcwbiomesoplenty:fir_barn_glass_door","railcraft:copper_gear","alltheores:enderium_rod","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","mythicbotany:gaia_pylon","mcwfences:dark_oak_curved_gate","ad_astra:pink_industrial_lamp","mcwwindows:crimson_plank_pane_window","mcwwindows:cherry_curtain_rod","mcwroofs:stone_bricks_top_roof","minecraft:chest","alltheores:diamond_plate","cfm:magenta_trampoline","supplementaries:pulley","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","connectedglass:clear_glass_lime2","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","minecraft:dye_gray_wool","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwbiomesoplenty:hellbark_stable_head_door","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:jacaranda_swamp_door","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwroofs:gutter_base_red","mcwtrpdoors:acacia_glass_trapdoor","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","bigreactors:crafting/blutonium_ingot_to_nugget","enderio:soularium_pressure_plate","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","mcwfurnitures:acacia_modern_desk","botania:spark_upgrade_recessive","sophisticatedbackpacks:chipped/carpenters_table_upgrade","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","mcwbiomesoplenty:jacaranda_mystic_door","sophisticatedstorage:oak_limited_barrel_4","dyenamics:navy_terracotta","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","mcwfurnitures:acacia_end_table","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","securitycraft:reinforced_blue_stained_glass_pane_from_glass","botania:spark_upgrade_isolated","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","alltheores:aluminum_rod","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","mcwroofs:yellow_striped_awning","minecraft:light_blue_terracotta","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","minecraft:orange_dye_from_red_yellow","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","ae2:tools/portable_item_cell_16k","minecraft:cyan_candle","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","railcraft:steel_tank_valve","cfm:cyan_cooler","botania:cosmetic_cat_ears","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","ad_astra:steel_rod","mcwdoors:acacia_modern_door","mcwbiomesoplenty:mahogany_mystic_door","mcwwindows:prismarine_four_window","enderio:redstone_alloy_grinding_ball","mcwroofs:light_gray_roof","securitycraft:secret_jungle_hanging_sign","mcwbiomesoplenty:umbran_beach_door","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:umbran_bark_glass_door","connectedglass:scratched_glass_orange2","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","travelersbackpack:redstone","mcwfurnitures:stripped_acacia_end_table","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","minecraft:slime_block","minecraft:acacia_planks","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwbiomesoplenty:fir_nether_door","enderio:soularium_grinding_ball","chimes:glass_bells","mcwwindows:iron_shutter","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","enderio:empty_soul_vial","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","ad_astra:nasa_workbench","ad_astra:etrionic_capacitor","mcwbiomesoplenty:empyreal_japanese2_door","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","mcwbiomesoplenty:empyreal_swamp_door","cfm:dye_yellow_picket_gate","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwfurnitures:stripped_acacia_coffee_table","alltheores:copper_rod","minecraft:shulker_box","simplylight:illuminant_slab","enderio:copper_alloy_ingot","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","ae2:network/cables/covered_blue","aether:diamond_gloves","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","enderio:resetting_lever_sixty_from_inv","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwbiomesoplenty:dead_stable_door","securitycraft:secret_birch_sign_item","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","minecraft:ender_chest","railcraft:nickel_gear","mcwbiomesoplenty:willow_western_door","minecraft:iron_trapdoor","minecraft:brown_candle","pneumaticcraft:thermal_lagging","ae2:network/cables/dense_covered_yellow","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","modularrouters:void_module","ae2:network/blocks/storage_drive","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","additionallanterns:dark_prismarine_lantern","alltheores:iridium_rod","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","domum_ornamentum:lime_floating_carpet","botania:conversions/blazeblock_deconstruct","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","securitycraft:reinforced_warped_fence_gate","mcwfurnitures:acacia_bookshelf","botania:skydirt_rod","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","expatternprovider:epa","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","botania:cosmetic_red_glasses","minecraft:light_gray_dye_from_gray_white_dye","ae2:network/cables/dense_covered_light_gray","alltheores:aluminum_plate","mcwfences:blackstone_pillar_wall","minecraft:magenta_dye_from_purple_and_pink","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","ad_astra:small_yellow_industrial_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwbiomesoplenty:stripped_umbran_pane_window","supplementaries:candle_holders/candle_holder_cyan_dye","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","cfm:stripped_acacia_crate","mcwbiomesoplenty:dead_barn_glass_door","silentgear:stone_torch","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","botania:spark_upgrade_dominant","cfm:black_kitchen_sink","mcwwindows:red_mosaic_glass","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","connectedglass:borderless_glass_pink2","rftoolsbase:filter_module","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwfurnitures:stripped_acacia_double_wardrobe","mcwwindows:green_mosaic_glass_pane","cfm:dye_cyan_picket_gate","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwbiomesoplenty:pine_western_door","utilitarian:utility/acacia_logs_to_trapdoors","forbidden_arcanus:sanity_meter","botania:diluted_pool","modularrouters:energy_output_module","mcwbiomesoplenty:hellbark_japanese_door","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","enderio:resetting_lever_three_hundred_inv_from_base","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","securitycraft:secret_dark_oak_hanging_sign","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alchemistry:fission_chamber_controller","alltheores:diamond_rod","mcwbiomesoplenty:palm_stable_door","minecraft:stone_slab_from_stone_stonecutting","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","chemlib:sodium_nugget_to_ingot","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthecompressed:decompress/energetic_alloy_block_1x","minecraft:stone_brick_slab_from_stone_stonecutting","aether:ice_from_bucket_freezing","mcwbiomesoplenty:palm_four_panel_door","pneumaticcraft:speed_upgrade","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","connectedglass:clear_glass_pink_pane2","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","ae2:network/cables/dense_smart_brown","botania:dye_white","mcwbiomesoplenty:dead_hedge","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","mcwfences:prismarine_grass_topped_wall","ad_astra:small_gray_industrial_lamp","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","mcwbiomesoplenty:fir_glass_door","rftoolsutility:energyplus_module","connectedglass:scratched_glass_yellow_pane2","ae2:tools/portable_item_cell_1k","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","supplementaries:candle_holders/candle_holder_pink_dye","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","ae2:network/cables/glass_blue","alltheores:lumium_rod","minecraft:lapis_lazuli","travelersbackpack:wolf","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","chemlib:lithium_ingot_to_block","securitycraft:secret_acacia_hanging_sign","minecraft:quartz_block","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","connectedglass:scratched_glass_lime2","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwbiomesoplenty:mahogany_bamboo_door","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","enderio:resetting_lever_sixty_from_prev","enderio:pulsating_alloy_ingot","mcwwindows:mud_brick_arrow_slit","botania:dye_gray","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","mcwbiomesoplenty:willow_mystic_door","allthemodium:unobtainium_ingot","mcwbiomesoplenty:pine_modern_door","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","mcwtrpdoors:acacia_mystic_trapdoor","securitycraft:cage_trap","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:umbran_paper_door","mcwbiomesoplenty:empyreal_pane_window","alltheores:iridium_gear","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwbiomesoplenty:empyreal_stable_door","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","mcwbiomesoplenty:hellbark_plank_four_window","alchemistry:reactor_input","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","cfm:white_kitchen_drawer","ae2:network/cables/smart_pink","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","comforts:hammock_to_white","mcwwindows:acacia_four_window","chemlib:potassium_ingot_from_blasting_potassium_dust","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","domum_ornamentum:yellow_floating_carpet","enderio:redstone_alloy_nugget","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","ae2:network/cables/dense_smart_yellow","deepresonance:machine_frame","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","cfm:stripped_acacia_cabinet","mcwfences:sandstone_grass_topped_wall","expatternprovider:ex_inscriber","minecraft:glowstone","additionallanterns:smooth_stone_chain","alltheores:gold_rod","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","botania:cloud_pendant","minecraft:magma_cream","botania:slime_bottle","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","ae2:block_cutter/walls/sky_stone_wall","mcwfurnitures:stripped_oak_table","blue_skies:maple_bookshelf","securitycraft:reinforced_black_stained_glass_pane_from_dye","pneumaticcraft:logistics_frame_default_storage","computercraft:cable","additionallanterns:cobbled_deepslate_lantern","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","mcwroofs:thatch_attic_roof","securitycraft:reinforced_orange_stained_glass_pane_from_glass","securitycraft:reinforced_lime_stained_glass","pneumaticcraft:wall_lamp_inverted_gray","minecraft:dye_lime_carpet","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","supplementaries:soap/map","mcwpaths:brick_running_bond_path","mcwfurnitures:stripped_acacia_stool_chair","supplementaries:sugar_cube","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwbiomesoplenty:magic_window2","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","simplylight:illuminant_red_block_on_toggle","cfm:acacia_table","mcwpaths:brick_windmill_weave_stairs","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","minecraft:dye_cyan_wool","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","botania:apothecary_livingrock","mcwtrpdoors:print_blossom","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","mcwbiomesoplenty:dead_barn_door","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","enderio:redstone_alloy_ingot","securitycraft:reinforced_oak_fence","railcraft:feed_station","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","dyenamics:persimmon_stained_glass","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","minecraft:dye_pink_carpet","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","railcraft:personal_world_spike","immersiveengineering:crafting/armor_steel_chestplate","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","connectedglass:clear_glass_pink2","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","minecraft:dye_brown_bed","immersiveengineering:crafting/stick_aluminum","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","botania:cosmetic_googly_eyes","mcwfurnitures:acacia_modern_wardrobe","xnet:redstoneproxy_update","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","merequester:requester","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","botania:cosmetic_black_tie","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:fir_four_panel_door","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","supplementaries:slingshot","allthemodium:allthemodium_plate","advanced_ae:quantumunit","mcwdoors:acacia_japanese2_door","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","everythingcopper:copper_rail","cfm:black_grill","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","blue_skies:glowing_nature_stonebrick_from_glowstone","mcwlights:green_paper_lamp","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_four_panel_door","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aquaculture:neptunium_ingot_from_nuggets","mcwroofs:bricks_upper_steep_roof","croptopia:french_fries","botania:terraform_rod","minecraft:iron_pickaxe","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","ae2:network/cables/glass_orange","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","allthecompressed:compress/redstone_alloy_block_1x","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","mcwbiomesoplenty:redwood_stable_head_door","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwwindows:brown_curtain","allthecompressed:compress/redstone_alloy_block_2x","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwbiomesoplenty:empyreal_western_door","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwroofs:acacia_lower_roof","mcwbiomesoplenty:empyreal_japanese_door","expatternprovider:epp_part","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","computercraft:monitor_advanced","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:hellbark_paper_door","cfm:white_kitchen_counter","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","mcwfurnitures:acacia_coffee_table","ae2:network/cables/covered_fluix_clean","mcwbiomesoplenty:umbran_stable_door","botania:elementium_block","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","aether:packed_ice_freezing","botania:cosmetic_anaglyph_glasses","mcwbiomesoplenty:willow_stable_head_door","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","minecraft:gray_candle","trashcans:energy_trash_can","botania:dreamwood_twig","mcwpaths:stone_crystal_floor","cfm:red_kitchen_sink","chimes:copper_chimes","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","botania:cosmetic_pink_flower_bud","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","mcwwindows:crimson_plank_parapet","chemlib:sodium_ingot_to_block","botania:cosmetic_blue_butterfly","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","botania:cosmetic_four_leaf_clover","pylons:clear_potion_filter","allthecompressed:compress/pulsating_alloy_block_2x","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","expatternprovider:wireless_connector","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","allthecompressed:compress/pulsating_alloy_block_1x","cfm:spatula","pneumaticcraft:charging_upgrade","cfm:dye_lime_picket_gate","ae2:network/cables/dense_covered_fluix_clean","allthecompressed:decompress/soularium_block_1x","enderio:extraction_speed_upgrade_3_upgrade","mcwlights:soul_mangrove_tiki_torch","mcwbiomesoplenty:fir_cottage_door","mcwfurnitures:acacia_bookshelf_cupboard","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","twigs:schist","mcwwindows:stripped_oak_log_four_window","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwbiomesoplenty:willow_glass_door","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwlights:copper_candle_holder","mcwfurnitures:acacia_covered_desk","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","ae2:tools/nether_quartz_spade","minecraft:honey_block","mcwbiomesoplenty:mahogany_barn_door","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","sophisticatedbackpacks:crafting_upgrade","aquaculture:birch_fish_mount","securitycraft:secret_crimson_hanging_sign","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","buildinggadgets2:template_manager","mcwwindows:jungle_window","botania:white_petal_block","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","aether:blue_ice_freezing","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","cfm:stripped_acacia_coffee_table","mcwbiomesoplenty:empyreal_barn_glass_door","cfm:white_sofa","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","aiotbotania:livingrock_sword","utilitix:dark_oak_shulker_boat_with_shell","cfm:black_kitchen_drawer","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","chemlib:gallium_ingot_to_nugget","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","mcwbiomesoplenty:maple_swamp_door","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","sophisticatedstorage:stonecutter_upgrade","cfm:acacia_kitchen_drawer","mcwroofs:white_terracotta_upper_lower_roof","mcwfences:vintage_metal_fence","aquaculture:double_hook","ae2:shaped/slabs/sky_stone_block","allthemodium:allthemodium_rod","handcrafted:oak_bench","mcwbiomesoplenty:maple_bark_glass_door","minecraft:yellow_candle","dyenamics:bubblegum_dye","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","minecraft:repeater","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:maple_stable_door","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","chemlib:magnesium_ingot_to_block","travelersbackpack:dye_yellow_sleeping_bag","botania:elementium_boots","mcwbiomesoplenty:fir_classic_door","mcwbiomesoplenty:empyreal_glass_door","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:oak_attic_roof","mcwtrpdoors:acacia_beach_trapdoor","mcwroofs:base_roof_block","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwbiomesoplenty:palm_window","mcwfurnitures:acacia_lower_bookshelf_drawer","mcwbiomesoplenty:fir_waffle_door","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","botania:terra_axe","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","mcwbiomesoplenty:pine_beach_door","sophisticatedbackpacks:chipped/glassblower_upgrade","utilitix:acacia_shulker_boat_with_shell","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","mcwwindows:oak_four_window","cfm:oak_coffee_table","alltheores:platinum_rod","mcwroofs:purple_terracotta_steep_roof","minecraft:light_gray_stained_glass","securitycraft:reinforced_blue_stained_glass","mcwwindows:yellow_mosaic_glass_pane","railcraft:world_spike","mcwdoors:garage_gray_door","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","minecraft:polished_andesite_from_andesite_stonecutting","littlelogistics:seater_car","botania:ender_eye_block","botania:reach_ring","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","minecraft:oak_pressure_plate","aether:skyroot_beehive","cfm:brown_grill","mcwwindows:quartz_window","mcwroofs:gray_roof","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","mcwroofs:gutter_base_brown","cfm:acacia_crate","botania:glimmering_livingwood_log","mcwbiomesoplenty:redwood_tropical_door","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","securitycraft:reinforced_chiseled_bookshelf","utilitix:mangrove_shulker_boat_with_shell","cfm:gray_kitchen_drawer","minecraft:clock","dyenamics:conifer_dye","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","enderio:entity_filter","mcwwindows:warped_curtain_rod","railcraft:lead_gear","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","allthetweaks:greg_star_block","mcwfences:oak_wired_fence","botania:twig_wand","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwdoors:acacia_western_door","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwbiomesoplenty:maple_paper_door","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","botania:livingwood_bow","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","mcwroofs:purple_terracotta_roof","connectedglass:scratched_glass_orange_pane2","botania:spark_changer","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","minecraft:polished_andesite_slab_from_andesite_stonecutting","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","mcwbiomesoplenty:magic_western_door","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","mcwbiomesoplenty:pine_swamp_door","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","enderio:advanced_item_filter","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","pneumaticcraft:armor_upgrade","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","mcwlights:festive_lantern","botania:conversions/gray_petal_block_deconstruct","mcwbiomesoplenty:magic_tropical_door","securitycraft:block_change_detector","supplementaries:pancake_fd","modularrouters:placer_module","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","minecraft:netherite_scrap_from_blasting","botania:lens_warp","mcwbiomesoplenty:dead_bamboo_door","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","botania:cosmetic_ancient_mask","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","undergarden:wigglewood_boat","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwpaths:andesite_crystal_floor_slab","pneumaticcraft:elevator_frame","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","connectedglass:scratched_glass_cyan_pane2","enderio:pulsating_alloy_grinding_ball","botania:cosmetic_orange_shades","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","botania:dreamwood_slab","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","create:copper_bars_from_ingots_copper_stonecutting","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","mcwbiomesoplenty:pine_bark_glass_door","securitycraft:secret_dark_oak_sign_item","ae2:network/cables/dense_covered_cyan","sophisticatedbackpacks:smithing_upgrade","mcwbiomesoplenty:palm_beach_door","alltheores:invar_ingot_from_dust_blasting","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","pneumaticcraft:heat_pipe","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","mcwbiomesoplenty:magic_bamboo_door","mcwfurnitures:oak_lower_triple_drawer","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwfurnitures:stripped_acacia_double_drawer","utilitix:bamboo_shulker_raft_with_shell","enderio:soularium_ingot","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","enderio:staff_of_travelling","minecraft:pink_candle","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","mcwroofs:thatch_lower_roof","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwroofs:white_roof_block","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwbiomesoplenty:mahogany_stable_door","securitycraft:block_pocket_manager","botania:cosmetic_eerie_mask","enderio:pulsating_alloy_nugget_to_ingot","mcwlights:soul_cherry_tiki_torch","expatternprovider:ei","botania:red_pavement","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","botania:cosmetic_wicked_eyepatch","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","cfm:purple_kitchen_drawer","botania:flower_bag","ad_astra:steel_plateblock","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","mcwfurnitures:acacia_triple_drawer","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","mcwfurnitures:stripped_acacia_double_drawer_counter","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","railcraft:bag_of_cement","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","botania:light_gray_petal_block","domum_ornamentum:white_paper_extra","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","cfm:acacia_desk","mcwwindows:dark_oak_plank_parapet","mcwbiomesoplenty:pine_waffle_door","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwfurnitures:stripped_acacia_drawer","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","connectedglass:clear_glass_gray2","sfm:manager","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwbiomesoplenty:willow_bamboo_door","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwbiomesoplenty:hellbark_tropical_door","mcwbiomesoplenty:magic_glass_door","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","connectedglass:tinted_borderless_glass_cyan2","botania:livingwood_twig","mcwbiomesoplenty:willow_window","ae2:tools/network_tool","rftoolsutility:fluidplus_module","deeperdarker:bloom_boat","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","mcwroofs:pink_terracotta_upper_steep_roof","dyenamics:rose_stained_glass","sophisticatedstorage:chipped/carpenters_table_upgrade","pneumaticcraft:vortex_cannon","ad_astra:lime_industrial_lamp","connectedglass:clear_glass_yellow_pane2","securitycraft:reinforced_jungle_fence","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","mcwroofs:acacia_steep_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwwindows:stripped_cherry_pane_window","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","mcwbiomesoplenty:hellbark_pyramid_gate","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","expatternprovider:pre_bus","mcwfurnitures:stripped_acacia_desk","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","ae2:network/cables/dense_smart_fluix_clean","minecraft:dye_orange_bed","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","alchemistry:reactor_output","sophisticatedstorage:basic_to_copper_tier_upgrade","ae2:network/crafting/molecular_assembler","littlelogistics:vessel_charger","mcwbiomesoplenty:dead_tropical_door","utilitix:minecart_tinkerer","mcwbiomesoplenty:jacaranda_paper_door","aether:white_cape","ae2:tools/misctools_entropy_manipulator","railcraft:radio_circuit","botania:elementium_hoe","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","chemlib:tantalum_nugget_to_ingot","mcwroofs:white_terracotta_steep_roof","mcwbiomesoplenty:mahogany_plank_window2","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","securitycraft:secret_acacia_sign_item","handcrafted:yellow_plate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","enderio:energetic_alloy_ingot","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","enderio:energetic_alloy_grinding_ball","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","utilitarian:snad/drit","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","mcwwindows:cherry_plank_four_window","mcwtrpdoors:acacia_swamp_trapdoor","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","mcwbiomesoplenty:fir_modern_door","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","ad_astra:steel_trapdoor","securitycraft:reinforced_pink_stained_glass_pane_from_dye","mcwbiomesoplenty:mahogany_barn_glass_door","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","minecraft:cartography_table","securitycraft:alarm","pneumaticcraft:manometer","ad_astra:launch_pad","mcwbiomesoplenty:dead_japanese2_door","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","ae2:network/blocks/energy_vibration_chamber","cfm:stripped_acacia_desk_cabinet","pneumaticcraft:wall_lamp_inverted_magenta","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","botania:elementium_leggings","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","additionallanterns:amethyst_lantern","utilitix:crude_furnace","dyenamics:peach_dye","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","botania:petal_light_gray","mcwfurnitures:stripped_acacia_striped_chair","mcwroofs:gray_steep_roof","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","alltheores:iron_ore_hammer","sophisticatedstorage:jukebox_upgrade","gtceu:shapeless/aluminium_wire_wire_gt_single_doubling","cfm:white_picket_gate","mcwfurnitures:stripped_oak_modern_wardrobe","connectedglass:scratched_glass_gray_pane2","mcwfences:railing_nether_brick_wall","mcwroofs:yellow_terracotta_upper_lower_roof","ae2:network/cables/glass_black","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","botania:polished_livingrock","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","cfm:black_trampoline","botania:brown_shiny_flower","mcwfurnitures:acacia_cupboard_counter","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","simplylight:illuminant_orange_block_dyed","mcwbiomesoplenty:magic_mystic_door","alchemistry:compactor","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","expatternprovider:oversize_interface_part","mcwwindows:oak_window2","mcwfurnitures:stripped_acacia_bookshelf","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","botania:conversions/white_petal_block_deconstruct","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","mcwbiomesoplenty:empyreal_bamboo_door","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","botania:thunder_sword","mcwdoors:oak_japanese2_door","simplylight:illuminant_black_block_dyed","connectedglass:borderless_glass_yellow_pane2","simplylight:illuminant_gray_block_on_toggle","immersiveengineering:crafting/plate_nickel_hammering","mcwdoors:acacia_bamboo_door","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","mcwwindows:quartz_pane_window","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","botania:cosmetic_thinking_hand","minecraft:shield","chemlib:lithium_ingot_from_smelting_lithium_dust","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","connectedglass:clear_glass_brown2","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","immersiveengineering:crafting/wirecoil_redstone","cfm:blue_kitchen_drawer","chemlib:magnesium_ingot_from_smelting_magnesium_dust","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_bricks_bridge","alltheores:nickel_rod","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","travelersbackpack:dye_brown_sleeping_bag","mcwbiomesoplenty:dead_mystic_door","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","botania:elementium_helmet","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","enderio:black_paper","enderio:vibrant_alloy_ingot","ae2:network/cables/dense_smart_fluix","mcwbiomesoplenty:pine_japanese_door","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","cfm:dye_orange_picket_gate","simplylight:illuminant_brown_block_on_toggle","sophisticatedbackpacks:pickup_upgrade","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwbiomesoplenty:redwood_glass_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","chemlib:lithium_ingot_from_blasting_lithium_dust","mcwfences:modern_andesite_wall","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","mcwbiomesoplenty:umbran_glass_door","ae2:network/cells/view_cell","botania:terra_pick","botania:brewery","mcwbiomesoplenty:maple_beach_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","expatternprovider:caner","mcwbiomesoplenty:magic_four_panel_door","mcwfurnitures:acacia_drawer_counter","botania:monocle","mcwroofs:deepslate_lower_roof","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:hellbark_four_panel_door","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","connectedglass:borderless_glass_orange_pane2","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","chemlib:gallium_nugget_to_ingot","minecraft:honeycomb_block","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","minecraft:yellow_stained_glass_pane_from_glass_pane","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwfurnitures:acacia_striped_chair","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","minecraft:andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","travelersbackpack:dye_pink_sleeping_bag","mcwbiomesoplenty:palm_barn_door","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","mcwbiomesoplenty:palm_mystic_door","minecraft:dye_pink_bed","simplylight:illuminant_brown_block_on_dyed","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:maple_japanese2_door","computercraft:wireless_modem_normal","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","ad_astra:zip_gun","croptopia:nougat","ae2:network/cables/covered_magenta","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","ad_astra:space_boots","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","expatternprovider:pattern_modifier","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","mcwlights:soul_bamboo_tiki_torch","mcwdoors:acacia_whispering_door","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwbiomesoplenty:jacaranda_cottage_door","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","ae2:network/cables/covered_gray","cfm:dye_brown_picket_fence","mcwbiomesoplenty:umbran_pyramid_gate","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","allthecompressed:compress/atm_star_block_1x","mcwtrpdoors:acacia_whispering_trapdoor","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","mcwbiomesoplenty:hellbark_glass_door","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","enderio:redstone_filter_base","twigs:bloodstone","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","mcwwindows:warped_planks_window","sophisticatedbackpacks:chipped/loom_table_upgrade","cfm:orange_kitchen_counter","mcwroofs:acacia_roof","mcwpaths:brick_flagstone_stairs","chemlib:potassium_block_to_ingot","pneumaticcraft:spawner_agitator","dyenamics:bed/persimmon_bed","ae2:network/cables/smart_light_gray","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwbiomesoplenty:empyreal_mystic_door","mcwroofs:gutter_base_light_blue","mcwpaths:andesite_crystal_floor_path","botania:cosmetic_polka_dotted_bows","create:crafting/kinetics/orange_seat","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","dyenamics:icy_blue_terracotta","dyenamics:cherenkov_stained_glass","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","chemlib:sodium_ingot_to_nugget","minecraft:yellow_stained_glass","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","enderio:energetic_alloy_nugget","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","sophisticatedbackpacks:stack_upgrade_tier_1","bigreactors:crafting/blutonium_component_to_storage","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","pneumaticcraft:compressed_iron_ingot_from_block","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbiomesoplenty:willow_pane_window","computercraft:wireless_modem_advanced","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","connectedglass:scratched_glass_gray2","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","ae2:tools/certus_quartz_cutting_knife","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:light_gray_terracotta","dyenamics:amber_stained_glass","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","botania:corporea_spark","merequester:requester_terminal","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","immersiveengineering:crafting/plate_lead_hammering","allthecompressed:decompress/pulsating_alloy_block_1x","mcwtrpdoors:acacia_paper_trapdoor","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","botania:manasteel_shears","mcwbiomesoplenty:pine_paper_door","ae2:network/cables/smart_green","mcwwindows:stone_brick_gothic","botania:elementium_pickaxe","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","minecraft:glass_bottle","travelersbackpack:dye_gray_sleeping_bag","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","cfm:acacia_upgraded_gate","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","botania:clip","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","twigs:cracked_bricks","supplementaries:slice_map","allthecompressed:decompress/copper_alloy_block_1x","mcwbridges:brick_bridge_pier","mcwdoors:acacia_mystic_door","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","botania:lens_normal","mcwfences:end_brick_pillar_wall","mcwroofs:bricks_steep_roof","computercraft:monitor_normal","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","delightful:knives/elementium_knife","pneumaticcraft:wall_lamp_inverted_cyan","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","modularrouters:modular_router","delightful:knives/nickel_knife","mcwroofs:stone_bricks_roof","cfm:cyan_grill","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","mcwbiomesoplenty:palm_bamboo_door","ae2:network/cables/smart_black","cfm:stripped_spruce_mail_box","botania:cosmetic_devil_tail","mcwdoors:acacia_japanese_door","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwtrpdoors:acacia_bark_trapdoor","mcwbiomesoplenty:hellbark_waffle_door","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","everythingcopper:copper_shovel","utilitarian:no_soliciting/no_soliciting_banner","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","minecraft:bucket","handcrafted:salmon_trophy","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","minecraft:polished_andesite","mcwbiomesoplenty:palm_swamp_door","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","ae2:network/cables/smart_orange","minecraft:unobtainium_mage_helmet_smithing","cfm:white_picket_fence","chemlib:tantalum_block_to_ingot","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","everythingcopper:copper_trapdoor","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:acacia_drawer","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","chemlib:gallium_block_to_ingot","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:fir_swamp_door","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwbiomesoplenty:dead_stable_head_door","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","minecraft:coal_block","mcwwindows:mangrove_plank_window","botania:cosmetic_tiny_potato_mask","computercraft:skull_dan200","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","securitycraft:secret_birch_hanging_sign","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwroofs:acacia_upper_lower_roof","ae2:network/parts/energy_acceptor","additionallanterns:gold_chain","mcwroofs:white_attic_roof","ae2:network/cables/smart_light_blue","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","cfm:dye_pink_picket_fence","mcwbiomesoplenty:redwood_bamboo_door","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","botania:elementium_chestplate","aquaculture:sushi","ae2:network/blocks/interfaces_interface_alt","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","mcwbiomesoplenty:magic_japanese2_door","mcwbiomesoplenty:hellbark_stable_door","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfurnitures:stripped_acacia_table","connectedglass:scratched_glass_lime_pane2","mcwfences:acacia_stockade_fence","allthecompressed:decompress/redstone_alloy_block_1x","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","twilightforest:canopy_boat","botania:quartz_red","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_3","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","bigreactors:turbine/basic/activetap_fe","ae2:network/cables/covered_light_gray","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","gtceu:shapeless/data_orb_nbt","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwdoors:acacia_barn_glass_door","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","minecraft:andesite_wall_from_andesite_stonecutting","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","mcwwindows:dark_oak_plank_window","minecraft:sticky_piston","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:oak_kitchen_sink_light","enderio:ensouled_chassis","mcwbiomesoplenty:empyreal_barn_door","mcwwindows:diorite_pane_window","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","connectedglass:scratched_glass_brown2","supplementaries:flags/flag_red","enderio:resetting_lever_ten_from_prev","mcwtrpdoors:acacia_classic_trapdoor","botania:lens_flare","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:light_gray_terracotta_steep_roof","mcwbiomesoplenty:hellbark_nether_door","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","mcwwindows:deepslate_window","ad_astra:brown_industrial_lamp","mcwbiomesoplenty:empyreal_paper_door","minecraft:gray_terracotta","mcwfurnitures:acacia_modern_chair","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","enderio:energetic_alloy_nugget_to_ingot","mcwbiomesoplenty:magic_bark_glass_door","dyenamics:peach_terracotta","botania:livingrock_wall","botania:cosmetic_engineer_goggles","minecraft:fishing_rod","xnet:connector_yellow_dye","mcwwindows:acacia_blinds","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwdoors:acacia_waffle_door","botania:blue_pavement","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","connectedglass:scratched_glass_yellow2","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","minecraft:beacon","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:dye_yellow_bed","ad_astra:white_flag","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","aiotbotania:terra_shovel","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:umbran_bamboo_door","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwroofs:brown_striped_awning","allthetweaks:atm_star_from_atmstar_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","mcwroofs:magenta_terracotta_lower_roof","ad_astra:vent","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:stone_attic_roof","handcrafted:terracotta_plate","mcwroofs:light_blue_terracotta_roof","mcwbiomesoplenty:willow_barn_glass_door","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","minecraft:glass_pane","minecraft:dye_brown_carpet","supplementaries:timber_brace","alltheores:iridium_ingot_from_raw_blasting","allthearcanistgear:unobtainium_robes_smithing","mcwbiomesoplenty:maple_barn_glass_door","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","mcwbiomesoplenty:pine_pane_window","enderio:wood_gear","supplementaries:bed_from_feather_block","connectedglass:borderless_glass_gray2","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","ae2:network/blocks/interfaces_interface_part","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","alltheores:lumium_plate","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","mcwbiomesoplenty:mahogany_paper_door","ae2:materials/annihilationcore","ae2:network/cables/dense_smart_from_smart","silentgear:stone_rod","ae2:network/cables/covered_green","ae2:network/cables/dense_covered_green","minecraft:leather_boots","railcraft:steel_spike_maul","pneumaticcraft:classify_filter","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","chemlib:lithium_block_to_ingot","mcwtrpdoors:acacia_barred_trapdoor","mcwlights:pink_paper_lamp","mcwbiomesoplenty:magic_nether_door","allthecompressed:compress/soularium_block_1x","pneumaticcraft:wall_lamp_inverted_light_blue","immersiveengineering:crafting/wire_aluminum","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","cfm:dye_gray_picket_fence","mcwroofs:stone_upper_lower_roof","bigreactors:turbine/basic/bearing","mcwfurnitures:acacia_bookshelf_drawer","expatternprovider:ebus_out","ae2:network/cables/covered_purple","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","cfm:acacia_park_bench","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","connectedglass:clear_glass_black_pane3","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","littlelogistics:transmitter_component","minecraft:brush","securitycraft:secret_spruce_hanging_sign","aether:book_of_lore","mcwfurnitures:stripped_acacia_triple_drawer","railcraft:steel_pickaxe","cfm:dye_gray_picket_gate","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","mcwfurnitures:acacia_wardrobe","sfm:cable","minecraft:hay_block","mcwbiomesoplenty:palm_japanese_door","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","mcwroofs:white_upper_steep_roof","twilightforest:mangrove_boat","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbiomesoplenty:jacaranda_barn_glass_door","allthecompressed:compress/soularium_block_2x","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","ae2:network/parts/monitors_storage","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwbiomesoplenty:fir_barn_door","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","botania:petal_light_gray_double","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","mythicbotany:alfsteel_pylon","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","mcwbiomesoplenty:willow_japanese2_door","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","ae2:network/cables/covered_black","ae2:network/cables/dense_smart_purple","ae2:tools/certus_quartz_wrench","mcwroofs:oak_planks_upper_steep_roof","minecraft:blaze_powder","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwbiomesoplenty:empyreal_window2","mcwbiomesoplenty:pine_barn_glass_door","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","enderio:soularium_nugget_to_ingot","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwwindows:granite_louvered_shutter","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","botania:tornado_rod","immersiveengineering:crafting/blueprint_bullets","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","connectedglass:tinted_borderless_glass_yellow2","mcwroofs:pink_terracotta_top_roof","mcwfurnitures:stripped_acacia_lower_bookshelf_drawer","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwbiomesoplenty:fir_mystic_door","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","securitycraft:secret_warped_hanging_sign","ae2:tools/certus_quartz_spade","mcwbiomesoplenty:fir_beach_door","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","ae2:network/cables/dense_covered_black","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","expatternprovider:epp_alt","occultism:crafting/spirit_campfire","handcrafted:yellow_crockery_combo","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","minecraft:white_stained_glass","mcwbiomesoplenty:umbran_mystic_door","securitycraft:secret_warped_sign_item","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","securitycraft:taser","mcwroofs:stone_bricks_lower_roof","chemlib:tantalum_ingot_to_nugget","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","aiotbotania:livingrock_hoe","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","mcwfurnitures:acacia_double_drawer_counter","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","ad_astra:fan","connectedglass:scratched_glass_pink_pane2","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","supplementaries:altimeter","ae2:network/cables/dense_smart_gray","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","cfm:red_kitchen_drawer","chemlib:magnesium_ingot_to_nugget","botania:manasteel_shovel","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwroofs:white_steep_roof","botania:cosmetic_puffy_scarf","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwbiomesoplenty:magic_stable_door","enderio:copper_alloy_block","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","immersiveengineering:crafting/plate_aluminum_hammering","delightful:knives/bronze_knife","travelersbackpack:quartz","mcwfurnitures:cabinet_door","supplementaries:candle_holders/candle_holder_orange_dye","mcwbiomesoplenty:mahogany_classic_door","mcwwindows:stripped_spruce_pane_window","sophisticatedbackpacks:tool_swapper_upgrade","minecraft:gold_nugget","bigreactors:energizer/controller","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","securitycraft:display_case","handcrafted:bench","minecraft:gray_stained_glass_pane_from_glass_pane","allthecompressed:compress/andesite_1x","mcwbiomesoplenty:willow_nether_door","mcwwindows:diorite_four_window","minecraft:stone_slab","pneumaticcraft:compressed_bricks","mcwbiomesoplenty:magic_classic_door","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","mcwbiomesoplenty:maple_four_panel_door","minecraft:brick_stairs_from_bricks_stonecutting","travelersbackpack:backpack_tank","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","ae2:network/parts/import_bus","croptopia:campfire_molasses","enderio:energetic_alloy_block","mcwdoors:acacia_stable_door","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwbiomesoplenty:pine_four_panel_door","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","mcwroofs:thatch_upper_lower_roof","mcwbiomesoplenty:hellbark_cottage_door","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","securitycraft:secret_cherry_hanging_sign","silentgear:upgrade_base","xnet:advanced_connector_green","ae2:network/cables/glass_pink","ae2:network/cables/smart_cyan","mcwwindows:crimson_stem_four_window","expatternprovider:wireless_tool","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","minecraft:dye_lime_bed","securitycraft:mine","mcwbiomesoplenty:jacaranda_glass_door","pneumaticcraft:compressed_stone_slab","mcwfurnitures:stripped_acacia_chair","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","allthecompressed:compress/copper_alloy_block_2x","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwroofs:bricks_top_roof","mcwbiomesoplenty:maple_stockade_fence","botania:starfield","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","connectedglass:tinted_borderless_glass_pink2","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","mcwfences:modern_mud_brick_wall","mcwbridges:acacia_bridge_pier","minecraft:stone_pressure_plate","chemlib:lithium_nugget_to_ingot","mcwbiomesoplenty:hellbark_classic_door","allthecompressed:compress/copper_alloy_block_1x","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","botania:yellow_pavement","connectedglass:borderless_glass_orange2","mcwbiomesoplenty:dead_glass_door","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","botania:cosmetic_alien_antenna","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","everythingcopper:copper_helmet","create:crafting/kinetics/yellow_seat","pneumaticcraft:logistics_core","mcwbiomesoplenty:willow_modern_door","mcwpaths:andesite_windmill_weave_path","mcwbiomesoplenty:palm_glass_door","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:bread","minecraft:mangrove_boat","mcwroofs:green_terracotta_steep_roof","mcwbiomesoplenty:redwood_western_door","mcwdoors:garage_silver_door","comforts:hammock_white","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","ad_astra:orange_industrial_lamp","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwbiomesoplenty:dead_classic_door","mcwwindows:dark_oak_curtain_rod","securitycraft:secret_cherry_sign_item","botania:horn_grass","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","botania:spectral_platform","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwbiomesoplenty:umbran_swamp_door","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","mcwfurnitures:acacia_desk","minecraft:iron_shovel","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","ae2:smelting/smooth_sky_stone_block","mcwwindows:stone_brick_arrow_slit","mcwbiomesoplenty:mahogany_cottage_door","domum_ornamentum:white_floating_carpet","mcwbiomesoplenty:umbran_waffle_door","croptopia:ham_sandwich","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","ad_astra:space_suit","securitycraft:disguise_module","mcwbiomesoplenty:willow_swamp_door","mcwwindows:deepslate_four_window","mcwdoors:acacia_glass_door","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","mcwwindows:dark_oak_louvered_shutter","mcwbiomesoplenty:fir_stable_head_door","cfm:light_blue_picket_fence","mcwbiomesoplenty:redwood_barn_door","mcwtrpdoors:acacia_four_panel_trapdoor","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","enderio:electromagnet","deeperdarker:echo_boat","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mythicbotany:ice_ring","mcwfences:birch_picket_fence","mcwbiomesoplenty:palm_modern_door","enderio:basic_fluid_filter","rftoolsutility:counter_module","botania:lens_redirect","minecraft:oak_door","biomesoplenty:jacaranda_boat","mcwbiomesoplenty:maple_cottage_door","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","mcwbiomesoplenty:empyreal_modern_door","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:black_roof_block","botania:elementium_shovel","connectedglass:tinted_borderless_glass_lime2","advancedperipherals:peripheral_casing","connectedglass:clear_glass_cyan_pane2","mythicbotany:alfsteel_template","mythicbotany:fire_ring","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","ae2:network/cables/dense_smart_white","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwfurnitures:stripped_acacia_large_drawer","mcwroofs:red_striped_awning","botania:cosmetic_devil_horns","cfm:oak_cabinet","enderio:soularium_block","cfm:stripped_oak_kitchen_sink_light","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","modularrouters:dropper_module","mcwbiomesoplenty:magic_highley_gate","computercraft:wired_modem","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","dyenamics:mint_terracotta","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","delightful:knives/refined_obsidian_knife","expatternprovider:ei_alt","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","supplementaries:wrench","minecraft:bow","mcwroofs:blue_terracotta_upper_steep_roof","deepresonance:radiation_suit_boots","botania:cobweb","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","cfm:acacia_kitchen_counter","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","chemlib:periodic_table","minecraft:andesite_stairs","delightful:knives/aluminum_knife","expatternprovider:wireless_ex_pat","bigreactors:energizer/chargingport_fe","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","botania:cosmetic_clock_eye","dyenamics:amber_dye","enderio:experience_rod","cfm:purple_kitchen_sink","cfm:black_cooler","supplementaries:sconce","silentgear:rough_rod","minecraft:unobtainium_spell_book_smithing","ae2:network/parts/storage_bus","minecraft:skull_banner_pattern","mcwwindows:jungle_plank_parapet","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","chemlib:potassium_ingot_from_smelting_potassium_dust","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:acacia_large_drawer","connectedglass:borderless_glass_brown2","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:jacaranda_plank_window2","sophisticatedbackpacks:feeding_upgrade","mcwbiomesoplenty:pine_stable_door","minecraft:granite","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","cfm:dye_cyan_picket_fence","cfm:stripped_acacia_desk","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","botania:manaweave_cloth","mcwbiomesoplenty:magic_barn_glass_door","mcwbiomesoplenty:redwood_mystic_door","enderio:enderios","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwbiomesoplenty:fir_bamboo_door","mcwroofs:andesite_roof","botania:black_pavement","travelersbackpack:blaze","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","minecraft:dye_yellow_carpet","ae2:shaped/stairs/sky_stone_block","botania:quartz_blaze","ae2:network/cables/glass_yellow","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","mcwfurnitures:stripped_acacia_wardrobe","dyenamics:rose_terracotta","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","alltheores:signalum_plate","mcwbiomesoplenty:dead_cottage_door","expatternprovider:oversize_interface","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","mcwfurnitures:stripped_acacia_modern_desk","dyenamics:bed/lavender_bed","cfm:lime_kitchen_counter","minecraft:dye_orange_carpet","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","cfm:stripped_oak_kitchen_sink_dark","mcwdoors:garage_black_door","minecraft:dye_cyan_bed","botania:mana_ring_greater","blue_skies:glowing_poison_stone","mcwroofs:brown_terracotta_steep_roof","mcwdoors:oak_barn_door","botania:alchemy_catalyst","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","enderio:pulsating_alloy_block","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","botania:lava_pendant","minecolonies:chainmailleggings","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","minecraft:smooth_stone","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:acacia_upgraded_fence","ae2:network/cables/dense_smart_light_gray","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","botania:conversions/elementium_block_deconstruct","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","minecraft:ender_eye","botania:cosmetic_kamui_eye","enderio:soularium_nugget","ad_astra:small_pink_industrial_lamp","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","mcwtrpdoors:acacia_ranch_trapdoor","mcwlights:cross_lantern","mcwbiomesoplenty:pine_barn_door","mcwwindows:bricks_four_window","railcraft:receiver_circuit","securitycraft:reinforced_packed_mud","chemlib:magnesium_block_to_ingot","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","handcrafted:yellow_bowl","ae2:network/blocks/io_condenser","sophisticatedbackpacks:smoking_upgrade","ae2:network/cables/covered_pink","railcraft:steel_tank_wall","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","mcwpaths:brick_clover_paving","securitycraft:trophy_system","botania:elementium_axe","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","connectedglass:scratched_glass_brown_pane2","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","advanced_ae:quantumstorage128","farmersdelight:pie_crust","rftoolsbuilder:vehicle_card","mcwlights:warped_tiki_torch","twigs:dandelion_paper_lantern","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","mcwwindows:black_curtain","dyenamics:wine_stained_glass","botania:pixie_ring","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","botania:drum_gathering","mcwwindows:jungle_plank_window2","ad_astra:space_pants","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","ae2:network/cables/dense_smart_black","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","mcwbiomesoplenty:mahogany_japanese_door","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","utilitarian:utility/acacia_logs_to_pressure_plates","mcwbiomesoplenty:umbran_four_panel_door","enderio:resetting_lever_five_inv","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","handcrafted:yellow_cup","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","ae2:network/cables/smart_blue","mcwbiomesoplenty:redwood_beach_door","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","botania:turntable","ae2:network/cells/item_storage_components_cell_1k_part","mcwbiomesoplenty:dead_highley_gate","ad_astra:steel_tank","mcwroofs:oak_planks_top_roof","biomesoplenty:hellbark_boat","minecraft:netherite_scrap","supplementaries:daub","railcraft:steel_chestplate","securitycraft:secret_oak_hanging_sign","mcwfences:oak_hedge","enderio:fluid_tank","mcwroofs:oak_planks_roof","botania:petal_white","ae2:decorative/quartz_block","enderio:redstone_alloy_block","ae2:network/cables/dense_smart_orange","mcwbiomesoplenty:magic_cottage_door","bigreactors:energizer/casing","mcwpaths:stone_flagstone","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deeperdarker:reinforced_echo_shard","cfm:dye_pink_picket_gate","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","cfm:dye_yellow_picket_fence","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","minecraft:acacia_wood","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","mcwbiomesoplenty:maple_tropical_door","pneumaticcraft:logistics_configurator","mcwbiomesoplenty:mahogany_beach_door","mcwtrpdoors:print_tropical","pneumaticcraft:logistics_frame_default_storage_self","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"],toBeDisplayed:["botania:dreamwood_planks","botania:star_sword","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","croptopia:mead","botania:alfheim_portal","mcwfurnitures:stripped_acacia_counter","mcwwindows:birch_four_window","biomesoplenty:fir_boat","sophisticatedstorage:diamond_to_netherite_tier_upgrade","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","immersiveengineering:crafting/gunpowder_from_dusts","modularrouters:energy_distributor_module","mcwroofs:blue_terracotta_attic_roof","handcrafted:spider_trophy","mcwbiomesoplenty:willow_waffle_door","mcwroofs:lime_terracotta_lower_roof","ae2:network/cables/dense_smart_green","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","ad_astra:cyan_industrial_lamp","mcwlights:chain_wall_lantern","allthemods:ae2/smart_dense_to_smart_normal","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","mcwtrpdoors:acacia_barrel_trapdoor","xnet:connector_green","advanced_ae:smallappupgrade","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mcwfurnitures:stripped_acacia_bookshelf_cupboard","mythicbotany:central_rune_holder","sophisticatedbackpacks:void_upgrade","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","railcraft:bushing_gear_bronze","mcwroofs:acacia_upper_steep_roof","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","mcwtrpdoors:acacia_barn_trapdoor","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","utilitix:oak_shulker_boat_with_shell","create:crafting/kinetics/gearbox","botania:terrasteel_block","sophisticatedstorage:copper_to_iron_tier_upgrade","mcwbiomesoplenty:umbran_modern_door","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","rftoolsutility:screen_link","minecraft:leather_helmet","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","create:crafting/materials/sand_paper","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","mcwbiomesoplenty:redwood_nether_door","energymeter:meter","mcwlights:covered_lantern","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","botania:elementium_sword","supplementaries:bubble_blower","supplementaries:crank","securitycraft:secret_bamboo_sign_item","minecraft:recovery_compass","connectedglass:clear_glass_gray_pane2","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","ae2:network/cables/covered_red","supplementaries:netherite_trapdoor","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","cfm:acacia_cabinet","mcwbiomesoplenty:willow_bark_glass_door","minecraft:pink_terracotta","aiotbotania:livingrock_axe","mcwbiomesoplenty:mahogany_plank_pane_window","mcwbiomesoplenty:maple_nether_door","mcwbiomesoplenty:empyreal_classic_door","connectedglass:clear_glass_brown_pane2","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:palm_waffle_door","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","ae2:network/cables/smart_fluix_clean","nethersdelight:golden_machete","ae2:network/parts/annihilation_plane_alt","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","ae2:network/blocks/energy_energy_cell","mcwbiomesoplenty:hellbark_barn_door","mcwlights:chain_lantern","securitycraft:camera_monitor","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","mcwbiomesoplenty:jacaranda_modern_door","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:dye_pink_wool","cfm:oak_kitchen_counter","minecraft:fire_charge","minecraft:golden_hoe","mcwfurnitures:acacia_lower_triple_drawer","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","railcraft:any_detector","ad_astra:gray_industrial_lamp","alchemistry:reactor_energy","botania:mana_distributor","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","sophisticatedstorage:controller","aiotbotania:livingrock_shovel","computercraft:printer","mcwroofs:stone_bricks_upper_steep_roof","minecraft:stone_button","mcwbiomesoplenty:redwood_bark_glass_door","simplylight:illuminant_cyan_block_on_dyed","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","pneumaticcraft:regulator_tube_module","ae2:network/wireless_terminal","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","mcwbiomesoplenty:umbran_japanese2_door","travelersbackpack:dye_lime_sleeping_bag","railcraft:brass_ingot_crafted_with_ingots","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","pneumaticcraft:transfer_gadget","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","enderio:extraction_speed_upgrade_3","mcwroofs:cobblestone_upper_lower_roof","enderio:extraction_speed_upgrade_4","mcwfences:deepslate_railing_gate","enderio:extraction_speed_upgrade_1","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","botania:open_bucket","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","cfm:stripped_crimson_mail_box","enderio:resetting_lever_five","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:hellbark_beach_door","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","ae2:network/cables/covered_cyan","minecraft:lime_candle","mcwbiomesoplenty:umbran_classic_door","ae2:block_cutter/slabs/sky_stone_slab","cfm:purple_cooler","ae2:network/parts/terminals","securitycraft:keypad_frame","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","botania:terrasteel_leggings","enderio:resetting_lever_thirty_inv_from_prev","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","mcwbiomesoplenty:hellbark_bark_glass_door","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","pneumaticcraft:vortex_tube","blue_skies:glowing_nature_stone","xnet:connector_routing","mcwwindows:warped_louvered_shutter","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:umbran_stable_head_door","travelersbackpack:cake","megacells:network/mega_interface","mcwroofs:green_terracotta_attic_roof","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwwindows:spruce_plank_parapet","mcwbiomesoplenty:palm_bark_glass_door","ae2:network/blocks/spatial_anchor","mcwbiomesoplenty:umbran_cottage_door","utilitix:directional_highspeed_rail","mcwwindows:light_blue_curtain","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","securitycraft:reinforced_purple_stained_glass","mcwpaths:oak_planks_path","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","mcwbiomesoplenty:pine_stable_head_door","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","mcwbiomesoplenty:redwood_modern_door","cfm:stripped_acacia_park_bench","securitycraft:secret_bamboo_hanging_sign","ae2:network/blocks/storage_chest","mcwroofs:oak_roof","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwbiomesoplenty:palm_japanese2_door","mcwroofs:thatch_roof","mcwbiomesoplenty:willow_curved_gate","minecraft:orange_stained_glass_pane_from_glass_pane","delightful:knives/osmium_knife","ae2:network/wireless_booster","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","mcwfences:blackstone_brick_railing_gate","expatternprovider:ingredient_buffer","sophisticatedbackpacks:filter_upgrade","connectedglass:tinted_borderless_glass_gray2","mcwbiomesoplenty:maple_japanese_door","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","ae2:network/cables/dense_covered_purple","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwfurnitures:acacia_stool_chair","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","ae2:decorative/quartz_fixture_from_anchors","mcwpaths:cobblestone_basket_weave_paving","allthearcanistgear:unobtainium_hat_smithing","create:crafting/materials/andesite_alloy","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","mcwfurnitures:stripped_acacia_modern_chair","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","mcwlights:golden_chandelier","minecraft:armor_stand","mcwpaths:stone_flagstone_stairs","chemlib:sodium_block_to_ingot","cfm:stripped_birch_kitchen_drawer","mcwfurnitures:stripped_acacia_lower_triple_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","twigs:chiseled_bricks","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","mcwlights:tavern_wall_lantern","croptopia:cheese","dyenamics:bed/rose_bed","dyenamics:navy_wool","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","mcwwindows:magenta_mosaic_glass_pane","alltheores:platinum_gear","mcwtrpdoors:metal_full_trapdoor","botania:cosmetic_black_bowtie","connectedglass:borderless_glass_pink_pane2","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","mcwbiomesoplenty:willow_beach_door","mcwpaths:andesite_running_bond_slab","mcwfurnitures:stripped_oak_lower_triple_drawer","utilitarian:utility/oak_logs_to_boats","croptopia:potato_chips","minecraft:chiseled_stone_bricks_stone_from_stonecutting","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","chemlib:sodium_ingot_from_blasting_sodium_dust","alltheores:mek_processing/iridium/ingot/from_dust_blasting","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","botania:manasteel_axe","mcwwindows:oak_blinds","mcwtrpdoors:acacia_cottage_trapdoor","mcwfences:birch_wired_fence","mcwbiomesoplenty:empyreal_beach_door","cfm:dye_orange_picket_fence","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","mcwdoors:metal_hospital_door","railcraft:signal_circuit","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","mcwbiomesoplenty:mahogany_swamp_door","croptopia:buttered_toast","mcwbiomesoplenty:hellbark_mystic_door","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","cfm:stripped_acacia_kitchen_counter","dyenamics:ultramarine_terracotta","mcwbiomesoplenty:hellbark_bamboo_door","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbiomesoplenty:umbran_barn_door","supplementaries:flags/flag_light_blue","mcwbiomesoplenty:maple_modern_door","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","botania:gray_petal_block","mcwbiomesoplenty:empyreal_cottage_door","utilitix:tiny_charcoal_to_tiny","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","allthecompressed:compress/redstone_block_1x","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","supplementaries:sign_post_oak","minecraft:dye_gray_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","botania:terrasteel_boots","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","gtceu:shapeless/data_stick_nbt","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","chemlib:gallium_ingot_from_blasting_gallium_dust","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","sophisticatedstorage:storage_tool","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","ae2:network/cables/dense_covered_pink","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","ae2:network/cables/glass_brown","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","ae2:tools/portable_item_cell_256k","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","securitycraft:secret_crimson_sign_item","mcwbiomesoplenty:empyreal_window","mcwroofs:purple_terracotta_upper_lower_roof","mcwbiomesoplenty:jacaranda_nether_door","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","ae2:network/parts/cable_anchor","mcwfurnitures:acacia_counter","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","blue_skies:cake_compat","utilitarian:utility/acacia_logs_to_doors","cfm:stripped_acacia_chair","ae2:network/blocks/crystal_processing_growth_accelerator","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","pneumaticcraft:liquid_hopper","botania:green_pavement","twilightforest:mining_boat","aether:golden_dart","enderio:vibrant_alloy_block","handcrafted:terracotta_cup","ad_astra:ti_69","mcwroofs:acacia_attic_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","minecraft:magenta_dye_from_blue_red_pink","mcwbiomesoplenty:dead_picket_fence","mcwbiomesoplenty:hellbark_window2","mcwbiomesoplenty:mahogany_nether_door","minecraft:sugar_from_sugar_cane","mcwbiomesoplenty:fir_bark_glass_door","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","botania:petal_brown","connectedglass:borderless_glass_lime2","ae2:network/parts/terminals_crafting","cfm:stripped_oak_chair","wirelesschargers:basic_wireless_player_charger","cfm:orange_trampoline","cfm:diving_board","mcwwindows:oak_shutter","cfm:rock_path","botania:mana_spreader","connectedglass:clear_glass_yellow2","connectedglass:scratched_glass_black1","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","mcwbiomesoplenty:redwood_waffle_door","allthecompressed:compress/energetic_alloy_block_1x","botania:slingshot","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","botania:lens_tripwire","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwfences:spruce_pyramid_gate","mcwbiomesoplenty:willow_barn_door","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","allthecompressed:compress/energetic_alloy_block_2x","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","mcwbiomesoplenty:umbran_nether_door","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","mcwwindows:crimson_planks_four_window","minecraft:piston","ad_astra:large_gas_tank","mcwbiomesoplenty:maple_waffle_door","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","undergarden:regalium_block","mcwbiomesoplenty:umbran_plank_pane_window","botania:virus_necrodermal","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","mcwbiomesoplenty:hellbark_japanese2_door","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","mcwbiomesoplenty:dead_modern_door","mcwbiomesoplenty:fir_stable_door","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","mcwbiomesoplenty:palm_classic_door","mcwdoors:acacia_cottage_door","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","chemlib:potassium_ingot_to_block","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","domum_ornamentum:black_floating_carpet","supplementaries:daub_brace","mcwfences:spruce_horse_fence","croptopia:beef_jerky","handcrafted:stackable_book","handcrafted:oak_shelf","mcwbiomesoplenty:magic_modern_door","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","computercraft:speaker","ad_astra:space_helmet","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","mcwroofs:lime_striped_awning","mcwbiomesoplenty:palm_tropical_door","additionallanterns:bricks_lantern","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","mcwbiomesoplenty:palm_cottage_door","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","rftoolsbuilder:vehicle_control_module","botania:petal_gray","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","mcwbiomesoplenty:pine_japanese2_door","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","botania:fire_rod","securitycraft:reinforced_crimson_fence_gate","mcwbiomesoplenty:rainbow_birch_hedge","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","mcwroofs:light_gray_steep_roof","botania:cosmetic_red_ribbons","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","botania:elementium_shears","create:layered_andesite_from_stone_types_andesite_stonecutting","dyenamics:ultramarine_wool","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","mcwfences:oak_stockade_fence","mcwfences:crimson_wired_fence","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:dye_lime_picket_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","cfm:stripped_acacia_bedside_cabinet","securitycraft:secret_mangrove_hanging_sign","mcwpaths:andesite_square_paving","pneumaticcraft:pressure_gauge","mcwfences:dark_oak_hedge","mcwbiomesoplenty:umbran_barn_glass_door","mcwfurnitures:acacia_double_wardrobe","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","mythicbotany:yggdrasil_branch","mcwroofs:red_terracotta_attic_roof","mcwdoors:acacia_classic_door","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","computercraft:pocket_computer_advanced","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","mcwwindows:white_curtain","mcwroofs:white_terracotta_roof","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","connectedglass:clear_glass_lime_pane2","alltheores:iridium_plate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","everythingcopper:copper_hopper","minecolonies:potato_soup","botania:spark_upgrade_dispersive","mcwbiomesoplenty:dead_bark_glass_door","mcwbiomesoplenty:fir_paper_door","mcwbiomesoplenty:pine_mystic_door","chemlib:sodium_ingot_from_smelting_sodium_dust","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:banner/mint_banner","botania:dreamwood_wand","botania:cosmetic_unicorn_horn","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","dyenamics:lavender_stained_glass","twilightforest:time_boat","mcwbiomesoplenty:fir_japanese_door","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","alchemistry:fusion_chamber_controller","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/smart_gray","mcwfurnitures:stripped_acacia_bookshelf_drawer","ae2:network/cables/dense_covered_white","botania:cosmetic_thick_eyebrows","modularrouters:distributor_module","mcwpaths:andesite_crystal_floor_stairs","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","cfm:red_cooler","botania:dreamwood_wall","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","create:crafting/logistics/pulse_extender","xnet:facade","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","enderio:resetting_lever_five_from_inv","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_1","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","cfm:acacia_desk_cabinet","undergarden:grongle_boat","ae2:decorative/quartz_fixture","mcwbiomesoplenty:dead_nether_door","cfm:acacia_blinds","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","mcwbiomesoplenty:dead_paper_door","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwbiomesoplenty:magic_beach_door","mcwwindows:spruce_curtain_rod","ae2:network/blocks/quantum_ring","aether:skyroot_loom","minecraft:brick_wall","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwfurnitures:stripped_acacia_glass_table","mcwfences:diorite_grass_topped_wall","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwfurnitures:acacia_chair","mcwbiomesoplenty:palm_nether_door","minecraft:raw_iron_block","mcwwindows:stone_window2","minecraft:oak_button","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:fir_western_door","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","simplylight:illuminant_yellow_block_on_toggle","dyenamics:honey_wool","mcwroofs:acacia_top_roof","xnet:advanced_connector_yellow_dye","enderio:soul_chain","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","minecraft:diamond_helmet","mcwfurnitures:acacia_glass_table","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","connectedglass:borderless_glass_lime_pane2","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bigreactors:smelting/graphite_from_coalblock","handcrafted:tropical_fish_trophy","simplylight:illuminant_brown_block_dyed","minecraft:compass","cfm:stripped_acacia_table","mcwbiomesoplenty:jacaranda_plank_pane_window","bigreactors:energizer/computerport","littlelogistics:vacuum_barge","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","mcwbiomesoplenty:maple_mystic_door","expatternprovider:silicon_block","minecraft:item_frame","itemcollectors:basic_collector","minecraft:loom","ad_astra:steel_engine","enderio:resetting_lever_three_hundred_inv","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","ad_astra:small_lime_industrial_lamp","sophisticatedstorage:stack_downgrade_tier_3","minecraft:arrow","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","dyenamics:wine_wool","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwbiomesoplenty:mahogany_stable_head_door","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","mcwpaths:brick_flagstone_slab","expatternprovider:ei_part","alltheores:nickel_plate","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","advanced_ae:quantumaccel","chemlib:potassium_ingot_to_nugget","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","dyenamics:banner/rose_banner","enderio:redstone_alloy_nugget_to_ingot","everythingcopper:copper_pressure_plate","mcwroofs:black_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","pneumaticcraft:logistics_frame_storage","mcwbiomesoplenty:jacaranda_beach_door","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","connectedglass:borderless_glass_brown_pane2","botania:mana_pool","cfm:brown_cooler","delightful:knives/terra_knife","mcwfences:diorite_pillar_wall","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","undergarden:shard_torch","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","paraglider:paraglider","botania:petal_gray_double","expatternprovider:ebus_in","sophisticatedstorage:shulker_box","computercraft:disk_drive","mcwroofs:blue_striped_awning","pneumaticcraft:wall_lamp_red","mcwbiomesoplenty:mahogany_modern_door","immersiveengineering:crafting/component_steel","utilitarian:utility/acacia_logs_to_boats","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","botania:cosmetic_lusitanic_shield","mcwfences:mangrove_hedge","sophisticatedbackpacks:compacting_upgrade","chemlib:tantalum_ingot_from_smelting_tantalum_dust","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","ae2:network/cables/covered_light_blue","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","enderio:resetting_lever_thirty_inv_from_base","botania:cosmetic_botanist_emblem","minecraft:jukebox","mcwwindows:jungle_curtain_rod","ad_astra:small_brown_industrial_lamp","mcwbiomesoplenty:palm_barn_glass_door","cfm:acacia_chair","mcwwindows:oak_window","mcwbiomesoplenty:mahogany_waffle_door","mcwbiomesoplenty:empyreal_stable_head_door","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","ae2:network/cables/smart_brown","mcwwindows:spruce_plank_pane_window","alltheores:signalum_rod","create:crafting/kinetics/white_seat","chemlib:gallium_ingot_to_block","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","ae2:network/cables/covered_orange","mcwwindows:red_curtain","mcwroofs:stone_bricks_steep_roof","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","railcraft:coal_coke_block_from_coal_coke","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","minecraft:baked_potato_from_campfire_cooking","connectedglass:tinted_borderless_glass_brown2","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","mcwroofs:red_terracotta_top_roof","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:jacaranda_waffle_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gray_terracotta_attic_roof","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","securitycraft:reinforced_light_gray_stained_glass","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwroofs:bricks_attic_roof","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","connectedglass:borderless_glass_yellow2","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","ad_astra:airlock","mcwroofs:grass_steep_roof","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","chemlib:magnesium_ingot_from_blasting_magnesium_dust","minecraft:end_stone_bricks","croptopia:egg_roll","mcwbiomesoplenty:dead_waffle_door","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","allthecompressed:compress/vibrant_alloy_block_1x","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","botania:lens_firework","mcwfurnitures:stripped_acacia_drawer_counter","expatternprovider:oversize_interface_alt","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","domum_ornamentum:yellow_brick_extra","minecraft:dye_brown_wool","aether:iron_pendant","securitycraft:redstone_module","mcwbiomesoplenty:pine_bamboo_door","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","minecraft:orange_candle","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","botania:quartz_sunny","create:framed_glass_from_glass_colorless_stonecutting","supplementaries:candle_holders/candle_holder_brown_dye","mcwfurnitures:stripped_acacia_cupboard_counter","botania:terrasteel_helmet","mcwbiomesoplenty:dead_swamp_door","railcraft:signal_lamp","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","sophisticatedstorage:decoration_table","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:redwood_classic_door","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","pneumaticcraft:paper_from_tag_filter","allthecompressed:compress/vibrant_alloy_block_2x","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","mcwbiomesoplenty:magic_waffle_door","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:gold_block","botania:floating_pure_daisy","minecraft:spruce_boat","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","sophisticatedstorage:storage_input","mcwbiomesoplenty:redwood_cottage_door","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","ae2:network/cables/dense_smart_cyan","minecraft:paper","travelersbackpack:dye_orange_sleeping_bag","chemlib:tantalum_ingot_to_block","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","cfm:acacia_bedside_cabinet","connectedglass:scratched_glass_cyan2","securitycraft:secret_mangrove_sign_item","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","botania:conversions/terrasteel_block_deconstruct","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwroofs:light_gray_terracotta_roof","supplementaries:candle_holders/candle_holder_gray_dye","botania:incense_plate","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:lime_terracotta_steep_roof","mcwlights:golden_small_chandelier","mcwbiomesoplenty:willow_paper_door","mcwwindows:lime_mosaic_glass","railways:stonecutting/riveted_locometal","securitycraft:reinforced_observer","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","mcwfences:acacia_curved_gate","allthemodium:unobtainium_rod","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","mcwbiomesoplenty:redwood_swamp_door","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","alltheores:lead_rod","mcwbiomesoplenty:jacaranda_western_door","mcwwindows:oak_plank_window","mcwwindows:oak_log_parapet","mcwbiomesoplenty:hellbark_modern_door","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","mcwfurnitures:stripped_acacia_modern_wardrobe","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","littlelogistics:spring","securitycraft:reinforced_bamboo_fence","mcwbiomesoplenty:jacaranda_barn_door","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","ae2:network/cables/covered_brown","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwdoors:acacia_beach_door","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:redwood_barn_glass_door","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","connectedglass:borderless_glass_gray_pane2","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","supplementaries:candle_holders/candle_holder_yellow_dye","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","minecraft:diamond_pickaxe","domum_ornamentum:blockbarreldeco_onside","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","pneumaticcraft:cannon_barrel","alltheores:platinum_plate","ad_astra:encased_steel_block","mcwroofs:orange_terracotta_upper_steep_roof","fluxnetworks:wipe_fluxpoint","pneumaticcraft:reinforced_brick_wall","ae2:network/cables/smart_purple","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","utilitarian:utility/acacia_logs_to_stairs","rftoolsbuilder:green_shield_template_block","cfm:acacia_coffee_table","mcwroofs:green_striped_awning","pneumaticcraft:wall_lamp_inverted_blue","securitycraft:codebreaker","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:light_blue_picket_gate","allthearcanistgear:unobtainium_leggings_smithing","constructionwand:infinity_wand","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","botania:cosmetic_hyper_plus","expatternprovider:assembler_matrix_wall","mcwroofs:black_roof","securitycraft:reinforced_gray_stained_glass_pane_from_glass","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","botania:cosmetic_eyepatch","cfm:blue_kitchen_counter","mcwbiomesoplenty:maple_four_window","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","chemlib:magnesium_nugget_to_ingot","mcwwindows:stripped_oak_log_window","ae2:network/cables/smart_magenta","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","botania:cosmetic_questgiver_mark","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mythicbotany:mana_collector","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","supplementaries:flags/flag_cyan","megacells:network/cell_dock","supplementaries:statue","mcwbiomesoplenty:mahogany_tropical_door","cfm:pink_grill","mcwroofs:gutter_middle_orange","undergarden:torch_ditchbulb_paste","mcwwindows:warped_stem_parapet","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","securitycraft:secret_jungle_sign_item","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","ae2:network/blocks/pattern_providers_interface","minecraft:oak_stairs","alchemistry:liquifier","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","ad_astra:small_orange_industrial_lamp","botania:aura_ring_greater","mcwbiomesoplenty:maple_stable_head_door","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","securitycraft:reinforced_piston","cfm:purple_kitchen_counter","securitycraft:reinforced_yellow_stained_glass","alltheores:bronze_plate","expatternprovider:threshold_export_bus","mcwpaths:brick_crystal_floor_slab","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","mcwfurnitures:acacia_double_drawer","aquaculture:tin_can_to_iron_nugget","alltheores:enderium_plate","mcwwindows:yellow_curtain","connectedglass:clear_glass_orange_pane2","mcwwindows:purple_mosaic_glass","allthecompressed:compress/acacia_log_1x","modularrouters:player_module","pneumaticcraft:pressure_chamber_wall","xnet:connector_yellow","mcwbiomesoplenty:pine_classic_door","mcwbiomesoplenty:hellbark_plank_pane_window","allthecompressed:decompress/vibrant_alloy_block_1x","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","alltheores:raw_iridium_block","mcwbiomesoplenty:willow_japanese_door","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwbiomesoplenty:pine_cottage_door","domum_ornamentum:gray_floating_carpet","ae2:network/cables/smart_red","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","comforts:sleeping_bag_white","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","ad_astra:yellow_industrial_lamp","cfm:dye_brown_picket_gate","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","chemlib:tantalum_ingot_from_blasting_tantalum_dust","dyenamics:banner/honey_banner","mcwbiomesoplenty:jacaranda_classic_door","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","securitycraft:secret_spruce_sign_item","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","mcwbiomesoplenty:pine_nether_door","cfm:oak_upgraded_fence","mcwbiomesoplenty:hellbark_barn_glass_door","deepresonance:radiation_monitor","botania:placeholder","railcraft:steel_gear","immersiveengineering:crafting/treated_wood_horizontal","allthemodium:piglich_heart_block","enderio:pulsating_alloy_nugget","sophisticatedstorage:crafting_upgrade","bigreactors:smelting/graphite_from_charcoal","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","minecraft:stone_bricks","securitycraft:reinforced_red_stained_glass","connectedglass:borderless_glass_cyan2","connectedglass:tinted_borderless_glass_orange2","mcwbiomesoplenty:jacaranda_wired_fence","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","minecraft:dye_orange_wool","aether:leather_gloves","xnet:netcable_green_dye","utilitarian:utility/acacia_logs_to_slabs","mcwbiomesoplenty:maple_plank_window2","mcwwindows:cherry_plank_window","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","mcwbiomesoplenty:jacaranda_bamboo_door","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","cfm:yellow_kitchen_drawer","twilightforest:dark_boat","minecraft:gunpowder","travelersbackpack:bookshelf","biomesoplenty:umbran_boat","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","botania:dye_light_gray","mcwbiomesoplenty:redwood_four_panel_door","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","immersiveengineering:crafting/alu_wallmount","pylons:potion_filter","enderio:resetting_lever_thirty","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","chemlib:lithium_ingot_to_nugget","create:crafting/kinetics/gray_seat","botania:conversions/light_gray_petal_block_deconstruct","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","pneumaticcraft:wall_lamp_light_gray","minecraft:dye_yellow_wool","ad_astra:steel_factory_block","mcwwindows:acacia_log_parapet","simplylight:illuminant_red_block_on_dyed","dyenamics:fluorescent_stained_glass","alltheores:bronze_rod","occultism:crafting/spirit_torch","chemlib:potassium_nugget_to_ingot","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","pneumaticcraft:reinforced_bricks_from_tile","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","alltheores:aluminum_gear","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","cfm:lime_grill","mcwfurnitures:acacia_table","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwroofs:lime_terracotta_upper_steep_roof","mcwwindows:cyan_curtain","securitycraft:reinforced_cyan_stained_glass","ae2:network/cables/smart_yellow","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","botania:cosmetic_groucho_glasses","mcwroofs:gray_terracotta_upper_steep_roof","minecraft:dye_lime_wool","securitycraft:secret_sign_item","mcwbiomesoplenty:fir_tropical_door","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","aquaculture:heavy_hook","mcwbiomesoplenty:palm_western_door","minecraft:yellow_terracotta","mcwwindows:acacia_window","mcwroofs:green_terracotta_roof","croptopia:food_press","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","connectedglass:scratched_glass_pink2","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","mcwfurnitures:oak_chair","mcwbiomesoplenty:willow_four_panel_door","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwwindows:stripped_crimson_stem_four_window","utilitix:spruce_shulker_boat_with_shell","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwbridges:oak_bridge_pier","pneumaticcraft:small_tank","ae2:network/cables/glass_green","mcwbiomesoplenty:willow_classic_door","botania:terrasteel_chestplate","cfm:magenta_kitchen_counter","create:crafting/kinetics/steam_engine","pneumaticcraft:wall_lamp_gray","cfm:spruce_mail_box","minecraft:barrel","mcwwindows:dark_oak_pane_window","sgjourney:fire_pit","utilitix:tiny_coal_to_tiny","pneumaticcraft:reinforced_brick_pillar","immersiveengineering:crafting/connector_hv","mcwpaths:brick_crystal_floor","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","mcwdoors:acacia_swamp_door","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwbiomesoplenty:willow_cottage_door","mcwfurnitures:oak_glass_kitchen_cabinet","ae2:network/cables/glass_fluix","botania:cosmetic_witch_pin","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","dyenamics:conifer_wool","minecraft:dye_gray_carpet","mcwpaths:gravel_path_block","supplementaries:lapis_bricks","minecraft:cauldron","mcwbridges:glass_bridge_pier","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","immersiveengineering:crafting/alu_fence","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","botania:petal_white_double","minecraft:andesite_wall","railcraft:steel_tank_gauge","cfm:stripped_birch_kitchen_sink_light","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:iron_ring","dimstorage:dim_core","minecraft:cobblestone_slab","cfm:cyan_kitchen_sink","mcwbiomesoplenty:dead_beach_door","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","chemlib:gallium_ingot_from_smelting_gallium_dust","utilitix:directional_rail","mcwbiomesoplenty:mahogany_four_panel_door","mcwbiomesoplenty:stripped_pine_log_four_window","mcwfurnitures:stripped_acacia_covered_desk","supplementaries:candle_holders/candle_holder_lime_dye","mcwbiomesoplenty:fir_barn_glass_door","railcraft:copper_gear","alltheores:enderium_rod","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","mythicbotany:gaia_pylon","mcwfences:dark_oak_curved_gate","ad_astra:pink_industrial_lamp","mcwwindows:crimson_plank_pane_window","mcwwindows:cherry_curtain_rod","mcwroofs:stone_bricks_top_roof","minecraft:chest","alltheores:diamond_plate","cfm:magenta_trampoline","supplementaries:pulley","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwbiomesoplenty:magic_paper_door","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwdoors:acacia_four_panel_door","mcwfences:birch_horse_fence","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","connectedglass:clear_glass_lime2","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","minecraft:dye_gray_wool","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwbiomesoplenty:hellbark_stable_head_door","mcwfences:mud_brick_pillar_wall","mcwbiomesoplenty:jacaranda_swamp_door","mcwroofs:grass_lower_roof","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwroofs:gutter_base_red","mcwtrpdoors:acacia_glass_trapdoor","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","createoreexcavation:vein_finder","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","ae2:network/cells/spatial_components","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","bigreactors:crafting/blutonium_ingot_to_nugget","enderio:soularium_pressure_plate","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","mcwfurnitures:acacia_modern_desk","botania:spark_upgrade_recessive","sophisticatedbackpacks:chipped/carpenters_table_upgrade","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","sophisticatedstorage:oak_limited_barrel_1","cfm:magenta_cooler","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_2","sophisticatedstorage:oak_limited_barrel_3","mcwbiomesoplenty:jacaranda_mystic_door","sophisticatedstorage:oak_limited_barrel_4","dyenamics:navy_terracotta","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","mcwfurnitures:acacia_end_table","everythingcopper:copper_leggings","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","securitycraft:reinforced_blue_stained_glass_pane_from_glass","botania:spark_upgrade_isolated","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","alltheores:aluminum_rod","sophisticatedstorage:basic_to_iron_tier_from_basic_to_copper_tier","mcwroofs:yellow_striped_awning","minecraft:light_blue_terracotta","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","minecraft:orange_dye_from_red_yellow","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","mcwbiomesoplenty:empyreal_waffle_door","croptopia:flour","ae2:tools/portable_item_cell_16k","minecraft:cyan_candle","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","expatternprovider:precise_storage_bus","railcraft:steel_tank_valve","cfm:cyan_cooler","botania:cosmetic_cat_ears","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","ad_astra:steel_rod","mcwdoors:acacia_modern_door","mcwbiomesoplenty:mahogany_mystic_door","mcwwindows:prismarine_four_window","enderio:redstone_alloy_grinding_ball","mcwroofs:light_gray_roof","securitycraft:secret_jungle_hanging_sign","mcwbiomesoplenty:umbran_beach_door","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:umbran_bark_glass_door","connectedglass:scratched_glass_orange2","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:pine_tropical_door","travelersbackpack:redstone","mcwfurnitures:stripped_acacia_end_table","mcwbiomesoplenty:maple_glass_door","mcwbiomesoplenty:maple_barn_door","minecraft:slime_block","minecraft:acacia_planks","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","mcwbiomesoplenty:fir_nether_door","enderio:soularium_grinding_ball","chimes:glass_bells","mcwwindows:iron_shutter","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","enderio:empty_soul_vial","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","ad_astra:nasa_workbench","ad_astra:etrionic_capacitor","mcwbiomesoplenty:empyreal_japanese2_door","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","mcwbiomesoplenty:empyreal_swamp_door","cfm:dye_yellow_picket_gate","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","railcraft:steel_shovel","enderio:resetting_lever_sixty_inv","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwfurnitures:stripped_acacia_coffee_table","alltheores:copper_rod","minecraft:shulker_box","simplylight:illuminant_slab","enderio:copper_alloy_ingot","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","minecraft:white_carpet","ae2:network/cables/covered_blue","aether:diamond_gloves","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","croptopia:pork_jerky","enderio:resetting_lever_sixty_from_inv","allthecompressed:compress/iron_block_1x","mcwroofs:base_lower_roof","mcwbiomesoplenty:dead_stable_door","securitycraft:secret_birch_sign_item","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","minecraft:ender_chest","railcraft:nickel_gear","mcwbiomesoplenty:willow_western_door","minecraft:iron_trapdoor","minecraft:brown_candle","pneumaticcraft:thermal_lagging","ae2:network/cables/dense_covered_yellow","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","modularrouters:void_module","ae2:network/blocks/storage_drive","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","immersiveengineering:crafting/redstone_acid","additionallanterns:dark_prismarine_lantern","alltheores:iridium_rod","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","domum_ornamentum:lime_floating_carpet","botania:conversions/blazeblock_deconstruct","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","securitycraft:reinforced_warped_fence_gate","mcwfurnitures:acacia_bookshelf","botania:skydirt_rod","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","ae2:network/cables/covered_lime","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","expatternprovider:epa","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","botania:cosmetic_red_glasses","minecraft:light_gray_dye_from_gray_white_dye","ae2:network/cables/dense_covered_light_gray","alltheores:aluminum_plate","mcwfences:blackstone_pillar_wall","minecraft:magenta_dye_from_purple_and_pink","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","ad_astra:small_yellow_industrial_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwbiomesoplenty:stripped_umbran_pane_window","supplementaries:candle_holders/candle_holder_cyan_dye","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","cfm:stripped_acacia_crate","mcwbiomesoplenty:dead_barn_glass_door","silentgear:stone_torch","handcrafted:terracotta_bowl","pneumaticcraft:thermostat_module","botania:spark_upgrade_dominant","cfm:black_kitchen_sink","mcwwindows:red_mosaic_glass","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","connectedglass:borderless_glass_pink2","rftoolsbase:filter_module","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","mcwfurnitures:stripped_acacia_double_wardrobe","mcwwindows:green_mosaic_glass_pane","cfm:dye_cyan_picket_gate","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","mcwbiomesoplenty:pine_western_door","utilitarian:utility/acacia_logs_to_trapdoors","forbidden_arcanus:sanity_meter","botania:diluted_pool","modularrouters:energy_output_module","mcwbiomesoplenty:hellbark_japanese_door","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","enderio:resetting_lever_three_hundred_inv_from_base","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","securitycraft:secret_dark_oak_hanging_sign","mcwwindows:crimson_planks_window2","ae2:network/cables/glass_lime","alchemistry:fission_chamber_controller","alltheores:diamond_rod","mcwbiomesoplenty:palm_stable_door","minecraft:stone_slab_from_stone_stonecutting","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","chemlib:sodium_nugget_to_ingot","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthecompressed:decompress/energetic_alloy_block_1x","minecraft:stone_brick_slab_from_stone_stonecutting","aether:ice_from_bucket_freezing","mcwbiomesoplenty:palm_four_panel_door","pneumaticcraft:speed_upgrade","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","connectedglass:clear_glass_pink_pane2","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","mcwbiomesoplenty:mahogany_japanese2_door","littlelogistics:locomotive_route","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","ae2:network/cables/dense_smart_brown","botania:dye_white","mcwbiomesoplenty:dead_hedge","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","mcwfences:prismarine_grass_topped_wall","ad_astra:small_gray_industrial_lamp","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","mcwbiomesoplenty:fir_glass_door","rftoolsutility:energyplus_module","connectedglass:scratched_glass_yellow_pane2","ae2:tools/portable_item_cell_1k","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","supplementaries:candle_holders/candle_holder_pink_dye","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","ae2:network/cables/glass_blue","alltheores:lumium_rod","minecraft:lapis_lazuli","travelersbackpack:wolf","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","chemlib:lithium_ingot_to_block","securitycraft:secret_acacia_hanging_sign","minecraft:quartz_block","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","pneumaticcraft:wall_lamp_inverted_yellow","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","connectedglass:scratched_glass_lime2","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","mcwbiomesoplenty:mahogany_bamboo_door","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","enderio:resetting_lever_sixty_from_prev","enderio:pulsating_alloy_ingot","mcwwindows:mud_brick_arrow_slit","botania:dye_gray","supplementaries:sack","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","mcwbiomesoplenty:willow_mystic_door","allthemodium:unobtainium_ingot","mcwbiomesoplenty:pine_modern_door","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","mcwtrpdoors:acacia_mystic_trapdoor","securitycraft:cage_trap","handcrafted:terracotta_medium_pot","minecraft:brick_slab","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:umbran_paper_door","mcwbiomesoplenty:empyreal_pane_window","alltheores:iridium_gear","mcwwindows:mangrove_shutter","mcwdoors:print_acacia","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwbiomesoplenty:empyreal_stable_door","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","mcwbiomesoplenty:hellbark_plank_four_window","alchemistry:reactor_input","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","cfm:white_kitchen_drawer","ae2:network/cables/smart_pink","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","comforts:hammock_to_white","mcwwindows:acacia_four_window","chemlib:potassium_ingot_from_blasting_potassium_dust","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","domum_ornamentum:yellow_floating_carpet","enderio:redstone_alloy_nugget","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","ae2:network/cables/dense_smart_yellow","deepresonance:machine_frame","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","mcwbiomesoplenty:dead_japanese_door","mcwbiomesoplenty:jacaranda_tropical_door","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","utilitarian:utility/green_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","cfm:stripped_acacia_cabinet","mcwfences:sandstone_grass_topped_wall","expatternprovider:ex_inscriber","minecraft:glowstone","additionallanterns:smooth_stone_chain","alltheores:gold_rod","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","farmersdelight:fried_egg","botania:cloud_pendant","minecraft:magma_cream","botania:slime_bottle","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","ae2:block_cutter/walls/sky_stone_wall","mcwfurnitures:stripped_oak_table","blue_skies:maple_bookshelf","securitycraft:reinforced_black_stained_glass_pane_from_dye","pneumaticcraft:logistics_frame_default_storage","computercraft:cable","additionallanterns:cobbled_deepslate_lantern","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","mcwroofs:thatch_attic_roof","securitycraft:reinforced_orange_stained_glass_pane_from_glass","securitycraft:reinforced_lime_stained_glass","pneumaticcraft:wall_lamp_inverted_gray","minecraft:dye_lime_carpet","mcwbiomesoplenty:redwood_window2","create:crafting/kinetics/purple_seat","supplementaries:soap/map","mcwpaths:brick_running_bond_path","mcwfurnitures:stripped_acacia_stool_chair","supplementaries:sugar_cube","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","mcwbiomesoplenty:magic_window2","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","simplylight:illuminant_red_block_on_toggle","cfm:acacia_table","mcwpaths:brick_windmill_weave_stairs","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","minecraft:jungle_boat","mcwbiomesoplenty:umbran_stockade_fence","minecraft:dye_cyan_wool","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","botania:apothecary_livingrock","mcwtrpdoors:print_blossom","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","mcwbiomesoplenty:dead_barn_door","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwbiomesoplenty:umbran_japanese_door","enderio:redstone_alloy_ingot","securitycraft:reinforced_oak_fence","railcraft:feed_station","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","dyenamics:persimmon_stained_glass","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","minecraft:dye_pink_carpet","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","railcraft:personal_world_spike","immersiveengineering:crafting/armor_steel_chestplate","handcrafted:oak_nightstand","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","connectedglass:clear_glass_pink2","ad_astra:steel_sliding_door","bigreactors:energizer/powerport_fe","minecraft:dye_brown_bed","immersiveengineering:crafting/stick_aluminum","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","botania:cosmetic_googly_eyes","mcwfurnitures:acacia_modern_wardrobe","xnet:redstoneproxy_update","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","merequester:requester","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","botania:cosmetic_black_tie","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","mcwbiomesoplenty:fir_four_panel_door","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","supplementaries:slingshot","allthemodium:allthemodium_plate","advanced_ae:quantumunit","mcwdoors:acacia_japanese2_door","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","everythingcopper:copper_rail","cfm:black_grill","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","blue_skies:glowing_nature_stonebrick_from_glowstone","mcwlights:green_paper_lamp","supplementaries:spring_launcher","ae2:network/cables/glass_red","mcwbiomesoplenty:empyreal_four_panel_door","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aquaculture:neptunium_ingot_from_nuggets","mcwroofs:bricks_upper_steep_roof","croptopia:french_fries","botania:terraform_rod","minecraft:iron_pickaxe","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","ae2:network/cables/glass_orange","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","allthecompressed:compress/redstone_alloy_block_1x","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","rftoolsutility:fluid_module","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","mcwbiomesoplenty:redwood_stable_head_door","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwwindows:brown_curtain","allthecompressed:compress/redstone_alloy_block_2x","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","mcwbiomesoplenty:empyreal_western_door","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwroofs:acacia_lower_roof","mcwbiomesoplenty:empyreal_japanese_door","expatternprovider:epp_part","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","computercraft:monitor_advanced","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:hellbark_paper_door","cfm:white_kitchen_counter","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","mcwfurnitures:acacia_coffee_table","ae2:network/cables/covered_fluix_clean","mcwbiomesoplenty:umbran_stable_door","botania:elementium_block","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","aether:packed_ice_freezing","botania:cosmetic_anaglyph_glasses","mcwbiomesoplenty:willow_stable_head_door","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","minecraft:gray_candle","trashcans:energy_trash_can","botania:dreamwood_twig","mcwpaths:stone_crystal_floor","cfm:red_kitchen_sink","chimes:copper_chimes","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","botania:cosmetic_pink_flower_bud","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","mcwbiomesoplenty:empyreal_hedge","mcwwindows:crimson_plank_parapet","chemlib:sodium_ingot_to_block","botania:cosmetic_blue_butterfly","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","botania:cosmetic_four_leaf_clover","pylons:clear_potion_filter","allthecompressed:compress/pulsating_alloy_block_2x","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","expatternprovider:wireless_connector","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","allthecompressed:compress/pulsating_alloy_block_1x","cfm:spatula","pneumaticcraft:charging_upgrade","cfm:dye_lime_picket_gate","ae2:network/cables/dense_covered_fluix_clean","allthecompressed:decompress/soularium_block_1x","enderio:extraction_speed_upgrade_3_upgrade","mcwlights:soul_mangrove_tiki_torch","mcwbiomesoplenty:fir_cottage_door","mcwfurnitures:acacia_bookshelf_cupboard","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","xnet:connector_red_dye","ae2:network/cables/covered_fluix","mcwroofs:red_terracotta_roof","minecolonies:shapetool","twigs:schist","mcwwindows:stripped_oak_log_four_window","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwbiomesoplenty:willow_glass_door","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","mcwlights:copper_candle_holder","mcwfurnitures:acacia_covered_desk","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","ae2:tools/nether_quartz_spade","minecraft:honey_block","mcwbiomesoplenty:mahogany_barn_door","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","sophisticatedbackpacks:crafting_upgrade","aquaculture:birch_fish_mount","securitycraft:secret_crimson_hanging_sign","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","buildinggadgets2:template_manager","mcwwindows:jungle_window","botania:white_petal_block","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","aether:blue_ice_freezing","mcwwindows:metal_window2","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","cfm:stripped_acacia_coffee_table","mcwbiomesoplenty:empyreal_barn_glass_door","cfm:white_sofa","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","botania:elven_spreader","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","aiotbotania:livingrock_sword","utilitix:dark_oak_shulker_boat_with_shell","cfm:black_kitchen_drawer","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","pneumaticcraft:heat_sink","chemlib:gallium_ingot_to_nugget","create:crafting/logistics/powered_latch","ae2:network/cables/dense_covered_lime","mcwbiomesoplenty:maple_swamp_door","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","sophisticatedstorage:stonecutter_upgrade","cfm:acacia_kitchen_drawer","mcwroofs:white_terracotta_upper_lower_roof","mcwfences:vintage_metal_fence","aquaculture:double_hook","ae2:shaped/slabs/sky_stone_block","allthemodium:allthemodium_rod","handcrafted:oak_bench","mcwbiomesoplenty:maple_bark_glass_door","minecraft:yellow_candle","dyenamics:bubblegum_dye","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","minecraft:repeater","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","mcwbiomesoplenty:maple_stable_door","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","chemlib:magnesium_ingot_to_block","travelersbackpack:dye_yellow_sleeping_bag","botania:elementium_boots","mcwbiomesoplenty:fir_classic_door","mcwbiomesoplenty:empyreal_glass_door","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","ae2:network/cables/dense_covered_fluix","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwbiomesoplenty:fir_japanese2_door","mcwroofs:oak_attic_roof","mcwtrpdoors:acacia_beach_trapdoor","mcwroofs:base_roof_block","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwbiomesoplenty:palm_window","mcwfurnitures:acacia_lower_bookshelf_drawer","mcwbiomesoplenty:fir_waffle_door","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/spring_green_banner","dyenamics:banner/amber_banner","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","botania:terra_axe","domum_ornamentum:light_blue_floating_carpet","pneumaticcraft:compressed_iron_chestplate","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","mcwbiomesoplenty:pine_beach_door","sophisticatedbackpacks:chipped/glassblower_upgrade","utilitix:acacia_shulker_boat_with_shell","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","enderio:resetting_lever_three_hundred_inv_from_prev","minecraft:iron_chestplate","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","mcwwindows:oak_four_window","cfm:oak_coffee_table","alltheores:platinum_rod","mcwroofs:purple_terracotta_steep_roof","minecraft:light_gray_stained_glass","securitycraft:reinforced_blue_stained_glass","mcwwindows:yellow_mosaic_glass_pane","railcraft:world_spike","mcwdoors:garage_gray_door","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","minecraft:polished_andesite_from_andesite_stonecutting","littlelogistics:seater_car","botania:ender_eye_block","botania:reach_ring","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","minecraft:oak_pressure_plate","aether:skyroot_beehive","cfm:brown_grill","mcwwindows:quartz_window","mcwroofs:gray_roof","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","mcwroofs:gutter_base_brown","cfm:acacia_crate","botania:glimmering_livingwood_log","mcwbiomesoplenty:redwood_tropical_door","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","securitycraft:reinforced_chiseled_bookshelf","utilitix:mangrove_shulker_boat_with_shell","cfm:gray_kitchen_drawer","minecraft:clock","dyenamics:conifer_dye","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","enderio:entity_filter","mcwwindows:warped_curtain_rod","railcraft:lead_gear","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","allthetweaks:greg_star_block","mcwfences:oak_wired_fence","botania:twig_wand","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwdoors:acacia_western_door","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","mcwbiomesoplenty:maple_paper_door","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","botania:livingwood_bow","minecraft:purple_stained_glass","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwbiomesoplenty:hellbark_western_door","mcwroofs:purple_terracotta_roof","connectedglass:scratched_glass_orange_pane2","botania:spark_changer","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","minecraft:polished_andesite_slab_from_andesite_stonecutting","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","mcwbiomesoplenty:magic_western_door","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","mcwbiomesoplenty:dead_four_panel_door","sophisticatedstorage:basic_to_iron_tier_upgrade","twigs:rhyolite","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","mcwbiomesoplenty:pine_swamp_door","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","enderio:advanced_item_filter","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","immersiveengineering:crafting/alu_scaffolding_standard","pneumaticcraft:armor_upgrade","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","mcwlights:festive_lantern","botania:conversions/gray_petal_block_deconstruct","mcwbiomesoplenty:magic_tropical_door","securitycraft:block_change_detector","supplementaries:pancake_fd","modularrouters:placer_module","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","minecraft:netherite_scrap_from_blasting","botania:lens_warp","mcwbiomesoplenty:dead_bamboo_door","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","minecraft:lime_stained_glass_pane_from_glass_pane","botania:cosmetic_ancient_mask","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","undergarden:wigglewood_boat","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwpaths:andesite_crystal_floor_slab","pneumaticcraft:elevator_frame","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","connectedglass:scratched_glass_cyan_pane2","enderio:pulsating_alloy_grinding_ball","botania:cosmetic_orange_shades","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","botania:dreamwood_slab","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","create:copper_bars_from_ingots_copper_stonecutting","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","mcwbiomesoplenty:pine_bark_glass_door","securitycraft:secret_dark_oak_sign_item","ae2:network/cables/dense_covered_cyan","sophisticatedbackpacks:smithing_upgrade","mcwbiomesoplenty:palm_beach_door","alltheores:invar_ingot_from_dust_blasting","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwwindows:acacia_plank_window","mcwbiomesoplenty:stripped_dead_log_window","pneumaticcraft:heat_pipe","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","mcwbiomesoplenty:magic_bamboo_door","mcwfurnitures:oak_lower_triple_drawer","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwfurnitures:stripped_acacia_double_drawer","utilitix:bamboo_shulker_raft_with_shell","enderio:soularium_ingot","sophisticatedbackpacks:anvil_upgrade","ae2:decorative/quartz_glass","enderio:staff_of_travelling","minecraft:pink_candle","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","mcwroofs:thatch_lower_roof","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwroofs:white_roof_block","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwbiomesoplenty:mahogany_stable_door","securitycraft:block_pocket_manager","botania:cosmetic_eerie_mask","enderio:pulsating_alloy_nugget_to_ingot","mcwlights:soul_cherry_tiki_torch","expatternprovider:ei","botania:red_pavement","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","mcwpaths:brick_running_bond_stairs","enderio:resetting_lever_sixty_inv_from_prev","botania:cosmetic_wicked_eyepatch","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","cfm:purple_kitchen_drawer","botania:flower_bag","ad_astra:steel_plateblock","pneumaticcraft:compressed_brick_wall","mcwbiomesoplenty:dead_western_door","mcwbiomesoplenty:maple_western_door","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","mcwfurnitures:acacia_triple_drawer","ae2:network/cables/dense_smart_red","mcwlights:golden_candle_holder","mcwfurnitures:stripped_acacia_double_drawer_counter","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","railcraft:bag_of_cement","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","botania:light_gray_petal_block","domum_ornamentum:white_paper_extra","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","cfm:acacia_desk","mcwwindows:dark_oak_plank_parapet","mcwbiomesoplenty:pine_waffle_door","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","mcwbiomesoplenty:magic_stable_head_door","additionallanterns:emerald_lantern","mcwbridges:balustrade_andesite_bridge","mcwfurnitures:stripped_acacia_drawer","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:palm_stable_head_door","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","connectedglass:clear_glass_gray2","sfm:manager","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwbiomesoplenty:willow_bamboo_door","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","mcwbiomesoplenty:hellbark_tropical_door","mcwbiomesoplenty:magic_glass_door","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","connectedglass:tinted_borderless_glass_cyan2","botania:livingwood_twig","mcwbiomesoplenty:willow_window","ae2:tools/network_tool","rftoolsutility:fluidplus_module","deeperdarker:bloom_boat","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","mcwroofs:pink_terracotta_upper_steep_roof","dyenamics:rose_stained_glass","sophisticatedstorage:chipped/carpenters_table_upgrade","pneumaticcraft:vortex_cannon","ad_astra:lime_industrial_lamp","connectedglass:clear_glass_yellow_pane2","securitycraft:reinforced_jungle_fence","ae2:network/cables/dense_covered_red","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","mcwroofs:acacia_steep_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwwindows:stripped_cherry_pane_window","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","mcwbiomesoplenty:jacaranda_bark_glass_door","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwbiomesoplenty:jacaranda_japanese2_door","create:crafting/materials/red_sand_paper","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","mcwbiomesoplenty:hellbark_pyramid_gate","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","expatternprovider:pre_bus","mcwfurnitures:stripped_acacia_desk","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","ae2:network/cables/dense_smart_fluix_clean","minecraft:dye_orange_bed","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","mcwbiomesoplenty:willow_tropical_door","simplylight:illuminant_light_gray_block_dyed","mcwdoors:acacia_barn_door","mcwbiomesoplenty:jacaranda_stable_door","mcwbiomesoplenty:magic_horse_fence","alchemistry:reactor_output","sophisticatedstorage:basic_to_copper_tier_upgrade","ae2:network/crafting/molecular_assembler","littlelogistics:vessel_charger","mcwbiomesoplenty:dead_tropical_door","utilitix:minecart_tinkerer","mcwbiomesoplenty:jacaranda_paper_door","aether:white_cape","ae2:tools/misctools_entropy_manipulator","railcraft:radio_circuit","botania:elementium_hoe","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","chemlib:tantalum_nugget_to_ingot","mcwroofs:white_terracotta_steep_roof","mcwbiomesoplenty:mahogany_plank_window2","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","securitycraft:secret_acacia_sign_item","handcrafted:yellow_plate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","enderio:energetic_alloy_ingot","mcwbiomesoplenty:empyreal_nether_door","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwbiomesoplenty:redwood_japanese2_door","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","enderio:energetic_alloy_grinding_ball","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","utilitarian:snad/drit","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","mcwwindows:cherry_plank_four_window","mcwtrpdoors:acacia_swamp_trapdoor","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","mcwbiomesoplenty:fir_modern_door","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwbiomesoplenty:umbran_tropical_door","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","ad_astra:steel_trapdoor","securitycraft:reinforced_pink_stained_glass_pane_from_dye","mcwbiomesoplenty:mahogany_barn_glass_door","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwroofs:stone_bricks_attic_roof","minecraft:cartography_table","securitycraft:alarm","pneumaticcraft:manometer","ad_astra:launch_pad","mcwbiomesoplenty:dead_japanese2_door","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","pneumaticcraft:compressed_brick_tile","ae2:network/blocks/energy_vibration_chamber","cfm:stripped_acacia_desk_cabinet","pneumaticcraft:wall_lamp_inverted_magenta","mcwbiomesoplenty:palm_paper_door","modularrouters:sender_module_1_alt","botania:elementium_leggings","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","additionallanterns:amethyst_lantern","utilitix:crude_furnace","dyenamics:peach_dye","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mcwwindows:stone_window","botania:petal_light_gray","mcwfurnitures:stripped_acacia_striped_chair","mcwroofs:gray_steep_roof","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","alltheores:iron_ore_hammer","sophisticatedstorage:jukebox_upgrade","gtceu:shapeless/aluminium_wire_wire_gt_single_doubling","cfm:white_picket_gate","mcwfurnitures:stripped_oak_modern_wardrobe","connectedglass:scratched_glass_gray_pane2","mcwfences:railing_nether_brick_wall","mcwroofs:yellow_terracotta_upper_lower_roof","ae2:network/cables/glass_black","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","everythingcopper:copper_ladder","botania:polished_livingrock","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","cfm:black_trampoline","botania:brown_shiny_flower","mcwfurnitures:acacia_cupboard_counter","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","simplylight:illuminant_orange_block_dyed","mcwbiomesoplenty:magic_mystic_door","alchemistry:compactor","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","expatternprovider:oversize_interface_part","mcwwindows:oak_window2","mcwfurnitures:stripped_acacia_bookshelf","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","botania:conversions/white_petal_block_deconstruct","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","bigreactors:reprocessor/collector","mcwbiomesoplenty:empyreal_bamboo_door","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","botania:thunder_sword","mcwdoors:oak_japanese2_door","simplylight:illuminant_black_block_dyed","connectedglass:borderless_glass_yellow_pane2","simplylight:illuminant_gray_block_on_toggle","immersiveengineering:crafting/plate_nickel_hammering","mcwdoors:acacia_bamboo_door","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","mcwwindows:quartz_pane_window","ae2:network/cables/covered_yellow","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","botania:cosmetic_thinking_hand","minecraft:shield","chemlib:lithium_ingot_from_smelting_lithium_dust","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","connectedglass:clear_glass_brown2","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","immersiveengineering:crafting/wirecoil_redstone","cfm:blue_kitchen_drawer","chemlib:magnesium_ingot_from_smelting_magnesium_dust","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_bricks_bridge","alltheores:nickel_rod","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","travelersbackpack:dye_brown_sleeping_bag","mcwbiomesoplenty:dead_mystic_door","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","botania:elementium_helmet","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:jacaranda_window","enderio:black_paper","enderio:vibrant_alloy_ingot","ae2:network/cables/dense_smart_fluix","mcwbiomesoplenty:pine_japanese_door","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","cfm:dye_orange_picket_gate","simplylight:illuminant_brown_block_on_toggle","sophisticatedbackpacks:pickup_upgrade","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwbiomesoplenty:redwood_glass_door","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","appbot:fluix_mana_pool","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","chemlib:lithium_ingot_from_blasting_lithium_dust","mcwfences:modern_andesite_wall","mcwbiomesoplenty:mahogany_western_door","mcwroofs:light_gray_terracotta_attic_roof","mcwbiomesoplenty:umbran_glass_door","ae2:network/cells/view_cell","botania:terra_pick","botania:brewery","mcwbiomesoplenty:maple_beach_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","expatternprovider:caner","mcwbiomesoplenty:magic_four_panel_door","mcwfurnitures:acacia_drawer_counter","botania:monocle","mcwroofs:deepslate_lower_roof","alltheores:invar_ingot_from_dust","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:hellbark_four_panel_door","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","connectedglass:borderless_glass_orange_pane2","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:brown_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","chemlib:gallium_nugget_to_ingot","minecraft:honeycomb_block","ae2:tools/certus_quartz_hoe","cfm:lime_kitchen_drawer","minecraft:yellow_stained_glass_pane_from_glass_pane","sophisticatedstorage:basic_tier_upgrade","mcwbiomesoplenty:redwood_pane_window","ae2:network/cables/glass_light_blue","mcwfurnitures:acacia_striped_chair","mcwdoors:acacia_paper_door","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","mcwbiomesoplenty:mahogany_glass_door","mcwbiomesoplenty:willow_stable_door","minecraft:andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","travelersbackpack:dye_pink_sleeping_bag","mcwbiomesoplenty:palm_barn_door","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","sophisticatedstorage:gold_to_diamond_tier_upgrade","domum_ornamentum:orange_floating_carpet","mcwbiomesoplenty:palm_mystic_door","minecraft:dye_pink_bed","simplylight:illuminant_brown_block_on_dyed","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:maple_japanese2_door","computercraft:wireless_modem_normal","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","ad_astra:zip_gun","croptopia:nougat","ae2:network/cables/covered_magenta","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","ad_astra:space_boots","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","expatternprovider:pattern_modifier","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","mcwlights:soul_bamboo_tiki_torch","mcwdoors:acacia_whispering_door","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwbiomesoplenty:jacaranda_cottage_door","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","ae2:network/cables/covered_gray","cfm:dye_brown_picket_fence","mcwbiomesoplenty:umbran_pyramid_gate","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","allthecompressed:compress/atm_star_block_1x","mcwtrpdoors:acacia_whispering_trapdoor","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","mcwbiomesoplenty:hellbark_glass_door","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","enderio:redstone_filter_base","twigs:bloodstone","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","mcwwindows:warped_planks_window","sophisticatedbackpacks:chipped/loom_table_upgrade","cfm:orange_kitchen_counter","mcwroofs:acacia_roof","mcwpaths:brick_flagstone_stairs","chemlib:potassium_block_to_ingot","pneumaticcraft:spawner_agitator","dyenamics:bed/persimmon_bed","ae2:network/cables/smart_light_gray","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwbiomesoplenty:empyreal_mystic_door","mcwroofs:gutter_base_light_blue","mcwpaths:andesite_crystal_floor_path","botania:cosmetic_polka_dotted_bows","create:crafting/kinetics/orange_seat","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","dyenamics:icy_blue_terracotta","dyenamics:cherenkov_stained_glass","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","chemlib:sodium_ingot_to_nugget","minecraft:yellow_stained_glass","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","enderio:energetic_alloy_nugget","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","sophisticatedbackpacks:stack_upgrade_tier_1","bigreactors:crafting/blutonium_component_to_storage","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","sophisticatedstorage:iron_to_gold_tier_upgrade","mcwwindows:acacia_shutter","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","pneumaticcraft:compressed_iron_ingot_from_block","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbiomesoplenty:willow_pane_window","computercraft:wireless_modem_advanced","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","connectedglass:scratched_glass_gray2","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","ae2:tools/certus_quartz_cutting_knife","handcrafted:bricks_corner_trim","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:light_gray_terracotta","dyenamics:amber_stained_glass","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","botania:corporea_spark","merequester:requester_terminal","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","immersiveengineering:crafting/plate_lead_hammering","allthecompressed:decompress/pulsating_alloy_block_1x","mcwtrpdoors:acacia_paper_trapdoor","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","domum_ornamentum:lime_brick_extra","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","botania:manasteel_shears","mcwbiomesoplenty:pine_paper_door","ae2:network/cables/smart_green","mcwwindows:stone_brick_gothic","botania:elementium_pickaxe","mcwbiomesoplenty:umbran_western_door","mcwdoors:acacia_nether_door","mcwbiomesoplenty:redwood_stable_door","minecraft:glass_bottle","travelersbackpack:dye_gray_sleeping_bag","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","cfm:acacia_upgraded_gate","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","botania:clip","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwdoors:acacia_stable_head_door","twigs:cracked_bricks","supplementaries:slice_map","allthecompressed:decompress/copper_alloy_block_1x","mcwbridges:brick_bridge_pier","mcwdoors:acacia_mystic_door","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","mcwbiomesoplenty:jacaranda_stable_head_door","botania:lens_normal","mcwfences:end_brick_pillar_wall","mcwroofs:bricks_steep_roof","computercraft:monitor_normal","cfm:gray_grill","dyenamics:honey_dye","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwbiomesoplenty:magic_barn_door","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","delightful:knives/elementium_knife","pneumaticcraft:wall_lamp_inverted_cyan","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","modularrouters:modular_router","delightful:knives/nickel_knife","mcwroofs:stone_bricks_roof","cfm:cyan_grill","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","farmersdelight:iron_knife","railcraft:steel_helmet","mcwbiomesoplenty:palm_bamboo_door","ae2:network/cables/smart_black","cfm:stripped_spruce_mail_box","botania:cosmetic_devil_tail","mcwdoors:acacia_japanese_door","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwtrpdoors:acacia_bark_trapdoor","mcwbiomesoplenty:hellbark_waffle_door","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","everythingcopper:copper_shovel","utilitarian:no_soliciting/no_soliciting_banner","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","minecraft:bucket","handcrafted:salmon_trophy","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","minecraft:polished_andesite","mcwbiomesoplenty:palm_swamp_door","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","ae2:network/cables/smart_orange","minecraft:unobtainium_mage_helmet_smithing","cfm:white_picket_fence","chemlib:tantalum_block_to_ingot","supplementaries:bellows","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","everythingcopper:copper_trapdoor","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:acacia_drawer","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","chemlib:gallium_block_to_ingot","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwbiomesoplenty:fir_swamp_door","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","mcwroofs:black_lower_roof","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwbiomesoplenty:dead_stable_head_door","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","minecraft:coal_block","mcwwindows:mangrove_plank_window","botania:cosmetic_tiny_potato_mask","computercraft:skull_dan200","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","securitycraft:secret_birch_hanging_sign","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwbiomesoplenty:maple_bamboo_door","mcwroofs:acacia_upper_lower_roof","ae2:network/parts/energy_acceptor","additionallanterns:gold_chain","mcwroofs:white_attic_roof","ae2:network/cables/smart_light_blue","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwroofs:orange_striped_awning","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","mcwwindows:dark_prismarine_pane_window","mcwbridges:andesite_bridge","pneumaticcraft:pressure_chamber_valve_x1","cfm:dye_pink_picket_fence","mcwbiomesoplenty:redwood_bamboo_door","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","botania:elementium_chestplate","aquaculture:sushi","ae2:network/blocks/interfaces_interface_alt","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","mcwbiomesoplenty:magic_japanese2_door","mcwbiomesoplenty:hellbark_stable_door","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfurnitures:stripped_acacia_table","connectedglass:scratched_glass_lime_pane2","mcwfences:acacia_stockade_fence","allthecompressed:decompress/redstone_alloy_block_1x","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","twilightforest:canopy_boat","botania:quartz_red","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_3","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","bigreactors:turbine/basic/activetap_fe","ae2:network/cables/covered_light_gray","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","gtceu:shapeless/data_orb_nbt","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","mcwdoors:acacia_barn_glass_door","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","minecraft:andesite_wall_from_andesite_stonecutting","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","pneumaticcraft:wall_lamp_purple","mcwwindows:dark_oak_plank_window","minecraft:sticky_piston","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","minecraft:redstone","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:oak_kitchen_sink_light","enderio:ensouled_chassis","mcwbiomesoplenty:empyreal_barn_door","mcwwindows:diorite_pane_window","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","connectedglass:scratched_glass_brown2","supplementaries:flags/flag_red","enderio:resetting_lever_ten_from_prev","mcwtrpdoors:acacia_classic_trapdoor","botania:lens_flare","mcwbiomesoplenty:jacaranda_japanese_door","mcwroofs:light_gray_terracotta_steep_roof","mcwbiomesoplenty:hellbark_nether_door","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","mcwbiomesoplenty:empyreal_tropical_door","pneumaticcraft:stone_base","mcwwindows:deepslate_window","ad_astra:brown_industrial_lamp","mcwbiomesoplenty:empyreal_paper_door","minecraft:gray_terracotta","mcwfurnitures:acacia_modern_chair","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","forbidden_arcanus:dark_nether_star","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","enderio:energetic_alloy_nugget_to_ingot","mcwbiomesoplenty:magic_bark_glass_door","dyenamics:peach_terracotta","botania:livingrock_wall","botania:cosmetic_engineer_goggles","minecraft:fishing_rod","xnet:connector_yellow_dye","mcwwindows:acacia_blinds","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwdoors:acacia_waffle_door","botania:blue_pavement","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","connectedglass:scratched_glass_yellow2","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","pneumaticcraft:seismic_sensor","aether:diamond_pickaxe_repairing","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","create:crafting/kinetics/vertical_gearbox","mcwlights:double_street_lamp","minecraft:beacon","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","minecraft:dye_yellow_bed","ad_astra:white_flag","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","mcwroofs:stone_steep_roof","botania:apothecary_default","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","aiotbotania:terra_shovel","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:umbran_bamboo_door","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwroofs:brown_striped_awning","allthetweaks:atm_star_from_atmstar_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","mcwroofs:magenta_terracotta_lower_roof","ad_astra:vent","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","dyenamics:persimmon_wool","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","handcrafted:oak_fancy_bed","minecraft:oak_slab","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:stone_attic_roof","handcrafted:terracotta_plate","mcwroofs:light_blue_terracotta_roof","mcwbiomesoplenty:willow_barn_glass_door","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","domum_ornamentum:brown_brick_extra","minecraft:glass_pane","minecraft:dye_brown_carpet","supplementaries:timber_brace","alltheores:iridium_ingot_from_raw_blasting","allthearcanistgear:unobtainium_robes_smithing","mcwbiomesoplenty:maple_barn_glass_door","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","domum_ornamentum:gray_brick_extra","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","mcwbiomesoplenty:pine_pane_window","enderio:wood_gear","supplementaries:bed_from_feather_block","connectedglass:borderless_glass_gray2","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","ae2:network/blocks/interfaces_interface_part","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","alltheores:lumium_plate","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","mcwbiomesoplenty:mahogany_paper_door","ae2:materials/annihilationcore","ae2:network/cables/dense_smart_from_smart","silentgear:stone_rod","ae2:network/cables/covered_green","ae2:network/cables/dense_covered_green","minecraft:leather_boots","railcraft:steel_spike_maul","pneumaticcraft:classify_filter","immersiveengineering:crafting/stick_steel","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","dyenamics:persimmon_dye","mcwwindows:gray_mosaic_glass","domum_ornamentum:orange_brick_extra","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwbiomesoplenty:redwood_japanese_door","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","chemlib:lithium_block_to_ingot","mcwtrpdoors:acacia_barred_trapdoor","mcwlights:pink_paper_lamp","mcwbiomesoplenty:magic_nether_door","allthecompressed:compress/soularium_block_1x","pneumaticcraft:wall_lamp_inverted_light_blue","immersiveengineering:crafting/wire_aluminum","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","cfm:dye_gray_picket_fence","mcwroofs:stone_upper_lower_roof","bigreactors:turbine/basic/bearing","mcwfurnitures:acacia_bookshelf_drawer","expatternprovider:ebus_out","ae2:network/cables/covered_purple","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","cfm:acacia_park_bench","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","connectedglass:clear_glass_black_pane3","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","littlelogistics:transmitter_component","minecraft:brush","securitycraft:secret_spruce_hanging_sign","aether:book_of_lore","mcwfurnitures:stripped_acacia_triple_drawer","railcraft:steel_pickaxe","cfm:dye_gray_picket_gate","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","mcwfurnitures:acacia_wardrobe","sfm:cable","minecraft:hay_block","mcwbiomesoplenty:palm_japanese_door","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","mcwroofs:white_upper_steep_roof","twilightforest:mangrove_boat","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwbiomesoplenty:jacaranda_barn_glass_door","allthecompressed:compress/soularium_block_2x","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","ae2:network/parts/monitors_storage","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","mcwbiomesoplenty:fir_barn_door","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","botania:petal_light_gray_double","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","bigreactors:blasting/graphite_from_charcoal","mythicbotany:alfsteel_pylon","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","railcraft:goggles","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","mcwbiomesoplenty:willow_japanese2_door","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","ae2:network/cables/covered_black","ae2:network/cables/dense_smart_purple","ae2:tools/certus_quartz_wrench","mcwroofs:oak_planks_upper_steep_roof","minecraft:blaze_powder","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","minecraft:oak_fence","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","mcwbiomesoplenty:empyreal_window2","mcwbiomesoplenty:pine_barn_glass_door","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","enderio:soularium_nugget_to_ingot","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwwindows:granite_louvered_shutter","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","botania:tornado_rod","immersiveengineering:crafting/blueprint_bullets","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","connectedglass:tinted_borderless_glass_yellow2","mcwroofs:pink_terracotta_top_roof","mcwfurnitures:stripped_acacia_lower_bookshelf_drawer","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwbiomesoplenty:fir_mystic_door","mcwfences:dark_oak_horse_fence","railcraft:bronze_gear","securitycraft:secret_warped_hanging_sign","ae2:tools/certus_quartz_spade","mcwbiomesoplenty:fir_beach_door","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","ae2:network/cables/dense_covered_black","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","expatternprovider:epp_alt","occultism:crafting/spirit_campfire","handcrafted:yellow_crockery_combo","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","minecraft:white_stained_glass","mcwbiomesoplenty:umbran_mystic_door","securitycraft:secret_warped_sign_item","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","securitycraft:taser","mcwroofs:stone_bricks_lower_roof","chemlib:tantalum_ingot_to_nugget","supplementaries:soap","mcwbiomesoplenty:empyreal_bark_glass_door","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","aiotbotania:livingrock_hoe","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","cfm:dark_oak_kitchen_drawer","botania:quartz_dark","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","mcwfurnitures:acacia_double_drawer_counter","ae2:network/cables/glass_light_gray","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","ad_astra:fan","connectedglass:scratched_glass_pink_pane2","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","ae2:network/parts/toggle_bus","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","supplementaries:altimeter","ae2:network/cables/dense_smart_gray","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","cfm:red_kitchen_drawer","chemlib:magnesium_ingot_to_nugget","botania:manasteel_shovel","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwroofs:white_steep_roof","botania:cosmetic_puffy_scarf","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwbiomesoplenty:magic_stable_door","enderio:copper_alloy_block","mcwlights:jungle_tiki_torch","twigs:stone_column_stonecutting","immersiveengineering:crafting/plate_aluminum_hammering","delightful:knives/bronze_knife","travelersbackpack:quartz","mcwfurnitures:cabinet_door","supplementaries:candle_holders/candle_holder_orange_dye","mcwbiomesoplenty:mahogany_classic_door","mcwwindows:stripped_spruce_pane_window","sophisticatedbackpacks:tool_swapper_upgrade","minecraft:gold_nugget","bigreactors:energizer/controller","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","minecraft:lime_terracotta","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","securitycraft:display_case","handcrafted:bench","minecraft:gray_stained_glass_pane_from_glass_pane","allthecompressed:compress/andesite_1x","mcwbiomesoplenty:willow_nether_door","mcwwindows:diorite_four_window","minecraft:stone_slab","pneumaticcraft:compressed_bricks","mcwbiomesoplenty:magic_classic_door","mcwpaths:brick_flagstone","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","mcwbiomesoplenty:maple_four_panel_door","minecraft:brick_stairs_from_bricks_stonecutting","travelersbackpack:backpack_tank","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","domum_ornamentum:paper_extra","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","ae2:network/parts/import_bus","croptopia:campfire_molasses","enderio:energetic_alloy_block","mcwdoors:acacia_stable_door","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","mcwbiomesoplenty:pine_four_panel_door","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","mcwroofs:thatch_upper_lower_roof","mcwbiomesoplenty:hellbark_cottage_door","railcraft:water_tank_siding","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","securitycraft:secret_cherry_hanging_sign","silentgear:upgrade_base","xnet:advanced_connector_green","ae2:network/cables/glass_pink","ae2:network/cables/smart_cyan","mcwwindows:crimson_stem_four_window","expatternprovider:wireless_tool","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","minecraft:dye_lime_bed","securitycraft:mine","mcwbiomesoplenty:jacaranda_glass_door","pneumaticcraft:compressed_stone_slab","mcwfurnitures:stripped_acacia_chair","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","mcwbiomesoplenty:maple_classic_door","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","allthecompressed:compress/copper_alloy_block_2x","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwroofs:bricks_top_roof","mcwbiomesoplenty:maple_stockade_fence","botania:starfield","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","connectedglass:tinted_borderless_glass_pink2","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","mcwfences:modern_mud_brick_wall","mcwbridges:acacia_bridge_pier","minecraft:stone_pressure_plate","chemlib:lithium_nugget_to_ingot","mcwbiomesoplenty:hellbark_classic_door","allthecompressed:compress/copper_alloy_block_1x","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","botania:yellow_pavement","connectedglass:borderless_glass_orange2","mcwbiomesoplenty:dead_glass_door","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","botania:cosmetic_alien_antenna","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","everythingcopper:copper_helmet","create:crafting/kinetics/yellow_seat","pneumaticcraft:logistics_core","mcwbiomesoplenty:willow_modern_door","mcwpaths:andesite_windmill_weave_path","mcwbiomesoplenty:palm_glass_door","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:bread","minecraft:mangrove_boat","mcwroofs:green_terracotta_steep_roof","mcwbiomesoplenty:redwood_western_door","mcwdoors:garage_silver_door","comforts:hammock_white","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","mcwfurnitures:stripped_oak_cupboard_counter","ad_astra:orange_industrial_lamp","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwbiomesoplenty:dead_classic_door","mcwwindows:dark_oak_curtain_rod","securitycraft:secret_cherry_sign_item","botania:horn_grass","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","botania:spectral_platform","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwbiomesoplenty:umbran_swamp_door","mcwwindows:birch_pane_window","mcwbiomesoplenty:redwood_paper_door","mcwbiomesoplenty:willow_pyramid_gate","mcwbiomesoplenty:mahogany_bark_glass_door","mcwfences:iron_cheval_de_frise","mcwfurnitures:acacia_desk","minecraft:iron_shovel","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","ae2:smelting/smooth_sky_stone_block","mcwwindows:stone_brick_arrow_slit","mcwbiomesoplenty:mahogany_cottage_door","domum_ornamentum:white_floating_carpet","mcwbiomesoplenty:umbran_waffle_door","croptopia:ham_sandwich","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","ad_astra:space_suit","securitycraft:disguise_module","mcwbiomesoplenty:willow_swamp_door","mcwwindows:deepslate_four_window","mcwdoors:acacia_glass_door","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","mcwwindows:dark_oak_louvered_shutter","mcwbiomesoplenty:fir_stable_head_door","cfm:light_blue_picket_fence","mcwbiomesoplenty:redwood_barn_door","mcwtrpdoors:acacia_four_panel_trapdoor","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","enderio:electromagnet","deeperdarker:echo_boat","mcwbiomesoplenty:hellbark_swamp_door","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mythicbotany:ice_ring","mcwfences:birch_picket_fence","mcwbiomesoplenty:palm_modern_door","enderio:basic_fluid_filter","rftoolsutility:counter_module","botania:lens_redirect","minecraft:oak_door","biomesoplenty:jacaranda_boat","mcwbiomesoplenty:maple_cottage_door","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","mcwbiomesoplenty:empyreal_modern_door","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:black_roof_block","botania:elementium_shovel","connectedglass:tinted_borderless_glass_lime2","advancedperipherals:peripheral_casing","connectedglass:clear_glass_cyan_pane2","mythicbotany:alfsteel_template","mythicbotany:fire_ring","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","modularrouters:sender_module_2_x4","ae2:network/cables/dense_smart_white","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","mcwfurnitures:stripped_acacia_large_drawer","mcwroofs:red_striped_awning","botania:cosmetic_devil_horns","cfm:oak_cabinet","enderio:soularium_block","cfm:stripped_oak_kitchen_sink_light","cfm:spruce_kitchen_drawer","handcrafted:oak_corner_trim","modularrouters:dropper_module","mcwbiomesoplenty:magic_highley_gate","computercraft:wired_modem","simplemagnets:basic_demagnetization_coil","mcwwindows:pink_curtain","dyenamics:mint_terracotta","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","delightful:knives/refined_obsidian_knife","expatternprovider:ei_alt","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","supplementaries:wrench","minecraft:bow","mcwroofs:blue_terracotta_upper_steep_roof","deepresonance:radiation_suit_boots","botania:cobweb","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","cfm:acacia_kitchen_counter","dyenamics:lavender_wool","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","chemlib:periodic_table","minecraft:andesite_stairs","delightful:knives/aluminum_knife","expatternprovider:wireless_ex_pat","bigreactors:energizer/chargingport_fe","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","botania:cosmetic_clock_eye","dyenamics:amber_dye","enderio:experience_rod","cfm:purple_kitchen_sink","cfm:black_cooler","supplementaries:sconce","silentgear:rough_rod","minecraft:unobtainium_spell_book_smithing","ae2:network/parts/storage_bus","minecraft:skull_banner_pattern","mcwwindows:jungle_plank_parapet","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","chemlib:potassium_ingot_from_smelting_potassium_dust","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:acacia_large_drawer","connectedglass:borderless_glass_brown2","mcwfurnitures:cabinet_drawer","domum_ornamentum:pink_brick_extra","mcwbiomesoplenty:jacaranda_plank_window2","sophisticatedbackpacks:feeding_upgrade","mcwbiomesoplenty:pine_stable_door","minecraft:granite","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","cfm:dye_cyan_picket_fence","cfm:stripped_acacia_desk","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","botania:manaweave_cloth","mcwbiomesoplenty:magic_barn_glass_door","mcwbiomesoplenty:redwood_mystic_door","enderio:enderios","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwbiomesoplenty:fir_bamboo_door","mcwroofs:andesite_roof","botania:black_pavement","travelersbackpack:blaze","mcwbiomesoplenty:magic_swamp_door","minecraft:allthemodium_mage_helmet_smithing","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","minecraft:dye_yellow_carpet","ae2:shaped/stairs/sky_stone_block","botania:quartz_blaze","ae2:network/cables/glass_yellow","dyenamics:bubblegum_wool","ae2:network/cables/covered_white","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","mcwfurnitures:stripped_acacia_wardrobe","dyenamics:rose_terracotta","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","alltheores:signalum_plate","mcwbiomesoplenty:dead_cottage_door","expatternprovider:oversize_interface","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","mcwfurnitures:stripped_acacia_modern_desk","dyenamics:bed/lavender_bed","cfm:lime_kitchen_counter","minecraft:dye_orange_carpet","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","securitycraft:speed_module","cfm:stripped_oak_kitchen_sink_dark","mcwdoors:garage_black_door","minecraft:dye_cyan_bed","botania:mana_ring_greater","blue_skies:glowing_poison_stone","mcwroofs:brown_terracotta_steep_roof","mcwdoors:oak_barn_door","botania:alchemy_catalyst","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","enderio:pulsating_alloy_block","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","botania:lava_pendant","minecolonies:chainmailleggings","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","minecraft:smooth_stone","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:acacia_upgraded_fence","ae2:network/cables/dense_smart_light_gray","cfm:oak_bedside_cabinet","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","botania:conversions/elementium_block_deconstruct","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","minecraft:ender_eye","botania:cosmetic_kamui_eye","enderio:soularium_nugget","ad_astra:small_pink_industrial_lamp","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","mcwtrpdoors:acacia_ranch_trapdoor","mcwlights:cross_lantern","mcwbiomesoplenty:pine_barn_door","mcwwindows:bricks_four_window","railcraft:receiver_circuit","securitycraft:reinforced_packed_mud","chemlib:magnesium_block_to_ingot","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","minecraft:pink_stained_glass_pane_from_glass_pane","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","pneumaticcraft:refinery_output","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","handcrafted:yellow_bowl","ae2:network/blocks/io_condenser","sophisticatedbackpacks:smoking_upgrade","ae2:network/cables/covered_pink","railcraft:steel_tank_wall","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","mcwpaths:brick_clover_paving","securitycraft:trophy_system","botania:elementium_axe","pneumaticcraft:reinforced_brick_from_slab","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","mcwbiomesoplenty:jacaranda_four_panel_door","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","connectedglass:scratched_glass_brown_pane2","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","advanced_ae:quantumstorage128","farmersdelight:pie_crust","rftoolsbuilder:vehicle_card","mcwlights:warped_tiki_torch","twigs:dandelion_paper_lantern","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","pneumaticcraft:wall_lamp_orange","mcwwindows:black_curtain","dyenamics:wine_stained_glass","botania:pixie_ring","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","botania:drum_gathering","mcwwindows:jungle_plank_window2","ad_astra:space_pants","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","ae2:network/cables/dense_smart_black","mcwpaths:andesite_clover_paving","computercraft:turtle_advanced_overlays/turtle_trans_overlay","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","mcwbiomesoplenty:mahogany_japanese_door","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","utilitarian:utility/acacia_logs_to_pressure_plates","mcwbiomesoplenty:umbran_four_panel_door","enderio:resetting_lever_five_inv","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","handcrafted:yellow_cup","mcwbiomesoplenty:pine_glass_door","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","ae2:network/cables/smart_blue","mcwbiomesoplenty:redwood_beach_door","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","botania:turntable","ae2:network/cells/item_storage_components_cell_1k_part","mcwbiomesoplenty:dead_highley_gate","ad_astra:steel_tank","mcwroofs:oak_planks_top_roof","biomesoplenty:hellbark_boat","minecraft:netherite_scrap","supplementaries:daub","railcraft:steel_chestplate","securitycraft:secret_oak_hanging_sign","mcwfences:oak_hedge","enderio:fluid_tank","mcwroofs:oak_planks_roof","botania:petal_white","ae2:decorative/quartz_block","enderio:redstone_alloy_block","ae2:network/cables/dense_smart_orange","mcwbiomesoplenty:magic_cottage_door","bigreactors:energizer/casing","mcwpaths:stone_flagstone","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwbiomesoplenty:magic_japanese_door","cfm:birch_mail_box","minecraft:lead","immersiveengineering:crafting/capacitor_hv","mcwdoors:acacia_bark_glass_door","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deeperdarker:reinforced_echo_shard","cfm:dye_pink_picket_gate","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","cfm:dye_yellow_picket_fence","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","minecraft:acacia_wood","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","mcwbiomesoplenty:maple_tropical_door","pneumaticcraft:logistics_configurator","mcwbiomesoplenty:mahogany_beach_door","mcwtrpdoors:print_tropical","pneumaticcraft:logistics_frame_default_storage_self","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:3967,warning_level:0}}