{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:1b,Amplifier:4b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:388,HiddenEffect:{Ambient:0b,Amplifier:2b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:0,Id:11,ShowIcon:1b,ShowParticles:1b,"forge:id":"minecraft:resistance"},Id:11,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:resistance"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:388,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:1846,Id:22,ShowIcon:1b,ShowParticles:1b,"forge:id":"minecraft:absorption"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:100,Id:241,ShowIcon:1b,ShowParticles:1b,"forge:id":"mahoutsukai:bleeding"}],Air:300s,Attributes:[{Base:1.0d,Name:"attributeslib:arrow_damage"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:1.5d,Name:"attributeslib:crit_damage"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:1.0d,Name:"eidolon:magic_power"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"attributeslib:armor_pierce"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:0.0d,Name:"attributeslib:elytra_flight"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"attributeslib:current_hp_damage"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:20.0d,Modifiers:[{Amount:10.0d,Name:"artifacts:crystal_heart_health_bonus",Operation:0,UUID:[I;-**********,-**********,-**********,-**********]},{Amount:4.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"attributeslib:overheal"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"attributeslib:healing_received"},{Base:0.0d,Name:"attributeslib:cold_damage"},{Base:1.0d,Name:"attributeslib:arrow_velocity"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.05d,Name:"attributeslib:crit_chance"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"minecraft:generic.attack_knockback"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:100.0d,Name:"irons_spellbooks:max_mana"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:the_end",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:175.0d,glyph:0,max:175},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;**********,-**********,-**********,-492283349],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:crystal_heart"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,ForgeCaps:{Parent:{energy:{energy:64000000},module_host:{modules:[{id:"draconicevolution:draconic_energy",x:0b,y:0b},{id:"draconicevolution:draconic_large_shield_capacity",x:6b,y:0b},{id:"draconicevolution:draconic_large_shield_capacity",x:4b,y:0b},{id:"draconicevolution:draconic_large_shield_capacity",x:2b,y:0b},{id:"draconicevolution:draconic_large_shield_capacity",x:6b,y:2b},{id:"draconicevolution:draconic_energy",x:1b,y:0b},{id:"draconicevolution:draconic_energy",x:1b,y:1b},{id:"draconicevolution:draconic_energy",x:0b,y:1b},{id:"draconicevolution:draconic_shield_recovery",x:6b,y:4b},{id:"draconicevolution:draconic_shield_recovery",x:7b,y:4b},{id:"draconicevolution:draconic_shield_recovery",x:7b,y:5b},{id:"draconicevolution:draconic_shield_recovery",x:6b,y:5b},{charge:519,id:"draconicevolution:draconic_undying",invul:0,x:0b,y:2b},{anim:1.0f,boost:0.0d,boost_time:0,cap:1120,cooldwn:4700,env_cdwn:0b,hit:1.0f,id:"draconicevolution:draconic_shield_control",max_boost:0.0d,points:0.0d,properties:{shield_mod.always_visible:{hud:1b,uni_name:[I;69243000,260918903,-1212119665,701830924]},shield_mod.enabled:{hud:1b,uni_name:[I;1902711762,1487815759,-1396478276,1436474048]}},visible:1b,x:2b,y:2b},{id:"draconicevolution:draconic_shield_recovery",x:5b,y:4b},{id:"draconicevolution:draconic_shield_recovery",x:5b,y:5b},{id:"draconicevolution:draconic_shield_recovery",x:4b,y:4b},{id:"draconicevolution:draconic_shield_recovery",x:4b,y:5b},{id:"draconicevolution:draconic_shield_recovery",x:3b,y:4b},{id:"draconicevolution:draconic_shield_recovery",x:3b,y:5b},{id:"draconicevolution:draconic_shield_recovery",x:2b,y:4b},{id:"draconicevolution:draconic_shield_recovery",x:2b,y:5b},{charge:326,id:"draconicevolution:draconic_undying",invul:0,x:0b,y:4b}],properties:{},provider_id:[I;-**********,-687127882,-**********,135167183]}}},Slot:0,id:"draconicevolution:draconic_chestpiece",tag:{}}],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"aether:diamond_gloves",tag:{Damage:321,Enchantments:[{id:"enderio:xp_boost",lvl:2s},{id:"tombstone:soulbound",lvl:1s}]}},{Count:1b,ForgeCaps:{Parent:{energy:{energy:117885276},module_host:{modules:[{id:"draconicevolution:draconic_energy",x:4b,y:0b},{id:"draconicevolution:draconic_energy",x:4b,y:1b},{id:"draconicevolution:draconic_energy",x:4b,y:2b},{id:"draconicevolution:draconic_energy",x:4b,y:3b},{id:"draconicevolution:draconic_energy",x:0b,y:4b},{id:"draconicevolution:draconic_energy",x:1b,y:4b},{id:"draconicevolution:draconic_energy",x:2b,y:4b},{id:"draconicevolution:draconic_energy",x:3b,y:4b},{id:"draconicevolution:draconic_energy",x:4b,y:4b},{core_x:-77,core_y:268,core_z:-84,dim:"allthemodium:mining",id:"draconicevolution:draconic_energy_link",link_charge:16000000L,link_id:[I;-**********,-399620585,-**********,-**********],properties:{energy_link_mod.enabled:{hud:1b,uni_name:[I;467734795,-78690879,-**********,-44441922]}},x:0b,y:0b}],properties:{charge_armor:{hud:1b,value:1b},charge_curios:{hud:1b,value:1b},charge_held_item:{hud:1b,value:1b},charge_hot_bar:{hud:1b,value:1b},charge_main:{hud:1b,value:1b}},provider_id:[I;**********,**********,-**********,7232736]}}},Slot:1,id:"draconicevolution:draconic_capacitor",tag:{}}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[{Count:1b,Slot:2,id:"botania:loki_ring",tag:{soulbindUUID:"0a22eecf-57bf-4275-abaf-265300a523b7",xOrigin:0,yOrigin:-**********,zOrigin:0}},{Count:1b,Slot:3,id:"irons_spellbooks:poisonward_ring"}],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:iron_backpack",tag:{contentsUuid:[I;-1083658357,909133016,-1640073468,-432066028],inventorySlots:54,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:2}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:1,id:"artifacts:vampiric_glove"}],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:59,wirelessNetwork:9},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:170,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:566840},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:194},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:10,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"apotheosis:socketing",ItemStack:{Count:1b,ForgeCaps:{Parent:{energy:{energy:16000000},module_host:{modules:[{id:"draconicevolution:draconic_energy",x:0b,y:0b},{id:"draconicevolution:draconic_proj_damage",x:3b,y:0b},{id:"draconicevolution:draconic_proj_damage",x:3b,y:1b},{id:"draconicevolution:draconic_proj_damage",x:2b,y:1b},{id:"draconicevolution:draconic_proj_velocity",x:4b,y:0b},{id:"draconicevolution:draconic_proj_velocity",x:5b,y:0b},{id:"draconicevolution:draconic_proj_velocity",x:4b,y:1b},{id:"draconicevolution:draconic_proj_velocity",x:5b,y:1b},{id:"draconicevolution:draconic_proj_velocity",x:4b,y:2b},{id:"draconicevolution:draconic_proj_velocity",x:5b,y:2b},{id:"draconicevolution:draconic_proj_velocity",x:4b,y:3b},{id:"draconicevolution:draconic_proj_velocity",x:5b,y:3b},{id:"draconicevolution:draconic_proj_grav_comp",x:0b,y:1b},{id:"draconicevolution:draconic_proj_grav_comp",x:0b,y:2b},{id:"draconicevolution:draconic_proj_grav_comp",x:0b,y:3b},{id:"draconicevolution:wyvern_auto_fire",properties:{auto_fire_mod.enable:{hud:1b,uni_name:[I;**********,**********,-**********,-**********]}},x:2b,y:2b},{id:"draconicevolution:draconic_proj_grav_comp",x:0b,y:4b},{id:"draconicevolution:draconic_speed",x:2b,y:0b},{id:"draconicevolution:draconic_speed",x:1b,y:0b},{id:"draconicevolution:draconic_proj_accuracy",x:2b,y:3b},{id:"draconicevolution:draconic_proj_accuracy",x:2b,y:4b},{id:"draconicevolution:draconic_proj_accuracy",x:4b,y:4b}],properties:{},provider_id:[I;**********,-**********,-**********,**********]}}},id:"draconicevolution:draconic_bow",tag:{Enchantments:[{id:"minecraft:punch",lvl:4s},{id:"apotheosis:endless_quiver",lvl:1s},{id:"tombstone:sanctified",lvl:11s},{id:"ensorcellation:quick_draw",lvl:4s},{id:"minecraft:power",lvl:8s}],RepairCost:1,affix_data:{affixes:{"apotheosis:ranged/attribute/agile":0.37662238f,"apotheosis:ranged/attribute/elven":0.62962854f,"apotheosis:ranged/attribute/streamlined":0.3371843f,"apotheosis:ranged/attribute/windswept":0.6096249f,"apotheosis:ranged/mob_effect/acidic":0.9959678f,"apotheosis:ranged/mob_effect/satanic":1.0f,"apotheosis:ranged/special/spectral":0.7544724f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:mythic"},gem:"apotheosis:core/combatant"}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}}],name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:ranged/attribute/elven"},"",{"translate":"affix.apotheosis:ranged/attribute/streamlined.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-1856398320,-2101460685,-1410133494,33066953]]}}}}],SelectedRecipe:"apotheosis:socketing"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:161,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["croptopia:toast","minecraft:enchanted_golden_apple","croptopia:buttered_toast","minecraft:bread","allthemodium:ancient_soulberries","minecraft:golden_apple","minecraft:cooked_beef"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:550s,knowledge:26,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{CreateNetheriteDivingBits:3b,MythicBotanyPlayerInfo:{},PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:overworld",FromPos:-824633905086L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:274877898813L,UID:[I;1754571561,-*********,-2090918463,*********]},{FromDim:"minecraft:the_nether",FromPos:274877898813L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-1354870991,-*********,-1630041127,*********]},{FromDim:"minecraft:overworld",FromPos:96207268925505L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;-881292501,-982302262,-1975081182,292275908]},{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;1685850278,1643399533,-1712149132,610735036]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","ba59ed03-fc91-4f2d-9d54-fa7014c385fa","70e61f07-a350-437a-ae01-15e3ef9a1bab","d1811b83-cc8c-4821-80c6-3e5ce576ae62","6c8881a8-dbdf-465b-bb3e-0c97f47d45a4","4f5f98b0-b346-45b6-83f1-59fad95c9199","db9fa2f7-bedb-4c7b-91c7-5ef71c247526","9ba0302b-f845-40f5-9335-3ed5ac7759bb"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-93,tb_last_ground_location_y:253,tb_last_ground_location_z:-126,tb_last_offhand_item:"minecraft:torch",twilightforest_banished:1b},"aae$downkey":0b,"aae$nokey":1b,"aae$upkey":0b,"apoth.affix_cooldown.apotheosis:armor/mob_effect/blinding":3638384L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/bolstering":1124791L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/bursting":3672101L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/nimble":3672101L,"apoth.affix_cooldown.apotheosis:berserkers_fury":3670436L,"apoth.affix_cooldown.apotheosis:ranged/mob_effect/ensnaring":3669467L,"apoth.affix_cooldown.apotheosis:ranged/mob_effect/grievous":3669467L,"apoth.affix_cooldown.apotheosis:sword/mob_effect/elusive":3655213L,apoth_reforge_seed:**********,"quark:trying_crawl":0b,simplylight_unlocked:2},Health:15.250001f,HurtByTimestamp:566777,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"ars_nouveau:caster_tome",tag:{"ars_nouveau:caster":{current_slot:0,flavor:"Encases your enemies or friends in a tomb of ice. Guaranteed to leave them shocked and confused.",hidden_recipe:"",is_hidden:0b,spell_count:1,spells:{spell0:{name:"Darkfira's Flash Freeze",recipe:{part0:"ars_nouveau:glyph_projectile",part1:"ars_nouveau:burst",part2:"ars_nouveau:glyph_sensitive",part3:"ars_nouveau:glyph_aoe",part4:"ars_nouveau:glyph_aoe",part5:"ars_nouveau:glyph_aoe",part6:"ars_nouveau:glyph_conjure_water",part7:"ars_nouveau:glyph_freeze",part8:"ars_nouveau:glyph_lightning",part9:"ars_nouveau:glyph_amplify",size:10},sound:{pitch:1.9f,soundTag:{id:"ars_nouveau:tempestry_family"},volume:1.69f},spellColor:{b:255,g:255,r:25,type:"ars_nouveau:constant"}}}},display:{Name:'{"italic":true,"color":"dark_purple","text":"Darkfira\'s Flash Freeze"}'}}},{Count:1b,Slot:1b,id:"allthemodium:unobtainium_axe",tag:{Damage:90,Enchantments:[{id:"ensorcellation:frost_aspect",lvl:4s}],RepairCost:3,affix_data:{affixes:{"apotheosis:durable":0.28f,"apotheosis:heavy_weapon/attribute/annihilating":0.84196955f,"apotheosis:heavy_weapon/attribute/berserking":0.2557494f,"apotheosis:heavy_weapon/attribute/forceful":0.36529547f,"apotheosis:heavy_weapon/attribute/giant_slaying":0.82728195f,"apotheosis:heavy_weapon/mob_effect/caustic":0.5218439f,"apotheosis:heavy_weapon/special/cleaving":0.9442972f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:heavy_weapon/attribute/giant_slaying"},"",{"translate":"affix.apotheosis:heavy_weapon/special/cleaving.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;-321207213,-2076753442,-1960818643,-1818716280]]},apoth_rchest:1b}},{Count:1b,Slot:2b,id:"allthemodium:vibranium_sword",tag:{Damage:22,RepairCost:31,affix_data:{affixes:{"apotheosis:durable":0.24000001f,"apotheosis:sword/attribute/glacial":0.88281995f,"apotheosis:sword/attribute/intricate":0.8808319f,"apotheosis:sword/mob_effect/elusive":0.697973f,"apotheosis:telepathic":0.98687714f,"irons_spellbooks:sword/attribute/mana_regen":0.37873155f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:epic"},gem:"apotheosis:core/samurai",uuids:[[I;-2004229432,1147751760,-1691130949,2119611682]]}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],name:'{"color":"#BB00BB","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:sword/mob_effect/elusive"},"",{"translate":"affix.apotheosis:sword/attribute/glacial.suffix"}]}',rarity:"apotheosis:epic",sockets:3,uuids:[[I;-1093363139,-490385964,-2080973597,-1167178618]]}}},{Count:1b,ForgeCaps:{Parent:{energy:{energy:16000000},module_host:{modules:[{id:"draconicevolution:draconic_energy",x:0b,y:0b},{id:"draconicevolution:draconic_proj_damage",x:3b,y:0b},{id:"draconicevolution:draconic_proj_damage",x:3b,y:1b},{id:"draconicevolution:draconic_proj_damage",x:2b,y:1b},{id:"draconicevolution:draconic_proj_velocity",x:4b,y:0b},{id:"draconicevolution:draconic_proj_velocity",x:5b,y:0b},{id:"draconicevolution:draconic_proj_velocity",x:4b,y:1b},{id:"draconicevolution:draconic_proj_velocity",x:5b,y:1b},{id:"draconicevolution:draconic_proj_velocity",x:4b,y:2b},{id:"draconicevolution:draconic_proj_velocity",x:5b,y:2b},{id:"draconicevolution:draconic_proj_velocity",x:4b,y:3b},{id:"draconicevolution:draconic_proj_velocity",x:5b,y:3b},{id:"draconicevolution:draconic_proj_grav_comp",x:0b,y:1b},{id:"draconicevolution:draconic_proj_grav_comp",x:0b,y:2b},{id:"draconicevolution:draconic_proj_grav_comp",x:0b,y:3b},{id:"draconicevolution:wyvern_auto_fire",properties:{auto_fire_mod.enable:{hud:1b,uni_name:[I;**********,**********,-**********,-**********]}},x:2b,y:2b},{id:"draconicevolution:draconic_proj_grav_comp",x:0b,y:4b},{id:"draconicevolution:draconic_speed",x:2b,y:0b},{id:"draconicevolution:draconic_speed",x:1b,y:0b},{id:"draconicevolution:draconic_proj_accuracy",x:2b,y:3b},{id:"draconicevolution:draconic_proj_accuracy",x:2b,y:4b},{id:"draconicevolution:draconic_proj_accuracy",x:4b,y:4b}],properties:{},provider_id:[I;**********,-**********,-**********,**********]}}},Slot:3b,id:"draconicevolution:draconic_bow",tag:{Enchantments:[{id:"minecraft:punch",lvl:4s},{id:"apotheosis:endless_quiver",lvl:1s},{id:"tombstone:sanctified",lvl:11s},{id:"ensorcellation:quick_draw",lvl:4s},{id:"minecraft:power",lvl:8s},{id:"tombstone:frostbite",lvl:11s},{id:"minecraft:flame",lvl:1s},{id:"evilcraft:poison_tip",lvl:6s},{id:"tombstone:plague_bringer",lvl:11s}],RepairCost:31,affix_data:{affixes:{"apotheosis:ranged/attribute/agile":0.37662238f,"apotheosis:ranged/attribute/elven":0.62962854f,"apotheosis:ranged/attribute/streamlined":0.3371843f,"apotheosis:ranged/attribute/windswept":0.6096249f,"apotheosis:ranged/mob_effect/acidic":0.9959678f,"apotheosis:ranged/mob_effect/satanic":1.0f,"apotheosis:ranged/special/spectral":0.7544724f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:mythic"},gem:"apotheosis:core/combatant",uuids:[[I;592333914,**********,-**********,-80837797]]}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}}],name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:ranged/attribute/elven"},"",{"translate":"affix.apotheosis:ranged/attribute/streamlined.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-1856398320,-2101460685,-1410133494,33066953]]}}},{Count:1b,Slot:4b,id:"minecraft:diamond_pickaxe",tag:{Damage:1550,Enchantments:[{id:"enderio:xp_boost",lvl:2s}]}},{Count:1b,Slot:5b,id:"ae2wtlib:wireless_universal_terminal",tag:{accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},blankPattern:[{Count:6b,Slot:0,id:"ae2:blank_pattern"}],crafting:1b,craftingGrid:[{Count:1b,Slot:0,id:"apotheosis:gem_dust"},{Count:1b,Slot:1,id:"apotheosis:gem_fused_slate"},{Count:1b,Slot:2,id:"apotheosis:gem_dust"},{Count:1b,Slot:3,id:"apotheosis:gem_fused_slate"},{Count:1b,Slot:5,id:"apotheosis:gem_fused_slate"},{Count:1b,Slot:6,id:"apotheosis:gem_dust"},{Count:1b,Slot:7,id:"apotheosis:gem_fused_slate"},{Count:1b,Slot:8,id:"apotheosis:gem_dust"}],currentTerminal:"crafting",encodedInputs:[{"#":1L,"#c":"ae2:i",id:"draconicevolution:draconium_ingot"},{"#":1L,"#c":"ae2:i",id:"minecraft:totem_of_undying"},{"#":1L,"#c":"ae2:i",id:"draconicevolution:draconium_ingot"},{"#":1L,"#c":"ae2:i",id:"draconicevolution:draconium_core"},{"#":1L,"#c":"ae2:i",id:"draconicevolution:module_core"},{"#":1L,"#c":"ae2:i",id:"draconicevolution:draconium_core"},{"#":1L,"#c":"ae2:i",id:"draconicevolution:draconium_ingot"},{"#":1L,"#c":"ae2:i",id:"draconicevolution:item_wyvern_shield_capacity"},{"#":1L,"#c":"ae2:i",id:"draconicevolution:draconium_ingot"}],filter_type:"ALL",internalCurrentPower:4800000.0d,internalMaxPower:4800000.0d,mode:"CRAFTING",pattern_access:1b,pattern_encoding:1b,show_pattern_providers:"VISIBLE",sort_by:"NAME",sort_direction:"ASCENDING",substitute:0b,substituteFluids:0b,view_mode:"ALL"}},{Count:2b,Slot:6b,id:"minecraft:iron_bars"},{Count:24b,Slot:8b,id:"minecraft:book"},{Count:64b,Slot:10b,id:"minecraft:arrow"},{Count:1b,Slot:11b,id:"minecraft:diamond_chestplate",tag:{Damage:0,Enchantments:[{id:"ars_nouveau:mana_regen",lvl:5s},{id:"minecraft:fire_protection",lvl:7s},{id:"minecraft:protection",lvl:7s}],affix_data:{affixes:{"apotheosis:armor/attribute/gravitational":0.6325595f,"apotheosis:armor/attribute/spiritual":0.121856034f,"apotheosis:armor/attribute/stalwart":3.7950277E-4f,"apotheosis:armor/attribute/winged":0.640812f,"apotheosis:armor/dmg_reduction/blockading":0.6337386f,"apotheosis:armor/mob_effect/bolstering":0.26494837f,"apotheosis:durable":0.31f},name:'{"italic":false,"color":"#ED7014","translate":"misc.apotheosis.affix_name.four","with":["Sandra\'s",{"translate":"affix.apotheosis:armor/attribute/gravitational"},"",{"translate":"affix.apotheosis:armor/mob_effect/bolstering.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-1431849172,-1212922162,-1454419139,-1171886100]]},apoth_boss:1b,display:{Name:'{"extra":[{"text":"Tunic"}],"text":""}'}}},{Count:1b,Slot:14b,id:"minecraft:shield",tag:{Damage:0,Enchantments:[{id:"ensorcellation:bulwark",lvl:1s},{id:"ensorcellation:soulbound",lvl:1s}],affix_data:{affixes:{"apotheosis:armor/attribute/ironforged":0.9436743f,"apotheosis:durable":0.27f,"apotheosis:shield/attribute/stalwart":0.2706341f,"apotheosis:shield/attribute/steel_touched":0.71129376f,"apotheosis:shield/mob_effect/devilish":0.13343972f,"apotheosis:shield/mob_effect/withering":0.21059287f,"irons_spellbooks:armor/attribute/mana":0.37653798f},name:'{"italic":false,"color":"#BB00BB","translate":"misc.apotheosis.affix_name.four","with":["Bob\'s",{"translate":"affix.apotheosis:shield/mob_effect/devilish"},"",{"translate":"affix.apotheosis:shield/attribute/steel_touched.suffix"}]}',rarity:"apotheosis:epic",sockets:3,uuids:[[I;1608675347,-1438301145,-1575292324,1203672740]]},apoth_boss:1b,display:{Name:'{"translate":"item.minecraft.shield"}'}}},{Count:1b,Slot:16b,id:"apotheosis:mythic_material"},{Count:1b,Slot:19b,id:"minecraft:shield",tag:{Damage:33,Enchantments:[{id:"apotheosis:reflective",lvl:4s},{id:"enderio:xp_boost",lvl:4s},{id:"ensorcellation:phalanx",lvl:5s}],affix_data:{affixes:{"apotheosis:armor/attribute/ironforged":0.2787211f,"apotheosis:durable":0.34f,"apotheosis:shield/attribute/ironforged":0.048652768f,"apotheosis:shield/attribute/steel_touched":0.56336266f,"apotheosis:shield/mob_effect/devilish":0.73441327f},name:'{"italic":false,"color":"#BB00BB","translate":"misc.apotheosis.affix_name.four","with":["Tyra\'s",{"translate":"affix.apotheosis:armor/attribute/ironforged"},"",{"translate":"affix.apotheosis:shield/attribute/ironforged.suffix"}]}',rarity:"apotheosis:epic",sockets:3,uuids:[[I;-1365997160,-1200339036,-2026312816,1575111248]]},apoth_boss:1b,display:{Name:'{"translate":"item.minecraft.shield"}'}}},{Count:1b,Slot:20b,id:"sophisticatedbackpacks:copper_backpack",tag:{contentsUuid:[I;506714437,-1448522250,-1469607483,-1395559575],inventorySlots:45,renderInfo:{upgradeItems:[{Count:1b,id:"minecraft:air",tag:{Charged:0b}}]},upgradeSlots:1}},{Count:23b,Slot:23b,id:"minecraft:sugar"},{Count:23b,Slot:24b,id:"minecraft:glowstone_dust"},{Count:1b,Slot:26b,id:"minecraft:diamond_sword",tag:{Damage:81,Enchantments:[{id:"tombstone:incurable_wounds",lvl:8s},{id:"enderio:xp_boost",lvl:4s},{id:"minecraft:bane_of_arthropods",lvl:7s},{id:"minecraft:fire_aspect",lvl:3s},{id:"apotheosis:scavenger",lvl:1s}]}},{Count:1b,Slot:27b,id:"minecraft:netherite_leggings",tag:{Damage:0,Enchantments:[{id:"minecraft:protection",lvl:4s},{id:"evilcraft:unusing",lvl:1s}],affix_data:{affixes:{"apotheosis:armor/attribute/stalwart":0.42092663f,"apotheosis:armor/attribute/steel_touched":0.24686337f,"apotheosis:armor/attribute/windswept":0.5879799f,"apotheosis:armor/dmg_reduction/blockading":0.5324942f,"apotheosis:durable":0.29000002f},name:'{"color":"#BB00BB","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/stalwart"},"",{"translate":"affix.apotheosis:armor/attribute/steel_touched.suffix"}]}',rarity:"apotheosis:epic",sockets:3}}},{Count:1b,Slot:28b,id:"minecraft:netherite_shovel",tag:{Damage:0,Enchantments:[{id:"minecraft:mending",lvl:1s},{id:"enderio:xp_boost",lvl:4s},{id:"minecraft:unbreaking",lvl:5s}],affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":0.20785719f,"apotheosis:breaker/attribute/experienced":0.16140121f,"apotheosis:breaker/attribute/lucky":0.84199405f,"apotheosis:breaker/mob_effect/swift":0.085805655f,"apotheosis:durable":0.29000002f},name:'{"italic":false,"color":"#BB00BB","translate":"misc.apotheosis.affix_name.four","with":["Sage\'s",{"translate":"affix.apotheosis:breaker/attribute/lucky"},"",{"translate":"affix.apotheosis:breaker/attribute/destructive.suffix"}]}',rarity:"apotheosis:epic",sockets:2,uuids:[[I;-1039086911,854085014,-2105737647,965380792]]},apoth_boss:1b,display:{Name:'{"extra":[{"text":"Digger"}],"text":"Fiery "}'}}},{Count:1b,Slot:31b,id:"botania:king_key",tag:{charging:1b,soulbindUUID:"0a22eecf-57bf-4275-abaf-265300a523b7"}},{Count:1b,Slot:35b,id:"ars_nouveau:caster_tome",tag:{"ars_nouveau:caster":{current_slot:0,flavor:"Fire at a body of water to create a Ice bubble in the depths.",hidden_recipe:"",is_hidden:0b,spell_count:1,spells:{spell0:{name:"Poseidon's Refuge",recipe:{part0:"ars_nouveau:glyph_projectile",part1:"ars_nouveau:glyph_sensitive",part2:"ars_nouveau:glyph_light",part3:"ars_nouveau:burst",part4:"ars_nouveau:glyph_aoe",part5:"ars_nouveau:glyph_aoe",part6:"ars_nouveau:glyph_sensitive",part7:"ars_nouveau:glyph_freeze",part8:"ars_nouveau:glyph_break",part9:"ars_nouveau:glyph_freeze",size:10},sound:{pitch:1.0f,soundTag:{id:"ars_nouveau:tempestry_family"},volume:1.0f},spellColor:{b:255,g:1,r:1,type:"ars_nouveau:constant"}}}},display:{Name:'{"italic":true,"color":"dark_purple","text":"Poseidon\'s Refuge"}'}}},{Count:1b,Slot:100b,id:"allthemodium:allthemodium_boots",tag:{Damage:16,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.7253811f,"apotheosis:armor/attribute/stalwart":0.45866388f,"apotheosis:armor/dmg_reduction/feathery":0.6902606f,"apotheosis:armor/mob_effect/nimble":0.2878012f,"apotheosis:durable":0.31f,"eidolon:wand/attribute/magic_power":0.28125584f,"irons_spellbooks:armor/attribute/spell_resist":0.5976552f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/dmg_reduction/feathery"},"",{"translate":"affix.apotheosis:armor/mob_effect/nimble.suffix"}]}',rarity:"apotheosis:mythic",sockets:2,uuids:[[I;-708857400,-199015045,-1835123022,987916872]]},apoth_rchest:1b}},{Count:1b,Slot:101b,id:"allthemodium:allthemodium_leggings",tag:{Damage:364,Enchantments:[{id:"ars_nouveau:mana_boost",lvl:3s},{id:"ars_nouveau:mana_regen",lvl:3s}]}},{Count:1b,Slot:102b,id:"allthemodium:allthemodium_chestplate",tag:{Damage:18,Enchantments:[{id:"apotheosis:rebounding",lvl:3s},{id:"ensorcellation:magic_protection",lvl:5s},{id:"apotheosis:berserkers_fury",lvl:3s}],RepairCost:1,affix_data:{affixes:{"apotheosis:armor/attribute/blessed":0.890219f,"apotheosis:armor/attribute/winged":0.017365336f,"apotheosis:armor/dmg_reduction/runed":0.36032683f,"apotheosis:armor/mob_effect/bursting":0.57670367f,"apotheosis:durable":0.38f,"eidolon:wand/attribute/magic_power":0.05343193f,"irons_spellbooks:armor/attribute/cooldown":0.4641384f},name:'{"italic":false,"color":"#ED7014","translate":"misc.apotheosis.affix_name.four","with":["Lydia\'s",{"translate":"affix.apotheosis:armor/dmg_reduction/runed"},"",{"translate":"affix.apotheosis:armor/attribute/blessed.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;454127727,185026026,-1311930638,-215652511]]},apoth_boss:1b,display:{Name:'{"extra":[{"text":"Chestplate"}],"text":""}'}}},{Count:1b,Slot:103b,id:"allthemodium:allthemodium_helmet",tag:{Damage:0,Enchantments:[{id:"minecraft:protection",lvl:6s},{id:"ars_nouveau:mana_regen",lvl:5s},{id:"ensorcellation:air_affinity",lvl:1s},{id:"ensorcellation:xp_boost",lvl:5s},{id:"ensorcellation:soulbound",lvl:1s},{id:"tombstone:spectral_conjurer",lvl:8s}],RepairCost:1,affix_data:{affixes:{"apotheosis:armor/attribute/steel_touched":0.6839971f,"apotheosis:armor/dmg_reduction/runed":0.012878537f,"apotheosis:armor/mob_effect/blinding":0.6044639f,"apotheosis:durable":0.39999998f,"eidolon:wand/attribute/magic_power":0.77209896f,"irons_spellbooks:armor/attribute/cooldown":0.069950104f,"irons_spellbooks:armor/attribute/mana":0.86846244f},name:'{"italic":false,"color":"#ED7014","translate":"misc.apotheosis.affix_name.four","with":["Ismael\'s",{"translate":"affix.apotheosis:armor/mob_effect/blinding"},"",{"translate":"affix.apotheosis:armor/attribute/steel_touched.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-1780936306,376983172,-2075037219,1540335201]]},apoth_boss:1b,display:{Name:'{"extra":[{"text":"Sallet"}],"text":""}'}}}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:the_nether",pos:[I;246,92,1]},Motion:[0.2384723504003563d,0.24813599859094576d,0.014869452036914612d],OnGround:0b,PortalCooldown:0,Pos:[-9974.281007818814d,93.25220334025373d,10054.375007918467d],Railways_DataVersion:2,Rotation:[-82.60803f,8.400534f],Score:45782,SelectedItemSlot:3,SleepTimer:0s,Spigot.ticksLived:566829,UUID:[I;170061519,1472152181,-1414584749,10822583],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-2742456836403108L,WorldUUIDLeast:-5840245348581346830L,WorldUUIDMost:8425103023517748583L,XpLevel:147,XpP:0.040524423f,XpSeed:-977969452,XpTotal:45090,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752057033329L,keepLevel:0b,lastKnownName:"loyu-oi",lastPlayed:1752319441647L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.25599796f,foodLevel:15,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwwindows:birch_four_window","biomesoplenty:fir_boat","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwroofs:blue_terracotta_attic_roof","handcrafted:spider_trophy","mcwroofs:lime_terracotta_lower_roof","mcwroofs:red_terracotta_lower_roof","ad_astra:cyan_industrial_lamp","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","xnet:connector_green","advanced_ae:smallappupgrade","create:oak_window","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","botania:missile_rod","sophisticatedbackpacks:void_upgrade","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","dyenamics:maroon_candle","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","utilitix:oak_shulker_boat_with_shell","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","rftoolsutility:screen_link","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","sliceanddice:slicer","minecraft:nether_brick_stairs","supplementaries:bubble_blower","supplementaries:crank","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","mcwroofs:black_steep_roof","allthecompressed:compress/ancient_stone_1x","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","minecraft:pink_terracotta","mcwbiomesoplenty:mahogany_plank_pane_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","nethersdelight:golden_machete","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","mcwlights:chain_lantern","securitycraft:camera_monitor","allthecompressed:compress/quartz_block_1x","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:mojang_banner_pattern","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","cfm:oak_kitchen_counter","minecraft:golden_hoe","minecraft:fire_charge","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:mana_distributor","allthemodium:smithing/vibranium_axe_smithing","biomesoplenty:mahogany_boat","aquaculture:note_hook","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","simplylight:illuminant_cyan_block_on_dyed","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:lodestone","occultism:crafting/magic_lamp_empty","mcwlights:black_lamp","minecraft:purpur_block","minecraft:polished_blackstone_slab","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:nether_brick_wall","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","botania:incense_stick","mcwbiomesoplenty:pine_plank_window2","mcwwindows:warped_louvered_shutter","blue_skies:glowing_nature_stone","xnet:connector_routing","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","mcwroofs:green_terracotta_attic_roof","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","immersiveengineering:crafting/shield","utilitix:directional_highspeed_rail","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","dyenamics:ultramarine_candle","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","cfm:stripped_crimson_kitchen_sink_dark","mcwroofs:prismarine_brick_top_roof","cfm:warped_mail_box","cfm:green_grill","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","dyenamics:fluorescent_candle","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","mcwroofs:oak_roof","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","mcwfences:nether_brick_pillar_wall","mcwbiomesoplenty:willow_curved_gate","delightful:knives/osmium_knife","mcwbridges:balustrade_prismarine_bricks_bridge","dyenamics:lavender_candle","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","mcwfences:blackstone_brick_railing_gate","sophisticatedbackpacks:filter_upgrade","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","mcwroofs:black_terracotta_roof","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","cfm:green_kitchen_sink","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","mcwpaths:cobblestone_basket_weave_paving","allthearcanistgear:unobtainium_hat_smithing","create:crafting/materials/andesite_alloy","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","mcwfurnitures:oak_double_drawer_counter","cfm:blue_grill","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","minecraft:quartz_slab_from_stonecutting","mcwroofs:nether_bricks_upper_lower_roof","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","appbot:mana_cell_housing","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","mcwbiomesoplenty:fir_hedge","minecraft:netherite_sword_smithing","croptopia:shaped_toast_sandwich","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","mcwdoors:metal_hospital_door","railcraft:signal_circuit","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","minecraft:prismarine_brick_slab_from_prismarine_stonecutting","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","croptopia:buttered_toast","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","dyenamics:ultramarine_terracotta","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","mekanism:transmitter/mechanical_pipe/basic","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","minecraft:purpur_stairs_from_purpur_block_stonecutting","travelersbackpack:bee","additionallanterns:prismarine_chain","nethersdelight:hoglin_sirloin_from_campfire_cooking","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwroofs:dark_prismarine_steep_roof","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","handcrafted:terracotta_wide_pot","mcwroofs:blackstone_lower_roof","reliquary:uncrafting/glass_bottle","ae2:tools/portable_item_cell_256k","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwroofs:purple_terracotta_upper_lower_roof","biomesoplenty:orange_dye_from_burning_blossom","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","handcrafted:wood_cup","twilightforest:mining_boat","handcrafted:terracotta_cup","additionallanterns:normal_nether_bricks_chain","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","mcwbiomesoplenty:dead_picket_fence","mcwbiomesoplenty:hellbark_window2","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","cfm:stripped_oak_chair","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","mcwwindows:oak_shutter","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwroofs:nether_bricks_top_roof","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","mcwroofs:nether_bricks_lower_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","aether:obsidian_from_bucket_freezing","minecraft:blue_dye","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","mcwwindows:crimson_planks_four_window","minecraft:piston","mcwlights:white_lamp","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","botania:mana_bomb","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","handcrafted:oak_shelf","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwfences:crimson_curved_gate","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","mcwbridges:end_stone_bricks_bridge_pier","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","mcwroofs:red_terracotta_upper_steep_roof","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwbiomesoplenty:rainbow_birch_hedge","botania:spawner_mover","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","xnet:antenna","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","dyenamics:persimmon_candle","cfm:stripped_warped_kitchen_drawer","mcwroofs:prismarine_brick_upper_steep_roof","mcwroofs:cobblestone_roof","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwfences:dark_oak_hedge","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","mcwroofs:white_terracotta_roof","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","cfm:light_gray_grill","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:hellbark_hedge","allthemodium:allthemodium_block","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","mcwroofs:light_gray_top_roof","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","minecraft:crafting_table","dyenamics:cherenkov_dye","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","railcraft:steel_sword","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","sophisticatedstorage:stack_upgrade_tier_1","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwwindows:spruce_curtain_rod","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","aether:netherite_helmet_repairing","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","mcwfences:diorite_grass_topped_wall","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwwindows:stone_window2","minecraft:oak_button","mcwroofs:prismarine_brick_attic_roof","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","croptopia:fruit_smoothie","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","simplylight:illuminant_yellow_block_on_toggle","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","minecraft:item_frame","itemcollectors:basic_collector","minecraft:loom","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","botania:exchange_rod","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","botania:crafting_halo","mcwwindows:acacia_plank_four_window","dyenamics:banner/rose_banner","botania:gaia_spreader","mcwroofs:black_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","travelersbackpack:horse","aether:diamond_gloves_repairing","cfm:brown_cooler","mcwfences:diorite_pillar_wall","mcwwindows:dark_prismarine_parapet","rftoolsutility:inventoryplus_module","dyenamics:amber_candle","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","undergarden:shard_torch","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","paraglider:paraglider","aether:golden_leggings_repairing","sophisticatedstorage:shulker_box","utilitix:anvil_cart","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","undergarden:blast_catalyst","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","sophisticatedbackpacks:compacting_upgrade","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","mcwroofs:dark_prismarine_roof","minecraft:composter","minecraft:jukebox","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwwindows:blackstone_pane_window","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","minecraft:end_stone_bricks","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","botania:quartz_sunny","create:framed_glass_from_glass_colorless_stonecutting","botania:flighttiara_0","railcraft:signal_lamp","reliquary:uncrafting/sugar","tombstone:bone_needle","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","sophisticatedstorage:decoration_table","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","utilitarian:utility/glow_ink_sac","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","minecraft:gold_block","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","travelersbackpack:gold","ae2:network/cables/dense_smart_cyan","mcwfences:warped_curved_gate","connectedglass:scratched_glass_cyan2","mcwdoors:oak_stable_door","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwroofs:light_gray_terracotta_roof","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:lime_terracotta_steep_roof","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","allthemodium:unobtainium_rod","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","mcwfurnitures:oak_bookshelf","mcwwindows:oak_plank_window","nethersdelight:hoglin_sirloin","mcwwindows:oak_log_parapet","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","mcwfences:acacia_hedge","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","additionallanterns:purpur_chain","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","mcwroofs:orange_terracotta_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","rftoolsbuilder:green_shield_template_block","allthemodium:vibranium_plate","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","mcwroofs:black_roof","securitycraft:reinforced_gray_stained_glass_pane_from_glass","alltheores:gold_plate","mcwbiomesoplenty:maple_four_window","cfm:blue_kitchen_counter","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwbridges:end_stone_bricks_bridge_stair","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","supplementaries:flags/flag_cyan","botania:diva_charm","cfm:pink_grill","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","securitycraft:reinforced_piston","cfm:purple_kitchen_counter","securitycraft:reinforced_yellow_stained_glass","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","twilightforest:wood/oak_banister","alltheores:enderium_plate","mcwwindows:purple_mosaic_glass","allthecompressed:compress/gold_block_1x","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:end_stone_brick_stairs","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwroofs:dark_prismarine_upper_lower_roof","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","domum_ornamentum:gray_floating_carpet","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","cfm:oak_upgraded_fence","botania:super_cloud_pendant","railcraft:steel_gear","sophisticatedstorage:crafting_upgrade","minecraft:prismarine_bricks","allthemodium:smithing/allthemodium_axe_smithing","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","connectedglass:borderless_glass_cyan2","mcwbiomesoplenty:jacaranda_wired_fence","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:dead_plank_window2","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","reliquary:glowing_water_from_potion_vial","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:cyan_stained_glass","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","pylons:potion_filter","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","cfm:stripped_mangrove_kitchen_sink_light","handcrafted:quartz_corner_trim","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","simplylight:illuminant_red_block_on_dyed","dyenamics:fluorescent_stained_glass","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","mcwpaths:stone_crystal_floor_path","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","cfm:lime_grill","mcwbiomesoplenty:hellbark_plank_window2","mcwroofs:lime_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass","handcrafted:golden_medium_pot","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwroofs:gray_terracotta_upper_steep_roof","sfm:fancy_to_cable","aquaculture:heavy_hook","minecraft:yellow_terracotta","mcwwindows:acacia_window","mcwroofs:green_terracotta_roof","mcwbridges:prismarine_bricks_bridge","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwwindows:stripped_crimson_stem_four_window","utilitix:spruce_shulker_boat_with_shell","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwbridges:oak_bridge_pier","cfm:magenta_kitchen_counter","cfm:spruce_mail_box","minecraft:barrel","mcwlights:orange_lamp","mcwwindows:dark_oak_pane_window","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","botania:laputa_shard","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","allthearcanistgear:vibranium_boots_smithing","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","minecraft:end_stone_brick_slab","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","mcwpaths:gravel_path_block","supplementaries:lapis_bricks","mcwbridges:glass_bridge_pier","mcwbridges:nether_bricks_bridge","minecraft:cauldron","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","mcwroofs:brown_terracotta_lower_roof","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","tombstone:impregnated_diamond","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","cfm:cyan_kitchen_sink","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","mcwbiomesoplenty:umbran_window2","alltheores:enderium_rod","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwlights:sea_lantern_slab","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwroofs:oak_upper_lower_roof","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwroofs:gutter_base_red","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","botania:holy_cloak","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","sophisticatedbackpacks:chipped/carpenters_table_upgrade","mcwbiomesoplenty:stripped_maple_log_window2","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","sophisticatedstorage:oak_limited_barrel_1","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","dyenamics:navy_terracotta","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","minecraft:light_blue_terracotta","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","minecraft:cyan_candle","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","cfm:cyan_cooler","railcraft:silver_gear","mcwwindows:prismarine_four_window","mcwroofs:light_gray_roof","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","dyenamics:bubblegum_candle","mcwlights:iron_triple_candle_holder","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","travelersbackpack:redstone","immersiveengineering:crafting/shovel_steel","chimes:glass_bells","mcwwindows:iron_shutter","minecraft:netherite_upgrade_smithing_template","aether:diamond_chestplate_repairing","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","immersiveengineering:crafting/sawblade","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","allthemodium:allthemodium_gear","railcraft:steel_shovel","croptopia:shaped_nether_wart_stew","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","allthecompressed:compress/oak_log_1x","minecraft:shulker_box","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","aether:diamond_gloves","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","botania:unholy_cloak","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","minecraft:lantern","mcwwindows:jungle_pane_window","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","minecraft:iron_trapdoor","mcwbridges:nether_bricks_bridge_pier","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","delightful:knives/bone_knife","mcwwindows:dark_oak_log_parapet","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","immersiveengineering:crafting/redstone_acid","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","securitycraft:reinforced_warped_fence_gate","mcwroofs:black_terracotta_attic_roof","mcwbiomesoplenty:stripped_jacaranda_pane_window","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwroofs:prismarine_brick_upper_lower_roof","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","minecraft:purpur_pillar","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","minecraft:redstone_lamp","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","supplementaries:candle_holders/candle_holder_cyan_dye","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","immersiveengineering:crafting/plate_silver_hammering","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","silentgear:stone_torch","handcrafted:terracotta_bowl","botania:black_hole_talisman","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","cfm:dye_cyan_picket_gate","mcwwindows:green_mosaic_glass_pane","forbidden_arcanus:sanity_meter","mcwlights:glowstone_slab","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","minecraft:polished_blackstone_pressure_plate","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwlights:lime_lamp","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwwindows:mangrove_plank_pane_window","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","securitycraft:storage_module","mcwbiomesoplenty:dead_hedge","handcrafted:wood_bowl","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","minecraft:lapis_lazuli","travelersbackpack:wolf","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","minecraft:quartz_block","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:unobtainium_mage_boots_smithing","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","supplementaries:sack","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","cfm:stripped_oak_mail_box","botania:forest_eye","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","allthemodium:smithing/allthemodium_helmet_smithing","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","securitycraft:cage_trap","alltheores:silver_plate","handcrafted:terracotta_medium_pot","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","allthemodium:smithing/allthemodium_boots_smithing","mcwwindows:mangrove_shutter","cfm:lime_cooler","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","supplementaries:candy","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","mcwbiomesoplenty:hellbark_plank_four_window","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","allthemodium:smithing/unobtainium_axe_smithing","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","cfm:white_kitchen_drawer","utilitarian:tps_meter","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:nether_bricks_upper_steep_roof","deepresonance:machine_frame","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","allthemodium:smithing/allthemodium_leggings_smithing","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","alltheores:gold_rod","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","rftoolspower:power_core1","botania:gaia_ingot","additionallanterns:cobbled_deepslate_lantern","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_window2","supplementaries:sugar_cube","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:magic_window2","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","mcwroofs:dark_prismarine_top_roof","simplylight:illuminant_red_block_on_toggle","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","minecraft:dye_cyan_wool","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","securitycraft:reinforced_oak_fence","mcwroofs:dark_prismarine_lower_roof","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","handcrafted:oak_nightstand","railcraft:personal_world_spike","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwroofs:nether_bricks_attic_roof","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","minecraft:cracked_nether_bricks","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","supplementaries:slingshot","minecraft:purpur_pillar_from_purpur_block_stonecutting","allthemodium:allthemodium_plate","simplylight:illuminant_green_block_toggle","cfm:black_grill","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","mcwroofs:nether_bricks_steep_roof","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","dyenamics:rose_candle","aether:aether_iron_nugget_from_blasting","aquaculture:neptunium_ingot_from_nuggets","minecraft:iron_pickaxe","aether:shield_repairing","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwwindows:granite_window","minecraft:vibranium_mage_chestplate_smithing","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","rftoolsutility:fluid_module","undergarden:smelt_catalyst","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","aether:leather_boots_repairing","cfm:post_box","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","dyenamics:aquamarine_candle","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","aether:leather_gloves_repairing","minecraft:lime_stained_glass","dyenamics:mint_candle","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","ae2:tools/nether_quartz_wrench","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","dyenamics:maroon_terracotta","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","aether:packed_ice_freezing","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwpaths:stone_crystal_floor","cfm:red_kitchen_sink","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","aether:diamond_leggings_repairing","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","mcwroofs:black_terracotta_lower_roof","immersiveengineering:crafting/radiator","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","cfm:spatula","pneumaticcraft:charging_upgrade","mcwlights:soul_mangrove_tiki_torch","xnet:connector_red_dye","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","sophisticatedbackpacks:crafting_upgrade","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","twilightdelight:cutting/ice_sword","pneumaticcraft:standby_upgrade","mcwwindows:jungle_window","minecraft:cobblestone_stairs","mcwwindows:crimson_louvered_shutter","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","reliquary:uncrafting/glowstone_dust","tombstone:ankh_of_prayer","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","utilitix:dark_oak_shulker_boat_with_shell","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","botania:obedience_stick","mcwwindows:bricks_pane_window","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","mcwlights:magenta_lamp","nethersdelight:hoglin_sirloin_from_smoking","create:crafting/logistics/powered_latch","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","sophisticatedstorage:stonecutter_upgrade","cfm:acacia_kitchen_drawer","mcwroofs:white_terracotta_upper_lower_roof","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","mcwroofs:prismarine_brick_steep_roof","handcrafted:oak_bench","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","mcwroofs:base_roof_block","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","mcwbiomesoplenty:palm_window","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:navy_candle","mcwroofs:dark_prismarine_upper_steep_roof","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","sophisticatedbackpacks:chipped/glassblower_upgrade","utilitix:acacia_shulker_boat_with_shell","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:prismarine_brick_slab","aether:chainmail_gloves_repairing","minecraft:iron_chestplate","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","allthearcanistgear:vibranium_robes_smithing","littlelogistics:seater_car","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","minecraft:oak_pressure_plate","bigreactors:turbine/basic/activefluidport_forge","aether:netherite_leggings_repairing","aether:skyroot_beehive","mcwwindows:quartz_window","cfm:brown_grill","mcwroofs:gray_roof","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","dyenamics:peach_candle","mcwbiomesoplenty:mahogany_horse_fence","utilitix:mangrove_shulker_boat_with_shell","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:netherite_axe_smithing","minecraft:clock","create:crafting/appliances/netherite_diving_helmet_from_netherite","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","mcwroofs:grass_upper_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","aquaculture:iron_fillet_knife","silentgear:diamond_shard","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","aether:leather_leggings_repairing","dyenamics:bed/conifer_bed","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwdoors:oak_modern_door","mcwwindows:birch_window2","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","minecraft:prismarine","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwroofs:purple_terracotta_roof","mcwbridges:nether_bricks_bridge_stair","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","twigs:rhyolite","pylons:infusion_pylon","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","dyenamics:honey_candle","securitycraft:block_change_detector","minecraft:polished_blackstone_button","supplementaries:pancake_fd","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwroofs:pink_terracotta_lower_roof","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwfurnitures:oak_drawer_counter","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","additionallanterns:quartz_chain","travelersbackpack:standard_smithing","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","connectedglass:scratched_glass_cyan_pane2","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","mcwpaths:red_sand_path_block","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","undergarden:gloom_o_lantern","mcwwindows:birch_window","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","ae2:network/cables/dense_covered_cyan","sophisticatedbackpacks:smithing_upgrade","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","mcwfurnitures:oak_lower_triple_drawer","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","utilitix:bamboo_shulker_raft_with_shell","sophisticatedbackpacks:anvil_upgrade","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwwindows:jungle_blinds","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","minecraft:end_stone_brick_wall","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","mcwlights:red_lamp","bloodmagic:blood_altar","cfm:purple_kitchen_drawer","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","minecraft:netherite_chestplate_smithing","mcwlights:golden_candle_holder","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwlights:light_blue_lamp","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","sfm:manager","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","allthemodium:smithing/allthemodium_sword_smithing","connectedglass:tinted_borderless_glass_cyan2","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","deeperdarker:bloom_boat","utilitix:linked_repeater","mcwroofs:pink_terracotta_upper_steep_roof","dyenamics:rose_stained_glass","sophisticatedstorage:chipped/carpenters_table_upgrade","securitycraft:reinforced_jungle_fence","mcwroofs:light_gray_upper_steep_roof","cfm:stripped_spruce_kitchen_drawer","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","allthemodium:ancient_stone_stairs","mcwwindows:stripped_cherry_pane_window","mcwroofs:dark_prismarine_attic_roof","cfm:light_blue_kitchen_sink","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:blue_terracotta_steep_roof","mcwbiomesoplenty:hellbark_pyramid_gate","farmersdelight:cooking/dog_food","allthearcanistgear:vibranium_hat_smithing","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","railcraft:radio_circuit","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","allthemodium:ancient_stone_wall","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","minecraft:iron_helmet","voidtotem:totem_of_void_undying","securitycraft:reinforced_pink_stained_glass_pane_from_dye","cfm:brown_kitchen_counter","securitycraft:alarm","mcwfurnitures:oak_counter","rftoolsutility:charged_porter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","mcwroofs:oak_top_roof","create:crafting/appliances/netherite_backtank_from_netherite","utilitix:crude_furnace","additionallanterns:amethyst_lantern","cfm:crimson_kitchen_sink_light","mcwwindows:stone_window","utilitix:comparator_redirector_down","mcwroofs:gray_steep_roof","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","sophisticatedstorage:jukebox_upgrade","mcwfurnitures:stripped_oak_modern_wardrobe","mcwfences:railing_nether_brick_wall","mcwroofs:yellow_terracotta_upper_lower_roof","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","minecraft:heavy_weighted_pressure_plate","cfm:black_trampoline","cfm:stripped_oak_crate","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","mcwdoors:oak_bark_glass_door","allthemodium:smithing/vibranium_sword_smithing","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","simplylight:illuminant_orange_block_dyed","minecraft:polished_blackstone_stairs","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","minecraft:dropper","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwdoors:oak_japanese2_door","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","utilitarian:utility/oak_logs_to_slabs","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","travelersbackpack:netherite","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","minecraft:shield","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwbridges:balustrade_end_stone_bricks_bridge","minecraft:quartz_bricks","cfm:blue_kitchen_drawer","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:prismarine_bricks_bridge_stair","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","minecraft:iron_nugget_from_smelting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","securitycraft:motion_activated_light","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","ae2:network/cables/dense_smart_fluix","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","sophisticatedbackpacks:pickup_upgrade","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwroofs:light_gray_terracotta_attic_roof","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","botania:blood_pendant","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","mcwwindows:metal_four_window","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","minecraft:honeycomb_block","cfm:lime_kitchen_drawer","mcwbiomesoplenty:redwood_pane_window","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","eccentrictome:tome","securitycraft:reinforced_warped_fence","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwbridges:prismarine_bricks_bridge_pier","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","mcwwindows:warped_planks_window","sophisticatedbackpacks:chipped/loom_table_upgrade","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","dyenamics:bed/persimmon_bed","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","dyenamics:icy_blue_terracotta","dyenamics:cherenkov_stained_glass","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","botania:astrolabe","sophisticatedbackpacks:stack_upgrade_tier_1","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:light_gray_terracotta","dyenamics:amber_stained_glass","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","botania:manasteel_shears","minecraft:glass_bottle","mcwfences:mesh_metal_fence","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","mcwwindows:prismarine_pane_window","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","blue_skies:trough","botania:lens_normal","mcwfences:end_brick_pillar_wall","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","minecraft:quartz_slab","mcwroofs:cyan_terracotta_upper_steep_roof","handcrafted:phantom_trophy","modularrouters:modular_router","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","minecraft:quartz_pillar_from_quartz_block_stonecutting","railcraft:steel_helmet","mcwroofs:nether_bricks_roof","cfm:stripped_spruce_mail_box","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwwindows:birch_log_parapet","rftoolsutility:button_module","minecraft:charcoal","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","supplementaries:bellows","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","handcrafted:kitchen_hood","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","mcwroofs:black_lower_roof","mcwfurnitures:stripped_oak_desk","mcwroofs:gutter_base_blue","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwwindows:dark_prismarine_pane_window","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:iron_sword","aquaculture:sushi","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","botania:runic_altar","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwroofs:gutter_base_lime","minecraft:prismarine_brick_stairs_from_prismarine_stonecutting","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","botania:abstruse_platform","mcwfences:crimson_stockade_fence","mcwbiomesoplenty:jacaranda_four_window","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","mcwwindows:dark_oak_plank_window","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:oak_kitchen_sink_light","mcwwindows:diorite_pane_window","utilitix:tiny_coal_from_tiny","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwbridges:end_stone_bricks_bridge","mcwroofs:light_gray_terracotta_steep_roof","mcwfurnitures:oak_desk","aether:stone_pickaxe_repairing","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","mcwwindows:deepslate_window","minecraft:gray_terracotta","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","minecraft:netherite_boots_smithing","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:prismarine_brick_lower_roof","dyenamics:peach_terracotta","minecraft:fishing_rod","xnet:connector_yellow_dye","delightful:knives/silver_knife","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:yellow_lamp","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","aether:diamond_pickaxe_repairing","blue_skies:lunar_bookshelf","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","mcwlights:double_street_lamp","minecraft:tnt","minecolonies:chainmailboots","additionallanterns:quartz_lantern","minecraft:flint_and_steel","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","xnet:advanced_connector_red_dye","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","rftoolsbase:tablet","mcwroofs:magenta_terracotta_lower_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","supplementaries:item_shelf","handcrafted:terracotta_plate","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","silentgear:coating_smithing_template","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","minecraft:glass_pane","supplementaries:timber_brace","allthearcanistgear:unobtainium_robes_smithing","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","mcwbiomesoplenty:pine_pane_window","enderio:wood_gear","supplementaries:bed_from_feather_block","cfm:red_kitchen_counter","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","silentgear:stone_rod","minecraft:leather_boots","create:crafting/appliances/netherite_diving_boots_from_netherite","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","utilitix:highspeed_rail","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","mcwlights:pink_paper_lamp","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","tombstone:white_marble","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","domum_ornamentum:magenta_floating_carpet","nethersdelight:nether_brick_smoker","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","aether:book_of_lore","railcraft:steel_pickaxe","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","aether:aether_iron_nugget_from_smelting","sfm:cable","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:white_upper_steep_roof","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","minecraft:end_stone_bricks_from_end_stone_stonecutting","botania:phantom_ink","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","mcwfences:mangrove_highley_gate","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","mythicbotany:alfsteel_pylon","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","railcraft:goggles","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","alltheores:osmium_rod","minecraft:oak_fence","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","minecraft:prismarine_brick_stairs","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","reliquary:uncrafting/redstone","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","minecraft:netherite_block","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","minecraft:crossbow","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwwindows:orange_mosaic_glass_pane","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","securitycraft:taser","allthemodium:vibranium_nugget_from_ingot","supplementaries:soap","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","minecraft:purpur_stairs","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","securitycraft:reinforced_andesite","aether:aether_tune_enchanting","supplementaries:altimeter","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","cfm:red_kitchen_drawer","minecraft:quartz_stairs","botania:manasteel_shovel","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwroofs:white_steep_roof","minecraft:purpur_slab","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwlights:jungle_tiki_torch","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","sophisticatedbackpacks:tool_swapper_upgrade","minecraft:gold_nugget","bigreactors:energizer/controller","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","botania:super_travel_belt","minecraft:lime_terracotta","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","handcrafted:bench","mcwwindows:diorite_four_window","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","travelersbackpack:backpack_tank","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","mcwwindows:stripped_birch_log_four_window","additionallanterns:netherite_chain","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","silentgear:upgrade_base","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","ae2:network/cables/smart_cyan","handcrafted:pufferfish_trophy","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","mcwfences:modern_mud_brick_wall","allthemodium:smithing/allthemodium_chestplate_smithing","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwroofs:green_terracotta_steep_roof","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","tombstone:dark_marble","minecraft:iron_shovel","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","enderio:basic_fluid_filter","rftoolsutility:counter_module","aether:netherite_boots_repairing","minecraft:oak_door","biomesoplenty:jacaranda_boat","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:black_roof_block","connectedglass:clear_glass_cyan_pane2","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","handcrafted:oak_corner_trim","cfm:spruce_kitchen_drawer","mcwbiomesoplenty:magic_highley_gate","minecraft:dark_prismarine","botania:balance_cloak","simplemagnets:basic_demagnetization_coil","dyenamics:mint_terracotta","mcwlights:soul_jungle_tiki_torch","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","supplementaries:candle_holders/candle_holder_magenta","mcwfences:oak_curved_gate","minecraft:bow","mcwroofs:blue_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","botania:super_lava_pendant","bigreactors:energizer/chargingport_fe","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","cfm:purple_kitchen_sink","cfm:black_cooler","supplementaries:sconce","silentgear:rough_rod","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:prismarine_brick_roof","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","sophisticatedbackpacks:feeding_upgrade","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","cfm:dye_cyan_picket_fence","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","aether:netherite_shovel_repairing","mcwfurnitures:oak_coffee_table","mcwbridges:balustrade_nether_bricks_bridge","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","mcwwindows:deepslate_pane_window","botania:quartz_blaze","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","dyenamics:rose_terracotta","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","minecraft:stone","twigs:lamp","dyenamics:bed/lavender_bed","cfm:lime_kitchen_counter","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","minecraft:dye_cyan_bed","blue_skies:glowing_poison_stone","tombstone:carmin_marble","mcwroofs:brown_terracotta_steep_roof","mcwdoors:oak_barn_door","botania:alchemy_catalyst","allthemodium:ancient_stone_bricks","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","minecraft:purpur_slab_from_purpur_block_stonecutting","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","mcwwindows:blue_mosaic_glass","additionallanterns:obsidian_chain","minecraft:ender_eye","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","mcwlights:brown_lamp","mcwwindows:bricks_four_window","railcraft:receiver_circuit","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","sophisticatedbackpacks:smoking_upgrade","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","securitycraft:trophy_system","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","mcwlights:warped_tiki_torch","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","minecraft:gold_nugget_from_smelting","dyenamics:wine_stained_glass","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","computercraft:turtle_advanced_overlays/turtle_trans_overlay","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","dyenamics:spring_green_candle","mcwbiomesoplenty:dead_highley_gate","mcwroofs:oak_planks_top_roof","biomesoplenty:hellbark_boat","railcraft:steel_chestplate","mcwfences:oak_hedge","mcwroofs:oak_planks_roof","enderio:fluid_tank","mcwpaths:stone_flagstone","bigreactors:energizer/casing","simplylight:illuminant_block_toggle","minecraft:nether_brick_slab","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","minecraft:enchanting_table","botania:mana_tablet","cfm:birch_mail_box","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deeperdarker:reinforced_echo_shard","mcwlights:brown_paper_lamp","minecraft:campfire","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","minecraft:black_stained_glass"],toBeDisplayed:["cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwwindows:birch_four_window","biomesoplenty:fir_boat","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwroofs:blue_terracotta_attic_roof","handcrafted:spider_trophy","mcwroofs:lime_terracotta_lower_roof","mcwroofs:red_terracotta_lower_roof","ad_astra:cyan_industrial_lamp","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","xnet:connector_green","advanced_ae:smallappupgrade","create:oak_window","minecraft:gold_nugget_from_blasting","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","aether:chainmail_leggings_repairing","botania:missile_rod","sophisticatedbackpacks:void_upgrade","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","dyenamics:maroon_candle","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwwindows:oak_plank_pane_window","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","utilitix:oak_shulker_boat_with_shell","securitycraft:reinforced_cherry_fence_gate","mcwroofs:magenta_terracotta_roof","rftoolsutility:screen_link","minecraft:leather_helmet","securitycraft:reinforced_acacia_fence","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","energymeter:meter","mcwlights:covered_lantern","sliceanddice:slicer","minecraft:nether_brick_stairs","supplementaries:bubble_blower","supplementaries:crank","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","mcwroofs:black_steep_roof","allthecompressed:compress/ancient_stone_1x","minecraft:unobtainium_mage_chestplate_smithing","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","minecraft:pink_terracotta","mcwbiomesoplenty:mahogany_plank_pane_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","immersiveengineering:crafting/rockcutter","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","nethersdelight:golden_machete","mcwroofs:green_terracotta_top_roof","mcwwindows:acacia_curtain_rod","mcwlights:chain_lantern","securitycraft:camera_monitor","allthecompressed:compress/quartz_block_1x","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","minecraft:mojang_banner_pattern","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","minecraft:vibranium_spell_book_smithing","cfm:oak_kitchen_counter","minecraft:golden_hoe","minecraft:fire_charge","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:mana_distributor","allthemodium:smithing/vibranium_axe_smithing","biomesoplenty:mahogany_boat","aquaculture:note_hook","additionallanterns:stone_bricks_lantern","mcwlights:dark_oak_ceiling_fan_light","simplylight:illuminant_cyan_block_on_dyed","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","minecraft:lodestone","occultism:crafting/magic_lamp_empty","mcwlights:black_lamp","minecraft:purpur_block","minecraft:polished_blackstone_slab","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:nether_brick_wall","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","blue_skies:glowing_blinding_stone","mcwbiomesoplenty:umbran_plank_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","botania:incense_stick","mcwbiomesoplenty:pine_plank_window2","mcwwindows:warped_louvered_shutter","blue_skies:glowing_nature_stone","xnet:connector_routing","mcwbiomesoplenty:palm_plank_window2","dyenamics:aquamarine_terracotta","mcwroofs:green_terracotta_attic_roof","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","immersiveengineering:crafting/shield","utilitix:directional_highspeed_rail","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","dyenamics:ultramarine_candle","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","cfm:stripped_crimson_kitchen_sink_dark","mcwroofs:prismarine_brick_top_roof","cfm:warped_mail_box","cfm:green_grill","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","dyenamics:fluorescent_candle","mcwroofs:white_terracotta_attic_roof","mcwwindows:dark_oak_shutter","mcwroofs:oak_roof","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_gray_roof_slab","mcwfences:nether_brick_pillar_wall","mcwbiomesoplenty:willow_curved_gate","delightful:knives/osmium_knife","mcwbridges:balustrade_prismarine_bricks_bridge","dyenamics:lavender_candle","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","mcwfences:blackstone_brick_railing_gate","sophisticatedbackpacks:filter_upgrade","aether:skyroot_barrel","mcwbiomesoplenty:maple_curved_gate","mcwroofs:black_terracotta_roof","mcwwindows:stripped_cherry_log_window2","minecraft:golden_pickaxe","cfm:green_kitchen_sink","minecraft:chiseled_bookshelf","mcwfences:diorite_railing_gate","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","mcwpaths:cobblestone_basket_weave_paving","allthearcanistgear:unobtainium_hat_smithing","create:crafting/materials/andesite_alloy","mcwwindows:blackstone_window","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","mcwfurnitures:oak_double_drawer_counter","cfm:blue_grill","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","minecraft:quartz_slab_from_stonecutting","mcwroofs:nether_bricks_upper_lower_roof","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","mcwtrpdoors:metal_full_trapdoor","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","dyenamics:icy_blue_candle","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","appbot:mana_cell_housing","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","mcwbiomesoplenty:fir_hedge","minecraft:netherite_sword_smithing","croptopia:shaped_toast_sandwich","mcwfences:birch_highley_gate","botania:manasteel_axe","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwdoors:oak_cottage_door","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","mcwdoors:metal_hospital_door","railcraft:signal_circuit","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","minecraft:prismarine_brick_slab_from_prismarine_stonecutting","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","mcwlights:cherry_tiki_torch","immersiveengineering:crafting/screwdriver","croptopia:buttered_toast","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","dyenamics:ultramarine_terracotta","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","mekanism:transmitter/mechanical_pipe/basic","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","twigs:quartz_column","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","minecraft:purpur_stairs_from_purpur_block_stonecutting","travelersbackpack:bee","additionallanterns:prismarine_chain","nethersdelight:hoglin_sirloin_from_campfire_cooking","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwroofs:dark_prismarine_steep_roof","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwroofs:gray_attic_roof","securitycraft:reinforced_andesite_with_vanilla_cobblestone","handcrafted:terracotta_wide_pot","mcwroofs:blackstone_lower_roof","reliquary:uncrafting/glass_bottle","ae2:tools/portable_item_cell_256k","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwroofs:purple_terracotta_upper_lower_roof","biomesoplenty:orange_dye_from_burning_blossom","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","handcrafted:wood_cup","twilightforest:mining_boat","handcrafted:terracotta_cup","additionallanterns:normal_nether_bricks_chain","mcwwindows:window_half_bar_base","littlelogistics:tug_route","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","mcwbiomesoplenty:dead_picket_fence","mcwbiomesoplenty:hellbark_window2","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","cfm:stripped_oak_chair","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","mcwwindows:oak_shutter","minecraft:wooden_hoe","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","minecraft:quartz_pillar","immersiveengineering:crafting/axe_steel","sophisticatedbackpacks:jukebox_upgrade","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwroofs:nether_bricks_top_roof","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","mcwroofs:nether_bricks_lower_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","aether:obsidian_from_bucket_freezing","minecraft:blue_dye","railcraft:steel_leggings","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","mcwwindows:crimson_planks_four_window","minecraft:piston","mcwlights:white_lamp","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","botania:mana_bomb","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","mcwroofs:gray_upper_steep_roof","mcwwindows:metal_curtain_rod","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","handcrafted:oak_shelf","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwfences:crimson_curved_gate","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","mcwbridges:end_stone_bricks_bridge_pier","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","aether:skyroot_piston","cfm:stripped_birch_kitchen_sink_dark","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","additionallanterns:bricks_lantern","quark:tweaks/crafting/utility/tools/stone_pickaxe","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","mcwroofs:red_terracotta_upper_steep_roof","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","securitycraft:reinforced_crimson_fence_gate","mcwbiomesoplenty:rainbow_birch_hedge","botania:spawner_mover","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","xnet:antenna","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","dyenamics:persimmon_candle","cfm:stripped_warped_kitchen_drawer","mcwroofs:prismarine_brick_upper_steep_roof","mcwroofs:cobblestone_roof","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwfences:dark_oak_hedge","botania:manasteel_chestplate","mcwfences:bamboo_highley_gate","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","computercraft:pocket_computer_advanced","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","mcwroofs:white_terracotta_roof","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","mcwwindows:mangrove_window","cfm:light_gray_grill","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:hellbark_hedge","allthemodium:allthemodium_block","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","mcwroofs:light_gray_top_roof","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwbiomesoplenty:redwood_picket_fence","dyenamics:banner/mint_banner","minecraft:crafting_table","dyenamics:cherenkov_dye","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","railcraft:steel_sword","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwwindows:oak_plank_four_window","domum_ornamentum:brown_bricks","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","sophisticatedstorage:stack_upgrade_tier_1","alchemistry:reactor_casing","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","mcwwindows:spruce_curtain_rod","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","aether:netherite_helmet_repairing","cfm:stripped_acacia_kitchen_sink_light","mcwbiomesoplenty:stripped_willow_log_window","laserio:logic_chip_raw","supplementaries:cage","mcwfences:diorite_grass_topped_wall","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","mcwwindows:stone_window2","minecraft:oak_button","mcwroofs:prismarine_brick_attic_roof","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:maple_window","croptopia:fruit_smoothie","mcwbiomesoplenty:stripped_pine_pane_window","botania:blaze_block","mcwtrpdoors:metal_warning_trapdoor","simplylight:illuminant_yellow_block_on_toggle","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","railcraft:steel_shears","allthemodium:unobtainium_gear","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","minecraft:item_frame","itemcollectors:basic_collector","minecraft:loom","railcraft:steel_axe","mcwtrpdoors:oak_bark_trapdoor","botania:exchange_rod","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","computercraft:computer_advanced","botania:crafting_halo","mcwwindows:acacia_plank_four_window","dyenamics:banner/rose_banner","botania:gaia_spreader","mcwroofs:black_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","handcrafted:wood_plate","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","travelersbackpack:horse","aether:diamond_gloves_repairing","cfm:brown_cooler","mcwfences:diorite_pillar_wall","mcwwindows:dark_prismarine_parapet","rftoolsutility:inventoryplus_module","dyenamics:amber_candle","mcwbiomesoplenty:mahogany_plank_four_window","ae2:tools/nether_quartz_pickaxe","undergarden:shard_torch","utilitix:experience_crystal","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","paraglider:paraglider","aether:golden_leggings_repairing","sophisticatedstorage:shulker_box","utilitix:anvil_cart","immersiveengineering:crafting/component_steel","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","undergarden:blast_catalyst","alchemistry:dissolver","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","sophisticatedbackpacks:compacting_upgrade","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","mcwroofs:dark_prismarine_roof","minecraft:composter","minecraft:jukebox","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwtrpdoors:oak_mystic_trapdoor","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","minecraft:magenta_terracotta","mcwroofs:white_upper_lower_roof","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","mcwroofs:red_terracotta_top_roof","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:utility/oak_logs_to_doors","mcwlights:spruce_ceiling_fan_light","mcwroofs:gray_terracotta_attic_roof","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","minecraft:chiseled_quartz_block","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","mcwwindows:blackstone_pane_window","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","minecraft:end_stone_bricks","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","handcrafted:oak_pillar_trim","dyenamics:banner/maroon_banner","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","botania:quartz_sunny","create:framed_glass_from_glass_colorless_stonecutting","botania:flighttiara_0","railcraft:signal_lamp","reliquary:uncrafting/sugar","tombstone:bone_needle","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","sophisticatedstorage:decoration_table","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","utilitarian:utility/glow_ink_sac","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwwindows:blackstone_window2","mcwroofs:white_top_roof","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","minecraft:gold_block","dyenamics:banner/navy_banner","aquaculture:gold_fillet_knife","travelersbackpack:gold","ae2:network/cables/dense_smart_cyan","mcwfences:warped_curved_gate","connectedglass:scratched_glass_cyan2","mcwdoors:oak_stable_door","travelersbackpack:warden","cfm:oak_upgraded_gate","mcwroofs:light_gray_terracotta_roof","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwroofs:lime_terracotta_steep_roof","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","securitycraft:reinforced_observer","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","allthemodium:unobtainium_rod","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","mcwfurnitures:oak_bookshelf","mcwwindows:oak_plank_window","nethersdelight:hoglin_sirloin","mcwwindows:oak_log_parapet","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","minecraft:writable_book","mcwfences:acacia_hedge","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","additionallanterns:purpur_chain","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","mcwroofs:orange_terracotta_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","rftoolsbuilder:green_shield_template_block","allthemodium:vibranium_plate","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","minecraft:polished_blackstone_wall","mcwfences:warped_horse_fence","mcwroofs:black_roof","securitycraft:reinforced_gray_stained_glass_pane_from_glass","alltheores:gold_plate","mcwbiomesoplenty:maple_four_window","cfm:blue_kitchen_counter","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwbridges:end_stone_bricks_bridge_stair","mcwroofs:base_top_roof","securitycraft:harming_module","minecraft:golden_boots","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","supplementaries:flags/flag_cyan","botania:diva_charm","cfm:pink_grill","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwroofs:gutter_middle_orange","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","additionallanterns:cobblestone_lantern","minecraft:nether_brick_wall_from_nether_bricks_stonecutting","minecraft:oak_stairs","alchemistry:liquifier","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:white_terracotta_upper_steep_roof","securitycraft:reinforced_piston","cfm:purple_kitchen_counter","securitycraft:reinforced_yellow_stained_glass","botania:swap_ring","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","twilightforest:wood/oak_banister","alltheores:enderium_plate","mcwwindows:purple_mosaic_glass","allthecompressed:compress/gold_block_1x","xnet:connector_yellow","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:end_stone_brick_stairs","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwroofs:dark_prismarine_upper_lower_roof","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","domum_ornamentum:gray_floating_carpet","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","minecraft:cyan_terracotta","mcwwindows:light_blue_mosaic_glass","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","cfm:oak_upgraded_fence","botania:super_cloud_pendant","railcraft:steel_gear","sophisticatedstorage:crafting_upgrade","minecraft:prismarine_bricks","allthemodium:smithing/allthemodium_axe_smithing","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","securitycraft:reinforced_red_stained_glass","connectedglass:borderless_glass_cyan2","mcwbiomesoplenty:jacaranda_wired_fence","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:dead_plank_window2","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","reliquary:glowing_water_from_potion_vial","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:cyan_stained_glass","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","pylons:potion_filter","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","cfm:stripped_mangrove_kitchen_sink_light","handcrafted:quartz_corner_trim","mcwwindows:acacia_log_parapet","reliquary:uncrafting/stick","simplylight:illuminant_red_block_on_dyed","dyenamics:fluorescent_stained_glass","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","handcrafted:oak_side_table","pneumaticcraft:entity_tracker_upgrade","mcwpaths:stone_crystal_floor_path","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","cfm:lime_grill","mcwbiomesoplenty:hellbark_plank_window2","mcwroofs:lime_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass","handcrafted:golden_medium_pot","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","immersiveengineering:crafting/component_iron","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwroofs:gray_terracotta_upper_steep_roof","sfm:fancy_to_cable","aquaculture:heavy_hook","minecraft:yellow_terracotta","mcwwindows:acacia_window","mcwroofs:green_terracotta_roof","mcwbridges:prismarine_bricks_bridge","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","minecraft:chiseled_nether_bricks_from_nether_bricks_stonecutting","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwwindows:stripped_crimson_stem_four_window","utilitix:spruce_shulker_boat_with_shell","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwbridges:oak_bridge_pier","cfm:magenta_kitchen_counter","cfm:spruce_mail_box","minecraft:barrel","mcwlights:orange_lamp","mcwwindows:dark_oak_pane_window","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","botania:dodge_ring","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","botania:laputa_shard","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","allthearcanistgear:vibranium_boots_smithing","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","minecraft:end_stone_brick_slab","mcwwindows:mangrove_plank_parapet","minecraft:smithing_table","mcwpaths:gravel_path_block","supplementaries:lapis_bricks","mcwbridges:glass_bridge_pier","mcwbridges:nether_bricks_bridge","minecraft:cauldron","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","mcwfurnitures:oak_kitchen_cabinet","minecraft:diamond_shovel","mcwroofs:brown_terracotta_lower_roof","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","tombstone:impregnated_diamond","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","cfm:cyan_kitchen_sink","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","mcwbiomesoplenty:umbran_window2","alltheores:enderium_rod","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwlights:sea_lantern_slab","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","minecraft:gray_stained_glass","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwroofs:oak_upper_lower_roof","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","mcwroofs:brown_terracotta_top_roof","travelersbackpack:standard","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwroofs:gutter_base_red","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","botania:holy_cloak","sophisticatedstorage:filter_upgrade","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","minecraft:golden_chestplate","securitycraft:reinforced_gray_stained_glass","sophisticatedbackpacks:chipped/carpenters_table_upgrade","mcwbiomesoplenty:stripped_maple_log_window2","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","sophisticatedstorage:oak_limited_barrel_1","sophisticatedstorage:oak_limited_barrel_2","pneumaticcraft:range_upgrade","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","dyenamics:navy_terracotta","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","railcraft:diamond_tunnel_bore_head","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","minecraft:light_blue_terracotta","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","minecraft:cyan_candle","ae2:tools/portable_item_cell_16k","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","cfm:cyan_cooler","railcraft:silver_gear","mcwwindows:prismarine_four_window","mcwroofs:light_gray_roof","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","dyenamics:bubblegum_candle","mcwlights:iron_triple_candle_holder","dyenamics:fluorescent_terracotta","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","travelersbackpack:redstone","immersiveengineering:crafting/shovel_steel","chimes:glass_bells","mcwwindows:iron_shutter","minecraft:netherite_upgrade_smithing_template","aether:diamond_chestplate_repairing","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","travelersbackpack:emerald","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","immersiveengineering:crafting/sawblade","ae2:tools/nether_quartz_axe","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","allthemodium:allthemodium_gear","railcraft:steel_shovel","croptopia:shaped_nether_wart_stew","mcwroofs:cobblestone_attic_roof","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","mcwroofs:magenta_terracotta_attic_roof","allthecompressed:compress/oak_log_1x","minecraft:shulker_box","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","aether:diamond_gloves","aquaculture:cooked_fish_fillet","travelersbackpack:blank_upgrade","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","botania:unholy_cloak","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","minecraft:lantern","mcwwindows:jungle_pane_window","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","minecraft:iron_trapdoor","mcwbridges:nether_bricks_bridge_pier","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","delightful:knives/bone_knife","mcwwindows:dark_oak_log_parapet","botania:ender_dagger","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","immersiveengineering:crafting/redstone_acid","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","botania:conversions/blazeblock_deconstruct","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","travelersbackpack:nether","securitycraft:reinforced_warped_fence_gate","mcwroofs:black_terracotta_attic_roof","mcwbiomesoplenty:stripped_jacaranda_pane_window","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwroofs:prismarine_brick_upper_lower_roof","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","mcwroofs:white_lower_roof","minecraft:purpur_pillar","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","minecraft:redstone_lamp","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","supplementaries:candle_holders/candle_holder_cyan_dye","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","mythicbotany:rune_holder","immersiveengineering:crafting/plate_silver_hammering","mcwroofs:gray_lower_roof","mcwwindows:spruce_plank_four_window","silentgear:stone_torch","handcrafted:terracotta_bowl","botania:black_hole_talisman","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwbiomesoplenty:dead_four_window","cfm:dye_cyan_picket_gate","mcwwindows:green_mosaic_glass_pane","forbidden_arcanus:sanity_meter","mcwlights:glowstone_slab","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwlights:purple_paper_lamp","securitycraft:bouncing_betty","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","minecraft:polished_blackstone_pressure_plate","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwlights:lime_lamp","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwwindows:mangrove_plank_pane_window","securitycraft:universal_block_remover","securitycraft:reinforced_bookshelf","alltheores:osmium_gear","minecraft:netherite_leggings_smithing","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","securitycraft:storage_module","mcwbiomesoplenty:dead_hedge","handcrafted:wood_bowl","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","ae2:tools/portable_item_cell_1k","cfm:oak_kitchen_sink_dark","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","minecraft:lapis_lazuli","travelersbackpack:wolf","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","minecraft:quartz_block","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","mcwroofs:purple_terracotta_attic_roof","mcwroofs:orange_terracotta_lower_roof","minecraft:unobtainium_mage_boots_smithing","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","mcwroofs:brown_terracotta_upper_steep_roof","aether:iron_axe_repairing","supplementaries:sack","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","cfm:stripped_oak_mail_box","botania:forest_eye","mcwwindows:stripped_oak_pane_window","additionallanterns:netherite_lantern","allthemodium:smithing/allthemodium_helmet_smithing","handcrafted:wither_skeleton_trophy","mcwroofs:blue_terracotta_top_roof","securitycraft:cage_trap","alltheores:silver_plate","handcrafted:terracotta_medium_pot","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","allthemodium:smithing/allthemodium_boots_smithing","mcwwindows:mangrove_shutter","cfm:lime_cooler","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","supplementaries:candy","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","mcwbiomesoplenty:hellbark_plank_four_window","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","allthemodium:smithing/unobtainium_axe_smithing","mcwwindows:spruce_window","rftoolsbase:smartwrench","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwroofs:gray_roof_block","cfm:white_kitchen_drawer","utilitarian:tps_meter","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","allthecompressed:compress/emerald_block_1x","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:nether_bricks_upper_steep_roof","deepresonance:machine_frame","mcwbiomesoplenty:hellbark_window","blue_skies:starlit_bookshelf","dyenamics:cherenkov_candle","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","allthemodium:smithing/allthemodium_leggings_smithing","mcwfences:sandstone_grass_topped_wall","minecraft:glowstone","alltheores:gold_rod","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","blue_skies:maple_bookshelf","rftoolspower:power_core1","botania:gaia_ingot","additionallanterns:cobbled_deepslate_lantern","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","simplylight:illuminant_lime_block_on_toggle","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_window2","supplementaries:sugar_cube","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:magic_window2","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","mcwroofs:dark_prismarine_top_roof","simplylight:illuminant_red_block_on_toggle","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","minecraft:dye_cyan_wool","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","securitycraft:reinforced_oak_fence","mcwroofs:dark_prismarine_lower_roof","minecraft:end_stone_brick_stairs_from_end_stone_brick_stonecutting","dyenamics:persimmon_stained_glass","cfm:light_blue_trampoline","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","handcrafted:oak_nightstand","railcraft:personal_world_spike","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","cfm:stripped_acacia_mail_box","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwroofs:nether_bricks_attic_roof","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","minecraft:cracked_nether_bricks","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","supplementaries:slingshot","minecraft:purpur_pillar_from_purpur_block_stonecutting","allthemodium:allthemodium_plate","simplylight:illuminant_green_block_toggle","cfm:black_grill","mcwwindows:mangrove_window2","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","mcwroofs:nether_bricks_steep_roof","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","dyenamics:rose_candle","aether:aether_iron_nugget_from_blasting","aquaculture:neptunium_ingot_from_nuggets","minecraft:iron_pickaxe","aether:shield_repairing","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwwindows:granite_window","minecraft:vibranium_mage_chestplate_smithing","mcwroofs:deepslate_top_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","rftoolsutility:fluid_module","undergarden:smelt_catalyst","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","aether:leather_boots_repairing","cfm:post_box","minecraft:nether_brick_stairs_from_nether_bricks_stonecutting","dyenamics:aquamarine_candle","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","rftoolsstorage:dump_module","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","aether:leather_gloves_repairing","minecraft:lime_stained_glass","dyenamics:mint_candle","mcwbiomesoplenty:magic_plank_four_window","minecraft:netherite_helmet_smithing","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","ae2:tools/nether_quartz_wrench","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","dyenamics:maroon_terracotta","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","aether:packed_ice_freezing","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwpaths:stone_crystal_floor","cfm:red_kitchen_sink","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","aether:diamond_leggings_repairing","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","mcwroofs:black_terracotta_lower_roof","immersiveengineering:crafting/radiator","minecraft:nether_brick_slab_from_nether_bricks_stonecutting","mcwlights:crimson_tiki_torch","supplementaries:candle_holders/candle_holder_purple","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","cfm:spatula","pneumaticcraft:charging_upgrade","mcwlights:soul_mangrove_tiki_torch","xnet:connector_red_dye","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","cfm:brown_kitchen_sink","mcwroofs:orange_terracotta_upper_lower_roof","mcwwindows:andesite_louvered_shutter","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","sophisticatedbackpacks:crafting_upgrade","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","twilightdelight:cutting/ice_sword","pneumaticcraft:standby_upgrade","mcwwindows:jungle_window","minecraft:cobblestone_stairs","mcwwindows:crimson_louvered_shutter","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","blue_skies:comet_bookshelf","cfm:crimson_kitchen_sink_dark","reliquary:uncrafting/glowstone_dust","tombstone:ankh_of_prayer","botania:invisibility_cloak","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","utilitix:dark_oak_shulker_boat_with_shell","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","botania:obedience_stick","mcwwindows:bricks_pane_window","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","mcwlights:magenta_lamp","nethersdelight:hoglin_sirloin_from_smoking","create:crafting/logistics/powered_latch","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","sophisticatedstorage:stonecutter_upgrade","cfm:acacia_kitchen_drawer","mcwroofs:white_terracotta_upper_lower_roof","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","mcwroofs:prismarine_brick_steep_roof","handcrafted:oak_bench","mcwfences:nether_brick_grass_topped_wall","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","mcwwindows:birch_louvered_shutter","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","mcwroofs:base_roof_block","dyenamics:bed/navy_bed","securitycraft:username_logger","potionblender:brewing_cauldron","cfm:oak_blinds","mcwbiomesoplenty:palm_window","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:navy_candle","mcwroofs:dark_prismarine_upper_steep_roof","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","mcwfences:deepslate_pillar_wall","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","sophisticatedbackpacks:chipped/glassblower_upgrade","utilitix:acacia_shulker_boat_with_shell","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:prismarine_brick_slab","aether:chainmail_gloves_repairing","minecraft:iron_chestplate","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","allthearcanistgear:vibranium_robes_smithing","littlelogistics:seater_car","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","mcwroofs:magenta_terracotta_steep_roof","minecraft:oak_pressure_plate","bigreactors:turbine/basic/activefluidport_forge","aether:netherite_leggings_repairing","aether:skyroot_beehive","mcwwindows:quartz_window","cfm:brown_grill","mcwroofs:gray_roof","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","dyenamics:peach_candle","mcwbiomesoplenty:mahogany_horse_fence","utilitix:mangrove_shulker_boat_with_shell","securitycraft:reinforced_chiseled_bookshelf","cfm:gray_kitchen_drawer","minecraft:netherite_axe_smithing","minecraft:clock","create:crafting/appliances/netherite_diving_helmet_from_netherite","domum_ornamentum:cyan_brick_extra","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","biomesoplenty:empyreal_boat","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","mcwroofs:grass_upper_steep_roof","mcwfurnitures:stripped_oak_double_drawer_counter","aquaculture:iron_fillet_knife","silentgear:diamond_shard","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","aether:leather_leggings_repairing","dyenamics:bed/conifer_bed","minecraft:red_nether_bricks","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","mcwdoors:oak_modern_door","mcwwindows:birch_window2","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","minecraft:prismarine","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwroofs:purple_terracotta_roof","mcwbridges:nether_bricks_bridge_stair","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","immersiveengineering:crafting/steel_fence","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","twigs:rhyolite","pylons:infusion_pylon","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","dyenamics:honey_candle","securitycraft:block_change_detector","minecraft:polished_blackstone_button","supplementaries:pancake_fd","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwroofs:pink_terracotta_lower_roof","travelersbackpack:dye_cyan_sleeping_bag","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwfurnitures:oak_drawer_counter","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","additionallanterns:quartz_chain","travelersbackpack:standard_smithing","mcwwindows:birch_curtain_rod","aether:diamond_axe_repairing","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","connectedglass:scratched_glass_cyan_pane2","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","mcwpaths:red_sand_path_block","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","undergarden:gloom_o_lantern","mcwwindows:birch_window","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","ae2:network/cables/dense_covered_cyan","sophisticatedbackpacks:smithing_upgrade","mcwfences:mud_brick_railing_gate","twigs:oak_table","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","mcwfurnitures:oak_lower_triple_drawer","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","utilitix:bamboo_shulker_raft_with_shell","sophisticatedbackpacks:anvil_upgrade","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","minecraft:red_terracotta","botania:glass_pickaxe","mcwbiomesoplenty:willow_horse_fence","additionallanterns:mossy_cobblestone_lantern","cfm:light_blue_grill","mcwwindows:jungle_blinds","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","minecraft:end_stone_brick_wall","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","mcwlights:red_lamp","bloodmagic:blood_altar","cfm:purple_kitchen_drawer","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","minecraft:netherite_hoe_smithing","mcwfences:dark_oak_wired_fence","minecraft:netherite_chestplate_smithing","mcwlights:golden_candle_holder","farmersdelight:diamond_knife","terralith:observer_alt","minecraft:oak_sign","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","mcwlights:light_blue_lamp","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","handcrafted:blaze_trophy","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","mcwroofs:purple_terracotta_top_roof","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","sfm:manager","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","allthemodium:smithing/allthemodium_sword_smithing","connectedglass:tinted_borderless_glass_cyan2","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","deeperdarker:bloom_boat","utilitix:linked_repeater","mcwroofs:pink_terracotta_upper_steep_roof","dyenamics:rose_stained_glass","sophisticatedstorage:chipped/carpenters_table_upgrade","securitycraft:reinforced_jungle_fence","mcwroofs:light_gray_upper_steep_roof","cfm:stripped_spruce_kitchen_drawer","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","allthemodium:ancient_stone_stairs","mcwwindows:stripped_cherry_pane_window","mcwroofs:dark_prismarine_attic_roof","cfm:light_blue_kitchen_sink","supplementaries:candle_holders/candle_holder_light_gray","mcwwindows:warped_planks_four_window","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:blue_terracotta_steep_roof","mcwbiomesoplenty:hellbark_pyramid_gate","farmersdelight:cooking/dog_food","allthearcanistgear:vibranium_hat_smithing","mcwroofs:oak_planks_attic_roof","dyenamics:amber_terracotta","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","railcraft:radio_circuit","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwbiomesoplenty:mahogany_plank_window2","minecraft:daylight_detector","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","allthemodium:ancient_stone_wall","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","minecraft:iron_helmet","voidtotem:totem_of_void_undying","securitycraft:reinforced_pink_stained_glass_pane_from_dye","cfm:brown_kitchen_counter","securitycraft:alarm","mcwfurnitures:oak_counter","rftoolsutility:charged_porter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","mcwroofs:oak_top_roof","create:crafting/appliances/netherite_backtank_from_netherite","utilitix:crude_furnace","additionallanterns:amethyst_lantern","cfm:crimson_kitchen_sink_light","mcwwindows:stone_window","utilitix:comparator_redirector_down","mcwroofs:gray_steep_roof","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","sophisticatedstorage:jukebox_upgrade","mcwfurnitures:stripped_oak_modern_wardrobe","mcwfences:railing_nether_brick_wall","mcwroofs:yellow_terracotta_upper_lower_roof","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","minecraft:heavy_weighted_pressure_plate","cfm:black_trampoline","cfm:stripped_oak_crate","additionallanterns:gold_lantern","immersiveengineering:crafting/wirecutter","mcwdoors:oak_bark_glass_door","allthemodium:smithing/vibranium_sword_smithing","mcwbiomesoplenty:stripped_willow_log_four_window","ad_astra:small_cyan_industrial_lamp","simplylight:illuminant_orange_block_dyed","minecraft:polished_blackstone_stairs","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","minecraft:dropper","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwdoors:oak_japanese2_door","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","utilitarian:utility/oak_logs_to_slabs","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","cfm:stripped_dark_oak_kitchen_sink_light","travelersbackpack:netherite","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","minecraft:shield","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","minecraft:cyan_stained_glass_pane_from_glass_pane","pneumaticcraft:minigun_upgrade","dyenamics:wine_candle","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwbridges:balustrade_end_stone_bricks_bridge","minecraft:quartz_bricks","cfm:blue_kitchen_drawer","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","mcwbridges:prismarine_bricks_bridge_stair","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","minecraft:iron_nugget_from_smelting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","securitycraft:motion_activated_light","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","ae2:network/cables/dense_smart_fluix","mcwroofs:white_terracotta_top_roof","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","sophisticatedbackpacks:pickup_upgrade","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","cfm:stripped_oak_kitchen_drawer","minecraft:bone_meal","mcwfences:oak_picket_fence","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwroofs:light_gray_terracotta_attic_roof","botania:brewery","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","botania:blood_pendant","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","mcwwindows:metal_four_window","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","minecraft:honeycomb_block","cfm:lime_kitchen_drawer","mcwbiomesoplenty:redwood_pane_window","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","eccentrictome:tome","securitycraft:reinforced_warped_fence","aether:aether_saddle","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","railcraft:steel_boots","mcwlights:pink_lamp","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","allthemodium:allthemodium_nugget_from_ingot","botania:mining_ring","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","minecraft:dye_cyan_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwbridges:prismarine_bricks_bridge_pier","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","travelersbackpack:dragon","allthecompressed:compress/oak_planks_1x","twigs:bloodstone","mcwwindows:warped_planks_window","sophisticatedbackpacks:chipped/loom_table_upgrade","cfm:orange_kitchen_counter","supplementaries:bamboo_spikes","dyenamics:bed/persimmon_bed","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","dyenamics:icy_blue_terracotta","dyenamics:cherenkov_stained_glass","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","alltheores:steel_rod","bigreactors:blasting/graphite_from_coal","botania:astrolabe","sophisticatedbackpacks:stack_upgrade_tier_1","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","supplementaries:flower_box","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:light_gray_terracotta","dyenamics:amber_stained_glass","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:spring_green_dye","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","botania:manasteel_shears","minecraft:glass_bottle","mcwfences:mesh_metal_fence","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","mcwlights:green_lamp","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","mcwwindows:prismarine_pane_window","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","blue_skies:trough","botania:lens_normal","mcwfences:end_brick_pillar_wall","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","mcwroofs:light_gray_upper_lower_roof","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","minecraft:quartz_slab","mcwroofs:cyan_terracotta_upper_steep_roof","handcrafted:phantom_trophy","modularrouters:modular_router","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","minecraft:quartz_pillar_from_quartz_block_stonecutting","railcraft:steel_helmet","mcwroofs:nether_bricks_roof","cfm:stripped_spruce_mail_box","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","mcwwindows:jungle_louvered_shutter","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwwindows:birch_log_parapet","rftoolsutility:button_module","minecraft:charcoal","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","supplementaries:bellows","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","handcrafted:kitchen_hood","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","mcwwindows:crimson_blinds","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","mcwroofs:black_lower_roof","mcwfurnitures:stripped_oak_desk","mcwroofs:gutter_base_blue","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwtrpdoors:oak_four_panel_trapdoor","pneumaticcraft:volume_upgrade","travelersbackpack:netherite_tier_upgrade","botania:travel_belt","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","additionallanterns:gold_chain","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwwindows:dark_prismarine_pane_window","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:end_stone_brick_slab_from_end_stone_brick_stonecutting","minecraft:iron_sword","aquaculture:sushi","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","botania:runic_altar","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:jacaranda_pyramid_gate","twilightforest:canopy_boat","botania:quartz_red","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","mcwroofs:gutter_base_lime","minecraft:prismarine_brick_stairs_from_prismarine_stonecutting","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","botania:abstruse_platform","mcwfences:crimson_stockade_fence","mcwbiomesoplenty:jacaranda_four_window","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","mcwwindows:mangrove_blinds","mcwwindows:dark_oak_plank_window","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","cfm:oak_kitchen_sink_light","mcwwindows:diorite_pane_window","utilitix:tiny_coal_from_tiny","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwbridges:end_stone_bricks_bridge","mcwroofs:light_gray_terracotta_steep_roof","mcwfurnitures:oak_desk","aether:stone_pickaxe_repairing","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","mcwwindows:deepslate_window","minecraft:gray_terracotta","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwwindows:white_mosaic_glass_pane","minecraft:netherite_boots_smithing","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:prismarine_brick_lower_roof","dyenamics:peach_terracotta","minecraft:fishing_rod","xnet:connector_yellow_dye","delightful:knives/silver_knife","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:yellow_lamp","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","supplementaries:candle_holders/candle_holder_cyan","supplementaries:timber_frame","aether:diamond_pickaxe_repairing","blue_skies:lunar_bookshelf","mcwroofs:cyan_terracotta_top_roof","mcwroofs:brown_terracotta_roof","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","mcwlights:double_street_lamp","minecraft:tnt","minecolonies:chainmailboots","additionallanterns:quartz_lantern","minecraft:flint_and_steel","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","xnet:advanced_connector_red_dye","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","rftoolsbase:tablet","mcwroofs:magenta_terracotta_lower_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","connectedglass:borderless_glass_cyan_pane2","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","supplementaries:item_shelf","handcrafted:terracotta_plate","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","mcwtrpdoors:oak_blossom_trapdoor","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","silentgear:coating_smithing_template","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","minecraft:glass_pane","supplementaries:timber_brace","allthearcanistgear:unobtainium_robes_smithing","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","farmersdelight:netherite_knife_smithing","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","mcwbiomesoplenty:pine_pane_window","enderio:wood_gear","supplementaries:bed_from_feather_block","cfm:red_kitchen_counter","dyenamics:cherenkov_terracotta","mcwwindows:oak_louvered_shutter","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","silentgear:stone_rod","minecraft:leather_boots","create:crafting/appliances/netherite_diving_boots_from_netherite","mcwbridges:oak_log_bridge_middle","immersiveengineering:crafting/stick_steel","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","utilitix:highspeed_rail","dyenamics:conifer_candle","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","mcwlights:pink_paper_lamp","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","tombstone:white_marble","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","mcwwindows:crimson_stem_parapet","minecraft:purple_terracotta","mcwbiomesoplenty:mahogany_highley_gate","domum_ornamentum:magenta_floating_carpet","nethersdelight:nether_brick_smoker","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","aether:book_of_lore","railcraft:steel_pickaxe","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","aether:aether_iron_nugget_from_smelting","sfm:cable","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:white_upper_steep_roof","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","minecraft:end_stone_bricks_from_end_stone_stonecutting","botania:phantom_ink","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","mcwfences:mangrove_highley_gate","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","mythicbotany:alfsteel_pylon","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","mcwwindows:bricks_window","railcraft:goggles","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","minecraft:blaze_powder","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","domum_ornamentum:blue_floating_carpet","alltheores:osmium_rod","minecraft:oak_fence","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","minecraft:prismarine_brick_stairs","mcwpaths:stone_flagstone_slab","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","reliquary:uncrafting/redstone","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","mcwroofs:magenta_terracotta_upper_steep_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","minecraft:netherite_block","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","mcwwindows:blue_mosaic_glass_pane","travelersbackpack:wither","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","mcwroofs:pink_terracotta_top_roof","cfm:green_kitchen_counter","minecraft:crossbow","mcwfurnitures:stripped_oak_modern_chair","pneumaticcraft:coordinate_tracker_upgrade","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwwindows:orange_mosaic_glass_pane","mcwbiomesoplenty:hellbark_curved_gate","minecraft:green_terracotta","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","securitycraft:taser","allthemodium:vibranium_nugget_from_ingot","supplementaries:soap","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","minecraft:purpur_stairs","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","securitycraft:reinforced_andesite","aether:aether_tune_enchanting","supplementaries:altimeter","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","cfm:red_kitchen_drawer","minecraft:quartz_stairs","botania:manasteel_shovel","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwroofs:white_steep_roof","minecraft:purpur_slab","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwlights:jungle_tiki_torch","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","sophisticatedbackpacks:tool_swapper_upgrade","minecraft:gold_nugget","bigreactors:energizer/controller","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","botania:super_travel_belt","minecraft:lime_terracotta","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","handcrafted:bench","mcwwindows:diorite_four_window","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","travelersbackpack:backpack_tank","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","aether:netherite_chestplate_repairing","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","mcwwindows:stripped_birch_log_four_window","additionallanterns:netherite_chain","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","silentgear:upgrade_base","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","ae2:network/cables/smart_cyan","handcrafted:pufferfish_trophy","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","minecraft:end_stone_brick_wall_from_end_stone_brick_stonecutting","handcrafted:oak_dining_bench","mcwfences:modern_mud_brick_wall","allthemodium:smithing/allthemodium_chestplate_smithing","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","dyenamics:aquamarine_dye","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","croptopia:cheeseburger","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwroofs:green_terracotta_steep_roof","mcwdoors:garage_silver_door","securitycraft:reinforced_red_stained_glass_pane_from_glass","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","additionallanterns:red_nether_bricks_lantern","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","tombstone:dark_marble","minecraft:iron_shovel","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","domum_ornamentum:white_floating_carpet","twilightforest:iron_ladder","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","cfm:fridge_light","supplementaries:gold_trapdoor","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","enderio:basic_fluid_filter","rftoolsutility:counter_module","aether:netherite_boots_repairing","minecraft:oak_door","biomesoplenty:jacaranda_boat","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:black_roof_block","connectedglass:clear_glass_cyan_pane2","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","sophisticatedbackpacks:smelting_upgrade","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","handcrafted:oak_corner_trim","cfm:spruce_kitchen_drawer","mcwbiomesoplenty:magic_highley_gate","minecraft:dark_prismarine","botania:balance_cloak","simplemagnets:basic_demagnetization_coil","dyenamics:mint_terracotta","mcwlights:soul_jungle_tiki_torch","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","framedblocks:framed_cube","immersiveengineering:crafting/connector_structural","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","mcwbiomesoplenty:flowering_oak_hedge","utilitarian:utility/logs_to_bowls","supplementaries:candle_holders/candle_holder_magenta","mcwfences:oak_curved_gate","minecraft:bow","mcwroofs:blue_terracotta_upper_steep_roof","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","botania:super_lava_pendant","bigreactors:energizer/chargingport_fe","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","cfm:purple_kitchen_sink","cfm:black_cooler","supplementaries:sconce","silentgear:rough_rod","minecraft:unobtainium_spell_book_smithing","mcwwindows:jungle_plank_parapet","minecraft:skull_banner_pattern","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:prismarine_brick_roof","botania:tiny_planet","mcwbiomesoplenty:palm_wired_fence","railcraft:iron_tunnel_bore_head","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","sophisticatedbackpacks:feeding_upgrade","minecraft:granite","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","cfm:dye_cyan_picket_fence","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","aether:netherite_shovel_repairing","mcwfurnitures:oak_coffee_table","mcwbridges:balustrade_nether_bricks_bridge","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","securitycraft:reinforced_acacia_fence_gate","cfm:lime_trampoline","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","mcwwindows:deepslate_pane_window","botania:quartz_blaze","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","dyenamics:rose_terracotta","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","minecraft:stone","twigs:lamp","dyenamics:bed/lavender_bed","cfm:lime_kitchen_counter","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","minecraft:dye_cyan_bed","blue_skies:glowing_poison_stone","tombstone:carmin_marble","mcwroofs:brown_terracotta_steep_roof","mcwdoors:oak_barn_door","botania:alchemy_catalyst","allthemodium:ancient_stone_bricks","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","minecraft:purpur_slab_from_purpur_block_stonecutting","mcwwindows:cherry_plank_pane_window","minecraft:nether_brick_fence","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","botania:lava_pendant","minecolonies:chainmailleggings","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","mcwwindows:blue_mosaic_glass","additionallanterns:obsidian_chain","minecraft:ender_eye","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","mcwlights:brown_lamp","mcwwindows:bricks_four_window","railcraft:receiver_circuit","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_mangrove_fence_gate","cfm:mangrove_kitchen_drawer","silentgear:blaze_gold_ingot","enderio:wood_gear_corner","rftoolsbase:manual","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","sophisticatedbackpacks:smoking_upgrade","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","securitycraft:trophy_system","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","mcwlights:warped_tiki_torch","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","securitycraft:keycard_lv5","reliquary:uncrafting/gunpowder_witch_hat","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","minecraft:gold_nugget_from_smelting","dyenamics:wine_stained_glass","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","computercraft:turtle_advanced_overlays/turtle_trans_overlay","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","mcwwindows:birch_plank_parapet","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","dyenamics:spring_green_candle","mcwbiomesoplenty:dead_highley_gate","mcwroofs:oak_planks_top_roof","biomesoplenty:hellbark_boat","railcraft:steel_chestplate","mcwfences:oak_hedge","mcwroofs:oak_planks_roof","enderio:fluid_tank","mcwpaths:stone_flagstone","bigreactors:energizer/casing","simplylight:illuminant_block_toggle","minecraft:nether_brick_slab","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","minecraft:enchanting_table","botania:mana_tablet","cfm:birch_mail_box","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deeperdarker:reinforced_echo_shard","mcwlights:brown_paper_lamp","minecraft:campfire","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","mcwbiomesoplenty:maple_highley_gate","reliquary:mob_charm_fragments/witch","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:6559,warning_level:0}}