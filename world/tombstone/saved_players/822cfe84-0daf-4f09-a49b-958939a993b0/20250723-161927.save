{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:1b,Amplifier:4b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:345,Id:11,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:resistance"},{Ambient:1b,Amplifier:5b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:345,Id:10,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:regeneration"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:209,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"},{Ambient:1b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:345,Id:46,ShowIcon:0b,ShowParticles:0b,"forge:id":"attributeslib:flying"}],Air:300s,Attributes:[{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"attributeslib:armor_shred"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:20.0d,Modifiers:[{Amount:10.0d,Name:"artifacts:crystal_heart_health_bonus",Operation:0,UUID:[I;-**********,-**********,-**********,-**********]},{Amount:4.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:1.0d,Name:"irons_spellbooks:cast_time_reduction"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"effect.attributeslib.flying 0",Operation:0,UUID:[I;-363375228,**********,-**********,**********]}],Name:"attributeslib:creative_flight"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.05d,Name:"attributeslib:crit_chance"},{Base:1.0d,Name:"forge:swim_speed"},{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Name:"attributeslib:healing_received"},{Base:0.0d,Name:"attributeslib:overheal"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:1.0d,Name:"eidolon:magic_power"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"attributeslib:cold_damage"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"allthemodium:mining",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{glyph0:"ars_nouveau:glyph_accelerate",size:1}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-**********,-**********,-**********,-**********],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:crystal_heart"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"ironjetpacks:jetpack",tag:{Energy:0,Engine:0b,Hover:1b,Id:"ironjetpacks:creative",Throttle:1.0d}}],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:crystal_heart"},{Count:1b,Slot:1,id:"artifacts:crystal_heart"}],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:lucky_scarf"}],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:netherite_backpack",tag:{contentsUuid:[I;985625898,1208829902,-2095988485,745459714],inventorySlots:120,openTabId:1,renderInfo:{upgradeItems:[{Count:1b,id:"sophisticatedbackpacks:advanced_magnet_upgrade",tag:{enabled:0b}},{Count:1b,id:"sophisticatedbackpacks:advanced_feeding_upgrade",tag:{feedAtHungerLevel:"any",filters:{Items:[{Count:1b,Slot:1,id:"artifacts:eternal_steak"}],Size:16},isAllowList:1b,primaryMatch:"item"}},{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_tier_4"},{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_tier_4"},{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_tier_4"},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}]},upgradeSlots:7}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"ae2wtlib:wireless_universal_terminal",tag:{accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},blankPattern:[{Count:13b,Slot:0,id:"ae2:blank_pattern"}],crafting:1b,currentTerminal:"crafting",encodedInputs:[{"#":1L,"#c":"ae2:i",id:"minecraft:stone_bricks"},{"#":1L,"#c":"ae2:i",id:"minecraft:stone_bricks"},{"#":1L,"#c":"ae2:i",id:"minecraft:stone_bricks"},{},{"#":1L,"#c":"ae2:i",id:"waystones:warp_stone",tag:{Damage:0}},{},{"#":1L,"#c":"ae2:i",id:"minecraft:obsidian"},{"#":1L,"#c":"ae2:i",id:"minecraft:obsidian"},{"#":1L,"#c":"ae2:i",id:"minecraft:obsidian"}],encodedOutputs:[{"#":1L,"#c":"ae2:i",id:"waystones:sharestone"}],ex_pattern_access:1b,filter_type:"ALL",internalCurrentPower:4800000.0d,internalMaxPower:4800000.0d,magnet_settings:{magnetMode:0b},mode:"CRAFTING",pattern_encoding:1b,show_pattern_providers:"VISIBLE",sort_by:"AMOUNT",sort_direction:"DESCENDING",stonecuttingRecipeId:"minecraft:chiseled_stone_bricks_stone_from_stonecutting",substitute:1b,substituteFluids:1b,upgrades:[{Count:1b,Slot:0,id:"ae2wtlib:magnet_card"},{Count:1b,Slot:1,id:"ae2insertexportcard:export_card",tag:{SelectedInventorySlots:[I;0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],upgrades:[{Count:1b,Slot:0,id:"ae2:speed_card"},{Count:1b,Slot:1,id:"ae2:crafting_card"}]}}],view_mode:"ALL"}}],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"allthewizardgear:unobtainium_spell_book",tag:{ISB_Spells:{data:[{id:"irons_spellbooks:echoing_strikes",index:0,level:5,locked:0b},{id:"irons_spellbooks:charge",index:1,level:3,locked:0b},{id:"irons_spellbooks:teleport",index:2,level:5,locked:0b},{id:"irons_spellbooks:acupuncture",index:3,level:9,locked:0b},{id:"irons_spellbooks:thunderstorm",index:4,level:8,locked:0b},{id:"irons_spellbooks:black_hole",index:5,level:6,locked:0b},{id:"irons_spellbooks:heartstop",index:6,level:10,locked:0b},{id:"irons_spellbooks:fortify",index:7,level:10,locked:0b}],maxSpells:15,mustEquip:1b,spellWheel:1b}}}],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:63,wirelessNetwork:4},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:1,furnaces:{furnace0:{X:-31,Y:253,Z:2}}},"ironfurnaces:show_config":{show:1},"irons_spellbooks:player_magic":{castingEquipmentSlot:"spellbook",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:2,lastIndex:5,lastSlot:"spellbook",slot:"spellbook"}},"l2library:conditionals":{data:{},tickSinceDeath:1406644},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:23652},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"productivebees:nests/nether_brick_nest",ItemStack:{Count:1b,id:"productivebees:nether_brick_nest"}}],SelectedRecipe:"productivebees:nests/nether_brick_nest"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:57,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:1b,isshrunk:0b,scale:0.5999999f,width:0.0f},"solcarrot:food":{foodList:["croptopia:calamari","minecraft:cooked_porkchop","artifacts:eternal_steak","minecraft:cooked_chicken","minecraft:golden_apple","forbidden_arcanus:tentacle","minecraft:bread","minecraft:baked_potato","ars_elemental:flashpine_pod"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:240s,knowledge:42,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{"BM:CurrentIncense":0.0d,"BM:MaxIncenseFromLastAltar":0.2d,CreateNetheriteDivingBits:3b,MythicBotanyPlayerInfo:{MimirKnowledge:1b},PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:overworld",FromPos:96207268921409L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;*********,*********,-1093180692,-*********]},{FromDim:"minecraft:the_nether",FromPos:12919261773922L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;1263394328,1022381903,-1397748416,*********]},{FromDim:"minecraft:overworld",FromPos:-9070971117498L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;-1223223403,2050180311,-1471092364,-720128879]},{FromDim:"minecraft:the_nether",FromPos:14293651308643L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-244014937,-1787607380,-1589206369,-1904927129]},{FromDim:"minecraft:overworld",FromPos:892528562249798L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:12919261773922L,UID:[I;1849276541,-865057272,-1530438419,-1713596833]},{FromDim:"minecraft:the_nether",FromPos:13469017571427L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:-6322192146365L,UID:[I;-743450201,725240014,-2140804546,-1060359424]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["88320f8d-ea59-4cc6-896d-79cc52d4aa80","ba59ed03-fc91-4f2d-9d54-fa7014c385fa","70e61f07-a350-437a-ae01-15e3ef9a1bab","36d86fd7-e43f-48b5-a2b9-6969a1ba515c","d1811b83-cc8c-4821-80c6-3e5ce576ae62","6c8881a8-dbdf-465b-bb3e-0c97f47d45a4","42d85d81-dfae-42b5-ab86-9ebce283d4c6","7ec92f04-1157-4f2a-a54c-4eb451158d8f","44305267-ff27-4e8f-8df6-4521174251c8","12598484-deba-416b-92ba-6606eb861918","f7479dcf-a487-494c-86a6-dc8476d92769"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:-40,tb_last_ground_location_y:78,tb_last_ground_location_z:-41,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},"aae$downkey":0b,"aae$nokey":1b,"aae$upkey":0b,"apoth.affix_cooldown.apotheosis:armor/mob_effect/blinding":12381719L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/bolstering":11463029L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/nimble":11463029L,"apoth.affix_cooldown.apotheosis:armor/mob_effect/revitalizing":11463029L,"apoth.affix_cooldown.apotheosis:sword/mob_effect/elusive":12251728L,apoth_reforge_seed:-**********,"quark:locked_once":1b,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedBackpackSettings:{},sophisticatedStorageSettings:{}},Health:54.0f,HurtByTimestamp:1400001,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"mekanism:atomic_disassembler",tag:{mekData:{EnergyContainers:[{Container:0b,stored:"1000000"}],mode:2}}},{Count:1b,Slot:1b,id:"draconicevolution:advanced_dislocator",tag:{blink:0b,fuel:712,locations:[{dim:"allthemodium:mining",heading:1b,lock:0b,name:"龙研",pitch:-13.489802f,x:-76.8226899930108d,y:253.0d,yaw:90.85258f,z:-67.30273597826825d},{dim:"allthemodium:mining",heading:1b,lock:0b,name:"Home",pitch:-14.273374f,x:-20.134841679590522d,y:253.0d,yaw:-41.0531f,z:-24.607846590509997d},{dim:"bloodmagic:dungeon",heading:1b,lock:0b,name:"-887 107 922",pitch:18.44117f,x:-887.5165896314352d,y:107.0d,yaw:145.594f,z:922.9814010166655d},{dim:"allthemodium:the_beyond",heading:1b,lock:0b,name:"Beyond",pitch:-11.345164f,x:-10154.903824812944d,y:61.0d,yaw:97.88904f,z:12041.177385654893d},{dim:"aether:the_aether",heading:1b,lock:0b,name:"Aether",pitch:-7.1935945f,x:-12.900022496938242d,y:122.0d,yaw:38.886078f,z:-67.50748698595618d},{dim:"minecraft:the_end",heading:1b,lock:0b,name:"End",pitch:-7.386873f,x:74.26933567172941d,y:58.0d,yaw:-25.231232f,z:9.768668428253266d},{dim:"minecraft:the_end",heading:1b,lock:0b,name:"endman",pitch:-12.828438f,x:-380.388376749449d,y:1.0d,yaw:58.078003f,z:-6.243998653552193d},{dim:"minecraft:the_nether",heading:1b,lock:0b,name:"Hell",pitch:10.818032f,x:49.932365586803556d,y:99.0d,yaw:178.2978f,z:32.09299542069718d},{dim:"allthemodium:the_other",heading:1b,lock:0b,name:"The Other",pitch:24.978199f,x:-1117.2444185278869d,y:216.0d,yaw:4.6787505f,z:-2077.9660253217216d},{dim:"minecraft:overworld",heading:1b,lock:0b,name:"主世界",pitch:-4.964909f,x:-26.58106273915268d,y:67.0d,yaw:-174.99971f,z:-60.103805809712405d},{dim:"minecraft:overworld",heading:1b,lock:0b,name:"snows",pitch:17.258091f,x:2493.3265887898015d,y:66.0d,yaw:94.26825f,z:-490.2947561934547d},{dim:"minecraft:overworld",heading:1b,lock:0b,name:"photom",pitch:31.706413f,x:-1671.6242141654104d,y:243.0d,yaw:-153.44302f,z:-1520.468928625713d},{dim:"minecraft:overworld",heading:1b,lock:0b,name:"ancient factory",pitch:31.169313f,x:-6197.724966669454d,y:-15.040358684719278d,yaw:76.93164f,z:-5805.317250572097d},{dim:"minecraft:overworld",heading:1b,lock:0b,name:"dark temple",pitch:-2.1139965f,x:-926.555596938422d,y:76.0d,yaw:109.074646f,z:-973.0116751051952d},{dim:"allthemodium:mining",heading:1b,lock:0b,name:"蜜蜂",pitch:-8.428856f,x:56.283251029742786d,y:253.0d,yaw:-25.812948f,z:26.75876506985726d}],selected:1}},{Count:57b,Slot:2b,id:"productivebees:advanced_oak_beehive"},{Count:45b,Slot:3b,id:"productivebees:bee_cage"},{Count:2b,Slot:5b,id:"minecraft:white_concrete"},{Count:9b,Slot:8b,id:"productivebees:gene",tag:{productivebees_gene_attribute:"productivebees:redstone",productivebees_gene_purity:100,productivebees_gene_value:0}},{Count:1b,Slot:9b,id:"tiab:time_in_a_bottle",tag:{storedTime:949920,totalAccumulatedTime:1827720}},{Count:1b,Slot:10b,id:"ae2:network_tool",tag:{inv:[{Count:28b,Slot:0,id:"ae2:crafting_card"},{Count:47b,Slot:1,id:"ae2:speed_card"},{Count:64b,Slot:3,id:"ae2:speed_card"},{Count:64b,Slot:4,id:"ae2:speed_card"},{Count:64b,Slot:5,id:"ae2:speed_card"},{Count:64b,Slot:6,id:"appflux:induction_card"},{Count:50b,Slot:7,id:"appflux:induction_card"}]}},{Count:32b,Slot:14b,id:"productivebees:honey_treat"},{Count:1b,Slot:27b,id:"productivebees:bee_cage",tag:{Age:0,AgeLocked:0b,AngerTime:0,BalmData:{},Bukkit.Aware:1b,Bukkit.updateLevel:2,CanUpdate:1b,ForcedAge:0,ForgeCaps:{"aether:mob_accessory":{DropChances:{aether_gloves:0.085f,aether_pendant:0.085f,hands:0.085f,necklace:0.085f}},"blueflame:blue_flame_on":{isOnFire:0b},"botania:loonium_drop":{},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;1471309323,1744652024,-1635708131,2108813187],hasTentacle:0b},"curios:inventory":{Curios:[]},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mekanism:radiation":{radiation:1.0E-7d},"pneumaticcraft:hacking":{},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{"naturesaura:time_alive":160},HasConverted:0b,HasNectar:0b,HasStung:0b,Health:15.0f,Invulnerable:0b,MaxHealth:20.0f,PersistenceRequired:0b,Spigot.ticksLived:180,"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":0L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bee_behavior:2,bee_endurance:3,bee_productivity:3,bee_temper:1,bee_type:"hive",bee_weather_tolerance:2,breathCollectionCooldown:600,entity:"productivebees:configurable_bee",isProductiveBee:1b,mod:"ProductiveBees",name:"Redstone Bee",type:"productivebees:redstone"}},{Count:1b,Slot:28b,id:"productivebees:bee_cage",tag:{Age:0,AgeLocked:0b,AngerTime:0,BalmData:{},Bukkit.Aware:1b,Bukkit.updateLevel:2,CanUpdate:1b,ForcedAge:0,ForgeCaps:{"aether:mob_accessory":{DropChances:{aether_gloves:0.085f,aether_pendant:0.085f,hands:0.085f,necklace:0.085f}},"blueflame:blue_flame_on":{isOnFire:0b},"botania:loonium_drop":{},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-1643765545,956320331,-1588141187,1327414606],hasTentacle:0b},"curios:inventory":{Curios:[]},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mekanism:radiation":{radiation:1.0E-7d},"pneumaticcraft:hacking":{},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{"naturesaura:time_alive":320},HasConverted:0b,HasNectar:0b,HasStung:0b,Health:7.0f,Invulnerable:0b,MaxHealth:20.0f,PersistenceRequired:0b,Spigot.ticksLived:327,"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":0L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bee_behavior:2,bee_endurance:3,bee_productivity:3,bee_temper:1,bee_type:"hive",bee_weather_tolerance:2,breathCollectionCooldown:600,entity:"productivebees:configurable_bee",isProductiveBee:1b,mod:"ProductiveBees",name:"Redstone Bee",type:"productivebees:redstone"}},{Count:8b,Slot:31b,id:"waystones:orange_sharestone"},{Count:1b,Slot:100b,id:"advanced_ae:quantum_boots",tag:{Damage:0,accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},charging_tag:{enabled:1b,filter:[],value:1},evasion_tag:{enabled:1b,filter:[],value:30},flight_drift_tag:{enabled:1b,filter:[],value:50},internalCurrentPower:2.0E8d,jump_height_tag:{enabled:1b,filter:[],value:3},step_assist_tag:{enabled:1b,filter:[],value:3}}},{Count:1b,Slot:101b,id:"advanced_ae:quantum_leggings",tag:{Damage:0,accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},charging_tag:{enabled:1b,filter:[],value:1},internalCurrentPower:2.5E8d,reach_tag:{enabled:1b,filter:[],value:5},sprint_speed_tag:{enabled:1b,filter:[],value:80},swim_speed_tag:{enabled:1b,filter:[],value:80},walk_speed_tag:{enabled:1b,filter:[],value:60}}},{Count:1b,Slot:102b,id:"advanced_ae:quantum_chestplate",tag:{Damage:0,accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},attack_speed_tag:{enabled:1b,filter:[],value:5},charging_tag:{enabled:1b,filter:[],value:1},flight_tag:{enabled:1b,filter:[],value:10},hp_buffer_tag:{enabled:1b,filter:[],value:20},internalCurrentPower:3.0E8d,lava_immunity_tag:{enabled:1b,filter:[],value:1},pick_craft_tag:{enabled:1b,filter:[],value:1},regeneration_tag:{enabled:1b,filter:[],value:1},strength_tag:{enabled:1b,filter:[],value:10}}},{Count:1b,Slot:103b,id:"advanced_ae:quantum_helmet",tag:{Damage:0,accessPoint:{dimension:"allthemodium:mining",pos:[I;-20,268,-23]},auto_feed_tag:{enabled:1b,filter:[],value:1},auto_stock_tag:{enabled:1b,filter:[],value:1},charging_tag:{enabled:1b,filter:[],value:1},internalCurrentPower:2.0E8d,luck_tag:{enabled:1b,filter:[],value:2},night_vision_on:1b,night_vision_tag:{enabled:1b,filter:[],value:1},water_breathing_tag:{enabled:1b,filter:[],value:1}}}],Invulnerable:0b,LastDeathLocation:{dimension:"allthemodium:mining",pos:[I;-7,254,-31]},Motion:[0.0d,0.0d,0.0d],OnGround:0b,PortalCooldown:0,Pos:[50.05221054598611d,257.69272041644695d,9.00965614975799d],Railways_DataVersion:2,Rotation:[157.33813f,54.05257f],Score:212854,SelectedItemSlot:7,SleepTimer:0s,SpawnAngle:-86.18854f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:-20,SpawnY:66,SpawnZ:-66,Spigot.ticksLived:1406640,UUID:[I;-2110980476,229592841,-1533307511,967414704],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":12094627954941L,WorldUUIDLeast:-4805342176547869332L,WorldUUIDMost:-3961303466219846528L,XpLevel:487,XpP:0.92615384f,XpSeed:1251721492,XpTotal:994256,abilities:{flySpeed:0.05f,flying:1b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752067849939L,keepLevel:0b,lastKnownName:"Ales_winter",lastPlayed:1753258767655L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:3.0585847f,foodLevel:20,foodSaturationLevel:9.8f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["botania:dreamwood_planks","botania:star_sword","mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","croptopia:mead","botania:alfheim_portal","mcwfurnitures:stripped_jungle_desk","arseng:source_storage_cell_256k","biomesoplenty:fir_boat","mcwwindows:birch_four_window","pneumaticcraft:chunkloader_upgrade","refinedstorage:coloring_recipes/red_detector","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","refinedstorage:coloring_recipes/pink_security_manager","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","minecraft:bone_block","refinedstorage:coloring_recipes/pink_controller","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","refinedstorage:coloring_recipes/red_pattern_grid","croptopia:shaped_beef_stew","ae2:network/cables/dense_smart_green","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","ae2:network/cells/item_storage_cell_256k_storage","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","mcwtrpdoors:acacia_barrel_trapdoor","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","minecraft:gold_nugget_from_blasting","minecolonies:cheese_ravioli","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","minecolonies:chainmailchestplate","sophisticatedstorage:backpack_advanced_magnet_upgrade_from_storage_advanced_magnet_upgrade","mythicbotany:central_rune_holder","botania:missile_rod","sophisticatedbackpacks:void_upgrade","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","refinedstorage:coloring_recipes/gray_grid","mcwpaths:cobbled_deepslate_running_bond_slab","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","sophisticatedstorage:crimson_limited_barrel_2","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","mcwfurnitures:spruce_modern_desk","sophisticatedstorage:crimson_limited_barrel_1","mcwfurnitures:spruce_drawer","sophisticatedstorage:crimson_limited_barrel_4","bigreactors:turbine/inanite_block","sophisticatedstorage:crimson_limited_barrel_3","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","twigs:polished_rhyolite_stonecutting","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","utilitix:oak_shulker_boat_with_shell","create:crafting/kinetics/gearbox","botania:terrasteel_block","arseng:portable_source_cell_64k","mcwroofs:magenta_terracotta_roof","securitycraft:reinforced_cherry_fence_gate","minecraft:leather_helmet","rftoolsutility:screen_link","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","allthecompressed:compress/vibranium_allthemodium_alloy_block_1x","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","mcwfurnitures:stripped_crimson_covered_desk","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:bubble_blower","refinedstorage:coloring_recipes/white_disk_manipulator","mcwdoors:crimson_bamboo_door","supplementaries:crank","securitycraft:secret_bamboo_sign_item","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","allthecompressed:compress/diamond_block_6x","aether:skyroot_pickaxe_repairing","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","allthecompressed:compress/ancient_stone_1x","minecraft:unobtainium_mage_chestplate_smithing","supplementaries:netherite_trapdoor","productivebees:stonecutter/fir_canvas_expansion_box","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","refinedstorage:coloring_recipes/magenta_security_manager","minecraft:pink_terracotta","aiotbotania:livingrock_axe","refinedstorage:coloring_recipes/blue_crafting_grid","mcwbiomesoplenty:mahogany_plank_pane_window","allthecompressed:compress/diamond_block_5x","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwtrpdoors:spruce_barred_trapdoor","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwpaths:cobblestone_dumble_paving","cfm:spruce_upgraded_fence","mcwroofs:crimson_top_roof","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","dyenamics:aquamarine_concrete_powder","refinedstorage:coloring_recipes/fluid_grid","mcwroofs:crimson_planks_upper_steep_roof","nethersdelight:golden_machete","ae2:network/cables/smart_fluix_clean","ae2:network/parts/annihilation_plane_alt","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","ae2:network/blocks/energy_energy_cell","mcwwindows:acacia_curtain_rod","productivebees:hives/advanced_cherry_canvas_hive","mcwlights:chain_lantern","securitycraft:camera_monitor","allthecompressed:compress/quartz_block_1x","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","railcraft:standard_rail_from_rail","refinedstorage:coloring_recipes/brown_security_manager","allthecompressed:decompress/gravel_4x","twilightdelight:torchberry_pie","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","refinedstorage:coloring_recipes/white_grid","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","botania:vial","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","aether:netherite_hoe_repairing","minecraft:vibranium_spell_book_smithing","minecraft:golden_hoe","minecraft:fire_charge","cfm:oak_kitchen_counter","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:pink_petal_block","minecraft:dye_green_bed","cfm:stripped_spruce_bedside_cabinet","railcraft:any_detector","connectedglass:borderless_glass_purple2","alchemistry:reactor_energy","botania:mana_distributor","allthecompressed:compress/netherrack_7x","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","sophisticatedstorage:controller","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","minecraft:stone_button","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","artifacts:eternal_steak_furnace","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","aether:golden_pickaxe_repairing","ae2:network/wireless_terminal","pneumaticcraft:regulator_tube_module","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:stripped_dead_log_window2","pneumaticcraft:logistics_frame_active_provider_self","allthemodium:vibranium_allthemodium_alloy_ingot_from_block","minecraft:lodestone","occultism:crafting/magic_lamp_empty","mcwfurnitures:stripped_crimson_bookshelf","appbot:portable_mana_storage_cell_4k","mcwlights:black_lamp","minecraft:purpur_block","pneumaticcraft:assembly_io_unit_import_from_export","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","megacells:crafting/greater_energy_card","mcwdoors:spruce_four_panel_door","farmersdelight:potato_from_crate","create:crafting/kinetics/speedometer","minecraft:polished_blackstone_slab","cfm:stripped_spruce_chair","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","twilightforest:compressed_blocks/steeleaf_block","allthetweaks:nether_star_from_nether_star_block","refinedstorage:coloring_recipes/brown_pattern_grid","refinedstorage:coloring_recipes/yellow_fluid_grid","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","connectedglass:borderless_glass_red2","refinedstorage:coloring_recipes/purple_crafting_grid","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","productivebees:expansion_boxes/expansion_box_acacia_canvas","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwdoors:jungle_bark_glass_door","refinedstorage:coloring_recipes/pink_network_receiver","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","refinedstorage:coloring_recipes/white_detector","blue_skies:glowing_blinding_stone","ae2:tools/paintballs_light_blue","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","tombstone:grave_plate","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","twilightforest:equipment/fiery_boots","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","refinedstorage:coloring_recipes/red_grid","handcrafted:jungle_bench","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","ae2:network/parts/terminals","securitycraft:keypad_frame","minecolonies:pasta_tomato","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","twilightforest:equipment/fiery_fiery_helmet","bigreactors:turbine/basic/shaft","croptopia:hamburger","enderio:resetting_lever_thirty_inv_from_prev","botania:terrasteel_leggings","littlelogistics:barrel_barge","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","cfm:spruce_bedside_cabinet","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","pneumaticcraft:vortex_tube","refinedstorage:coloring_recipes/orange_wireless_transmitter","xnet:connector_routing","blue_skies:glowing_nature_stone","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:palm_plank_window2","travelersbackpack:cake","bloodmagic:ritual_diviner_1","megacells:network/mega_interface","mcwroofs:green_terracotta_attic_roof","productivebees:stonecutter/palm_canvas_expansion_box","securitycraft:reinforced_black_stained_glass_pane_from_glass","twilightforest:equipment/fiery_helmet","utilitix:directional_highspeed_rail","handcrafted:crimson_cupboard","mcwwindows:light_blue_curtain","refinedstorage:coloring_recipes/magenta_network_receiver","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","refinedstorage:coloring_recipes/brown_network_receiver","mcwfurnitures:stripped_spruce_double_drawer","securitycraft:reinforced_purple_stained_glass","ae2:network/crafting/cpu_crafting_monitor","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","refinedstorage:coloring_recipes/light_gray_fluid_grid","minecraft:cooked_chicken_from_smoking","create:crafting/kinetics/chute","refinedstorage:coloring_recipes/purple_crafter_manager","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","pneumaticcraft:assembly_platform","mcwroofs:white_terracotta_attic_roof","advanced_ae:quantum_boots_item_reset","ae2:network/blocks/storage_chest","securitycraft:secret_bamboo_hanging_sign","cfm:jungle_desk","mcwroofs:oak_roof","refinedstorage:coloring_recipes/yellow_crafter","cfm:stripped_crimson_chair","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwroofs:light_gray_roof_slab","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwroofs:thatch_roof","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","ae2:network/wireless_booster","ae2:tools/portable_fluid_cell_256k","dyenamics:fluorescent_wool","refinedstorage:coloring_recipes/blue_network_transmitter","additionallanterns:end_stone_lantern","connectedglass:clear_glass_light_blue2","cfm:mangrove_kitchen_sink_dark","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","ae2:tools/paintballs_orange","mcwfences:blackstone_brick_railing_gate","croptopia:crab_legs","sophisticatedbackpacks:filter_upgrade","mcwdoors:crimson_glass_door","forbidden_arcanus:arcane_crystal_dust","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","megacells:cells/portable/portable_fluid_cell_4m","ae2:network/cables/dense_covered_purple","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","bigreactors:crafting/blutonium_storage_to_component","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","productivebees:hives/advanced_oak_canvas_hive","ae2:decorative/quartz_fixture_from_anchors","additionallanterns:andesite_lantern","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","supplementaries:evilcraft/sign_post_undead","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","mcwlights:golden_chandelier","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","minecraft:armor_stand","allthecompressed:compress/unobtainium_vibranium_alloy_block_1x","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","mcwdoors:crimson_cottage_door","twigs:chiseled_bricks","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","croptopia:cheese","mcwdoors:crimson_classic_door","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","dyenamics:navy_wool","mcwwindows:magenta_mosaic_glass_pane","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","megacells:cells/portable/portable_fluid_cell_1m","mcwtrpdoors:metal_full_trapdoor","mcwroofs:spruce_top_roof","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","allthecompressed:compress/aluminum_block_1x","additionallanterns:iron_lantern","refinedstorage:coloring_recipes/black_detector","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","sophisticatedstorage:backpack_stack_upgrade_tier_4_from_storage_stack_upgrade_tier_5","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","reliquary:void_tear","mcwpaths:andesite_running_bond_slab","appbot:mana_cell_housing","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","minecraft:chiseled_stone_bricks_stone_from_stonecutting","croptopia:potato_chips","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","botania:red_string_fertilizer","minecraft:netherite_sword_smithing","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","botania:manasteel_axe","create:crafting/kinetics/mechanical_crafter","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","ae2:block_cutter/slabs/fluix_slab","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","dankstorage:4_to_5","delightful:food/baklava","oceansdelight:seagrass_salad","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","ae2:tools/paintballs_red","twilightforest:charm_of_life_2","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","dyenamics:dye_conifer_carpet","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","appmek:chemical_storage_cell_256k","mcwlights:cherry_tiki_torch","connectedglass:borderless_glass_pane3","croptopia:buttered_toast","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","cfm:pink_cooler","littlelogistics:junction_rail","refinedstorage:coloring_recipes/magenta_controller","dyenamics:ultramarine_terracotta","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","refinedstorage:coloring_recipes/red_relay","cfm:red_grill","ae2:tools/paintballs_gray","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","refinedstorage:coloring_recipes/red_crafter","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","croptopia:chicken_and_dumplings","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","botania:gray_petal_block","utilitix:tiny_charcoal_to_tiny","connectedglass:borderless_glass_light_blue2","mcwtrpdoors:jungle_paper_trapdoor","refinedstorage:coloring_recipes/magenta_crafting_grid","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","ae2:network/cells/fluid_storage_cell_256k","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","ae2additions:components/1024k","twigs:quartz_column","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","chemlib:calcium_block_to_ingot","minecraft:purpur_stairs_from_purpur_block_stonecutting","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","minecraft:crimson_trapdoor","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","supplementaries:sign_post_oak","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","immersiveengineering:crafting/toolbox","refinedstorage:coloring_recipes/brown_crafter_manager","refinedstorage:coloring_recipes/black_grid","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","botania:terrasteel_boots","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","twilightdelight:hydra_burger","refinedstorage:coloring_recipes/controller","chemlib:gallium_ingot_from_blasting_gallium_dust","mcwroofs:orange_concrete_top_roof","forbidden_arcanus:aureal_bottle","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwroofs:gray_attic_roof","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:crimson_planks","forbidden_arcanus:darkstone_pedestal","railcraft:blast_furnace_bricks","ae2:network/cables/dense_covered_pink","mcwfurnitures:crimson_modern_chair","ae2:network/cables/glass_brown","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","botania:red_string_relay","ae2:tools/portable_item_cell_256k","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","minecraft:flower_banner_pattern","mcwbiomesoplenty:empyreal_window","securitycraft:secret_crimson_sign_item","mcwfurnitures:spruce_stool_chair","mcwroofs:purple_terracotta_upper_lower_roof","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","ae2:network/parts/cable_anchor","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","blue_skies:cake_compat","minecraft:mangrove_planks","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwdoors:spruce_stable_head_door","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","productivebees:stonecutter/aspen_canvas_expansion_box","pneumaticcraft:liquid_hopper","refinedstorage:coloring_recipes/pink_fluid_grid","botania:green_pavement","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","ad_astra:ti_69","mcwwindows:window_half_bar_base","megacells:cells/portable/portable_item_cell_4m","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwfurnitures:stripped_crimson_coffee_table","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwdoors:crimson_stable_head_door","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:fried_chicken","bloodmagic:blood_rune_orb_2","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","botania:shimmerwood_planks","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime2","ae2:network/parts/terminals_crafting","megacells:cells/portable/portable_item_cell_1m","cfm:stripped_oak_chair","xnet:netcable_blue","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","cfm:rock_path","botania:mana_spreader","connectedglass:clear_glass_yellow2","minecraft:wooden_hoe","minecraft:cooked_beef_from_campfire_cooking","createoreexcavation:diamond_drill","minecraft:quartz_pillar","mcwbiomesoplenty:magic_plank_pane_window","refinedstorage:coloring_recipes/light_gray_network_transmitter","botania:slingshot","immersiveengineering:crafting/axe_steel","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","sophisticatedbackpacks:jukebox_upgrade","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","ae2:network/parts/energy_level_emitter","productivebees:stonecutter/jungle_canvas_hive","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","refinedstorage:coloring_recipes/yellow_network_receiver","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","pneumaticcraft:flux_compressor","mcwroofs:gutter_base_green","cfm:stripped_crimson_table","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","mcwwindows:crimson_planks_four_window","minecraft:piston","ad_astra:large_gas_tank","refinedstorage:coloring_recipes/orange_grid","mcwlights:white_lamp","mcwfurnitures:jungle_coffee_table","appmek:chemical_storage_cell_4k","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","undergarden:regalium_block","ae2:network/cells/item_storage_components_cell_16k_part","mcwbiomesoplenty:umbran_plank_pane_window","botania:virus_necrodermal","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","refinedstorage:coloring_recipes/magenta_network_transmitter","botania:mana_bomb","minecraft:wooden_axe","cfm:stripped_oak_desk","connectedglass:borderless_glass_light_gray2","refinedstorage:coloring_recipes/magenta_crafting_monitor","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwroofs:gray_upper_steep_roof","botania:third_eye","productivetrees:wood/brown_amber_wood","mcwwindows:metal_curtain_rod","refinedstorage:coloring_recipes/magenta_relay","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","forbidden_arcanus:edelwood_planks","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","croptopia:beef_jerky","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","forbidden_arcanus:deorum_trapdoor","silentgear:sinew_fiber","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","mcwtrpdoors:spruce_mystic_trapdoor","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","ad_astra:space_helmet","create:crafting/appliances/clipboard","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","cfm:stripped_birch_kitchen_sink_dark","refinedstorage:coloring_recipes/lime_crafting_grid","aether:skyroot_piston","refinedstorage:coloring_recipes/brown_crafting_monitor","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","appmek:chemical_storage_cell_1k","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","bigreactors:crafting/magentite_component_to_storage","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","productivebees:stonecutter/oak_canvas_expansion_box","expatternprovider:ex_io_port","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","farmersdelight:stove","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","refinedstorage:coloring_recipes/lime_detector","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","botania:petal_gray","occultism:blasting/iesnium_ingot_from_raw","utilitarian:utility/spruce_logs_to_pressure_plates","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","create:crafting/curiosities/minecart_coupling","mcwfences:granite_grass_topped_wall","botania:fire_rod","botania:petal_pink_double","handcrafted:crimson_counter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","undergarden:smelt_gloomper_leg","mcwbiomesoplenty:rainbow_birch_hedge","botania:spawner_mover","productivebees:expansion_boxes/expansion_box_crimson_canvas","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","mcwfurnitures:stripped_crimson_chair","xnet:antenna","mcwfurnitures:jungle_stool_chair","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","refinedstorage:coloring_recipes/white_crafter_manager","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","botania:bifrost_pane","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","botania:scorched_seeds","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","refinedstorage:coloring_recipes/pink_crafting_monitor","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","refinedstorage:coloring_recipes/pink_crafter_manager","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","securitycraft:secret_mangrove_hanging_sign","mcwpaths:andesite_square_paving","pneumaticcraft:pressure_gauge","pneumaticcraft:programmer","mcwfences:dark_oak_hedge","pylons:expulsion_pylon","botania:manasteel_chestplate","botania:magenta_shiny_flower","mcwfences:bamboo_highley_gate","mythicbotany:yggdrasil_branch","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","allthemodium:unobtainium_allthemodium_alloy_ingot_from_block","computercraft:pocket_computer_advanced","ae2:tools/paintballs_blue","mcwroofs:jungle_roof","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","ae2:tools/paintballs_lime","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","megacells:cells/cell_component_1m","mcwtrpdoors:crimson_mystic_trapdoor","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","create:crafting/kinetics/sequenced_gearshift","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","refinedstorage:coloring_recipes/blue_wireless_transmitter","refinedstorage:coloring_recipes/wireless_transmitter","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","croptopia:carnitas","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","everythingcopper:copper_hopper","minecolonies:potato_soup","botania:spark_upgrade_dispersive","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:banner/mint_banner","botania:dreamwood_wand","mcwfurnitures:crimson_bookshelf","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","refinedstorage:coloring_recipes/red_controller","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","alchemistry:fusion_chamber_controller","securitycraft:briefcase","mcwwindows:green_mosaic_glass","create:crafting/logistics/stockpile_switch","mcwbiomesoplenty:empyreal_four_window","handcrafted:crimson_side_table","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/smart_gray","ae2:network/cables/dense_covered_white","refinedstorage:coloring_recipes/green_crafting_monitor","mcwpaths:andesite_crystal_floor_stairs","modularrouters:distributor_module","botania:red_string_dispenser","domum_ornamentum:brown_bricks","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","cfm:red_cooler","botania:dreamwood_wall","refinedstorage:coloring_recipes/white_fluid_grid","securitycraft:reinforced_green_stained_glass_pane_from_dye","bigreactors:turbine/basic/blade","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","create:crafting/logistics/pulse_extender","xnet:facade","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","enderio:resetting_lever_five_from_inv","handcrafted:crimson_shelf","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_1","minecraft:allthemodium_spell_book_smithing","sophisticatedstorage:stack_upgrade_tier_2","sophisticatedstorage:stack_upgrade_tier_3","mcwwindows:stripped_acacia_log_four_window","utilitarian:utility/crimson_logs_to_trapdoors","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","ae2:decorative/quartz_fixture","sophisticatedstorage:stack_upgrade_tier_5","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","occultism:crafting/raw_iesnium_block","littlelogistics:automatic_switch_rail","railcraft:signal_capacitor_box","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","mcwfurnitures:crimson_wardrobe","refinedstorage:coloring_recipes/light_gray_crafting_grid","minecraft:brick_wall","nethersdelight:diamond_machete","utilitix:advanced_brewery","aether:netherite_helmet_repairing","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","laserio:logic_chip_raw","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","expatternprovider:cobblestone_cell","refinedstorage:coloring_recipes/red_disk_manipulator","minecraft:raw_iron_block","mcwwindows:stone_window2","simplylight:illuminant_purple_block_on_dyed","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","botania:blaze_block","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","productivetrees:planks/brown_amber_planks","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/yellow_grid","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwfurnitures:jungle_striped_chair","bloodmagic:smelting/blasting_ingot_from_raw_hellforged","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","pneumaticcraft:pneumatic_helmet","productivebees:stonecutter/magic_canvas_expansion_box","additionallanterns:diamond_lantern","mcwfurnitures:stripped_crimson_triple_drawer","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","refinedstorage:basic_processor","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bloodmagic:alchemy_table","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","enderio:void_chassis","refinedstorage:coloring_recipes/cyan_grid","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","expatternprovider:silicon_block","minecraft:item_frame","itemcollectors:basic_collector","minecraft:loom","enderio:resetting_lever_three_hundred_inv","ad_astra:steel_engine","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","handcrafted:crimson_table","mcwtrpdoors:oak_bark_trapdoor","botania:exchange_rod","minecraft:arrow","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","dyenamics:wine_wool","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","appbot:portable_mana_storage_cell_16k","cfm:pink_picket_fence","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","refinedstorage:coloring_recipes/black_crafting_grid","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","refinedstorage:coloring_recipes/white_wireless_transmitter","minecraft:polished_blackstone","computercraft:computer_advanced","utilitarian:utility/spruce_logs_to_stairs","advanced_ae:quantumaccel","refinedstorage:coloring_recipes/blue_relay","bloodmagic:smelting/ingot_from_raw_hellforged","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","botania:horn_snow","handcrafted:crimson_desk","reliquary:uncrafting/slime_ball","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","botania:gaia_spreader","mcwroofs:black_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","railways:crafting/smokestack_woodburner","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","refinedstorage:coloring_recipes/yellow_wireless_transmitter","mcwtrpdoors:oak_cottage_trapdoor","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:fluidizer/glass","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","aether:diamond_gloves_repairing","cfm:brown_cooler","xnet:netcable_red","delightful:knives/terra_knife","mcwfences:diorite_pillar_wall","appbot:portable_mana_storage_cell_1k","megacells:cells/portable/portable_item_cell_64m","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","productivebees:hives/advanced_snake_block_canvas_hive","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","utilitix:experience_crystal","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","minecraft:respawn_anchor","paraglider:paraglider","botania:petal_gray_double","mcwpaths:cobbled_deepslate_flagstone","expatternprovider:ebus_in","aether:golden_leggings_repairing","dankstorage:dock","sophisticatedstorage:shulker_box","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","mcwroofs:blue_striped_awning","mcwfurnitures:spruce_desk","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","refinedstorage:coloring_recipes/red_crafter_manager","refinedstorage:coloring_recipes/crafter","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","refinedstorage:coloring_recipes/crafter_manager","botania:fabulous_pool_upgrade","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","refinedstorage:coloring_recipes/green_fluid_grid","ae2:tools/paintballs_purple","mcwbiomesoplenty:jacaranda_picket_fence","mcwdoors:crimson_mystic_door","mcwfences:mangrove_hedge","mcwtrpdoors:spruce_swamp_trapdoor","sophisticatedbackpacks:compacting_upgrade","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cells/item_storage_cell_64k_storage","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","aether:aether_gold_nugget_from_blasting","mcwwindows:oak_window","mcwfurnitures:stripped_crimson_modern_wardrobe","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","ae2:network/cables/smart_brown","minecraft:coarse_dirt","alltheores:signalum_rod","create:crafting/kinetics/white_seat","chemlib:gallium_ingot_to_block","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwroofs:stone_bricks_steep_roof","mcwwindows:red_curtain","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","railcraft:coal_coke_block_from_coal_coke","mcwwindows:light_gray_mosaic_glass","mcwroofs:white_upper_lower_roof","minecraft:magenta_terracotta","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","refinedstorage:coloring_recipes/white_network_transmitter","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","minecraft:gold_ingot_from_gold_block","mcwdoors:print_whispering","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","refinedstorage:coloring_recipes/black_disk_manipulator","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gray_terracotta_attic_roof","utilitarian:utility/oak_logs_to_doors","dyenamics:dye_spring_green_carpet","mcwbridges:rope_crimson_bridge","railways:crafting/smokestack_diesel","mcwroofs:gutter_base_orange","aether:bow_repairing","mcwfurnitures:stripped_spruce_stool_chair","securitycraft:reinforced_light_gray_stained_glass","farmersdelight:cooking/pasta_with_mutton_chop","minecraft:chiseled_quartz_block","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwroofs:bricks_attic_roof","occultism:crafting/otherstone_slab","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","minecraft:crimson_door","connectedglass:borderless_glass_yellow2","mcwdoors:jungle_whispering_door","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","utilitarian:utility/crimson_logs_to_slabs","refinedstorage:coloring_recipes/cyan_crafter","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","create:crafting/logistics/display_link","ad_astra:airlock","mcwroofs:grass_steep_roof","cfm:pink_picket_gate","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","minecolonies:pottage","croptopia:egg_roll","connectedglass:borderless_glass_black2","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","create:crafting/kinetics/mechanical_mixer","refinedstorage:coloring_recipes/black_network_transmitter","mcwlights:mangrove_tiki_torch","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","minecraft:lime_dye","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","botania:quartz_sunny","mcwbiomesoplenty:stripped_redwood_log_window","create:framed_glass_from_glass_colorless_stonecutting","productivebees:stonecutter/maple_canvas_expansion_box","botania:terrasteel_helmet","botania:flighttiara_0","enderio:infinity_rod","railcraft:signal_lamp","refinedstorage:coloring_recipes/orange_crafter_manager","botania:infused_seeds","cfm:jungle_blinds","tombstone:bone_needle","minecraft:stone_brick_wall","forbidden_arcanus:deorum_pressure_plate","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","refinedstorage:coloring_recipes/pink_relay","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:decoration_table","forbidden_arcanus:deorum_chain","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","refinedstorage:coloring_recipes/black_wireless_transmitter","pneumaticcraft:paper_from_tag_filter","delightful:knives/draco_arcanus_knife","expatternprovider:epp_upgrade","allthemodium:vibranium_allthemodium_alloy_block","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","minecraft:cyan_dye","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwroofs:white_top_roof","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:gold_block","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","botania:floating_pure_daisy","handcrafted:jungle_counter","aquaculture:gold_fillet_knife","dyenamics:banner/navy_banner","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","allthecompressed:compress/unobtainium_allthemodium_alloy_block_1x","mythicbotany:wither_aconite_floating","minecraft:paper","ae2:network/cables/dense_smart_cyan","cfm:magenta_picket_fence","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","securitycraft:secret_mangrove_sign_item","refinedstorage:coloring_recipes/orange_disk_manipulator","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","botania:conversions/terrasteel_block_deconstruct","cfm:spruce_kitchen_counter","travelersbackpack:warden","cfm:oak_upgraded_gate","refinedstorage:coloring_recipes/red_fluid_grid","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","forbidden_arcanus:golden_orchid_seeds","botania:incense_plate","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","pneumaticcraft:micromissiles","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","pneumaticcraft:pneumatic_boots","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","refinedstorage:coloring_recipes/pink_crafter","productivebees:stonecutter/warped_canvas_hive","mcwdoors:spruce_nether_door","cfm:crimson_mail_box","pneumaticcraft:assembly_io_unit_export","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","twilightforest:wood/mossy_towerwood","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","mcwfences:acacia_curved_gate","botania:lime_petal_block","allthemodium:unobtainium_rod","bloodmagic:blood_rune_capacity_2","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","minecraft:cooked_rabbit","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","refinedstorage:coloring_recipes/cyan_crafting_grid","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","allthecompressed:compress/blazing_crystal_block_1x","alltheores:lead_rod","utilitix:stonecutter_cart","cfm:dye_green_picket_fence","refinedstorage:coloring_recipes/blue_controller","mcwwindows:oak_plank_window","ae2wtlib:wireless_universal_terminal/ce","refinedstorage:coloring_recipes/yellow_crafting_monitor","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","refinedstorage:coloring_recipes/security_manager","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwtrpdoors:crimson_glass_trapdoor","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","cfm:stripped_crimson_park_bench","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","additionallanterns:purpur_chain","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:light_gray_dye_from_oxeye_daisy","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","productivebees:hives/advanced_jungle_canvas_hive","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","ae2:network/cables/smart_purple","mcwtrpdoors:spruce_ranch_trapdoor","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","refinedstorage:coloring_recipes/magenta_crafter_manager","pneumaticcraft:wall_lamp_inverted_blue","supplementaries:flax_block_uncrafting","allthemodium:vibranium_plate","securitycraft:codebreaker","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:light_blue_picket_gate","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","constructionwand:infinity_wand","farmersdelight:golden_knife","minecraft:orange_stained_glass","bloodmagic:blood_rune_speed_2","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","minecraft:polished_blackstone_wall","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","farmersdelight:cooking/squid_ink_pasta","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","megacells:crafting/mega_energy_cell","cfm:blue_kitchen_counter","mcwbiomesoplenty:maple_four_window","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwroofs:base_top_roof","ae2:network/cables/smart_magenta","minecraft:golden_boots","securitycraft:harming_module","mcwdoors:crimson_swamp_door","mcwroofs:crimson_planks_upper_lower_roof","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","refinedstorage:coloring_recipes/green_security_manager","create:crafting/kinetics/item_vault","mythicbotany:mana_collector","pneumaticcraft:drone","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","megacells:network/cell_dock","supplementaries:flags/flag_cyan","supplementaries:statue","immersiveengineering:crafting/tinted_glass_lead_wire","reliquary:wraith_node","create:crafting/kinetics/clockwork_bearing","minecraft:dye_green_carpet","botania:diva_charm","cfm:pink_grill","mcwroofs:gutter_middle_orange","undergarden:torch_ditchbulb_paste","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","forbidden_arcanus:arcane_edelwood_planks","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","mcwroofs:jungle_attic_roof","pneumaticcraft:assembly_io_unit_export_from_import","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","utilitix:diamond_shears","minecraft:wooden_pickaxe","handcrafted:crimson_corner_trim","supplementaries:clock_block","securitycraft:secret_jungle_sign_item","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","ae2:network/crafting/cpu_crafting_accelerator","mcwbiomesoplenty:fir_stockade_fence","alltheores:copper_plate","refinedstorage:coloring_recipes/black_crafter","additionallanterns:cobblestone_lantern","allthecompressed:decompress/dirt_6x","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","dimstorage:dimensional_tablet","rftoolsbase:machine_base","botania:aura_ring_greater","rftoolsutility:counterplus_module","mcwroofs:white_terracotta_upper_steep_roof","mcwbiomesoplenty:dead_plank_window","mcwfurnitures:spruce_end_table","mcwdoors:crimson_whispering_door","securitycraft:reinforced_piston","cfm:purple_kitchen_counter","securitycraft:reinforced_yellow_stained_glass","handcrafted:crimson_couch","create:crafting/logistics/andesite_funnel","expatternprovider:threshold_export_bus","botania:swap_ring","mcwpaths:brick_crystal_floor_slab","refinedstorage:coloring_recipes/pink_pattern_grid","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","alltheores:enderium_plate","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","refinedstorage:coloring_recipes/gray_wireless_transmitter","productivebees:stonecutter/umbran_canvas_expansion_box","mcwpaths:blackstone_basket_weave_paving","modularrouters:player_module","pneumaticcraft:pressure_chamber_wall","minecraft:glass","allthecompressed:compress/gold_block_1x","xnet:connector_yellow","mcwdoors:spruce_paper_door","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","ae2:tools/portable_fluid_cell_64k","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwroofs:brown_concrete_attic_roof","alltheores:raw_iridium_block","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","ae2additions:components/super/64k","ae2:network/cables/smart_red","create:crafting/kinetics/nixie_tube","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","minecraft:light_blue_concrete_powder","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","refinedstorage:coloring_recipes/cyan_crafting_monitor","appmek:chemical_storage_cell_64k","mcwwindows:light_blue_mosaic_glass","ad_astra:green_industrial_lamp","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","refinedstorage:coloring_recipes/gray_crafting_monitor","mcwbiomesoplenty:stripped_maple_pane_window","botania:spawner_claw","rftoolsstorage:storage_control_module","securitycraft:secret_spruce_sign_item","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","bigreactors:crafting/magentite_storage_to_component","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","botania:placeholder","botania:super_cloud_pendant","railcraft:steel_gear","allthemodium:piglich_heart_block","sophisticatedstorage:crafting_upgrade","bigreactors:smelting/graphite_from_charcoal","minecraft:prismarine_bricks","pneumaticcraft:programming_puzzle","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","minecraft:stone_bricks","securitycraft:reinforced_red_stained_glass","mcwroofs:green_concrete_top_roof","refinedstorage:coloring_recipes/orange_crafter","connectedglass:borderless_glass_cyan2","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","ae2:tools/paintballs_cyan","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","villagertools:restock","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","reliquary:glowing_water_from_potion_vial","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","bloodmagic:blood_rune_acceleration_2","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","refinedstorage:coloring_recipes/orange_crafting_grid","ae2:network/cables/smart_fluix","pylons:potion_filter","enderio:resetting_lever_thirty","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","pneumaticcraft:solar_compressor","farmersdelight:cooking/pumpkin_soup","supplementaries:ars_nouveau/sign_post_archwood","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","refinedstorage:coloring_recipes/green_network_receiver","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","refinedstorage:coloring_recipes/red_wireless_transmitter","handcrafted:quartz_corner_trim","create:crafting/kinetics/contraption_controls","ad_astra:steel_factory_block","mcwfurnitures:stripped_spruce_triple_drawer","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","refinedstorage:coloring_recipes/light_gray_crafter_manager","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","mcwroofs:light_gray_concrete_lower_roof","cfm:crimson_cabinet","occultism:crafting/spirit_torch","twigs:rhyolite_slab","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","productivebees:expansion_boxes/expansion_box_cherry_canvas","pylons:interdiction_pylon","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","mcwwindows:cyan_curtain","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:crimson_modern_wardrobe","handcrafted:golden_medium_pot","ae2:network/cables/smart_yellow","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","mcwfurnitures:crimson_striped_chair","mcwroofs:gray_terracotta_upper_steep_roof","botania:mana_glass_pane","securitycraft:secret_sign_item","megacells:cells/portable/portable_fluid_cell_16m","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","connectedglass:borderless_glass_white2","create:crimson_window","ae2additions:components/super/1k","aquaculture:heavy_hook","aether:netherite_sword_repairing","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","mcwwindows:acacia_window","refinedstorage:coloring_recipes/green_controller","croptopia:food_press","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","cfm:black_picket_fence","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwfurnitures:crimson_lower_triple_drawer","mcwwindows:stripped_crimson_stem_four_window","twilightdelight:fiery_cooking_pot","utilitix:spruce_shulker_boat_with_shell","forbidden_arcanus:arcane_bone_meal","immersiveengineering:crafting/powerpack","advanced_ae:import_export_bus","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","sliceanddice:sprinkler","pneumaticcraft:small_tank","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","botania:terrasteel_chestplate","mcwroofs:orange_concrete_lower_roof","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","minecraft:barrel","mcwlights:orange_lamp","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","pneumaticcraft:reinforced_brick_pillar","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","mcwpaths:brick_crystal_floor","handcrafted:crimson_bench","botania:dodge_ring","ae2:materials/basiccard","pneumaticcraft:elytra_upgrade","refinedstorage:coloring_recipes/white_controller","mcwwindows:cherry_window","botania:laputa_shard","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfurnitures:crimson_covered_desk","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","allthearcanistgear:vibranium_boots_smithing","mcwpaths:cobbled_deepslate_honeycomb_paving","productivebees:hives/advanced_birch_canvas_hive","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","ae2:network/cables/glass_fluix","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:cauldron","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","delightful:smelting/roasted_acorn","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","twilightforest:compressed_blocks/fiery_block","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","ae2:block_cutter/walls/smooth_sky_stone_wall","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","allthemodium:unobtainium_vibranium_alloy_ingot_from_block","cfm:stripped_birch_kitchen_sink_light","mcwdoors:spruce_modern_door","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:netherite_gloves_repairing","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","cfm:cyan_kitchen_sink","dyenamics:peach_concrete_powder","ae2additions:components/super/4k","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","chemlib:gallium_ingot_from_smelting_gallium_dust","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","sophisticatedstorage:storage_void_upgrade_from_backpack_void_upgrade","railcraft:copper_gear","alltheores:enderium_rod","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","refinedstorage:coloring_recipes/blue_security_manager","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","connectedglass:borderless_glass_green2","mythicbotany:gaia_pylon","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwroofs:stone_bricks_top_roof","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","utilitarian:utility/spruce_logs_to_boats","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","refinedstorage:coloring_recipes/gray_crafting_grid","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","refinedstorage:coloring_recipes/lime_security_manager","mcwfurnitures:crimson_triple_drawer","mcwfurnitures:stripped_crimson_stool_chair","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","connectedglass:clear_glass_lime2","farmersdelight:cooking/mushroom_rice","refinedstorage:coloring_recipes/gray_crafter","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","ae2:network/cells/item_storage_cell_4k","mcwroofs:brown_terracotta_top_roof","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","dyenamics:dye_persimmon_carpet","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","ae2additions:cells/super/16m","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","refinedstorage:coloring_recipes/gray_network_transmitter","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","refinedstorage:coloring_recipes/magenta_pattern_grid","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","botania:holy_cloak","productivebees:stonecutter/concrete_canvas_hive","createoreexcavation:vein_finder","forbidden_arcanus:arcane_crystal_from_arcane_crystal_block","sophisticatedstorage:filter_upgrade","refinedstorage:coloring_recipes/yellow_crafting_grid","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","aether:aether_gold_nugget_from_smelting","create:crafting/kinetics/fluid_pipe_vertical","securitycraft:reinforced_tinted_glass","ae2:network/cells/spatial_components","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","mcwtrpdoors:spruce_tropical_trapdoor","cfm:stripped_oak_park_bench","mcwtrpdoors:print_cottage","securitycraft:reinforced_gray_stained_glass","botania:spark_upgrade_recessive","sophisticatedbackpacks:chipped/carpenters_table_upgrade","minecraft:copper_ingot_from_smelting_raw_copper","create:crafting/kinetics/mechanical_arm","mcwbiomesoplenty:stripped_maple_log_window2","advanced_ae:stock_export_bus","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","botania:dye_lime","mysticalagriculture:awakened_supremium_block_uncraft","pneumaticcraft:range_upgrade","cfm:magenta_cooler","dyenamics:navy_terracotta","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","ae2:tools/paintballs_light_gray","everythingcopper:copper_leggings","ae2:network/cells/item_storage_cell_1k","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","securitycraft:reinforced_blue_stained_glass_pane_from_glass","refinedstorage:coloring_recipes/green_disk_manipulator","botania:spark_upgrade_isolated","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","sophisticatedstorage:backpack_stack_upgrade_tier_3_from_storage_stack_upgrade_tier_4","cfm:stripped_spruce_table","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","croptopia:flour","twilightforest:equipment/fiery_leggings","refinedstorage:coloring_recipes/lime_grid","minecraft:jungle_sign","ae2:tools/portable_item_cell_16k","enderio:basic_capacitor","mcwfences:railing_mud_brick_wall","expatternprovider:precise_storage_bus","mcwwindows:crimson_planks_window","railcraft:steel_tank_valve","occultism:crafting/book_of_calling_djinni_manage_machine","cfm:cyan_cooler","ae2:network/parts/formation_plane","connectedglass:clear_glass_pane3","mcwfurnitures:stripped_crimson_drawer","railcraft:silver_gear","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","ad_astra:steel_rod","mcwwindows:prismarine_four_window","refinedstorage:coloring_recipes/magenta_wireless_transmitter","mcwroofs:light_blue_concrete_top_roof","mcwroofs:light_gray_roof","securitycraft:secret_jungle_hanging_sign","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","dyenamics:fluorescent_terracotta","extradisks:withering_processor","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","travelersbackpack:redstone","minecraft:slime_block","mcwdoors:crimson_waffle_door","mcwfurnitures:crimson_lower_bookshelf_drawer","twilightforest:magic_map_focus","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","minecraft:cooked_rabbit_from_campfire_cooking","chimes:glass_bells","refinedstorage:coloring_recipes/purple_grid","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","bigreactors:turbine/basic/casing","travelersbackpack:emerald","farmersdelight:cutting_board","bigreactors:reactor/reinforced/controller_ingots_uranium","croptopia:trail_mix","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","bloodmagic:blood_rune_sac_2","appbot:portable_mana_storage_cell_64k","ad_astra:nasa_workbench","sophisticatedstorage:storage_stack_upgrade_tier_2_from_backpack_stack_upgrade_tier_1","ad_astra:etrionic_capacitor","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","mcwbridges:blackstone_bridge_pier","ae2:tools/nether_quartz_axe","allthecompressed:compress/nether_star_block_3x","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","enderio:resetting_lever_sixty_inv","sophisticatedstorage:basic_to_gold_tier_upgrade","railcraft:steel_shovel","mcwfurnitures:stripped_spruce_large_drawer","mcwpaths:blackstone_windmill_weave_path","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","appmek:portable_chemical_storage_cell_16k","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:magenta_terracotta_attic_roof","mcwwindows:stripped_mangrove_log_four_window","allthecompressed:decompress/nether_star_block_3x","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","alltheores:copper_rod","minecraft:shulker_box","productivebees:stonecutter/cherry_canvas_hive","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","allthecompressed:compress/nether_star_block_2x","minecraft:white_carpet","utilitix:filter_rail","aether:diamond_gloves","minecraft:spruce_planks","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","enderio:resetting_lever_sixty_from_inv","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","handcrafted:crimson_dining_bench","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","mcwfurnitures:spruce_triple_drawer","minecraft:cracked_stone_bricks","securitycraft:secret_birch_sign_item","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","allthecompressed:decompress/nether_star_block_4x","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","botania:unholy_cloak","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","travelersbackpack:dye_green_sleeping_bag","dimstorage:dim_wall","allthecompressed:compress/nether_star_block_5x","arseng:source_storage_cell_4k","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","refinedstorage:coloring_recipes/black_pattern_grid","ae2:network/cells/fluid_storage_cell_4k","minecraft:magenta_stained_glass","minecraft:ender_chest","refinedstorage:coloring_recipes/lime_crafter_manager","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","allthecompressed:decompress/nether_star_block_1x","ae2:network/cables/dense_covered_yellow","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","create:crafting/kinetics/shaft","mcwpaths:cobbled_deepslate_running_bond_path","mcwfurnitures:stripped_crimson_wardrobe","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","modularrouters:void_module","connectedglass:scratched_glass_green2","ae2:network/blocks/storage_drive","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","allthecompressed:compress/nether_star_block_4x","mcwfurnitures:stripped_crimson_double_drawer_counter","immersiveengineering:crafting/redstone_acid","dankstorage:2_to_3","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","allthecompressed:decompress/nether_star_block_2x","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","refinedstorage:coloring_recipes/blue_detector","mysticalagriculture:imperium_essence_uncraft","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","refinedstorage:coloring_recipes/lime_controller","domum_ornamentum:lime_floating_carpet","botania:conversions/blazeblock_deconstruct","mcwfurnitures:spruce_large_drawer","farmersdelight:flint_knife","biomesoplenty:maple_boat","appbot:portable_mana_storage_cell_256k","aether:golden_boots_repairing","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","corail_woodcutter:warped_woodcutter","travelersbackpack:nether","mcwbiomesoplenty:redwood_plank_pane_window","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","refinedstorage:coloring_recipes/cyan_fluid_grid","securitycraft:reinforced_warped_fence_gate","botania:skydirt_rod","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","refinedstorage:coloring_recipes/yellow_network_transmitter","allthecompressed:compress/nether_star_block_6x","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","expatternprovider:epa","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","forbidden_arcanus:deorum_ingot_from_deorum_block","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","mcwtrpdoors:print_barred","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwroofs:white_lower_roof","mcwlights:gray_paper_lamp","minecraft:purpur_pillar","botania:drum_wild","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","mcwfurnitures:crimson_double_drawer","simplylight:illuminant_gray_block_dyed","blue_skies:dusk_spear","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","refinedstorage:coloring_recipes/green_crafter_manager","refinedstorage:coloring_recipes/light_gray_pattern_grid","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","bigreactors:turbine/reinforced/controller","minecraft:redstone_lamp","mcwtrpdoors:jungle_barrel_trapdoor","allthecompressed:decompress/nether_star_block_5x","refinedstorage:coloring_recipes/yellow_pattern_grid","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","allthecompressed:compress/cobbled_deepslate_1x","productivebees:stonecutter/acacia_canvas_expansion_box","twilightdelight:berry_stick","silentgear:stone_torch","handcrafted:terracotta_bowl","botania:black_hole_talisman","littlecontraptions:contraption_barge","pneumaticcraft:thermostat_module","botania:spark_upgrade_dominant","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","connectedglass:borderless_glass_pink2","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","twilightforest:wood/smoked_cracked_towerwood","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","blue_skies:comet_spear","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","botania:diluted_pool","modularrouters:energy_output_module","mcwlights:glowstone_slab","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","appbot:mana_storage_cell_64k","minecraft:crimson_sign","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","minecraft:polished_blackstone_pressure_plate","twilightdelight:borer_tear_soup","refinedstorage:improved_processor","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","ae2:network/cables/glass_lime","mcwwindows:crimson_planks_window2","securitycraft:secret_dark_oak_hanging_sign","alchemistry:fission_chamber_controller","alltheores:diamond_rod","railcraft:animal_detector","minecraft:stone_slab_from_stone_stonecutting","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwroofs:brown_concrete_steep_roof","refinedstorage:coloring_recipes/lime_pattern_grid","reliquary:infernal_claw","mcwwindows:stripped_jungle_log_window","minecraft:stone_brick_slab_from_stone_stonecutting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","mcwroofs:spruce_upper_lower_roof","pneumaticcraft:speed_upgrade","ae2:network/crafting/64k_cpu_crafting_storage","productivebees:stonecutter/grimwood_canvas_expansion_box","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","create:crafting/materials/andesite_alloy_block","cfm:spruce_chair","littlelogistics:locomotive_route","connectedglass:clear_glass_white2","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","botania:dreamwood","minecraft:netherite_leggings_smithing","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwtrpdoors:cherry_ranch_trapdoor","mcwfurnitures:crimson_coffee_table","create:crafting/kinetics/sticky_mechanical_piston","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","refinedstorage:coloring_recipes/green_relay","ae2:network/cables/dense_smart_brown","chemlib:calcium_nugget_to_ingot","forbidden_arcanus:blasting/arcane_crystal_from_blasting","pneumaticcraft:module_expansion_card","mcwbiomesoplenty:dead_hedge","ars_nouveau:archwood_planks","botania:petal_pink","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","mcwfences:prismarine_grass_topped_wall","occultism:crafting/book_of_calling_foliot_transport_items","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwpaths:andesite_windmill_weave_stairs","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","ae2:tools/portable_item_cell_1k","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","create:crafting/kinetics/gantry_shaft","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","allthecompressed:compress/emerald_block_6x","mcwbridges:balustrade_blackstone_bridge","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","allthecompressed:compress/nether_star_block_1x","refinedstorage:coloring_recipes/purple_relay","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","refinedstorage:coloring_recipes/light_gray_relay","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","securitycraft:secret_acacia_hanging_sign","minecraft:quartz_block","botania:glimmering_dreamwood_log","minecraft:brick","arseng:portable_source_cell_16k","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","mcwroofs:lime_concrete_top_roof","allthecompressed:compress/emerald_block_5x","arseng:source_storage_cell_1k","mcwroofs:purple_terracotta_attic_roof","bloodmagic:blood_rune_charging_2","sophisticatedstorage:stack_upgrade_tier_1_plus","mcwroofs:orange_terracotta_lower_roof","refinedstorage:coloring_recipes/cyan_network_receiver","ae2:network/cells/fluid_storage_cell_1k","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","appbot:mana_storage_cell_256k","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwroofs:brown_terracotta_upper_steep_roof","mcwdoors:oak_swamp_door","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","botania:dye_gray","supplementaries:sack","utilitarian:no_soliciting/restraining_order","handcrafted:crimson_chair","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","refinedstorage:coloring_recipes/cyan_crafter_manager","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","refinedstorage:coloring_recipes/gray_controller","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","create:andesite_ladder_from_andesite_alloy_stonecutting","productivebees:hives/advanced_bamboo_canvas_hive","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","minecraft:brown_dye","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","pneumaticcraft:assembly_io_unit_import","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","blue_skies:zeal_lighter","handcrafted:terracotta_medium_pot","minecraft:brick_slab","sophisticatedstorage:storage_pickup_upgrade_from_backpack_pickup_upgrade","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","expatternprovider:ex_drive","refinedstorage:coloring_recipes/gray_disk_manipulator","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","sophisticatedstorage:storage_magnet_upgrade_from_backpack_magnet_upgrade","ae2:network/cells/fluid_storage_cell_64k_storage","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","dyenamics:mint_dye","refinedstorage:coloring_recipes/purple_security_manager","silentgear:emerald_from_shards","supplementaries:candy","minecolonies:chicken_broth","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","pneumaticcraft:pneumatic_leggings","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","alchemistry:reactor_input","railcraft:diamond_spike_maul","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwroofs:gray_roof_block","mcwbiomesoplenty:fir_plank_four_window","botania:mushroom_11","allthecompressed:compress/blaze_block_1x","cfm:white_kitchen_drawer","ae2:network/crafting/1k_cpu_crafting_storage","botania:mushroom_10","utilitarian:tps_meter","utilitarian:utility/spruce_logs_to_doors","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","ae2:network/cables/smart_pink","mcwbiomesoplenty:pine_horse_fence","pneumaticcraft:pneumatic_dynamo","refinedstorage:coloring_recipes/green_detector","allthecompressed:compress/emerald_block_1x","comforts:hammock_to_white","mcwwindows:acacia_four_window","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","ae2:decorative/cut_quartz_block","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwfurnitures:spruce_modern_chair","refinedstorage:coloring_recipes/light_gray_security_manager","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","mcwbiomesoplenty:stripped_empyreal_log_window2","reliquary:witherless_rose","dyenamics:honey_stained_glass","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","alltheores:invar_rod","expatternprovider:ex_inscriber","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","reliquary:mercy_cross","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","twilightforest:compressed_blocks/carminite_block","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","productivebees:expansion_boxes/expansion_box_bamboo_canvas","botania:dirt_rod","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","blue_skies:maple_bookshelf","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","connectedglass:clear_glass_red2","pneumaticcraft:logistics_frame_default_storage","botania:gaia_ingot","mcwroofs:white_concrete_upper_steep_roof","croptopia:pork_and_beans","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","cfm:crimson_coffee_table","create:crafting/kinetics/belt_connector","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","refinedstorage:coloring_recipes/light_gray_detector","simplylight:illuminant_lime_block_on_toggle","ae2:misc/deconstruction_certus_quartz_block","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","mcwroofs:thatch_attic_roof","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","reliquary:mob_charm_fragments/enderman","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","mcwtrpdoors:crimson_beach_trapdoor","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","refinedstorage:coloring_recipes/purple_detector","create:crafting/kinetics/purple_seat","mcwpaths:brick_running_bond_path","twilightforest:equipment/fiery_iron_sword","supplementaries:sugar_cube","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","botania:mana_pylon","handcrafted:crimson_nightstand","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","cfm:spruce_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","mcwpaths:brick_windmill_weave_stairs","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","mcwdoors:spruce_tropical_door","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","minecraft:jungle_boat","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","advanced_ae:smalladvpatpro","botania:apothecary_livingrock","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","refinedstorage:coloring_recipes/brown_disk_manipulator","ae2:tools/paintballs_green","mcwtrpdoors:jungle_mystic_trapdoor","mcwdoors:crimson_stable_door","refinedstorage:coloring_recipes/network_transmitter","minecraft:dye_green_wool","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","railcraft:feed_station","cfm:stripped_crimson_cabinet","connectedglass:scratched_glass_green_pane2","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","mcwfurnitures:crimson_double_drawer_counter","mcwfurnitures:spruce_chair","productivebees:expansion_boxes/expansion_box_spruce_canvas","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","arseng:source_storage_cell_64k","cfm:stripped_crimson_coffee_table","simplylight:edge_light","mcwfences:railing_granite_wall","refinedstorage:coloring_recipes/cyan_disk_manipulator","immersiveengineering:crafting/plate_uranium_hammering","railcraft:personal_world_spike","immersiveengineering:crafting/armor_steel_chestplate","refinedstorage:coloring_recipes/light_gray_crafter","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","connectedglass:clear_glass_pink2","ad_astra:steel_sliding_door","refinedstorage:coloring_recipes/green_pattern_grid","bigreactors:energizer/powerport_fe","megacells:crafting/greater_energy_card_upgraded","aether:ambrosium_block","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","dyenamics:bubblegum_concrete_powder","utilitarian:utility/crimson_logs_to_pressure_plates","advanced_ae:quantum_leggings_item_reset","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","expatternprovider:water_cell","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","merequester:requester","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","refinedstorage:coloring_recipes/magenta_fluid_grid","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","supplementaries:slingshot","minecraft:purpur_pillar_from_purpur_block_stonecutting","allthemodium:allthemodium_plate","advanced_ae:quantumunit","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","productivebees:expansion_boxes/expansion_box_mangrove_canvas","everythingcopper:copper_rail","ae2:network/cells/item_storage_components_cell_4k_part","refinedstorage:coloring_recipes/brown_crafting_grid","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","minecraft:crimson_pressure_plate","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","dyenamics:dye_ultramarine_carpet","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aquaculture:neptunium_ingot_from_nuggets","mcwroofs:bricks_upper_steep_roof","croptopia:french_fries","botania:terraform_rod","minecraft:iron_pickaxe","refinedstorage:coloring_recipes/light_gray_wireless_transmitter","ae2:shaped/not_so_mysterious_cube","twilightdelight:steeleaf_knife","mcwbiomesoplenty:willow_plank_four_window","reliquary:fortune_coin","handcrafted:crimson_pillar_trim","mcwfences:railing_sandstone_wall","bigreactors:crafting/cyanite_storage_to_component","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwroofs:magenta_concrete_attic_roof","ae2:network/cables/glass_orange","mcwwindows:granite_window","mcwfurnitures:stripped_spruce_table","deepresonance:resonating_plate","minecraft:vibranium_mage_chestplate_smithing","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","reliquary:angelheart_vial","create:crafting/kinetics/cart_assembler","ae2:network/cells/fluid_storage_cell_256k_storage","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","refinedstorage:advanced_processor","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","mcwfences:modern_end_brick_wall","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwbridges:balustrade_mossy_stone_bricks_bridge","dyenamics:dye_wine_carpet","ae2:tools/paintballs_black","minecraft:netherite_helmet_smithing","refinedstorage:coloring_recipes/brown_wireless_transmitter","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","mcwtrpdoors:crimson_barn_trapdoor","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","cfm:white_kitchen_counter","mcwbiomesoplenty:stripped_umbran_log_four_window","create:crafting/kinetics/filter","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","ae2:network/cables/covered_fluix_clean","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","trashcans:energy_trash_can","botania:aura_ring","botania:dreamwood_twig","mcwdoors:print_nether","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","megacells:crafting/decompression_module","cfm:fridge_dark","chimes:copper_chimes","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwfurnitures:crimson_bookshelf_cupboard","mcwbiomesoplenty:stripped_mahogany_log_four_window","create:crafting/kinetics/brass_door","farmersdelight:cooking_pot","sophisticatedstorage:crimson_chest","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","mcwtrpdoors:mangrove_bark_trapdoor","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","ae2:tools/paintballs_yellow","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","dyenamics:dye_icy_blue_carpet","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","expatternprovider:wireless_connector","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","reliquary:mob_charm_fragments/wither_skeleton","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","forbidden_arcanus:clibano_combustion/arcane_crystal_from_clibano_combusting","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","blue_skies:moonstone_pressure_plate","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","ae2:network/cables/dense_covered_fluix_clean","littlelogistics:switch_rail","dyenamics:dye_honey_carpet","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","ae2:network/cables/covered_fluix","xnet:connector_red_dye","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwroofs:crimson_planks_lower_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwroofs:orange_terracotta_upper_lower_roof","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","minecraft:mossy_stone_brick_slab","minecraft:andesite_slab","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","minecraft:honey_block","reliquary:mob_charm_fragments/slime","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","create:crafting/kinetics/large_cogwheel","cfm:stripped_spruce_desk","sophisticatedbackpacks:crafting_upgrade","mcwdoors:crimson_japanese2_door","productivebees:stonecutter/willow_canvas_expansion_box","aquaculture:birch_fish_mount","ae2:network/cells/fluid_storage_cell_1k_storage","bigreactors:crafting/cyanite_component_to_storage","mcwwindows:stripped_mangrove_log_window","securitycraft:secret_crimson_hanging_sign","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","create:crafting/kinetics/metal_bracket","mcwwindows:jungle_window","refinedstorage:coloring_recipes/purple_fluid_grid","minecraft:cobblestone_stairs","connectedglass:clear_glass_magenta2","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","blue_skies:comet_bookshelf","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","tombstone:ankh_of_prayer","cfm:white_sofa","refinedstorage:coloring_recipes/red_crafting_monitor","mcwbiomesoplenty:pine_highley_gate","botania:elven_spreader","mcwfences:ornate_metal_fence","ae2:shaped/slabs/fluix_block","mcwfences:acacia_highley_gate","utilitix:dark_oak_shulker_boat_with_shell","aiotbotania:livingrock_sword","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","allthecompressed:compress/uranium_block_1x","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","refinedstorage:coloring_recipes/blue_grid","mcwfurnitures:oak_cupboard_counter","mcwbridges:crimson_rail_bridge","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","laserio:filter_count_nbtclear","travelersbackpack:squid","mcwroofs:spruce_roof","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","ae2:network/blocks/cell_workbench","pneumaticcraft:heat_sink","minecraft:polished_andesite_slab","mcwlights:magenta_lamp","chemlib:gallium_ingot_to_nugget","create:crafting/logistics/powered_latch","productivebees:stonecutter/warped_canvas_expansion_box","ae2:network/cables/dense_covered_lime","mcwfurnitures:crimson_chair","refinedstorage:coloring_recipes/blue_crafter_manager","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","sophisticatedstorage:stonecutter_upgrade","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","mcwfurnitures:stripped_crimson_end_table","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","farmersdelight:cooking/pasta_with_meatballs","create:crafting/materials/andesite_alloy_from_block","bigreactors:turbine/basic/controller","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","refinedstorage:coloring_recipes/yellow_detector","create:crafting/kinetics/water_wheel","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwpaths:blackstone_crystal_floor_path","ae2additions:cells/super/16m-housing","cfm:jungle_cabinet","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","mcwroofs:base_roof_block","appflux:flux_accessor_alt","productivebees:hives/advanced_mangrove_canvas_hive","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","ae2:tools/paintballs_white","mcwbiomesoplenty:palm_window","twigs:blackstone_column","botania:conversions/pink_petal_block_deconstruct","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","littlecontraptions:barge_assembler","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","utilitarian:utility/crimson_logs_to_doors","create:crafting/kinetics/analog_lever","create:andesite_scaffolding_from_andesite_alloy_stonecutting","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","twilightforest:equipment/fiery_iron_pickaxe","botania:terra_axe","domum_ornamentum:light_blue_floating_carpet","mcwroofs:crimson_lower_roof","mcwtrpdoors:crimson_blossom_trapdoor","ae2:shaped/stairs/fluix_block","create:crafting/kinetics/sticker","ae2:decorative/fluix_block","pneumaticcraft:compressed_iron_chestplate","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","create:crafting/logistics/content_observer","refinedstorage:coloring_recipes/gray_network_receiver","ae2:block_cutter/slabs/quartz_slab","appmek:portable_chemical_storage_cell_256k","refinedstorage:coloring_recipes/green_wireless_transmitter","sophisticatedbackpacks:chipped/glassblower_upgrade","utilitix:acacia_shulker_boat_with_shell","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","mcwroofs:crimson_planks_top_roof","enderio:resetting_lever_three_hundred_inv_from_prev","refinedstorage:coloring_recipes/white_network_receiver","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","twilightforest:naga_banner_pattern","minecraft:crimson_fence","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","minecraft:light_gray_stained_glass","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","mcwdoors:garage_gray_door","railcraft:world_spike","allthecompressed:decompress/netherrack_6x","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","ae2:network/cells/item_storage_components_cell_256k_part","minecraft:polished_andesite_from_andesite_stonecutting","allthearcanistgear:vibranium_robes_smithing","littlelogistics:seater_car","botania:ender_eye_block","minecraft:crimson_stairs","refinedstorage:coloring_recipes/orange_fluid_grid","mcwbiomesoplenty:stripped_dead_pane_window","ae2:shaped/walls/fluix_block","mcwroofs:magenta_terracotta_steep_roof","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","botania:mushroom_9","mcwtrpdoors:crimson_four_panel_trapdoor","mcwfurnitures:crimson_modern_desk","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","botania:mushroom_5","ae2:misc/chests_smooth_sky_stone","minecraft:stone_brick_stairs","botania:mushroom_4","refinedstorage:coloring_recipes/red_network_transmitter","aether:skyroot_beehive","bigreactors:turbine/basic/activefluidport_forge","botania:mushroom_3","bigreactors:reprocessor/outputport","cfm:brown_grill","mcwwindows:quartz_window","mcwroofs:gray_roof","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","cfm:stripped_crimson_desk","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","refinedstorage:coloring_recipes/purple_wireless_transmitter","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","botania:glimmering_livingwood_log","blue_skies:coarse_lunar_dirt","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","minecraft:crimson_button","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","utilitix:mangrove_shulker_boat_with_shell","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","refinedstorage:coloring_recipes/magenta_crafter","minecraft:clock","mcwroofs:red_concrete_top_roof","occultism:crafting/otherstone_pedestal","create:crafting/appliances/netherite_diving_helmet_from_netherite","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","railcraft:lead_gear","dyenamics:dye_peach_carpet","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","megacells:crafting/compression_card","mcwfences:oak_wired_fence","botania:twig_wand","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","silentgear:diamond_shard","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","cfm:lime_picket_fence","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","minecraft:red_nether_bricks","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwtrpdoors:spruce_whispering_trapdoor","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","minecraft:prismarine","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwfurnitures:stripped_crimson_bookshelf_cupboard","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","minecolonies:pasta_plain","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","silentgear:leather_scrap","mcwroofs:purple_terracotta_roof","twilightforest:equipment/fiery_sword","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","botania:keep_ivy","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","chemlib:calcium_ingot_from_blasting_calcium_dust","cfm:stripped_spruce_crate","twigs:rhyolite","pylons:infusion_pylon","mcwwindows:spruce_window2","create:crafting/kinetics/linear_chassis","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","mekanism:processing/steel/ingot/from_dust_smelting","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","bigreactors:turbine/ludicrite_block","pneumaticcraft:armor_upgrade","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","ae2:network/blocks/energy_dense_energy_cell","minecraft:lime_concrete_powder","refinedstorage:coloring_recipes/purple_network_transmitter","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","botania:conversions/gray_petal_block_deconstruct","securitycraft:block_change_detector","minecraft:polished_blackstone_button","advanced_ae:advpatpro2","supplementaries:pancake_fd","modularrouters:placer_module","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","minecraft:netherite_scrap_from_blasting","botania:petal_black","botania:lens_warp","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","mcwbiomesoplenty:maple_window2","mcwdoors:crimson_barn_door","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","refinedstorage:coloring_recipes/orange_security_manager","mcwbridges:bridge_torch","refinedstorage:coloring_recipes/orange_detector","botania:white_shiny_flower","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","undergarden:wigglewood_boat","connectedglass:scratched_glass1","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","mcwpaths:andesite_crystal_floor_slab","pneumaticcraft:elevator_frame","securitycraft:reinforced_crimson_fence","minecraft:wooden_shovel","refinedstorage:coloring_recipes/orange_crafting_monitor","additionallanterns:quartz_chain","createoreexcavation:vein_atlas","refinedstorage:coloring_recipes/magenta_disk_manipulator","aether:diamond_axe_repairing","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","botania:dreamwood_slab","refinedstorage:coloring_recipes/lime_crafting_monitor","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","botania:vivid_seeds","mcwfences:acacia_picket_fence","reliquary:uncrafting/wither_skeleton_skull","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","twilightdelight:cooking/tear_drink","mysticalagriculture:supremium_block_uncraft","securitycraft:secret_dark_oak_sign_item","ae2:network/cables/dense_covered_cyan","blue_skies:moonstone_from_shard","sophisticatedbackpacks:smithing_upgrade","minecraft:gold_ingot_from_smelting_raw_gold","botania:golden_seeds","mcwfences:mud_brick_railing_gate","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","mysticalagriculture:essence/minecraft/amethyst","chemlib:calcium_ingot_to_nugget","mcwfurnitures:oak_lower_triple_drawer","alltheores:uranium_gear","railcraft:logbook","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","botania:shimmerrock","mcwbridges:jungle_log_bridge_middle","utilitix:bamboo_shulker_raft_with_shell","ae2:decorative/quartz_glass","sophisticatedbackpacks:anvil_upgrade","mcwroofs:green_concrete_steep_roof","botania:conversions/lime_petal_block_deconstruct","mcwfences:mangrove_wired_fence","refinedstorage:coloring_recipes/gray_security_manager","minecraft:red_terracotta","mcwfurnitures:oak_wardrobe","twilightdelight:fiery_knife","mcwroofs:thatch_lower_roof","create:crafting/kinetics/basin","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","connectedglass:clear_glass1","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwpaths:blackstone_dumble_paving","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","securitycraft:block_pocket_manager","refinedstorage:coloring_recipes/orange_pattern_grid","mcwlights:soul_cherry_tiki_torch","expatternprovider:ei","botania:red_pavement","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","enderio:resetting_lever_sixty_inv_from_prev","mcwpaths:brick_running_bond_stairs","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","mcwlights:red_lamp","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","refinedstorage:coloring_recipes/gray_crafter_manager","botania:flower_bag","cfm:purple_kitchen_drawer","ad_astra:steel_plateblock","botania:black_shiny_flower","pneumaticcraft:compressed_brick_wall","mcwtrpdoors:crimson_classic_trapdoor","twilightforest:berry_torch","mcwfences:red_sandstone_railing_gate","enderio:primitive_alloy_smelter","minecraft:netherite_hoe_smithing","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","minecraft:netherite_chestplate_smithing","mcwfurnitures:stripped_crimson_cupboard_counter","cfm:jungle_kitchen_counter","ae2:network/cables/dense_smart_red","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","farmersdelight:diamond_knife","terralith:observer_alt","refinedstorage:coloring_recipes/green_crafting_grid","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","bigreactors:fluidizer/powerport","mcwlights:light_blue_lamp","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","domum_ornamentum:white_paper_extra","mcwfurnitures:crimson_glass_table","connectedglass:borderless_glass_blue2","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","mcwbridges:balustrade_andesite_bridge","additionallanterns:emerald_lantern","refinedstorage:coloring_recipes/pink_crafting_grid","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","advanced_ae:quantum_helmet_item_reset","mcwbiomesoplenty:red_maple_hedge","refinedstorage:coloring_recipes/white_crafter","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","refinedstorage:coloring_recipes/black_crafting_monitor","connectedglass:clear_glass_gray2","sfm:manager","mcwfurnitures:stripped_spruce_counter","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwroofs:spruce_lower_roof","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","littlelogistics:tee_junction_rail","botania:livingwood_twig","mcwbridges:crimson_bridge_pier","mcwdoors:spruce_whispering_door","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","ae2:tools/network_tool","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","deeperdarker:bloom_boat","refinedstorage:coloring_recipes/cyan_pattern_grid","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","minecolonies:doublegrass","mcwroofs:pink_terracotta_upper_steep_roof","dyenamics:rose_stained_glass","mcwpaths:blackstone_diamond_paving","farmersdelight:cooking/hot_cocoa","sophisticatedstorage:chipped/carpenters_table_upgrade","pneumaticcraft:vortex_cannon","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","refinedstorage:coloring_recipes/black_security_manager","croptopia:steamed_crab","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","ae2:network/cables/dense_covered_red","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","botania:red_string","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","allthemodium:ancient_stone_stairs","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","refinedstorage:coloring_recipes/purple_pattern_grid","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","mcwbridges:crimson_log_bridge_middle","blue_skies:maple_spear","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","mcwroofs:crimson_roof","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","reliquary:mob_charm_fragments/zombified_piglin","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","refinedstorage:coloring_recipes/black_fluid_grid","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","expatternprovider:pre_bus","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","refinedstorage:coloring_recipes/brown_grid","allthearcanistgear:vibranium_hat_smithing","ae2:network/cables/dense_smart_fluix_clean","farmersdelight:cooking/noodle_soup","dyenamics:amber_terracotta","allthecompressed:compress/netherite_block_1x","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","mcwfurnitures:crimson_table","mcwbiomesoplenty:magic_horse_fence","alchemistry:reactor_output","create:crafting/kinetics/white_sail","refinedstorage:coloring_recipes/lime_wireless_transmitter","ae2:network/crafting/molecular_assembler","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","bigreactors:reprocessor/powerport","mcwbridges:stone_bridge_pier","aether:white_cape","refinedstorage:coloring_recipes/relay","cfm:green_picket_gate","ae2:tools/misctools_entropy_manipulator","railcraft:radio_circuit","aether:netherite_pickaxe_repairing","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","refinedstorage:coloring_recipes/lime_crafter","minecraft:dispenser","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","ae2:decorative/sky_stone_brick_from_stonecutting","securitycraft:secret_acacia_sign_item","create:crafting/kinetics/hand_crank","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwtrpdoors:spruce_classic_trapdoor","allthemodium:ancient_stone_wall","mcwfences:mangrove_picket_fence","refinedstorage:coloring_recipes/magenta_detector","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","botania:dry_seeds","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","handcrafted:jungle_desk","utilitarian:snad/drit","refinedstorage:coloring_recipes/purple_controller","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","refinedstorage:coloring_recipes/orange_controller","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","refinedstorage:coloring_recipes/detector","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","ad_astra:steel_trapdoor","voidtotem:totem_of_void_undying","botania:red_string_alt","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwfurnitures:stripped_crimson_desk","mcwroofs:stone_bricks_attic_roof","pneumaticcraft:fluid_mixer","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","mcwdoors:crimson_western_door","securitycraft:alarm","pneumaticcraft:manometer","ad_astra:launch_pad","cfm:magenta_picket_gate","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","ae2:network/blocks/energy_vibration_chamber","pneumaticcraft:compressed_brick_tile","mcwtrpdoors:crimson_swamp_trapdoor","mcwfurnitures:stripped_crimson_drawer_counter","ae2:tools/fluix_shovel","reliquary:magicbane","pneumaticcraft:wall_lamp_inverted_magenta","minecraft:cooked_porkchop_from_smoking","modularrouters:sender_module_1_alt","botania:petal_lime_double","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","refinedstorage:coloring_recipes/cyan_security_manager","refinedstorage:coloring_recipes/cyan_relay","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mysticalagriculture:essence/minecraft/netherite_ingot","refinedstorage:coloring_recipes/blue_crafter","mcwwindows:stone_window","mcwroofs:gray_steep_roof","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwroofs:crimson_upper_lower_roof","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","alltheores:iron_ore_hammer","refinedstorage:coloring_recipes/lime_network_transmitter","mcwroofs:crimson_upper_steep_roof","sophisticatedstorage:jukebox_upgrade","productivebees:stonecutter/yucca_canvas_hive","mcwtrpdoors:jungle_classic_trapdoor","dankstorage:dank_6","cfm:white_picket_gate","mcwfurnitures:stripped_oak_modern_wardrobe","dankstorage:dank_7","dyenamics:mint_concrete_powder","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","dankstorage:dank_3","dankstorage:dank_4","ae2:network/cables/glass_black","mcwroofs:yellow_terracotta_upper_lower_roof","dankstorage:dank_5","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","mcwtrpdoors:crimson_paper_trapdoor","everythingcopper:copper_ladder","botania:polished_livingrock","mcwwindows:warped_stem_window","railcraft:invar_gear","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","bloodmagic:blood_rune_aug_capacity_2","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","productivebees:expansion_boxes/expansion_box_warped_canvas","ae2:tools/portable_fluid_cell_16k","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","minecraft:polished_blackstone_stairs","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","cfm:stripped_crimson_crate","minecraft:dropper","create:crafting/kinetics/piston_extension_pole","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","advanced_ae:quantum_chestplate_item_reset","expatternprovider:oversize_interface_part","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","bigreactors:reprocessor/collector","create:crafting/kinetics/lime_seat","mcwdoors:crimson_tropical_door","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","botania:thunder_sword","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","megacells:cells/portable/portable_item_cell_16m","mcwfurnitures:stripped_crimson_lower_triple_drawer","ae2:network/crafting/4k_cpu_crafting_storage","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","cfm:stripped_dark_oak_kitchen_sink_light","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","productivebees:stonecutter/yucca_canvas_expansion_box","mcwwindows:quartz_pane_window","refinedstorage:coloring_recipes/green_network_transmitter","mcwfurnitures:stripped_crimson_bookshelf_drawer","bloodmagic:raw_hellforged_block","refinedstorage:coloring_recipes/orange_network_receiver","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:fancy_painting","mcwfurnitures:spruce_table","bigreactors:energizer/energycore","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","cfm:crimson_chair","minecraft:cooked_porkchop_from_campfire_cooking","pneumaticcraft:minigun_upgrade","connectedglass:clear_glass_brown2","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","minecraft:cooked_rabbit_from_smoking","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","twigs:polished_rhyolite","minecraft:quartz_bricks","dyenamics:dye_rose_carpet","utilitarian:utility/crimson_logs_to_stairs","botania:temperance_stone","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwpaths:stone_running_bond_stairs","expatternprovider:u_terminal/upgrade","refinedstorage:coloring_recipes/yellow_disk_manipulator","mcwbridges:balustrade_bricks_bridge","rftoolsbuilder:red_shield_template_block","refinedstorage:coloring_recipes/white_crafting_grid","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","allthetweaks:atm_star_block","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","chemlib:calcium_ingot_to_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","bloodmagic:soul_forge","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","cfm:green_picket_fence","mcwroofs:white_terracotta_top_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","pneumaticcraft:assembly_laser","refinedstorage:coloring_recipes/red_security_manager","mcwfurnitures:crimson_double_wardrobe","pneumaticcraft:pneumatic_chestplate","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","mcwroofs:orange_concrete_upper_lower_roof","mcwfurnitures:spruce_modern_wardrobe","mcwtrpdoors:spruce_barn_trapdoor","ae2:network/blocks/pattern_providers_interface_part","appbot:fluix_mana_pool","productivebees:stonecutter/hellbark_canvas_hive","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwroofs:light_gray_terracotta_attic_roof","ae2:network/cells/view_cell","mcwfurnitures:crimson_stool_chair","botania:terra_pick","botania:brewery","mcwroofs:crimson_attic_roof","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","mcwroofs:light_gray_concrete_upper_lower_roof","cfm:stripped_crimson_desk_cabinet","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","chemlib:gallium_nugget_to_ingot","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","artifacts:eternal_steak_smoker","ae2:tools/certus_quartz_hoe","mcwtrpdoors:crimson_whispering_trapdoor","cfm:lime_kitchen_drawer","ae2:network/cables/glass_light_blue","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:spruce_drawer_counter","mcwdoors:crimson_beach_door","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","botania:mutated_seeds","pneumaticcraft:smart_chest_kit","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","create:crafting/appliances/filter_clear","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","twilightdelight:cooking/torchberry_juice","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","create:crafting/kinetics/goggles","aether:aether_saddle","create:crafting/kinetics/rope_pulley","farmersdelight:wheat_dough_from_water","mcwroofs:crimson_planks_roof","create:crafting/kinetics/cuckoo_clock","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","refinedstorage:coloring_recipes/gray_detector","mcwlights:pink_lamp","railcraft:steel_boots","ad_astra:zip_gun","refinedstorage:coloring_recipes/white_crafting_monitor","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","refinedstorage:coloring_recipes/black_controller","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","deeperdarker:soul_elytra","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","expatternprovider:pattern_modifier","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","sophisticatedstorage:storage_link_from_controller","securitycraft:reinforced_dropper","mcwfurnitures:stripped_crimson_glass_table","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwfurnitures:spruce_double_drawer","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","allthecompressed:compress/atm_star_block_1x","travelersbackpack:magma_cube","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwfurnitures:stripped_spruce_drawer_counter","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","refinedstorage:coloring_recipes/white_security_manager","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","twigs:bloodstone","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","productivebees:expansion_boxes/expansion_box_oak_canvas","mcwwindows:warped_planks_window","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwroofs:orange_concrete_steep_roof","cfm:orange_kitchen_counter","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","ae2:network/cables/smart_light_gray","mcwbiomesoplenty:stripped_palm_log_four_window","arseng:portable_source_cell_256k","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","ae2:network/wireless_crafting_terminal","create:crafting/kinetics/orange_seat","refinedstorage:coloring_recipes/white_relay","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:icy_blue_terracotta","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","bigreactors:fluidizer/outputport","refinedstorage:coloring_recipes/brown_network_transmitter","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","refinedstorage:coloring_recipes/gray_relay","bigreactors:blasting/graphite_from_coal","cfm:crimson_desk_cabinet","sophisticatedbackpacks:stack_upgrade_tier_2","botania:astrolabe","sophisticatedbackpacks:stack_upgrade_tier_1","appmek:portable_chemical_storage_cell_1k","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","pneumaticcraft:compressed_iron_ingot_from_block","sophisticatedstorage:backpack_stack_upgrade_tier_1_from_storage_stack_upgrade_tier_2","minecolonies:raw_noodle","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","securitycraft:reinforced_brown_stained_glass_pane_from_dye","minecraft:cooked_mutton","railways:crafting/smokestack_long","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","twilightforest:material/carminite","refinedstorage:coloring_recipes/brown_fluid_grid","twilightdelight:fiery_knifealt","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","travelersbackpack:pig","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","allthecompressed:decompress/emerald_block_4x","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","dankstorage:3_to_4","mcwfurnitures:spruce_counter","ae2:tools/certus_quartz_cutting_knife","utilitarian:snad/snad","handcrafted:bricks_corner_trim","croptopia:shaped_bacon","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:light_gray_terracotta","dyenamics:amber_stained_glass","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:spring_green_dye","refinedstorage:coloring_recipes/cyan_wireless_transmitter","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","cfm:dye_green_picket_gate","refinedstorage:coloring_recipes/pattern_grid","sophisticatedstorage:jungle_chest","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","botania:corporea_spark","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","occultism:crafting/book_of_calling_foliot_cleaner","merequester:requester_terminal","productivebees:hives/advanced_acacia_canvas_hive","mcwfurnitures:stripped_jungle_covered_desk","ae2:shaped/stairs/smooth_sky_stone_block","occultism:smelting/burnt_otherstone","cfm:black_picket_gate","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","allthecompressed:decompress/emerald_block_5x","appmek:portable_chemical_storage_cell_4k","minecraft:red_dye_from_rose_bush","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:crimson_cottage_trapdoor","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","botania:manasteel_shears","ae2:network/cells/item_storage_cell_64k","ae2:network/cables/smart_green","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","ae2:block_cutter/walls/fluix_wall","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","refinedstorage:coloring_recipes/black_network_receiver","mcwlights:green_lamp","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","minecraft:soul_campfire","botania:clip","botania:red_string_interceptor","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","refinedstorage:coloring_recipes/red_network_receiver","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","refinedstorage:coloring_recipes/pink_grid","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","ae2:tools/paintballs_magenta","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","refinedstorage:coloring_recipes/gray_pattern_grid","twigs:cracked_bricks","supplementaries:slice_map","productivebees:expansion_boxes/expansion_box_jungle_canvas","littlelogistics:automatic_tee_junction_rail","mcwbridges:brick_bridge_pier","connectedglass:borderless_glass_green_pane2","ae2:misc/deconstruction_fluix_block","refinedstorage:coloring_recipes/pink_detector","allthecompressed:compress/gravel_5x","mcwroofs:cobblestone_lower_roof","alltheores:invar_plate","occultism:crafting/butcher_knife","blue_skies:lunar_spear","cfm:stripped_spruce_desk_cabinet","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","minecraft:crimson_fence_gate","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","botania:lens_normal","mcwfences:end_brick_pillar_wall","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","enderio:reinforced_obsidian_block","mcwroofs:light_gray_upper_lower_roof","pneumaticcraft:wall_lamp_inverted_cyan","dankstorage:6_to_7","minecraft:cooked_chicken_from_campfire_cooking","mcwroofs:spruce_steep_roof","mcwfurnitures:jungle_modern_chair","ae2:network/cells/item_storage_cell_4k_storage","create:crafting/kinetics/radial_chassis","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","minecraft:quartz_slab","handcrafted:phantom_trophy","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","mcwroofs:stone_bricks_roof","cfm:cyan_grill","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","ae2:block_cutter/stairs/smooth_sky_stone_stairs","dyenamics:amber_wool","farmersdelight:iron_knife","minecraft:quartz_pillar_from_quartz_block_stonecutting","railcraft:steel_helmet","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","ae2:network/cables/smart_black","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","allthemodium:unobtainium_allthemodium_alloy_block","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","ae2:block_cutter/stairs/fluix_stairs","farmersdelight:chicken_sandwich","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","enderio:iron_gear","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","ae2:network/cables/smart_orange","cfm:white_picket_fence","mcwdoors:crimson_four_panel_door","dyenamics:dye_amber_carpet","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","cfm:crimson_crate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","handcrafted:kitchen_hood","appbot:mana_storage_cell_4k","supplementaries:fodder","mcwdoors:jungle_bamboo_door","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","chemlib:gallium_block_to_ingot","mcwroofs:yellow_concrete_roof","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","mcwfurnitures:stripped_crimson_table","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:black_lower_roof","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","reliquary:ender_staff","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwfurnitures:stripped_spruce_bookshelf_drawer","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","travelersbackpack:netherite_tier_upgrade","securitycraft:secret_birch_hanging_sign","botania:travel_belt","bigreactors:reprocessor/fluidinjector","mcwbiomesoplenty:fir_highley_gate","refinedstorage:coloring_recipes/lime_network_receiver","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","ae2:network/parts/energy_acceptor","additionallanterns:gold_chain","ae2:materials/advancedcard","minecraft:cooked_porkchop","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwroofs:andesite_lower_roof","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","cfm:stripped_crimson_bedside_cabinet","mcwbridges:andesite_bridge","mcwwindows:dark_prismarine_pane_window","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","minecraft:spruce_wood","occultism:crafting/golden_sacrificial_bowl","aquaculture:sushi","forbidden_arcanus:stellarite_block_from_stellarite_piece","ae2:network/blocks/interfaces_interface_alt","mcwfurnitures:jungle_table","refinedstorage:coloring_recipes/purple_network_receiver","mcwdoors:metal_reinforced_door","ae2:tools/portable_fluid_cell_4k","domum_ornamentum:blue_cobblestone_extra","railways:crafting/smokestack_caboosestyle","additionallanterns:blackstone_chain","pneumaticcraft:programmable_controller","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","ae2:tools/fluix_upgrade_smithing_template","mcwpaths:crimson_planks_path","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","botania:quartz_red","simplemagnets:advanced_demagnetization_coil","cfm:crimson_bedside_cabinet","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","appbot:mana_storage_cell_1k","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","refinedstorage:coloring_recipes/cyan_controller","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","productivebees:stonecutter/maple_canvas_hive","mcwroofs:crimson_planks_steep_roof","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","twilightforest:equipment/fiery_pickaxe","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","supplementaries:candle_holders/candle_holder_green_dye","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","create:crafting/kinetics/metal_girder","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","minecraft:redstone","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","mcwroofs:crimson_steep_roof","ae2:tools/portable_fluid_cell_1k","minecraft:powered_rail","productivebees:expansion_boxes/expansion_box_birch_canvas","botania:dye_pink","mcwwindows:diorite_pane_window","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","enderio:resetting_lever_ten_from_prev","mcwfurnitures:stripped_crimson_modern_chair","mcwroofs:magenta_concrete_upper_lower_roof","sophisticatedstorage:storage_stack_upgrade_tier_5_from_backpack_stack_upgrade_tier_4","reliquary:crimson_cloth","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","twilightforest:equipment/fiery_ingot_crafting","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","connectedglass:clear_glass_purple2","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","minecraft:gray_terracotta","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwfurnitures:stripped_crimson_lower_bookshelf_drawer","mcwwindows:white_mosaic_glass_pane","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","cfm:crimson_blinds","cfm:stripped_spruce_coffee_table","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","twilightforest:equipment/fiery_fiery_chestplate","dyenamics:peach_terracotta","botania:livingrock_wall","allthemodium:unobtainium_vibranium_alloy_block","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","reliquary:alkahestry_altar","delightful:knives/silver_knife","productivebees:stonecutter/snake_block_canvas_expansion_box","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","mcwdoors:crimson_modern_door","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:yellow_lamp","botania:blue_pavement","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","refinedstorage:coloring_recipes/blue_crafting_monitor","supplementaries:timber_frame","mcwfurnitures:stripped_crimson_counter","aether:diamond_pickaxe_repairing","pneumaticcraft:seismic_sensor","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","dankstorage:5_to_6","mcwroofs:brown_terracotta_roof","forbidden_arcanus:dark_rune_from_dark_rune_block","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","ae2:network/cells/fluid_storage_cell_4k_storage","create:crafting/kinetics/vertical_gearbox","productivebees:stonecutter/redwood_canvas_hive","mcwlights:double_street_lamp","minecraft:beacon","mysticalagriculture:tertium_block_uncraft","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwdoors:spruce_stable_door","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","forbidden_arcanus:tiled_polished_darkstone_bricks_from_polished_darkstone_stonecutting","minecraft:flint_and_steel","ad_astra:white_flag","railways:crafting/smokestack_streamlined","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","allthecompressed:decompress/diamond_block_4x","cfm:spruce_upgraded_gate","mcwtrpdoors:oak_swamp_trapdoor","minecraft:black_dye","mcwfurnitures:crimson_cupboard_counter","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwroofs:stone_steep_roof","botania:apothecary_default","farmersdelight:oak_cabinet","allthecompressed:decompress/diamond_block_5x","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","aiotbotania:terra_shovel","mcwfurnitures:spruce_cupboard_counter","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","minecolonies:fish_n_chips","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","productivebees:expansion_boxes/expansion_box_snake_block_canvas","ae2:shaped/walls/smooth_sky_stone_block","mcwdoors:crimson_barn_glass_door","allthetweaks:atm_star_from_atmstar_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","rftoolsbase:tablet","ae2:network/crafting/256k_cpu_crafting_storage","mcwroofs:magenta_terracotta_lower_roof","megacells:cells/portable/portable_fluid_cell_64m","create:crafting/kinetics/windmill_bearing","railcraft:routing_table_book","ad_astra:vent","refinedstorage:coloring_recipes/light_gray_disk_manipulator","minecraft:crimson_hyphae","create:crafting/kinetics/turntable","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","twilightforest:equipment/fiery_fiery_boots","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","sophisticatedstorage:storage_link","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","handcrafted:terracotta_plate","supplementaries:stone_tile","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","silentgear:coating_smithing_template","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","minecraft:glass_pane","refinedstorage:coloring_recipes/lime_relay","supplementaries:timber_brace","alltheores:iridium_ingot_from_raw_blasting","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","farmersdelight:netherite_knife_smithing","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","railways:crafting/smokestack_oilburner","securitycraft:sonic_security_system","ae2:network/crafting/cpu_crafting_unit","minecraft:clay","mcwbiomesoplenty:pine_pane_window","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","connectedglass:borderless_glass_gray2","connectedglass:borderless_glass_magenta2","ae2additions:components/super/256k","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","ae2:network/blocks/interfaces_interface_part","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:cyan_mosaic_glass","refinedstorage:coloring_recipes/brown_controller","mcwlights:magenta_paper_lamp","create:crafting/kinetics/cogwheel","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","cfm:crimson_upgraded_fence","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_spruce_cupboard_counter","cfm:orange_kitchen_drawer","ae2:materials/annihilationcore","croptopia:chicken_and_noodles","ae2:network/cables/dense_smart_from_smart","mcwdoors:crimson_paper_door","silentgear:stone_rod","ae2:network/cables/dense_covered_green","minecraft:leather_boots","minecraft:netherite_ingot_from_netherite_block","railcraft:steel_spike_maul","refinedstorage:coloring_recipes/gray_fluid_grid","pneumaticcraft:classify_filter","create:crafting/logistics/andesite_tunnel","immersiveengineering:crafting/stick_steel","wirelesschargers:basic_wireless_block_charger","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","mcwfurnitures:stripped_crimson_large_drawer","megacells:network/mega_pattern_provider","mcwfurnitures:stripped_crimson_double_wardrobe","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwlights:pink_paper_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","ae2:network/crystal_resonance_generator","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","cfm:stripped_crimson_kitchen_counter","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","expatternprovider:ebus_out","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","dyenamics:dye_mint_carpet","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","minecraft:magma_block","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","aether:book_of_lore","minecraft:brush","securitycraft:secret_spruce_hanging_sign","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","sfm:cable","minecraft:hay_block","twilightforest:equipment/fiery_fiery_leggings","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:white_upper_steep_roof","delightful:knives/invar_knife","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","railways:crafting/smokestack_coalburner","mcwfences:modern_granite_wall","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","pneumaticcraft:etching_tank","ae2:network/parts/monitors_storage","chemlib:calcium_ingot_from_smelting_calcium_dust","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","cfm:crimson_kitchen_counter","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","evilcraft:smelting/hardened_blood_shard","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","pneumaticcraft:assembly_controller","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","refinedstorage:coloring_recipes/light_gray_network_receiver","bigreactors:blasting/graphite_from_charcoal","refinedstorage:coloring_recipes/pink_wireless_transmitter","mythicbotany:alfsteel_pylon","create:crafting/kinetics/rotation_speed_controller","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","railcraft:goggles","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","refinedstorage:coloring_recipes/green_grid","botania:petal_lime","ae2:tools/certus_quartz_wrench","ae2:network/cables/dense_smart_purple","minecraft:blaze_powder","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","refinedstorage:coloring_recipes/black_relay","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","mcwroofs:crimson_planks_attic_roof","ae2:decorative/sky_stone_small_brick_from_stonecutting","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","mcwfurnitures:crimson_end_table","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","allthecompressed:compress/dirt_7x","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","securitycraft:sentry","botania:tornado_rod","mcwfurnitures:crimson_drawer_counter","minecraft:netherite_block","immersiveengineering:crafting/blueprint_bullets","alltheores:uranium_rod","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","supplementaries:dispenser_minecart","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","handcrafted:crimson_drawer","minecolonies:meat_ravioli","travelersbackpack:wither","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","mcwfurnitures:crimson_desk","refinedstorage:coloring_recipes/purple_disk_manipulator","refinedstorage:coloring_recipes/cyan_detector","mcwroofs:pink_terracotta_top_roof","create:crafting/logistics/redstone_contact","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","occultism:crafting/otherstone_frame","mcwbiomesoplenty:stripped_willow_log_window2","refinedstorage:coloring_recipes/white_pattern_grid","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","securitycraft:secret_warped_hanging_sign","ae2:tools/certus_quartz_spade","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","minecraft:crimson_slab","megacells:cells/portable/portable_fluid_cell_256m","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","twilightforest:equipment/fortification_scepter","twilightforest:carminite_builder","mcwroofs:black_concrete_upper_lower_roof","ae2:network/cables/dense_covered_black","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","expatternprovider:epp_alt","occultism:crafting/spirit_campfire","utilitix:piston_controller_rail","croptopia:roasted_smoking","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","itemcollectors:advanced_collector","minecraft:green_terracotta","minecraft:white_stained_glass","connectedglass:clear_glass_light_gray2","securitycraft:secret_warped_sign_item","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","refinedstorage:coloring_recipes/yellow_controller","securitycraft:taser","allthemodium:vibranium_nugget_from_ingot","ae2:network/cells/fluid_storage_cell_64k","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","refinedstorage:coloring_recipes/crafting_monitor","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","ae2:tools/paintballs_pink","minecraft:purpur_stairs","aiotbotania:livingrock_hoe","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","handcrafted:crimson_fancy_bed","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","reliquary:angelic_feather","ae2:network/parts/toggle_bus","mcwfurnitures:stripped_crimson_modern_desk","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","create:crafting/kinetics/large_cogwheel_from_little","utilitix:linked_crystal","refinedstorage:coloring_recipes/yellow_relay","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","occultism:crafting/book_of_calling_foliot_lumberjack","twigs:jungle_table","twilightforest:carminite_reactor","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","supplementaries:altimeter","ae2:network/cables/dense_smart_gray","pneumaticcraft:assembly_program_drill_laser","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","refinedstorage:coloring_recipes/yellow_security_manager","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","botania:manasteel_shovel","dyenamics:bed/spring_green_bed","ae2:network/parts/terminals_pattern_encoding","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","mcwpaths:cobbled_deepslate_crystal_floor_slab","silentgear:leather_from_scraps","minecraft:purpur_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","twigs:stone_column_stonecutting","mcwlights:jungle_tiki_torch","cfm:lime_picket_gate","mcwpaths:blackstone_crystal_floor","blue_skies:bluebright_spear","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","sophisticatedbackpacks:tool_swapper_upgrade","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","arseng:portable_source_cell_4k","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","botania:super_travel_belt","minecraft:lime_terracotta","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","securitycraft:display_case","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","allthecompressed:compress/andesite_1x","mcwwindows:diorite_four_window","minecraft:stone_slab","mcwfurnitures:stripped_spruce_desk","pneumaticcraft:compressed_bricks","mcwpaths:brick_flagstone","productivebees:stonecutter/comb_canvas_hive","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","refinedstorage:coloring_recipes/blue_disk_manipulator","minecraft:blast_furnace","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbridges:glass_bridge","refinedstorage:coloring_recipes/black_crafter_manager","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","refinedstorage:coloring_recipes/purple_crafter","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","ae2:network/parts/import_bus","croptopia:campfire_molasses","mcwtrpdoors:crimson_tropical_trapdoor","ae2:network/crafting/16k_cpu_crafting_storage","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","ae2:network/upgrade_wireless_crafting_terminal","occultism:crafting/lens_frame","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","mcwroofs:thatch_upper_lower_roof","pneumaticcraft:assembly_drill","additionallanterns:netherite_chain","railcraft:water_tank_siding","refinedstorage:coloring_recipes/green_crafter","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","securitycraft:secret_cherry_hanging_sign","refinedstorage:coloring_recipes/crafting_grid","ae2:network/cables/glass_pink","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","ae2:network/cables/smart_cyan","mcwroofs:black_concrete_steep_roof","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","mcwtrpdoors:crimson_ranch_trapdoor","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","botania:starfield","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","mcwdoors:spruce_waffle_door","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","mcwfences:modern_mud_brick_wall","pneumaticcraft:empty_pcb_from_failed_pcb","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","trashcans:ultimate_trash_can","connectedglass:borderless_glass_orange2","botania:yellow_pavement","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","everythingcopper:copper_helmet","create:crafting/kinetics/yellow_seat","pneumaticcraft:logistics_core","mcwroofs:red_concrete_upper_lower_roof","mcwpaths:andesite_windmill_weave_path","mcwdoors:spruce_swamp_door","modularrouters:blank_upgrade","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","botania:horn_leaves","mcwfurnitures:crimson_counter","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","croptopia:cheeseburger","cfm:stripped_spruce_cabinet","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","minecraft:bread","mcwroofs:green_terracotta_steep_roof","mcwdoors:spruce_barn_glass_door","mcwdoors:garage_silver_door","comforts:hammock_white","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","mcwfurnitures:stripped_oak_cupboard_counter","alltheores:osmium_plate","minecraft:lever","mcwwindows:stripped_warped_stem_four_window","mcwfurnitures:spruce_coffee_table","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","botania:water_rod","botania:horn_grass","securitycraft:secret_cherry_sign_item","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","botania:spectral_platform","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","refinedstorage:coloring_recipes/light_gray_grid","dyenamics:peach_wool","undergarden:smoke_gloomper_leg","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","refinedstorage:coloring_recipes/yellow_crafter_manager","refinedstorage:coloring_recipes/cyan_network_transmitter","supplementaries:sign_post_crimson","minecraft:iron_shovel","tombstone:dark_marble","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","chimes:amethyst_chimes","mcwwindows:stone_brick_arrow_slit","ae2:smelting/smooth_sky_stone_block","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","croptopia:ham_sandwich","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","ad_astra:space_suit","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","cfm:light_blue_picket_fence","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","supplementaries:gold_trapdoor","cfm:fridge_light","forbidden_arcanus:deorum_block_from_deorum_ingot","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mcwfurnitures:stripped_crimson_double_drawer","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","ae2:network/cells/item_storage_cell_256k","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","pneumaticcraft:logistics_frame_passive_provider_self","mcwdoors:jungle_waffle_door","mcwroofs:black_roof_block","mcwbiomesoplenty:hellbark_pane_window","create:crafting/kinetics/wrench","bloodmagic:blood_rune_self_sac_2","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","twilightforest:charm_of_keeping_2","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","modularrouters:sender_module_2_x4","sophisticatedbackpacks:smelting_upgrade","sophisticatedstorage:crimson_barrel","reliquary:fertile_lily_pad","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","additionallanterns:crimson_chain","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","modularrouters:dropper_module","twilightforest:lich_banner_pattern","mcwbiomesoplenty:magic_highley_gate","minecraft:dark_prismarine","ae2:tools/fluix_sword","botania:balance_cloak","simplemagnets:basic_demagnetization_coil","dyenamics:mint_terracotta","mcwwindows:pink_curtain","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","delightful:knives/refined_obsidian_knife","expatternprovider:ei_alt","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","refinedstorage:coloring_recipes/blue_network_receiver","twigs:cobblestone_bricks_stonecutting","forbidden_arcanus:deorum_ingot_from_deorum_nugget","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","twilightforest:knight_phantom_banner_pattern","pneumaticcraft:security_station","framedblocks:framed_cube","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","immersiveengineering:crafting/connector_structural","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/brown_relay","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","supplementaries:wrench","mcwroofs:blue_terracotta_upper_steep_roof","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","minecraft:bow","dyenamics:lavender_wool","aether:skyroot_smithing_table","refinedstorage:coloring_recipes/blue_fluid_grid","aquaculture:brown_mushroom_from_fish","mcwfurnitures:crimson_large_drawer","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","chemlib:periodic_table","minecraft:andesite_stairs","expatternprovider:wireless_ex_pat","botania:super_lava_pendant","forbidden_arcanus:deorum_nugget_from_deorum_ingot","refinedstorage:coloring_recipes/purple_crafting_monitor","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwfurnitures:stripped_crimson_striped_chair","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","minecraft:jungle_slab","refinedstorage:coloring_recipes/brown_crafter","supplementaries:sconce","silentgear:rough_rod","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","minecraft:skull_banner_pattern","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","occultism:crafting/sacrificial_bowl","railcraft:iron_tunnel_bore_head","mcwfurnitures:spruce_glass_table","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","minecraft:mossy_stone_brick_wall","connectedglass:borderless_glass_brown2","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","forbidden_arcanus:deorum_door","mcwbiomesoplenty:jacaranda_plank_window2","sophisticatedbackpacks:feeding_upgrade","minecraft:granite","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwfurnitures:spruce_covered_desk","botania:ghost_rail","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","minecraft:black_dye_from_wither_rose","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","refinedstorage:coloring_recipes/network_receiver","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","mcwdoors:spruce_mystic_door","sophisticatedstorage:backpack_advanced_void_upgrade_from_storage_advanced_void_upgrade","mcwtrpdoors:spruce_beach_trapdoor","enderio:enderios","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwroofs:andesite_roof","botania:black_pavement","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","supplementaries:pedestal","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:network/cables/glass_yellow","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","dyenamics:rose_terracotta","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","twigs:crimson_table","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","alltheores:signalum_plate","mcwdoors:jungle_tropical_door","expatternprovider:oversize_interface","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","refinedstorage:coloring_recipes/magenta_grid","minecraft:blackstone_stairs","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","sophisticatedstorage:storage_feeding_upgrade_from_backpack_feeding_upgrade","dyenamics:icy_blue_wool","forbidden_arcanus:deorum_soul_lantern","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","botania:mana_ring_greater","twilightforest:equipment/fiery_chestplate","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","refinedstorage:coloring_recipes/brown_detector","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","mcwdoors:oak_barn_door","create:andesite_bars_from_andesite_alloy_stonecutting","botania:alchemy_catalyst","refinedstorage:coloring_recipes/blue_pattern_grid","allthemodium:ancient_stone_bricks","mcwfurnitures:spruce_double_drawer_counter","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","dyenamics:dye_bubblegum_carpet","botania:redstone_root","ae2:tools/paintballs_brown","mcwwindows:stripped_jungle_log_window2","create:crafting/kinetics/display_board","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","dyenamics:dye_aquamarine_carpet","refinedstorage:coloring_recipes/light_gray_crafting_monitor","minecraft:cobbled_deepslate_wall","minecraft:purpur_slab_from_purpur_block_stonecutting","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","botania:lava_pendant","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","reliquary:fertile_essence","supplementaries:candle_holders/candle_holder_black","forbidden_arcanus:smelting/arcane_crystal_from_smelting","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","ae2:network/cables/dense_smart_light_gray","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","mcwroofs:white_concrete_attic_roof","cfm:crimson_desk","blue_skies:frostbright_spear","twilightdelight:torchberries_crate","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","botania:hourglass","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","reliquary:uncrafting/ender_pearl","mcwfurnitures:stripped_spruce_coffee_table","minecraft:ender_eye","additionallanterns:obsidian_chain","supplementaries:blackstone_lamp","mcwfurnitures:spruce_double_wardrobe","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","refinedstorage:coloring_recipes/disk_manipulator","mcwlights:cross_lantern","mcwlights:brown_lamp","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwwindows:bricks_four_window","railcraft:receiver_circuit","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_packed_mud","minecraft:green_candle","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","rftoolsbase:manual","enderio:wood_gear_corner","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","ae2:network/parts/terminals_pattern_access","additionallanterns:diamond_chain","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","ae2:network/blocks/io_condenser","ae2:block_cutter/walls/quartz_wall","sophisticatedbackpacks:smoking_upgrade","supplementaries:stonecutting/stone_tile","railcraft:steel_tank_wall","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","securitycraft:trophy_system","ad_astra:small_green_industrial_lamp","pneumaticcraft:reinforced_brick_from_slab","minecraft:cooked_beef","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwtrpdoors:crimson_bark_trapdoor","mcwlights:yellow_paper_lamp","twilightdelight:torchberry_cookie","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","advanced_ae:quantumstorage128","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","refinedstorage:coloring_recipes/light_gray_controller","bloodmagic:blood_rune_displacement_2","securitycraft:keycard_lv4","securitycraft:keycard_lv3","cfm:crimson_table","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:age_detector","pneumaticcraft:wall_lamp_orange","minecraft:gold_nugget_from_smelting","mcwwindows:black_curtain","dyenamics:wine_stained_glass","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:cobbled_deepslate_crystal_floor_stairs","botania:pixie_ring","blue_skies:starlit_spear","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","refinedstorage:coloring_recipes/grid","botania:drum_gathering","refinedstorage:coloring_recipes/orange_network_transmitter","mcwwindows:jungle_plank_window2","ad_astra:space_pants","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","occultism:smelting/iesnium_ingot_from_raw","refinedstorage:coloring_recipes/lime_fluid_grid","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","refinedstorage:coloring_recipes/red_crafting_grid","productivebees:hives/advanced_dark_oak_canvas_hive","mcwdoors:crimson_japanese_door","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","enderio:resetting_lever_five_inv","mcwfurnitures:crimson_drawer","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","utilitix:armed_stand","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","bigreactors:turbine/ridiculite_block","ae2:network/cables/smart_blue","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","appmek:portable_chemical_storage_cell_64k","botania:turntable","ae2:network/cells/item_storage_components_cell_1k_part","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","refinedstorage:coloring_recipes/orange_relay","ad_astra:steel_tank","minecraft:netherite_scrap","supplementaries:daub","railcraft:steel_chestplate","mcwfurnitures:stripped_jungle_end_table","create:crafting/kinetics/wooden_bracket","productivebees:stonecutter/acacia_canvas_hive","securitycraft:secret_oak_hanging_sign","mcwfences:oak_hedge","mcwdoors:spruce_japanese2_door","enderio:fluid_tank","arseng:portable_source_cell_1k","botania:petal_white","ae2:decorative/quartz_block","ae2:network/cables/dense_smart_orange","bigreactors:energizer/casing","mcwpaths:stone_flagstone","cfm:crimson_park_bench","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","ae2:block_cutter/slabs/smooth_sky_stone_slab","mcwfurnitures:crimson_bookshelf_drawer","mcwbiomesoplenty:hellbark_highley_gate","create:crafting/logistics/redstone_link","minecraft:enchanting_table","mcwpaths:cobbled_deepslate_square_paving","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deepresonance:radiation_suit_chestplate","refinedstorage:coloring_recipes/lime_disk_manipulator","refinedstorage:coloring_recipes/pink_disk_manipulator","mcwlights:brown_paper_lamp","minecraft:campfire","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","refinedstorage:coloring_recipes/pink_network_transmitter","megacells:cells/portable/portable_item_cell_256m","farmersdelight:cooking/chicken_soup","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","pneumaticcraft:logistics_configurator","mysticalagriculture:imperium_block_uncraft","pneumaticcraft:logistics_frame_default_storage_self","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","cfm:crimson_upgraded_gate","minecraft:black_stained_glass"],toBeDisplayed:["botania:dreamwood_planks","botania:star_sword","mcwfurnitures:stripped_spruce_bookshelf","mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","allthecompressed:compress/diamond_block_1x","sfm:disk","croptopia:mead","botania:alfheim_portal","mcwfurnitures:stripped_jungle_desk","arseng:source_storage_cell_256k","biomesoplenty:fir_boat","mcwwindows:birch_four_window","pneumaticcraft:chunkloader_upgrade","refinedstorage:coloring_recipes/red_detector","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","minecraft:stonecutter","botania:ender_hand","littlelogistics:seater_barge","refinedstorage:coloring_recipes/pink_security_manager","immersiveengineering:crafting/gunpowder_from_dusts","mcwroofs:blue_terracotta_attic_roof","modularrouters:energy_distributor_module","handcrafted:spider_trophy","minecraft:bone_block","refinedstorage:coloring_recipes/pink_controller","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwroofs:lime_terracotta_lower_roof","refinedstorage:coloring_recipes/red_pattern_grid","croptopia:shaped_beef_stew","ae2:network/cables/dense_smart_green","mcwroofs:red_terracotta_lower_roof","pneumaticcraft:wall_lamp_white","ae2:network/cells/item_storage_cell_256k_storage","mcwlights:chain_wall_lantern","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:bell_lantern","enderio:resetting_lever_ten_inv_from_prev","mcwtrpdoors:acacia_barrel_trapdoor","cfm:jungle_coffee_table","xnet:connector_green","mcwpaths:jungle_planks_path","advanced_ae:smallappupgrade","minecraft:gold_nugget_from_blasting","minecolonies:cheese_ravioli","securitycraft:reinforced_white_stained_glass","allthemodium:ancient_mossy_stone_from_vinecrafting","minecolonies:chainmailchestplate","sophisticatedstorage:backpack_advanced_magnet_upgrade_from_storage_advanced_magnet_upgrade","mythicbotany:central_rune_holder","botania:missile_rod","sophisticatedbackpacks:void_upgrade","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","railcraft:diamond_crowbar","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","productivebees:stonecutter/dark_oak_canvas_expansion_box","rftoolsutility:machineinformation_module","refinedstorage:coloring_recipes/gray_grid","mcwpaths:cobbled_deepslate_running_bond_slab","productivetrees:wood/soul_tree_wood","mcwwindows:oak_plank_pane_window","sophisticatedstorage:crimson_limited_barrel_2","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","mcwfurnitures:spruce_modern_desk","sophisticatedstorage:crimson_limited_barrel_1","mcwfurnitures:spruce_drawer","sophisticatedstorage:crimson_limited_barrel_4","bigreactors:turbine/inanite_block","sophisticatedstorage:crimson_limited_barrel_3","alltheores:iridium_ingot_from_raw","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","twigs:polished_rhyolite_stonecutting","mcwroofs:gray_top_roof","supplementaries:candle_holders/candle_holder_green","botania:manasteel_leggings","utilitix:oak_shulker_boat_with_shell","create:crafting/kinetics/gearbox","botania:terrasteel_block","arseng:portable_source_cell_64k","mcwroofs:magenta_terracotta_roof","securitycraft:reinforced_cherry_fence_gate","minecraft:leather_helmet","rftoolsutility:screen_link","minecraft:glow_item_frame","securitycraft:reinforced_acacia_fence","mcwpaths:blackstone_flagstone_slab","create:crafting/materials/sand_paper","allthecompressed:compress/vibranium_allthemodium_alloy_block_1x","dyenamics:banner/icy_blue_banner","mcwroofs:gray_terracotta_roof","mcwroofs:white_roof_slab","mcwfurnitures:stripped_crimson_covered_desk","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","pneumaticcraft:crop_support","supplementaries:bubble_blower","refinedstorage:coloring_recipes/white_disk_manipulator","mcwdoors:crimson_bamboo_door","supplementaries:crank","securitycraft:secret_bamboo_sign_item","minecraft:recovery_compass","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","allthecompressed:compress/diamond_block_6x","aether:skyroot_pickaxe_repairing","alltheores:iron_plate","allthearcanistgear:vibranium_leggings_smithing","mcwbridges:glass_bridge_stair","mcwwindows:yellow_mosaic_glass","modularrouters:bulk_item_filter","mcwroofs:black_steep_roof","allthecompressed:compress/ancient_stone_1x","minecraft:unobtainium_mage_chestplate_smithing","supplementaries:netherite_trapdoor","productivebees:stonecutter/fir_canvas_expansion_box","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","littlelogistics:rapid_hopper","refinedstorage:coloring_recipes/magenta_security_manager","minecraft:pink_terracotta","aiotbotania:livingrock_axe","refinedstorage:coloring_recipes/blue_crafting_grid","mcwbiomesoplenty:mahogany_plank_pane_window","allthecompressed:compress/diamond_block_5x","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwtrpdoors:spruce_barred_trapdoor","immersiveengineering:crafting/rockcutter","ae2:network/cables/dense_smart_light_blue","mcwpaths:cobblestone_dumble_paving","cfm:spruce_upgraded_fence","mcwroofs:crimson_top_roof","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","dyenamics:aquamarine_concrete_powder","refinedstorage:coloring_recipes/fluid_grid","mcwroofs:crimson_planks_upper_steep_roof","nethersdelight:golden_machete","ae2:network/cables/smart_fluix_clean","ae2:network/parts/annihilation_plane_alt","handcrafted:jungle_cupboard","mcwroofs:green_terracotta_top_roof","ae2:network/blocks/energy_energy_cell","mcwwindows:acacia_curtain_rod","productivebees:hives/advanced_cherry_canvas_hive","mcwlights:chain_lantern","securitycraft:camera_monitor","allthecompressed:compress/quartz_block_1x","dyenamics:spring_green_wool","immersiveengineering:crafting/clinker_brick_sill","railcraft:standard_rail_from_rail","refinedstorage:coloring_recipes/brown_security_manager","allthecompressed:decompress/gravel_4x","twilightdelight:torchberry_pie","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","refinedstorage:coloring_recipes/white_grid","advanced_ae:advpartenc","mcwroofs:black_top_roof","botania:bauble_box","mcwfences:modern_nether_brick_wall","botania:vial","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","mcwroofs:base_roof","aether:netherite_hoe_repairing","minecraft:vibranium_spell_book_smithing","minecraft:golden_hoe","minecraft:fire_charge","cfm:oak_kitchen_counter","ae2:tools/misctools_charged_staff","cfm:purple_trampoline","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","botania:pink_petal_block","minecraft:dye_green_bed","cfm:stripped_spruce_bedside_cabinet","railcraft:any_detector","connectedglass:borderless_glass_purple2","alchemistry:reactor_energy","botania:mana_distributor","allthecompressed:compress/netherrack_7x","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","sophisticatedstorage:controller","aiotbotania:livingrock_shovel","mcwroofs:stone_bricks_upper_steep_roof","minecraft:stone_button","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","pneumaticcraft:compressed_iron_helmet","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","artifacts:eternal_steak_furnace","mcwfurnitures:oak_striped_chair","minecraft:tinted_glass","aether:golden_pickaxe_repairing","ae2:network/wireless_terminal","pneumaticcraft:regulator_tube_module","mcwwindows:birch_plank_window","minecraft:sandstone","mcwbiomesoplenty:stripped_dead_log_window2","pneumaticcraft:logistics_frame_active_provider_self","allthemodium:vibranium_allthemodium_alloy_ingot_from_block","minecraft:lodestone","occultism:crafting/magic_lamp_empty","mcwfurnitures:stripped_crimson_bookshelf","appbot:portable_mana_storage_cell_4k","mcwlights:black_lamp","minecraft:purpur_block","pneumaticcraft:assembly_io_unit_import_from_export","productivebees:hives/advanced_crimson_canvas_hive","railcraft:brass_ingot_crafted_with_ingots","megacells:crafting/greater_energy_card","mcwdoors:spruce_four_panel_door","farmersdelight:potato_from_crate","create:crafting/kinetics/speedometer","minecraft:polished_blackstone_slab","cfm:stripped_spruce_chair","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:stone_stairs","handcrafted:evoker_trophy","twilightforest:compressed_blocks/steeleaf_block","allthetweaks:nether_star_from_nether_star_block","refinedstorage:coloring_recipes/brown_pattern_grid","refinedstorage:coloring_recipes/yellow_fluid_grid","pneumaticcraft:transfer_gadget","mcwfurnitures:stripped_spruce_striped_chair","connectedglass:borderless_glass_red2","refinedstorage:coloring_recipes/purple_crafting_grid","constructionwand:core_destruction","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","productivebees:expansion_boxes/expansion_box_acacia_canvas","rftoolsutility:redstone_module","handcrafted:terracotta_thick_pot","mcwdoors:jungle_bark_glass_door","refinedstorage:coloring_recipes/pink_network_receiver","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","productivetrees:sawdust_to_paper_water_bottle","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","refinedstorage:coloring_recipes/white_detector","blue_skies:glowing_blinding_stone","ae2:tools/paintballs_light_blue","mcwbiomesoplenty:umbran_plank_four_window","botania:livingrock_slate","tombstone:grave_plate","cfm:stripped_crimson_mail_box","handcrafted:jungle_side_table","mcwbridges:spruce_bridge_pier","enderio:resetting_lever_five","productivebees:stonecutter/oak_canvas_hive","mcwroofs:cyan_terracotta_roof","cfm:gray_trampoline","twilightforest:equipment/fiery_boots","mcwbiomesoplenty:empyreal_wired_fence","sophisticatedbackpacks:upgrade_base","refinedstorage:coloring_recipes/red_grid","handcrafted:jungle_bench","cfm:purple_cooler","ae2:block_cutter/slabs/sky_stone_slab","ae2:network/parts/terminals","securitycraft:keypad_frame","minecolonies:pasta_tomato","immersiveengineering:crafting/chute_steel","mcwlights:birch_ceiling_fan_light","twilightforest:equipment/fiery_fiery_helmet","bigreactors:turbine/basic/shaft","croptopia:hamburger","enderio:resetting_lever_thirty_inv_from_prev","botania:terrasteel_leggings","littlelogistics:barrel_barge","mcwroofs:yellow_terracotta_steep_roof","securitycraft:whitelist_module","cfm:spruce_bedside_cabinet","botania:incense_stick","mcwroofs:stone_lower_roof","mcwbiomesoplenty:pine_plank_window2","ae2:network/blocks/energy_energy_acceptor","pneumaticcraft:vortex_tube","refinedstorage:coloring_recipes/orange_wireless_transmitter","xnet:connector_routing","blue_skies:glowing_nature_stone","dyenamics:aquamarine_terracotta","mcwbiomesoplenty:palm_plank_window2","travelersbackpack:cake","bloodmagic:ritual_diviner_1","megacells:network/mega_interface","mcwroofs:green_terracotta_attic_roof","productivebees:stonecutter/palm_canvas_expansion_box","securitycraft:reinforced_black_stained_glass_pane_from_glass","twilightforest:equipment/fiery_helmet","utilitix:directional_highspeed_rail","handcrafted:crimson_cupboard","mcwwindows:light_blue_curtain","refinedstorage:coloring_recipes/magenta_network_receiver","pneumaticcraft:wall_lamp_inverted_white","mcwlights:golden_chain","megacells:crafting/mega_crafting_unit","mcwbiomesoplenty:magic_picket_fence","refinedstorage:coloring_recipes/brown_network_receiver","mcwfurnitures:stripped_spruce_double_drawer","securitycraft:reinforced_purple_stained_glass","ae2:network/crafting/cpu_crafting_monitor","mcwroofs:cyan_terracotta_steep_roof","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","cfm:stripped_crimson_kitchen_sink_dark","farmersdelight:cooking/rabbit_stew","cfm:warped_mail_box","mcwpaths:andesite_flagstone_path","xnet:antenna_base","cfm:green_grill","refinedstorage:coloring_recipes/light_gray_fluid_grid","minecraft:cooked_chicken_from_smoking","create:crafting/kinetics/chute","refinedstorage:coloring_recipes/purple_crafter_manager","mcwwindows:dark_prismarine_window2","minecraft:cyan_glazed_terracotta","pneumaticcraft:search_upgrade","aether:iron_sword_repairing","cfm:orange_grill","pneumaticcraft:assembly_platform","mcwroofs:white_terracotta_attic_roof","advanced_ae:quantum_boots_item_reset","ae2:network/blocks/storage_chest","securitycraft:secret_bamboo_hanging_sign","cfm:jungle_desk","mcwroofs:oak_roof","refinedstorage:coloring_recipes/yellow_crafter","cfm:stripped_crimson_chair","minecraft:iron_hoe","minecraft:iron_ingot_from_iron_block","mcwroofs:light_gray_roof_slab","mcwtrpdoors:oak_barred_trapdoor","mcwfurnitures:jungle_drawer","mcwroofs:light_blue_concrete_lower_roof","twigs:chiseled_bricks_stonecutting","mcwroofs:purple_striped_awning","create:crafting/kinetics/fluid_tank","xnet:netcable_routing","mcwfences:nether_brick_pillar_wall","ad_astra:oxygen_loader","mcwroofs:thatch_roof","mcwdoors:spruce_glass_door","mcwbiomesoplenty:willow_curved_gate","mcwbridges:rope_jungle_bridge","delightful:knives/osmium_knife","minecraft:jungle_trapdoor","ae2:network/wireless_booster","ae2:tools/portable_fluid_cell_256k","dyenamics:fluorescent_wool","refinedstorage:coloring_recipes/blue_network_transmitter","additionallanterns:end_stone_lantern","connectedglass:clear_glass_light_blue2","cfm:mangrove_kitchen_sink_dark","sophisticatedstorage:storage_output","mcwdoors:oak_four_panel_door","connectedglass:clear_glass_cyan2","ae2:tools/paintballs_orange","mcwfences:blackstone_brick_railing_gate","croptopia:crab_legs","sophisticatedbackpacks:filter_upgrade","mcwdoors:crimson_glass_door","forbidden_arcanus:arcane_crystal_dust","botania:smelt_rod","mcwbiomesoplenty:maple_curved_gate","bigreactors:turbine/reinforced/activetap_fe","mcwroofs:black_terracotta_roof","megacells:cells/portable/portable_fluid_cell_4m","ae2:network/cables/dense_covered_purple","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","ae2:network/cables/dense_smart_blue","cfm:green_kitchen_sink","pneumaticcraft:thermal_compressor","pneumaticcraft:refinery","minecraft:chiseled_bookshelf","minecolonies:veggie_ravioli","mcwfences:diorite_railing_gate","allthetweaks:nether_star_block","minecraft:polished_blackstone_slab_from_polished_blackstone_stonecutting","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","bigreactors:crafting/blutonium_storage_to_component","productivebees:stonecutter/mangrove_canvas_hive","minecraft:target","productivebees:hives/advanced_oak_canvas_hive","ae2:decorative/quartz_fixture_from_anchors","additionallanterns:andesite_lantern","mcwpaths:cobblestone_basket_weave_paving","mcwroofs:green_concrete_upper_lower_roof","allthearcanistgear:unobtainium_hat_smithing","ae2:network/parts/export_bus","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","supplementaries:evilcraft/sign_post_undead","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","minecraft:white_terracotta","mcwwindows:dark_prismarine_brick_gothic","botania:tiny_planet_block","mcwroofs:black_roof_slab","mcwbiomesoplenty:pine_stockade_fence","sophisticatedstorage:compression_upgrade","mcwpaths:cobblestone_diamond_paving","botania:mana_detector","mcwlights:golden_chandelier","cfm:blue_grill","mcwfurnitures:oak_double_drawer_counter","minecraft:armor_stand","allthecompressed:compress/unobtainium_vibranium_alloy_block_1x","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwroofs:orange_terracotta_top_roof","mcwwindows:birch_plank_pane_window","enderio:resetting_lever_ten_inv","mcwdoors:crimson_cottage_door","twigs:chiseled_bricks","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","sophisticatedbackpacks:chipped/alchemy_bench_upgrade","biomesoplenty:redwood_boat","railcraft:iron_crowbar","mcwfences:mesh_metal_fence_gate","minecraft:quartz_slab_from_stonecutting","mcwbiomesoplenty:fir_pyramid_gate","mcwroofs:blue_terracotta_roof","xnet:netcable_yellow","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","croptopia:cheese","mcwdoors:crimson_classic_door","mcwpaths:blackstone_crystal_floor_stairs","dyenamics:bed/rose_bed","dyenamics:navy_wool","mcwwindows:magenta_mosaic_glass_pane","pneumaticcraft:reinforced_bricks_from_tile_stonecutting","megacells:cells/portable/portable_fluid_cell_1m","mcwtrpdoors:metal_full_trapdoor","mcwroofs:spruce_top_roof","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","allthecompressed:compress/aluminum_block_1x","additionallanterns:iron_lantern","refinedstorage:coloring_recipes/black_detector","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","sophisticatedstorage:backpack_stack_upgrade_tier_4_from_storage_stack_upgrade_tier_5","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwroofs:green_terracotta_upper_lower_roof","reliquary:void_tear","mcwpaths:andesite_running_bond_slab","appbot:mana_cell_housing","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","minecraft:chiseled_stone_bricks_stone_from_stonecutting","croptopia:potato_chips","pneumaticcraft:compressed_brick_pillar","mcwbiomesoplenty:fir_hedge","handcrafted:jungle_pillar_trim","botania:red_string_fertilizer","minecraft:netherite_sword_smithing","mcwfences:birch_highley_gate","ae2:network/cables/dense_smart_lime","botania:manasteel_axe","create:crafting/kinetics/mechanical_crafter","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","ae2:block_cutter/slabs/fluix_slab","immersiveengineering:crafting/torch","allthemodium:ancient_stone_slabs","allthecompressed:compress/terracotta_1x","mcwfences:warped_pyramid_gate","dankstorage:4_to_5","delightful:food/baklava","oceansdelight:seagrass_salad","mcwdoors:jungle_stable_door","mcwdoors:metal_hospital_door","railcraft:signal_circuit","ae2:tools/paintballs_red","twilightforest:charm_of_life_2","minecraft:magenta_concrete_powder","minecraft:red_dye_from_poppy","dyenamics:bubblegum_stained_glass","dyenamics:dye_conifer_carpet","mcwbiomesoplenty:pine_window2","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","appmek:chemical_storage_cell_256k","mcwlights:cherry_tiki_torch","connectedglass:borderless_glass_pane3","croptopia:buttered_toast","simplylight:illuminant_purple_block_toggle","mcwfurnitures:stripped_spruce_glass_table","cfm:pink_cooler","littlelogistics:junction_rail","refinedstorage:coloring_recipes/magenta_controller","dyenamics:ultramarine_terracotta","minecraft:mossy_stone_brick_slab_from_mossy_stone_brick_stonecutting","refinedstorage:coloring_recipes/red_relay","cfm:red_grill","ae2:tools/paintballs_gray","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwwindows:blackstone_brick_gothic","minecraft:stone_bricks_from_stone_stonecutting","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","refinedstorage:coloring_recipes/red_crafter","dankstorage:1_to_2","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","immersiveengineering:crafting/sword_steel","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","croptopia:chicken_and_dumplings","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:spruce_wardrobe","botania:gray_petal_block","utilitix:tiny_charcoal_to_tiny","connectedglass:borderless_glass_light_blue2","mcwtrpdoors:jungle_paper_trapdoor","refinedstorage:coloring_recipes/magenta_crafting_grid","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","cfm:stripped_spruce_kitchen_counter","immersiveengineering:crafting/connector_lv_relay","sgjourney:sandstone_with_lapis","ae2:network/cells/fluid_storage_cell_256k","allthecompressed:compress/redstone_block_1x","mcwfences:red_sandstone_pillar_wall","ae2additions:components/1024k","twigs:quartz_column","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","chemlib:calcium_block_to_ingot","minecraft:purpur_stairs_from_purpur_block_stonecutting","mcwroofs:blue_concrete_roof","travelersbackpack:bee","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","minecraft:crimson_trapdoor","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","supplementaries:sign_post_oak","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","botania:livingrock_slab","immersiveengineering:crafting/toolbox","refinedstorage:coloring_recipes/brown_crafter_manager","refinedstorage:coloring_recipes/black_grid","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","botania:terrasteel_boots","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","twilightdelight:hydra_burger","refinedstorage:coloring_recipes/controller","chemlib:gallium_ingot_from_blasting_gallium_dust","mcwroofs:orange_concrete_top_roof","forbidden_arcanus:aureal_bottle","bigreactors:turbine/basic/passivetap_fe","mcwpaths:brick_running_bond","minecraft:polished_blackstone_from_blackstone_stonecutting","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","ad_astra:engine_frame","mcwwindows:hammer","handcrafted:fox_trophy","shrink:shrinking_device","mcwpaths:cobblestone_clover_paving","mcwroofs:gray_attic_roof","mcwdoors:jungle_japanese2_door","securitycraft:reinforced_andesite_with_vanilla_cobblestone","minecraft:crimson_planks","forbidden_arcanus:darkstone_pedestal","railcraft:blast_furnace_bricks","ae2:network/cables/dense_covered_pink","mcwfurnitures:crimson_modern_chair","ae2:network/cables/glass_brown","handcrafted:terracotta_wide_pot","pneumaticcraft:wall_lamp_pink","mcwwindows:purple_curtain","mcwroofs:blackstone_lower_roof","railcraft:steel_anvil","botania:red_string_relay","ae2:tools/portable_item_cell_256k","botania:glimmering_dreamwood","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","botania:runic_altar_alt","minecraft:flower_banner_pattern","mcwbiomesoplenty:empyreal_window","securitycraft:secret_crimson_sign_item","mcwfurnitures:spruce_stool_chair","mcwroofs:purple_terracotta_upper_lower_roof","mcwroofs:gray_striped_awning","mcwwindows:lime_curtain","ae2:network/parts/cable_anchor","mcwbiomesoplenty:stripped_hellbark_log_window","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting_raw_copper","mcwlights:reinforced_torch","blue_skies:cake_compat","minecraft:mangrove_planks","ae2:network/blocks/crystal_processing_growth_accelerator","minecraft:gold_ingot_from_blasting_raw_gold","mcwdoors:spruce_stable_head_door","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mythicbotany:kvasir_mead","productivebees:stonecutter/aspen_canvas_expansion_box","pneumaticcraft:liquid_hopper","refinedstorage:coloring_recipes/pink_fluid_grid","botania:green_pavement","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","dyenamics:lavender_concrete_powder","handcrafted:terracotta_cup","mcwroofs:white_concrete_roof","ad_astra:ti_69","mcwwindows:window_half_bar_base","megacells:cells/portable/portable_item_cell_4m","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwfurnitures:stripped_crimson_coffee_table","immersiveengineering:crafting/sawdust","mcwfurnitures:oak_modern_chair","mcwdoors:crimson_stable_head_door","mcwwindows:oak_curtain_rod","railcraft:sheep_detector","croptopia:fried_chicken","bloodmagic:blood_rune_orb_2","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","botania:shimmerwood_planks","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","connectedglass:borderless_glass_lime2","ae2:network/parts/terminals_crafting","megacells:cells/portable/portable_item_cell_1m","cfm:stripped_oak_chair","xnet:netcable_blue","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","cfm:diving_board","mcwroofs:lime_concrete_steep_roof","cfm:rock_path","botania:mana_spreader","connectedglass:clear_glass_yellow2","minecraft:wooden_hoe","minecraft:cooked_beef_from_campfire_cooking","createoreexcavation:diamond_drill","minecraft:quartz_pillar","mcwbiomesoplenty:magic_plank_pane_window","refinedstorage:coloring_recipes/light_gray_network_transmitter","botania:slingshot","immersiveengineering:crafting/axe_steel","ae2:tools/certus_quartz_pickaxe","ae2:network/cables/glass_gray","sophisticatedbackpacks:jukebox_upgrade","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","ae2:network/blocks/crystal_processing_charger","ae2:network/parts/energy_level_emitter","productivebees:stonecutter/jungle_canvas_hive","railcraft:gold_gear","mcwlights:light_gray_lamp","mcwwindows:mangrove_curtain_rod","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","refinedstorage:coloring_recipes/yellow_network_receiver","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","handcrafted:jungle_couch","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","mcwbridges:blackstone_bridge","securitycraft:panic_button","productivebees:stonecutter/snake_block_canvas_hive","mcwroofs:gutter_middle_brown","mcwtrpdoors:spruce_glass_trapdoor","pneumaticcraft:flux_compressor","mcwroofs:gutter_base_green","cfm:stripped_crimson_table","mcwfurnitures:stripped_spruce_double_wardrobe","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","railcraft:steel_leggings","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:blackstone_stairs_from_blackstone_stonecutting","mcwwindows:crimson_planks_four_window","minecraft:piston","ad_astra:large_gas_tank","refinedstorage:coloring_recipes/orange_grid","mcwlights:white_lamp","mcwfurnitures:jungle_coffee_table","appmek:chemical_storage_cell_4k","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","minecraft:white_banner","naturescompass:natures_compass","minecraft:smooth_quartz","mcwbiomesoplenty:dead_plank_four_window","undergarden:regalium_block","ae2:network/cells/item_storage_components_cell_16k_part","mcwbiomesoplenty:umbran_plank_pane_window","botania:virus_necrodermal","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","refinedstorage:coloring_recipes/magenta_network_transmitter","botania:mana_bomb","minecraft:wooden_axe","cfm:stripped_oak_desk","connectedglass:borderless_glass_light_gray2","refinedstorage:coloring_recipes/magenta_crafting_monitor","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwroofs:gray_upper_steep_roof","botania:third_eye","productivetrees:wood/brown_amber_wood","mcwwindows:metal_curtain_rod","refinedstorage:coloring_recipes/magenta_relay","mcwlights:cross_wall_lantern","farmersdelight:fried_egg_from_campfire_cooking","mcwroofs:oak_steep_roof","forbidden_arcanus:edelwood_planks","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","productivebees:stonecutter/mahogany_canvas_hive","botania:mana_diamond_block","supplementaries:daub_brace","domum_ornamentum:black_floating_carpet","mcwfences:spruce_horse_fence","croptopia:beef_jerky","handcrafted:stackable_book","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","minecraft:stone_stairs_from_stone_stonecutting","forbidden_arcanus:deorum_trapdoor","silentgear:sinew_fiber","mcwfences:crimson_curved_gate","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","mcwwindows:dark_prismarine_brick_arrow_slit","mcwtrpdoors:spruce_mystic_trapdoor","allthecompressed:compress/steel_block_1x","comforts:sleeping_bag_to_white","create:crafting/kinetics/copper_valve_handle","ad_astra:space_helmet","create:crafting/appliances/clipboard","supplementaries:flags/flag_magenta","minecraft:diamond_axe","productivebees:stonecutter/jacaranda_canvas_expansion_box","supplementaries:candle_holders/candle_holder_red","mcwroofs:red_terracotta_upper_lower_roof","cfm:stripped_birch_kitchen_sink_dark","refinedstorage:coloring_recipes/lime_crafting_grid","aether:skyroot_piston","refinedstorage:coloring_recipes/brown_crafting_monitor","mcwroofs:black_terracotta_top_roof","mcwbiomesoplenty:fir_horse_fence","appmek:chemical_storage_cell_1k","mcwroofs:lime_striped_awning","additionallanterns:bricks_lantern","travelersbackpack:skeleton","handcrafted:creeper_trophy","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwroofs:gray_terracotta_lower_roof","bigreactors:crafting/magentite_component_to_storage","mcwlights:covered_wall_lantern","mcwfurnitures:spruce_lower_triple_drawer","productivebees:stonecutter/oak_canvas_expansion_box","expatternprovider:ex_io_port","dyenamics:banner/bubblegum_banner","domum_ornamentum:brown_stone_bricks","farmersdelight:stove","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","refinedstorage:coloring_recipes/lime_detector","mcwpaths:blackstone_flagstone_path","rftoolsbuilder:vehicle_control_module","botania:petal_gray","occultism:blasting/iesnium_ingot_from_raw","utilitarian:utility/spruce_logs_to_pressure_plates","travelersbackpack:sheep","mcwroofs:red_terracotta_upper_steep_roof","create:crafting/curiosities/minecart_coupling","mcwfences:granite_grass_topped_wall","botania:fire_rod","botania:petal_pink_double","handcrafted:crimson_counter","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","undergarden:smelt_gloomper_leg","mcwbiomesoplenty:rainbow_birch_hedge","botania:spawner_mover","productivebees:expansion_boxes/expansion_box_crimson_canvas","immersiveengineering:crafting/pickaxe_steel","mcwbiomesoplenty:stripped_umbran_log_window","pneumaticcraft:pressure_chamber_interface","mcwfurnitures:stripped_crimson_chair","xnet:antenna","mcwfurnitures:jungle_stool_chair","mcwroofs:light_gray_steep_roof","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","refinedstorage:coloring_recipes/white_crafter_manager","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","dyenamics:ultramarine_wool","productivebees:stonecutter/bamboo_canvas_expansion_box","botania:bifrost_pane","mcwroofs:cobblestone_roof","ae2:misc/chests_sky_stone","handcrafted:pillager_trophy","sfm:water_tank","botania:scorched_seeds","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","refinedstorage:coloring_recipes/pink_crafting_monitor","immersiveengineering:crafting/blueprint_components","nethersdelight:iron_machete","allthecompressed:compress/blackstone_1x","mcwfences:dark_oak_picket_fence","refinedstorage:coloring_recipes/pink_crafter_manager","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","securitycraft:secret_mangrove_hanging_sign","mcwpaths:andesite_square_paving","pneumaticcraft:pressure_gauge","pneumaticcraft:programmer","mcwfences:dark_oak_hedge","pylons:expulsion_pylon","botania:manasteel_chestplate","botania:magenta_shiny_flower","mcwfences:bamboo_highley_gate","mythicbotany:yggdrasil_branch","mcwroofs:red_terracotta_attic_roof","mcwfences:end_brick_grass_topped_wall","allthemodium:teleport_pad","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","allthemodium:unobtainium_allthemodium_alloy_ingot_from_block","computercraft:pocket_computer_advanced","ae2:tools/paintballs_blue","mcwroofs:jungle_roof","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_spruce_wardrobe","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","ae2:tools/paintballs_lime","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","megacells:cells/cell_component_1m","mcwtrpdoors:crimson_mystic_trapdoor","mcwroofs:white_terracotta_roof","mcwwindows:white_curtain","create:crafting/kinetics/sequenced_gearshift","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","refinedstorage:coloring_recipes/blue_wireless_transmitter","refinedstorage:coloring_recipes/wireless_transmitter","create:crafting/kinetics/black_seat","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwpaths:brick_crystal_floor_path","croptopia:carnitas","minecraft:detector_rail","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","bigreactors:reactor/basic/activetap_fe","supplementaries:daub_frame","railcraft:signal_tuner","botania:manasteel_sword","cfm:pink_kitchen_counter","cfm:white_cooler","mcwroofs:light_gray_top_roof","mcwbiomesoplenty:jacaranda_highley_gate","immersiveengineering:crafting/jerrycan","everythingcopper:copper_hopper","minecolonies:potato_soup","botania:spark_upgrade_dispersive","mcwroofs:light_blue_terracotta_upper_steep_roof","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:banner/mint_banner","botania:dreamwood_wand","mcwfurnitures:crimson_bookshelf","minecraft:crafting_table","dyenamics:lavender_stained_glass","biomesoplenty:tnt_from_bop_sand","dyenamics:cherenkov_dye","twilightforest:time_boat","refinedstorage:coloring_recipes/red_controller","minecraft:green_stained_glass_pane_from_glass_pane","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","alchemistry:fusion_chamber_controller","securitycraft:briefcase","mcwwindows:green_mosaic_glass","create:crafting/logistics/stockpile_switch","mcwbiomesoplenty:empyreal_four_window","handcrafted:crimson_side_table","mcwlights:copper_triple_candle_holder","railcraft:steel_sword","cfm:white_grill","mcwwindows:cobblestone_arrow_slit","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","croptopia:butter","utilitix:comparator_redirector_up","minecraft:anvil","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","ae2:network/cables/smart_gray","ae2:network/cables/dense_covered_white","refinedstorage:coloring_recipes/green_crafting_monitor","mcwpaths:andesite_crystal_floor_stairs","modularrouters:distributor_module","botania:red_string_dispenser","domum_ornamentum:brown_bricks","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","cfm:red_cooler","botania:dreamwood_wall","refinedstorage:coloring_recipes/white_fluid_grid","securitycraft:reinforced_green_stained_glass_pane_from_dye","bigreactors:turbine/basic/blade","mcwbiomesoplenty:magic_wired_fence","bigreactors:reactor/reinforced/passivetap_fe","create:crafting/logistics/pulse_extender","xnet:facade","pneumaticcraft:display_table_from_shelf","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwroofs:green_terracotta_lower_roof","aether:golden_pendant","enderio:resetting_lever_five_from_inv","handcrafted:crimson_shelf","alchemistry:reactor_casing","sophisticatedstorage:stack_upgrade_tier_1","minecraft:allthemodium_spell_book_smithing","sophisticatedstorage:stack_upgrade_tier_2","sophisticatedstorage:stack_upgrade_tier_3","mcwwindows:stripped_acacia_log_four_window","utilitarian:utility/crimson_logs_to_trapdoors","undergarden:grongle_boat","mcwdoors:spruce_japanese_door","ae2:decorative/quartz_fixture","sophisticatedstorage:stack_upgrade_tier_5","mcwwindows:cherry_plank_window2","domum_ornamentum:green_floating_carpet","botania:sextant","mcwfences:mangrove_curved_gate","botania:manasteel_helmet","occultism:crafting/raw_iesnium_block","littlelogistics:automatic_switch_rail","railcraft:signal_capacitor_box","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","ae2:network/blocks/quantum_ring","mcwdoors:jungle_western_door","aether:skyroot_loom","mcwfurnitures:crimson_wardrobe","refinedstorage:coloring_recipes/light_gray_crafting_grid","minecraft:brick_wall","nethersdelight:diamond_machete","utilitix:advanced_brewery","aether:netherite_helmet_repairing","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","laserio:logic_chip_raw","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","productivebees:stonecutter/redwood_canvas_expansion_box","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:stripped_spruce_chair","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","railcraft:bronze_tunnel_bore_head","mcwroofs:orange_terracotta_steep_roof","expatternprovider:cobblestone_cell","refinedstorage:coloring_recipes/red_disk_manipulator","minecraft:raw_iron_block","mcwwindows:stone_window2","simplylight:illuminant_purple_block_on_dyed","croptopia:fruit_smoothie","mcwbiomesoplenty:maple_window","botania:blaze_block","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","pneumaticcraft:compressed_brick_stairs","productivetrees:planks/brown_amber_planks","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/yellow_grid","productivebees:hives/advanced_warped_canvas_hive","simplylight:illuminant_yellow_block_on_toggle","productivebees:stonecutter/bamboo_canvas_hive","mcwfurnitures:jungle_striped_chair","bloodmagic:smelting/blasting_ingot_from_raw_hellforged","dyenamics:honey_wool","xnet:advanced_connector_yellow_dye","pneumaticcraft:pneumatic_helmet","productivebees:stonecutter/magic_canvas_expansion_box","additionallanterns:diamond_lantern","mcwfurnitures:stripped_crimson_triple_drawer","railcraft:steel_shears","allthemodium:unobtainium_gear","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","refinedstorage:basic_processor","securitycraft:reinforced_lime_stained_glass_pane_from_glass","bloodmagic:alchemy_table","handcrafted:tropical_fish_trophy","bigreactors:smelting/graphite_from_coalblock","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","enderio:void_chassis","refinedstorage:coloring_recipes/cyan_grid","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","littlelogistics:vacuum_barge","bigreactors:energizer/computerport","mcwroofs:lime_terracotta_upper_lower_roof","allthemodium:allthemodium_apple","immersiveengineering:crafting/armor_steel_helmet","expatternprovider:silicon_block","minecraft:item_frame","itemcollectors:basic_collector","minecraft:loom","enderio:resetting_lever_three_hundred_inv","ad_astra:steel_engine","mcwtrpdoors:spruce_barrel_trapdoor","railcraft:steel_axe","handcrafted:crimson_table","mcwtrpdoors:oak_bark_trapdoor","botania:exchange_rod","minecraft:arrow","sophisticatedstorage:stack_downgrade_tier_3","mcwroofs:gutter_middle_light_blue","sophisticatedstorage:stack_downgrade_tier_2","sophisticatedstorage:stack_downgrade_tier_1","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","dyenamics:wine_wool","handcrafted:skeleton_trophy","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","handcrafted:golden_wide_pot","mcwbiomesoplenty:origin_hedge","aether:netherite_gloves_smithing","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","sophisticatedbackpacks:everlasting_upgrade","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","botania:red_string_comparator","appbot:portable_mana_storage_cell_16k","cfm:pink_picket_fence","mcwroofs:pink_terracotta_steep_roof","mcwbiomesoplenty:empyreal_stockade_fence","utilitarian:utility/spruce_logs_to_trapdoors","mcwpaths:brick_flagstone_slab","mcwroofs:gray_concrete_attic_roof","refinedstorage:coloring_recipes/black_crafting_grid","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","refinedstorage:coloring_recipes/white_wireless_transmitter","minecraft:polished_blackstone","computercraft:computer_advanced","utilitarian:utility/spruce_logs_to_stairs","advanced_ae:quantumaccel","refinedstorage:coloring_recipes/blue_relay","bloodmagic:smelting/ingot_from_raw_hellforged","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","botania:livingwood","mcwlights:copper_chandelier","botania:horn_snow","handcrafted:crimson_desk","reliquary:uncrafting/slime_ball","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","botania:gaia_spreader","mcwroofs:black_upper_lower_roof","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","railways:crafting/smokestack_woodburner","minecraft:jungle_button","pneumaticcraft:logistics_frame_storage","refinedstorage:coloring_recipes/yellow_wireless_transmitter","mcwtrpdoors:oak_cottage_trapdoor","securitycraft:reinforced_birch_fence_gate","sophisticatedstorage:hopper_upgrade","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","modularrouters:extruder_module_2","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:fluidizer/glass","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:stone_top_roof","cfm:oak_chair","travelersbackpack:horse","mcwroofs:orange_concrete_attic_roof","botania:mana_pool","aether:diamond_gloves_repairing","cfm:brown_cooler","xnet:netcable_red","delightful:knives/terra_knife","mcwfences:diorite_pillar_wall","appbot:portable_mana_storage_cell_1k","megacells:cells/portable/portable_item_cell_64m","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","productivebees:hives/advanced_snake_block_canvas_hive","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","ae2:tools/nether_quartz_pickaxe","utilitix:experience_crystal","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","minecraft:respawn_anchor","paraglider:paraglider","botania:petal_gray_double","mcwpaths:cobbled_deepslate_flagstone","expatternprovider:ebus_in","aether:golden_leggings_repairing","dankstorage:dock","sophisticatedstorage:shulker_box","minecraft:polished_blackstone_brick_stairs_from_blackstone_stonecutting","mcwroofs:blue_striped_awning","mcwfurnitures:spruce_desk","utilitix:anvil_cart","pneumaticcraft:wall_lamp_red","refinedstorage:coloring_recipes/red_crafter_manager","refinedstorage:coloring_recipes/crafter","mcwbridges:jungle_bridge_pier","immersiveengineering:crafting/component_steel","refinedstorage:coloring_recipes/crafter_manager","botania:fabulous_pool_upgrade","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","refinedstorage:coloring_recipes/green_fluid_grid","ae2:tools/paintballs_purple","mcwbiomesoplenty:jacaranda_picket_fence","mcwdoors:crimson_mystic_door","mcwfences:mangrove_hedge","mcwtrpdoors:spruce_swamp_trapdoor","sophisticatedbackpacks:compacting_upgrade","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","mcwpaths:blackstone_flagstone","undergarden:catalyst","ae2:network/cells/item_storage_cell_64k_storage","domum_ornamentum:purple_cobblestone_extra","mcwpaths:blackstone_windmill_weave","minecraft:iron_axe","modularrouters:puller_module_2_x4","create:crafting/kinetics/fluid_valve","supplementaries:crystal_display","enderio:resetting_lever_thirty_inv_from_base","minecraft:jukebox","utilitix:piston_cart","mcwwindows:jungle_curtain_rod","aether:diamond_sword_repairing","aether:aether_gold_nugget_from_blasting","mcwwindows:oak_window","mcwfurnitures:stripped_crimson_modern_wardrobe","mcwbiomesoplenty:stripped_magic_pane_window","mcwfurnitures:stripped_spruce_lower_triple_drawer","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","ae2:network/cables/smart_brown","minecraft:coarse_dirt","alltheores:signalum_rod","create:crafting/kinetics/white_seat","chemlib:gallium_ingot_to_block","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwroofs:stone_bricks_steep_roof","mcwwindows:red_curtain","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","railcraft:coal_coke_block_from_coal_coke","mcwwindows:light_gray_mosaic_glass","mcwroofs:white_upper_lower_roof","minecraft:magenta_terracotta","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","refinedstorage:coloring_recipes/white_network_transmitter","mcwdoors:spruce_classic_door","supplementaries:flags/flag_black","minecraft:gold_ingot_from_gold_block","mcwdoors:print_whispering","mcwroofs:red_terracotta_top_roof","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","cfm:light_gray_cooler","mcwroofs:oak_upper_steep_roof","occultism:crafting/demons_dream_essence_from_seeds","minecraft:iron_door","refinedstorage:coloring_recipes/black_disk_manipulator","mcwfurnitures:stripped_spruce_double_drawer_counter","handcrafted:white_cushion","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gray_terracotta_attic_roof","utilitarian:utility/oak_logs_to_doors","dyenamics:dye_spring_green_carpet","mcwbridges:rope_crimson_bridge","railways:crafting/smokestack_diesel","mcwroofs:gutter_base_orange","aether:bow_repairing","mcwfurnitures:stripped_spruce_stool_chair","securitycraft:reinforced_light_gray_stained_glass","farmersdelight:cooking/pasta_with_mutton_chop","minecraft:chiseled_quartz_block","cfm:stripped_spruce_kitchen_sink_light","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","mcwroofs:bricks_attic_roof","occultism:crafting/otherstone_slab","mcwwindows:blackstone_pane_window","enderio:resetting_lever_three_hundred_from_inv","minecraft:crimson_door","connectedglass:borderless_glass_yellow2","mcwdoors:jungle_whispering_door","pneumaticcraft:reinforced_bricks_from_stone_stonecutting","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","utilitarian:utility/crimson_logs_to_slabs","refinedstorage:coloring_recipes/cyan_crafter","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","create:crafting/logistics/display_link","ad_astra:airlock","mcwroofs:grass_steep_roof","cfm:pink_picket_gate","alchemistry:combiner","occultism:crafting/brush","ae2:network/cables/glass_white","mcwdoors:jungle_stable_head_door","minecraft:end_stone_bricks","minecolonies:pottage","croptopia:egg_roll","connectedglass:borderless_glass_black2","pneumaticcraft:reinforced_stone_slab_from_stone_stonecutting","railcraft:iron_gear","sophisticatedstorage:chipped/loom_table_upgrade","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","ae2:network/cells/item_storage_cell_1k_storage","dyenamics:banner/maroon_banner","mcwroofs:yellow_concrete_steep_roof","aether:netherite_axe_repairing","mcwfences:birch_hedge","mcwroofs:base_attic_roof","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","create:crafting/kinetics/mechanical_mixer","refinedstorage:coloring_recipes/black_network_transmitter","mcwlights:mangrove_tiki_torch","cfm:spruce_table","aether:iron_pendant","mcwfurnitures:spruce_bookshelf","securitycraft:redstone_module","minecraft:lime_dye","mcwtrpdoors:spruce_paper_trapdoor","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","allthemodium:unobtainium_plate","supplementaries:flags/flag_purple","sophisticatedstorage:jungle_barrel","botania:quartz_sunny","mcwbiomesoplenty:stripped_redwood_log_window","create:framed_glass_from_glass_colorless_stonecutting","productivebees:stonecutter/maple_canvas_expansion_box","botania:terrasteel_helmet","botania:flighttiara_0","enderio:infinity_rod","railcraft:signal_lamp","refinedstorage:coloring_recipes/orange_crafter_manager","botania:infused_seeds","cfm:jungle_blinds","tombstone:bone_needle","minecraft:stone_brick_wall","forbidden_arcanus:deorum_pressure_plate","twilightforest:twilight_oak_boat","mcwroofs:black_striped_awning","mcwlights:soul_classic_street_lamp","xnet:netcable_green","cfm:warped_kitchen_sink_light","refinedstorage:coloring_recipes/pink_relay","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:decoration_table","forbidden_arcanus:deorum_chain","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwfurnitures:stripped_spruce_modern_desk","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","refinedstorage:coloring_recipes/black_wireless_transmitter","pneumaticcraft:paper_from_tag_filter","delightful:knives/draco_arcanus_knife","expatternprovider:epp_upgrade","allthemodium:vibranium_allthemodium_alloy_block","utilitarian:utility/glow_ink_sac","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","minecraft:cyan_dye","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","minecraft:orange_terracotta","mcwroofs:white_top_roof","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:gold_block","minecraft:spruce_boat","mcwbiomesoplenty:maple_plank_pane_window","botania:floating_pure_daisy","handcrafted:jungle_counter","aquaculture:gold_fillet_knife","dyenamics:banner/navy_banner","sophisticatedstorage:storage_input","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","allthecompressed:compress/unobtainium_allthemodium_alloy_block_1x","mythicbotany:wither_aconite_floating","minecraft:paper","ae2:network/cables/dense_smart_cyan","cfm:magenta_picket_fence","botania:elf_glass_pane","botania:white_pavement","mcwfences:warped_curved_gate","bigreactors:energizer/powerport_fe_active","pneumaticcraft:compressed_bricks_from_tile","securitycraft:secret_mangrove_sign_item","refinedstorage:coloring_recipes/orange_disk_manipulator","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","utilitix:stone_wall_stonecutting","botania:conversions/terrasteel_block_deconstruct","cfm:spruce_kitchen_counter","travelersbackpack:warden","cfm:oak_upgraded_gate","refinedstorage:coloring_recipes/red_fluid_grid","mcwroofs:light_gray_terracotta_roof","croptopia:cashew_chicken","minecraft:copper_ingot_from_blasting_raw_copper","forbidden_arcanus:golden_orchid_seeds","botania:incense_plate","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","pneumaticcraft:pneumatic_wrench","sophisticatedstorage:compacting_upgrade","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","pneumaticcraft:micromissiles","mcwroofs:jungle_planks_attic_roof","mcwroofs:lime_terracotta_steep_roof","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","pneumaticcraft:pneumatic_boots","securitycraft:reinforced_observer","railways:stonecutting/riveted_locometal","refinedstorage:coloring_recipes/pink_crafter","productivebees:stonecutter/warped_canvas_hive","mcwdoors:spruce_nether_door","cfm:crimson_mail_box","pneumaticcraft:assembly_io_unit_export","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","supplementaries:sconce_lever","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","twilightforest:wood/mossy_towerwood","pneumaticcraft:wall_lamp_inverted_lime","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","botania:natura_pylon","mcwfences:acacia_curved_gate","botania:lime_petal_block","allthemodium:unobtainium_rod","bloodmagic:blood_rune_capacity_2","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","minecraft:cooked_rabbit","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","refinedstorage:coloring_recipes/cyan_crafting_grid","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","allthecompressed:compress/blazing_crystal_block_1x","alltheores:lead_rod","utilitix:stonecutter_cart","cfm:dye_green_picket_fence","refinedstorage:coloring_recipes/blue_controller","mcwwindows:oak_plank_window","ae2wtlib:wireless_universal_terminal/ce","refinedstorage:coloring_recipes/yellow_crafting_monitor","supplementaries:notice_board","mcwroofs:yellow_terracotta_roof","connectedglass:tinted_borderless_glass_green2","aquaculture:dark_oak_fish_mount","mcwroofs:yellow_terracotta_lower_roof","ad_astra:radio","allthecompressed:compress/jungle_planks_1x","securitycraft:reinforced_bamboo_fence","littlelogistics:spring","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","refinedstorage:coloring_recipes/security_manager","mcwlights:garden_light","mcwroofs:light_blue_terracotta_steep_roof","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwtrpdoors:crimson_glass_trapdoor","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","cfm:stripped_crimson_park_bench","minecraft:writable_book","pneumaticcraft:reinforced_stone","mcwfences:acacia_hedge","mcwroofs:black_terracotta_steep_roof","minecraft:golden_apple","botania:terra_sword","additionallanterns:purpur_chain","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","minecraft:light_gray_dye_from_oxeye_daisy","create:crafting/schematics/empty_schematic","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","productivebees:stonecutter/dead_canvas_expansion_box","pneumaticcraft:cannon_barrel","ad_astra:encased_steel_block","advanced_ae:advpatpropart","mcwroofs:orange_terracotta_upper_steep_roof","productivebees:hives/advanced_jungle_canvas_hive","pneumaticcraft:reinforced_brick_wall","minecraft:polished_blackstone_bricks_from_blackstone_stonecutting","ae2:network/cables/smart_purple","mcwtrpdoors:spruce_ranch_trapdoor","mcwtrpdoors:print_classic","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:umbran_plank_window2","botania:itemfinder","rftoolsbuilder:green_shield_template_block","mcwroofs:green_striped_awning","refinedstorage:coloring_recipes/magenta_crafter_manager","pneumaticcraft:wall_lamp_inverted_blue","supplementaries:flax_block_uncrafting","allthemodium:vibranium_plate","securitycraft:codebreaker","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:light_blue_picket_gate","allthearcanistgear:unobtainium_leggings_smithing","securitycraft:reinforced_glass_pane","constructionwand:infinity_wand","farmersdelight:golden_knife","minecraft:orange_stained_glass","bloodmagic:blood_rune_speed_2","aquaculture:wooden_fillet_knife","pneumaticcraft:logistics_module","create:crafting/materials/rose_quartz","minecraft:polished_blackstone_wall","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","farmersdelight:cooking/squid_ink_pasta","mcwroofs:black_roof","expatternprovider:assembler_matrix_wall","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","pneumaticcraft:logistics_frame_requester","alltheores:gold_plate","megacells:crafting/mega_energy_cell","cfm:blue_kitchen_counter","mcwbiomesoplenty:maple_four_window","ae2:network/blocks/interfaces_interface","utilitarian:redstone_clock","twigs:quartz_column_stonecutting","mcwwindows:stripped_oak_log_window","mcwroofs:base_top_roof","ae2:network/cables/smart_magenta","minecraft:golden_boots","securitycraft:harming_module","mcwdoors:crimson_swamp_door","mcwroofs:crimson_planks_upper_lower_roof","immersiveengineering:crafting/grit_sand","minecraft:netherite_shovel_smithing","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","refinedstorage:coloring_recipes/green_security_manager","create:crafting/kinetics/item_vault","mythicbotany:mana_collector","pneumaticcraft:drone","mcwfences:warped_highley_gate","pneumaticcraft:minigun","ad_astra:steel_door","megacells:network/cell_dock","supplementaries:flags/flag_cyan","supplementaries:statue","immersiveengineering:crafting/tinted_glass_lead_wire","reliquary:wraith_node","create:crafting/kinetics/clockwork_bearing","minecraft:dye_green_carpet","botania:diva_charm","cfm:pink_grill","mcwroofs:gutter_middle_orange","undergarden:torch_ditchbulb_paste","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","forbidden_arcanus:arcane_edelwood_planks","mcwwindows:granite_window2","sophisticatedstorage:chipped/glassblower_upgrade","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","minecraft:emerald","mcwroofs:jungle_attic_roof","pneumaticcraft:assembly_io_unit_export_from_import","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","utilitix:diamond_shears","minecraft:wooden_pickaxe","handcrafted:crimson_corner_trim","supplementaries:clock_block","securitycraft:secret_jungle_sign_item","mcwbridges:balustrade_cobblestone_bridge","aether:chainmail_chestplate_repairing","mcwwindows:warped_stem_window2","ae2:network/crafting/cpu_crafting_accelerator","mcwbiomesoplenty:fir_stockade_fence","alltheores:copper_plate","refinedstorage:coloring_recipes/black_crafter","additionallanterns:cobblestone_lantern","allthecompressed:decompress/dirt_6x","ae2:network/blocks/pattern_providers_interface","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","rftoolsbuilder:shape_card_def","dimstorage:dimensional_tablet","rftoolsbase:machine_base","botania:aura_ring_greater","rftoolsutility:counterplus_module","mcwroofs:white_terracotta_upper_steep_roof","mcwbiomesoplenty:dead_plank_window","mcwfurnitures:spruce_end_table","mcwdoors:crimson_whispering_door","securitycraft:reinforced_piston","cfm:purple_kitchen_counter","securitycraft:reinforced_yellow_stained_glass","handcrafted:crimson_couch","create:crafting/logistics/andesite_funnel","expatternprovider:threshold_export_bus","botania:swap_ring","mcwpaths:brick_crystal_floor_slab","refinedstorage:coloring_recipes/pink_pattern_grid","immersiveengineering:crafting/rs_engineering","pneumaticcraft:wall_lamp_yellow","aquaculture:tin_can_to_iron_nugget","paraglider:cosmetic/goddess_statue","alltheores:enderium_plate","mcwwindows:yellow_curtain","mcwwindows:purple_mosaic_glass","refinedstorage:coloring_recipes/gray_wireless_transmitter","productivebees:stonecutter/umbran_canvas_expansion_box","mcwpaths:blackstone_basket_weave_paving","modularrouters:player_module","pneumaticcraft:pressure_chamber_wall","minecraft:glass","allthecompressed:compress/gold_block_1x","xnet:connector_yellow","mcwdoors:spruce_paper_door","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","ae2:tools/portable_fluid_cell_64k","xnet:netcable_yellow_dye","mcwroofs:cobblestone_upper_steep_roof","mcwroofs:brown_concrete_attic_roof","alltheores:raw_iridium_block","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","domum_ornamentum:gray_floating_carpet","ae2additions:components/super/64k","ae2:network/cables/smart_red","create:crafting/kinetics/nixie_tube","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","botania:manasteel_hoe","minecraft:light_blue_concrete_powder","createoreexcavation:drill","bigreactors:reprocessor/controller","mcwroofs:gutter_middle_magenta","productivebees:stonecutter/driftwood_canvas_expansion_box","comforts:sleeping_bag_white","ae2:network/cables/dense_covered_brown","minecraft:cyan_terracotta","refinedstorage:coloring_recipes/cyan_crafting_monitor","appmek:chemical_storage_cell_64k","mcwwindows:light_blue_mosaic_glass","ad_astra:green_industrial_lamp","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","refinedstorage:coloring_recipes/gray_crafting_monitor","mcwbiomesoplenty:stripped_maple_pane_window","botania:spawner_claw","rftoolsstorage:storage_control_module","securitycraft:secret_spruce_sign_item","enderio:pressurized_fluid_tank","allthecompressed:compress/coal_block_1x","cfm:oak_upgraded_fence","bigreactors:crafting/magentite_storage_to_component","advanced_ae:smalladvpatpropart","deepresonance:radiation_monitor","botania:placeholder","botania:super_cloud_pendant","railcraft:steel_gear","allthemodium:piglich_heart_block","sophisticatedstorage:crafting_upgrade","bigreactors:smelting/graphite_from_charcoal","minecraft:prismarine_bricks","pneumaticcraft:programming_puzzle","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","minecraft:stone_bricks","securitycraft:reinforced_red_stained_glass","mcwroofs:green_concrete_top_roof","refinedstorage:coloring_recipes/orange_crafter","connectedglass:borderless_glass_cyan2","mcwbiomesoplenty:jacaranda_wired_fence","ae2:tools/network_color_applicator","ae2:tools/paintballs_cyan","delightful:knives/steel_knife","ad_astra:steel_block","cfm:spruce_kitchen_sink_light","aether:leather_gloves","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","minecraft:allthemodium_mage_boots_smithing","mcwpaths:andesite_flagstone","villagertools:restock","mcwbiomesoplenty:dead_plank_window2","handcrafted:bricks_pillar_trim","botania:quartz_lavender","minecraft:golden_axe","immersiveengineering:crafting/gunpart_drum","immersiveengineering:crafting/paper_from_sawdust","reliquary:glowing_water_from_potion_vial","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","travelersbackpack:bookshelf","minecraft:gunpowder","bloodmagic:blood_rune_acceleration_2","immersiveengineering:crafting/wire_lead","mcwpaths:brick_diamond_paving","minecraft:cyan_stained_glass","pneumaticcraft:compressed_brick_slab","mcwroofs:red_concrete_roof","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","ae2:shaped/slabs/quartz_block","rftoolscontrol:card_base","refinedstorage:coloring_recipes/orange_crafting_grid","ae2:network/cables/smart_fluix","pylons:potion_filter","enderio:resetting_lever_thirty","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","pneumaticcraft:solar_compressor","farmersdelight:cooking/pumpkin_soup","supplementaries:ars_nouveau/sign_post_archwood","create:crafting/kinetics/gray_seat","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","create:crafting/kinetics/brown_seat","refinedstorage:coloring_recipes/green_network_receiver","pneumaticcraft:wall_lamp_light_gray","littlelogistics:energy_tug","refinedstorage:coloring_recipes/red_wireless_transmitter","handcrafted:quartz_corner_trim","create:crafting/kinetics/contraption_controls","ad_astra:steel_factory_block","mcwfurnitures:stripped_spruce_triple_drawer","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","refinedstorage:coloring_recipes/light_gray_crafter_manager","minecraft:blackstone_wall_from_blackstone_stonecutting","dyenamics:fluorescent_stained_glass","dyenamics:amber_concrete_powder","mcwroofs:light_gray_concrete_lower_roof","cfm:crimson_cabinet","occultism:crafting/spirit_torch","twigs:rhyolite_slab","ae2:network/cables/glass_cyan","immersiveengineering:crafting/stick_iron","productivebees:expansion_boxes/expansion_box_cherry_canvas","pylons:interdiction_pylon","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:entity_tracker_upgrade","pneumaticcraft:reinforced_bricks_from_tile","mcwpaths:stone_crystal_floor_path","twigs:stone_column","minecraft:lectern","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwroofs:lime_terracotta_upper_steep_roof","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","mcwwindows:cyan_curtain","securitycraft:reinforced_cyan_stained_glass","mcwfurnitures:crimson_modern_wardrobe","handcrafted:golden_medium_pot","ae2:network/cables/smart_yellow","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","mcwfurnitures:spruce_lower_bookshelf_drawer","pneumaticcraft:gas_lift","aiotbotania:terra_hoe","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","ae2:materials/formationcore","mcwfurnitures:crimson_striped_chair","mcwroofs:gray_terracotta_upper_steep_roof","botania:mana_glass_pane","securitycraft:secret_sign_item","megacells:cells/portable/portable_fluid_cell_16m","sfm:fancy_to_cable","occultism:crafting/demons_dream_essence_from_fruit","connectedglass:borderless_glass_white2","create:crimson_window","ae2additions:components/super/1k","aquaculture:heavy_hook","aether:netherite_sword_repairing","minecraft:white_concrete_powder","minecraft:yellow_terracotta","mcwroofs:green_terracotta_roof","mcwwindows:acacia_window","refinedstorage:coloring_recipes/green_controller","croptopia:food_press","blue_skies:frostbright_bookshelf","sophisticatedbackpacks:blasting_upgrade","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","utilitarian:snad/soul_snad","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","enderio:enchanter","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwpaths:brick_flagstone_path","cfm:black_picket_fence","mcwfurnitures:oak_chair","dyenamics:honey_terracotta","mcwpaths:brick_honeycomb_paving","mcwfurnitures:crimson_lower_triple_drawer","mcwwindows:stripped_crimson_stem_four_window","twilightdelight:fiery_cooking_pot","utilitix:spruce_shulker_boat_with_shell","forbidden_arcanus:arcane_bone_meal","immersiveengineering:crafting/powerpack","advanced_ae:import_export_bus","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","sliceanddice:sprinkler","pneumaticcraft:small_tank","ae2:network/cables/glass_green","ae2:tools/fluix_hoe","botania:terrasteel_chestplate","mcwroofs:orange_concrete_lower_roof","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","pneumaticcraft:wall_lamp_gray","ae2:shaped/walls/quartz_block","cfm:spruce_mail_box","minecraft:barrel","mcwlights:orange_lamp","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","pneumaticcraft:reinforced_brick_pillar","minecraft:polished_blackstone_slab_from_blackstone_stonecutting","immersiveengineering:crafting/connector_hv","mcwpaths:brick_crystal_floor","handcrafted:crimson_bench","botania:dodge_ring","ae2:materials/basiccard","pneumaticcraft:elytra_upgrade","refinedstorage:coloring_recipes/white_controller","mcwwindows:cherry_window","botania:laputa_shard","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfurnitures:crimson_covered_desk","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","allthearcanistgear:vibranium_boots_smithing","mcwpaths:cobbled_deepslate_honeycomb_paving","productivebees:hives/advanced_birch_canvas_hive","mcwlights:blue_lamp","mcwbiomesoplenty:stripped_jacaranda_log_window","littlelogistics:car_dock_rail","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","dyenamics:conifer_terracotta","aether:golden_gloves","ae2:network/cables/glass_fluix","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","productivebees:stonecutter/spruce_canvas_expansion_box","mcwpaths:blackstone_crystal_floor_slab","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","dyenamics:conifer_wool","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:cauldron","mcwbridges:glass_bridge_pier","minecraft:pink_concrete_powder","delightful:smelting/roasted_acorn","mcwdoors:spruce_barn_door","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","supplementaries:blackboard","twilightforest:compressed_blocks/fiery_block","cfm:yellow_grill","mcwfences:jungle_picket_fence","ae2:tools/nether_quartz_cutting_knife","sophisticatedstorage:storage_io","silentgear:blueprint_paper","securitycraft:portable_radar","dyenamics:ultramarine_concrete_powder","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","mcwfurnitures:oak_kitchen_cabinet","ae2:block_cutter/walls/smooth_sky_stone_wall","mcwwindows:end_brick_gothic","ae2:network/cables/dense_covered_magenta","mcwroofs:brown_terracotta_lower_roof","minecraft:andesite_wall","railcraft:steel_tank_gauge","allthemodium:unobtainium_vibranium_alloy_ingot_from_block","cfm:stripped_birch_kitchen_sink_light","mcwdoors:spruce_modern_door","xnet:connector_upgrade","mcwlights:dark_oak_tiki_torch","mcwroofs:grass_roof","ad_astra:rocket_nose_cone","utilitarian:utility/oak_logs_to_trapdoors","mcwpaths:brick_running_bond_slab","aether:netherite_gloves_repairing","aether:iron_ring","utilitarian:utility/jungle_logs_to_stairs","tombstone:impregnated_diamond","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","mcwroofs:jungle_planks_upper_lower_roof","cfm:cyan_kitchen_sink","dyenamics:peach_concrete_powder","ae2additions:components/super/4k","sophisticatedbackpacks:refill_upgrade","supplementaries:faucet","chemlib:gallium_ingot_from_smelting_gallium_dust","productivebees:stonecutter/umbran_canvas_hive","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","minecraft:popped_chorus_fruit","sophisticatedstorage:storage_void_upgrade_from_backpack_void_upgrade","railcraft:copper_gear","alltheores:enderium_rod","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","refinedstorage:coloring_recipes/blue_security_manager","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","connectedglass:borderless_glass_green2","mythicbotany:gaia_pylon","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwroofs:stone_bricks_top_roof","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","cfm:magenta_trampoline","utilitarian:utility/spruce_logs_to_boats","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","refinedstorage:coloring_recipes/gray_crafting_grid","handcrafted:stone_pillar_trim","allthecompressed:compress/glass_1x","sophisticatedstorage:chipped/alchemy_bench_upgrade","ad_astra:compressor","productivebees:stonecutter/rosewood_canvas_hive","refinedstorage:coloring_recipes/lime_security_manager","mcwfurnitures:crimson_triple_drawer","mcwfurnitures:stripped_crimson_stool_chair","additionallanterns:end_stone_chain","mcwlights:crimson_ceiling_fan_light","connectedglass:clear_glass_lime2","farmersdelight:cooking/mushroom_rice","refinedstorage:coloring_recipes/gray_crafter","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","ae2:blasting/silicon_from_certus_quartz_dust","ae2:network/cells/item_storage_cell_4k","mcwroofs:brown_terracotta_top_roof","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","dyenamics:dye_persimmon_carpet","travelersbackpack:standard","pneumaticcraft:reinforced_stone_slab","ae2additions:cells/super/16m","utilitix:stone_wall","botania:manasteel_pick","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","minecraft:brick_wall_from_bricks_stonecutting","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","refinedstorage:coloring_recipes/gray_network_transmitter","mcwpaths:brick_windmill_weave_slab","sfm:fancy_cable","mcwfurnitures:stripped_spruce_modern_chair","refinedstorage:coloring_recipes/magenta_pattern_grid","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","minecraft:end_stone_brick_slab_from_end_stone_stonecutting","delightful:knives/certus_quartz_knife","botania:holy_cloak","productivebees:stonecutter/concrete_canvas_hive","createoreexcavation:vein_finder","forbidden_arcanus:arcane_crystal_from_arcane_crystal_block","sophisticatedstorage:filter_upgrade","refinedstorage:coloring_recipes/yellow_crafting_grid","simplylight:illuminant_brown_block_toggle","ae2:network/parts/annihilation_plane_alt2","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","littlelogistics:receiver_component","mcwroofs:cobblestone_steep_roof","aether:aether_gold_nugget_from_smelting","create:crafting/kinetics/fluid_pipe_vertical","securitycraft:reinforced_tinted_glass","ae2:network/cells/spatial_components","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","mcwtrpdoors:spruce_tropical_trapdoor","cfm:stripped_oak_park_bench","mcwtrpdoors:print_cottage","securitycraft:reinforced_gray_stained_glass","botania:spark_upgrade_recessive","sophisticatedbackpacks:chipped/carpenters_table_upgrade","minecraft:copper_ingot_from_smelting_raw_copper","create:crafting/kinetics/mechanical_arm","mcwbiomesoplenty:stripped_maple_log_window2","advanced_ae:stock_export_bus","allthemodium:vibranium_rod","mcwwindows:crimson_curtain_rod","mcwlights:wall_lamp","mcwbiomesoplenty:redwood_plank_window2","securitycraft:universal_key_changer","minecraft:netherite_ingot","botania:dye_lime","mysticalagriculture:awakened_supremium_block_uncraft","pneumaticcraft:range_upgrade","cfm:magenta_cooler","dyenamics:navy_terracotta","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","ae2:tools/paintballs_light_gray","everythingcopper:copper_leggings","ae2:network/cells/item_storage_cell_1k","railcraft:diamond_tunnel_bore_head","pneumaticcraft:reinforced_brick_tile","mcwroofs:gutter_base_pink","ae2:tools/nether_quartz_hoe","mcwbridges:andesite_bridge_stair","securitycraft:reinforced_blue_stained_glass_pane_from_glass","refinedstorage:coloring_recipes/green_disk_manipulator","botania:spark_upgrade_isolated","mcwroofs:lime_terracotta_attic_roof","mcwlights:iron_framed_torch","productivebees:stonecutter/aspen_canvas_hive","sophisticatedstorage:backpack_stack_upgrade_tier_3_from_storage_stack_upgrade_tier_4","cfm:stripped_spruce_table","minecraft:light_blue_terracotta","mcwroofs:yellow_striped_awning","dyenamics:bed/honey_bed","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","croptopia:flour","twilightforest:equipment/fiery_leggings","refinedstorage:coloring_recipes/lime_grid","minecraft:jungle_sign","ae2:tools/portable_item_cell_16k","enderio:basic_capacitor","mcwfences:railing_mud_brick_wall","expatternprovider:precise_storage_bus","mcwwindows:crimson_planks_window","railcraft:steel_tank_valve","occultism:crafting/book_of_calling_djinni_manage_machine","cfm:cyan_cooler","ae2:network/parts/formation_plane","connectedglass:clear_glass_pane3","mcwfurnitures:stripped_crimson_drawer","railcraft:silver_gear","immersiveengineering:crafting/empty_casing","immersiveengineering:crafting/wirecoil_copper","ad_astra:steel_rod","mcwwindows:prismarine_four_window","refinedstorage:coloring_recipes/magenta_wireless_transmitter","mcwroofs:light_blue_concrete_top_roof","mcwroofs:light_gray_roof","securitycraft:secret_jungle_hanging_sign","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","dyenamics:fluorescent_terracotta","extradisks:withering_processor","sophisticatedbackpacks:deposit_upgrade","mcwroofs:pink_terracotta_roof","mcwfences:spruce_picket_fence","mcwtrpdoors:mangrove_ranch_trapdoor","travelersbackpack:redstone","minecraft:slime_block","mcwdoors:crimson_waffle_door","mcwfurnitures:crimson_lower_bookshelf_drawer","twilightforest:magic_map_focus","mcwwindows:blue_curtain","immersiveengineering:crafting/shovel_steel","minecraft:cooked_rabbit_from_campfire_cooking","chimes:glass_bells","refinedstorage:coloring_recipes/purple_grid","minecraft:netherite_upgrade_smithing_template","immersiveengineering:crafting/blueprint_molds","minecraft:brown_terracotta","handcrafted:kitchen_hood_pipe","bigreactors:turbine/basic/casing","travelersbackpack:emerald","farmersdelight:cutting_board","bigreactors:reactor/reinforced/controller_ingots_uranium","croptopia:trail_mix","cfm:birch_kitchen_drawer","trashcans:item_trash_can","mcwpaths:blackstone_running_bond_path","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","create:smelting/scoria","bloodmagic:blood_rune_sac_2","appbot:portable_mana_storage_cell_64k","ad_astra:nasa_workbench","sophisticatedstorage:storage_stack_upgrade_tier_2_from_backpack_stack_upgrade_tier_1","ad_astra:etrionic_capacitor","botania:apothecary_deepslate","immersiveengineering:crafting/sawblade","modularrouters:flinger_module","mcwbridges:blackstone_bridge_pier","ae2:tools/nether_quartz_axe","allthecompressed:compress/nether_star_block_3x","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","mcwroofs:magenta_striped_awning","allthemodium:allthemodium_gear","enderio:resetting_lever_sixty_inv","sophisticatedstorage:basic_to_gold_tier_upgrade","railcraft:steel_shovel","mcwfurnitures:stripped_spruce_large_drawer","mcwpaths:blackstone_windmill_weave_path","croptopia:shaped_nether_wart_stew","minecraft:end_crystal","mcwroofs:cobblestone_attic_roof","appmek:portable_chemical_storage_cell_16k","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwroofs:magenta_terracotta_attic_roof","mcwwindows:stripped_mangrove_log_four_window","allthecompressed:decompress/nether_star_block_3x","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","mcwpaths:blackstone_clover_paving","alltheores:copper_rod","minecraft:shulker_box","productivebees:stonecutter/cherry_canvas_hive","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwroofs:white_terracotta_lower_roof","mcwpaths:cobblestone_square_paving","botania:pump","mcwfurnitures:stripped_oak_double_drawer","allthecompressed:compress/nether_star_block_2x","minecraft:white_carpet","utilitix:filter_rail","aether:diamond_gloves","minecraft:spruce_planks","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","cfm:orange_kitchen_sink","mcwroofs:deepslate_steep_roof","enderio:resetting_lever_sixty_from_inv","croptopia:pork_jerky","mcwpaths:cobbled_deepslate_basket_weave_paving","allthecompressed:compress/iron_block_1x","handcrafted:crimson_dining_bench","mcwroofs:base_lower_roof","minecraft:polished_blackstone_bricks","mcwfurnitures:spruce_triple_drawer","minecraft:cracked_stone_bricks","securitycraft:secret_birch_sign_item","railcraft:steel_tunnel_bore_head","minecolonies:chainmailhelmet","allthecompressed:decompress/nether_star_block_4x","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","botania:unholy_cloak","ae2:network/parts/tunnels_me","mcwroofs:oak_lower_roof","deeperdarker:sonorous_staff","croptopia:toast_with_jam","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","securitycraft:crystal_quartz_item","travelersbackpack:dye_green_sleeping_bag","dimstorage:dim_wall","allthecompressed:compress/nether_star_block_5x","arseng:source_storage_cell_4k","mcwbiomesoplenty:redwood_window","cfm:spruce_park_bench","refinedstorage:coloring_recipes/black_pattern_grid","ae2:network/cells/fluid_storage_cell_4k","minecraft:magenta_stained_glass","minecraft:ender_chest","refinedstorage:coloring_recipes/lime_crafter_manager","minecraft:iron_trapdoor","pneumaticcraft:thermal_lagging","allthecompressed:decompress/nether_star_block_1x","ae2:network/cables/dense_covered_yellow","minecolonies:supplycampdeployer","handcrafted:stone_corner_trim","create:crafting/kinetics/shaft","mcwpaths:cobbled_deepslate_running_bond_path","mcwfurnitures:stripped_crimson_wardrobe","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","allthemodium:ancient_smooth_stone_from_ancient_stone_smelting","modularrouters:void_module","connectedglass:scratched_glass_green2","ae2:network/blocks/storage_drive","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwroofs:bricks_lower_roof","allthecompressed:compress/nether_star_block_4x","mcwfurnitures:stripped_crimson_double_drawer_counter","immersiveengineering:crafting/redstone_acid","dankstorage:2_to_3","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","cfm:spruce_cabinet","allthecompressed:decompress/nether_star_block_2x","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","botania:knockback_belt","refinedstorage:coloring_recipes/blue_detector","mysticalagriculture:imperium_essence_uncraft","productivebees:stonecutter/river_canvas_expansion_box","handcrafted:jungle_chair","refinedstorage:coloring_recipes/lime_controller","domum_ornamentum:lime_floating_carpet","botania:conversions/blazeblock_deconstruct","mcwfurnitures:spruce_large_drawer","farmersdelight:flint_knife","biomesoplenty:maple_boat","appbot:portable_mana_storage_cell_256k","aether:golden_boots_repairing","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwfurnitures:stripped_spruce_drawer","corail_woodcutter:warped_woodcutter","travelersbackpack:nether","mcwbiomesoplenty:redwood_plank_pane_window","handcrafted:white_sheet","immersiveengineering:smoking/clinker_brick","minecraft:polished_andesite_slab_from_polished_andesite_stonecutting","refinedstorage:coloring_recipes/cyan_fluid_grid","securitycraft:reinforced_warped_fence_gate","botania:skydirt_rod","mcwroofs:black_terracotta_attic_roof","ae2:network/cables/glass_purple","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","refinedstorage:coloring_recipes/yellow_network_transmitter","allthecompressed:compress/nether_star_block_6x","expatternprovider:epp","simplylight:walllamp","mcwwindows:dark_oak_four_window","undergarden:tusk_to_bonemeal","mcwfences:oak_horse_fence","immersiveengineering:crafting/plate_steel_hammering","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","expatternprovider:epa","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","forbidden_arcanus:deorum_ingot_from_deorum_block","ae2:network/cables/dense_covered_light_gray","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","mcwtrpdoors:print_barred","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwroofs:white_lower_roof","mcwlights:gray_paper_lamp","minecraft:purpur_pillar","botania:drum_wild","simplylight:illuminant_red_block_dyed","mcwlights:square_wall_lamp","mcwfurnitures:crimson_double_drawer","simplylight:illuminant_gray_block_dyed","blue_skies:dusk_spear","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","refinedstorage:coloring_recipes/green_crafter_manager","refinedstorage:coloring_recipes/light_gray_pattern_grid","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","bigreactors:turbine/reinforced/controller","minecraft:redstone_lamp","mcwtrpdoors:jungle_barrel_trapdoor","allthecompressed:decompress/nether_star_block_5x","refinedstorage:coloring_recipes/yellow_pattern_grid","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","minecraft:end_rod","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","aether:skyroot_bookshelf","minecraft:polished_andesite_stairs_from_polished_andesite_stonecutting","mythicbotany:rune_holder","ae2:tools/certus_quartz_axe","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:gray_lower_roof","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","allthecompressed:compress/cobbled_deepslate_1x","productivebees:stonecutter/acacia_canvas_expansion_box","twilightdelight:berry_stick","silentgear:stone_torch","handcrafted:terracotta_bowl","botania:black_hole_talisman","littlecontraptions:contraption_barge","pneumaticcraft:thermostat_module","botania:spark_upgrade_dominant","ae2:tools/fluix_axe","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","connectedglass:borderless_glass_pink2","rftoolsbase:filter_module","mcwroofs:grass_top_roof","railcraft:signal_sequencer_box","twilightforest:wood/smoked_cracked_towerwood","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","blue_skies:comet_spear","mcwwindows:green_mosaic_glass_pane","create:crafting/schematics/schematic_and_quill","allthemodium:vibranium_ingot","botania:auto_crafting_halo","mcwroofs:pink_concrete_top_roof","utilitarian:utility/jungle_logs_to_pressure_plates","forbidden_arcanus:sanity_meter","botania:diluted_pool","modularrouters:energy_output_module","mcwlights:glowstone_slab","dyenamics:banner/fluorescent_banner","handcrafted:vindicator_trophy","mcwlights:soul_acacia_tiki_torch","appbot:mana_storage_cell_64k","minecraft:crimson_sign","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","minecraft:painting","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","aether:skyroot_lectern","mcwfurnitures:stripped_spruce_bookshelf_cupboard","enderio:resetting_lever_three_hundred_inv_from_base","botania:avatar","minecraft:polished_blackstone_pressure_plate","twilightdelight:borer_tear_soup","refinedstorage:improved_processor","biomesoplenty:blackstone_bulb","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","ae2:network/cables/glass_lime","mcwwindows:crimson_planks_window2","securitycraft:secret_dark_oak_hanging_sign","alchemistry:fission_chamber_controller","alltheores:diamond_rod","railcraft:animal_detector","minecraft:stone_slab_from_stone_stonecutting","mcwtrpdoors:jungle_whispering_trapdoor","ae2:network/blocks/crank","mcwroofs:light_blue_terracotta_upper_lower_roof","mcwroofs:brown_concrete_steep_roof","refinedstorage:coloring_recipes/lime_pattern_grid","reliquary:infernal_claw","mcwwindows:stripped_jungle_log_window","minecraft:stone_brick_slab_from_stone_stonecutting","mcwlights:lime_lamp","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","mcwroofs:spruce_upper_lower_roof","pneumaticcraft:speed_upgrade","ae2:network/crafting/64k_cpu_crafting_storage","productivebees:stonecutter/grimwood_canvas_expansion_box","minecraft:gold_ingot_from_nuggets","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","create:crafting/materials/andesite_alloy_block","cfm:spruce_chair","littlelogistics:locomotive_route","connectedglass:clear_glass_white2","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","securitycraft:reinforced_bookshelf","botania:dreamwood","minecraft:netherite_leggings_smithing","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","enderio:resetting_lever_sixty","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwtrpdoors:cherry_ranch_trapdoor","mcwfurnitures:crimson_coffee_table","create:crafting/kinetics/sticky_mechanical_piston","minecraft:stone_brick_stairs_from_stone_stonecutting","securitycraft:storage_module","mcwroofs:brown_concrete_top_roof","refinedstorage:coloring_recipes/green_relay","ae2:network/cables/dense_smart_brown","chemlib:calcium_nugget_to_ingot","forbidden_arcanus:blasting/arcane_crystal_from_blasting","pneumaticcraft:module_expansion_card","mcwbiomesoplenty:dead_hedge","ars_nouveau:archwood_planks","botania:petal_pink","pneumaticcraft:reinforced_brick_slab_from_bricks_stonecutting","mcwfences:prismarine_grass_topped_wall","occultism:crafting/book_of_calling_foliot_transport_items","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwpaths:andesite_windmill_weave_stairs","minecraft:raw_copper_block","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","mcwroofs:white_roof","pneumaticcraft:compressed_stone_from_slab","rftoolsutility:energyplus_module","ae2:tools/portable_item_cell_1k","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","create:crafting/kinetics/gantry_shaft","mcwbiomesoplenty:stripped_umbran_log_window2","minecraft:netherite_machete_smithing","allthecompressed:compress/emerald_block_6x","mcwbridges:balustrade_blackstone_bridge","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","cfm:stripped_warped_kitchen_sink_dark","ae2:network/cables/glass_blue","allthecompressed:compress/nether_star_block_1x","refinedstorage:coloring_recipes/purple_relay","minecraft:lapis_lazuli","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","productivebees:stonecutter/mahogany_canvas_expansion_box","ae2:tools/portable_item_cell_4k","aquaculture:redstone_hook","refinedstorage:coloring_recipes/light_gray_relay","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","securitycraft:secret_acacia_hanging_sign","minecraft:quartz_block","botania:glimmering_dreamwood_log","minecraft:brick","arseng:portable_source_cell_16k","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","pneumaticcraft:wall_lamp_inverted_yellow","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwlights:golden_triple_candle_holder","mcwroofs:lime_concrete_top_roof","allthecompressed:compress/emerald_block_5x","arseng:source_storage_cell_1k","mcwroofs:purple_terracotta_attic_roof","bloodmagic:blood_rune_charging_2","sophisticatedstorage:stack_upgrade_tier_1_plus","mcwroofs:orange_terracotta_lower_roof","refinedstorage:coloring_recipes/cyan_network_receiver","ae2:network/cells/fluid_storage_cell_1k","minecraft:coal","minecraft:unobtainium_mage_boots_smithing","appbot:mana_storage_cell_256k","mcwwindows:quartz_four_window","mcwroofs:blue_terracotta_upper_lower_roof","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwroofs:brown_terracotta_upper_steep_roof","mcwdoors:oak_swamp_door","enderio:resetting_lever_sixty_from_prev","mcwwindows:mud_brick_arrow_slit","botania:dye_gray","supplementaries:sack","utilitarian:no_soliciting/restraining_order","handcrafted:crimson_chair","mcwwindows:cherry_window2","minecraft:vibranium_mage_leggings_smithing","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","refinedstorage:coloring_recipes/cyan_crafter_manager","cfm:stripped_oak_mail_box","pneumaticcraft:wall_lamp_brown","refinedstorage:coloring_recipes/gray_controller","mcwwindows:nether_brick_gothic","botania:forest_eye","mcwwindows:stripped_oak_pane_window","productivetrees:planks/soul_tree_planks","additionallanterns:netherite_lantern","mcwroofs:light_blue_concrete_upper_lower_roof","allthemodium:unobtainium_ingot","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","create:andesite_ladder_from_andesite_alloy_stonecutting","productivebees:hives/advanced_bamboo_canvas_hive","mcwroofs:blue_terracotta_top_roof","pneumaticcraft:omnidirectional_hopper","minecraft:brown_dye","securitycraft:cage_trap","minecraft:polished_blackstone_stairs_from_blackstone_stonecutting","pneumaticcraft:assembly_io_unit_import","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","blue_skies:zeal_lighter","handcrafted:terracotta_medium_pot","minecraft:brick_slab","sophisticatedstorage:storage_pickup_upgrade_from_backpack_pickup_upgrade","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","expatternprovider:ex_drive","refinedstorage:coloring_recipes/gray_disk_manipulator","cfm:lime_cooler","mcwpaths:andesite_crystal_floor","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","sophisticatedstorage:storage_magnet_upgrade_from_backpack_magnet_upgrade","ae2:network/cells/fluid_storage_cell_64k_storage","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","ae2:misc/tank_sky_stone","dyenamics:mint_dye","refinedstorage:coloring_recipes/purple_security_manager","silentgear:emerald_from_shards","supplementaries:candy","minecolonies:chicken_broth","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","productivebees:stonecutter/cherry_canvas_expansion_box","pneumaticcraft:pneumatic_leggings","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","alchemistry:reactor_input","railcraft:diamond_spike_maul","minecraft:jungle_pressure_plate","paraglider:cosmetic/goron_goddess_statue","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwroofs:gray_roof_block","mcwbiomesoplenty:fir_plank_four_window","botania:mushroom_11","allthecompressed:compress/blaze_block_1x","cfm:white_kitchen_drawer","ae2:network/crafting/1k_cpu_crafting_storage","botania:mushroom_10","utilitarian:tps_meter","utilitarian:utility/spruce_logs_to_doors","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","botania:mushroom_15","botania:mushroom_14","botania:mushroom_13","botania:mushroom_12","dyenamics:banner/persimmon_banner","ae2:network/cables/smart_pink","mcwbiomesoplenty:pine_horse_fence","pneumaticcraft:pneumatic_dynamo","refinedstorage:coloring_recipes/green_detector","allthecompressed:compress/emerald_block_1x","comforts:hammock_to_white","mcwwindows:acacia_four_window","securitycraft:floor_trap","mcwroofs:yellow_terracotta_top_roof","xnet:connector_red","minecraft:golden_carrot","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","ae2:network/cables/dense_smart_yellow","mcwdoors:jungle_glass_door","blue_skies:starlit_bookshelf","mcwbiomesoplenty:hellbark_window","ae2:decorative/cut_quartz_block","xnet:netcable_red_dye","mcwroofs:light_gray_terracotta_upper_steep_roof","mcwfurnitures:spruce_modern_chair","refinedstorage:coloring_recipes/light_gray_security_manager","mcwwindows:pink_mosaic_glass_pane","productivebees:stonecutter/magic_canvas_hive","mcwbiomesoplenty:stripped_empyreal_log_window2","reliquary:witherless_rose","dyenamics:honey_stained_glass","mcwtrpdoors:spruce_four_panel_trapdoor","mcwfences:sandstone_grass_topped_wall","alltheores:invar_rod","expatternprovider:ex_inscriber","minecraft:glowstone","alltheores:gold_rod","additionallanterns:smooth_stone_chain","immersiveengineering:crafting/armor_steel_leggings","bigreactors:blasting/graphite_from_coalblock","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwroofs:green_terracotta_upper_steep_roof","reliquary:mercy_cross","farmersdelight:fried_egg","minecraft:magma_cream","mcwbiomesoplenty:maple_pane_window","twilightforest:compressed_blocks/carminite_block","mcwfurnitures:oak_bookshelf_cupboard","supplementaries:key","productivebees:expansion_boxes/expansion_box_bamboo_canvas","botania:dirt_rod","minecraft:blackstone_wall","ae2:block_cutter/walls/sky_stone_wall","blue_skies:maple_bookshelf","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","connectedglass:clear_glass_red2","pneumaticcraft:logistics_frame_default_storage","botania:gaia_ingot","mcwroofs:white_concrete_upper_steep_roof","croptopia:pork_and_beans","mcwpaths:blackstone_windmill_weave_stairs","additionallanterns:cobbled_deepslate_lantern","cfm:crimson_coffee_table","create:crafting/kinetics/belt_connector","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:stone_chain","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","pneumaticcraft:compressed_brick_from_slab","refinedstorage:coloring_recipes/light_gray_detector","simplylight:illuminant_lime_block_on_toggle","ae2:misc/deconstruction_certus_quartz_block","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","mcwroofs:thatch_attic_roof","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","reliquary:mob_charm_fragments/enderman","pneumaticcraft:wall_lamp_inverted_gray","sophisticatedstorage:backpack_void_upgrade_from_storage_void_upgrade","mcwtrpdoors:crimson_beach_trapdoor","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","refinedstorage:coloring_recipes/purple_detector","create:crafting/kinetics/purple_seat","mcwpaths:brick_running_bond_path","twilightforest:equipment/fiery_iron_sword","supplementaries:sugar_cube","securitycraft:security_camera","utilitix:cherry_shulker_boat_with_shell","mcwbiomesoplenty:palm_curved_gate","cfm:light_gray_kitchen_drawer","computercraft:computer_normal","botania:mana_pylon","handcrafted:crimson_nightstand","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","cfm:spruce_blinds","botania:bellows","mcwwindows:deepslate_window2","immersiveengineering:crafting/lantern","minecraft:sea_lantern","mcwbiomesoplenty:redwood_plank_four_window","pneumaticcraft:thermopneumatic_processing_plant","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","mcwpaths:brick_windmill_weave_stairs","securitycraft:reinforced_lectern","additionallanterns:normal_sandstone_lantern","mcwdoors:spruce_tropical_door","mcwroofs:magenta_terracotta_upper_lower_roof","aquaculture:gold_nugget_from_gold_fish","minecraft:diamond_hoe","minecraft:jungle_boat","dyenamics:spring_green_terracotta","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","immersiveengineering:crafting/wirecoil_steel","supplementaries:candle_holders/candle_holder_pink","mcwroofs:blackstone_top_roof","ae2additions:super_cell_housing","cfm:brown_trampoline","advanced_ae:smalladvpatpro","botania:apothecary_livingrock","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","railcraft:iron_spike_maul","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","refinedstorage:coloring_recipes/brown_disk_manipulator","ae2:tools/paintballs_green","mcwtrpdoors:jungle_mystic_trapdoor","mcwdoors:crimson_stable_door","refinedstorage:coloring_recipes/network_transmitter","minecraft:dye_green_wool","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","railcraft:feed_station","cfm:stripped_crimson_cabinet","connectedglass:scratched_glass_green_pane2","pneumaticcraft:wall_lamp_inverted_green","mcwroofs:stone_upper_steep_roof","mcwfurnitures:crimson_double_drawer_counter","mcwfurnitures:spruce_chair","productivebees:expansion_boxes/expansion_box_spruce_canvas","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","cfm:light_blue_trampoline","immersiveengineering:crafting/empty_shell","arseng:source_storage_cell_64k","cfm:stripped_crimson_coffee_table","simplylight:edge_light","mcwfences:railing_granite_wall","refinedstorage:coloring_recipes/cyan_disk_manipulator","immersiveengineering:crafting/plate_uranium_hammering","railcraft:personal_world_spike","immersiveengineering:crafting/armor_steel_chestplate","refinedstorage:coloring_recipes/light_gray_crafter","mcwlights:lava_lamp","toolbelt:pouch","domum_ornamentum:light_gray_floating_carpet","connectedglass:clear_glass_pink2","ad_astra:steel_sliding_door","refinedstorage:coloring_recipes/green_pattern_grid","bigreactors:energizer/powerport_fe","megacells:crafting/greater_energy_card_upgraded","aether:ambrosium_block","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","cfm:magenta_kitchen_drawer","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","dyenamics:bubblegum_concrete_powder","utilitarian:utility/crimson_logs_to_pressure_plates","advanced_ae:quantum_leggings_item_reset","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","expatternprovider:water_cell","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","merequester:requester","delightful:food/cooking/ender_nectar","mcwfences:stone_brick_railing_gate","refinedstorage:coloring_recipes/magenta_fluid_grid","silentgear:dried_sinew","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","ae2:tools/matter_cannon","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","farmersdelight:cooking/fish_stew","supplementaries:slingshot","minecraft:purpur_pillar_from_purpur_block_stonecutting","allthemodium:allthemodium_plate","advanced_ae:quantumunit","ae2:network/parts/level_emitter","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","minecraft:activator_rail","forbidden_arcanus:smelting/arcane_crystal_dust_from_smelting","productivebees:expansion_boxes/expansion_box_mangrove_canvas","everythingcopper:copper_rail","ae2:network/cells/item_storage_components_cell_4k_part","refinedstorage:coloring_recipes/brown_crafting_grid","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","minecraft:crimson_pressure_plate","pneumaticcraft:wall_lamp_inverted_black","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","forbidden_arcanus:clibano_combustion/gold_ingot_from_clibano_combusting_raw_gold","mcwlights:green_paper_lamp","blue_skies:glowing_nature_stonebrick_from_glowstone","supplementaries:spring_launcher","ae2:network/cables/glass_red","dyenamics:dye_ultramarine_carpet","mcwbiomesoplenty:empyreal_curved_gate","utilitix:jungle_shulker_boat_with_shell","cfm:crimson_kitchen_drawer","travelersbackpack:end","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","aquaculture:neptunium_ingot_from_nuggets","mcwroofs:bricks_upper_steep_roof","croptopia:french_fries","botania:terraform_rod","minecraft:iron_pickaxe","refinedstorage:coloring_recipes/light_gray_wireless_transmitter","ae2:shaped/not_so_mysterious_cube","twilightdelight:steeleaf_knife","mcwbiomesoplenty:willow_plank_four_window","reliquary:fortune_coin","handcrafted:crimson_pillar_trim","mcwfences:railing_sandstone_wall","bigreactors:crafting/cyanite_storage_to_component","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwroofs:magenta_concrete_attic_roof","ae2:network/cables/glass_orange","mcwwindows:granite_window","mcwfurnitures:stripped_spruce_table","deepresonance:resonating_plate","minecraft:vibranium_mage_chestplate_smithing","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","immersiveengineering:crafting/steel_wallmount","mcwfences:modern_diorite_wall","reliquary:angelheart_vial","create:crafting/kinetics/cart_assembler","ae2:network/cells/fluid_storage_cell_256k_storage","allthemodium:unobtainium_nugget_from_ingot","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","biomesoplenty:blackstone_spines_from_blackstone_stonecutting","mcwpaths:blackstone_running_bond_slab","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","refinedstorage:advanced_processor","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","mcwwindows:brown_curtain","mcwfences:modern_end_brick_wall","rftoolsstorage:dump_module","mcwroofs:black_concrete_roof","cfm:orange_cooler","sophisticatedbackpacks:inception_upgrade","minecraft:lime_stained_glass","mcwbridges:balustrade_mossy_stone_bricks_bridge","dyenamics:dye_wine_carpet","ae2:tools/paintballs_black","minecraft:netherite_helmet_smithing","refinedstorage:coloring_recipes/brown_wireless_transmitter","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","pneumaticcraft:wall_lamp_light_blue","mcwroofs:pink_concrete_upper_steep_roof","alltheores:steel_plate","mcwbiomesoplenty:mahogany_plank_window","mcwroofs:gray_terracotta_top_roof","minecraft:stone_brick_walls_from_stone_stonecutting","mcwtrpdoors:crimson_barn_trapdoor","ae2:tools/nether_quartz_wrench","modularrouters:puller_module_2","mcwpaths:andesite_running_bond_stairs","cfm:white_kitchen_counter","mcwbiomesoplenty:stripped_umbran_log_four_window","create:crafting/kinetics/filter","cfm:light_blue_kitchen_counter","modularrouters:puller_module_1","ae2:network/cables/covered_fluix_clean","dyenamics:maroon_terracotta","immersiveengineering:crafting/plate_copper_hammering","ae2:network/blocks/spatial_io_pylon","mcwfences:granite_railing_gate","mcwroofs:light_blue_terracotta_lower_roof","minecraft:yellow_dye_from_dandelion","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwdoors:spruce_bamboo_door","mcwbiomesoplenty:stripped_palm_pane_window","botania:cobble_rod","trashcans:energy_trash_can","botania:aura_ring","botania:dreamwood_twig","mcwdoors:print_nether","biomesoplenty:blackstone_spines","mcwpaths:stone_crystal_floor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","megacells:crafting/decompression_module","cfm:fridge_dark","chimes:copper_chimes","mcwbiomesoplenty:willow_window2","botania:livingrock_stairs","mcwfurnitures:crimson_bookshelf_cupboard","mcwbiomesoplenty:stripped_mahogany_log_four_window","create:crafting/kinetics/brass_door","farmersdelight:cooking_pot","sophisticatedstorage:crimson_chest","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","mcwtrpdoors:mangrove_bark_trapdoor","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfurnitures:stripped_spruce_end_table","mcwfences:dark_oak_stockade_fence","ae2:tools/network_memory_card","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","ae2:tools/paintballs_yellow","mcwbiomesoplenty:empyreal_hedge","minecraft:polished_blackstone_brick_slab_from_blackstone_stonecutting","minecraft:polished_blackstone_brick_wall_from_polished_blackstone_stonecutting","dyenamics:dye_icy_blue_carpet","aether:diamond_leggings_repairing","mcwdoors:jungle_nether_door","croptopia:frying_pan","minecraft:sugar_from_honey_bottle","cfm:stripped_jungle_kitchen_counter","pylons:clear_potion_filter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","expatternprovider:wireless_connector","mcwroofs:cyan_terracotta_upper_lower_roof","mcwroofs:orange_terracotta_attic_roof","reliquary:mob_charm_fragments/wither_skeleton","minecraft:leather_chestplate","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","forbidden_arcanus:clibano_combustion/arcane_crystal_from_clibano_combusting","immersiveengineering:crafting/radiator","mcwroofs:black_terracotta_lower_roof","blue_skies:moonstone_pressure_plate","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","cfm:spruce_crate","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwwindows:prismarine_brick_gothic","botania:flask","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","mcwpaths:blackstone_windmill_weave_slab","pneumaticcraft:charging_upgrade","ae2:network/cables/dense_covered_fluix_clean","littlelogistics:switch_rail","dyenamics:dye_honey_carpet","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwroofs:purple_concrete_attic_roof","nethersdelight:blackstone_furnace","constructionwand:core_angel","enderio:resetting_lever_thirty_from_prev","ae2:network/cables/covered_fluix","xnet:connector_red_dye","mcwroofs:red_terracotta_roof","minecolonies:shapetool","functionalstorage:netherite_upgrade","twigs:schist","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","mcwroofs:lime_terracotta_top_roof","mcwroofs:crimson_planks_lower_roof","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","expatternprovider:circuit_cutter","mcwroofs:orange_terracotta_upper_lower_roof","mcwtrpdoors:jungle_blossom_trapdoor","mcwlights:copper_candle_holder","minecraft:mossy_stone_brick_slab","minecraft:andesite_slab","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:polished_blackstone_wall_from_polished_blackstone_stonecutting","ae2:tools/nether_quartz_spade","minecraft:honey_block","reliquary:mob_charm_fragments/slime","pneumaticcraft:reinforced_brick_tile_from_bricks_stonecutting","create:crafting/kinetics/large_cogwheel","cfm:stripped_spruce_desk","sophisticatedbackpacks:crafting_upgrade","mcwdoors:crimson_japanese2_door","productivebees:stonecutter/willow_canvas_expansion_box","aquaculture:birch_fish_mount","ae2:network/cells/fluid_storage_cell_1k_storage","bigreactors:crafting/cyanite_component_to_storage","mcwwindows:stripped_mangrove_log_window","securitycraft:secret_crimson_hanging_sign","pneumaticcraft:standby_upgrade","twilightdelight:cutting/ice_sword","buildinggadgets2:template_manager","connectedglass:clear_glass_green_pane2","create:crafting/kinetics/metal_bracket","mcwwindows:jungle_window","refinedstorage:coloring_recipes/purple_fluid_grid","minecraft:cobblestone_stairs","connectedglass:clear_glass_magenta2","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","blue_skies:comet_bookshelf","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","tombstone:ankh_of_prayer","cfm:white_sofa","refinedstorage:coloring_recipes/red_crafting_monitor","mcwbiomesoplenty:pine_highley_gate","botania:elven_spreader","mcwfences:ornate_metal_fence","ae2:shaped/slabs/fluix_block","mcwfences:acacia_highley_gate","utilitix:dark_oak_shulker_boat_with_shell","aiotbotania:livingrock_sword","immersiveengineering:crafting/drillhead_iron","cfm:black_kitchen_drawer","mcwroofs:andesite_steep_roof","botania:obedience_stick","mcwwindows:bricks_pane_window","allthecompressed:compress/uranium_block_1x","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","refinedstorage:coloring_recipes/blue_grid","mcwfurnitures:oak_cupboard_counter","mcwbridges:crimson_rail_bridge","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","laserio:filter_count_nbtclear","travelersbackpack:squid","mcwroofs:spruce_roof","mcwroofs:base_upper_steep_roof","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","ae2:network/blocks/cell_workbench","pneumaticcraft:heat_sink","minecraft:polished_andesite_slab","mcwlights:magenta_lamp","chemlib:gallium_ingot_to_nugget","create:crafting/logistics/powered_latch","productivebees:stonecutter/warped_canvas_expansion_box","ae2:network/cables/dense_covered_lime","mcwfurnitures:crimson_chair","refinedstorage:coloring_recipes/blue_crafter_manager","botania:mana_fluxfield","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","cfm:acacia_kitchen_drawer","utilitarian:utility/oak_logs_to_pressure_plates","sophisticatedstorage:stonecutter_upgrade","mcwroofs:white_terracotta_upper_lower_roof","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","mcwfurnitures:stripped_crimson_end_table","aquaculture:double_hook","allthemodium:allthemodium_rod","ae2:shaped/slabs/sky_stone_block","farmersdelight:cooking/pasta_with_meatballs","create:crafting/materials/andesite_alloy_from_block","bigreactors:turbine/basic/controller","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwroofs:light_gray_terracotta_lower_roof","immersiveengineering:crafting/wire_steel","handcrafted:terracotta_thin_pot","utilitarian:angel_block","refinedstorage:coloring_recipes/yellow_detector","create:crafting/kinetics/water_wheel","forbidden_arcanus:clibano_combustion/netherite_scrap_from_clibano_combusting","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","botania:conversions/manasteel_block_deconstruct","dyenamics:spring_green_concrete_powder","mcwpaths:blackstone_crystal_floor_path","ae2additions:cells/super/16m-housing","cfm:jungle_cabinet","minecraft:iron_leggings","ad_astra:steel_cable","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","mcwroofs:base_roof_block","appflux:flux_accessor_alt","productivebees:hives/advanced_mangrove_canvas_hive","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","ae2:tools/paintballs_white","mcwbiomesoplenty:palm_window","twigs:blackstone_column","botania:conversions/pink_petal_block_deconstruct","cfm:pink_kitchen_sink","advanced_ae:quantumcore","mcwfurnitures:stripped_oak_drawer_counter","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","littlecontraptions:barge_assembler","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","immersiveengineering:crafting/gunpart_hammer","immersiveengineering:crafting/coal_coke_to_coke","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwroofs:light_gray_striped_awning","mcwfurnitures:jungle_double_wardrobe","utilitarian:utility/crimson_logs_to_doors","create:crafting/kinetics/analog_lever","create:andesite_scaffolding_from_andesite_alloy_stonecutting","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","twilightforest:equipment/fiery_iron_pickaxe","botania:terra_axe","domum_ornamentum:light_blue_floating_carpet","mcwroofs:crimson_lower_roof","mcwtrpdoors:crimson_blossom_trapdoor","ae2:shaped/stairs/fluix_block","create:crafting/kinetics/sticker","ae2:decorative/fluix_block","pneumaticcraft:compressed_iron_chestplate","ae2:network/cables/dense_covered_blue","dyenamics:aquamarine_wool","rftoolsutility:inventory_module","mcwpaths:brick_square_paving","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","create:crafting/logistics/content_observer","refinedstorage:coloring_recipes/gray_network_receiver","ae2:block_cutter/slabs/quartz_slab","appmek:portable_chemical_storage_cell_256k","refinedstorage:coloring_recipes/green_wireless_transmitter","sophisticatedbackpacks:chipped/glassblower_upgrade","utilitix:acacia_shulker_boat_with_shell","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:empyreal_plank_four_window","minecraft:jungle_door","mcwroofs:crimson_planks_top_roof","enderio:resetting_lever_three_hundred_inv_from_prev","refinedstorage:coloring_recipes/white_network_receiver","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","twilightforest:naga_banner_pattern","minecraft:crimson_fence","mcwlights:iron_small_chandelier","botania:mushroom_2","botania:mushroom_1","botania:mushroom_0","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","mcwpaths:blackstone_square_paving","mcwpaths:brick_crystal_floor_stairs","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","botania:divining_rod","cfm:oak_coffee_table","mcwroofs:purple_terracotta_steep_roof","minecraft:light_gray_stained_glass","productivebees:hives/advanced_spruce_canvas_hive","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","minecraft:vibranium_mage_helmet_smithing","mcwdoors:garage_gray_door","railcraft:world_spike","allthecompressed:decompress/netherrack_6x","mcwfurnitures:stripped_oak_counter","handcrafted:witch_trophy","ae2:network/cells/item_storage_components_cell_256k_part","minecraft:polished_andesite_from_andesite_stonecutting","allthearcanistgear:vibranium_robes_smithing","littlelogistics:seater_car","botania:ender_eye_block","minecraft:crimson_stairs","refinedstorage:coloring_recipes/orange_fluid_grid","mcwbiomesoplenty:stripped_dead_pane_window","ae2:shaped/walls/fluix_block","mcwroofs:magenta_terracotta_steep_roof","securitycraft:reinforced_diorite","handcrafted:jungle_drawer","botania:mushroom_9","mcwtrpdoors:crimson_four_panel_trapdoor","mcwfurnitures:crimson_modern_desk","botania:mushroom_8","botania:mushroom_7","botania:mushroom_6","botania:mushroom_5","ae2:misc/chests_smooth_sky_stone","minecraft:stone_brick_stairs","botania:mushroom_4","refinedstorage:coloring_recipes/red_network_transmitter","aether:skyroot_beehive","bigreactors:turbine/basic/activefluidport_forge","botania:mushroom_3","bigreactors:reprocessor/outputport","cfm:brown_grill","mcwwindows:quartz_window","mcwroofs:gray_roof","immersiveengineering:crafting/steel_scaffolding_standard","minecraft:diorite","cfm:stripped_crimson_desk","supplementaries:present","securitycraft:reinforced_dark_oak_fence","ad_astra:cable_duct","refinedstorage:coloring_recipes/purple_wireless_transmitter","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","botania:glimmering_livingwood_log","blue_skies:coarse_lunar_dirt","minecraft:polished_blackstone_wall_from_blackstone_stonecutting","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","minecraft:crimson_button","connectedglass:clear_glass_orange2","mcwbiomesoplenty:mahogany_horse_fence","securitycraft:reinforced_chiseled_bookshelf","create:crafting/kinetics/light_blue_seat","utilitix:mangrove_shulker_boat_with_shell","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:netherite_axe_smithing","botania:goddess_charm","refinedstorage:coloring_recipes/magenta_crafter","minecraft:clock","mcwroofs:red_concrete_top_roof","occultism:crafting/otherstone_pedestal","create:crafting/appliances/netherite_diving_helmet_from_netherite","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","railcraft:steel_crowbar","railcraft:lead_gear","dyenamics:dye_peach_carpet","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","megacells:crafting/compression_card","mcwfences:oak_wired_fence","botania:twig_wand","twigs:silt","mcwroofs:grass_upper_steep_roof","mcwroofs:blue_concrete_steep_roof","silentgear:diamond_shard","aquaculture:iron_fillet_knife","mcwfurnitures:stripped_oak_double_drawer_counter","dyenamics:rose_wool","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","productivebees:stonecutter/driftwood_canvas_hive","cfm:lime_picket_fence","dyenamics:bed/conifer_bed","ae2:block_cutter/stairs/quartz_stairs","minecraft:red_nether_bricks","productivebees:stonecutter/kousa_canvas_hive","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwtrpdoors:spruce_whispering_trapdoor","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwwindows:birch_window2","mcwdoors:oak_modern_door","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","botania:magnet_ring","minecraft:prismarine","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwfurnitures:stripped_crimson_bookshelf_cupboard","mcwroofs:light_blue_striped_awning","mcwlights:copper_wall_candle_holder","modularrouters:fluid_module","minecolonies:pasta_plain","mcwlights:purple_lamp","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","silentgear:leather_scrap","mcwroofs:purple_terracotta_roof","twilightforest:equipment/fiery_sword","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","mcwroofs:jungle_upper_steep_roof","cfm:acacia_kitchen_sink_dark","botania:keep_ivy","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","immersiveengineering:crafting/steel_fence","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","chemlib:calcium_ingot_from_blasting_calcium_dust","cfm:stripped_spruce_crate","twigs:rhyolite","pylons:infusion_pylon","mcwwindows:spruce_window2","create:crafting/kinetics/linear_chassis","forbidden_arcanus:diamond_blacksmith_gavel","mcwroofs:brown_terracotta_upper_lower_roof","utilitix:mob_yoinker","mcwwindows:andesite_four_window","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwroofs:thatch_top_roof","mekanism:processing/steel/ingot/from_dust_smelting","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","bigreactors:turbine/ludicrite_block","pneumaticcraft:armor_upgrade","botania:dreamwood_stairs","mcwfences:railing_prismarine_wall","ae2:network/blocks/energy_dense_energy_cell","minecraft:lime_concrete_powder","refinedstorage:coloring_recipes/purple_network_transmitter","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","minecraft:brick_slab_from_bricks_stonecutting","cfm:stripped_jungle_table","minecraft:polished_blackstone_bricks_from_polished_blackstone_stonecutting","mcwlights:festive_lantern","botania:conversions/gray_petal_block_deconstruct","securitycraft:block_change_detector","minecraft:polished_blackstone_button","advanced_ae:advpatpro2","supplementaries:pancake_fd","modularrouters:placer_module","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","productivebees:stonecutter/fir_canvas_hive","minecraft:netherite_scrap_from_blasting","botania:petal_black","botania:lens_warp","enderio:resetting_lever_three_hundred_from_prev","mcwroofs:pink_terracotta_lower_roof","mcwbiomesoplenty:maple_window2","mcwdoors:crimson_barn_door","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","refinedstorage:coloring_recipes/orange_security_manager","mcwbridges:bridge_torch","refinedstorage:coloring_recipes/orange_detector","botania:white_shiny_flower","cfm:stripped_mangrove_kitchen_drawer","mcwroofs:gutter_middle_black","undergarden:wigglewood_boat","connectedglass:scratched_glass1","minecraft:book","mcwfurnitures:oak_drawer_counter","mcwroofs:white_concrete_upper_lower_roof","mcwpaths:andesite_crystal_floor_slab","pneumaticcraft:elevator_frame","securitycraft:reinforced_crimson_fence","minecraft:wooden_shovel","refinedstorage:coloring_recipes/orange_crafting_monitor","additionallanterns:quartz_chain","createoreexcavation:vein_atlas","refinedstorage:coloring_recipes/magenta_disk_manipulator","aether:diamond_axe_repairing","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","create:crafting/kinetics/fluid_pipe","pneumaticcraft:wall_lamp_cyan","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","minecraft:polished_blackstone_brick_stairs_from_polished_blackstone_stonecutting","pneumaticcraft:pressure_chamber_glass_x4","mcwbridges:brick_bridge","botania:dreamwood_slab","refinedstorage:coloring_recipes/lime_crafting_monitor","connectedglass:clear_glass_green2","mcwpaths:red_sand_path_block","ad_astra:wheel","pneumaticcraft:pressure_chamber_glass_x1","cfm:light_blue_cooler","allthecompressed:compress/lapis_block_1x","mcwroofs:lime_terracotta_roof","mcwdoors:print_jungle","create:copper_bars_from_ingots_copper_stonecutting","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","enderio:resetting_lever_five_inv_from_base","mcwwindows:birch_window","botania:vivid_seeds","mcwfences:acacia_picket_fence","reliquary:uncrafting/wither_skeleton_skull","supplementaries:flags/flag_yellow","allthecompressed:compress/spruce_log_1x","domum_ornamentum:sand_bricks","twilightdelight:cooking/tear_drink","mysticalagriculture:supremium_block_uncraft","securitycraft:secret_dark_oak_sign_item","ae2:network/cables/dense_covered_cyan","blue_skies:moonstone_from_shard","sophisticatedbackpacks:smithing_upgrade","minecraft:gold_ingot_from_smelting_raw_gold","botania:golden_seeds","mcwfences:mud_brick_railing_gate","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","pneumaticcraft:heat_pipe","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","railcraft:signal_block_surveyor","pneumaticcraft:wall_lamp_inverted_purple","mcwpaths:stone_windmill_weave","mysticalagriculture:essence/minecraft/amethyst","chemlib:calcium_ingot_to_nugget","mcwfurnitures:oak_lower_triple_drawer","alltheores:uranium_gear","railcraft:logbook","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","botania:shimmerrock","mcwbridges:jungle_log_bridge_middle","utilitix:bamboo_shulker_raft_with_shell","ae2:decorative/quartz_glass","sophisticatedbackpacks:anvil_upgrade","mcwroofs:green_concrete_steep_roof","botania:conversions/lime_petal_block_deconstruct","mcwfences:mangrove_wired_fence","refinedstorage:coloring_recipes/gray_security_manager","minecraft:red_terracotta","mcwfurnitures:oak_wardrobe","twilightdelight:fiery_knife","mcwroofs:thatch_lower_roof","create:crafting/kinetics/basin","mcwdoors:jungle_japanese_door","botania:glass_pickaxe","connectedglass:clear_glass1","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwpaths:blackstone_dumble_paving","mcwroofs:white_roof_block","mcwlights:oak_tiki_torch","securitycraft:block_pocket_manager","refinedstorage:coloring_recipes/orange_pattern_grid","mcwlights:soul_cherry_tiki_torch","expatternprovider:ei","botania:red_pavement","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","enderio:resetting_lever_sixty_inv_from_prev","mcwpaths:brick_running_bond_stairs","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","productivebees:stonecutter/crimson_canvas_hive","mcwlights:red_lamp","bloodmagic:blood_altar","mcwfurnitures:spruce_bookshelf_cupboard","refinedstorage:coloring_recipes/gray_crafter_manager","botania:flower_bag","cfm:purple_kitchen_drawer","ad_astra:steel_plateblock","botania:black_shiny_flower","pneumaticcraft:compressed_brick_wall","mcwtrpdoors:crimson_classic_trapdoor","twilightforest:berry_torch","mcwfences:red_sandstone_railing_gate","enderio:primitive_alloy_smelter","minecraft:netherite_hoe_smithing","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","minecraft:netherite_chestplate_smithing","mcwfurnitures:stripped_crimson_cupboard_counter","cfm:jungle_kitchen_counter","ae2:network/cables/dense_smart_red","mcwfurnitures:stripped_jungle_drawer_counter","mcwlights:golden_candle_holder","cfm:spruce_coffee_table","farmersdelight:diamond_knife","terralith:observer_alt","refinedstorage:coloring_recipes/green_crafting_grid","minecraft:quartz_stairs_from_quartz_block_stonecutting","railcraft:bag_of_cement","bigreactors:fluidizer/powerport","mcwlights:light_blue_lamp","mcwwindows:stone_pane_window","sophisticatedstorage:blasting_upgrade","domum_ornamentum:white_paper_extra","mcwfurnitures:crimson_glass_table","connectedglass:borderless_glass_blue2","mcwbiomesoplenty:fir_window2","dyenamics:fluorescent_dye","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwroofs:yellow_concrete_lower_roof","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","mcwbridges:balustrade_andesite_bridge","additionallanterns:emerald_lantern","refinedstorage:coloring_recipes/pink_crafting_grid","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwroofs:purple_terracotta_top_roof","advanced_ae:quantum_helmet_item_reset","mcwbiomesoplenty:red_maple_hedge","refinedstorage:coloring_recipes/white_crafter","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","allthecompressed:compress/glowstone_1x","mcwroofs:gutter_middle_blue","refinedstorage:coloring_recipes/black_crafting_monitor","connectedglass:clear_glass_gray2","sfm:manager","mcwfurnitures:stripped_spruce_counter","mcwbiomesoplenty:stripped_mahogany_log_window2","botania:apothecary_mossy","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","mcwwindows:stripped_dark_oak_pane_window","eidolon:smooth_stone_masonry_stonecutter_0","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwroofs:spruce_lower_roof","mcwwindows:cyan_mosaic_glass_pane","botania:mana_tablet_alt","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","cfm:oak_crate","mcwroofs:blackstone_upper_steep_roof","littlelogistics:tee_junction_rail","botania:livingwood_twig","mcwbridges:crimson_bridge_pier","mcwdoors:spruce_whispering_door","mcwbiomesoplenty:willow_window","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","ae2:tools/network_tool","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","minecraft:bowl","deeperdarker:bloom_boat","refinedstorage:coloring_recipes/cyan_pattern_grid","mcwwindows:ender_brick_arrow_slit","utilitix:linked_repeater","minecolonies:doublegrass","mcwroofs:pink_terracotta_upper_steep_roof","dyenamics:rose_stained_glass","mcwpaths:blackstone_diamond_paving","farmersdelight:cooking/hot_cocoa","sophisticatedstorage:chipped/carpenters_table_upgrade","pneumaticcraft:vortex_cannon","advanced_ae:smalladvpatpro2","minecraft:purple_concrete_powder","refinedstorage:coloring_recipes/black_security_manager","croptopia:steamed_crab","productivebees:stonecutter/wisteria_canvas_hive","securitycraft:reinforced_jungle_fence","ae2:network/cables/dense_covered_red","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_kitchen_drawer","mcwroofs:light_gray_upper_steep_roof","botania:red_string","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","minecraft:spectral_arrow","mcwroofs:light_blue_concrete_upper_steep_roof","allthemodium:ancient_stone_stairs","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","refinedstorage:coloring_recipes/purple_pattern_grid","cfm:light_blue_kitchen_sink","ad_astra:steel_panel","mcwbridges:crimson_log_bridge_middle","blue_skies:maple_spear","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","mcwroofs:crimson_roof","domum_ornamentum:green_brick_extra","supplementaries:candle_holders/candle_holder_light_gray","reliquary:mob_charm_fragments/zombified_piglin","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","refinedstorage:coloring_recipes/black_fluid_grid","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwroofs:blue_terracotta_steep_roof","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","expatternprovider:pre_bus","pneumaticcraft:reinforced_brick_stairs_from_bricks_stonecutting","refinedstorage:coloring_recipes/brown_grid","allthearcanistgear:vibranium_hat_smithing","ae2:network/cables/dense_smart_fluix_clean","farmersdelight:cooking/noodle_soup","dyenamics:amber_terracotta","allthecompressed:compress/netherite_block_1x","mcwdoors:spruce_beach_door","simplylight:illuminant_light_gray_block_dyed","mcwfurnitures:crimson_table","mcwbiomesoplenty:magic_horse_fence","alchemistry:reactor_output","create:crafting/kinetics/white_sail","refinedstorage:coloring_recipes/lime_wireless_transmitter","ae2:network/crafting/molecular_assembler","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","bigreactors:reprocessor/powerport","mcwbridges:stone_bridge_pier","aether:white_cape","refinedstorage:coloring_recipes/relay","cfm:green_picket_gate","ae2:tools/misctools_entropy_manipulator","railcraft:radio_circuit","aether:netherite_pickaxe_repairing","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","mcwroofs:white_terracotta_steep_roof","tombstone:fishing_rod_of_misadventure","refinedstorage:coloring_recipes/lime_crafter","minecraft:dispenser","minecraft:daylight_detector","mcwbiomesoplenty:mahogany_plank_window2","mcwpaths:cobbled_deepslate_clover_paving","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","ae2:decorative/sky_stone_brick_from_stonecutting","securitycraft:secret_acacia_sign_item","create:crafting/kinetics/hand_crank","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwtrpdoors:spruce_classic_trapdoor","allthemodium:ancient_stone_wall","mcwfences:mangrove_picket_fence","refinedstorage:coloring_recipes/magenta_detector","aquaculture:planks_from_driftwood","additionallanterns:bricks_chain","botania:dry_seeds","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","mcwroofs:base_steep_roof","mcwroofs:yellow_terracotta_upper_steep_roof","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","supplementaries:flags/flag_lime","create:crafting/logistics/powered_toggle_latch","mcwroofs:gray_terracotta_steep_roof","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","pneumaticcraft:wall_lamp_blue","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","mcwpaths:brick_dumble_paving","handcrafted:jungle_desk","utilitarian:snad/drit","refinedstorage:coloring_recipes/purple_controller","pneumaticcraft:reinforced_brick_pillar_from_bricks_stonecutting","refinedstorage:coloring_recipes/orange_controller","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","refinedstorage:coloring_recipes/detector","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","ad_astra:steel_trapdoor","voidtotem:totem_of_void_undying","botania:red_string_alt","securitycraft:reinforced_pink_stained_glass_pane_from_dye","create:crafting/kinetics/cyan_seat","cfm:brown_kitchen_counter","mcwfurnitures:stripped_crimson_desk","mcwroofs:stone_bricks_attic_roof","pneumaticcraft:fluid_mixer","mcwroofs:lime_concrete_attic_roof","minecraft:cartography_table","railcraft:manual_rolling_machine","mcwdoors:crimson_western_door","securitycraft:alarm","pneumaticcraft:manometer","ad_astra:launch_pad","cfm:magenta_picket_gate","rftoolsutility:charged_porter","mcwfurnitures:oak_counter","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","ae2:network/blocks/energy_vibration_chamber","pneumaticcraft:compressed_brick_tile","mcwtrpdoors:crimson_swamp_trapdoor","mcwfurnitures:stripped_crimson_drawer_counter","ae2:tools/fluix_shovel","reliquary:magicbane","pneumaticcraft:wall_lamp_inverted_magenta","minecraft:cooked_porkchop_from_smoking","modularrouters:sender_module_1_alt","botania:petal_lime_double","mcwroofs:oak_top_roof","ae2:block_cutter/stairs/sky_stone_stairs","refinedstorage:coloring_recipes/cyan_security_manager","refinedstorage:coloring_recipes/cyan_relay","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","ae2:network/cells/view_cell_storage","cfm:crimson_kitchen_sink_light","mcwwindows:green_curtain","utilitix:comparator_redirector_down","mysticalagriculture:essence/minecraft/netherite_ingot","refinedstorage:coloring_recipes/blue_crafter","mcwwindows:stone_window","mcwroofs:gray_steep_roof","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwroofs:crimson_upper_lower_roof","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","alltheores:iron_ore_hammer","refinedstorage:coloring_recipes/lime_network_transmitter","mcwroofs:crimson_upper_steep_roof","sophisticatedstorage:jukebox_upgrade","productivebees:stonecutter/yucca_canvas_hive","mcwtrpdoors:jungle_classic_trapdoor","dankstorage:dank_6","cfm:white_picket_gate","mcwfurnitures:stripped_oak_modern_wardrobe","dankstorage:dank_7","dyenamics:mint_concrete_powder","dankstorage:dank_2","mcwfences:railing_nether_brick_wall","dankstorage:dank_3","dankstorage:dank_4","ae2:network/cables/glass_black","mcwroofs:yellow_terracotta_upper_lower_roof","dankstorage:dank_5","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","dankstorage:dank_1","mcwfences:crimson_picket_fence","minecraft:unobtainium_mage_leggings_smithing","mcwtrpdoors:crimson_paper_trapdoor","everythingcopper:copper_ladder","botania:polished_livingrock","mcwwindows:warped_stem_window","railcraft:invar_gear","mcwbiomesoplenty:fir_curved_gate","aether:diamond_hoe_repairing","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:thatch_upper_steep_roof","mcwroofs:red_concrete_upper_steep_roof","bloodmagic:blood_rune_aug_capacity_2","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","productivebees:expansion_boxes/expansion_box_warped_canvas","ae2:tools/portable_fluid_cell_16k","simplylight:illuminant_orange_block_dyed","mcwdoors:print_spruce","minecraft:polished_blackstone_stairs","minecraft:netherite_pickaxe_smithing","alchemistry:compactor","cfm:stripped_crimson_crate","minecraft:dropper","create:crafting/kinetics/piston_extension_pole","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","ae2:network/cables/smart_white","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","pneumaticcraft:pressure_tube","botania:manasteel_block","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","enderio:resetting_lever_ten","advanced_ae:quantum_chestplate_item_reset","expatternprovider:oversize_interface_part","mcwwindows:oak_window2","sophisticatedstorage:pickup_upgrade","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","farmersdelight:honey_cookie","productivebees:stonecutter/spruce_canvas_hive","mcwfences:mangrove_horse_fence","bigreactors:reprocessor/collector","create:crafting/kinetics/lime_seat","mcwdoors:crimson_tropical_door","minecraft:golden_helmet","botania:animated_torch","mcwpaths:brick_basket_weave_paving","minecraft:mossy_stone_brick_wall_from_mossy_stone_brick_stonecutting","botania:thunder_sword","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","megacells:cells/portable/portable_item_cell_16m","mcwfurnitures:stripped_crimson_lower_triple_drawer","ae2:network/crafting/4k_cpu_crafting_storage","railcraft:cart_dispenser","utilitarian:utility/oak_logs_to_slabs","ae2:network/cables/dense_smart_pink","create:crafting/kinetics/green_seat","cfm:stripped_dark_oak_kitchen_sink_light","securitycraft:reinforced_spruce_fence_gate","additionallanterns:obsidian_lantern","dyenamics:cherenkov_concrete_powder","travelersbackpack:netherite","productivebees:stonecutter/yucca_canvas_expansion_box","mcwwindows:quartz_pane_window","refinedstorage:coloring_recipes/green_network_transmitter","mcwfurnitures:stripped_crimson_bookshelf_drawer","bloodmagic:raw_hellforged_block","refinedstorage:coloring_recipes/orange_network_receiver","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:base_roof_slab","farmersdelight:cooking/mushroom_stew","handcrafted:fancy_painting","mcwfurnitures:spruce_table","bigreactors:energizer/energycore","cfm:spruce_desk_cabinet","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","mcwwindows:oak_pane_window","minecraft:blackstone_slab","cfm:crimson_chair","minecraft:cooked_porkchop_from_campfire_cooking","pneumaticcraft:minigun_upgrade","connectedglass:clear_glass_brown2","mcwwindows:stripped_cherry_log_window","mcwroofs:light_blue_terracotta_attic_roof","minecraft:cooked_rabbit_from_smoking","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","twigs:polished_rhyolite","minecraft:quartz_bricks","dyenamics:dye_rose_carpet","utilitarian:utility/crimson_logs_to_stairs","botania:temperance_stone","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","ae2:shaped/stairs/quartz_block","mcwroofs:gray_roof_slab","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","bigreactors:reactor/basic/fuelrod_ingots_uranium","mcwpaths:stone_running_bond_stairs","expatternprovider:u_terminal/upgrade","refinedstorage:coloring_recipes/yellow_disk_manipulator","mcwbridges:balustrade_bricks_bridge","rftoolsbuilder:red_shield_template_block","refinedstorage:coloring_recipes/white_crafting_grid","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","allthetweaks:atm_star_block","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","minecraft:raw_gold_block","chemlib:calcium_ingot_to_block","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwroofs:magenta_terracotta_top_roof","bloodmagic:soul_forge","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","pneumaticcraft:pressure_chamber_valve","pneumaticcraft:logistics_frame_passive_provider","mcwroofs:jungle_planks_steep_roof","securitycraft:motion_activated_light","pneumaticcraft:wall_lamp_magenta","computercraft:pocket_computer_normal","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","ae2:network/parts/formation_plane_alt","minecraft:chiseled_polished_blackstone_from_polished_blackstone_stonecutting","enderio:black_paper","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","cfm:green_picket_fence","mcwroofs:white_terracotta_top_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","sophisticatedbackpacks:pickup_upgrade","pneumaticcraft:assembly_laser","refinedstorage:coloring_recipes/red_security_manager","mcwfurnitures:crimson_double_wardrobe","pneumaticcraft:pneumatic_chestplate","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwbiomesoplenty:stripped_magic_log_window2","pneumaticcraft:compressed_iron_leggings","mcwroofs:orange_concrete_upper_lower_roof","mcwfurnitures:spruce_modern_wardrobe","mcwtrpdoors:spruce_barn_trapdoor","ae2:network/blocks/pattern_providers_interface_part","appbot:fluix_mana_pool","productivebees:stonecutter/hellbark_canvas_hive","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","botania:manasteel_boots","minecraft:netherite_drill_smithing","mcwfences:modern_andesite_wall","mcwroofs:light_gray_terracotta_attic_roof","ae2:network/cells/view_cell","mcwfurnitures:crimson_stool_chair","botania:terra_pick","botania:brewery","mcwroofs:crimson_attic_roof","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","enderio:resetting_lever_ten_from_inv","botania:blood_pendant","mcwroofs:light_gray_concrete_upper_lower_roof","cfm:stripped_crimson_desk_cabinet","mcwfurnitures:jungle_large_drawer","botania:monocle","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","computercraft:turtle_normal_overlays/turtle_rainbow_overlay","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","botania:terra_plate","modularrouters:sender_module_3","productivebees:stonecutter/crimson_canvas_expansion_box","modularrouters:sender_module_2","minecraft:lightning_rod","modularrouters:sender_module_1","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","pneumaticcraft:wall_lamp_lime","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","dyenamics:lavender_terracotta","chemlib:gallium_nugget_to_ingot","mcwroofs:jungle_planks_top_roof","minecraft:honeycomb_block","artifacts:eternal_steak_smoker","ae2:tools/certus_quartz_hoe","mcwtrpdoors:crimson_whispering_trapdoor","cfm:lime_kitchen_drawer","ae2:network/cables/glass_light_blue","mcwbiomesoplenty:redwood_pane_window","mcwfurnitures:spruce_drawer_counter","mcwdoors:crimson_beach_door","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","botania:mutated_seeds","pneumaticcraft:smart_chest_kit","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","create:crafting/appliances/filter_clear","pneumaticcraft:reinforced_bricks","simplylight:edge_light_top","mcwdoors:spruce_bark_glass_door","cfm:spruce_desk","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","twilightdelight:cooking/torchberry_juice","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","cfm:stripped_spruce_park_bench","eccentrictome:tome","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","create:crafting/kinetics/goggles","aether:aether_saddle","create:crafting/kinetics/rope_pulley","farmersdelight:wheat_dough_from_water","mcwroofs:crimson_planks_roof","create:crafting/kinetics/cuckoo_clock","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","refinedstorage:coloring_recipes/gray_detector","mcwlights:pink_lamp","railcraft:steel_boots","ad_astra:zip_gun","refinedstorage:coloring_recipes/white_crafting_monitor","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","allthemodium:allthemodium_nugget_from_ingot","refinedstorage:coloring_recipes/black_controller","botania:mining_ring","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","ad_astra:space_boots","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","deeperdarker:soul_elytra","supplementaries:turn_table","mcwroofs:cyan_terracotta_attic_roof","expatternprovider:pattern_modifier","rftoolsbuilder:space_chamber_card","mcwroofs:gray_terracotta_upper_lower_roof","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","sophisticatedstorage:chipped/tinkering_table_upgrade","buildinggadgets2:gadget_copy_paste","allthecompressed:compress/end_stone_1x","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","sophisticatedstorage:storage_link_from_controller","securitycraft:reinforced_dropper","mcwfurnitures:stripped_crimson_glass_table","twigs:blackstone_column_stonecutting","aquaculture:oak_fish_mount","minecraft:diamond_boots","pneumaticcraft:redstone_module","mcwfurnitures:spruce_double_drawer","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","allthecompressed:compress/atm_star_block_1x","travelersbackpack:magma_cube","mcwfurnitures:stripped_oak_triple_drawer","allthecompressed:compress/soul_sand_1x","allthetweaks:ender_pearl_block","mcwfurnitures:stripped_spruce_drawer_counter","mcwwindows:mangrove_plank_four_window","mythicbotany:mana_infuser","refinedstorage:coloring_recipes/white_security_manager","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwwindows:gray_curtain","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","minecraft:brewing_stand","everythingcopper:copper_door","travelersbackpack:dragon","twigs:bloodstone","mcwfurnitures:stripped_spruce_modern_wardrobe","create:crafting/kinetics/red_seat","ae2:network/parts/quartz_fiber_part","productivebees:expansion_boxes/expansion_box_oak_canvas","mcwwindows:warped_planks_window","sophisticatedbackpacks:chipped/loom_table_upgrade","mcwroofs:orange_concrete_steep_roof","cfm:orange_kitchen_counter","mcwdoors:jungle_paper_door","mcwpaths:brick_flagstone_stairs","dyenamics:bed/persimmon_bed","pneumaticcraft:spawner_agitator","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","ae2:network/cables/smart_light_gray","mcwbiomesoplenty:stripped_palm_log_four_window","arseng:portable_source_cell_256k","mcwroofs:gutter_base_light_blue","mcwpaths:blackstone_running_bond_stairs","mcwpaths:andesite_crystal_floor_path","ae2:network/wireless_crafting_terminal","create:crafting/kinetics/orange_seat","refinedstorage:coloring_recipes/white_relay","minecraft:diamond_block","mcwroofs:deepslate_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","productivebees:stonecutter/jacaranda_canvas_hive","dyenamics:icy_blue_terracotta","dyenamics:cherenkov_stained_glass","mcwroofs:jungle_planks_upper_steep_roof","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","modularrouters:vacuum_module","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","bigreactors:fluidizer/outputport","refinedstorage:coloring_recipes/brown_network_transmitter","cfm:green_cooler","alltheores:steel_rod","botania:mana_gun","refinedstorage:coloring_recipes/gray_relay","bigreactors:blasting/graphite_from_coal","cfm:crimson_desk_cabinet","sophisticatedbackpacks:stack_upgrade_tier_2","botania:astrolabe","sophisticatedbackpacks:stack_upgrade_tier_1","appmek:portable_chemical_storage_cell_1k","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","ae2:network/cables/dense_covered_light_blue","simplylight:illuminant_lime_block_on_dyed","pneumaticcraft:compressed_iron_ingot_from_block","sophisticatedstorage:backpack_stack_upgrade_tier_1_from_storage_stack_upgrade_tier_2","minecolonies:raw_noodle","sophisticatedbackpacks:restock_upgrade","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","farmersdelight:wheat_dough_from_eggs","securitycraft:reinforced_brown_stained_glass_pane_from_dye","minecraft:cooked_mutton","railways:crafting/smokestack_long","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","forbidden_arcanus:clibano_combustion/iron_ingot_from_clibano_combusting_raw_iron","twilightforest:material/carminite","refinedstorage:coloring_recipes/brown_fluid_grid","twilightdelight:fiery_knifealt","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","minecraft:black_terracotta","travelersbackpack:pig","supplementaries:flower_box","productivebees:stonecutter/kousa_canvas_expansion_box","allthecompressed:decompress/emerald_block_4x","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","computercraft:turtle_normal_overlays/turtle_trans_overlay","dankstorage:3_to_4","mcwfurnitures:spruce_counter","ae2:tools/certus_quartz_cutting_knife","utilitarian:snad/snad","handcrafted:bricks_corner_trim","croptopia:shaped_bacon","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","minecraft:light_gray_terracotta","dyenamics:amber_stained_glass","minecraft:white_bed","mcwroofs:pink_terracotta_attic_roof","reliquary:glowing_water","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","dyenamics:spring_green_dye","refinedstorage:coloring_recipes/cyan_wireless_transmitter","dyenamics:wine_terracotta","mcwroofs:light_gray_attic_roof","cfm:dye_green_picket_gate","refinedstorage:coloring_recipes/pattern_grid","sophisticatedstorage:jungle_chest","minecraft:oak_boat","minecraft:cobblestone_slab_from_cobblestone_stonecutting","botania:corporea_spark","minecraft:mossy_stone_brick_stairs_from_mossy_stone_brick_stonecutting","occultism:crafting/book_of_calling_foliot_cleaner","merequester:requester_terminal","productivebees:hives/advanced_acacia_canvas_hive","mcwfurnitures:stripped_jungle_covered_desk","ae2:shaped/stairs/smooth_sky_stone_block","occultism:smelting/burnt_otherstone","cfm:black_picket_gate","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","allthecompressed:decompress/emerald_block_5x","appmek:portable_chemical_storage_cell_4k","minecraft:red_dye_from_rose_bush","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:crimson_cottage_trapdoor","mcwfurnitures:stripped_spruce_lower_bookshelf_drawer","simplylight:illuminant_blue_block_on_toggle","mcwroofs:black_attic_roof","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","sophisticatedstorage:chipped/botanist_workbench_upgrade","botania:manasteel_shears","ae2:network/cells/item_storage_cell_64k","ae2:network/cables/smart_green","mcwfurnitures:stripped_jungle_large_drawer","minecraft:jungle_fence","mcwfurnitures:spruce_striped_chair","mcwwindows:stone_brick_gothic","ae2:block_cutter/walls/fluix_wall","handcrafted:blackstone_pillar_trim","minecraft:glass_bottle","minecraft:iron_ingot_from_smelting_raw_iron","mcwfences:mesh_metal_fence","mcwbiomesoplenty:magic_plank_window2","mcwroofs:pink_terracotta_upper_lower_roof","refinedstorage:coloring_recipes/black_network_receiver","mcwlights:green_lamp","botania:conjuration_catalyst","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","sophisticatedbackpacks:chipped/tinkering_table_upgrade","dyenamics:banner/conifer_banner","minecraft:soul_campfire","botania:clip","botania:red_string_interceptor","securitycraft:reinforced_magenta_stained_glass","travelersbackpack:spider","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","domum_ornamentum:green_cactus_extra","refinedstorage:coloring_recipes/red_network_receiver","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","refinedstorage:coloring_recipes/pink_grid","domum_ornamentum:green_cobblestone_extra","sophisticatedbackpacks:stack_downgrade_tier_2","sophisticatedbackpacks:stack_downgrade_tier_1","sophisticatedbackpacks:stack_downgrade_tier_3","mcwwindows:prismarine_pane_window","pneumaticcraft:air_canister","mcwpaths:brick_windmill_weave_path","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","ae2:tools/paintballs_magenta","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","pneumaticcraft:compressed_iron_gear","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","refinedstorage:coloring_recipes/gray_pattern_grid","twigs:cracked_bricks","supplementaries:slice_map","productivebees:expansion_boxes/expansion_box_jungle_canvas","littlelogistics:automatic_tee_junction_rail","mcwbridges:brick_bridge_pier","connectedglass:borderless_glass_green_pane2","ae2:misc/deconstruction_fluix_block","refinedstorage:coloring_recipes/pink_detector","allthecompressed:compress/gravel_5x","mcwroofs:cobblestone_lower_roof","alltheores:invar_plate","occultism:crafting/butcher_knife","blue_skies:lunar_spear","cfm:stripped_spruce_desk_cabinet","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","delightful:knives/manasteel_knife","minecraft:crimson_fence_gate","securitycraft:reinforced_sticky_piston","pneumaticcraft:kerosene_lamp","botania:lens_normal","mcwfences:end_brick_pillar_wall","mcwroofs:bricks_steep_roof","cfm:gray_grill","dyenamics:honey_dye","paraglider:cosmetic/rito_goddess_statue","aether:skyroot_iron_vanilla_shield","mcwwindows:prismarine_brick_arrow_slit","allthemodium:ancient_smooth_stone_from_ancient_stone_blasting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","enderio:resetting_lever_sixty_inv_from_base","mcwtrpdoors:oak_barn_trapdoor","mcwroofs:black_terracotta_upper_steep_roof","enderio:reinforced_obsidian_block","mcwroofs:light_gray_upper_lower_roof","pneumaticcraft:wall_lamp_inverted_cyan","dankstorage:6_to_7","minecraft:cooked_chicken_from_campfire_cooking","mcwroofs:spruce_steep_roof","mcwfurnitures:jungle_modern_chair","ae2:network/cells/item_storage_cell_4k_storage","create:crafting/kinetics/radial_chassis","mcwfurnitures:spruce_bookshelf_drawer","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","mcwroofs:cyan_terracotta_upper_steep_roof","minecraft:quartz_slab","handcrafted:phantom_trophy","modularrouters:modular_router","mcwfurnitures:stripped_jungle_coffee_table","mcwroofs:stone_bricks_roof","cfm:cyan_grill","botania:thorn_chakram","simplylight:illuminant_purple_block_dyed","ae2:block_cutter/stairs/smooth_sky_stone_stairs","dyenamics:amber_wool","farmersdelight:iron_knife","minecraft:quartz_pillar_from_quartz_block_stonecutting","railcraft:steel_helmet","mcwfurnitures:stripped_jungle_double_drawer","mcwroofs:spruce_attic_roof","ae2:network/cables/smart_black","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","minecraft:observer","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","allthemodium:unobtainium_allthemodium_alloy_block","cfm:dark_oak_kitchen_sink_dark","mcwlights:cyan_lamp","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","immersiveengineering:crafting/manual","cfm:dark_oak_mail_box","minecraft:polished_blackstone_stairs_from_polished_blackstone_stonecutting","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","immersiveengineering:crafting/hoe_steel","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","handcrafted:salmon_trophy","minecraft:bucket","mcwroofs:grass_upper_lower_roof","sophisticatedbackpacks:chipped/botanist_workbench_upgrade","supplementaries:speaker_block","ae2:block_cutter/stairs/fluix_stairs","farmersdelight:chicken_sandwich","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","enderio:iron_gear","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","twigs:mixed_bricks","mcwpaths:andesite_honeycomb_paving","railcraft:switch_track_lever","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","minecraft:charcoal","mcwroofs:black_concrete_attic_roof","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","botania:petal_magenta","silentgear:emerald_shard","minecraft:unobtainium_mage_helmet_smithing","ae2:network/cables/smart_orange","cfm:white_picket_fence","mcwdoors:crimson_four_panel_door","dyenamics:dye_amber_carpet","supplementaries:bellows","productivebees:stonecutter/dark_oak_canvas_hive","pneumaticcraft:reinforced_brick_stairs","ae2:network/cables/smart_lime","ae2:network/cables/dense_smart_magenta","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","cfm:crimson_crate","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","mcwroofs:bricks_roof","handcrafted:kitchen_hood","appbot:mana_storage_cell_4k","supplementaries:fodder","mcwdoors:jungle_bamboo_door","sophisticatedbackpacks:stonecutter_upgrade","mcwfences:birch_pyramid_gate","chemlib:gallium_block_to_ingot","mcwroofs:yellow_concrete_roof","dyenamics:banner/aquamarine_banner","mcwroofs:red_terracotta_steep_roof","mcwfurnitures:stripped_oak_glass_table","mcwfurnitures:stripped_crimson_table","cfm:white_trampoline","securitycraft:universal_owner_changer","mcwroofs:blackstone_attic_roof","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:black_lower_roof","productivebees:expansion_boxes/expansion_box_dark_oak_canvas","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","reliquary:ender_staff","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","mcwlights:cherry_ceiling_fan_light","minecraft:coal_block","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwwindows:nether_brick_arrow_slit","mcwfurnitures:stripped_spruce_bookshelf_drawer","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","travelersbackpack:netherite_tier_upgrade","securitycraft:secret_birch_hanging_sign","botania:travel_belt","bigreactors:reprocessor/fluidinjector","mcwbiomesoplenty:fir_highley_gate","refinedstorage:coloring_recipes/lime_network_receiver","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","ae2:network/parts/energy_acceptor","additionallanterns:gold_chain","ae2:materials/advancedcard","minecraft:cooked_porkchop","mcwroofs:white_attic_roof","mcwwindows:crimson_stem_window2","ae2:network/cables/smart_light_blue","cfm:stripped_acacia_kitchen_sink_dark","mcwlights:gray_lamp","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","utilitix:birch_shulker_boat_with_shell","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwroofs:orange_striped_awning","dyenamics:banner/cherenkov_banner","mcwlights:black_paper_lamp","mcwroofs:andesite_lower_roof","forbidden_arcanus:arcane_crystal_block_from_arcane_crystal","mcwpaths:blackstone_running_bond","mcwwindows:stripped_acacia_log_window2","pneumaticcraft:pressure_chamber_valve_x4","aiotbotania:livingrock_pickaxe","cfm:stripped_crimson_bedside_cabinet","mcwbridges:andesite_bridge","mcwwindows:dark_prismarine_pane_window","pneumaticcraft:pressure_chamber_valve_x1","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","minecraft:spruce_wood","occultism:crafting/golden_sacrificial_bowl","aquaculture:sushi","forbidden_arcanus:stellarite_block_from_stellarite_piece","ae2:network/blocks/interfaces_interface_alt","mcwfurnitures:jungle_table","refinedstorage:coloring_recipes/purple_network_receiver","mcwdoors:metal_reinforced_door","ae2:tools/portable_fluid_cell_4k","domum_ornamentum:blue_cobblestone_extra","railways:crafting/smokestack_caboosestyle","additionallanterns:blackstone_chain","pneumaticcraft:programmable_controller","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","ae2:tools/fluix_upgrade_smithing_template","mcwpaths:crimson_planks_path","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","botania:quartz_red","simplemagnets:advanced_demagnetization_coil","cfm:crimson_bedside_cabinet","sophisticatedstorage:generic_limited_barrel_3","pneumaticcraft:logistics_frame_active_provider","sophisticatedstorage:generic_limited_barrel_4","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwroofs:base_upper_lower_roof","croptopia:crema","appbot:mana_storage_cell_1k","dyenamics:mint_stained_glass","supplementaries:redstone_illuminator","bigreactors:turbine/basic/activetap_fe","mcwroofs:gutter_base_lime","deepresonance:tank","refinedstorage:coloring_recipes/cyan_controller","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","productivebees:stonecutter/maple_canvas_hive","mcwroofs:crimson_planks_steep_roof","mcwbiomesoplenty:mahogany_picket_fence","mcwroofs:stone_bricks_upper_lower_roof","twilightforest:equipment/fiery_pickaxe","mcwfurnitures:stripped_jungle_modern_desk","mcwroofs:light_blue_terracotta_top_roof","dyenamics:bed/fluorescent_bed","supplementaries:candle_holders/candle_holder_green_dye","utilitix:crossing_rail","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","advanced_ae:quantumstorage256","handcrafted:jungle_table","forbidden_arcanus:clibano_combustion/arcane_crystal_dust_from_clibano_combusting","minecraft:andesite_wall_from_andesite_stonecutting","supplementaries:planter_rich","create:crafting/kinetics/metal_girder","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","productivebees:stonecutter/dead_canvas_hive","mcwbiomesoplenty:jacaranda_four_window","mcwwindows:magenta_curtain","mcwbridges:iron_bridge_pier","pneumaticcraft:magnet_upgrade","pneumaticcraft:wall_lamp_purple","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","minecraft:redstone","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","mcwroofs:crimson_steep_roof","ae2:tools/portable_fluid_cell_1k","minecraft:powered_rail","productivebees:expansion_boxes/expansion_box_birch_canvas","botania:dye_pink","mcwwindows:diorite_pane_window","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwbridges:jungle_rail_bridge","mcwroofs:gutter_base_white","additionallanterns:stone_lantern","supplementaries:flags/flag_red","enderio:resetting_lever_ten_from_prev","mcwfurnitures:stripped_crimson_modern_chair","mcwroofs:magenta_concrete_upper_lower_roof","sophisticatedstorage:storage_stack_upgrade_tier_5_from_backpack_stack_upgrade_tier_4","reliquary:crimson_cloth","mcwroofs:light_gray_terracotta_steep_roof","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","mcwroofs:purple_terracotta_lower_roof","minecraft:blue_terracotta","supplementaries:stone_lamp","twilightforest:equipment/fiery_ingot_crafting","sophisticatedstorage:void_upgrade","pneumaticcraft:gilded_upgrade","connectedglass:clear_glass_purple2","pneumaticcraft:stone_base","productivebees:stonecutter/comb_canvas_expansion_box","mcwwindows:deepslate_window","minecraft:yellow_concrete_powder","minecraft:gray_terracotta","minecraft:comparator","sophisticatedbackpacks:stack_upgrade_starter_tier","mcwfurnitures:stripped_crimson_lower_bookshelf_drawer","mcwwindows:white_mosaic_glass_pane","botania:conversions/manadiamond_block_deconstruct","minecraft:netherite_boots_smithing","cfm:crimson_blinds","cfm:stripped_spruce_coffee_table","forbidden_arcanus:dark_nether_star","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","twilightforest:equipment/fiery_fiery_chestplate","dyenamics:peach_terracotta","botania:livingrock_wall","allthemodium:unobtainium_vibranium_alloy_block","minecraft:fishing_rod","minecraft:cooked_beef_from_smoking","xnet:connector_yellow_dye","reliquary:alkahestry_altar","delightful:knives/silver_knife","productivebees:stonecutter/snake_block_canvas_expansion_box","advanced_ae:quantumstructure","mcwwindows:dark_oak_window2","mcwdoors:crimson_modern_door","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:yellow_lamp","botania:blue_pavement","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","refinedstorage:coloring_recipes/blue_crafting_monitor","supplementaries:timber_frame","mcwfurnitures:stripped_crimson_counter","aether:diamond_pickaxe_repairing","pneumaticcraft:seismic_sensor","sophisticatedstorage:packing_tape","blue_skies:lunar_bookshelf","enderio:resetting_lever_thirty_from_inv","mcwroofs:cyan_terracotta_top_roof","dankstorage:5_to_6","mcwroofs:brown_terracotta_roof","forbidden_arcanus:dark_rune_from_dark_rune_block","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","ae2:network/cells/fluid_storage_cell_4k_storage","create:crafting/kinetics/vertical_gearbox","productivebees:stonecutter/redwood_canvas_hive","mcwlights:double_street_lamp","minecraft:beacon","mysticalagriculture:tertium_block_uncraft","minecraft:tnt","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwdoors:spruce_stable_door","mcwlights:copper_small_chandelier","aquaculture:nether_star_hook","additionallanterns:quartz_lantern","forbidden_arcanus:tiled_polished_darkstone_bricks_from_polished_darkstone_stonecutting","minecraft:flint_and_steel","ad_astra:white_flag","railways:crafting/smokestack_streamlined","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","allthecompressed:decompress/diamond_block_4x","cfm:spruce_upgraded_gate","mcwtrpdoors:oak_swamp_trapdoor","minecraft:black_dye","mcwfurnitures:crimson_cupboard_counter","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","cfm:stripped_dark_oak_kitchen_drawer","mcwroofs:stone_steep_roof","botania:apothecary_default","farmersdelight:oak_cabinet","allthecompressed:decompress/diamond_block_5x","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","aiotbotania:terra_shovel","mcwfurnitures:spruce_cupboard_counter","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:empyreal_plank_pane_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","minecolonies:fish_n_chips","mcwdoors:oak_bamboo_door","travelersbackpack:hay","travelersbackpack:creeper","pneumaticcraft:wall_lamp_inverted_pink","xnet:advanced_connector_red_dye","mcwroofs:brown_striped_awning","minecraft:brown_concrete_powder","productivebees:expansion_boxes/expansion_box_snake_block_canvas","ae2:shaped/walls/smooth_sky_stone_block","mcwdoors:crimson_barn_glass_door","allthetweaks:atm_star_from_atmstar_block","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:light_gray_roof_block","rftoolsbase:tablet","ae2:network/crafting/256k_cpu_crafting_storage","mcwroofs:magenta_terracotta_lower_roof","megacells:cells/portable/portable_fluid_cell_64m","create:crafting/kinetics/windmill_bearing","railcraft:routing_table_book","ad_astra:vent","refinedstorage:coloring_recipes/light_gray_disk_manipulator","minecraft:crimson_hyphae","create:crafting/kinetics/turntable","utilitarian:fluid_hopper","productivebees:stonecutter/concrete_canvas_expansion_box","railcraft:invar_ingot_crafted_with_ingots","mcwroofs:cyan_terracotta_lower_roof","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","mcwbiomesoplenty:pine_hedge","mcwpaths:blackstone_strewn_rocky_path","twilightforest:equipment/fiery_fiery_boots","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","sophisticatedstorage:storage_link","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","cfm:door_mat","mcwroofs:stone_attic_roof","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","handcrafted:terracotta_plate","supplementaries:stone_tile","mcwroofs:light_blue_terracotta_roof","rftoolsbuilder:blue_shield_template_block","allthemodium:vibranium_gear","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","silentgear:coating_smithing_template","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","minecraft:glass_pane","refinedstorage:coloring_recipes/lime_relay","supplementaries:timber_brace","alltheores:iridium_ingot_from_raw_blasting","mcwtrpdoors:jungle_barn_trapdoor","allthearcanistgear:unobtainium_robes_smithing","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","handcrafted:jungle_nightstand","mcwroofs:gutter_middle_yellow","farmersdelight:netherite_knife_smithing","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","railways:crafting/smokestack_oilburner","securitycraft:sonic_security_system","ae2:network/crafting/cpu_crafting_unit","minecraft:clay","mcwbiomesoplenty:pine_pane_window","ae2:shaped/slabs/smooth_sky_stone_block","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwtrpdoors:spruce_bark_trapdoor","connectedglass:borderless_glass_gray2","connectedglass:borderless_glass_magenta2","ae2additions:components/super/256k","securitycraft:protecto","mcwpaths:andesite_basket_weave_paving","bigreactors:reactor/reinforced/activetap_fe","cfm:red_kitchen_counter","ae2:network/blocks/interfaces_interface_part","mcwlights:copper_chain","dyenamics:cherenkov_terracotta","mcwwindows:cyan_mosaic_glass","refinedstorage:coloring_recipes/brown_controller","mcwlights:magenta_paper_lamp","create:crafting/kinetics/cogwheel","ae2additions:blocks/wireless_transceiver","mcwroofs:pink_concrete_steep_roof","cfm:crimson_upgraded_fence","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","mcwfurnitures:stripped_spruce_cupboard_counter","cfm:orange_kitchen_drawer","ae2:materials/annihilationcore","croptopia:chicken_and_noodles","ae2:network/cables/dense_smart_from_smart","mcwdoors:crimson_paper_door","silentgear:stone_rod","ae2:network/cables/dense_covered_green","minecraft:leather_boots","minecraft:netherite_ingot_from_netherite_block","railcraft:steel_spike_maul","refinedstorage:coloring_recipes/gray_fluid_grid","pneumaticcraft:classify_filter","create:crafting/logistics/andesite_tunnel","immersiveengineering:crafting/stick_steel","wirelesschargers:basic_wireless_block_charger","productivebees:stonecutter/birch_canvas_expansion_box","mcwwindows:gray_mosaic_glass","mcwfurnitures:stripped_crimson_large_drawer","megacells:network/mega_pattern_provider","mcwfurnitures:stripped_crimson_double_wardrobe","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwroofs:black_upper_steep_roof","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","dyenamics:persimmon_terracotta","alltheores:uranium_plate","mcwlights:pink_paper_lamp","pneumaticcraft:wall_lamp_inverted_light_blue","ae2:network/crystal_resonance_generator","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","dyenamics:bubblegum_terracotta","mcwwindows:jungle_plank_window","utilitix:hand_bell","mcwbiomesoplenty:palm_window2","minecraft:baked_potato_from_smoking","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","cfm:stripped_crimson_kitchen_counter","aether:iron_gloves","mcwroofs:light_gray_terracotta_upper_lower_roof","ad_astra:steel_plating","minecraft:chiseled_stone_bricks","mcwroofs:stone_upper_lower_roof","tombstone:white_marble","bigreactors:turbine/basic/bearing","expatternprovider:ebus_out","minecraft:polished_blackstone_brick_wall_from_blackstone_stonecutting","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","occultism:crafting/dictionary_of_spirits","productivebees:stonecutter/palm_canvas_hive","alltheores:iron_rod","mcwroofs:cyan_striped_awning","mcwfences:railing_diorite_wall","dyenamics:dye_mint_carpet","caupona:mud_kitchen_stove","pneumaticcraft:stomp_upgrade","minecraft:magma_block","minecraft:purple_terracotta","enderio:resetting_lever_thirty_inv","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","forbidden_arcanus:lens_of_veritatis","xnet:advanced_connector_routing","domum_ornamentum:magenta_floating_carpet","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","productivebees:stonecutter/jungle_canvas_expansion_box","littlelogistics:transmitter_component","aether:book_of_lore","minecraft:brush","securitycraft:secret_spruce_hanging_sign","mcwroofs:jungle_planks_roof","railcraft:steel_pickaxe","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:vibranium_mage_boots_smithing","sfm:cable","minecraft:hay_block","twilightforest:equipment/fiery_fiery_leggings","mcwroofs:deepslate_upper_steep_roof","computercraft:turtle_advanced_overlays/turtle_rainbow_overlay","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:white_upper_steep_roof","delightful:knives/invar_knife","pneumaticcraft:display_shelf","mcwfences:birch_curved_gate","productivebees:stonecutter/birch_canvas_hive","mcwbiomesoplenty:mahogany_window2","minecraft:end_stone_bricks_from_end_stone_stonecutting","mcwwindows:red_sandstone_window2","pneumaticcraft:compressed_iron_boots","supplementaries:ash_brick","mcwbiomesoplenty:palm_pyramid_gate","railways:crafting/smokestack_coalburner","mcwfences:modern_granite_wall","create:jungle_window","productivebees:stonecutter/grimwood_canvas_hive","pneumaticcraft:etching_tank","ae2:network/parts/monitors_storage","chemlib:calcium_ingot_from_smelting_calcium_dust","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","cfm:crimson_kitchen_counter","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwdoors:oak_tropical_door","evilcraft:smelting/hardened_blood_shard","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","littlelogistics:locomotive_dock_rail","pneumaticcraft:reinforced_chest","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","pneumaticcraft:assembly_controller","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","refinedstorage:coloring_recipes/light_gray_network_receiver","bigreactors:blasting/graphite_from_charcoal","refinedstorage:coloring_recipes/pink_wireless_transmitter","mythicbotany:alfsteel_pylon","create:crafting/kinetics/rotation_speed_controller","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","modularrouters:detector_module","mcwwindows:bricks_window","littlelogistics:fluid_hopper","twigs:paper_lantern","railcraft:goggles","handcrafted:quartz_pillar_trim","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","modularrouters:activator_module","travelersbackpack:iron","modularrouters:augment_core","refinedstorage:coloring_recipes/green_grid","botania:petal_lime","ae2:tools/certus_quartz_wrench","ae2:network/cables/dense_smart_purple","minecraft:blaze_powder","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","minecraft:chain","dyenamics:rose_concrete_powder","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","mcwwindows:blackstone_brick_arrow_slit","alltheores:osmium_rod","pneumaticcraft:liquid_compressor","mcwfences:crimson_highley_gate","ad_astra:reinforced_door","refinedstorage:coloring_recipes/black_relay","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","mcwroofs:crimson_planks_attic_roof","ae2:decorative/sky_stone_small_brick_from_stonecutting","mcwroofs:black_terracotta_upper_lower_roof","simplylight:illuminant_black_block_on_toggle","mcwfurnitures:crimson_end_table","mcwwindows:acacia_plank_window2","enderio:basic_item_filter","mcwroofs:black_concrete_top_roof","mcwwindows:jungle_plank_pane_window","ae2:network/blocks/inscribers","mcwwindows:cherry_four_window","immersiveengineering:crafting/fertilizer","mcwroofs:magenta_terracotta_upper_steep_roof","allthecompressed:compress/dirt_7x","constructionwand:diamond_wand","mcwroofs:gray_concrete_roof","mcwwindows:warped_stem_four_window","securitycraft:sentry","botania:tornado_rod","mcwfurnitures:crimson_drawer_counter","minecraft:netherite_block","immersiveengineering:crafting/blueprint_bullets","alltheores:uranium_rod","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","supplementaries:dispenser_minecart","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwroofs:stone_roof","mcwwindows:blue_mosaic_glass_pane","handcrafted:crimson_drawer","minecolonies:meat_ravioli","travelersbackpack:wither","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","mcwbiomesoplenty:maple_wired_fence","mcwfurnitures:crimson_desk","refinedstorage:coloring_recipes/purple_disk_manipulator","refinedstorage:coloring_recipes/cyan_detector","mcwroofs:pink_terracotta_top_roof","create:crafting/logistics/redstone_contact","cfm:green_kitchen_counter","ae2:network/cables/dense_covered_gray","minecraft:crossbow","pneumaticcraft:coordinate_tracker_upgrade","minecraft:calibrated_sculk_sensor","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","occultism:crafting/otherstone_frame","mcwbiomesoplenty:stripped_willow_log_window2","refinedstorage:coloring_recipes/white_pattern_grid","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","securitycraft:secret_warped_hanging_sign","ae2:tools/certus_quartz_spade","utilitarian:utility/spruce_logs_to_slabs","minecraft:diamond","mcwroofs:gray_upper_lower_roof","mcwbiomesoplenty:willow_wired_fence","connectedglass:clear_glass_blue2","minecraft:crimson_slab","megacells:cells/portable/portable_fluid_cell_256m","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","twilightforest:equipment/fortification_scepter","twilightforest:carminite_builder","mcwroofs:black_concrete_upper_lower_roof","ae2:network/cables/dense_covered_black","mcwroofs:gray_concrete_upper_lower_roof","ae2:network/blocks/controller","mcwwindows:orange_mosaic_glass_pane","expatternprovider:epp_alt","occultism:crafting/spirit_campfire","utilitix:piston_controller_rail","croptopia:roasted_smoking","mcwroofs:bricks_upper_lower_roof","mcwbiomesoplenty:hellbark_curved_gate","itemcollectors:advanced_collector","minecraft:green_terracotta","minecraft:white_stained_glass","connectedglass:clear_glass_light_gray2","securitycraft:secret_warped_sign_item","xnet:advanced_connector_green_dye","securitycraft:reinforced_granite","refinedstorage:coloring_recipes/yellow_controller","securitycraft:taser","allthemodium:vibranium_nugget_from_ingot","ae2:network/cells/fluid_storage_cell_64k","mcwroofs:stone_bricks_lower_roof","supplementaries:soap","refinedstorage:coloring_recipes/crafting_monitor","pneumaticcraft:heat_frame","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","ae2:tools/paintballs_pink","minecraft:purpur_stairs","aiotbotania:livingrock_hoe","rftoolsutility:redstone_information","supplementaries:gold_door","blue_skies:dusk_bookshelf","botania:quartz_dark","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","ae2:misc/fluixpearl","ae2:network/cables/glass_light_gray","handcrafted:crimson_fancy_bed","mcwfences:acacia_horse_fence","immersiveengineering:crafting/coke_to_coal_coke","ad_astra:fan","immersiveengineering:crafting/armor_steel_boots","mcwfences:spruce_hedge","twigs:mixed_bricks_stonecutting","mcwfences:jungle_hedge","reliquary:angelic_feather","ae2:network/parts/toggle_bus","mcwfurnitures:stripped_crimson_modern_desk","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","create:crafting/kinetics/large_cogwheel_from_little","utilitix:linked_crystal","refinedstorage:coloring_recipes/yellow_relay","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","occultism:crafting/book_of_calling_foliot_lumberjack","twigs:jungle_table","twilightforest:carminite_reactor","immersiveengineering:crafting/clinker_brick_quoin","domum_ornamentum:pink_floating_carpet","croptopia:campfire_caramel","botania:virus_nullodermal","securitycraft:reinforced_andesite","supplementaries:altimeter","ae2:network/cables/dense_smart_gray","pneumaticcraft:assembly_program_drill_laser","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","supplementaries:end_stone_lamp","mcwfurnitures:oak_triple_drawer","allthecompressed:compress/stone_1x","mcwroofs:gutter_middle_green","refinedstorage:coloring_recipes/yellow_security_manager","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","minecraft:quartz_stairs","botania:manasteel_shovel","dyenamics:bed/spring_green_bed","ae2:network/parts/terminals_pattern_encoding","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","bigreactors:reactor/basic/controller_ingots_uranium","mcwroofs:white_steep_roof","mcwpaths:cobbled_deepslate_crystal_floor_slab","silentgear:leather_from_scraps","minecraft:purpur_slab","pneumaticcraft:wall_lamp_inverted_red","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","minecraft:chiseled_polished_blackstone_from_blackstone_stonecutting","twigs:stone_column_stonecutting","mcwlights:jungle_tiki_torch","cfm:lime_picket_gate","mcwpaths:blackstone_crystal_floor","blue_skies:bluebright_spear","travelersbackpack:quartz","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","sophisticatedbackpacks:tool_swapper_upgrade","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","bigreactors:energizer/controller","mcwbiomesoplenty:yellow_maple_hedge","arseng:portable_source_cell_4k","mcwbiomesoplenty:stripped_maple_log_four_window","delightful:food/marshmallow_stick","botania:super_travel_belt","minecraft:lime_terracotta","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","securitycraft:display_case","minecraft:blackstone_slab_from_blackstone_stonecutting","minecraft:jungle_wood","handcrafted:bench","paraglider:cosmetic/kakariko_goddess_statue","allthecompressed:compress/andesite_1x","mcwwindows:diorite_four_window","minecraft:stone_slab","mcwfurnitures:stripped_spruce_desk","pneumaticcraft:compressed_bricks","mcwpaths:brick_flagstone","productivebees:stonecutter/comb_canvas_hive","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","ae2:tools/nether_quartz_sword","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","minecraft:brick_stairs_from_bricks_stonecutting","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","minecraft:jungle_stairs","travelersbackpack:backpack_tank","refinedstorage:coloring_recipes/blue_disk_manipulator","minecraft:blast_furnace","minecraft:polished_andesite_stairs","additionallanterns:smooth_stone_lantern","pneumaticcraft:reinforced_stone_from_slab","mcwbridges:glass_bridge","refinedstorage:coloring_recipes/black_crafter_manager","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","enderio:resetting_lever_ten_inv_from_base","refinedstorage:coloring_recipes/purple_crafter","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:mud_brick_gothic","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","ae2:network/parts/import_bus","croptopia:campfire_molasses","mcwtrpdoors:crimson_tropical_trapdoor","ae2:network/crafting/16k_cpu_crafting_storage","expatternprovider:crystal_fixer","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","ae2:network/upgrade_wireless_crafting_terminal","occultism:crafting/lens_frame","mcwwindows:stripped_birch_log_four_window","pneumaticcraft:wall_lamp_green","mcwroofs:thatch_upper_lower_roof","pneumaticcraft:assembly_drill","additionallanterns:netherite_chain","railcraft:water_tank_siding","refinedstorage:coloring_recipes/green_crafter","pneumaticcraft:wall_lamp_inverted_light_gray","cfm:jungle_kitchen_sink_dark","mcwpaths:brick_windmill_weave","utilitix:weak_redstone_torch","mcwroofs:blackstone_roof","handcrafted:blackstone_corner_trim","silentgear:upgrade_base","securitycraft:secret_cherry_hanging_sign","refinedstorage:coloring_recipes/crafting_grid","ae2:network/cables/glass_pink","xnet:advanced_connector_green","mcwwindows:crimson_stem_four_window","ae2:network/cables/smart_cyan","mcwroofs:black_concrete_steep_roof","expatternprovider:wireless_tool","minecraft:cobbled_deepslate_stairs","handcrafted:pufferfish_trophy","minecraft:baked_potato","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","mcwtrpdoors:crimson_ranch_trapdoor","pneumaticcraft:compressed_stone_slab","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwroofs:light_gray_lower_roof","mcwlights:framed_torch","dyenamics:bed/peach_bed","minecraft:spyglass","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwroofs:bricks_top_roof","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","botania:starfield","minecraft:golden_leggings","mcwroofs:gutter_middle_cyan","cfm:mangrove_mail_box","sophisticatedstorage:smelting_upgrade","mcwdoors:spruce_waffle_door","travelersbackpack:ghast","simplylight:illuminant_purple_block_on_toggle","mcwfences:modern_mud_brick_wall","pneumaticcraft:empty_pcb_from_failed_pcb","minecraft:stone_pressure_plate","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","trashcans:ultimate_trash_can","connectedglass:borderless_glass_orange2","botania:yellow_pavement","ad_astra:fuel_refinery","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","everythingcopper:copper_helmet","create:crafting/kinetics/yellow_seat","pneumaticcraft:logistics_core","mcwroofs:red_concrete_upper_lower_roof","mcwpaths:andesite_windmill_weave_path","mcwdoors:spruce_swamp_door","modularrouters:blank_upgrade","mcwdoors:spruce_western_door","dyenamics:banner/wine_banner","mcwbiomesoplenty:jacaranda_pane_window","travelersbackpack:white_sleeping_bag","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","ae2:shaped/walls/sky_stone_block","botania:horn_leaves","mcwfurnitures:crimson_counter","mcwfences:guardian_metal_fence","ae2:tools/fluix_pickaxe","croptopia:cheeseburger","cfm:stripped_spruce_cabinet","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","minecraft:bread","mcwroofs:green_terracotta_steep_roof","mcwdoors:spruce_barn_glass_door","mcwdoors:garage_silver_door","comforts:hammock_white","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","ae2:tools/portable_item_cell_64k","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","mcwfurnitures:stripped_oak_cupboard_counter","alltheores:osmium_plate","minecraft:lever","mcwwindows:stripped_warped_stem_four_window","mcwfurnitures:spruce_coffee_table","immersiveengineering:crafting/wirecoil_structure_steel","mcwwindows:dark_oak_curtain_rod","botania:water_rod","botania:horn_grass","securitycraft:secret_cherry_sign_item","minecraft:end_stone_brick_wall_from_end_stone_stonecutting","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:white_concrete_steep_roof","additionallanterns:red_nether_bricks_lantern","mcwroofs:gray_concrete_lower_roof","botania:spectral_platform","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","refinedstorage:coloring_recipes/light_gray_grid","dyenamics:peach_wool","undergarden:smoke_gloomper_leg","forbidden_arcanus:clibano_combustion/emerald_from_clibano_combusting","mcwroofs:yellow_terracotta_attic_roof","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","refinedstorage:coloring_recipes/yellow_crafter_manager","refinedstorage:coloring_recipes/cyan_network_transmitter","supplementaries:sign_post_crimson","minecraft:iron_shovel","tombstone:dark_marble","sophisticatedstorage:paintbrush","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","pneumaticcraft:display_table","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","chimes:amethyst_chimes","mcwwindows:stone_brick_arrow_slit","ae2:smelting/smooth_sky_stone_block","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","productivebees:stonecutter/willow_canvas_hive","twilightforest:iron_ladder","mcwroofs:jungle_planks_lower_roof","croptopia:ham_sandwich","securitycraft:track_mine","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","ad_astra:space_suit","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwfurnitures:oak_covered_desk","pneumaticcraft:camo_applicator","cfm:light_blue_picket_fence","domum_ornamentum:blockbarreldeco_standing","ad_astra:rocket_fin","botania:livingwood_planks","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","supplementaries:gold_trapdoor","cfm:fridge_light","forbidden_arcanus:deorum_block_from_deorum_ingot","mcwwindows:stripped_spruce_log_window2","mcwroofs:andesite_upper_lower_roof","mcwfurnitures:stripped_crimson_double_drawer","minecraft:mossy_stone_brick_stairs","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","rftoolsutility:counter_module","cfm:stripped_jungle_crate","minecraft:amethyst_block","biomesoplenty:jacaranda_boat","ae2:decorative/sky_stone_brick","mcwroofs:red_concrete_attic_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","allthemodium:vibranium_block","mcwlights:cyan_paper_lamp","ae2:network/cells/item_storage_cell_256k","mcwroofs:orange_terracotta_roof","minecraft:nether_brick","mcwfurnitures:stripped_spruce_covered_desk","pneumaticcraft:logistics_frame_passive_provider_self","mcwdoors:jungle_waffle_door","mcwroofs:black_roof_block","mcwbiomesoplenty:hellbark_pane_window","create:crafting/kinetics/wrench","bloodmagic:blood_rune_self_sac_2","advancedperipherals:peripheral_casing","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","botania:prism","twilightforest:charm_of_keeping_2","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","modularrouters:sender_module_2_x4","sophisticatedbackpacks:smelting_upgrade","sophisticatedstorage:crimson_barrel","reliquary:fertile_lily_pad","mcwfurnitures:stripped_oak_end_table","ae2:network/cables/dense_smart_white","minecraft:allthemodium_mage_leggings_smithing","mcwroofs:red_striped_awning","additionallanterns:crimson_chain","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","cfm:spruce_kitchen_drawer","mcwroofs:lime_concrete_upper_lower_roof","modularrouters:dropper_module","twilightforest:lich_banner_pattern","mcwbiomesoplenty:magic_highley_gate","minecraft:dark_prismarine","ae2:tools/fluix_sword","botania:balance_cloak","simplemagnets:basic_demagnetization_coil","dyenamics:mint_terracotta","mcwwindows:pink_curtain","mcwroofs:yellow_concrete_upper_lower_roof","mcwlights:soul_jungle_tiki_torch","mcwwindows:light_gray_curtain","productivebees:stonecutter/rosewood_canvas_expansion_box","securitycraft:limited_use_keycard","ae2:network/cables/glass_magenta","delightful:knives/refined_obsidian_knife","expatternprovider:ei_alt","supplementaries:candle_holders/candle_holder","supplementaries:netherite_door","refinedstorage:coloring_recipes/blue_network_receiver","twigs:cobblestone_bricks_stonecutting","forbidden_arcanus:deorum_ingot_from_deorum_nugget","cfm:light_gray_kitchen_counter","minecraft:candle","aether:diamond_helmet_repairing","twilightforest:knight_phantom_banner_pattern","pneumaticcraft:security_station","framedblocks:framed_cube","minecraft:polished_blackstone_brick_slab_from_polished_blackstone_stonecutting","immersiveengineering:crafting/connector_structural","mcwbiomesoplenty:flowering_oak_hedge","ae2:smelting/silicon_from_certus_quartz_dust","pneumaticcraft:compressed_iron_block_from_ingot","expatternprovider:assembler_matrix_glass","mcwpaths:andesite_flagstone_slab","pneumaticcraft:wall_lamp_black","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","ae2:network/blocks/spatial_io_port","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","refinedstorage:coloring_recipes/brown_relay","ae2:network/cables/glass_fluix_clean","mcwfences:oak_curved_gate","supplementaries:wrench","mcwroofs:blue_terracotta_upper_steep_roof","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","minecraft:bow","dyenamics:lavender_wool","aether:skyroot_smithing_table","refinedstorage:coloring_recipes/blue_fluid_grid","aquaculture:brown_mushroom_from_fish","mcwfurnitures:crimson_large_drawer","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","chemlib:periodic_table","minecraft:andesite_stairs","expatternprovider:wireless_ex_pat","botania:super_lava_pendant","forbidden_arcanus:deorum_nugget_from_deorum_ingot","refinedstorage:coloring_recipes/purple_crafting_monitor","bigreactors:energizer/chargingport_fe","handcrafted:bear_trophy","mcwbiomesoplenty:stripped_redwood_log_four_window","mcwfurnitures:stripped_crimson_striped_chair","cfm:purple_kitchen_sink","mcwpaths:blackstone_flagstone_stairs","cfm:black_cooler","minecraft:jungle_slab","refinedstorage:coloring_recipes/brown_crafter","supplementaries:sconce","silentgear:rough_rod","ae2:network/parts/storage_bus","minecraft:unobtainium_spell_book_smithing","minecraft:skull_banner_pattern","botania:spark","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","occultism:crafting/sacrificial_bowl","railcraft:iron_tunnel_bore_head","mcwfurnitures:spruce_glass_table","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","minecraft:mossy_stone_brick_wall","connectedglass:borderless_glass_brown2","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","forbidden_arcanus:deorum_door","mcwbiomesoplenty:jacaranda_plank_window2","sophisticatedbackpacks:feeding_upgrade","minecraft:granite","botania:mana_mirror","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","mcwfurnitures:spruce_covered_desk","botania:ghost_rail","minecraft:firework_rocket_simple","sophisticatedstorage:smoking_upgrade","minecraft:black_dye_from_wither_rose","allthemodium:unobtainium_block","mcwwindows:stripped_spruce_log_four_window","refinedstorage:coloring_recipes/network_receiver","mcwroofs:blackstone_upper_lower_roof","immersiveengineering:crafting/coil_hv","mcwdoors:spruce_mystic_door","sophisticatedstorage:backpack_advanced_void_upgrade_from_storage_advanced_void_upgrade","mcwtrpdoors:spruce_beach_trapdoor","enderio:enderios","pneumaticcraft:wall_lamp_inverted_orange","mcwfurnitures:oak_coffee_table","pneumaticcraft:wall_lamp_inverted_brown","mcwroofs:andesite_roof","botania:black_pavement","forbidden_arcanus:blasting/arcane_crystal_dust_from_blasting","supplementaries:pedestal","travelersbackpack:blaze","minecraft:allthemodium_mage_helmet_smithing","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","botania:quartz_blaze","ae2:network/cables/glass_yellow","ae2:shaped/stairs/sky_stone_block","dyenamics:bubblegum_wool","mcwfences:andesite_grass_topped_wall","immersiveengineering:crafting/drillhead_steel","aiotbotania:livingrock_shears","dyenamics:rose_terracotta","pneumaticcraft:pressure_chamber_glass","forbidden_arcanus:wooden_blacksmith_gavel","twigs:crimson_table","allthemodium:smithing/allthemodium_pickaxe_smithing","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","alltheores:signalum_plate","mcwdoors:jungle_tropical_door","expatternprovider:oversize_interface","twigs:lamp","minecraft:stone","mcwwindows:orange_curtain","dyenamics:bed/lavender_bed","mcwroofs:spruce_upper_steep_roof","refinedstorage:coloring_recipes/magenta_grid","minecraft:blackstone_stairs","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","sophisticatedstorage:storage_feeding_upgrade_from_backpack_feeding_upgrade","dyenamics:icy_blue_wool","forbidden_arcanus:deorum_soul_lantern","securitycraft:speed_module","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","botania:mana_ring_greater","twilightforest:equipment/fiery_chestplate","blue_skies:glowing_poison_stone","mcwfurnitures:jungle_triple_drawer","refinedstorage:coloring_recipes/brown_detector","tombstone:carmin_marble","create:crafting/appliances/tree_fertilizer","mcwroofs:brown_terracotta_steep_roof","mcwdoors:oak_barn_door","create:andesite_bars_from_andesite_alloy_stonecutting","botania:alchemy_catalyst","refinedstorage:coloring_recipes/blue_pattern_grid","allthemodium:ancient_stone_bricks","mcwfurnitures:spruce_double_drawer_counter","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","mcwfurnitures:jungle_lower_bookshelf_drawer","rftoolsutility:text_module","dyenamics:dye_bubblegum_carpet","botania:redstone_root","ae2:tools/paintballs_brown","mcwwindows:stripped_jungle_log_window2","create:crafting/kinetics/display_board","mcwlights:soul_warped_tiki_torch","connectedglass:clear_glass_black2","dyenamics:dye_aquamarine_carpet","refinedstorage:coloring_recipes/light_gray_crafting_monitor","minecraft:cobbled_deepslate_wall","minecraft:purpur_slab_from_purpur_block_stonecutting","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","botania:lava_pendant","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","reliquary:fertile_essence","supplementaries:candle_holders/candle_holder_black","forbidden_arcanus:smelting/arcane_crystal_from_smelting","simplylight:illuminant_orange_block_on_dyed","ad_astra:oxygen_gear","minecraft:smooth_stone","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","ae2:tools/certus_quartz_sword","ae2:network/cables/dense_covered_orange","cfm:oak_bedside_cabinet","ae2:network/cables/dense_smart_light_gray","minecraft:iron_ingot_from_blasting_raw_iron","mcwfurnitures:oak_bookshelf_drawer","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","cfm:oak_park_bench","productivebees:stonecutter/hellbark_canvas_expansion_box","mcwroofs:cyan_concrete_attic_roof","immersiveengineering:crafting/conveyor_basic","mcwroofs:light_gray_terracotta_top_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","productivebees:stonecutter/river_canvas_hive","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","sophisticatedstorage:chipped/mason_table_upgrade","cfm:pink_kitchen_drawer","mcwroofs:white_concrete_attic_roof","cfm:crimson_desk","blue_skies:frostbright_spear","twilightdelight:torchberries_crate","everythingcopper:copper_pickaxe","littlelogistics:tug","mcwroofs:blue_concrete_lower_roof","botania:hourglass","pneumaticcraft:reinforced_brick_slab","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","reliquary:uncrafting/ender_pearl","mcwfurnitures:stripped_spruce_coffee_table","minecraft:ender_eye","additionallanterns:obsidian_chain","supplementaries:blackstone_lamp","mcwfurnitures:spruce_double_wardrobe","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwpaths:brick_strewn_rocky_path","mcwbiomesoplenty:umbran_plank_window","enderio:resetting_lever_three_hundred","refinedstorage:coloring_recipes/disk_manipulator","mcwlights:cross_lantern","mcwlights:brown_lamp","productivebees:stonecutter/mangrove_canvas_expansion_box","mcwwindows:bricks_four_window","railcraft:receiver_circuit","minecraft:quartz_bricks_from_quartz_block_stonecutting","securitycraft:reinforced_packed_mud","minecraft:green_candle","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","bigreactors:fluidizer/fluidinjector","silentgear:blaze_gold_ingot","rftoolsbase:manual","enderio:wood_gear_corner","pneumaticcraft:refinery_output","dyenamics:bed/icy_blue_bed","sophisticatedstorage:feeding_upgrade","ae2:network/parts/terminals_pattern_access","additionallanterns:diamond_chain","immersiveengineering:crafting/connector_hv_relay","deepresonance:radiation_suit_leggings","ae2:network/blocks/io_condenser","ae2:block_cutter/walls/quartz_wall","sophisticatedbackpacks:smoking_upgrade","supplementaries:stonecutting/stone_tile","railcraft:steel_tank_wall","minecraft:cooked_mutton_from_campfire_cooking","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","botania:mana_void","dyenamics:conifer_concrete_powder","mcwpaths:brick_clover_paving","securitycraft:trophy_system","ad_astra:small_green_industrial_lamp","pneumaticcraft:reinforced_brick_from_slab","minecraft:cooked_beef","mcwwindows:birch_plank_window2","mcwroofs:brown_terracotta_attic_roof","railcraft:steel_hoe","mcwbiomesoplenty:pine_curved_gate","cfm:green_trampoline","immersiveengineering:crafting/coil_mv","minecraft:cooked_mutton_from_smoking","supplementaries:flags/flag_gray","mcwdoors:oak_mystic_door","mcwtrpdoors:crimson_bark_trapdoor","mcwlights:yellow_paper_lamp","twilightdelight:torchberry_cookie","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","advanced_ae:quantumstorage128","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","refinedstorage:coloring_recipes/light_gray_controller","bloodmagic:blood_rune_displacement_2","securitycraft:keycard_lv4","securitycraft:keycard_lv3","cfm:crimson_table","securitycraft:keycard_lv2","cfm:stripped_jungle_kitchen_drawer","securitycraft:keycard_lv1","railcraft:age_detector","pneumaticcraft:wall_lamp_orange","minecraft:gold_nugget_from_smelting","mcwwindows:black_curtain","dyenamics:wine_stained_glass","minecraft:chiseled_quartz_block_from_quartz_block_stonecutting","productivebees:stonecutter/wisteria_canvas_expansion_box","mcwpaths:cobbled_deepslate_crystal_floor_stairs","botania:pixie_ring","blue_skies:starlit_spear","mcwfurnitures:stripped_oak_bookshelf_drawer","immersiveengineering:crafting/capacitor_mv","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","refinedstorage:coloring_recipes/grid","botania:drum_gathering","refinedstorage:coloring_recipes/orange_network_transmitter","mcwwindows:jungle_plank_window2","ad_astra:space_pants","securitycraft:reinforced_purple_stained_glass_pane_from_glass","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwpaths:andesite_clover_paving","ae2:network/cables/dense_smart_black","computercraft:turtle_advanced_overlays/turtle_trans_overlay","occultism:smelting/iesnium_ingot_from_raw","refinedstorage:coloring_recipes/lime_fluid_grid","cfm:magenta_grill","mcwroofs:purple_terracotta_upper_steep_roof","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","mcwroofs:pink_striped_awning","refinedstorage:coloring_recipes/red_crafting_grid","productivebees:hives/advanced_dark_oak_canvas_hive","mcwdoors:crimson_japanese_door","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","enderio:resetting_lever_five_inv","mcwfurnitures:crimson_drawer","mcwdoors:garage_white_door","ae2:decorative/cut_quartz_block_from_stonecutting","utilitix:armed_stand","botania:lexicon","botania:ice_pendant","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","bigreactors:turbine/ridiculite_block","ae2:network/cables/smart_blue","minecraft:end_stone_brick_stairs_from_end_stone_stonecutting","mcwroofs:gutter_middle_white","dyenamics:maroon_wool","immersiveengineering:crafting/plate_gold_hammering","appmek:portable_chemical_storage_cell_64k","botania:turntable","ae2:network/cells/item_storage_components_cell_1k_part","mcwfurnitures:jungle_lower_triple_drawer","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","refinedstorage:coloring_recipes/orange_relay","ad_astra:steel_tank","minecraft:netherite_scrap","supplementaries:daub","railcraft:steel_chestplate","mcwfurnitures:stripped_jungle_end_table","create:crafting/kinetics/wooden_bracket","productivebees:stonecutter/acacia_canvas_hive","securitycraft:secret_oak_hanging_sign","mcwfences:oak_hedge","mcwdoors:spruce_japanese2_door","enderio:fluid_tank","arseng:portable_source_cell_1k","botania:petal_white","ae2:decorative/quartz_block","ae2:network/cables/dense_smart_orange","bigreactors:energizer/casing","mcwpaths:stone_flagstone","cfm:crimson_park_bench","mcwpaths:blackstone_honeycomb_paving","simplylight:illuminant_block_toggle","xnet:connector_blue","ae2:block_cutter/slabs/smooth_sky_stone_slab","mcwfurnitures:crimson_bookshelf_drawer","mcwbiomesoplenty:hellbark_highley_gate","create:crafting/logistics/redstone_link","minecraft:enchanting_table","mcwpaths:cobbled_deepslate_square_paving","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","mcwroofs:blue_terracotta_lower_roof","deepresonance:radiation_suit_chestplate","refinedstorage:coloring_recipes/lime_disk_manipulator","refinedstorage:coloring_recipes/pink_disk_manipulator","mcwlights:brown_paper_lamp","minecraft:campfire","allthearcanistgear:unobtainium_boots_smithing","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","minecraft:brick_stairs","mcwbiomesoplenty:maple_highley_gate","refinedstorage:coloring_recipes/pink_network_transmitter","megacells:cells/portable/portable_item_cell_256m","farmersdelight:cooking/chicken_soup","blue_skies:bluebright_bookshelf","aether:skyroot_note_block","pneumaticcraft:reinforced_brick_wall_from_bricks_stonecutting","pneumaticcraft:logistics_configurator","mysticalagriculture:imperium_block_uncraft","pneumaticcraft:logistics_frame_default_storage_self","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","sophisticatedbackpacks:chipped/mason_table_upgrade","productivetrees:sawdust_to_paper","cfm:crimson_upgraded_gate","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:9956,warning_level:0}}