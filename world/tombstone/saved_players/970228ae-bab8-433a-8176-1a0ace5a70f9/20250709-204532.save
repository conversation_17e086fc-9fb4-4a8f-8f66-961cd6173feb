{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"}],Air:300s,Attributes:[{Base:0.0d,Name:"minecraft:generic.luck"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:4.0d,Modifiers:[{Amount:0.4d,Name:"artifacts:feral_claws_attack_speed_bonus",Operation:0,UUID:[I;-**********,-**********,-**********,**********]}],Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"artifacts:steadfast_spikes_knockback_resistance",Operation:0,UUID:[I;-706276632,**********,-**********,**********]}],Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Modifiers:[{Amount:4.0d,Name:"artifacts:power_glove_attack_damage_bonus",Operation:0,UUID:[I;308939635,-**********,-**********,-702737548]}],Name:"minecraft:generic.attack_damage"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:1.0d,Name:"attributeslib:mining_speed"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:obsidian_skull"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{CachedModifiers:[],Cosmetics:{Items:[],Size:32},DropRule:"DEFAULT",HasCosmetic:0b,PersistentModifiers:[{Amount:30.0d,Name:"legacy",Operation:0,UUID:[I;185510845,1109413535,-1157942256,228769150]}],RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1},{Render:0b,Slot:2},{Render:0b,Slot:3},{Render:0b,Slot:4},{Render:0b,Slot:5},{Render:0b,Slot:6},{Render:0b,Slot:7},{Render:0b,Slot:8},{Render:0b,Slot:9},{Render:0b,Slot:10},{Render:0b,Slot:11},{Render:0b,Slot:12},{Render:0b,Slot:13},{Render:0b,Slot:14},{Render:0b,Slot:15},{Render:0b,Slot:16},{Render:0b,Slot:17},{Render:0b,Slot:18},{Render:0b,Slot:19},{Render:0b,Slot:20},{Render:0b,Slot:21},{Render:0b,Slot:22},{Render:0b,Slot:23},{Render:0b,Slot:24},{Render:0b,Slot:25},{Render:0b,Slot:26},{Render:0b,Slot:27},{Render:0b,Slot:28},{Render:0b,Slot:29},{Render:0b,Slot:30},{Render:0b,Slot:31}],Size:32},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:night_vision_goggles"},{Count:1b,Slot:1,id:"artifacts:plastic_drinking_hat"},{Count:1b,Slot:2,id:"artifacts:novelty_drinking_hat"},{Count:1b,Slot:3,id:"artifacts:superstitious_hat"},{Count:1b,Slot:4,id:"artifacts:anglers_hat"},{Count:1b,Slot:5,id:"artifacts:lucky_scarf"},{Count:1b,Slot:6,id:"artifacts:cross_necklace",tag:{CanApplyBonus:1b}},{Count:1b,Slot:7,id:"artifacts:antidote_vessel"},{Count:1b,Slot:8,id:"artifacts:digging_claws"},{Count:1b,Slot:9,id:"artifacts:feral_claws"},{Count:1b,Slot:10,id:"artifacts:power_glove"},{Count:1b,Slot:11,id:"artifacts:vampiric_glove"},{Count:1b,Slot:12,id:"artifacts:golden_hook"},{Count:1b,Slot:13,id:"artifacts:pickaxe_heater"},{Count:1b,Slot:14,id:"artifacts:steadfast_spikes"}],Size:32},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1},{Render:0b,Slot:2},{Render:0b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:netherite_backpack",tag:{contentsUuid:[I;390048106,-673756075,-1938614503,949916074],inventorySlots:120,renderInfo:{upgradeItems:[{Count:1b,id:"sophisticatedbackpacks:crafting_upgrade"},{Count:1b,id:"sophisticatedbackpacks:advanced_feeding_upgrade",tag:{feedAtHungerLevel:"any",filters:{Items:[{Count:1b,Slot:0,id:"artifacts:eternal_steak"},{Count:1b,Slot:1,id:"artifacts:everlasting_beef"}],Size:16},isAllowList:1b}},{Count:1b,id:"sophisticatedbackpacks:advanced_magnet_upgrade",tag:{filterByStorage:1b}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_omega_tier"}]},upgradeSlots:7}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:77806},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:0b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:15},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"apotheosis:socketing",ItemStack:{Count:1b,id:"allthemodium:alloy_paxel",tag:{Enchantments:[{id:"apotheosis:miners_fervor",lvl:5s},{id:"minecraft:fortune",lvl:8s},{id:"evilcraft:life_stealing",lvl:6s},{id:"apotheosis:scavenger",lvl:3s},{id:"minecraft:looting",lvl:8s}],RepairCost:31,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":1.0f,"apotheosis:breaker/attribute/experienced":1.0f,"apotheosis:breaker/attribute/lengthy":1.0f,"apotheosis:breaker/attribute/lucky":1.0f,"apotheosis:breaker/special/enlightened":1.0f,"apotheosis:breaker/special/omnetic":1.0f,"apotheosis:telepathic":1.0f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:ancient"},gem:"apotheosis:overworld/earth"}},{Count:0b,id:"minecraft:air",tag:{Charged:0b}},{Count:0b,id:"minecraft:air",tag:{Charged:0b}}],name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/special/omnetic"},"",{"translate":"affix.apotheosis:breaker/attribute/experienced.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-421150767,-835042629,-1465530726,946707014]]}}}}],SelectedRecipe:"apotheosis:socketing"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:135,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["artifacts:eternal_steak"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:2,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},apoth_reforge_seed:*********,"quark:trying_crawl":0b,sophisticatedBackpackSettings:{}},Health:20.0f,HurtByTimestamp:72949,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"allthemodium:alloy_paxel",tag:{Enchantments:[{id:"apotheosis:miners_fervor",lvl:5s},{id:"minecraft:fortune",lvl:8s},{id:"evilcraft:life_stealing",lvl:6s},{id:"apotheosis:scavenger",lvl:3s},{id:"minecraft:looting",lvl:8s}],RepairCost:31,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":1.0f,"apotheosis:breaker/attribute/experienced":1.0f,"apotheosis:breaker/attribute/lengthy":1.0f,"apotheosis:breaker/attribute/lucky":1.0f,"apotheosis:breaker/special/enlightened":1.0f,"apotheosis:breaker/special/omnetic":1.0f,"apotheosis:telepathic":1.0f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:ancient"},gem:"apotheosis:overworld/earth"}},{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:ancient"},gem:"apotheosis:the_end/endersurge"}},{Count:0b,id:"minecraft:air",tag:{Charged:0b}}],name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/special/omnetic"},"",{"translate":"affix.apotheosis:breaker/attribute/experienced.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-421150767,-835042629,-1465530726,946707014]]}}},{Count:1b,Slot:1b,id:"minecraft:water_bucket"},{Count:64b,Slot:2b,id:"twigs:rhyolite"},{Count:64b,Slot:3b,id:"twigs:rhyolite"},{Count:12b,Slot:8b,id:"minecraft:sugar_cane"},{Count:1b,Slot:9b,id:"minecraft:crying_obsidian"},{Count:27b,Slot:10b,id:"minecraft:string"},{Count:1b,Slot:11b,id:"minecraft:bow",tag:{Damage:163}},{Count:15b,Slot:12b,id:"minecraft:arrow"},{Count:1b,Slot:13b,id:"minecraft:carrot"},{Count:22b,Slot:14b,id:"minecraft:rotten_flesh"},{Count:28b,Slot:15b,id:"minecraft:spider_eye"},{Count:21b,Slot:16b,id:"minecraft:gunpowder"},{Count:4b,Slot:17b,id:"paraglider:spirit_orb"},{Count:1b,Slot:18b,id:"tombstone:grave_dust"},{Count:64b,Slot:19b,id:"mysticalagriculture:prosperity_shard"},{Count:2b,Slot:20b,id:"enderio:broken_spawner",tag:{BlockEntityTag:{EntityStorage:{Entity:{id:"minecraft:cave_spider"}}}}},{Count:2b,Slot:21b,id:"reliquary:zombie_heart"},{Count:1b,Slot:22b,id:"reliquary:rib_bone"},{Count:1b,Slot:23b,id:"reliquary:catalyzing_gland"},{Count:7b,Slot:24b,id:"irons_spellbooks:arcane_essence"},{Count:1b,Slot:25b,id:"irons_spellbooks:scroll",tag:{ISB_Spells:{data:[{id:"irons_spellbooks:angel_wing",index:0,level:2,locked:1b}],maxSpells:1,mustEquip:0b,spellWheel:0b}}},{Count:15b,Slot:26b,id:"silentgear:bort"},{Count:25b,Slot:27b,id:"twigs:pebble"},{Count:1b,Slot:28b,id:"deepresonance:resonating_crystal_natural",tag:{BlockEntityTag:{Info:{efficiency:1.9184772968292236d,owner:"",power:40.554569244384766d,powered:0b,purity:6.849904537200928d,strength:0.022871920838952065d},Items:[],id:"deepresonance:resonating_crystal"}}},{Count:1b,Slot:29b,id:"forbidden_arcanus:spawner_scrap"},{Count:1b,Slot:30b,id:"tombstone:gift",tag:{Items:[{Count:1b,id:"tombstone:grave_dust"}]}},{Count:16b,Slot:31b,id:"minecraft:bone"}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:overworld",pos:[I;-4,66,-45]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-305.07895912044006d,-54.0d,-390.2717802974497d],Railways_DataVersion:2,Rotation:[-75.58389f,35.250057f],Score:1416,SelectedItemSlot:1,SleepTimer:0s,Spigot.ticksLived:77804,UUID:[I;-1761466194,-1162329286,-2122966518,-832933639],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-83837763215414L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:30,XpP:0.18749982f,XpSeed:0,XpTotal:1416,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752060955368L,keepLevel:0b,lastKnownName:"Elle_Fanning",lastPlayed:1752065132815L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:2.3080063f,foodLevel:20,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["cfm:stripped_mangrove_kitchen_sink_dark","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","biomesoplenty:fir_boat","trashcans:energy_trash_can","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","mcwpaths:cobbled_deepslate_crystal_floor_path","cfm:red_kitchen_sink","mcwroofs:granite_attic_roof","mcwroofs:sandstone_upper_lower_roof","chimes:copper_chimes","cfm:fridge_dark","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","mcwpaths:granite_windmill_weave_slab","pneumaticcraft:security_upgrade","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","supplementaries:flint_block","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","croptopia:frying_pan","mcwfences:acacia_wired_fence","reliquary:mob_charm_fragments/cave_spider","mcwbridges:granite_bridge_stair","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwpaths:granite_running_bond_slab","twigs:polished_rhyolite_stonecutting","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","supplementaries:candle_holders/candle_holder_green","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","aether:skyroot_fletching_table","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","rftoolsutility:screen_link","mcwroofs:purple_concrete_attic_roof","energymeter:meter","minecraft:polished_granite_from_granite_stonecutting","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","supplementaries:bubble_blower","mcwroofs:gutter_base_light_gray","delightful:knives/tin_knife","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","alltheores:iron_plate","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:sandstone_slab_from_sandstone_stonecutting","aquaculture:birch_fish_mount","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwpaths:cobblestone_dumble_paving","dyenamics:aquamarine_concrete_powder","cfm:crimson_kitchen_sink_dark","nethersdelight:golden_machete","tombstone:ankh_of_prayer","mcwbiomesoplenty:pine_highley_gate","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","aquaculture:gold_nugget_from_smelting","create:crafting/logistics/powered_latch","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","mcwpaths:cobbled_deepslate_windmill_weave_path","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","utilitarian:angel_block","occultism:crafting/magic_lamp_empty","minecraft:red_concrete_powder","simplylight:illuminant_orange_block_on_toggle","railcraft:brass_ingot_crafted_with_ingots","dyenamics:spring_green_concrete_powder","mcwpaths:sandstone_running_bond_stairs","create:granite_from_stone_types_granite_stonecutting","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwtrpdoors:print_whispering","simplylight:edge_light_bottom_from_top","potionblender:brewing_cauldron","securitycraft:username_logger","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","cfm:stripped_dark_oak_kitchen_sink_dark","create:industrial_iron_block_from_ingots_iron_stonecutting","tombstone:grave_plate","mcwfences:deepslate_pillar_wall","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","domum_ornamentum:light_blue_floating_carpet","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","minecraft:iron_chestplate","mcwlights:iron_small_chandelier","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","utilitix:directional_highspeed_rail","mcwdoors:garage_gray_door","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","create:cut_granite_bricks_from_stone_types_granite_stonecutting","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","securitycraft:reinforced_diorite","cfm:green_grill","pneumaticcraft:search_upgrade","cfm:orange_grill","bigreactors:turbine/basic/activefluidport_forge","cfm:brown_grill","mcwroofs:gutter_base_brown","reliquary:mob_charm_fragments/creeper","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","mcwroofs:light_blue_concrete_lower_roof","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","railcraft:lead_gear","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","simplylight:illuminant_blue_block_dyed","delightful:knives/osmium_knife","mcwfences:oak_wired_fence","twigs:silt","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","cfm:mangrove_kitchen_sink_dark","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","minecraft:granite_stairs_from_granite_stonecutting","mcwbiomesoplenty:maple_curved_gate","mcwlights:copper_wall_candle_holder","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:bamboo_raft","cfm:green_kitchen_sink","mcwfences:diorite_railing_gate","alltheores:tin_rod","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","cfm:acacia_kitchen_sink_dark","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","create:crafting/materials/andesite_alloy","minecraft:granite_slab","mcwlights:golden_low_candle_holder","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","supplementaries:cog_block","cfm:blue_grill","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","twigs:rocky_dirt","mcwpaths:granite_flagstone_stairs","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwpaths:sandstone_flagstone_slab","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfences:birch_highley_gate","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","create:crafting/kinetics/fluid_pipe","mcwfences:warped_pyramid_gate","create:small_granite_brick_slab_from_stone_types_granite_stonecutting","mcwdoors:metal_hospital_door","railcraft:signal_circuit","create:copper_bars_from_ingots_copper_stonecutting","minecraft:magenta_concrete_powder","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","immersiveengineering:crafting/screwdriver","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","cfm:red_grill","mcwfences:railing_blackstone_wall","mcwbiomesoplenty:willow_picket_fence","ae2:misc/tiny_tnt","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:granite_flagstone_path","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","additionallanterns:normal_sandstone_chain","mcwpaths:sandstone_windmill_weave","cfm:light_gray_trampoline","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","simplylight:illuminant_lime_block_dyed","bloodmagic:blood_altar","mcwpaths:granite_diamond_paving","forbidden_arcanus:clibano_core","mcwroofs:orange_concrete_top_roof","additionallanterns:granite_chain","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","create:small_granite_brick_wall_from_stone_types_granite_stonecutting","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwpaths:granite_honeycomb_paving","mcwpaths:cobblestone_clover_paving","mcwlights:golden_candle_holder","farmersdelight:diamond_knife","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","mcwroofs:yellow_concrete_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","littlelogistics:tug_route","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","deeperdarker:bloom_boat","utilitix:linked_repeater","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","minecraft:purple_concrete_powder","minecraft:sugar_from_sugar_cane","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","cfm:orange_trampoline","mcwroofs:lime_concrete_steep_roof","cfm:light_blue_kitchen_sink","createoreexcavation:diamond_drill","immersiveengineering:crafting/light_engineering","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","reliquary:mob_charm_fragments/zombified_piglin","railcraft:gold_gear","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","reliquary:mob_charm_fragments/zombie","mcwbiomesoplenty:magic_stockade_fence","securitycraft:panic_button","farmersdelight:cooking/dog_food","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","farmersdelight:organic_compost_from_rotten_flesh","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","railcraft:radio_circuit","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","domum_ornamentum:black_floating_carpet","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","minecraft:cut_sandstone_from_sandstone_stonecutting","create:crafting/logistics/powered_toggle_latch","mcwpaths:granite_windmill_weave","mcwpaths:granite_windmill_weave_path","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","minecraft:iron_bars","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","utilitarian:snad/drit","create:crafting/kinetics/copper_valve_handle","corail_woodcutter:bamboo_woodcutter","minecraft:iron_helmet","minecraft:diamond_axe","mcwpaths:granite_running_bond_path","supplementaries:candle_holders/candle_holder_red","mcwroofs:granite_top_roof","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","securitycraft:alarm","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","create:crafting/materials/andesite_alloy_from_zinc","travelersbackpack:skeleton","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","packingtape:tape","mcwpaths:sandstone_honeycomb_paving","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","create:copper_shingles_from_ingots_copper_stonecutting","mcwfences:granite_grass_topped_wall","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","create:polished_cut_granite_wall_from_stone_types_granite_stonecutting","mcwbiomesoplenty:rainbow_birch_hedge","create:cut_granite_stairs_from_stone_types_granite_stonecutting","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwpaths:cobbled_deepslate_flagstone_path","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","mcwroofs:cobblestone_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwfences:dark_oak_picket_fence","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","farmersdelight:cooking/fried_rice","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwpaths:granite_crystal_floor_path","mcwfences:bamboo_highley_gate","create:cut_granite_slab_from_stone_types_granite_stonecutting","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","create:crafting/kinetics/rose_quartz_lamp","immersiveengineering:crafting/plate_nickel_hammering","mcwfences:jungle_curved_gate","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","minecraft:shield","mcwroofs:gray_concrete_steep_roof","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","pneumaticcraft:minigun_upgrade","railcraft:signal_tuner","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","cfm:pink_trampoline","mcwbridges:balustrade_sandstone_bridge","securitycraft:briefcase","alltheores:nickel_rod","mcwfences:spruce_wired_fence","mcwlights:copper_triple_candle_holder","cfm:white_grill","minecraft:iron_nugget_from_smelting","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwbiomesoplenty:mahogany_stockade_fence","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","mcwfences:double_curved_metal_fence","mcwroofs:sandstone_lower_roof","travelersbackpack:hose_nozzle","securitycraft:claymore","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","twigs:polished_tuff_stonecutting","supplementaries:lock_block","reliquary:mob_charm_fragments/skeleton","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","simplylight:illuminant_brown_block_on_toggle","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","domum_ornamentum:green_floating_carpet","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","mcwpaths:granite_crystal_floor","minecraft:carrot_on_a_stick","aether:skyroot_loom","mcwwindows:metal_pane_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:granite_wall","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","minecraft:redstone_block","handcrafted:granite_corner_trim","minecraft:shears","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","aquaculture:bobber","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_dyed","minecraft:compass","simplylight:illuminant_brown_block_on_dyed","delightful:knives/lead_knife","mcwbiomesoplenty:redwood_hedge","minecraft:loom","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:icy_blue_concrete_powder","minecraft:arrow","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:turn_table","pneumaticcraft:item_life_upgrade","twilightforest:sorting_boat","mcwroofs:sandstone_attic_roof","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwpaths:granite_running_bond_stairs","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","mcwpaths:granite_flagstone","create:cut_granite_brick_wall_from_stone_types_granite_stonecutting","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","alltheores:nickel_plate","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","mcwpaths:stone_windmill_weave_path","mcwfences:red_sandstone_grass_topped_wall","everythingcopper:copper_door","mcwlights:copper_chandelier","mcwroofs:orange_concrete_steep_roof","everythingcopper:copper_pressure_plate","mcwfences:warped_wired_fence","mcwroofs:gutter_base_light_blue","pneumaticcraft:dispenser_upgrade","mcwbridges:granite_bridge","minecraft:diamond_block","mcwroofs:deepslate_roof","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","rftoolsutility:inventoryplus_module","mcwpaths:granite_crystal_floor_stairs","mcwfences:bamboo_picket_fence","bigreactors:blasting/graphite_from_coal","minecraft:respawn_anchor","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","cfm:stripped_crimson_kitchen_sink_light","simplylight:illuminant_lime_block_on_dyed","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","cfm:dark_oak_kitchen_sink_light","buildinggadgets2:gadget_exchanging","minecraft:lapis_block","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","immersiveengineering:crafting/voltmeter","minecraft:iron_axe","utilitarian:snad/snad","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","minecraft:jukebox","securitycraft:blacklist_module","create:layered_granite_from_stone_types_granite_stonecutting","mcwbiomesoplenty:snowblossom_hedge","domum_ornamentum:cyan_floating_carpet","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","mcwfences:dark_oak_highley_gate","mcwbiomesoplenty:palm_highley_gate","immersiveengineering:crafting/plate_lead_hammering","minecraft:fermented_spider_eye","simplylight:illuminant_blue_block_on_toggle","securitycraft:smart_module","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","mcwfences:mesh_metal_fence","minecraft:iron_door","minecraft:polished_granite","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","mcwroofs:gutter_base_orange","aether:bow_repairing","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","travelersbackpack:spider","mcwfences:quartz_railing_gate","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","mcwpaths:granite_clover_paving","reliquary:uncrafting/spider_eye","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alltheores:zinc_plate","alchemistry:combiner","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","aether:skyroot_boat","supplementaries:candle_holders/candle_holder_yellow","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwroofs:yellow_concrete_steep_roof","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","securitycraft:reinforced_sticky_piston","aether:iron_pendant","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","simplylight:illuminant_slab_from_panel","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","tombstone:bone_needle","twilightforest:twilight_oak_boat","modularrouters:modular_router","cfm:warped_kitchen_sink_light","delightful:knives/nickel_knife","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","minecraft:spruce_boat","minecraft:gold_block","aquaculture:gold_fillet_knife","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","cfm:gray_kitchen_sink","minecraft:paper","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","cfm:dark_oak_kitchen_sink_dark","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","mcwfences:jungle_horse_fence","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","minecraft:bucket","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbridges:sandstone_bridge","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","mcwroofs:black_concrete_attic_roof","mcwlights:golden_small_chandelier","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","simplylight:illuminant_block_on_toggle","minecraft:iron_nugget","domum_ornamentum:purple_floating_carpet","twigs:polished_tuff_bricks_from_tuff_stonecutting","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","sophisticatedstorage:storage_stack_upgrade_omega_tier_from_backpack_stack_upgrade_omega_tier","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","alltheores:lead_rod","mcwroofs:gutter_base_blue","mcwroofs:granite_roof","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","pneumaticcraft:volume_upgrade","aquaculture:dark_oak_fish_mount","littlelogistics:spring","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:gutter_base","additionallanterns:gold_chain","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","minecraft:golden_apple","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","minecraft:diamond_pickaxe","mcwpaths:cobbled_deepslate_flagstone_slab","mcwpaths:sandstone_dumble_paving","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwdoors:metal_reinforced_door","create:tuff_from_stone_types_tuff_stonecutting","domum_ornamentum:blue_cobblestone_extra","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:mangrove_kitchen_sink_light","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","mcwpaths:sandstone_running_bond_path","mcwlights:jungle_ceiling_fan_light","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","utilitarian:redstone_clock","mcwfences:jungle_highley_gate","handcrafted:granite_pillar_trim","mcwbiomesoplenty:mahogany_picket_fence","securitycraft:harming_module","silentgear:bort_block","minecraft:golden_boots","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","pneumaticcraft:magnet_upgrade","cfm:pink_grill","mcwbridges:iron_bridge_pier","mcwroofs:gutter_middle_orange","mcwroofs:sandstone_roof","minecraft:sticky_piston","mcwwindows:granite_window2","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","utilitix:diamond_shears","mcwbridges:balustrade_cobblestone_bridge","mcwbiomesoplenty:fir_stockade_fence","alltheores:copper_plate","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","mcwpaths:granite_crystal_floor_slab","alchemistry:liquifier","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsutility:counterplus_module","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_piston","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","minecraft:fishing_rod","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","delightful:knives/silver_knife","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","mcwroofs:brown_concrete_attic_roof","mcwpaths:granite_windmill_weave_stairs","mcwroofs:roofing_hammer","domum_ornamentum:gray_floating_carpet","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","create:copper_tiles_from_ingots_copper_stonecutting","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","minecraft:granite_wall_from_granite_stonecutting","mcwroofs:gutter_middle_magenta","minecraft:granite_stairs","sophisticatedstorage:packing_tape","silentgear:fine_silk","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","railcraft:tin_gear","minecraft:tnt","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","mcwpaths:granite_square_paving","minecraft:flint_and_steel","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","biomesoplenty:pine_boat","create:cut_granite_wall_from_stone_types_granite_stonecutting","cfm:magenta_kitchen_sink","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","cfm:spruce_kitchen_sink_light","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:creeper","minecraft:golden_axe","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","immersiveengineering:crafting/wire_lead","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","rftoolscontrol:card_base","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwwindows:granite_four_window","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","twigs:rhyolite_slab","immersiveengineering:crafting/stick_iron","create:polished_cut_granite_from_stone_types_granite_stonecutting","minecraft:granite_slab_from_granite_stonecutting","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:entity_tracker_upgrade","mcwpaths:stone_crystal_floor_path","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","minecraft:golden_sword","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","simplylight:illuminant_yellow_block_toggle","create:crafting/appliances/copper_diving_boots","create:polished_cut_granite_slab_from_stone_types_granite_stonecutting","mcwroofs:magenta_concrete_roof","immersiveengineering:crafting/component_iron","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","sfm:fancy_to_cable","mcwlights:copper_chain","mcwpaths:granite_running_bond","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","minecraft:cherry_boat","silentgear:stone_rod","aquaculture:cooked_fish_fillet_from_campfire","immersiveengineering:crafting/powerpack","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","create:crafting/kinetics/steam_engine","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","silentgear:bort_from_block","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","pneumaticcraft:elytra_upgrade","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","alltheores:zinc_rod","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","aether:iron_gloves","mcwfences:deepslate_brick_pillar_wall","tombstone:white_marble","aether:golden_gloves","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","pneumaticcraft:stomp_upgrade","minecraft:polished_granite_stairs_from_granite_stonecutting","supplementaries:lapis_bricks","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","create:small_granite_brick_stairs_from_stone_types_granite_stonecutting","domum_ornamentum:magenta_floating_carpet","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","minecraft:brush","domum_ornamentum:architectscutter","cfm:yellow_grill","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","sfm:cable","cfm:stripped_birch_kitchen_sink_light","aether:iron_ring","mcwroofs:deepslate_upper_steep_roof","tombstone:impregnated_diamond","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","railcraft:copper_gear","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","minecraft:minecart","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwpaths:granite_basket_weave_paving","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","supplementaries:pulley","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:jungle_fish_mount","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","alltheores:zinc_gear","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","biomesoplenty:dead_boat","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwfences:mangrove_stockade_fence","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","alltheores:osmium_rod","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","minecraft:golden_chestplate","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","securitycraft:universal_key_changer","mcwroofs:gray_concrete_roof","pneumaticcraft:range_upgrade","constructionwand:diamond_wand","securitycraft:sentry","supplementaries:hourglass","everythingcopper:copper_leggings","mcwroofs:gutter_base_pink","mcwroofs:magenta_concrete_lower_roof","mcwlights:iron_framed_torch","minecraft:deepslate","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","supplementaries:checker","mcwbiomesoplenty:maple_wired_fence","silentgear:fine_silk_cloth","mcwfences:railing_mud_brick_wall","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","railcraft:silver_gear","mcwbridges:cobblestone_bridge_pier","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwfences:dark_oak_horse_fence","create:cut_granite_brick_stairs_from_stone_types_granite_stonecutting","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:slime_block","mcwroofs:granite_upper_lower_roof","securitycraft:taser","create:copycat_panel_from_ingots_zinc_stonecutting","supplementaries:soap","mcwbridges:cobblestone_bridge","rftoolsutility:redstone_information","supplementaries:gold_door","minecraft:note_block","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","utilitix:linked_crystal","mcwpaths:stone_windmill_weave_stairs","domum_ornamentum:pink_floating_carpet","minecraft:smooth_sandstone","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","supplementaries:altimeter","utilitarian:angel_block_rot","everythingcopper:copper_shears","mcwroofs:lime_concrete_roof","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","simplylight:illuminant_cyan_block_on_toggle","mcwbridges:granite_bridge_pier","mcwfences:bamboo_wired_fence","minecraft:lantern","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","railcraft:nickel_gear","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwpaths:cobbled_deepslate_running_bond_path","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","handcrafted:bench","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","travelersbackpack:backpack_tank","railcraft:zinc_gear","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:lime_floating_carpet","biomesoplenty:maple_boat","croptopia:campfire_molasses","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","create:cut_granite_from_stone_types_granite_stonecutting","corail_woodcutter:warped_woodcutter","railcraft:water_tank_siding","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","create:crafting/kinetics/steam_whistle","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","minecraft:fletching_table","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","mythicbotany:rune_holder","immersiveengineering:crafting/plate_silver_hammering","modularrouters:blank_upgrade","allthecompressed:compress/cobbled_deepslate_1x","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwdoors:garage_silver_door","create:crafting/appliances/copper_diving_helmet","cfm:black_kitchen_sink","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","alltheores:silver_rod","minecraft:lever","alltheores:osmium_plate","mcwpaths:cobbled_deepslate_dumble_paving","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","securitycraft:bouncing_betty","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwfences:deepslate_grass_topped_wall","reliquary:uncrafting/string","mcwpaths:cobbled_deepslate_windmill_weave_stairs","domum_ornamentum:white_floating_carpet","mcwlights:golden_wall_candle_holder","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwpaths:granite_strewn_rocky_path","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","supplementaries:gold_trapdoor","pneumaticcraft:speed_upgrade","terralith:dropper_alt","mcwfences:birch_picket_fence","enderio:basic_fluid_filter","rftoolsutility:counter_module","littlelogistics:locomotive_route","create:granite_pillar_from_stone_types_granite_stonecutting","biomesoplenty:jacaranda_boat","mcwroofs:granite_lower_roof","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","delightful:knives/zinc_knife","mcwroofs:granite_steep_roof","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","securitycraft:storage_module","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","cfm:stripped_oak_kitchen_sink_light","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwbiomesoplenty:magic_highley_gate","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:candle","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","domum_ornamentum:brown_floating_carpet","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","mcwfences:oak_curved_gate","supplementaries:wrench","minecraft:bow","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","biomesoplenty:palm_boat","aether:iron_axe_repairing","reliquary:uncrafting/bone","cfm:purple_kitchen_sink","supplementaries:sack","mcwroofs:deepslate_upper_lower_roof","minecraft:dark_oak_boat","create:copycat_step_from_ingots_zinc_stonecutting","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","minecraft:firework_rocket_simple","mcwroofs:blackstone_upper_lower_roof","mcwroofs:gray_concrete_upper_steep_roof","mcwwindows:sandstone_window","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwroofs:pink_concrete_lower_roof","cfm:lime_trampoline","supplementaries:iron_gate","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","rftoolsbase:smartwrench","mcwfences:andesite_grass_topped_wall","mcwpaths:sandstone_flagstone_path","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:pine_horse_fence","mcwdoors:metal_windowed_door","securitycraft:floor_trap","minecraft:stone","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","deepresonance:machine_frame","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","tombstone:carmin_marble","mcwfences:sandstone_grass_topped_wall","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","botania:redstone_root","alltheores:gold_rod","mcwpaths:sandstone_diamond_paving","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","biomesoplenty:magic_boat","sfm:printing_press","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","supplementaries:key","mcwfences:bamboo_pyramid_gate","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","minecraft:polished_granite_slab_from_granite_stonecutting","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","mcwroofs:white_concrete_attic_roof","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","alltheores:lead_plate","additionallanterns:obsidian_chain","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","railcraft:receiver_circuit","mcwwindows:deepslate_window2","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","aquaculture:gold_nugget_from_gold_fish","allthecompressed:compress/granite_1x","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","mcwroofs:blackstone_top_roof","immersiveengineering:crafting/connector_hv_relay","supplementaries:candle_holders/candle_holder_pink","additionallanterns:diamond_chain","ae2additions:super_cell_housing","cfm:brown_trampoline","minecraft:light_weighted_pressure_plate","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","everythingcopper:copper_axe","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","create:polished_cut_granite_stairs_from_stone_types_granite_stonecutting","mcwbridges:balustrade_granite_bridge","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","forbidden_arcanus:arcane_chiseled_darkstone","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","mcwlights:lava_lamp","domum_ornamentum:light_gray_floating_carpet","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","dyenamics:bubblegum_concrete_powder","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","create:crafting/kinetics/empty_blaze_burner","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","everythingcopper:copper_rail","mcwdoors:garage_white_door","cfm:black_grill","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","create:small_granite_bricks_from_stone_types_granite_stonecutting","minecraft:iron_pickaxe","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","simplylight:illuminant_black_block_on_dyed","minecraft:lead","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","minecraft:campfire","cfm:white_kitchen_sink","mcwfences:modern_end_brick_wall","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","mcwpaths:granite_dumble_paving","mcwpaths:granite_flagstone_slab","aether:skyroot_note_block","mcwfences:railing_quartz_wall","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","bigreactors:smelting/graphite_from_coal","create:cut_granite_brick_slab_from_stone_types_granite_stonecutting","mcwroofs:granite_upper_steep_roof"],toBeDisplayed:["cfm:stripped_mangrove_kitchen_sink_dark","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","biomesoplenty:fir_boat","trashcans:energy_trash_can","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","mcwpaths:cobbled_deepslate_crystal_floor_path","cfm:red_kitchen_sink","mcwroofs:granite_attic_roof","mcwroofs:sandstone_upper_lower_roof","chimes:copper_chimes","cfm:fridge_dark","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","mcwpaths:granite_windmill_weave_slab","pneumaticcraft:security_upgrade","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","supplementaries:flint_block","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","croptopia:frying_pan","mcwfences:acacia_wired_fence","reliquary:mob_charm_fragments/cave_spider","mcwbridges:granite_bridge_stair","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwpaths:granite_running_bond_slab","twigs:polished_rhyolite_stonecutting","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","supplementaries:candle_holders/candle_holder_green","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","aether:skyroot_fletching_table","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","rftoolsutility:screen_link","mcwroofs:purple_concrete_attic_roof","energymeter:meter","minecraft:polished_granite_from_granite_stonecutting","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","supplementaries:bubble_blower","mcwroofs:gutter_base_light_gray","delightful:knives/tin_knife","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","alltheores:iron_plate","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:sandstone_slab_from_sandstone_stonecutting","aquaculture:birch_fish_mount","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwpaths:cobblestone_dumble_paving","dyenamics:aquamarine_concrete_powder","cfm:crimson_kitchen_sink_dark","nethersdelight:golden_machete","tombstone:ankh_of_prayer","mcwbiomesoplenty:pine_highley_gate","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","aquaculture:gold_nugget_from_smelting","create:crafting/logistics/powered_latch","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","mcwpaths:cobbled_deepslate_windmill_weave_path","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","aquaculture:double_hook","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","utilitarian:angel_block","occultism:crafting/magic_lamp_empty","minecraft:red_concrete_powder","simplylight:illuminant_orange_block_on_toggle","railcraft:brass_ingot_crafted_with_ingots","dyenamics:spring_green_concrete_powder","mcwpaths:sandstone_running_bond_stairs","create:granite_from_stone_types_granite_stonecutting","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwtrpdoors:print_whispering","simplylight:edge_light_bottom_from_top","potionblender:brewing_cauldron","securitycraft:username_logger","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","cfm:stripped_dark_oak_kitchen_sink_dark","create:industrial_iron_block_from_ingots_iron_stonecutting","tombstone:grave_plate","mcwfences:deepslate_pillar_wall","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","domum_ornamentum:light_blue_floating_carpet","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","minecraft:iron_chestplate","mcwlights:iron_small_chandelier","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","utilitix:directional_highspeed_rail","mcwdoors:garage_gray_door","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","create:cut_granite_bricks_from_stone_types_granite_stonecutting","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","securitycraft:reinforced_diorite","cfm:green_grill","pneumaticcraft:search_upgrade","cfm:orange_grill","bigreactors:turbine/basic/activefluidport_forge","cfm:brown_grill","mcwroofs:gutter_base_brown","reliquary:mob_charm_fragments/creeper","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","mcwroofs:light_blue_concrete_lower_roof","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","railcraft:lead_gear","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","simplylight:illuminant_blue_block_dyed","delightful:knives/osmium_knife","mcwfences:oak_wired_fence","twigs:silt","mcwroofs:blue_concrete_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","cfm:mangrove_kitchen_sink_dark","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","minecraft:diamond_leggings","mcwpaths:stone_running_bond_slab","minecraft:granite_stairs_from_granite_stonecutting","mcwbiomesoplenty:maple_curved_gate","mcwlights:copper_wall_candle_holder","minecraft:golden_pickaxe","mcwpaths:cobbled_deepslate_flagstone_stairs","minecraft:bamboo_raft","cfm:green_kitchen_sink","mcwfences:diorite_railing_gate","alltheores:tin_rod","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","cfm:acacia_kitchen_sink_dark","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","create:crafting/materials/andesite_alloy","minecraft:granite_slab","mcwlights:golden_low_candle_holder","mcwroofs:gutter_base_gray","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","supplementaries:cog_block","cfm:blue_grill","mcwlights:golden_chandelier","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","twigs:rocky_dirt","mcwpaths:granite_flagstone_stairs","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwpaths:sandstone_flagstone_slab","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfences:birch_highley_gate","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","create:crafting/kinetics/fluid_pipe","mcwfences:warped_pyramid_gate","create:small_granite_brick_slab_from_stone_types_granite_stonecutting","mcwdoors:metal_hospital_door","railcraft:signal_circuit","create:copper_bars_from_ingots_copper_stonecutting","minecraft:magenta_concrete_powder","minecraft:sandstone_slab","mcwfences:acacia_picket_fence","immersiveengineering:crafting/screwdriver","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","cfm:red_grill","mcwfences:railing_blackstone_wall","mcwbiomesoplenty:willow_picket_fence","ae2:misc/tiny_tnt","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:granite_flagstone_path","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","additionallanterns:normal_sandstone_chain","mcwpaths:sandstone_windmill_weave","cfm:light_gray_trampoline","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","simplylight:illuminant_lime_block_dyed","bloodmagic:blood_altar","mcwpaths:granite_diamond_paving","forbidden_arcanus:clibano_core","mcwroofs:orange_concrete_top_roof","additionallanterns:granite_chain","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","create:small_granite_brick_wall_from_stone_types_granite_stonecutting","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwpaths:granite_honeycomb_paving","mcwpaths:cobblestone_clover_paving","mcwlights:golden_candle_holder","farmersdelight:diamond_knife","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","mcwroofs:yellow_concrete_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","cfm:purple_grill","create:crafting/kinetics/hose_pulley","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","dyenamics:lavender_concrete_powder","mcwroofs:blackstone_upper_steep_roof","mcwroofs:white_concrete_roof","littlelogistics:tug_route","enderio:conduit_binder_composite","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","deeperdarker:bloom_boat","utilitix:linked_repeater","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","minecraft:purple_concrete_powder","minecraft:sugar_from_sugar_cane","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","cfm:orange_trampoline","mcwroofs:lime_concrete_steep_roof","cfm:light_blue_kitchen_sink","createoreexcavation:diamond_drill","immersiveengineering:crafting/light_engineering","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","reliquary:mob_charm_fragments/zombified_piglin","railcraft:gold_gear","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","reliquary:mob_charm_fragments/zombie","mcwbiomesoplenty:magic_stockade_fence","securitycraft:panic_button","farmersdelight:cooking/dog_food","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","farmersdelight:organic_compost_from_rotten_flesh","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","railcraft:radio_circuit","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","domum_ornamentum:black_floating_carpet","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","minecraft:cut_sandstone_from_sandstone_stonecutting","create:crafting/logistics/powered_toggle_latch","mcwpaths:granite_windmill_weave","mcwpaths:granite_windmill_weave_path","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","minecraft:iron_bars","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","utilitarian:snad/drit","create:crafting/kinetics/copper_valve_handle","corail_woodcutter:bamboo_woodcutter","minecraft:iron_helmet","minecraft:diamond_axe","mcwpaths:granite_running_bond_path","supplementaries:candle_holders/candle_holder_red","mcwroofs:granite_top_roof","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","securitycraft:alarm","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","create:crafting/materials/andesite_alloy_from_zinc","travelersbackpack:skeleton","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","packingtape:tape","mcwpaths:sandstone_honeycomb_paving","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","create:copper_shingles_from_ingots_copper_stonecutting","mcwfences:granite_grass_topped_wall","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","create:polished_cut_granite_wall_from_stone_types_granite_stonecutting","mcwbiomesoplenty:rainbow_birch_hedge","create:cut_granite_stairs_from_stone_types_granite_stonecutting","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwpaths:cobbled_deepslate_flagstone_path","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","mcwroofs:cobblestone_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwfences:dark_oak_picket_fence","alchemistry:compactor","travelersbackpack:sandstone","minecraft:dropper","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","farmersdelight:cooking/fried_rice","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwpaths:granite_crystal_floor_path","mcwfences:bamboo_highley_gate","create:cut_granite_slab_from_stone_types_granite_stonecutting","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","securitycraft:reinforced_dispenser","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","create:crafting/kinetics/rose_quartz_lamp","immersiveengineering:crafting/plate_nickel_hammering","mcwfences:jungle_curved_gate","cfm:stripped_dark_oak_kitchen_sink_light","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwtrpdoors:metal_trapdoor","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","minecraft:shield","mcwroofs:gray_concrete_steep_roof","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","pneumaticcraft:minigun_upgrade","railcraft:signal_tuner","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:mahogany_pyramid_gate","mcwpaths:stone_running_bond_stairs","cfm:pink_trampoline","mcwbridges:balustrade_sandstone_bridge","securitycraft:briefcase","alltheores:nickel_rod","mcwfences:spruce_wired_fence","mcwlights:copper_triple_candle_holder","cfm:white_grill","minecraft:iron_nugget_from_smelting","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","mcwbiomesoplenty:mahogany_stockade_fence","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","mcwfences:double_curved_metal_fence","mcwroofs:sandstone_lower_roof","travelersbackpack:hose_nozzle","securitycraft:claymore","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","twigs:polished_tuff_stonecutting","supplementaries:lock_block","reliquary:mob_charm_fragments/skeleton","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","simplylight:illuminant_brown_block_on_toggle","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","minecraft:bone_meal","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","domum_ornamentum:green_floating_carpet","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","mcwpaths:granite_crystal_floor","minecraft:carrot_on_a_stick","aether:skyroot_loom","mcwwindows:metal_pane_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:granite_wall","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","minecraft:redstone_block","handcrafted:granite_corner_trim","minecraft:shears","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","aquaculture:bobber","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_dyed","minecraft:compass","simplylight:illuminant_brown_block_on_dyed","delightful:knives/lead_knife","mcwbiomesoplenty:redwood_hedge","minecraft:loom","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:icy_blue_concrete_powder","minecraft:arrow","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:turn_table","pneumaticcraft:item_life_upgrade","twilightforest:sorting_boat","mcwroofs:sandstone_attic_roof","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwpaths:granite_running_bond_stairs","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","mcwpaths:granite_flagstone","create:cut_granite_brick_wall_from_stone_types_granite_stonecutting","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","alltheores:nickel_plate","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","mcwpaths:stone_windmill_weave_path","mcwfences:red_sandstone_grass_topped_wall","everythingcopper:copper_door","mcwlights:copper_chandelier","mcwroofs:orange_concrete_steep_roof","everythingcopper:copper_pressure_plate","mcwfences:warped_wired_fence","mcwroofs:gutter_base_light_blue","pneumaticcraft:dispenser_upgrade","mcwbridges:granite_bridge","minecraft:diamond_block","mcwroofs:deepslate_roof","bigreactors:turbine/reinforced/passivefluidport_forge","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","rftoolsutility:inventoryplus_module","mcwpaths:granite_crystal_floor_stairs","mcwfences:bamboo_picket_fence","bigreactors:blasting/graphite_from_coal","minecraft:respawn_anchor","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","cfm:stripped_crimson_kitchen_sink_light","simplylight:illuminant_lime_block_on_dyed","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","cfm:dark_oak_kitchen_sink_light","buildinggadgets2:gadget_exchanging","minecraft:lapis_block","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","immersiveengineering:crafting/voltmeter","minecraft:iron_axe","utilitarian:snad/snad","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","minecraft:jukebox","securitycraft:blacklist_module","create:layered_granite_from_stone_types_granite_stonecutting","mcwbiomesoplenty:snowblossom_hedge","domum_ornamentum:cyan_floating_carpet","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","mcwfences:dark_oak_highley_gate","mcwbiomesoplenty:palm_highley_gate","immersiveengineering:crafting/plate_lead_hammering","minecraft:fermented_spider_eye","simplylight:illuminant_blue_block_on_toggle","securitycraft:smart_module","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","mcwfences:mesh_metal_fence","minecraft:iron_door","minecraft:polished_granite","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","mcwroofs:gutter_base_orange","aether:bow_repairing","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","travelersbackpack:spider","mcwfences:quartz_railing_gate","twilightforest:transformation_boat","cfm:acacia_kitchen_sink_light","mcwpaths:granite_clover_paving","reliquary:uncrafting/spider_eye","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alltheores:zinc_plate","alchemistry:combiner","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","aether:skyroot_boat","supplementaries:candle_holders/candle_holder_yellow","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwroofs:yellow_concrete_steep_roof","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","securitycraft:reinforced_sticky_piston","aether:iron_pendant","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","simplylight:illuminant_slab_from_panel","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","tombstone:bone_needle","twilightforest:twilight_oak_boat","modularrouters:modular_router","cfm:warped_kitchen_sink_light","delightful:knives/nickel_knife","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","minecraft:spruce_boat","minecraft:gold_block","aquaculture:gold_fillet_knife","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","cfm:gray_kitchen_sink","minecraft:paper","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","cfm:dark_oak_kitchen_sink_dark","everythingcopper:copper_shovel","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","mcwfences:jungle_horse_fence","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","minecraft:bucket","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwbridges:sandstone_bridge","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","mcwroofs:black_concrete_attic_roof","mcwlights:golden_small_chandelier","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","simplylight:illuminant_block_on_toggle","minecraft:iron_nugget","domum_ornamentum:purple_floating_carpet","twigs:polished_tuff_bricks_from_tuff_stonecutting","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","sophisticatedstorage:storage_stack_upgrade_omega_tier_from_backpack_stack_upgrade_omega_tier","mcwroofs:gutter_middle_light_gray","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","alltheores:lead_rod","mcwroofs:gutter_base_blue","mcwroofs:granite_roof","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","pneumaticcraft:volume_upgrade","aquaculture:dark_oak_fish_mount","littlelogistics:spring","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:gutter_base","additionallanterns:gold_chain","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","minecraft:golden_apple","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","minecraft:diamond_pickaxe","mcwpaths:cobbled_deepslate_flagstone_slab","mcwpaths:sandstone_dumble_paving","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwdoors:metal_reinforced_door","create:tuff_from_stone_types_tuff_stonecutting","domum_ornamentum:blue_cobblestone_extra","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:mangrove_kitchen_sink_light","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","minecraft:chiseled_sandstone_from_sandstone_stonecutting","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwfences:warped_horse_fence","twilightforest:canopy_boat","mcwpaths:sandstone_running_bond_path","mcwlights:jungle_ceiling_fan_light","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","utilitarian:redstone_clock","mcwfences:jungle_highley_gate","handcrafted:granite_pillar_trim","mcwbiomesoplenty:mahogany_picket_fence","securitycraft:harming_module","silentgear:bort_block","minecraft:golden_boots","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","pneumaticcraft:magnet_upgrade","cfm:pink_grill","mcwbridges:iron_bridge_pier","mcwroofs:gutter_middle_orange","mcwroofs:sandstone_roof","minecraft:sticky_piston","mcwwindows:granite_window2","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","utilitix:diamond_shears","mcwbridges:balustrade_cobblestone_bridge","mcwbiomesoplenty:fir_stockade_fence","alltheores:copper_plate","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","mcwpaths:granite_crystal_floor_slab","alchemistry:liquifier","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsutility:counterplus_module","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_piston","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","minecraft:fishing_rod","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","delightful:knives/silver_knife","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","mcwroofs:brown_concrete_attic_roof","mcwpaths:granite_windmill_weave_stairs","mcwroofs:roofing_hammer","domum_ornamentum:gray_floating_carpet","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","create:copper_tiles_from_ingots_copper_stonecutting","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","minecraft:granite_wall_from_granite_stonecutting","mcwroofs:gutter_middle_magenta","minecraft:granite_stairs","sophisticatedstorage:packing_tape","silentgear:fine_silk","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","railcraft:tin_gear","minecraft:tnt","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","mcwpaths:granite_square_paving","minecraft:flint_and_steel","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","biomesoplenty:pine_boat","create:cut_granite_wall_from_stone_types_granite_stonecutting","cfm:magenta_kitchen_sink","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","cfm:spruce_kitchen_sink_light","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","travelersbackpack:creeper","minecraft:golden_axe","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","biomesoplenty:umbran_boat","immersiveengineering:crafting/wire_lead","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","rftoolscontrol:card_base","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","simplylight:illuminant_magenta_block_toggle","mcwwindows:granite_four_window","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","mcwwindows:metal_window","twigs:rhyolite_slab","immersiveengineering:crafting/stick_iron","create:polished_cut_granite_from_stone_types_granite_stonecutting","minecraft:granite_slab_from_granite_stonecutting","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:entity_tracker_upgrade","mcwpaths:stone_crystal_floor_path","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","minecraft:golden_sword","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","simplylight:illuminant_yellow_block_toggle","create:crafting/appliances/copper_diving_boots","create:polished_cut_granite_slab_from_stone_types_granite_stonecutting","mcwroofs:magenta_concrete_roof","immersiveengineering:crafting/component_iron","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","sfm:fancy_to_cable","mcwlights:copper_chain","mcwpaths:granite_running_bond","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","minecraft:cherry_boat","silentgear:stone_rod","aquaculture:cooked_fish_fillet_from_campfire","immersiveengineering:crafting/powerpack","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwroofs:orange_concrete_lower_roof","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","create:crafting/kinetics/steam_engine","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","silentgear:bort_from_block","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","pneumaticcraft:elytra_upgrade","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","alltheores:zinc_rod","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","aether:iron_gloves","mcwfences:deepslate_brick_pillar_wall","tombstone:white_marble","aether:golden_gloves","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","pneumaticcraft:stomp_upgrade","minecraft:polished_granite_stairs_from_granite_stonecutting","supplementaries:lapis_bricks","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","create:small_granite_brick_stairs_from_stone_types_granite_stonecutting","domum_ornamentum:magenta_floating_carpet","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","minecraft:brush","domum_ornamentum:architectscutter","cfm:yellow_grill","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","sfm:cable","cfm:stripped_birch_kitchen_sink_light","aether:iron_ring","mcwroofs:deepslate_upper_steep_roof","tombstone:impregnated_diamond","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:palm_pyramid_gate","railcraft:copper_gear","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","minecraft:minecart","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwpaths:granite_basket_weave_paving","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","supplementaries:pulley","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:jungle_fish_mount","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","alltheores:zinc_gear","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","biomesoplenty:dead_boat","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","mcwfences:mangrove_stockade_fence","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","alltheores:osmium_rod","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","minecraft:golden_chestplate","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","securitycraft:universal_key_changer","mcwroofs:gray_concrete_roof","pneumaticcraft:range_upgrade","constructionwand:diamond_wand","securitycraft:sentry","supplementaries:hourglass","everythingcopper:copper_leggings","mcwroofs:gutter_base_pink","mcwroofs:magenta_concrete_lower_roof","mcwlights:iron_framed_torch","minecraft:deepslate","pneumaticcraft:ender_visor_upgrade","simplylight:illuminant_blue_block_on_dyed","supplementaries:checker","mcwbiomesoplenty:maple_wired_fence","silentgear:fine_silk_cloth","mcwfences:railing_mud_brick_wall","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","railcraft:silver_gear","mcwbridges:cobblestone_bridge_pier","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwfences:dark_oak_horse_fence","create:cut_granite_brick_stairs_from_stone_types_granite_stonecutting","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:slime_block","mcwroofs:granite_upper_lower_roof","securitycraft:taser","create:copycat_panel_from_ingots_zinc_stonecutting","supplementaries:soap","mcwbridges:cobblestone_bridge","rftoolsutility:redstone_information","supplementaries:gold_door","minecraft:note_block","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","utilitix:linked_crystal","mcwpaths:stone_windmill_weave_stairs","domum_ornamentum:pink_floating_carpet","minecraft:smooth_sandstone","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","supplementaries:altimeter","utilitarian:angel_block_rot","everythingcopper:copper_shears","mcwroofs:lime_concrete_roof","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","simplylight:illuminant_cyan_block_on_toggle","mcwbridges:granite_bridge_pier","mcwfences:bamboo_wired_fence","minecraft:lantern","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","railcraft:nickel_gear","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwpaths:cobbled_deepslate_running_bond_path","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","mcwpaths:cobbled_deepslate_strewn_rocky_path","utilitix:dimmable_redstone_lamp","handcrafted:bench","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","travelersbackpack:backpack_tank","railcraft:zinc_gear","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:lime_floating_carpet","biomesoplenty:maple_boat","croptopia:campfire_molasses","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","create:cut_granite_from_stone_types_granite_stonecutting","corail_woodcutter:warped_woodcutter","railcraft:water_tank_siding","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","create:crafting/kinetics/steam_whistle","mcwroofs:black_concrete_steep_roof","minecraft:cobbled_deepslate_stairs","minecraft:fletching_table","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwbiomesoplenty:maple_stockade_fence","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","twigs:tuff_stairs","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","mythicbotany:rune_holder","immersiveengineering:crafting/plate_silver_hammering","modularrouters:blank_upgrade","allthecompressed:compress/cobbled_deepslate_1x","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwdoors:garage_silver_door","create:crafting/appliances/copper_diving_helmet","cfm:black_kitchen_sink","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwfences:acacia_pyramid_gate","alltheores:silver_rod","minecraft:lever","alltheores:osmium_plate","mcwpaths:cobbled_deepslate_dumble_paving","mcwroofs:white_concrete_steep_roof","mcwroofs:gray_concrete_lower_roof","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","securitycraft:bouncing_betty","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwfences:deepslate_grass_topped_wall","reliquary:uncrafting/string","mcwpaths:cobbled_deepslate_windmill_weave_stairs","domum_ornamentum:white_floating_carpet","mcwlights:golden_wall_candle_holder","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwpaths:granite_strewn_rocky_path","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","supplementaries:gold_trapdoor","pneumaticcraft:speed_upgrade","terralith:dropper_alt","mcwfences:birch_picket_fence","enderio:basic_fluid_filter","rftoolsutility:counter_module","littlelogistics:locomotive_route","create:granite_pillar_from_stone_types_granite_stonecutting","biomesoplenty:jacaranda_boat","mcwroofs:granite_lower_roof","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","delightful:knives/zinc_knife","mcwroofs:granite_steep_roof","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","securitycraft:storage_module","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","cfm:cyan_trampoline","cfm:stripped_oak_kitchen_sink_light","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwbiomesoplenty:magic_highley_gate","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:candle","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","domum_ornamentum:brown_floating_carpet","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","mcwfences:oak_curved_gate","supplementaries:wrench","minecraft:bow","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","biomesoplenty:palm_boat","aether:iron_axe_repairing","reliquary:uncrafting/bone","cfm:purple_kitchen_sink","supplementaries:sack","mcwroofs:deepslate_upper_lower_roof","minecraft:dark_oak_boat","create:copycat_step_from_ingots_zinc_stonecutting","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","minecraft:firework_rocket_simple","mcwroofs:blackstone_upper_lower_roof","mcwroofs:gray_concrete_upper_steep_roof","mcwwindows:sandstone_window","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwroofs:pink_concrete_lower_roof","cfm:lime_trampoline","supplementaries:iron_gate","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","rftoolsbase:smartwrench","mcwfences:andesite_grass_topped_wall","mcwpaths:sandstone_flagstone_path","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:pine_horse_fence","mcwdoors:metal_windowed_door","securitycraft:floor_trap","minecraft:stone","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","deepresonance:machine_frame","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","tombstone:carmin_marble","mcwfences:sandstone_grass_topped_wall","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","botania:redstone_root","alltheores:gold_rod","mcwpaths:sandstone_diamond_paving","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","biomesoplenty:magic_boat","sfm:printing_press","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","supplementaries:key","mcwfences:bamboo_pyramid_gate","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","minecraft:polished_granite_slab_from_granite_stonecutting","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","mcwroofs:white_concrete_attic_roof","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","alltheores:lead_plate","additionallanterns:obsidian_chain","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","railcraft:receiver_circuit","mcwwindows:deepslate_window2","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","aquaculture:gold_nugget_from_gold_fish","allthecompressed:compress/granite_1x","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","mcwroofs:blackstone_top_roof","immersiveengineering:crafting/connector_hv_relay","supplementaries:candle_holders/candle_holder_pink","additionallanterns:diamond_chain","ae2additions:super_cell_housing","cfm:brown_trampoline","minecraft:light_weighted_pressure_plate","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","everythingcopper:copper_axe","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","create:polished_cut_granite_stairs_from_stone_types_granite_stonecutting","mcwbridges:balustrade_granite_bridge","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","forbidden_arcanus:arcane_chiseled_darkstone","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","mcwlights:lava_lamp","domum_ornamentum:light_gray_floating_carpet","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","dyenamics:bubblegum_concrete_powder","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","create:crafting/kinetics/empty_blaze_burner","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","charginggadgets:charging_station","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","everythingcopper:copper_rail","mcwdoors:garage_white_door","cfm:black_grill","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","create:small_granite_bricks_from_stone_types_granite_stonecutting","minecraft:iron_pickaxe","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","mcwwindows:granite_window","mcwpaths:stone_flagstone","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","simplylight:illuminant_black_block_on_dyed","minecraft:lead","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","minecraft:campfire","cfm:white_kitchen_sink","mcwfences:modern_end_brick_wall","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","mcwpaths:granite_dumble_paving","mcwpaths:granite_flagstone_slab","aether:skyroot_note_block","mcwfences:railing_quartz_wall","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","bigreactors:smelting/graphite_from_coal","create:cut_granite_brick_slab_from_stone_types_granite_stonecutting","mcwroofs:granite_upper_steep_roof"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:7246,warning_level:0}}