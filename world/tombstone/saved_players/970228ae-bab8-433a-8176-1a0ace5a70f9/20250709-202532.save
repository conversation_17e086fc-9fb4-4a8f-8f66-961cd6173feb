{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"}],Air:300s,Attributes:[{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:4.0d,Modifiers:[{Amount:0.4d,Name:"artifacts:feral_claws_attack_speed_bonus",Operation:0,UUID:[I;-**********,-**********,-**********,**********]}],Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:4.5d,Name:"forge:block_reach"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"artifacts:steadfast_spikes_knockback_resistance",Operation:0,UUID:[I;-706276632,**********,-**********,**********]}],Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:1.0d,Modifiers:[{Amount:4.0d,Name:"artifacts:power_glove_attack_damage_bonus",Operation:0,UUID:[I;308939635,-**********,-**********,-702737548]}],Name:"minecraft:generic.attack_damage"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"attributeslib:creative_flight"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:obsidian_skull"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{CachedModifiers:[],Cosmetics:{Items:[],Size:32},DropRule:"DEFAULT",HasCosmetic:0b,PersistentModifiers:[{Amount:30.0d,Name:"legacy",Operation:0,UUID:[I;185510845,1109413535,-1157942256,228769150]}],RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1},{Render:0b,Slot:2},{Render:0b,Slot:3},{Render:0b,Slot:4},{Render:0b,Slot:5},{Render:0b,Slot:6},{Render:0b,Slot:7},{Render:0b,Slot:8},{Render:0b,Slot:9},{Render:0b,Slot:10},{Render:0b,Slot:11},{Render:0b,Slot:12},{Render:0b,Slot:13},{Render:0b,Slot:14},{Render:0b,Slot:15},{Render:0b,Slot:16},{Render:0b,Slot:17},{Render:0b,Slot:18},{Render:0b,Slot:19},{Render:0b,Slot:20},{Render:0b,Slot:21},{Render:0b,Slot:22},{Render:0b,Slot:23},{Render:0b,Slot:24},{Render:0b,Slot:25},{Render:0b,Slot:26},{Render:0b,Slot:27},{Render:0b,Slot:28},{Render:0b,Slot:29},{Render:0b,Slot:30},{Render:0b,Slot:31}],Size:32},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:night_vision_goggles"},{Count:1b,Slot:1,id:"artifacts:plastic_drinking_hat"},{Count:1b,Slot:2,id:"artifacts:novelty_drinking_hat"},{Count:1b,Slot:3,id:"artifacts:superstitious_hat"},{Count:1b,Slot:4,id:"artifacts:anglers_hat"},{Count:1b,Slot:5,id:"artifacts:lucky_scarf"},{Count:1b,Slot:6,id:"artifacts:cross_necklace",tag:{CanApplyBonus:1b}},{Count:1b,Slot:7,id:"artifacts:antidote_vessel"},{Count:1b,Slot:8,id:"artifacts:digging_claws"},{Count:1b,Slot:9,id:"artifacts:feral_claws"},{Count:1b,Slot:10,id:"artifacts:power_glove"},{Count:1b,Slot:11,id:"artifacts:vampiric_glove"},{Count:1b,Slot:12,id:"artifacts:golden_hook"},{Count:1b,Slot:13,id:"artifacts:pickaxe_heater"},{Count:1b,Slot:14,id:"artifacts:steadfast_spikes"}],Size:32},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1},{Render:0b,Slot:2},{Render:0b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:netherite_backpack",tag:{contentsUuid:[I;390048106,-673756075,-1938614503,949916074],inventorySlots:120,renderInfo:{upgradeItems:[{Count:1b,id:"sophisticatedbackpacks:crafting_upgrade"},{Count:1b,id:"sophisticatedbackpacks:advanced_feeding_upgrade",tag:{feedAtHungerLevel:"any",filters:{Items:[{Count:1b,Slot:0,id:"artifacts:eternal_steak"},{Count:1b,Slot:1,id:"artifacts:everlasting_beef"}],Size:16},isAllowList:1b}},{Count:1b,id:"sophisticatedbackpacks:advanced_magnet_upgrade",tag:{filterByStorage:1b}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_omega_tier"}]},upgradeSlots:7}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:54933},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:0b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{},"rftoolsutility:properties":{allowFlying:0b,buffTicks:148,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:[]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:1,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},apoth_reforge_seed:*********,"quark:trying_crawl":0b,sophisticatedBackpackSettings:{}},Health:20.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"allthemodium:alloy_paxel",tag:{affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":1.0f,"apotheosis:breaker/attribute/experienced":1.0f,"apotheosis:breaker/attribute/lengthy":1.0f,"apotheosis:breaker/attribute/lucky":1.0f,"apotheosis:breaker/special/enlightened":1.0f,"apotheosis:breaker/special/omnetic":1.0f,"apotheosis:telepathic":1.0f},name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/special/omnetic"},"",{"translate":"affix.apotheosis:breaker/attribute/experienced.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-421150767,-835042629,-**********,946707014]]}}},{Count:1b,Slot:1b,id:"minecraft:enchanted_book",tag:{StoredEnchantments:[{id:"apotheosis:miners_fervor",lvl:5s}]}},{Count:1b,Slot:2b,id:"minecraft:enchanted_book",tag:{StoredEnchantments:[{id:"minecraft:fortune",lvl:8s}]}},{Count:1b,Slot:8b,id:"minecraft:anvil"},{Count:1b,Slot:9b,id:"tombstone:gift",tag:{Items:[{Count:1b,id:"tombstone:grave_dust"}]}},{Count:1b,Slot:10b,id:"minecraft:slime_ball"}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:overworld",pos:[I;-4,66,-45]},Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-290.53217172215864d,70.0d,-362.7671218964463d],Railways_DataVersion:2,Rotation:[-174.12741f,51.449856f],Score:0,SelectedItemSlot:5,SleepTimer:0s,Spigot.ticksLived:54933,UUID:[I;-1761466194,-1162329286,-2122966518,-832933639],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-79714594500538L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:0,XpP:0.0f,XpSeed:0,XpTotal:0,abilities:{flySpeed:0.05f,flying:0b,instabuild:1b,invulnerable:1b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752060955368L,keepLevel:0b,lastKnownName:"Elle_Fanning",lastPlayed:1752063932802L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.0f,foodLevel:20,foodSaturationLevel:5.0f,foodTickTimer:0,playerGameType:1,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwbiomesoplenty:magic_horse_fence","corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","mcwroofs:black_concrete_attic_roof","mcwroofs:magenta_concrete_top_roof","mcwfences:railing_deepslate_brick_wall","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwroofs:lime_concrete_roof","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","mcwbiomesoplenty:redwood_highley_gate","aquaculture:cooked_fish_fillet_from_smoking","mcwfences:warped_stockade_fence","mcwfences:panelled_metal_fence_gate","simplylight:illuminant_block_on_toggle","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","aquaculture:cooked_fish_fillet","mcwfences:mangrove_picket_fence","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","aquaculture:planks_from_driftwood","sophisticatedstorage:storage_stack_upgrade_omega_tier_from_backpack_stack_upgrade_omega_tier","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwbiomesoplenty:umbran_horse_fence","mcwfences:birch_stockade_fence","mcwfences:dark_oak_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwbiomesoplenty:empyreal_hedge","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:hellbark_picket_fence","mcwfences:crimson_curved_gate","allthecompressed:compress/sand_1x","corail_woodcutter:crimson_woodcutter","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","corail_woodcutter:bamboo_woodcutter","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","aquaculture:jellyfish_to_slimeball","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwroofs:lime_concrete_attic_roof","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","mcwroofs:gray_concrete_top_roof","mcwroofs:green_concrete_attic_roof","corail_woodcutter:spruce_woodcutter","mcwbiomesoplenty:mahogany_wired_fence","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwfences:curved_metal_fence_gate","mcwroofs:light_gray_concrete_top_roof","mcwfences:acacia_hedge","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwfences:majestic_metal_fence_gate","mcwroofs:purple_concrete_attic_roof","packingtape:tape","energymeter:meter","simplylight:rodlamp","mcwroofs:purple_concrete_upper_lower_roof","sliceanddice:slicer","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","aquaculture:iron_nugget_from_smelting","railcraft:water_tank_siding","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:rainbow_birch_hedge","mcwroofs:black_concrete_steep_roof","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","dyenamics:mint_concrete_powder","mcwfences:oak_horse_fence","mcwfences:flowering_azalea_hedge","mcwfences:railing_nether_brick_wall","mcwfences:expanded_mesh_metal_fence","mcwfences:acacia_stockade_fence","mcwfences:crimson_picket_fence","aquaculture:birch_fish_mount","mcwfences:blackstone_pillar_wall","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwroofs:brown_concrete_roof","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwfences:warped_horse_fence","mcwbiomesoplenty:maple_stockade_fence","mcwbiomesoplenty:mahogany_curved_gate","mcwroofs:red_concrete_upper_steep_roof","simplylight:illuminant_red_block_toggle","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","dyenamics:aquamarine_concrete_powder","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","mcwfences:dark_oak_hedge","aquaculture:neptunium_ingot_from_neptunium_block","immersiveengineering:crafting/grit_sand","mcwfences:warped_picket_fence","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:pine_wired_fence","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","mcwfences:bamboo_highley_gate","mcwroofs:pink_concrete_roof","mcwfences:warped_highley_gate","mcwroofs:red_concrete_upper_lower_roof","mcwfences:crimson_stockade_fence","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:mangrove_horse_fence","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","mcwfences:end_brick_grass_topped_wall","minecraft:sticky_piston","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","corail_woodcutter:acacia_woodcutter","mcwbiomesoplenty:jacaranda_stockade_fence","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","mcwroofs:cyan_concrete_upper_lower_roof","simplylight:illuminant_gray_block_on_toggle","aquaculture:gold_nugget_from_smelting","mcwfences:jungle_curved_gate","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:fir_stockade_fence","mcwfences:acacia_pyramid_gate","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","minecraft:green_concrete_powder","dyenamics:cherenkov_concrete_powder","mcwroofs:magenta_concrete_upper_lower_roof","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:white_concrete_steep_roof","mcwroofs:cyan_concrete_lower_roof","mcwroofs:gray_concrete_lower_roof","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","aquaculture:double_hook","mcwroofs:pink_concrete_top_roof","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:cyan_concrete_steep_roof","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwroofs:gray_concrete_steep_roof","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwbiomesoplenty:willow_pyramid_gate","minecraft:yellow_concrete_powder","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","aquaculture:tin_can_to_iron_nugget","minecraft:red_concrete_powder","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","simplylight:illuminant_orange_block_on_toggle","aquaculture:iron_fishing_rod","mcwfences:deepslate_grass_topped_wall","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwbiomesoplenty:redwood_picket_fence","dyenamics:spring_green_concrete_powder","mcwroofs:red_concrete_steep_roof","minecraft:glass","minecraft:crafting_table","simplylight:illuminant_light_blue_block_toggle","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","mcwroofs:brown_concrete_steep_roof","mcwroofs:brown_concrete_attic_roof","mcwfences:spruce_wired_fence","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","mcwfences:gothic_metal_fence","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","mcwroofs:black_concrete_lower_roof","minecraft:light_blue_concrete_powder","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","mcwfences:deepslate_railing_gate","mcwbiomesoplenty:mahogany_stockade_fence","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","mcwfences:birch_picket_fence","mcwfences:double_curved_metal_fence","mcwfences:deepslate_pillar_wall","sophisticatedstorage:packing_tape","mcwbiomesoplenty:magic_wired_fence","mcwbiomesoplenty:empyreal_wired_fence","mcwroofs:red_concrete_attic_roof","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","simplylight:illuminant_pink_block_on_dyed","mcwfences:stone_grass_topped_wall","dyenamics:navy_concrete_powder","mcwfences:oak_picket_fence","mcwroofs:brown_concrete_top_roof","domum_ornamentum:sand_stone_bricks","mcwfences:mud_brick_grass_topped_wall","mcwroofs:orange_concrete_upper_lower_roof","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_horse_fence","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwroofs:lime_concrete_upper_lower_roof","mcwbiomesoplenty:magic_highley_gate","mcwroofs:cyan_concrete_roof","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","mcwbiomesoplenty:magic_picket_fence","mcwroofs:yellow_concrete_upper_lower_roof","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","minecraft:brown_concrete_powder","mcwroofs:light_gray_concrete_upper_steep_roof","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","mcwroofs:red_concrete_roof","mcwroofs:light_gray_concrete_roof","simplylight:illuminant_yellow_block_on_toggle","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","mcwbiomesoplenty:pine_hedge","mcwroofs:lime_concrete_top_roof","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:bulb","simplylight:illuminant_green_block_dyed","simplylight:illuminant_magenta_block_toggle","mcwfences:oak_curved_gate","mcwbiomesoplenty:mahogany_horse_fence","aquaculture:bobber","mcwroofs:light_blue_concrete_lower_roof","simplylight:edge_light_top","mcwfences:nether_brick_pillar_wall","aquaculture:brown_mushroom_from_fish","mcwroofs:light_blue_concrete_steep_roof","mcwroofs:red_concrete_top_roof","mcwfences:granite_pillar_wall","simplylight:illuminant_brown_block_dyed","simplylight:illuminant_brown_block_on_dyed","mcwbiomesoplenty:willow_curved_gate","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","twigs:silt","mcwbiomesoplenty:redwood_hedge","mcwroofs:blue_concrete_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","ae2wtlib:quantum_bridge_card","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:icy_blue_concrete_powder","mcwbiomesoplenty:maple_curved_gate","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwroofs:orange_concrete_roof","mcwroofs:light_gray_concrete_steep_roof","mcwroofs:lime_concrete_upper_steep_roof","mcwbiomesoplenty:palm_wired_fence","simplylight:illuminant_yellow_block_toggle","mcwroofs:magenta_concrete_roof","mcwbiomesoplenty:hellbark_stockade_fence","mcwfences:diorite_railing_gate","mcwroofs:green_concrete_upper_steep_roof","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:green_concrete_roof","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","mcwbiomesoplenty:cypress_hedge","mcwroofs:gray_concrete_upper_steep_roof","mcwroofs:pink_concrete_steep_roof","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","minecraft:white_concrete_powder","aquaculture:oak_fish_mount","mcwroofs:green_concrete_upper_lower_roof","mcwbiomesoplenty:redwood_wired_fence","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","simplylight:illuminant_light_gray_block_on_dyed","mcwroofs:pink_concrete_lower_roof","mcwfences:red_sandstone_grass_topped_wall","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwbiomesoplenty:redwood_pyramid_gate","mcwfences:andesite_grass_topped_wall","mcwbiomesoplenty:empyreal_highley_gate","mcwroofs:orange_concrete_steep_roof","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","mcwroofs:orange_concrete_lower_roof","mcwbiomesoplenty:pine_horse_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:warped_wired_fence","mcwfences:bamboo_fence","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfences:jungle_stockade_fence","mcwfences:end_brick_railing_gate","mcwroofs:brown_concrete_lower_roof","simplylight:illuminant_cyan_block_dyed","aquaculture:golden_fishing_rod","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfences:oak_highley_gate","simplylight:illuminant_light_gray_block_toggle","mcwfences:diorite_pillar_wall","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwfences:sandstone_grass_topped_wall","mcwfences:bamboo_picket_fence","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfences:birch_highley_gate","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","simplylight:illuminant_lime_block_on_dyed","mcwroofs:purple_concrete_top_roof","utilitix:anvil_cart","simplylight:illuminant_orange_block_on_dyed","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","mcwfences:bamboo_pyramid_gate","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","mcwroofs:white_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_picket_fence","mcwroofs:cyan_concrete_attic_roof","minecraft:magenta_concrete_powder","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","mcwfences:acacia_picket_fence","domum_ornamentum:sand_bricks","mcwroofs:white_concrete_attic_roof","mcwbiomesoplenty:palm_stockade_fence","mcwroofs:blue_concrete_lower_roof","utilitarian:snad/snad","dyenamics:peach_concrete_powder","mcwfences:birch_curved_gate","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:snowblossom_hedge","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","mcwfences:railing_blackstone_wall","mcwfences:dark_oak_highley_gate","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwbiomesoplenty:palm_highley_gate","mcwbiomesoplenty:dead_wired_fence","simplylight:illuminant_red_block_on_toggle","minecraft:chest","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:jungle_fish_mount","aquaculture:light_hook","mcwroofs:green_concrete_steep_roof","simplylight:illuminant_blue_block_on_toggle","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:modern_sandstone_wall","aquaculture:gold_nugget_from_gold_fish","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwbiomesoplenty:dead_curved_gate","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:willow_horse_fence","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","dyenamics:conifer_concrete_powder","mcwroofs:blue_concrete_roof","mcwroofs:lime_concrete_lower_roof","mcwfences:mesh_metal_fence","mcwbiomesoplenty:empyreal_picket_fence","mcwfences:mangrove_stockade_fence","mcwbiomesoplenty:pine_curved_gate","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","simplylight:illuminant_lime_block_dyed","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:orange_concrete_top_roof","simplylight:illuminant_yellow_block_dyed","minecraft:blue_concrete_powder","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","simplylight:illuminant_block_dyed","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","mcwbiomesoplenty:orange_maple_hedge","simplylight:illuminant_black_block_on_toggle","dyenamics:bubblegum_concrete_powder","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","mcwroofs:black_concrete_top_roof","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:gray_concrete_roof","mcwbiomesoplenty:maple_horse_fence","mcwroofs:yellow_concrete_lower_roof","mcwroofs:yellow_concrete_steep_roof","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:stone_pillar_wall","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwfences:spruce_stockade_fence","mcwroofs:magenta_concrete_lower_roof","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","securitycraft:reinforced_sticky_piston","simplylight:illuminant_green_block_toggle","mcwbiomesoplenty:red_maple_hedge","simplylight:illuminant_blue_block_on_dyed","mcwfences:end_brick_pillar_wall","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","mcwbiomesoplenty:palm_hedge","mcwroofs:purple_concrete_roof","mcwfences:railing_mud_brick_wall","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","enderio:conduit_binder_composite","mcwfences:dark_oak_horse_fence","mcwfences:oak_hedge","mcwroofs:light_blue_concrete_top_roof","mcwfences:railing_sandstone_wall","mcwbiomesoplenty:willow_wired_fence","mcwroofs:magenta_concrete_attic_roof","mcwbiomesoplenty:dead_picket_fence","mcwroofs:black_concrete_upper_steep_roof","minecraft:black_concrete_powder","mcwfences:modern_diorite_wall","mcwroofs:black_concrete_upper_lower_roof","minecraft:purple_concrete_powder","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwroofs:gray_concrete_upper_lower_roof","mcwbiomesoplenty:magic_hedge","mcwbiomesoplenty:hellbark_highley_gate","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","simplylight:illuminant_black_block_on_dyed","mcwroofs:lime_concrete_steep_roof","minecraft:lead","mcwfences:spruce_highley_gate","minecraft:slime_block","mcwbiomesoplenty:maple_picket_fence","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwfences:deepslate_brick_railing_gate","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","dyenamics:fluorescent_concrete_powder","mcwfences:modern_blackstone_wall","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge"],toBeDisplayed:["mcwbiomesoplenty:magic_horse_fence","corail_woodcutter:birch_woodcutter","mcwfences:granite_railing_gate","mcwroofs:black_concrete_attic_roof","mcwroofs:magenta_concrete_top_roof","mcwfences:railing_deepslate_brick_wall","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwroofs:lime_concrete_roof","simplylight:illuminant_yellow_block_on_dyed","simplylight:illuminant_slab","mcwbiomesoplenty:redwood_highley_gate","aquaculture:cooked_fish_fillet_from_smoking","mcwfences:warped_stockade_fence","mcwfences:panelled_metal_fence_gate","simplylight:illuminant_block_on_toggle","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","aquaculture:cooked_fish_fillet","mcwfences:mangrove_picket_fence","simplylight:illuminant_magenta_block_on_dyed","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","aquaculture:planks_from_driftwood","sophisticatedstorage:storage_stack_upgrade_omega_tier_from_backpack_stack_upgrade_omega_tier","simplylight:illuminant_light_gray_block_on_toggle","mcwfences:acorn_metal_fence","mcwbiomesoplenty:umbran_horse_fence","mcwfences:birch_stockade_fence","mcwfences:dark_oak_stockade_fence","simplylight:illuminant_cyan_block_on_toggle","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","mcwbiomesoplenty:empyreal_hedge","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:hellbark_picket_fence","mcwfences:crimson_curved_gate","allthecompressed:compress/sand_1x","corail_woodcutter:crimson_woodcutter","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:yellow_maple_hedge","corail_woodcutter:bamboo_woodcutter","mcwfences:acacia_wired_fence","aquaculture:dark_oak_fish_mount","aquaculture:jellyfish_to_slimeball","simplylight:illuminant_light_blue_block_dyed","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwroofs:lime_concrete_attic_roof","mcwfences:panelled_metal_fence","mcwbiomesoplenty:fir_horse_fence","mcwroofs:gray_concrete_top_roof","mcwroofs:green_concrete_attic_roof","corail_woodcutter:spruce_woodcutter","mcwbiomesoplenty:mahogany_wired_fence","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwfences:curved_metal_fence_gate","mcwroofs:light_gray_concrete_top_roof","mcwfences:acacia_hedge","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwfences:majestic_metal_fence_gate","mcwroofs:purple_concrete_attic_roof","packingtape:tape","energymeter:meter","simplylight:rodlamp","mcwroofs:purple_concrete_upper_lower_roof","sliceanddice:slicer","mcwfences:granite_grass_topped_wall","corail_woodcutter:warped_woodcutter","mcwfences:modern_deepslate_brick_wall","aquaculture:iron_nugget_from_smelting","railcraft:water_tank_siding","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwbiomesoplenty:rainbow_birch_hedge","mcwroofs:black_concrete_steep_roof","simplylight:walllamp","mcwbiomesoplenty:fir_picket_fence","dyenamics:mint_concrete_powder","mcwfences:oak_horse_fence","mcwfences:flowering_azalea_hedge","mcwfences:railing_nether_brick_wall","mcwfences:expanded_mesh_metal_fence","mcwfences:acacia_stockade_fence","mcwfences:crimson_picket_fence","aquaculture:birch_fish_mount","mcwfences:blackstone_pillar_wall","mcwbiomesoplenty:fir_curved_gate","aquaculture:gold_nugget_from_blasting","mcwroofs:brown_concrete_roof","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwbiomesoplenty:willow_highley_gate","mcwfences:warped_horse_fence","mcwbiomesoplenty:maple_stockade_fence","mcwbiomesoplenty:mahogany_curved_gate","mcwroofs:red_concrete_upper_steep_roof","simplylight:illuminant_red_block_toggle","simplylight:illuminant_purple_block_on_toggle","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_red_block_dyed","dyenamics:aquamarine_concrete_powder","simplylight:illuminant_gray_block_dyed","simplylight:illuminant_orange_block_dyed","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwfences:dark_oak_picket_fence","mcwbiomesoplenty:pine_highley_gate","mcwfences:jungle_highley_gate","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","simplylight:illuminant_pink_block_toggle","mcwbiomesoplenty:mahogany_picket_fence","mcwfences:ornate_metal_fence","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:palm_picket_fence","mcwfences:acacia_highley_gate","mcwfences:dark_oak_hedge","aquaculture:neptunium_ingot_from_neptunium_block","immersiveengineering:crafting/grit_sand","mcwfences:warped_picket_fence","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:pine_picket_fence","mcwbiomesoplenty:hellbark_horse_fence","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:pine_wired_fence","mcwbiomesoplenty:umbran_curved_gate","mcwfences:modern_nether_brick_wall","mcwfences:bamboo_highley_gate","mcwroofs:pink_concrete_roof","mcwfences:warped_highley_gate","mcwroofs:red_concrete_upper_lower_roof","mcwfences:crimson_stockade_fence","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfences:mangrove_horse_fence","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","mcwfences:dark_oak_pyramid_gate","mcwfences:end_brick_grass_topped_wall","minecraft:sticky_piston","mcwroofs:light_blue_concrete_roof","mcwfences:railing_red_sandstone_wall","corail_woodcutter:acacia_woodcutter","mcwbiomesoplenty:jacaranda_stockade_fence","mcwfences:guardian_metal_fence","simplylight:illuminant_black_block_dyed","mcwroofs:cyan_concrete_upper_lower_roof","simplylight:illuminant_gray_block_on_toggle","aquaculture:gold_nugget_from_smelting","mcwfences:jungle_curved_gate","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:fir_stockade_fence","mcwfences:acacia_pyramid_gate","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","minecraft:green_concrete_powder","dyenamics:cherenkov_concrete_powder","mcwroofs:magenta_concrete_upper_lower_roof","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:white_concrete_steep_roof","mcwroofs:cyan_concrete_lower_roof","mcwroofs:gray_concrete_lower_roof","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","aquaculture:double_hook","mcwroofs:pink_concrete_top_roof","mcwbiomesoplenty:magic_pyramid_gate","mcwroofs:cyan_concrete_steep_roof","mcwroofs:yellow_concrete_top_roof","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwroofs:gray_concrete_steep_roof","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwbiomesoplenty:willow_pyramid_gate","minecraft:yellow_concrete_powder","mcwfences:iron_cheval_de_frise","mcwbiomesoplenty:jacaranda_highley_gate","aquaculture:tin_can_to_iron_nugget","minecraft:red_concrete_powder","mcwroofs:blue_concrete_upper_lower_roof","mcwfences:modern_red_sandstone_wall","simplylight:illuminant_orange_block_on_toggle","aquaculture:iron_fishing_rod","mcwfences:deepslate_grass_topped_wall","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwbiomesoplenty:redwood_picket_fence","dyenamics:spring_green_concrete_powder","mcwroofs:red_concrete_steep_roof","minecraft:glass","minecraft:crafting_table","simplylight:illuminant_light_blue_block_toggle","mcwroofs:brown_concrete_upper_steep_roof","mcwfences:bastion_metal_fence_gate","mcwbiomesoplenty:mahogany_pyramid_gate","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","simplylight:edge_light_bottom_from_top","mcwroofs:brown_concrete_steep_roof","mcwroofs:brown_concrete_attic_roof","mcwfences:spruce_wired_fence","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","mcwfences:gothic_metal_fence","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","mcwroofs:black_concrete_lower_roof","minecraft:light_blue_concrete_powder","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","mcwfences:deepslate_railing_gate","mcwbiomesoplenty:mahogany_stockade_fence","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","mcwfences:birch_picket_fence","mcwfences:double_curved_metal_fence","mcwfences:deepslate_pillar_wall","sophisticatedstorage:packing_tape","mcwbiomesoplenty:magic_wired_fence","mcwbiomesoplenty:empyreal_wired_fence","mcwroofs:red_concrete_attic_roof","mcwfences:nether_brick_railing_gate","simplylight:illuminant_magenta_block_dyed","simplylight:illuminant_brown_block_on_toggle","mcwfences:quartz_grass_topped_wall","simplylight:illuminant_pink_block_on_dyed","mcwfences:stone_grass_topped_wall","dyenamics:navy_concrete_powder","mcwfences:oak_picket_fence","mcwroofs:brown_concrete_top_roof","domum_ornamentum:sand_stone_bricks","mcwfences:mud_brick_grass_topped_wall","mcwroofs:orange_concrete_upper_lower_roof","mcwfences:mangrove_curved_gate","mcwbiomesoplenty:dead_hedge","mcwfences:modern_andesite_wall","mcwfences:prismarine_grass_topped_wall","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","mcwbiomesoplenty:jacaranda_wired_fence","mcwroofs:orange_concrete_upper_steep_roof","mcwfences:bamboo_horse_fence","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_horse_fence","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwroofs:lime_concrete_upper_lower_roof","mcwbiomesoplenty:magic_highley_gate","mcwroofs:cyan_concrete_roof","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","mcwbiomesoplenty:magic_picket_fence","mcwroofs:yellow_concrete_upper_lower_roof","simplylight:illuminant_green_block_on_toggle","aquaculture:spruce_fish_mount","minecraft:brown_concrete_powder","mcwroofs:light_gray_concrete_upper_steep_roof","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","mcwroofs:red_concrete_roof","mcwroofs:light_gray_concrete_roof","simplylight:illuminant_yellow_block_on_toggle","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","mcwbiomesoplenty:pine_hedge","mcwroofs:lime_concrete_top_roof","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:bulb","simplylight:illuminant_green_block_dyed","simplylight:illuminant_magenta_block_toggle","mcwfences:oak_curved_gate","mcwbiomesoplenty:mahogany_horse_fence","aquaculture:bobber","mcwroofs:light_blue_concrete_lower_roof","simplylight:edge_light_top","mcwfences:nether_brick_pillar_wall","aquaculture:brown_mushroom_from_fish","mcwroofs:light_blue_concrete_steep_roof","mcwroofs:red_concrete_top_roof","mcwfences:granite_pillar_wall","simplylight:illuminant_brown_block_dyed","simplylight:illuminant_brown_block_on_dyed","mcwbiomesoplenty:willow_curved_gate","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","twigs:silt","mcwbiomesoplenty:redwood_hedge","mcwroofs:blue_concrete_steep_roof","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","ae2wtlib:quantum_bridge_card","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:icy_blue_concrete_powder","mcwbiomesoplenty:maple_curved_gate","aquaculture:gold_hook","mcwroofs:light_blue_concrete_upper_lower_roof","mcwroofs:orange_concrete_roof","mcwroofs:light_gray_concrete_steep_roof","mcwroofs:lime_concrete_upper_steep_roof","mcwbiomesoplenty:palm_wired_fence","simplylight:illuminant_yellow_block_toggle","mcwroofs:magenta_concrete_roof","mcwbiomesoplenty:hellbark_stockade_fence","mcwfences:diorite_railing_gate","mcwroofs:green_concrete_upper_steep_roof","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:green_concrete_roof","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","simplylight:illuminant_panel","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","mcwbiomesoplenty:cypress_hedge","mcwroofs:gray_concrete_upper_steep_roof","mcwroofs:pink_concrete_steep_roof","simplylight:illuminant_gray_block_on_dyed","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","minecraft:white_concrete_powder","aquaculture:oak_fish_mount","mcwroofs:green_concrete_upper_lower_roof","mcwbiomesoplenty:redwood_wired_fence","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","simplylight:illuminant_light_gray_block_on_dyed","mcwroofs:pink_concrete_lower_roof","mcwfences:red_sandstone_grass_topped_wall","aquaculture:cooked_fish_fillet_from_campfire","mcwbiomesoplenty:pine_stockade_fence","mcwbiomesoplenty:redwood_pyramid_gate","mcwfences:andesite_grass_topped_wall","mcwbiomesoplenty:empyreal_highley_gate","mcwroofs:orange_concrete_steep_roof","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","mcwbiomesoplenty:hellbark_wired_fence","mcwroofs:orange_concrete_lower_roof","mcwbiomesoplenty:pine_horse_fence","simplylight:illuminant_cyan_block_toggle","mcwfences:warped_wired_fence","mcwfences:bamboo_fence","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfences:jungle_stockade_fence","mcwfences:end_brick_railing_gate","mcwroofs:brown_concrete_lower_roof","simplylight:illuminant_cyan_block_dyed","aquaculture:golden_fishing_rod","mcwroofs:orange_concrete_attic_roof","simplylight:illuminant_pink_block_dyed","mcwfences:oak_highley_gate","simplylight:illuminant_light_gray_block_toggle","mcwfences:diorite_pillar_wall","mcwfences:railing_end_brick_wall","aquaculture:fishing_line","mcwfences:jungle_pyramid_gate","mcwfences:sandstone_grass_topped_wall","mcwfences:bamboo_picket_fence","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfences:deepslate_brick_pillar_wall","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfences:birch_highley_gate","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","simplylight:illuminant_lime_block_on_dyed","mcwroofs:purple_concrete_top_roof","utilitix:anvil_cart","simplylight:illuminant_orange_block_on_dyed","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","mcwfences:bamboo_pyramid_gate","mcwfences:warped_pyramid_gate","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","mcwroofs:white_concrete_upper_steep_roof","mcwbiomesoplenty:jacaranda_picket_fence","mcwroofs:cyan_concrete_attic_roof","minecraft:magenta_concrete_powder","mcwfences:mangrove_hedge","mcwfences:blackstone_railing_gate","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","mcwfences:acacia_picket_fence","domum_ornamentum:sand_bricks","mcwroofs:white_concrete_attic_roof","mcwbiomesoplenty:palm_stockade_fence","mcwroofs:blue_concrete_lower_roof","utilitarian:snad/snad","dyenamics:peach_concrete_powder","mcwfences:birch_curved_gate","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:snowblossom_hedge","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","mcwfences:railing_blackstone_wall","mcwfences:dark_oak_highley_gate","mcwbiomesoplenty:willow_picket_fence","mcwfences:dark_oak_curved_gate","mcwbiomesoplenty:palm_highley_gate","mcwbiomesoplenty:dead_wired_fence","simplylight:illuminant_red_block_on_toggle","minecraft:chest","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:jungle_fish_mount","aquaculture:light_hook","mcwroofs:green_concrete_steep_roof","simplylight:illuminant_blue_block_on_toggle","mcwfences:birch_horse_fence","mcwfences:mangrove_wired_fence","mcwfences:mangrove_highley_gate","mcwfences:modern_sandstone_wall","aquaculture:gold_nugget_from_gold_fish","mcwbiomesoplenty:redwood_horse_fence","mcwbiomesoplenty:umbran_stockade_fence","mcwbiomesoplenty:dead_curved_gate","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:willow_horse_fence","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","dyenamics:conifer_concrete_powder","mcwroofs:blue_concrete_roof","mcwroofs:lime_concrete_lower_roof","mcwfences:mesh_metal_fence","mcwbiomesoplenty:empyreal_picket_fence","mcwfences:mangrove_stockade_fence","mcwbiomesoplenty:pine_curved_gate","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwfences:quartz_railing_gate","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","simplylight:illuminant_lime_block_dyed","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:orange_concrete_top_roof","simplylight:illuminant_yellow_block_dyed","minecraft:blue_concrete_powder","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwfences:red_sandstone_railing_gate","simplylight:illuminant_block_dyed","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","mcwfences:sandstone_pillar_wall","mcwbiomesoplenty:orange_maple_hedge","simplylight:illuminant_black_block_on_toggle","dyenamics:bubblegum_concrete_powder","minecraft:light_gray_concrete_powder","mcwbiomesoplenty:dead_stockade_fence","mcwroofs:black_concrete_top_roof","mcwfences:crimson_pyramid_gate","mcwfences:stone_brick_railing_gate","mcwroofs:gray_concrete_roof","mcwbiomesoplenty:maple_horse_fence","mcwroofs:yellow_concrete_lower_roof","mcwroofs:yellow_concrete_steep_roof","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","mcwfences:jungle_wired_fence","mcwfences:stone_pillar_wall","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwfences:spruce_stockade_fence","mcwroofs:magenta_concrete_lower_roof","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","securitycraft:reinforced_sticky_piston","simplylight:illuminant_green_block_toggle","mcwbiomesoplenty:red_maple_hedge","simplylight:illuminant_blue_block_on_dyed","mcwfences:end_brick_pillar_wall","mcwbiomesoplenty:maple_wired_fence","mcwbiomesoplenty:maple_pyramid_gate","simplylight:illuminant_slab_from_panel","simplylight:illuminant_block","mcwbiomesoplenty:palm_hedge","mcwroofs:purple_concrete_roof","mcwfences:railing_mud_brick_wall","mcwbiomesoplenty:empyreal_curved_gate","corail_woodcutter:jungle_woodcutter","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","aquaculture:neptunium_ingot_from_nuggets","mcwbiomesoplenty:dead_highley_gate","enderio:conduit_binder_composite","mcwfences:dark_oak_horse_fence","mcwfences:oak_hedge","mcwroofs:light_blue_concrete_top_roof","mcwfences:railing_sandstone_wall","mcwbiomesoplenty:willow_wired_fence","mcwroofs:magenta_concrete_attic_roof","mcwbiomesoplenty:dead_picket_fence","mcwroofs:black_concrete_upper_steep_roof","minecraft:black_concrete_powder","mcwfences:modern_diorite_wall","mcwroofs:black_concrete_upper_lower_roof","minecraft:purple_concrete_powder","simplylight:illuminant_purple_block_dyed","simplylight:illuminant_block_toggle","mcwroofs:gray_concrete_upper_lower_roof","mcwbiomesoplenty:magic_hedge","mcwbiomesoplenty:hellbark_highley_gate","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_picket_fence","mcwbiomesoplenty:hellbark_curved_gate","mcwfences:spruce_curved_gate","mcwroofs:light_blue_concrete_upper_steep_roof","simplylight:illuminant_black_block_on_dyed","mcwroofs:lime_concrete_steep_roof","minecraft:lead","mcwfences:spruce_highley_gate","minecraft:slime_block","mcwbiomesoplenty:maple_picket_fence","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwfences:modern_end_brick_wall","mcwroofs:black_concrete_roof","mcwbiomesoplenty:maple_highley_gate","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwfences:deepslate_brick_railing_gate","mcwbiomesoplenty:hellbark_pyramid_gate","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwfences:railing_quartz_wall","mcwfences:jungle_horse_fence","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","dyenamics:fluorescent_concrete_powder","mcwfences:modern_blackstone_wall","simplylight:illuminant_light_gray_block_dyed","mcwfences:jungle_hedge"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:8377,warning_level:0}}