{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:100,Id:241,ShowIcon:1b,ShowParticles:1b,"forge:id":"mahoutsukai:bleeding"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"}],Air:300s,Attributes:[{Base:3.0d,Name:"forge:entity_reach"},{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:4.5d,Name:"forge:block_reach"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"artifacts:steadfast_spikes_knockback_resistance",Operation:0,UUID:[I;-706276632,1065698154,-1122152339,2032106173]}],Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:4.0d,Modifiers:[{Amount:0.4d,Name:"artifacts:feral_claws_attack_speed_bonus",Operation:0,UUID:[I;-**********,-**********,-**********,**********]}],Name:"minecraft:generic.attack_speed"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:1.0d,Modifiers:[{Amount:4.0d,Name:"artifacts:power_glove_attack_damage_bonus",Operation:0,UUID:[I;308939635,-**********,-**********,-702737548]}],Name:"minecraft:generic.attack_damage"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:0.0d,Name:"attributeslib:creative_flight"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:27.461731f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:1b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:obsidian_skull"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{CachedModifiers:[],Cosmetics:{Items:[],Size:32},DropRule:"DEFAULT",HasCosmetic:0b,PersistentModifiers:[{Amount:30.0d,Name:"legacy",Operation:0,UUID:[I;185510845,1109413535,-1157942256,228769150]}],RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1},{Render:0b,Slot:2},{Render:0b,Slot:3},{Render:0b,Slot:4},{Render:0b,Slot:5},{Render:0b,Slot:6},{Render:0b,Slot:7},{Render:0b,Slot:8},{Render:0b,Slot:9},{Render:0b,Slot:10},{Render:0b,Slot:11},{Render:0b,Slot:12},{Render:0b,Slot:13},{Render:0b,Slot:14},{Render:0b,Slot:15},{Render:0b,Slot:16},{Render:0b,Slot:17},{Render:0b,Slot:18},{Render:0b,Slot:19},{Render:0b,Slot:20},{Render:0b,Slot:21},{Render:0b,Slot:22},{Render:0b,Slot:23},{Render:0b,Slot:24},{Render:0b,Slot:25},{Render:0b,Slot:26},{Render:0b,Slot:27},{Render:0b,Slot:28},{Render:0b,Slot:29},{Render:0b,Slot:30},{Render:0b,Slot:31}],Size:32},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:night_vision_goggles"},{Count:1b,Slot:1,id:"artifacts:plastic_drinking_hat"},{Count:1b,Slot:2,id:"artifacts:novelty_drinking_hat"},{Count:1b,Slot:3,id:"artifacts:superstitious_hat"},{Count:1b,Slot:4,id:"artifacts:anglers_hat"},{Count:1b,Slot:5,id:"artifacts:lucky_scarf"},{Count:1b,Slot:6,id:"artifacts:cross_necklace",tag:{CanApplyBonus:1b}},{Count:1b,Slot:7,id:"artifacts:antidote_vessel"},{Count:1b,Slot:8,id:"artifacts:digging_claws"},{Count:1b,Slot:9,id:"artifacts:feral_claws"},{Count:1b,Slot:10,id:"artifacts:power_glove"},{Count:1b,Slot:11,id:"artifacts:vampiric_glove"},{Count:1b,Slot:12,id:"artifacts:golden_hook"},{Count:1b,Slot:13,id:"artifacts:pickaxe_heater"},{Count:1b,Slot:14,id:"artifacts:steadfast_spikes"}],Size:32},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1},{Render:0b,Slot:2},{Render:0b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:netherite_backpack",tag:{contentsUuid:[I;390048106,-673756075,-1938614503,949916074],inventorySlots:120,openTabId:0,renderInfo:{upgradeItems:[{Count:1b,id:"sophisticatedbackpacks:crafting_upgrade"},{Count:1b,id:"sophisticatedbackpacks:advanced_feeding_upgrade",tag:{feedAtHungerLevel:"any",filters:{Items:[{Count:1b,Slot:0,id:"artifacts:eternal_steak"},{Count:1b,Slot:1,id:"artifacts:everlasting_beef"}],Size:16},isAllowList:1b}},{Count:1b,id:"sophisticatedbackpacks:advanced_magnet_upgrade",tag:{filterByStorage:1b}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_omega_tier"}]},upgradeSlots:7}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:150,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:102951},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:0b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:20},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:gold_ingot_from_gold_block",ItemStack:{Count:9b,id:"minecraft:gold_ingot"}}],SelectedRecipe:"minecraft:gold_ingot_from_gold_block"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:10,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:1b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f},"solcarrot:food":{foodList:["artifacts:eternal_steak"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:4,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:274877898813L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:37383395778533L,UID:[I;**********,-*********,-**********,-**********]},{FromDim:"minecraft:overworld",FromPos:-824633905086L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:274877898813L,UID:[I;**********,**********,-**********,-*********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},apoth_reforge_seed:997815291,"quark:trying_crawl":0b,sophisticatedBackpackSettings:{}},Health:20.0f,HurtByTimestamp:87985,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"allthemodium:alloy_paxel",tag:{Enchantments:[{id:"apotheosis:miners_fervor",lvl:5s},{id:"minecraft:fortune",lvl:8s},{id:"evilcraft:life_stealing",lvl:6s},{id:"apotheosis:scavenger",lvl:3s},{id:"minecraft:looting",lvl:8s}],RepairCost:31,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":1.0f,"apotheosis:breaker/attribute/experienced":1.0f,"apotheosis:breaker/attribute/lengthy":1.0f,"apotheosis:breaker/attribute/lucky":1.0f,"apotheosis:breaker/special/enlightened":1.0f,"apotheosis:breaker/special/omnetic":1.0f,"apotheosis:telepathic":1.0f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:ancient"},gem:"apotheosis:overworld/earth"}},{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:ancient"},gem:"apotheosis:the_end/endersurge"}},{Count:0b,id:"minecraft:air",tag:{Charged:0b}}],name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/special/omnetic"},"",{"translate":"affix.apotheosis:breaker/attribute/experienced.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-421150767,-835042629,-1465530726,946707014]]}}},{Count:1b,Slot:1b,id:"minecraft:bucket"},{Count:18b,Slot:2b,id:"powah:uraninite"},{Count:25b,Slot:3b,id:"twigs:pebble"},{Count:27b,Slot:9b,id:"minecraft:oak_planks"},{Count:10b,Slot:10b,id:"minecraft:glass"},{Count:1b,Slot:100b,id:"minecraft:iron_boots",tag:{Damage:12,Enchantments:[{id:"minecraft:fire_protection",lvl:2s}],affix_data:{affixes:{"apotheosis:armor/attribute/stalwart":0.65391093f,"apotheosis:armor/dmg_reduction/feathery":0.47197884f,"apotheosis:durable":0.13f,"irons_spellbooks:armor/attribute/cooldown":0.7432037f,"irons_spellbooks:armor/attribute/spell_resist":0.2161668f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist"},"",{"translate":"affix.apotheosis:armor/attribute/stalwart.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;-248386340,-113817461,-1407838129,-1437069820]]}}},{Count:1b,Slot:101b,id:"minecraft:iron_leggings",tag:{Damage:3,Enchantments:[{id:"minecraft:fire_protection",lvl:3s}],affix_data:{affixes:{"apotheosis:armor/attribute/ironforged":0.8978907f,"irons_spellbooks:armor/attribute/cooldown":0.47664762f,"irons_spellbooks:armor/attribute/spell_resist":0.18819135f},name:'{"italic":false,"color":"#33FF33","translate":"misc.apotheosis.affix_name.four","with":["Sandra\'s",{"translate":"affix.apotheosis:armor/attribute/ironforged"},"",{"translate":"affix.irons_spellbooks:armor/attribute/cooldown.suffix"}]}',rarity:"apotheosis:uncommon",sockets:1,uuids:[[I;1270799571,1369000677,-1782250961,1241766737]]},apoth_boss:1b,display:{Name:'{"extra":[{"text":"Pants"}],"text":""}'}}},{Count:1b,Slot:102b,id:"minecraft:iron_chestplate",tag:{Damage:0,affix_data:{affixes:{"apotheosis:armor/attribute/gravitational":0.21295738f,"apotheosis:armor/dmg_reduction/blast_forged":0.43555927f,"apotheosis:durable":0.13f,"irons_spellbooks:armor/attribute/mana":0.73035914f,"irons_spellbooks:armor/attribute/spell_power":0.096836746f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/gravitational"},"",{"translate":"affix.irons_spellbooks:armor/attribute/mana.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;-1046819102,1467105548,-1553607551,-779806150]]},apoth_rchest:1b}}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:overworld",pos:[I;-4,66,-45]},Motion:[0.0d,-0.06272000098705292d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[-1092.300518523459d,-43.0d,-315.91144316748023d],Railways_DataVersion:2,Rotation:[-91.907684f,90.0f],Score:4692,SelectedItemSlot:1,SleepTimer:0s,Spigot.ticksLived:102939,UUID:[I;-1761466194,-1162329286,-2122966518,-832933639],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-302365698916356L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:48,XpP:0.25745285f,XpSeed:0,XpTotal:4692,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752060955368L,keepLevel:0b,lastKnownName:"Elle_Fanning",lastPlayed:1752066590002L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:1.8966217f,foodLevel:17,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","handcrafted:spider_trophy","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:granite_attic_roof","mcwroofs:sandstone_upper_lower_roof","chimes:copper_chimes","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","mcwpaths:granite_windmill_weave_slab","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","xnet:connector_green","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:oak_window","create:crafting/appliances/crafting_blueprint","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","supplementaries:flint_block","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","mcwwindows:oak_plank_pane_window","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","reliquary:mob_charm_fragments/cave_spider","mcwbridges:granite_bridge_stair","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","railcraft:controller_circuit","mcwpaths:granite_running_bond_slab","twigs:polished_rhyolite_stonecutting","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","supplementaries:candle_holders/candle_holder_green","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","aether:skyroot_fletching_table","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","mcwroofs:purple_concrete_attic_roof","energymeter:meter","minecraft:polished_granite_from_granite_stonecutting","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","xnet:connector_red_dye","minecolonies:shapetool","supplementaries:bubble_blower","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","supplementaries:crank","delightful:knives/tin_knife","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","mcwwindows:andesite_louvered_shutter","alltheores:iron_plate","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","mcwpaths:cobblestone_dumble_paving","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","cfm:crimson_kitchen_sink_dark","nethersdelight:golden_machete","tombstone:ankh_of_prayer","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","cfm:black_kitchen_drawer","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwpaths:andesite_windmill_weave_slab","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:oak_kitchen_counter","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","mcwpaths:cobbled_deepslate_windmill_weave_path","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","handcrafted:oak_bench","mcwfurnitures:oak_striped_chair","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:angel_block","occultism:crafting/magic_lamp_empty","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","railcraft:brass_ingot_crafted_with_ingots","dyenamics:spring_green_concrete_powder","mcwpaths:sandstone_running_bond_stairs","create:granite_from_stone_types_granite_stonecutting","cfm:jungle_cabinet","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","twigs:mossy_bricks_from_vine","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","create:industrial_iron_block_from_ingots_iron_stonecutting","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","supplementaries:daub_cross_brace","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwbiomesoplenty:umbran_plank_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:pine_plank_window2","mcwbiomesoplenty:hellbark_four_window","mcwwindows:warped_louvered_shutter","xnet:connector_routing","mcwbiomesoplenty:palm_plank_window2","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","utilitix:directional_highspeed_rail","mcwwindows:oak_four_window","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","mcwlights:golden_chain","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","littlelogistics:seater_car","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","create:cut_granite_bricks_from_stone_types_granite_stonecutting","mcwbiomesoplenty:stripped_dead_pane_window","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","cfm:warped_mail_box","securitycraft:reinforced_diorite","cfm:green_grill","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","minecraft:oak_pressure_plate","minecraft:stone_brick_stairs","cfm:orange_grill","bigreactors:turbine/basic/activefluidport_forge","cfm:brown_grill","mcwwindows:quartz_window","mcwwindows:dark_oak_shutter","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","cfm:jungle_desk","mcwroofs:oak_roof","reliquary:mob_charm_fragments/creeper","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","mcwtrpdoors:oak_barred_trapdoor","cfm:gray_kitchen_drawer","mcwroofs:light_blue_concrete_lower_roof","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","railcraft:lead_gear","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","delightful:knives/osmium_knife","mcwfences:oak_wired_fence","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","cfm:mangrove_kitchen_sink_dark","mcwfurnitures:stripped_oak_double_drawer_counter","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","mcwdoors:oak_four_panel_door","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","minecraft:granite_stairs_from_granite_stonecutting","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","cfm:green_kitchen_sink","mcwfences:diorite_railing_gate","alltheores:tin_rod","mcwroofs:gutter_base_black","mcwfurnitures:stripped_jungle_chair","mcwroofs:green_concrete_roof","mcwwindows:white_mosaic_glass","minecraft:redstone_torch","cfm:acacia_kitchen_sink_dark","mcwroofs:jungle_upper_steep_roof","minecraft:polished_andesite_slab_from_andesite_stonecutting","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","create:crafting/materials/andesite_alloy","mcwwindows:andesite_four_window","minecraft:granite_slab","mcwtrpdoors:jungle_swamp_trapdoor","mcwlights:golden_low_candle_holder","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","supplementaries:cog_block","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","pneumaticcraft:armor_upgrade","mcwwindows:birch_plank_pane_window","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","twigs:rocky_dirt","mcwpaths:granite_flagstone_stairs","bloodmagic:sacrificial_dagger","mcwwindows:magenta_mosaic_glass_pane","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","mcwbridges:bridge_torch","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","mcwpaths:andesite_crystal_floor_slab","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwwindows:oak_blinds","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwdoors:oak_cottage_door","create:crafting/kinetics/fluid_pipe","minecolonies:doublefern","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","create:small_granite_brick_slab_from_stone_types_granite_stonecutting","delightful:food/baklava","mcwpaths:red_sand_path_block","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","cfm:light_blue_cooler","railcraft:signal_circuit","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","undergarden:gloom_o_lantern","sophisticatedstorage:oak_chest_from_vanilla_chest","minecraft:sandstone_slab","mcwwindows:blackstone_four_window","mcwwindows:birch_window","mcwfences:acacia_picket_fence","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","twigs:oak_table","simplylight:illuminant_purple_block_toggle","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","cfm:pink_cooler","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:oak_wardrobe","mcwpaths:granite_flagstone_path","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwlights:upgraded_torch","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwwindows:jungle_blinds","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","mcwwindows:stripped_dark_oak_log_window2","mcwpaths:sandstone_windmill_weave","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","bloodmagic:blood_altar","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","mcwpaths:granite_diamond_paving","forbidden_arcanus:clibano_core","cfm:purple_kitchen_drawer","mcwroofs:orange_concrete_top_roof","additionallanterns:granite_chain","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","minecraft:blue_stained_glass","create:small_granite_brick_wall_from_stone_types_granite_stonecutting","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwpaths:granite_honeycomb_paving","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","minecraft:oak_sign","mcwwindows:stone_pane_window","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwroofs:gutter_base_magenta","mcwbiomesoplenty:fir_plank_window2","domum_ornamentum:cobblestone_extra","mcwbridges:balustrade_andesite_bridge","additionallanterns:emerald_lantern","handcrafted:oak_counter","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","create:crafting/kinetics/hose_pulley","eidolon:smooth_stone_masonry_stonecutter_0","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","minecolonies:doublegrass","dyenamics:rose_stained_glass","farmersdelight:cooking/hot_cocoa","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:purple_concrete_powder","minecraft:sugar_from_sugar_cane","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","xnet:advanced_connector_yellow","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","cfm:stripped_oak_chair","cfm:orange_trampoline","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","wirelesschargers:basic_wireless_player_charger","mcwroofs:lime_concrete_steep_roof","mcwdoors:jungle_swamp_door","mcwwindows:oak_shutter","cfm:rock_path","cfm:light_blue_kitchen_sink","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","reliquary:mob_charm_fragments/zombified_piglin","mcwwindows:warped_planks_four_window","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","reliquary:mob_charm_fragments/zombie","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","mcwroofs:oak_planks_attic_roof","rftoolsutility:moduleplus_template","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","mcwbridges:stone_bridge_pier","deepresonance:resonating_plate_block","naturescompass:natures_compass","railcraft:radio_circuit","mcwbiomesoplenty:dead_plank_four_window","mcwwindows:diorite_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","minecraft:wooden_axe","mcwpaths:cobbled_deepslate_clover_paving","cfm:stripped_oak_desk","mcwbiomesoplenty:mahogany_plank_window2","mcwtrpdoors:oak_glass_trapdoor","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwbiomesoplenty:willow_four_window","mcwlights:golden_double_candle_holder","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","domum_ornamentum:black_floating_carpet","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","handcrafted:oak_shelf","minecraft:cut_sandstone_from_sandstone_stonecutting","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","create:crafting/logistics/powered_toggle_latch","supplementaries:flags/flag_lime","mcwpaths:granite_windmill_weave","mcwpaths:granite_windmill_weave_path","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","create:crafting/kinetics/copper_valve_handle","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:mangrove_louvered_shutter","mcwwindows:warped_blinds","supplementaries:flags/flag_magenta","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","minecraft:diamond_axe","mcwpaths:granite_running_bond_path","securitycraft:reinforced_pink_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_red","mcwroofs:granite_top_roof","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","securitycraft:alarm","mcwfurnitures:oak_counter","rftoolsutility:charged_porter","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","additionallanterns:bricks_lantern","create:crafting/materials/andesite_alloy_from_zinc","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","mcwroofs:oak_top_roof","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwpaths:sandstone_honeycomb_paving","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwwindows:stone_window","create:copper_shingles_from_ingots_copper_stonecutting","mcwfences:granite_grass_topped_wall","everythingcopper:copper_sword","mcwwindows:birch_shutter","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","create:polished_cut_granite_wall_from_stone_types_granite_stonecutting","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","create:cut_granite_stairs_from_stone_types_granite_stonecutting","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","mcwroofs:cobblestone_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","additionallanterns:gold_lantern","securitycraft:reinforced_mossy_cobblestone_from_vine","mcwdoors:oak_bark_glass_door","sfm:water_tank","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwfences:dark_oak_picket_fence","alchemistry:compactor","travelersbackpack:sandstone","cfm:jungle_upgraded_fence","minecraft:dropper","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwpaths:granite_crystal_floor_path","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","create:cut_granite_slab_from_stone_types_granite_stonecutting","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","mcwroofs:jungle_roof","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwpaths:andesite_running_bond","create:crafting/kinetics/rose_quartz_lamp","immersiveengineering:crafting/plate_nickel_hammering","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","minecraft:torch","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwwindows:mangrove_window","mcwwindows:quartz_pane_window","cfm:oak_desk","mcwtrpdoors:metal_trapdoor","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","minecraft:shield","mcwfurnitures:jungle_double_drawer_counter","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","supplementaries:daub_frame","pneumaticcraft:minigun_upgrade","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:banner/mint_banner","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:sandstone_wall","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:stripped_mahogany_log_window","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","cfm:pink_trampoline","mcwbridges:balustrade_sandstone_bridge","securitycraft:briefcase","mcwwindows:green_mosaic_glass","alltheores:nickel_rod","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:stripped_oak_large_drawer","mcwlights:copper_triple_candle_holder","handcrafted:andesite_corner_trim","cfm:white_grill","minecraft:iron_nugget_from_smelting","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","securitycraft:motion_activated_light","mcwpaths:andesite_crystal_floor_stairs","mcwfences:double_curved_metal_fence","mcwroofs:sandstone_lower_roof","mcwwindows:oak_plank_four_window","travelersbackpack:hose_nozzle","securitycraft:claymore","cfm:red_cooler","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","twigs:polished_tuff_stonecutting","supplementaries:lock_block","reliquary:mob_charm_fragments/skeleton","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","mcwwindows:stripped_acacia_log_four_window","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","domum_ornamentum:green_floating_carpet","mcwwindows:cherry_plank_window2","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","mcwpaths:granite_crystal_floor","minecraft:carrot_on_a_stick","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:stone_window2","mcwwindows:metal_four_window","minecraft:oak_button","minecraft:granite_wall","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","handcrafted:granite_corner_trim","minecraft:shears","mcwbiomesoplenty:maple_window","supplementaries:flags/flag_blue","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","xnet:advanced_connector_yellow_dye","allthecompressed:compress/cobblestone_1x","additionallanterns:diamond_lantern","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","mcwwindows:quartz_window2","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","securitycraft:reinforced_lime_stained_glass_pane_from_glass","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","domum_ornamentum:orange_floating_carpet","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","expatternprovider:silicon_block","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","croptopia:nougat","itemcollectors:basic_collector","minecraft:loom","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","mcwtrpdoors:oak_bark_trapdoor","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","minecraft:arrow","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","minecraft:oak_fence_gate","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwroofs:sandstone_attic_roof","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","buildinggadgets2:gadget_copy_paste","handcrafted:golden_wide_pot","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","mcwpaths:granite_running_bond_stairs","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","mcwpaths:granite_flagstone","create:cut_granite_brick_wall_from_stone_types_granite_stonecutting","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","alltheores:nickel_plate","supplementaries:flags/flag_green","cfm:stripped_jungle_mail_box","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwwindows:acacia_plank_four_window","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","mcwlights:copper_chandelier","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","mcwbiomesoplenty:stripped_palm_log_four_window","sophisticatedstorage:generic_chest","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","mcwroofs:gutter_base_light_blue","securitycraft:reinforced_birch_fence_gate","mcwpaths:andesite_crystal_floor_path","additionallanterns:diorite_lantern","minecraft:dried_kelp_from_campfire_cooking","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:granite_bridge","minecraft:diamond_block","mcwroofs:deepslate_roof","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","cfm:brown_cooler","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwwindows:dark_prismarine_parapet","minecraft:yellow_stained_glass","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwpaths:granite_crystal_floor_stairs","utilitix:experience_crystal","mcwwindows:red_sandstone_pane_window","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","cfm:green_cooler","handcrafted:goat_trophy","bigreactors:blasting/graphite_from_coal","minecraft:respawn_anchor","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","paraglider:paraglider","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbridges:jungle_bridge_pier","mcwtrpdoors:oak_barrel_trapdoor","mcwbiomesoplenty:willow_pane_window","buildinggadgets2:gadget_exchanging","aether:flint_and_steel_repairing","minecraft:lapis_block","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","supplementaries:flower_box","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","minecraft:iron_axe","utilitarian:snad/snad","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","mcwbiomesoplenty:empyreal_plank_window2","minecraft:jukebox","securitycraft:blacklist_module","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","create:layered_granite_from_stone_types_granite_stonecutting","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","minecraft:glass_bottle","tombstone:blue_marble","mcwfences:mesh_metal_fence","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","minecraft:polished_granite","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","mcwbiomesoplenty:magic_plank_window2","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwwindows:spruce_four_window","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","mcwpaths:granite_clover_paving","reliquary:uncrafting/spider_eye","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alltheores:zinc_plate","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","mcwwindows:prismarine_pane_window","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","supplementaries:slice_map","mcwroofs:cobblestone_lower_roof","handcrafted:oak_pillar_trim","occultism:crafting/butcher_knife","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","securitycraft:reinforced_sticky_piston","aether:iron_pendant","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","cfm:gray_grill","supplementaries:flags/flag_purple","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:stripped_redwood_log_window","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","tombstone:bone_needle","minecraft:stone_brick_wall","handcrafted:golden_thick_pot","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","cfm:warped_kitchen_sink_light","delightful:knives/nickel_knife","mcwfurnitures:stripped_jungle_coffee_table","cfm:cyan_grill","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_fir_log_window2","cfm:stripped_spruce_mail_box","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","aquaculture:gold_fillet_knife","dyenamics:banner/navy_banner","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","cfm:gray_kitchen_sink","minecraft:paper","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwdoors:oak_stable_door","cfm:dark_oak_kitchen_sink_dark","twigs:smooth_stone_bricks","mcwwindows:jungle_louvered_shutter","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:oak_upgraded_gate","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","croptopia:cashew_chicken","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","minecraft:bucket","handcrafted:salmon_trophy","mcwroofs:grass_upper_lower_roof","dyenamics:fluorescent_concrete_powder","supplementaries:speaker_block","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","farmersdelight:chicken_sandwich","minecraft:polished_andesite","securitycraft:reinforced_cherry_fence","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwpaths:andesite_honeycomb_paving","mcwbridges:sandstone_bridge","mcwbiomesoplenty:redwood_four_window","mcwwindows:birch_log_parapet","mcwbiomesoplenty:magic_plank_window","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","mcwroofs:black_concrete_attic_roof","minecraft:charcoal","additionallanterns:copper_lantern","mcwlights:golden_small_chandelier","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","domum_ornamentum:purple_floating_carpet","mcwwindows:stripped_warped_pane_window","twigs:polished_tuff_bricks_from_tuff_stonecutting","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:dead_pane_window","mcwwindows:crimson_blinds","mcwwindows:diorite_window2","sophisticatedstorage:storage_stack_upgrade_omega_tier_from_backpack_stack_upgrade_omega_tier","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwfurnitures:oak_bookshelf","xnet:connector_blue_dye","croptopia:roasted_nuts_from_campfire","utilitarian:utility/jungle_logs_to_doors","alltheores:lead_rod","mcwroofs:gutter_base_blue","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","mcwroofs:granite_roof","mcwwindows:oak_log_parapet","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","littlelogistics:spring","securitycraft:reinforced_bamboo_fence","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","mcwroofs:gutter_base","additionallanterns:gold_chain","mcwroofs:gutter_middle_purple","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:golden_apple","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","minecraft:diamond_pickaxe","mcwpaths:cobbled_deepslate_flagstone_slab","domum_ornamentum:blockbarreldeco_onside","mcwpaths:sandstone_dumble_paving","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","mcwroofs:andesite_lower_roof","mcwroofs:deepslate_attic_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwbridges:andesite_bridge","mcwwindows:dark_prismarine_pane_window","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwfurnitures:jungle_table","mcwbiomesoplenty:umbran_plank_window2","mcwdoors:metal_reinforced_door","create:tuff_from_stone_types_tuff_stonecutting","domum_ornamentum:blue_cobblestone_extra","rftoolsbuilder:green_shield_template_block","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","securitycraft:reinforced_glass_pane","cfm:mangrove_kitchen_sink_light","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","minecraft:chiseled_sandstone_from_sandstone_stonecutting","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","mcwbiomesoplenty:maple_plank_window","delightful:campfire/roasted_acorn","mcwfences:warped_horse_fence","twilightforest:canopy_boat","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_3","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_4","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","handcrafted:granite_pillar_trim","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","securitycraft:harming_module","silentgear:bort_block","minecraft:golden_boots","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","minecraft:andesite_wall_from_andesite_stonecutting","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwbiomesoplenty:jacaranda_four_window","minecraft:conduit","pneumaticcraft:magnet_upgrade","cfm:pink_grill","mcwbridges:iron_bridge_pier","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:mangrove_blinds","mcwroofs:sandstone_roof","minecraft:sticky_piston","mcwwindows:granite_window2","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","cfm:oak_kitchen_sink_light","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwwindows:diorite_pane_window","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwroofs:gutter_base_white","mcwpaths:granite_crystal_floor_slab","minecraft:oak_stairs","additionallanterns:stone_lantern","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","mcwfurnitures:oak_desk","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","mcwwindows:jungle_log_parapet","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwwindows:white_mosaic_glass_pane","mcwwindows:purple_mosaic_glass","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:fishing_rod","xnet:connector_yellow_dye","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","delightful:knives/silver_knife","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","mcwwindows:acacia_blinds","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwpaths:granite_windmill_weave_stairs","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwroofs:roofing_hammer","domum_ornamentum:gray_floating_carpet","cfm:stripped_jungle_park_bench","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","create:copper_tiles_from_ingots_copper_stonecutting","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","minecraft:granite_wall_from_granite_stonecutting","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","minecraft:granite_stairs","sophisticatedstorage:packing_tape","mcwwindows:light_blue_mosaic_glass","silentgear:fine_silk","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:andesite_pane_window","mcwwindows:acacia_plank_pane_window","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","dyenamics:banner/honey_banner","railcraft:tin_gear","mcwlights:double_street_lamp","minecraft:tnt","mcwbiomesoplenty:stripped_maple_pane_window","minecraft:mossy_stone_bricks_from_vine","rftoolsstorage:storage_control_module","minecraft:andesite_slab_from_andesite_stonecutting","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","mcwpaths:granite_square_paving","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","minecraft:flint_and_steel","deepresonance:radiation_monitor","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","create:cut_granite_wall_from_stone_types_granite_stonecutting","cfm:magenta_kitchen_sink","mcwfurnitures:jungle_drawer_counter","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","xnet:advanced_connector_blue_dye","cfm:spruce_kitchen_sink_light","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","xnet:netcable_green_dye","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:cherry_plank_window","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:maple_plank_window2","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwpaths:andesite_flagstone","mcwdoors:oak_bamboo_door","mcwbiomesoplenty:dead_plank_window2","travelersbackpack:creeper","xnet:advanced_connector_red_dye","minecraft:golden_axe","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","immersiveengineering:crafting/wire_lead","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:stone_tile","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:willow_plank_window","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","twigs:rhyolite_slab","mcwroofs:andesite_top_roof","immersiveengineering:crafting/stick_iron","create:polished_cut_granite_from_stone_types_granite_stonecutting","minecraft:granite_slab_from_granite_stonecutting","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","minecraft:glass_pane","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","supplementaries:timber_brace","mcwpaths:stone_crystal_floor_path","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","minecraft:golden_sword","mcwroofs:lime_concrete_upper_steep_roof","securitycraft:reinforced_brown_stained_glass","cfm:lime_grill","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","create:polished_cut_granite_slab_from_stone_types_granite_stonecutting","minecraft:clay","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","handcrafted:golden_medium_pot","enderio:wood_gear","supplementaries:bed_from_feather_block","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","mcwpaths:andesite_basket_weave_paving","sfm:fancy_to_cable","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwpaths:granite_running_bond","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","minecraft:mossy_cobblestone_from_vine","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","mcwwindows:acacia_window","cfm:orange_kitchen_drawer","minecraft:cherry_boat","silentgear:stone_rod","dyenamics:icy_blue_stained_glass","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","mcwroofs:orange_concrete_lower_roof","cfm:oak_hedge","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","create:crafting/kinetics/steam_engine","cfm:spruce_mail_box","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","silentgear:bort_from_block","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","mcwlights:pink_paper_lamp","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","xnet:connector_green_dye","utilitix:hand_bell","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","alltheores:zinc_rod","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","aether:iron_gloves","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","minecraft:chiseled_stone_bricks","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","tombstone:white_marble","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","pneumaticcraft:stomp_upgrade","mcwpaths:gravel_path_block","minecraft:polished_granite_stairs_from_granite_stonecutting","mcwfurnitures:jungle_cupboard_counter","mcwwindows:crimson_stem_parapet","supplementaries:lapis_bricks","croptopia:chocolate","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","mcwbridges:glass_bridge_pier","create:small_granite_brick_stairs_from_stone_types_granite_stonecutting","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","delightful:smelting/roasted_acorn","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","minecraft:brush","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","aether:iron_boots_repairing","mcwfurnitures:oak_kitchen_cabinet","minecraft:andesite_wall","sfm:cable","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","rftoolsutility:module_template","mcwroofs:deepslate_upper_steep_roof","tombstone:impregnated_diamond","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwbiomesoplenty:palm_pyramid_gate","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwpaths:granite_basket_weave_paving","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","supplementaries:pulley","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:jungle_fish_mount","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","allthecompressed:compress/glass_1x","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","mcwlights:crimson_ceiling_fan_light","alltheores:zinc_gear","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","biomesoplenty:dead_boat","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","supplementaries:flags/flag_orange","travelersbackpack:standard","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","mcwroofs:oak_planks_upper_steep_roof","create:copper_ladder_from_ingots_copper_stonecutting","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","alltheores:osmium_rod","simplylight:illuminant_brown_block_toggle","minecraft:oak_fence","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwbiomesoplenty:empyreal_window2","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","securitycraft:universal_key_changer","mcwwindows:jungle_plank_pane_window","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","sophisticatedstorage:oak_limited_barrel_1","mcwroofs:gray_concrete_roof","pneumaticcraft:range_upgrade","constructionwand:diamond_wand","sophisticatedstorage:oak_limited_barrel_2","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","mcwwindows:warped_stem_four_window","securitycraft:sentry","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","everythingcopper:copper_leggings","mcwroofs:gutter_base_pink","mcwbridges:andesite_bridge_stair","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","minecraft:deepslate","pneumaticcraft:ender_visor_upgrade","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwwindows:spruce_plank_window","supplementaries:checker","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","silentgear:fine_silk_cloth","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","minecraft:crossbow","cfm:cyan_cooler","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","railcraft:silver_gear","mcwbridges:cobblestone_bridge_pier","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","create:cut_granite_brick_stairs_from_stone_types_granite_stonecutting","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:orange_paper_lamp","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","mcwfences:spruce_picket_fence","croptopia:roasted_smoking","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:white_stained_glass","minecraft:slime_block","xnet:advanced_connector_green_dye","mcwroofs:granite_upper_lower_roof","securitycraft:taser","create:copycat_panel_from_ingots_zinc_stonecutting","supplementaries:soap","mcwwindows:iron_shutter","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","travelersbackpack:emerald","cfm:dark_oak_kitchen_drawer","croptopia:trail_mix","mcwbiomesoplenty:pine_plank_window","farmersdelight:cutting_board","create:andesite_pillar_from_stone_types_andesite_stonecutting","cfm:birch_kitchen_drawer","trashcans:item_trash_can","minecraft:note_block","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","domum_ornamentum:pink_floating_carpet","minecraft:smooth_sandstone","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","supplementaries:altimeter","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","mcwroofs:lime_concrete_roof","allthecompressed:compress/oak_log_1x","mcwbiomesoplenty:stripped_hellbark_log_window2","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","cfm:red_kitchen_drawer","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","aether:diamond_gloves","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","minecraft:cracked_stone_bricks","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwbridges:granite_bridge_pier","mcwlights:jungle_tiki_torch","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:magenta_stained_glass","minecraft:gold_nugget","railcraft:nickel_gear","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwbiomesoplenty:stripped_maple_log_four_window","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","mcwpaths:cobbled_deepslate_strewn_rocky_path","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","allthecompressed:compress/andesite_1x","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","mcwwindows:diorite_four_window","additionallanterns:dark_prismarine_lantern","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwwindows:mangrove_plank_window2","travelersbackpack:backpack_tank","mcwwindows:mangrove_pane_window","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","railcraft:zinc_gear","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:lime_floating_carpet","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","farmersdelight:flint_knife","biomesoplenty:maple_boat","croptopia:campfire_molasses","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","mcwwindows:stripped_birch_log_four_window","create:cut_granite_from_stone_types_granite_stonecutting","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","railcraft:water_tank_siding","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","minecraft:fletching_table","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","securitycraft:reinforced_orange_stained_glass","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","handcrafted:oak_dining_bench","simplylight:illuminant_gray_block_dyed","twigs:tuff_stairs","aether:wooden_pickaxe_repairing","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwwindows:spruce_shutter","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","mythicbotany:rune_holder","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","immersiveengineering:crafting/plate_silver_hammering","mcwpaths:andesite_windmill_weave_path","mcwwindows:spruce_plank_four_window","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","allthecompressed:compress/cobbled_deepslate_1x","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","cfm:black_kitchen_sink","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","alltheores:silver_rod","mcwroofs:grass_top_roof","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:stripped_warped_stem_four_window","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","reliquary:uncrafting/string","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","mcwwindows:crimson_planks_window2","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwtrpdoors:jungle_whispering_trapdoor","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","mcwpaths:granite_strewn_rocky_path","domum_ornamentum:blockbarreldeco_standing","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","supplementaries:gold_trapdoor","pneumaticcraft:speed_upgrade","mcwroofs:andesite_upper_lower_roof","mcwwindows:stripped_spruce_log_window2","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","enderio:basic_fluid_filter","rftoolsutility:counter_module","minecraft:oak_trapdoor","littlelogistics:locomotive_route","create:granite_pillar_from_stone_types_granite_stonecutting","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:granite_lower_roof","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","delightful:knives/zinc_knife","mcwroofs:granite_steep_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","alltheores:osmium_gear","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","mcwlights:cyan_paper_lamp","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","securitycraft:storage_module","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","botania:vine_ball","cfm:cyan_trampoline","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","cfm:spruce_kitchen_drawer","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:candle","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","mcwwindows:stripped_mangrove_log_window2","mcwpaths:andesite_flagstone_slab","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:brick","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","botania:water_ring","twigs:bone_meal_from_seashells","mcwfences:oak_curved_gate","supplementaries:wrench","minecraft:bow","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:quartz_four_window","mcwwindows:light_gray_mosaic_glass_pane","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","minecraft:andesite_stairs","mcwdoors:oak_swamp_door","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","aether:iron_axe_repairing","reliquary:uncrafting/bone","cfm:purple_kitchen_sink","supplementaries:sack","mcwwindows:cherry_window2","cfm:black_cooler","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","minecraft:smoker","cfm:stripped_oak_mail_box","silentgear:rough_rod","mcwwindows:stripped_oak_pane_window","mcwwindows:jungle_plank_parapet","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","create:copycat_step_from_ingots_zinc_stonecutting","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","minecraft:brown_dye","mcwbiomesoplenty:jacaranda_plank_window2","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","mcwwindows:dark_oak_plank_pane_window","mcwbiomesoplenty:stripped_empyreal_log_window","minecraft:firework_rocket_simple","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","mcwpaths:andesite_crystal_floor","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","mcwfurnitures:oak_coffee_table","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","mcwroofs:andesite_roof","simplylight:illuminant_black_block_toggle","rftoolsbase:infused_enderpearl","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","supplementaries:pedestal","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwpaths:sandstone_flagstone_path","cfm:white_kitchen_drawer","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","mcwdoors:jungle_tropical_door","minecraft:stone","twigs:lamp","dyenamics:bed/lavender_bed","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","mcwpaths:andesite_running_bond_path","travelersbackpack:lapis","deepresonance:machine_frame","terralith:lever_alt","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","xnet:netcable_red_dye","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","mcwwindows:pink_mosaic_glass_pane","mcwdoors:oak_barn_door","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","alltheores:gold_rod","additionallanterns:smooth_stone_chain","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","mcwbiomesoplenty:maple_pane_window","bigreactors:turbine/reinforced/activefluidport_forge","mcwfurnitures:oak_bookshelf_cupboard","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","supplementaries:key","mcwbridges:stone_brick_bridge","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:cobbled_deepslate_lantern","mcwtrpdoors:oak_tropical_trapdoor","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","minecraft:dried_kelp_from_smelting","minecraft:polished_granite_slab_from_granite_stonecutting","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:white_concrete_attic_roof","securitycraft:reinforced_mossy_stone_bricks_from_vine","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","mcwbiomesoplenty:redwood_window2","additionallanterns:obsidian_chain","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","securitycraft:security_camera","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:magic_window2","mcwlights:cross_lantern","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","railcraft:receiver_circuit","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","allthecompressed:compress/granite_1x","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","mcwroofs:blackstone_top_roof","immersiveengineering:crafting/connector_hv_relay","supplementaries:candle_holders/candle_holder_pink","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","ae2additions:super_cell_housing","cfm:brown_trampoline","supplementaries:stonecutting/stone_tile","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","create:polished_cut_granite_stairs_from_stone_types_granite_stonecutting","mcwlights:yellow_paper_lamp","mcwbridges:balustrade_granite_bridge","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","mcwroofs:oak_planks_upper_lower_roof","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","handcrafted:oak_nightstand","railcraft:personal_world_spike","dyenamics:wine_stained_glass","mcwlights:lava_lamp","domum_ornamentum:light_gray_floating_carpet","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwpaths:andesite_clover_paving","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","cfm:jungle_table","aether:iron_chestplate_repairing","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwwindows:cherry_pane_window","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","everythingcopper:copper_rail","mcwdoors:garage_white_door","cfm:black_grill","utilitix:armed_stand","botania:lexicon","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwlights:green_paper_lamp","mcwwindows:birch_plank_parapet","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","supplementaries:daub","create:small_granite_bricks_from_stone_types_granite_stonecutting","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","enderio:fluid_tank","mcwroofs:oak_planks_roof","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","mcwpaths:stone_flagstone","deepresonance:resonating_plate","bigreactors:energizer/casing","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","xnet:connector_blue","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","minecraft:campfire","mcwdoors:jungle_barn_door","handcrafted:oak_drawer","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:maple_highley_gate","cfm:orange_cooler","farmersdelight:cooking/chicken_soup","mcwpaths:granite_dumble_paving","mcwpaths:granite_flagstone_slab","minecraft:lime_stained_glass","aether:skyroot_note_block","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","create:cut_granite_brick_slab_from_stone_types_granite_stonecutting","mcwroofs:granite_upper_steep_roof","mcwlights:bell_wall_lantern","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","minecraft:black_stained_glass"],toBeDisplayed:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","sfm:disk","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","mcwpaths:stone_crystal_floor","handcrafted:spider_trophy","mcwpaths:cobbled_deepslate_crystal_floor_path","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:granite_attic_roof","mcwroofs:sandstone_upper_lower_roof","chimes:copper_chimes","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","mcwpaths:granite_windmill_weave_slab","cfm:jungle_coffee_table","pneumaticcraft:security_upgrade","xnet:connector_green","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:oak_window","create:crafting/appliances/crafting_blueprint","securitycraft:reinforced_white_stained_glass","mcwbiomesoplenty:umbran_horse_fence","minecolonies:chainmailchestplate","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","supplementaries:flint_block","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","mcwwindows:oak_plank_pane_window","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","reliquary:mob_charm_fragments/cave_spider","mcwbridges:granite_bridge_stair","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","railcraft:controller_circuit","mcwpaths:granite_running_bond_slab","twigs:polished_rhyolite_stonecutting","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","supplementaries:candle_holders/candle_holder_green","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","aether:skyroot_fletching_table","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","dyenamics:banner/icy_blue_banner","mcwroofs:purple_concrete_attic_roof","energymeter:meter","minecraft:polished_granite_from_granite_stonecutting","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","xnet:connector_red_dye","minecolonies:shapetool","supplementaries:bubble_blower","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","supplementaries:crank","delightful:knives/tin_knife","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","mcwwindows:andesite_louvered_shutter","alltheores:iron_plate","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwfences:prismarine_railing_gate","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","mcwpaths:cobblestone_dumble_paving","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","cfm:crimson_kitchen_sink_dark","nethersdelight:golden_machete","tombstone:ankh_of_prayer","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","securitycraft:camera_monitor","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","cfm:black_kitchen_drawer","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","minecraft:cyan_concrete_powder","cfm:stripped_oak_cabinet","mcwpaths:andesite_windmill_weave_slab","mcwfences:dark_oak_pyramid_gate","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:oak_kitchen_counter","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","create:crafting/logistics/powered_latch","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","solcarrot:food_book","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","mcwpaths:cobbled_deepslate_windmill_weave_path","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","handcrafted:oak_bench","mcwfurnitures:oak_striped_chair","mcwfences:nether_brick_grass_topped_wall","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:angel_block","occultism:crafting/magic_lamp_empty","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","railcraft:brass_ingot_crafted_with_ingots","dyenamics:spring_green_concrete_powder","mcwpaths:sandstone_running_bond_stairs","create:granite_from_stone_types_granite_stonecutting","cfm:jungle_cabinet","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","minecraft:gray_concrete_powder","mcwtrpdoors:jungle_four_panel_trapdoor","mcwfences:oak_pyramid_gate","mcwbiomesoplenty:palm_window","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","twigs:mossy_bricks_from_vine","cfm:pink_kitchen_sink","mcwroofs:cobblestone_upper_lower_roof","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","create:industrial_iron_block_from_ingots_iron_stonecutting","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","supplementaries:daub_cross_brace","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwbiomesoplenty:umbran_plank_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwfurnitures:jungle_double_wardrobe","cfm:gray_trampoline","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","securitycraft:whitelist_module","cfm:blue_kitchen_sink","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:pine_plank_window2","mcwbiomesoplenty:hellbark_four_window","mcwwindows:warped_louvered_shutter","xnet:connector_routing","mcwbiomesoplenty:palm_plank_window2","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","utilitix:directional_highspeed_rail","mcwwindows:oak_four_window","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","mcwlights:golden_chain","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","littlelogistics:seater_car","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","create:cut_granite_bricks_from_stone_types_granite_stonecutting","mcwbiomesoplenty:stripped_dead_pane_window","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","cfm:warped_mail_box","securitycraft:reinforced_diorite","cfm:green_grill","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","minecraft:oak_pressure_plate","minecraft:stone_brick_stairs","cfm:orange_grill","bigreactors:turbine/basic/activefluidport_forge","cfm:brown_grill","mcwwindows:quartz_window","mcwwindows:dark_oak_shutter","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","cfm:jungle_desk","mcwroofs:oak_roof","reliquary:mob_charm_fragments/creeper","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","minecraft:iron_hoe","mcwbiomesoplenty:mahogany_horse_fence","mcwtrpdoors:oak_barred_trapdoor","cfm:gray_kitchen_drawer","mcwroofs:light_blue_concrete_lower_roof","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","minecraft:clock","mcwfences:nether_brick_pillar_wall","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwwindows:warped_curtain_rod","railcraft:lead_gear","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","delightful:knives/osmium_knife","mcwfences:oak_wired_fence","twigs:silt","additionallanterns:end_stone_lantern","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","cfm:mangrove_kitchen_sink_dark","mcwfurnitures:stripped_oak_double_drawer_counter","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","mcwdoors:oak_four_panel_door","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","minecraft:granite_stairs_from_granite_stonecutting","cfm:stripped_crimson_kitchen_drawer","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","cfm:green_kitchen_sink","mcwfences:diorite_railing_gate","alltheores:tin_rod","mcwroofs:gutter_base_black","mcwfurnitures:stripped_jungle_chair","mcwroofs:green_concrete_roof","mcwwindows:white_mosaic_glass","minecraft:redstone_torch","cfm:acacia_kitchen_sink_dark","mcwroofs:jungle_upper_steep_roof","minecraft:polished_andesite_slab_from_andesite_stonecutting","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","create:crafting/materials/andesite_alloy","mcwwindows:andesite_four_window","minecraft:granite_slab","mcwtrpdoors:jungle_swamp_trapdoor","mcwlights:golden_low_candle_holder","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","supplementaries:cog_block","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","mcwbiomesoplenty:redwood_pyramid_gate","cfm:stripped_birch_kitchen_drawer","pneumaticcraft:armor_upgrade","mcwwindows:birch_plank_pane_window","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","securitycraft:block_change_detector","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","twigs:rocky_dirt","mcwpaths:granite_flagstone_stairs","bloodmagic:sacrificial_dagger","mcwwindows:magenta_mosaic_glass_pane","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","mcwbridges:bridge_torch","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwbiomesoplenty:fir_hedge","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","mcwpaths:andesite_crystal_floor_slab","mcwfences:birch_highley_gate","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","mcwwindows:oak_blinds","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwwindows:birch_curtain_rod","mcwdoors:oak_cottage_door","create:crafting/kinetics/fluid_pipe","minecolonies:doublefern","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","create:small_granite_brick_slab_from_stone_types_granite_stonecutting","delightful:food/baklava","mcwpaths:red_sand_path_block","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","cfm:light_blue_cooler","railcraft:signal_circuit","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","mcwbiomesoplenty:pine_window2","undergarden:gloom_o_lantern","sophisticatedstorage:oak_chest_from_vanilla_chest","minecraft:sandstone_slab","mcwwindows:blackstone_four_window","mcwwindows:birch_window","mcwfences:acacia_picket_fence","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwfences:mud_brick_railing_gate","twigs:oak_table","simplylight:illuminant_purple_block_toggle","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","cfm:pink_cooler","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwfurnitures:oak_wardrobe","mcwpaths:granite_flagstone_path","mcwdoors:jungle_japanese_door","mcwtrpdoors:jungle_paper_trapdoor","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwlights:upgraded_torch","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwwindows:jungle_blinds","mcwroofs:blue_concrete_roof","mcwlights:oak_tiki_torch","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","mcwwindows:stripped_dark_oak_log_window2","mcwpaths:sandstone_windmill_weave","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","bloodmagic:blood_altar","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","mcwpaths:granite_diamond_paving","forbidden_arcanus:clibano_core","cfm:purple_kitchen_drawer","mcwroofs:orange_concrete_top_roof","additionallanterns:granite_chain","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","minecraft:blue_stained_glass","create:small_granite_brick_wall_from_stone_types_granite_stonecutting","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwpaths:granite_honeycomb_paving","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","minecraft:oak_sign","mcwwindows:stone_pane_window","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwroofs:blackstone_lower_roof","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwroofs:gutter_base_magenta","mcwbiomesoplenty:fir_plank_window2","domum_ornamentum:cobblestone_extra","mcwbridges:balustrade_andesite_bridge","additionallanterns:emerald_lantern","handcrafted:oak_counter","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:empyreal_window","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","create:crafting/kinetics/hose_pulley","eidolon:smooth_stone_masonry_stonecutter_0","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwwindows:cyan_mosaic_glass_pane","mcwtrpdoors:jungle_bark_trapdoor","dyenamics:lavender_concrete_powder","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","utilitix:linked_repeater","minecolonies:doublegrass","dyenamics:rose_stained_glass","farmersdelight:cooking/hot_cocoa","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:purple_concrete_powder","minecraft:sugar_from_sugar_cane","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","xnet:advanced_connector_yellow","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","cfm:stripped_oak_chair","cfm:orange_trampoline","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","wirelesschargers:basic_wireless_player_charger","mcwroofs:lime_concrete_steep_roof","mcwdoors:jungle_swamp_door","mcwwindows:oak_shutter","cfm:rock_path","cfm:light_blue_kitchen_sink","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","supplementaries:candle_holders/candle_holder_light_gray","reliquary:mob_charm_fragments/zombified_piglin","mcwwindows:warped_planks_four_window","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","reliquary:mob_charm_fragments/zombie","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","mcwroofs:oak_planks_attic_roof","rftoolsutility:moduleplus_template","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","simplylight:illuminant_light_gray_block_dyed","securitycraft:reinforced_spruce_fence","mcwbiomesoplenty:magic_horse_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","mcwbridges:stone_bridge_pier","deepresonance:resonating_plate_block","naturescompass:natures_compass","railcraft:radio_circuit","mcwbiomesoplenty:dead_plank_four_window","mcwwindows:diorite_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","minecraft:wooden_axe","mcwpaths:cobbled_deepslate_clover_paving","cfm:stripped_oak_desk","mcwbiomesoplenty:mahogany_plank_window2","mcwtrpdoors:oak_glass_trapdoor","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwbiomesoplenty:willow_four_window","mcwlights:golden_double_candle_holder","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","domum_ornamentum:black_floating_carpet","supplementaries:daub_brace","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","minecraft:hopper","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","handcrafted:oak_shelf","minecraft:cut_sandstone_from_sandstone_stonecutting","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","create:crafting/logistics/powered_toggle_latch","supplementaries:flags/flag_lime","mcwpaths:granite_windmill_weave","mcwpaths:granite_windmill_weave_path","travelersbackpack:iron_tier_upgrade","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","create:crafting/kinetics/copper_valve_handle","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:mangrove_louvered_shutter","mcwwindows:warped_blinds","supplementaries:flags/flag_magenta","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","minecraft:diamond_axe","mcwpaths:granite_running_bond_path","securitycraft:reinforced_pink_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_red","mcwroofs:granite_top_roof","mcwroofs:lime_concrete_attic_roof","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","securitycraft:alarm","mcwfurnitures:oak_counter","rftoolsutility:charged_porter","mcwbiomesoplenty:fir_horse_fence","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","additionallanterns:bricks_lantern","create:crafting/materials/andesite_alloy_from_zinc","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","mcwroofs:oak_top_roof","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","mcwpaths:sandstone_honeycomb_paving","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwwindows:stone_window","create:copper_shingles_from_ingots_copper_stonecutting","mcwfences:granite_grass_topped_wall","everythingcopper:copper_sword","mcwwindows:birch_shutter","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","create:polished_cut_granite_wall_from_stone_types_granite_stonecutting","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","create:cut_granite_stairs_from_stone_types_granite_stonecutting","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:jungle_stool_chair","mcwfurnitures:stripped_oak_modern_wardrobe","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwroofs:brown_concrete_roof","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","mcwroofs:cobblestone_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","additionallanterns:gold_lantern","securitycraft:reinforced_mossy_cobblestone_from_vine","mcwdoors:oak_bark_glass_door","sfm:water_tank","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","simplylight:illuminant_orange_block_dyed","nethersdelight:iron_machete","create:layered_tuff_from_stone_types_tuff_stonecutting","mcwfences:dark_oak_picket_fence","alchemistry:compactor","travelersbackpack:sandstone","cfm:jungle_upgraded_fence","minecraft:dropper","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwpaths:granite_crystal_floor_path","mcwfences:bamboo_highley_gate","mcwwindows:oak_window2","create:cut_granite_slab_from_stone_types_granite_stonecutting","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","mcwroofs:jungle_roof","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwpaths:andesite_running_bond","create:crafting/kinetics/rose_quartz_lamp","immersiveengineering:crafting/plate_nickel_hammering","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","minecraft:torch","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","cfm:light_gray_grill","mcwwindows:mangrove_window","mcwwindows:quartz_pane_window","cfm:oak_desk","mcwtrpdoors:metal_trapdoor","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:magic_pyramid_gate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","minecraft:shield","mcwfurnitures:jungle_double_drawer_counter","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","supplementaries:daub_frame","pneumaticcraft:minigun_upgrade","railcraft:signal_tuner","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","mcwfurnitures:jungle_double_drawer","undergarden:slingshot","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:banner/mint_banner","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:sandstone_wall","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","mcwbiomesoplenty:stripped_mahogany_log_window","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","cfm:pink_trampoline","mcwbridges:balustrade_sandstone_bridge","securitycraft:briefcase","mcwwindows:green_mosaic_glass","alltheores:nickel_rod","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwbiomesoplenty:empyreal_four_window","mcwfurnitures:stripped_oak_large_drawer","mcwlights:copper_triple_candle_holder","handcrafted:andesite_corner_trim","cfm:white_grill","minecraft:iron_nugget_from_smelting","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","utilitix:comparator_redirector_up","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","securitycraft:motion_activated_light","mcwpaths:andesite_crystal_floor_stairs","mcwfences:double_curved_metal_fence","mcwroofs:sandstone_lower_roof","mcwwindows:oak_plank_four_window","travelersbackpack:hose_nozzle","securitycraft:claymore","cfm:red_cooler","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","twigs:polished_tuff_stonecutting","supplementaries:lock_block","reliquary:mob_charm_fragments/skeleton","mcwpaths:cobbled_deepslate_windmill_weave_slab","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","mcwwindows:stripped_acacia_log_four_window","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","undergarden:grongle_boat","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","domum_ornamentum:green_floating_carpet","mcwwindows:cherry_plank_window2","securitycraft:laser_block","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","mcwpaths:granite_crystal_floor","minecraft:carrot_on_a_stick","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_gray_concrete_upper_lower_roof","mcwroofs:light_blue_concrete_attic_roof","mcwfurnitures:jungle_large_drawer","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:deepslate_lower_roof","mcwroofs:white_concrete_top_roof","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:stone_window2","mcwwindows:metal_four_window","minecraft:oak_button","minecraft:granite_wall","minecraft:orange_concrete_powder","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","handcrafted:granite_corner_trim","minecraft:shears","mcwbiomesoplenty:maple_window","supplementaries:flags/flag_blue","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","cfm:lime_kitchen_drawer","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","xnet:advanced_connector_yellow_dye","allthecompressed:compress/cobblestone_1x","additionallanterns:diamond_lantern","mcwpaths:stone_running_bond_path","mcwroofs:yellow_concrete_upper_steep_roof","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","mcwwindows:quartz_window2","securitycraft:reinforced_pink_stained_glass_pane_from_glass","mcwpaths:cobblestone_honeycomb_paving","simplylight:edge_light_top","securitycraft:reinforced_lime_stained_glass_pane_from_glass","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","domum_ornamentum:orange_floating_carpet","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","expatternprovider:silicon_block","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","croptopia:nougat","itemcollectors:basic_collector","minecraft:loom","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","mcwtrpdoors:oak_bark_trapdoor","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","minecraft:arrow","supplementaries:candle_holders/candle_holder_gray","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","minecraft:oak_fence_gate","supplementaries:turn_table","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","mcwroofs:sandstone_attic_roof","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","buildinggadgets2:gadget_copy_paste","handcrafted:golden_wide_pot","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","mcwlights:soul_spruce_tiki_torch","mcwpaths:granite_running_bond_stairs","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","mcwpaths:granite_flagstone","create:cut_granite_brick_wall_from_stone_types_granite_stonecutting","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","mcwroofs:gray_concrete_attic_roof","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","alltheores:nickel_plate","supplementaries:flags/flag_green","cfm:stripped_jungle_mail_box","securitycraft:reinforced_white_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwwindows:acacia_plank_four_window","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","mcwlights:copper_chandelier","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","mcwdoors:jungle_paper_door","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","mcwbiomesoplenty:stripped_palm_log_four_window","sophisticatedstorage:generic_chest","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","mcwroofs:gutter_base_light_blue","securitycraft:reinforced_birch_fence_gate","mcwpaths:andesite_crystal_floor_path","additionallanterns:diorite_lantern","minecraft:dried_kelp_from_campfire_cooking","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","mcwbridges:granite_bridge","minecraft:diamond_block","mcwroofs:deepslate_roof","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","cfm:brown_cooler","mcwfences:diorite_pillar_wall","dyenamics:cherenkov_stained_glass","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwwindows:dark_prismarine_parapet","minecraft:yellow_stained_glass","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwpaths:granite_crystal_floor_stairs","utilitix:experience_crystal","mcwwindows:red_sandstone_pane_window","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","cfm:green_cooler","handcrafted:goat_trophy","bigreactors:blasting/graphite_from_coal","minecraft:respawn_anchor","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","paraglider:paraglider","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","mcwbridges:jungle_bridge_pier","mcwtrpdoors:oak_barrel_trapdoor","mcwbiomesoplenty:willow_pane_window","buildinggadgets2:gadget_exchanging","aether:flint_and_steel_repairing","minecraft:lapis_block","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:fir_wired_fence","mcwlights:iron_double_candle_holder","supplementaries:flower_box","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","minecraft:iron_axe","utilitarian:snad/snad","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","mcwbiomesoplenty:empyreal_plank_window2","minecraft:jukebox","securitycraft:blacklist_module","mcwwindows:jungle_curtain_rod","dyenamics:amber_stained_glass","create:layered_granite_from_stone_types_granite_stonecutting","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","immersiveengineering:crafting/plate_lead_hammering","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwfurnitures:stripped_jungle_large_drawer","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","minecraft:glass_bottle","tombstone:blue_marble","mcwfences:mesh_metal_fence","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","minecraft:polished_granite","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","mcwroofs:gutter_base_orange","mcwbiomesoplenty:magic_plank_window2","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwfences:quartz_railing_gate","mcwwindows:blackstone_pane_window","mcwwindows:spruce_four_window","twilightforest:transformation_boat","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","mcwpaths:granite_clover_paving","reliquary:uncrafting/spider_eye","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","mcwlights:bamboo_tiki_torch","domum_ornamentum:green_cobblestone_extra","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","alltheores:zinc_plate","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","mcwwindows:prismarine_pane_window","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","railcraft:iron_gear","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","supplementaries:slice_map","mcwroofs:cobblestone_lower_roof","handcrafted:oak_pillar_trim","occultism:crafting/butcher_knife","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","securitycraft:reinforced_sticky_piston","aether:iron_pendant","securitycraft:redstone_module","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","cfm:gray_grill","supplementaries:flags/flag_purple","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:stripped_redwood_log_window","create:framed_glass_from_glass_colorless_stonecutting","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","tombstone:bone_needle","minecraft:stone_brick_wall","handcrafted:golden_thick_pot","twilightforest:twilight_oak_boat","undergarden:undergarden_scaffolding","mcwlights:soul_classic_street_lamp","modularrouters:modular_router","cfm:warped_kitchen_sink_light","delightful:knives/nickel_knife","mcwfurnitures:stripped_jungle_coffee_table","cfm:cyan_grill","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_fir_log_window2","cfm:stripped_spruce_mail_box","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","aquaculture:gold_fillet_knife","dyenamics:banner/navy_banner","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","cfm:gray_kitchen_sink","minecraft:paper","pneumaticcraft:inventory_upgrade","mcwfences:warped_curved_gate","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","mcwdoors:oak_stable_door","cfm:dark_oak_kitchen_sink_dark","twigs:smooth_stone_bricks","mcwwindows:jungle_louvered_shutter","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:oak_upgraded_gate","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","croptopia:cashew_chicken","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","minecraft:bucket","handcrafted:salmon_trophy","mcwroofs:grass_upper_lower_roof","dyenamics:fluorescent_concrete_powder","supplementaries:speaker_block","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","farmersdelight:chicken_sandwich","minecraft:polished_andesite","securitycraft:reinforced_cherry_fence","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwpaths:andesite_honeycomb_paving","mcwbridges:sandstone_bridge","mcwbiomesoplenty:redwood_four_window","mcwwindows:birch_log_parapet","mcwbiomesoplenty:magic_plank_window","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","mcwroofs:black_concrete_attic_roof","minecraft:charcoal","additionallanterns:copper_lantern","mcwlights:golden_small_chandelier","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:redwood_highley_gate","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","domum_ornamentum:purple_floating_carpet","mcwwindows:stripped_warped_pane_window","twigs:polished_tuff_bricks_from_tuff_stonecutting","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwbiomesoplenty:dead_pane_window","mcwwindows:crimson_blinds","mcwwindows:diorite_window2","sophisticatedstorage:storage_stack_upgrade_omega_tier_from_backpack_stack_upgrade_omega_tier","dyenamics:banner/aquamarine_banner","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","utilitix:reinforced_rail","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","mcwfurnitures:oak_bookshelf","xnet:connector_blue_dye","croptopia:roasted_nuts_from_campfire","utilitarian:utility/jungle_logs_to_doors","alltheores:lead_rod","mcwroofs:gutter_base_blue","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","mcwroofs:granite_roof","mcwwindows:oak_log_parapet","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","supplementaries:notice_board","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","littlelogistics:spring","securitycraft:reinforced_bamboo_fence","minecolonies:large_empty_bottle","mcwbiomesoplenty:fir_highley_gate","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","minecraft:iron_block","mcwfurnitures:jungle_modern_desk","mcwlights:garden_light","mcwroofs:gutter_base","additionallanterns:gold_chain","mcwroofs:gutter_middle_purple","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","cfm:stripped_acacia_kitchen_sink_dark","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:golden_apple","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","minecraft:diamond_pickaxe","mcwpaths:cobbled_deepslate_flagstone_slab","domum_ornamentum:blockbarreldeco_onside","mcwpaths:sandstone_dumble_paving","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","simplylight:rodlamp","mcwroofs:andesite_lower_roof","mcwroofs:deepslate_attic_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwbridges:andesite_bridge","mcwwindows:dark_prismarine_pane_window","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwfurnitures:jungle_table","mcwbiomesoplenty:umbran_plank_window2","mcwdoors:metal_reinforced_door","create:tuff_from_stone_types_tuff_stonecutting","domum_ornamentum:blue_cobblestone_extra","rftoolsbuilder:green_shield_template_block","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","securitycraft:reinforced_glass_pane","cfm:mangrove_kitchen_sink_light","farmersdelight:golden_knife","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","minecraft:chiseled_sandstone_from_sandstone_stonecutting","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","mcwbiomesoplenty:maple_plank_window","delightful:campfire/roasted_acorn","mcwfences:warped_horse_fence","twilightforest:canopy_boat","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_3","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_4","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","mcwbiomesoplenty:maple_four_window","utilitarian:redstone_clock","deepresonance:tank","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","handcrafted:granite_pillar_trim","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","securitycraft:harming_module","silentgear:bort_block","minecraft:golden_boots","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","minecraft:andesite_wall_from_andesite_stonecutting","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","supplementaries:flags/flag_cyan","mcwbiomesoplenty:jacaranda_four_window","minecraft:conduit","pneumaticcraft:magnet_upgrade","cfm:pink_grill","mcwbridges:iron_bridge_pier","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwbiomesoplenty:palm_plank_four_window","mcwwindows:mangrove_blinds","mcwroofs:sandstone_roof","minecraft:sticky_piston","mcwwindows:granite_window2","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwbiomesoplenty:jacaranda_stockade_fence","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","mcwroofs:cyan_concrete_upper_lower_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","cfm:oak_kitchen_sink_light","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwwindows:diorite_pane_window","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","additionallanterns:cobblestone_lantern","mcwroofs:gutter_base_white","mcwpaths:granite_crystal_floor_slab","minecraft:oak_stairs","additionallanterns:stone_lantern","alchemistry:liquifier","supplementaries:flags/flag_red","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","mcwroofs:magenta_concrete_upper_lower_roof","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","mcwroofs:cyan_concrete_steep_roof","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","mcwfurnitures:oak_desk","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","mcwwindows:jungle_log_parapet","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwwindows:white_mosaic_glass_pane","mcwwindows:purple_mosaic_glass","mcwroofs:pink_concrete_attic_roof","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:fishing_rod","xnet:connector_yellow_dye","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","delightful:knives/silver_knife","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","mcwwindows:acacia_blinds","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwpaths:granite_windmill_weave_stairs","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","mcwroofs:roofing_hammer","domum_ornamentum:gray_floating_carpet","cfm:stripped_jungle_park_bench","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","create:copper_tiles_from_ingots_copper_stonecutting","minecraft:light_blue_concrete_powder","supplementaries:candle_holders/candle_holder_cyan","createoreexcavation:drill","minecraft:granite_wall_from_granite_stonecutting","mcwroofs:gutter_middle_magenta","supplementaries:timber_frame","minecraft:granite_stairs","sophisticatedstorage:packing_tape","mcwwindows:light_blue_mosaic_glass","silentgear:fine_silk","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:andesite_pane_window","mcwwindows:acacia_plank_pane_window","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","dyenamics:banner/honey_banner","railcraft:tin_gear","mcwlights:double_street_lamp","minecraft:tnt","mcwbiomesoplenty:stripped_maple_pane_window","minecraft:mossy_stone_bricks_from_vine","rftoolsstorage:storage_control_module","minecraft:andesite_slab_from_andesite_stonecutting","enderio:pressurized_fluid_tank","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","mcwpaths:granite_square_paving","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","minecraft:flint_and_steel","deepresonance:radiation_monitor","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","create:cut_granite_wall_from_stone_types_granite_stonecutting","cfm:magenta_kitchen_sink","mcwfurnitures:jungle_drawer_counter","xnet:advanced_connector_red","aquaculture:tin_can_to_iron_nugget_from_blasting","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","xnet:advanced_connector_blue_dye","cfm:spruce_kitchen_sink_light","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","xnet:netcable_green_dye","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:cherry_plank_window","mcwwindows:dark_prismarine_window","mcwbiomesoplenty:maple_plank_window2","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwpaths:andesite_flagstone","mcwdoors:oak_bamboo_door","mcwbiomesoplenty:dead_plank_window2","travelersbackpack:creeper","xnet:advanced_connector_red_dye","minecraft:golden_axe","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","immersiveengineering:crafting/wire_lead","minecraft:cyan_stained_glass","securitycraft:reinforced_lime_stained_glass_pane_from_dye","mcwroofs:red_concrete_roof","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","ae2:network/cables/smart_fluix","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:stone_tile","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:willow_plank_window","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","supplementaries:flags/flag_white","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwbiomesoplenty:redwood_stockade_fence","mcwroofs:light_gray_concrete_lower_roof","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","twigs:rhyolite_slab","mcwroofs:andesite_top_roof","immersiveengineering:crafting/stick_iron","create:polished_cut_granite_from_stone_types_granite_stonecutting","minecraft:granite_slab_from_granite_stonecutting","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","minecraft:glass_pane","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","supplementaries:timber_brace","mcwpaths:stone_crystal_floor_path","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","mcwlights:acacia_ceiling_fan_light","minecraft:golden_sword","mcwroofs:lime_concrete_upper_steep_roof","securitycraft:reinforced_brown_stained_glass","cfm:lime_grill","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:sonic_security_system","securitycraft:reinforced_cyan_stained_glass","create:polished_cut_granite_slab_from_stone_types_granite_stonecutting","minecraft:clay","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","handcrafted:golden_medium_pot","enderio:wood_gear","supplementaries:bed_from_feather_block","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","mcwpaths:andesite_basket_weave_paving","sfm:fancy_to_cable","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwpaths:granite_running_bond","mcwwindows:cyan_mosaic_glass","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","minecraft:mossy_cobblestone_from_vine","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","minecraft:white_concrete_powder","mcwwindows:acacia_window","cfm:orange_kitchen_drawer","minecraft:cherry_boat","silentgear:stone_rod","dyenamics:icy_blue_stained_glass","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","handcrafted:sandstone_corner_trim","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwbridges:oak_log_bridge_middle","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","mcwroofs:orange_concrete_lower_roof","cfm:oak_hedge","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","create:crafting/kinetics/steam_engine","cfm:spruce_mail_box","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","silentgear:bort_from_block","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","mcwlights:pink_paper_lamp","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","xnet:connector_green_dye","utilitix:hand_bell","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","alltheores:zinc_rod","cfm:yellow_kitchen_sink","mcwpaths:cobbled_deepslate_honeycomb_paving","silentgear:diamond_from_shards","aether:iron_gloves","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","minecraft:chiseled_stone_bricks","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","tombstone:white_marble","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","dyenamics:wine_concrete_powder","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","mcwroofs:blue_concrete_attic_roof","caupona:mud_kitchen_stove","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","pneumaticcraft:stomp_upgrade","mcwpaths:gravel_path_block","minecraft:polished_granite_stairs_from_granite_stonecutting","mcwfurnitures:jungle_cupboard_counter","mcwwindows:crimson_stem_parapet","supplementaries:lapis_bricks","croptopia:chocolate","mcwbiomesoplenty:mahogany_highley_gate","minecraft:pink_concrete_powder","minecraft:cauldron","mcwbridges:glass_bridge_pier","create:small_granite_brick_stairs_from_stone_types_granite_stonecutting","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","delightful:smelting/roasted_acorn","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","minecraft:brush","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","mcwwindows:bricks_window2","dyenamics:ultramarine_concrete_powder","mcwpaths:stone_crystal_floor_stairs","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","aether:iron_boots_repairing","mcwfurnitures:oak_kitchen_cabinet","minecraft:andesite_wall","sfm:cable","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","rftoolsutility:module_template","mcwroofs:deepslate_upper_steep_roof","tombstone:impregnated_diamond","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","mcwfences:birch_curved_gate","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwbiomesoplenty:palm_pyramid_gate","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","mcwfences:modern_granite_wall","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","forbidden_arcanus:golden_blacksmith_gavel","mcwfences:dark_oak_curved_gate","mcwpaths:granite_basket_weave_paving","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwbiomesoplenty:dead_wired_fence","minecraft:chest","alltheores:diamond_plate","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwroofs:blue_concrete_top_roof","cfm:magenta_trampoline","supplementaries:pulley","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:jungle_fish_mount","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","allthecompressed:compress/glass_1x","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","ad_astra:compressor","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","mcwlights:crimson_ceiling_fan_light","alltheores:zinc_gear","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","biomesoplenty:dead_boat","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","supplementaries:flags/flag_orange","travelersbackpack:standard","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","sfm:fancy_cable","travelersbackpack:iron","mcwroofs:gutter_base_red","mcwroofs:oak_planks_upper_steep_roof","create:copper_ladder_from_ingots_copper_stonecutting","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","alltheores:osmium_rod","simplylight:illuminant_brown_block_toggle","minecraft:oak_fence","mcwfences:crimson_highley_gate","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwbiomesoplenty:empyreal_window2","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","mcwpaths:stone_flagstone_slab","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","securitycraft:universal_key_changer","mcwwindows:jungle_plank_pane_window","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","sophisticatedstorage:oak_limited_barrel_1","mcwroofs:gray_concrete_roof","pneumaticcraft:range_upgrade","constructionwand:diamond_wand","sophisticatedstorage:oak_limited_barrel_2","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","sophisticatedstorage:oak_limited_barrel_4","mcwwindows:warped_stem_four_window","securitycraft:sentry","securitycraft:reinforced_black_stained_glass","supplementaries:hourglass","everythingcopper:copper_leggings","mcwroofs:gutter_base_pink","mcwbridges:andesite_bridge_stair","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwroofs:magenta_concrete_lower_roof","buildinggadgets2:gadget_destruction","mcwlights:iron_framed_torch","minecraft:deepslate","pneumaticcraft:ender_visor_upgrade","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:blue_mosaic_glass_pane","mcwwindows:spruce_plank_window","supplementaries:checker","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","silentgear:fine_silk_cloth","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","minecraft:crossbow","cfm:cyan_cooler","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","railcraft:silver_gear","mcwbridges:cobblestone_bridge_pier","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","create:cut_granite_brick_stairs_from_stone_types_granite_stonecutting","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:orange_paper_lamp","mcwlights:iron_triple_candle_holder","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","mcwfences:spruce_picket_fence","croptopia:roasted_smoking","mcwbiomesoplenty:hellbark_curved_gate","travelersbackpack:redstone","minecraft:white_stained_glass","minecraft:slime_block","xnet:advanced_connector_green_dye","mcwroofs:granite_upper_lower_roof","securitycraft:taser","create:copycat_panel_from_ingots_zinc_stonecutting","supplementaries:soap","mcwwindows:iron_shutter","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","travelersbackpack:emerald","cfm:dark_oak_kitchen_drawer","croptopia:trail_mix","mcwbiomesoplenty:pine_plank_window","farmersdelight:cutting_board","create:andesite_pillar_from_stone_types_andesite_stonecutting","cfm:birch_kitchen_drawer","trashcans:item_trash_can","minecraft:note_block","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwpaths:sandstone_flagstone","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","domum_ornamentum:pink_floating_carpet","minecraft:smooth_sandstone","securitycraft:reinforced_andesite","mcwroofs:cobblestone_attic_roof","supplementaries:altimeter","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","mcwroofs:lime_concrete_roof","allthecompressed:compress/oak_log_1x","mcwbiomesoplenty:stripped_hellbark_log_window2","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","mcwroofs:gutter_middle_green","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","cfm:red_kitchen_drawer","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","mcwpaths:cobblestone_square_paving","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","aether:diamond_gloves","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwpaths:cobbled_deepslate_basket_weave_paving","mcwpaths:cobbled_deepslate_crystal_floor_slab","minecraft:cracked_stone_bricks","simplylight:illuminant_light_gray_block_on_toggle","minecolonies:chainmailhelmet","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwbridges:granite_bridge_pier","mcwlights:jungle_tiki_torch","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","dimstorage:dim_wall","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:magenta_stained_glass","minecraft:gold_nugget","railcraft:nickel_gear","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","minecraft:iron_trapdoor","mcwbiomesoplenty:stripped_maple_log_four_window","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","mcwpaths:cobbled_deepslate_strewn_rocky_path","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","allthecompressed:compress/andesite_1x","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","mcwwindows:diorite_four_window","additionallanterns:dark_prismarine_lantern","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwwindows:mangrove_plank_window2","travelersbackpack:backpack_tank","mcwwindows:mangrove_pane_window","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","railcraft:zinc_gear","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","domum_ornamentum:lime_floating_carpet","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","farmersdelight:flint_knife","biomesoplenty:maple_boat","croptopia:campfire_molasses","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","mcwwindows:stripped_birch_log_four_window","create:cut_granite_from_stone_types_granite_stonecutting","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","railcraft:water_tank_siding","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","securitycraft:reinforced_warped_fence_gate","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","minecraft:fletching_table","handcrafted:pufferfish_trophy","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","securitycraft:mine","securitycraft:reinforced_orange_stained_glass","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","handcrafted:oak_dining_bench","simplylight:illuminant_gray_block_dyed","twigs:tuff_stairs","aether:wooden_pickaxe_repairing","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwwindows:spruce_shutter","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","mythicbotany:rune_holder","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","immersiveengineering:crafting/plate_silver_hammering","mcwpaths:andesite_windmill_weave_path","mcwwindows:spruce_plank_four_window","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","allthecompressed:compress/cobbled_deepslate_1x","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","silentgear:stone_torch","mcwfences:guardian_metal_fence","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","cfm:black_kitchen_sink","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","alltheores:silver_rod","mcwroofs:grass_top_roof","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:stripped_warped_stem_four_window","mcwwindows:dark_oak_curtain_rod","mcwwindows:green_mosaic_glass_pane","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","mcwfences:iron_cheval_de_frise","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","reliquary:uncrafting/string","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","mcwwindows:crimson_planks_window2","securitycraft:disguise_module","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwtrpdoors:jungle_whispering_trapdoor","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","mcwpaths:granite_strewn_rocky_path","domum_ornamentum:blockbarreldeco_standing","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","cfm:fridge_light","supplementaries:gold_trapdoor","pneumaticcraft:speed_upgrade","mcwroofs:andesite_upper_lower_roof","mcwwindows:stripped_spruce_log_window2","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","enderio:basic_fluid_filter","rftoolsutility:counter_module","minecraft:oak_trapdoor","littlelogistics:locomotive_route","create:granite_pillar_from_stone_types_granite_stonecutting","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwroofs:granite_lower_roof","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","delightful:knives/zinc_knife","mcwroofs:granite_steep_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","alltheores:osmium_gear","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","mcwlights:cyan_paper_lamp","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","securitycraft:storage_module","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","botania:vine_ball","cfm:cyan_trampoline","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","rftoolsutility:energyplus_module","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","cfm:spruce_kitchen_drawer","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwbiomesoplenty:magic_highley_gate","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","minecraft:candle","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","framedblocks:framed_cube","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwbiomesoplenty:flowering_oak_hedge","mcwwindows:stripped_mangrove_log_window2","mcwpaths:andesite_flagstone_slab","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:brick","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","botania:water_ring","twigs:bone_meal_from_seashells","mcwfences:oak_curved_gate","supplementaries:wrench","minecraft:bow","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:quartz_four_window","mcwwindows:light_gray_mosaic_glass_pane","biomesoplenty:palm_boat","securitycraft:reinforced_oak_fence_gate","minecraft:andesite_stairs","mcwdoors:oak_swamp_door","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","aether:iron_axe_repairing","reliquary:uncrafting/bone","cfm:purple_kitchen_sink","supplementaries:sack","mcwwindows:cherry_window2","cfm:black_cooler","mcwroofs:deepslate_upper_lower_roof","supplementaries:sconce","minecraft:smoker","cfm:stripped_oak_mail_box","silentgear:rough_rod","mcwwindows:stripped_oak_pane_window","mcwwindows:jungle_plank_parapet","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","create:copycat_step_from_ingots_zinc_stonecutting","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwroofs:light_blue_concrete_upper_lower_roof","mcwbiomesoplenty:palm_wired_fence","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","minecraft:brown_dye","mcwbiomesoplenty:jacaranda_plank_window2","securitycraft:cage_trap","mcwbiomesoplenty:hellbark_stockade_fence","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","mcwwindows:dark_oak_plank_pane_window","mcwbiomesoplenty:stripped_empyreal_log_window","minecraft:firework_rocket_simple","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","mcwpaths:andesite_crystal_floor","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","mcwfurnitures:oak_coffee_table","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","silentgear:emerald_from_shards","mcwroofs:andesite_roof","simplylight:illuminant_black_block_toggle","rftoolsbase:infused_enderpearl","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","supplementaries:pedestal","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwfences:andesite_grass_topped_wall","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","mcwpaths:sandstone_flagstone_path","cfm:white_kitchen_drawer","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","forbidden_arcanus:wooden_blacksmith_gavel","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","mcwdoors:jungle_tropical_door","minecraft:stone","twigs:lamp","dyenamics:bed/lavender_bed","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwroofs:brown_concrete_lower_roof","mcwpaths:andesite_running_bond_path","travelersbackpack:lapis","deepresonance:machine_frame","terralith:lever_alt","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","simplylight:illuminant_cyan_block_dyed","securitycraft:speed_module","xnet:netcable_red_dye","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","mcwwindows:pink_mosaic_glass_pane","mcwdoors:oak_barn_door","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","botania:redstone_root","alltheores:gold_rod","additionallanterns:smooth_stone_chain","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","mcwlights:mangrove_ceiling_fan_light","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","biomesoplenty:magic_boat","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","mcwbiomesoplenty:maple_pane_window","bigreactors:turbine/reinforced/activefluidport_forge","mcwfurnitures:oak_bookshelf_cupboard","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","supplementaries:key","mcwbridges:stone_brick_bridge","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:white_concrete_upper_steep_roof","mcwroofs:cyan_concrete_attic_roof","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:cobbled_deepslate_lantern","mcwtrpdoors:oak_tropical_trapdoor","mcwdoors:jail_door","mcwfences:blackstone_railing_gate","dyenamics:banner/ultramarine_banner","minecraft:dried_kelp_from_smelting","minecraft:polished_granite_slab_from_granite_stonecutting","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwroofs:white_concrete_attic_roof","securitycraft:reinforced_mossy_stone_bricks_from_vine","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","mcwbiomesoplenty:redwood_window2","additionallanterns:obsidian_chain","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","securitycraft:security_camera","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:magic_window2","mcwlights:cross_lantern","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","railcraft:receiver_circuit","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","enderio:wood_gear_corner","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","allthecompressed:compress/granite_1x","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","mcwroofs:blackstone_top_roof","immersiveengineering:crafting/connector_hv_relay","supplementaries:candle_holders/candle_holder_pink","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","ae2additions:super_cell_housing","cfm:brown_trampoline","supplementaries:stonecutting/stone_tile","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","mcwtrpdoors:jungle_mystic_trapdoor","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","securitycraft:reinforced_oak_fence","create:polished_cut_granite_stairs_from_stone_types_granite_stonecutting","mcwlights:yellow_paper_lamp","mcwbridges:balustrade_granite_bridge","domum_ornamentum:cream_bricks","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwlights:warped_tiki_torch","dyenamics:persimmon_stained_glass","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","forbidden_arcanus:arcane_chiseled_darkstone","mcwroofs:jungle_lower_roof","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","securitycraft:keycard_lv5","securitycraft:keycard_lv4","mcwroofs:oak_planks_upper_lower_roof","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","handcrafted:oak_nightstand","railcraft:personal_world_spike","dyenamics:wine_stained_glass","mcwlights:lava_lamp","domum_ornamentum:light_gray_floating_carpet","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwpaths:andesite_clover_paving","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","cfm:magenta_grill","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","cfm:jungle_table","aether:iron_chestplate_repairing","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","mcwbiomesoplenty:empyreal_pyramid_gate","supplementaries:slingshot","mcwwindows:cherry_pane_window","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","everythingcopper:copper_rail","mcwdoors:garage_white_door","cfm:black_grill","utilitix:armed_stand","botania:lexicon","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwlights:green_paper_lamp","mcwwindows:birch_plank_parapet","supplementaries:spring_launcher","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","corail_woodcutter:jungle_woodcutter","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","supplementaries:daub","create:small_granite_bricks_from_stone_types_granite_stonecutting","mcwfurnitures:stripped_jungle_end_table","minecraft:iron_pickaxe","mcwfences:oak_hedge","minecraft:cut_sandstone","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","enderio:fluid_tank","mcwroofs:oak_planks_roof","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","mcwpaths:stone_flagstone","deepresonance:resonating_plate","bigreactors:energizer/casing","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","xnet:connector_blue","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","rftoolsutility:fluid_module","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","simplylight:illuminant_black_block_on_dyed","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","mcwfences:spruce_highley_gate","minecraft:birch_boat","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","cfm:post_box","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","minecraft:campfire","mcwdoors:jungle_barn_door","handcrafted:oak_drawer","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","securitycraft:reinforced_birch_fence","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:maple_highley_gate","cfm:orange_cooler","farmersdelight:cooking/chicken_soup","mcwpaths:granite_dumble_paving","mcwpaths:granite_flagstone_slab","minecraft:lime_stained_glass","aether:skyroot_note_block","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","create:cut_granite_brick_slab_from_stone_types_granite_stonecutting","mcwroofs:granite_upper_steep_roof","mcwlights:bell_wall_lantern","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:8379,warning_level:0}}