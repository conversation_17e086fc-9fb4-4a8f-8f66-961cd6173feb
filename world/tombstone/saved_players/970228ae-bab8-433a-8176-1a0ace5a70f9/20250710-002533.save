{AbsorptionAmount:0.0f,ActiveEffects:[{Ambient:0b,Amplifier:1b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:8,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:jump_boost"},{Ambient:0b,Amplifier:0b,CurativeItems:[{Count:1b,id:"minecraft:milk_bucket"}],Duration:38,Id:16,ShowIcon:0b,ShowParticles:0b,"forge:id":"minecraft:night_vision"}],Air:300s,Attributes:[{Base:3.0d,Name:"forge:entity_reach"},{Base:1.0d,Name:"irons_spellbooks:mana_regen"},{Base:100.0d,Name:"irons_spellbooks:max_mana"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.feather"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.warding"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:1.0d,Name:"attributeslib:experience_gained"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:1.0d,Name:"irons_spellbooks:spell_resist"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:4.5d,Name:"forge:block_reach"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:1.0d,Name:"irons_spellbooks:cast_time_reduction"},{Base:0.0d,Modifiers:[{Amount:1.0d,Name:"artifacts:steadfast_spikes_knockback_resistance",Operation:0,UUID:[I;-706276632,1065698154,-1122152339,2032106173]}],Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:1.0d,Name:"eidolon:chanting_speed"},{Base:1.0d,Name:"attributeslib:mining_speed"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.0d,Name:"minecraft:generic.luck"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:4.0d,Modifiers:[{Amount:0.4d,Name:"artifacts:feral_claws_attack_speed_bonus",Operation:0,UUID:[I;-**********,-**********,-**********,**********]}],Name:"minecraft:generic.attack_speed"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.wixie"},{Base:1.0d,Modifiers:[{Amount:4.0d,Name:"artifacts:power_glove_attack_damage_bonus",Operation:0,UUID:[I;308939635,-**********,-**********,-702737548]}],Name:"minecraft:generic.attack_damage"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"}],BalmData:{},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:0b,Slot:0b,id:"minecraft:air",tag:{}},{Count:0b,Slot:1b,id:"minecraft:air",tag:{}},{Count:0b,Slot:2b,id:"minecraft:air",tag:{}},{Count:0b,Slot:3b,id:"minecraft:air",tag:{}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;-**********,-559854368,-**********,**********],hasTentacle:0b},"comforts:sleep_data":{sleepTime:0L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:obsidian_skull"}],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"darkutils:charm_warding"}],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{CachedModifiers:[],Cosmetics:{Items:[],Size:42},DropRule:"DEFAULT",HasCosmetic:0b,PersistentModifiers:[{Amount:40.0d,Name:"legacy",Operation:0,UUID:[I;185510845,1109413535,-1157942256,228769150]}],RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1},{Render:0b,Slot:2},{Render:0b,Slot:3},{Render:0b,Slot:4},{Render:0b,Slot:5},{Render:0b,Slot:6},{Render:0b,Slot:7},{Render:0b,Slot:8},{Render:0b,Slot:9},{Render:0b,Slot:10},{Render:0b,Slot:11},{Render:0b,Slot:12},{Render:0b,Slot:13},{Render:0b,Slot:14},{Render:0b,Slot:15},{Render:0b,Slot:16},{Render:0b,Slot:17},{Render:0b,Slot:18},{Render:0b,Slot:19},{Render:0b,Slot:20},{Render:0b,Slot:21},{Render:0b,Slot:22},{Render:0b,Slot:23},{Render:0b,Slot:24},{Render:0b,Slot:25},{Render:0b,Slot:26},{Render:0b,Slot:27},{Render:0b,Slot:28},{Render:0b,Slot:29},{Render:0b,Slot:30},{Render:0b,Slot:31},{Render:0b,Slot:32},{Render:0b,Slot:33},{Render:0b,Slot:34},{Render:0b,Slot:35},{Render:0b,Slot:36},{Render:0b,Slot:37},{Render:0b,Slot:38},{Render:0b,Slot:39},{Render:0b,Slot:40},{Render:0b,Slot:41}],Size:42},SavedBaseSize:2,Stacks:{Items:[{Count:1b,Slot:0,id:"artifacts:night_vision_goggles"},{Count:1b,Slot:1,id:"artifacts:plastic_drinking_hat"},{Count:1b,Slot:2,id:"artifacts:novelty_drinking_hat"},{Count:1b,Slot:3,id:"artifacts:superstitious_hat"},{Count:1b,Slot:4,id:"artifacts:anglers_hat"},{Count:1b,Slot:5,id:"artifacts:lucky_scarf"},{Count:1b,Slot:6,id:"artifacts:cross_necklace",tag:{CanApplyBonus:1b}},{Count:1b,Slot:7,id:"artifacts:antidote_vessel"},{Count:1b,Slot:8,id:"artifacts:digging_claws"},{Count:1b,Slot:9,id:"artifacts:feral_claws"},{Count:1b,Slot:10,id:"artifacts:power_glove"},{Count:1b,Slot:11,id:"artifacts:vampiric_glove"},{Count:1b,Slot:12,id:"artifacts:golden_hook"},{Count:1b,Slot:13,id:"artifacts:pickaxe_heater"},{Count:1b,Slot:14,id:"artifacts:steadfast_spikes"},{Count:1b,Slot:15,id:"darkutils:charm_sleep"},{Count:1b,Slot:16,id:"eidolon:prestigious_palm"},{Count:1b,Slot:17,id:"artifacts:bunny_hoppers"},{Count:1b,Slot:18,id:"irons_spellbooks:fireward_ring"},{Count:1b,Slot:19,id:"irons_spellbooks:emerald_stoneplate_ring"},{Count:1b,Slot:20,id:"irons_spellbooks:heavy_chain_necklace"},{Count:1b,Slot:21,id:"irons_spellbooks:cast_time_ring"},{Count:1b,Slot:22,id:"irons_spellbooks:cooldown_ring"},{Count:1b,Slot:23,id:"irons_spellbooks:silver_ring"},{Count:1b,Slot:24,id:"irons_spellbooks:mana_ring"},{Count:1b,Slot:25,id:"irons_spellbooks:frostward_ring"},{Count:1b,Slot:26,id:"irons_spellbooks:poisonward_ring"},{Count:1b,Slot:27,id:"irons_spellbooks:amethyst_resonance_charm"},{Count:1b,Slot:28,id:"botania:super_travel_belt"},{Count:1b,Slot:29,id:"eidolon:enervating_ring"},{Count:1b,Slot:30,id:"mythicbotany:aura_ring_greatest"},{Count:1b,Slot:31,id:"mythicbotany:mana_ring_greatest",tag:{mana:201105}}],Size:42},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1},{Render:0b,Slot:2},{Render:0b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[{Count:1b,Slot:0,id:"sophisticatedbackpacks:netherite_backpack",tag:{contentsUuid:[I;390048106,-673756075,-1938614503,949916074],inventorySlots:120,openTabId:0,renderInfo:{upgradeItems:[{Count:1b,id:"sophisticatedbackpacks:crafting_upgrade"},{Count:1b,id:"sophisticatedbackpacks:advanced_feeding_upgrade",tag:{feedAtHungerLevel:"any",filters:{Items:[{Count:1b,Slot:0,id:"artifacts:eternal_steak"},{Count:1b,Slot:1,id:"artifacts:everlasting_beef"}],Size:16},isAllowList:1b}},{Count:1b,id:"sophisticatedbackpacks:advanced_magnet_upgrade",tag:{filterByStorage:1b}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"minecraft:air",tag:{}},{Count:1b,id:"sophisticatedbackpacks:stack_upgrade_omega_tier"}]},upgradeSlots:7}}],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0},{Render:0b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:0b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:275,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:90540},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:3},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"allthemodium:allthemodium_nugget_from_ingot",ItemStack:{Count:9b,id:"allthemodium:allthemodium_nugget"}}]},"rftoolsutility:properties":{allowFlying:0b,buffTicks:1,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:1b,isshrunk:0b,scale:0.21f,width:0.0f},"solcarrot:food":{foodList:["artifacts:eternal_steak"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:7,perks:[{id:3s,level:2b}]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{ReturnPortalList:[{FromDim:"minecraft:the_nether",FromPos:274877898813L,ToAxis1Size:3,ToAxis2Size:3,ToMinCorner:37383395778533L,UID:[I;1818712172,-*********,-1126065099,-1002846341]},{FromDim:"minecraft:overworld",FromPos:-824633905086L,ToAxis1Size:2,ToAxis2Size:3,ToMinCorner:274877898813L,UID:[I;1092338285,1093092712,-1258036536,-*********]}],TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:[]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"allthemodium:mining",tb_last_ground_location_x:-952,tb_last_ground_location_y:253,tb_last_ground_location_z:-327,tb_last_offhand_item:"minecraft:air",twilightforest_banished:1b},apoth_reforge_seed:997815291,"quark:trying_crawl":0b,simplylight_unlocked:2,sophisticatedBackpackSettings:{}},Health:20.0f,HurtByTimestamp:12137,HurtTime:0s,Inventory:[{Count:26b,Slot:9b,id:"minecraft:oak_planks"},{Count:10b,Slot:10b,id:"minecraft:glass"},{Count:1b,Slot:11b,id:"minecraft:dirt"},{Count:1b,Slot:12b,id:"minecraft:water_bucket"},{Count:1b,Slot:13b,id:"minecraft:brush",tag:{Damage:1}},{Count:1b,Slot:14b,id:"allthemodium:allthemodium_nugget"},{Count:1b,Slot:15b,id:"allthemodium:alloy_paxel",tag:{Enchantments:[{id:"apotheosis:miners_fervor",lvl:5s},{id:"minecraft:fortune",lvl:8s},{id:"evilcraft:life_stealing",lvl:6s},{id:"apotheosis:scavenger",lvl:3s},{id:"minecraft:looting",lvl:8s}],RepairCost:31,affix_data:{affixes:{"apotheosis:breaker/attribute/destructive":1.0f,"apotheosis:breaker/attribute/experienced":1.0f,"apotheosis:breaker/attribute/lengthy":1.0f,"apotheosis:breaker/attribute/lucky":1.0f,"apotheosis:breaker/special/enlightened":1.0f,"apotheosis:breaker/special/omnetic":1.0f,"apotheosis:telepathic":1.0f},gems:[{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:ancient"},gem:"apotheosis:overworld/earth"}},{Count:1b,id:"apotheosis:gem",tag:{affix_data:{rarity:"apotheosis:ancient"},gem:"apotheosis:the_end/endersurge"}},{Count:0b,id:"minecraft:air",tag:{Charged:0b}}],name:'{"color":"#ED7014","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:breaker/special/omnetic"},"",{"translate":"affix.apotheosis:breaker/attribute/experienced.suffix"}]}',rarity:"apotheosis:mythic",sockets:3,uuids:[[I;-421150767,-835042629,-1465530726,946707014]]}}},{Count:1b,Slot:16b,id:"allthemodium:teleport_pad"},{Count:1b,Slot:100b,id:"minecraft:iron_boots",tag:{Damage:15,Enchantments:[{id:"minecraft:fire_protection",lvl:2s}],affix_data:{affixes:{"apotheosis:armor/attribute/stalwart":0.65391093f,"apotheosis:armor/dmg_reduction/feathery":0.47197884f,"apotheosis:durable":0.13f,"irons_spellbooks:armor/attribute/cooldown":0.7432037f,"irons_spellbooks:armor/attribute/spell_resist":0.2161668f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_resist"},"",{"translate":"affix.apotheosis:armor/attribute/stalwart.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;-248386340,-113817461,-1407838129,-1437069820]]}}},{Count:1b,Slot:101b,id:"minecraft:iron_leggings",tag:{Damage:6,Enchantments:[{id:"minecraft:fire_protection",lvl:3s}],affix_data:{affixes:{"apotheosis:armor/attribute/ironforged":0.8978907f,"irons_spellbooks:armor/attribute/cooldown":0.47664762f,"irons_spellbooks:armor/attribute/spell_resist":0.18819135f},name:'{"italic":false,"color":"#33FF33","translate":"misc.apotheosis.affix_name.four","with":["Sandra\'s",{"translate":"affix.apotheosis:armor/attribute/ironforged"},"",{"translate":"affix.irons_spellbooks:armor/attribute/cooldown.suffix"}]}',rarity:"apotheosis:uncommon",sockets:1,uuids:[[I;1270799571,1369000677,-1782250961,1241766737]]},apoth_boss:1b,display:{Name:'{"extra":[{"text":"Pants"}],"text":""}'}}},{Count:1b,Slot:102b,id:"minecraft:iron_chestplate",tag:{Damage:0,affix_data:{affixes:{"apotheosis:armor/attribute/gravitational":0.21295738f,"apotheosis:armor/dmg_reduction/blast_forged":0.43555927f,"apotheosis:durable":0.13f,"irons_spellbooks:armor/attribute/mana":0.73035914f,"irons_spellbooks:armor/attribute/spell_power":0.096836746f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.apotheosis:armor/attribute/gravitational"},"",{"translate":"affix.irons_spellbooks:armor/attribute/mana.suffix"}]}',rarity:"apotheosis:rare",sockets:1,uuids:[[I;-1046819102,1467105548,-1553607551,-779806150]]},apoth_rchest:1b}}],Invulnerable:0b,LastDeathLocation:{dimension:"minecraft:overworld",pos:[I;-1093,-43,-316]},Motion:[0.0d,-0.06272000098705292d,0.0d],OnGround:0b,PortalCooldown:0,Pos:[-1470.8952306933106d,62.073264324665075d,75.23053805523648d],Railways_DataVersion:2,Rotation:[84.06349f,15.3001175f],Score:4936,SelectedItemSlot:2,SleepTimer:0s,Spigot.ticksLived:90535,UUID:[I;-1761466194,-1162329286,-2122966518,-832933639],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":-257010844462947L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:18,XpP:0.86538494f,XpSeed:1943878380,XpTotal:486,abilities:{flySpeed:0.05f,flying:1b,instabuild:1b,invulnerable:1b,mayBuild:1b,mayfly:1b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1752060955368L,keepLevel:0b,lastKnownName:"Elle_Fanning",lastPlayed:1752078333324L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.6239111f,foodLevel:20,foodSaturationLevel:1.0f,foodTickTimer:0,playerGameType:1,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","handcrafted:spider_trophy","mcwpaths:cobbled_deepslate_crystal_floor_path","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","mcwpaths:granite_windmill_weave_slab","cfm:jungle_coffee_table","xnet:connector_green","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","create:cut_diorite_from_stone_types_diorite_stonecutting","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwwindows:oak_plank_pane_window","reliquary:mob_charm_fragments/cave_spider","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwpaths:granite_running_bond_slab","twigs:polished_rhyolite_stonecutting","supplementaries:candle_holders/candle_holder_green","aether:skyroot_fletching_table","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","dyenamics:banner/icy_blue_banner","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","supplementaries:bubble_blower","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","mcwbiomesoplenty:mahogany_plank_pane_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","mcwwindows:acacia_curtain_rod","mcwlights:chain_lantern","securitycraft:camera_monitor","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:oak_kitchen_counter","mcwpaths:diorite_running_bond_stairs","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","mcwpaths:diorite_running_bond_slab","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","occultism:crafting/magic_lamp_empty","railcraft:brass_ingot_crafted_with_ingots","mcwpaths:sandstone_running_bond_stairs","create:granite_from_stone_types_granite_stonecutting","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","twigs:mossy_bricks_from_vine","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","securitycraft:whitelist_module","mcwbiomesoplenty:pine_plank_window2","mcwwindows:warped_louvered_shutter","xnet:connector_routing","mcwbiomesoplenty:palm_plank_window2","create:cut_diorite_wall_from_stone_types_diorite_stonecutting","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","utilitix:directional_highspeed_rail","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","create:small_diorite_bricks_from_stone_types_diorite_stonecutting","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","create:cut_granite_bricks_from_stone_types_granite_stonecutting","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","cfm:warped_mail_box","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","cfm:orange_grill","mcwwindows:dark_oak_shutter","allthecompressed:compress/diorite_1x","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_blue_concrete_lower_roof","mcwfurnitures:jungle_drawer","mcwfences:nether_brick_pillar_wall","mcwbiomesoplenty:willow_curved_gate","delightful:knives/osmium_knife","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","minecraft:granite_stairs_from_granite_stonecutting","mcwbiomesoplenty:maple_curved_gate","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","cfm:green_kitchen_sink","mcwfences:diorite_railing_gate","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwwindows:birch_plank_pane_window","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","mcwbiomesoplenty:fir_hedge","mcwfences:birch_highley_gate","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwdoors:oak_cottage_door","create:diorite_from_stone_types_diorite_stonecutting","immersiveengineering:crafting/torch","mcwbridges:diorite_bridge_stair","mcwfences:warped_pyramid_gate","delightful:food/baklava","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","dyenamics:dye_conifer_carpet","mcwbiomesoplenty:pine_window2","create:small_diorite_brick_stairs_from_stone_types_diorite_stonecutting","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","create:small_diorite_brick_wall_from_stone_types_diorite_stonecutting","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","croptopia:chicken_and_dumplings","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwpaths:granite_flagstone_path","mcwtrpdoors:jungle_paper_trapdoor","mcwroofs:diorite_upper_lower_roof","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","mcwlights:upgraded_torch","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","handcrafted:diorite_pillar_trim","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","mcwroofs:orange_concrete_top_roof","additionallanterns:granite_chain","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","create:small_granite_brick_wall_from_stone_types_granite_stonecutting","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwpaths:granite_honeycomb_paving","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","croptopia:fried_chicken","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","cfm:stripped_oak_chair","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","cfm:rock_path","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwpaths:diorite_honeycomb_paving","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","allthemodium:allthemodium_ingot_from_raw_smelting","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","mcwwindows:metal_curtain_rod","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","domum_ornamentum:black_floating_carpet","supplementaries:daub_brace","minecraft:diorite_stairs_from_diorite_stonecutting","mcwfences:spruce_horse_fence","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","mcwpaths:granite_windmill_weave","mcwpaths:granite_windmill_weave_path","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","mcwpaths:granite_running_bond_path","supplementaries:candle_holders/candle_holder_red","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwbiomesoplenty:fir_horse_fence","additionallanterns:bricks_lantern","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","create:cut_diorite_stairs_from_stone_types_diorite_stonecutting","botania:petal_gray","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","create:polished_cut_granite_wall_from_stone_types_granite_stonecutting","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","create:cut_granite_stairs_from_stone_types_granite_stonecutting","mcwbiomesoplenty:stripped_umbran_log_window","mcwfurnitures:jungle_stool_chair","create:polished_cut_diorite_slab_from_stone_types_diorite_stonecutting","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","mcwroofs:cobblestone_roof","securitycraft:reinforced_mossy_cobblestone_from_vine","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","mcwfences:bamboo_highley_gate","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","mcwroofs:jungle_roof","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","supplementaries:daub_frame","railcraft:signal_tuner","cfm:pink_kitchen_counter","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:banner/mint_banner","mcwpaths:diorite_running_bond_path","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwpaths:andesite_crystal_floor_stairs","mcwroofs:sandstone_lower_roof","mcwwindows:oak_plank_four_window","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","domum_ornamentum:green_floating_carpet","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","minecraft:carrot_on_a_stick","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwpaths:diorite_basket_weave_paving","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","mcwwindows:stone_window2","minecraft:oak_button","minecraft:granite_wall","simplylight:illuminant_purple_block_on_dyed","handcrafted:granite_corner_trim","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","allthemodium:allthemodium_apple","expatternprovider:silicon_block","itemcollectors:basic_collector","minecraft:loom","mcwtrpdoors:oak_bark_trapdoor","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","mcwroofs:sandstone_attic_roof","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","handcrafted:golden_wide_pot","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","mcwpaths:granite_running_bond_stairs","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwpaths:granite_flagstone","create:cut_granite_brick_wall_from_stone_types_granite_stonecutting","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","alltheores:nickel_plate","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","mcwpaths:diorite_crystal_floor_slab","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","cfm:brown_cooler","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","mcwpaths:granite_crystal_floor_stairs","utilitix:experience_crystal","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","minecraft:respawn_anchor","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","paraglider:paraglider","minecraft:deepslate_brick_slab","dankstorage:dock","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","mcwbridges:jungle_bridge_pier","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","minecraft:jukebox","mcwwindows:jungle_curtain_rod","create:layered_granite_from_stone_types_granite_stonecutting","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","dyenamics:dye_spring_green_carpet","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwpaths:granite_clover_paving","reliquary:uncrafting/spider_eye","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","railcraft:iron_gear","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","handcrafted:oak_pillar_trim","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","create:framed_glass_from_glass_colorless_stonecutting","mcwpaths:diorite_windmill_weave_stairs","mcwpaths:diorite_diamond_paving","cfm:jungle_blinds","tombstone:bone_needle","minecraft:stone_brick_wall","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","aquaculture:gold_fillet_knife","dyenamics:banner/navy_banner","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","mcwfences:warped_curved_gate","botania:light_gray_shiny_flower","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","mcwpaths:diorite_windmill_weave_path","cfm:oak_upgraded_gate","croptopia:cashew_chicken","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwpaths:diorite_flagstone","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","alltheores:lead_rod","mcwwindows:oak_plank_window","mcwroofs:granite_roof","mcwwindows:oak_log_parapet","supplementaries:notice_board","aquaculture:dark_oak_fish_mount","littlelogistics:spring","securitycraft:reinforced_bamboo_fence","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:acacia_hedge","minecraft:golden_apple","minecraft:diamond_pickaxe","domum_ornamentum:blockbarreldeco_onside","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","mcwbiomesoplenty:maple_four_window","cfm:blue_kitchen_counter","utilitarian:redstone_clock","handcrafted:granite_pillar_trim","mcwwindows:stripped_oak_log_window","securitycraft:harming_module","silentgear:bort_block","minecraft:golden_boots","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","supplementaries:flags/flag_cyan","cfm:pink_grill","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwpaths:diorite_running_bond","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwwindows:purple_mosaic_glass","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwpaths:granite_windmill_weave_stairs","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","domum_ornamentum:gray_floating_carpet","cfm:stripped_jungle_park_bench","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:light_blue_concrete_powder","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","minecraft:granite_stairs","mcwwindows:light_blue_mosaic_glass","silentgear:fine_silk","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","mcwpaths:granite_square_paving","cfm:oak_upgraded_fence","deepresonance:radiation_monitor","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","cfm:spruce_kitchen_sink_light","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","mcwpaths:andesite_flagstone","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:dead_plank_window2","minecraft:golden_axe","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","immersiveengineering:crafting/wire_lead","minecraft:cyan_stained_glass","mcwroofs:red_concrete_roof","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","mcwroofs:diorite_steep_roof","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwroofs:light_gray_concrete_lower_roof","twigs:rhyolite_slab","immersiveengineering:crafting/stick_iron","create:polished_cut_granite_from_stone_types_granite_stonecutting","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","sfm:fancy_to_cable","minecraft:mossy_cobblestone_from_vine","aquaculture:heavy_hook","minecraft:white_concrete_powder","mcwwindows:acacia_window","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","cfm:black_picket_fence","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwroofs:orange_concrete_lower_roof","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","cfm:spruce_mail_box","silentgear:bort_from_block","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","alltheores:zinc_rod","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:pink_concrete_powder","minecraft:cauldron","mcwbridges:glass_bridge_pier","delightful:smelting/roasted_acorn","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","aether:iron_boots_repairing","mcwfurnitures:oak_kitchen_cabinet","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","rftoolsutility:module_template","tombstone:impregnated_diamond","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwfences:dark_oak_curved_gate","mcwpaths:granite_basket_weave_paving","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","cfm:magenta_trampoline","supplementaries:pulley","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","allthecompressed:compress/glass_1x","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","dyenamics:dye_persimmon_carpet","travelersbackpack:standard","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_2","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","create:cut_diorite_bricks_from_stone_types_diorite_stonecutting","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","everythingcopper:copper_leggings","mcwroofs:gutter_base_pink","mcwbridges:andesite_bridge_stair","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwlights:iron_framed_torch","pneumaticcraft:ender_visor_upgrade","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","cfm:cyan_cooler","railcraft:silver_gear","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwpaths:diorite_crystal_floor","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwfences:spruce_picket_fence","travelersbackpack:redstone","minecraft:slime_block","mcwroofs:granite_upper_lower_roof","create:copycat_panel_from_ingots_zinc_stonecutting","mcwwindows:iron_shutter","travelersbackpack:emerald","croptopia:trail_mix","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:sandstone_flagstone","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","allthemodium:allthemodium_gear","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","minecraft:diorite_wall","mcwfurnitures:stripped_oak_double_drawer","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwpaths:cobbled_deepslate_basket_weave_paving","minecraft:cracked_stone_bricks","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwbridges:granite_bridge_pier","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","railcraft:nickel_gear","minecraft:iron_trapdoor","minecraft:deepslate_brick_wall","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","railcraft:zinc_gear","minecraft:deepslate_brick_stairs_from_deepslate_bricks_stonecutting","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","create:cut_granite_from_stone_types_granite_stonecutting","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","securitycraft:reinforced_warped_fence_gate","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","mythicbotany:rune_holder","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwroofs:diorite_attic_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","mcwpaths:diorite_crystal_floor_stairs","reliquary:uncrafting/string","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwtrpdoors:jungle_whispering_trapdoor","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwroofs:granite_lower_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","securitycraft:storage_module","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","minecraft:polished_diorite_from_diorite_stonecutting","cfm:cyan_trampoline","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","mcwpaths:diorite_flagstone_stairs","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","botania:water_ring","twigs:bone_meal_from_seashells","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","aether:iron_axe_repairing","reliquary:uncrafting/bone","supplementaries:sack","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","mcwwindows:stripped_oak_pane_window","mcwbridges:diorite_bridge_pier","additionallanterns:netherite_lantern","create:copycat_step_from_ingots_zinc_stonecutting","mcwroofs:light_blue_concrete_upper_lower_roof","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","minecraft:brown_dye","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwpaths:andesite_crystal_floor","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","create:polished_cut_diorite_stairs_from_stone_types_diorite_stonecutting","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","xnet:netcable_red_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","alltheores:gold_rod","additionallanterns:smooth_stone_chain","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwpaths:diorite_square_paving","supplementaries:key","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwroofs:white_concrete_upper_steep_roof","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:cobbled_deepslate_lantern","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","minecraft:dried_kelp_from_smelting","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_window2","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","additionallanterns:normal_sandstone_lantern","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","mcwtrpdoors:jungle_mystic_trapdoor","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","create:polished_cut_granite_stairs_from_stone_types_granite_stonecutting","mcwbridges:balustrade_granite_bridge","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","handcrafted:oak_nightstand","railcraft:personal_world_spike","mcwlights:lava_lamp","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","supplementaries:slingshot","allthemodium:allthemodium_plate","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","everythingcopper:copper_rail","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","mcwlights:green_paper_lamp","supplementaries:spring_launcher","dyenamics:dye_ultramarine_carpet","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","aquaculture:neptunium_ingot_from_nuggets","create:small_granite_bricks_from_stone_types_granite_stonecutting","minecraft:iron_pickaxe","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","minecraft:deepslate_tiles","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","cfm:orange_cooler","minecraft:lime_stained_glass","dyenamics:dye_wine_carpet","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwpaths:stone_crystal_floor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:granite_attic_roof","mcwroofs:sandstone_upper_lower_roof","chimes:copper_chimes","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","dyenamics:dye_icy_blue_carpet","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwbridges:granite_bridge_stair","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","dyenamics:dye_honey_carpet","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwroofs:purple_concrete_attic_roof","minecraft:polished_granite_from_granite_stonecutting","constructionwand:core_angel","xnet:connector_red_dye","minecolonies:shapetool","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","delightful:knives/tin_knife","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwwindows:andesite_louvered_shutter","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","tombstone:ankh_of_prayer","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","cfm:black_kitchen_drawer","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","create:crafting/logistics/powered_latch","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","minecraft:cracked_deepslate_bricks","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","handcrafted:oak_bench","mcwfences:nether_brick_grass_topped_wall","utilitarian:angel_block","mcwroofs:diorite_roof","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","dyenamics:spring_green_concrete_powder","cfm:jungle_cabinet","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","create:cut_diorite_slab_from_stone_types_diorite_stonecutting","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","additionallanterns:deepslate_bricks_chain","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","minecraft:oak_pressure_plate","minecraft:stone_brick_stairs","bigreactors:turbine/basic/activefluidport_forge","cfm:brown_grill","mcwwindows:quartz_window","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","reliquary:mob_charm_fragments/creeper","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:clock","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwroofs:diorite_lower_roof","mcwwindows:warped_curtain_rod","railcraft:lead_gear","mcwpaths:diorite_windmill_weave","dyenamics:dye_peach_carpet","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","alltheores:tin_rod","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","mcwroofs:jungle_upper_steep_roof","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:polished_diorite_slab_from_diorite_stonecutting","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","mcwwindows:andesite_four_window","minecraft:granite_slab","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","mcwlights:festive_lantern","securitycraft:block_change_detector","twigs:rocky_dirt","mcwpaths:granite_flagstone_stairs","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwwindows:birch_curtain_rod","create:crafting/kinetics/fluid_pipe","minecolonies:doublefern","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","create:small_granite_brick_slab_from_stone_types_granite_stonecutting","mcwpaths:red_sand_path_block","cfm:light_blue_cooler","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","minecraft:sandstone_slab","mcwwindows:birch_window","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwbridges:balustrade_diorite_bridge","mcwfences:mud_brick_railing_gate","twigs:oak_table","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","mcwfurnitures:oak_lower_triple_drawer","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwdoors:jungle_japanese_door","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","mcwpaths:granite_diamond_paving","cfm:purple_kitchen_drawer","botania:flower_bag","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","cfm:jungle_kitchen_counter","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","minecraft:oak_sign","mcwwindows:stone_pane_window","mcwpaths:diorite_flagstone_slab","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwbridges:deepslate_brick_bridge_pier","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","mcwbridges:balustrade_andesite_bridge","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","create:crafting/kinetics/hose_pulley","eidolon:smooth_stone_masonry_stonecutter_0","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","deeperdarker:bloom_boat","create:small_diorite_brick_slab_from_stone_types_diorite_stonecutting","utilitix:linked_repeater","minecolonies:doublegrass","dyenamics:rose_stained_glass","farmersdelight:cooking/hot_cocoa","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","create:diorite_pillar_from_stone_types_diorite_stonecutting","supplementaries:candle_holders/candle_holder_light_gray","reliquary:mob_charm_fragments/zombified_piglin","mcwwindows:warped_planks_four_window","create:polished_cut_diorite_from_stone_types_diorite_stonecutting","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwroofs:oak_planks_attic_roof","rftoolsutility:moduleplus_template","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","mcwbridges:stone_bridge_pier","railcraft:radio_circuit","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","create:crafting/logistics/powered_toggle_latch","supplementaries:flags/flag_lime","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","cfm:brown_kitchen_counter","mcwroofs:granite_top_roof","mcwroofs:lime_concrete_attic_roof","securitycraft:alarm","mcwfurnitures:oak_counter","rftoolsutility:charged_porter","minecraft:polished_diorite","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","create:crafting/materials/andesite_alloy_from_zinc","mcwroofs:oak_top_roof","mcwpaths:sandstone_honeycomb_paving","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwwindows:stone_window","botania:petal_light_gray","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","alchemistry:compactor","minecraft:deepslate_brick_stairs","travelersbackpack:sandstone","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwpaths:granite_crystal_floor_path","mcwwindows:oak_window2","create:cut_granite_slab_from_stone_types_granite_stonecutting","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","create:crafting/kinetics/rose_quartz_lamp","immersiveengineering:crafting/plate_nickel_hammering","mcwpaths:diorite_clover_paving","utilitarian:utility/oak_logs_to_slabs","mcwbridges:balustrade_deepslate_bricks_bridge","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","botania:gray_shiny_flower","mcwbiomesoplenty:magic_pyramid_gate","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","dyenamics:dye_rose_carpet","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","alltheores:nickel_rod","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","twigs:polished_tuff_stonecutting","reliquary:mob_charm_fragments/skeleton","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","mcwfences:modern_andesite_wall","mcwpaths:granite_crystal_floor","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","cfm:lime_kitchen_drawer","mcwbiomesoplenty:redwood_pane_window","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","allthemodium:allthemodium_nugget_from_ingot","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","buildinggadgets2:gadget_copy_paste","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","cfm:orange_kitchen_counter","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","mcwbridges:granite_bridge","minecraft:diamond_block","handcrafted:diorite_corner_trim","mcwroofs:deepslate_roof","minecraft:deepslate_tile_stairs_from_deepslate_bricks_stonecutting","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","minecraft:diorite_slab","dyenamics:cherenkov_stained_glass","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","create:cut_diorite_brick_slab_from_stone_types_diorite_stonecutting","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","mcwfurnitures:stripped_jungle_covered_desk","cfm:black_picket_gate","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","immersiveengineering:crafting/plate_lead_hammering","simplylight:illuminant_blue_block_on_toggle","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwfurnitures:stripped_jungle_large_drawer","minecraft:glass_bottle","mcwfences:mesh_metal_fence","minecraft:deepslate_brick_slab_from_deepslate_bricks_stonecutting","minecraft:polished_granite","mcwbiomesoplenty:magic_plank_window2","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwroofs:diorite_upper_steep_roof","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","alltheores:zinc_plate","mcwwindows:prismarine_pane_window","mcwpaths:diorite_flagstone_path","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:slice_map","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","securitycraft:reinforced_sticky_piston","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","minecraft:cooked_chicken_from_campfire_cooking","mcwfurnitures:jungle_modern_chair","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","modularrouters:modular_router","delightful:knives/nickel_knife","mcwfurnitures:stripped_jungle_coffee_table","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","mcwfurnitures:stripped_jungle_double_drawer","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","minecraft:bucket","handcrafted:salmon_trophy","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","farmersdelight:chicken_sandwich","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwpaths:andesite_honeycomb_paving","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","mcwroofs:black_concrete_attic_roof","minecraft:charcoal","minecraft:diorite_wall_from_diorite_stonecutting","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","dyenamics:dye_amber_carpet","cfm:white_picket_fence","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","twigs:polished_tuff_bricks_from_tuff_stonecutting","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","sophisticatedstorage:storage_stack_upgrade_omega_tier_from_backpack_stack_upgrade_omega_tier","dyenamics:banner/aquamarine_banner","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwpaths:sandstone_dumble_paving","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwbridges:andesite_bridge","mcwwindows:dark_prismarine_pane_window","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_3","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","minecraft:deepslate_brick_wall_from_deepslate_bricks_stonecutting","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","minecraft:andesite_wall_from_andesite_stonecutting","mcwfences:crimson_stockade_fence","mcwbiomesoplenty:jacaranda_four_window","minecraft:conduit","pneumaticcraft:magnet_upgrade","mcwbridges:iron_bridge_pier","mcwwindows:mangrove_blinds","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","mcwwindows:diorite_pane_window","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","mcwpaths:granite_crystal_floor_slab","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwroofs:magenta_concrete_upper_lower_roof","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwwindows:white_mosaic_glass_pane","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:fishing_rod","xnet:connector_yellow_dye","delightful:knives/silver_knife","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","minecraft:granite_wall_from_granite_stonecutting","supplementaries:timber_frame","sophisticatedstorage:packing_tape","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","railcraft:tin_gear","mcwlights:double_street_lamp","minecraft:tnt","minecraft:mossy_stone_bricks_from_vine","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","mcwroofs:diorite_top_roof","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","create:cut_granite_wall_from_stone_types_granite_stonecutting","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","xnet:advanced_connector_red_dye","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","securitycraft:reinforced_lime_stained_glass_pane_from_dye","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:stone_tile","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","minecraft:granite_slab_from_granite_stonecutting","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","create:polished_cut_granite_slab_from_stone_types_granite_stonecutting","minecraft:clay","mcwbridges:deepslate_brick_bridge","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwpaths:andesite_basket_weave_paving","cfm:red_kitchen_counter","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwpaths:granite_running_bond","mcwwindows:cyan_mosaic_glass","mcwbridges:diorite_bridge","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","croptopia:chicken_and_noodles","silentgear:stone_rod","handcrafted:sandstone_corner_trim","mcwbridges:oak_log_bridge_middle","mcwpaths:diorite_crystal_floor_path","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","cfm:oak_hedge","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","mcwlights:pink_paper_lamp","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","aether:iron_gloves","minecraft:chiseled_stone_bricks","tombstone:white_marble","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","dyenamics:dye_mint_carpet","caupona:mud_kitchen_stove","mcwpaths:diorite_strewn_rocky_path","pneumaticcraft:stomp_upgrade","minecraft:polished_granite_stairs_from_granite_stonecutting","mcwwindows:crimson_stem_parapet","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","create:small_granite_brick_stairs_from_stone_types_granite_stonecutting","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","minecraft:brush","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:deepslate_tiles_from_deepslate_bricks_stonecutting","sfm:cable","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","alltheores:zinc_gear","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","create:polished_cut_diorite_wall_from_stone_types_diorite_stonecutting","mcwwindows:bricks_window","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","alltheores:osmium_rod","minecraft:oak_fence","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwwindows:blue_mosaic_glass_pane","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","silentgear:fine_silk_cloth","cfm:green_kitchen_counter","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","create:cut_granite_brick_stairs_from_stone_types_granite_stonecutting","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","minecraft:deepslate_tile_wall_from_deepslate_bricks_stonecutting","croptopia:roasted_smoking","mcwbiomesoplenty:hellbark_curved_gate","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","supplementaries:soap","create:cut_diorite_brick_stairs_from_stone_types_diorite_stonecutting","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","mcwfences:acacia_horse_fence","mcwpaths:diorite_windmill_weave_slab","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","domum_ornamentum:pink_floating_carpet","securitycraft:reinforced_andesite","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwpaths:cobbled_deepslate_crystal_floor_slab","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwlights:jungle_tiki_torch","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","allthecompressed:compress/andesite_1x","mcwwindows:diorite_four_window","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","travelersbackpack:backpack_tank","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","croptopia:campfire_molasses","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","mcwwindows:stripped_birch_log_four_window","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","silentgear:upgrade_base","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","minecraft:fletching_table","handcrafted:pufferfish_trophy","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","twigs:tuff_stairs","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","allthecompressed:compress/grass_block_1x","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","minecraft:diorite_slab_from_diorite_stonecutting","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","mcwpaths:andesite_windmill_weave_path","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","mcwpaths:granite_strewn_rocky_path","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","cfm:fridge_light","supplementaries:gold_trapdoor","mcwroofs:andesite_upper_lower_roof","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","rftoolsutility:counter_module","create:granite_pillar_from_stone_types_granite_stonecutting","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","delightful:knives/zinc_knife","mcwroofs:granite_steep_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","minecraft:nether_brick","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","minecraft:polished_diorite_stairs_from_diorite_stonecutting","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","botania:vine_ball","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","cfm:spruce_kitchen_drawer","mcwbiomesoplenty:magic_highley_gate","create:cut_diorite_brick_wall_from_stone_types_diorite_stonecutting","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","mcwbiomesoplenty:flowering_oak_hedge","mcwpaths:andesite_flagstone_slab","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","supplementaries:wrench","minecraft:bow","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","minecraft:andesite_stairs","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","cfm:purple_kitchen_sink","cfm:black_cooler","mcwpaths:diorite_dumble_paving","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwbiomesoplenty:palm_wired_fence","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","minecraft:firework_rocket_simple","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","mcwfurnitures:oak_coffee_table","minecraft:andesite","mcwroofs:andesite_roof","rftoolsbase:infused_enderpearl","supplementaries:pedestal","minecraft:allthemodium_mage_helmet_smithing","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","mcwfences:andesite_grass_topped_wall","mcwpaths:sandstone_flagstone_path","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwdoors:jungle_tropical_door","minecraft:stone","twigs:lamp","dyenamics:bed/lavender_bed","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","minecraft:deepslate_tile_slab_from_deepslate_bricks_stonecutting","securitycraft:speed_module","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","mcwdoors:oak_barn_door","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","dyenamics:dye_bubblegum_carpet","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","dyenamics:dye_aquamarine_carpet","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","create:layered_diorite_from_stone_types_diorite_stonecutting","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:cyan_concrete_attic_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","minecraft:polished_granite_slab_from_granite_stonecutting","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","mcwroofs:white_concrete_attic_roof","securitycraft:reinforced_mossy_stone_bricks_from_vine","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","additionallanterns:obsidian_chain","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","railcraft:receiver_circuit","mcwwindows:bricks_four_window","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","allthecompressed:compress/granite_1x","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","supplementaries:stonecutting/stone_tile","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","mcwwindows:birch_plank_window2","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","mcwlights:yellow_paper_lamp","additionallanterns:diorite_chain","domum_ornamentum:cream_bricks","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwlights:warped_tiki_torch","forbidden_arcanus:arcane_chiseled_darkstone","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","dyenamics:wine_stained_glass","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","mcwpaths:andesite_clover_paving","minecraft:diorite_stairs","cfm:magenta_grill","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","mcwroofs:oak_planks_roof","mcwpaths:stone_flagstone","bigreactors:energizer/casing","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","mcwpaths:granite_dumble_paving","mcwpaths:granite_flagstone_slab","aether:skyroot_note_block","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","create:cut_granite_brick_slab_from_stone_types_granite_stonecutting","mcwroofs:granite_upper_steep_roof","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"],toBeDisplayed:["mcwpaths:andesite_windmill_weave","cfm:stripped_mangrove_kitchen_sink_dark","cfm:yellow_cooler","sfm:disk","mcwfurnitures:stripped_jungle_desk","biomesoplenty:fir_boat","mcwwindows:birch_four_window","mcwbiomesoplenty:jacaranda_window2","dyenamics:banner/lavender_banner","littlelogistics:seater_barge","handcrafted:spider_trophy","mcwpaths:cobbled_deepslate_crystal_floor_path","minecraft:deepslate_brick_stairs_from_cobbled_deepslate_stonecutting","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","mcwpaths:granite_windmill_weave_slab","cfm:jungle_coffee_table","xnet:connector_green","create:oak_window","securitycraft:reinforced_white_stained_glass","minecolonies:chainmailchestplate","mythicbotany:central_rune_holder","create:cut_diorite_from_stone_types_diorite_stonecutting","utilitarian:utility/oak_logs_to_stairs","allthecompressed:compress/netherrack_1x","mcwbiomesoplenty:hellbark_picket_fence","minecraft:cobblestone_wall","immersiveengineering:crafting/plate_iron_hammering","rftoolsutility:machineinformation_module","mcwpaths:cobbled_deepslate_running_bond_slab","mcwwindows:oak_plank_pane_window","reliquary:mob_charm_fragments/cave_spider","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","simplylight:illuminant_light_blue_block_dyed","railcraft:controller_circuit","mcwpaths:granite_running_bond_slab","twigs:polished_rhyolite_stonecutting","supplementaries:candle_holders/candle_holder_green","aether:skyroot_fletching_table","securitycraft:reinforced_cherry_fence_gate","rftoolsutility:screen_link","securitycraft:reinforced_acacia_fence","dyenamics:banner/icy_blue_banner","energymeter:meter","mcwlights:covered_lantern","mcwroofs:purple_concrete_upper_lower_roof","minecraft:copper_block","sliceanddice:slicer","supplementaries:bubble_blower","supplementaries:crank","aquaculture:iron_nugget_from_smelting","minecraft:green_stained_glass","alltheores:iron_plate","mcwwindows:yellow_mosaic_glass","mcwbridges:glass_bridge_stair","minecraft:sandstone_slab_from_sandstone_stonecutting","allthearcanistgear:elemental_legs_to_allthemodium_leggings_smithing","mcwbiomesoplenty:mahogany_plank_pane_window","mcwfences:prismarine_railing_gate","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","mcwpaths:cobblestone_dumble_paving","mcwwindows:dark_oak_blinds","dyenamics:aquamarine_concrete_powder","create:small_andesite_bricks_from_stone_types_andesite_stonecutting","nethersdelight:golden_machete","mcwwindows:acacia_curtain_rod","mcwlights:chain_lantern","securitycraft:camera_monitor","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwfences:modern_nether_brick_wall","minecraft:cyan_concrete_powder","mcwpaths:andesite_windmill_weave_slab","forbidden_arcanus:deorum_ingot","minecraft:golden_hoe","cfm:oak_kitchen_counter","mcwpaths:diorite_running_bond_stairs","cfm:purple_trampoline","cfm:jungle_kitchen_sink_light","mcwpaths:dirt_path_block","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","biomesoplenty:mahogany_boat","aquaculture:note_hook","mcwroofs:cyan_concrete_upper_steep_roof","mcwlights:dark_oak_ceiling_fan_light","additionallanterns:stone_bricks_lantern","simplylight:illuminant_cyan_block_on_dyed","mcwroofs:cyan_concrete_lower_roof","minecraft:brown_stained_glass","mcwbiomesoplenty:magic_window","mcwroofs:yellow_concrete_top_roof","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwroofs:magenta_concrete_upper_steep_roof","mcwfurnitures:oak_striped_chair","mcwpaths:diorite_running_bond_slab","minecraft:sandstone","mcwwindows:birch_plank_window","mcwbiomesoplenty:stripped_dead_log_window2","occultism:crafting/magic_lamp_empty","railcraft:brass_ingot_crafted_with_ingots","mcwpaths:sandstone_running_bond_stairs","create:granite_from_stone_types_granite_stonecutting","mcwbiomesoplenty:mahogany_window","simplylight:illuminant_light_blue_block_toggle","minecraft:gray_concrete_powder","mcwfences:oak_pyramid_gate","rftoolsutility:redstone_module","mcwdoors:jungle_bark_glass_door","twigs:mossy_bricks_from_vine","mcwroofs:cobblestone_upper_lower_roof","mcwfences:deepslate_railing_gate","create:industrial_iron_block_from_ingots_iron_stonecutting","supplementaries:daub_cross_brace","mcwbiomesoplenty:umbran_plank_four_window","tombstone:grave_plate","cfm:stripped_crimson_mail_box","cfm:gray_trampoline","mcwbiomesoplenty:empyreal_wired_fence","cfm:purple_cooler","securitycraft:keypad_frame","mcwlights:birch_ceiling_fan_light","securitycraft:whitelist_module","mcwbiomesoplenty:pine_plank_window2","mcwwindows:warped_louvered_shutter","xnet:connector_routing","mcwbiomesoplenty:palm_plank_window2","create:cut_diorite_wall_from_stone_types_diorite_stonecutting","mcwwindows:spruce_plank_parapet","securitycraft:reinforced_black_stained_glass_pane_from_glass","utilitix:directional_highspeed_rail","mcwlights:golden_chain","mcwbiomesoplenty:magic_picket_fence","create:small_diorite_bricks_from_stone_types_diorite_stonecutting","mcwpaths:oak_planks_path","securitycraft:reinforced_purple_stained_glass","mcwwindows:granite_pane_window","everythingcopper:copper_chestplate","create:cut_granite_bricks_from_stone_types_granite_stonecutting","farmersdelight:cooking/rabbit_stew","cfm:stripped_crimson_kitchen_sink_dark","mcwpaths:andesite_flagstone_path","cfm:warped_mail_box","cfm:green_grill","minecraft:cooked_chicken_from_smoking","mcwwindows:dark_prismarine_window2","pneumaticcraft:search_upgrade","cfm:orange_grill","mcwwindows:dark_oak_shutter","allthecompressed:compress/diorite_1x","cfm:jungle_desk","mcwroofs:oak_roof","minecraft:iron_hoe","mcwtrpdoors:oak_barred_trapdoor","mcwroofs:light_blue_concrete_lower_roof","mcwfurnitures:jungle_drawer","mcwfences:nether_brick_pillar_wall","mcwbiomesoplenty:willow_curved_gate","delightful:knives/osmium_knife","additionallanterns:end_stone_lantern","cfm:mangrove_kitchen_sink_dark","mcwdoors:oak_four_panel_door","mcwfences:blackstone_brick_railing_gate","minecraft:granite_stairs_from_granite_stonecutting","mcwbiomesoplenty:maple_curved_gate","minecraft:golden_pickaxe","mcwwindows:stripped_cherry_log_window2","mcwpaths:cobbled_deepslate_flagstone_stairs","cfm:green_kitchen_sink","mcwfences:diorite_railing_gate","allthearcanistgear:elemental_chest_to_allthemodium_robes_smithing","mcwroofs:gutter_base_black","mcwroofs:green_concrete_roof","minecraft:redstone_torch","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","minecraft:target","additionallanterns:andesite_lantern","mcwroofs:green_concrete_upper_lower_roof","mcwpaths:cobblestone_basket_weave_paving","create:crafting/materials/andesite_alloy","mcwtrpdoors:jungle_swamp_trapdoor","mcwroofs:gutter_base_gray","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","mcwbiomesoplenty:pine_stockade_fence","mcwpaths:cobblestone_diamond_paving","cfm:blue_grill","mcwlights:golden_chandelier","mcwfurnitures:oak_double_drawer_counter","mcwpaths:stone_flagstone_stairs","cfm:stripped_birch_kitchen_drawer","mcwwindows:birch_plank_pane_window","enderio:stone_gear","travelersbackpack:gold_tier_upgrade","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwbiomesoplenty:fir_pyramid_gate","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","mcwwindows:magenta_mosaic_glass_pane","mcwtrpdoors:metal_full_trapdoor","mcwpaths:sandstone_windmill_weave_path","supplementaries:bomb","mcwroofs:gutter_middle_pink","dyenamics:navy_stained_glass","additionallanterns:iron_lantern","mcwwindows:gray_mosaic_glass_pane","simplylight:illuminant_light_gray_block_toggle","utilitix:mob_bell","mcwfences:railing_end_brick_wall","mcwpaths:andesite_running_bond_slab","mcwpaths:sandstone_flagstone_slab","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","mcwbiomesoplenty:fir_hedge","mcwfences:birch_highley_gate","mcwwindows:oak_blinds","mcwfences:birch_wired_fence","mcwroofs:red_concrete_lower_roof","mcwdoors:oak_cottage_door","create:diorite_from_stone_types_diorite_stonecutting","immersiveengineering:crafting/torch","mcwbridges:diorite_bridge_stair","mcwfences:warped_pyramid_gate","delightful:food/baklava","mcwdoors:metal_hospital_door","mcwdoors:jungle_stable_door","railcraft:signal_circuit","minecraft:magenta_concrete_powder","dyenamics:bubblegum_stained_glass","dyenamics:dye_conifer_carpet","mcwbiomesoplenty:pine_window2","create:small_diorite_brick_stairs_from_stone_types_diorite_stonecutting","sophisticatedstorage:oak_chest_from_vanilla_chest","mcwwindows:blackstone_four_window","immersiveengineering:crafting/screwdriver","mcwlights:cherry_tiki_torch","create:small_diorite_brick_wall_from_stone_types_diorite_stonecutting","simplylight:illuminant_purple_block_toggle","cfm:pink_cooler","cfm:red_grill","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","supplementaries:flags/flag_light_blue","cfm:birch_kitchen_sink_light","mcwpaths:cobbled_deepslate_running_bond","travelersbackpack:enderman","additionallanterns:emerald_chain","mcwwindows:dark_oak_plank_four_window","croptopia:chicken_and_dumplings","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwpaths:andesite_strewn_rocky_path","mcwpaths:granite_flagstone_path","mcwtrpdoors:jungle_paper_trapdoor","mcwroofs:diorite_upper_lower_roof","modularrouters:blank_module","mcwbiomesoplenty:dead_curved_gate","immersiveengineering:crafting/connector_lv_relay","mcwpaths:sandstone_windmill_weave_stairs","sgjourney:sandstone_with_lapis","mcwfences:red_sandstone_pillar_wall","mcwlights:upgraded_torch","cfm:jungle_park_bench","mcwbiomesoplenty:magic_curved_gate","mcwroofs:purple_concrete_steep_roof","mcwroofs:blue_concrete_roof","cfm:light_blue_kitchen_drawer","mcwpaths:sand_path_block","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwbiomesoplenty:stripped_mahogany_pane_window","cfm:stripped_warped_mail_box","mcwwindows:stripped_dark_oak_log_window2","handcrafted:diorite_pillar_trim","mcwpaths:sandstone_windmill_weave","immersiveengineering:crafting/toolbox","create:crafting/logistics/pulse_repeater","connectedglass:borderless_glass1","mcwroofs:jungle_top_roof","mcwwindows:crimson_shutter","simplylight:illuminant_lime_block_dyed","cfm:stripped_birch_mail_box","additionallanterns:andesite_chain","forbidden_arcanus:clibano_core","mcwroofs:orange_concrete_top_roof","additionallanterns:granite_chain","bigreactors:turbine/basic/passivetap_fe","minecraft:blue_concrete_powder","minecraft:blue_stained_glass","create:small_granite_brick_wall_from_stone_types_granite_stonecutting","mcwwindows:dark_oak_window","minecraft:chiseled_deepslate_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:prismarine_pillar_wall","tombstone:green_marble","mcwpaths:granite_honeycomb_paving","mcwwindows:hammer","handcrafted:fox_trophy","mcwpaths:cobblestone_clover_paving","shrink:shrinking_device","securitycraft:reinforced_andesite_with_vanilla_cobblestone","mcwdoors:jungle_japanese2_door","mcwroofs:blackstone_lower_roof","mcwroofs:gutter_base_magenta","domum_ornamentum:cobblestone_extra","handcrafted:oak_counter","mcwbiomesoplenty:empyreal_window","mcwbiomesoplenty:stripped_hellbark_log_window","mcwlights:reinforced_torch","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","dyenamics:lavender_concrete_powder","mcwroofs:white_concrete_roof","mcwwindows:window_half_bar_base","littlelogistics:tug_route","mcwbridges:andesite_bridge_pier","securitycraft:reinforced_brown_stained_glass_pane_from_glass","mcwroofs:oak_planks_steep_roof","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","croptopia:fried_chicken","mcwbiomesoplenty:dead_picket_fence","minecraft:black_concrete_powder","mcwbiomesoplenty:hellbark_window2","minecraft:sugar_from_sugar_cane","mcwlights:oak_ceiling_fan_light","mcwbiomesoplenty:magic_hedge","xnet:advanced_connector_yellow","cfm:stripped_oak_chair","cfm:orange_trampoline","wirelesschargers:basic_wireless_player_charger","mcwroofs:lime_concrete_steep_roof","mcwwindows:oak_shutter","cfm:rock_path","minecraft:wooden_hoe","createoreexcavation:diamond_drill","mcwbiomesoplenty:magic_plank_pane_window","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwpaths:diorite_honeycomb_paving","railcraft:gold_gear","mcwwindows:mangrove_curtain_rod","mcwroofs:cyan_concrete_top_roof","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","aquaculture:stone_fillet_knife","mcwfences:deepslate_brick_grass_topped_wall","mcwbiomesoplenty:magic_stockade_fence","mcwwindows:andesite_window2","securitycraft:panic_button","mcwroofs:gutter_middle_brown","mcwroofs:gutter_base_green","minecraft:blue_dye","aether:obsidian_from_bucket_freezing","delightful:food/cooking/nut_butter_bottle","securitycraft:reinforced_pink_stained_glass","farmersdelight:organic_compost_from_rotten_flesh","securitycraft:reinforced_spruce_fence","minecraft:piston","mcwwindows:crimson_planks_four_window","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","deepresonance:resonating_plate_block","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","allthemodium:allthemodium_ingot_from_raw_smelting","mcwbiomesoplenty:umbran_plank_pane_window","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","minecraft:wooden_axe","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwbiomesoplenty:willow_four_window","mcwwindows:metal_curtain_rod","mcwlights:cross_wall_lantern","mcwroofs:oak_steep_roof","aether:skyroot_tripwire_hook","ae2:decorative/light_detector","mcwfences:birch_stockade_fence","domum_ornamentum:black_floating_carpet","supplementaries:daub_brace","minecraft:diorite_stairs_from_diorite_stonecutting","mcwfences:spruce_horse_fence","handcrafted:oak_shelf","mcwwindows:jungle_plank_four_window","mcwroofs:andesite_attic_roof","mcwpaths:granite_windmill_weave","mcwpaths:granite_windmill_weave_path","mcwfences:crimson_curved_gate","mcwroofs:sandstone_top_roof","create:andesite_from_stone_types_andesite_stonecutting","minecraft:polished_andesite_stairs_from_andesite_stonecutting","securitycraft:reinforced_green_stained_glass","minecraft:iron_bars","create:crafting/kinetics/copper_valve_handle","mcwwindows:mangrove_louvered_shutter","supplementaries:flags/flag_magenta","minecraft:diamond_axe","mcwpaths:granite_running_bond_path","supplementaries:candle_holders/candle_holder_red","cfm:stripped_birch_kitchen_sink_dark","aether:skyroot_piston","mcwbiomesoplenty:fir_horse_fence","additionallanterns:bricks_lantern","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","dyenamics:honey_concrete_powder","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","dyenamics:banner/bubblegum_banner","securitycraft:reinforced_fence_gate","mcwfences:majestic_metal_fence_gate","mcwwindows:birch_plank_four_window","packingtape:tape","create:cut_diorite_stairs_from_stone_types_diorite_stonecutting","botania:petal_gray","mcwfences:granite_grass_topped_wall","mcwwindows:birch_shutter","create:polished_cut_granite_wall_from_stone_types_granite_stonecutting","securitycraft:reinforced_crimson_fence_gate","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","create:cut_granite_stairs_from_stone_types_granite_stonecutting","mcwbiomesoplenty:stripped_umbran_log_window","mcwfurnitures:jungle_stool_chair","create:polished_cut_diorite_slab_from_stone_types_diorite_stonecutting","travelersbackpack:diamond","cfm:stripped_warped_kitchen_drawer","create:layered_andesite_from_stone_types_andesite_stonecutting","mcwroofs:brown_concrete_roof","mcwroofs:cobblestone_roof","securitycraft:reinforced_mossy_cobblestone_from_vine","sfm:water_tank","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","nethersdelight:iron_machete","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","mcwpaths:andesite_square_paving","mcwfences:dark_oak_hedge","mcwfences:bamboo_highley_gate","mcwfences:end_brick_grass_topped_wall","bigreactors:reactor/basic/passivetap_fe","allthemodium:teleport_pad","securitycraft:reinforced_dispenser","mcwroofs:jungle_roof","dyenamics:dye_navy_carpet","mcwfurnitures:stripped_jungle_stool_chair","mcwpaths:andesite_running_bond","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","minecraft:torch","mcwlights:striped_wall_lantern","securitycraft:reinforced_gray_stained_glass_pane_from_dye","mcwwindows:window_base","cfm:light_gray_grill","mcwwindows:mangrove_window","cfm:oak_desk","forbidden_arcanus:runic_glass","securitycraft:reinforced_bamboo_fence_gate","mcwbiomesoplenty:hellbark_hedge","utilitix:glue_ball","mcwfurnitures:jungle_double_drawer_counter","allthemodium:allthemodium_block","supplementaries:daub_frame","railcraft:signal_tuner","cfm:pink_kitchen_counter","cfm:white_cooler","mcwbiomesoplenty:jacaranda_highley_gate","everythingcopper:copper_hopper","immersiveengineering:crafting/jerrycan","cfm:black_kitchen_counter","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","create:crafting/kinetics/copper_valve_handle_from_others","dyenamics:banner/mint_banner","mcwpaths:diorite_running_bond_path","minecraft:crafting_table","biomesoplenty:tnt_from_bop_sand","dyenamics:lavender_stained_glass","twilightforest:time_boat","minecraft:sandstone_wall","mcwbiomesoplenty:stripped_mahogany_log_window","cfm:pink_trampoline","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","securitycraft:briefcase","mcwwindows:green_mosaic_glass","mcwbiomesoplenty:empyreal_four_window","mcwlights:copper_triple_candle_holder","cfm:white_grill","supplementaries:jar","mcwlights:iron_low_candle_holder","aquaculture:acacia_fish_mount","utilitix:comparator_redirector_up","mcwwindows:red_mosaic_glass_pane","mcwbiomesoplenty:mahogany_stockade_fence","mcwpaths:andesite_crystal_floor_stairs","mcwroofs:sandstone_lower_roof","mcwwindows:oak_plank_four_window","cfm:red_cooler","securitycraft:reinforced_green_stained_glass_pane_from_dye","mcwbiomesoplenty:magic_wired_fence","create:crafting/logistics/pulse_extender","bigreactors:reactor/reinforced/passivetap_fe","xnet:facade","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwpaths:sandstone_basket_weave_paving","aether:golden_pendant","minecraft:allthemodium_spell_book_smithing","mcwwindows:stripped_acacia_log_four_window","undergarden:grongle_boat","domum_ornamentum:green_floating_carpet","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","minecraft:carrot_on_a_stick","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","aether:skyroot_loom","utilitix:advanced_brewery","nethersdelight:diamond_machete","mcwlights:copper_double_candle_holder","cfm:stripped_acacia_kitchen_sink_light","mcwpaths:diorite_basket_weave_paving","mcwroofs:blue_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_willow_log_window","supplementaries:cage","mcwroofs:light_blue_concrete_attic_roof","create:cut_tuff_brick_slab_from_stone_types_tuff_stonecutting","mcwfences:diorite_grass_topped_wall","mcwroofs:white_concrete_top_roof","mcwwindows:stone_window2","minecraft:oak_button","minecraft:granite_wall","simplylight:illuminant_purple_block_on_dyed","handcrafted:granite_corner_trim","mcwbiomesoplenty:maple_window","mcwbiomesoplenty:stripped_pine_pane_window","mcwtrpdoors:metal_warning_trapdoor","minecraft:polished_deepslate_wall_from_cobbled_deepslate_stonecutting","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","xnet:advanced_connector_yellow_dye","additionallanterns:diamond_lantern","mcwroofs:yellow_concrete_upper_steep_roof","minecraft:diamond_helmet","securitycraft:reinforced_purple_stained_glass_pane_from_dye","aquaculture:bobber","mcwwindows:quartz_window2","mcwpaths:cobblestone_honeycomb_paving","securitycraft:reinforced_lime_stained_glass_pane_from_glass","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","minecraft:compass","mcwbiomesoplenty:jacaranda_plank_pane_window","allthemodium:allthemodium_apple","expatternprovider:silicon_block","itemcollectors:basic_collector","minecraft:loom","mcwtrpdoors:oak_bark_trapdoor","minecraft:arrow","mcwroofs:gutter_middle_light_blue","mcwroofs:light_gray_concrete_steep_roof","supplementaries:flags/flag_pink","securitycraft:reinforced_magenta_stained_glass_pane_from_glass","pneumaticcraft:item_life_upgrade","allthearcanistgear:elemental_boots_to_allthemodium_boots_smithing","mcwroofs:sandstone_attic_roof","sophisticatedstorage:storage_crafting_upgrade_from_backpack_crafting_upgrade","handcrafted:golden_wide_pot","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwbiomesoplenty:origin_hedge","securitycraft:universal_block_reinforcer_lvl2","mcwbiomesoplenty:redwood_curved_gate","securitycraft:universal_block_reinforcer_lvl3","mcwlights:soul_spruce_tiki_torch","mcwpaths:granite_running_bond_stairs","securitycraft:universal_block_reinforcer_lvl1","mcwbridges:pliers","mcwpaths:granite_flagstone","create:cut_granite_brick_wall_from_stone_types_granite_stonecutting","mcwbiomesoplenty:empyreal_stockade_fence","mcwroofs:gray_concrete_attic_roof","alltheores:nickel_plate","supplementaries:flags/flag_green","securitycraft:reinforced_white_stained_glass_pane_from_glass","undergarden:smogstem_boat","mcwpaths:diorite_crystal_floor_slab","cfm:jungle_crate","mcwwindows:acacia_plank_four_window","mcwlights:copper_chandelier","dyenamics:banner/rose_banner","everythingcopper:copper_pressure_plate","quark:building/crafting/furnaces/cobblestone_furnace","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","mcwtrpdoors:oak_cottage_trapdoor","mcwwindows:blackstone_parapet","securitycraft:reinforced_birch_fence_gate","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","mcwbiomesoplenty:stripped_maple_log_window","pneumaticcraft:dispenser_upgrade","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","mcwroofs:orange_concrete_attic_roof","cfm:brown_cooler","mcwfences:diorite_pillar_wall","forbidden_arcanus:blacksmith_gavel_head","rftoolsutility:inventoryplus_module","mcwwindows:dark_prismarine_parapet","mcwbiomesoplenty:mahogany_plank_four_window","mcwfurnitures:jungle_end_table","mcwpaths:granite_crystal_floor_stairs","utilitix:experience_crystal","undergarden:shard_torch","minecraft:light_blue_stained_glass","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","minecraft:respawn_anchor","mcwpaths:cobbled_deepslate_flagstone","create:polished_cut_tuff_from_stone_types_tuff_stonecutting","paraglider:paraglider","minecraft:deepslate_brick_slab","dankstorage:dock","create:cut_tuff_wall_from_stone_types_tuff_stonecutting","utilitix:anvil_cart","mcwbridges:jungle_bridge_pier","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","alchemistry:dissolver","reliquary:mob_charm_fragments/spider","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","minecraft:polished_deepslate_stairs_from_cobbled_deepslate_stonecutting","cfm:gray_kitchen_counter","mcwlights:iron_double_candle_holder","undergarden:catalyst","domum_ornamentum:purple_cobblestone_extra","minecraft:iron_axe","create:crafting/kinetics/fluid_valve","minecraft:sandstone_stairs","minecraft:jukebox","mcwwindows:jungle_curtain_rod","create:layered_granite_from_stone_types_granite_stonecutting","mcwwindows:oak_window","mcwbiomesoplenty:stripped_magic_pane_window","domum_ornamentum:cyan_floating_carpet","mcwwindows:spruce_plank_pane_window","minecraft:coarse_dirt","mcwfences:dark_oak_highley_gate","mcwwindows:magenta_mosaic_glass","mcwtrpdoors:oak_mystic_trapdoor","minecraft:fermented_spider_eye","mcwwindows:light_gray_mosaic_glass","mcwfurnitures:stripped_oak_stool_chair","securitycraft:smart_module","mcwbiomesoplenty:umbran_four_window","supplementaries:flags/flag_black","mcwdoors:print_whispering","mcwwindows:acacia_louvered_shutter","minecraft:gold_ingot_from_gold_block","minecraft:cobbled_deepslate_slab","mcwroofs:lime_concrete_lower_roof","tombstone:blue_marble","mcwroofs:oak_upper_steep_roof","cfm:light_gray_cooler","minecraft:iron_door","mcwdoors:metal_warning_door","mcwbiomesoplenty:empyreal_picket_fence","mcwlights:spruce_ceiling_fan_light","utilitarian:utility/oak_logs_to_doors","dyenamics:dye_spring_green_carpet","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwroofs:gutter_base_orange","aether:bow_repairing","securitycraft:reinforced_light_gray_stained_glass","minecraft:cobblestone_stairs_from_cobblestone_stonecutting","cfm:stripped_spruce_kitchen_sink_light","allthemodium:allthemodium_dust_from_ore_crushing","mcwwindows:blackstone_pane_window","mcwdoors:jungle_whispering_door","create:cut_andesite_bricks_from_stone_types_andesite_stonecutting","mcwpaths:granite_clover_paving","reliquary:uncrafting/spider_eye","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","handcrafted:hammer","mcwroofs:grass_steep_roof","alchemistry:combiner","mcwdoors:jungle_stable_head_door","railcraft:iron_gear","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:candle_holders/candle_holder_yellow","handcrafted:oak_pillar_trim","mcwroofs:yellow_concrete_steep_roof","dyenamics:banner/maroon_banner","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwroofs:gutter_base_yellow","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","aether:iron_pendant","securitycraft:redstone_module","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","supplementaries:flags/flag_purple","mcwbiomesoplenty:stripped_redwood_log_window","create:framed_glass_from_glass_colorless_stonecutting","mcwpaths:diorite_windmill_weave_stairs","mcwpaths:diorite_diamond_paving","cfm:jungle_blinds","tombstone:bone_needle","minecraft:stone_brick_wall","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","cfm:warped_kitchen_sink_light","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","mcwbiomesoplenty:stripped_redwood_pane_window","cfm:stripped_mangrove_mail_box","mcwwindows:granite_parapet","create:polished_cut_tuff_wall_from_stone_types_tuff_stonecutting","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","minecraft:spruce_boat","minecraft:gold_block","mcwbiomesoplenty:maple_plank_pane_window","aquaculture:gold_fillet_knife","dyenamics:banner/navy_banner","everythingcopper:copper_boots","travelersbackpack:gold","create:copper_scaffolding_from_ingots_copper_stonecutting","minecraft:paper","mcwfences:warped_curved_gate","botania:light_gray_shiny_flower","mcwdoors:oak_stable_door","twigs:smooth_stone_bricks","mcwpaths:diorite_windmill_weave_path","cfm:oak_upgraded_gate","croptopia:cashew_chicken","dyenamics:fluorescent_concrete_powder","cfm:stripped_spruce_kitchen_sink_dark","mcwfences:modern_blackstone_wall","securitycraft:reinforced_cherry_fence","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","additionallanterns:copper_lantern","mcwlights:golden_small_chandelier","mcwwindows:lime_mosaic_glass","sophisticatedstorage:oak_barrel","cfm:crimson_mail_box","mcwpaths:diorite_flagstone","mcwbiomesoplenty:fir_pane_window","supplementaries:sconce_lever","mcwbiomesoplenty:hellbark_plank_window","mcwfurnitures:stripped_jungle_bookshelf_drawer","domum_ornamentum:beige_stone_bricks","mcwfurnitures:jungle_bookshelf_drawer","additionallanterns:blackstone_lantern","forbidden_arcanus:dark_runic_glass","minecraft:iron_nugget","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwroofs:gutter_middle_light_gray","mcwbiomesoplenty:stripped_empyreal_pane_window","utilitix:reinforced_rail","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","alltheores:lead_rod","mcwwindows:oak_plank_window","mcwroofs:granite_roof","mcwwindows:oak_log_parapet","supplementaries:notice_board","aquaculture:dark_oak_fish_mount","littlelogistics:spring","securitycraft:reinforced_bamboo_fence","minecolonies:large_empty_bottle","sophisticatedstorage:upgrade_base","simplylight:illuminant_lime_block_toggle","minecraft:iron_block","mcwlights:garden_light","mcwroofs:gutter_base","mcwroofs:gutter_middle_purple","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:acacia_hedge","minecraft:golden_apple","minecraft:diamond_pickaxe","domum_ornamentum:blockbarreldeco_onside","simplylight:rodlamp","mcwroofs:deepslate_attic_roof","mcwroofs:purple_concrete_upper_steep_roof","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","create:tuff_from_stone_types_tuff_stonecutting","rftoolsbuilder:green_shield_template_block","mcwpaths:sandstone_crystal_floor_stairs","rftoolsutility:energy_module","mcwfences:flowering_azalea_hedge","securitycraft:reinforced_glass_pane","farmersdelight:golden_knife","minecraft:orange_stained_glass","aquaculture:wooden_fillet_knife","reliquary:mob_charm_fragments/ghast","create:crafting/materials/rose_quartz","mcwbiomesoplenty:maple_plank_window","mcwfences:warped_horse_fence","securitycraft:reinforced_gray_stained_glass_pane_from_glass","cfm:stripped_jungle_coffee_table","mcwpaths:sandstone_strewn_rocky_path","alltheores:gold_plate","mcwbiomesoplenty:maple_four_window","cfm:blue_kitchen_counter","utilitarian:redstone_clock","handcrafted:granite_pillar_trim","mcwwindows:stripped_oak_log_window","securitycraft:harming_module","silentgear:bort_block","minecraft:golden_boots","immersiveengineering:crafting/grit_sand","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","mcwfences:warped_highley_gate","supplementaries:flags/flag_cyan","cfm:pink_grill","mcwroofs:gutter_middle_orange","mcwwindows:warped_stem_parapet","undergarden:torch_ditchbulb_paste","mcwbiomesoplenty:palm_plank_four_window","mcwroofs:sandstone_roof","mcwpaths:diorite_running_bond","mcwwindows:granite_window2","securitycraft:reinforced_cyan_stained_glass_pane_from_dye","mcwpaths:sandstone_crystal_floor","mcwroofs:jungle_attic_roof","securitycraft:reinforced_light_gray_stained_glass_pane_from_glass","sophisticatedstorage:storage_advanced_feeding_upgrade_from_backpack_advanced_feeding_upgrade","utilitix:diamond_shears","minecraft:wooden_pickaxe","supplementaries:clock_block","mcwbridges:balustrade_cobblestone_bridge","mcwwindows:warped_stem_window2","mcwbiomesoplenty:fir_stockade_fence","mcwbridges:asian_red_bridge_pier","alltheores:copper_plate","additionallanterns:cobblestone_lantern","minecraft:oak_stairs","alchemistry:liquifier","mcwfurnitures:jungle_covered_desk","mcwwindows:acacia_pane_window","mcwbiomesoplenty:palm_pane_window","rftoolsbuilder:shape_card_def","rftoolsutility:counterplus_module","mcwbiomesoplenty:dead_plank_window","securitycraft:reinforced_piston","securitycraft:reinforced_yellow_stained_glass","cfm:purple_kitchen_counter","mcwwindows:jungle_log_parapet","mcwwindows:prismarine_parapet","immersiveengineering:crafting/rs_engineering","aquaculture:tin_can_to_iron_nugget","mcwwindows:purple_mosaic_glass","minecraft:glass","xnet:connector_yellow","allthecompressed:compress/gold_block_1x","mcwbiomesoplenty:hellbark_plank_pane_window","create:small_andesite_brick_wall_from_stone_types_andesite_stonecutting","sophisticatedstorage:storage_advanced_magnet_upgrade_from_backpack_advanced_magnet_upgrade","mcwbiomesoplenty:umbran_wired_fence","mcwroofs:cobblestone_upper_steep_roof","xnet:netcable_yellow_dye","mcwroofs:brown_concrete_attic_roof","mcwpaths:granite_windmill_weave_stairs","mcwlights:festive_wall_lantern","mcwwindows:mangrove_log_parapet","domum_ornamentum:gray_floating_carpet","cfm:stripped_jungle_park_bench","dyenamics:persimmon_concrete_powder","securitycraft:reinforced_dark_oak_fence_gate","minecraft:light_blue_concrete_powder","createoreexcavation:drill","mcwroofs:gutter_middle_magenta","minecraft:granite_stairs","mcwwindows:light_blue_mosaic_glass","silentgear:fine_silk","mcwlights:rustic_torch","minecraft:polished_deepslate_from_cobbled_deepslate_stonecutting","mcwwindows:acacia_plank_pane_window","dyenamics:banner/honey_banner","mcwbiomesoplenty:stripped_maple_pane_window","rftoolsstorage:storage_control_module","enderio:pressurized_fluid_tank","mcwpaths:granite_square_paving","cfm:oak_upgraded_fence","deepresonance:radiation_monitor","minecraft:allthemodium_mage_chestplate_smithing","cfm:magenta_kitchen_sink","xnet:advanced_connector_red","mcwroofs:green_concrete_top_roof","securitycraft:reinforced_red_stained_glass","mcwbiomesoplenty:jacaranda_wired_fence","cfm:spruce_kitchen_sink_light","xnet:netcable_green_dye","mcwwindows:cherry_plank_window","mcwbiomesoplenty:maple_plank_window2","mcwpaths:andesite_flagstone","minecraft:allthemodium_mage_boots_smithing","mcwbiomesoplenty:dead_plank_window2","minecraft:golden_axe","twilightforest:dark_boat","cfm:yellow_kitchen_drawer","biomesoplenty:umbran_boat","immersiveengineering:crafting/wire_lead","minecraft:cyan_stained_glass","mcwroofs:red_concrete_roof","forbidden_arcanus:utrem_jar","additionallanterns:crimson_lantern","rftoolscontrol:card_base","ae2:network/cables/smart_fluix","mcwroofs:diorite_steep_roof","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","everythingcopper:copper_hoe","cfm:stripped_mangrove_kitchen_sink_light","mcwwindows:acacia_log_parapet","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","dyenamics:amber_concrete_powder","dyenamics:fluorescent_stained_glass","mcwroofs:light_gray_concrete_lower_roof","twigs:rhyolite_slab","immersiveengineering:crafting/stick_iron","create:polished_cut_granite_from_stone_types_granite_stonecutting","twigs:rhyolite_stairs","farmersdelight:cooking/vegetable_noodles","pneumaticcraft:entity_tracker_upgrade","handcrafted:oak_side_table","mcwpaths:stone_crystal_floor_path","mcwlights:acacia_ceiling_fan_light","mcwroofs:lime_concrete_upper_steep_roof","cfm:lime_grill","create:crafting/appliances/copper_diving_boots","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","securitycraft:reinforced_cyan_stained_glass","handcrafted:golden_medium_pot","minecraft:smooth_stone_slab","immersiveengineering:crafting/component_iron","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","allthecompressed:compress/tuff_1x","sfm:fancy_to_cable","minecraft:mossy_cobblestone_from_vine","aquaculture:heavy_hook","minecraft:white_concrete_powder","mcwwindows:acacia_window","minecraft:cherry_boat","dyenamics:icy_blue_stained_glass","create:small_andesite_brick_stairs_from_stone_types_andesite_stonecutting","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","aquaculture:cooked_fish_fillet_from_campfire","cfm:black_picket_fence","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","mcwroofs:orange_concrete_lower_roof","create:crafting/kinetics/steam_engine","cfm:magenta_kitchen_counter","cfm:spruce_mail_box","silentgear:bort_from_block","minecraft:barrel","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","mcwwindows:dark_oak_pane_window","immersiveengineering:crafting/connector_hv","create:tuff_pillar_from_stone_types_tuff_stonecutting","handcrafted:oak_table","pneumaticcraft:elytra_upgrade","mcwwindows:cherry_window","aquaculture:golden_fishing_rod","simplemagnets:basicmagnet","mcwfences:oak_highley_gate","xnet:connector_green_dye","aquaculture:fishing_line","cfm:stripped_jungle_kitchen_sink_dark","forbidden_arcanus:obsidian_skull","alltheores:zinc_rod","mcwpaths:cobbled_deepslate_honeycomb_paving","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","aether:golden_gloves","mcwfurnitures:oak_glass_kitchen_cabinet","dyenamics:wine_concrete_powder","mcwroofs:blue_concrete_attic_roof","minecraft:smithing_table","mcwwindows:mangrove_plank_parapet","mcwpaths:gravel_path_block","mcwfurnitures:jungle_cupboard_counter","supplementaries:lapis_bricks","croptopia:chocolate","minecraft:pink_concrete_powder","minecraft:cauldron","mcwbridges:glass_bridge_pier","delightful:smelting/roasted_acorn","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","cfm:yellow_grill","mcwfences:jungle_picket_fence","dyenamics:ultramarine_concrete_powder","securitycraft:portable_radar","nethersdelight:blackstone_blast_furnace","domum_ornamentum:red_floating_carpet","minecraft:diamond_shovel","aether:iron_boots_repairing","mcwfurnitures:oak_kitchen_cabinet","minecraft:andesite_wall","cfm:stripped_birch_kitchen_sink_light","mcwlights:dark_oak_tiki_torch","xnet:connector_upgrade","mcwroofs:grass_roof","utilitarian:utility/oak_logs_to_trapdoors","aether:iron_ring","rftoolsutility:module_template","tombstone:impregnated_diamond","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","minecraft:cobblestone_slab","handcrafted:golden_thin_pot","dyenamics:peach_concrete_powder","cfm:cyan_kitchen_sink","supplementaries:faucet","utilitix:directional_rail","mcwbiomesoplenty:stripped_pine_log_four_window","railcraft:copper_gear","mcwbiomesoplenty:umbran_window2","corail_woodcutter:cherry_woodcutter","minecraft:minecart","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","mcwfences:dark_oak_curved_gate","mcwpaths:granite_basket_weave_paving","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","minecraft:chest","alltheores:diamond_plate","cfm:magenta_trampoline","supplementaries:pulley","minecraft:gray_stained_glass","immersiveengineering:crafting/connector_lv","mcwwindows:sandstone_four_window","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","bigreactors:reprocessor/casing","mcwroofs:oak_upper_lower_roof","mcwfences:birch_horse_fence","allthecompressed:compress/glass_1x","ad_astra:compressor","mcwlights:crimson_ceiling_fan_light","minecraft:golden_shovel","create:crafting/kinetics/smart_fluid_pipe","minecraft:deepslate_bricks_from_cobbled_deepslate_stonecutting","dyenamics:dye_persimmon_carpet","travelersbackpack:standard","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","mcwfences:mud_brick_pillar_wall","mcwroofs:grass_lower_roof","sfm:fancy_cable","mcwroofs:gutter_base_red","create:copper_ladder_from_ingots_copper_stonecutting","simplylight:illuminant_brown_block_toggle","forbidden_arcanus:edelwood_ladder","littlelogistics:barge","minecraft:iron_boots","mcwroofs:cobblestone_steep_roof","create:crafting/kinetics/fluid_pipe_vertical","mcwfences:sandstone_pillar_wall","mcwwindows:light_blue_mosaic_glass_pane","minecraft:diamond_chestplate","minecraft:golden_chestplate","cfm:stripped_oak_park_bench","securitycraft:reinforced_gray_stained_glass","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","securitycraft:universal_key_changer","mcwbiomesoplenty:redwood_plank_window2","sophisticatedstorage:oak_limited_barrel_1","pneumaticcraft:range_upgrade","sophisticatedstorage:oak_limited_barrel_2","cfm:magenta_cooler","sophisticatedstorage:oak_limited_barrel_3","create:cut_diorite_bricks_from_stone_types_diorite_stonecutting","sophisticatedstorage:oak_limited_barrel_4","securitycraft:reinforced_black_stained_glass","domum_ornamentum:beige_bricks","supplementaries:hourglass","everythingcopper:copper_leggings","mcwroofs:gutter_base_pink","mcwbridges:andesite_bridge_stair","securitycraft:reinforced_blue_stained_glass_pane_from_glass","mcwlights:iron_framed_torch","pneumaticcraft:ender_visor_upgrade","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","supplementaries:checker","mcwfences:railing_mud_brick_wall","mcwwindows:crimson_planks_window","cfm:cyan_cooler","railcraft:silver_gear","immersiveengineering:crafting/wirecoil_copper","immersiveengineering:crafting/empty_casing","mcwpaths:diorite_crystal_floor","mcwwindows:prismarine_four_window","mcwroofs:light_blue_concrete_top_roof","mcwwindows:stripped_jungle_log_four_window","mcwroofs:gutter_base_purple","mcwlights:iron_triple_candle_holder","mcwfences:spruce_picket_fence","travelersbackpack:redstone","minecraft:slime_block","mcwroofs:granite_upper_lower_roof","create:copycat_panel_from_ingots_zinc_stonecutting","mcwwindows:iron_shutter","travelersbackpack:emerald","croptopia:trail_mix","farmersdelight:cutting_board","cfm:birch_kitchen_drawer","trashcans:item_trash_can","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","minecraft:note_block","mcwpaths:sandstone_flagstone","mcwpaths:stone_windmill_weave_stairs","mcwwindows:brown_mosaic_glass_pane","allthemodium:allthemodium_gear","minecraft:smooth_sandstone","mcwroofs:cobblestone_attic_roof","utilitarian:angel_block_rot","mcwbiomesoplenty:stripped_jacaranda_log_four_window","mcwwindows:stripped_mangrove_log_four_window","everythingcopper:copper_shears","allthecompressed:compress/oak_log_1x","alltheores:copper_rod","reliquary:uncrafting/gunpowder_creeper_gland","simplylight:illuminant_slab","twigs:cobblestone_bricks","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","allthecompressed:compress/obsidian_1x","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","mcwpaths:cobblestone_square_paving","minecraft:diorite_wall","mcwfurnitures:stripped_oak_double_drawer","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","mcwroofs:deepslate_steep_roof","cfm:orange_kitchen_sink","mcwpaths:cobbled_deepslate_basket_weave_paving","minecraft:cracked_stone_bricks","minecolonies:chainmailhelmet","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwbridges:granite_bridge_pier","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","minecraft:lantern","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","dimstorage:dim_wall","mcwbiomesoplenty:redwood_window","minecraft:magenta_stained_glass","railcraft:nickel_gear","minecraft:iron_trapdoor","minecraft:deepslate_brick_wall","mcwpaths:cobbled_deepslate_running_bond_path","minecolonies:supplycampdeployer","aquaculture:jellyfish_to_slimeball","delightful:knives/bone_knife","mcwwindows:dark_oak_log_parapet","mcwfences:panelled_metal_fence","mcwpaths:sandstone_square_paving","mcwfurnitures:oak_stool_chair","immersiveengineering:crafting/redstone_acid","mcwroofs:green_concrete_attic_roof","additionallanterns:dark_prismarine_lantern","mcwwindows:mangrove_plank_window2","mcwwindows:mangrove_pane_window","railcraft:zinc_gear","minecraft:deepslate_brick_stairs_from_deepslate_bricks_stonecutting","domum_ornamentum:lime_floating_carpet","farmersdelight:flint_knife","biomesoplenty:maple_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","create:cut_granite_from_stone_types_granite_stonecutting","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","create:polished_cut_tuff_slab_from_stone_types_tuff_stonecutting","securitycraft:reinforced_warped_fence_gate","mcwbiomesoplenty:stripped_jacaranda_pane_window","create:crafting/kinetics/steam_whistle","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwfences:oak_horse_fence","caupona:clay_cistern","mcwfurnitures:oak_large_drawer","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwroofs:jungle_steep_roof","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","croptopia:cooking_pot","mcwlights:gray_paper_lamp","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","aquaculture:tackle_box","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","minecraft:deepslate_brick_wall_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_umbran_pane_window","terralith:dispenser_alt","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","mythicbotany:rune_holder","create:polished_cut_andesite_slab_from_stone_types_andesite_stonecutting","immersiveengineering:crafting/plate_silver_hammering","mcwwindows:spruce_plank_four_window","allthecompressed:compress/cobbled_deepslate_1x","silentgear:stone_torch","cfm:black_kitchen_sink","minecraft:acacia_boat","mcwwindows:red_mosaic_glass","mcwfences:acacia_pyramid_gate","mcwroofs:grass_top_roof","mcwroofs:diorite_attic_roof","mcwbiomesoplenty:dead_four_window","mcwpaths:cobbled_deepslate_dumble_paving","mcwwindows:green_mosaic_glass_pane","mcwroofs:pink_concrete_top_roof","forbidden_arcanus:sanity_meter","utilitarian:utility/jungle_logs_to_pressure_plates","dyenamics:banner/fluorescent_banner","mcwlights:soul_acacia_tiki_torch","securitycraft:bouncing_betty","mcwlights:purple_paper_lamp","mcwroofs:blue_concrete_upper_lower_roof","minecraft:stone_brick_slab","mcwfences:modern_red_sandstone_wall","mcwpaths:diorite_crystal_floor_stairs","reliquary:uncrafting/string","mcwroofs:andesite_upper_steep_roof","mcwlights:golden_wall_candle_holder","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","mcwroofs:brown_concrete_steep_roof","ae2:network/blocks/crank","mcwtrpdoors:jungle_whispering_trapdoor","mcwwindows:bamboo_shutter","mcwwindows:stripped_jungle_log_window","allthemodium:raw_allthemodium_block","mcwroofs:black_concrete_lower_roof","mcwroofs:white_concrete_lower_roof","mcwroofs:brown_concrete_upper_lower_roof","aether:ice_from_bucket_freezing","pneumaticcraft:speed_upgrade","terralith:dropper_alt","dyenamics:ultramarine_stained_glass","mcwlights:white_paper_lamp","minecraft:oak_trapdoor","littlelogistics:locomotive_route","mcwroofs:granite_lower_roof","securitycraft:universal_block_remover","mcwwindows:mangrove_plank_pane_window","alltheores:osmium_gear","simplylight:illuminant_magenta_block_dyed","supplementaries:candle_holders/candle_holder_brown","chimes:iron_chimes","mcwfences:quartz_grass_topped_wall","mcwroofs:brown_concrete_top_roof","alltheores:tin_plate","securitycraft:storage_module","mcwbiomesoplenty:dead_hedge","mcwfences:prismarine_grass_topped_wall","supplementaries:candle_holders/candle_holder_light_blue","supplementaries:goblet","mcwroofs:orange_concrete_upper_steep_roof","mcwpaths:andesite_windmill_weave_stairs","mcwfences:bamboo_horse_fence","minecraft:polished_diorite_from_diorite_stonecutting","cfm:cyan_trampoline","rftoolsutility:energyplus_module","dyenamics:spring_green_stained_glass","cfm:oak_kitchen_sink_dark","railcraft:bronze_ingot_crafted_with_ingots","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:stripped_umbran_log_window2","simplylight:illuminant_green_block_on_toggle","cfm:stripped_warped_kitchen_sink_dark","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","travelersbackpack:wolf","mcwroofs:light_gray_concrete_upper_steep_roof","aquaculture:redstone_hook","forbidden_arcanus:stone_blacksmith_gavel","mcwwindows:stripped_mangrove_log_window2","minecraft:brick","rftoolsbuilder:yellow_shield_template_block","mcwwindows:stripped_cherry_log_four_window","xnet:netcable_blue_dye","domum_ornamentum:brown_floating_carpet","mcwpaths:diorite_flagstone_stairs","cfm:stripped_oak_table","mcwroofs:lime_concrete_top_roof","mcwlights:golden_triple_candle_holder","botania:water_ring","twigs:bone_meal_from_seashells","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","croptopia:chicken_and_rice","mcwdoors:oak_swamp_door","aether:iron_axe_repairing","reliquary:uncrafting/bone","supplementaries:sack","mcwwindows:cherry_window2","mcwroofs:deepslate_upper_lower_roof","minecraft:smoker","cfm:stripped_oak_mail_box","mcwwindows:stripped_oak_pane_window","mcwbridges:diorite_bridge_pier","additionallanterns:netherite_lantern","create:copycat_step_from_ingots_zinc_stonecutting","mcwroofs:light_blue_concrete_upper_lower_roof","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","minecraft:brown_dye","securitycraft:cage_trap","mcwroofs:green_concrete_upper_steep_roof","alltheores:silver_plate","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:mangrove_shutter","mcwpaths:andesite_crystal_floor","cfm:lime_cooler","mcwroofs:gray_concrete_upper_steep_roof","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","mcwroofs:oak_planks_lower_roof","securitycraft:reinforced_light_blue_stained_glass_pane_from_glass","create:polished_cut_diorite_stairs_from_stone_types_diorite_stonecutting","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","dyenamics:maroon_concrete_powder","mcwbiomesoplenty:hellbark_plank_four_window","mcwroofs:pink_concrete_lower_roof","additionallanterns:granite_lantern","mcwlights:iron_chandelier","mcwwindows:spruce_plank_window2","rftoolsbase:smartwrench","mcwwindows:spruce_window","mcwwindows:lime_mosaic_glass_pane","mcwwindows:prismarine_window2","mcwbiomesoplenty:fir_plank_four_window","cfm:white_kitchen_drawer","utilitarian:tps_meter","minecraft:cobbled_deepslate_wall_from_cobbled_deepslate_stonecutting","dyenamics:banner/persimmon_banner","mcwbiomesoplenty:pine_horse_fence","mcwwindows:acacia_four_window","mcwwindows:warped_shutter","securitycraft:floor_trap","xnet:connector_red","domum_ornamentum:yellow_floating_carpet","ae2:network/cells/fluid_cell_housing","mcwpaths:andesite_running_bond_path","deepresonance:machine_frame","mcwdoors:jungle_glass_door","mcwbiomesoplenty:hellbark_window","xnet:netcable_red_dye","mcwwindows:pink_mosaic_glass_pane","mcwbiomesoplenty:stripped_empyreal_log_window2","dyenamics:honey_stained_glass","mcwfences:sandstone_grass_topped_wall","alltheores:gold_rod","additionallanterns:smooth_stone_chain","mcwlights:mangrove_ceiling_fan_light","biomesoplenty:magic_boat","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","mcwpaths:diorite_square_paving","supplementaries:key","mcwfurnitures:stripped_oak_table","securitycraft:reinforced_black_stained_glass_pane_from_dye","mcwroofs:white_concrete_upper_steep_roof","minecraft:cobbled_deepslate_stairs_from_cobbled_deepslate_stonecutting","additionallanterns:cobbled_deepslate_lantern","mcwdoors:jail_door","dyenamics:banner/ultramarine_banner","minecraft:dried_kelp_from_smelting","simplylight:illuminant_lime_block_on_toggle","railcraft:empty_detector","securitycraft:reinforced_jungle_fence_gate","securitycraft:reinforced_lime_stained_glass","securitycraft:reinforced_orange_stained_glass_pane_from_glass","mcwbiomesoplenty:redwood_window2","securitycraft:security_camera","mcwbiomesoplenty:palm_curved_gate","computercraft:computer_normal","cfm:light_gray_kitchen_drawer","mcwbiomesoplenty:magic_window2","minecraft:deepslate_tile_stairs_from_cobbled_deepslate_stonecutting","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:birch_blinds","mcwwindows:deepslate_window2","mcwwindows:diorite_parapet","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","reliquary:uncrafting/rotten_flesh","additionallanterns:normal_sandstone_lantern","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","mcwroofs:blackstone_top_roof","supplementaries:candle_holders/candle_holder_pink","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:light_weighted_pressure_plate","cfm:gray_cooler","mcwtrpdoors:jungle_mystic_trapdoor","everythingcopper:copper_axe","mcwbiomesoplenty:fir_four_window","cfm:green_kitchen_drawer","mcwtrpdoors:jungle_glass_trapdoor","securitycraft:reinforced_oak_fence","create:polished_cut_granite_stairs_from_stone_types_granite_stonecutting","mcwbridges:balustrade_granite_bridge","dyenamics:persimmon_stained_glass","twigs:gravel_bricks","immersiveengineering:crafting/empty_shell","cfm:light_blue_trampoline","handcrafted:oak_cupboard","simplylight:edge_light","mcwfences:railing_granite_wall","mcwroofs:oak_planks_upper_lower_roof","handcrafted:oak_nightstand","railcraft:personal_world_spike","mcwlights:lava_lamp","domum_ornamentum:light_gray_floating_carpet","mcwdoors:oak_nether_door","mcwbiomesoplenty:stripped_pine_log_window","minecraft:stone_brick_wall_from_stone_bricks_stonecutting","cfm:magenta_kitchen_drawer","dyenamics:bubblegum_concrete_powder","cfm:stripped_acacia_mail_box","handcrafted:andesite_pillar_trim","create:crafting/kinetics/empty_blaze_burner","handcrafted:oak_chair","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwroofs:magenta_concrete_steep_roof","mcwfences:mangrove_pyramid_gate","securitycraft:reinforced_light_blue_stained_glass","utilitix:gilding_crystal","mcwfences:spruce_stockade_fence","aether:iron_chestplate_repairing","charginggadgets:charging_station","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","supplementaries:slingshot","allthemodium:allthemodium_plate","twigs:cobblestone_from_pebble","simplylight:illuminant_green_block_toggle","create:cut_andesite_stairs_from_stone_types_andesite_stonecutting","everythingcopper:copper_rail","cfm:black_grill","mcwwindows:mangrove_window2","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","mcwbridges:balustrade_stone_bricks_bridge","simplylight:illuminant_block","mcwlights:green_paper_lamp","supplementaries:spring_launcher","dyenamics:dye_ultramarine_carpet","mcwbiomesoplenty:empyreal_curved_gate","cfm:crimson_kitchen_drawer","corail_woodcutter:jungle_woodcutter","aquaculture:neptunium_ingot_from_nuggets","create:small_granite_bricks_from_stone_types_granite_stonecutting","minecraft:iron_pickaxe","mcwbiomesoplenty:willow_plank_four_window","mcwwindows:jungle_shutter","mcwfences:railing_sandstone_wall","mcwroofs:magenta_concrete_attic_roof","create:cut_andesite_from_stone_types_andesite_stonecutting","mcwwindows:granite_window","deepresonance:resonating_plate","mcwroofs:deepslate_top_roof","mcwroofs:black_concrete_upper_steep_roof","mcwfences:modern_diorite_wall","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","rftoolsutility:fluid_module","minecraft:deepslate_tiles","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","cfm:post_box","forbidden_arcanus:clibano_combustion/copper_ingot_from_clibano_combusting","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","handcrafted:oak_drawer","mcwfences:modern_end_brick_wall","mcwwindows:warped_plank_parapet","mcwroofs:black_concrete_roof","rftoolsstorage:dump_module","cfm:orange_cooler","minecraft:lime_stained_glass","dyenamics:dye_wine_carpet","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","mcwroofs:pink_concrete_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_window","mcwpaths:andesite_running_bond_stairs","mcwbiomesoplenty:stripped_umbran_log_four_window","cfm:white_kitchen_counter","cfm:light_blue_kitchen_counter","immersiveengineering:crafting/plate_copper_hammering","mcwfences:granite_railing_gate","mcwroofs:magenta_concrete_top_roof","aether:packed_ice_freezing","mcwbiomesoplenty:stripped_palm_pane_window","trashcans:energy_trash_can","mcwpaths:stone_crystal_floor","mcwtrpdoors:jungle_cottage_trapdoor","cfm:red_kitchen_sink","mcwroofs:granite_attic_roof","mcwroofs:sandstone_upper_lower_roof","chimes:copper_chimes","cfm:fridge_dark","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","farmersdelight:cooking_pot","simplylight:illuminant_magenta_block_on_dyed","pneumaticcraft:security_upgrade","mcwbiomesoplenty:palm_plank_window","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","twigs:smooth_stone_brick_stairs_from_smooth_stone_stonecutting","reliquary:uncrafting/ghast_tear","mcwbiomesoplenty:empyreal_hedge","dyenamics:dye_icy_blue_carpet","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:crimson_plank_parapet","croptopia:frying_pan","cfm:stripped_jungle_kitchen_counter","mcwroofs:grass_attic_roof","mcwfences:acacia_wired_fence","mcwbridges:granite_bridge_stair","mcwtrpdoors:oak_ranch_trapdoor","mcwwindows:black_mosaic_glass","immersiveengineering:crafting/radiator","mcwroofs:gray_concrete_top_roof","supplementaries:candle_holders/candle_holder_purple","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","cfm:yellow_trampoline","mcwroofs:gutter_middle_lime","immersiveengineering:crafting/hammer","mcwpaths:cobbled_deepslate_running_bond_stairs","cfm:spatula","pneumaticcraft:charging_upgrade","dyenamics:dye_honey_carpet","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","mcwroofs:purple_concrete_attic_roof","minecraft:polished_granite_from_granite_stonecutting","constructionwand:core_angel","xnet:connector_red_dye","minecolonies:shapetool","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","cfm:blue_cooler","mcwroofs:gutter_base_light_gray","allthearcanistgear:elemental_hat_to_allthemodium_hat_smithing","delightful:knives/tin_knife","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","cfm:brown_kitchen_sink","mcwwindows:andesite_louvered_shutter","mcwlights:copper_candle_holder","securitycraft:reinforced_andesite_with_vanilla_diorite","minecraft:andesite_slab","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","pneumaticcraft:standby_upgrade","mcwwindows:jungle_window","minecraft:cobblestone_stairs","supplementaries:candle_holders/candle_holder_orange","mcwwindows:crimson_louvered_shutter","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","minecraft:stone_brick_stairs_from_stone_bricks_stonecutting","dyenamics:dye_lavender_carpet","cfm:crimson_kitchen_sink_dark","allthecompressed:compress/gravel_1x","tombstone:ankh_of_prayer","mcwbiomesoplenty:pine_highley_gate","mcwwindows:spruce_louvered_shutter","mcwfences:ornate_metal_fence","mcwfences:acacia_highley_gate","immersiveengineering:crafting/drillhead_iron","mcwroofs:andesite_steep_roof","cfm:black_kitchen_drawer","mcwwindows:bricks_pane_window","mcwroofs:pink_concrete_roof","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","create:crafting/logistics/powered_latch","solcarrot:food_book","mcwwindows:orange_mosaic_glass","utilitarian:utility/charcoal_from_campfire","mcwwindows:warped_planks_window2","minecraft:cracked_deepslate_bricks","utilitarian:utility/oak_logs_to_pressure_plates","cfm:acacia_kitchen_drawer","mcwpaths:cobbled_deepslate_windmill_weave_path","mcwfences:vintage_metal_fence","aquaculture:double_hook","allthemodium:allthemodium_rod","handcrafted:oak_bench","mcwfences:nether_brick_grass_topped_wall","utilitarian:angel_block","mcwroofs:diorite_roof","mcwwindows:birch_louvered_shutter","minecraft:red_concrete_powder","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","dyenamics:spring_green_concrete_powder","cfm:jungle_cabinet","minecraft:iron_leggings","mcwlights:tavern_lantern","mcwtrpdoors:print_whispering","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","create:cut_diorite_slab_from_stone_types_diorite_stonecutting","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","securitycraft:username_logger","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","additionallanterns:deepslate_bricks_chain","cfm:pink_kitchen_sink","mcwfurnitures:stripped_oak_drawer_counter","cfm:stripped_dark_oak_kitchen_sink_dark","dyenamics:banner/amber_banner","dyenamics:banner/spring_green_banner","mcwwindows:red_sandstone_window","mcwwindows:spruce_blinds","mcwwindows:mangrove_four_window","mcwwindows:jungle_four_window","mcwfences:deepslate_pillar_wall","create:cut_andesite_wall_from_stone_types_andesite_stonecutting","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","securitycraft:reinforced_orange_stained_glass_pane_from_dye","mcwlights:birch_tiki_torch","domum_ornamentum:light_blue_floating_carpet","rftoolsutility:inventory_module","mcwfences:stone_grass_topped_wall","cfm:blue_kitchen_sink","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","minecraft:iron_chestplate","cfm:stripped_jungle_bedside_cabinet","mcwlights:iron_small_chandelier","additionallanterns:normal_nether_bricks_lantern","mcwbridges:rope_oak_bridge","dyenamics:bed/mint_bed","mcwbiomesoplenty:jacaranda_horse_fence","twilightdelight:cutting/ice_bow","mcwwindows:oak_four_window","cfm:oak_coffee_table","minecraft:light_gray_stained_glass","mcwwindows:yellow_mosaic_glass_pane","securitycraft:reinforced_blue_stained_glass","mcwdoors:garage_gray_door","railcraft:world_spike","mcwfurnitures:stripped_oak_counter","minecraft:polished_andesite_from_andesite_stonecutting","mcwbridges:sandstone_bridge_pier","littlelogistics:seater_car","mcwbiomesoplenty:stripped_dead_pane_window","securitycraft:reinforced_diorite","minecraft:oak_pressure_plate","minecraft:stone_brick_stairs","bigreactors:turbine/basic/activefluidport_forge","cfm:brown_grill","mcwwindows:quartz_window","securitycraft:reinforced_dark_oak_fence","mcwroofs:gutter_base_brown","cfm:stripped_jungle_cabinet","blue_skies:coarse_lunar_dirt","reliquary:mob_charm_fragments/creeper","minecraft:smooth_stone_slab_from_smooth_stone_stonecutting","mcwbiomesoplenty:mahogany_horse_fence","cfm:gray_kitchen_drawer","minecraft:jungle_planks","minecraft:clock","mcwroofs:red_concrete_top_roof","securitycraft:portable_tune_player","mcwroofs:diorite_lower_roof","mcwwindows:warped_curtain_rod","railcraft:lead_gear","mcwpaths:diorite_windmill_weave","dyenamics:dye_peach_carpet","biomesoplenty:empyreal_boat","twigs:smooth_stone_brick_wall_from_smooth_stone_stonecutting","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","mcwfences:oak_wired_fence","twigs:silt","mcwroofs:blue_concrete_steep_roof","mcwroofs:grass_upper_steep_roof","aquaculture:iron_fillet_knife","silentgear:diamond_shard","mcwfurnitures:stripped_oak_double_drawer_counter","travelersbackpack:coal","mcwbridges:cobblestone_bridge_stair","dyenamics:bed/conifer_bed","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","dyenamics:peach_stained_glass","minecraft:diamond_leggings","mcwdoors:oak_modern_door","mcwwindows:birch_window2","mcwpaths:stone_running_bond_slab","cfm:stripped_crimson_kitchen_drawer","dyenamics:dye_fluorescent_carpet","mcwlights:blue_paper_lamp","securitycraft:reinforced_yellow_stained_glass_pane_from_dye","minecraft:purple_stained_glass","mcwlights:copper_wall_candle_holder","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","alltheores:tin_rod","mcwfurnitures:stripped_jungle_chair","mcwwindows:white_mosaic_glass","cfm:acacia_kitchen_sink_dark","mcwroofs:jungle_upper_steep_roof","minecraft:polished_andesite_slab_from_andesite_stonecutting","cfm:stripped_jungle_chair","securitycraft:reinforced_red_stained_glass_pane_from_dye","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","minecraft:polished_diorite_slab_from_diorite_stonecutting","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","utilitix:mob_yoinker","mcwwindows:andesite_four_window","minecraft:granite_slab","mcwlights:golden_low_candle_holder","supplementaries:feather_block","mcwpaths:stone_windmill_weave_slab","alchemistry:atomizer","supplementaries:cog_block","mcwbiomesoplenty:redwood_pyramid_gate","pneumaticcraft:armor_upgrade","mcwfences:railing_prismarine_wall","minecraft:lime_concrete_powder","minecraft:sandstone_wall_from_sandstone_stonecutting","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","mcwlights:festive_lantern","securitycraft:block_change_detector","twigs:rocky_dirt","mcwpaths:granite_flagstone_stairs","bloodmagic:sacrificial_dagger","mcwfences:end_brick_railing_gate","mcwlights:copper_low_candle_holder","mcwbiomesoplenty:maple_window2","mcwfences:jungle_pyramid_gate","blue_skies:coarse_turquoise_dirt","mcwbridges:bridge_torch","cfm:stripped_mangrove_kitchen_drawer","undergarden:wigglewood_boat","mcwroofs:gutter_middle_black","mcwroofs:white_concrete_upper_lower_roof","mcwfurnitures:oak_drawer_counter","mcwpaths:andesite_crystal_floor_slab","minecraft:wooden_shovel","securitycraft:reinforced_crimson_fence","createoreexcavation:vein_atlas","mcwwindows:birch_curtain_rod","create:crafting/kinetics/fluid_pipe","minecolonies:doublefern","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","create:small_granite_brick_slab_from_stone_types_granite_stonecutting","mcwpaths:red_sand_path_block","cfm:light_blue_cooler","create:copper_bars_from_ingots_copper_stonecutting","mcwdoors:print_jungle","dyenamics:dye_maroon_carpet","undergarden:gloom_o_lantern","minecraft:sandstone_slab","mcwwindows:birch_window","mcwfences:acacia_picket_fence","supplementaries:flags/flag_yellow","domum_ornamentum:sand_bricks","mcwbridges:balustrade_diorite_bridge","mcwfences:mud_brick_railing_gate","twigs:oak_table","minecraft:deepslate_brick_slab_from_cobbled_deepslate_stonecutting","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","railcraft:signal_block_surveyor","mcwpaths:stone_windmill_weave","mcwfurnitures:oak_lower_triple_drawer","securitycraft:reinforced_light_gray_stained_glass_pane_from_dye","ae2:misc/tiny_tnt","mcwroofs:green_concrete_steep_roof","mcwfences:mangrove_wired_fence","mcwfurnitures:oak_wardrobe","mcwdoors:jungle_japanese_door","mcwbiomesoplenty:willow_horse_fence","cfm:light_blue_grill","additionallanterns:mossy_cobblestone_lantern","mcwwindows:jungle_blinds","mcwlights:oak_tiki_torch","mcwlights:soul_cherry_tiki_torch","securitycraft:reinforced_mangrove_fence","mcwwindows:stripped_acacia_log_window","additionallanterns:normal_sandstone_chain","dyenamics:conifer_stained_glass","cfm:light_gray_trampoline","bloodmagic:blood_altar","mcwpaths:granite_diamond_paving","cfm:purple_kitchen_drawer","botania:flower_bag","create:cut_tuff_slab_from_stone_types_tuff_stonecutting","mcwfences:red_sandstone_railing_gate","dyenamics:aquamarine_stained_glass","mcwfences:dark_oak_wired_fence","cfm:jungle_kitchen_counter","mcwlights:golden_candle_holder","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","minecraft:oak_sign","mcwwindows:stone_pane_window","mcwpaths:diorite_flagstone_slab","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","mcwbridges:deepslate_brick_bridge_pier","mcwroofs:yellow_concrete_lower_roof","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:dark_oak_plank_parapet","mcwwindows:red_sandstone_four_window","mcwwindows:spruce_log_parapet","mcwbiomesoplenty:fir_plank_window2","mcwbridges:balustrade_andesite_bridge","additionallanterns:emerald_lantern","mcwwindows:golden_curtain_rod","everythingcopper:copper_bars","mcwbiomesoplenty:red_maple_hedge","securitycraft:reinforced_redstone_lamp","mcwbiomesoplenty:maple_pyramid_gate","pneumaticcraft:block_tracker_upgrade","mcwroofs:gutter_middle_blue","sfm:manager","mcwroofs:purple_concrete_roof","mcwbiomesoplenty:stripped_mahogany_log_window2","cfm:purple_grill","create:crafting/kinetics/hose_pulley","eidolon:smooth_stone_masonry_stonecutter_0","mcwwindows:stripped_dark_oak_pane_window","botania:red_string_container","bigreactors:turbine/basic/passivefluidport_forge","mcwwindows:cyan_mosaic_glass_pane","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","create:cut_andesite_slab_from_stone_types_andesite_stonecutting","mcwroofs:blackstone_upper_steep_roof","cfm:oak_crate","enderio:conduit_binder_composite","mcwbiomesoplenty:willow_window","rftoolsutility:fluidplus_module","minecraft:deepslate_tile_wall_from_cobbled_deepslate_stonecutting","deeperdarker:bloom_boat","create:small_diorite_brick_slab_from_stone_types_diorite_stonecutting","utilitix:linked_repeater","minecolonies:doublegrass","dyenamics:rose_stained_glass","farmersdelight:cooking/hot_cocoa","minecraft:purple_concrete_powder","create:small_tuff_bricks_from_stone_types_tuff_stonecutting","minecraft:polished_deepslate_slab_from_cobbled_deepslate_stonecutting","securitycraft:reinforced_jungle_fence","cfm:stripped_spruce_kitchen_drawer","dyenamics:dye_cherenkov_carpet","mcwroofs:pink_concrete_upper_lower_roof","mcwfences:bastion_metal_fence","mcwfences:spruce_curved_gate","mcwwindows:dark_oak_plank_window2","mcwroofs:light_blue_concrete_upper_steep_roof","mcwfurnitures:jungle_counter","mcwwindows:stripped_cherry_pane_window","mcwdoors:jungle_swamp_door","cfm:light_blue_kitchen_sink","immersiveengineering:crafting/light_engineering","mcwpaths:andesite_flagstone_stairs","create:diorite_pillar_from_stone_types_diorite_stonecutting","supplementaries:candle_holders/candle_holder_light_gray","reliquary:mob_charm_fragments/zombified_piglin","mcwwindows:warped_planks_four_window","create:polished_cut_diorite_from_stone_types_diorite_stonecutting","mcwwindows:stripped_crimson_stem_window2","mcwbiomesoplenty:stripped_empyreal_log_four_window","immersiveengineering:crafting/cokebrick","mcwbiomesoplenty:hellbark_pyramid_gate","reliquary:mob_charm_fragments/zombie","farmersdelight:cooking/dog_food","create:polished_cut_andesite_from_stone_types_andesite_stonecutting","mcwroofs:oak_planks_attic_roof","rftoolsutility:moduleplus_template","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","littlelogistics:vessel_charger","utilitix:minecart_tinkerer","mcwbridges:stone_bridge_pier","railcraft:radio_circuit","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","mcwlights:iron_candle_holder","tombstone:fishing_rod_of_misadventure","minecraft:dispenser","mcwpaths:cobbled_deepslate_clover_paving","mcwbiomesoplenty:mahogany_plank_window2","mcwfurnitures:oak_modern_wardrobe","mcwfences:panelled_metal_fence_gate","mcwlights:golden_double_candle_holder","mcwdoors:oak_western_door","cfm:light_gray_kitchen_sink","mcwfences:mangrove_picket_fence","aquaculture:planks_from_driftwood","mcwfences:acorn_metal_fence","mcwfurnitures:oak_drawer","minecraft:tripwire_hook","cfm:birch_kitchen_sink_dark","create:crafting/kinetics/mechanical_pump","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","minecraft:hopper","create:cut_andesite_brick_wall_from_stone_types_andesite_stonecutting","minecraft:cut_sandstone_from_sandstone_stonecutting","create:crafting/logistics/powered_toggle_latch","supplementaries:flags/flag_lime","travelersbackpack:iron_tier_upgrade","additionallanterns:red_sandstone_lantern","create:crafting/appliances/copper_backtank","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","utilitarian:snad/drit","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","mcwwindows:acacia_plank_parapet","mcwwindows:warped_blinds","deepresonance:radiation_suit_helmet","minecraft:iron_helmet","securitycraft:reinforced_pink_stained_glass_pane_from_dye","cfm:brown_kitchen_counter","mcwroofs:granite_top_roof","mcwroofs:lime_concrete_attic_roof","securitycraft:alarm","mcwfurnitures:oak_counter","rftoolsutility:charged_porter","minecraft:polished_diorite","mcwbiomesoplenty:umbran_pane_window","corail_woodcutter:spruce_woodcutter","create:crafting/materials/andesite_alloy_from_zinc","mcwroofs:oak_top_roof","mcwpaths:sandstone_honeycomb_paving","reliquary:uncrafting/gold_nugget","utilitix:crude_furnace","additionallanterns:amethyst_lantern","create:small_tuff_brick_slab_from_stone_types_tuff_stonecutting","cfm:crimson_kitchen_sink_light","utilitix:comparator_redirector_down","mcwwindows:stone_window","botania:petal_light_gray","create:copper_shingles_from_ingots_copper_stonecutting","everythingcopper:copper_sword","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","cfm:white_picket_gate","dyenamics:mint_concrete_powder","mcwfences:railing_nether_brick_wall","mcwpaths:cobbled_deepslate_flagstone_path","mcwfurnitures:stripped_oak_bookshelf","mcwwindows:brown_mosaic_glass","mcwfences:crimson_picket_fence","everythingcopper:copper_ladder","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","pneumaticcraft:speed_upgrade_from_glycerol","mcwfurnitures:jungle_chair","minecraft:heavy_weighted_pressure_plate","mcwroofs:red_concrete_upper_steep_roof","cfm:black_trampoline","immersiveengineering:crafting/wirecutter","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","mcwbiomesoplenty:stripped_willow_log_four_window","simplylight:illuminant_orange_block_dyed","create:layered_tuff_from_stone_types_tuff_stonecutting","alchemistry:compactor","minecraft:deepslate_brick_stairs","travelersbackpack:sandstone","minecraft:dropper","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","cfm:warped_kitchen_drawer","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","cfm:oak_mail_box","mcwpaths:granite_crystal_floor_path","mcwwindows:oak_window2","create:cut_granite_slab_from_stone_types_granite_stonecutting","mcwlights:light_gray_paper_lamp","forbidden_arcanus:iron_blacksmith_gavel","mcwfences:mangrove_horse_fence","minecraft:golden_helmet","mcwdoors:oak_japanese2_door","minecraft:polished_deepslate","simplylight:illuminant_black_block_dyed","simplylight:illuminant_gray_block_on_toggle","create:crafting/kinetics/rose_quartz_lamp","immersiveengineering:crafting/plate_nickel_hammering","mcwpaths:diorite_clover_paving","utilitarian:utility/oak_logs_to_slabs","mcwbridges:balustrade_deepslate_bricks_bridge","cfm:stripped_dark_oak_kitchen_sink_light","additionallanterns:obsidian_lantern","securitycraft:reinforced_spruce_fence_gate","dyenamics:cherenkov_concrete_powder","mcwwindows:quartz_pane_window","mcwtrpdoors:metal_trapdoor","botania:gray_shiny_flower","mcwbiomesoplenty:magic_pyramid_gate","minecraft:shield","mcwroofs:gray_concrete_steep_roof","cfm:jungle_bedside_cabinet","twigs:cobblestone_brick_wall_from_cobblestone_stonecutting","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","pneumaticcraft:minigun_upgrade","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","additionallanterns:cobblestone_chain","undergarden:slingshot","mcwpaths:sandstone_running_bond_slab","twigs:polished_rhyolite","dyenamics:dye_rose_carpet","additionallanterns:stone_bricks_chain","cfm:blue_kitchen_drawer","mcwbiomesoplenty:mahogany_pyramid_gate","blue_skies:food_prep_table","mcwpaths:stone_running_bond_stairs","mcwbridges:balustrade_sandstone_bridge","alltheores:nickel_rod","rftoolsbuilder:red_shield_template_block","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","handcrafted:andesite_corner_trim","minecraft:iron_nugget_from_smelting","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","twigs:smooth_stone_bricks_from_smooth_stone_stonecutting","xnet:advanced_connector_blue","mcwpaths:cobbled_deepslate_windmill_weave","mcwfences:modern_prismarine_wall","aquaculture:iron_nugget_from_blasting","securitycraft:motion_activated_light","mcwfences:double_curved_metal_fence","travelersbackpack:hose_nozzle","securitycraft:claymore","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","twigs:polished_tuff_stonecutting","reliquary:mob_charm_fragments/skeleton","mcwpaths:cobbled_deepslate_windmill_weave_slab","ae2:network/cables/dense_smart_fluix","mcwroofs:sandstone_steep_roof","twigs:rhyolite_wall","mcwroofs:gutter_base_cyan","mcwroofs:gutter_middle_gray","mcwfurnitures:oak_table","allthemodium:allthemodium_ingot","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","simplylight:illuminant_pink_block_on_dyed","minecraft:wooden_sword","minecraft:bone_meal","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","domum_ornamentum:sand_stone_bricks","mcwroofs:orange_concrete_upper_lower_roof","mcwbiomesoplenty:stripped_magic_log_window2","mcwpaths:andesite_diamond_paving","mcwwindows:prismarine_window","securitycraft:laser_block","mcwfences:modern_andesite_wall","mcwpaths:granite_crystal_floor","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","mcwroofs:light_gray_concrete_upper_lower_roof","mcwfurnitures:jungle_large_drawer","mcwroofs:deepslate_lower_roof","cfm:acacia_mail_box","minecraft:cookie","aquaculture:spruce_fish_mount","rftoolsutility:clock_module","minecraft:lightning_rod","mcwwindows:metal_four_window","minecraft:orange_concrete_powder","mcwbiomesoplenty:umbran_window","minecraft:redstone_block","minecraft:shears","supplementaries:flags/flag_blue","cfm:lime_kitchen_drawer","mcwbiomesoplenty:redwood_pane_window","allthecompressed:compress/cobblestone_1x","mcwpaths:stone_running_bond_path","simplylight:illuminant_green_block_dyed","minecraft:andesite_stairs_from_andesite_stonecutting","minecraft:stone_brick_slab_from_stone_bricks_stonecutting","securitycraft:reinforced_pink_stained_glass_pane_from_glass","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","create:vertical_framed_glass_from_glass_colorless_stonecutting","mcwfences:granite_pillar_wall","domum_ornamentum:orange_floating_carpet","simplylight:illuminant_brown_block_on_dyed","delightful:knives/lead_knife","securitycraft:reinforced_warped_fence","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","croptopia:nougat","rftoolsstorage:storage_module0","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","allthemodium:allthemodium_nugget_from_ingot","dyenamics:icy_blue_concrete_powder","mcwfurnitures:stripped_jungle_cupboard_counter","rftoolsbase:crafting_card","forbidden_arcanus:deorum_glass","supplementaries:candle_holders/candle_holder_gray","minecraft:oak_fence_gate","supplementaries:turn_table","rftoolsbuilder:space_chamber_card","aether:iron_pickaxe_repairing","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","buildinggadgets2:gadget_copy_paste","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:umbran_pyramid_gate","mcwroofs:yellow_concrete_attic_roof","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","minecraft:sandstone_stairs_from_sandstone_stonecutting","mcwpaths:podzol_path_block","bigreactors:turbine/reinforced/passivetap_fe","mcwbiomesoplenty:cypress_hedge","cfm:stripped_warped_kitchen_sink_light","additionallanterns:deepslate_bricks_lantern","securitycraft:reinforced_dropper","aquaculture:oak_fish_mount","minecraft:diamond_boots","cfm:stripped_jungle_mail_box","mcwbiomesoplenty:redwood_wired_fence","handcrafted:oak_couch","mcwfurnitures:stripped_oak_triple_drawer","allthetweaks:ender_pearl_block","mcwwindows:mangrove_plank_four_window","mcwpaths:stone_windmill_weave_path","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","forbidden_arcanus:deorum_lantern","everythingcopper:copper_door","allthecompressed:compress/oak_planks_1x","mcwroofs:orange_concrete_steep_roof","mcwwindows:warped_planks_window","cfm:orange_kitchen_counter","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","mcwbiomesoplenty:stripped_palm_log_four_window","mcwroofs:gutter_base_light_blue","mcwpaths:andesite_crystal_floor_path","minecraft:dried_kelp_from_campfire_cooking","mcwbridges:granite_bridge","minecraft:diamond_block","handcrafted:diorite_corner_trim","mcwroofs:deepslate_roof","minecraft:deepslate_tile_stairs_from_deepslate_bricks_stonecutting","mcwdoors:metal_door","simplylight:illuminant_pink_block_dyed","minecraft:diorite_slab","dyenamics:cherenkov_stained_glass","create:polished_cut_andesite_wall_from_stone_types_andesite_stonecutting","minecraft:yellow_stained_glass","mcwtrpdoors:oak_beach_trapdoor","mcwbiomesoplenty:stripped_magic_log_window","mcwwindows:red_sandstone_pane_window","cfm:green_cooler","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","cfm:stripped_crimson_kitchen_sink_light","mcwwindows:acacia_shutter","simplylight:illuminant_lime_block_on_dyed","securitycraft:reinforced_white_stained_glass_pane_from_dye","cfm:dark_oak_kitchen_sink_light","securitycraft:reinforced_brown_stained_glass_pane_from_dye","create:cut_diorite_brick_slab_from_stone_types_diorite_stonecutting","mcwbiomesoplenty:willow_pane_window","aether:flint_and_steel_repairing","minecraft:lapis_block","aether:skyroot_chest","deepresonance:filter_material","mcwbiomesoplenty:fir_wired_fence","supplementaries:flower_box","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","utilitarian:snad/snad","mcwbiomesoplenty:empyreal_plank_window2","securitycraft:blacklist_module","dyenamics:amber_stained_glass","mcwbiomesoplenty:stripped_fir_pane_window","mcwbiomesoplenty:snowblossom_hedge","minecraft:cobblestone_slab_from_cobblestone_stonecutting","minecraft:oak_boat","mcwfurnitures:stripped_jungle_covered_desk","cfm:black_picket_gate","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","immersiveengineering:crafting/plate_lead_hammering","simplylight:illuminant_blue_block_on_toggle","mcwbiomesoplenty:redwood_horse_fence","cfm:red_trampoline","mcwfurnitures:stripped_jungle_large_drawer","minecraft:glass_bottle","mcwfences:mesh_metal_fence","minecraft:deepslate_brick_slab_from_deepslate_bricks_stonecutting","minecraft:polished_granite","mcwbiomesoplenty:magic_plank_window2","twigs:cobblestone_brick_stairs_from_cobblestone_stonecutting","dyenamics:banner/conifer_banner","travelersbackpack:spider","securitycraft:reinforced_magenta_stained_glass","mcwroofs:diorite_upper_steep_roof","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","twilightforest:transformation_boat","botania:speed_up_belt","cfm:acacia_kitchen_sink_light","dyenamics:bed/cherenkov_bed","mcwroofs:light_gray_concrete_attic_roof","mcwroofs:purple_concrete_lower_roof","domum_ornamentum:green_cobblestone_extra","alltheores:zinc_plate","mcwwindows:prismarine_pane_window","mcwpaths:diorite_flagstone_path","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","farmersdelight:cooking/beef_stew","mcwbiomesoplenty:stripped_redwood_log_window2","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","supplementaries:slice_map","mcwroofs:cobblestone_lower_roof","occultism:crafting/butcher_knife","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","securitycraft:reinforced_sticky_piston","mcwfences:end_brick_pillar_wall","create:polished_cut_tuff_stairs_from_stone_types_tuff_stonecutting","cfm:gray_grill","aether:skyroot_iron_vanilla_shield","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","minecraft:cooked_chicken_from_campfire_cooking","mcwfurnitures:jungle_modern_chair","handcrafted:golden_thick_pot","undergarden:undergarden_scaffolding","modularrouters:modular_router","delightful:knives/nickel_knife","mcwfurnitures:stripped_jungle_coffee_table","cfm:cyan_grill","simplylight:illuminant_purple_block_dyed","farmersdelight:iron_knife","mcwfurnitures:stripped_jungle_double_drawer","minecraft:deepslate_tiles_from_cobbled_deepslate_stonecutting","cfm:stripped_spruce_mail_box","cfm:gray_kitchen_sink","pneumaticcraft:inventory_upgrade","mcwfences:bamboo_stockade_fence","mcwwindows:pink_mosaic_glass","cfm:dark_oak_kitchen_sink_dark","mcwwindows:jungle_louvered_shutter","everythingcopper:copper_shovel","create:cut_andesite_brick_stairs_from_stone_types_andesite_stonecutting","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","minecraft:cut_sandstone_slab_from_sandstone_stonecutting","cfm:dark_oak_mail_box","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","mcwpaths:stone_strewn_rocky_path","cfm:stripped_jungle_kitchen_sink_light","mcwpaths:stone_crystal_floor_slab","delightful:smoking/roasted_acorn","minecraft:bucket","handcrafted:salmon_trophy","mcwroofs:grass_upper_lower_roof","supplementaries:speaker_block","farmersdelight:chicken_sandwich","minecraft:polished_andesite","cfm:oak_desk_cabinet","securitycraft:reinforced_green_stained_glass_pane_from_glass","corail_woodcutter:birch_woodcutter","mcwlights:iron_wall_candle_holder","mcwpaths:andesite_honeycomb_paving","mcwbridges:sandstone_bridge","mcwwindows:birch_log_parapet","immersiveengineering:crafting/wire_copper","rftoolsutility:button_module","mcwroofs:black_concrete_attic_roof","minecraft:charcoal","minecraft:diorite_wall_from_diorite_stonecutting","mcwfurnitures:stripped_oak_double_wardrobe","twigs:tuff_wall","mcwfences:railing_deepslate_brick_wall","allthecompressed:compress/dirt_1x","silentgear:emerald_shard","dyenamics:dye_amber_carpet","cfm:white_picket_fence","everythingcopper:copper_trapdoor","bigreactors:reactor/reinforced/activefluidport_forge","mcwbiomesoplenty:redwood_highley_gate","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","simplylight:illuminant_block_on_toggle","domum_ornamentum:purple_floating_carpet","twigs:polished_tuff_bricks_from_tuff_stonecutting","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwroofs:yellow_concrete_roof","mcwwindows:crimson_blinds","sophisticatedstorage:storage_stack_upgrade_omega_tier_from_backpack_stack_upgrade_omega_tier","dyenamics:banner/aquamarine_banner","mcwfurnitures:stripped_oak_glass_table","cfm:white_trampoline","mcwroofs:blackstone_attic_roof","handcrafted:oak_desk","xnet:connector_blue_dye","utilitarian:utility/jungle_logs_to_doors","mcwroofs:gutter_base_blue","mcwfurnitures:stripped_oak_desk","mcwwindows:diorite_louvered_shutter","terralith:piston_alt","corail_woodcutter:crimson_woodcutter","securitycraft:universal_block_modifier","mcwfurnitures:oak_end_table","minecraft:coal_block","mcwlights:cherry_ceiling_fan_light","croptopia:candied_nuts","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","pneumaticcraft:volume_upgrade","mcwtrpdoors:oak_four_panel_trapdoor","mcwbiomesoplenty:fir_highley_gate","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","additionallanterns:gold_chain","mcwwindows:crimson_stem_window2","cfm:stripped_acacia_kitchen_sink_dark","mcwfences:curved_metal_fence_gate","dyenamics:maroon_stained_glass","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","mcwpaths:cobbled_deepslate_flagstone_slab","mcwpaths:sandstone_dumble_paving","mcwlights:black_paper_lamp","dyenamics:banner/cherenkov_banner","mcwroofs:andesite_lower_roof","mcwwindows:stripped_acacia_log_window2","sophisticatedstorage:oak_chest","mcwbridges:andesite_bridge","mcwwindows:dark_prismarine_pane_window","minecraft:chiseled_stone_bricks_from_stone_bricks_stonecutting","mcwwindows:oak_plank_parapet","mcwfurnitures:oak_modern_desk","minecraft:iron_sword","mcwpaths:sandstone_clover_paving","aquaculture:sushi","mcwfurnitures:jungle_table","mcwdoors:metal_reinforced_door","domum_ornamentum:blue_cobblestone_extra","cfm:stripped_oak_coffee_table","cfm:mangrove_kitchen_sink_light","mcwfences:acacia_stockade_fence","minecraft:dried_kelp_from_smoking","minecraft:chiseled_sandstone_from_sandstone_stonecutting","mcwbiomesoplenty:jacaranda_pyramid_gate","mcwpaths:andesite_dumble_paving","delightful:campfire/roasted_acorn","twilightforest:canopy_boat","mcwpaths:sandstone_running_bond_path","sophisticatedstorage:generic_limited_barrel_3","mcwlights:jungle_ceiling_fan_light","sophisticatedstorage:generic_limited_barrel_4","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","dyenamics:mint_stained_glass","create:cut_tuff_brick_stairs_from_stone_types_tuff_stonecutting","supplementaries:redstone_illuminator","mcwpaths:sandstone_flagstone_stairs","mcwroofs:gutter_base_lime","deepresonance:tank","mcwlights:striped_lantern","minecraft:deepslate_brick_wall_from_deepslate_bricks_stonecutting","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","mcwroofs:gutter_middle_red","minecraft:andesite_wall_from_andesite_stonecutting","mcwfences:crimson_stockade_fence","mcwbiomesoplenty:jacaranda_four_window","minecraft:conduit","pneumaticcraft:magnet_upgrade","mcwbridges:iron_bridge_pier","mcwwindows:mangrove_blinds","minecraft:sticky_piston","mcwwindows:dark_oak_plank_window","mcwroofs:light_blue_concrete_roof","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwpaths:cobbled_deepslate_diamond_paving","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:cyan_concrete_upper_lower_roof","cfm:oak_kitchen_sink_light","mcwwindows:diorite_pane_window","delightful:storage/acorn_storage_block","minecraft:green_concrete_powder","mcwroofs:gutter_base_white","mcwpaths:granite_crystal_floor_slab","additionallanterns:stone_lantern","supplementaries:flags/flag_red","mcwroofs:magenta_concrete_upper_lower_roof","mcwroofs:cyan_concrete_steep_roof","mcwfurnitures:oak_desk","create:small_tuff_brick_wall_from_stone_types_tuff_stonecutting","pneumaticcraft:gilded_upgrade","mcwpaths:sandstone_running_bond","minecraft:yellow_concrete_powder","mcwwindows:deepslate_window","mcwwindows:white_mosaic_glass_pane","mcwroofs:pink_concrete_attic_roof","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwroofs:red_concrete_steep_roof","minecraft:fishing_rod","xnet:connector_yellow_dye","delightful:knives/silver_knife","mcwwindows:acacia_blinds","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwroofs:roofing_hammer","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","create:copper_tiles_from_ingots_copper_stonecutting","supplementaries:candle_holders/candle_holder_cyan","minecraft:granite_wall_from_granite_stonecutting","supplementaries:timber_frame","sophisticatedstorage:packing_tape","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","create:cut_tuff_stairs_from_stone_types_tuff_stonecutting","railcraft:tin_gear","mcwlights:double_street_lamp","minecraft:tnt","minecraft:mossy_stone_bricks_from_vine","minecraft:andesite_slab_from_andesite_stonecutting","minecolonies:chainmailboots","mcwlights:copper_small_chandelier","additionallanterns:quartz_lantern","minecraft:flint_and_steel","dyenamics:navy_concrete_powder","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","mcwroofs:diorite_top_roof","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","create:cut_granite_wall_from_stone_types_granite_stonecutting","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","cfm:stripped_dark_oak_kitchen_drawer","mcwbridges:oak_rail_bridge","xnet:advanced_connector_blue_dye","minecraft:white_wool_from_string","mcwroofs:cyan_concrete_roof","mcwbiomesoplenty:empyreal_plank_pane_window","mcwwindows:dark_prismarine_window","minecraft:cobblestone_wall_from_cobblestone_stonecutting","ae2:network/cells/item_cell_housing","mcwdoors:oak_bamboo_door","travelersbackpack:creeper","xnet:advanced_connector_red_dye","handcrafted:sandstone_pillar_trim","minecraft:brown_concrete_powder","securitycraft:reinforced_lime_stained_glass_pane_from_dye","utilitarian:fluid_hopper","railcraft:invar_ingot_crafted_with_ingots","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","minecraft:oak_slab","handcrafted:oak_fancy_bed","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","mcwwindows:granite_four_window","create:tiled_glass_from_glass_colorless_stonecutting","mcwlights:wall_lantern","mcwroofs:light_blue_concrete_steep_roof","immersiveengineering:crafting/concrete","supplementaries:stone_tile","rftoolsbuilder:blue_shield_template_block","mcwtrpdoors:oak_blossom_trapdoor","create:cut_tuff_brick_wall_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:willow_plank_window","supplementaries:flags/flag_white","mcwbiomesoplenty:redwood_stockade_fence","create:horizontal_framed_glass_from_glass_colorless_stonecutting","mcwwindows:metal_window","mcwroofs:andesite_top_roof","minecraft:granite_slab_from_granite_stonecutting","minecraft:glass_pane","supplementaries:timber_brace","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","cfm:warped_kitchen_sink_dark","mcwroofs:gutter_middle_yellow","mcwroofs:orange_concrete_roof","minecraft:golden_sword","securitycraft:reinforced_brown_stained_glass","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","securitycraft:reinforced_magenta_stained_glass_pane_from_dye","securitycraft:sonic_security_system","create:polished_cut_granite_slab_from_stone_types_granite_stonecutting","minecraft:clay","mcwbridges:deepslate_brick_bridge","mcwbiomesoplenty:pine_pane_window","mcwroofs:magenta_concrete_roof","enderio:wood_gear","supplementaries:bed_from_feather_block","mcwpaths:andesite_basket_weave_paving","cfm:red_kitchen_counter","mcwlights:copper_chain","mcwwindows:oak_louvered_shutter","mcwpaths:granite_running_bond","mcwwindows:cyan_mosaic_glass","mcwbridges:diorite_bridge","mcwlights:magenta_paper_lamp","mcwroofs:pink_concrete_steep_roof","ae2additions:blocks/wireless_transceiver","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","cfm:orange_kitchen_drawer","croptopia:chicken_and_noodles","silentgear:stone_rod","handcrafted:sandstone_corner_trim","mcwbridges:oak_log_bridge_middle","mcwpaths:diorite_crystal_floor_path","wirelesschargers:basic_wireless_block_charger","mcwwindows:gray_mosaic_glass","cfm:oak_hedge","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","utilitix:highspeed_rail","mcwfurnitures:jungle_bookshelf","mcwfences:jungle_stockade_fence","mcwlights:pink_paper_lamp","twigs:polished_rhyolite_bricks_from_rhyolite_stonecutting","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","cfm:yellow_kitchen_sink","silentgear:diamond_from_shards","aether:iron_gloves","minecraft:chiseled_stone_bricks","tombstone:white_marble","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","alltheores:iron_rod","mcwfences:railing_diorite_wall","dyenamics:dye_mint_carpet","caupona:mud_kitchen_stove","mcwpaths:diorite_strewn_rocky_path","pneumaticcraft:stomp_upgrade","minecraft:polished_granite_stairs_from_granite_stonecutting","mcwwindows:crimson_stem_parapet","mcwbiomesoplenty:mahogany_highley_gate","allthemodium:allthemodium_carrot","create:small_granite_brick_stairs_from_stone_types_granite_stonecutting","domum_ornamentum:magenta_floating_carpet","xnet:advanced_connector_routing","mcwbiomesoplenty:pine_pyramid_gate","mcwfences:railing_stone_brick_wall","minecraft:brush","mcwwindows:bricks_window2","mcwpaths:stone_crystal_floor_stairs","minecraft:deepslate_tiles_from_deepslate_bricks_stonecutting","sfm:cable","mcwroofs:deepslate_upper_steep_roof","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","mcwfences:modern_granite_wall","forbidden_arcanus:golden_blacksmith_gavel","mcwbiomesoplenty:dead_wired_fence","mcwdoors:oak_tropical_door","mcwbiomesoplenty:mahogany_pane_window","mcwbiomesoplenty:stripped_willow_pane_window","mcwroofs:blue_concrete_top_roof","simplylight:illuminant_block_on","aquaculture:jungle_fish_mount","mcwfences:mangrove_highley_gate","create:polished_cut_andesite_stairs_from_stone_types_andesite_stonecutting","farmersdelight:cooking/bone_broth","bigreactors:reactor/reinforced/passivefluidport_forge","dyenamics:banner/peach_banner","mcwdoors:oak_waffle_door","mcwwindows:andesite_parapet","alltheores:zinc_gear","biomesoplenty:dead_boat","supplementaries:flags/flag_orange","create:polished_cut_diorite_wall_from_stone_types_diorite_stonecutting","mcwwindows:bricks_window","cfm:jungle_mail_box","aquaculture:iron_hook","aquaculture:diamond_fishing_rod","mcwwindows:andesite_window","travelersbackpack:iron","mcwroofs:oak_planks_upper_steep_roof","additionallanterns:basalt_lantern","cfm:brown_kitchen_drawer","mcwfences:blackstone_grass_topped_wall","aquaculture:diamond_hook","dyenamics:rose_concrete_powder","minecraft:chain","mcwdoors:oak_glass_door","cfm:stripped_acacia_kitchen_drawer","forbidden_arcanus:tiled_polished_darkstone_bricks_from_darkstone_stonecutting","domum_ornamentum:blue_floating_carpet","alltheores:osmium_rod","minecraft:oak_fence","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwpaths:stone_flagstone_slab","simplylight:illuminant_black_block_on_toggle","mcwroofs:black_concrete_top_roof","mcwwindows:acacia_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","mcwwindows:granite_louvered_shutter","mcwroofs:gray_concrete_roof","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","securitycraft:sentry","mcwroofs:magenta_concrete_lower_roof","cfm:cyan_kitchen_counter","buildinggadgets2:gadget_destruction","minecraft:deepslate","mcwwindows:blue_mosaic_glass_pane","mcwdoors:jungle_mystic_door","mcwbiomesoplenty:maple_wired_fence","silentgear:fine_silk_cloth","cfm:green_kitchen_counter","minecraft:crossbow","mcwpaths:sandstone_crystal_floor_path","pneumaticcraft:coordinate_tracker_upgrade","mcwfurnitures:stripped_oak_modern_chair","mcwbridges:cobblestone_bridge_pier","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","create:cut_granite_brick_stairs_from_stone_types_granite_stonecutting","mcwbiomesoplenty:willow_wired_fence","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwroofs:black_concrete_upper_lower_roof","mcwroofs:gray_concrete_upper_lower_roof","mcwroofs:sandstone_upper_steep_roof","mcwwindows:orange_mosaic_glass_pane","minecraft:deepslate_tile_wall_from_deepslate_bricks_stonecutting","croptopia:roasted_smoking","mcwbiomesoplenty:hellbark_curved_gate","minecraft:white_stained_glass","xnet:advanced_connector_green_dye","securitycraft:taser","supplementaries:soap","create:cut_diorite_brick_stairs_from_stone_types_diorite_stonecutting","mcwbridges:cobblestone_bridge","mcwwindows:oak_plank_window2","rftoolsutility:redstone_information","supplementaries:gold_door","cfm:dark_oak_kitchen_drawer","mcwbiomesoplenty:pine_plank_window","create:andesite_pillar_from_stone_types_andesite_stonecutting","mcwfences:acacia_horse_fence","mcwpaths:diorite_windmill_weave_slab","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwdoors:jungle_classic_door","mcwbiomesoplenty:willow_plank_window2","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","domum_ornamentum:pink_floating_carpet","securitycraft:reinforced_andesite","supplementaries:altimeter","mcwroofs:lime_concrete_roof","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","mcwroofs:gutter_middle_green","cfm:red_kitchen_drawer","cfm:stripped_jungle_desk","dyenamics:bed/spring_green_bed","cfm:stripped_dark_oak_mail_box","minecraft:red_stained_glass","mcwpaths:cobbled_deepslate_crystal_floor_slab","simplylight:illuminant_light_gray_block_on_toggle","supplementaries:candle_holders/candle_holder_blue","mcwpaths:cobbled_deepslate_crystal_floor","mcwlights:jungle_tiki_torch","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","allthecompressed:compress/sand_1x","additionallanterns:cobbled_deepslate_chain","minecraft:gold_nugget","littlelogistics:fluid_barge","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","mcwpaths:cobbled_deepslate_strewn_rocky_path","cfm:stripped_jungle_desk_cabinet","mcwwindows:stripped_dark_oak_log_window","utilitix:dimmable_redstone_lamp","minecraft:jungle_wood","handcrafted:bench","allthecompressed:compress/andesite_1x","mcwwindows:diorite_four_window","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","appmek:chemical_cell_housing","mcwroofs:light_gray_concrete_top_roof","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","travelersbackpack:backpack_tank","minecraft:blast_furnace","additionallanterns:smooth_stone_lantern","mcwbridges:glass_bridge","mcwbiomesoplenty:pine_plank_four_window","mcwroofs:blackstone_steep_roof","mcwroofs:cobblestone_top_roof","securitycraft:electrified_iron_fence","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwwindows:warped_plank_pane_window","mcwfurnitures:stripped_oak_wardrobe","croptopia:campfire_molasses","securitycraft:reinforced_blue_stained_glass_pane_from_dye","supplementaries:candle_holders/candle_holder_white","occultism:crafting/lens_frame","mcwwindows:stripped_birch_log_four_window","railcraft:water_tank_siding","cfm:jungle_kitchen_sink_dark","mcwroofs:blackstone_roof","utilitix:weak_redstone_torch","silentgear:upgrade_base","xnet:advanced_connector_green","mcwroofs:black_concrete_steep_roof","mcwwindows:crimson_stem_four_window","minecraft:cobbled_deepslate_stairs","minecraft:fletching_table","handcrafted:pufferfish_trophy","mcwbiomesoplenty:fir_picket_fence","securitycraft:mine","securitycraft:reinforced_orange_stained_glass","mcwfences:expanded_mesh_metal_fence","supplementaries:candle_holders/candle_holder_lime","mcwlights:framed_torch","dyenamics:bed/peach_bed","cfm:spruce_kitchen_sink_dark","mcwdoors:jungle_cottage_door","mcwbiomesoplenty:maple_stockade_fence","minecraft:pink_stained_glass","mcwroofs:gutter_middle_cyan","minecraft:golden_leggings","cfm:mangrove_mail_box","simplylight:illuminant_purple_block_on_toggle","handcrafted:oak_dining_bench","twigs:tuff_stairs","aether:wooden_pickaxe_repairing","mcwfences:modern_mud_brick_wall","allthecompressed:compress/grass_block_1x","mcwtrpdoors:print_beach","mcwfences:bamboo_curved_gate","mcwwindows:spruce_shutter","minecraft:diorite_slab_from_diorite_stonecutting","cfm:blue_trampoline","handcrafted:wolf_trophy","mcwwindows:black_mosaic_glass_pane","securitycraft:sc_manual","mcwroofs:red_concrete_upper_lower_roof","everythingcopper:copper_helmet","mcwpaths:andesite_windmill_weave_path","modularrouters:blank_upgrade","mcwbiomesoplenty:jacaranda_pane_window","dyenamics:banner/wine_banner","corail_woodcutter:acacia_woodcutter","silentgear:sturdy_repair_kit","mcwfences:guardian_metal_fence","pneumaticcraft:flippers_upgrade","minecraft:mangrove_boat","mcwdoors:garage_silver_door","minecolonies:eggdrop_soup","securitycraft:reinforced_red_stained_glass_pane_from_glass","create:crafting/appliances/copper_diving_helmet","supplementaries:flags/flag_light_gray","simplylight:illuminant_blue_block_toggle","mcwbiomesoplenty:magic_four_window","alltheores:silver_rod","mcwfurnitures:stripped_oak_cupboard_counter","minecraft:lever","alltheores:osmium_plate","mcwwindows:stripped_warped_stem_four_window","mcwwindows:dark_oak_curtain_rod","mcwroofs:white_concrete_steep_roof","mcwbiomesoplenty:jacaranda_plank_four_window","mcwroofs:gray_concrete_lower_roof","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","minecraft:cobbled_deepslate_slab_from_cobbled_deepslate_stonecutting","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","mcwfences:iron_cheval_de_frise","minecraft:iron_shovel","tombstone:dark_marble","cfm:lime_kitchen_sink","biomesoplenty:willow_boat","mcwwindows:purple_mosaic_glass_pane","mcwfences:deepslate_grass_topped_wall","mcwpaths:cobbled_deepslate_windmill_weave_stairs","mcwfurnitures:jungle_desk","domum_ornamentum:white_floating_carpet","mcwroofs:brown_concrete_upper_steep_roof","securitycraft:track_mine","mcwfences:bastion_metal_fence_gate","securitycraft:disguise_module","mcwwindows:deepslate_four_window","mcwfurnitures:oak_covered_desk","mcwwindows:dark_oak_louvered_shutter","mcwpaths:granite_strewn_rocky_path","domum_ornamentum:blockbarreldeco_standing","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","cfm:fridge_light","supplementaries:gold_trapdoor","mcwroofs:andesite_upper_lower_roof","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","minecraft:cooked_chicken","enderio:basic_fluid_filter","rftoolsutility:counter_module","create:granite_pillar_from_stone_types_granite_stonecutting","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","minecraft:oak_door","mcwroofs:red_concrete_attic_roof","delightful:knives/zinc_knife","mcwroofs:granite_steep_roof","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","mcwlights:cyan_paper_lamp","minecraft:nether_brick","mcwdoors:jungle_waffle_door","mcwbiomesoplenty:hellbark_pane_window","mythicbotany:alfsteel_template","mcwlights:warped_ceiling_fan_light","ad_astra:coal_generator","minecraft:polished_diorite_stairs_from_diorite_stonecutting","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwwindows:window_centre_bar_base","mcwfurnitures:stripped_oak_end_table","mcwbridges:asian_red_bridge","minecraft:allthemodium_mage_leggings_smithing","botania:vine_ball","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","cfm:stripped_oak_kitchen_sink_light","mcwroofs:lime_concrete_upper_lower_roof","handcrafted:oak_corner_trim","cfm:spruce_kitchen_drawer","mcwbiomesoplenty:magic_highley_gate","create:cut_diorite_brick_wall_from_stone_types_diorite_stonecutting","mcwroofs:yellow_concrete_upper_lower_roof","simplemagnets:basic_demagnetization_coil","mcwlights:soul_jungle_tiki_torch","securitycraft:limited_use_keycard","supplementaries:candle_holders/candle_holder","twigs:cobblestone_bricks_stonecutting","cfm:light_gray_kitchen_counter","minecraft:candle","framedblocks:framed_cube","mcwbiomesoplenty:flowering_oak_hedge","mcwpaths:andesite_flagstone_slab","utilitarian:utility/logs_to_bowls","mcwroofs:light_gray_concrete_roof","supplementaries:candle_holders/candle_holder_magenta","minecraft:deepslate_tile_slab_from_cobbled_deepslate_stonecutting","mcwfences:oak_curved_gate","supplementaries:wrench","minecraft:bow","deepresonance:radiation_suit_boots","securitycraft:reinforced_cyan_stained_glass_pane_from_glass","aether:skyroot_smithing_table","aquaculture:brown_mushroom_from_fish","mcwwindows:light_gray_mosaic_glass_pane","securitycraft:reinforced_oak_fence_gate","minecraft:andesite_stairs","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","cfm:purple_kitchen_sink","cfm:black_cooler","mcwpaths:diorite_dumble_paving","supplementaries:sconce","silentgear:rough_rod","mcwwindows:jungle_plank_parapet","minecraft:dark_oak_boat","aquaculture:gold_hook","mcwbiomesoplenty:mahogany_four_window","mcwbiomesoplenty:palm_wired_fence","securitycraft:reinforced_yellow_stained_glass_pane_from_glass","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","mcwbiomesoplenty:hellbark_stockade_fence","mcwwindows:dark_oak_plank_pane_window","minecraft:firework_rocket_simple","mcwwindows:stripped_spruce_log_four_window","mcwroofs:blackstone_upper_lower_roof","mcwfurnitures:oak_coffee_table","minecraft:andesite","mcwroofs:andesite_roof","rftoolsbase:infused_enderpearl","supplementaries:pedestal","minecraft:allthemodium_mage_helmet_smithing","cfm:lime_trampoline","securitycraft:reinforced_acacia_fence_gate","supplementaries:iron_gate","mcwlights:light_blue_paper_lamp","travelersbackpack:chicken","mcwwindows:deepslate_pane_window","mcwfences:andesite_grass_topped_wall","mcwpaths:sandstone_flagstone_path","forbidden_arcanus:wooden_blacksmith_gavel","handcrafted:silverfish_trophy","mcwdoors:metal_windowed_door","cfm:yellow_kitchen_counter","mcwdoors:jungle_tropical_door","minecraft:stone","twigs:lamp","dyenamics:bed/lavender_bed","cfm:lime_kitchen_counter","mcwroofs:brown_concrete_lower_roof","travelersbackpack:lapis","terralith:lever_alt","simplylight:illuminant_cyan_block_dyed","minecraft:deepslate_tile_slab_from_deepslate_bricks_stonecutting","securitycraft:speed_module","domum_ornamentum:cream_stone_bricks","mcwdoors:garage_black_door","cfm:stripped_oak_kitchen_sink_dark","mcwfurnitures:jungle_triple_drawer","tombstone:carmin_marble","mcwdoors:oak_barn_door","cfm:cyan_kitchen_drawer","mcwpaths:stone_flagstone_path","rftoolsutility:text_module","mcwfurnitures:jungle_lower_bookshelf_drawer","dyenamics:dye_bubblegum_carpet","botania:redstone_root","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","mcwpaths:sandstone_diamond_paving","dyenamics:dye_aquamarine_carpet","minecraft:cobbled_deepslate_wall","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","sfm:printing_press","supplementaries:timber_cross_brace","bigreactors:turbine/reinforced/activefluidport_forge","minecolonies:chainmailleggings","mcwroofs:purple_concrete_top_roof","supplementaries:candle_holders/candle_holder_black","create:layered_diorite_from_stone_types_diorite_stonecutting","simplylight:illuminant_orange_block_on_dyed","mcwpaths:sandstone_windmill_weave_slab","mcwbridges:stone_brick_bridge","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","twigs:smooth_stone_brick_slab_from_smooth_stone_stonecutting","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","mcwroofs:cyan_concrete_attic_roof","mcwtrpdoors:oak_tropical_trapdoor","mcwfences:blackstone_railing_gate","minecraft:polished_granite_slab_from_granite_stonecutting","aether:golden_ring","mcwbiomesoplenty:empyreal_horse_fence","cfm:stripped_oak_bedside_cabinet","mcwroofs:white_concrete_attic_roof","securitycraft:reinforced_mossy_stone_bricks_from_vine","cfm:pink_kitchen_drawer","everythingcopper:copper_pickaxe","mcwroofs:blue_concrete_lower_roof","mcwwindows:blue_mosaic_glass","alltheores:lead_plate","additionallanterns:obsidian_chain","create:cut_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwbiomesoplenty:umbran_plank_window","mcwlights:cross_lantern","railcraft:receiver_circuit","mcwwindows:bricks_four_window","cfm:mangrove_kitchen_drawer","securitycraft:reinforced_mangrove_fence_gate","enderio:wood_gear_corner","dyenamics:bed/icy_blue_bed","allthecompressed:compress/granite_1x","immersiveengineering:crafting/connector_hv_relay","additionallanterns:diamond_chain","deepresonance:radiation_suit_leggings","supplementaries:stonecutting/stone_tile","twigs:cobblestone_brick_slab_from_cobblestone_stonecutting","dyenamics:conifer_concrete_powder","create:small_tuff_brick_stairs_from_stone_types_tuff_stonecutting","securitycraft:trophy_system","mcwwindows:birch_plank_window2","mcwbiomesoplenty:pine_curved_gate","immersiveengineering:crafting/coil_mv","cfm:green_trampoline","mcwdoors:oak_mystic_door","supplementaries:flags/flag_gray","mcwlights:yellow_paper_lamp","additionallanterns:diorite_chain","domum_ornamentum:cream_bricks","allthemodium:allthemodium_ingot_from_raw_blasting","mcwbiomesoplenty:palm_horse_fence","mcwroofs:green_concrete_lower_roof","mcwlights:warped_tiki_torch","forbidden_arcanus:arcane_chiseled_darkstone","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","mcwroofs:jungle_lower_roof","securitycraft:keycard_lv5","securitycraft:keycard_lv4","securitycraft:keycard_lv3","securitycraft:keycard_lv2","securitycraft:keycard_lv1","cfm:stripped_jungle_kitchen_drawer","dyenamics:wine_stained_glass","mcwpaths:cobbled_deepslate_crystal_floor_stairs","mcwpaths:sandstone_crystal_floor_slab","immersiveengineering:crafting/capacitor_mv","mcwfurnitures:stripped_oak_bookshelf_drawer","securitycraft:reinforced_light_blue_stained_glass_pane_from_dye","mcwwindows:jungle_plank_window2","minecraft:light_gray_concrete_powder","securitycraft:reinforced_purple_stained_glass_pane_from_glass","mcwbiomesoplenty:dead_stockade_fence","mcwpaths:andesite_clover_paving","minecraft:diorite_stairs","cfm:magenta_grill","immersiveengineering:crafting/capacitor_lv","mcwfences:stone_pillar_wall","securitycraft:door_indestructible_iron_item","cfm:jungle_table","mcwbiomesoplenty:empyreal_pyramid_gate","mcwwindows:cherry_pane_window","mcwdoors:garage_white_door","utilitix:armed_stand","botania:lexicon","mcwpaths:stone_running_bond","trashcans:liquid_trash_can","create:small_andesite_brick_slab_from_stone_types_andesite_stonecutting","mcwwindows:birch_plank_parapet","mcwroofs:gutter_middle_white","immersiveengineering:crafting/plate_gold_hammering","mcwfurnitures:jungle_lower_triple_drawer","rftoolsbase:infused_diamond","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","mcwroofs:oak_planks_top_roof","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","minecraft:cut_sandstone","enderio:fluid_tank","mcwroofs:oak_planks_roof","mcwpaths:stone_flagstone","bigreactors:energizer/casing","simplylight:illuminant_block_toggle","xnet:connector_blue","mcwbiomesoplenty:hellbark_highley_gate","mcwpaths:cobbled_deepslate_square_paving","minecraft:enchanting_table","cfm:birch_mail_box","minecraft:lead","farmersdelight:roast_chicken_block","immersiveengineering:crafting/capacitor_hv","immersiveengineering:crafting/coil_lv","deepresonance:radiation_suit_chestplate","mcwlights:brown_paper_lamp","minecraft:campfire","cfm:white_kitchen_sink","travelersbackpack:standard_no_tanks","securitycraft:reinforced_birch_fence","create:cut_tuff_bricks_from_stone_types_tuff_stonecutting","mcwbiomesoplenty:maple_highley_gate","farmersdelight:cooking/chicken_soup","mcwpaths:granite_dumble_paving","mcwpaths:granite_flagstone_slab","aether:skyroot_note_block","create:cut_tuff_from_stone_types_tuff_stonecutting","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","create:cut_granite_brick_slab_from_stone_types_granite_stonecutting","mcwroofs:granite_upper_steep_roof","mcwlights:bell_wall_lantern","minecraft:black_stained_glass"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:2925,warning_level:0}}