{AbsorptionAmount:0.0f,Air:300s,Attributes:[{Base:1.0d,Name:"minecraft:generic.attack_damage"},{Base:4.5d,Name:"forge:block_reach"},{Base:4.0d,Name:"minecraft:generic.attack_speed"},{Base:0.0d,Name:"voidscape:voidic_dmg"},{Base:0.08d,Name:"forge:entity_gravity"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.mana_regen"},{Base:1.0d,Name:"attributeslib:draw_speed"},{Base:0.0d,Name:"voidscape:voidic_res"},{Base:1.0d,Name:"forge:swim_speed"},{Base:0.0d,Name:"minecraft:generic.knockback_resistance"},{Base:0.0d,Name:"minecraft:generic.armor"},{Base:20.0d,Modifiers:[{Amount:0.0d,Name:"Health Gained from Trying New Foods",Operation:0,UUID:[I;-**********,221857896,-**********,-**********]}],Name:"minecraft:generic.max_health"},{Base:1.0d,Name:"ars_nouveau:ars_nouveau.perk.saturation"},{Base:0.0d,Name:"attributeslib:creative_flight"},{Base:0.1d,Name:"caelus:fall_flying"},{Base:3.0d,Name:"forge:entity_reach"},{Base:0.0d,Name:"ars_nouveau:ars_nouveau.perk.max_mana"},{Base:0.6d,Name:"forge:step_height_addition"},{Base:0.10000000149011612d,Name:"minecraft:generic.movement_speed"},{Base:1.0d,Name:"irons_spellbooks:spell_power"},{Base:1.0d,Name:"irons_spellbooks:cooldown_reduction"},{Base:0.0d,Name:"minecraft:generic.armor_toughness"}],BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["cca8d749-2936-4d9a-a535-4335d9016efe"]}},Brain:{memories:{}},Bukkit.updateLevel:2,CanUpdate:1b,DataVersion:3465,DeathTime:0s,Dimension:"minecraft:overworld",EnderItems:[],FallDistance:0.0f,FallFlying:0b,Fire:-20s,ForgeCaps:{"aether:aether_player":{CanGetPortal:0b,CanShowPatreonMessage:1b,CanSpawnInAether:0b,HasSeenSunSpirit:0b,LifeShardCount:0,LoginsUntilPatreonMessage:-1,RemedyStartDuration:0,SavedHealth:0.0f},"ars_nouveau:mana":{book_tier:0,current:100.0d,glyph:0,max:100},"ars_nouveau:player_data":{familiars:{size:0},glyphs:{size:0}},"blue_skies:player_capability":{ArcInventory:[{Count:1b,Slot:0b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:2b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},{Count:1b,Slot:3b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}}],EverbrightProgression:0b,EverdawnProgression:0b,FullDuskInvis:0b,KeystoneTeleportPos:{X:0,Y:0,Z:0},NatureHealth:0.0f,OpenedBlueLore:0b,SupporterPetAudible:0b,SupporterPetEnabled:0b,SupporterPetID:0b,SupporterStyleID:0b,UsedBlueLore:0b},"blueflame:blue_flame_on":{isOnFire:0b},"botania:kept_items":{stacks:[]},"cataclysm:charge_cap":{ChargeDamage:0.0f,ChargeTime:0,KnockbackSpeed:0.0f,dx:0.0f,dz:0.0f,isCharge:0b,timer:0},"cataclysm:hook_cap":{hasHook:0b},"cataclysm:parry_cap":{frame:0},"cataclysm:render_rush_cap":{damage:0.0f,isRush:0b,timer:0},"cataclysm:tentacle_cap":{getLastTentacleID:0,getLastTentacleUUID:[I;679150030,**********,-**********,**********],hasTentacle:0b},"comforts:sleep_data":{sleepTime:14654357L,tiredTime:0L,wakeTime:0L},"curios:inventory":{Curios:[{Identifier:"an_focus",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"belt",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"body",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"bracelet",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"bundle",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"charm",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"curio",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"head",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"necklace",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"ring",StacksHandler:{Cosmetics:{Items:[],Size:4},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1},{Render:1b,Slot:2},{Render:1b,Slot:3}],Size:4},SavedBaseSize:4,Stacks:{Items:[],Size:4},Visible:1b}},{Identifier:"back",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"hands",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:1b}},{Identifier:"grid",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"feet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"gtceu_magnet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"crafting_on_a_stick",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"adv_pattern_encoder",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"aether_accessory",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_cape",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_gloves",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_pendant",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"aether_ring",StacksHandler:{Cosmetics:{Items:[],Size:2},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0},{Render:1b,Slot:1}],Size:2},SavedBaseSize:2,Stacks:{Items:[],Size:2},Visible:0b}},{Identifier:"aether_shield",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:0b}},{Identifier:"angelring",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"deep_learner",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:0b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"heartamulet",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"living_armour_socket",StacksHandler:{Cosmetics:{Items:[],Size:0},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[],Size:0},SavedBaseSize:0,Stacks:{Items:[],Size:0},Visible:1b}},{Identifier:"magic_book",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}},{Identifier:"spellbook",StacksHandler:{Cosmetics:{Items:[],Size:1},DropRule:"DEFAULT",HasCosmetic:0b,RenderToggle:1b,Renders:{Renders:[{Render:1b,Slot:0}],Size:1},SavedBaseSize:1,Stacks:{Items:[],Size:1},Visible:1b}}]},"eidolon:knowledge":{facts:[],researches:[],runes:[],signs:[]},"eidolon:player_data":{dashTicks:0,flightStartTime:0L,isFlying:0b,lastFlapTime:0L,wingCharges:0},"eidolon:soul":{etherealHealth:0.0f,magic:0.0f,maxEtherealHealth:0.0f,maxMagic:0.0f},"fluxnetworks:flux_player":{superAdmin:0b,wirelessMode:0,wirelessNetwork:0},"forbidden_arcanus:aureal":{Aureal:0,Corruption:0,CorruptionTimer:0},"gtceu:medical_condition_tracker":{medical_conditions:[],permanent_conditions:[]},"hyperbox:return_point":{},"immersiveengineering:skyhook_data":0,"ironfurnaces:furnaces_list":{count:0,furnaces:{}},"ironfurnaces:show_config":{show:0},"irons_spellbooks:player_magic":{castingEquipmentSlot:"",castingSpellId:"",castingSpellLevel:0,effectFlags:0L,evasionHitsRemaining:0.0f,heartStopAccumulatedDamage:0.0f,isCasting:0b,mana:100,spellSelection:{index:-1,lastIndex:-1,lastSlot:"",slot:""}},"l2library:conditionals":{data:{},tickSinceDeath:12379},"lost_aether_content:player_capability":{InKingFight:0b,PhoenixRiseTime:0,WhaleFogTime:0},"lostcities:spawnset":{spawnSet:1b},"mahoutsukai:living_mahou":{MAHOUTSUKAI_AUTHORITY_HIT:0b,MAHOUTSUKAI_KODOKU:0},"mahoutsukai:mahou":{MAHOUTSUKAI_CANCEL_TIME:0L,MAHOUTSUKAI_FAE_SPAWN:1b,MAHOUTSUKAI_LAST_RECIPE_CLOTH:0b,MAHOUTSUKAI_MANA_FULL:1b,MAHOUTSUKAI_MANA_UP_COUNTER:100,MAHOUTSUKAI_PAGE:0,MAHOUTSUKAI_PLAYER_CHARGE_RATE:0.1d,MAHOUTSUKAI_PLAYER_DAMAGE_EXCHANGE_USES:0,MAHOUTSUKAI_PLAYER_DEATH_COLLECTION_USES_LEFT:0.0f,MAHOUTSUKAI_PLAYER_HAS_MAGIC:0b,MAHOUTSUKAI_PLAYER_MAX_MANA:100,MAHOUTSUKAI_PLAYER_POSSESSING:0b,MAHOUTSUKAI_PLAYER_PREV_PITCH:0.0f,MAHOUTSUKAI_PLAYER_PREV_YAW:0.0f,MAHOUTSUKAI_PLAYER_PROTECTIVE_DISPLACEMENT_USES:0,MAHOUTSUKAI_PLAYER_STORED_MANA:100,MAHOUTSUKAI_SHOW_MAHOU:1b},"mahoutsukai:mahou_settings":{intsettings:[{key:0,values:[I;255,94,204,255,94,204]},{key:1,values:[I;216,0,0,0,0,0]},{key:2,values:[I;255,255,150,0,0,0]},{key:3,values:[I;244,237,64,0,0,0]},{key:4,values:[I;255,50,50,0,0,0]},{key:5,values:[I;150,0,0,0,0,0]},{key:6,values:[I;224,64,0,0,0,0]},{key:7,values:[I;140,82,191,0,0,0]},{key:8,values:[I;166,125,224,0,0,0]},{key:9,values:[I;179,255,255,179,255,255]}]},"mcjtylib:preferences":{buffStyle:"botright",buffX:-20,buffY:-20,style:"flat gradient"},"mekanism:radiation":{radiation:1.0E-7d},"occultism:double_jump":{jumps:0},"occultism:familiar_settings":{bat_familiar:1b,beaver_familiar:1b,beholder_familiar:1b,blacksmith_familiar:1b,chimera_familiar:1b,cthulhu_familiar:1b,deer_familiar:1b,devil_familiar:1b,dragon_familiar:1b,fairy_familiar:1b,goat_familiar:1b,greedy_familiar:1b,guardian_familiar:1b,headless_familiar:1b,mummy_familiar:1b,otherworld_bird:1b,shub_niggurath_familiar:1b},"paraglider:paragliding_movement_handler":{panicParagliding:0b,panicParaglidingDelay:10,recoveryDelay:0,stamina:{depleted:0b,stamina:1000},vessels:{essences:0,heartContainers:0,staminaVessels:0}},"pneumaticcraft:hacking":{},"polymorph:player_recipe_data":{RecipeDataSet:[{Id:"minecraft:jungle_boat",ItemStack:{Count:1b,id:"minecraft:jungle_boat"}}],SelectedRecipe:"minecraft:jungle_boat"},"rftoolsutility:properties":{allowFlying:0b,buffTicks:42,buffTimeouts:[I;],buffs:[I;],destinations:[],oldAllowFlying:0b,onElevator:0b},"sgjourney:ancient_gene":{AncientGene:"INHERITED",FirstJoin:0b},"sgjourney:bloodstream_naquadah":{HasNaquadah:0b},"shrink:shrunk":{defaulteyeheight:0.0f,fixed:0b,height:0.0f,isshrinking:0b,isshrunk:0b,scale:1.0f,width:0.0f},"solcarrot:food":{foodList:["delightful:acorn"]},"structure_gel:gel_entity":{portal:"structure_gel:empty"},"sushigocrafting:weight_discovery":{},"tombstone:cap_protected_entity":{active:0b},"tombstone:cap_tombstone":{alignment:0s,knowledge:0,perks:[]},"toolbelt:belt_slot":{Items:[],Size:1},"travelersbackpack:travelers_backpack":{},"twilightforest:cap_feather_fan_fall":{featherFanFalling:0b},"twilightforest:cap_shield":{permshields:0,tempshields:0},"twilightforest:cap_thrown":{throwCooldown:0,throwX:0.0d,throwY:0.0d,throwZ:0.0d,yetiThrown:0b},"twilightforest:giant_pick_mine":{giantBlockConversion:0,giantPickBreaking:0b,giantPickMining:0L},"voidscape:data":{"voidscape:insanity":{inPortal:0b,infusion:0.0f,paranoia:0.0f,pleaseLeavePortal:0b,teleportTick:0}}},ForgeData:{PlayerPersisted:{BalmData:{TrashSlot:{Count:1b,id:"minecraft:air",tag:{Charged:0b,ISB_Spells:{data:[],maxSpells:15,mustEquip:1b,spellWheel:1b},decoration:"minecraft:red_x",display:{Name:'{"translate":"filled_map.buried_treasure"}'},maxSearchRadius:50,skinKnown:0b,zoomLevel:1}},WaystonesData:{Waystones:["cca8d749-2936-4d9a-a535-4335d9016efe","8736efdf-35ba-4252-bdd1-7c81e39a90da"]}},silentlib.SpawnItemsGiven:{},tb_cooldowns:[],tb_last_ground_location_dim:"minecraft:overworld",tb_last_ground_location_x:-120,tb_last_ground_location_y:65,tb_last_ground_location_z:-167,twilightforest_banished:1b},"quark:trying_crawl":0b,simplylight_unlocked:2},Health:20.0f,HurtByTimestamp:0,HurtTime:0s,Inventory:[{Count:1b,Slot:0b,id:"minecraft:stone_sword",tag:{Damage:0}},{Count:8b,Slot:1b,id:"minecraft:cooked_beef"},{Count:8b,Slot:2b,id:"minecraft:torch"},{Count:1b,Slot:100b,id:"minecraft:iron_boots",tag:{Damage:0,affix_data:{affixes:{"apotheosis:armor/attribute/steel_touched":0.34490967f,"apotheosis:armor/dmg_reduction/feathery":0.5304296f,"apotheosis:armor/mob_effect/nimble":0.71066624f,"apotheosis:durable":0.05f,"irons_spellbooks:armor/attribute/cooldown":0.53441757f,"irons_spellbooks:armor/attribute/spell_power":0.07750028f},name:'{"color":"#5555FF","translate":"misc.apotheosis.affix_name.three","with":[{"translate":"affix.irons_spellbooks:armor/attribute/spell_power"},"",{"translate":"affix.apotheosis:armor/attribute/steel_touched.suffix"}]}',rarity:"apotheosis:rare",sockets:2,uuids:[[I;-**********,**********,-**********,**********]]},apoth_rchest:1b}},{Count:1b,Slot:101b,id:"minecraft:iron_leggings",tag:{Damage:0}},{Count:1b,Slot:102b,id:"minecraft:leather_chestplate",tag:{Damage:0}},{Count:1b,Slot:103b,id:"minecraft:leather_helmet",tag:{Damage:0}}],Invulnerable:0b,Motion:[0.0d,-0.0784000015258789d,0.0d],OnGround:1b,PortalCooldown:0,Pos:[29.06392006440651d,73.0d,-1570.078856670727d],Railways_DataVersion:2,Rotation:[103.93872f,37.823017f],Score:690,SelectedItemSlot:6,SleepTimer:0s,SpawnAngle:-92.59244f,SpawnDimension:"minecraft:overworld",SpawnForced:0b,SpawnX:34,SpawnY:69,SpawnZ:-1570,Spigot.ticksLived:12378,UUID:[I;2106306351,-903852996,-1271914942,1562842769],"Void TotemIsFallDamageImmune":0b,"Void TotemLastSaveBlockPos":8246330773577L,WorldUUIDLeast:-4845340647225732833L,WorldUUIDMost:-5172224539240085156L,XpLevel:22,XpP:0.1527781f,XpSeed:1608870013,XpTotal:690,abilities:{flySpeed:0.05f,flying:0b,instabuild:0b,invulnerable:0b,mayBuild:1b,mayfly:0b,walkSpeed:0.1f},ad_astra_giselle_addon:{"ad_astra_giselle_addon:proof_duration":{"ad_astra_giselle_addon:acid_rain":0,"ad_astra_giselle_addon:gravity":0,"ad_astra_giselle_addon:hot_temperature":0,"ad_astra_giselle_addon:oxygen":0}},bukkit:{firstPlayed:1753508022176L,keepLevel:0b,lastKnownName:"Ying_Tao",lastPlayed:1753510791925L,newExp:0,newLevel:0,newTotalExp:0},foodExhaustionLevel:0.9164045f,foodLevel:17,foodSaturationLevel:0.0f,foodTickTimer:0,playerGameType:0,recipeBook:{isBlastingFurnaceFilteringCraftable:0b,isBlastingFurnaceGuiOpen:0b,isFilteringCraftable:0b,isFurnaceFilteringCraftable:0b,isFurnaceGuiOpen:0b,isGuiOpen:0b,isSmokerFilteringCraftable:0b,isSmokerGuiOpen:0b,isaltarFilteringCraftable:0b,isaltarGuiOpen:0b,iscookingFilteringCraftable:0b,iscookingGuiOpen:0b,isfreezerFilteringCraftable:0b,isfreezerGuiOpen:0b,isincubatorFilteringCraftable:0b,isincubatorGuiOpen:0b,recipes:["mcwfences:granite_railing_gate","minecraft:yellow_dye_from_dandelion","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","mcwwindows:birch_four_window","biomesoplenty:fir_boat","mcwbiomesoplenty:stripped_palm_pane_window","mcwbiomesoplenty:jacaranda_window2","handcrafted:spider_trophy","mcwtrpdoors:jungle_cottage_trapdoor","croptopia:shaped_beef_stew","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","mcwbiomesoplenty:palm_plank_window","mcwpaths:jungle_planks_path","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","minecraft:gold_nugget_from_blasting","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","minecraft:cooked_salmon_from_campfire_cooking","mcwbiomesoplenty:empyreal_hedge","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:oak_plank_pane_window","cfm:stripped_jungle_kitchen_counter","buildinggadgets2:gadget_cut_paste","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","minecraft:leather_chestplate","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","immersiveengineering:crafting/radiator","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","biomesoplenty:mahogany_chest_boat","minecraft:cooked_salmon_from_smoking","aether:skyroot_fletching_table","minecraft:leather_helmet","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","create:crafting/materials/sand_paper","energymeter:meter","mcwlights:covered_lantern","constructionwand:core_angel","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","mcwtrpdoors:jungle_blossom_trapdoor","minecraft:bamboo_chest_raft","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","mcwfences:prismarine_railing_gate","buildinggadgets2:template_manager","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","dyenamics:spring_green_wool","mcwfences:acacia_highley_gate","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","cfm:oak_kitchen_counter","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","biomesoplenty:mahogany_boat","aquaculture:note_hook","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwwindows:warped_planks_window2","utilitarian:utility/charcoal_from_campfire","utilitarian:utility/oak_logs_to_pressure_plates","biomesoplenty:palm_chest_boat","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","farmersdelight:cooking/pasta_with_meatballs","mcwwindows:birch_plank_window","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:angel_block","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","mcwbiomesoplenty:mahogany_window","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwlights:tavern_lantern","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","mcwfences:oak_pyramid_gate","mcwdoors:jungle_bark_glass_door","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwbiomesoplenty:umbran_plank_four_window","mcwwindows:jungle_four_window","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","dyenamics:aquamarine_wool","croptopia:hamburger","littlelogistics:barrel_barge","mcwfences:stone_grass_topped_wall","twilightforest:time_chest_boat","minecraft:brown_carpet","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:pine_plank_window2","minecraft:jungle_door","undergarden:wigglewood_chest_boat","handcrafted:brown_sheet","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","cfm:stripped_jungle_bedside_cabinet","travelersbackpack:cake","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","farmersdelight:barbecue_stick","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","mcwwindows:granite_pane_window","mcwbiomesoplenty:stripped_dead_pane_window","handcrafted:jungle_drawer","mcwwindows:dark_prismarine_window2","mcwwindows:quartz_window","supplementaries:present","cfm:stripped_jungle_cabinet","mcwroofs:oak_roof","cfm:jungle_desk","minecraft:mangrove_chest_boat","aether:crossbow_repairing","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","mcwtrpdoors:oak_barred_trapdoor","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","create:crafting/kinetics/fluid_tank","mcwfences:nether_brick_pillar_wall","mcwroofs:thatch_roof","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twilightforest:transformation_chest_boat","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","dyenamics:rose_wool","travelersbackpack:coal","mcwdoors:oak_four_panel_door","aether:leather_leggings_repairing","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","mcwwindows:stripped_cherry_log_window2","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:jungle_upper_steep_roof","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","cfm:stripped_jungle_chair","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","additionallanterns:andesite_lantern","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwwindows:andesite_four_window","mcwtrpdoors:jungle_swamp_trapdoor","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","supplementaries:feather_block","mcwbiomesoplenty:pine_stockade_fence","mcwroofs:thatch_top_roof","mcwfurnitures:oak_double_drawer_counter","mcwbiomesoplenty:redwood_pyramid_gate","mcwwindows:birch_plank_pane_window","travelersbackpack:brown_sleeping_bag","mcwfences:railing_prismarine_wall","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","croptopia:fruit_salad","minecraft:brown_banner","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","supplementaries:pancake_fd","dyenamics:navy_wool","mcwfences:end_brick_railing_gate","additionallanterns:iron_lantern","simplylight:illuminant_light_gray_block_toggle","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","croptopia:potato_chips","undergarden:wigglewood_boat","minecraft:book","mcwbiomesoplenty:fir_hedge","mcwfurnitures:oak_drawer_counter","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","delightful:food/baklava","mcwdoors:jungle_stable_door","mcwdoors:print_jungle","minecraft:red_dye_from_poppy","mcwbiomesoplenty:pine_window2","undergarden:gloom_o_lantern","mcwwindows:birch_window","mcwwindows:blackstone_four_window","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","twilightforest:twilight_oak_chest_boat","minecraft:cooked_cod_from_smoking","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbridges:jungle_log_bridge_middle","mcwfences:mangrove_wired_fence","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","farmersdelight:cooking/dumplings","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwlights:oak_tiki_torch","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwwindows:stripped_acacia_log_window","mcwwindows:stripped_dark_oak_log_window2","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","aether:stone_sword_repairing","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","mcwdoors:jungle_japanese2_door","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwbiomesoplenty:empyreal_window","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:stripped_hellbark_log_window","blue_skies:cake_compat","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwwindows:stripped_dark_oak_pane_window","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","cfm:oak_crate","mcwwindows:window_half_bar_base","mcwbiomesoplenty:willow_window","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:dead_picket_fence","mcwbiomesoplenty:hellbark_window2","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","mcwfences:bastion_metal_fence","mcwwindows:dark_oak_plank_window2","mcwfences:spruce_curved_gate","cfm:stripped_oak_chair","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:jungle_counter","cfm:diving_board","mcwdoors:jungle_swamp_door","minecraft:cooked_beef_from_campfire_cooking","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","mcwwindows:andesite_window2","mcwbiomesoplenty:magic_stockade_fence","farmersdelight:cooking/noodle_soup","delightful:food/cooking/nut_butter_bottle","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","mcwwindows:crimson_planks_four_window","minecraft:brown_bed","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","mcwbiomesoplenty:mahogany_plank_window2","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwfurnitures:oak_modern_wardrobe","mcwbiomesoplenty:willow_four_window","mcwfences:panelled_metal_fence_gate","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","croptopia:apple_sapling","farmersdelight:fried_egg_from_campfire_cooking","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwwindows:jungle_plank_four_window","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","minecraft:cooked_cod","minecraft:diamond_axe","create:crafting/kinetics/cyan_seat","minecraft:cartography_table","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","comforts:hammock_to_brown","additionallanterns:bricks_lantern","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","minecraft:cooked_porkchop_from_smoking","mcwroofs:oak_top_roof","mcwwindows:birch_plank_four_window","mcwfences:majestic_metal_fence_gate","rftoolsbuilder:vehicle_control_module","additionallanterns:amethyst_lantern","mcwwindows:stone_window","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","mcwfurnitures:jungle_stool_chair","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwfurnitures:stripped_oak_bookshelf","dankstorage:dank_1","mcwfences:crimson_picket_fence","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwfurnitures:jungle_chair","dyenamics:ultramarine_wool","mcwroofs:thatch_upper_steep_roof","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","sfm:water_tank","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","simplylight:illuminant_orange_block_dyed","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwwindows:oak_window2","mcwfences:bamboo_highley_gate","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","mcwfences:end_brick_grass_topped_wall","mcwdoors:oak_japanese2_door","mcwroofs:jungle_roof","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","mcwwindows:window_base","create:crafting/kinetics/green_seat","additionallanterns:obsidian_lantern","mcwwindows:mangrove_window","mcwwindows:quartz_pane_window","create:crafting/kinetics/black_seat","cfm:oak_desk","minecraft:map","mcwbiomesoplenty:magic_pyramid_gate","croptopia:carnitas","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","minecraft:cooked_porkchop_from_campfire_cooking","mcwbiomesoplenty:jacaranda_highley_gate","minecolonies:potato_soup","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","twilightforest:time_boat","mcwbiomesoplenty:stripped_mahogany_log_window","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwbiomesoplenty:empyreal_four_window","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","minecraft:iron_nugget_from_smelting","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","comforts:sleeping_bag_brown","mcwroofs:jungle_planks_steep_roof","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:magic_wired_fence","enderio:black_paper","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwwindows:stripped_acacia_log_four_window","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","nethersdelight:diamond_machete","mcwbiomesoplenty:stripped_willow_log_window","mcwfurnitures:jungle_large_drawer","mcwfences:diorite_grass_topped_wall","aquaculture:spruce_fish_mount","mcwwindows:stone_window2","mcwwindows:metal_four_window","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","mcwbiomesoplenty:maple_window","croptopia:fruit_smoothie","mcwbiomesoplenty:stripped_pine_pane_window","mcwroofs:jungle_planks_top_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","dyenamics:honey_wool","additionallanterns:diamond_lantern","minecolonies:apple_pie","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","comforts:hammock_brown","aquaculture:bobber","mcwwindows:quartz_window2","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","minecraft:item_frame","croptopia:nougat","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","mcwfurnitures:stripped_jungle_cupboard_counter","mcwtrpdoors:oak_bark_trapdoor","rftoolsbase:crafting_card","minecraft:arrow","supplementaries:turn_table","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","dyenamics:wine_wool","buildinggadgets2:gadget_copy_paste","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","aquaculture:oak_fish_mount","minecraft:diamond_boots","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwwindows:acacia_plank_four_window","create:crafting/kinetics/red_seat","mcwwindows:warped_planks_window","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","create:crafting/kinetics/orange_seat","mcwbiomesoplenty:stripped_maple_log_window","minecraft:diamond_block","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","travelersbackpack:horse","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_four_window","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","mcwfurnitures:jungle_end_table","mcwwindows:red_sandstone_pane_window","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","simplylight:illuminant_lime_block_on_dyed","farmersdelight:wheat_dough_from_eggs","minecraft:cooked_mutton","mcwbridges:jungle_bridge_pier","mcwbiomesoplenty:willow_pane_window","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","travelersbackpack:pig","undergarden:catalyst","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","croptopia:shaped_bacon","mcwbiomesoplenty:empyreal_plank_window2","minecraft:jukebox","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:stripped_fir_pane_window","mcwwindows:oak_window","mcwbiomesoplenty:snowblossom_hedge","mcwbiomesoplenty:stripped_magic_pane_window","mcwwindows:spruce_plank_pane_window","minecraft:oak_boat","sophisticatedstorage:jungle_chest","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwtrpdoors:oak_mystic_trapdoor","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","mcwfurnitures:stripped_jungle_large_drawer","mcwdoors:print_whispering","minecraft:jungle_fence","mcwroofs:oak_upper_steep_roof","mcwfences:mesh_metal_fence","utilitarian:utility/oak_logs_to_doors","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwbiomesoplenty:magic_plank_window2","farmersdelight:cooking/pasta_with_mutton_chop","mcwwindows:blackstone_pane_window","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","botania:speed_up_belt","dyenamics:bed/cherenkov_bed","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","occultism:crafting/brush","mcwwindows:prismarine_pane_window","mcwdoors:jungle_stable_head_door","croptopia:egg_roll","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:slice_map","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","mcwfences:end_brick_pillar_wall","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","mcwbiomesoplenty:stripped_redwood_log_window","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_jungle_coffee_table","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","pneumaticcraft:paper_from_tag_filter","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","cfm:brown_sofa","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","handcrafted:jungle_counter","mcwfences:warped_curved_gate","biomesoplenty:hellbark_chest_boat","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwdoors:oak_stable_door","oceansdelight:stuffed_cod","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","cfm:oak_upgraded_gate","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","delightful:smoking/roasted_acorn","handcrafted:salmon_trophy","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","cfm:oak_desk_cabinet","corail_woodcutter:birch_woodcutter","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","minecraft:charcoal","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","silentgear:emerald_shard","supplementaries:bellows","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","utilitarian:utility/jungle_logs_to_doors","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","mcwfurnitures:oak_end_table","croptopia:candied_nuts","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","minecraft:cooked_porkchop","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","create:crafting/schematics/empty_schematic","mcwlights:black_paper_lamp","simplylight:rodlamp","mcwwindows:stripped_acacia_log_window2","mcwwindows:dark_prismarine_pane_window","mcwfurnitures:oak_modern_desk","aether:leather_helmet_repairing","aquaculture:sushi","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","mcwfurnitures:jungle_table","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:maple_plank_window","mcwbiomesoplenty:jacaranda_pyramid_gate","delightful:campfire/roasted_acorn","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwbiomesoplenty:maple_four_window","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","create:crafting/kinetics/item_vault","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","deeperdarker:bloom_chest_boat","mcwwindows:granite_window2","mcwwindows:dark_oak_plank_window","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","utilitix:diamond_shears","supplementaries:clock_block","mcwwindows:warped_stem_window2","mcwwindows:diorite_pane_window","mcwbiomesoplenty:fir_stockade_fence","delightful:storage/acorn_storage_block","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","mcwbiomesoplenty:dead_plank_window","mcwfurnitures:oak_desk","mcwwindows:deepslate_window","aquaculture:tin_can_to_iron_nugget","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:cooked_beef_from_smoking","mcwbiomesoplenty:umbran_wired_fence","aether:leather_chestplate_repairing","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","minecraft:flint_and_steel","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","mcwbiomesoplenty:jacaranda_wired_fence","aether:leather_gloves","minecraft:spruce_chest_boat","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:maple_plank_window2","mcwwindows:dark_prismarine_window","mcwwindows:cherry_plank_window","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","travelersbackpack:hay","twilightforest:dark_boat","biomesoplenty:umbran_boat","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","mcwwindows:granite_four_window","mcwlights:wall_lantern","farmersdelight:cooking/pumpkin_soup","cfm:door_mat","create:crafting/kinetics/gray_seat","create:crafting/kinetics/brown_seat","mcwbiomesoplenty:willow_plank_window","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwwindows:metal_window","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","mcwbiomesoplenty:pine_pane_window","comforts:sleeping_bag_to_brown","supplementaries:bed_from_feather_block","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","utilitix:jungle_shulker_boat","mcwlights:magenta_paper_lamp","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","biomesoplenty:dead_chest_boat","mcwwindows:acacia_window","minecraft:cherry_boat","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","minecraft:leather_boots","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwwindows:dark_oak_pane_window","mcwlights:pink_paper_lamp","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","supplementaries:boat_jar","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","minecraft:baked_potato_from_smoking","aquaculture:fishing_line","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","mcwfences:railing_diorite_wall","dyenamics:conifer_wool","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","minecraft:cauldron","delightful:smelting/roasted_acorn","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","mcwroofs:jungle_planks_roof","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwfences:jungle_picket_fence","silentgear:blueprint_paper","mcwfurnitures:oak_kitchen_cabinet","aether:iron_boots_repairing","minecraft:diamond_shovel","mcwlights:dark_oak_tiki_torch","minecraft:hay_block","utilitarian:utility/oak_logs_to_trapdoors","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:umbran_window2","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwroofs:oak_upper_lower_roof","aquaculture:jungle_fish_mount","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","bigreactors:reactor/reinforced/passivefluidport_forge","mcwdoors:oak_waffle_door","biomesoplenty:dead_boat","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","twigs:paper_lantern","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","mcwwindows:andesite_window","aquaculture:diamond_fishing_rod","additionallanterns:basalt_lantern","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","mcwdoors:oak_glass_door","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwfences:sandstone_pillar_wall","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwwindows:acacia_plank_window2","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","immersiveengineering:crafting/blueprint_bullets","mcwlights:iron_framed_torch","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","croptopia:flour","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwwindows:crimson_planks_window","mcwfences:railing_mud_brick_wall","mcwfurnitures:stripped_oak_modern_chair","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwwindows:prismarine_four_window","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwfences:spruce_picket_fence","croptopia:roasted_smoking","mcwbiomesoplenty:hellbark_curved_gate","supplementaries:soap","mcwwindows:oak_plank_window2","immersiveengineering:crafting/blueprint_molds","travelersbackpack:emerald","mcwbiomesoplenty:pine_plank_window","croptopia:trail_mix","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwbiomesoplenty:willow_plank_window2","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","allthecompressed:compress/oak_log_1x","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","handcrafted:brown_cushion","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","croptopia:pork_jerky","simplylight:illuminant_light_gray_block_on_toggle","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","mcwwindows:stripped_dark_oak_log_window","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","minecraft:jungle_wood","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwwindows:diorite_four_window","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","mcwwindows:mangrove_plank_window2","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwwindows:mangrove_pane_window","minecraft:jungle_stairs","additionallanterns:smooth_stone_lantern","mcwbiomesoplenty:pine_plank_four_window","handcrafted:jungle_chair","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","cfm:jungle_kitchen_sink_dark","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","mcwwindows:crimson_stem_four_window","handcrafted:pufferfish_trophy","minecraft:fletching_table","minecraft:baked_potato","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","minecraft:cooked_salmon","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:oak_chest_boat","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:stripped_umbran_pane_window","twilightforest:mining_chest_boat","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","securitycraft:sc_manual","create:crafting/kinetics/yellow_seat","mcwwindows:spruce_plank_four_window","mcwbiomesoplenty:jacaranda_pane_window","corail_woodcutter:acacia_woodcutter","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","minecraft:mangrove_boat","minecraft:bread","productivetrees:crates/red_delicious_apple_crate_unpack","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwfurnitures:stripped_oak_cupboard_counter","mcwbiomesoplenty:dead_four_window","mcwwindows:stripped_warped_stem_four_window","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","create:crafting/schematics/schematic_and_quill","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","utilitarian:utility/jungle_logs_to_pressure_plates","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","mcwlights:soul_acacia_tiki_torch","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","minecraft:painting","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwtrpdoors:jungle_whispering_trapdoor","mcwfurnitures:oak_covered_desk","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","aether:ice_from_bucket_freezing","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwwindows:mangrove_plank_pane_window","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","mcwfences:quartz_grass_topped_wall","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","farmersdelight:grilled_salmon","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:magic_highley_gate","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","framedblocks:framed_cube","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","mcwwindows:stripped_mangrove_log_window2","utilitarian:utility/logs_to_bowls","biomesoplenty:fir_chest_boat","mcwwindows:stripped_cherry_log_four_window","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwfences:oak_curved_gate","dyenamics:lavender_wool","aquaculture:brown_mushroom_from_fish","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","croptopia:apple_juice","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","minecraft:jungle_slab","supplementaries:sconce","mcwwindows:stripped_oak_pane_window","twilightforest:dark_chest_boat","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","mcwbiomesoplenty:mahogany_four_window","aquaculture:gold_hook","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","mcwwindows:dark_oak_plank_pane_window","mcwbiomesoplenty:hellbark_stockade_fence","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:stripped_spruce_log_four_window","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","enderio:enderios","mcwfurnitures:oak_coffee_table","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","mcwbiomesoplenty:hellbark_plank_four_window","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","travelersbackpack:chicken","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwwindows:spruce_plank_window2","dyenamics:bubblegum_wool","mcwwindows:spruce_window","mcwwindows:prismarine_window2","mcwfences:andesite_grass_topped_wall","mcwbiomesoplenty:fir_plank_four_window","farmersdelight:cooking/apple_cider","forbidden_arcanus:wooden_blacksmith_gavel","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwwindows:acacia_four_window","mcwdoors:jungle_tropical_door","twigs:lamp","dyenamics:bed/lavender_bed","mcwbiomesoplenty:hellbark_window","mcwdoors:jungle_glass_door","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","mcwfurnitures:jungle_triple_drawer","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:jungle_lower_bookshelf_drawer","ad_astra:brown_flag","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","biomesoplenty:jacaranda_chest_boat","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","biomesoplenty:magic_boat","farmersdelight:fried_egg","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","simplylight:illuminant_orange_block_on_dyed","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","croptopia:pork_and_beans","additionallanterns:cobbled_deepslate_lantern","immersiveengineering:crafting/conveyor_basic","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","cfm:stripped_oak_bedside_cabinet","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","mcwroofs:thatch_attic_roof","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","farmersdelight:onion_crate","create:crafting/kinetics/purple_seat","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:magic_window2","mcwlights:cross_lantern","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","securitycraft:reinforced_packed_mud","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","additionallanterns:diamond_chain","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:cooked_mutton_from_campfire_cooking","mcwtrpdoors:jungle_mystic_trapdoor","mcwbiomesoplenty:fir_four_window","minecraft:cooked_beef","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","minecraft:cooked_mutton_from_smoking","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","cfm:stripped_jungle_kitchen_drawer","minecraft:gold_nugget_from_smelting","toolbelt:pouch","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","mcwwindows:jungle_plank_window2","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwfences:mangrove_pyramid_gate","mcwfences:stone_pillar_wall","mcwfences:spruce_stockade_fence","cfm:jungle_table","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","mcwwindows:cherry_pane_window","mcwbiomesoplenty:empyreal_pyramid_gate","simplylight:illuminant_green_block_toggle","mcwwindows:mangrove_window2","botania:lexicon","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwlights:green_paper_lamp","mcwbiomesoplenty:empyreal_curved_gate","dyenamics:maroon_wool","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","mcwwindows:granite_window","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","aether:leather_boots_repairing","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","mcwfences:modern_end_brick_wall","mcwbiomesoplenty:maple_highley_gate","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","mcwbiomesoplenty:stripped_umbran_log_four_window"],toBeDisplayed:["mcwfences:granite_railing_gate","minecraft:yellow_dye_from_dandelion","aether:packed_ice_freezing","mcwfurnitures:stripped_jungle_desk","mcwwindows:birch_four_window","biomesoplenty:fir_boat","mcwbiomesoplenty:stripped_palm_pane_window","mcwbiomesoplenty:jacaranda_window2","handcrafted:spider_trophy","mcwtrpdoors:jungle_cottage_trapdoor","croptopia:shaped_beef_stew","mcwbiomesoplenty:willow_window2","mcwbiomesoplenty:stripped_mahogany_log_four_window","mcwlights:chain_wall_lantern","mcwlights:bell_lantern","croptopia:burrito","simplylight:illuminant_magenta_block_on_dyed","cfm:jungle_coffee_table","mcwbiomesoplenty:palm_plank_window","mcwpaths:jungle_planks_path","mcwbiomesoplenty:dead_window2","create:crafting/appliances/crafting_blueprint","minecraft:gold_nugget_from_blasting","mcwbiomesoplenty:umbran_horse_fence","mcwfences:dark_oak_stockade_fence","mythicbotany:central_rune_holder","minecraft:cooked_salmon_from_campfire_cooking","mcwbiomesoplenty:empyreal_hedge","create:crafting/kinetics/light_gray_seat","utilitarian:utility/oak_logs_to_stairs","mcwbiomesoplenty:hellbark_picket_fence","supplementaries:flint_block","mcwdoors:jungle_nether_door","mcwwindows:oak_plank_pane_window","cfm:stripped_jungle_kitchen_counter","buildinggadgets2:gadget_cut_paste","mcwfences:acacia_wired_fence","utilitarian:no_soliciting/soliciting_carpets/light_gray_soliciting_carpet","minecraft:leather_chestplate","simplylight:illuminant_light_blue_block_dyed","mcwtrpdoors:oak_ranch_trapdoor","immersiveengineering:crafting/radiator","mcwlights:crimson_tiki_torch","supplementaries:flags/flag_brown","biomesoplenty:mahogany_chest_boat","minecraft:cooked_salmon_from_smoking","aether:skyroot_fletching_table","minecraft:leather_helmet","mcwfurnitures:stripped_jungle_double_wardrobe","mcwlights:soul_mangrove_tiki_torch","create:crafting/materials/sand_paper","energymeter:meter","mcwlights:covered_lantern","constructionwand:core_angel","mcwwindows:stripped_oak_log_four_window","mcwfurnitures:stripped_jungle_bookshelf","mcwwindows:stripped_birch_log_window2","mcwfurnitures:stripped_jungle_counter","aquaculture:iron_nugget_from_smelting","mcwtrpdoors:jungle_blossom_trapdoor","minecraft:bamboo_chest_raft","mcwbiomesoplenty:mahogany_plank_pane_window","aquaculture:birch_fish_mount","mcwwindows:stripped_mangrove_log_window","mcwfences:prismarine_railing_gate","buildinggadgets2:template_manager","mcwwindows:jungle_window","mcwbiomesoplenty:willow_highley_gate","mcwbiomesoplenty:mahogany_curved_gate","simplylight:illuminant_red_block_toggle","mcwwindows:metal_window2","aether:blue_ice_freezing","mcwwindows:stripped_crimson_stem_window","handcrafted:jungle_cupboard","mcwwindows:acacia_curtain_rod","mcwbiomesoplenty:pine_highley_gate","mcwlights:chain_lantern","mcwfences:ornate_metal_fence","dyenamics:spring_green_wool","mcwfences:acacia_highley_gate","corail_woodcutter:dark_oak_woodcutter","mcwbiomesoplenty:umbran_curved_gate","travelersbackpack:diamond_tier_upgrade","mcwwindows:bricks_pane_window","mcwfences:modern_nether_brick_wall","ae2wtlib:wireless_universal_terminal/upgrade_crafting","mcwfurnitures:oak_cupboard_counter","mcwfences:sandstone_railing_gate","cfm:stripped_oak_cabinet","mcwfences:dark_oak_pyramid_gate","cfm:oak_kitchen_counter","dyenamics:cherenkov_wool","cfm:jungle_kitchen_sink_light","additionallanterns:prismarine_lantern","mcwbiomesoplenty:stripped_fir_log_four_window","aquaculture:gold_nugget_from_smelting","cfm:jungle_kitchen_drawer","biomesoplenty:mahogany_boat","aquaculture:note_hook","solcarrot:food_book","additionallanterns:stone_bricks_lantern","mcwwindows:warped_planks_window2","utilitarian:utility/charcoal_from_campfire","utilitarian:utility/oak_logs_to_pressure_plates","biomesoplenty:palm_chest_boat","simplylight:illuminant_cyan_block_on_dyed","mcwfences:vintage_metal_fence","aquaculture:double_hook","mcwbiomesoplenty:magic_window","mcwlights:lime_paper_lamp","mcwbiomesoplenty:willow_stockade_fence","mcwfurnitures:oak_striped_chair","farmersdelight:cooking/pasta_with_meatballs","mcwwindows:birch_plank_window","mcwfences:nether_brick_grass_topped_wall","minecraft:jungle_fence_gate","mcwbiomesoplenty:stripped_dead_log_window2","utilitarian:angel_block","mcwbiomesoplenty:pine_four_window","mcwbiomesoplenty:stripped_pine_log_window2","simplylight:illuminant_orange_block_on_toggle","mcwbiomesoplenty:mahogany_window","cfm:jungle_cabinet","simplylight:illuminant_light_blue_block_toggle","mcwlights:tavern_lantern","toolbelt:belt","simplylight:edge_light_bottom_from_top","mcwroofs:oak_attic_roof","dyenamics:bed/navy_bed","potionblender:brewing_cauldron","cfm:oak_blinds","mcwtrpdoors:jungle_four_panel_trapdoor","mcwbiomesoplenty:palm_window","mcwfences:oak_pyramid_gate","mcwdoors:jungle_bark_glass_door","mcwfurnitures:stripped_oak_drawer_counter","mcwfences:deepslate_railing_gate","mcwwindows:red_sandstone_window","mcwwindows:mangrove_four_window","mcwbiomesoplenty:umbran_plank_four_window","mcwwindows:jungle_four_window","handcrafted:jungle_side_table","mcwfences:deepslate_pillar_wall","mcwfurnitures:jungle_double_wardrobe","cfm:jungle_chair","mcwbiomesoplenty:empyreal_wired_fence","mcwlights:birch_tiki_torch","sophisticatedbackpacks:upgrade_base","handcrafted:jungle_bench","dyenamics:aquamarine_wool","croptopia:hamburger","littlelogistics:barrel_barge","mcwfences:stone_grass_topped_wall","twilightforest:time_chest_boat","minecraft:brown_carpet","mcwbiomesoplenty:empyreal_plank_four_window","mcwbiomesoplenty:hellbark_four_window","mcwbiomesoplenty:pine_plank_window2","minecraft:jungle_door","undergarden:wigglewood_chest_boat","handcrafted:brown_sheet","mcwbiomesoplenty:palm_plank_window2","aether:iron_shovel_repairing","cfm:stripped_jungle_bedside_cabinet","travelersbackpack:cake","additionallanterns:normal_nether_bricks_lantern","dyenamics:bed/mint_bed","farmersdelight:barbecue_stick","mcwbiomesoplenty:jacaranda_horse_fence","utilitix:directional_highspeed_rail","mcwwindows:oak_four_window","cfm:oak_coffee_table","mcwfurnitures:stripped_oak_counter","mcwbiomesoplenty:magic_picket_fence","mcwwindows:granite_pane_window","mcwbiomesoplenty:stripped_dead_pane_window","handcrafted:jungle_drawer","mcwwindows:dark_prismarine_window2","mcwwindows:quartz_window","supplementaries:present","cfm:stripped_jungle_cabinet","mcwroofs:oak_roof","cfm:jungle_desk","minecraft:mangrove_chest_boat","aether:crossbow_repairing","mcwbiomesoplenty:mahogany_horse_fence","create:crafting/kinetics/light_blue_seat","mcwtrpdoors:oak_barred_trapdoor","minecraft:jungle_planks","mcwfurnitures:jungle_drawer","create:crafting/kinetics/fluid_tank","mcwfences:nether_brick_pillar_wall","mcwroofs:thatch_roof","mcwwindows:warped_curtain_rod","mcwbiomesoplenty:willow_curved_gate","biomesoplenty:empyreal_boat","mcwbridges:rope_jungle_bridge","mcwlights:soul_birch_tiki_torch","simplylight:illuminant_blue_block_dyed","domum_ornamentum:wheat_extra","mcwfences:oak_wired_fence","minecraft:jungle_trapdoor","twilightforest:transformation_chest_boat","dyenamics:fluorescent_wool","additionallanterns:end_stone_lantern","mcwfurnitures:stripped_oak_double_drawer_counter","silentgear:diamond_shard","dyenamics:rose_wool","travelersbackpack:coal","mcwdoors:oak_four_panel_door","aether:leather_leggings_repairing","dyenamics:bed/conifer_bed","mcwfences:blackstone_brick_railing_gate","aquaculture:bonemeal_from_fish_bones","dyenamics:bed/amber_bed","mcwwindows:birch_window2","mcwdoors:oak_modern_door","minecraft:diamond_leggings","mcwbiomesoplenty:maple_curved_gate","mcwlights:blue_paper_lamp","mcwwindows:stripped_cherry_log_window2","mcwfurnitures:stripped_oak_covered_desk","minecraft:bamboo_raft","mcwfences:diorite_railing_gate","mcwfurnitures:stripped_jungle_chair","mcwroofs:jungle_upper_steep_roof","mcwbiomesoplenty:mahogany_hedge","mcwfences:railing_andesite_wall","cfm:stripped_jungle_chair","handcrafted:jungle_corner_trim","simplylight:illuminant_gray_block_on_dyed","mcwwindows:sandstone_window2","biomesoplenty:empyreal_chest_boat","additionallanterns:andesite_lantern","mcwwindows:spruce_window2","forbidden_arcanus:diamond_blacksmith_gavel","mcwwindows:andesite_four_window","mcwtrpdoors:jungle_swamp_trapdoor","mcwwindows:blackstone_window","simplylight:illuminant_light_gray_block_on_dyed","supplementaries:feather_block","mcwbiomesoplenty:pine_stockade_fence","mcwroofs:thatch_top_roof","mcwfurnitures:oak_double_drawer_counter","mcwbiomesoplenty:redwood_pyramid_gate","mcwwindows:birch_plank_pane_window","travelersbackpack:brown_sleeping_bag","mcwfences:railing_prismarine_wall","minecraft:leather_leggings","mcwbiomesoplenty:hellbark_wired_fence","cfm:stripped_jungle_table","biomesoplenty:redwood_boat","mcwfences:mesh_metal_fence_gate","mcwlights:festive_lantern","mcwbiomesoplenty:fir_pyramid_gate","croptopia:fruit_salad","minecraft:brown_banner","mcwfurnitures:jungle_wardrobe","mcwlights:tavern_wall_lantern","dyenamics:bed/rose_bed","supplementaries:pancake_fd","dyenamics:navy_wool","mcwfences:end_brick_railing_gate","additionallanterns:iron_lantern","simplylight:illuminant_light_gray_block_toggle","mcwbiomesoplenty:maple_window2","mcwfences:railing_end_brick_wall","mcwfences:jungle_pyramid_gate","mcwbridges:bridge_torch","utilitarian:utility/oak_logs_to_boats","mcwfurnitures:stripped_oak_lower_triple_drawer","croptopia:potato_chips","undergarden:wigglewood_boat","minecraft:book","mcwbiomesoplenty:fir_hedge","mcwfurnitures:oak_drawer_counter","handcrafted:jungle_pillar_trim","mcwfences:birch_highley_gate","createoreexcavation:vein_atlas","mcwfences:birch_wired_fence","twilightforest:sorting_chest_boat","mcwwindows:birch_curtain_rod","aether:skyroot_cartography_table","minecolonies:mutton_dinner","mcwdoors:oak_cottage_door","utilitarian:no_soliciting/soliciting_carpets/gray_soliciting_carpet","immersiveengineering:crafting/torch","mcwfences:warped_pyramid_gate","delightful:food/baklava","mcwdoors:jungle_stable_door","mcwdoors:print_jungle","minecraft:red_dye_from_poppy","mcwbiomesoplenty:pine_window2","undergarden:gloom_o_lantern","mcwwindows:birch_window","mcwwindows:blackstone_four_window","mcwfences:acacia_picket_fence","mcwlights:cherry_tiki_torch","mcwfences:mud_brick_railing_gate","simplylight:illuminant_purple_block_toggle","mcwbiomesoplenty:stripped_dead_log_window","mcwwindows:acacia_plank_window","twilightforest:twilight_oak_chest_boat","minecraft:cooked_cod_from_smoking","mcwbiomesoplenty:dead_plank_pane_window","mcwfurnitures:oak_double_drawer","mcwbiomesoplenty:stripped_palm_log_window2","mcwfences:railing_blackstone_wall","mcwfurnitures:oak_lower_triple_drawer","travelersbackpack:ocelot","mcwlights:soul_double_street_lamp","mcwbiomesoplenty:willow_picket_fence","mcwbridges:jungle_log_bridge_middle","mcwfences:mangrove_wired_fence","mcwwindows:dark_oak_plank_four_window","additionallanterns:emerald_chain","mcwfences:modern_sandstone_wall","minecraft:diamond_sword","mcwfurnitures:oak_wardrobe","mcwroofs:thatch_lower_roof","mcwtrpdoors:jungle_paper_trapdoor","mcwdoors:jungle_japanese_door","mcwbiomesoplenty:dead_curved_gate","mcwbiomesoplenty:willow_horse_fence","mcwfences:red_sandstone_pillar_wall","additionallanterns:mossy_cobblestone_lantern","farmersdelight:cooking/dumplings","cfm:jungle_park_bench","mcwlights:upgraded_torch","mcwbiomesoplenty:magic_curved_gate","mcwlights:oak_tiki_torch","mcwbiomesoplenty:fir_plank_window","dyenamics:bed/bubblegum_bed","mcwlights:soul_cherry_tiki_torch","mcwbiomesoplenty:stripped_mahogany_pane_window","mcwwindows:stripped_acacia_log_window","mcwwindows:stripped_dark_oak_log_window2","mcwroofs:jungle_top_roof","simplylight:illuminant_lime_block_dyed","mcwwindows:dark_oak_window","mcwfences:red_sandstone_railing_gate","mcwbiomesoplenty:jacaranda_curved_gate","mcwfences:dark_oak_wired_fence","mcwfences:prismarine_pillar_wall","aether:stone_sword_repairing","mcwwindows:hammer","handcrafted:fox_trophy","cfm:jungle_kitchen_counter","mcwfurnitures:stripped_jungle_drawer_counter","farmersdelight:diamond_knife","mcwdoors:jungle_japanese2_door","mcwwindows:stone_pane_window","domum_ornamentum:white_paper_extra","mcwbiomesoplenty:fir_window2","mcwbiomesoplenty:maple_horse_fence","allthecompressed:compress/jungle_log_1x","handcrafted:blaze_trophy","mcwwindows:red_sandstone_four_window","mcwbiomesoplenty:fir_plank_window2","additionallanterns:emerald_lantern","mcwbiomesoplenty:empyreal_window","mcwwindows:golden_curtain_rod","mcwbiomesoplenty:red_maple_hedge","mcwbiomesoplenty:stripped_hellbark_log_window","blue_skies:cake_compat","mcwlights:reinforced_torch","mcwbiomesoplenty:maple_pyramid_gate","mcwfurnitures:oak_double_wardrobe","minecraft:iron_nugget_from_blasting","mcwbiomesoplenty:stripped_mahogany_log_window2","mcwwindows:stripped_dark_oak_pane_window","croptopia:apple_pie","bigreactors:turbine/basic/passivefluidport_forge","twilightforest:mining_boat","mcwtrpdoors:jungle_bark_trapdoor","aether:golden_dart","utilitarian:no_soliciting/soliciting_carpets/blue_soliciting_carpet","cfm:oak_crate","mcwwindows:window_half_bar_base","mcwbiomesoplenty:willow_window","mcwfurnitures:oak_modern_chair","mcwwindows:oak_curtain_rod","deeperdarker:bloom_boat","mcwbiomesoplenty:dead_picket_fence","mcwbiomesoplenty:hellbark_window2","mcwbiomesoplenty:magic_hedge","biomesoplenty:maple_chest_boat","mcwfences:bastion_metal_fence","mcwwindows:dark_oak_plank_window2","mcwfences:spruce_curved_gate","cfm:stripped_oak_chair","mcwwindows:stripped_cherry_pane_window","mcwfurnitures:jungle_counter","cfm:diving_board","mcwdoors:jungle_swamp_door","minecraft:cooked_beef_from_campfire_cooking","mcwbiomesoplenty:magic_plank_pane_window","createoreexcavation:diamond_drill","mcwdoors:oak_paper_door","mcwbiomesoplenty:maple_picket_fence","mcwwindows:warped_planks_four_window","create:crafting/materials/red_sand_paper","mcwwindows:mangrove_curtain_rod","mcwwindows:stripped_crimson_stem_window2","sophisticatedstorage:spruce_barrel_from_vanilla_barrel","mcwbiomesoplenty:stripped_empyreal_log_four_window","croptopia:meringue","mcwfences:spruce_pyramid_gate","aquaculture:worm_farm","mcwbiomesoplenty:hellbark_pyramid_gate","handcrafted:jungle_couch","mcwfences:deepslate_brick_grass_topped_wall","mcwwindows:andesite_window2","mcwbiomesoplenty:magic_stockade_fence","farmersdelight:cooking/noodle_soup","delightful:food/cooking/nut_butter_bottle","simplylight:illuminant_light_gray_block_dyed","mcwbiomesoplenty:magic_horse_fence","mcwwindows:crimson_planks_four_window","minecraft:brown_bed","mcwfurnitures:jungle_coffee_table","mcwlights:soul_dark_oak_tiki_torch","naturescompass:natures_compass","mcwbiomesoplenty:dead_plank_four_window","mcwbiomesoplenty:umbran_plank_pane_window","mcwwindows:diorite_window","mcwbiomesoplenty:dead_pyramid_gate","mcwbiomesoplenty:dead_horse_fence","simplylight:illuminant_yellow_block_on_dyed","mcwwindows:spruce_pane_window","mcwbiomesoplenty:mahogany_plank_window2","cfm:stripped_oak_desk","mcwtrpdoors:oak_glass_trapdoor","mcwfurnitures:oak_modern_wardrobe","mcwbiomesoplenty:willow_four_window","mcwfences:panelled_metal_fence_gate","railcraft:villager_detector","supplementaries:sign_post_jungle","mcwwindows:metal_curtain_rod","mcwdoors:oak_western_door","farmersdelight:cooking/ratatouille","mcwfences:mangrove_picket_fence","croptopia:apple_sapling","farmersdelight:fried_egg_from_campfire_cooking","mcwlights:cross_wall_lantern","aquaculture:planks_from_driftwood","mcwroofs:oak_steep_roof","mcwfences:acorn_metal_fence","mcwfences:birch_stockade_fence","mcwfurnitures:oak_drawer","simplylight:lamp_post","simplylight:illuminant_light_blue_block_on_dyed","mcwfences:spruce_horse_fence","croptopia:beef_jerky","mcwwindows:jungle_plank_four_window","mcwfences:crimson_curved_gate","additionallanterns:red_sandstone_lantern","corail_woodcutter:mangrove_woodcutter","mcwbiomesoplenty:stripped_magic_log_four_window","handcrafted:jungle_desk","mcwwindows:cherry_plank_four_window","corail_woodcutter:bamboo_woodcutter","additionallanterns:bone_lantern","cfm:oak_kitchen_drawer","minecraft:cooked_cod","minecraft:diamond_axe","create:crafting/kinetics/cyan_seat","minecraft:cartography_table","mcwfurnitures:oak_counter","undergarden:grongle_chest_boat","mcwbiomesoplenty:umbran_pane_window","mcwbiomesoplenty:fir_horse_fence","corail_woodcutter:spruce_woodcutter","comforts:hammock_to_brown","additionallanterns:bricks_lantern","travelersbackpack:skeleton","mcwbiomesoplenty:palm_four_window","mcwlights:red_paper_lamp","mcwfences:andesite_pillar_wall","mcwlights:covered_wall_lantern","minecraft:cooked_porkchop_from_smoking","mcwroofs:oak_top_roof","mcwwindows:birch_plank_four_window","mcwfences:majestic_metal_fence_gate","rftoolsbuilder:vehicle_control_module","additionallanterns:amethyst_lantern","mcwwindows:stone_window","mcwfences:granite_grass_topped_wall","mcwfences:modern_deepslate_brick_wall","mcwbiomesoplenty:stripped_fir_log_window","biomesoplenty:magic_chest_boat","mcwfurnitures:stripped_jungle_wardrobe","mcwbiomesoplenty:rainbow_birch_hedge","mcwbiomesoplenty:stripped_umbran_log_window","mcwtrpdoors:jungle_classic_trapdoor","mcwfurnitures:stripped_oak_modern_wardrobe","mcwfurnitures:jungle_stool_chair","mcwfences:railing_nether_brick_wall","travelersbackpack:diamond","mcwfurnitures:stripped_oak_bookshelf","dankstorage:dank_1","mcwfences:crimson_picket_fence","mcwwindows:warped_stem_window","mcwbiomesoplenty:fir_curved_gate","mcwfurnitures:jungle_chair","dyenamics:ultramarine_wool","mcwroofs:thatch_upper_steep_roof","cfm:stripped_oak_crate","additionallanterns:gold_lantern","mcwdoors:oak_bark_glass_door","sfm:water_tank","mcwbiomesoplenty:stripped_willow_log_four_window","mcwfences:crimson_wired_fence","mcwfences:oak_stockade_fence","immersiveengineering:crafting/blueprint_components","simplylight:illuminant_orange_block_dyed","mcwfences:dark_oak_picket_fence","cfm:jungle_upgraded_fence","croptopia:shaped_water_bottle","simplylight:illuminant_pink_block_toggle","mcwtrpdoors:jungle_ranch_trapdoor","farmersdelight:cooking/fried_rice","dyenamics:bed/maroon_bed","mcwfences:dark_oak_hedge","mcwfences:warped_picket_fence","mcwbiomesoplenty:pine_picket_fence","mcwwindows:oak_window2","mcwfences:bamboo_highley_gate","mcwlights:light_gray_paper_lamp","mcwfences:mangrove_horse_fence","create:crafting/kinetics/lime_seat","mcwfences:end_brick_grass_topped_wall","mcwdoors:oak_japanese2_door","mcwroofs:jungle_roof","simplylight:illuminant_black_block_dyed","mcwfurnitures:stripped_jungle_stool_chair","simplylight:illuminant_gray_block_on_toggle","mcwbiomesoplenty:fir_plank_pane_window","mcwfences:jungle_curved_gate","utilitarian:utility/oak_logs_to_slabs","mcwlights:striped_wall_lantern","mcwwindows:window_base","create:crafting/kinetics/green_seat","additionallanterns:obsidian_lantern","mcwwindows:mangrove_window","mcwwindows:quartz_pane_window","create:crafting/kinetics/black_seat","cfm:oak_desk","minecraft:map","mcwbiomesoplenty:magic_pyramid_gate","croptopia:carnitas","mcwbiomesoplenty:hellbark_hedge","mcwfurnitures:jungle_double_drawer_counter","cfm:jungle_bedside_cabinet","sophisticatedstorage:generic_barrel","mcwwindows:oak_pane_window","minecraft:cooked_porkchop_from_campfire_cooking","mcwbiomesoplenty:jacaranda_highley_gate","minecolonies:potato_soup","mcwwindows:stripped_cherry_log_window","aquaculture:iron_fishing_rod","mcwfurnitures:jungle_double_drawer","mcwbiomesoplenty:redwood_picket_fence","minecraft:crafting_table","twilightforest:time_boat","mcwbiomesoplenty:stripped_mahogany_log_window","mcwbiomesoplenty:mahogany_pyramid_gate","utilitarian:no_soliciting/soliciting_carpets/yellow_soliciting_carpet","mcwbiomesoplenty:empyreal_four_window","mcwfences:spruce_wired_fence","mcwfurnitures:stripped_oak_large_drawer","minecraft:iron_nugget_from_smelting","aquaculture:acacia_fish_mount","mcwbiomesoplenty:umbran_hedge","mcwfences:crimson_horse_fence","mcwbiomesoplenty:mahogany_stockade_fence","aquaculture:iron_nugget_from_blasting","mcwfences:modern_prismarine_wall","delightful:food/cooking/jam_jar","comforts:sleeping_bag_brown","mcwroofs:jungle_planks_steep_roof","mcwfences:double_curved_metal_fence","mcwwindows:oak_plank_four_window","handcrafted:jungle_dining_bench","railcraft:golden_ticket","mcwbiomesoplenty:jacaranda_window","mcwwindows:stripped_birch_log_window","mcwbiomesoplenty:magic_wired_fence","enderio:black_paper","supplementaries:lock_block","mcwfences:nether_brick_railing_gate","aether:skyroot_jukebox","mcwfurnitures:oak_table","simplylight:illuminant_brown_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/black_soliciting_carpet","mcwwindows:stripped_acacia_log_four_window","undergarden:smogstem_chest_boat","biomesoplenty:redwood_chest_boat","simplylight:illuminant_pink_block_on_dyed","undergarden:grongle_boat","cfm:stripped_oak_kitchen_drawer","mcwfences:oak_picket_fence","mcwbiomesoplenty:stripped_magic_log_window2","mcwwindows:prismarine_window","mcwwindows:cherry_plank_window2","mcwfences:mangrove_curved_gate","mcwfences:modern_andesite_wall","mcwwindows:spruce_curtain_rod","mcwdoors:jungle_barn_glass_door","mcwdoors:jungle_western_door","mcwwindows:metal_pane_window","mcwbiomesoplenty:empyreal_plank_window","nethersdelight:diamond_machete","mcwbiomesoplenty:stripped_willow_log_window","mcwfurnitures:jungle_large_drawer","mcwfences:diorite_grass_topped_wall","aquaculture:spruce_fish_mount","mcwwindows:stone_window2","mcwwindows:metal_four_window","simplylight:illuminant_purple_block_on_dyed","mcwbiomesoplenty:umbran_window","mcwbiomesoplenty:maple_window","croptopia:fruit_smoothie","mcwbiomesoplenty:stripped_pine_pane_window","mcwroofs:jungle_planks_top_roof","simplylight:illuminant_yellow_block_on_toggle","mcwfurnitures:jungle_striped_chair","mcwbiomesoplenty:redwood_pane_window","dyenamics:honey_wool","additionallanterns:diamond_lantern","minecolonies:apple_pie","simplylight:illuminant_green_block_dyed","minecraft:diamond_helmet","comforts:hammock_brown","aquaculture:bobber","mcwwindows:quartz_window2","simplylight:edge_light_top","mcwfurnitures:stripped_oak_coffee_table","mcwfences:granite_pillar_wall","handcrafted:tropical_fish_trophy","mcwfurnitures:stripped_jungle_modern_wardrobe","simplylight:illuminant_brown_block_dyed","mcwbiomesoplenty:jacaranda_plank_pane_window","simplylight:illuminant_brown_block_on_dyed","deeperdarker:echo_chest_boat","aether:aether_saddle","farmersdelight:wheat_dough_from_water","mcwbiomesoplenty:redwood_hedge","dyenamics:bed/aquamarine_bed","minecraft:item_frame","croptopia:nougat","ae2wtlib:quantum_bridge_card","dyenamics:bed/ultramarine_bed","create:crafting/kinetics/pink_seat","mcwfurnitures:stripped_jungle_cupboard_counter","mcwtrpdoors:oak_bark_trapdoor","rftoolsbase:crafting_card","minecraft:arrow","supplementaries:turn_table","mcwlights:soul_bamboo_tiki_torch","twilightforest:sorting_boat","mcwwindows:stripped_spruce_log_window","mcwfurnitures:stripped_jungle_table","dyenamics:wine_wool","buildinggadgets2:gadget_copy_paste","utilitarian:no_soliciting/soliciting_carpets/lime_soliciting_carpet","mcwfences:modern_stone_brick_wall","mcwbiomesoplenty:origin_hedge","mcwbiomesoplenty:redwood_curved_gate","mcwbiomesoplenty:umbran_pyramid_gate","mcwlights:soul_spruce_tiki_torch","simplylight:illuminant_pink_block_on_toggle","mcwwindows:stone_four_window","simplylight:illuminant_panel","mcwbiomesoplenty:cypress_hedge","mcwbiomesoplenty:empyreal_stockade_fence","additionallanterns:deepslate_bricks_lantern","aquaculture:oak_fish_mount","minecraft:diamond_boots","mcwbiomesoplenty:redwood_wired_fence","undergarden:smogstem_boat","mcwfurnitures:stripped_oak_triple_drawer","mcwwindows:mangrove_plank_four_window","minecraft:stick","mcwfences:red_sandstone_grass_topped_wall","cfm:jungle_crate","forbidden_arcanus:deorum_lantern","mcwwindows:acacia_plank_four_window","create:crafting/kinetics/red_seat","mcwwindows:warped_planks_window","mcwdoors:jungle_paper_door","dyenamics:bed/persimmon_bed","mcwroofs:jungle_upper_lower_roof","rftoolsbuilder:vehicle_status_module","mcwbiomesoplenty:stripped_palm_log_four_window","mcwfences:warped_wired_fence","sophisticatedstorage:generic_chest","minecraft:jungle_button","mcwtrpdoors:oak_cottage_trapdoor","additionallanterns:diorite_lantern","mcwbiomesoplenty:pine_window","create:crafting/kinetics/orange_seat","mcwbiomesoplenty:stripped_maple_log_window","minecraft:diamond_block","bigreactors:turbine/reinforced/passivefluidport_forge","cfm:oak_chair","travelersbackpack:horse","biomesoplenty:willow_chest_boat","simplylight:illuminant_pink_block_dyed","mcwfences:diorite_pillar_wall","mcwroofs:jungle_planks_upper_steep_roof","mcwbiomesoplenty:mahogany_plank_four_window","mcwbiomesoplenty:stripped_magic_log_window","mcwtrpdoors:oak_beach_trapdoor","mcwfurnitures:jungle_end_table","mcwwindows:red_sandstone_pane_window","utilitix:experience_crystal","mcwfences:bamboo_picket_fence","mcwfurnitures:stripped_oak_chair","handcrafted:goat_trophy","bigreactors:blasting/graphite_from_coal","mcwwindows:dark_prismarine_four_window","simplylight:illuminant_lime_block_on_dyed","farmersdelight:wheat_dough_from_eggs","minecraft:cooked_mutton","mcwbridges:jungle_bridge_pier","mcwbiomesoplenty:willow_pane_window","mcwtrpdoors:oak_barrel_trapdoor","buildinggadgets2:gadget_exchanging","mcwbiomesoplenty:jacaranda_picket_fence","mcwfences:mangrove_hedge","aether:skyroot_chest","mcwbiomesoplenty:fir_wired_fence","travelersbackpack:pig","undergarden:catalyst","immersiveengineering:crafting/voltmeter","mcwbiomesoplenty:redwood_plank_window","croptopia:shaped_bacon","mcwbiomesoplenty:empyreal_plank_window2","minecraft:jukebox","mcwwindows:jungle_curtain_rod","mcwbiomesoplenty:stripped_fir_pane_window","mcwwindows:oak_window","mcwbiomesoplenty:snowblossom_hedge","mcwbiomesoplenty:stripped_magic_pane_window","mcwwindows:spruce_plank_pane_window","minecraft:oak_boat","sophisticatedstorage:jungle_chest","create:crafting/kinetics/white_seat","mcwfences:dark_oak_highley_gate","mcwfurnitures:stripped_jungle_covered_desk","mcwbiomesoplenty:palm_highley_gate","mcwfurnitures:stripped_oak_kitchen_cabinet","mcwtrpdoors:oak_mystic_trapdoor","simplylight:illuminant_blue_block_on_toggle","mcwfurnitures:stripped_oak_stool_chair","minecraft:baked_potato_from_campfire_cooking","mcwbiomesoplenty:umbran_four_window","mcwbiomesoplenty:redwood_horse_fence","mcwfurnitures:stripped_jungle_large_drawer","mcwdoors:print_whispering","minecraft:jungle_fence","mcwroofs:oak_upper_steep_roof","mcwfences:mesh_metal_fence","utilitarian:utility/oak_logs_to_doors","mcwbiomesoplenty:empyreal_picket_fence","utilitarian:no_soliciting/soliciting_carpets/orange_soliciting_carpet","mcwbiomesoplenty:magic_plank_window2","farmersdelight:cooking/pasta_with_mutton_chop","mcwwindows:blackstone_pane_window","mcwfences:quartz_railing_gate","mcwwindows:spruce_four_window","mcwdoors:jungle_whispering_door","twilightforest:transformation_boat","botania:speed_up_belt","dyenamics:bed/cherenkov_bed","mcwlights:bamboo_tiki_torch","simplylight:illuminant_yellow_block_dyed","occultism:crafting/brush","mcwwindows:prismarine_pane_window","mcwdoors:jungle_stable_head_door","croptopia:egg_roll","mcwfurnitures:stripped_jungle_striped_chair","mcwlights:spruce_tiki_torch","simplylight:illuminant_block_dyed","mcwbiomesoplenty:orange_maple_hedge","mcwbiomesoplenty:stripped_redwood_log_window2","farmersdelight:cooking/beef_stew","aether:skyroot_boat","mcwtrpdoors:jungle_barred_trapdoor","mcwtrpdoors:oak_whispering_trapdoor","supplementaries:slice_map","mcwbiomesoplenty:dead_window","mcwfences:jungle_wired_fence","mcwfences:birch_hedge","simplylight:illuminant_gray_block_toggle","mcwwindows:stripped_birch_pane_window","mcwlights:mangrove_tiki_torch","mcwfences:end_brick_pillar_wall","minecraft:oak_planks","simplylight:illuminant_slab_from_panel","mcwbiomesoplenty:jacaranda_plank_window","mcwbiomesoplenty:stripped_redwood_log_window","sophisticatedstorage:jungle_barrel","mcwbiomesoplenty:palm_hedge","mcwdoors:oak_japanese_door","mcwtrpdoors:oak_barn_trapdoor","cfm:jungle_blinds","mcwfurnitures:jungle_modern_chair","twilightforest:twilight_oak_boat","mcwlights:soul_classic_street_lamp","mcwfurnitures:stripped_jungle_coffee_table","utilitarian:no_soliciting/soliciting_carpets/pink_soliciting_carpet","sophisticatedstorage:jungle_limited_barrel_4","sophisticatedstorage:jungle_limited_barrel_3","mcwbiomesoplenty:stripped_redwood_pane_window","pneumaticcraft:paper_from_tag_filter","simplylight:illuminant_purple_block_dyed","dyenamics:amber_wool","sophisticatedstorage:jungle_limited_barrel_2","sophisticatedstorage:jungle_limited_barrel_1","mcwwindows:acacia_window2","handcrafted:skeleton_horse_trophy","mcwfurnitures:stripped_jungle_double_drawer","cfm:brown_sofa","mcwwindows:blackstone_window2","additionallanterns:warped_lantern","mcwbiomesoplenty:stripped_fir_log_window2","mcwbiomesoplenty:maple_plank_pane_window","minecraft:spruce_boat","handcrafted:jungle_counter","mcwfences:warped_curved_gate","biomesoplenty:hellbark_chest_boat","minecraft:cooked_cod_from_campfire_cooking","mcwfences:bamboo_stockade_fence","mcwdoors:oak_stable_door","oceansdelight:stuffed_cod","utilitarian:no_soliciting/no_soliciting_banner","mcwfences:deepslate_brick_railing_gate","cfm:oak_upgraded_gate","mcwfences:jungle_horse_fence","mcwfurnitures:stripped_oak_modern_desk","delightful:smoking/roasted_acorn","handcrafted:salmon_trophy","supplementaries:speaker_block","mcwfences:modern_blackstone_wall","cfm:oak_desk_cabinet","corail_woodcutter:birch_woodcutter","mcwbiomesoplenty:redwood_four_window","mcwbiomesoplenty:magic_plank_window","minecraft:charcoal","additionallanterns:copper_lantern","mcwroofs:jungle_planks_attic_roof","twilightforest:canopy_chest_boat","mcwfurnitures:stripped_oak_double_wardrobe","mcwfences:railing_deepslate_brick_wall","silentgear:emerald_shard","supplementaries:bellows","mcwbiomesoplenty:fir_pane_window","mcwbiomesoplenty:hellbark_plank_window","mcwbiomesoplenty:redwood_highley_gate","mcwfurnitures:stripped_jungle_bookshelf_drawer","mcwfurnitures:jungle_bookshelf_drawer","croptopia:roasted_nuts","mcwbiomesoplenty:magic_pane_window","additionallanterns:blackstone_lantern","simplylight:illuminant_block_on_toggle","mcwwindows:stripped_warped_pane_window","mcwbiomesoplenty:umbran_highley_gate","mcwfences:acacia_curved_gate","mcwdoors:jungle_bamboo_door","mcwfences:birch_pyramid_gate","mcwbiomesoplenty:dead_pane_window","mcwwindows:diorite_window2","mcwbiomesoplenty:stripped_empyreal_pane_window","mcwfurnitures:stripped_oak_glass_table","farmersdelight:cooking/baked_cod_stew","mcwfurnitures:oak_bookshelf","croptopia:roasted_nuts_from_campfire","utilitarian:utility/jungle_logs_to_doors","mcwwindows:oak_plank_window","mcwfurnitures:stripped_oak_desk","corail_woodcutter:crimson_woodcutter","supplementaries:notice_board","mcwfurnitures:oak_end_table","croptopia:candied_nuts","minecraft:coal_block","mcwwindows:mangrove_plank_window","mcwdoors:oak_beach_door","mcwtrpdoors:oak_four_panel_trapdoor","aquaculture:dark_oak_fish_mount","allthecompressed:compress/jungle_planks_1x","mcwbiomesoplenty:fir_highley_gate","simplylight:illuminant_lime_block_toggle","mcwbiomesoplenty:stripped_dead_log_four_window","mcwfurnitures:jungle_modern_desk","minecraft:cooked_porkchop","mcwwindows:crimson_stem_window2","mcwbiomesoplenty:mahogany_wired_fence","mcwfurnitures:stripped_oak_double_kitchen_cabinet","mcwfences:curved_metal_fence_gate","mcwfences:acacia_hedge","mcwwindows:warped_pane_window","minecraft:oak_wood","minecraft:emerald_block","simplylight:illuminant_orange_block_toggle","simplylight:illuminant_magenta_block_on_toggle","domum_ornamentum:blockbarreldeco_onside","minecraft:diamond_pickaxe","create:crafting/schematics/empty_schematic","mcwlights:black_paper_lamp","simplylight:rodlamp","mcwwindows:stripped_acacia_log_window2","mcwwindows:dark_prismarine_pane_window","mcwfurnitures:oak_modern_desk","aether:leather_helmet_repairing","aquaculture:sushi","mcwtrpdoors:print_classic","mcwbiomesoplenty:umbran_plank_window2","mcwfurnitures:jungle_table","mcwfences:flowering_azalea_hedge","cfm:stripped_oak_coffee_table","mcwfences:acacia_stockade_fence","mcwbiomesoplenty:maple_plank_window","mcwbiomesoplenty:jacaranda_pyramid_gate","delightful:campfire/roasted_acorn","mcwfences:warped_horse_fence","twilightforest:canopy_boat","sophisticatedstorage:generic_limited_barrel_3","sophisticatedstorage:generic_limited_barrel_4","cfm:stripped_jungle_coffee_table","sophisticatedstorage:generic_limited_barrel_1","sophisticatedstorage:generic_limited_barrel_2","mcwbiomesoplenty:maple_four_window","mcwlights:striped_lantern","mcwfences:jungle_highley_gate","cfm:stripped_oak_kitchen_counter","mcwwindows:stripped_oak_log_window","mcwbiomesoplenty:mahogany_picket_fence","mcwfurnitures:stripped_jungle_modern_desk","dyenamics:bed/fluorescent_bed","mcwbiomesoplenty:palm_picket_fence","handcrafted:jungle_table","simplylight:illuminant_green_block_on_dyed","mcwbiomesoplenty:hellbark_horse_fence","create:crafting/kinetics/item_vault","mcwfences:warped_highley_gate","mcwfences:crimson_stockade_fence","minecolonies:blockconstructiontape","mcwbiomesoplenty:jacaranda_four_window","croptopia:scrambled_eggs","mcwbiomesoplenty:palm_plank_four_window","deeperdarker:bloom_chest_boat","mcwwindows:granite_window2","mcwwindows:dark_oak_plank_window","mcwtrpdoors:oak_paper_trapdoor","mcwfences:railing_red_sandstone_wall","mcwbiomesoplenty:jacaranda_stockade_fence","mcwroofs:jungle_attic_roof","utilitix:diamond_shears","supplementaries:clock_block","mcwwindows:warped_stem_window2","mcwwindows:diorite_pane_window","mcwbiomesoplenty:fir_stockade_fence","delightful:storage/acorn_storage_block","additionallanterns:cobblestone_lantern","mcwbridges:jungle_rail_bridge","additionallanterns:stone_lantern","mcwfurnitures:jungle_covered_desk","mcwbiomesoplenty:palm_pane_window","mcwwindows:acacia_pane_window","mcwbiomesoplenty:dead_plank_window","mcwfurnitures:oak_desk","mcwwindows:deepslate_window","aquaculture:tin_can_to_iron_nugget","utilitarian:no_soliciting/soliciting_carpets/red_soliciting_carpet","mcwfences:andesite_railing_gate","mcwbiomesoplenty:hellbark_plank_pane_window","minecraft:cooked_beef_from_smoking","mcwbiomesoplenty:umbran_wired_fence","aether:leather_chestplate_repairing","mcwwindows:dark_oak_window2","mcwwindows:crimson_stem_window","mcwbiomesoplenty:pine_plank_pane_window","mcwlights:festive_wall_lantern","cfm:stripped_jungle_park_bench","mcwfurnitures:stripped_oak_drawer","ae2wtlib:magnet_card","biomesoplenty:umbran_chest_boat","mcwlights:rustic_torch","mcwwindows:acacia_plank_pane_window","mcwwindows:andesite_pane_window","mcwlights:soul_oak_tiki_torch","mcwlights:double_street_lamp","mcwbiomesoplenty:stripped_maple_pane_window","cfm:oak_upgraded_fence","additionallanterns:quartz_lantern","minecraft:flint_and_steel","mcwfences:mud_brick_grass_topped_wall","mcwtrpdoors:oak_swamp_trapdoor","biomesoplenty:pine_boat","mcwfurnitures:stripped_jungle_double_drawer_counter","mcwfurnitures:jungle_drawer_counter","aquaculture:tin_can_to_iron_nugget_from_blasting","create:crafting/kinetics/blue_seat","mcwbiomesoplenty:jacaranda_wired_fence","aether:leather_gloves","minecraft:spruce_chest_boat","mcwbiomesoplenty:empyreal_plank_pane_window","mcwbiomesoplenty:maple_plank_window2","mcwwindows:dark_prismarine_window","mcwwindows:cherry_plank_window","mcwbiomesoplenty:dead_plank_window2","mcwdoors:oak_bamboo_door","travelersbackpack:hay","twilightforest:dark_boat","biomesoplenty:umbran_boat","dyenamics:persimmon_wool","corail_woodcutter:bamboo_mosaic_woodcutter","mcwfences:quartz_pillar_wall","additionallanterns:crimson_lantern","mcwbiomesoplenty:pine_hedge","simplylight:bulb","mcwwindows:stripped_acacia_pane_window","create:crafting/kinetics/magenta_seat","mcwwindows:stripped_dark_oak_log_four_window","simplylight:illuminant_magenta_block_toggle","mcwwindows:granite_four_window","mcwlights:wall_lantern","farmersdelight:cooking/pumpkin_soup","cfm:door_mat","create:crafting/kinetics/gray_seat","create:crafting/kinetics/brown_seat","mcwbiomesoplenty:willow_plank_window","mcwfurnitures:jungle_modern_wardrobe","simplylight:illuminant_red_block_on_dyed","handcrafted:jungle_shelf","mcwbiomesoplenty:redwood_stockade_fence","mcwwindows:metal_window","mcwtrpdoors:jungle_barn_trapdoor","mcwfurnitures:jungle_glass_table","handcrafted:jungle_nightstand","mcwbiomesoplenty:fir_window","simplylight:illuminant_yellow_block_toggle","mcwbiomesoplenty:hellbark_plank_window2","mcwfurnitures:stripped_jungle_glass_table","mcwbiomesoplenty:pine_pane_window","comforts:sleeping_bag_to_brown","supplementaries:bed_from_feather_block","mcwfurnitures:stripped_oak_glass_kitchen_cabinet","simplylight:illuminant_light_blue_block_on_toggle","mcwfences:cathedral_metal_fence","utilitix:jungle_shulker_boat","mcwlights:magenta_paper_lamp","ae2additions:blocks/wireless_transceiver","aquaculture:heavy_hook","mcwbiomesoplenty:willow_hedge","aquaculture:diamond_fillet_knife","biomesoplenty:dead_chest_boat","mcwwindows:acacia_window","minecraft:cherry_boat","utilitarian:no_soliciting/soliciting_carpets/purple_soliciting_carpet","minecraft:leather_boots","aquaculture:cooked_fish_fillet_from_campfire","mcwfurnitures:oak_chair","mcwwindows:stripped_crimson_stem_four_window","immersiveengineering:crafting/powerpack","mcwbiomesoplenty:empyreal_highley_gate","mcwfurnitures:stripped_jungle_modern_chair","mcwbridges:oak_bridge_pier","simplylight:illuminant_cyan_block_toggle","mcwfences:bamboo_fence","mcwfurnitures:jungle_bookshelf","utilitix:highspeed_rail","mcwfences:jungle_stockade_fence","minecraft:barrel","mcwwindows:dark_oak_pane_window","mcwlights:pink_paper_lamp","utilitix:tiny_coal_to_tiny","sgjourney:fire_pit","supplementaries:boat_jar","mcwwindows:cherry_window","minecraft:jungle_chest_boat","aquaculture:golden_fishing_rod","mcwfences:oak_highley_gate","mcwwindows:jungle_plank_window","mcwbiomesoplenty:palm_window2","utilitix:hand_bell","minecraft:baked_potato_from_smoking","aquaculture:fishing_line","minecolonies:supplychestdeployer","silentgear:diamond_from_shards","mcwbiomesoplenty:stripped_jacaranda_log_window","mcwfurnitures:stripped_jungle_drawer","mcwbiomesoplenty:stripped_palm_log_window","mcwfences:deepslate_brick_pillar_wall","mcwfurnitures:oak_glass_kitchen_cabinet","corail_woodcutter:oak_woodcutter","mcwfurnitures:oak_glass_table","cfm:oak_table","mcwfences:railing_diorite_wall","dyenamics:conifer_wool","mcwfurnitures:jungle_cupboard_counter","mcwbiomesoplenty:mahogany_highley_gate","minecraft:cauldron","delightful:smelting/roasted_acorn","mcwfences:railing_stone_brick_wall","mcwbiomesoplenty:pine_pyramid_gate","productivetrees:crates/red_delicious_apple_crate","domum_ornamentum:architectscutter","mcwwindows:stripped_mangrove_pane_window","mcwroofs:jungle_planks_roof","farmersdelight:fried_egg_from_smoking","mcwwindows:bricks_window2","mcwfences:jungle_picket_fence","silentgear:blueprint_paper","mcwfurnitures:oak_kitchen_cabinet","aether:iron_boots_repairing","minecraft:diamond_shovel","mcwlights:dark_oak_tiki_torch","minecraft:hay_block","utilitarian:utility/oak_logs_to_trapdoors","utilitarian:utility/jungle_logs_to_stairs","dimstorage:dim_core","mcwbiomesoplenty:palm_stockade_fence","twilightforest:mangrove_boat","mcwroofs:jungle_planks_upper_lower_roof","mcwfences:birch_curved_gate","mcwbiomesoplenty:mahogany_window2","mcwbiomesoplenty:stripped_pine_log_four_window","mcwwindows:red_sandstone_window2","mcwbiomesoplenty:palm_pyramid_gate","mcwbiomesoplenty:umbran_window2","mcwfences:modern_granite_wall","corail_woodcutter:cherry_woodcutter","create:jungle_window","mcwlights:soul_crimson_tiki_torch","mcwfurnitures:oak_double_kitchen_cabinet","dyenamics:mint_wool","mcwfences:dark_oak_curved_gate","mcwwindows:cherry_curtain_rod","mcwwindows:crimson_plank_pane_window","mcwbiomesoplenty:dead_wired_fence","mcwbiomesoplenty:stripped_willow_pane_window","mcwbiomesoplenty:mahogany_pane_window","mcwdoors:oak_tropical_door","minecraft:chest","alltheores:diamond_plate","supplementaries:pulley","mcwwindows:sandstone_four_window","simplylight:illuminant_block_on","mcwfences:wooden_cheval_de_frise","aquaculture:light_hook","mcwroofs:oak_upper_lower_roof","aquaculture:jungle_fish_mount","mcwfences:birch_horse_fence","mcwfences:mangrove_highley_gate","bigreactors:reactor/reinforced/passivefluidport_forge","mcwdoors:oak_waffle_door","biomesoplenty:dead_boat","mcwwindows:bricks_window","mcwfences:mangrove_stockade_fence","cfm:jungle_upgraded_gate","mcwfurnitures:stripped_oak_bookshelf_cupboard","twigs:paper_lantern","mcwfences:mud_brick_pillar_wall","aquaculture:iron_hook","mcwwindows:andesite_window","aquaculture:diamond_fishing_rod","additionallanterns:basalt_lantern","aquaculture:diamond_hook","mcwfences:blackstone_grass_topped_wall","mcwdoors:oak_glass_door","simplylight:illuminant_brown_block_toggle","mcwfences:crimson_highley_gate","mcwbiomesoplenty:empyreal_window2","mcwfences:sandstone_pillar_wall","minecraft:diamond_chestplate","cfm:stripped_oak_park_bench","simplylight:illuminant_black_block_on_toggle","mcwbiomesoplenty:stripped_maple_log_window2","mcwwindows:crimson_curtain_rod","mcwwindows:acacia_plank_window2","mcwbiomesoplenty:redwood_plank_window2","mcwwindows:jungle_plank_pane_window","mcwwindows:cherry_four_window","constructionwand:diamond_wand","mcwwindows:warped_stem_four_window","immersiveengineering:crafting/blueprint_bullets","mcwlights:iron_framed_torch","dyenamics:bed/honey_bed","simplylight:illuminant_blue_block_on_dyed","mcwwindows:spruce_plank_window","mcwdoors:jungle_mystic_door","sophisticatedbackpacks:backpack","croptopia:flour","mcwbiomesoplenty:maple_wired_fence","minecraft:jungle_sign","mcwwindows:crimson_planks_window","mcwfences:railing_mud_brick_wall","mcwfurnitures:stripped_oak_modern_chair","mcwbiomesoplenty:stripped_willow_log_window2","mcwbiomesoplenty:maple_plank_four_window","mcwlights:classic_street_lamp","mcwfences:dark_oak_horse_fence","mcwwindows:prismarine_four_window","mcwbiomesoplenty:willow_wired_fence","mcwwindows:stripped_jungle_log_four_window","mcwlights:orange_paper_lamp","mcwbiomesoplenty:stripped_hellbark_pane_window","mcwfences:spruce_picket_fence","croptopia:roasted_smoking","mcwbiomesoplenty:hellbark_curved_gate","supplementaries:soap","mcwwindows:oak_plank_window2","immersiveengineering:crafting/blueprint_molds","travelersbackpack:emerald","mcwbiomesoplenty:pine_plank_window","croptopia:trail_mix","utilitarian:no_soliciting/soliciting_carpets/white_soliciting_carpet","mcwfences:acacia_horse_fence","mcwfences:spruce_hedge","mcwfences:jungle_hedge","mcwbiomesoplenty:willow_plank_window2","mcwdoors:jungle_classic_door","utilitix:linked_crystal","mcwwindows:stripped_jungle_pane_window","mcwwindows:stripped_crimson_pane_window","twigs:jungle_table","mcwbiomesoplenty:stripped_jacaranda_log_four_window","utilitarian:angel_block_rot","mcwwindows:stripped_mangrove_log_four_window","allthecompressed:compress/oak_log_1x","mcwbiomesoplenty:stripped_hellbark_log_window2","mcwfurnitures:oak_triple_drawer","simplylight:illuminant_slab","aquaculture:cooked_fish_fillet_from_smoking","mcwwindows:sandstone_pane_window","utilitarian:utility/jungle_logs_to_slabs","mcwfences:warped_stockade_fence","cfm:stripped_jungle_desk","mcwfurnitures:stripped_oak_double_drawer","dyenamics:bed/spring_green_bed","handcrafted:brown_cushion","aether:diamond_gloves","aquaculture:cooked_fish_fillet","mcwdoors:jungle_four_panel_door","travelersbackpack:blank_upgrade","croptopia:pork_jerky","simplylight:illuminant_light_gray_block_on_toggle","mcwdoors:oak_stable_head_door","simplylight:illuminant_cyan_block_on_toggle","mcwlights:jungle_tiki_torch","mcwroofs:oak_lower_roof","mcwfences:bamboo_wired_fence","mcwbiomesoplenty:willow_plank_pane_window","mcwwindows:jungle_pane_window","mcwfurnitures:cabinet_door","mcwwindows:stripped_spruce_pane_window","mcwbiomesoplenty:redwood_window","mcwbiomesoplenty:yellow_maple_hedge","mcwbiomesoplenty:stripped_maple_log_four_window","mcwwindows:stripped_dark_oak_log_window","aquaculture:jellyfish_to_slimeball","cfm:stripped_jungle_desk_cabinet","minecraft:jungle_wood","mcwfences:panelled_metal_fence","mcwfurnitures:oak_stool_chair","mcwwindows:diorite_four_window","additionallanterns:dark_prismarine_lantern","utilitarian:no_soliciting/soliciting_carpets/brown_soliciting_carpet","mcwwindows:mangrove_plank_window2","cfm:jungle_desk_cabinet","mcwfurnitures:jungle_bookshelf_cupboard","mcwwindows:mangrove_pane_window","minecraft:jungle_stairs","additionallanterns:smooth_stone_lantern","mcwbiomesoplenty:pine_plank_four_window","handcrafted:jungle_chair","domum_ornamentum:paper_extra","mcwfurnitures:stripped_jungle_triple_drawer","mcwwindows:crimson_pane_window","mcwwindows:warped_plank_pane_window","utilitarian:utility/jungle_logs_to_boats","mcwfurnitures:stripped_oak_wardrobe","biomesoplenty:maple_boat","minecraft:cherry_chest_boat","mcwlights:acacia_tiki_torch","mcwwindows:stripped_warped_stem_window","mcwwindows:stripped_birch_log_four_window","mcwroofs:thatch_upper_lower_roof","corail_woodcutter:warped_woodcutter","mcwbiomesoplenty:redwood_plank_pane_window","cfm:jungle_kitchen_sink_dark","silentgear:upgrade_base","mcwbiomesoplenty:stripped_jacaranda_pane_window","mcwwindows:crimson_stem_four_window","handcrafted:pufferfish_trophy","minecraft:fletching_table","minecraft:baked_potato","simplylight:walllamp","mcwwindows:dark_oak_four_window","mcwbiomesoplenty:fir_picket_fence","mcwfences:oak_horse_fence","minecraft:cooked_salmon","mcwfurnitures:oak_large_drawer","mcwfences:expanded_mesh_metal_fence","utilitarian:no_soliciting/soliciting_carpets/cyan_soliciting_carpet","mcwlights:framed_torch","mcwroofs:jungle_steep_roof","dyenamics:bed/peach_bed","mcwfences:blackstone_pillar_wall","aquaculture:gold_nugget_from_blasting","mcwdoors:jungle_cottage_door","mcwlights:gray_paper_lamp","mcwbiomesoplenty:maple_stockade_fence","minecraft:oak_chest_boat","simplylight:illuminant_purple_block_on_toggle","simplylight:illuminant_red_block_dyed","simplylight:illuminant_gray_block_dyed","mcwfurnitures:oak_lower_bookshelf_drawer","mcwfences:modern_mud_brick_wall","mcwfences:azalea_hedge","mcwbiomesoplenty:palm_plank_pane_window","mcwtrpdoors:print_beach","simplylight:illuminant_block_on_dyed","mcwbiomesoplenty:jacaranda_hedge","mcwfences:fortress_metal_fence","mcwtrpdoors:jungle_barrel_trapdoor","mcwfences:bamboo_curved_gate","mcwbiomesoplenty:stripped_umbran_pane_window","twilightforest:mining_chest_boat","aquaculture:neptunium_ingot_from_neptunium_block","mcwbiomesoplenty:pine_wired_fence","handcrafted:wolf_trophy","securitycraft:sc_manual","create:crafting/kinetics/yellow_seat","mcwwindows:spruce_plank_four_window","mcwbiomesoplenty:jacaranda_pane_window","corail_woodcutter:acacia_woodcutter","silentgear:stone_torch","mcwfences:guardian_metal_fence","croptopia:cheeseburger","minecraft:mangrove_boat","minecraft:bread","productivetrees:crates/red_delicious_apple_crate_unpack","simplylight:illuminant_blue_block_toggle","minecraft:acacia_boat","mcwbiomesoplenty:magic_four_window","mcwfences:acacia_pyramid_gate","mcwfurnitures:stripped_oak_cupboard_counter","mcwbiomesoplenty:dead_four_window","mcwwindows:stripped_warped_stem_four_window","mcwwindows:dark_oak_curtain_rod","mcwbiomesoplenty:jacaranda_plank_four_window","create:crafting/schematics/schematic_and_quill","additionallanterns:red_nether_bricks_lantern","farmersdelight:cooking/vegetable_soup","mcwfences:twilight_metal_fence","mcwfences:curved_metal_fence","dyenamics:peach_wool","utilitarian:utility/jungle_logs_to_pressure_plates","mcwwindows:birch_pane_window","mcwbiomesoplenty:willow_pyramid_gate","mcwlights:soul_acacia_tiki_torch","minecraft:dark_oak_chest_boat","mcwfences:iron_cheval_de_frise","mcwlights:purple_paper_lamp","minecraft:painting","mcwfences:modern_red_sandstone_wall","biomesoplenty:willow_boat","mcwfences:deepslate_grass_topped_wall","mcwfurnitures:jungle_desk","mcwroofs:jungle_planks_lower_roof","mcwfences:bastion_metal_fence_gate","minecraft:acacia_chest_boat","mcwwindows:crimson_planks_window2","alltheores:diamond_rod","mcwwindows:deepslate_four_window","mcwtrpdoors:jungle_whispering_trapdoor","mcwfurnitures:oak_covered_desk","domum_ornamentum:blockbarreldeco_standing","mcwwindows:stripped_jungle_log_window","mcwdoors:print_oak","mcwfences:gothic_metal_fence","deeperdarker:echo_boat","aether:ice_from_bucket_freezing","mcwwindows:stripped_spruce_log_window2","mcwfences:birch_picket_fence","mcwlights:white_paper_lamp","cfm:stripped_jungle_crate","biomesoplenty:jacaranda_boat","mcwwindows:mangrove_plank_pane_window","mcwfurnitures:stripped_jungle_bookshelf_cupboard","mcwfurnitures:stripped_jungle_lower_bookshelf_drawer","simplylight:illuminant_magenta_block_dyed","mcwlights:cyan_paper_lamp","mcwfences:quartz_grass_topped_wall","mcwbiomesoplenty:hellbark_pane_window","mcwdoors:jungle_waffle_door","farmersdelight:grilled_salmon","mcwbiomesoplenty:stripped_hellbark_log_four_window","mcwbiomesoplenty:dead_hedge","mcwwindows:window_centre_bar_base","mcwfences:prismarine_grass_topped_wall","mcwfurnitures:stripped_oak_end_table","mcwfences:bamboo_horse_fence","utilitarian:utility/jungle_logs_to_trapdoors","cfm:oak_cabinet","mcwwindows:stripped_oak_log_window2","mcwbiomesoplenty:magic_highley_gate","mcwbiomesoplenty:stripped_umbran_log_window2","twilightforest:mangrove_chest_boat","mcwlights:soul_jungle_tiki_torch","simplylight:illuminant_green_block_on_toggle","utilitarian:no_soliciting/soliciting_carpets/light_blue_soliciting_carpet","framedblocks:framed_cube","aquaculture:redstone_hook","mcwbiomesoplenty:flowering_oak_hedge","mcwwindows:stripped_mangrove_log_window2","utilitarian:utility/logs_to_bowls","biomesoplenty:fir_chest_boat","mcwwindows:stripped_cherry_log_four_window","domum_ornamentum:brown_floating_carpet","cfm:stripped_oak_table","mcwfences:oak_curved_gate","dyenamics:lavender_wool","aquaculture:brown_mushroom_from_fish","mcwwindows:quartz_four_window","biomesoplenty:palm_boat","mcwdoors:oak_swamp_door","mcwbiomesoplenty:stripped_redwood_log_four_window","handcrafted:bear_trophy","croptopia:apple_juice","utilitarian:no_soliciting/restraining_order","mcwwindows:cherry_window2","minecraft:jungle_slab","supplementaries:sconce","mcwwindows:stripped_oak_pane_window","twilightforest:dark_chest_boat","additionallanterns:netherite_lantern","minecraft:dark_oak_boat","mcwbiomesoplenty:mahogany_four_window","aquaculture:gold_hook","aether:skyroot_bed","mcwbiomesoplenty:palm_wired_fence","aether:iron_leggings_repairing","handcrafted:wither_skeleton_trophy","twigs:stick_from_twig","mcwfurnitures:cabinet_drawer","mcwbiomesoplenty:jacaranda_plank_window2","mcwwindows:dark_oak_plank_pane_window","mcwbiomesoplenty:hellbark_stockade_fence","mcwbiomesoplenty:stripped_empyreal_log_window","mcwbiomesoplenty:empyreal_pane_window","mcwwindows:stripped_spruce_log_four_window","mcwbiomesoplenty:stripped_jacaranda_log_window2","mcwwindows:sandstone_window","enderio:enderios","mcwfurnitures:oak_coffee_table","supplementaries:candy","silentgear:emerald_from_shards","simplylight:illuminant_black_block_toggle","mcwfences:modern_quartz_wall","mcwbiomesoplenty:hellbark_plank_four_window","mcwlights:light_blue_paper_lamp","minecraft:jungle_pressure_plate","travelersbackpack:chicken","additionallanterns:granite_lantern","mcwwindows:deepslate_pane_window","mcwwindows:spruce_plank_window2","dyenamics:bubblegum_wool","mcwwindows:spruce_window","mcwwindows:prismarine_window2","mcwfences:andesite_grass_topped_wall","mcwbiomesoplenty:fir_plank_four_window","farmersdelight:cooking/apple_cider","forbidden_arcanus:wooden_blacksmith_gavel","mcwbiomesoplenty:pine_horse_fence","handcrafted:silverfish_trophy","mcwwindows:acacia_four_window","mcwdoors:jungle_tropical_door","twigs:lamp","dyenamics:bed/lavender_bed","mcwbiomesoplenty:hellbark_window","mcwdoors:jungle_glass_door","simplylight:illuminant_cyan_block_dyed","dyenamics:icy_blue_wool","mcwfurnitures:jungle_triple_drawer","mcwbiomesoplenty:stripped_empyreal_log_window2","mcwdoors:oak_barn_door","mcwfences:sandstone_grass_topped_wall","mcwfurnitures:jungle_lower_bookshelf_drawer","ad_astra:brown_flag","mcwwindows:stripped_jungle_log_window2","mcwlights:soul_warped_tiki_torch","biomesoplenty:jacaranda_chest_boat","mcwfurnitures:stripped_jungle_lower_triple_drawer","mcwwindows:cherry_plank_pane_window","biomesoplenty:magic_boat","farmersdelight:fried_egg","mcwbiomesoplenty:maple_pane_window","mcwfurnitures:oak_bookshelf_cupboard","simplylight:illuminant_orange_block_on_dyed","mcwfurnitures:stripped_oak_table","mcwfences:bamboo_pyramid_gate","cfm:oak_bedside_cabinet","mcwfurnitures:oak_bookshelf_drawer","cfm:oak_park_bench","croptopia:pork_and_beans","additionallanterns:cobbled_deepslate_lantern","immersiveengineering:crafting/conveyor_basic","mcwtrpdoors:oak_tropical_trapdoor","minecraft:birch_chest_boat","mcwfences:blackstone_railing_gate","cfm:stripped_oak_bedside_cabinet","mcwbiomesoplenty:empyreal_horse_fence","simplylight:illuminant_lime_block_on_toggle","aether:skyroot_chest_boat","mcwroofs:thatch_attic_roof","handcrafted:jungle_fancy_bed","mcwbiomesoplenty:redwood_window2","farmersdelight:onion_crate","create:crafting/kinetics/purple_seat","mcwbiomesoplenty:umbran_plank_window","mcwbiomesoplenty:palm_curved_gate","mcwbiomesoplenty:magic_window2","mcwlights:cross_lantern","mcwfurnitures:stripped_oak_striped_chair","mcwwindows:bricks_four_window","mcwwindows:deepslate_window2","securitycraft:reinforced_packed_mud","mcwbiomesoplenty:redwood_plank_four_window","simplylight:illuminant_red_block_on_toggle","additionallanterns:normal_sandstone_lantern","dyenamics:bed/icy_blue_bed","aquaculture:gold_nugget_from_gold_fish","minecraft:jungle_boat","minecraft:diamond_hoe","mcwbiomesoplenty:umbran_stockade_fence","mcwbridges:bridge_lantern","additionallanterns:diamond_chain","ae2additions:super_cell_housing","cfm:brown_trampoline","mcwdoors:oak_whispering_door","mcwwindows:jungle_window2","minecraft:cooked_mutton_from_campfire_cooking","mcwtrpdoors:jungle_mystic_trapdoor","mcwbiomesoplenty:fir_four_window","minecraft:cooked_beef","mcwwindows:birch_plank_window2","mcwtrpdoors:jungle_glass_trapdoor","mcwbiomesoplenty:pine_curved_gate","minecraft:cooked_mutton_from_smoking","mcwdoors:oak_mystic_door","mcwlights:yellow_paper_lamp","mcwbiomesoplenty:palm_horse_fence","farmersdelight:pie_crust","mcwlights:warped_tiki_torch","rftoolsbuilder:vehicle_card","utilitarian:no_soliciting/soliciting_carpets/green_soliciting_carpet","twigs:dandelion_paper_lantern","mcwroofs:jungle_lower_roof","simplylight:edge_light","mcwfences:railing_granite_wall","cfm:stripped_jungle_kitchen_drawer","minecraft:gold_nugget_from_smelting","toolbelt:pouch","mcwfurnitures:stripped_oak_bookshelf_drawer","mcwdoors:oak_nether_door","mcwroofs:thatch_steep_roof","mcwbiomesoplenty:stripped_pine_log_window","mcwwindows:jungle_plank_window2","mcwbiomesoplenty:dead_stockade_fence","immersiveengineering:crafting/blueprint_bannerpatterns","mcwfences:crimson_pyramid_gate","cfm:stripped_oak_desk_cabinet","mcwfences:stone_brick_railing_gate","mcwfences:mangrove_pyramid_gate","mcwfences:stone_pillar_wall","mcwfences:spruce_stockade_fence","cfm:jungle_table","mcwfurnitures:stripped_oak_lower_bookshelf_drawer","charginggadgets:charging_station","farmersdelight:cooking/fish_stew","mcwwindows:cherry_pane_window","mcwbiomesoplenty:empyreal_pyramid_gate","simplylight:illuminant_green_block_toggle","mcwwindows:mangrove_window2","botania:lexicon","mcwdoors:jungle_modern_door","additionallanterns:purpur_lantern","mcwwindows:stripped_warped_stem_window2","simplylight:illuminant_block","mcwlights:green_paper_lamp","mcwbiomesoplenty:empyreal_curved_gate","dyenamics:maroon_wool","corail_woodcutter:jungle_woodcutter","croptopia:campfire_toast","mcwfurnitures:jungle_lower_triple_drawer","aquaculture:neptunium_ingot_from_nuggets","croptopia:french_fries","mcwbiomesoplenty:dead_highley_gate","biomesoplenty:hellbark_boat","supplementaries:daub","mcwfurnitures:stripped_jungle_end_table","mcwfences:oak_hedge","mcwbiomesoplenty:willow_plank_four_window","mcwfences:railing_sandstone_wall","mcwwindows:granite_window","mcwfences:modern_diorite_wall","simplylight:illuminant_block_toggle","dyenamics:bed/wine_bed","mcwtrpdoors:jungle_tropical_trapdoor","mcwbiomesoplenty:hellbark_highley_gate","simplylight:illuminant_black_block_on_dyed","mcwfences:spruce_highley_gate","minecraft:birch_boat","aether:leather_boots_repairing","mcwlights:brown_paper_lamp","utilitarian:no_soliciting/soliciting_carpets/magenta_soliciting_carpet","mcwdoors:jungle_barn_door","minecraft:campfire","mcwfences:modern_end_brick_wall","mcwbiomesoplenty:maple_highley_gate","mcwbiomesoplenty:magic_plank_four_window","mcwfences:railing_quartz_wall","mcwbiomesoplenty:umbran_picket_fence","biomesoplenty:pine_chest_boat","mcwbiomesoplenty:mahogany_plank_window","mcwdoors:oak_barn_glass_door","bigreactors:smelting/graphite_from_coal","mcwlights:bell_wall_lantern","mcwbiomesoplenty:stripped_umbran_log_four_window"]},seenCredits:0b,warden_spawn_tracker:{cooldown_ticks:0,ticks_since_last_warning:375,warning_level:0}}