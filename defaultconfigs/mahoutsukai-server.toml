
[boundary]

	[boundary.alarm]
		#This is the mana cost per cycle
		#Range: 0 ~ *********
		ALARM_BARRIER_MANA_COST = 1
		#This is how often the boundary drains mana
		#Range: 0 ~ *********
		ALARM_BARRIER_MANA_CYCLE = 10
		#This is the range of the boundary
		#Range: 0 ~ *********
		ALARM_BARRIER_RADIUS = 10
		#This is how often the boundary does it's thing
		#Range: 0 ~ *********
		ALARM_BARRIER_CYCLE = 20
		#Messages are sent to Action Bar as opposed to the Chat window
		ALARM_ACTION_BAR_MESSAGES = false

	[boundary.displacement]
		#This is the mana cost per cycle
		#Range: 0 ~ *********
		DISPLACEMENT_BARRIER_MANA_COST = 1
		#This is how often the boundary drains mana
		#Range: 0 ~ *********
		DISPLACEMENT_BARRIER_MANA_CYCLE = 5
		#This is the range of the boundary
		#Range: 0 ~ *********
		DISPLACEMENT_BARRIER_RADIUS = 11
		#This is how often the boundary does it's thing
		#Range: 0 ~ *********
		DISPLACEMENT_BARRIER_CYCLE = 4

	[boundary."drain life"]
		#This is the mana cost per cycle
		#Range: 0 ~ *********
		DRAIN_LIFE_BARRIER_MANA_COST = 5
		#This is how often the boundary drains mana
		#Range: 0 ~ *********
		DRAIN_LIFE_BARRIER_MANA_CYCLE = 1
		#This is the range of the boundary
		#Range: 0 ~ *********
		DRAIN_LIFE_BARRIER_RADIUS = 10
		#This is how often the boundary does it's thing
		#Range: 0 ~ *********
		DRAIN_LIFE_BARRIER_CYCLE = 20
		#This is whether the boundary ignores hunger when refilling
		DRAIN_LIFE_IGNORE_HUNGER = false
		#This is how much mana the boundary restores
		#Range: 0 ~ 10000000
		DRAIN_LIFE_MANA_REGEN = 1
		#This is how much the boundary heals
		#Range: -1.0E7 ~ 1.0E7
		DRAIN_LIFE_HEAL_FACTOR = 0.5
		#This is how much damage the boundary does
		#Range: 0.0 ~ 1.0E7
		DRAIN_LIFE_DAMAGE = 2.0
		#The boundary does this much times its normal damage if the target has magic resistance
		#Range: 0.0 ~ 1.0E7
		DRAIN_LIFE_PUNISH_RESIST = 1.8
		#Entities that won't be considered for the boundary
		DRAIN_LIFE_ENTITY_BLACKLIST = []

	[boundary.gravity]
		#This is the mana cost per cycle
		#Range: 0 ~ *********
		GRAVITY_BARRIER_MANA_COST = 1
		#This is how often the boundary drains mana
		#Range: 0 ~ *********
		GRAVITY_BARRIER_MANA_CYCLE = 2
		#This is the range of the boundary
		#Range: 0 ~ *********
		GRAVITY_BARRIER_RADIUS = 10
		#This is how often the boundary does it's thing
		#Range: 0 ~ *********
		GRAVITY_BARRIER_CYCLE = 1
		#This is a multiplicative inrease in downward velocity
		#Range: 0.0 ~ 1.0E8
		GRAVITY_BARRIER_FACTOR = 1.4
		#This is how often the boundary does it's thing
		#Range: 0.0 ~ 1.0E8
		GRAVITY_BARRIER_MAX = 5.0E7

	[boundary.enclosure]
		#This is the mana cost per cycle
		#Range: 0 ~ *********
		ENCLOSURE_BARRIER_MANA_COST = 20
		#This is how often the boundary does it's thing
		#Range: 0 ~ *********
		ENCLOSURE_BARRIER_CYCLE = 20
		#This is the range of the boundary
		#Range: 0 ~ *********
		ENCLOSURE_BARRIER_RADIUS = 5

	[boundary.tangible]
		#This is the mana cost per cycle
		#Range: 0 ~ *********
		TANGIBLE_BARRIER_MANA_COST = 1
		#This is how often the boundary drains mana
		#Range: 0 ~ *********
		TANGIBLE_BARRIER_MANA_CYCLE = 3
		#This is the range of the boundary
		#Range: 0 ~ *********
		TANGIBLE_BARRIER_RADIUS = 10
		#This is how often the boundary does it's thing
		#Range: 0 ~ *********
		TANGIBLE_BARRIER_CYCLE = 20

[displacement]

	[displacement.ascension]
		#This is how often the circle checks for things to teleport
		#Range: 0 ~ *********
		ASCENSION_BLOCK_CYCLE = 4
		#This is the cost of using the scroll (also the cost per teleport if not a scroll)
		#Range: 0 ~ *********
		ASCENSION_SCROLL_MANA_COST = 30

	[displacement.equivalent]
		#This is how often the circle checks for things to teleport
		#Range: 0 ~ *********
		EQUIVALENT_DISPLACEMENT_BLOCK_CYCLE = 4
		#This is the cooldown between teleports (so you don't get shot back and forth rapidly)
		#Range: 0 ~ *********
		EQUIVALENT_DISPLACEMENT_RECEIVE_COOLDOWN = 15
		#This is the cost per teleport
		#Range: 0 ~ *********
		EQUIVALENT_DISPLACEMENT_MANA_COST = 60
		#Max Distance for Equivalent Displacement
		#Range: -1.0E7 ~ 1.0E7
		EQUIVALENT_DISPLACEMENT_MAX_DISTANCE = -1.0
		#Whether Equivalent Displacement can traverse dimensions
		EQUIVALENT_DISPLACEMENT_DIMENSIONAL_TRAVEL = true

	[displacement.mental]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		MENTAL_DISPLACEMENT_MANA_COST = 300
		#This is how far you can go before being sent back to your body
		#Range: 0 ~ *********
		MENTAL_DISPLACEMENT_RANGE = 20

	[displacement.projectile]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		PROJECTILE_DISPLACEMENT_MANA_COST = 50

	[displacement.ordered]
		#This is how often the circle checks for things to teleport
		#Range: 0 ~ *********
		ORDERED_DISPLACEMENT_BLOCK_CYCLE = 4
		#This is the cooldown between teleports (so you don't get shot back and forth rapidly)
		#Range: 0 ~ *********
		ORDERED_DISPLACEMENT_RECEIVE_COOLDOWN = 15
		#This is the cost per teleport
		#Range: 0 ~ *********
		ORDERED_DISPLACEMENT_MANA_COST = 40
		#This is the max distance between ordered displacement circles.
		#Range: 0 ~ *********
		ORDERED_DISPLACEMENT_RADIUS = 20

	[displacement.protective]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		PROTECTIVE_DISPLACEMENT_MANA_COST = 50

	[displacement.scrying]
		#This is the cost per name tag
		#Range: 0 ~ *********
		SCRYING_MANA_COST = 50
		#This is how long (in ticks) before a single name tag use runs out
		#Range: 0 ~ *********
		SCRYING_DURATION = 1200
		#This is how often a Scrying tile entity will send updates
		#Range: 0 ~ *********
		SCRYING_BLOCK_CYCLE = 4
		#This is whether the scrying circle can scry non players
		SCRY_NON_PLAYERS = true

[projection]

	[projection."weapon projectile"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		WEAPON_SHOOTER_MANA_COST = 100
		#Speed of projectiles shot from the weapon projectile bow
		#Range: 0.0 ~ 1.0E7
		WEAPON_SHOOTER_PROJECTILE_SPEED = 3.0
		#Damage multiplier for projectiles shot from the weapon projectile bow
		#Range: 0.0 ~ 1.0E7
		WEAPON_SHOOTER_DAMAGE_MULTIPLIER = 1.0
		#Weapon projectile bow durability
		#Range: 0 ~ 10000000
		WEAPON_SHOOTER_DURABILITY = 30

	[projection.treasury]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		TREASURY_PROJECTION_SCROLL_MANA_COST = 1000
		#This is the cost per projectile when using the gauntlet
		#Range: 0 ~ *********
		TREASURY_PROJECTION_GAUNTLET_MANA_COST = 120
		#This is the delay between projectiles of the gauntlet
		#Range: 0 ~ *********
		TREASURY_PROJECTION_COOLDOWN = 10
		#This is the cost per chain when using the gauntlet in Chains mode
		#Range: 0 ~ *********
		TREASURY_PROJECTION_CHAINS_MANA_COST = 120
		#This is the delay between chains of the gauntlet in Chains mode
		#Range: 0 ~ *********
		TREASURY_PROJECTION_CHAINS_COOLDOWN = 10
		#How many initial chains to spawn
		#Range: 0 ~ *********
		TREASURY_PROJECTION_CHAINS_COUNT = 16
		#How much damage chains do on contact
		#Range: 0.0 ~ 1.0E8
		TREASURY_PROJECTION_CHAINS_DAMAGE = 1.0
		#This is the cost per weapon when using the gauntlet in AOE mode
		#Range: 0 ~ *********
		TREASURY_PROJECTION_AOE_MANA_COST = 120
		#This is the delay between weapons of the gauntlet in AOE mode
		#Range: 0 ~ *********
		TREASURY_PROJECTION_AOE_COOLDOWN = 4
		#This is the max distance between the target and the projectile spawn
		#Range: 0 ~ *********
		TREASURY_PROJECTION_DISTANCE = 20
		#This is the min distance between the target and the projectile spawn
		#Range: 0 ~ *********
		TREASURY_PROJECTION_MIN_DISTANCE = 8
		#This is the wind up time each projectile has before firing.
		#Range: 0.0 ~ 1.0E8
		TREASURY_PROJECTION_SPEED = 30.0
		#Ignore whitelist; allow everything
		TREASURY_PROJECTION_ALLOW_ALL = false
		#Ignore whitelist & blacklist; allow everything
		TREASURY_PROJECTION_DENY_ALL = false
		#A list of additional registry names which will be considered for selection in Treasury Projection
		TREASURY_PROJECTION_WHITELIST = []
		#A list of  registry names which will be not considered for selection in Treasury Projection
		TREASURY_PROJECTION_BLACKLIST = []
		#Treasury Projection Speed Increase Per Available Sword
		#Range: -10000.0 ~ 10000.0
		TREASURY_PROJECTION_SPEED_INCREASE_PER_SWORD = 1.0
		#Treasury Projection Speed Minimum
		#Range: 0.0 ~ 100000.0
		TREASURY_PROJECTION_SPEED_MIN = 10.0
		#Treasury Projection Cooldown Increase Per Available Sword
		#Range: -10000.0 ~ 10000.0
		TREASURY_PROJECTION_COOLDOWN_INCREASE_PER_SWORD = 1.0
		#Treasury Projection Cooldown Minimum
		#Range: 0.0 ~ 100000.0
		TREASURY_PROJECTION_COOLDOWN_MIN = 3.0
		#Treasury Projection AOE Mode Max Spawns Per Tick
		#Range: 0.0 ~ 100000.0
		TREASURY_PROJECTION_AOE_MAX_SPAWNS = 3.0
		#Treasury Projection AOE Mode Swords Per Spawn
		#Range: 0.0 ~ 100000.0
		TREASURY_PROJECTION_AOE_SWORDS_PER_SPAWN = 5.0
		#Treasury Projection Increases Requires Unique Weapons
		TREASURY_PROJECTION_INCREASE_REQUIRES_UNIQUE = true
		#Treasury Projection Only Uses Ender Chest
		TREASURY_PROJECTION_ENDER_CHEST_ONLY = false
		#Treasury Projection weapons despawn after hitting the ground
		TREASURY_PROJECTION_DESPAWN_AFTER_LAND = false

	[projection.strengthening]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		STRENGTHENING_MANA_COST = 50
		#Strengthening Item Spawn Blacklist
		STRENGTHENING_ITEM_SPAWN_BLACKLIST = []
		#Items that Strengthening won't work on
		STRENGTHENING_ITEM_BLACKLIST = []
		#Max Strengthening Cap
		#Range: 0 ~ *********
		STRENGTHENING_CAP = 50

	[projection."reality marble"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		MARBLE_MANA_COST = 4000
		#If you have dimension conflicts, change this to something else
		#Range: -1000000 ~ *********
		MARBLE_DIMENSION = -1
		#Durability of swords spawned in the Marble Dimension
		#Range: 0 ~ *********
		MARBLE_DIMENSION_DURABILITY = 3
		#Distance between swords spawned in the Marble Dimension
		#Range: 0 ~ *********
		MARBLE_DIMENSION_SPAWN_RATE = 13
		#Probability of sword spawn
		#Range: 0.0 ~ 1.0E8
		MARBLE_DIMENSION_SPAWN_CHANCE = 0.8
		#These will spawn in the Reality Marble as entities.. No guarantees on functionality if you change this.
		MARBLE_SWORD_SPAWN_LIST = ["minecraft:iron_sword", "minecraft:diamond_sword", "minecraft:stone_sword"]
		#Reality Marble Mob Blacklist
		MARBLE_MOB_BLACKLIST = []

	[projection.proximity]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		PROXIMITY_PROJECTION_MANA_COST = 160
		#How much durability the Proximity Projection Keys have
		#Range: 0 ~ *********
		PROXIMITY_PROJECTION_DURABILITY = 30

	[projection.projection]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		PROJECTION_MANA_COST = 100
		#A list of item names which cannot be projected. Such as minecraft:diamond_sword , for example.
		PROJECTION_BLACKLIST = []
		#If False, PROJECTION_BLACKLIST will be considered a whitelist
		PROJECTION_IS_BLACKLIST = true
		#Projection Enchantment Blacklist
		PROJECTION_ENCHANTMENT_BLACKLIST = []
		#Either the amount of damage a projected item takes from its max, or the total durability of the projected item
		#Range: 0 ~ *********
		PROJECTION_DURABILITY_FACTOR = 5
		#Whether the projected durability is based on max damage (false) or just flat (true)
		PROJECTION_DURABILITY_FLAT = true
		#Projection items cannot be used in anvil
		PROJECTION_ANVIL_CANCEL = false
		#Projection Explicit Whitelist
		PROJECTION_EXPLICIT_WHITELIST = []

	[projection."power consolidation"]
		#This is the cost per sword transformation.
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_SWORD_MANA_COST = 5000
		#This is the cost per cycle for creating the lake.
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_LAKE_MANA_COST = 30
		#This is how often the lake does it's thing to make a lake
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_LAKE_CYCLE = 20
		#This is the max radius of the lake
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_LAKE_RADIUS = 12
		#This is the minimum threshhold a lake needs to be to be able to transform a sword into Caliburn.
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_LAKE_THRESHHOLD = 150
		#This is a nerf factor applied to enchantments to reduce the amount of power that can be consolidated. Lower is less nerf.
		#Range: 0.0 ~ 1.0E8
		POWER_CONSOLIDATION_NERF_FACTOR = 0.6
		#This is the distance before the lake begins to generate fog. (Note: Higher values may decrease FPS)
		#Range: 0.0 ~ 1.0E8
		POWER_CONSOLIDATION_FOG_RADIUS = 8.0
		#This is the range for pulling undead mobs with Caliburn
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_PULL_RADIUS = 20
		#This is the range for smiting undead mobs with Caliburn
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_SMITE_RADIUS = 10
		#How much durability Caliburn has.
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_DURABILITY = 1000
		#This is the range for undead mobs to fear Caliburn
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_FEAR_RADIUS = 15
		#Entities must be this close to the wielder of Caliburn to be affected by the sweep.
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_SWEEP_RADIUS = 6
		#The highest attack Caliburn can go to
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_ATTACK_CAP = 5000
		#Entities must be this close to the wielder of Caliburn's look vector to be affected by the sweep.
		#Range: 0 ~ *********
		POWER_CONSOLIDATION_SWEEP_LOOK_RADIUS = 3
		#Acts as a blacklist if the toggle is on
		POWER_CONSOLIDATION_BLACKLIST = ["mahoutsukai:clarent", "mahoutsukai:morgan"]
		#Toggles the sword blacklist functionality
		POWER_CONSOLIDATION_CALIBURN_BLACKLIST_TOGGLE = true
		#Toggles whether it water should attempt to create tile entities. Only needed for old worlds with murky water in them.
		POWER_CONSOLIDATION_RETRO_FOG_FIX = false
		#Acts as a blacklist for the Fear Effect & Pull Effect on Caliburn
		POWER_CONSOLIDATION_FEAR_BLACKLIST = []
		#Power Consolidation Enchant Blacklist
		POWER_CONSOLIDATION_ENCHANT_BLACKLIST = ["minecraft:mending"]
		#Power Consolidation Damage Cap Tiers
		POWER_CONSOLIDATION_TIERS = [100.0, 150.0, 200.0, 250.0, 300.0, 350.0, 400.0, 450.0, 500.0, 550.0, 600.0, 650.0, 700.0, 750.0, 800.0, 850.0, 900.0, 950.0, 1000.0, 1500.0, 2000.0, 2500.0, 3000.0, 3500.0, 4000.0, 4500.0, 5000.0]
		#Power Consolidation Tier Upgrade Items
		POWER_CONSOLIDATION_TIER_UPGRADE_ITEM = ["irons_spellbooks:ender_upgrade_orb"]
		#Words which are considered unholy by Caliburn and Rhongomyniad
		UNHOLY_WORDS = ["zomb", "vampir", "demon", "devil", "skele", "lich", "evil", "curse", "undead", "wither"]

	[projection.rhongomyniad]
		#Rhongomyniad Lake Nerf Factor
		#Range: -1.0E7 ~ 1.0E7
		RHONGOMYNIAD_NERF_FACTOR = 0.6
		#Rhongomyniad Speed Factor
		#Range: -1.0E7 ~ 1.0E7
		RHONGOMYNIAD_SPEED_FACTOR = 1.4
		#Rhongomyniad Mana Cost
		#Range: 0 ~ 10000000
		RHONGOMYNIAD_MANA_COST = 300
		#Rhongomyniad Max Smites
		#Range: 0 ~ 10000000
		RHONGOMYNIAD_MAX_SMITES = 10
		#Rhongomyniad Range
		#Range: 0 ~ 10000000
		RHONGOMYNIAD_RANGE = 20
		#Rhongomyniad Respects Geas And Caster Immunity
		RHONGOMYNIAD_RESPECT_IMMUNE = false

	[projection.clarent]
		#Clarent is Unbreakable
		CLARENT_UNBREAKABLE = true
		#Clarent Durability
		#Range: 0 ~ *********
		CLARENT_DURABILITY = 1500
		#How long Clarent's wound lasts
		#Range: 0 ~ *********
		CLARENT_WOUND_TICKS = 600
		#How much damage the wound does per hit
		#Range: 0.0 ~ 1.0E8
		CLARENT_WOUND_DAMAGE = 0.2
		#How much Clarent's stored damage decreases per tick while blocking
		#Range: 0.0 ~ 1.0E8
		CLARENT_DECREASE_PER_BLOCKING_TICK = 0.02
		#How many hits the wound does each time it activates
		#Range: 0 ~ *********
		CLARENT_WOUND_DAMAGE_HITS = 3
		#Which swords cannot turn into Clarent
		CLARENT_SWORD_BLACKLIST = ["mahoutsukai:caliburn", "mahoutsukai:morgan"]
		#Max stored damage for Clarent
		#Range: 0 ~ *********
		CLARENT_ATTACK_CAP = 5000000
		#Nerf factor for damage stored by Clarent
		#Range: 0.0 ~ 1.0E8
		CLARENT_STORED_FACTOR = 0.5
		#Clarent Blacklist Toggle
		CLARENT_BLACKLIST_TOGGLE = true
		#Clarent Mana Per Damage
		#Range: 0.0 ~ 1.0E7
		CLARENT_MANA_PER_DAMAGE = 10.0

	[projection.emrys]
		#How far the focused lightning can go
		#Range: 0 ~ *********
		EMRYS_MAX_RANGE = 22
		#Emrys Damage Focused Per Second
		#Range: 0.0 ~ 1.0E8
		EMRYS_DAMAGE_FOCUSED_PER_SECOND = 4.0
		#How much damage the chain lightning does
		#Range: 0.0 ~ 1.0E8
		EMRYS_DAMAGE_ZAP = 1.5
		#Whether sky lightning from the emrys staff ignites the ground
		EMRYS_BOLT_FIRE = false
		#How much mana per second drained by Emrys Staff when using focused attack
		#Range: 0 ~ *********
		EMRYS_MANA_COST_FOCUSED = 200
		#How much mana per second drained by Emrys Staff when using focused attack
		#Range: 0 ~ *********
		EMRYS_MANA_COST_PASSIVE = 80
		#Disable Emrys
		DISABLE_EMRYS = false

	[projection."morgan and caliburn"]
		#List of allowed pets that can be killed with Caliburn to create Morgan
		MORGAN_TRANSFORM_ENTITY_WHITELIST = ["minecraft:warden"]
		#How much damage Morgan gains from killing children
		#Range: 0.0 ~ 1.0E8
		MORGAN_CHILD_INCREASE = 1.0
		#How much damage Morgan gains from killing adult villagers
		#Range: 0.0 ~ 1.0E8
		MORGAN_ADULT_INCREASE = 0.2
		#How much durability Morgan gains from killing villagers
		#Range: 0 ~ *********
		MORGAN_HEAL_FACTOR = 30
		#Cooldown between special cast for Caliburn and Morgan
		#Range: 0 ~ *********
		MORGAN_CALIBURN_POWER_COOLDOWN = 600
		#How long Morgan's Rage attack lasts
		#Range: 0 ~ *********
		MORGAN_RAGE_TIME = 120
		#How far Morgan's ball can go before stopping
		#Range: 0 ~ *********
		MORGAN_MAX_BALL_RANGE = 15
		#How far Morgan's ball's spikes can go
		#Range: 0 ~ *********
		MORGAN_SPIKE_RANGE = 10
		#Morgan Upgrade Mana Cost
		#Range: 0 ~ 10000000
		MORGAN_UPGRADE_MANA_COST = 400
		#Caliburn Morgan Ability Mana Cost
		#Range: 0 ~ *********
		CALIBURN_MORGAN_ABILITY_MANA_COST = 300

	[projection.replica]
		#Replica Shockwave Base Damage
		#Range: 0.0 ~ 1.0E7
		REPLICA_BASE_DAMAGE = 4.0
		#Replica Friend Teleport Range
		#Range: 0.0 ~ 1.0E7
		REPLICA_TELEPORT_FRIEND_RANGE = 6.0
		#Replica Friend Teleport Max Distance
		#Range: -1.0E7 ~ 1.0E7
		REPLICA_TELEPORT_MAX_DISTANCE = -1.0
		#Replica Teleport Start Time
		#Range: 0 ~ 10000000
		REPLICA_TELEPORT_START_TELEPORTING = 110
		#Replica Teleport Stop Time
		#Range: 0 ~ 10000000
		REPLICA_TELEPORT_STOP_TELEPORTING = 120
		#Replica Teleport Life
		#Range: 0 ~ 10000000
		REPLICA_TELEPORT_LIFE = 140
		#Replica Teleport Mana Cost
		#Range: 0 ~ 10000000
		REPLICA_TELEPORT_MANA_COST = 300
		#Replica Shockwave Mana Cost
		#Range: 0 ~ 10000000
		REPLICA_ATTACK_MANA_COST = 30
		#Replica Shockwave Cooldown
		#Range: 0 ~ 1000000
		REPLICA_SHOCKWAVE_COOLDOWN = 50
		#Replica Shockwave Range
		#Range: 0.0 ~ 1.0E7
		REPLICA_ATTACK_RANGE = 8.0
		#Replica can Teleport across Dimensions
		REPLICA_TELEPORT_CROSS_DIMENSION = true
		#Replica Cover Move Range
		#Range: 0.0 ~ 1.0E7
		REPLICA_COVER_MOVE_RANGE = 20.0
		#Replica Shockwave Max Damage
		#Range: 0.0 ~ 1.0E7
		REPLICA_MAX_DAMAGE = 5000000.0
		#Replica is disabled
		REPLICA_DISABLED = false
		#Replica Banned Damage Types
		REPLICA_BANNED_DAMAGE_TYPES = []
		#Replica Durability
		#Range: 0 ~ 10000000
		REPLICA_DURABILITY = 10000
		#Replica is Unbreakable
		REPLICA_IS_UNBREAKABLE = true

[exchange]

	[exchange.alchemical]
		#This is how often the circle changes a layer underneath it
		#Range: 0 ~ *********
		ALCHEMICAL_EXCHANGE_BLOCK_CYCLE = 20
		#How far down gets changed by the Alchemical Exchange
		#Range: 0 ~ *********
		ALCHEMICAL_EXCHANGE_HEIGHT = 5
		#This is the cost for each block changed by the circle
		#Range: 0 ~ *********
		ALCHEMICAL_EXCHANGE_MANA_COST = 16
		#This is a list of blocks available in the dirt-class exchange.
		ALCHEMICAL_DIRT_CLASS = ["minecraft:dirt", "minecraft:sand", "minecraft:soul_sand", "minecraft:snow_block"]
		#This is a list of blocks available in the stone-class exchange.
		ALCHEMICAL_STONE_CLASS = ["minecraft:stone", "minecraft:ice", "minecraft:packed_ice", "minecraft:sandstone", "minecraft:red_sandstone", "minecraft:gravel", "minecraft:end_stone", "minecraft:purpur_block", "minecraft:magma_block", "minecraft:netherrack", "minecraft:terracotta"]
		#This is a list of blocks available in the metal-class exchange.
		ALCHEMICAL_METAL_CLASS = ["minecraft:iron_ore", "minecraft:gold_ore", "minecraft:nether_quartz_ore"]
		#This is a list of blocks available in the gem-class exchange.
		ALCHEMICAL_GEM_CLASS = ["minecraft:diamond_ore", "minecraft:emerald_ore"]
		#This is a list of blocks available in the clay-class exchange.
		ALCHEMICAL_CLAY_CLASS = ["minecraft:clay", "minecraft:coal_ore", "minecraft:redstone_ore", "minecraft:lapis_ore"]
		#This is a list of blocks available in the wood-class exchange.
		ALCHEMICAL_WOOD_CLASS = ["minecraft:oak_log", "minecraft:birch_log", "minecraft:acacia_log", "minecraft:jungle_log", "minecraft:spruce_log", "minecraft:dark_oak_log", "minecraft:cactus", "minecraft:pumpkin", "minecraft:melon", "minecraft:brown_mushroom_block", "minecraft:red_mushroom_block", "minecraft:hay_block", "minecraft:nether_wart_block"]
		#This is a list of blocks available in the special-class exchange.
		ALCHEMICAL_SPECIAL_CLASS = ["minecraft:prismarine", "minecraft:glowstone", "minecraft:sea_lantern", "minecraft:obsidian", "minecraft:slime_block"]
		#This is a list of blocks available in the grass-class exchange.
		ALCHEMICAL_GRASS_CLASS = ["minecraft:grass_block", "minecraft:mycelium"]
		#This is a list of blocks available in the fluid-class exchange.
		ALCHEMICAL_FLUID_CLASS = ["minecraft:lava", "minecraft:water"]
		#This is a list of blocks available in the moving-fluid-class exchange.
		ALCHEMICAL_MOVING_FLUID_CLASS = []

	[exchange.catalyst]
		#This is how often the circle checks for catalysts on top to change
		#Range: 0 ~ *********
		CATALYST_EXCHANGE_BLOCK_CYCLE = 4
		#This is the cost for each catalyst exchanged by the circle
		#Range: 0 ~ *********
		CATALYST_EXCHANGE_MANA_COST = 50

	[exchange.chronal]
		#This is how often the circle restores or deducts mana
		#Range: 0 ~ *********
		CHRONAL_EXCHANGE_BLOCK_CYCLE = 20
		#This is how much mana is gained or lost every cycle
		#Range: 0 ~ *********
		CHRONAL_EXCHANGE_MANA_GAIN_LOSS = 10

	[exchange.durability]
		#This is how often the circle restores mana
		#Range: 0 ~ *********
		DURABILITY_EXCHANGE_BLOCK_CYCLE = 20
		#This is the limit on mana restored per cycle
		#Range: 0 ~ *********
		DURABILITY_EXCHANGE_MANA_GAIN_CAP = 10
		#Take this much durability per mana point gained
		#Range: 0.0 ~ 1.0E8
		DURABILITY_EXCHANGE_EFFICIENCY = 1
		#Items on this list will not be considered valid for durability exchange
		DURABILITY_EXCHANGE_BLACKLIST = []
		#Durability Exchange Tax Brackets
		DURABILITY_EXCHANGE_TAX_BRACKETS = [5000.0, 20000.0, 100000.0]
		#Durability Exchange Tax Rates
		DURABILITY_EXCHANGE_TAX_RATES = [0.02, 0.03, 0.04]

	[exchange.damage]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		DAMAGE_EXCHANGE_MANA_COST = 40
		#This is the how much mana you get back when you're injured
		#Range: 0 ~ *********
		DAMAGE_EXCHANGE_MANA_GAIN = 20
		#This is the new value of damage done while damage exchange is active
		#Range: 0.0 ~ 1.0E8
		DAMAGE_EXCHANGE_REDUCE_TO = 1.0
		#Damage Exchange Cap
		#Range: 0 ~ *********0
		DAMAGE_EXCHANGE_CAP = 5

	[exchange.contract]
		#This is the cost of forming a contract with another player
		#Range: 0 ~ *********
		CONTRACT_MANA_COST = 10
		#This is how often the block checks for players on top of it
		#Range: 0 ~ *********
		CONTRACT_BLOCK_CYCLE = 4
		#This is the max distance between contract circles.
		#Range: 0 ~ *********
		CONTRACT_RADIUS = 10

	[exchange.immunity]
		#This is the mana cost per scroll
		#Range: 0 ~ *********
		IMMUNITY_EXCHANGE_MANA_COST = 400
		#This is how long the buff/debuff lasts (in ticks)
		#Range: 0 ~ *********
		IMMUNITY_EXCHANGE_TIME = 1200

[mystic]

	[mystic.spatial]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		SPATIAL_DISORIENTATION_MANA_COST = 100
		#This is the cost of using the staff on a block (per second!)
		#Range: 0 ~ *********
		SPATIAL_DISORIENTATION_MANA_COST_AOE = 20
		#This is a flat cost of using the staff on an entity
		#Range: 0 ~ *********
		SPATIAL_DISORIENTATION_MANA_COST_ST = 200
		#Spatial Disorientation Air Resistance
		SPATIAL_DISORIENTATION_AIR_RESISTANCE = false
		#Spatial Disorientation Launch Speed
		#Range: 0.0 ~ 1.0E7
		SPATIAL_DISORIENTATION_SPEED = 7.0
		#Spatial Disorientation AOE Mode Radius
		#Range: 0.0 ~ 1.0E7
		SPATIAL_DISORIENTATION_AOE_RADIUS = 4.0

	[mystic."big explosion"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		MYSTIC_STAFF_SUMMON_MANA_COST = 100
		#This is the cost of using the giant explosion
		#Range: 0 ~ *********
		MYSTIC_STAFF_BIG_MANA_COST = 5000
		#Whether the damage is flat or scales with caster's 'level'
		MYSTIC_STAFF_BIG_SCALES = true
		#Either a fraction of the max mana (if scaling) or a flat damage value
		#Range: 0.0 ~ 1.0E8
		MYSTIC_STAFF_BIG_FACTOR = 0.002
		#Explosion Size for Big Explosion
		#Range: 0 ~ *********
		MYSTIC_STAFF_BIG_SIZE = 30
		#Mystic Staff Creates Rain and Thunder
		MYSTIC_STAFF_BIG_RAIN = true
		#Mystic Staff Scaling Mana Factor
		#Range: 0.0 ~ 1.0E7
		MYSTIC_STAFF_SCALING_MANA = 0.0
		#Mystic Staff World Damage
		MYSTIC_STAFF_WORLD_DAMAGE = true

	[mystic."aoe explosion"]
		#This is the cost of using the multiple explosion mode
		#Range: 0 ~ *********
		MYSTIC_STAFF_AOE_MANA_COST = 600
		#Whether the damage is flat or scales with caster's 'level'
		MYSTIC_STAFF_AOE_SCALES = true
		#Either a fraction of the max mana (if scaling) or a flat damage value
		#Range: 0.0 ~ 1.0E8
		MYSTIC_STAFF_AOE_FACTOR = 0.001
		#Explosion Size for AOE Explosion
		#Range: 0 ~ *********
		MYSTIC_STAFF_AOE_SIZE = 4
		#Delay between firing on AOE Mode of Explosion Staff
		#Range: 0 ~ 10000000
		MYSTIC_STAFF_AOE_DELAY = 3
		#Mystic Staff AOE Scaling Mana Factor
		#Range: 0.0 ~ 1.0E7
		MYSTIC_STAFF_AOE_SCALING_MANA = 0.0

	[mystic.beam]
		#Mystic Staff Beam Mana Per Tick
		#Range: 0 ~ *********
		MYSTIC_STAFF_BEAM_MANA_PER_TICK = 500
		#Mystic Staff Beam Damage Factor
		#Range: -1.0E7 ~ 1.0E8
		MYSTIC_STAFF_BEAM_DAMAGE_FACTOR = 0.001
		#Whether the damage is flat or scales with caster's 'level'
		MYSTIC_STAFF_BEAM_DAMAGE_SCALES = true
		#Mystic Staff Beam Damage Acceleration
		#Range: 0.0 ~ 1.0E8
		MYSTIC_STAFF_BEAM_DAMAGE_ACCELERATION = 1.5
		#Mystic Staff Beam Max Block Break Per Tick
		#Range: 0 ~ *********
		MYSTIC_STAFF_BEAM_MAX_BLOCK_BREAK_PER_TICK = 90
		#Mystic Staff Beam Block Whitelist
		MYSTIC_STAFF_BEAM_BLOCK_WHITELIST = []
		#Mystic Staff Beam Scaling Mana Factor
		#Range: 0.0 ~ 1.0E7
		MYSTIC_STAFF_BEAM_SCALING_MANA = 0.0

	[mystic."rho aias"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		RHO_AIAS_MANA_COST = 300
		#How far away Rho Aias will delete entities
		#Range: 0 ~ *********
		RHO_AIAS_KILL_RANGE = 30
		#How long Rho Aias lasts
		#Range: 0 ~ *********
		RHO_AIAS_LIFE = 1200
		#Entities which Rho Aias will delete if within range
		RHO_AIAS_ENTITY_KILL_LIST = ["mahoutsukai:gandr_entity"]
		#Entities which Rho Aias will delete if colliding with.
		RHO_AIAS_COLLIDE_KILL_LIST = ["mahoutsukai:gandr_entity"]
		#Logs detected entities from the kill list, for debug only
		RHO_AIAS_KILL_DEBUG = false
		#Whether Rho Aias boops all the time or just when caster sneaking
		RHO_AIAS_SNEAK_BOOP = true

	[mystic."shared pain"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		DAMAGE_REPLICATION_MANA_COST = 160

	[mystic."borrowed authority"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		AUTHORITY_MANA_COST = 900
		#This is how long the effect lasts (in ticks)
		#Range: 0 ~ *********
		AUTHORITY_TIME = 400
		#This is how much damage done to the player when attacking something
		#Range: 0.0 ~ 1.0E8
		AUTHORITY_DAMAGE_TO_SELF = 1.5
		#This is how much damaqe is done when the player hits something
		#Range: 0.0 ~ 1.0E8
		AUTHORITY_DAMAGE_TO_OTHER = 3.0
		#This is the slowdown factor when a punched thing hits a wall. Lower is slower
		#Range: 0.0 ~ 1.0E8
		AUTHORITY_BREAK_SLOW_DOWN_FACTOR = 0.5
		#This is the slowdown factor when a punched thing is flying through the air normally. Lower is slower
		#Range: 0.0 ~ 1.0E8
		AUTHORITY_NORMAL_SLOW_DOWN_FACTOR = 0.875
		#This is the multiplier for speed when the buff is active. Lower is slower
		#Range: 0.0 ~ 1.0E8
		AUTHORITY_SPEED_MULTIPLIER = 1.8
		#Blocks with higher than this resistance will not be broken. Set to 0 for no breaking.
		#Range: 0 ~ *********
		AUTHORITY_BLOCK_RESISTANCE_LIMIT = 45
		#Borrowed Authority Drops Blocks
		AUTHORITY_DROPS_BLOCKS = true

	[mystic."cup of heaven"]
		#This is multiplied by the square root of the area of the target network, and then multiplied by the sum of potion to get the mana cost.
		#Range: 0 ~ *********
		HEAVENS_CUP_MANA_COST = 2
		#This is the maximum distance the player can be from a network to activate it
		#Range: 0 ~ *********
		HEAVENS_CUP_START_DISTANCE = 20
		#This is how long potion generated by the network last.
		#Range: 0 ~ *********
		HEAVENS_CUP_DURATION = 1200
		#This is the maximum distance between nodes for them to be counted as part of the network.
		#Range: 0 ~ *********
		HEAVENS_CUP_NETWORK_DISTANCE = 30
		#Iterations per tick for Heavens Cup
		#Range: 0 ~ *********
		HEAVENS_CUP_MAX_ITERS = 10
		#List of effects, in order, achievable by the cup of heaven
		HEAVENS_CUP_EFFECTS = ["poison", "weakness", "blindness", "slowness", "wither", "levitation"]

[eyes]

	[eyes.clairvoyance]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		CLAIRVOYANCE_MANA_COST = 220
		#This is how long the buff lasts
		#Range: 0 ~ *********
		CLAIRVOYANCE_TIME = 1200
		#Entities within this radius will have their movement predicted
		#Range: 0 ~ *********
		CLAIRVOYANCE_RANGE = 30

	[eyes.binding]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		MYSTIC_EYES_MANA_COST = 320
		#This is how long the buff lasts
		#Range: 0 ~ *********
		MYSTIC_EYES_TIME = 600
		#Entities must be within this radius of the user to be affected
		#Range: 0 ~ *********
		MYSTIC_EYES_RANGE_FROM_USER = 5
		#Entities must be this close to the user's look vector to be affected
		#Range: 0 ~ *********
		MYSTIC_EYES_RANGE_FROM_LOOK_VEC = 4

	[eyes.reversion]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		REVERSION_EYES_MANA_COST = 410
		#This is how long the buff lasts
		#Range: 0 ~ *********
		REVERSION_EYES_TIME = 600
		#Entities must be within this radius of the user to be affected
		#Range: 0 ~ *********
		REVERSION_EYES_RANGE_FROM_USER = 10
		#Entities must be this close to the user's look vector to be affected
		#Range: 0 ~ *********
		REVERSION_EYES_RANGE_FROM_LOOK_VEC = 4

	[eyes."death collection"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		DEATH_COLLECTION_MANA_COST = 400
		#This is how long the buff lasts
		#Range: 0 ~ *********
		DEATH_COLLECTION_TIME = 600
		#Entities must be within this radius of the user to be counted
		#Range: 0 ~ *********
		DEATH_COLLECTION_RANGE_FROM_USER = 10
		#Mob deaths will count this much to the next death prevention.
		#Range: 0.0 ~ 1.0E8
		DEATH_COLLECTION_SOUL_VALUE_MOB = 0.25
		#Player deaths will count this much to the next death prevention.
		#Range: 0.0 ~ 1.0E8
		DEATH_COLLECTION_SOUL_VALUE_PLAYER = 1.0
		#Death Collection Revive Value
		#Range: 0.0 ~ 1.0E8
		DEATH_COLLECTION_REVIVE_VALUE = 12.0
		#Max Souls possible for Death Collection
		#Range: -1.0E7 ~ 1.0E7
		DEATH_COLLECTION_MAX = 1.0E7
		#Mana drained per soul held
		#Range: -1.0E7 ~ 1.0E7
		DEATH_COLLECTION_DRAIN_PER_SOUL = 1.0
		#Souls drain with negative mana regen
		DEATH_COLLECTION_NEGATIVE_REGEN_PUNISHMENT = true

	[eyes."black flame"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		BLACK_FLAME_MANA_COST = 300
		#This is how long the buff lasts
		#Range: 0 ~ *********
		BLACK_FLAME_TIME = 100
		#Blocks and entities within this range will be ignited if the user looks at them
		#Range: 0 ~ *********
		BLACK_FLAME_RANGE_FROM_USER = 30
		#Something is lit on fire every this many ticks, from the player's eyes
		#Range: 0 ~ *********
		BLACK_FLAME_IGNITION_TICKS = 1
		#How often flames do an update (in ticks)
		#Range: 0 ~ *********
		BLACK_FLAME_UPDATE_TICKS = 1
		#Control the speed at which flames die (number between 0 and 3, probably)
		#Range: 0 ~ *********
		BLACK_FLAME_DEATH_AGE = 6
		#Black Flame Damage
		#Range: -1.0E7 ~ 1.0E7
		BLACK_FLAME_DAMAGE = 1.0
		#How long the wither lasts after stepping in black flame
		#Range: 0 ~ *********
		BLACK_FLAME_DEBUFF_TIME = 180

	[eyes."fay sight"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		FAY_SIGHT_MANA_COST = 100
		#This is how long the buff lasts
		#Range: 0 ~ *********
		FAY_SIGHT_TIME = 600

	[eyes.leylines]
		#This value is added to mana regen. It is the floor for mana added by leylines. By default, you lose 1 mana from regen for not being near a leyline.
		#Range: -10000000 ~ *********
		LEY_PUNISHMENT = -1
		#The factor used in calculating the leyline bonus regen
		#Range: 0.0 ~ 1.0E8
		LEY_FACTOR = 0.01
		#Whether the bonus regen is flat or based on max mana
		LEY_FLAT = false
		#How far away ley points are from each other
		#Range: 0 ~ *********
		LEY_DISTANCE = 300
		#An offset in case you don't want 0,0 to be a leyline
		#Range: 0 ~ *********
		LEY_OFFSET = 0
		#Set this to false if you don't like ley lines affecting mana regen
		LEY_AFFECTS_MANA = true
		#The Y value Ley Lines should render at
		#Range: 0 ~ *********
		LEY_RENDER_HEIGHT = 70
		#Modifier for Fae spawn rate
		#Range: 0.0 ~ 1.0E8
		FAE_SPAWN_RATE = 1.0
		#If false, will show lines instead of runes. Lines not may not be compatible with shaders.
		LEY_RUNES = true
		#If true, the dimension list is a blacklist. If false it is a whitelist.
		LEY_BLACKLIST = true
		#Leyline Eytra boost limited to Fay Sight
		LEY_ELYTRA_LIMITED_TO_FAY_SIGHT = false
		#Whitelist or blacklist of dimension IDs
		LEY_DIMENSION_LIST = []

	[eyes.insight]
		#The cost of using the scroll.
		#Range: 0 ~ *********
		INSIGHT_MANA_COST = 320
		#How long the buff lasts
		#Range: 0 ~ *********
		INSIGHT_TIME = 1200

[familiar]

	[familiar."shared vision"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		POSSESS_ENTITY_MANA_COST = 200

	[familiar."recall familiar"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		RECALL_FAMILIAR_MANA_COST = 20

	[familiar."summon familiar"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		SUMMON_FAMILIAR_MANA_COST = 200
		#How often the familiar tries to talk to you
		#Range: 0 ~ *********
		FAMILIAR_REPORT_SURROUNDINGS_TIME = 400
		#How much health the familiar has
		#Range: 0 ~ *********
		FAMILIAR_HEALTH = 10
		#The chance that a familiar will complain about being hurt.
		#Range: 0.0 ~ 1.0E8
		FAMILIAR_OUCH_CHANCE = 0.3
		#Messages are sent to Action Bar as opposed to the Chat window
		FAMILIAR_ACTION_BAR_MESSAGES = false
		#Familiar chunk loads
		FAMILIAR_CHUNKLOADS = true

	[familiar."butterfly effect"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		BUTTERFLY_EFFECT_MANA_COST = 100
		#This is the limit on butterflies per block
		#Range: 0 ~ *********
		BUTTERFLY_EFFECT_BLOCK_LIMIT = 4
		#This is the limit on butterflies per chunk
		#Range: 0 ~ *********
		BUTTERFLY_EFFECT_CHUNK_LIMIT = 40
		#This is the how long the effect lasts
		#Range: 0 ~ *********
		BUTTERFLY_EFFECT_DURATION = 400
		#Whether the effect disappears after the first butterfly placed.
		BUTTERFLY_EFFECT_SINGLE_USE = false
		#Whether people other than the caster can see the butterfly effect
		BUTTERFLY_EFFECT_VISIBLE_TO_OTHERS = false

	[familiar."swap familiar"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		SWAP_FAMILIAR_MANA_COST = 40

	[familiar."familiars garden"]
		#This is the cost of using the scroll
		#Range: 0 ~ *********
		FAMILIARS_GARDEN_MANA_COST = 200
		#This is the range of the effect from the familiar
		#Range: 0 ~ *********
		FAMILIARS_GARDEN_RANGE = 7
		#This is how many ticks it lasts from the time of using the scroll.
		#Range: 0 ~ *********
		FAMILIARS_GARDEN_TIME = 1200

[secret]

	[secret.retribution]
		#Retribution Mana Per Difference
		#Range: 0 ~ *********
		RETRIBUTION_MANA_PER_DIFFERENCE = 40
		#Retribution Mana Charged Per Percent
		RETRIBUTION_MANA_CHARGED_PER_PERCENT = false
		#A list of entity registry names this spell won't work against
		RETRIBUTION_BLACKLIST = []

	[secret."presence concealment"]
		#Ripper Cooldown
		#Range: 0 ~ *********
		RIPPER_COOLDOWN = 800
		#Ripper Mana Cost
		#Range: 0 ~ *********
		RIPPER_MANA_COST = 800
		#Ripper Fog Range
		#Range: 0 ~ *********
		RIPPER_FOG_RANGE = 20
		#Ripper Fog Distance
		#Range: 0 ~ *********
		RIPPER_FOG_DISTANCE = 5
		#Ripper Invis Cooldown
		#Range: 0 ~ *********
		RIPPER_INVIS_COOLDOWN = 40
		#Ripper Gives Invis
		RIPPER_GIVES_INVIS = true
		#Ripper Speed
		#Range: -1000000.0 ~ 1000000.0
		RIPPER_SPEED = 3.0
		#Ripper Movement Speed
		#Range: -1.0E7 ~ 1000000.0
		RIPPER_MOVEMENT_SPEED = 0.05
		#Ripper Damage
		#Range: 0.0 ~ 1000000.0
		RIPPER_DAMAGE = 2.5
		#Ripper Damage Bonus From Behind
		#Range: 0.0 ~ 1000000.0
		RIPPER_DAMAGE_BONUS_FROM_BEHIND = 6.0
		#Ripper Damage Bonus Scales With Mana
		RIPPER_DAMAGE_BONUS_SCALES_WITH_MANA = false
		#Ripper Durability
		#Range: 0 ~ *********0
		RIPPER_DURABILITY = 1200
		#Ripper Fog Mana Cost
		#Range: 0 ~ 10000000
		RIPPER_FOG_MANA_COST = 200
		#Ripper Invis Mana Cost
		#Range: 0 ~ 10000000
		RIPPER_INVIS_MANA_COST = 0

	[secret.gandr]
		#Gandr Black Particles Spawned on Hit
		#Range: 0 ~ *********
		GANDR_BLACK_PARTICLES_HIT = 150
		#Gandr Red Particles Spawned on Hit
		#Range: 0 ~ *********
		GANDR_RED_PARTICLES_HIT = 40
		#Gandr Red Particles Spawned on Launch
		#Range: 0 ~ *********
		GANDR_RED_PARTICLES_LAUNCH = 25
		#Gandr Hit Radius
		#Range: 0.0 ~ 1.0E8
		GANDR_HIT_RADIUS = 6.0
		#Gandr Effect Cloud Duration
		#Range: 0 ~ *********
		GANDR_CLOUD_DURATION = 200
		#Gandr Mana Cost Scales with Max Mana
		GANDR_MANA_SCALES = true
		#Gandr Mana Cost Factor
		#Range: 0.0 ~ 1.0E8
		GANDR_MANA_COST_FACTOR = 0.05
		#Gandr Damage Scales with Max Mana
		GANDR_DAMAGE_SCALES = true
		#Gandr Damage Factor
		#Range: 0.0 ~ 1.0E8
		GANDR_DAMAGE_FACTOR = 0.009999999776482582
		#Gandr Max Damage
		#Range: 0.0 ~ 1.0E8
		GANDR_MAX_DAMAGE = 1000.0
		#Gandr Min Damage
		#Range: 0.0 ~ 1.0E8
		GANDR_MIN_DAMAGE = 5.0
		#Gandr Projectile Speed
		#Range: 0.0 ~ 1.0E8
		GANDR_SPEED = 1.0
		#Gandr Effect Blacklist
		GANDR_EFFECT_BLACKLIST = []
		#Gandr Effect Whitelist
		GANDR_EFFECT_WHITELIST = ["mahoutsukai:misfortune", "mahoutsukai:bound", "mahoutsukai:fear", "mahoutsukai:black_burning", "mahoutsukai:wounded", "mahoutsukai:confusion"]

	[secret."fallen down"]
		#Fallen Down Block Break Per Tick
		#Range: 0 ~ *********
		FALLEN_DOWN_BLOCK_BREAK_PER_TICK = 800
		#Fallen Down Radius
		#Range: 0 ~ *********
		FALLEN_DOWN_RADIUS = 30
		#Fallen Down Mana Per Tick
		#Range: 0 ~ *********
		FALLEN_DOWN_MANA_COST = 2000
		#Fallen Down Beam Mode Blocks In
		FALLEN_DOWN_BEAM_IN = ["minecraft:grass_block", "minecraft:sand", "minecraft:red_sand", "minecraft:cobblestone", "minecraft:clay", "minecraft:ice", "minecraft:packed_ice", "minecraft:blue_ice"]
		#Fallen Down Beam Mode Blocks Out
		FALLEN_DOWN_BEAM_OUT = ["minecraft:dirt", "minecraft:glass", "minecraft:red_stained_glass", "minecraft:stone", "minecraft:terracotta", "minecraft:water", "minecraft:water", "minecraft:water"]
		#Fallen Down Beam Damage
		#Range: 0.0 ~ 1.0E9
		FALLEN_DOWN_BEAM_DAMAGE = 2.0
		#Fallen Down Beam Health Percentage Damage
		#Range: 0.0 ~ 1.0E9
		FALLEN_DOWN_BEAM_TARGET_HEALTH_PERCENTAGE_DAMAGE = 0.05
		#Fallen Down Beam Growth
		#Range: 0.0 ~ 1.0E9
		FALLEN_DOWN_BEAM_GROWTH = 0.12
		#Fallen Down Beam Mana Scaled Damage
		#Range: 0.0 ~ 1.0E9
		FALLEN_DOWN_BEAM_MANA_SCALED_DAMAGE = 0.0

	[secret.geas]
		#Geas Mana Cost
		#Range: 0 ~ *********
		GEAS_MANA_COST = 30
		#Geas Mana Regen
		#Range: 0.0 ~ 1.0E8
		MANA_REGEN_PER_GEAS = 3.0
		#Geas Max Mana Regen
		#Range: 0.0 ~ 1.0E8
		GEAS_MAX_MANA_REGEN = 1.0E7
		#Geas Blacklist
		GEAS_BLACKLIST = ["minecraft:armor_stand"]

	[secret."rule breaker"]
		#Rule Breaker Durability
		#Range: 0 ~ 10000000
		RULE_BREAKER_DURABILITY = 40
		#Rule Breaker Duration
		#Range: 0 ~ 10000000
		RULE_BREAKER_DURATION = 800
		#Rule Breaker Mana Cost
		#Range: 0 ~ 10000000
		RULE_BREAKER_MANA_COST = 5000
		#Rule Breaker Range
		#Range: 0 ~ 10000000
		RULE_BREAKER_RANGE = 20
		#Rule Breaker Item Whitelist
		RULE_BREAKER_ITEM_WHITELIST = ["minecraft:chorus_fruit"]

	[secret."selective displacement"]
		#Selective Displacement Time
		#Range: 0 ~ 10000000
		SELECTIVE_DISPLACEMENT_TIME = 1200
		#Selective Displacement Range
		#Range: 0 ~ 10000000
		SELECTIVE_DISPLACEMENT_RANGE = 40
		#Selective Displacement Cooldown
		#Range: 0 ~ 10000000
		SELECTIVE_DISPLACEMENT_COOLDOWN = 60
		#Selective Displacement Mana Cost
		#Range: 0 ~ 10000000
		SELECTIVE_DISPLACEMENT_MANA_COST = 900

[kodoku]
	#Base chance of setting target on fire with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_FIRE_CHANCE = 0.1
	#Base chance of applying potion to target with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_SPLASH_CHANCE = 0.05
	#Base chance of making target jump with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_HOP_CHANCE = 0.08
	#Base chance of making target glow with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_GLOW_CHANCE = 0.02
	#Base chance of making target confused with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_CONFUSE_CHANCE = 0.01
	#Base chance of teleporting target with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_TELEPORT_CHANCE = 0.08
	#Base chance of lowering target hunger with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_HUNGER_CHANCE = 0.15
	#Base chance of lightning striking target with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_MISFORTUNE_LIGHTNING_CHANCE = 2.0E-5
	#Base chance of aggroing  with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_MISFORTUNE_AGGRO_CHANCE = 4.0E-4
	#Base chance of dropping a random item with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_MISFORTUNE_DROP_CHANCE = 7.142857142857143E-5
	#Base chance of tripping with kodoku value 1
	#Range: 0.0 ~ 1.0
	KODOKU_MISFORTUNE_TRIP_CHANCE = 4.0E-5
	#Kodoku value divided by this number, and that many drops are removed from drop tables
	#Range: 0 ~ *********
	KODOKU_LOOT_DIVISOR = 10
	#Kodoku value divided by this number, and that much extra damage applied to tools on use
	#Range: 0 ~ *********
	KODOKU_TOOL_BREAK_DIVISOR = 10
	#Multiplied by mob's health and added to kodoku value when killed
	#Range: -1.0E7 ~ 1.0E7
	KODOKU_HEALTH_FACTOR = 0.1
	#Multiplied by mob's armor and added to kodoku value when killed
	#Range: -1.0E7 ~ 1.0E7
	KODOKU_ARMOR_FACTOR = 0.0
	#Multiplied by (hostile) mob's health and added to kodoku value when killed
	#Range: -1.0E7 ~ 1.0E7
	KODOKU_HEALTH_FACTOR_MOB = 0.1
	#Multiplied by (hostile) mob's armor and added to kodoku value when killed
	#Range: -1.0E7 ~ 1.0E7
	KODOKU_ARMOR_FACTOR_MOB = 0.0

[mana]
	#How high a player's personal mana can go
	#Range: 0 ~ *********
	MAX_MANA_CAP = 200000
	#How much mana is spent before it increases
	#Range: 0 ~ *********
	MANA_UP_COUNTER = 100
	#Mana required for increase, as a percentage of the max mana
	#Range: 0.0 ~ 1.0E8
	MANA_UP_COUNTER_RATIO = 0.0
	#Maximum amount of mana required for increase
	#Range: 0 ~ *********
	MANA_UP_COUNTER_MAX = 100
	#How much mana regen scaled with max mana is added to player regen
	#Range: 0.0 ~ 1.0E7
	MANA_ADDED_SCALING_REGEN_PER_TICK = 0.0
	#How much max mana increases every time it does
	#Range: 0 ~ *********
	MANA_INCREASE = 1
	#How much mana players regen per tick
	#Range: 0 ~ *********
	MANA_REGEN_PER_TICK = 1
	#Percentage of max mana players get back by sleeping
	#Range: 0.0 ~ 1.0E8
	MANA_RECOVERY_SLEEP = 0.5
	#Scaling factor for how much hunger affects regen
	#Range: -1.0E7 ~ 1.0E7
	MANA_REGEN_HUNGER_FACTOR = 1.0
	#If true, mana cost of things is ignored in creative mode
	CREATIVE_IGNORES_MANA_COSTS = true
	#Apply logarithmic punishment to mana increase based on current max
	MANA_INCREASE_LOG_PUNISHMENT = false
	#Punishment factor for mana increase based on current max
	#Range: -1.0E7 ~ 1.0E7
	MANA_INCREASE_PUNISHMENT_FACTOR = 0.0

["mana storage"]
	#How much this circuit can hold
	#Range: 0 ~ *********
	MANA_CIRCUIT_MAGITECH_CAPACITY = 100000
	#How much this circuit can hold
	#Range: 0 ~ *********
	MANA_CIRCUIT_CAPACITY = 100000
	#How far away things can be from a Mana Circuit to use it
	#Range: 0 ~ *********
	MANA_CIRCUIT_RANGE = 10
	#How much mana fits into an attuned diamond
	#Range: 0 ~ *********
	ATTUNED_DIAMOND_CAPACITY = 10000
	#How much mana fits into an attuned emerald
	#Range: 0 ~ *********
	ATTUNED_EMERALD_CAPACITY = 5000

[projector]
	#A list of URLs to pull Magic Circle textures from
	MAGIC_CIRCLES = []
	#A list of URLs to pull Magic Runes textures from
	MAGIC_RUNES = []
	#Base rotation speed for projector
	#Range: 0.0 ~ 1000000.0
	PROJECTOR_DEFAULT_ROTATION_SPEED = 1.0
	#Stop spinning when powered off
	PROJECTOR_CONTINUES_ROTATE_WHEN_OFF = false
	#Projector Offset Cap
	#Range: -1.0E7 ~ 1.0E7
	PROJECTOR_OFFSET_CAP = 60.0

[fogprojector]
	#Fog Projector Max Radius
	#Range: -1.0E7 ~ 1.0E7
	FOG_PROJECTOR_MAX_RADIUS = 30.0

["mystic code"]
	#How much durability the Mystic Code - First Sorcery item has
	#Range: 0 ~ *********
	FIRST_SORCERY_DURABILITY = 50
	#Mystic Code First Sorcery Blacklist
	MYSTIC_CODE_FIRST_SORCERY_BLACKLIST = ["mahoutsukai:scroll_boundary_drain_life"]
	#Mystic Code Enchant Blacklist
	MYSTIC_CODE_ENCHANT_BLACKLIST = []

[other]
	#Bleeding only occurs when using dagger
	BLEEDING_FROM_DAGGER_ONLY = false
	#The block range value for any spells which use the player's line of sight
	#Range: 0 ~ *********
	GLOBAL_LOOK_RANGE = 100
	#If false, scrolls will not disappear in creative.
	CREATIVE_MODE_SPELLS = false
	#Should magic damage the player
	MAGIC_DAMAGES_PLAYER = false
	#If Magic damages the player, should it be flat damage or based on mana used
	MAGIC_DAMAGE_FLAT = true
	#If damage is flat, this value is dealt to player's health. If not flat, this value is multiplied by mana used and rounded up.
	#Range: 0.0 ~ 1.0E8
	MAGIC_DAMAGE_VALUE = 1.0
	#What type of damage magic damage inflicts.
	MAGIC_DAMAGE_GENERIC = true
	#If true, only allow blood circles on solid blocks
	BLOOD_CIRCLE_SOLID_ONLY = true
	#Spell Creation Blacklist
	SPELL_BLACKLIST = []
	#Chunk Packet Replies Enabled
	CHUNK_PACKET_REPLIES_ENABLED = true
	#Buff Clear Items
	BUFF_CLEAR_ITEMS = ["minecraft:milk_bucket"]
	#Gamestages Enabled
	GAMESTAGES_ENABLED = false
	#Armor Stands are immune to spells
	ARMOR_STANDS_IMMUNE = true
	#Mahou Resets After Death
	ONE_LIFE = false

