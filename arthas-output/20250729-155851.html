<!DOCTYPE html>
<html lang='en'>
<head>
<meta charset='utf-8'>
<style>
	body {margin: 0; padding: 10px 10px 22px 10px; background-color: #ffffff}
	h1 {margin: 5px 0 0 0; font-size: 18px; font-weight: normal; text-align: center}
	header {margin: -24px 0 5px 0; line-height: 24px}
	button {font: 12px sans-serif; cursor: pointer}
	p {position: fixed; bottom: 0; margin: 0; padding: 2px 3px 2px 3px; outline: 1px solid #ffc000; display: none; overflow: hidden; white-space: nowrap; background-color: #ffffe0}
	a {color: #0366d6}
	#hl {position: absolute; display: none; overflow: hidden; white-space: nowrap; pointer-events: none; background-color: #ffffe0; outline: 1px solid #ffc000; height: 15px}
	#hl span {padding: 0 3px 0 3px}
	#status {left: 0}
	#match {right: 0}
	#reset {cursor: pointer}
	#canvas {width: 100%; height: 1072px}
</style>
</head>
<body style='font: 12px Verdana, sans-serif'>
<h1>CPU profile</h1>
<header style='text-align: left'><button id='reverse' title='Reverse'>&#x1f53b;</button>&nbsp;&nbsp;<button id='search' title='Search'>&#x1f50d;</button></header>
<header style='text-align: right'>Produced by <a href='https://github.com/async-profiler/async-profiler'>async-profiler</a></header>
<canvas id='canvas'></canvas>
<div id='hl'><span></span></div>
<p id='status'></p>
<p id='match'>Matched: <span id='matchval'></span> <span id='reset' title='Clear'>&#x274c;</span></p>
<script>
	// Copyright The async-profiler authors
	// SPDX-License-Identifier: Apache-2.0
	'use strict';
	let root, px, pattern;
	let level0 = 0, left0 = 0, width0 = 0;
	let nav = [], navIndex, matchval;
	let reverse = false;
	const levels = Array(67);
	for (let h = 0; h < levels.length; h++) {
		levels[h] = [];
	}

	const canvas = document.getElementById('canvas');
	const c = canvas.getContext('2d');
	const hl = document.getElementById('hl');
	const status = document.getElementById('status');

	const canvasWidth = canvas.offsetWidth;
	const canvasHeight = canvas.offsetHeight;
	canvas.style.width = canvasWidth + 'px';
	canvas.width = canvasWidth * (devicePixelRatio || 1);
	canvas.height = canvasHeight * (devicePixelRatio || 1);
	if (devicePixelRatio) c.scale(devicePixelRatio, devicePixelRatio);
	c.font = document.body.style.font;

	const palette = [
		[0xb2e1b2, 20, 20, 20],
		[0x50e150, 30, 30, 30],
		[0x50cccc, 30, 30, 30],
		[0xe15a5a, 30, 40, 40],
		[0xc8c83c, 30, 30, 10],
		[0xe17d00, 30, 30,  0],
		[0xcce880, 20, 20, 20],
	];

	function getColor(p) {
		const v = Math.random();
		return '#' + (p[0] + ((p[1] * v) << 16 | (p[2] * v) << 8 | (p[3] * v))).toString(16);
	}

	function f(key, level, left, width, inln, c1, int) {
		levels[level0 = level].push({level, left: left0 += left, width: width0 = width || width0,
			color: getColor(palette[key & 7]), title: cpool[key >>> 3],
			details: (int ? ', int=' + int : '') + (c1 ? ', c1=' + c1 : '') + (inln ? ', inln=' + inln : '')
		});
	}

	function u(key, width, inln, c1, int) {
		f(key, level0 + 1, 0, width, inln, c1, int)
	}

	function n(key, width, inln, c1, int) {
		f(key, level0, width0, width, inln, c1, int)
	}

	function samples(n) {
		return n === 1 ? '1 sample' : n.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' samples';
	}

	function pct(a, b) {
		return a >= b ? '100' : (100 * a / b).toFixed(2);
	}

	function findFrame(frames, x) {
		let left = 0;
		let right = frames.length - 1;

		while (left <= right) {
			const mid = (left + right) >>> 1;
			const f = frames[mid];

			if (f.left > x) {
				right = mid - 1;
			} else if (f.left + f.width <= x) {
				left = mid + 1;
			} else {
				return f;
			}
		}

		if (frames[left] && (frames[left].left - x) * px < 0.5) return frames[left];
		if (frames[right] && (x - (frames[right].left + frames[right].width)) * px < 0.5) return frames[right];

		return null;
	}

	function removeStack(left, width) {
		for (let h = 0; h < levels.length; h++) {
			const frames = levels[h], newFrames = [];
			for (let i = 0; i < frames.length; i++) {
				const f = frames[i];
				if (f.left >= left + width) {
					f.left -= width;
				} else if (f.left + f.width > left) {
					if ((f.width -= width) <= 0 && h) continue;
				}
				newFrames.push(f);
			}
			levels[h] = newFrames;
		}
	}

	function search(r) {
		if (r === true && (r = prompt('Enter regexp to search:', '')) === null) {
			return;
		}

		pattern = r ? RegExp(r) : undefined;
		const matched = render(root, nav = []);
		navIndex = -1;
		document.getElementById('matchval').textContent = matchval = pct(matched, root.width) + '%';
		document.getElementById('match').style.display = r ? 'inline-block' : 'none';
	}

	function render(newRoot, nav) {
		if (root) {
			c.fillStyle = '#ffffff';
			c.fillRect(0, 0, canvasWidth, canvasHeight);
		}

		root = newRoot || levels[0][0];
		px = canvasWidth / root.width;

		const x0 = root.left;
		const x1 = x0 + root.width;
		const marked = [];

		function mark(f) {
			return marked[f.left] || (marked[f.left] = f);
		}

		function totalMarked() {
			let total = 0;
			let left = 0;
			Object.keys(marked).sort(function(a, b) { return a - b; }).forEach(function(x) {
				if (+x >= left) {
					const m = marked[x];
					if (nav) nav.push(m);
					total += m.width;
					left = +x + m.width;
				}
			});
			return total;
		}

		function drawFrame(f, y) {
			if (f.left < x1 && f.left + f.width > x0) {
				c.fillStyle = pattern && f.title.match(pattern) && mark(f) ? '#ee00ee' : f.color;
				c.fillRect((f.left - x0) * px, y, f.width * px, 15);

				if (f.width * px >= 21) {
					const chars = Math.floor(f.width * px / 7);
					const title = f.title.length <= chars ? f.title : f.title.substring(0, chars - 2) + '..';
					c.fillStyle = '#000000';
					c.fillText(title, Math.max(f.left - x0, 0) * px + 3, y + 12, f.width * px - 6);
				}

				if (f.level < root.level) {
					c.fillStyle = 'rgba(255, 255, 255, 0.5)';
					c.fillRect((f.left - x0) * px, y, f.width * px, 15);
				}
			}
		}

		for (let h = 0; h < levels.length; h++) {
			const y = reverse ? h * 16 : canvasHeight - (h + 1) * 16;
			const frames = levels[h];
			for (let i = 0; i < frames.length; i++) {
				drawFrame(frames[i], y);
			}
		}

		return totalMarked();
	}

	function unpack(cpool) {
		for (let i = 1; i < cpool.length; i++) {
			cpool[i] = cpool[i - 1].substring(0, cpool[i].charCodeAt(0) - 32) + cpool[i].substring(1);
		}
	}

	canvas.onmousemove = function() {
		const h = Math.floor((reverse ? event.offsetY : (canvasHeight - event.offsetY)) / 16);
		if (h >= 0 && h < levels.length) {
			const f = findFrame(levels[h], event.offsetX / px + root.left);
			if (f) {
				if (f !== root) getSelection().removeAllRanges();
				hl.style.left = (Math.max(f.left - root.left, 0) * px + canvas.offsetLeft) + 'px';
				hl.style.width = (Math.min(f.width, root.width) * px) + 'px';
				hl.style.top = ((reverse ? h * 16 : canvasHeight - (h + 1) * 16) + canvas.offsetTop) + 'px';
				hl.firstChild.textContent = f.title;
				hl.style.display = 'block';
				canvas.title = f.title + '\n(' + samples(f.width) + f.details + ', ' + pct(f.width, levels[0][0].width) + '%)';
				canvas.style.cursor = 'pointer';
				canvas.onclick = function() {
					if (event.altKey && h >= root.level) {
						removeStack(f.left, f.width);
						root.width > f.width ? render(root) : render();
					} else if (f !== root) {
						render(f);
					}
					canvas.onmousemove();
				};
				status.textContent = 'Function: ' + canvas.title;
				status.style.display = 'inline-block';
				return;
			}
		}
		canvas.onmouseout();
	}

	canvas.onmouseout = function() {
		hl.style.display = 'none';
		status.style.display = 'none';
		canvas.title = '';
		canvas.style.cursor = '';
		canvas.onclick = null;
	}

	canvas.ondblclick = function() {
		getSelection().selectAllChildren(hl);
	}

	document.getElementById('reverse').onclick = function() {
		reverse = !reverse;
		render();
	}

	document.getElementById('search').onclick = function() {
		search(true);
	}

	document.getElementById('reset').onclick = function() {
		search(false);
	}

	window.onkeydown = function(event) {
		if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
			event.preventDefault();
			search(true);
		} else if (event.key === 'Escape') {
			search(false);
		} else if ((event.key === 'n' || event.key === 'N') && nav.length > 0) {
			navIndex = (navIndex + (event.shiftKey ? nav.length - 1 : 1)) % nav.length;
			render(nav[navIndex]);
			document.getElementById('matchval').textContent = matchval + ' (' + (navIndex + 1) + ' of ' + nav.length + ')';
			window.scroll(0, reverse ? root.level * 16 : canvasHeight - (root.level + 1) * 16);
			canvas.onmousemove();
		}
	}

const cpool = [
'all',
' /root/server/main/plugins/RoseGarden/tmp/sqlite-3.42.0.0-24bb1e29-8eb6-41ca-8532-9892337c342c-libsqlitejdbc.so',
'!usr/lib/x86_64-linux-gnu/libc.so.6',
' ConcurrentGCThread::run',
' G1CardSet::add_card',
'#oncurrentRefineThread::run_service',
'"DirtyCardQueueSet::refine_buffer',
'<completed_buffer_concurrently',
'"PrimaryConcurrentRefineThread::do_refinement_step',
'"RemSet::refine_card_concurrently',
' I2C/C2I adapters',
' Java_com_sun_management_internal_OperatingSystemImpl_getSingleCpuLoad0',
' Thread::call_run',
' [unknown]',
'(_Java]',
' __mark_inode_dirty',
'"rcu_read_lock',
'"x64_sys_fchown',
' appeng/me/Grid.saveNodeData',
'.Node.saveToNBT',
'*ManagedGridNode.saveToNBT',
' chown_common',
'!om/gregtechceu/gtceu/api/blockentity/MetaMachineBlockEntity.m_142466_',
':machine/IMachineBlockEntity.loadCustomPersistedData',
'BMachineCoverContainer.onCoverDirty',
'CetaMachine.loadCustomPersistedData',
'Btrait/MachineTrait.getSyncStorage',
':recipe/content/IContentSerializer.fromNbt',
'Aingredient/FluidIngredient.equals',
'6integration/ae2/machine/trait/GridNodeHolder.serializeGridNode',
'Futils/SerializableManagedGridNode.serializeNBT',
'$lowdragmc/lowdraglib/async/AsyncThreadData$$Lambda.0x0000000088622b40.run',
'N.searchingTask',
'9syncdata/ManagedFieldUtils$$Lambda.0x000000008861e7e8.accept',
'S.lambda$getFieldRefs$1',
'BSyncUtils.isChanged',
'Bblockentity/IAsyncAutoSyncBlockEntity.asyncTick',
'PutoPersistBlockEntity.loadManagedPersistentData',
'OManagedBlockEntity.getNonLazyFields',
'Bfield/FieldManagedStorage$$Lambda.0x000000008861a1d8.onFieldChanged',
'[.getNonLazyFields',
'\\init',
'\\lambda$init$1',
'Bmanaged/ManagedArrayLikeRef.checkArrayLikeChanges',
'^update',
'QField$Boolean.booleanValue',
'V.value',
'QRef$BooleanRef.update',
'KultiManagedStorage.hasDirtySyncFields',
'JReadOnlyManagedField.isDirty',
'_serializeUid',
'YRef.update',
'NonlyArrayRef.update',
'RRef.markAsDirty',
'Vupdate',
'JSimpleObjectRef.update',
'$mojang/datafixers/util/Either$$Lambda.0x00000000885b9470.apply',
'BLeft.map',
'A.lambda$mapLeft$0',
'BmapLeft',
'+serialization/DynamicOps.convertMap',
'9JsonOps.createMap',
'$sun/management/OperatingSystemMXBean.getSystemCpuLoad',
'3internal/OperatingSystemImpl$ContainerCpuTicks.getContainerCpuLoad',
'PSystemCpuTicks$$Lambda.0x00000000875de0d0.applyAsDouble',
'^.lambda$cpuSetCalc$1',
'O.getCpuLoad',
'SProcessCpuLoad',
'SSingleCpuLoad0',
' do_syscall_64',
' entry_SYSCALL_64_after_hwframe',
'!xt4_setattr',
' fchown',
'!scanf',
' get_cpuload_internal',
' it/unimi/dsi/fastutil/objects/Object2IntOpenHashMap.find',
'Tput',
'"able stub',
' java/io/FileCleanable.<init>',
'6register',
',InputStream.<init>',
',Reader.<init>',
'%lang/Thread.run',
'4With',
'*invoke/Invokers$Holder.invokeExact_MT',
'1LambdaForm$DMH.0x00000000801c1c00.invokeVirtual',
'Me9c00.invokeSpecial',
'<MH.0x000000008869d000.invoke',
'K72c400.invoke',
'Lb1c00.invoke',
'*ref/PhantomReference.<init>',
'.Reference.<init>',
'-lect/Field.get',
';Boolean',
'2Method.invoke',
'%security/AccessController.doPrivileged',
'?executePrivileged',
'&ql/DriverManager.getConnection',
'%util/AbstractCollection.containsAll',
'2Map.equals',
'*BitSet.set',
'*HashMap$KeySpliterator.forEachRemaining',
'1.get',
'5Node',
'*Objects.equals',
'+ptional.map',
'*concurrent/CompletableFuture$AsyncSupply.run',
'GCompletion.run',
'GUniApply.tryFire',
'5Executors$RunnableAdapter.call',
'5FutureTask.runAndReset',
'5ScheduledThreadPoolExecutor$ScheduledFutureTask.run',
'5ThreadPoolExecutor$Worker.run',
'G.runWorker',
'5locks/ReentrantLock.lock',
'*stream/AbstractPipeline.copyInto',
'Bevaluate',
'BwrapAndCopyInto',
'1ForEachOps$ForEachOp$OfRef.evaluateSequential',
'E.evaluateSequential',
'1ReferencePipeline$3$1.accept',
'B.forEach',
'!dk/internal/platform/CgroupMetrics.getCpuQuota',
'<SubsystemController.getStringValue',
'<Util$$Lambda.0x00000000875dda70.run',
'@.lambda$readStringValue$0',
'AreadStringValue',
'6cgroupv2/CgroupV2Subsystem.getCpuQuota',
'TFromCpuMax',
'-ref/PhantomCleanable.<init>',
'0lect/DirectMethodHandleAccessor.invoke',
'VImpl',
'5MethodHandleObjectFieldAccessorImpl.get',
' locked_inode_to_wb_and_lock_list',
' net/coreprotect/consumer/Consumer.run',
'9process/Process.processConsumer',
'0database/Database.getConnection',
'BperformCheckpoint',
'$minecraft/nbt/CompoundTag.equals',
'2ListTag.m_128728_',
'2NbtOps.convertTo',
'.server/MinecraftServer$$Lambda.0x000000008769f3c0.run',
'D.executeModerately',
'Em_129940_',
'K61_',
'H30006_',
'K11_',
'G206580_',
'G7245_',
'5dedicated/DedicatedServer.m_7038_',
'5level/ChunkMap$$Lambda.0x00000000885e8b28.apply',
'Z9190.apply',
'C.m_214854_',
'G87044_',
'@TaskPriorityQueueSorter$$Lambda.0x00000000885db8e8.run',
'W.m_143188_',
';ServerChunkCache$$Lambda.0x00000000887403f0.get',
'LMainThreadExecutor.m_6367_',
'a7245_',
'K.m_7587_',
'N8466_',
'P75_',
'.util/thread/BlockableEventLoop.m_18699_',
'Q701_',
'O6367_',
'O7245_',
'.world/item/ItemStack.m_150942_',
'E41728_',
'4level/block/entity/BlockEntity$$Lambda.0x00000000885ecd68.apply',
'R.handler$zcb000$injectLoad',
'Sm_142466_',
'V55241_',
'Z6_',
':chunk/LevelChunk.m_62952_',
'@storage/ChunkSerializer$$Lambda.0x00000000885adc20.m_196866_',
'W.m_196900_',
'-forge/common/capabilities/CapabilityProvider.areCapsCompatible',
'!otify_change',
' org/sqlite/JDBC.connect',
'1reateConnection',
'+SQLiteConfig.apply',
'4nection.<init>',
'+core/DB.prepare',
'0NativeDB._exec',
'>_utf8',
'9prepare',
'@_utf8',
'+jdbc3/JDBC3Connection.<init>',
'6Statement$$Lambda.0x0000000087d3a330.call',
'S819f070.call',
'?.execute',
'GLargeUpdate',
'GUpdate',
'@lambda$execute$0',
'NLargeUpdate$2',
'@withConnectionTimeout',
'/4/JDBC4Connection.<init>',
' thread_native_entry',
' vfs_fchown',
'!oid G1CardSet::iterate_cards_during_transfer<G1TransferCard>',
'%OopOopIterateDispatch<G1ConcurrentRefineOopClosure>::Table::oop_oop_iterate<InstanceKlass, oopDesc*>',
' xyz/jpenilla/tabtps/common/util/CPUMonitor$$Lambda.0x0000000088344d98.run',
'J.currentProcessCpuLoad',
'RSystemCpuLoad',
'KrecordUsage'
];
unpack(cpool);

n(3,2192)
u(19,5)
u(1580)
u(100)
u(28)
u(44)
u(68)
u(60)
u(52)
u(76)
f(1604,10,2,3)
f(36,11,1,2)
f(1596,12,1,1)
u(36)
f(115,1,1,15)
u(225,14)
n(697,1)
f(657,1,1,2172)
u(665)
u(897,57)
u(905)
u(889)
u(881)
u(873)
u(249,54)
u(257)
u(289)
f(305,11,9,1)
n(353)
u(346)
f(386,11,1)
n(433,41)
f(209,12,20,1)
n(321,4)
u(329)
f(914,14,1,3)
f(377,12,3,1)
u(361)
u(746)
f(409,12,1,9)
f(393,13,3,2)
u(754,2,1,0,0)
f(1041,15,1,1)
u(1049)
u(673)
u(713)
u(689)
u(193)
u(441)
u(281)
u(1337)
u(1329)
u(1409)
u(1409)
f(401,13,1)
u(753)
u(1041)
u(1049)
u(673)
u(705)
u(681)
u(233)
u(241)
u(161)
u(153)
u(145)
f(1106,13,1,3,2,0,0)
u(834,3,2,0,0)
f(793,15,2,1)
u(817)
u(825)
f(417,12,1,2)
f(434,13,1,1)
f(425,12,1)
u(266)
u(274)
u(314)
u(338)
u(802)
u(802)
f(433,12,1,2)
n(441,1)
f(441,11,1)
u(370)
u(738)
u(1058)
u(674)
f(1609,8,1,3)
u(1633)
u(1617,1)
u(537)
u(505)
u(977)
u(1017)
u(1025)
u(985)
u(1009)
u(761)
u(769)
u(993)
u(1001)
u(649)
u(641)
u(642)
u(634)
u(626)
u(1034)
u(722)
u(730)
f(1625,10,1,2)
u(497)
u(529)
u(505)
u(513)
u(521)
u(545)
u(91)
u(595)
u(587)
u(19)
f(1073,3,2)
u(1081)
u(1089,1)
u(1089)
u(777)
u(777)
u(1425)
u(1433)
u(1569)
u(1497)
u(1449)
u(1441)
u(1521)
u(1561)
u(1505)
u(1545)
u(1457)
u(1481)
u(1489)
u(107)
u(107)
u(11)
f(1097,5,1)
u(1537)
u(1529)
u(1561)
u(1513)
u(1553)
u(1465)
u(1473)
u(579)
u(565)
u(557)
u(141)
u(1589)
u(173)
u(1421)
u(573)
u(125)
u(1069)
u(133)
f(1129,3,1,2113)
u(1177)
u(1169)
u(1193)
u(1161)
u(1145)
u(1137)
u(1297)
u(1185)
u(1153)
u(1281)
u(1265)
u(1321)
u(1257)
u(1313)
u(849)
u(1249)
u(1289)
u(1273)
u(1305)
u(1265)
u(1321)
u(1257)
u(1313)
u(1233)
u(1241)
u(857)
u(865)
u(1201)
u(1225)
u(473)
u(457)
u(449)
u(465)
u(1209)
u(1217)
u(1385)
u(1393)
u(1401)
u(1369)
u(841)
u(1345)
u(1377)
u(177)
u(1361)
u(1353)
u(297)
u(185)
u(201)
u(107)
u(83,1)
n(217)
u(1121)
u(481)
u(489)
u(489)
u(969)
u(929)
u(945)
u(953)
u(937)
u(921)
u(809)
u(961)
u(619)
f(609,53,1,2110)
u(601)
f(225,55,33,2077)
f(786,56,1874,203)
f(1113,53,203,1)

search();
</script></body></html>
