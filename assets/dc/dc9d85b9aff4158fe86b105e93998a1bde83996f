# (c) 2023 SpigotMC Pty. Ltd.
aa net/minecraft/SharedConstants
aaa net/minecraft/network/protocol/game/PacketPlayInPickItem
aab net/minecraft/network/protocol/game/PacketPlayInAutoRecipe
aac net/minecraft/network/protocol/game/PacketPlayInAbilities
aad net/minecraft/network/protocol/game/PacketPlayInBlockDig
aad$a net/minecraft/network/protocol/game/PacketPlayInBlockDig$EnumPlayerDigType
aae net/minecraft/network/protocol/game/PacketPlayInEntityAction
aae$a net/minecraft/network/protocol/game/PacketPlayInEntityAction$EnumPlayerAction
aaf net/minecraft/network/protocol/game/PacketPlayInSteerVehicle
aag net/minecraft/network/protocol/game/ServerboundPongPacket
aah net/minecraft/network/protocol/game/PacketPlayInRecipeSettings
aai net/minecraft/network/protocol/game/PacketPlayInRecipeDisplayed
aaj net/minecraft/network/protocol/game/PacketPlayInItemName
aak net/minecraft/network/protocol/game/PacketPlayInResourcePackStatus
aak$a net/minecraft/network/protocol/game/PacketPlayInResourcePackStatus$EnumResourcePackStatus
aal net/minecraft/network/protocol/game/PacketPlayInAdvancements
aal$a net/minecraft/network/protocol/game/PacketPlayInAdvancements$Status
aam net/minecraft/network/protocol/game/PacketPlayInTrSel
aan net/minecraft/network/protocol/game/PacketPlayInBeacon
aao net/minecraft/network/protocol/game/PacketPlayInHeldItemSlot
aap net/minecraft/network/protocol/game/PacketPlayInSetCommandBlock
aaq net/minecraft/network/protocol/game/PacketPlayInSetCommandMinecart
aar net/minecraft/network/protocol/game/PacketPlayInSetCreativeSlot
aas net/minecraft/network/protocol/game/PacketPlayInSetJigsaw
aat net/minecraft/network/protocol/game/PacketPlayInStruct
aau net/minecraft/network/protocol/game/PacketPlayInUpdateSign
aav net/minecraft/network/protocol/game/PacketPlayInArmAnimation
aaw net/minecraft/network/protocol/game/PacketPlayInSpectate
aax net/minecraft/network/protocol/game/PacketPlayInUseItem
aay net/minecraft/network/protocol/game/PacketPlayInBlockPlace
aaz net/minecraft/network/protocol/game/VecDeltaCodec
ab net/minecraft/SystemReport
abb net/minecraft/network/protocol/handshake/PacketHandshakingInSetProtocol
abc net/minecraft/network/protocol/handshake/PacketHandshakingInListener
abe net/minecraft/network/protocol/login/PacketLoginOutListener
abf net/minecraft/network/protocol/login/PacketLoginOutCustomPayload
abg net/minecraft/network/protocol/login/PacketLoginOutSuccess
abh net/minecraft/network/protocol/login/PacketLoginOutEncryptionBegin
abi net/minecraft/network/protocol/login/PacketLoginOutSetCompression
abj net/minecraft/network/protocol/login/PacketLoginOutDisconnect
abk net/minecraft/network/protocol/login/PacketLoginInListener
abl net/minecraft/network/protocol/login/PacketLoginInCustomPayload
abm net/minecraft/network/protocol/login/PacketLoginInStart
abn net/minecraft/network/protocol/login/PacketLoginInEncryptionBegin
abq net/minecraft/network/protocol/status/PacketStatusOutListener
abr net/minecraft/network/protocol/status/PacketStatusOutPong
abs net/minecraft/network/protocol/status/PacketStatusOutServerInfo
abt net/minecraft/network/protocol/status/ServerPing
abt$b net/minecraft/network/protocol/status/ServerPing$ServerPingPlayerSample
abt$c net/minecraft/network/protocol/status/ServerPing$ServerData
abu net/minecraft/network/protocol/status/PacketStatusInListener
abv net/minecraft/network/protocol/status/PacketStatusInPing
abw net/minecraft/network/protocol/status/PacketStatusInStart
aby net/minecraft/network/syncher/DataWatcherObject
abz net/minecraft/network/syncher/DataWatcherSerializer
ac net/minecraft/SystemUtils
ac$a net/minecraft/SystemUtils$IdentityHashingStrategy
ac$b net/minecraft/SystemUtils$OS
aca net/minecraft/network/syncher/DataWatcherRegistry
acb net/minecraft/network/syncher/DataWatcher
acb$a net/minecraft/network/syncher/DataWatcher$Item
acf net/minecraft/recipebook/AutoRecipeAbstract
acg net/minecraft/recipebook/AutoRecipe
aci net/minecraft/resources/DynamicOpsWrapper
acj net/minecraft/resources/FileToIdConverter
ack net/minecraft/resources/HolderSetCodec
acl net/minecraft/resources/RegistryDataLoader
acm net/minecraft/resources/RegistryFileCodec
acn net/minecraft/resources/RegistryFixedCodec
aco net/minecraft/resources/RegistryOps
acp net/minecraft/resources/ResourceKey
acq net/minecraft/resources/MinecraftKey
acs net/minecraft/server/DispenserRegistry
acu net/minecraft/server/ServerCommand
acv net/minecraft/server/DebugOutputStream
acw net/minecraft/server/EULA
acx net/minecraft/server/RedirectStream
acy net/minecraft/server/AdvancementDataPlayer
acz net/minecraft/server/RegistryLayer
ad net/minecraft/WorldVersion
ada net/minecraft/server/DataPackResources
adb net/minecraft/server/CancelledPacketHandleException
adc net/minecraft/server/AdvancementDataWorld
add net/minecraft/server/CustomFunctionManager
ade net/minecraft/server/CustomFunctionData
ade$a net/minecraft/server/CustomFunctionData$ExecutionContext
ade$a$a net/minecraft/server/CustomFunctionData$ExecutionContext$AbortingReturnValueConsumer
ade$b net/minecraft/server/CustomFunctionData$QueuedCommand
ade$c net/minecraft/server/CustomFunctionData$TraceCallbacks
adf net/minecraft/server/IMinecraftServer
adg net/minecraft/server/ScoreboardServer
adg$a net/minecraft/server/ScoreboardServer$Action
adh net/minecraft/server/Services
adi net/minecraft/server/TickTask
adj net/minecraft/server/WorldLoader
adk net/minecraft/server/WorldStem
adl net/minecraft/server/advancements/AdvancementVisibilityEvaluator
adn net/minecraft/server/bossevents/BossBattleCustom
ado net/minecraft/server/bossevents/BossBattleCustomData
adt net/minecraft/server/commands/CommandAdvancement
adt$a net/minecraft/server/commands/CommandAdvancement$Action
adt$b net/minecraft/server/commands/CommandAdvancement$Filter
adu net/minecraft/server/commands/CommandAttribute
adv net/minecraft/server/commands/CommandBanIp
adw net/minecraft/server/commands/CommandBanList
adx net/minecraft/server/commands/CommandBan
ady net/minecraft/server/commands/CommandBossBar
ae net/minecraft/advancements/Advancement
ae$a net/minecraft/advancements/Advancement$SerializedAdvancement
aea net/minecraft/server/commands/CommandClear
aeb net/minecraft/server/commands/CommandClone
aeb$a net/minecraft/server/commands/CommandClone$CommandCloneStoredTileEntity
aeb$d net/minecraft/server/commands/CommandClone$Mode
aec net/minecraft/server/commands/DamageCommand
aed net/minecraft/server/commands/CommandDatapack
aee net/minecraft/server/commands/CommandDeop
aef net/minecraft/server/commands/CommandDebug
aei net/minecraft/server/commands/CommandGamemodeDefault
aej net/minecraft/server/commands/CommandDifficulty
aek net/minecraft/server/commands/CommandEffect
ael net/minecraft/server/commands/CommandMe
aem net/minecraft/server/commands/CommandEnchant
aen net/minecraft/server/commands/CommandExecute
aeo net/minecraft/server/commands/CommandXp
aeo$a net/minecraft/server/commands/CommandXp$Unit
aep net/minecraft/server/commands/FillBiomeCommand
aeq net/minecraft/server/commands/CommandFill
aeq$a net/minecraft/server/commands/CommandFill$Mode
aer net/minecraft/server/commands/CommandForceload
aes net/minecraft/server/commands/CommandFunction
aet net/minecraft/server/commands/CommandGamemode
aeu net/minecraft/server/commands/CommandGamerule
aev net/minecraft/server/commands/CommandGive
aew net/minecraft/server/commands/CommandHelp
aex net/minecraft/server/commands/ItemCommands
aey net/minecraft/server/commands/JfrCommand
aez net/minecraft/server/commands/CommandKick
af net/minecraft/advancements/Advancements
afa net/minecraft/server/commands/CommandKill
afb net/minecraft/server/commands/CommandList
afc net/minecraft/server/commands/CommandLocate
afd net/minecraft/server/commands/CommandLoot
afe net/minecraft/server/commands/CommandTell
aff net/minecraft/server/commands/CommandOp
afg net/minecraft/server/commands/CommandPardon
afh net/minecraft/server/commands/CommandPardonIP
afi net/minecraft/server/commands/CommandParticle
afj net/minecraft/server/commands/PerfCommand
afk net/minecraft/server/commands/PlaceCommand
afl net/minecraft/server/commands/CommandPlaySound
afm net/minecraft/server/commands/CommandPublish
afo net/minecraft/server/commands/CommandRecipe
afp net/minecraft/server/commands/CommandReload
afr net/minecraft/server/commands/ReturnCommand
afs net/minecraft/server/commands/RideCommand
aft net/minecraft/server/commands/CommandSaveAll
afu net/minecraft/server/commands/CommandSaveOff
afv net/minecraft/server/commands/CommandSaveOn
afw net/minecraft/server/commands/CommandSay
afx net/minecraft/server/commands/CommandSchedule
afy net/minecraft/server/commands/CommandScoreboard
afz net/minecraft/server/commands/CommandSeed
ag net/minecraft/advancements/AdvancementProgress
aga net/minecraft/server/commands/CommandSetBlock
aga$a net/minecraft/server/commands/CommandSetBlock$Filter
aga$b net/minecraft/server/commands/CommandSetBlock$Mode
agb net/minecraft/server/commands/CommandIdleTimeout
agc net/minecraft/server/commands/CommandSpawnpoint
agd net/minecraft/server/commands/CommandSetWorldSpawn
age net/minecraft/server/commands/SpawnArmorTrimsCommand
agf net/minecraft/server/commands/CommandSpectate
agg net/minecraft/server/commands/CommandSpreadPlayers
agh net/minecraft/server/commands/CommandStop
agi net/minecraft/server/commands/CommandStopSound
agj net/minecraft/server/commands/CommandSummon
agk net/minecraft/server/commands/CommandTag
agl net/minecraft/server/commands/CommandTeam
agm net/minecraft/server/commands/CommandTeamMsg
agn net/minecraft/server/commands/CommandTeleport
ago net/minecraft/server/commands/CommandTellRaw
agp net/minecraft/server/commands/CommandTime
agq net/minecraft/server/commands/CommandTitle
agr net/minecraft/server/commands/CommandTrigger
agt net/minecraft/server/commands/CommandWeather
agu net/minecraft/server/commands/CommandWhitelist
agv net/minecraft/server/commands/CommandWorldBorder
agw net/minecraft/server/commands/data/CommandDataAccessorTile
agx net/minecraft/server/commands/data/CommandDataAccessor
agy net/minecraft/server/commands/data/CommandData
agz net/minecraft/server/commands/data/CommandDataAccessorEntity
ah net/minecraft/advancements/AdvancementRewards
aha net/minecraft/server/commands/data/CommandDataStorage
ahd net/minecraft/server/dedicated/DedicatedPlayerList
ahe net/minecraft/server/dedicated/DedicatedServer
ahf net/minecraft/server/dedicated/DedicatedServerProperties
ahf$a net/minecraft/server/dedicated/DedicatedServerProperties$WorldDimensionData
ahg net/minecraft/server/dedicated/DedicatedServerSettings
ahh net/minecraft/server/dedicated/ThreadWatchdog
ahi net/minecraft/server/dedicated/PropertyManager
ahi$a net/minecraft/server/dedicated/PropertyManager$EditableProperty
ahk net/minecraft/server/gui/ServerGUI
ahl net/minecraft/server/gui/PlayerListBox
ahm net/minecraft/server/gui/GuiStatsComponent
ahp net/minecraft/server/level/PlayerChunk
ahp$a net/minecraft/server/level/PlayerChunk$Failure
ahq net/minecraft/server/level/ChunkLevel
ahr net/minecraft/server/level/PlayerChunkMap
ahr$b net/minecraft/server/level/PlayerChunkMap$EntityTracker
ahs net/minecraft/server/level/ChunkTaskQueue
aht net/minecraft/server/level/ChunkTaskQueueSorter
ahu net/minecraft/server/level/ChunkMap
ahv net/minecraft/server/level/BlockPosition2D
ahw net/minecraft/server/level/DemoPlayerInteractManager
ahx net/minecraft/server/level/ChunkMapDistance
ahy net/minecraft/server/level/FullChunkStatus
ahz net/minecraft/server/level/PlayerMap
ai net/minecraft/advancements/CriterionTriggers
aia net/minecraft/server/level/WorldProviderNormal
aib net/minecraft/server/level/LightEngineGraphSection
aic net/minecraft/server/level/BossBattleServer
aid net/minecraft/server/level/ChunkProviderServer
aie net/minecraft/server/level/EntityTrackerEntry
aif net/minecraft/server/level/WorldServer
aig net/minecraft/server/level/EntityPlayer
aih net/minecraft/server/level/PlayerInteractManager
aii net/minecraft/server/level/LightEngineThreaded
aii$a net/minecraft/server/level/LightEngineThreaded$Update
aij net/minecraft/server/level/Ticket
aik net/minecraft/server/level/TicketType
ail net/minecraft/server/level/TickingTracker
aim net/minecraft/server/level/RegionLimitedWorldAccess
aio net/minecraft/server/level/progress/WorldLoadListener
aip net/minecraft/server/level/progress/WorldLoadListenerFactory
aiq net/minecraft/server/level/progress/WorldLoadListenerLogger
aiu net/minecraft/server/network/FilteredText
aiv net/minecraft/server/network/LegacyPingHandler
aiw net/minecraft/server/network/MemoryServerHandshakePacketListenerImpl
aix net/minecraft/server/network/ServerConnection
aix$a net/minecraft/server/network/ServerConnection$LatencySimulator
aix$a$a net/minecraft/server/network/ServerConnection$LatencySimulator$DelayedMessage
aiy net/minecraft/server/network/PlayerConnection
aiz net/minecraft/server/network/HandshakeListener
aj net/minecraft/advancements/Criterion
aja net/minecraft/server/network/LoginListener
aja$a net/minecraft/server/network/LoginListener$EnumProtocolState
ajb net/minecraft/server/network/ServerPlayerConnection
ajc net/minecraft/server/network/PacketStatusListener
ajd net/minecraft/server/network/ITextFilter
aje net/minecraft/server/network/TextFilter
ajh net/minecraft/server/packs/ResourcePackAbstract
aji net/minecraft/server/packs/BuiltInMetadata
ajj net/minecraft/server/packs/FeatureFlagsMetadataSection
ajk net/minecraft/server/packs/ResourcePackFile
ajl net/minecraft/server/packs/IResourcePack
ajm net/minecraft/server/packs/EnumResourcePackType
ajn net/minecraft/server/packs/PathPackResources
ajo net/minecraft/server/packs/ResourcePackVanilla
ajp net/minecraft/server/packs/VanillaPackResourcesBuilder
ajq net/minecraft/server/packs/linkfs/DummyFileAttributes
ajr net/minecraft/server/packs/linkfs/LinkFSFileStore
ajs net/minecraft/server/packs/linkfs/LinkFSPath
ajt net/minecraft/server/packs/linkfs/LinkFSProvider
aju net/minecraft/server/packs/linkfs/LinkFileSystem
ajv net/minecraft/server/packs/linkfs/PathContents
ajx net/minecraft/server/packs/metadata/ResourcePackMetaParser
ajy net/minecraft/server/packs/metadata/MetadataSectionType
ajz net/minecraft/server/packs/metadata/pack/ResourcePackInfo
ak net/minecraft/advancements/CriterionProgress
aka net/minecraft/server/packs/metadata/pack/ResourcePackInfoDeserializer
ake net/minecraft/server/packs/repository/BuiltInPackSource
akf net/minecraft/server/packs/repository/ResourcePackSourceFolder
akg net/minecraft/server/packs/repository/ResourcePackLoader
akg$b net/minecraft/server/packs/repository/ResourcePackLoader$Position
akh net/minecraft/server/packs/repository/EnumResourcePackVersion
aki net/minecraft/server/packs/repository/ResourcePackRepository
akj net/minecraft/server/packs/repository/PackSource
akk net/minecraft/server/packs/repository/ResourcePackSource
akl net/minecraft/server/packs/repository/ResourcePackSourceVanilla
akn net/minecraft/server/packs/resources/IReloadableResourceManager
ako net/minecraft/server/packs/resources/ResourceManagerFallback
akp net/minecraft/server/packs/resources/IoSupplier
akq net/minecraft/server/packs/resources/ResourceManager
akr net/minecraft/server/packs/resources/IReloadListener
aks net/minecraft/server/packs/resources/ReloadableProfiled
akt net/minecraft/server/packs/resources/IReloadable
akv net/minecraft/server/packs/resources/IResource
akw net/minecraft/server/packs/resources/ResourceFilterSection
akx net/minecraft/server/packs/resources/IResourceManager
akx$a net/minecraft/server/packs/resources/IResourceManager$Empty
akz net/minecraft/server/packs/resources/ResourceMetadata
al net/minecraft/advancements/CriterionTrigger
ala net/minecraft/server/packs/resources/ResourceProvider
alb net/minecraft/server/packs/resources/ResourceDataJson
alc net/minecraft/server/packs/resources/ResourceDataAbstract
ald net/minecraft/server/packs/resources/Reloadable
alf net/minecraft/server/players/ExpirableListEntry
alg net/minecraft/server/players/UserCache
alg$a net/minecraft/server/players/UserCache$UserCacheEntry
alh net/minecraft/server/players/IpBanList
ali net/minecraft/server/players/IpBanEntry
alj net/minecraft/server/players/NameReferencingFileConverter
alj$a net/minecraft/server/players/NameReferencingFileConverter$FileConversionException
alk net/minecraft/server/players/PlayerList
all net/minecraft/server/players/OpList
alm net/minecraft/server/players/OpListEntry
aln net/minecraft/server/players/SleepStatus
alo net/minecraft/server/players/JsonListEntry
alp net/minecraft/server/players/JsonList
alq net/minecraft/server/players/GameProfileBanList
alr net/minecraft/server/players/GameProfileBanEntry
als net/minecraft/server/players/WhiteList
alt net/minecraft/server/players/WhiteListEntry
alv net/minecraft/server/rcon/RemoteStatusReply
alw net/minecraft/server/rcon/StatusChallengeUtils
alx net/minecraft/server/rcon/RemoteControlCommandListener
alz net/minecraft/server/rcon/thread/RemoteConnectionThread
am net/minecraft/advancements/CriterionInstance
ama net/minecraft/server/rcon/thread/RemoteStatusListener
ama$a net/minecraft/server/rcon/thread/RemoteStatusListener$RemoteStatusChallenge
amb net/minecraft/server/rcon/thread/RemoteControlSession
amc net/minecraft/server/rcon/thread/RemoteControlListener
ame net/minecraft/sounds/Music
amf net/minecraft/sounds/Musics
amg net/minecraft/sounds/SoundEffect
amh net/minecraft/sounds/SoundEffects
ami net/minecraft/sounds/SoundCategory
amk net/minecraft/stats/RecipeBook
aml net/minecraft/stats/RecipeBookSettings
amm net/minecraft/stats/RecipeBookServer
amn net/minecraft/stats/ServerStatisticManager
amo net/minecraft/stats/Statistic
amp net/minecraft/stats/Counter
amq net/minecraft/stats/StatisticWrapper
amr net/minecraft/stats/StatisticList
ams net/minecraft/stats/StatisticManager
amu net/minecraft/tags/BannerPatternTags
amv net/minecraft/tags/BiomeTags
amw net/minecraft/tags/TagsBlock
amx net/minecraft/tags/CatVariantTags
amy net/minecraft/tags/DamageTypeTags
amz net/minecraft/tags/TagsEntity
an net/minecraft/advancements/AdvancementDisplay
anb net/minecraft/tags/TagsFluid
anc net/minecraft/tags/GameEventTags
and net/minecraft/tags/InstrumentTags
ane net/minecraft/tags/TagsItem
anf net/minecraft/tags/PaintingVariantTags
ang net/minecraft/tags/PoiTypeTags
anh net/minecraft/tags/StructureTags
anj net/minecraft/tags/TagEntry
ank net/minecraft/tags/TagFile
anl net/minecraft/tags/TagKey
anm net/minecraft/tags/TagDataPack
ann net/minecraft/tags/TagRegistry
ano net/minecraft/tags/TagNetworkSerialization
anr net/minecraft/util/AbortableIterationConsumer
ans net/minecraft/util/DataBits
ant net/minecraft/util/Brightness
anu net/minecraft/util/ByIdMap
anv net/minecraft/util/EntitySlice
any net/minecraft/util/RegistryID
anz net/minecraft/util/MinecraftEncryption
ao net/minecraft/advancements/AdvancementFrameType
aoa net/minecraft/util/CryptographyException
aob net/minecraft/util/CSVWriter
aod net/minecraft/util/CubicSpline
aoe net/minecraft/util/DebugBuffer
aof net/minecraft/util/DependencySorter
aog net/minecraft/util/SessionLock
aog$a net/minecraft/util/SessionLock$ExceptionWorldConflict
aoh net/minecraft/util/ExceptionSuppressor
aoi net/minecraft/util/ExtraCodecs
aoj net/minecraft/util/FastBufferedInputStream
aok net/minecraft/util/ColorUtil
aol net/minecraft/util/FileZipper
aom net/minecraft/util/FormattedString
aon net/minecraft/util/FormattedStringEmpty
aoo net/minecraft/util/CircularTimer
aop net/minecraft/util/FutureChain
aoq net/minecraft/util/Graph
aor net/minecraft/util/ChatDeserializer
aos net/minecraft/util/HttpUtilities
aot net/minecraft/util/InclusiveRange
aou net/minecraft/util/KeyDispatchDataCodec
aov net/minecraft/util/LazyInitVar
aow net/minecraft/util/LinearCongruentialGenerator
aox net/minecraft/util/ChatTypeAdapterFactory
aoy net/minecraft/util/MemoryReserve
aoz net/minecraft/util/ModCheck
ap net/minecraft/advancements/AdvancementRequirements
apa net/minecraft/util/MathHelper
apb net/minecraft/util/NativeModuleLister
apc net/minecraft/util/OptionEnum
apd net/minecraft/util/ParticleUtils
ape net/minecraft/util/IProgressUpdate
apf net/minecraft/util/RandomSource
apg net/minecraft/util/ResourceLocationPattern
aph net/minecraft/util/SegmentedAnglePrecision
api net/minecraft/util/SignatureUpdater
apj net/minecraft/util/SignatureValidator
apk net/minecraft/util/Signer
apl net/minecraft/util/SimpleBitStorage
apm net/minecraft/util/SingleKeyCache
apo net/minecraft/util/ArraySetSorted
app net/minecraft/util/SpawnUtil
apq net/minecraft/util/StringDecomposer
apr net/minecraft/util/INamable
aps net/minecraft/util/UtilColor
apt net/minecraft/util/TaskChainer
apu net/minecraft/util/ThreadingDetector
apv net/minecraft/util/TimeSource
apw net/minecraft/util/TimeRange
apx net/minecraft/util/ToFloatFunction
apy net/minecraft/util/Tuple
apz net/minecraft/util/Unit
aq net/minecraft/advancements/AdvancementTree
aqa net/minecraft/util/VisibleForDebug
aqb net/minecraft/util/ZeroBitStorage
aqc net/minecraft/util/datafix/DataFixTypes
aqd net/minecraft/util/datafix/DataConverterRegistry
aqe net/minecraft/util/datafix/DataBitsPacked
aqf net/minecraft/util/datafix/fixes/AbstractArrowPickupFix
aqg net/minecraft/util/datafix/fixes/AbstractPoiSectionFix
aqh net/minecraft/util/datafix/fixes/DataConverterUUIDBase
aqi net/minecraft/util/datafix/fixes/AddFlagIfNotPresentFix
aqj net/minecraft/util/datafix/fixes/DataConverterAddChoices
aqk net/minecraft/util/datafix/fixes/DataConverterAdvancement
aql net/minecraft/util/datafix/fixes/DataConverterAdvancementBase
aqm net/minecraft/util/datafix/fixes/DataConverterAttributes
aqn net/minecraft/util/datafix/fixes/DataConverterBedItem
aqo net/minecraft/util/datafix/fixes/DataConverterBiome
aqp net/minecraft/util/datafix/fixes/DataConverterBitStorageAlign
aqq net/minecraft/util/datafix/fixes/BlendingDataFix
aqr net/minecraft/util/datafix/fixes/BlendingDataRemoveFromNetherEndFix
aqs net/minecraft/util/datafix/fixes/DataConverterBannerColour
aqt net/minecraft/util/datafix/fixes/DataConverterPiston
aqu net/minecraft/util/datafix/fixes/DataConverterCustomNameTile
aqv net/minecraft/util/datafix/fixes/DataConverterTileEntity
aqw net/minecraft/util/datafix/fixes/DataConverterJukeBox
aqx net/minecraft/util/datafix/fixes/DataConverterBlockEntityKeepPacked
aqy net/minecraft/util/datafix/fixes/BlockEntityRenameFix
aqz net/minecraft/util/datafix/fixes/DataConverterShulkerBoxBlock
ar net/minecraft/advancements/critereon/CriterionInstanceAbstract
ara net/minecraft/util/datafix/fixes/BlockEntitySignDoubleSidedEditableTextFix
arb net/minecraft/util/datafix/fixes/DataConverterSignText
arc net/minecraft/util/datafix/fixes/DataConverterBlockEntityUUID
ard net/minecraft/util/datafix/fixes/DataConverterBlockName
are net/minecraft/util/datafix/fixes/DataConverterBlockRename
arf net/minecraft/util/datafix/fixes/BlockRenameFixWithJigsaw
arg net/minecraft/util/datafix/fixes/DataConverterFlattenData
arh net/minecraft/util/datafix/fixes/DataConverterFlattenState
ari net/minecraft/util/datafix/fixes/DataConverterCatType
arj net/minecraft/util/datafix/fixes/CauldronRenameFix
ark net/minecraft/util/datafix/fixes/CavesAndCliffsRenames
arl net/minecraft/util/datafix/fixes/DataConverterBedBlock
arm net/minecraft/util/datafix/fixes/DataConverterLeavesBiome
arn net/minecraft/util/datafix/fixes/ChunkDeleteIgnoredLightDataFix
aro net/minecraft/util/datafix/fixes/ChunkDeleteLightFix
arp net/minecraft/util/datafix/fixes/ChunkHeightAndBiomeFix
arq net/minecraft/util/datafix/fixes/DataConverterChunkLightRemove
arr net/minecraft/util/datafix/fixes/ChunkConverterPalette
arr$b net/minecraft/util/datafix/fixes/ChunkConverterPalette$Direction
arr$b$a net/minecraft/util/datafix/fixes/ChunkConverterPalette$Direction$Axis
arr$b$b net/minecraft/util/datafix/fixes/ChunkConverterPalette$Direction$AxisDirection
ars net/minecraft/util/datafix/fixes/ChunkProtoTickListFix
art net/minecraft/util/datafix/fixes/ChunkRenamesFix
aru net/minecraft/util/datafix/fixes/DataConverterChunkStatus
arv net/minecraft/util/datafix/fixes/DataConverterChunkStatus2
arw net/minecraft/util/datafix/fixes/DataConverterChunkStructuresTemplateRename
arx net/minecraft/util/datafix/fixes/DataConverterProtoChunk
ary net/minecraft/util/datafix/fixes/DataConverterColorlessShulkerEntity
arz net/minecraft/util/datafix/fixes/CriteriaRenameFix
as net/minecraft/advancements/critereon/CriterionTriggerBeeNestDestroyed
asa net/minecraft/util/datafix/fixes/DecoratedPotFieldRenameFix
asb net/minecraft/util/datafix/fixes/DataConverterDye
asc net/minecraft/util/datafix/fixes/EffectDurationFix
asd net/minecraft/util/datafix/fixes/DataConverterArmorStand
ase net/minecraft/util/datafix/fixes/DataConverterEntityBlockState
asf net/minecraft/util/datafix/fixes/EntityBrushableBlockFieldsRenameFix
asg net/minecraft/util/datafix/fixes/DataConverterEntityCatSplit
ash net/minecraft/util/datafix/fixes/DataConverterEntityCodSalmon
asi net/minecraft/util/datafix/fixes/DataConverterCustomNameEntity
asj net/minecraft/util/datafix/fixes/DataConverterGuardian
ask net/minecraft/util/datafix/fixes/DataConverterEquipment
asl net/minecraft/util/datafix/fixes/EntityGoatMissingStateFix
asm net/minecraft/util/datafix/fixes/DataConverterHealth
asn net/minecraft/util/datafix/fixes/DataConverterSaddle
aso net/minecraft/util/datafix/fixes/DataConverterHorse
asp net/minecraft/util/datafix/fixes/DataConverterEntity
asq net/minecraft/util/datafix/fixes/DataConverterItemFrame
asr net/minecraft/util/datafix/fixes/DataConverterMinecart
ass net/minecraft/util/datafix/fixes/EntityPaintingFieldsRenameFix
ast net/minecraft/util/datafix/fixes/DataConverterHanging
asu net/minecraft/util/datafix/fixes/DataConverterPainting
asv net/minecraft/util/datafix/fixes/DataConverterEntityProjectileOwner
asw net/minecraft/util/datafix/fixes/DataConverterEntityPufferfish
asx net/minecraft/util/datafix/fixes/DataConverterEntityRavagerRename
asy net/minecraft/util/datafix/fixes/DataConverterDropChances
asz net/minecraft/util/datafix/fixes/DataConverterEntityName
at net/minecraft/advancements/critereon/CriterionConditionBlock
ata net/minecraft/util/datafix/fixes/DataConverterRiding
atb net/minecraft/util/datafix/fixes/DataConverterShulker
atc net/minecraft/util/datafix/fixes/DataConverterEntityShulkerRotation
atd net/minecraft/util/datafix/fixes/DataConverterSkeleton
ate net/minecraft/util/datafix/fixes/DataConverterUUID
atf net/minecraft/util/datafix/fixes/DataConverterEntityRename
atg net/minecraft/util/datafix/fixes/DataConverterEntityTippedArrow
ath net/minecraft/util/datafix/fixes/DataConverterEntityUUID
ati net/minecraft/util/datafix/fixes/EntityVariantFix
atj net/minecraft/util/datafix/fixes/DataConverterWolf
atk net/minecraft/util/datafix/fixes/DataConverterZombieType
atl net/minecraft/util/datafix/fixes/DataConverterZombie
atm net/minecraft/util/datafix/fixes/DataConverterEntityZombifiedPiglinRename
atn net/minecraft/util/datafix/fixes/FeatureFlagRemoveFix
ato net/minecraft/util/datafix/fixes/FilteredBooksFix
atp net/minecraft/util/datafix/fixes/FilteredSignsFix
atq net/minecraft/util/datafix/fixes/DataConverterPOIRebuild
atr net/minecraft/util/datafix/fixes/DataConverterFurnaceRecipesUsed
ats net/minecraft/util/datafix/fixes/GoatHornIdFix
att net/minecraft/util/datafix/fixes/DataConverterGossip
atu net/minecraft/util/datafix/fixes/DataConverterHeightmapRenaming
atv net/minecraft/util/datafix/fixes/DataConverterIglooMetadataRemoval
atw net/minecraft/util/datafix/fixes/DataConverterBanner
atx net/minecraft/util/datafix/fixes/DataConverterCustomNameItem
aty net/minecraft/util/datafix/fixes/DataConverterMaterialId
atz net/minecraft/util/datafix/fixes/DataConverterItemLoreComponentize
au net/minecraft/advancements/critereon/CriterionTriggerBredAnimals
aua net/minecraft/util/datafix/fixes/DataConverterPotionId
aub net/minecraft/util/datafix/fixes/ItemRemoveBlockEntityTagFix
auc net/minecraft/util/datafix/fixes/DataConverterItemName
aud net/minecraft/util/datafix/fixes/DataConverterShulkerBoxItem
aue net/minecraft/util/datafix/fixes/DataConverterSpawnEgg
auf net/minecraft/util/datafix/fixes/DataConverterItemStackEnchantment
aug net/minecraft/util/datafix/fixes/DataConverterMap
auh net/minecraft/util/datafix/fixes/DataConverterFlattenSpawnEgg
aui net/minecraft/util/datafix/fixes/ItemStackTagFix
auj net/minecraft/util/datafix/fixes/DataConverterFlatten
auk net/minecraft/util/datafix/fixes/DataConverterItemStackUUID
aul net/minecraft/util/datafix/fixes/DataConverterPotionWater
aum net/minecraft/util/datafix/fixes/DataConverterBook
aun net/minecraft/util/datafix/fixes/DataConverterJigsawProperties
auo net/minecraft/util/datafix/fixes/DataConverterJigsawRotation
aup net/minecraft/util/datafix/fixes/DataConverterLeaves
auq net/minecraft/util/datafix/fixes/LegacyDragonFightFix
aur net/minecraft/util/datafix/fixes/DataConverterLevelDataGeneratorOptions
aus net/minecraft/util/datafix/fixes/DataConverterWorldGenSettings
aut net/minecraft/util/datafix/fixes/DataConverterMiscUUID
auu net/minecraft/util/datafix/fixes/DataConverterMapId
auv net/minecraft/util/datafix/fixes/DataConverterMemoryExpiry
auw net/minecraft/util/datafix/fixes/DataConverterMissingDimension
auy net/minecraft/util/datafix/fixes/DataConverterMobSpawner
auz net/minecraft/util/datafix/fixes/DataConverterNamedEntity
av net/minecraft/advancements/critereon/CriterionTriggerBrewedPotion
ava net/minecraft/util/datafix/fixes/NamespacedTypeRenameFix
avb net/minecraft/util/datafix/fixes/DataConverterNewVillage
avc net/minecraft/util/datafix/fixes/DataConverterObjectiveDisplayName
avd net/minecraft/util/datafix/fixes/DataConverterObjectiveRenderType
ave net/minecraft/util/datafix/fixes/DataConverterOminousBannerBlockEntityRename
avf net/minecraft/util/datafix/fixes/DataConverterOminousBannerRename
avg net/minecraft/util/datafix/fixes/OptionsAccessibilityOnboardFix
avh net/minecraft/util/datafix/fixes/DataConverterOptionsAddTextBackground
avi net/minecraft/util/datafix/fixes/OptionsAmbientOcclusionFix
avj net/minecraft/util/datafix/fixes/DataConverterVBO
avk net/minecraft/util/datafix/fixes/DataConverterKeybind
avl net/minecraft/util/datafix/fixes/DataConverterKeybind2
avm net/minecraft/util/datafix/fixes/DataConverterLang
avn net/minecraft/util/datafix/fixes/OptionsProgrammerArtFix
avo net/minecraft/util/datafix/fixes/DataConverterSettingRename
avp net/minecraft/util/datafix/fixes/OverreachingTickFix
avq net/minecraft/util/datafix/fixes/DataConverterPlayerUUID
avr net/minecraft/util/datafix/fixes/PoiTypeRemoveFix
avs net/minecraft/util/datafix/fixes/PoiTypeRenameFix
avt net/minecraft/util/datafix/fixes/DataConverterRecipes
avu net/minecraft/util/datafix/fixes/DataConverterRecipeRename
avv net/minecraft/util/datafix/fixes/DataConverterRedstoneConnections
avw net/minecraft/util/datafix/fixes/DataConverterTypes
avx net/minecraft/util/datafix/fixes/RemapChunkStatusFix
avy net/minecraft/util/datafix/fixes/DataConverterRemoveGolemGossip
avz net/minecraft/util/datafix/fixes/DataConverterCoralFan
aw net/minecraft/advancements/critereon/CriterionTriggerChangedDimension
awa net/minecraft/util/datafix/fixes/DataConverterCoral
awb net/minecraft/util/datafix/fixes/DataConverterPOI
awc net/minecraft/util/datafix/fixes/SavedDataFeaturePoolElementFix
awd net/minecraft/util/datafix/fixes/DataConverterSavedDataUUID
awe net/minecraft/util/datafix/fixes/DataConverterEntityNameAbstract
awf net/minecraft/util/datafix/fixes/DataConverterEntityRenameAbstract
awg net/minecraft/util/datafix/fixes/SpawnerDataFix
awh net/minecraft/util/datafix/fixes/DataConverterStatistic
awi net/minecraft/util/datafix/fixes/StatsRenameFix
awj net/minecraft/util/datafix/fixes/DataConverterStriderGravity
awk net/minecraft/util/datafix/fixes/DataConverterStructureReference
awl net/minecraft/util/datafix/fixes/StructureSettingsFlattenFix
awm net/minecraft/util/datafix/fixes/StructuresBecomeConfiguredFix
awn net/minecraft/util/datafix/fixes/DataConverterTeamDisplayName
awo net/minecraft/util/datafix/fixes/DataConverterTrappedChest
awp net/minecraft/util/datafix/fixes/VariantRenameFix
awq net/minecraft/util/datafix/fixes/DataConverterVillagerProfession
awr net/minecraft/util/datafix/fixes/DataConverterVillagerFollowRange
aws net/minecraft/util/datafix/fixes/DataConverterVillagerLevelXp
awt net/minecraft/util/datafix/fixes/DataConverterVillagerTrade
awu net/minecraft/util/datafix/fixes/DataConverterWallProperty
awv net/minecraft/util/datafix/fixes/WeaponSmithChestLootTableFix
aww net/minecraft/util/datafix/fixes/WorldGenSettingsDisallowOldCustomWorldsFix
awx net/minecraft/util/datafix/fixes/DataConverterWorldGenSettingsBuilding
awy net/minecraft/util/datafix/fixes/WorldGenSettingsHeightAndBiomeFix
awz net/minecraft/util/datafix/fixes/DataConverterShoulderEntity
ax net/minecraft/advancements/critereon/CriterionTriggerChanneledLightning
axa net/minecraft/util/datafix/fixes/DataConverterZombieVillagerLevelXp
axd net/minecraft/util/datafix/schemas/DataConverterSchemaNamed
axe net/minecraft/util/datafix/schemas/DataConverterSchemaV100
axf net/minecraft/util/datafix/schemas/DataConverterSchemaV102
axg net/minecraft/util/datafix/schemas/DataConverterSchemaV1022
axh net/minecraft/util/datafix/schemas/DataConverterSchemaV106
axi net/minecraft/util/datafix/schemas/DataConverterSchemaV107
axj net/minecraft/util/datafix/schemas/DataConverterSchemaV1125
axk net/minecraft/util/datafix/schemas/DataConverterSchemaV135
axl net/minecraft/util/datafix/schemas/DataConverterSchemaV143
axm net/minecraft/util/datafix/schemas/DataConverterSchemaV1451
axn net/minecraft/util/datafix/schemas/DataConverterSchemaV1451_1
axo net/minecraft/util/datafix/schemas/DataConverterSchemaV1451_2
axp net/minecraft/util/datafix/schemas/DataConverterSchemaV1451_3
axq net/minecraft/util/datafix/schemas/DataConverterSchemaV1451_4
axr net/minecraft/util/datafix/schemas/DataConverterSchemaV1451_5
axs net/minecraft/util/datafix/schemas/DataConverterSchemaV1451_6
axt net/minecraft/util/datafix/schemas/DataConverterSchemaV1460
axu net/minecraft/util/datafix/schemas/DataConverterSchemaV1466
axv net/minecraft/util/datafix/schemas/DataConverterSchemaV1470
axw net/minecraft/util/datafix/schemas/DataConverterSchemaV1481
axx net/minecraft/util/datafix/schemas/DataConverterSchemaV1483
axy net/minecraft/util/datafix/schemas/DataConverterSchemaV1486
axz net/minecraft/util/datafix/schemas/DataConverterSchemaV1510
ay net/minecraft/advancements/critereon/CriterionTriggerConstructBeacon
aya net/minecraft/util/datafix/schemas/DataConverterSchemaV1800
ayb net/minecraft/util/datafix/schemas/DataConverterSchemaV1801
ayc net/minecraft/util/datafix/schemas/DataConverterSchemaV1904
ayd net/minecraft/util/datafix/schemas/DataConverterSchemaV1906
aye net/minecraft/util/datafix/schemas/DataConverterSchemaV1909
ayf net/minecraft/util/datafix/schemas/DataConverterSchemaV1920
ayg net/minecraft/util/datafix/schemas/DataConverterSchemaV1928
ayh net/minecraft/util/datafix/schemas/DataConverterSchemaV1929
ayi net/minecraft/util/datafix/schemas/DataConverterSchemaV1931
ayj net/minecraft/util/datafix/schemas/DataConverterSchemaV2100
ayk net/minecraft/util/datafix/schemas/DataConverterSchemaV2501
ayl net/minecraft/util/datafix/schemas/DataConverterSchemaV2502
aym net/minecraft/util/datafix/schemas/DataConverterSchemaV2505
ayn net/minecraft/util/datafix/schemas/DataConverterSchemaV2509
ayo net/minecraft/util/datafix/schemas/DataConverterSchemaV2519
ayp net/minecraft/util/datafix/schemas/DataConverterSchemaV2522
ayq net/minecraft/util/datafix/schemas/DataConverterSchemaV2551
ayr net/minecraft/util/datafix/schemas/DataConverterSchemaV2568
ays net/minecraft/util/datafix/schemas/V2571
ayt net/minecraft/util/datafix/schemas/V2684
ayu net/minecraft/util/datafix/schemas/V2686
ayv net/minecraft/util/datafix/schemas/V2688
ayw net/minecraft/util/datafix/schemas/V2704
ayx net/minecraft/util/datafix/schemas/V2707
ayy net/minecraft/util/datafix/schemas/V2831
ayz net/minecraft/util/datafix/schemas/V2832
az net/minecraft/advancements/critereon/CriterionTriggerConsumeItem
aza net/minecraft/util/datafix/schemas/V2842
azb net/minecraft/util/datafix/schemas/V3076
azc net/minecraft/util/datafix/schemas/V3078
azd net/minecraft/util/datafix/schemas/V3081
aze net/minecraft/util/datafix/schemas/V3082
azf net/minecraft/util/datafix/schemas/V3083
azg net/minecraft/util/datafix/schemas/V3202
azh net/minecraft/util/datafix/schemas/V3203
azi net/minecraft/util/datafix/schemas/V3204
azj net/minecraft/util/datafix/schemas/V3325
azk net/minecraft/util/datafix/schemas/V3326
azl net/minecraft/util/datafix/schemas/V3327
azm net/minecraft/util/datafix/schemas/V3328
azn net/minecraft/util/datafix/schemas/V3438
azo net/minecraft/util/datafix/schemas/V3448
azp net/minecraft/util/datafix/schemas/DataConverterSchemaV501
azq net/minecraft/util/datafix/schemas/DataConverterSchemaV700
azr net/minecraft/util/datafix/schemas/DataConverterSchemaV701
azs net/minecraft/util/datafix/schemas/DataConverterSchemaV702
azt net/minecraft/util/datafix/schemas/DataConverterSchemaV703
azu net/minecraft/util/datafix/schemas/DataConverterSchemaV704
azv net/minecraft/util/datafix/schemas/DataConverterSchemaV705
azw net/minecraft/util/datafix/schemas/DataConverterSchemaV808
azx net/minecraft/util/datafix/schemas/DataConverterSchemaV99
ba net/minecraft/advancements/critereon/ContextAwarePredicate
bad net/minecraft/util/monitoring/jmx/MinecraftServerBeans
bag net/minecraft/util/profiling/MethodProfiler
bah net/minecraft/util/profiling/GameProfilerSwitcher
bai net/minecraft/util/profiling/MethodProfilerResultsEmpty
baj net/minecraft/util/profiling/MethodProfilerResultsFilled
bak net/minecraft/util/profiling/GameProfilerDisabled
bal net/minecraft/util/profiling/GameProfilerFillerActive
bam net/minecraft/util/profiling/MethodProfilerResults
ban net/minecraft/util/profiling/GameProfilerFiller
bao net/minecraft/util/profiling/MethodProfilerResult
bap net/minecraft/util/profiling/MethodProfilerResultsField
baq net/minecraft/util/profiling/GameProfilerTick
bar net/minecraft/util/profiling/jfr/Environment
bas net/minecraft/util/profiling/jfr/JfrProfiler
bat net/minecraft/util/profiling/jfr/JvmProfiler
bau net/minecraft/util/profiling/jfr/Percentiles
bav net/minecraft/util/profiling/jfr/SummaryReporter
baw net/minecraft/util/profiling/jfr/callback/ProfiledDuration
bay net/minecraft/util/profiling/jfr/event/PacketEvent
bb net/minecraft/advancements/critereon/CriterionTriggerCuredZombieVillager
bbb net/minecraft/util/profiling/jfr/parse/JfrStatsParser
bbc net/minecraft/util/profiling/jfr/parse/JfrStatsResult
bbe net/minecraft/util/profiling/jfr/serialize/JfrResultJsonSerializer
bbg net/minecraft/util/profiling/jfr/stats/ChunkGenStat
bbh net/minecraft/util/profiling/jfr/stats/CpuLoadStat
bbi net/minecraft/util/profiling/jfr/stats/FileIOStat
bbj net/minecraft/util/profiling/jfr/stats/GcHeapStat
bbk net/minecraft/util/profiling/jfr/stats/NetworkPacketSummary
bbl net/minecraft/util/profiling/jfr/stats/ThreadAllocationStat
bbm net/minecraft/util/profiling/jfr/stats/TickTimeStat
bbo net/minecraft/util/profiling/jfr/stats/TimedStat
bbp net/minecraft/util/profiling/jfr/stats/TimedStatSummary
bbr net/minecraft/util/profiling/metrics/MetricCategory
bbs net/minecraft/util/profiling/metrics/MetricSampler
bbt net/minecraft/util/profiling/metrics/MetricsRegistry
bbu net/minecraft/util/profiling/metrics/MetricsSamplerProvider
bbv net/minecraft/util/profiling/metrics/ProfilerMeasured
bbx net/minecraft/util/profiling/metrics/profiling/ActiveMetricsRecorder
bby net/minecraft/util/profiling/metrics/profiling/InactiveMetricsRecorder
bbz net/minecraft/util/profiling/metrics/profiling/MetricsRecorder
bc net/minecraft/advancements/critereon/CriterionConditionDamage
bca net/minecraft/util/profiling/metrics/profiling/ProfilerSamplerAdapter
bcb net/minecraft/util/profiling/metrics/profiling/ServerMetricsSamplersProvider
bcd net/minecraft/util/profiling/metrics/storage/MetricsPersister
bce net/minecraft/util/profiling/metrics/storage/RecordedDeviation
bch net/minecraft/util/random/SimpleWeightedRandomList
bci net/minecraft/util/random/Weight
bcj net/minecraft/util/random/WeightedEntry
bck net/minecraft/util/random/WeightedRandom2
bcl net/minecraft/util/random/WeightedRandomList
bcn net/minecraft/util/thread/IAsyncTaskHandler
bcp net/minecraft/util/thread/Mailbox
bcq net/minecraft/util/thread/ThreadedMailbox
bcr net/minecraft/util/thread/IAsyncTaskHandlerReentrant
bcs net/minecraft/util/thread/PairedQueue
bcu net/minecraft/util/valueproviders/BiasedToBottomInt
bcv net/minecraft/util/valueproviders/ClampedInt
bcw net/minecraft/util/valueproviders/ClampedNormalFloat
bcx net/minecraft/util/valueproviders/ClampedNormalInt
bcy net/minecraft/util/valueproviders/ConstantFloat
bcz net/minecraft/util/valueproviders/ConstantInt
bd net/minecraft/advancements/critereon/CriterionConditionDamageSource
bda net/minecraft/util/valueproviders/FloatProvider
bdb net/minecraft/util/valueproviders/FloatProviderType
bdc net/minecraft/util/valueproviders/IntProvider
bdd net/minecraft/util/valueproviders/IntProviderType
bdf net/minecraft/util/valueproviders/SampledFloat
bdg net/minecraft/util/valueproviders/TrapezoidFloat
bdh net/minecraft/util/valueproviders/UniformFloat
bdi net/minecraft/util/valueproviders/UniformInt
bdj net/minecraft/util/valueproviders/WeightedListInt
bdl net/minecraft/util/worldupdate/WorldUpgrader
bdn net/minecraft/world/BossBattle
bdn$a net/minecraft/world/BossBattle$BarColor
bdn$b net/minecraft/world/BossBattle$BarStyle
bdo net/minecraft/world/Clearable
bdp net/minecraft/world/InventoryLargeChest
bdq net/minecraft/world/IInventory
bdr net/minecraft/world/ContainerUtil
bds net/minecraft/world/IInventoryListener
bdt net/minecraft/world/InventoryUtils
bdu net/minecraft/world/EnumDifficulty
bdv net/minecraft/world/DifficultyDamageScaler
bdw net/minecraft/world/EnumHand
bdx net/minecraft/world/EnumInteractionResult
bdy net/minecraft/world/InteractionResultWrapper
bdz net/minecraft/world/ChestLock
be net/minecraft/advancements/critereon/LootDeserializationContext
bea net/minecraft/world/ITileInventory
beb net/minecraft/world/INamableTileEntity
bec net/minecraft/world/RandomSequence
bed net/minecraft/world/RandomSequences
bee net/minecraft/world/InventorySubcontainer
bef net/minecraft/world/TileInventory
beg net/minecraft/world/IWorldInventory
beh net/minecraft/world/IInventoryHolder
bei net/minecraft/world/damagesource/CombatEntry
bej net/minecraft/world/damagesource/CombatMath
bek net/minecraft/world/damagesource/CombatTracker
bel net/minecraft/world/damagesource/DamageEffects
bem net/minecraft/world/damagesource/DamageScaling
ben net/minecraft/world/damagesource/DamageSource
beo net/minecraft/world/damagesource/DamageSources
bep net/minecraft/world/damagesource/DamageType
beq net/minecraft/world/damagesource/DamageTypes
ber net/minecraft/world/damagesource/DeathMessageType
bes net/minecraft/world/damagesource/FallLocation
beu net/minecraft/world/effect/MobEffectAbsorption
bev net/minecraft/world/effect/MobEffectAttackDamage
bew net/minecraft/world/effect/MobEffectHealthBoost
bex net/minecraft/world/effect/InstantMobEffect
bey net/minecraft/world/effect/MobEffectList
bez net/minecraft/world/effect/MobEffectInfo
bf net/minecraft/advancements/critereon/CriterionConditionDistance
bfa net/minecraft/world/effect/MobEffect
bfb net/minecraft/world/effect/MobEffectUtil
bfc net/minecraft/world/effect/MobEffects
bfe net/minecraft/world/entity/EntityAgeable
bff net/minecraft/world/entity/AnimationState
bfg net/minecraft/world/entity/EntityAreaEffectCloud
bfh net/minecraft/world/entity/Attackable
bfi net/minecraft/world/entity/Display
bfi$a net/minecraft/world/entity/Display$BillboardConstraints
bfi$b net/minecraft/world/entity/Display$BlockDisplay
bfi$c net/minecraft/world/entity/Display$ColorInterpolator
bfi$d net/minecraft/world/entity/Display$FloatInterpolator
bfi$e net/minecraft/world/entity/Display$GenericInterpolator
bfi$f net/minecraft/world/entity/Display$IntInterpolator
bfi$g net/minecraft/world/entity/Display$ItemDisplay
bfi$k net/minecraft/world/entity/Display$TextDisplay
bfi$k$a net/minecraft/world/entity/Display$TextDisplay$Align
bfi$k$b net/minecraft/world/entity/Display$TextDisplay$CachedInfo
bfi$k$c net/minecraft/world/entity/Display$TextDisplay$CachedLine
bfi$k$d net/minecraft/world/entity/Display$TextDisplay$LineSplitter
bfj net/minecraft/world/entity/Entity
bfj$a net/minecraft/world/entity/Entity$MoveFunction
bfj$b net/minecraft/world/entity/Entity$MovementEmission
bfj$c net/minecraft/world/entity/Entity$RemovalReason
bfk net/minecraft/world/entity/EntitySize
bfm net/minecraft/world/entity/IEntitySelector
bfm$a net/minecraft/world/entity/IEntitySelector$EntitySelectorEquipable
bfn net/minecraft/world/entity/EntityTypes
bfn$a net/minecraft/world/entity/EntityTypes$Builder
bfo net/minecraft/world/entity/EnumItemSlot
bfo$a net/minecraft/world/entity/EnumItemSlot$Function
bfp net/minecraft/world/entity/EntityExperienceOrb
bfq net/minecraft/world/entity/EntityFlying
bfr net/minecraft/world/entity/GlowSquid
bfs net/minecraft/world/entity/HasCustomInventoryScreen
bft net/minecraft/world/entity/EnumMainHand
bfu net/minecraft/world/entity/Interaction
bfu$a net/minecraft/world/entity/Interaction$PlayerAction
bfv net/minecraft/world/entity/SaddleStorage
bfw net/minecraft/world/entity/ISteerable
bfx net/minecraft/world/entity/LerpingModel
bfy net/minecraft/world/entity/EntityLightning
bfz net/minecraft/world/entity/EntityLiving
bg net/minecraft/advancements/critereon/DistanceTrigger
bga net/minecraft/world/entity/Marker
bgb net/minecraft/world/entity/EntityInsentient
bgc net/minecraft/world/entity/EnumCreatureType
bgd net/minecraft/world/entity/EnumMobSpawn
bge net/minecraft/world/entity/EnumMonsterType
bgf net/minecraft/world/entity/EnumMoveType
bgg net/minecraft/world/entity/IEntityAngerable
bgh net/minecraft/world/entity/OwnableEntity
bgi net/minecraft/world/entity/EntityCreature
bgj net/minecraft/world/entity/PlayerRideable
bgk net/minecraft/world/entity/IJumpable
bgl net/minecraft/world/entity/EntityPose
bgm net/minecraft/world/entity/PowerableMob
bgn net/minecraft/world/entity/RelativeMovement
bgo net/minecraft/world/entity/ReputationHandler
bgp net/minecraft/world/entity/RiderShieldingMount
bgq net/minecraft/world/entity/ISaddleable
bgr net/minecraft/world/entity/IShearable
bgs net/minecraft/world/entity/SlotAccess
bgt net/minecraft/world/entity/GroupDataEntity
bgu net/minecraft/world/entity/EntityPositionTypes
bgu$c net/minecraft/world/entity/EntityPositionTypes$Surface
bgv net/minecraft/world/entity/EntityTameableAnimal
bgw net/minecraft/world/entity/Targeting
bgx net/minecraft/world/entity/TraceableEntity
bgy net/minecraft/world/entity/VariantHolder
bgz net/minecraft/world/entity/WalkAnimationState
bh net/minecraft/advancements/critereon/CriterionTriggerEffectsChanged
bha net/minecraft/world/entity/ai/BehaviorController
bhb net/minecraft/world/entity/ai/attributes/AttributeBase
bhc net/minecraft/world/entity/ai/attributes/AttributeModifiable
bhd net/minecraft/world/entity/ai/attributes/AttributeMapBase
bhe net/minecraft/world/entity/ai/attributes/AttributeModifier
bhe$a net/minecraft/world/entity/ai/attributes/AttributeModifier$Operation
bhf net/minecraft/world/entity/ai/attributes/AttributeProvider
bhf$a net/minecraft/world/entity/ai/attributes/AttributeProvider$Builder
bhg net/minecraft/world/entity/ai/attributes/GenericAttributes
bhh net/minecraft/world/entity/ai/attributes/AttributeDefaults
bhi net/minecraft/world/entity/ai/attributes/AttributeRanged
bhk net/minecraft/world/entity/ai/behavior/BehaviorFindPosition
bhl net/minecraft/world/entity/ai/behavior/BehaviorMakeLoveAnimal
bhm net/minecraft/world/entity/ai/behavior/AnimalPanic
bhn net/minecraft/world/entity/ai/behavior/BehaviorCareer
bho net/minecraft/world/entity/ai/behavior/BehaviorFollowAdult
bhp net/minecraft/world/entity/ai/behavior/BehaviorRetreat
bhq net/minecraft/world/entity/ai/behavior/BehaviorPacify
bhr net/minecraft/world/entity/ai/behavior/Behavior
bhr$a net/minecraft/world/entity/ai/behavior/Behavior$Status
bhs net/minecraft/world/entity/ai/behavior/BehaviorControl
bht net/minecraft/world/entity/ai/behavior/BehaviorUtil
bhu net/minecraft/world/entity/ai/behavior/BehaviorTarget
bhv net/minecraft/world/entity/ai/behavior/BehaviorCelebrate
bhw net/minecraft/world/entity/ai/behavior/BehaviorExpirableMemory
bhx net/minecraft/world/entity/ai/behavior/CountDownCooldownTicks
bhy net/minecraft/world/entity/ai/behavior/Croak
bhz net/minecraft/world/entity/ai/behavior/BehaviorCrossbowAttack
bhz$a net/minecraft/world/entity/ai/behavior/BehaviorCrossbowAttack$BowState
bi net/minecraft/advancements/critereon/CriterionTriggerEnchantedItem
bia net/minecraft/world/entity/ai/behavior/BehaviorStopRiding
bib net/minecraft/world/entity/ai/behavior/BehaviorNop
bic net/minecraft/world/entity/ai/behavior/BehaviorPositionEntity
bid net/minecraft/world/entity/ai/behavior/BehaviorRemoveMemory
bie net/minecraft/world/entity/ai/behavior/FollowTemptation
bif net/minecraft/world/entity/ai/behavior/BehaviorGate
bif$a net/minecraft/world/entity/ai/behavior/BehaviorGate$Order
bif$b net/minecraft/world/entity/ai/behavior/BehaviorGate$Execution
big net/minecraft/world/entity/ai/behavior/BehaviorVillageHeroGift
bih net/minecraft/world/entity/ai/behavior/GoAndGiveItemsToTarget
bii net/minecraft/world/entity/ai/behavior/BehaviorNearestVillage
bij net/minecraft/world/entity/ai/behavior/BehaviorPotentialJobSite
bik net/minecraft/world/entity/ai/behavior/GoToTargetLocation
bil net/minecraft/world/entity/ai/behavior/BehaviorFindAdmirableItem
bim net/minecraft/world/entity/ai/behavior/BehaviorFarm
bin net/minecraft/world/entity/ai/behavior/BehaviorStrollInside
bio net/minecraft/world/entity/ai/behavior/BehaviorInteract
bip net/minecraft/world/entity/ai/behavior/BehaviorInteractDoor
biq net/minecraft/world/entity/ai/behavior/BehaviorBedJump
bir net/minecraft/world/entity/ai/behavior/BehaviorHome
bis net/minecraft/world/entity/ai/behavior/LongJumpMidJump
bit net/minecraft/world/entity/ai/behavior/LongJumpToPreferredBlock
biu net/minecraft/world/entity/ai/behavior/LongJumpToRandomPos
biv net/minecraft/world/entity/ai/behavior/BehaviorInteractPlayer
biw net/minecraft/world/entity/ai/behavior/BehaviorLook
bix net/minecraft/world/entity/ai/behavior/BehaviorAttack
biy net/minecraft/world/entity/ai/behavior/BehaviorStartRiding
biz net/minecraft/world/entity/ai/behavior/BehaviorOutside
bj net/minecraft/advancements/critereon/CriterionConditionEnchantments
bja net/minecraft/world/entity/ai/behavior/BehavorMove
bjb net/minecraft/world/entity/ai/behavior/OneShot
bjc net/minecraft/world/entity/ai/behavior/BehaviorPlay
bjd net/minecraft/world/entity/ai/behavior/BehaviorBetterJob
bje net/minecraft/world/entity/ai/behavior/BehaviorPosition
bjf net/minecraft/world/entity/ai/behavior/PrepareRamNearestTarget
bjg net/minecraft/world/entity/ai/behavior/RamTarget
bjh net/minecraft/world/entity/ai/behavior/RandomLookAround
bji net/minecraft/world/entity/ai/behavior/BehaviorStrollRandomUnconstrained
bjj net/minecraft/world/entity/ai/behavior/BehaviorBellAlert
bjk net/minecraft/world/entity/ai/behavior/BehaviorProfession
bjl net/minecraft/world/entity/ai/behavior/BehaviorRaidReset
bjm net/minecraft/world/entity/ai/behavior/BehaviorBellRing
bjn net/minecraft/world/entity/ai/behavior/BehaviorGateSingle
bjo net/minecraft/world/entity/ai/behavior/BehaviorWalkHome
bjp net/minecraft/world/entity/ai/behavior/BehaviorLookTarget
bjq net/minecraft/world/entity/ai/behavior/SetEntityLookTargetSometimes
bjr net/minecraft/world/entity/ai/behavior/BehaviorHide
bjs net/minecraft/world/entity/ai/behavior/BehaviorLookInteract
bjt net/minecraft/world/entity/ai/behavior/BehaviorRaid
bju net/minecraft/world/entity/ai/behavior/BehaviorWalkAway
bjv net/minecraft/world/entity/ai/behavior/BehaviorWalkAwayOutOfRange
bjw net/minecraft/world/entity/ai/behavior/BehaviorWalkAwayBlock
bjx net/minecraft/world/entity/ai/behavior/BehaviorLookWalk
bjy net/minecraft/world/entity/ai/behavior/BehaviorTradePlayer
bjz net/minecraft/world/entity/ai/behavior/ShufflingList
bk net/minecraft/advancements/critereon/CriterionTriggerEnterBlock
bka net/minecraft/world/entity/ai/behavior/BehaviorSleep
bkb net/minecraft/world/entity/ai/behavior/BehaviorBell
bkc net/minecraft/world/entity/ai/behavior/BehaviorAttackTargetSet
bkd net/minecraft/world/entity/ai/behavior/BehaviorCelebrateDeath
bke net/minecraft/world/entity/ai/behavior/StayCloseToTarget
bkf net/minecraft/world/entity/ai/behavior/BehaviorAttackTargetForget
bkg net/minecraft/world/entity/ai/behavior/BehaviorForgetAnger
bkh net/minecraft/world/entity/ai/behavior/BehaviorStrollPosition
bki net/minecraft/world/entity/ai/behavior/BehaviorStrollPlace
bkj net/minecraft/world/entity/ai/behavior/BehaviorStrollPlaceList
bkk net/minecraft/world/entity/ai/behavior/BehaviorSwim
bkl net/minecraft/world/entity/ai/behavior/BehaviorTradeVillager
bkm net/minecraft/world/entity/ai/behavior/TriggerGate
bkn net/minecraft/world/entity/ai/behavior/TryFindLand
bko net/minecraft/world/entity/ai/behavior/TryFindLandNearWater
bkp net/minecraft/world/entity/ai/behavior/TryFindWater
bkq net/minecraft/world/entity/ai/behavior/TryLaySpawnOnWaterNearLand
bkr net/minecraft/world/entity/ai/behavior/BehaviorSchedule
bks net/minecraft/world/entity/ai/behavior/BehaviorBonemeal
bkt net/minecraft/world/entity/ai/behavior/BehaviorPositionValidate
bku net/minecraft/world/entity/ai/behavior/BehaviorStrollRandom
bkv net/minecraft/world/entity/ai/behavior/BehaviorCooldown
bkw net/minecraft/world/entity/ai/behavior/Behaviors
bkx net/minecraft/world/entity/ai/behavior/BehaviorMakeLove
bky net/minecraft/world/entity/ai/behavior/BehaviorPanic
bkz net/minecraft/world/entity/ai/behavior/BehaviorWake
bl net/minecraft/advancements/critereon/CriterionConditionEntityEquipment
bla net/minecraft/world/entity/ai/behavior/BehaviorWorkComposter
blb net/minecraft/world/entity/ai/behavior/BehaviorWork
blc net/minecraft/world/entity/ai/behavior/BehaviorLeaveJob
bld net/minecraft/world/entity/ai/behavior/declarative/BehaviorBuilder
ble net/minecraft/world/entity/ai/behavior/declarative/MemoryAccessor
blf net/minecraft/world/entity/ai/behavior/declarative/MemoryCondition
blg net/minecraft/world/entity/ai/behavior/declarative/Trigger
blj net/minecraft/world/entity/ai/behavior/warden/Digging
blk net/minecraft/world/entity/ai/behavior/warden/Emerging
bll net/minecraft/world/entity/ai/behavior/warden/ForceUnmount
blm net/minecraft/world/entity/ai/behavior/warden/Roar
bln net/minecraft/world/entity/ai/behavior/warden/SetRoarTarget
blo net/minecraft/world/entity/ai/behavior/warden/SetWardenLookTarget
blp net/minecraft/world/entity/ai/behavior/warden/Sniffing
blq net/minecraft/world/entity/ai/behavior/warden/SonicBoom
blr net/minecraft/world/entity/ai/behavior/warden/TryToSniff
blt net/minecraft/world/entity/ai/control/EntityAIBodyControl
blu net/minecraft/world/entity/ai/control/Control
blv net/minecraft/world/entity/ai/control/ControllerMoveFlying
blw net/minecraft/world/entity/ai/control/ControllerJump
blx net/minecraft/world/entity/ai/control/ControllerLook
bly net/minecraft/world/entity/ai/control/ControllerMove
bly$a net/minecraft/world/entity/ai/control/ControllerMove$Operation
blz net/minecraft/world/entity/ai/control/SmoothSwimmingLookControl
bm net/minecraft/advancements/critereon/CriterionConditionEntityFlags
bma net/minecraft/world/entity/ai/control/SmoothSwimmingMoveControl
bmc net/minecraft/world/entity/ai/goal/PathfinderGoalAvoidTarget
bmd net/minecraft/world/entity/ai/goal/PathfinderGoalBeg
bme net/minecraft/world/entity/ai/goal/PathfinderGoalBoat
bmf net/minecraft/world/entity/ai/goal/PathfinderGoalBreakDoor
bmg net/minecraft/world/entity/ai/goal/PathfinderGoalBreath
bmh net/minecraft/world/entity/ai/goal/PathfinderGoalBreed
bmi net/minecraft/world/entity/ai/goal/PathfinderGoalCatSitOnBed
bmj net/minecraft/world/entity/ai/goal/PathfinderGoalJumpOnBlock
bmk net/minecraft/world/entity/ai/goal/ClimbOnTopOfPowderSnowGoal
bml net/minecraft/world/entity/ai/goal/PathfinderGoalWaterJump
bmm net/minecraft/world/entity/ai/goal/PathfinderGoalDoorInteract
bmn net/minecraft/world/entity/ai/goal/PathfinderGoalEatTile
bmo net/minecraft/world/entity/ai/goal/PathfinderGoalFleeSun
bmp net/minecraft/world/entity/ai/goal/PathfinderGoalFloat
bmq net/minecraft/world/entity/ai/goal/PathfinderGoalFollowBoat
bmr net/minecraft/world/entity/ai/goal/PathfinderGoalFishSchool
bms net/minecraft/world/entity/ai/goal/PathfinderGoalFollowEntity
bmt net/minecraft/world/entity/ai/goal/PathfinderGoalFollowOwner
bmu net/minecraft/world/entity/ai/goal/PathfinderGoalFollowParent
bmv net/minecraft/world/entity/ai/goal/PathfinderGoal
bmv$a net/minecraft/world/entity/ai/goal/PathfinderGoal$Type
bmw net/minecraft/world/entity/ai/goal/PathfinderGoalSelector
bmx net/minecraft/world/entity/ai/goal/PathfinderGoalStrollVillageGolem
bmy net/minecraft/world/entity/ai/goal/PathfinderGoalInteract
bmz net/minecraft/world/entity/ai/goal/PathfinderGoalWaterJumpAbstract
bn net/minecraft/advancements/critereon/CriterionTriggerEntityHurtPlayer
bna net/minecraft/world/entity/ai/goal/PathfinderGoalPerch
bnb net/minecraft/world/entity/ai/goal/PathfinderGoalLeapAtTarget
bnc net/minecraft/world/entity/ai/goal/PathfinderGoalLlamaFollow
bnd net/minecraft/world/entity/ai/goal/PathfinderGoalLookAtPlayer
bne net/minecraft/world/entity/ai/goal/PathfinderGoalLookAtTradingPlayer
bnf net/minecraft/world/entity/ai/goal/PathfinderGoalMeleeAttack
bng net/minecraft/world/entity/ai/goal/PathfinderGoalStrollVillage
bnh net/minecraft/world/entity/ai/goal/PathfinderGoalMoveThroughVillage
bni net/minecraft/world/entity/ai/goal/PathfinderGoalGotoTarget
bnj net/minecraft/world/entity/ai/goal/PathfinderGoalMoveTowardsRestriction
bnk net/minecraft/world/entity/ai/goal/PathfinderGoalMoveTowardsTarget
bnl net/minecraft/world/entity/ai/goal/PathfinderGoalOcelotAttack
bnm net/minecraft/world/entity/ai/goal/PathfinderGoalOfferFlower
bnn net/minecraft/world/entity/ai/goal/PathfinderGoalDoorOpen
bno net/minecraft/world/entity/ai/goal/PathfinderGoalPanic
bnp net/minecraft/world/entity/ai/goal/PathfinderGoalRaid
bnq net/minecraft/world/entity/ai/goal/PathfinderGoalRandomLookaround
bnr net/minecraft/world/entity/ai/goal/RandomStandGoal
bns net/minecraft/world/entity/ai/goal/PathfinderGoalRandomStroll
bnt net/minecraft/world/entity/ai/goal/PathfinderGoalRandomSwim
bnu net/minecraft/world/entity/ai/goal/PathfinderGoalArrowAttack
bnv net/minecraft/world/entity/ai/goal/PathfinderGoalBowShoot
bnw net/minecraft/world/entity/ai/goal/PathfinderGoalCrossbowAttack
bnw$a net/minecraft/world/entity/ai/goal/PathfinderGoalCrossbowAttack$State
bnx net/minecraft/world/entity/ai/goal/PathfinderGoalRemoveBlock
bny net/minecraft/world/entity/ai/goal/PathfinderGoalRestrictSun
bnz net/minecraft/world/entity/ai/goal/PathfinderGoalTame
bo net/minecraft/advancements/critereon/CriterionConditionEntity
boa net/minecraft/world/entity/ai/goal/PathfinderGoalSit
bob net/minecraft/world/entity/ai/goal/PathfinderGoalNearestVillage
boc net/minecraft/world/entity/ai/goal/PathfinderGoalSwell
bod net/minecraft/world/entity/ai/goal/PathfinderGoalTempt
boe net/minecraft/world/entity/ai/goal/PathfinderGoalTradeWithPlayer
bof net/minecraft/world/entity/ai/goal/PathfinderGoalWater
bog net/minecraft/world/entity/ai/goal/PathfinderGoalUseItem
boh net/minecraft/world/entity/ai/goal/PathfinderGoalRandomFly
boi net/minecraft/world/entity/ai/goal/PathfinderGoalRandomStrollLand
boj net/minecraft/world/entity/ai/goal/PathfinderGoalWrapped
bok net/minecraft/world/entity/ai/goal/PathfinderGoalZombieAttack
bom net/minecraft/world/entity/ai/goal/target/PathfinderGoalDefendVillage
bon net/minecraft/world/entity/ai/goal/target/PathfinderGoalHurtByTarget
boo net/minecraft/world/entity/ai/goal/target/PathfinderGoalNearestAttackableTarget
bop net/minecraft/world/entity/ai/goal/target/PathfinderGoalNearestAttackableTargetWitch
boq net/minecraft/world/entity/ai/goal/target/PathfinderGoalNearestHealableRaider
bor net/minecraft/world/entity/ai/goal/target/PathfinderGoalRandomTargetNonTamed
bos net/minecraft/world/entity/ai/goal/target/PathfinderGoalOwnerHurtByTarget
bot net/minecraft/world/entity/ai/goal/target/PathfinderGoalOwnerHurtTarget
bou net/minecraft/world/entity/ai/goal/target/PathfinderGoalUniversalAngerReset
bov net/minecraft/world/entity/ai/goal/target/PathfinderGoalTarget
box net/minecraft/world/entity/ai/gossip/Reputation
boy net/minecraft/world/entity/ai/gossip/ReputationType
bp net/minecraft/advancements/critereon/EntitySubPredicate
bpa net/minecraft/world/entity/ai/memory/ExpirableMemory
bpb net/minecraft/world/entity/ai/memory/MemoryModuleType
bpc net/minecraft/world/entity/ai/memory/MemoryStatus
bpd net/minecraft/world/entity/ai/memory/NearestVisibleLivingEntities
bpe net/minecraft/world/entity/ai/memory/MemoryTarget
bpg net/minecraft/world/entity/ai/navigation/AmphibiousPathNavigation
bph net/minecraft/world/entity/ai/navigation/NavigationFlying
bpi net/minecraft/world/entity/ai/navigation/Navigation
bpj net/minecraft/world/entity/ai/navigation/NavigationAbstract
bpk net/minecraft/world/entity/ai/navigation/NavigationSpider
bpl net/minecraft/world/entity/ai/navigation/NavigationGuardian
bpo net/minecraft/world/entity/ai/sensing/SensorAdult
bpp net/minecraft/world/entity/ai/sensing/AxolotlAttackablesSensor
bpq net/minecraft/world/entity/ai/sensing/SensorDummy
bpr net/minecraft/world/entity/ai/sensing/FrogAttackablesSensor
bps net/minecraft/world/entity/ai/sensing/SensorGolemLastSeen
bpt net/minecraft/world/entity/ai/sensing/SensorHoglinSpecific
bpu net/minecraft/world/entity/ai/sensing/SensorHurtBy
bpv net/minecraft/world/entity/ai/sensing/IsInWaterSensor
bpw net/minecraft/world/entity/ai/sensing/SensorNearestBed
bpx net/minecraft/world/entity/ai/sensing/SensorNearestItems
bpy net/minecraft/world/entity/ai/sensing/SensorNearestLivingEntities
bpz net/minecraft/world/entity/ai/sensing/NearestVisibleLivingEntitySensor
bq net/minecraft/advancements/critereon/CriterionConditionEntityType
bqa net/minecraft/world/entity/ai/sensing/SensorPiglinBruteSpecific
bqb net/minecraft/world/entity/ai/sensing/SensorPiglinSpecific
bqc net/minecraft/world/entity/ai/sensing/SensorNearestPlayers
bqd net/minecraft/world/entity/ai/sensing/SensorSecondaryPlaces
bqe net/minecraft/world/entity/ai/sensing/EntitySenses
bqf net/minecraft/world/entity/ai/sensing/Sensor
bqg net/minecraft/world/entity/ai/sensing/SensorType
bqh net/minecraft/world/entity/ai/sensing/TemptingSensor
bqi net/minecraft/world/entity/ai/sensing/SensorVillagerBabies
bqj net/minecraft/world/entity/ai/sensing/SensorVillagerHostiles
bqk net/minecraft/world/entity/ai/sensing/WardenEntitySensor
bqm net/minecraft/world/entity/ai/targeting/PathfinderTargetCondition
bqo net/minecraft/world/entity/ai/util/AirAndWaterRandomPos
bqp net/minecraft/world/entity/ai/util/AirRandomPos
bqq net/minecraft/world/entity/ai/util/DefaultRandomPos
bqr net/minecraft/world/entity/ai/util/PathfinderGoalUtil
bqs net/minecraft/world/entity/ai/util/HoverRandomPos
bqt net/minecraft/world/entity/ai/util/LandRandomPos
bqu net/minecraft/world/entity/ai/util/RandomPositionGenerator
bqw net/minecraft/world/entity/ai/village/ReputationEvent
bqx net/minecraft/world/entity/ai/village/VillageSiege
bqx$a net/minecraft/world/entity/ai/village/VillageSiege$State
bqz net/minecraft/world/entity/ai/village/poi/VillagePlace
bqz$b net/minecraft/world/entity/ai/village/poi/VillagePlace$Occupancy
br net/minecraft/advancements/critereon/EntityVariantPredicate
bra net/minecraft/world/entity/ai/village/poi/VillagePlaceRecord
brb net/minecraft/world/entity/ai/village/poi/VillagePlaceSection
brc net/minecraft/world/entity/ai/village/poi/VillagePlaceType
brd net/minecraft/world/entity/ai/village/poi/PoiTypes
brf net/minecraft/world/entity/ambient/EntityAmbient
brg net/minecraft/world/entity/ambient/EntityBat
bri net/minecraft/world/entity/animal/EntityFish
brj net/minecraft/world/entity/animal/EntityGolem
brk net/minecraft/world/entity/animal/EntityFishSchool
brl net/minecraft/world/entity/animal/EntityAnimal
brm net/minecraft/world/entity/animal/EntityBee
brn net/minecraft/world/entity/animal/Bucketable
bro net/minecraft/world/entity/animal/EntityCat
bro$c net/minecraft/world/entity/animal/EntityCat$PathfinderGoalTemptChance
brp net/minecraft/world/entity/animal/CatVariant
brq net/minecraft/world/entity/animal/EntityChicken
brr net/minecraft/world/entity/animal/EntityCod
brs net/minecraft/world/entity/animal/EntityCow
brt net/minecraft/world/entity/animal/EntityDolphin
bru net/minecraft/world/entity/animal/EntityBird
brv net/minecraft/world/entity/animal/EntityFox
brv$v net/minecraft/world/entity/animal/EntityFox$Type
brw net/minecraft/world/entity/animal/FrogVariant
brx net/minecraft/world/entity/animal/EntityIronGolem
brx$a net/minecraft/world/entity/animal/EntityIronGolem$CrackLevel
bry net/minecraft/world/entity/animal/EntityMushroomCow
bry$a net/minecraft/world/entity/animal/EntityMushroomCow$Type
brz net/minecraft/world/entity/animal/EntityOcelot
bs net/minecraft/advancements/critereon/CriterionTriggerFilledBucket
bsa net/minecraft/world/entity/animal/EntityPanda
bsa$a net/minecraft/world/entity/animal/EntityPanda$Gene
bsb net/minecraft/world/entity/animal/EntityParrot
bsb$b net/minecraft/world/entity/animal/EntityParrot$Variant
bsc net/minecraft/world/entity/animal/EntityPig
bsd net/minecraft/world/entity/animal/EntityPolarBear
bse net/minecraft/world/entity/animal/EntityPufferFish
bsf net/minecraft/world/entity/animal/EntityRabbit
bsf$a net/minecraft/world/entity/animal/EntityRabbit$PathfinderGoalKillerRabbitMeleeAttack
bsf$b net/minecraft/world/entity/animal/EntityRabbit$PathfinderGoalRabbitAvoidTarget
bsf$c net/minecraft/world/entity/animal/EntityRabbit$GroupDataRabbit
bsf$d net/minecraft/world/entity/animal/EntityRabbit$ControllerJumpRabbit
bsf$e net/minecraft/world/entity/animal/EntityRabbit$ControllerMoveRabbit
bsf$f net/minecraft/world/entity/animal/EntityRabbit$PathfinderGoalRabbitPanic
bsf$g net/minecraft/world/entity/animal/EntityRabbit$PathfinderGoalEatCarrots
bsf$h net/minecraft/world/entity/animal/EntityRabbit$Variant
bsg net/minecraft/world/entity/animal/EntitySalmon
bsh net/minecraft/world/entity/animal/EntitySheep
bsi net/minecraft/world/entity/animal/EntityPerchable
bsj net/minecraft/world/entity/animal/EntitySnowman
bsk net/minecraft/world/entity/animal/EntitySquid
bsk$b net/minecraft/world/entity/animal/EntitySquid$PathfinderGoalSquid
bsl net/minecraft/world/entity/animal/EntityTropicalFish
bsl$a net/minecraft/world/entity/animal/EntityTropicalFish$Base
bsl$b net/minecraft/world/entity/animal/EntityTropicalFish$Variant
bsm net/minecraft/world/entity/animal/EntityTurtle
bsn net/minecraft/world/entity/animal/EntityWaterAnimal
bso net/minecraft/world/entity/animal/EntityWolf
bsp net/minecraft/world/entity/animal/allay/Allay
bsq net/minecraft/world/entity/animal/allay/AllayAi
bss net/minecraft/world/entity/animal/axolotl/Axolotl
bss$d net/minecraft/world/entity/animal/axolotl/Axolotl$Variant
bst net/minecraft/world/entity/animal/axolotl/AxolotlAi
bsu net/minecraft/world/entity/animal/axolotl/PlayDead
bsv net/minecraft/world/entity/animal/axolotl/ValidatePlayDead
bsx net/minecraft/world/entity/animal/camel/Camel
bsy net/minecraft/world/entity/animal/camel/CamelAi
bt net/minecraft/advancements/critereon/CriterionConditionInOpenWater
bta net/minecraft/world/entity/animal/frog/Frog
btb net/minecraft/world/entity/animal/frog/FrogAi
btc net/minecraft/world/entity/animal/frog/ShootTongue
btd net/minecraft/world/entity/animal/frog/Tadpole
bte net/minecraft/world/entity/animal/frog/TadpoleAi
btg net/minecraft/world/entity/animal/goat/Goat
bth net/minecraft/world/entity/animal/goat/GoatAi
btj net/minecraft/world/entity/animal/horse/EntityHorseChestedAbstract
btk net/minecraft/world/entity/animal/horse/EntityHorseAbstract
btl net/minecraft/world/entity/animal/horse/EntityHorseDonkey
btm net/minecraft/world/entity/animal/horse/EntityHorse
btn net/minecraft/world/entity/animal/horse/EntityLlama
btn$d net/minecraft/world/entity/animal/horse/EntityLlama$Variant
bto net/minecraft/world/entity/animal/horse/HorseStyle
btp net/minecraft/world/entity/animal/horse/EntityHorseMule
btq net/minecraft/world/entity/animal/horse/EntityHorseSkeleton
btr net/minecraft/world/entity/animal/horse/PathfinderGoalHorseTrap
bts net/minecraft/world/entity/animal/horse/EntityLlamaTrader
btt net/minecraft/world/entity/animal/horse/HorseColor
btu net/minecraft/world/entity/animal/horse/EntityHorseZombie
btx net/minecraft/world/entity/animal/sniffer/Sniffer
btx$a net/minecraft/world/entity/animal/sniffer/Sniffer$State
bty net/minecraft/world/entity/animal/sniffer/SnifferAi
btz net/minecraft/world/entity/boss/EntityComplexPart
bu net/minecraft/advancements/critereon/CriterionTriggerFishingRodHooked
bua net/minecraft/world/entity/boss/enderdragon/EntityEnderCrystal
bub net/minecraft/world/entity/boss/enderdragon/EntityEnderDragon
bud net/minecraft/world/entity/boss/enderdragon/phases/AbstractDragonController
bue net/minecraft/world/entity/boss/enderdragon/phases/AbstractDragonControllerLanded
buf net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerCharge
bug net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerDying
buh net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerHold
bui net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerHover
buj net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerLandingFly
buk net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerLanding
bul net/minecraft/world/entity/boss/enderdragon/phases/IDragonController
bum net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerLandedAttack
bun net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerLandedFlame
buo net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerLandedSearch
bup net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerStrafe
buq net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerFly
bur net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerPhase
bus net/minecraft/world/entity/boss/enderdragon/phases/DragonControllerManager
buv net/minecraft/world/entity/boss/wither/EntityWither
bux net/minecraft/world/entity/decoration/EntityArmorStand
buy net/minecraft/world/entity/decoration/GlowItemFrame
buz net/minecraft/world/entity/decoration/EntityHanging
bv net/minecraft/advancements/critereon/CriterionConditionFluid
bva net/minecraft/world/entity/decoration/EntityItemFrame
bvb net/minecraft/world/entity/decoration/EntityLeash
bvc net/minecraft/world/entity/decoration/EntityPainting
bvd net/minecraft/world/entity/decoration/PaintingVariant
bve net/minecraft/world/entity/decoration/PaintingVariants
bvg net/minecraft/world/entity/item/EntityFallingBlock
bvh net/minecraft/world/entity/item/EntityItem
bvi net/minecraft/world/entity/item/EntityTNTPrimed
bvk net/minecraft/world/entity/monster/EntityIllagerAbstract
bvl net/minecraft/world/entity/monster/EntitySkeletonAbstract
bvm net/minecraft/world/entity/monster/EntityBlaze
bvm$a net/minecraft/world/entity/monster/EntityBlaze$PathfinderGoalBlazeFireball
bvn net/minecraft/world/entity/monster/EntityCaveSpider
bvo net/minecraft/world/entity/monster/EntityCreeper
bvp net/minecraft/world/entity/monster/ICrossbow
bvq net/minecraft/world/entity/monster/EntityDrowned
bvr net/minecraft/world/entity/monster/EntityGuardianElder
bvs net/minecraft/world/entity/monster/EntityEnderman
bvs$b net/minecraft/world/entity/monster/EntityEnderman$PathfinderGoalEndermanPlaceBlock
bvs$c net/minecraft/world/entity/monster/EntityEnderman$PathfinderGoalPlayerWhoLookedAtTarget
bvs$d net/minecraft/world/entity/monster/EntityEnderman$PathfinderGoalEndermanPickupBlock
bvt net/minecraft/world/entity/monster/EntityEndermite
bvu net/minecraft/world/entity/monster/IMonster
bvv net/minecraft/world/entity/monster/EntityEvoker
bvw net/minecraft/world/entity/monster/EntityGhast
bvw$a net/minecraft/world/entity/monster/EntityGhast$PathfinderGoalGhastMoveTowardsTarget
bvw$b net/minecraft/world/entity/monster/EntityGhast$ControllerGhast
bvw$c net/minecraft/world/entity/monster/EntityGhast$PathfinderGoalGhastAttackTarget
bvw$d net/minecraft/world/entity/monster/EntityGhast$PathfinderGoalGhastIdleMove
bvx net/minecraft/world/entity/monster/EntityGiantZombie
bvy net/minecraft/world/entity/monster/EntityGuardian
bvy$a net/minecraft/world/entity/monster/EntityGuardian$PathfinderGoalGuardianAttack
bvy$b net/minecraft/world/entity/monster/EntityGuardian$EntitySelectorGuardianTargetHumanSquid
bvy$c net/minecraft/world/entity/monster/EntityGuardian$ControllerMoveGuardian
bvz net/minecraft/world/entity/monster/EntityZombieHusk
bw net/minecraft/advancements/critereon/CriterionTriggerImpossible
bwa net/minecraft/world/entity/monster/EntityIllagerIllusioner
bwb net/minecraft/world/entity/monster/EntityMagmaCube
bwc net/minecraft/world/entity/monster/EntityMonster
bwd net/minecraft/world/entity/monster/EntityMonsterPatrolling
bwe net/minecraft/world/entity/monster/EntityPhantom
bwe$a net/minecraft/world/entity/monster/EntityPhantom$AttackPhase
bwf net/minecraft/world/entity/monster/EntityPillager
bwg net/minecraft/world/entity/monster/IRangedEntity
bwh net/minecraft/world/entity/monster/EntityRavager
bwi net/minecraft/world/entity/monster/EntityShulker
bwj net/minecraft/world/entity/monster/EntitySilverfish
bwj$a net/minecraft/world/entity/monster/EntitySilverfish$PathfinderGoalSilverfishHideInBlock
bwj$b net/minecraft/world/entity/monster/EntitySilverfish$PathfinderGoalSilverfishWakeOthers
bwk net/minecraft/world/entity/monster/EntitySkeleton
bwl net/minecraft/world/entity/monster/EntitySlime
bwl$a net/minecraft/world/entity/monster/EntitySlime$PathfinderGoalSlimeNearestPlayer
bwl$b net/minecraft/world/entity/monster/EntitySlime$PathfinderGoalSlimeRandomJump
bwl$c net/minecraft/world/entity/monster/EntitySlime$PathfinderGoalSlimeIdle
bwl$d net/minecraft/world/entity/monster/EntitySlime$ControllerMoveSlime
bwl$e net/minecraft/world/entity/monster/EntitySlime$PathfinderGoalSlimeRandomDirection
bwm net/minecraft/world/entity/monster/EntityIllagerWizard
bwm$a net/minecraft/world/entity/monster/EntityIllagerWizard$Spell
bwm$c net/minecraft/world/entity/monster/EntityIllagerWizard$PathfinderGoalCastSpell
bwn net/minecraft/world/entity/monster/EntitySpider
bwn$a net/minecraft/world/entity/monster/EntitySpider$PathfinderGoalSpiderMeleeAttack
bwn$b net/minecraft/world/entity/monster/EntitySpider$GroupDataSpider
bwn$c net/minecraft/world/entity/monster/EntitySpider$PathfinderGoalSpiderNearestAttackableTarget
bwo net/minecraft/world/entity/monster/EntitySkeletonStray
bwp net/minecraft/world/entity/monster/EntityStrider
bwq net/minecraft/world/entity/monster/EntityVex
bwr net/minecraft/world/entity/monster/EntityVindicator
bws net/minecraft/world/entity/monster/EntityWitch
bwt net/minecraft/world/entity/monster/EntitySkeletonWither
bwu net/minecraft/world/entity/monster/EntityZoglin
bwv net/minecraft/world/entity/monster/EntityZombie
bwv$b net/minecraft/world/entity/monster/EntityZombie$GroupDataZombie
bww net/minecraft/world/entity/monster/EntityZombieVillager
bwx net/minecraft/world/entity/monster/EntityPigZombie
bwy net/minecraft/world/entity/monster/hoglin/EntityHoglin
bwz net/minecraft/world/entity/monster/hoglin/HoglinAI
bx net/minecraft/advancements/critereon/CriterionTriggerInventoryChanged
bxa net/minecraft/world/entity/monster/hoglin/IOglin
bxd net/minecraft/world/entity/monster/piglin/EntityPiglinAbstract
bxe net/minecraft/world/entity/monster/piglin/EntityPiglin
bxf net/minecraft/world/entity/monster/piglin/PiglinAI
bxg net/minecraft/world/entity/monster/piglin/EntityPiglinArmPose
bxh net/minecraft/world/entity/monster/piglin/EntityPiglinBrute
bxi net/minecraft/world/entity/monster/piglin/PiglinBruteAI
bxj net/minecraft/world/entity/monster/piglin/BehaviorRememberHuntedHoglin
bxk net/minecraft/world/entity/monster/piglin/BehaviorStartAdmiringItem
bxl net/minecraft/world/entity/monster/piglin/BehaviorHuntHoglin
bxm net/minecraft/world/entity/monster/piglin/BehaviorStopAdmiringItem
bxn net/minecraft/world/entity/monster/piglin/BehaviorAdmireTimeout
bxo net/minecraft/world/entity/monster/piglin/BehaviorStopAdmiring
bxq net/minecraft/world/entity/monster/warden/AngerLevel
bxr net/minecraft/world/entity/monster/warden/AngerManagement
bxs net/minecraft/world/entity/monster/warden/Warden
bxt net/minecraft/world/entity/monster/warden/WardenAi
bxu net/minecraft/world/entity/monster/warden/WardenSpawnTracker
bxw net/minecraft/world/entity/npc/EntityVillagerAbstract
bxx net/minecraft/world/entity/npc/MobSpawnerCat
bxy net/minecraft/world/entity/npc/MerchantWrapper
bxz net/minecraft/world/entity/npc/InventoryCarrier
by net/minecraft/advancements/critereon/CriterionTriggerItemDurabilityChanged
bya net/minecraft/world/entity/npc/NPC
byb net/minecraft/world/entity/npc/EntityVillager
byc net/minecraft/world/entity/npc/VillagerData
byd net/minecraft/world/entity/npc/VillagerDataHolder
bye net/minecraft/world/entity/npc/VillagerProfession
byf net/minecraft/world/entity/npc/VillagerTrades
byf$f net/minecraft/world/entity/npc/VillagerTrades$IMerchantRecipeOption
byg net/minecraft/world/entity/npc/VillagerType
byh net/minecraft/world/entity/npc/EntityVillagerTrader
byi net/minecraft/world/entity/npc/MobSpawnerTrader
byl net/minecraft/world/entity/player/PlayerAbilities
bym net/minecraft/world/entity/player/EnumChatVisibility
byn net/minecraft/world/entity/player/PlayerInventory
byo net/minecraft/world/entity/player/EntityHuman
byo$a net/minecraft/world/entity/player/EntityHuman$EnumBedResult
byp net/minecraft/world/entity/player/PlayerModelPart
byr net/minecraft/world/entity/player/ProfilePublicKey
bys net/minecraft/world/entity/player/AutoRecipeStackManager
byu net/minecraft/world/entity/projectile/EntityArrow
byu$a net/minecraft/world/entity/projectile/EntityArrow$PickupStatus
byv net/minecraft/world/entity/projectile/EntityFireball
byw net/minecraft/world/entity/projectile/EntityTippedArrow
byx net/minecraft/world/entity/projectile/EntityDragonFireball
byy net/minecraft/world/entity/projectile/EntityEvokerFangs
byz net/minecraft/world/entity/projectile/EntityEnderSignal
bz net/minecraft/advancements/critereon/CriterionConditionItem
bza net/minecraft/world/entity/projectile/EntityFireballFireball
bzb net/minecraft/world/entity/projectile/EntityFireworks
bzc net/minecraft/world/entity/projectile/EntityFishingHook
bzc$a net/minecraft/world/entity/projectile/EntityFishingHook$HookState
bzc$b net/minecraft/world/entity/projectile/EntityFishingHook$WaterPosition
bzd net/minecraft/world/entity/projectile/ItemSupplier
bze net/minecraft/world/entity/projectile/EntityLargeFireball
bzf net/minecraft/world/entity/projectile/EntityLlamaSpit
bzg net/minecraft/world/entity/projectile/IProjectile
bzh net/minecraft/world/entity/projectile/ProjectileHelper
bzi net/minecraft/world/entity/projectile/EntityShulkerBullet
bzj net/minecraft/world/entity/projectile/EntitySmallFireball
bzk net/minecraft/world/entity/projectile/EntitySnowball
bzl net/minecraft/world/entity/projectile/EntitySpectralArrow
bzm net/minecraft/world/entity/projectile/EntityProjectileThrowable
bzn net/minecraft/world/entity/projectile/EntityProjectile
bzo net/minecraft/world/entity/projectile/EntityEgg
bzp net/minecraft/world/entity/projectile/EntityEnderPearl
bzq net/minecraft/world/entity/projectile/EntityThrownExpBottle
bzr net/minecraft/world/entity/projectile/EntityPotion
bzs net/minecraft/world/entity/projectile/EntityThrownTrident
bzt net/minecraft/world/entity/projectile/EntityWitherSkull
bzv net/minecraft/world/entity/raid/Raid
bzv$a net/minecraft/world/entity/raid/Raid$Status
bzv$b net/minecraft/world/entity/raid/Raid$Wave
bzw net/minecraft/world/entity/raid/EntityRaider
bzx net/minecraft/world/entity/raid/PersistentRaid
bzz net/minecraft/world/entity/schedule/Activity
ca net/minecraft/advancements/critereon/ItemUsedOnLocationTrigger
caa net/minecraft/world/entity/schedule/ActivityFrame
cab net/minecraft/world/entity/schedule/Schedule
cac net/minecraft/world/entity/schedule/ScheduleBuilder
cad net/minecraft/world/entity/schedule/ScheduleActivity
caf net/minecraft/world/entity/vehicle/EntityMinecartAbstract
caf$a net/minecraft/world/entity/vehicle/EntityMinecartAbstract$EnumMinecartType
cag net/minecraft/world/entity/vehicle/EntityMinecartContainer
cah net/minecraft/world/entity/vehicle/EntityBoat
cah$a net/minecraft/world/entity/vehicle/EntityBoat$EnumStatus
cah$b net/minecraft/world/entity/vehicle/EntityBoat$EnumBoatType
cai net/minecraft/world/entity/vehicle/ChestBoat
caj net/minecraft/world/entity/vehicle/ContainerEntity
cak net/minecraft/world/entity/vehicle/DismountUtil
cal net/minecraft/world/entity/vehicle/EntityMinecartRideable
cam net/minecraft/world/entity/vehicle/EntityMinecartChest
can net/minecraft/world/entity/vehicle/EntityMinecartCommandBlock
cao net/minecraft/world/entity/vehicle/EntityMinecartFurnace
cap net/minecraft/world/entity/vehicle/EntityMinecartHopper
caq net/minecraft/world/entity/vehicle/EntityMinecartMobSpawner
car net/minecraft/world/entity/vehicle/EntityMinecartTNT
cat net/minecraft/world/flag/FeatureElement
cau net/minecraft/world/flag/FeatureFlag
cav net/minecraft/world/flag/FeatureFlagRegistry
caw net/minecraft/world/flag/FeatureFlagSet
cax net/minecraft/world/flag/FeatureFlagUniverse
cay net/minecraft/world/flag/FeatureFlags
cb net/minecraft/advancements/critereon/CriterionTriggerKilledByCrossbow
cbb net/minecraft/world/food/FoodMetaData
cbc net/minecraft/world/food/FoodInfo
cbd net/minecraft/world/food/Foods
cbf net/minecraft/world/inventory/Container
cbg net/minecraft/world/inventory/ContainerFurnace
cbh net/minecraft/world/inventory/ContainerAnvil
cbi net/minecraft/world/inventory/ContainerBeacon
cbi$a net/minecraft/world/inventory/ContainerBeacon$SlotBeacon
cbj net/minecraft/world/inventory/ContainerBlastFurnace
cbk net/minecraft/world/inventory/ContainerBrewingStand
cbk$b net/minecraft/world/inventory/ContainerBrewingStand$SlotBrewing
cbk$c net/minecraft/world/inventory/ContainerBrewingStand$SlotPotionBottle
cbl net/minecraft/world/inventory/ContainerCartography
cbm net/minecraft/world/inventory/ContainerChest
cbn net/minecraft/world/inventory/ClickAction
cbo net/minecraft/world/inventory/InventoryClickType
cbp net/minecraft/world/inventory/IContainerProperties
cbq net/minecraft/world/inventory/ContainerAccess
cbr net/minecraft/world/inventory/ICrafting
cbs net/minecraft/world/inventory/ContainerSynchronizer
cbt net/minecraft/world/inventory/InventoryCrafting
cbu net/minecraft/world/inventory/ContainerWorkbench
cbv net/minecraft/world/inventory/ContainerProperty
cbw net/minecraft/world/inventory/ContainerDispenser
cbx net/minecraft/world/inventory/ContainerEnchantTable
cby net/minecraft/world/inventory/SlotFurnaceFuel
cbz net/minecraft/world/inventory/ContainerFurnaceFurnace
cc net/minecraft/advancements/critereon/CriterionTriggerKilled
cca net/minecraft/world/inventory/SlotFurnaceResult
ccb net/minecraft/world/inventory/ContainerGrindstone
ccc net/minecraft/world/inventory/ContainerHopper
ccd net/minecraft/world/inventory/ContainerHorse
cce net/minecraft/world/inventory/ContainerPlayer
ccf net/minecraft/world/inventory/ContainerAnvilAbstract
ccg net/minecraft/world/inventory/ItemCombinerMenuSlotDefinition
cch net/minecraft/world/inventory/ContainerLectern
cci net/minecraft/world/inventory/ContainerLoom
ccj net/minecraft/world/inventory/ITileEntityContainer
cck net/minecraft/world/inventory/Containers
cck$a net/minecraft/world/inventory/Containers$Supplier
ccl net/minecraft/world/inventory/InventoryMerchant
ccm net/minecraft/world/inventory/ContainerMerchant
ccn net/minecraft/world/inventory/SlotMerchantResult
cco net/minecraft/world/inventory/InventoryEnderChest
ccp net/minecraft/world/inventory/ContainerRecipeBook
ccq net/minecraft/world/inventory/RecipeBookType
ccr net/minecraft/world/inventory/RecipeHolder
ccs net/minecraft/world/inventory/InventoryCraftResult
cct net/minecraft/world/inventory/SlotResult
ccu net/minecraft/world/inventory/ContainerShulkerBox
ccv net/minecraft/world/inventory/SlotShulkerBox
ccw net/minecraft/world/inventory/ContainerProperties
ccx net/minecraft/world/inventory/Slot
ccy net/minecraft/world/inventory/ContainerSmithing
ccz net/minecraft/world/inventory/ContainerSmoker
cd net/minecraft/advancements/critereon/CriterionTriggerLevitation
cda net/minecraft/world/inventory/AutoRecipeOutput
cdb net/minecraft/world/inventory/ContainerStonecutter
cdc net/minecraft/world/inventory/TransientCraftingContainer
cde net/minecraft/world/inventory/tooltip/BundleTooltip
cdf net/minecraft/world/inventory/tooltip/TooltipComponent
cdh net/minecraft/world/item/AdventureModeCheck
cdi net/minecraft/world/item/ItemAir
cdj net/minecraft/world/item/ItemArmor
cdk net/minecraft/world/item/ArmorMaterial
cdl net/minecraft/world/item/EnumArmorMaterial
cdm net/minecraft/world/item/ItemArmorStand
cdn net/minecraft/world/item/ItemArrow
cdo net/minecraft/world/item/ItemAxe
cdp net/minecraft/world/item/ItemBanner
cdq net/minecraft/world/item/ItemBannerPattern
cdr net/minecraft/world/item/ItemBed
cds net/minecraft/world/item/ItemBlock
cdt net/minecraft/world/item/ItemBoat
cdu net/minecraft/world/item/ItemBoneMeal
cdv net/minecraft/world/item/ItemBook
cdw net/minecraft/world/item/ItemGlassBottle
cdx net/minecraft/world/item/ItemBow
cdy net/minecraft/world/item/ItemSoup
cdz net/minecraft/world/item/BrushItem
ce net/minecraft/advancements/critereon/CriterionConditionLight
cea net/minecraft/world/item/ItemBucket
ceb net/minecraft/world/item/BundleItem
cec net/minecraft/world/item/ItemChorusFruit
ced net/minecraft/world/item/ItemCompass
cee net/minecraft/world/item/ItemWorldMapBase
cef net/minecraft/world/item/CreativeModeTab
ceg net/minecraft/world/item/CreativeModeTabs
ceh net/minecraft/world/item/ItemCrossbow
cei net/minecraft/world/item/ItemDebugStick
cej net/minecraft/world/item/ItemTool
cek net/minecraft/world/item/DiscFragmentItem
cel net/minecraft/world/item/DispensibleContainerItem
cem net/minecraft/world/item/ItemBisected
cen net/minecraft/world/item/EnumColor
ceo net/minecraft/world/item/ItemDye
cep net/minecraft/world/item/ItemArmorColorable
ceq net/minecraft/world/item/ItemHorseArmorDyeable
cer net/minecraft/world/item/IDyeable
ces net/minecraft/world/item/ItemEgg
cet net/minecraft/world/item/ItemElytra
ceu net/minecraft/world/item/ItemMapEmpty
cev net/minecraft/world/item/ItemEnchantedBook
cew net/minecraft/world/item/ItemGoldenAppleEnchanted
cex net/minecraft/world/item/ItemEndCrystal
cey net/minecraft/world/item/ItemEnderEye
cez net/minecraft/world/item/ItemEnderPearl
cf net/minecraft/advancements/critereon/LighthingBoltPredicate
cfa net/minecraft/world/item/Equipable
cfb net/minecraft/world/item/ItemExpBottle
cfc net/minecraft/world/item/ItemFireball
cfd net/minecraft/world/item/ItemFireworks
cfd$a net/minecraft/world/item/ItemFireworks$EffectType
cfe net/minecraft/world/item/ItemFireworksCharge
cff net/minecraft/world/item/ItemFishingRod
cfg net/minecraft/world/item/ItemFlintAndSteel
cfh net/minecraft/world/item/ItemCarrotStick
cfi net/minecraft/world/item/ItemRestricted
cfj net/minecraft/world/item/GlowInkSacItem
cfk net/minecraft/world/item/ItemHanging
cfl net/minecraft/world/item/HangingSignItem
cfm net/minecraft/world/item/ItemHoe
cfn net/minecraft/world/item/ItemHoneyBottle
cfo net/minecraft/world/item/HoneycombItem
cfp net/minecraft/world/item/ItemHorseArmor
cfq net/minecraft/world/item/InkSacItem
cfr net/minecraft/world/item/Instrument
cfs net/minecraft/world/item/InstrumentItem
cft net/minecraft/world/item/Instruments
cfu net/minecraft/world/item/Item
cfu$a net/minecraft/world/item/Item$Info
cfv net/minecraft/world/item/ItemCooldown
cfv$a net/minecraft/world/item/ItemCooldown$Info
cfw net/minecraft/world/item/ItemDisplayContext
cfx net/minecraft/world/item/ItemItemFrame
cfy net/minecraft/world/item/ItemNamedBlock
cfz net/minecraft/world/item/ItemStack
cfz$a net/minecraft/world/item/ItemStack$HideFlags
cg net/minecraft/advancements/critereon/LightningStrikeTrigger
cga net/minecraft/world/item/ItemStackLinkedSet
cgb net/minecraft/world/item/ItemLiquidUtil
cgc net/minecraft/world/item/Items
cgd net/minecraft/world/item/ItemKnowledgeBook
cge net/minecraft/world/item/ItemLeash
cgf net/minecraft/world/item/ItemLingeringPotion
cgg net/minecraft/world/item/ItemWorldMap
cgh net/minecraft/world/item/ItemMilkBucket
cgi net/minecraft/world/item/ItemMinecart
cgj net/minecraft/world/item/MobBucketItem
cgk net/minecraft/world/item/ItemNameTag
cgl net/minecraft/world/item/ItemPickaxe
cgm net/minecraft/world/item/PlaceOnWaterBlockItem
cgn net/minecraft/world/item/ItemSkullPlayer
cgo net/minecraft/world/item/ItemPotion
cgp net/minecraft/world/item/ItemProjectileWeapon
cgq net/minecraft/world/item/EnumItemRarity
cgr net/minecraft/world/item/ItemRecord
cgs net/minecraft/world/item/ItemSaddle
cgt net/minecraft/world/item/ItemScaffolding
cgu net/minecraft/world/item/ItemCooldownPlayer
cgv net/minecraft/world/item/ItemShears
cgw net/minecraft/world/item/ItemShield
cgx net/minecraft/world/item/ItemSpade
cgy net/minecraft/world/item/SignApplicator
cgz net/minecraft/world/item/ItemSign
ch net/minecraft/advancements/critereon/CriterionConditionLocation
cha net/minecraft/world/item/ItemNetherStar
chb net/minecraft/world/item/SmithingTemplateItem
chc net/minecraft/world/item/ItemSnowball
chd net/minecraft/world/item/SolidBucketItem
che net/minecraft/world/item/ItemMonsterEgg
chf net/minecraft/world/item/ItemSpectralArrow
chg net/minecraft/world/item/ItemSplashPotion
chh net/minecraft/world/item/SpyglassItem
chi net/minecraft/world/item/ItemBlockWallable
chj net/minecraft/world/item/ItemSuspiciousStew
chk net/minecraft/world/item/ItemSword
chl net/minecraft/world/item/ItemPotionThrowable
chm net/minecraft/world/item/ToolMaterial
chn net/minecraft/world/item/ItemToolMaterial
cho net/minecraft/world/item/EnumToolMaterial
chp net/minecraft/world/item/ItemTippedArrow
chq net/minecraft/world/item/TooltipFlag
chr net/minecraft/world/item/ItemTrident
chs net/minecraft/world/item/EnumAnimation
cht net/minecraft/world/item/ItemVanishable
chu net/minecraft/world/item/ItemBookAndQuill
chv net/minecraft/world/item/ItemWrittenBook
chw net/minecraft/world/item/alchemy/PotionRegistry
chx net/minecraft/world/item/alchemy/PotionBrewer
chx$a net/minecraft/world/item/alchemy/PotionBrewer$PredicatedCombination
chy net/minecraft/world/item/alchemy/PotionUtil
chz net/minecraft/world/item/alchemy/Potions
ci net/minecraft/advancements/critereon/CriterionTriggerPlayerGeneratesContainerLoot
cib net/minecraft/world/item/armortrim/ArmorTrim
cic net/minecraft/world/item/armortrim/TrimMaterial
cid net/minecraft/world/item/armortrim/TrimMaterials
cie net/minecraft/world/item/armortrim/TrimPattern
cif net/minecraft/world/item/armortrim/TrimPatterns
cih net/minecraft/world/item/context/BlockActionContext
cii net/minecraft/world/item/context/BlockActionContextDirectional
cij net/minecraft/world/item/context/ItemActionContext
cil net/minecraft/world/item/crafting/RecipeCooking
cim net/minecraft/world/item/crafting/RecipeArmorDye
cin net/minecraft/world/item/crafting/RecipeBannerDuplicate
cio net/minecraft/world/item/crafting/RecipeBlasting
cip net/minecraft/world/item/crafting/RecipeBookClone
ciq net/minecraft/world/item/crafting/RecipeCampfire
cir net/minecraft/world/item/crafting/CookingBookCategory
cis net/minecraft/world/item/crafting/CraftingBookCategory
cit net/minecraft/world/item/crafting/RecipeCrafting
ciu net/minecraft/world/item/crafting/IRecipeComplex
civ net/minecraft/world/item/crafting/DecoratedPotRecipe
ciw net/minecraft/world/item/crafting/RecipeFireworks
cix net/minecraft/world/item/crafting/RecipeFireworksFade
ciy net/minecraft/world/item/crafting/RecipeFireworksStar
ciz net/minecraft/world/item/crafting/RecipeItemStack
ciz$a net/minecraft/world/item/crafting/RecipeItemStack$StackProvider
ciz$c net/minecraft/world/item/crafting/RecipeItemStack$Provider
cj net/minecraft/advancements/critereon/CriterionConditionValue
cj$c net/minecraft/advancements/critereon/CriterionConditionValue$DoubleRange
cj$d net/minecraft/advancements/critereon/CriterionConditionValue$IntegerRange
cja net/minecraft/world/item/crafting/RecipeMapClone
cjb net/minecraft/world/item/crafting/RecipeMapExtend
cjc net/minecraft/world/item/crafting/IRecipe
cjd net/minecraft/world/item/crafting/CraftingManager
cje net/minecraft/world/item/crafting/RecipeSerializer
cjf net/minecraft/world/item/crafting/Recipes
cjg net/minecraft/world/item/crafting/RecipeRepair
cjh net/minecraft/world/item/crafting/ShapedRecipes
cji net/minecraft/world/item/crafting/ShapelessRecipes
cjj net/minecraft/world/item/crafting/RecipiesShield
cjk net/minecraft/world/item/crafting/RecipeShulkerBox
cjl net/minecraft/world/item/crafting/RecipeSerializerCooking
cjm net/minecraft/world/item/crafting/SimpleCraftingRecipeSerializer
cjn net/minecraft/world/item/crafting/RecipeSingleItem
cjo net/minecraft/world/item/crafting/FurnaceRecipe
cjp net/minecraft/world/item/crafting/SmithingRecipe
cjq net/minecraft/world/item/crafting/SmithingTransformRecipe
cjr net/minecraft/world/item/crafting/SmithingTrimRecipe
cjs net/minecraft/world/item/crafting/RecipeSmoking
cjt net/minecraft/world/item/crafting/RecipeStonecutting
cju net/minecraft/world/item/crafting/RecipeSuspiciousStew
cjv net/minecraft/world/item/crafting/RecipeTippedArrow
cjx net/minecraft/world/item/enchantment/EnchantmentArrowDamage
cjy net/minecraft/world/item/enchantment/EnchantmentFlameArrows
cjz net/minecraft/world/item/enchantment/EnchantmentInfiniteArrows
ck net/minecraft/advancements/critereon/CriterionConditionMobEffect
cka net/minecraft/world/item/enchantment/EnchantmentArrowKnockback
ckb net/minecraft/world/item/enchantment/EnchantmentPiercing
ckc net/minecraft/world/item/enchantment/EnchantmentBinding
ckd net/minecraft/world/item/enchantment/EnchantmentWeaponDamage
cke net/minecraft/world/item/enchantment/EnchantmentDurability
ckf net/minecraft/world/item/enchantment/EnchantmentDigging
ckg net/minecraft/world/item/enchantment/Enchantment
ckg$a net/minecraft/world/item/enchantment/Enchantment$Rarity
ckh net/minecraft/world/item/enchantment/EnchantmentSlotType
cki net/minecraft/world/item/enchantment/EnchantmentManager
ckj net/minecraft/world/item/enchantment/WeightedRandomEnchant
ckk net/minecraft/world/item/enchantment/Enchantments
ckl net/minecraft/world/item/enchantment/EnchantmentFire
ckm net/minecraft/world/item/enchantment/EnchantmentLure
ckn net/minecraft/world/item/enchantment/EnchantmentFrostWalker
cko net/minecraft/world/item/enchantment/EnchantmentKnockback
ckp net/minecraft/world/item/enchantment/EnchantmentLootBonus
ckq net/minecraft/world/item/enchantment/EnchantmentMending
ckr net/minecraft/world/item/enchantment/EnchantmentMultishot
cks net/minecraft/world/item/enchantment/EnchantmentOxygen
ckt net/minecraft/world/item/enchantment/EnchantmentProtection
ckt$a net/minecraft/world/item/enchantment/EnchantmentProtection$DamageType
cku net/minecraft/world/item/enchantment/EnchantmentQuickCharge
ckv net/minecraft/world/item/enchantment/EnchantmentSoulSpeed
ckw net/minecraft/world/item/enchantment/EnchantmentSweeping
ckx net/minecraft/world/item/enchantment/SwiftSneakEnchantment
cky net/minecraft/world/item/enchantment/EnchantmentThorns
ckz net/minecraft/world/item/enchantment/EnchantmentTridentChanneling
cl net/minecraft/advancements/critereon/CriterionConditionNBT
cla net/minecraft/world/item/enchantment/EnchantmentTridentImpaling
clb net/minecraft/world/item/enchantment/EnchantmentTridentLoyalty
clc net/minecraft/world/item/enchantment/EnchantmentTridentRiptide
cld net/minecraft/world/item/enchantment/EnchantmentSilkTouch
cle net/minecraft/world/item/enchantment/EnchantmentVanishing
clf net/minecraft/world/item/enchantment/EnchantmentDepthStrider
clg net/minecraft/world/item/enchantment/EnchantmentWaterWorker
clj net/minecraft/world/item/trading/IMerchant
clk net/minecraft/world/item/trading/MerchantRecipe
cll net/minecraft/world/item/trading/MerchantRecipeList
cln net/minecraft/world/level/CommandBlockListenerAbstract
clo net/minecraft/world/level/MobSpawnerAbstract
clp net/minecraft/world/level/IBlockLightAccess
clq net/minecraft/world/level/VoxelShapeSpliterator
clr net/minecraft/world/level/BlockActionData
cls net/minecraft/world/level/IBlockAccess
clt net/minecraft/world/level/ChunkCoordIntPair
clu net/minecraft/world/level/ClipBlockStateContext
clv net/minecraft/world/level/RayTrace
clv$a net/minecraft/world/level/RayTrace$BlockCollisionOption
clv$b net/minecraft/world/level/RayTrace$FluidCollisionOption
clw net/minecraft/world/level/ICollisionAccess
clx net/minecraft/world/level/ColorResolver
cly net/minecraft/world/level/ICombinedAccess
clz net/minecraft/world/level/MobSpawner
cm net/minecraft/advancements/critereon/PickedUpItemTrigger
cma net/minecraft/world/level/DataPackConfiguration
cmb net/minecraft/world/level/BlockAccessAir
cmc net/minecraft/world/level/ExplosionDamageCalculatorEntity
cmd net/minecraft/world/level/IEntityAccess
cme net/minecraft/world/level/Explosion
cme$a net/minecraft/world/level/Explosion$Effect
cmf net/minecraft/world/level/ExplosionDamageCalculator
cmg net/minecraft/world/level/FoliageColor
cmh net/minecraft/world/level/ForcedChunk
cmi net/minecraft/world/level/GameRules
cmi$a net/minecraft/world/level/GameRules$GameRuleBoolean
cmi$b net/minecraft/world/level/GameRules$GameRuleCategory
cmi$c net/minecraft/world/level/GameRules$GameRuleVisitor
cmi$d net/minecraft/world/level/GameRules$GameRuleInt
cmi$e net/minecraft/world/level/GameRules$GameRuleKey
cmi$f net/minecraft/world/level/GameRules$GameRuleDefinition
cmi$g net/minecraft/world/level/GameRules$GameRuleValue
cmj net/minecraft/world/level/EnumGamemode
cmk net/minecraft/world/level/GrassColor
cml net/minecraft/world/level/IMaterial
cmm net/minecraft/world/level/World
cmn net/minecraft/world/level/GeneratorAccess
cmo net/minecraft/world/level/LevelHeightAccessor
cmp net/minecraft/world/level/IWorldReader
cmq net/minecraft/world/level/WorldSettings
cmr net/minecraft/world/level/VirtualLevelWritable
cms net/minecraft/world/level/VirtualLevelReadable
cmt net/minecraft/world/level/IWorldTime
cmu net/minecraft/world/level/IWorldWriter
cmv net/minecraft/world/level/EnumSkyBlock
cmw net/minecraft/world/level/LocalMobCapCalculator
cmx net/minecraft/world/level/SpawnerCreature
cmy net/minecraft/world/level/BlockColumn
cmz net/minecraft/world/level/ChunkCache
cn net/minecraft/advancements/critereon/CriterionTriggerPlayerHurtEntity
cna net/minecraft/world/level/SpawnerCreatureProbabilities
cnb net/minecraft/world/level/WorldAccess
cnc net/minecraft/world/level/SignalGetter
cnd net/minecraft/world/level/MobSpawnerData
cne net/minecraft/world/level/StructureManager
cnf net/minecraft/world/level/WorldDataConfiguration
cng net/minecraft/world/level/GeneratorAccessSeed
cnh net/minecraft/world/level/biome/CaveSound
cni net/minecraft/world/level/biome/CaveSoundSettings
cnj net/minecraft/world/level/biome/BiomeParticles
cnk net/minecraft/world/level/biome/BiomeBase
cnk$b net/minecraft/world/level/biome/BiomeBase$ClimateSettings
cnk$c net/minecraft/world/level/biome/BiomeBase$Precipitation
cnk$d net/minecraft/world/level/biome/BiomeBase$TemperatureModifier
cnl net/minecraft/world/level/biome/BiomeSettingsGeneration
cnm net/minecraft/world/level/biome/BiomeManager
cnm$a net/minecraft/world/level/biome/BiomeManager$Provider
cnn net/minecraft/world/level/biome/BiomeResolver
cno net/minecraft/world/level/biome/WorldChunkManager
cnp net/minecraft/world/level/biome/BiomeSources
cnq net/minecraft/world/level/biome/BiomeFog
cnq$b net/minecraft/world/level/biome/BiomeFog$GrassColor
cnr net/minecraft/world/level/biome/Biomes
cns net/minecraft/world/level/biome/WorldChunkManagerCheckerBoard
cnt net/minecraft/world/level/biome/Climate
cnt$f net/minecraft/world/level/biome/Climate$Sampler
cnu net/minecraft/world/level/biome/FeatureSorter
cnv net/minecraft/world/level/biome/WorldChunkManagerHell
cnw net/minecraft/world/level/biome/BiomeSettingsMobs
cnx net/minecraft/world/level/biome/WorldChunkManagerMultiNoise
cny net/minecraft/world/level/biome/MultiNoiseBiomeSourceParameterList
cnz net/minecraft/world/level/biome/MultiNoiseBiomeSourceParameterLists
co net/minecraft/advancements/critereon/CriterionTriggerPlayerInteractedWithEntity
coa net/minecraft/world/level/biome/OverworldBiomeBuilder
cob net/minecraft/world/level/biome/WorldChunkManagerTheEnd
cod net/minecraft/world/level/block/BlockBannerAbstract
coe net/minecraft/world/level/block/AbstractCandleBlock
cof net/minecraft/world/level/block/AbstractCauldronBlock
cog net/minecraft/world/level/block/BlockChestAbstract
coh net/minecraft/world/level/block/BlockFurnace
coi net/minecraft/world/level/block/BlockGlassAbstract
coj net/minecraft/world/level/block/BlockSkullAbstract
cok net/minecraft/world/level/block/BlockAir
col net/minecraft/world/level/block/AmethystBlock
com net/minecraft/world/level/block/AmethystClusterBlock
coo net/minecraft/world/level/block/BlockAnvil
cop net/minecraft/world/level/block/BlockStemAttached
coq net/minecraft/world/level/block/AzaleaBlock
cor net/minecraft/world/level/block/BlockBambooSapling
cos net/minecraft/world/level/block/BlockBamboo
cot net/minecraft/world/level/block/BlockBanner
cou net/minecraft/world/level/block/BlockBarrel
cov net/minecraft/world/level/block/BlockBarrier
cow net/minecraft/world/level/block/BlockCoralFanAbstract
cox net/minecraft/world/level/block/BlockCoralDead
coy net/minecraft/world/level/block/BlockCoralBase
coz net/minecraft/world/level/block/BlockCoralFanWallAbstract
cp net/minecraft/advancements/critereon/CriterionConditionPlayer
cpa net/minecraft/world/level/block/BlockTileEntity
cpb net/minecraft/world/level/block/BlockFireAbstract
cpc net/minecraft/world/level/block/BlockPressurePlateAbstract
cpd net/minecraft/world/level/block/BlockMinecartTrackAbstract
cpe net/minecraft/world/level/block/IBeaconBeam
cpf net/minecraft/world/level/block/BlockBeacon
cpg net/minecraft/world/level/block/BlockBed
cph net/minecraft/world/level/block/BlockBeehive
cpi net/minecraft/world/level/block/BlockBeetroot
cpj net/minecraft/world/level/block/BlockBell
cpk net/minecraft/world/level/block/BigDripleafBlock
cpl net/minecraft/world/level/block/BigDripleafStemBlock
cpm net/minecraft/world/level/block/BlockBlastFurnace
cpn net/minecraft/world/level/block/Block
cpo net/minecraft/world/level/block/Blocks
cpp net/minecraft/world/level/block/IBlockFragilePlantElement
cpq net/minecraft/world/level/block/BlockBrewingStand
cpr net/minecraft/world/level/block/BrushableBlock
cps net/minecraft/world/level/block/BlockBubbleColumn
cpt net/minecraft/world/level/block/IFluidSource
cpu net/minecraft/world/level/block/BuddingAmethystBlock
cpv net/minecraft/world/level/block/BlockPlant
cpw net/minecraft/world/level/block/BlockButtonAbstract
cpx net/minecraft/world/level/block/BlockCactus
cpy net/minecraft/world/level/block/BlockCake
cpz net/minecraft/world/level/block/CalibratedSculkSensorBlock
cq net/minecraft/advancements/critereon/PlayerTrigger
cqa net/minecraft/world/level/block/BlockCampfire
cqb net/minecraft/world/level/block/CandleBlock
cqc net/minecraft/world/level/block/CandleCakeBlock
cqd net/minecraft/world/level/block/CarpetBlock
cqe net/minecraft/world/level/block/BlockCarrots
cqf net/minecraft/world/level/block/BlockCartographyTable
cqg net/minecraft/world/level/block/BlockPumpkinCarved
cqh net/minecraft/world/level/block/BlockCauldron
cqi net/minecraft/world/level/block/CaveVines
cqj net/minecraft/world/level/block/CaveVinesBlock
cqk net/minecraft/world/level/block/CaveVinesPlantBlock
cql net/minecraft/world/level/block/CeilingHangingSignBlock
cqm net/minecraft/world/level/block/BlockChain
cqn net/minecraft/world/level/block/ChangeOverTimeBlock
cqo net/minecraft/world/level/block/CherryLeavesBlock
cqp net/minecraft/world/level/block/BlockChest
cqq net/minecraft/world/level/block/ChiseledBookShelfBlock
cqr net/minecraft/world/level/block/BlockChorusFlower
cqs net/minecraft/world/level/block/BlockChorusFruit
cqt net/minecraft/world/level/block/BlockCocoa
cqu net/minecraft/world/level/block/BlockCommand
cqv net/minecraft/world/level/block/BlockRedstoneComparator
cqw net/minecraft/world/level/block/BlockComposter
cqw$a net/minecraft/world/level/block/BlockComposter$ContainerEmpty
cqw$b net/minecraft/world/level/block/BlockComposter$ContainerInput
cqw$c net/minecraft/world/level/block/BlockComposter$ContainerOutput
cqx net/minecraft/world/level/block/BlockConcretePowder
cqy net/minecraft/world/level/block/BlockConduit
cqz net/minecraft/world/level/block/BlockCoral
cr net/minecraft/advancements/critereon/RecipeCraftedTrigger
cra net/minecraft/world/level/block/BlockCoralFan
crb net/minecraft/world/level/block/BlockCoralPlant
crc net/minecraft/world/level/block/BlockCoralFanWall
crd net/minecraft/world/level/block/BlockWorkbench
cre net/minecraft/world/level/block/BlockCrops
crf net/minecraft/world/level/block/BlockTall
crg net/minecraft/world/level/block/BlockCryingObsidian
crh net/minecraft/world/level/block/BlockDaylightDetector
cri net/minecraft/world/level/block/BlockDeadBush
crj net/minecraft/world/level/block/DecoratedPotBlock
crk net/minecraft/world/level/block/BlockMinecartDetector
crl net/minecraft/world/level/block/BlockDiodeAbstract
crm net/minecraft/world/level/block/BlockDirectional
crn net/minecraft/world/level/block/BlockGrassPath
cro net/minecraft/world/level/block/BlockDispenser
crp net/minecraft/world/level/block/BlockDoor
crq net/minecraft/world/level/block/DoubleBlockFinder
crq$a net/minecraft/world/level/block/DoubleBlockFinder$BlockType
crq$b net/minecraft/world/level/block/DoubleBlockFinder$Combiner
crq$c net/minecraft/world/level/block/DoubleBlockFinder$Result
crq$c$a net/minecraft/world/level/block/DoubleBlockFinder$Result$Double
crq$c$b net/minecraft/world/level/block/DoubleBlockFinder$Result$Single
crr net/minecraft/world/level/block/BlockTallPlant
crs net/minecraft/world/level/block/BlockDragonEgg
crt net/minecraft/world/level/block/DropExperienceBlock
cru net/minecraft/world/level/block/BlockDropper
crv net/minecraft/world/level/block/BlockEnchantmentTable
crw net/minecraft/world/level/block/BlockEndGateway
crx net/minecraft/world/level/block/BlockEnderPortal
cry net/minecraft/world/level/block/BlockEnderPortalFrame
crz net/minecraft/world/level/block/BlockEndRod
cs net/minecraft/advancements/critereon/CriterionTriggerRecipeUnlocked
csa net/minecraft/world/level/block/BlockEnderChest
csb net/minecraft/world/level/block/ITileEntity
csc net/minecraft/world/level/block/EquipableCarvedPumpkinBlock
csd net/minecraft/world/level/block/BlockAttachable
cse net/minecraft/world/level/block/Fallable
csf net/minecraft/world/level/block/BlockFalling
csg net/minecraft/world/level/block/BlockSoil
csh net/minecraft/world/level/block/BlockFence
csi net/minecraft/world/level/block/BlockFenceGate
csj net/minecraft/world/level/block/BlockFire
csk net/minecraft/world/level/block/BlockFletchingTable
csl net/minecraft/world/level/block/BlockFlowers
csm net/minecraft/world/level/block/BlockFlowerPot
csn net/minecraft/world/level/block/FrogspawnBlock
cso net/minecraft/world/level/block/BlockIceFrost
csp net/minecraft/world/level/block/BlockFungi
csq net/minecraft/world/level/block/BlockFurnaceFurace
csr net/minecraft/world/level/block/GameMasterBlock
css net/minecraft/world/level/block/BlockGlass
cst net/minecraft/world/level/block/BlockGlazedTerracotta
csu net/minecraft/world/level/block/GlowLichenBlock
csv net/minecraft/world/level/block/BlockGrass
csw net/minecraft/world/level/block/BlockGravel
csx net/minecraft/world/level/block/BlockGrindstone
csy net/minecraft/world/level/block/BlockGrowingAbstract
csz net/minecraft/world/level/block/BlockGrowingStem
ct net/minecraft/advancements/critereon/LootSerializationContext
cta net/minecraft/world/level/block/BlockGrowingTop
ctb net/minecraft/world/level/block/BlockHalfTransparent
ctc net/minecraft/world/level/block/HangingRootsBlock
ctd net/minecraft/world/level/block/BlockHay
cte net/minecraft/world/level/block/BlockHoney
ctf net/minecraft/world/level/block/BlockHopper
ctg net/minecraft/world/level/block/BlockFacingHorizontal
cth net/minecraft/world/level/block/BlockHugeMushroom
cti net/minecraft/world/level/block/BlockIce
ctj net/minecraft/world/level/block/BlockMonsterEggs
ctk net/minecraft/world/level/block/InfestedRotatedPillarBlock
ctl net/minecraft/world/level/block/BlockIronBars
ctm net/minecraft/world/level/block/BlockJigsaw
ctn net/minecraft/world/level/block/BlockJukeBox
cto net/minecraft/world/level/block/BlockKelp
ctp net/minecraft/world/level/block/BlockKelpPlant
ctq net/minecraft/world/level/block/BlockLadder
ctr net/minecraft/world/level/block/BlockLantern
cts net/minecraft/world/level/block/LavaCauldronBlock
ctt net/minecraft/world/level/block/LayeredCauldronBlock
ctu net/minecraft/world/level/block/BlockLeaves
ctv net/minecraft/world/level/block/BlockLectern
ctx net/minecraft/world/level/block/BlockLever
cty net/minecraft/world/level/block/LightBlock
ctz net/minecraft/world/level/block/LightningRodBlock
cu net/minecraft/advancements/critereon/CriterionTriggerShotCrossbow
cua net/minecraft/world/level/block/BlockFluids
cub net/minecraft/world/level/block/IFluidContainer
cuc net/minecraft/world/level/block/BlockLoom
cud net/minecraft/world/level/block/BlockMagma
cue net/minecraft/world/level/block/MangroveLeavesBlock
cuf net/minecraft/world/level/block/MangrovePropaguleBlock
cug net/minecraft/world/level/block/MangroveRootsBlock
cuh net/minecraft/world/level/block/BlockMelon
cui net/minecraft/world/level/block/EnumBlockMirror
cuj net/minecraft/world/level/block/MossBlock
cuk net/minecraft/world/level/block/MudBlock
cul net/minecraft/world/level/block/MultifaceBlock
cum net/minecraft/world/level/block/MultifaceSpreader
cun net/minecraft/world/level/block/BlockMushroom
cuo net/minecraft/world/level/block/BlockMycel
cup net/minecraft/world/level/block/BlockPortal
cuq net/minecraft/world/level/block/BlockNetherSprouts
cur net/minecraft/world/level/block/BlockNetherVinesUtil
cus net/minecraft/world/level/block/BlockNetherWart
cut net/minecraft/world/level/block/BlockNetherrack
cuu net/minecraft/world/level/block/BlockNote
cuv net/minecraft/world/level/block/BlockNylium
cuw net/minecraft/world/level/block/BlockObserver
cux net/minecraft/world/level/block/PiglinWallSkullBlock
cuy net/minecraft/world/level/block/PinkPetalsBlock
cuz net/minecraft/world/level/block/BlockSprawling
cv net/minecraft/advancements/critereon/CriterionTriggerAbstract
cva net/minecraft/world/level/block/PitcherCropBlock
cvb net/minecraft/world/level/block/BlockSkullPlayer
cvc net/minecraft/world/level/block/BlockSkullPlayerWall
cvd net/minecraft/world/level/block/PointedDripstoneBlock
cve net/minecraft/world/level/block/BlockPotatoes
cvf net/minecraft/world/level/block/PowderSnowBlock
cvg net/minecraft/world/level/block/PowderSnowCauldronBlock
cvh net/minecraft/world/level/block/BlockPowered
cvi net/minecraft/world/level/block/BlockPoweredRail
cvj net/minecraft/world/level/block/BlockPressurePlateBinary
cvj$a net/minecraft/world/level/block/BlockPressurePlateBinary$EnumMobType
cvk net/minecraft/world/level/block/BlockPumpkin
cvl net/minecraft/world/level/block/BlockMinecartTrack
cvm net/minecraft/world/level/block/MinecartTrackLogic
cvn net/minecraft/world/level/block/BlockRedstoneOre
cvo net/minecraft/world/level/block/BlockRedstoneWire
cvp net/minecraft/world/level/block/BlockRedstoneLamp
cvq net/minecraft/world/level/block/BlockRedstoneTorch
cvq$a net/minecraft/world/level/block/BlockRedstoneTorch$RedstoneUpdateInfo
cvr net/minecraft/world/level/block/BlockRedstoneTorchWall
cvs net/minecraft/world/level/block/EnumRenderType
cvt net/minecraft/world/level/block/BlockRepeater
cvu net/minecraft/world/level/block/BlockRespawnAnchor
cvv net/minecraft/world/level/block/RodBlock
cvw net/minecraft/world/level/block/RootedDirtBlock
cvx net/minecraft/world/level/block/BlockRoots
cvy net/minecraft/world/level/block/BlockRotatable
cvz net/minecraft/world/level/block/EnumBlockRotation
cw net/minecraft/advancements/critereon/CriterionSlideDownBlock
cwa net/minecraft/world/level/block/BlockSand
cwb net/minecraft/world/level/block/BlockSapling
cwc net/minecraft/world/level/block/BlockScaffolding
cwd net/minecraft/world/level/block/SculkBehaviour
cwe net/minecraft/world/level/block/SculkBlock
cwf net/minecraft/world/level/block/SculkCatalystBlock
cwg net/minecraft/world/level/block/SculkSensorBlock
cwh net/minecraft/world/level/block/SculkShriekerBlock
cwi net/minecraft/world/level/block/SculkSpreader
cwj net/minecraft/world/level/block/SculkVeinBlock
cwk net/minecraft/world/level/block/BlockSeaPickle
cwl net/minecraft/world/level/block/SeagrassBlock
cwm net/minecraft/world/level/block/BlockShulkerBox
cwn net/minecraft/world/level/block/BlockSign
cwo net/minecraft/world/level/block/IBlockWaterlogged
cwp net/minecraft/world/level/block/BlockSkull
cwp$b net/minecraft/world/level/block/BlockSkull$Type
cwq net/minecraft/world/level/block/BlockStepAbstract
cwr net/minecraft/world/level/block/BlockSlime
cws net/minecraft/world/level/block/SmallDripleafBlock
cwt net/minecraft/world/level/block/BlockSmithingTable
cwu net/minecraft/world/level/block/BlockSmoker
cwv net/minecraft/world/level/block/SnifferEggBlock
cww net/minecraft/world/level/block/BlockSnow
cwx net/minecraft/world/level/block/BlockDirtSnow
cwy net/minecraft/world/level/block/BlockSoulFire
cwz net/minecraft/world/level/block/BlockSlowSand
cx net/minecraft/advancements/critereon/SlimePredicate
cxa net/minecraft/world/level/block/SoundEffectType
cxb net/minecraft/world/level/block/BlockMobSpawner
cxc net/minecraft/world/level/block/BlockSponge
cxd net/minecraft/world/level/block/SporeBlossomBlock
cxe net/minecraft/world/level/block/BlockDirtSnowSpreadable
cxf net/minecraft/world/level/block/BlockStainedGlass
cxg net/minecraft/world/level/block/BlockStainedGlassPane
cxh net/minecraft/world/level/block/BlockStairs
cxi net/minecraft/world/level/block/BlockFloorSign
cxj net/minecraft/world/level/block/BlockStem
cxk net/minecraft/world/level/block/BlockStemmed
cxl net/minecraft/world/level/block/BlockStonecutter
cxm net/minecraft/world/level/block/BlockStructure
cxn net/minecraft/world/level/block/BlockStructureVoid
cxo net/minecraft/world/level/block/BlockReed
cxp net/minecraft/world/level/block/EnumBlockSupport
cxq net/minecraft/world/level/block/SuspiciousEffectHolder
cxr net/minecraft/world/level/block/BlockSweetBerryBush
cxs net/minecraft/world/level/block/BlockTallPlantFlower
cxt net/minecraft/world/level/block/BlockLongGrass
cxu net/minecraft/world/level/block/TallSeagrassBlock
cxv net/minecraft/world/level/block/BlockTarget
cxw net/minecraft/world/level/block/TintedGlassBlock
cxx net/minecraft/world/level/block/BlockTNT
cxy net/minecraft/world/level/block/BlockTorch
cxz net/minecraft/world/level/block/TorchflowerCropBlock
cy net/minecraft/advancements/critereon/StartRidingTrigger
cya net/minecraft/world/level/block/BlockTrapdoor
cyb net/minecraft/world/level/block/BlockChestTrapped
cyc net/minecraft/world/level/block/BlockTripwire
cyd net/minecraft/world/level/block/BlockTripwireHook
cye net/minecraft/world/level/block/BlockTurtleEgg
cyf net/minecraft/world/level/block/BlockTwistingVines
cyg net/minecraft/world/level/block/BlockTwistingVinesPlant
cyh net/minecraft/world/level/block/BlockVine
cyi net/minecraft/world/level/block/BlockBannerWall
cyj net/minecraft/world/level/block/BlockCobbleWall
cyk net/minecraft/world/level/block/WallHangingSignBlock
cyl net/minecraft/world/level/block/BlockWallSign
cym net/minecraft/world/level/block/BlockSkullWall
cyn net/minecraft/world/level/block/BlockTorchWall
cyo net/minecraft/world/level/block/BlockWaterLily
cyp net/minecraft/world/level/block/WeatheringCopper
cyq net/minecraft/world/level/block/WeatheringCopperFullBlock
cyr net/minecraft/world/level/block/WeatheringCopperSlabBlock
cys net/minecraft/world/level/block/WeatheringCopperStairBlock
cyt net/minecraft/world/level/block/BlockWeb
cyu net/minecraft/world/level/block/BlockWeepingVines
cyv net/minecraft/world/level/block/BlockWeepingVinesPlant
cyw net/minecraft/world/level/block/BlockPressurePlateWeighted
cyx net/minecraft/world/level/block/BlockWetSponge
cyy net/minecraft/world/level/block/BlockWitherRose
cyz net/minecraft/world/level/block/BlockWitherSkull
cz net/minecraft/advancements/critereon/CriterionTriggerProperties
cza net/minecraft/world/level/block/BlockWitherSkullWall
czb net/minecraft/world/level/block/BlockCarpet
czc net/minecraft/world/level/block/entity/TileEntityFurnace
czd net/minecraft/world/level/block/entity/TileEntityBanner
cze net/minecraft/world/level/block/entity/EnumBannerPatternType
czf net/minecraft/world/level/block/entity/BannerPatterns
czg net/minecraft/world/level/block/entity/TileEntityBarrel
czh net/minecraft/world/level/block/entity/TileEntityContainer
czi net/minecraft/world/level/block/entity/TileEntityBeacon
czi$a net/minecraft/world/level/block/entity/TileEntityBeacon$BeaconColorTracker
czj net/minecraft/world/level/block/entity/TileEntityBed
czk net/minecraft/world/level/block/entity/TileEntityBeehive
czk$a net/minecraft/world/level/block/entity/TileEntityBeehive$HiveBee
czk$b net/minecraft/world/level/block/entity/TileEntityBeehive$ReleaseStatus
czl net/minecraft/world/level/block/entity/TileEntityBell
czm net/minecraft/world/level/block/entity/TileEntityBlastFurnace
czn net/minecraft/world/level/block/entity/TileEntity
czo net/minecraft/world/level/block/entity/BlockEntityTicker
czp net/minecraft/world/level/block/entity/TileEntityTypes
czq net/minecraft/world/level/block/entity/TileEntityBrewingStand
czr net/minecraft/world/level/block/entity/BrushableBlockEntity
czs net/minecraft/world/level/block/entity/CalibratedSculkSensorBlockEntity
czt net/minecraft/world/level/block/entity/TileEntityCampfire
czu net/minecraft/world/level/block/entity/TileEntityChest
czv net/minecraft/world/level/block/entity/ChestLidController
czw net/minecraft/world/level/block/entity/ChiseledBookShelfBlockEntity
czx net/minecraft/world/level/block/entity/TileEntityCommand
czx$a net/minecraft/world/level/block/entity/TileEntityCommand$Type
czy net/minecraft/world/level/block/entity/TileEntityComparator
czz net/minecraft/world/level/block/entity/TileEntityConduit
da net/minecraft/advancements/critereon/CriterionTriggerSummonedEntity
daa net/minecraft/world/level/block/entity/ContainerOpenersCounter
dab net/minecraft/world/level/block/entity/TileEntityLightDetector
dac net/minecraft/world/level/block/entity/DecoratedPotBlockEntity
dad net/minecraft/world/level/block/entity/DecoratedPotPatterns
dae net/minecraft/world/level/block/entity/TileEntityDispenser
daf net/minecraft/world/level/block/entity/TileEntityDropper
dag net/minecraft/world/level/block/entity/TileEntityEnchantTable
dah net/minecraft/world/level/block/entity/TileEntityEnderChest
dai net/minecraft/world/level/block/entity/TileEntityFurnaceFurnace
daj net/minecraft/world/level/block/entity/HangingSignBlockEntity
dak net/minecraft/world/level/block/entity/IHopper
dal net/minecraft/world/level/block/entity/TileEntityHopper
dam net/minecraft/world/level/block/entity/TileEntityJigsaw
dam$a net/minecraft/world/level/block/entity/TileEntityJigsaw$JointType
dan net/minecraft/world/level/block/entity/TileEntityJukeBox
dao net/minecraft/world/level/block/entity/TileEntityLectern
dap net/minecraft/world/level/block/entity/LidBlockEntity
daq net/minecraft/world/level/block/entity/TileEntityLootable
dar net/minecraft/world/level/block/entity/SculkCatalystBlockEntity
das net/minecraft/world/level/block/entity/SculkSensorBlockEntity
dat net/minecraft/world/level/block/entity/SculkShriekerBlockEntity
dau net/minecraft/world/level/block/entity/TileEntityShulkerBox
dau$a net/minecraft/world/level/block/entity/TileEntityShulkerBox$AnimationPhase
dav net/minecraft/world/level/block/entity/TileEntitySign
daw net/minecraft/world/level/block/entity/SignText
dax net/minecraft/world/level/block/entity/TileEntitySkull
day net/minecraft/world/level/block/entity/TileEntitySmoker
daz net/minecraft/world/level/block/entity/TileEntityMobSpawner
db net/minecraft/advancements/critereon/TagPredicate
dba net/minecraft/world/level/block/entity/TileEntityStructure
dba$a net/minecraft/world/level/block/entity/TileEntityStructure$UpdateType
dbb net/minecraft/world/level/block/entity/TileEntityEndGateway
dbc net/minecraft/world/level/block/entity/TileEntityEnderPortal
dbd net/minecraft/world/level/block/entity/TickingBlockEntity
dbe net/minecraft/world/level/block/entity/TileEntityChestTrapped
dbg net/minecraft/world/level/block/grower/WorldGenMegaTreeProvider
dbh net/minecraft/world/level/block/grower/WorldGenTreeProvider
dbi net/minecraft/world/level/block/grower/WorldGenTreeProviderAcacia
dbj net/minecraft/world/level/block/grower/AzaleaTreeGrower
dbk net/minecraft/world/level/block/grower/WorldGenTreeProviderBirch
dbl net/minecraft/world/level/block/grower/CherryTreeGrower
dbm net/minecraft/world/level/block/grower/WorldGenMegaTreeProviderDarkOak
dbn net/minecraft/world/level/block/grower/WorldGenMegaTreeProviderJungle
dbo net/minecraft/world/level/block/grower/MangroveTreeGrower
dbp net/minecraft/world/level/block/grower/WorldGenTreeProviderOak
dbq net/minecraft/world/level/block/grower/WorldGenTreeProviderSpruce
dbt net/minecraft/world/level/block/piston/BlockPistonMoving
dbu net/minecraft/world/level/block/piston/BlockPiston
dbv net/minecraft/world/level/block/piston/BlockPistonExtension
dbw net/minecraft/world/level/block/piston/PistonUtil
dbx net/minecraft/world/level/block/piston/TileEntityPiston
dby net/minecraft/world/level/block/piston/PistonExtendsChecker
dc net/minecraft/advancements/critereon/CriterionTriggerTamedAnimal
dca net/minecraft/world/level/block/state/BlockBase
dca$a net/minecraft/world/level/block/state/BlockBase$BlockData
dca$a$a net/minecraft/world/level/block/state/BlockBase$BlockData$Cache
dca$c net/minecraft/world/level/block/state/BlockBase$EnumRandomOffset
dca$d net/minecraft/world/level/block/state/BlockBase$Info
dcb net/minecraft/world/level/block/state/IBlockData
dcc net/minecraft/world/level/block/state/BlockStateList
dcd net/minecraft/world/level/block/state/IBlockDataHolder
dcf net/minecraft/world/level/block/state/pattern/ShapeDetectorBlock
dcg net/minecraft/world/level/block/state/pattern/ShapeDetector
dcg$a net/minecraft/world/level/block/state/pattern/ShapeDetector$BlockLoader
dcg$b net/minecraft/world/level/block/state/pattern/ShapeDetector$ShapeDetectorCollection
dch net/minecraft/world/level/block/state/pattern/ShapeDetectorBuilder
dcj net/minecraft/world/level/block/state/predicate/BlockPredicate
dck net/minecraft/world/level/block/state/predicate/BlockStatePredicate
dcm net/minecraft/world/level/block/state/properties/BlockPropertyAttachPosition
dcn net/minecraft/world/level/block/state/properties/BlockPropertyBambooSize
dco net/minecraft/world/level/block/state/properties/BlockPropertyBedPart
dcp net/minecraft/world/level/block/state/properties/BlockPropertyBellAttach
dcq net/minecraft/world/level/block/state/properties/BlockSetType
dcr net/minecraft/world/level/block/state/properties/BlockProperties
dcs net/minecraft/world/level/block/state/properties/BlockStateBoolean
dct net/minecraft/world/level/block/state/properties/BlockPropertyChestType
dcu net/minecraft/world/level/block/state/properties/BlockPropertyComparatorMode
dcv net/minecraft/world/level/block/state/properties/BlockStateDirection
dcw net/minecraft/world/level/block/state/properties/BlockPropertyDoorHinge
dcx net/minecraft/world/level/block/state/properties/BlockPropertyDoubleBlockHalf
dcy net/minecraft/world/level/block/state/properties/DripstoneThickness
dcz net/minecraft/world/level/block/state/properties/BlockStateEnum
dd net/minecraft/advancements/critereon/CriterionTriggerTargetHit
dda net/minecraft/world/level/block/state/properties/BlockPropertyHalf
ddb net/minecraft/world/level/block/state/properties/BlockStateInteger
ddc net/minecraft/world/level/block/state/properties/BlockPropertyInstrument
ddd net/minecraft/world/level/block/state/properties/BlockPropertyPistonType
dde net/minecraft/world/level/block/state/properties/IBlockState
ddf net/minecraft/world/level/block/state/properties/BlockPropertyTrackPosition
ddg net/minecraft/world/level/block/state/properties/BlockPropertyRedstoneSide
ddh net/minecraft/world/level/block/state/properties/RotationSegment
ddi net/minecraft/world/level/block/state/properties/SculkSensorPhase
ddj net/minecraft/world/level/block/state/properties/BlockPropertySlabType
ddk net/minecraft/world/level/block/state/properties/BlockPropertyStairsShape
ddl net/minecraft/world/level/block/state/properties/BlockPropertyStructureMode
ddm net/minecraft/world/level/block/state/properties/Tilt
ddn net/minecraft/world/level/block/state/properties/BlockPropertyWallHeight
ddo net/minecraft/world/level/block/state/properties/BlockPropertyWood
ddq net/minecraft/world/level/border/IWorldBorderListener
ddr net/minecraft/world/level/border/BorderStatus
dds net/minecraft/world/level/border/WorldBorder
ddu net/minecraft/world/level/chunk/BlockColumn
ddv net/minecraft/world/level/chunk/BulkSectionAccess
ddw net/minecraft/world/level/chunk/CarvingMask
ddx net/minecraft/world/level/chunk/IChunkAccess
ddy net/minecraft/world/level/chunk/ChunkGenerator
ddz net/minecraft/world/level/chunk/ChunkGeneratorStructureState
de net/minecraft/advancements/critereon/CriterionTriggerVillagerTrade
dea net/minecraft/world/level/chunk/ChunkGenerators
deb net/minecraft/world/level/chunk/IChunkProvider
dec net/minecraft/world/level/chunk/ChunkStatus
dec$a net/minecraft/world/level/chunk/ChunkStatus$Type
ded net/minecraft/world/level/chunk/NibbleArray
dee net/minecraft/world/level/chunk/ChunkEmpty
def net/minecraft/world/level/chunk/DataPaletteGlobal
deg net/minecraft/world/level/chunk/DataPaletteHash
deh net/minecraft/world/level/chunk/ProtoChunkExtension
dei net/minecraft/world/level/chunk/Chunk
dei$b net/minecraft/world/level/chunk/Chunk$EnumTileEntityState
dej net/minecraft/world/level/chunk/ChunkSection
dek net/minecraft/world/level/chunk/LightChunk
del net/minecraft/world/level/chunk/ILightAccess
dem net/minecraft/world/level/chunk/DataPaletteLinear
den net/minecraft/world/level/chunk/MissingPaletteEntryException
deo net/minecraft/world/level/chunk/DataPalette
dep net/minecraft/world/level/chunk/DataPaletteExpandable
deq net/minecraft/world/level/chunk/DataPaletteBlock
der net/minecraft/world/level/chunk/PalettedContainerRO
des net/minecraft/world/level/chunk/ProtoChunk
det net/minecraft/world/level/chunk/SingleValuePalette
deu net/minecraft/world/level/chunk/StructureAccess
dev net/minecraft/world/level/chunk/ChunkConverter
dev$b net/minecraft/world/level/chunk/ChunkConverter$Type
dex net/minecraft/world/level/chunk/storage/ChunkScanAccess
dey net/minecraft/world/level/chunk/storage/ChunkRegionLoader
dez net/minecraft/world/level/chunk/storage/IChunkLoader
df net/minecraft/advancements/critereon/CriterionTriggerUsedEnderEye
dfa net/minecraft/world/level/chunk/storage/EntityStorage
dfb net/minecraft/world/level/chunk/storage/IOWorker
dfb$b net/minecraft/world/level/chunk/storage/IOWorker$Priority
dfc net/minecraft/world/level/chunk/storage/RegionFileBitSet
dfd net/minecraft/world/level/chunk/storage/RegionFile
dfd$a net/minecraft/world/level/chunk/storage/RegionFile$ChunkBuffer
dfe net/minecraft/world/level/chunk/storage/RegionFileCache
dff net/minecraft/world/level/chunk/storage/RegionFileCompression
dfg net/minecraft/world/level/chunk/storage/RegionFileSection
dfi net/minecraft/world/level/dimension/BuiltinDimensionTypes
dfk net/minecraft/world/level/dimension/DimensionManager
dfl net/minecraft/world/level/dimension/WorldDimension
dfm net/minecraft/world/level/dimension/end/EnumDragonRespawn
dfn net/minecraft/world/level/dimension/end/EnderDragonBattle
dfq net/minecraft/world/level/entity/ChunkEntities
dfr net/minecraft/world/level/entity/ChunkStatusUpdateListener
dfs net/minecraft/world/level/entity/EntityAccess
dft net/minecraft/world/level/entity/EntityInLevelCallback
dfu net/minecraft/world/level/entity/EntityLookup
dfv net/minecraft/world/level/entity/EntityPersistentStorage
dfw net/minecraft/world/level/entity/EntitySection
dfx net/minecraft/world/level/entity/EntitySectionStorage
dfy net/minecraft/world/level/entity/EntityTickList
dfz net/minecraft/world/level/entity/EntityTypeTest
dg net/minecraft/advancements/critereon/CriterionTriggerUsedTotem
dga net/minecraft/world/level/entity/LevelCallback
dgb net/minecraft/world/level/entity/LevelEntityGetter
dgc net/minecraft/world/level/entity/LevelEntityGetterAdapter
dgd net/minecraft/world/level/entity/PersistentEntitySectionManager
dgf net/minecraft/world/level/entity/Visibility
dgh net/minecraft/world/level/gameevent/BlockPositionSource
dgi net/minecraft/world/level/gameevent/DynamicGameEventListener
dgj net/minecraft/world/level/gameevent/EntityPositionSource
dgk net/minecraft/world/level/gameevent/EuclideanGameEventListenerRegistry
dgl net/minecraft/world/level/gameevent/GameEvent
dgm net/minecraft/world/level/gameevent/GameEventDispatcher
dgn net/minecraft/world/level/gameevent/GameEventListener
dgo net/minecraft/world/level/gameevent/GameEventListenerRegistry
dgp net/minecraft/world/level/gameevent/PositionSource
dgq net/minecraft/world/level/gameevent/PositionSourceType
dgs net/minecraft/world/level/gameevent/vibrations/VibrationInfo
dgt net/minecraft/world/level/gameevent/vibrations/VibrationSelector
dgu net/minecraft/world/level/gameevent/vibrations/VibrationSystem
dgw net/minecraft/world/level/levelgen/Aquifer
dgx net/minecraft/world/level/levelgen/Beardifier
dgy net/minecraft/world/level/levelgen/BelowZeroRetrogen
dgz net/minecraft/world/level/levelgen/BitRandomSource
dh net/minecraft/advancements/critereon/UsingItemTrigger
dha net/minecraft/world/level/levelgen/Column
dhb net/minecraft/world/level/levelgen/ChunkProviderDebug
dhd net/minecraft/world/level/levelgen/DensityFunction
dhe net/minecraft/world/level/levelgen/DensityFunctions
dhf net/minecraft/world/level/levelgen/ChunkProviderFlat
dhg net/minecraft/world/level/levelgen/WorldGenStage
dhg$a net/minecraft/world/level/levelgen/WorldGenStage$Features
dhg$b net/minecraft/world/level/levelgen/WorldGenStage$Decoration
dhh net/minecraft/world/level/levelgen/GeodeBlockSettings
dhi net/minecraft/world/level/levelgen/GeodeCrackSettings
dhj net/minecraft/world/level/levelgen/GeodeLayerSettings
dhk net/minecraft/world/level/levelgen/HeightMap
dhk$a net/minecraft/world/level/levelgen/HeightMap$Type
dhk$b net/minecraft/world/level/levelgen/HeightMap$Use
dhl net/minecraft/world/level/levelgen/LegacyRandomSource
dhm net/minecraft/world/level/levelgen/MarsagliaPolarGaussian
dhn net/minecraft/world/level/levelgen/ChunkGeneratorAbstract
dho net/minecraft/world/level/levelgen/NoiseChunk
dhp net/minecraft/world/level/levelgen/GeneratorSettingBase
dhq net/minecraft/world/level/levelgen/NoiseRouter
dhr net/minecraft/world/level/levelgen/NoiseRouterData
dhs net/minecraft/world/level/levelgen/NoiseSettings
dht net/minecraft/world/level/levelgen/Noises
dhu net/minecraft/world/level/levelgen/OreVeinifier
dhv net/minecraft/world/level/levelgen/MobSpawnerPatrol
dhw net/minecraft/world/level/levelgen/MobSpawnerPhantom
dhx net/minecraft/world/level/levelgen/PositionalRandomFactory
dhy net/minecraft/world/level/levelgen/RandomState
dhz net/minecraft/world/level/levelgen/RandomSupport
di net/minecraft/advancements/critereon/CriterionConditionRange
dia net/minecraft/world/level/levelgen/SingleThreadedRandomSource
dib net/minecraft/world/level/levelgen/SurfaceRules
dic net/minecraft/world/level/levelgen/SurfaceSystem
did net/minecraft/world/level/levelgen/ThreadSafeLegacyRandomSource
die net/minecraft/world/level/levelgen/VerticalAnchor
dif net/minecraft/world/level/levelgen/WorldDimensions
dig net/minecraft/world/level/levelgen/GeneratorSettings
dih net/minecraft/world/level/levelgen/WorldGenerationContext
dii net/minecraft/world/level/levelgen/WorldOptions
dij net/minecraft/world/level/levelgen/SeededRandom
dik net/minecraft/world/level/levelgen/Xoroshiro128PlusPlus
dil net/minecraft/world/level/levelgen/XoroshiroRandomSource
dim net/minecraft/world/level/levelgen/blending/Blender
din net/minecraft/world/level/levelgen/blending/BlendingData
dip net/minecraft/world/level/levelgen/blockpredicates/AllOfPredicate
diq net/minecraft/world/level/levelgen/blockpredicates/AnyOfPredicate
dir net/minecraft/world/level/levelgen/blockpredicates/BlockPredicate
dis net/minecraft/world/level/levelgen/blockpredicates/BlockPredicateType
dit net/minecraft/world/level/levelgen/blockpredicates/CombiningPredicate
diu net/minecraft/world/level/levelgen/blockpredicates/HasSturdyFacePredicate
div net/minecraft/world/level/levelgen/blockpredicates/InsideWorldBoundsPredicate
diw net/minecraft/world/level/levelgen/blockpredicates/MatchingBlockTagPredicate
dix net/minecraft/world/level/levelgen/blockpredicates/MatchingBlocksPredicate
diy net/minecraft/world/level/levelgen/blockpredicates/MatchingFluidsPredicate
diz net/minecraft/world/level/levelgen/blockpredicates/NotPredicate
dja net/minecraft/world/level/levelgen/blockpredicates/ReplaceablePredicate
djb net/minecraft/world/level/levelgen/blockpredicates/SolidPredicate
djc net/minecraft/world/level/levelgen/blockpredicates/StateTestingPredicate
djd net/minecraft/world/level/levelgen/blockpredicates/TrueBlockPredicate
dje net/minecraft/world/level/levelgen/blockpredicates/WouldSurvivePredicate
djg net/minecraft/world/level/levelgen/carver/CanyonCarverConfiguration
djh net/minecraft/world/level/levelgen/carver/WorldGenCanyon
dji net/minecraft/world/level/levelgen/carver/WorldGenCarverConfiguration
djj net/minecraft/world/level/levelgen/carver/CarverDebugSettings
djk net/minecraft/world/level/levelgen/carver/CarvingContext
djl net/minecraft/world/level/levelgen/carver/CaveCarverConfiguration
djm net/minecraft/world/level/levelgen/carver/WorldGenCaves
djn net/minecraft/world/level/levelgen/carver/WorldGenCarverWrapper
djo net/minecraft/world/level/levelgen/carver/WorldGenCavesHell
djp net/minecraft/world/level/levelgen/carver/WorldGenCarverAbstract
djr net/minecraft/world/level/levelgen/feature/WorldGenMushrooms
djs net/minecraft/world/level/levelgen/feature/WorldGenFeatureBamboo
djt net/minecraft/world/level/levelgen/feature/WorldGenFeatureBasaltColumns
dju net/minecraft/world/level/levelgen/feature/WorldGenFeatureBasaltPillar
djv net/minecraft/world/level/levelgen/feature/WorldGenTaigaStructure
djw net/minecraft/world/level/levelgen/feature/BlockColumnFeature
djx net/minecraft/world/level/levelgen/feature/WorldGenFeatureBlockPile
djy net/minecraft/world/level/levelgen/feature/WorldGenFeatureBlueIce
djz net/minecraft/world/level/levelgen/feature/WorldGenBonusChest
dka net/minecraft/world/level/levelgen/feature/WorldGenFeatureChorusPlant
dkb net/minecraft/world/level/levelgen/feature/WorldGenFeatureConfigured
dkc net/minecraft/world/level/levelgen/feature/WorldGenFeatureCoralClaw
dkd net/minecraft/world/level/levelgen/feature/WorldGenFeatureCoral
dke net/minecraft/world/level/levelgen/feature/WorldGenFeatureCoralMushroom
dkf net/minecraft/world/level/levelgen/feature/WorldGenFeatureCoralTree
dkg net/minecraft/world/level/levelgen/feature/WorldGenFeatureDelta
dkh net/minecraft/world/level/levelgen/feature/WorldGenDesertWell
dki net/minecraft/world/level/levelgen/feature/DiskFeature
dkj net/minecraft/world/level/levelgen/feature/DripstoneClusterFeature
dkk net/minecraft/world/level/levelgen/feature/DripstoneUtils
dkl net/minecraft/world/level/levelgen/feature/WorldGenEndGateway
dkm net/minecraft/world/level/levelgen/feature/WorldGenEndIsland
dkn net/minecraft/world/level/levelgen/feature/WorldGenEndTrophy
dko net/minecraft/world/level/levelgen/feature/WorldGenerator
dkq net/minecraft/world/level/levelgen/feature/FeaturePlaceContext
dkr net/minecraft/world/level/levelgen/feature/WorldGenFeatureFill
dks net/minecraft/world/level/levelgen/feature/WorldGenFossils
dkt net/minecraft/world/level/levelgen/feature/FossilFeatureConfiguration
dku net/minecraft/world/level/levelgen/feature/GeodeFeature
dkv net/minecraft/world/level/levelgen/feature/WorldGenLightStone1
dkw net/minecraft/world/level/levelgen/feature/WorldGenHugeMushroomBrown
dkx net/minecraft/world/level/levelgen/feature/WorldGenFeatureHugeFungiConfiguration
dky net/minecraft/world/level/levelgen/feature/WorldGenFeatureHugeFungi
dkz net/minecraft/world/level/levelgen/feature/WorldGenHugeMushroomRed
dl net/minecraft/commands/CommandExceptionProvider
dla net/minecraft/world/level/levelgen/feature/WorldGenPackedIce2
dlb net/minecraft/world/level/levelgen/feature/WorldGenFeatureIceburg
dlc net/minecraft/world/level/levelgen/feature/WorldGenFeatureKelp
dld net/minecraft/world/level/levelgen/feature/WorldGenLakes
dle net/minecraft/world/level/levelgen/feature/LargeDripstoneFeature
dlf net/minecraft/world/level/levelgen/feature/WorldGenDungeons
dlg net/minecraft/world/level/levelgen/feature/MultifaceGrowthFeature
dlh net/minecraft/world/level/levelgen/feature/WorldGenFeatureNetherForestVegetation
dli net/minecraft/world/level/levelgen/feature/WorldGenFeatureEmpty
dlj net/minecraft/world/level/levelgen/feature/WorldGenMinable
dlk net/minecraft/world/level/levelgen/feature/PointedDripstoneFeature
dll net/minecraft/world/level/levelgen/feature/WorldGenFeatureChoice
dlm net/minecraft/world/level/levelgen/feature/WorldGenFeatureRandomPatch
dln net/minecraft/world/level/levelgen/feature/WorldGenFeatureRandomChoice
dlo net/minecraft/world/level/levelgen/feature/WorldGenFeatureNetherrackReplaceBlobs
dlp net/minecraft/world/level/levelgen/feature/WorldGenFeatureReplaceBlock
dlq net/minecraft/world/level/levelgen/feature/RootSystemFeature
dlr net/minecraft/world/level/levelgen/feature/ScatteredOreFeature
dls net/minecraft/world/level/levelgen/feature/SculkPatchFeature
dlt net/minecraft/world/level/levelgen/feature/WorldGenFeatureSeaPickel
dlu net/minecraft/world/level/levelgen/feature/WorldGenFeatureSeaGrass
dlv net/minecraft/world/level/levelgen/feature/WorldGenFeatureBlock
dlw net/minecraft/world/level/levelgen/feature/WorldGenFeatureRandom2Configuration
dlx net/minecraft/world/level/levelgen/feature/WorldGenFeatureIceSnow
dly net/minecraft/world/level/levelgen/feature/WorldGenEnder
dly$a net/minecraft/world/level/levelgen/feature/WorldGenEnder$Spike
dlz net/minecraft/world/level/levelgen/feature/WorldGenLiquids
dm net/minecraft/commands/CommandBuildContext
dma net/minecraft/world/level/levelgen/feature/WorldGenTrees
dmb net/minecraft/world/level/levelgen/feature/WorldGenFeatureTwistingVines
dmc net/minecraft/world/level/levelgen/feature/UnderwaterMagmaFeature
dmd net/minecraft/world/level/levelgen/feature/VegetationPatchFeature
dme net/minecraft/world/level/levelgen/feature/WorldGenVines
dmf net/minecraft/world/level/levelgen/feature/WorldGenFeatureEndPlatform
dmg net/minecraft/world/level/levelgen/feature/WaterloggedVegetationPatchFeature
dmh net/minecraft/world/level/levelgen/feature/WorldGenFeatureWeepingVines
dmi net/minecraft/world/level/levelgen/feature/WeightedPlacedFeature
dmj net/minecraft/world/level/levelgen/feature/configurations/BlockColumnConfiguration
dmk net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureBlockPileConfiguration
dml net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureLakeConfiguration
dmm net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureBasaltColumnsConfiguration
dmn net/minecraft/world/level/levelgen/feature/configurations/WorldGenDecoratorFrequencyConfiguration
dmo net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureDeltaConfiguration
dmp net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureCircleConfiguration
dmq net/minecraft/world/level/levelgen/feature/configurations/DripstoneClusterConfiguration
dmr net/minecraft/world/level/levelgen/feature/configurations/WorldGenEndGatewayConfiguration
dms net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureConfiguration
dmt net/minecraft/world/level/levelgen/feature/configurations/GeodeConfiguration
dmu net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureMushroomConfiguration
dmv net/minecraft/world/level/levelgen/feature/configurations/LargeDripstoneConfiguration
dmw net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureFillConfiguration
dmx net/minecraft/world/level/levelgen/feature/configurations/MultifaceGrowthConfiguration
dmy net/minecraft/world/level/levelgen/feature/configurations/NetherForestVegetationConfig
dmz net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureEmptyConfiguration
dn net/minecraft/commands/CustomFunction
dna net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureOreConfiguration
dnb net/minecraft/world/level/levelgen/feature/configurations/PointedDripstoneConfiguration
dnc net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureConfigurationChance
dnd net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureChoiceConfiguration
dne net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureRandomChoiceConfiguration
dnf net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureRandomPatchConfiguration
dng net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureReplaceBlockConfiguration
dnh net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureRadiusConfiguration
dni net/minecraft/world/level/levelgen/feature/configurations/RootSystemConfiguration
dnj net/minecraft/world/level/levelgen/feature/configurations/SculkPatchConfiguration
dnk net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureBlockConfiguration
dnl net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureRandom2
dnm net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureEndSpikeConfiguration
dnn net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureHellFlowingLavaConfiguration
dno net/minecraft/world/level/levelgen/feature/configurations/WorldGenFeatureTreeConfiguration
dnp net/minecraft/world/level/levelgen/feature/configurations/TwistingVinesConfig
dnq net/minecraft/world/level/levelgen/feature/configurations/UnderwaterMagmaConfiguration
dnr net/minecraft/world/level/levelgen/feature/configurations/VegetationPatchConfiguration
dnt net/minecraft/world/level/levelgen/feature/featuresize/FeatureSize
dnu net/minecraft/world/level/levelgen/feature/featuresize/FeatureSizeType
dnv net/minecraft/world/level/levelgen/feature/featuresize/FeatureSizeThreeLayers
dnw net/minecraft/world/level/levelgen/feature/featuresize/FeatureSizeTwoLayers
dny net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerAcacia
dnz net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerBlob
doa net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerBush
dob net/minecraft/world/level/levelgen/feature/foliageplacers/CherryFoliagePlacer
doc net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerDarkOak
dod net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerFancy
doe net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacer
dof net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacers
dog net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerJungle
doh net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerMegaPine
doi net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerPine
doj net/minecraft/world/level/levelgen/feature/foliageplacers/RandomSpreadFoliagePlacer
dok net/minecraft/world/level/levelgen/feature/foliageplacers/WorldGenFoilagePlacerSpruce
don net/minecraft/world/level/levelgen/feature/rootplacers/AboveRootPlacement
doo net/minecraft/world/level/levelgen/feature/rootplacers/MangroveRootPlacement
dop net/minecraft/world/level/levelgen/feature/rootplacers/MangroveRootPlacer
doq net/minecraft/world/level/levelgen/feature/rootplacers/RootPlacer
dor net/minecraft/world/level/levelgen/feature/rootplacers/RootPlacerType
dot net/minecraft/world/level/levelgen/feature/stateproviders/WorldGenFeatureStateProvider
dou net/minecraft/world/level/levelgen/feature/stateproviders/WorldGenFeatureStateProviders
dov net/minecraft/world/level/levelgen/feature/stateproviders/DualNoiseProvider
dow net/minecraft/world/level/levelgen/feature/stateproviders/NoiseBasedStateProvider
dox net/minecraft/world/level/levelgen/feature/stateproviders/NoiseProvider
doy net/minecraft/world/level/levelgen/feature/stateproviders/NoiseThresholdProvider
doz net/minecraft/world/level/levelgen/feature/stateproviders/RandomizedIntStateProvider
dp net/minecraft/commands/CommandException
dpa net/minecraft/world/level/levelgen/feature/stateproviders/WorldGenFeatureStateProviderRotatedBlock
dpb net/minecraft/world/level/levelgen/feature/stateproviders/RuleBasedBlockStateProvider
dpc net/minecraft/world/level/levelgen/feature/stateproviders/WorldGenFeatureStateProviderSimpl
dpd net/minecraft/world/level/levelgen/feature/stateproviders/WorldGenFeatureStateProviderWeighted
dpf net/minecraft/world/level/levelgen/feature/treedecorators/WorldGenFeatureTreeAlterGround
dpg net/minecraft/world/level/levelgen/feature/treedecorators/AttachedToLeavesDecorator
dph net/minecraft/world/level/levelgen/feature/treedecorators/WorldGenFeatureTreeBeehive
dpi net/minecraft/world/level/levelgen/feature/treedecorators/WorldGenFeatureTreeCocoa
dpj net/minecraft/world/level/levelgen/feature/treedecorators/WorldGenFeatureTreeVineLeaves
dpk net/minecraft/world/level/levelgen/feature/treedecorators/WorldGenFeatureTree
dpl net/minecraft/world/level/levelgen/feature/treedecorators/WorldGenFeatureTrees
dpm net/minecraft/world/level/levelgen/feature/treedecorators/WorldGenFeatureTreeVineTrunk
dpo net/minecraft/world/level/levelgen/feature/trunkplacers/BendingTrunkPlacer
dpp net/minecraft/world/level/levelgen/feature/trunkplacers/CherryTrunkPlacer
dpq net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacerDarkOak
dpr net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacerFancy
dps net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacerForking
dpt net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacerGiant
dpu net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacerMegaJungle
dpv net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacerStraight
dpw net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacer
dpx net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacers
dpy net/minecraft/world/level/levelgen/feature/trunkplacers/UpwardsBranchingTrunkPlacer
dq net/minecraft/commands/CommandSigningContext
dqa net/minecraft/world/level/levelgen/flat/WorldGenFlatLayerInfo
dqb net/minecraft/world/level/levelgen/flat/FlatLevelGeneratorPreset
dqc net/minecraft/world/level/levelgen/flat/FlatLevelGeneratorPresets
dqd net/minecraft/world/level/levelgen/flat/GeneratorSettingsFlat
dqf net/minecraft/world/level/levelgen/heightproviders/BiasedToBottomHeight
dqg net/minecraft/world/level/levelgen/heightproviders/ConstantHeight
dqh net/minecraft/world/level/levelgen/heightproviders/HeightProvider
dqi net/minecraft/world/level/levelgen/heightproviders/HeightProviderType
dqj net/minecraft/world/level/levelgen/heightproviders/TrapezoidHeight
dqk net/minecraft/world/level/levelgen/heightproviders/UniformHeight
dql net/minecraft/world/level/levelgen/heightproviders/VeryBiasedToBottomHeight
dqm net/minecraft/world/level/levelgen/heightproviders/WeightedListHeight
dqo net/minecraft/world/level/levelgen/material/MaterialRuleList
dqs net/minecraft/world/level/levelgen/placement/BiomeFilter
dqt net/minecraft/world/level/levelgen/placement/BlockPredicateFilter
dqu net/minecraft/world/level/levelgen/placement/CarvingMaskPlacement
dqv net/minecraft/world/level/levelgen/placement/CaveSurface
dqw net/minecraft/world/level/levelgen/placement/CountOnEveryLayerPlacement
dqx net/minecraft/world/level/levelgen/placement/CountPlacement
dqy net/minecraft/world/level/levelgen/placement/EnvironmentScanPlacement
dqz net/minecraft/world/level/levelgen/placement/HeightRangePlacement
dr net/minecraft/commands/ICommandListener
dra net/minecraft/world/level/levelgen/placement/HeightmapPlacement
drb net/minecraft/world/level/levelgen/placement/InSquarePlacement
drc net/minecraft/world/level/levelgen/placement/NoiseBasedCountPlacement
drd net/minecraft/world/level/levelgen/placement/NoiseThresholdCountPlacement
dre net/minecraft/world/level/levelgen/placement/PlacedFeature
drf net/minecraft/world/level/levelgen/placement/PlacementContext
drg net/minecraft/world/level/levelgen/placement/PlacementFilter
drh net/minecraft/world/level/levelgen/placement/PlacementModifier
dri net/minecraft/world/level/levelgen/placement/PlacementModifierType
drj net/minecraft/world/level/levelgen/placement/RandomOffsetPlacement
drk net/minecraft/world/level/levelgen/placement/RarityFilter
drl net/minecraft/world/level/levelgen/placement/RepeatingPlacement
drm net/minecraft/world/level/levelgen/placement/SurfaceRelativeThresholdFilter
drn net/minecraft/world/level/levelgen/placement/SurfaceWaterDepthFilter
drp net/minecraft/world/level/levelgen/presets/WorldPreset
drq net/minecraft/world/level/levelgen/presets/WorldPresets
drs net/minecraft/world/level/levelgen/structure/StructureBoundingBox
drt net/minecraft/world/level/levelgen/structure/BuiltinStructureSets
dru net/minecraft/world/level/levelgen/structure/BuiltinStructures
drv net/minecraft/world/level/levelgen/structure/PersistentStructureLegacy
drw net/minecraft/world/level/levelgen/structure/WorldGenFeaturePillagerOutpostPoolPiece
dry net/minecraft/world/level/levelgen/structure/WorldGenScatteredPiece
drz net/minecraft/world/level/levelgen/structure/SinglePieceStructure
ds net/minecraft/commands/CommandListenerWrapper
dsa net/minecraft/world/level/levelgen/structure/Structure
dsb net/minecraft/world/level/levelgen/structure/StructureCheck
dsc net/minecraft/world/level/levelgen/structure/StructureCheckResult
dsd net/minecraft/world/level/levelgen/structure/PersistentIndexed
dse net/minecraft/world/level/levelgen/structure/StructurePiece
dse$a net/minecraft/world/level/levelgen/structure/StructurePiece$StructurePieceBlockSelector
dsf net/minecraft/world/level/levelgen/structure/StructurePieceAccessor
dsg net/minecraft/world/level/levelgen/structure/StructureSet
dsh net/minecraft/world/level/levelgen/structure/StructureSpawnOverride
dsi net/minecraft/world/level/levelgen/structure/StructureStart
dsj net/minecraft/world/level/levelgen/structure/StructureType
dsk net/minecraft/world/level/levelgen/structure/DefinedStructurePiece
dsl net/minecraft/world/level/levelgen/structure/TerrainAdjustment
dsp net/minecraft/world/level/levelgen/structure/pieces/PiecesContainer
dsq net/minecraft/world/level/levelgen/structure/pieces/StructurePieceSerializationContext
dsr net/minecraft/world/level/levelgen/structure/pieces/WorldGenFeatureStructurePieceType
dss net/minecraft/world/level/levelgen/structure/pieces/StructurePiecesBuilder
dsu net/minecraft/world/level/levelgen/structure/placement/ConcentricRingsStructurePlacement
dsv net/minecraft/world/level/levelgen/structure/placement/RandomSpreadStructurePlacement
dsw net/minecraft/world/level/levelgen/structure/placement/RandomSpreadType
dsx net/minecraft/world/level/levelgen/structure/placement/StructurePlacement
dsy net/minecraft/world/level/levelgen/structure/placement/StructurePlacementType
dt net/minecraft/commands/CommandDispatcher
dt$a net/minecraft/commands/CommandDispatcher$ServerType
dta net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePoolEmpty
dtb net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePoolFeature
dtc net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructureJigsawJunction
dtd net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructureJigsawPlacement
dte net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePoolLegacySingle
dtf net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePoolList
dtg net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePoolSingle
dth net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePoolStructure
dti net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePools
dtj net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePoolTemplate
dtj$a net/minecraft/world/level/levelgen/structure/pools/WorldGenFeatureDefinedStructurePoolTemplate$Matching
dtl net/minecraft/world/level/levelgen/structure/structures/BuriedTreasurePieces
dtm net/minecraft/world/level/levelgen/structure/structures/BuriedTreasureStructure
dtn net/minecraft/world/level/levelgen/structure/structures/DesertPyramidPiece
dto net/minecraft/world/level/levelgen/structure/structures/DesertPyramidStructure
dtp net/minecraft/world/level/levelgen/structure/structures/EndCityPieces
dtq net/minecraft/world/level/levelgen/structure/structures/EndCityStructure
dtr net/minecraft/world/level/levelgen/structure/structures/IglooPieces
dts net/minecraft/world/level/levelgen/structure/structures/IglooStructure
dtt net/minecraft/world/level/levelgen/structure/structures/JigsawStructure
dtu net/minecraft/world/level/levelgen/structure/structures/JungleTemplePiece
dtv net/minecraft/world/level/levelgen/structure/structures/JungleTempleStructure
dtw net/minecraft/world/level/levelgen/structure/structures/MineshaftPieces
dtx net/minecraft/world/level/levelgen/structure/structures/MineshaftStructure
dty net/minecraft/world/level/levelgen/structure/structures/NetherFortressPieces
dtz net/minecraft/world/level/levelgen/structure/structures/NetherFortressStructure
du net/minecraft/commands/ICompletionProvider
dua net/minecraft/world/level/levelgen/structure/structures/NetherFossilPieces
dub net/minecraft/world/level/levelgen/structure/structures/NetherFossilStructure
duc net/minecraft/world/level/levelgen/structure/structures/OceanMonumentPieces
dud net/minecraft/world/level/levelgen/structure/structures/OceanMonumentStructure
due net/minecraft/world/level/levelgen/structure/structures/OceanRuinPieces
duf net/minecraft/world/level/levelgen/structure/structures/OceanRuinStructure
dug net/minecraft/world/level/levelgen/structure/structures/RuinedPortalPiece
duh net/minecraft/world/level/levelgen/structure/structures/RuinedPortalStructure
dui net/minecraft/world/level/levelgen/structure/structures/ShipwreckPieces
duj net/minecraft/world/level/levelgen/structure/structures/ShipwreckStructure
duk net/minecraft/world/level/levelgen/structure/structures/StrongholdPieces
dul net/minecraft/world/level/levelgen/structure/structures/StrongholdStructure
dum net/minecraft/world/level/levelgen/structure/structures/SwampHutPiece
dun net/minecraft/world/level/levelgen/structure/structures/SwampHutStructure
duo net/minecraft/world/level/levelgen/structure/structures/WoodlandMansionPieces
dup net/minecraft/world/level/levelgen/structure/structures/WoodlandMansionStructure
dur net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureTestTrue
dus net/minecraft/world/level/levelgen/structure/templatesystem/PosRuleTestAxisAlignedLinear
dut net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorBlackstoneReplace
duu net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorBlockAge
duv net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorBlockIgnore
duw net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureTestBlock
dux net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorRotation
duy net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureTestBlockState
duz net/minecraft/world/level/levelgen/structure/templatesystem/CappedProcessor
dv net/minecraft/commands/arguments/ArgumentAngle
dva net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorGravity
dvb net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorJigsawReplacement
dvc net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorLavaSubmergedBlock
dvd net/minecraft/world/level/levelgen/structure/templatesystem/PosRuleTestLinear
dve net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorNop
dvf net/minecraft/world/level/levelgen/structure/templatesystem/PosRuleTestTrue
dvg net/minecraft/world/level/levelgen/structure/templatesystem/PosRuleTest
dvh net/minecraft/world/level/levelgen/structure/templatesystem/PosRuleTestType
dvi net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorPredicates
dvj net/minecraft/world/level/levelgen/structure/templatesystem/ProtectedBlockProcessor
dvk net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureTestRandomBlock
dvl net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureTestRandomBlockState
dvm net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessorRule
dvn net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureRuleTest
dvo net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureRuleTestType
dvp net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureInfo
dvq net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureProcessor
dvr net/minecraft/world/level/levelgen/structure/templatesystem/ProcessorList
dvs net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureStructureProcessorType
dvt net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructure
dvt$c net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructure$BlockInfo
dvt$d net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructure$EntityInfo
dvu net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplateManager
dvv net/minecraft/world/level/levelgen/structure/templatesystem/DefinedStructureTestTag
dvx net/minecraft/world/level/levelgen/structure/templatesystem/rule/blockentity/AppendLoot
dvy net/minecraft/world/level/levelgen/structure/templatesystem/rule/blockentity/AppendStatic
dvz net/minecraft/world/level/levelgen/structure/templatesystem/rule/blockentity/Clear
dw net/minecraft/commands/arguments/ArgumentSignatures
dwa net/minecraft/world/level/levelgen/structure/templatesystem/rule/blockentity/Passthrough
dwb net/minecraft/world/level/levelgen/structure/templatesystem/rule/blockentity/RuleBlockEntityModifier
dwc net/minecraft/world/level/levelgen/structure/templatesystem/rule/blockentity/RuleBlockEntityModifierType
dwe net/minecraft/world/level/levelgen/synth/BlendedNoise
dwf net/minecraft/world/level/levelgen/synth/NoiseGeneratorPerlin
dwg net/minecraft/world/level/levelgen/synth/NoiseUtils
dwh net/minecraft/world/level/levelgen/synth/NoiseGeneratorNormal
dwi net/minecraft/world/level/levelgen/synth/NoiseGeneratorOctaves
dwj net/minecraft/world/level/levelgen/synth/NoiseGenerator3
dwk net/minecraft/world/level/levelgen/synth/NoiseGenerator3Handler
dwm net/minecraft/world/level/lighting/LightEngineBlock
dwn net/minecraft/world/level/lighting/LightEngineStorageBlock
dwo net/minecraft/world/level/lighting/ChunkSkyLightSources
dwp net/minecraft/world/level/lighting/LightEngineStorageArray
dwq net/minecraft/world/level/lighting/LightEngineGraph
dwr net/minecraft/world/level/lighting/LightEngineLayerEventListener
dwr$a net/minecraft/world/level/lighting/LightEngineLayerEventListener$Void
dws net/minecraft/world/level/lighting/LightEngineStorage
dwt net/minecraft/world/level/lighting/LevelLightEngine
dwu net/minecraft/world/level/lighting/LeveledPriorityQueue
dwv net/minecraft/world/level/lighting/LightEngine
dww net/minecraft/world/level/lighting/ILightEngine
dwx net/minecraft/world/level/lighting/LightEngineSky
dwy net/minecraft/world/level/lighting/LightEngineStorageSky
dx net/minecraft/commands/arguments/ArgumentChatFormat
dxb net/minecraft/world/level/material/FluidTypeEmpty
dxc net/minecraft/world/level/material/FluidTypeFlowing
dxd net/minecraft/world/level/material/FluidType
dxe net/minecraft/world/level/material/Fluid
dxf net/minecraft/world/level/material/FluidTypes
dxh net/minecraft/world/level/material/FluidTypeLava
dxi net/minecraft/world/level/material/MaterialMapColor
dxj net/minecraft/world/level/material/EnumPistonReaction
dxk net/minecraft/world/level/material/FluidTypeWater
dxn net/minecraft/world/level/pathfinder/AmphibiousNodeEvaluator
dxo net/minecraft/world/level/pathfinder/Path
dxp net/minecraft/world/level/pathfinder/PathType
dxq net/minecraft/world/level/pathfinder/PathfinderFlying
dxr net/minecraft/world/level/pathfinder/PathPoint
dxs net/minecraft/world/level/pathfinder/PathfinderAbstract
dxt net/minecraft/world/level/pathfinder/PathEntity
dxu net/minecraft/world/level/pathfinder/PathMode
dxv net/minecraft/world/level/pathfinder/Pathfinder
dxw net/minecraft/world/level/pathfinder/PathfinderWater
dxx net/minecraft/world/level/pathfinder/PathDestination
dxy net/minecraft/world/level/pathfinder/PathfinderNormal
dy net/minecraft/commands/arguments/ArgumentChatComponent
dya net/minecraft/world/level/portal/PortalTravelAgent
dyb net/minecraft/world/level/portal/ShapeDetectorShape
dyc net/minecraft/world/level/portal/BlockPortalShape
dye net/minecraft/world/level/redstone/CollectingNeighborUpdater
dyg net/minecraft/world/level/redstone/NeighborUpdater
dyj net/minecraft/world/level/saveddata/PersistentBase
dyk net/minecraft/world/level/saveddata/maps/MapIconBanner
dyl net/minecraft/world/level/saveddata/maps/MapIcon
dyl$a net/minecraft/world/level/saveddata/maps/MapIcon$Type
dym net/minecraft/world/level/saveddata/maps/WorldMapFrame
dyn net/minecraft/world/level/saveddata/maps/PersistentIdCounts
dyo net/minecraft/world/level/saveddata/maps/WorldMap
dyo$a net/minecraft/world/level/saveddata/maps/WorldMap$WorldMapHumanTracker
dyr net/minecraft/world/level/storage/PersistentCommandStorage
dys net/minecraft/world/level/storage/DataVersion
dyt net/minecraft/world/level/storage/SecondaryWorldData
dyu net/minecraft/world/level/storage/WorldPersistentData
dyv net/minecraft/world/level/storage/WorldData
dyw net/minecraft/world/level/storage/SavedFile
dyx net/minecraft/world/level/storage/LevelStorageException
dyy net/minecraft/world/level/storage/Convertable
dyy$c net/minecraft/world/level/storage/Convertable$ConversionSession
dyz net/minecraft/world/level/storage/WorldInfo
dz net/minecraft/commands/arguments/ArgumentNBTTag
dza net/minecraft/world/level/storage/LevelVersion
dzb net/minecraft/world/level/storage/WorldNBTStorage
dzc net/minecraft/world/level/storage/WorldDataServer
dzd net/minecraft/world/level/storage/IWorldDataServer
dze net/minecraft/world/level/storage/SaveData
dzf net/minecraft/world/level/storage/WorldDataMutable
dzg net/minecraft/world/level/storage/loot/LootTables
dzh net/minecraft/world/level/storage/loot/LootSerialization
dzi net/minecraft/world/level/storage/loot/JsonRegistry
dzj net/minecraft/world/level/storage/loot/IntRange
dzk net/minecraft/world/level/storage/loot/LootTableInfo
dzk$a net/minecraft/world/level/storage/loot/LootTableInfo$Builder
dzk$b net/minecraft/world/level/storage/loot/LootTableInfo$EntityTarget
dzl net/minecraft/world/level/storage/loot/LootItemUser
dzm net/minecraft/world/level/storage/loot/LootDataId
dzn net/minecraft/world/level/storage/loot/LootDataManager
dzo net/minecraft/world/level/storage/loot/LootDataResolver
dzp net/minecraft/world/level/storage/loot/LootDataType
dzq net/minecraft/world/level/storage/loot/LootParams
dzr net/minecraft/world/level/storage/loot/LootSelector
dzs net/minecraft/world/level/storage/loot/LootTable
dzt net/minecraft/world/level/storage/loot/LootSerializer
dzu net/minecraft/world/level/storage/loot/LootSerializerType
dzv net/minecraft/world/level/storage/loot/LootCollector
dzw net/minecraft/world/level/storage/loot/entries/LootEntryAlternatives
dzx net/minecraft/world/level/storage/loot/entries/LootEntryChildren
dzy net/minecraft/world/level/storage/loot/entries/LootEntryChildrenAbstract
dzz net/minecraft/world/level/storage/loot/entries/LootSelectorDynamic
e com/mojang/math/GivensParameters
ea net/minecraft/commands/arguments/ArgumentDimension
eaa net/minecraft/world/level/storage/loot/entries/LootSelectorEmpty
eab net/minecraft/world/level/storage/loot/entries/LootEntryGroup
eac net/minecraft/world/level/storage/loot/entries/LootItem
ead net/minecraft/world/level/storage/loot/entries/LootEntries
eae net/minecraft/world/level/storage/loot/entries/LootEntry
eaf net/minecraft/world/level/storage/loot/entries/LootEntryAbstract
eaf$b net/minecraft/world/level/storage/loot/entries/LootEntryAbstract$Serializer
eag net/minecraft/world/level/storage/loot/entries/LootEntryType
eah net/minecraft/world/level/storage/loot/entries/LootSelectorEntry
eai net/minecraft/world/level/storage/loot/entries/LootSelectorLootTable
eaj net/minecraft/world/level/storage/loot/entries/LootEntrySequence
eak net/minecraft/world/level/storage/loot/entries/LootSelectorTag
eam net/minecraft/world/level/storage/loot/functions/LootItemFunctionApplyBonus
ean net/minecraft/world/level/storage/loot/functions/LootItemFunctionExplosionDecay
eao net/minecraft/world/level/storage/loot/functions/LootItemFunctionCopyState
eap net/minecraft/world/level/storage/loot/functions/LootItemFunctionCopyName
eap$a net/minecraft/world/level/storage/loot/functions/LootItemFunctionCopyName$Source
eaq net/minecraft/world/level/storage/loot/functions/LootItemFunctionCopyNBT
eaq$c net/minecraft/world/level/storage/loot/functions/LootItemFunctionCopyNBT$Action
ear net/minecraft/world/level/storage/loot/functions/LootItemFunctionEnchant
eas net/minecraft/world/level/storage/loot/functions/LootEnchantLevel
eat net/minecraft/world/level/storage/loot/functions/LootItemFunctionExplorationMap
eau net/minecraft/world/level/storage/loot/functions/LootItemFunctionFillPlayerHead
eav net/minecraft/world/level/storage/loot/functions/FunctionReference
eaw net/minecraft/world/level/storage/loot/functions/LootItemFunctionUser
eax net/minecraft/world/level/storage/loot/functions/LootItemFunctionLimitCount
eay net/minecraft/world/level/storage/loot/functions/LootItemFunctionConditional
eaz net/minecraft/world/level/storage/loot/functions/LootItemFunction
eb net/minecraft/commands/arguments/ArgumentAnchor
eb$a net/minecraft/commands/arguments/ArgumentAnchor$Anchor
eba net/minecraft/world/level/storage/loot/functions/LootItemFunctionType
ebb net/minecraft/world/level/storage/loot/functions/LootItemFunctions
ebc net/minecraft/world/level/storage/loot/functions/LootEnchantFunction
ebd net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetAttribute
ebe net/minecraft/world/level/storage/loot/functions/SetBannerPatternFunction
ebf net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetContents
ebg net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetTable
ebh net/minecraft/world/level/storage/loot/functions/SetEnchantmentsFunction
ebi net/minecraft/world/level/storage/loot/functions/SetInstrumentFunction
ebj net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetCount
ebk net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetDamage
ebl net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetLore
ebm net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetName
ebn net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetTag
ebo net/minecraft/world/level/storage/loot/functions/SetPotionFunction
ebp net/minecraft/world/level/storage/loot/functions/LootItemFunctionSetStewEffect
ebq net/minecraft/world/level/storage/loot/functions/LootItemFunctionSmelt
ebt net/minecraft/world/level/storage/loot/parameters/LootContextParameter
ebu net/minecraft/world/level/storage/loot/parameters/LootContextParameterSet
ebu$a net/minecraft/world/level/storage/loot/parameters/LootContextParameterSet$Builder
ebv net/minecraft/world/level/storage/loot/parameters/LootContextParameterSets
ebw net/minecraft/world/level/storage/loot/parameters/LootContextParameters
eby net/minecraft/world/level/storage/loot/predicates/AllOfCondition
ebz net/minecraft/world/level/storage/loot/predicates/AnyOfCondition
ec net/minecraft/commands/arguments/ArgumentEntity
ec$a net/minecraft/commands/arguments/ArgumentEntity$Info
ec$a$a net/minecraft/commands/arguments/ArgumentEntity$Info$Template
eca net/minecraft/world/level/storage/loot/predicates/LootItemConditionTableBonus
ecb net/minecraft/world/level/storage/loot/predicates/CompositeLootItemCondition
ecc net/minecraft/world/level/storage/loot/predicates/LootItemConditionReference
ecd net/minecraft/world/level/storage/loot/predicates/LootItemConditionUser
ece net/minecraft/world/level/storage/loot/predicates/LootItemConditionDamageSourceProperties
ecf net/minecraft/world/level/storage/loot/predicates/LootItemConditionEntityScore
ecg net/minecraft/world/level/storage/loot/predicates/LootItemConditionSurvivesExplosion
ech net/minecraft/world/level/storage/loot/predicates/LootItemConditionInverted
eci net/minecraft/world/level/storage/loot/predicates/LootItemConditionLocationCheck
ecj net/minecraft/world/level/storage/loot/predicates/LootItemConditionBlockStateProperty
eck net/minecraft/world/level/storage/loot/predicates/LootItemCondition
ecl net/minecraft/world/level/storage/loot/predicates/LootItemConditionType
ecm net/minecraft/world/level/storage/loot/predicates/LootItemConditions
ecn net/minecraft/world/level/storage/loot/predicates/LootItemConditionEntityProperty
eco net/minecraft/world/level/storage/loot/predicates/LootItemConditionKilledByPlayer
ecp net/minecraft/world/level/storage/loot/predicates/LootItemConditionRandomChance
ecq net/minecraft/world/level/storage/loot/predicates/LootItemConditionRandomChanceWithLooting
ecr net/minecraft/world/level/storage/loot/predicates/LootItemConditionMatchTool
ecs net/minecraft/world/level/storage/loot/predicates/LootItemConditionTimeCheck
ect net/minecraft/world/level/storage/loot/predicates/ValueCheckCondition
ecu net/minecraft/world/level/storage/loot/predicates/LootItemConditionWeatherCheck
ecw net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider
ecx net/minecraft/world/level/storage/loot/providers/nbt/LootNbtProviderType
ecy net/minecraft/world/level/storage/loot/providers/nbt/NbtProvider
ecz net/minecraft/world/level/storage/loot/providers/nbt/NbtProviders
ed net/minecraft/commands/arguments/GameModeArgument
eda net/minecraft/world/level/storage/loot/providers/nbt/StorageNbtProvider
edc net/minecraft/world/level/storage/loot/providers/number/BinomialDistributionGenerator
edd net/minecraft/world/level/storage/loot/providers/number/ConstantValue
ede net/minecraft/world/level/storage/loot/providers/number/LootNumberProviderType
edf net/minecraft/world/level/storage/loot/providers/number/NumberProvider
edg net/minecraft/world/level/storage/loot/providers/number/NumberProviders
edh net/minecraft/world/level/storage/loot/providers/number/ScoreboardValue
edi net/minecraft/world/level/storage/loot/providers/number/UniformGenerator
edk net/minecraft/world/level/storage/loot/providers/score/ContextScoreboardNameProvider
edl net/minecraft/world/level/storage/loot/providers/score/FixedScoreboardNameProvider
edm net/minecraft/world/level/storage/loot/providers/score/LootScoreProviderType
edn net/minecraft/world/level/storage/loot/providers/score/ScoreboardNameProvider
edo net/minecraft/world/level/storage/loot/providers/score/ScoreboardNameProviders
edr net/minecraft/world/level/timers/CustomFunctionCallback
eds net/minecraft/world/level/timers/CustomFunctionCallbackTag
edt net/minecraft/world/level/timers/CustomFunctionCallbackTimer
edu net/minecraft/world/level/timers/CustomFunctionCallbackTimers
edv net/minecraft/world/level/timers/CustomFunctionCallbackTimerQueue
edx net/minecraft/world/level/validation/ContentValidationException
edy net/minecraft/world/level/validation/DirectoryValidator
edz net/minecraft/world/level/validation/ForbiddenSymlinkInfo
ee net/minecraft/commands/arguments/ArgumentProfile
eea net/minecraft/world/level/validation/PathAllowList
eed net/minecraft/world/phys/AxisAlignedBB
eee net/minecraft/world/phys/MovingObjectPositionBlock
eef net/minecraft/world/phys/MovingObjectPositionEntity
eeg net/minecraft/world/phys/MovingObjectPosition
eeg$a net/minecraft/world/phys/MovingObjectPosition$EnumMovingObjectType
eeh net/minecraft/world/phys/Vec2F
eei net/minecraft/world/phys/Vec3D
eek net/minecraft/world/phys/shapes/VoxelShapeArray
eel net/minecraft/world/phys/shapes/VoxelShapeBitSet
eem net/minecraft/world/phys/shapes/OperatorBoolean
een net/minecraft/world/phys/shapes/VoxelShapeCollision
eeo net/minecraft/world/phys/shapes/VoxelShapeCubePoint
eep net/minecraft/world/phys/shapes/VoxelShapeCube
eeq net/minecraft/world/phys/shapes/VoxelShapeCubeMerger
eer net/minecraft/world/phys/shapes/VoxelShapeDiscrete
ees net/minecraft/world/phys/shapes/VoxelShapeCollisionEntity
eet net/minecraft/world/phys/shapes/VoxelShapeMergerIdentical
eeu net/minecraft/world/phys/shapes/VoxelShapeMerger
eev net/minecraft/world/phys/shapes/VoxelShapeMergerList
eew net/minecraft/world/phys/shapes/VoxelShapeMergerDisjoint
eex net/minecraft/world/phys/shapes/DoubleListOffset
eey net/minecraft/world/phys/shapes/VoxelShapes
eez net/minecraft/world/phys/shapes/VoxelShapeSlice
ef net/minecraft/commands/arguments/HeightmapTypeArgument
efa net/minecraft/world/phys/shapes/VoxelShapeDiscreteSlice
efb net/minecraft/world/phys/shapes/VoxelShape
efd net/minecraft/world/scores/ScoreboardObjective
efe net/minecraft/world/scores/ScoreboardTeam
eff net/minecraft/world/scores/ScoreboardScore
efg net/minecraft/world/scores/Scoreboard
efh net/minecraft/world/scores/PersistentScoreboard
efi net/minecraft/world/scores/ScoreboardTeamBase
efi$a net/minecraft/world/scores/ScoreboardTeamBase$EnumTeamPush
efi$b net/minecraft/world/scores/ScoreboardTeamBase$EnumNameTagVisibility
efj net/minecraft/world/scores/criteria/IScoreboardCriteria
efj$a net/minecraft/world/scores/criteria/IScoreboardCriteria$EnumScoreboardHealthDisplay
efm net/minecraft/world/ticks/TickListEmpty
efn net/minecraft/world/ticks/ContainerSingleItem
efo net/minecraft/world/ticks/LevelChunkTicks
efp net/minecraft/world/ticks/LevelTickAccess
efq net/minecraft/world/ticks/TickListServer
efr net/minecraft/world/ticks/ProtoChunkTickList
efs net/minecraft/world/ticks/TickListChunk
eft net/minecraft/world/ticks/NextTickListEntry
efu net/minecraft/world/ticks/SerializableTickContainer
efv net/minecraft/world/ticks/TickList
efw net/minecraft/world/ticks/TickContainerAccess
efx net/minecraft/world/ticks/TickListPriority
efy net/minecraft/world/ticks/TickListWorldGen
eg net/minecraft/commands/arguments/ArgumentChat
eh net/minecraft/commands/arguments/ArgumentNBTKey
ei net/minecraft/commands/arguments/ArgumentNBTBase
ej net/minecraft/commands/arguments/ArgumentScoreboardObjective
ek net/minecraft/commands/arguments/ArgumentScoreboardCriteria
el net/minecraft/commands/arguments/ArgumentMathOperation
em net/minecraft/commands/arguments/ArgumentParticle
en net/minecraft/commands/arguments/ArgumentCriterionValue
eo net/minecraft/commands/arguments/ResourceArgument
ep net/minecraft/commands/arguments/ResourceKeyArgument
eq net/minecraft/commands/arguments/ArgumentMinecraftKeyRegistered
er net/minecraft/commands/arguments/ResourceOrTagArgument
es net/minecraft/commands/arguments/ResourceOrTagKeyArgument
et net/minecraft/commands/arguments/ArgumentScoreholder
eu net/minecraft/commands/arguments/ArgumentScoreboardSlot
ev net/minecraft/commands/arguments/SignedArgument
ew net/minecraft/commands/arguments/ArgumentInventorySlot
ex net/minecraft/commands/arguments/StringRepresentableArgument
ey net/minecraft/commands/arguments/ArgumentScoreboardTeam
ez net/minecraft/commands/arguments/TemplateMirrorArgument
f com/mojang/math/MatrixUtil
fa net/minecraft/commands/arguments/TemplateRotationArgument
fb net/minecraft/commands/arguments/ArgumentTime
fc net/minecraft/commands/arguments/ArgumentUUID
fd net/minecraft/commands/arguments/blocks/ArgumentTileLocation
fe net/minecraft/commands/arguments/blocks/ArgumentBlockPredicate
ff net/minecraft/commands/arguments/blocks/ArgumentTile
fg net/minecraft/commands/arguments/blocks/ArgumentBlock
fi net/minecraft/commands/arguments/coordinates/ArgumentPosition
fj net/minecraft/commands/arguments/coordinates/ArgumentVec2I
fk net/minecraft/commands/arguments/coordinates/IVectorPosition
fl net/minecraft/commands/arguments/coordinates/ArgumentVectorPosition
fm net/minecraft/commands/arguments/coordinates/ArgumentRotation
fn net/minecraft/commands/arguments/coordinates/ArgumentRotationAxis
fo net/minecraft/commands/arguments/coordinates/ArgumentVec2
fp net/minecraft/commands/arguments/coordinates/ArgumentVec3
fq net/minecraft/commands/arguments/coordinates/ArgumentParserPosition
fr net/minecraft/commands/arguments/coordinates/VectorPosition
ft net/minecraft/commands/arguments/item/ArgumentTag
fu net/minecraft/commands/arguments/item/ArgumentItemStack
fv net/minecraft/commands/arguments/item/ArgumentPredicateItemStack
fw net/minecraft/commands/arguments/item/ArgumentParserItemStack
fx net/minecraft/commands/arguments/item/ArgumentItemPredicate
ga net/minecraft/commands/arguments/selector/EntitySelector
gb net/minecraft/commands/arguments/selector/ArgumentParserSelector
gc net/minecraft/commands/arguments/selector/options/PlayerSelector
gg net/minecraft/commands/synchronization/ArgumentTypeInfo
gh net/minecraft/commands/synchronization/ArgumentTypeInfos
gi net/minecraft/commands/synchronization/ArgumentUtils
gj net/minecraft/commands/synchronization/SingletonArgumentInfo
gk net/minecraft/commands/synchronization/CompletionProviders
gl net/minecraft/commands/synchronization/brigadier/DoubleArgumentInfo
gm net/minecraft/commands/synchronization/brigadier/FloatArgumentInfo
gn net/minecraft/commands/synchronization/brigadier/IntegerArgumentInfo
go net/minecraft/commands/synchronization/brigadier/LongArgumentInfo
gp net/minecraft/commands/synchronization/brigadier/ArgumentSerializerString
gs net/minecraft/core/EnumAxisCycle
gu net/minecraft/core/BlockPosition
gu$a net/minecraft/core/BlockPosition$MutableBlockPosition
gv net/minecraft/core/ISourceBlock
gw net/minecraft/core/SourceBlock
gx net/minecraft/core/CursorPosition
gy net/minecraft/core/DefaultedMappedRegistry
gz net/minecraft/core/RegistryBlocks
h com/mojang/math/PointGroupO
ha net/minecraft/core/EnumDirection
ha$a net/minecraft/core/EnumDirection$EnumAxis
ha$b net/minecraft/core/EnumDirection$EnumAxisDirection
ha$c net/minecraft/core/EnumDirection$EnumDirectionLimit
hb net/minecraft/core/EnumDirection8
hc net/minecraft/core/BlockPropertyJigsawOrientation
hd net/minecraft/core/GlobalPos
he net/minecraft/core/Holder
hf net/minecraft/core/HolderGetter
hg net/minecraft/core/HolderLookup
hh net/minecraft/core/HolderOwner
hi net/minecraft/core/HolderSet
hi$c net/minecraft/core/HolderSet$Named
hj net/minecraft/core/Registry
hk net/minecraft/core/RegistryBlockID
hl net/minecraft/core/LayeredRegistryAccess
hm net/minecraft/core/RegistryMaterials
hn net/minecraft/core/NonNullList
ho net/minecraft/core/IPosition
hp net/minecraft/core/Position
hq net/minecraft/core/QuartPos
hr net/minecraft/core/IRegistry
hs net/minecraft/core/IRegistryCustom
hs$b net/minecraft/core/IRegistryCustom$Dimension
ht net/minecraft/core/RegistryCodecs
hu net/minecraft/core/RegistrySetBuilder
hv net/minecraft/core/RegistrySynchronization
hw net/minecraft/core/Vector3f
hx net/minecraft/core/SectionPosition
hy net/minecraft/core/UUIDUtil
hz net/minecraft/core/BaseBlockPosition
i com/mojang/math/PointGroupS
ia net/minecraft/core/IRegistryWritable
ib net/minecraft/core/cauldron/CauldronInteraction
id net/minecraft/core/dispenser/DispenseBehaviorProjectile
ie net/minecraft/core/dispenser/DispenseBehaviorBoat
ig net/minecraft/core/dispenser/DispenseBehaviorItem
ih net/minecraft/core/dispenser/IDispenseBehavior
ii net/minecraft/core/dispenser/DispenseBehaviorMaybe
ij net/minecraft/core/dispenser/DispenseBehaviorShears
ik net/minecraft/core/dispenser/DispenseBehaviorShulkerBox
in net/minecraft/core/particles/ParticleParamBlock
io net/minecraft/core/particles/DustColorTransitionOptions
ip net/minecraft/core/particles/ParticleParamRedstone
iq net/minecraft/core/particles/DustParticleOptionsBase
ir net/minecraft/core/particles/ParticleParamItem
it net/minecraft/core/particles/ParticleParam
iu net/minecraft/core/particles/Particle
iv net/minecraft/core/particles/Particles
iw net/minecraft/core/particles/SculkChargeParticleOptions
ix net/minecraft/core/particles/ShriekParticleOption
iy net/minecraft/core/particles/ParticleType
iz net/minecraft/core/particles/VibrationParticleOption
j com/mojang/math/Transformation
jb net/minecraft/core/registries/BuiltInRegistries
jc net/minecraft/core/registries/Registries
jg net/minecraft/data/CachedOutput
ji net/minecraft/data/DebugReportProvider
jk net/minecraft/data/PackOutput
l net/minecraft/BlockUtil
l$a net/minecraft/BlockUtil$Rectangle
l$b net/minecraft/BlockUtil$IntBounds
m net/minecraft/CharPredicate
me net/minecraft/data/registries/VanillaRegistries
mg net/minecraft/data/structures/DebugReportNBT
mh net/minecraft/data/structures/SnbtToNbt
mi net/minecraft/data/structures/StructureUpdater
n net/minecraft/EnumChatFormat
nd net/minecraft/data/worldgen/AncientCityStructurePieces
ne net/minecraft/data/worldgen/AncientCityStructurePools
net/minecraft/server/Main net/minecraft/server/Main
net/minecraft/server/MinecraftServer net/minecraft/server/MinecraftServer
net/minecraft/server/MinecraftServer$a net/minecraft/server/MinecraftServer$ReloadableResources
net/minecraft/server/MinecraftServer$b net/minecraft/server/MinecraftServer$ServerResourcePackInfo
net/minecraft/server/MinecraftServer$c net/minecraft/server/MinecraftServer$TimeProfiler
net/minecraft/util/profiling/jfr/event/NetworkSummaryEvent net/minecraft/util/profiling/jfr/event/NetworkSummaryEvent
nf net/minecraft/data/worldgen/WorldGenFeatureBastionBridge
ng net/minecraft/data/worldgen/WorldGenFeatureBastionHoglinStable
nh net/minecraft/data/worldgen/WorldGenFeatureBastionUnits
ni net/minecraft/data/worldgen/WorldGenFeatureBastionPieces
nj net/minecraft/data/worldgen/WorldGenFeatureBastionExtra
nk net/minecraft/data/worldgen/WorldGenFeatureBastionTreasure
nl net/minecraft/data/worldgen/BiomeSettings
nm net/minecraft/data/worldgen/BootstapContext
nn net/minecraft/data/worldgen/WorldGenCarvers
no net/minecraft/data/worldgen/WorldGenFeatureDesertVillage
np net/minecraft/data/worldgen/DimensionTypes
nq net/minecraft/data/worldgen/NoiseData
nr net/minecraft/data/worldgen/WorldGenFeaturePillagerOutpostPieces
ns net/minecraft/data/worldgen/WorldGenFeatureVillagePlain
nt net/minecraft/data/worldgen/WorldGenFeaturePieces
nu net/minecraft/data/worldgen/ProcessorLists
nv net/minecraft/data/worldgen/WorldGenFeatureVillageSavanna
nw net/minecraft/data/worldgen/WorldGenFeatureVillageSnowy
nx net/minecraft/data/worldgen/StructureSets
ny net/minecraft/data/worldgen/Structures
nz net/minecraft/data/worldgen/SurfaceRuleData
o net/minecraft/CrashReport
oa net/minecraft/data/worldgen/WorldGenFeatureVillageTaiga
ob net/minecraft/data/worldgen/TerrainProvider
oc net/minecraft/data/worldgen/TrailRuinsStructurePools
od net/minecraft/data/worldgen/WorldGenFeatureVillages
oe net/minecraft/data/worldgen/biome/BiomeData
of net/minecraft/data/worldgen/biome/EndBiomes
og net/minecraft/data/worldgen/biome/NetherBiomes
oh net/minecraft/data/worldgen/biome/OverworldBiomes
oj net/minecraft/data/worldgen/features/AquaticFeatures
ok net/minecraft/data/worldgen/features/CaveFeatures
ol net/minecraft/data/worldgen/features/EndFeatures
om net/minecraft/data/worldgen/features/FeatureUtils
on net/minecraft/data/worldgen/features/MiscOverworldFeatures
oo net/minecraft/data/worldgen/features/NetherFeatures
op net/minecraft/data/worldgen/features/OreFeatures
oq net/minecraft/data/worldgen/features/PileFeatures
or net/minecraft/data/worldgen/features/TreeFeatures
os net/minecraft/data/worldgen/features/VegetationFeatures
ov net/minecraft/data/worldgen/placement/AquaticPlacements
ow net/minecraft/data/worldgen/placement/CavePlacements
ox net/minecraft/data/worldgen/placement/EndPlacements
oy net/minecraft/data/worldgen/placement/MiscOverworldPlacements
oz net/minecraft/data/worldgen/placement/NetherPlacements
p net/minecraft/CrashReportSystemDetails
p$a net/minecraft/CrashReportSystemDetails$CrashReportDetail
pa net/minecraft/data/worldgen/placement/OrePlacements
pb net/minecraft/data/worldgen/placement/PlacementUtils
pc net/minecraft/data/worldgen/placement/TreePlacements
pd net/minecraft/data/worldgen/placement/VegetationPlacements
pe net/minecraft/data/worldgen/placement/VillagePlacements
pg net/minecraft/gametest/framework/AfterBatch
ph net/minecraft/gametest/framework/BeforeBatch
pi net/minecraft/gametest/framework/ExhaustedAttemptsException
pj net/minecraft/gametest/framework/GameTest
pk net/minecraft/gametest/framework/GameTestHarnessAssertion
pl net/minecraft/gametest/framework/GameTestHarnessAssertionPosition
pm net/minecraft/gametest/framework/GameTestHarnessBatch
pn net/minecraft/gametest/framework/GameTestHarnessBatchRunner
po net/minecraft/gametest/framework/GameTestHarnessEvent
pp net/minecraft/gametest/framework/GameTestGenerator
pq net/minecraft/gametest/framework/GameTestHarnessHelper
pr net/minecraft/gametest/framework/GameTestHarnessInfo
ps net/minecraft/gametest/framework/GameTestHarnessListener
pt net/minecraft/gametest/framework/GameTestHarnessRegistry
pu net/minecraft/gametest/framework/GameTestHarnessRunner
pv net/minecraft/gametest/framework/GameTestHarnessSequence
px net/minecraft/gametest/framework/GameTestHarnessTicker
py net/minecraft/gametest/framework/GameTestHarnessTimeout
pz net/minecraft/gametest/framework/GlobalTestReporter
q net/minecraft/CrashReportCallable
qb net/minecraft/gametest/framework/GameTestHarnessLogger
qc net/minecraft/gametest/framework/GameTestHarnessCollector
qd net/minecraft/gametest/framework/ReportGameListener
qe net/minecraft/gametest/framework/GameTestHarnessStructures
qg net/minecraft/gametest/framework/GameTestHarnessTestClassArgument
qh net/minecraft/gametest/framework/GameTestHarnessTestCommand
qi net/minecraft/gametest/framework/GameTestHarnessTestFunction
qj net/minecraft/gametest/framework/GameTestHarnessTestFunctionArgument
qk net/minecraft/gametest/framework/GameTestHarnessITestReporter
qm net/minecraft/locale/LocaleLanguage
qo net/minecraft/nbt/NBTTagByteArray
qp net/minecraft/nbt/NBTTagByte
qq net/minecraft/nbt/NBTList
qr net/minecraft/nbt/NBTTagCompound
qs net/minecraft/nbt/NBTTagDouble
qt net/minecraft/nbt/NBTTagEnd
qu net/minecraft/nbt/NBTTagFloat
qv net/minecraft/nbt/NBTTagIntArray
qw net/minecraft/nbt/NBTTagInt
qx net/minecraft/nbt/NBTTagList
qy net/minecraft/nbt/NBTTagLongArray
qz net/minecraft/nbt/NBTTagLong
r net/minecraft/DefaultUncaughtExceptionHandler
ra net/minecraft/nbt/NBTReadLimiter
rb net/minecraft/nbt/NBTCompressedStreamTools
rc net/minecraft/nbt/DynamicOpsNBT
rd net/minecraft/nbt/GameProfileSerializer
re net/minecraft/nbt/NBTNumber
rf net/minecraft/nbt/NBTTagShort
rg net/minecraft/nbt/SnbtPrinterTagVisitor
rh net/minecraft/nbt/StreamTagVisitor
ri net/minecraft/nbt/NBTTagString
rj net/minecraft/nbt/StringTagVisitor
rk net/minecraft/nbt/NBTBase
rl net/minecraft/nbt/MojangsonParser
rm net/minecraft/nbt/NBTTagType
rn net/minecraft/nbt/NBTTagTypes
ro net/minecraft/nbt/TagVisitor
rp net/minecraft/nbt/TextComponentTagVisitor
rr net/minecraft/nbt/visitors/CollectFields
rs net/minecraft/nbt/visitors/CollectToTag
rt net/minecraft/nbt/visitors/FieldSelector
ru net/minecraft/nbt/visitors/FieldTree
rw net/minecraft/nbt/visitors/SkipFields
ry net/minecraft/network/PacketEncryptionHandler
rz net/minecraft/network/PacketDecrypter
s net/minecraft/ThreadNamedUncaughtExceptionHandler
sa net/minecraft/network/PacketEncrypter
sb net/minecraft/network/PacketDecompressor
sc net/minecraft/network/PacketCompressor
sd net/minecraft/network/NetworkManager
sd$a net/minecraft/network/NetworkManager$QueuedPacket
se net/minecraft/network/EnumProtocol
sf net/minecraft/network/PacketDataSerializer
sg net/minecraft/network/PacketBundlePacker
sh net/minecraft/network/PacketBundleUnpacker
si net/minecraft/network/PacketDecoder
sj net/minecraft/network/PacketEncoder
sk net/minecraft/network/PacketListener
sl net/minecraft/network/PacketSendListener
sm net/minecraft/network/NetworkManagerServer
sn net/minecraft/network/SkipEncodeException
so net/minecraft/network/TickablePacketListener
sp net/minecraft/network/PacketSplitter
sq net/minecraft/network/PacketPrepender
sr net/minecraft/network/chat/ChatDecorator
ss net/minecraft/network/chat/ChatMessageType
st net/minecraft/network/chat/ChatDecoration
su net/minecraft/network/chat/ChatClickable
su$a net/minecraft/network/chat/ChatClickable$EnumClickAction
sv net/minecraft/network/chat/CommonComponents
sw net/minecraft/network/chat/IChatBaseComponent
sw$a net/minecraft/network/chat/IChatBaseComponent$ChatSerializer
sx net/minecraft/network/chat/ComponentContents
sy net/minecraft/network/chat/ChatComponentUtils
sz net/minecraft/network/chat/FilterMask
t net/minecraft/MinecraftVersion
ta net/minecraft/network/chat/IChatFormatted
tb net/minecraft/network/chat/ChatHoverable
tb$a net/minecraft/network/chat/ChatHoverable$EnumHoverAction
tc net/minecraft/network/chat/LastSeenMessages
te net/minecraft/network/chat/LastSeenMessagesValidator
tf net/minecraft/network/chat/LastSeenTrackedEntry
th net/minecraft/network/chat/MessageSignature
ti net/minecraft/network/chat/MessageSignatureCache
tj net/minecraft/network/chat/IChatMutableComponent
tk net/minecraft/network/chat/OutgoingChatMessage
tl net/minecraft/network/chat/PlayerChatMessage
tm net/minecraft/network/chat/RemoteChatSession
tn net/minecraft/network/chat/SignableCommand
to net/minecraft/network/chat/SignedMessageBody
tp net/minecraft/network/chat/SignedMessageChain
tq net/minecraft/network/chat/SignedMessageLink
tr net/minecraft/network/chat/SignedMessageValidator
ts net/minecraft/network/chat/ChatModifier
ts$b net/minecraft/network/chat/ChatModifier$ChatModifierSerializer
tu net/minecraft/network/chat/ChatHexColor
tv net/minecraft/network/chat/ThrowingComponent
tw net/minecraft/network/chat/contents/BlockDataSource
tx net/minecraft/network/chat/contents/DataSource
ty net/minecraft/network/chat/contents/EntityDataSource
tz net/minecraft/network/chat/contents/KeybindContents
ua net/minecraft/network/chat/contents/KeybindResolver
ub net/minecraft/network/chat/contents/LiteralContents
uc net/minecraft/network/chat/contents/NbtContents
ud net/minecraft/network/chat/contents/ScoreContents
ue net/minecraft/network/chat/contents/SelectorContents
uf net/minecraft/network/chat/contents/StorageDataSource
ug net/minecraft/network/chat/contents/TranslatableContents
uh net/minecraft/network/chat/contents/TranslatableFormatException
ul net/minecraft/network/protocol/BundleDelimiterPacket
um net/minecraft/network/protocol/BundlePacket
un net/minecraft/network/protocol/BundlerInfo
uo net/minecraft/network/protocol/Packet
up net/minecraft/network/protocol/EnumProtocolDirection
uq net/minecraft/network/protocol/PlayerConnectionUtils
ur net/minecraft/network/protocol/game/PacketListenerPlayOut
us net/minecraft/network/protocol/game/PacketPlayOutSpawnEntity
ut net/minecraft/network/protocol/game/PacketPlayOutSpawnEntityExperienceOrb
uu net/minecraft/network/protocol/game/PacketPlayOutNamedEntitySpawn
uv net/minecraft/network/protocol/game/PacketPlayOutAnimation
uw net/minecraft/network/protocol/game/PacketPlayOutStatistic
ux net/minecraft/network/protocol/game/ClientboundBlockChangedAckPacket
uy net/minecraft/network/protocol/game/PacketPlayOutBlockBreakAnimation
uz net/minecraft/network/protocol/game/PacketPlayOutTileEntityData
v net/minecraft/FileUtils
va net/minecraft/network/protocol/game/PacketPlayOutBlockAction
vb net/minecraft/network/protocol/game/PacketPlayOutBlockChange
vc net/minecraft/network/protocol/game/PacketPlayOutBoss
vc$c net/minecraft/network/protocol/game/PacketPlayOutBoss$Action
vd net/minecraft/network/protocol/game/ClientboundBundlePacket
ve net/minecraft/network/protocol/game/PacketPlayOutServerDifficulty
vf net/minecraft/network/protocol/game/ClientboundChunksBiomesPacket
vg net/minecraft/network/protocol/game/ClientboundClearTitlesPacket
vh net/minecraft/network/protocol/game/PacketPlayOutTabComplete
vi net/minecraft/network/protocol/game/PacketPlayOutCommands
vj net/minecraft/network/protocol/game/PacketPlayOutCloseWindow
vk net/minecraft/network/protocol/game/PacketPlayOutWindowItems
vl net/minecraft/network/protocol/game/PacketPlayOutWindowData
vm net/minecraft/network/protocol/game/PacketPlayOutSetSlot
vn net/minecraft/network/protocol/game/PacketPlayOutSetCooldown
vo net/minecraft/network/protocol/game/ClientboundCustomChatCompletionsPacket
vo$a net/minecraft/network/protocol/game/ClientboundCustomChatCompletionsPacket$Action
vp net/minecraft/network/protocol/game/PacketPlayOutCustomPayload
vq net/minecraft/network/protocol/game/ClientboundDamageEventPacket
vr net/minecraft/network/protocol/game/ClientboundDeleteChatPacket
vs net/minecraft/network/protocol/game/PacketPlayOutKickDisconnect
vt net/minecraft/network/protocol/game/ClientboundDisguisedChatPacket
vu net/minecraft/network/protocol/game/PacketPlayOutEntityStatus
vv net/minecraft/network/protocol/game/PacketPlayOutExplosion
vw net/minecraft/network/protocol/game/PacketPlayOutUnloadChunk
vx net/minecraft/network/protocol/game/PacketPlayOutGameStateChange
vy net/minecraft/network/protocol/game/PacketPlayOutOpenWindowHorse
vz net/minecraft/network/protocol/game/ClientboundHurtAnimationPacket
wa net/minecraft/network/protocol/game/ClientboundInitializeBorderPacket
wb net/minecraft/network/protocol/game/PacketPlayOutKeepAlive
wc net/minecraft/network/protocol/game/ClientboundLevelChunkPacketData
wd net/minecraft/network/protocol/game/ClientboundLevelChunkWithLightPacket
we net/minecraft/network/protocol/game/PacketPlayOutWorldEvent
wf net/minecraft/network/protocol/game/PacketPlayOutWorldParticles
wg net/minecraft/network/protocol/game/PacketPlayOutLightUpdate
wh net/minecraft/network/protocol/game/ClientboundLightUpdatePacketData
wi net/minecraft/network/protocol/game/PacketPlayOutLogin
wj net/minecraft/network/protocol/game/PacketPlayOutMap
wk net/minecraft/network/protocol/game/PacketPlayOutOpenWindowMerchant
wl net/minecraft/network/protocol/game/PacketPlayOutEntity
wl$a net/minecraft/network/protocol/game/PacketPlayOutEntity$PacketPlayOutRelEntityMove
wl$b net/minecraft/network/protocol/game/PacketPlayOutEntity$PacketPlayOutRelEntityMoveLook
wl$c net/minecraft/network/protocol/game/PacketPlayOutEntity$PacketPlayOutEntityLook
wm net/minecraft/network/protocol/game/PacketPlayOutVehicleMove
wn net/minecraft/network/protocol/game/PacketPlayOutOpenBook
wo net/minecraft/network/protocol/game/PacketPlayOutOpenWindow
wp net/minecraft/network/protocol/game/PacketPlayOutOpenSignEditor
wq net/minecraft/network/protocol/game/ClientboundPingPacket
wr net/minecraft/network/protocol/game/PacketPlayOutAutoRecipe
ws net/minecraft/network/protocol/game/PacketPlayOutAbilities
wt net/minecraft/network/protocol/game/ClientboundPlayerChatPacket
wu net/minecraft/network/protocol/game/ClientboundPlayerCombatEndPacket
wv net/minecraft/network/protocol/game/ClientboundPlayerCombatEnterPacket
ww net/minecraft/network/protocol/game/ClientboundPlayerCombatKillPacket
wx net/minecraft/network/protocol/game/ClientboundPlayerInfoRemovePacket
wy net/minecraft/network/protocol/game/ClientboundPlayerInfoUpdatePacket
wz net/minecraft/network/protocol/game/PacketPlayOutLookAt
x net/minecraft/Optionull
xa net/minecraft/network/protocol/game/PacketPlayOutPosition
xb net/minecraft/network/protocol/game/PacketPlayOutRecipes
xb$a net/minecraft/network/protocol/game/PacketPlayOutRecipes$Action
xc net/minecraft/network/protocol/game/PacketPlayOutEntityDestroy
xd net/minecraft/network/protocol/game/PacketPlayOutRemoveEntityEffect
xe net/minecraft/network/protocol/game/PacketPlayOutResourcePackSend
xf net/minecraft/network/protocol/game/PacketPlayOutRespawn
xg net/minecraft/network/protocol/game/PacketPlayOutEntityHeadRotation
xh net/minecraft/network/protocol/game/PacketPlayOutMultiBlockChange
xi net/minecraft/network/protocol/game/PacketPlayOutSelectAdvancementTab
xj net/minecraft/network/protocol/game/ClientboundServerDataPacket
xk net/minecraft/network/protocol/game/ClientboundSetActionBarTextPacket
xl net/minecraft/network/protocol/game/ClientboundSetBorderCenterPacket
xm net/minecraft/network/protocol/game/ClientboundSetBorderLerpSizePacket
xn net/minecraft/network/protocol/game/ClientboundSetBorderSizePacket
xo net/minecraft/network/protocol/game/ClientboundSetBorderWarningDelayPacket
xp net/minecraft/network/protocol/game/ClientboundSetBorderWarningDistancePacket
xq net/minecraft/network/protocol/game/PacketPlayOutCamera
xr net/minecraft/network/protocol/game/PacketPlayOutHeldItemSlot
xs net/minecraft/network/protocol/game/PacketPlayOutViewCentre
xt net/minecraft/network/protocol/game/PacketPlayOutViewDistance
xu net/minecraft/network/protocol/game/PacketPlayOutSpawnPosition
xv net/minecraft/network/protocol/game/PacketPlayOutScoreboardDisplayObjective
xw net/minecraft/network/protocol/game/PacketPlayOutEntityMetadata
xx net/minecraft/network/protocol/game/PacketPlayOutAttachEntity
xy net/minecraft/network/protocol/game/PacketPlayOutEntityVelocity
xz net/minecraft/network/protocol/game/PacketPlayOutEntityEquipment
y net/minecraft/ReportedException
ya net/minecraft/network/protocol/game/PacketPlayOutExperience
yb net/minecraft/network/protocol/game/PacketPlayOutUpdateHealth
yc net/minecraft/network/protocol/game/PacketPlayOutScoreboardObjective
yd net/minecraft/network/protocol/game/PacketPlayOutMount
ye net/minecraft/network/protocol/game/PacketPlayOutScoreboardTeam
yf net/minecraft/network/protocol/game/PacketPlayOutScoreboardScore
yg net/minecraft/network/protocol/game/ClientboundSetSimulationDistancePacket
yh net/minecraft/network/protocol/game/ClientboundSetSubtitleTextPacket
yi net/minecraft/network/protocol/game/PacketPlayOutUpdateTime
yj net/minecraft/network/protocol/game/ClientboundSetTitleTextPacket
yk net/minecraft/network/protocol/game/ClientboundSetTitlesAnimationPacket
yl net/minecraft/network/protocol/game/PacketPlayOutEntitySound
ym net/minecraft/network/protocol/game/PacketPlayOutNamedSoundEffect
yn net/minecraft/network/protocol/game/PacketPlayOutStopSound
yo net/minecraft/network/protocol/game/ClientboundSystemChatPacket
yp net/minecraft/network/protocol/game/PacketPlayOutPlayerListHeaderFooter
yq net/minecraft/network/protocol/game/PacketPlayOutNBTQuery
yr net/minecraft/network/protocol/game/PacketPlayOutCollect
ys net/minecraft/network/protocol/game/PacketPlayOutEntityTeleport
yt net/minecraft/network/protocol/game/PacketPlayOutAdvancements
yu net/minecraft/network/protocol/game/PacketPlayOutUpdateAttributes
yu$a net/minecraft/network/protocol/game/PacketPlayOutUpdateAttributes$AttributeSnapshot
yv net/minecraft/network/protocol/game/ClientboundUpdateEnabledFeaturesPacket
yw net/minecraft/network/protocol/game/PacketPlayOutEntityEffect
yx net/minecraft/network/protocol/game/PacketPlayOutRecipeUpdate
yy net/minecraft/network/protocol/game/PacketPlayOutTags
yz net/minecraft/network/protocol/game/DebugEntityNameGenerator
z net/minecraft/ResourceKeyInvalidException
za net/minecraft/network/protocol/game/PacketDebug
zb net/minecraft/network/protocol/game/PacketListenerPlayIn
zc net/minecraft/network/protocol/game/ServerPacketListener
zd net/minecraft/network/protocol/game/PacketPlayInTeleportAccept
ze net/minecraft/network/protocol/game/PacketPlayInTileNBTQuery
zf net/minecraft/network/protocol/game/PacketPlayInDifficultyChange
zg net/minecraft/network/protocol/game/ServerboundChatAckPacket
zh net/minecraft/network/protocol/game/ServerboundChatCommandPacket
zi net/minecraft/network/protocol/game/PacketPlayInChat
zj net/minecraft/network/protocol/game/ServerboundChatSessionUpdatePacket
zk net/minecraft/network/protocol/game/PacketPlayInClientCommand
zk$a net/minecraft/network/protocol/game/PacketPlayInClientCommand$EnumClientCommand
zl net/minecraft/network/protocol/game/PacketPlayInSettings
zm net/minecraft/network/protocol/game/PacketPlayInTabComplete
zn net/minecraft/network/protocol/game/PacketPlayInEnchantItem
zo net/minecraft/network/protocol/game/PacketPlayInWindowClick
zp net/minecraft/network/protocol/game/PacketPlayInCloseWindow
zq net/minecraft/network/protocol/game/PacketPlayInCustomPayload
zr net/minecraft/network/protocol/game/PacketPlayInBEdit
zs net/minecraft/network/protocol/game/PacketPlayInEntityNBTQuery
zt net/minecraft/network/protocol/game/PacketPlayInUseEntity
zt$a net/minecraft/network/protocol/game/PacketPlayInUseEntity$EnumEntityUseAction
zu net/minecraft/network/protocol/game/PacketPlayInJigsawGenerate
zv net/minecraft/network/protocol/game/PacketPlayInKeepAlive
zw net/minecraft/network/protocol/game/PacketPlayInDifficultyLock
zx net/minecraft/network/protocol/game/PacketPlayInFlying
zx$a net/minecraft/network/protocol/game/PacketPlayInFlying$PacketPlayInPosition
zx$b net/minecraft/network/protocol/game/PacketPlayInFlying$PacketPlayInPositionLook
zx$c net/minecraft/network/protocol/game/PacketPlayInFlying$PacketPlayInLook
zy net/minecraft/network/protocol/game/PacketPlayInVehicleMove
zz net/minecraft/network/protocol/game/PacketPlayInBoatMove
